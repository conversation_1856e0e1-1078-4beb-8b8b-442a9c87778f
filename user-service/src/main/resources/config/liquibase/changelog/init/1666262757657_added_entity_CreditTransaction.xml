<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263467906-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="credit_transaction"/>
            </not>
        </preConditions>
        <createTable tableName="credit_transaction">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="profile_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="credit" type="INT"/>
            <column defaultValueNumeric="0" name="credit_type" remarks="credit type,0:monthly credit;1:bulk credit"
                    type="TINYINT(3)"/>
            <column name="tenant_id" type="BIGINT"/>
            <column name="user_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="exists_contacts" type="TEXT"/>
            <column name="es_contacts" type="TEXT"/>
            <column name="api_source" type="VARCHAR(50)"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="talent_id" type="BIGINT"/>
            <column name="common_db_search_es_id" type="VARCHAR(255)"/>
        </createTable>

        <addUniqueConstraint columnNames="profile_id, tenant_id, status"
                             constraintName="idx_credit_transaction_profile_id_tenant_id_status"
                             tableName="credit_transaction"/>
        <createIndex indexName="idx_crdit_transaction_tenant_id" tableName="credit_transaction">
            <column name="tenant_id"/>
        </createIndex>
        <createIndex indexName="idx_credit_transaction_tenant_type_user" tableName="credit_transaction">
            <column name="tenant_id"/>
            <column defaultValueNumeric="0" name="credit_type"/>
            <column name="user_id"/>
        </createIndex>
        <createIndex indexName="idx_credit_transaction_type_date" tableName="credit_transaction">
            <column defaultValueNumeric="0" name="credit_type"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
