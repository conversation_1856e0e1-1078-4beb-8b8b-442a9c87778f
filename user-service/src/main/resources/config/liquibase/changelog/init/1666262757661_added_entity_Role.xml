<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264177966-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="role"/>
            </not>
        </preConditions>
        <createTable tableName="role">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(50)"/>
            <column name="data_scope" type="TINYINT(3)"/>
            <column defaultValueNumeric="1" name="client_contact_data_scope" type="TINYINT(3)"/>
            <column defaultValueNumeric="1" name="report_data_scope" type="TINYINT(3)"/>
            <column defaultValueNumeric="1" name="home_and_calendar_data_scope" type="TINYINT(3)"/>
            <column defaultValueNumeric="1" name="candidate_pipeline_management_data_scope" type="TINYINT(3)"/>
            <column name="is_internal" type="TINYINT(3)"/>
            <column name="tenant_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="status" type="INT"/>
            <column name="description" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
