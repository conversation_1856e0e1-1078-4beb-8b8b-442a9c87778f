<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264908329-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user_admin_role"/>
            </not>
        </preConditions>
        <createTable tableName="user_admin_role">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" type="BIGINT"/>
            <column name="role_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_user_role" tableName="user_admin_role">
            <column name="user_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
