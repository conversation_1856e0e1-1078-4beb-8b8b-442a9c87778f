<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity SystemConfigDefault.
    -->
    <changeSet id="1682011226909-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="system_config_default"/>
            </not>
        </preConditions>
        <createTable tableName="system_config_default">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="category" type="tinyint(3)">
                <constraints nullable="false"/>
            </column>
            <column name="subcategory" type="smallint"></column>
            <column name="default_config" type="json"/>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <createIndex indexName="index_system_config_default_category" tableName="system_config_default">
            <column name="category"/>
        </createIndex>
        <createIndex indexName="index_system_config_default_subcategory" tableName="system_config_default">
            <column name="subcategory"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
