<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264879858-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user"/>
            </not>
        </preConditions>
        <createTable tableName="user">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="uid" type="VARCHAR(255)">
                <constraints unique="true"/>
            </column>
            <column name="username" type="VARCHAR(50)">
                <constraints unique="true"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="alternate_email" type="VARCHAR(100)"/>
            <column name="password_hash" type="VARCHAR(60)"/>
            <column name="first_name" type="VARCHAR(50)"/>
            <column name="last_name" type="VARCHAR(50)"/>
            <column name="phone" type="VARCHAR(255)"/>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="image_url" type="VARCHAR(256)"/>
            <column name="activated" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="1" name="division_id" type="BIGINT"/>
            <column name="lang_key" type="VARCHAR(5)"/>
            <column name="activation_key" type="VARCHAR(20)"/>
            <column name="reset_key" type="VARCHAR(20)"/>
            <column name="note" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="credit" type="INT"/>
            <column defaultValueNumeric="0" name="bulk_credit" remarks="bulk credit" type="INT"/>
            <column name="custom_timezone" type="VARCHAR(50)"/>
            <column name="sync_lark" type="BIT(1)" defaultValue="0"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="reset_date" type="timestamp"/>
            <column name="last_sync_time" type="timestamp"/>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column defaultValueNumeric="0" name="account_type" type="INT"/>
            <column defaultValueNumeric="1" name="data_scope" type="INT"/>
            <column defaultValueNumeric="1" name="client_contact_data_scope" type="TINYINT(3)"/>
            <column defaultValueNumeric="1" name="report_data_scope" type="TINYINT(3)"/>
            <column defaultValueNumeric="1" name="home_and_calendar_data_scope" type="TINYINT(3)"/>
            <column defaultValueNumeric="1" name="candidate_pipeline_management_data_scope" type="TINYINT(3)"/>
            <column name="enum_level_of_experience_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="fk_user_tenant_id" tableName="user">
            <column name="tenant_id"/>
        </createIndex>
        <addForeignKeyConstraint baseColumnNames="tenant_id" baseTableName="user" constraintName="fk_user_tenant_id"
                                 deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="tenant" validate="true"/>
    </changeSet>

</databaseChangeLog>
