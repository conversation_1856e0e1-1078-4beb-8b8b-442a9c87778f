<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20250122090001-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user_delivery_country_relation"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE `user_delivery_country_relation` (
              `id` bigint NOT NULL AUTO_INCREMENT,
              `user_id` bigint DEFAULT NULL,
              `enum_country_id` bigint DEFAULT NULL,
              `official_country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
              `count` bigint DEFAULT NULL,
              `top` bit(1) DEFAULT NULL,
              `updated` bit(1) DEFAULT NULL,
              PRIMARY KEY (`id`),
              KEY `idx_user_enum_id` (`user_id`,`enum_country_id`),
              KEY `idx_user_id` (`user_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        </sql>

    </changeSet>

</databaseChangeLog>
