<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264946681-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user_favorite_linkedin_project"/>
            </not>
        </preConditions>
        <createTable tableName="user_favorite_linkedin_project">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="linkedin_project_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_user_favorite_linkedin_project_lpid" tableName="user_favorite_linkedin_project">
            <column name="linkedin_project_id"/>
        </createIndex>
        <createIndex indexName="idx_user_favorite_linkedin_project_uid" tableName="user_favorite_linkedin_project">
            <column name="user_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
