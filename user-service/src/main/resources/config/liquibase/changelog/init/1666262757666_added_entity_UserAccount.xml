<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="*************-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user_account"/>
            </not>
        </preConditions>
        <createTable tableName="user_account">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" type="BIGINT"/>
            <column name="account_type" type="INT"/>
            <column name="amount" type="INT"/>
            <column defaultValueNumeric="0" name="bulk_credit" remarks="bulk credit" type="INT"/>
            <column name="frozen_amount" type="INT"/>
            <column name="total_amount" type="INT"/>
            <column name="account_status" type="INT"/>
            <column name="expire_date" type="VARCHAR(50)"/>
            <column defaultValueNumeric="0" name="version" type="INT"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column defaultValueNumeric="1" name="credit_effect_type" remarks="credit effect type" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="effect_credit" remarks="effect credit amount" type="INT"/>
        </createTable>

        <createIndex indexName="idx_user_account_status_expire_date" tableName="user_account">
            <column name="account_status"/>
            <column name="expire_date"/>
        </createIndex>
        <createIndex indexName="idx_user_account_user_id" tableName="user_account">
            <column name="user_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
