<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264975074-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user_last_login"/>
            </not>
        </preConditions>
        <createTable tableName="user_last_login">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="time_zone" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="location" type="VARCHAR(255)"/>
            <column name="ip" type="VARCHAR(50)"/>
            <column name="mac" type="VARCHAR(255)"/>
            <column name="user_agent" type="VARCHAR(255)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="last_login_time" type="timestamp">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
