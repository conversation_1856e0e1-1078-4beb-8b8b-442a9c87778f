<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264040878-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="permission_team"/>
            </not>
        </preConditions>
        <createTable tableName="permission_team">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(50)"/>
            <column name="code" type="VARCHAR(80)">
                <constraints unique="true"/>
            </column>
            <column name="parent_id" type="BIGINT"/>
            <column name="level" type="INT"/>
            <column name="is_leaf" type="TINYINT(3)"/>
            <column name="tenant_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="deleted" type="TINYINT(3)"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="team_category_id" type="INT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
