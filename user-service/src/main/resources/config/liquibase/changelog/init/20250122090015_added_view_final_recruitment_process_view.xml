<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20250122090015-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <viewExists viewName="final_recruitment_process_view"/>
            </not>
        </preConditions>

        <createView viewName="final_recruitment_process_view">
            SELECT
            `trpku`.`user_id` AS `user_id`,
            `trpku`.`talent_recruitment_process_id` AS `talent_recruitment_process_id`,
            `trp`.`talent_id` AS `talent_id`,
            `trp`.`job_id` AS `job_id`,
            `trp`.`recruitment_process_id` AS `recruitment_process_id`,
            `rpn`.`node_type` AS `node_type`
            FROM
            `talent_recruitment_process_kpi_user` `trpku`
            JOIN `talent_recruitment_process` `trp` ON `trp`.`id` = `trpku`.`talent_recruitment_process_id`
            JOIN `recruitment_process` `rp` ON `rp`.`id` = `trp`.`recruitment_process_id` AND  `rp`.`job_type` != 5
            JOIN `user` `u` ON `u`.id = `trpku`.`user_id` AND `rp`.`tenant_id` = `u`.`tenant_id`
            JOIN `recruitment_process_node` `rpn` ON `trp`.`recruitment_process_id` = `rpn`.`recruitment_process_id`
            JOIN `talent_recruitment_process_node` `trpn` ON `trpn`.`talent_recruitment_process_id` = `trp`.`id`
            AND `trpn`.`next_node_id` IS NULL
            AND `trpn`.`node_status` = 1
            WHERE
            `rpn`.`next_node_id` IS NULL
            AND `rp`.`status` = 0
            GROUP BY
            `trpku`.`user_id`,
            `trpku`.`talent_recruitment_process_id`
        </createView>
    </changeSet>

</databaseChangeLog>
