<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="Longfei (generated)" id="1683831627325-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user_impersonation"/>
            </not>
        </preConditions>
        <createTable tableName="user_impersonation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="grant_from_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="grant_to_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="effective_start_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="expire_at" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
        <createIndex tableName="user_impersonation" indexName="index_user_impersonation_1">
            <column name="grant_from_user_id"/>
            <column name="grant_to_user_id"/>
            <column name="tenant_id"/>
        </createIndex>
        <createIndex tableName="user_impersonation" indexName="index_user_impersonation_2">
            <column name="grant_to_user_id"/>
            <column name="tenant_id"/>
        </createIndex>
        <createIndex tableName="user_impersonation" indexName="index_user_impersonation_3">
            <column name="tenant_id"/>
        </createIndex>

    </changeSet>
</databaseChangeLog>