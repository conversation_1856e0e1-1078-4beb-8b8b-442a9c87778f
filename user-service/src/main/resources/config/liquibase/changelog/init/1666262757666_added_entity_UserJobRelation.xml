<!--<?xml version="1.1" encoding="UTF-8" standalone="no"?>-->
<!--<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"-->
<!--                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"-->
<!--                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"-->
<!--                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">-->
<!--    <changeSet author="m (generated)" id="1666264965564-1">-->
<!--        <preConditions onFail="MARK_RAN">-->
<!--            <not>-->
<!--                <tableExists tableName="user_job_relation"/>-->
<!--            </not>-->
<!--        </preConditions>-->
<!--        <createTable tableName="user_job_relation">-->
<!--            <column autoIncrement="true" name="id" type="BIGINT">-->
<!--                <constraints nullable="false" primaryKey="true"/>-->
<!--            </column>-->
<!--            <column name="permission" type="INT"/>-->
<!--            <column name="search_string" type="TEXT"/>-->
<!--            <column name="user_id" type="BIGINT">-->
<!--                <constraints nullable="false"/>-->
<!--            </column>-->
<!--            <column name="job_id" type="BIGINT">-->
<!--                <constraints nullable="false"/>-->
<!--            </column>-->
<!--            <column name="created_by" type="VARCHAR(50)">-->
<!--                <constraints nullable="false"/>-->
<!--            </column>-->
<!--            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">-->
<!--                <constraints nullable="false"/>-->
<!--            </column>-->
<!--            <column name="last_modified_by" type="VARCHAR(50)"/>-->
<!--            <column name="last_modified_date" type="timestamp"/>-->
<!--        </createTable>-->

<!--        <createIndex indexName="idx_user_job_relation_user_id" tableName="user_job_relation">-->
<!--            <column name="user_id"/>-->
<!--        </createIndex>-->
<!--        <createIndex indexName="index_ujr_job_id" tableName="user_job_relation">-->
<!--            <column name="job_id"/>-->
<!--        </createIndex>-->
<!--    </changeSet>-->

<!--</databaseChangeLog>-->
