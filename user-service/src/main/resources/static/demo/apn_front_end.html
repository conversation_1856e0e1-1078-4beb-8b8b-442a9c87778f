<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>APN</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>

<body>

    <div id="w" class="easyui-window" title="Login" data-options="iconCls:'icon-view'" style="width:400px;height:250px;padding:10px;">
        <form id="ff" method="post" enctype="multipart/form-data">
            <div style="margin-bottom:20px">
                <input class="easyui-textbox" id="username"  prompt="username" style="width:90%" data-options="required:true">
            </div>
            <div style="margin-bottom:20px">
                <input class="easyui-passwordbox" id="password" prompt="password" value="QWEqwe!@#123" style="width:90%" data-options="required:true">
            </div>
        </form>
        <div style="text-align:center;padding:5px 0">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="login()" style="width:80px">Login</a>
        </div>
    </div>


    <div id="Candidate">
        <div class="easyui-panel" title="Candidate" style="width:700px;height:100px;padding:10px;">
            <a id="Add_Candidate" href="#" class="easyui-linkbutton" onclick="addCandidate()">Add</a> 
            <a id="Delete_Candidate" href="#" class="easyui-linkbutton" onclick="deleteCandidate()">Delete</a> 
            <a id="Update_Candidate" href="#" class="easyui-linkbutton" onclick="updateCandidate()">Update</a> 
            <a id="Get_Candidate" href="#" class="easyui-linkbutton" onclick="getCandidate()">Get</a> 
        </div>
    </div>

    <h2></h2>
    <div id="Job">
        <div class="easyui-panel" title="Job" style="width:700px;height:100px;padding:10px;">
            <a id="Add_Job" href="#" class="easyui-linkbutton" onclick="addJob()">Add</a> 
            <a id="Delete_Job" href="#" class="easyui-linkbutton" onclick="deleteJob()">Delete</a> 
            <a id="Update_Job" href="#" class="easyui-linkbutton" onclick="updateJob()">Update</a> 
            <a id="Get_Job" href="#" class="easyui-linkbutton" onclick="getJob()">Get</a> 
        </div>
    </div>

    <h2></h2>
    <div id="Company">
        <div class="easyui-panel" title="Company" style="width:700px;height:100px;padding:10px;">
            <a id="Add_Company" href="#" class="easyui-linkbutton" onclick="addCompany()">Add</a> 
            <a id="Delete_Company" href="#" class="easyui-linkbutton" onclick="deleteCompany()">Delete</a> 
            <a id="Update_Company" href="#" class="easyui-linkbutton" onclick="updateCompany()">Update</a> 
            <a id="Get_Company" href="#" class="easyui-linkbutton" onclick="getCompany()">Get</a> 
        </div>
    </div>

    <h2></h2>
    <div id="User">
        <div class="easyui-panel" title="User" style="width:700px;height:100px;padding:10px;">
            <a id="Add_User" href="#" class="easyui-linkbutton" onclick="addUser()">Add</a> 
            <a id="Delete_User" href="#" class="easyui-linkbutton" onclick="deleteUser()">Delete</a> 
            <a id="Update_User" href="#" class="easyui-linkbutton" onclick="updateUser()">Update</a> 
            <a id="Get_User" href="#" class="easyui-linkbutton" onclick="getUser()">Get</a> 
        </div>
    </div>

    <h2></h2>
    <div id="Test_Module">
        <div class="easyui-panel" title="Test Module" style="width:700px;height:200px;padding:10px;">
            <a id="Test_API_A" href="#" class="easyui-linkbutton" onclick="testApiA()">testApiA</a> 
            <a id="Test_API_B" href="#" class="easyui-linkbutton" onclick="testApiB()">testApiB</a> 
        </div>
    </div>

    <script type="text/javascript">
        var access_token;
        const components = ['APN', 'Add Candidate', 'Delete Candidate', 'Update Candidate', 'Get Candidate', 'Add Job', 'Delete Job', 'Update Job', 'Get Job', 'Add Company', 'Delete Company', 'Candidate', 'Update Company', 'Get Company', 'Get My Account', 'Job', 'Company', 'User', 'Add User', 'Delete User', 'Get User', 'Update User', "Test Module", "Test API A", "Test API B"];
        function login(){
            var payload = {
                "username": $('#username').textbox('getValue'),
                "password": "QWEqwe!@#123"
            };

            $.ajax({
                url: 'http://localhost:8888/user/api/v1/login',
                dataType: 'text',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify(payload),
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    access_token = obj.credential.access_token;
                    getAccount();
                    $('#w').window('close');
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Error', "Login failed", 'error');
                }
            });
        }

        function getAccount(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v1/account',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    var authorities = obj.authorities;
                    console.log(authorities);
                    components.forEach(c => {
                        if (authorities.includes(c)) {
                            // $('#'+c.replace(" ", "_")).linkbutton('resize', {
                            //     width: '100%',
                            //     height: 32
                            // });
                            $('#'+c.replaceAll(" ", "_")).show();
                        }   
                    });
                    
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }


        function sendWithAjax(uri, method){
            $.ajax({
                url: 'http://localhost:8888' + uri,
                dataType: 'text',
                type: method,
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    //var obj = JSON.parse(data)
                    $.messager.show({
                        title:'',
                        msg:data,
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    
                    $.messager.alert('Error', "NO PERMISSION", 'error');
                }
            });
        }

        // Candidate

        function addCandidate(){
            var uri = '/user/api/v3/candidates/add-candidate';
            var method= 'post';
            sendWithAjax(uri, method);
        }

        function deleteCandidate(){
            var uri = '/user/api/v3/candidates/delete-candidate/1';
            var method= 'delete';
            sendWithAjax(uri, method);
        }

        function updateCandidate(){
            var uri = '/user/api/v3/candidates/update-candidate';
            var method= 'put';
            sendWithAjax(uri, method);
        }

        function getCandidate(){
            var uri = '/user/api/v3/candidates/get-candidates';
            var method= 'get';
            sendWithAjax(uri, method);
        }

        // Job

        function addJob(){
            var uri = '/user/api/v3/jobs/add-job';
            var method= 'post';
            sendWithAjax(uri, method);
        }

        function deleteJob(){
            var uri = '/user/api/v3/jobs/delete-job/1';
            var method= 'delete';
            sendWithAjax(uri, method);
        }

        function updateJob(){
            var uri = '/user/api/v3/jobs/update-job';
            var method= 'put';
            sendWithAjax(uri, method);
        }

        function getJob(){
            var uri = '/user/api/v3/jobs/get-jobs';
            var method= 'get';
            sendWithAjax(uri, method);
        }

        // Company

        function addCompany(){
            var uri = '/user/api/v3/companies/add-company';
            var method= 'post';
            sendWithAjax(uri, method);
        }

        function deleteCompany(){
            var uri = '/user/api/v3/companies/delete-company/1';
            var method= 'delete';
            sendWithAjax(uri, method);
        }

        function updateCompany(){
            var uri = '/user/api/v3/companies/update-company';
            var method= 'put';
            sendWithAjax(uri, method);
        }

        function getCompany(){
            var uri = '/user/api/v3/companies/get-companies';
            var method= 'get';
            sendWithAjax(uri, method);
        }

        // User

        function addUser(){
            var uri = '/user/api/v3/users/add-user';
            var method= 'post';
            sendWithAjax(uri, method);
        }

        function deleteUser(){
            var uri = '/user/api/v3/users/delete-user/1';
            var method= 'delete';
            sendWithAjax(uri, method);
        }

        function updateUser(){
            var uri = '/user/api/v3/users/update-user';
            var method= 'put';
            sendWithAjax(uri, method);
        }

        function getUser(){
            var uri = '/user/api/v3/users/get-users';
            var method= 'get';
            sendWithAjax(uri, method);
        }

        // Test Module

        function testApiA(){
            var uri = '/user/api/v3/test_module/test-api-a';
            var method= 'post';
            sendWithAjax(uri, method);
        }

        function testApiB(){
            var uri = '/user/api/v3/test_module/test-api-b';
            var method= 'get';
            sendWithAjax(uri, method);
        }
            

        $(function(){
            components.forEach(c => {
                $('#'+c.replaceAll(" ", "_")).hide();
            });
        });
    </script>
 
</body>
</html>