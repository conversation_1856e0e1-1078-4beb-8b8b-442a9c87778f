<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Tenant's Modules Management</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>

<body>
    <h2>Set Tenant Modules</h2>

    <h3></h2>
    <div style="margin:20px 0"></div>
    <div class="easyui-panel" style="width:100%;max-width:400px;padding:5px 20px;">
        <div style="margin-bottom:20px">
            <input id="tenant" class="easyui-combobox" name="tenant" style="width:100%;" data-options="
                    url:'http://localhost:8888/user/api/v3/users/get-tenants',
                    method:'get',
                    valueField: 'id',
                    textField: 'name',
                    label: 'Please select a tenant:',
                    labelPosition: 'top'
                    ">
        </div>

        <div style="margin:20px 0; display: none;">
        <a href="#" class="easyui-linkbutton" onclick="getSelected()">Get Selected</a> 
    </div>
    </div>


    <div style="margin:20px 0; display: none;">
        <a href="#" class="easyui-linkbutton" onclick="getCheckedIds()">GetChecked</a>
    </div>
    <div style="margin:10px 0; display: none;">
        <input type="checkbox" checked onchange="$('#tt').treegrid({cascadeCheck:$(this).is(':checked')})">CascadeCheck 
        <input type="checkbox" onchange="$('#tt').treegrid({onlyLeafCheck:$(this).is(':checked')})">OnlyLeafCheck
    </div>

    <h2></h2>


    

        <!-- <ul id="tt" 
            class="easyui-tree" 
            data-options="url:'http://localhost:8888/user/api/v3/users/get-privilege-tree',method:'get',animate:true,checkbox:true">
            
        </ul> -->

    <pre style="background-color: grey;width: 100%;height: 5px;"></pre>
    <h3>Please select permission scope:</h3>
    <form id="ff" method="post" enctype="multipart/form-data">
        <input id="job" value="2" class="easyui-switchbutton" label="Job:" style="width:100px;height:30px" 
                    data-options="
                    onText:'',
                    offText:''">
        
        <br><br>
        <input id="candidate" value="3" class="easyui-switchbutton" label="Candidate:" style="width:100px;height:30px" 
                    data-options="
                    onText:'',
                    offText:''">

        <br><br>
        <input id="company" value="4" class="easyui-switchbutton" label="Company:" style="width:100px;height:30px" 
                    data-options="
                    onText:'',
                    offText:''">
        
    </form>


    <div style="text-align:center;padding:5px 0;">
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="submitForm()" style="width:80px">Submit</a>
    </div>

    <script type="text/javascript">
        function getParent(node, nodeIds){
            var p = $('#tt').treegrid('getParent', node.id)
            if (p) {
                nodeIds.add(p.id);
                getParent(p, nodeIds);
            }
        }

        function getCheckedIds(){
            var nodes = $('#tt').treegrid('getCheckedNodes'); //$('#tt').tree('getChecked');
            var nodeIds = new Set()
            var s = '';
            for(var i=0; i<nodes.length; i++){
                var node = nodes[i];
                if (s != '') s += '\n';
                s += node.name;
                nodeIds.add(node.id);
                //getParent(node, nodeIds);
            }

            return nodeIds;
        }

        function getSelected(){
            var value = $('#user').combobox('getValue');
            //alert(value);
            
        }

        function submitForm(){
            
            $('#ff').form('submit');
        }

        $(function(){
            $('#ff').form({
                iframe: false,
                url: "http://localhost:8888/user/api/v3/users/set-tenant-modules",
                onSubmit: function(param){
                    // do some check
                    // return false to prevent submit;
                    var involvedModules = [];
                    var uninvolvedModules = [];
                    var jobId = parseInt($('#job').val());
                    var candidateId = parseInt($('#candidate').val());
                    var companyId = parseInt($('#company').val());
                    if ($('#job').switchbutton('options').checked) {
                        involvedModules.push(jobId);
                    }else{
                        uninvolvedModules.push(jobId);
                    }

                    if ($('#candidate').switchbutton('options').checked) {
                        involvedModules.push(candidateId);
                    }else{
                        uninvolvedModules.push(candidateId);
                    }

                    if ($('#company').switchbutton('options').checked) {
                        involvedModules.push(companyId);
                    }else{
                        uninvolvedModules.push(companyId);
                    }

                    param.tenantId = $('#tenant').combobox('getValue');
                    param.involvedModules = involvedModules;
                    param.uninvolvedModules = uninvolvedModules;
                },
                success:function(data){
                    //$.messager.alert('Info', data, 'info');
                    $.messager.show({
                        title:'',
                        msg:"Update successfully",
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });

            $('#role').combobox({
                onChange: function(newVal, oldVal){
                    var root = $('#tt').treegrid('getRoot');
                    //$('#tt').tree('uncheck', root.target);
                    $('#tt').treegrid('uncheckNode', root.id);
                    $.get(`http://localhost:8888/user/api/v3/users/role/${newVal}/privileges`, function(data, status){
                        //alert("Data: " + data + "\nStatus: " + status);
                        data.forEach(privilegeId => {
                            //var node = $('#tt').tree('find', privilegeId);
                            //$('#tt').tree('check', node.target);
                            var node = $('#tt').treegrid('find', privilegeId);
                            $('#tt').treegrid('checkNode', node.id);
                        });
                      });
                }
            });
        });
        
    </script>
</body>
</html>