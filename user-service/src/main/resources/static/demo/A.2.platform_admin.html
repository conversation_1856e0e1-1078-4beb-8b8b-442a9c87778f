<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title id="title">APN</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>

<body>
    <h1>
        <div id="login_user">
        
        </div>
    </h1>

    <div id="w" class="easyui-window" title="Login" data-options="iconCls:'icon-view'" style="width:400px;height:250px;padding:10px;">
        <form id="ff" method="post" enctype="multipart/form-data">
            <div style="margin-bottom:20px">
                <input class="easyui-textbox" id="username"  prompt="username" style="width:90%" data-options="required:true">
            </div>
            <div style="margin-bottom:20px">
                <input class="easyui-passwordbox" id="password" prompt="password" value="QWEqwe!@#123" style="width:90%" data-options="required:true">
            </div>
        </form>
        <div style="text-align:center;padding:5px 0">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="login()" style="width:80px">Login</a>
        </div>
    </div>

    <div id="tabs" class="easyui-tabs" style="width:1100px;height:400px">

        <div title="DB Table Management" style="padding:10px">
            <table id="dg_tables" class="easyui-datagrid"  title="DB Table Management" style="width:600px;height:300px"
                data-options="
                    iconCls: 'icon-view',
                    singleSelect: true,
                    toolbar: toolbar,
                    url: ''
                ">
                <thead>
                    <tr>
                        <th data-options="field:'id',width:50">ID</th>
                        <th data-options="field:'name',width:100">Name</th>
                        <th data-options="field:'userOwnerColumn',width:200">UserOwnerColumn</th>
                        <th data-options="field:'departmentOwnerColumn',width:200">TeamOwnerColumn</th>
                    </tr>
                </thead>
            </table>
        </div>

        <div title="System Module Management" style="padding:10px">
            <table>
                <tr>
                    <td>
                        <table id="dg_modules" class="easyui-datagrid"  title="System Module Management" style="width:300px;height:300px"
                            data-options="
                                iconCls: 'icon-view',
                                singleSelect: true,
                                toolbar: toolbar,
                                onDblClickRow: onDblClickRow,
                                url: ''
                            ">
                            <thead>
                                <tr>
                                    <th data-options="field:'id',width:100">ID</th>
                                    <th data-options="field:'name',width:250">Name</th>
                                </tr>
                            </thead>
                        </table>
                    </td>
                    <td>
                        <table id="dg_tables_by_module" class="easyui-datagrid"  title="DB Tables" style="width:300px;height:300px"
                            data-options="
                                iconCls: 'icon-view',
                                singleSelect: true,
                                toolbar: toolbar,
                                url: ''
                            ">
                            <thead>
                                <tr>
                                    <th data-options="field:'id',width:100">ID</th>
                                    <th data-options="field:'name',width:250">Name</th>
                                </tr>
                            </thead>
                        </table>
                    </td>
                </tr>
            </table>
        </div>

        <div title="Tenant Management" style="padding:10px">
            <table>
                <tr>
                    <td>
                        <table id="dg_tenants" class="easyui-datagrid"  title="Tenant Management" style="width:400px;height:300px"
                            data-options="
                                iconCls: 'icon-view',
                                singleSelect: true,
                                toolbar: toolbar,
                                onClickRow: onClickRow,
                                url: ''
                            ">
                            <thead>
                                <tr>
                                    <th data-options="field:'id',width:70">ID</th>
                                    <th data-options="field:'name',width:300">Name</th>
                                </tr>
                            </thead>
                        </table>
                    </td>
                    <td id="permission_scope" style="padding-left: 50px">
                        <h3>Set modules that invole data permission:</h3>
                        <br/>
                        <form id="ff" method="post" enctype="multipart/form-data">
                            <input id="job" value="2" class="easyui-switchbutton" labelWidth="100" label="Job:" style="width:80px;height:30px;color: blue" 
                                        data-options="
                                        onText:'ON',
                                        offText:'OFF'">
                            
                            <br><br>
                            <input id="candidate" value="3" class="easyui-switchbutton" labelWidth="100" label="Candidate:" style="width:80px;height:30px" 
                                        data-options="
                                        onText:'ON',
                                        offText:'OFF'">

                            <br><br>
                            <input id="company" value="4" class="easyui-switchbutton" labelWidth="100" label="Company:" style="width:80px;height:30px" 
                                        data-options="
                                        onText:'ON',
                                        offText:'OFF'">
                            
                        </form>
                        <br/><br/><br/>
                        <div style="text-align:center;padding:5px 0;">
                            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="submitForm()" style="width:80px">Submit</a>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div title="Tenant Admin Management" style="padding:10px">
            <table id="dg_tenant_admins" class="easyui-datagrid"  title="Tenant Admin Management" style="width:500px;height:300px"
                data-options="
                    iconCls: 'icon-view',
                    singleSelect: true,
                    toolbar: toolbar,
                    url: ''
                ">
                <thead>
                    <tr>
                        <th data-options="field:'id',width:100">ID</th>
                        <th data-options="field:'username',width:200">Username</th>
                        <th data-options="field:'tenant.name',width:200, formatter:function(value,row){return row.tenant.name}">Tenant</th>
                    </tr>
                </thead>
            </table>
        </div>

        <div title="Platform Admin Management" style="padding:10px">
            <table id="dg_platform_admins" class="easyui-datagrid"  title="Platform Admin Management" style="width:400px;height:300px"
                data-options="
                    iconCls: 'icon-view',
                    singleSelect: true,
                    toolbar: toolbar,
                    url: ''
                ">
                <thead>
                    <tr>
                        <th data-options="field:'id',width:100">ID</th>
                        <th data-options="field:'username',width:250">Username</th>
                    </tr>
                </thead>
            </table>
        </div>
        
    </div>


    <script type="text/javascript">
        var access_token;
        var tenantId = undefined;

        var toolbar = [{
                text:'Add',
                iconCls:'icon-add',
                handler:function(){
                    $('#dg').datagrid('endEdit', editIndex);
                }
            },'-',{
                text:'Delete',
                iconCls:'icon-remove',
                handler:function(){
                    if (editIndex == undefined){
                        alert('please selelct a row');
                        return;
                    }
                    
                }
            },'-',{
                text:'Refresh',
                iconCls:'icon-reload',
                handler:function(){
                    getMyJobs();
                }
            }];

        function onClickRow(index, row){
            tenantId = row.id;
            $('#permission_scope').show();
        }

        function onDblClickRow(index, row){
            getModuleTables(row.id);    
        }
        
        function login(){
            var payload = {
                "username": $('#username').textbox('getValue'),
                "password": $('#password').textbox('getValue') //"QWEqwe!@#123"
            };

            $.ajax({
                url: 'http://localhost:8888/user/api/v3/account/admin/login',
                dataType: 'text',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify(payload),
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    access_token = obj.credential.access_token;
                    getPlatformAdmins();
                    getModules();
                    getTables();
                    getTenants();
                    getTenantAdmin();
                    $('#w').window('close');
                    $('#tabs').show();
                    $('#login_user').text('当前登录用户：'+payload.username);
                    $('#title').text(payload.username);
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Error', "Login failed", 'error');
                }
            });
        }

        function getPlatformAdmins(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/admin/platform-admins',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_platform_admins').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }

        function getModules(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/admin/modules',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_modules').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }

        function getModuleTables(moduleId){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/admin/module/'+moduleId+'/tables',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_tables_by_module').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }

        function getTables(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/admin/tables',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_tables').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }

        function getTenants(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/admin/tenants',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_tenants').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }

        function getTenantAdmin(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/admin/tenant-admins',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_tenant_admins').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }

        function submitForm(){
            var param = {};
            var involvedModules = [];
            var uninvolvedModules = [];
            var jobId = parseInt($('#job').val());
            var candidateId = parseInt($('#candidate').val());
            var companyId = parseInt($('#company').val());
            if ($('#job').switchbutton('options').checked) {
                involvedModules.push(jobId);
            }else{
                uninvolvedModules.push(jobId);
            }

            if ($('#candidate').switchbutton('options').checked) {
                involvedModules.push(candidateId);
            }else{
                uninvolvedModules.push(candidateId);
            }

            if ($('#company').switchbutton('options').checked) {
                involvedModules.push(companyId);
            }else{
                uninvolvedModules.push(companyId);
            }
            param.tenantId = tenantId;
            param.involvedModules = involvedModules;
            param.uninvolvedModules = uninvolvedModules;
            
            //$('#ff').form('submit');
            $.ajax({
                url: "http://localhost:8888/user/api/v3/permissions/admin/set-tenant-modules",
                dataType: 'text',
                data: JSON.stringify(param),
                type: 'post',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_tenants').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }
        

        $(function(){
            $('#tabs').hide();
            $('#permission_scope').hide();

            $('#ff').form({
                iframe: false,
                url: "http://localhost:8888/user/api/v3/permissions/admin/set-tenant-modules",
                onSubmit: function(param){
                    // do some check
                    // return false to prevent submit;
                    var involvedModules = [];
                    var uninvolvedModules = [];
                    var jobId = parseInt($('#job').val());
                    var candidateId = parseInt($('#candidate').val());
                    var companyId = parseInt($('#company').val());
                    if ($('#job').switchbutton('options').checked) {
                        involvedModules.push(jobId);
                    }else{
                        uninvolvedModules.push(jobId);
                    }

                    if ($('#candidate').switchbutton('options').checked) {
                        involvedModules.push(candidateId);
                    }else{
                        uninvolvedModules.push(candidateId);
                    }

                    if ($('#company').switchbutton('options').checked) {
                        involvedModules.push(companyId);
                    }else{
                        uninvolvedModules.push(companyId);
                    }
                    param.tenantId = tenantId;
                    param.involvedModules = involvedModules;
                    param.uninvolvedModules = uninvolvedModules;
                },
                success:function(data){
                    //$.messager.alert('Info', data, 'info');
                    $.messager.show({
                        title:'',
                        msg:"Updated successfully",
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });

        });
    </script>

</body>
</html>