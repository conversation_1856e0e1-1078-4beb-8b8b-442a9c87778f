<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>User Role Management</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>

<body>
    <h2>Set User Role</h2>

    <h3></h2>
    <div style="margin:20px 0"></div>
    <div class="easyui-panel" style="width:100%;max-width:500px;padding:30px 60px;">
        <div style="margin-bottom:20px">
            <input id="user" class="easyui-combobox" name="user" style="width:100%;" data-options="
                    url:'http://localhost:8888/user/api/v3/users/get-all-users',
                    method:'get',
                    valueField: 'id',
                    textField: 'text',
                    label: 'Please select an user:',
                    labelPosition: 'top'
                    ">
        </div>

        <div style="margin:20px 0; display: none;">
            <a href="#" class="easyui-linkbutton" onclick="getSelected()">Get Selected</a> 
        </div>
    </div>


    <div style="margin:20px 0; display: none;">
        <a href="#" class="easyui-linkbutton" onclick="getCheckedIds()">GetChecked</a> 
    </div>
    <div style="margin:10px 0; display: none;">
        <input type="checkbox" checked onchange="$('#tt').tree({cascadeCheck:$(this).is(':checked')})">CascadeCheck 
        <input type="checkbox" onchange="$('#tt').tree({onlyLeafCheck:$(this).is(':checked')})">OnlyLeafCheck
    </div>

    <h3>Please select roles</h2>

    <div class="easyui-panel" style="padding:5px">
        <ul id="tt" 
            class="easyui-tree" 
            data-options="url:'http://localhost:8888/user/api/v3/users/get-roles-tree',method:'get',animate:true,checkbox:true">
            
        </ul>
    </div>

    <form id="ff" method="post" enctype="multipart/form-data">
        
    </form>

    <div style="text-align:center;padding:5px 0">
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="submitForm()" style="width:80px">Submit</a>
        
    </div>

    <script type="text/javascript">
        function getCheckedIds(){
            var nodes = $('#tt').tree('getChecked'); //$('#tt').tree('getChecked');
            var nodeIds = new Set()
            var s = '';
            for(var i=0; i<nodes.length; i++){
                var node = nodes[i];
                if (s != '') s += '\n';
                s += node.text;
                nodeIds.add(node.id);
            }
            //alert(s);
            //alert(nodeIds.size);
            return nodeIds;
        }

        function getSelected(){
            var value = $('#user').combobox('getValue');
            alert(value);
        }

        function submitForm(){
            
            $('#ff').form('submit');
        }

        $(function(){
            $('#ff').form({
                iframe: false,
                url: "http://localhost:8888/user/api/v3/users/set-user-role",
                onSubmit: function(param){
                    // do some check
                    // return false to prevent submit;
                    param.userId = $('#user').combobox('getValue');
                    param.roleIds = Array.from(getCheckedIds());
                },
                success:function(data){
                    //$.messager.alert('Info', data, 'info');
                    $.messager.show({
                        title:'',
                        msg:"Update successfully",
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                }
            });

            $('#user').combobox({
                onSelect: function(record){
                    var roots = $('#tt').tree('getRoots');
                    roots.forEach(node => {
                        $('#tt').tree('uncheck', node.target);
                    });
                    
                    $.get(`http://localhost:8888/user/api/v3/users/user/${record.id}/roles`, function(data, status){
                        //alert("Data: " + data + "\nStatus: " + status);
                        data.forEach(roleId => {
                            var node = $('#tt').tree('find', roleId);
                            $('#tt').tree('check', node.target);
                        });
                    });
                }
            });
        });
        
    </script>
</body>
</html>