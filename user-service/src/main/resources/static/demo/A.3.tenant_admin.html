<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title id="title">APN - TenantAdmin</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>
<body>
    <h1>
        <div id="login_user">
        
        </div>
    </h1>

    <div id="w" class="easyui-window" title="Login" data-options="iconCls:'icon-view'" style="width:400px;height:250px;padding:10px;">
        <form id="ff" method="post" enctype="multipart/form-data">
            <div style="margin-bottom:20px">
                <input class="easyui-textbox" id="username"  prompt="username" style="width:90%" data-options="required:true">
            </div>
            <div style="margin-bottom:20px">
                <input class="easyui-passwordbox" id="password" prompt="password" value="QWEqwe!@#123" style="width:90%" data-options="required:true">
            </div>
        </form>
        <div style="text-align:center;padding:5px 0">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="login()" style="width:80px">Login</a>
        </div>
    </div>

    <div id="w_users" class="easyui-window" title="Users" data-options="iconCls:'icon-view'" style="width:400px;height:250px;padding:10px;">
        <form id="ff" method="post" enctype="multipart/form-data">
            <div style="margin-bottom:20px">
                <input id="user_team" class="easyui-combobox" name="user" style="width:100%;" data-options="
                        url:'http://localhost:8888/user/api/v3/permissions/users/all',
                        method:'get',
                        valueField: 'id',
                        textField: 'text',
                        label: 'Please select an user:',
                        labelPosition: 'top'
                        ">
            </div>
        </form>
        <div style="text-align:center;padding:5px 0">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="addUserToTeam()" style="width:80px">Confirm</a>
        </div>
    </div>

    <div id="tabs" class="easyui-tabs" style="width:100%;height:800px">
        

        <div title="Team Management" style="padding:10px">
            <table>
                <tbody>
                    <tr>
                        <td>
                            <table id="tg_teams" class="easyui-treegrid" title="Teams" style="width:400px;height:300px"
                                data-options="
                                    iconCls: 'icon-ok',
                                    rownumbers: true,
                                    animate: true,
                                    collapsible: true,
                                    fitColumns: true,
                                    url: 'http://localhost:8888/user/api/v3/permissions/teams',
                                    method: 'get',
                                    idField: 'id',
                                    treeField: 'name',
                                    onDblClickCell: function(field,row){
                                       if(row.isLeaf){
                                            var id = row.id;
                                            $('#input_team_id').val(id);
                                            var api = 'http://localhost:8888/user/api/v3/permissions/team-members/'+id;
                                            $('#dg_team_memgers').datagrid('load', api);
                                       }
                                    }
                                ">
                                 <input type="hidden" id="input_team_id" name="" value="">
                                <thead>
                                    <tr>
                                        <th data-options="field:'name',width:45,editor:'text'">Name</th>
                                    </tr>
                                </thead>
                            </table>
                        </td>
                        <td>
                            <table id="dg_team_memgers" class="easyui-datagrid" title="Team Members" style="width:700px;height:300px"
                                data-options="singleSelect:true,collapsible:true,url:'',method:'get', toolbar: toolbar_add_teammates">
                                <thead>
                                    <tr>
                                        <th data-options="field:'id',width:80,align:'center'">ID</th>
                                        <th data-options="field:'username',width:100,align:'center'">Username</th>
                                        <th data-options="field:'email',width:300,align:'center'">Email</th>
                                    </tr>
                                </thead>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div title="Role Management" style="padding:10px">
            <table id="dg_role" title="" style="width:400px;height:300px"
                data-options="
                    iconCls: 'icon-view',
                    singleSelect: true,
                    toolbar: toolbar,
                    url: ''
                ">
                <thead>
                    <tr>
                        <th data-options="field:'id',width:100">ID</th>
                        <th data-options="field:'name',width:250">Name</th>
                    </tr>
                </thead>
            </table>
        </div>

        <div title="User Role Management" style="padding:10px">
            <div class="easyui-panel" style="width:100%;max-width:500px;padding:30px 60px;">
                <div style="margin-bottom:20px">
                    <input id="user" class="easyui-combobox" name="user" style="width:100%;" data-options="
                            url:'http://localhost:8888/user/api/v3/permissions/users/all',
                            method:'get',
                            valueField: 'id',
                            textField: 'text',
                            label: 'Please select an user:',
                            labelPosition: 'top'
                            ">
                </div>

                <h3>Please select roles</h2>

                <div class="easyui-panel" style="padding:5px">
                    <ul id="tt_role" 
                        class="easyui-tree" 
                        data-options="url:'http://localhost:8888/user/api/v3/permissions/roles-tree',method:'get',animate:true,checkbox:true">
                        
                    </ul>
                </div>

                <form id="ff_user_role" method="post" enctype="multipart/form-data">
                    
                </form>

                <div style="text-align:center;padding:5px 0">
                    <a href="javascript:void(0)" class="easyui-linkbutton" onclick="submitUserRoleForm()" style="width:80px">Submit</a>
                    
                </div>
            </div>
        </div>

        <div title="Role Data Permission Management" style="padding:10px">
            <div class="easyui-panel" style="width:100%;max-width:400px;padding:5px 20px;">
                <div style="margin-bottom:20px">
                    <input id="role" class="easyui-combobox" name="role" style="width:100%;" data-options="
                            url:'http://localhost:8888/user/api/v3/permissions/roles',
                            method:'get',
                            valueField: 'id',
                            textField: 'name',
                            label: 'Please select a role:',
                            labelPosition: 'top'
                            ">
                </div>
            </div>

            <pre style="background-color: grey;width: 500px;height: 5px;"></pre>
            <h3>Please select permission scope:</h3>
            <form id="ff_role_permission" method="post" enctype="multipart/form-data">
                <div style="margin-bottom:20px">
                    <input id="ds_1" class="easyui-radiobutton" name="data_scope" value="1" label=""> &nbsp;&nbsp;&nbsp;&nbsp;The user only
                </div>
                <div style="margin-bottom:20px">
                    <input id="ds_2" class="easyui-radiobutton" name="data_scope" value="2" label="">&nbsp;&nbsp;&nbsp;&nbsp;The team that user assigned to
                </div>
                <div style="margin-bottom:20px">
                    <input id="ds_3" class="easyui-radiobutton" name="data_scope" value="3" 
                        label=""
                        data-options="
                            onChange: function(checked){
                                if(checked){
                                    $('#div_tg_role_permission').show();
                                    $('#tt_role_permission').treegrid('reload');
                                }else{
                                    $('#div_tg_role_permission').hide();
                                }
                            }
                        ">&nbsp;&nbsp;&nbsp;&nbsp;The assigned team & the specific scope as blow:

                    <input id="sb_role_permission" class="easyui-switchbutton" style="width:100px;height:30px" 
                            data-options="
                            onText:'Modify',
                            offText:'ReadOnly'">

                    <div id="div_tg_role_permission" style="display: none;">
                        <table id="tt_role_permission"  title="" class="easyui-treegrid" style="width:300px;height:260px"
                            data-options="
                                url: 'http://localhost:8888/user/api/v3/permissions/teams',
                                method: 'get',
                                checkbox: true,
                                rownumbers: true,
                                idField: 'id',
                                treeField: 'name'
                            ">
                            <thead>
                                <tr>
                                    <th data-options="field:'name'" width="280">Team</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>

                <div style="margin-bottom:20px">
                    <input id="ds_99_role_permission" class="easyui-radiobutton" name="data_scope" value="99" label="">&nbsp;&nbsp;&nbsp;&nbsp;All
                </div>

                <div style="text-align:left;padding-left:100px;padding-top: 50px">
                    <a href="javascript:void(0)" class="easyui-linkbutton" onclick="submitRolePermissionForm()" style="width:80px">Submit</a>
                </div>
            </form>
            
        </div>

        <div title="User Data Permission Management" style="padding:10px">
            <div class="easyui-panel" style="width:100%;max-width:400px;padding:5px 20px;">
                <div style="margin-bottom:20px">
                    <input id="user_permission" class="easyui-combobox" name="user_permission" style="width:100%;" data-options="
                            url:'http://localhost:8888/user/api/v3/permissions/users',
                            method:'get',
                            valueField: 'id',
                            textField: 'username',
                            label: 'Please select an user:',
                            labelPosition: 'top'
                            ">
                </div>

            </div>

            <pre style="background-color: grey;width: 500px;height: 5px;"></pre>
            <h3>Please select permission scope:</h3>
            <form id="ff_user_permission" method="post" enctype="multipart/form-data">
                <div style="margin-bottom:20px">
                    <input id="user_permission_ds_1" class="easyui-radiobutton" name="data_scope" value="1" label=""> &nbsp;&nbsp;&nbsp;&nbsp;The user only
                </div>
                <div style="margin-bottom:20px">
                    <input id="user_permission_ds_2" class="easyui-radiobutton" name="data_scope" value="2" label="">&nbsp;&nbsp;&nbsp;&nbsp;The team that user assigned to
                </div>
                <div style="margin-bottom:20px">
                    <input id="user_permission_ds_3" class="easyui-radiobutton" name="data_scope" value="3" 
                        label=""
                        data-options="
                            onChange: function(checked){
                                if(checked){
                                    $('#div_tg_user_permission').show();
                                    $('#tt_user_permission').treegrid('reload');
                                }else{
                                    $('#div_tg_user_permission').hide();
                                }
                            }
                        ">&nbsp;&nbsp;&nbsp;&nbsp;The assigned team & the specific scope as blow:

                    <input id="sb_user_permission" class="easyui-switchbutton" style="width:100px;height:30px" 
                            data-options="
                            onText:'Modify',
                            offText:'ReadOnly'">

                    <div id="div_tg_user_permission" style="display: none;">
                        <table id="tt_user_permission"  title="" class="easyui-treegrid" style="width:300px;height:260px"
                            data-options="
                                url: 'http://localhost:8888/user/api/v3/permissions/teams',
                                method: 'get',
                                checkbox: true,
                                rownumbers: true,
                                idField: 'id',
                                treeField: 'name'
                            ">
                            <thead>
                                <tr>
                                    <th data-options="field:'name'" width="280">Team</th>
                                </tr>
                            </thead>
                        </table>
                    </div>
                </div>

                <div style="margin-bottom:20px">
                    <input id="ds_99_user_permission" class="easyui-radiobutton" name="data_scope" value="99" label="">&nbsp;&nbsp;&nbsp;&nbsp;All
                </div>

                <div style="text-align:left;padding-left:100px;padding-top: 50px">
                    <a href="javascript:void(0)" class="easyui-linkbutton" onclick="submitUserPermissionForm()" style="width:80px">Submit</a>
                </div>
            </form>
        </div>

        <div title="User Role Privilege Management" style="padding:10px">
            <div class="easyui-panel" style="width:100%;max-width:400px;padding:5px 20px;">
                <div style="margin-bottom:20px">
                    <input id="role_user_role_privilege" class="easyui-combobox" name="role" style="width:100%;" data-options="
                            url:'http://localhost:8888/user/api/v3/permissions/roles',
                            method:'get',
                            valueField: 'id',
                            textField: 'name',
                            label: 'Please select a role:',
                            labelPosition: 'top'
                            ">
                </div>
            </div>

            <table id="tt_user_role_privilege"  title="Please select privileges" class="easyui-treegrid" style="width:700px;height:750px"
                    data-options="
                        url: 'http://localhost:8888/user/api/v3/permissions/privilege-tree',
                        method: 'get',
                        checkbox: true,
                        rownumbers: true,
                        idField: 'id',
                        treeField: 'name'
                    ">
                <thead>
                    <tr>
                        <th data-options="field:'name'" width="280">Name</th>
                        <th data-options="field:'api'" width="400" align="left">API</th>
                    </tr>
                </thead>
            </table>
                
            <form id="ff_user_role_privilege" method="post" enctype="multipart/form-data">
                
            </form>

            <div style="text-align:center;padding:5px 0">
                <a href="javascript:void(0)" class="easyui-linkbutton" onclick="submitForm()" style="width:80px">Submit</a>
                
            </div>
        </div>
        
    </div>
    
    <!-- Tean & Role Management-->
    <script type="text/javascript">
        var access_token;
        var tenantId = undefined;

        var toolbar = [{
            text:'Add',
            iconCls:'icon-add',
            handler:function(){
                $('#dg').datagrid('endEdit', editIndex);
            }
        },'-',{
            text:'Delete',
            iconCls:'icon-remove',
            handler:function(){
                
            }
        },'-',{
            text:'Refresh',
            iconCls:'icon-reload',
            handler:function(){
                getMyJobs();
            }
        }];

        var toolbar_add_teammates = [{
            text:'Add',
            iconCls:'icon-add',
            handler:function(){
                $('#w_users').window('open');
            }
        },'-',{
            text:'Delete',
            iconCls:'icon-remove',
            handler:function(){
                removeUserFromTeam();
            }
        },'-',{
            text:'Refresh',
            iconCls:'icon-reload',
            handler:function(){
                team_id = $('#input_team_id').val();
                var api = 'http://localhost:8888/user/api/v3/permissions/team-members/'+team_id;
                $('#dg_team_memgers').datagrid('load', api);
            }
        }];
        
        function onClickRow(index, row){
            tenantId = row.id;
            $('#permission_scope').show();
        }
        
        function login(){
            var payload = {
                "username": $('#username').textbox('getValue'),
                "password": $('#password').textbox('getValue')
            };

            $.ajax({
                url: 'http://localhost:8888/user/api/v3/login',
                dataType: 'text',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify(payload),
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    access_token = obj.credential.access_token;
                    //getMyJobs();
                    $('#w').window('close');
                    $('#tabs').show();
                    $('#login_user').text('当前登录用户：'+payload.username);
                    $('#title').text(payload.username);
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Error', "Login failed", 'error');
                }
            });
        }

        function addUserToTeam(){
        	var team_id = $('#input_team_id').val();
            var user_id = $('#user_team').combobox('getValue');
            var uri = '/user/api/v3/permissions/team/'+team_id+'/users';
            var method= 'post';
            var data = {
                'userIds': [user_id]
            }
            sendWithAjax(uri, method, data);
            
            $('#w_users').window('close');
        }

        function removeUserFromTeam(){
        	var team_id = $('#input_team_id').val();
            var user_id = $('#dg_team_memgers').datagrid('getSelected').id;
            var uri = '/user/api/v3/permissions/team/'+team_id+'/users';
            var data = {
                'userIds': [user_id]
            }
            var method= 'delete';
            sendWithAjax(uri, method, data);
        }

        function sendWithAjax(uri, method, data){
            $.ajax({
                url: 'http://localhost:8888' + uri,
                dataType: 'text',
                data: JSON.stringify(data),
                type: method,
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    //var obj = JSON.parse(data)
                    $.messager.show({
                        title:'',
                        msg:data,
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                    var team_id = $('#input_team_id').val();
                    var api = 'http://localhost:8888/user/api/v3/permissions/team-members/'+team_id;
            		$('#dg_team_memgers').datagrid('load', api);
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    
                    $.messager.alert('Error', "NO PERMISSION", 'error');
                }
            });
        }
        
        $(function(){
            $('#w_users').window('close');
            $('#tabs').hide();

            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/roles',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                //headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_role').datagrid({data: obj});
                    
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', 'Status Code: '+textStatus, 'info');
                }
            });
        });

    </script>


    <!--User Role Management-->
    <script type="text/javascript">
        function getCheckedIds(){
            var nodes = $('#tt_role').tree('getChecked'); //$('#tt_role').tree('getChecked');
            var nodeIds = new Set()
            var s = '';
            for(var i=0; i<nodes.length; i++){
                var node = nodes[i];
                if (s != '') s += '\n';
                s += node.text;
                nodeIds.add(node.id);
            }
            //alert(s);
            //alert(nodeIds.size);
            return nodeIds;
        }

        function getSelected(){
            var value = $('#user').combobox('getValue');
            alert(value);
        }

        function submitUserRoleForm(){
            
            //$('#ff_user_role').form('submit');

            var userId = $('#user').combobox('getValue');
            var roleIds = Array.from(getCheckedIds());
            var uri = '/user/api/v3/permissions/set-user-role';
            var data = {
                'userId': userId,
                'roleIds': roleIds
            }
            var method = 'post';
            sendWithAjax(uri, method, data);
        }

        $(function(){
            $('#ff_user_role').form({
                iframe: false,
                url: "http://localhost:8888/user/api/v3/permissions/set-user-role",
                onSubmit: function(param){
                    // do some check
                    // return false to prevent submit;
                    param.userId = $('#user').combobox('getValue');
                    param.roleIds = Array.from(getCheckedIds());
                },
                success:function(data){
                    //$.messager.alert('Info', data, 'info');
                    $.messager.show({
                        title:'',
                        msg:"Updated successfully",
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                }
            });

            $('#user').combobox({
                onSelect: function(record){
                    var roots = $('#tt_role').tree('getRoots');
                    roots.forEach(node => {
                        $('#tt_role').tree('uncheck', node.target);
                    });
                    
                    $.get(`http://localhost:8888/user/api/v3/permissions/user/${record.id}/roles`, function(data, status){
                        //alert("Data: " + data + "\nStatus: " + status);
                        data.forEach(roleId => {
                            var node = $('#tt_role').tree('find', roleId);
                            $('#tt_role').tree('check', node.target);
                        });
                    });
                }
            });
        });
        
    </script>
 
    <!--Role Data Permission Management-->
    <script type="text/javascript">
        function getRolePermissionParent(node, nodeIds){
            var p = $('#tt_role_permission').treegrid('getParent', node.id)
            if (p) {
                nodeIds.add(p.id);
                getRolePermissionParent(p, nodeIds);
            }
        }

        function getRolePermissionCheckedIds(){
            var nodes = $('#tt_role_permission').treegrid('getCheckedNodes'); //$('#tt').tree('getChecked');
            var nodeIds = new Set()
            var s = '';
            for(var i=0; i<nodes.length; i++){
                var node = nodes[i];
                if (s != '') s += '\n';
                s += node.name;
                nodeIds.add(node.id);
                //getParent(node, nodeIds);
            }

            return nodeIds;
        }

        function submitRolePermissionForm(){
            
            //$('#ff_role_permission').form('submit');
            var uri = '/user/api/v3/permissions/set-role-data-permission';
            var param = {};
            param.roleId = $('#role').combobox('getValue');
            //alert('roleId='+param.roleId);
            var data_scope = 0;
            if($('#ds_1').radiobutton('options').checked){
                data_scope = 1;
            }else if ($('#ds_2').radiobutton('options').checked){
                data_scope = 2;
            }else if ($('#ds_3').radiobutton('options').checked){
                data_scope = 3;
                param.teamIds = Array.from(getRolePermissionCheckedIds());
                param.modifiable = $('#sb_role_permission').switchbutton('options').checked;
            }else if ($('#ds_99_role_permission').radiobutton('options').checked){
                data_scope = 99;
            }
            param.dataScope = data_scope;
            var method = 'post';
            sendWithAjax(uri, method, param);
        }

        $(function(){
            $('#ff_role_permission').form({
                iframe: false,
                url: "http://localhost:8888/user/api/v3/permissions/set-role-data-permission",
                onSubmit: function(param){
                    // do some check
                    // return false to prevent submit;
                    param.roleId = $('#role').combobox('getValue');
                    //alert('roleId='+param.roleId);
                    data_scope = 0;
                    if($('#ds_1').radiobutton('options').checked){
                        data_scope = 1;
                    }else if ($('#ds_2').radiobutton('options').checked){
                        data_scope = 2;
                    }else if ($('#ds_3').radiobutton('options').checked){
                        data_scope = 3;
                        param.departmentIds = Array.from(getRolePermissionCheckedIds());
                        param.modifiable = $('#sb_role_permission').switchbutton('options').checked;
                    }else if ($('#ds_99_role_permission').radiobutton('options').checked){
                        data_scope = 99;
                    }
                    param.dataScope = data_scope;
                },
                success:function(data){
                    //$.messager.alert('Info', data, 'info');
                    $.messager.show({
                        title:'',
                        msg:"Updated successfully",
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });

            $('#role').combobox({
                onChange: function(newVal, oldVal){
                    var root = $('#tt_role_permission').treegrid('getRoot');
                    //$('#tt').tree('uncheck', root.target);
                    $('#tt_role_permission').treegrid('uncheckNode', root.id);
                    $.get(`http://localhost:8888/user/api/v3/permissions/role/${newVal}/privileges`, function(data, status){
                        //alert("Data: " + data + "\nStatus: " + status);
                        data.forEach(privilegeId => {
                            //var node = $('#tt').tree('find', privilegeId);
                            //$('#tt').tree('check', node.target);
                            var node = $('#tt_role_permission').treegrid('find', privilegeId);
                            $('#tt_role_permission').treegrid('checkNode', node.id);
                        });
                      });
                }
            });
        });
        
    </script>

    <!--User Data Permission Management-->
    <script type="text/javascript">
        function getUserPermissionParent(node, nodeIds){
            var p = $('#tt_user_permission').treegrid('getParent', node.id)
            if (p) {
                nodeIds.add(p.id);
                getUserPermissionParent(p, nodeIds);
            }
        }

        function getUserPermissionCheckedIds(){
            var nodes = $('#tt_user_permission').treegrid('getCheckedNodes'); //$('#tt').tree('getChecked');
            var nodeIds = new Set()
            var s = '';
            for(var i=0; i<nodes.length; i++){
                var node = nodes[i];
                if (s != '') s += '\n';
                s += node.name;
                nodeIds.add(node.id);
                //getParent(node, nodeIds);
            }

            return nodeIds;
        }

        function submitUserPermissionForm(){
            
            //$('#ff_user_permission').form('submit');
            var uri = '/user/api/v3/permissions/set-user-data-permission';
            var param = {};
            param.userId = $('#user_permission').combobox('getValue');
            var data_scope = 0;
            if($('#user_permission_ds_1').radiobutton('options').checked){
                data_scope = 1;
            }else if ($('#user_permission_ds_2').radiobutton('options').checked){
                data_scope = 2;
            }else if ($('#user_permission_ds_3').radiobutton('options').checked){
                data_scope = 3;
                param.teamIds = Array.from(getUserPermissionCheckedIds());
                param.modifiable = $('#sb_user_permission').switchbutton('options').checked;
            }else if ($('#ds_99_user_permission').radiobutton('options').checked){
                data_scope = 99;
            }
            param.dataScope = data_scope;
            var method = 'post';
            sendWithAjax(uri, method, param);
        }

        $(function(){
            $('#ff_user_permission').form({
                iframe: false,
                url: "http://localhost:8888/user/api/v3/permissions/set-user-data-permission",
                onSubmit: function(param){
                    // do some check
                    // return false to prevent submit;
                    param.userId = $('#user_permission').combobox('getValue');
                    //alert('roleId='+param.roleId);
                    data_scope = 0;
                    if($('#user_permission_ds_1').radiobutton('options').checked){
                        data_scope = 1;
                    }else if ($('#user_permission_ds_2').radiobutton('options').checked){
                        data_scope = 2;
                    }else if ($('#user_permission_ds_3').radiobutton('options').checked){
                        data_scope = 3;
                        param.departmentIds = Array.from(getUserPermissionCheckedIds());
                        param.modifiable = $('#sb_user_permission').switchbutton('options').checked;
                    }else if ($('#ds_99_user_permission').radiobutton('options').checked){
                        data_scope = 99;
                    }
                    param.dataScope = data_scope;
                },
                success:function(data){
                    //$.messager.alert('Info', data, 'info');
                    $.messager.show({
                        title:'',
                        msg:"Updated successfully",
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });

            $('#role').combobox({
                onChange: function(newVal, oldVal){
                    var root = $('#tt_user_permission').treegrid('getRoot');
                    //$('#tt').tree('uncheck', root.target);
                    $('#tt_user_permission').treegrid('uncheckNode', root.id);
                    $.get(`http://localhost:8888/user/api/v3/permissions/role/${newVal}/privileges`, function(data, status){
                        //alert("Data: " + data + "\nStatus: " + status);
                        data.forEach(privilegeId => {
                            //var node = $('#tt').tree('find', privilegeId);
                            //$('#tt').tree('check', node.target);
                            var node = $('#tt_user_permission').treegrid('find', privilegeId);
                            $('#tt_user_permission').treegrid('checkNode', node.id);
                        });
                      });
                }
            });
        });
        
    </script>

    <script type="text/javascript">
        function getParentPrivilege(node, nodeIds){
            var p = $('#tt_user_role_privilege').treegrid('getParent', node.id)
            if (p) {
                nodeIds.add(p.id);
                getParentPrivilege(p, nodeIds);
            }
        }

        function getCheckedPrivilegeIds(){
            var nodes = $('#tt_user_role_privilege').treegrid('getCheckedNodes'); //$('#tt').tree('getChecked');
            var nodeIds = new Set()
            var s = '';
            for(var i=0; i<nodes.length; i++){
                var node = nodes[i];
                if (s != '') s += '\n';
                s += node.name;
                nodeIds.add(node.id);
                getParentPrivilege(node, nodeIds);
                
            }

            return nodeIds;
        }

        function submitForm(){
            //$('#ff_user_role_privilege').form('submit');
            var uri = '/user/api/v3/permissions/set-role-privilege';
            var param = {};
            param.roleId = $('#role_user_role_privilege').combobox('getValue');
            param.privilegeIds = Array.from(getCheckedPrivilegeIds());
            var method = 'post';
            sendWithAjax(uri, method, param);
        }

        $(function(){
            $('#ff_user_role_privilege').form({
                iframe: false,
                url: "http://localhost:8888/user/api/v3/permissions/set-role-privilege",
                onSubmit: function(param){
                    // do some check
                    // return false to prevent submit;
                    param.roleId = $('#role_user_role_privilege').combobox('getValue');
                    param.privilegeIds = Array.from(getCheckedPrivilegeIds());
                },
                success:function(data){
                    //$.messager.alert('Info', data, 'info');
                    $.messager.show({
                        title:'',
                        msg:"Update successfully",
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });

            $('#role_user_role_privilege').combobox({
                onChange: function(newVal, oldVal){
                    var root = $('#tt_user_role_privilege').treegrid('getRoot');
                    //$('#tt').tree('uncheck', root.target);
                    $('#tt_user_role_privilege').treegrid('uncheckNode', root.id);
                    $.get(`http://localhost:8888/user/api/v3/permissions/role/${newVal}/privileges`, function(data, status){
                        //alert("Data: " + data + "\nStatus: " + status);
                        data.forEach(privilegeId => {
                            //var node = $('#tt').tree('find', privilegeId);
                            //$('#tt').tree('check', node.target);
                            var node = $('#tt_user_role_privilege').treegrid('find', privilegeId);
                            $('#tt_user_role_privilege').treegrid('checkNode', node.id);
                        });
                      });
                }
            });
        });
        
    </script>
</body>
</html>