<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Role Management</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>
<body>
    <h2>Role Management</h2>
    <p>Select one node and click edit button to perform editing.</p>
    
    <table id="dg" title="" style="width:400px;height:300px"
            data-options="
                iconCls: 'icon-view',
                singleSelect: true,
                toolbar: toolbar,
                url: ''
            ">
            <thead>
                <tr>
                    <th data-options="field:'id',width:100">ID</th>
                    <th data-options="field:'name',width:250">Name</th>
                </tr>
            </thead>
        </table>

    <script type="text/javascript">
        var toolbar = [{
                text:'Add',
                iconCls:'icon-add',
                handler:function(){
                    $('#dg').datagrid('endEdit', editIndex);
                }
            },'-',{
                text:'Delete',
                iconCls:'icon-remove',
                handler:function(){
                    if (editIndex == undefined){
                        alert('please selelct a row');
                        return;
                    }
                    //alert(editIndex);
                    var row = $('#dg').datagrid('getSelected');
                    //alert(row.id);
                    deleteJob(row.id);
                }
            },'-',{
                text:'Refresh',
                iconCls:'icon-reload',
                handler:function(){
                    getMyJobs();
                }
            }];

        $(function(){
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/users/get-roles',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                //headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg').datagrid({data: obj});
                    
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', 'Status Code: '+textStatus, 'info');
                }
            });
        });
    </script>
 
</body>
</html>