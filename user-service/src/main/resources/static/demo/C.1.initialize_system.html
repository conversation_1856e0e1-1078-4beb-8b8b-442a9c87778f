<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title id="title">APN</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>

<body>
    <h1>
        <div id="login_user">
        
        </div>
    </h1>

    <div id="w" class="easyui-window" title="Login" data-options="iconCls:'icon-view'" style="width:400px;height:250px;padding:10px;">
        <form id="ff" method="post" enctype="multipart/form-data">
            <div style="margin-bottom:20px">
                <input class="easyui-textbox" id="username"  prompt="username" style="width:90%" data-options="required:true">
            </div>
            <div style="margin-bottom:20px">
                <input class="easyui-passwordbox" id="password" prompt="password" value="QWEqwe!@#123" style="width:90%" data-options="required:true">
            </div>
        </form>
        <div style="text-align:center;padding:5px 0">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="login()" style="width:80px">Login</a>
        </div>
    </div>


    <div id="tabs" class="easyui-tabs" style="width:800px;height:400px">
        <div title="DB Tables" style="padding:10px">
            <table id="dg_tables" class="easyui-datagrid"  title="DB Tables" style="width:600px;height:300px"
                data-options="
                    iconCls: 'icon-view',
                    singleSelect: true,
                    toolbar: toolbar,
                    url: ''
                ">
                <thead>
                    <tr>
                        <th data-options="field:'id',width:50">ID</th>
                        <th data-options="field:'name',width:100">Name</th>
                        <th data-options="field:'userOwnerColumn',width:200">UserOwnerColumn</th>
                        <th data-options="field:'departmentOwnerColumn',width:200">TeamOwnerColumn</th>
                    </tr>
                </thead>
            </table>
        </div>

        <div title="System Modules" style="padding:10px">
            <table>
                <tr>
                    <td>
                        <table id="dg_modules" class="easyui-datagrid"  title="System Modules" style="width:300px;height:300px"
                            data-options="
                                iconCls: 'icon-view',
                                singleSelect: true,
                                toolbar: toolbar,
                                onDblClickRow: onDblClickRow,
                                url: ''
                            ">
                            <thead>
                                <tr>
                                    <th data-options="field:'id',width:100">ID</th>
                                    <th data-options="field:'name',width:250">Name</th>
                                </tr>
                            </thead>
                        </table>
                    </td>
                    <td>
                        <table id="dg_tables_by_module" class="easyui-datagrid"  title="DB Tables" style="width:300px;height:300px"
                            data-options="
                                iconCls: 'icon-view',
                                singleSelect: true,
                                toolbar: toolbar,
                                url: ''
                            ">
                            <thead>
                                <tr>
                                    <th data-options="field:'id',width:100">ID</th>
                                    <th data-options="field:'name',width:250">Name</th>
                                </tr>
                            </thead>
                        </table>
                    </td>
                </tr>
            </table>
        </div>

        <div title="Platform Admins" style="padding:10px">
            <table id="dg_platform_admins" class="easyui-datagrid"  title="Platform Admins" style="width:400px;height:300px"
                data-options="
                    iconCls: 'icon-view',
                    singleSelect: true,
                    toolbar: toolbar,
                    url: ''
                ">
                <thead>
                    <tr>
                        <th data-options="field:'id',width:100">ID</th>
                        <th data-options="field:'username',width:250">Username</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>


    <script type="text/javascript">
        var access_token;

        var toolbar = [{
                text:'Add',
                iconCls:'icon-add',
                handler:function(){
                    $('#dg').datagrid('endEdit', editIndex);
                }
            },'-',{
                text:'Delete',
                iconCls:'icon-remove',
                handler:function(){
                    if (editIndex == undefined){
                        alert('please selelct a row');
                        return;
                    }
                    //alert(editIndex);
                    var row = $('#dg').datagrid('getSelected');
                    //alert(row.id);
                    deleteJob(row.id);
                }
            },'-',{
                text:'Refresh',
                iconCls:'icon-reload',
                handler:function(){
                    getMyJobs();
                }
            }];

        function onDblClickRow(index, row){
            getModuleTables(row.id);    
        }
        
        function login(){
            var payload = {
                "username": $('#username').textbox('getValue'),
                "password": $('#password').textbox('getValue') //"QWEqwe!@#123"
            };

            $.ajax({
                url: 'http://localhost:8888/user/api/v3/account/admin/login',
                dataType: 'text',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify(payload),
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    access_token = obj.credential.access_token;
                    getPlatformAdmins();
                    getModules();
                    getTables();
                    $('#w').window('close');
                    $('#tabs').show();
                    $('#login_user').text('当前登录用户：'+payload.username);
                    $('#title').text('登录用户-'+payload.username);
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Error', "Login failed", 'error');
                }
            });
        }

        function getPlatformAdmins(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/admin/users/platform-admin',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_platform_admins').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }

        function getModules(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/admin/users/modules',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_modules').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }

        function getModuleTables(moduleId){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/admin/users/module/'+moduleId+'/tables',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_tables_by_module').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }

        function getTables(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/admin/users/tables',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg_tables').datagrid({data: obj});
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }
        

        $(function(){
            $('#tabs').hide();
        });
    </script>
 
</body>
</html>