.messager-body {
  padding: 10px 10px 30px 10px;
  overflow: auto;
}
.messager-button {
  text-align: center;
  padding: 5px;
}
.messager-button .l-btn {
  width: 70px;
}
.messager-icon {
  float: left;
  width: 32px;
  height: 32px;
  margin: 0 10px 10px 0;
}
.messager-error {
  background: url('images/messager_icons.png') no-repeat scroll -64px 0;
}
.messager-info {
  background: url('images/messager_icons.png') no-repeat scroll 0 0;
}
.messager-question {
  background: url('images/messager_icons.png') no-repeat scroll -32px 0;
}
.messager-warning {
  background: url('images/messager_icons.png') no-repeat scroll -96px 0;
}
.messager-progress {
  padding: 10px;
}
.messager-p-msg {
  margin-bottom: 5px;
}
.messager-body .messager-input {
  width: 100%;
  padding: 4px 0;
  outline-style: none;
  border: 1px solid #ddd;
}
.window-thinborder .messager-button {
  padding-bottom: 8px;
}
.messager-tip {
  box-shadow: 0 1px 6px #eee;
  height: auto;
}
.messager-tip .messager-body {
  margin: 0;
  padding: 0 10px;
  height: 40px;
  line-height: 40px;
}
.messager-tip .messager-body>.f-row {
  align-items: center;
}
.messager-tip .messager-icon {
  width: 16px;
  height: 16px;
  margin: 0 10px 0 0;
}
.messager-tip .messager-info {
  background: url('images/messager_icons16.png') no-repeat scroll 0 0;
}
.messager-tip .messager-question {
  background: url('images/messager_icons16.png') no-repeat scroll -16px 0;
}
.messager-tip .messager-error {
  background: url('images/messager_icons16.png') no-repeat scroll -32px 0;
}
.messager-tip .messager-warning {
  background: url('images/messager_icons16.png') no-repeat scroll -48px 0;
}
