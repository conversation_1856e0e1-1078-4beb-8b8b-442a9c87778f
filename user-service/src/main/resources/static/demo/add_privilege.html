<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Privilege Management</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>
<body>
    <h2>Privilege Management</h2>
    <p>Select one node and click edit button to perform editing.</p>
    <div style="margin:20px 0;">
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#w').window('open')">Add</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="edit()">Edit</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="save()">Save</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="cancel()">Cancel</a>
    </div>
    <table id="tg" class="easyui-treegrid" title="Privilege Management" style="width:800px;height:950px"
            data-options="
                iconCls: 'icon-ok',
                rownumbers: true,
                animate: true,
                collapsible: true,
                fitColumns: true,
                url: 'http://localhost:8888/user/api/v3/users/get-privileges',
                method: 'get',
                idField: 'id',
                treeField: 'name'
            ">
        <thead>
            <tr>
                <th data-options="field:'name',width:45,editor:'text'">Name</th>
                <th data-options="field:'api',width:70,editor:'text'">API</th>
                <th data-options="field:'isControlled',width:30,editor:'text'">Is Controlled</th>
            </tr>
        </thead>
    </table>

    <!--
    <div style="margin:20px 0;">
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#w').window('open')">Open</a>
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#w').window('close')">Close</a>
    </div>
    application/json
    -->
    <div id="w" class="easyui-window" title="Add New Privilege" data-options="iconCls:'icon-save'" style="width:400px;height:250px;padding:10px;">
        <form id="ff" method="post" enctype="multipart/form-data">
            <div style="margin-bottom:20px">
                <input class="easyui-textbox" name="name" style="width:90%" data-options="label:'Name:',required:true">
            </div>
            <div style="margin-bottom:20px">
                <input class="easyui-textbox" name="api" style="width:90%" data-options="label:'API:',required:false">
            </div>
            <div style="margin-bottom:20px">
                <input class="easyui-checkbox" id="isControlled" name="_isControlled" style="width:5%" data-options="label:'Ctrled:'">
            </div>
            <!-- <div style="margin-bottom:20px; ">
                <input class="easyui-numberbox" id="_parentId" name="_parentId" style="width:90%" data-options="label:'_parentId:'">
            </div> -->
        </form>
        <div style="text-align:center;padding:5px 0">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="submitForm()" style="width:80px">Submit</a>
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="clearForm()" style="width:80px">Cancel</a>
        </div>
    </div>

    <script type="text/javascript">

        function formatProgress(value){
            if (value){
                var s = '<div style="width:100%;border:1px solid #ccc">' +
                        '<div style="width:' + value + '%;background:#cc0000;color:#fff">' + value + '%' + '</div>'
                        '</div>';
                return s;
            } else {
                return '';
            }
        }
        var editingId;
        function edit(){
            if (editingId != undefined){
                $('#tg').treegrid('select', editingId);
                return;
            }
            var row = $('#tg').treegrid('getSelected');
            if (row){
                editingId = row.id
                $('#tg').treegrid('beginEdit', editingId);
            }
        }
        function save(){
            if (editingId != undefined){
                var t = $('#tg');
                t.treegrid('endEdit', editingId);
                editingId = undefined;
                var persons = 0;
                var rows = t.treegrid('getChildren');
                for(var i=0; i<rows.length; i++){
                    var p = parseInt(rows[i].persons);
                    if (!isNaN(p)){
                        persons += p;
                    }
                }
                var frow = t.treegrid('getFooterRows')[0];
                frow.persons = persons;
                t.treegrid('reloadFooter');
            }
        }
        function cancel(){
            if (editingId != undefined){
                $('#tg').treegrid('cancelEdit', editingId);
                editingId = undefined;
            }
        }

        function submitForm(){
            
            var row = $('#tg').treegrid('getSelected');
            if (row){
                //parentId = row.id
                //$('#_parentId').numberbox('setValue', parentId);
                $('#ff').form('submit');
            }else{
                $.messager.alert('Info', 'Please select a parent', 'info');
            }
        }

        function clearForm(){
            //$('#ff').form('clear');
            $('#w').window('close')
        }

        $(function(){
            $('#ff').form({
                iframe: false,
                url: "http://localhost:8888/user/api/v3/users/add-privilege",
                onSubmit: function(param){
                    // do some check
                    // return false to prevent submit;
                    param.parentId = $('#tg').treegrid('getSelected').id;
                    param.isLeaf = true;
                    var opts = $('#isControlled').checkbox('options');
                    param.isControlled = opts.checked;
                },
                success:function(data){
                    $.messager.alert('Info', data, 'info');
                    $('#tg').treegrid('reload');
                    $('#w').window('close');
                }
            });
            $('#w').window('close');
        });
    </script>
 
</body>
</html>