<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>User Data Permission Management</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>

<body>
    <h2>Set User Data Permission</h2>

    <h3></h2>
    <div style="margin:20px 0"></div>
    <div class="easyui-panel" style="width:100%;max-width:400px;padding:5px 20px;">
        <div style="margin-bottom:20px">
            <input id="user" class="easyui-combobox" name="user" style="width:100%;" data-options="
                    url:'http://localhost:8888/user/api/v3/users/get-users',
                    method:'get',
                    valueField: 'id',
                    textField: 'username',
                    label: 'Please select an user:',
                    labelPosition: 'top'
                    ">
        </div>

        <div style="margin:20px 0; display: none;">
            <a href="#" class="easyui-linkbutton" onclick="getSelected()">Get Selected</a> 
        </div>
    </div>


    <div style="margin:20px 0; display: none;">
        <a href="#" class="easyui-linkbutton" onclick="getCheckedIds()">GetChecked</a>
    </div>
    <div style="margin:10px 0; display: none;">
        <input type="checkbox" checked onchange="$('#tt').treegrid({cascadeCheck:$(this).is(':checked')})">CascadeCheck 
        <input type="checkbox" onchange="$('#tt').treegrid({onlyLeafCheck:$(this).is(':checked')})">OnlyLeafCheck
    </div>

    <h2></h2>


    

        <!-- <ul id="tt" 
            class="easyui-tree" 
            data-options="url:'http://localhost:8888/user/api/v3/users/get-privilege-tree',method:'get',animate:true,checkbox:true">
            
        </ul> -->

    <pre style="background-color: grey;width: 100%;height: 5px;"></pre>
    <h3>Please select permission scope:</h3>
    <form id="ff" method="post" enctype="multipart/form-data">
        <div style="margin-bottom:20px">
            <input id="ds_1" class="easyui-radiobutton" name="data_scope" value="1" label=""> &nbsp;&nbsp;&nbsp;&nbsp;The user only
        </div>
        <div style="margin-bottom:20px">
            <input id="ds_2" class="easyui-radiobutton" name="data_scope" value="2" label="">&nbsp;&nbsp;&nbsp;&nbsp;The team that user assigned to
        </div>
        <div style="margin-bottom:20px">
            <input id="ds_3" class="easyui-radiobutton" name="data_scope" value="3" 
                label=""
                data-options="
                    onChange: function(checked){
                        if(checked){
                            $('#div_tg').show();
                            $('#tt').treegrid('reload');
                        }else{
                            $('#div_tg').hide();
                        }
                    }
                ">&nbsp;&nbsp;&nbsp;&nbsp;The assigned team & the specific scope as blow:

            <input id="sb" class="easyui-switchbutton" style="width:100px;height:30px" 
                    data-options="
                    onText:'Modify',
                    offText:'ReadOnly'">

            <div id="div_tg" style="display: none;">
                <table id="tt"  title="" class="easyui-treegrid" style="width:300px;height:260px"
                    data-options="
                        url: 'http://localhost:8888/user/api/v3/users/get-teams',
                        method: 'get',
                        checkbox: true,
                        rownumbers: true,
                        idField: 'id',
                        treeField: 'name'
                    ">
                    <thead>
                        <tr>
                            <th data-options="field:'name'" width="280">Team</th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <div style="margin-bottom:20px">
            <input id="ds_99" class="easyui-radiobutton" name="data_scope" value="99" label="">&nbsp;&nbsp;&nbsp;&nbsp;All
        </div>
    </form>


    <div style="text-align:center;padding:5px 0;">
        <a href="javascript:void(0)" class="easyui-linkbutton" onclick="submitForm()" style="width:80px">Submit</a>
    </div>

    <script type="text/javascript">
        function getParent(node, nodeIds){
            var p = $('#tt').treegrid('getParent', node.id)
            if (p) {
                nodeIds.add(p.id);
                getParent(p, nodeIds);
            }
        }

        function getCheckedIds(){
            var nodes = $('#tt').treegrid('getCheckedNodes'); //$('#tt').tree('getChecked');
            var nodeIds = new Set()
            var s = '';
            for(var i=0; i<nodes.length; i++){
                var node = nodes[i];
                if (s != '') s += '\n';
                s += node.name;
                nodeIds.add(node.id);
                //getParent(node, nodeIds);
            }

            return nodeIds;
        }

        function getSelected(){
            var value = $('#user').combobox('getValue');
            //alert(value);
            
        }

        function submitForm(){
            
            $('#ff').form('submit');
        }

        $(function(){
            $('#ff').form({
                iframe: false,
                url: "http://localhost:8888/user/api/v3/users/set-user-data-permission",
                onSubmit: function(param){
                    // do some check
                    // return false to prevent submit;
                    param.userId = $('#user').combobox('getValue');
                    //alert('roleId='+param.roleId);
                    data_scope = 0;
                    if($('#ds_1').radiobutton('options').checked){
                        data_scope = 1;
                    }else if ($('#ds_2').radiobutton('options').checked){
                        data_scope = 2;
                    }else if ($('#ds_3').radiobutton('options').checked){
                        data_scope = 3;
                        param.departmentIds = Array.from(getCheckedIds());
                        param.modifiable = $('#sb').switchbutton('options').checked;
                    }else if ($('#ds_99').radiobutton('options').checked){
                        data_scope = 99;
                    }
                    param.dataScope = data_scope;
                },
                success:function(data){
                    //$.messager.alert('Info', data, 'info');
                    $.messager.show({
                        title:'',
                        msg:"Update successfully",
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });

            $('#role').combobox({
                onChange: function(newVal, oldVal){
                    var root = $('#tt').treegrid('getRoot');
                    //$('#tt').tree('uncheck', root.target);
                    $('#tt').treegrid('uncheckNode', root.id);
                    $.get(`http://localhost:8888/user/api/v3/users/role/${newVal}/privileges`, function(data, status){
                        //alert("Data: " + data + "\nStatus: " + status);
                        data.forEach(privilegeId => {
                            //var node = $('#tt').tree('find', privilegeId);
                            //$('#tt').tree('check', node.target);
                            var node = $('#tt').treegrid('find', privilegeId);
                            $('#tt').treegrid('checkNode', node.id);
                        });
                      });
                }
            });
        });
        
    </script>
</body>
</html>