<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title id="title">APN</title>
    <link rel="stylesheet" type="text/css" href="library/themes/default/easyui.css">
    <link rel="stylesheet" type="text/css" href="library/themes/icon.css">
    <link rel="stylesheet" type="text/css" href="library/demo.css">
    <script type="text/javascript" src="library/jquery.min.js"></script>
    <script type="text/javascript" src="library/jquery.easyui.min.js"></script>
</head>

<body>

    <h1>
        <div id="login_user">
        
        </div>
    </h1>

    <div id="w" class="easyui-window" title="Login" data-options="iconCls:'icon-view'" style="width:400px;height:250px;padding:10px;">
        <form id="ff" method="post" enctype="multipart/form-data">
            <div style="margin-bottom:20px">
                <input class="easyui-textbox" id="username"  prompt="username" style="width:90%" data-options="required:true">
            </div>
            <div style="margin-bottom:20px">
                <input class="easyui-passwordbox" id="password" prompt="password" value="QWEqwe!@#123" style="width:90%" data-options="required:true">
            </div>
        </form>
        <div style="text-align:center;padding:5px 0">
            <a href="javascript:void(0)" class="easyui-linkbutton" onclick="login()" style="width:80px">Login</a>
        </div>
    </div>

    <div id="div_jobs">
        <table id="dg" class="easyui-datagrid" title="My job list" style="width:750px;height:500px"
            data-options="
                iconCls: 'icon-view',
                singleSelect: true,
                toolbar: toolbar,
                url: '',
                method: 'get',
                onClickCell: onClickCell,
                onEndEdit: onEndEdit
            ">
            <thead>
                <tr>
                    <th data-options="field:'id',width:100">Job ID</th>
                    <th data-options="field:'title',width:250,editor:'text'">Title</th>
                    <th data-options="field:'jobType',width:100,align:'center',editor:'text'">Job Type</th>
                    <th data-options="field:'status',width:80,align:'center',editor:'text'">Status</th>
                    <th data-options="field:'userId',width:100,align:'center'">User ID</th>
                    <th data-options="field:'departmentId',width:100,align:'center'">Team ID</th>
                </tr>
                </tr>
            </thead>
        </table>
        <script type="text/javascript">

            function saveJob(){
                $('#dg').datagrid('endEdit', editIndex);
            }


            var editIndex = undefined;
            function endEditing(){
                //alert('endEditing');
                if (editIndex == undefined){return true}
                if ($('#dg').datagrid('validateRow', editIndex)){
                    $('#dg').datagrid('endEdit', editIndex);
                    editIndex = undefined;
                    return true;
                } else {
                    return false;
                }
            }
            function onClickCell(index, field){
                //alert('onClickCell');
                if (editIndex != index){
                    if (endEditing()){
                        $('#dg').datagrid('selectRow', index)
                                .datagrid('beginEdit', index);
                        var ed = $('#dg').datagrid('getEditor', {index:index,field:field});
                        if (ed){
                            ($(ed.target).data('textbox') ? $(ed.target).textbox('textbox') : $(ed.target)).focus();
                        }
                        editIndex = index;
                    } else {
                        setTimeout(function(){
                            $('#dg').datagrid('selectRow', editIndex);
                        },0);
                    }
                }
            }
            function onEndEdit(index, row){
                //alert('onEndEdit');
                // var ed = $(this).datagrid('getEditor', {
                //     index: index,
                //     field: 'title'
                // });
                // newTitle = $(ed.target).val();
                //$('#dg').datagrid('cancelEdit');
                //alert(row.id + " " + row.title);
                //alert(newTitle);
                updateJob(row);
            }

            function removeit(){
                if (editIndex == undefined){return}
                $('#dg').datagrid('cancelEdit', editIndex)
                        .datagrid('deleteRow', editIndex);
                editIndex = undefined;
            }

            function acceptit(){
                if (endEditing()){
                    $('#dg').datagrid('acceptChanges');
                }
            }
            function reject(){
                $('#dg').datagrid('rejectChanges');
                editIndex = undefined;
            }
            function getChanges(){
                var rows = $('#dg').datagrid('getChanges');
                alert(rows.length+' rows are changed!');
            }

            $(function(){
                /**var dg = $('#dg').datagrid('load', 'http://localhost:8888/user/api/v3/permissions/jobs/get-jobs');
                dg.datagrid('enableCellEditing').datagrid('gotoCell', {
                    index: 0,
                    field: 'productid'
                });*/
            });
        </script>
    </div>

    <script type="text/javascript">

        toolbar_create = {
            text:'Create',
            iconCls:'icon-add',
            handler:function(){
                //$('#dg').datagrid('endEdit', editIndex);
            }
        }

        toolbar_save = {
            text:'Save',
            iconCls:'icon-save',
            handler:function(){
                $('#dg').datagrid('endEdit', editIndex);
            }
        }

        toolbar_delete = {
            text:'Delete',
            iconCls:'icon-remove',
            handler:function(){
                if (editIndex == undefined){
                    alert('please selelct a row');
                    return;
                }
                //alert(editIndex);
                var row = $('#dg').datagrid('getSelected');
                //alert(row.id);
                deleteJob(row.id);
            }
        }

        toolbar_refresh = {
            text:'Refresh',
            iconCls:'icon-reload',
            handler:function(){
                getMyJobs();
            }
        }

        var toolbar = [];
        
        var access_token;

        function getAccount(){
        
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/account',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    var authorities = obj.authorities;
                    console.log(authorities);

                    
                    if (authorities.includes('Job')) {
                        getMyJobs();
                        $('#div_jobs').show();
                    }else{
                        return;
                    }

                    if (authorities.includes('Add Job')) {
                        toolbar.push(toolbar_create);
                        toolbar.push('-');
                    }

                    if (authorities.includes('Update Job')) {
                        toolbar.push(toolbar_save);
                        toolbar.push('-');
                    }

                    if (authorities.includes('Delete Job')) {
                        toolbar.push(toolbar_delete);
                        toolbar.push('-');
                    }

                    if (authorities.includes('Get Job')) {
                        toolbar.push(toolbar_refresh);
                        toolbar.push('-');
                    }
                    
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Info', errorThrown, 'info');
                }
            });
        }
        
        function login(){
            var payload = {
                "username": $('#username').textbox('getValue'),
                "password": $('#password').textbox('getValue')
            };

            $.ajax({
                url: 'http://localhost:8888/user/api/v3/login',
                dataType: 'text',
                type: 'post',
                contentType: 'application/json',
                data: JSON.stringify(payload),
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    access_token = obj.credential.access_token;
                    getAccount();
                    $('#w').window('close');
                    $('#login_user').text('当前登录用户：'+payload.username);
                    $('#title').text(payload.username);
                    getMyJobs();
                    
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    $.messager.alert('Error', "Login failed", 'error');
                }
            });
        }

        function getMyJobs(){
            
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/jobs/get-jobs',
                dataType: 'text',
                type: 'get',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    var obj = JSON.parse(data)
                    $('#dg').datagrid({data:obj});
                    
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    //$.messager.alert('Info', 'Status Code: '+textStatus, 'info');
                    $.messager.alert('Error', "NO PERMISSION", 'error');
                }
            });
        }

        function updateJob(row){
            //alert(row.title);
            //alert(newTitle);
            var payload = {
                "id": row.id,
                "title": row.title
            };
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/jobs/update-job',
                dataType: 'text',
                type: 'put',
                contentType: 'application/json',
                data: JSON.stringify(payload),
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    //row.title = newTitle;
                    //alert(textStatus);
                    $.messager.show({
                        title:'',
                        msg:'Update successfully!',
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    //$.messager.alert('Info', errorThrown, 'info');
                    $.messager.alert('Error', "NO PERMISSION", 'error');
                    reject();
                }
            });
        }

        function deleteJob(jobId){
            //alert(row.title);
            //alert(newTitle);
            $.ajax({
                url: 'http://localhost:8888/user/api/v3/permissions/jobs/delete-job/'+jobId,
                dataType: 'text',
                type: 'delete',
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    $.messager.show({
                        title:'',
                        msg:'Delete successfully!',
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                    removeit();
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    //$.messager.alert('Info', errorThrown, 'info');
                    $.messager.alert('Error', "NO PERMISSION", 'error');
                    reject();
                }
            });
        }


        function sendWithAjax(uri, method){
            $.ajax({
                url: 'http://localhost:8888' + uri,
                dataType: 'text',
                type: method,
                contentType: 'application/json',
                headers: { 'Authorization': `bearer ${access_token}` },
                success: function( data, textStatus, jQxhr ){
                    //var obj = JSON.parse(data)
                    $.messager.show({
                        title:'',
                        msg:data,
                        timeout:1000,
                        height: 50,
                        width:300,
                        showType:'show',
                        style:{
                            right:'',
                            top:document.body.height,
                            bottom:''
                        }
                    });
                },
                error: function( jqXhr, textStatus, errorThrown ){
                    
                    $.messager.alert('Error', "NO PERMISSION", 'error');
                }
            });
        }
       
        function getJob(){
            var uri = '/user/api/v3/permissions/jobs/get-jobs';
            var method= 'get';
            sendWithAjax(uri, method);
        }

        $(function(){
            $('#div_jobs').hide();
        });
    </script>
 
</body>
</html>