* {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -o-box-sizing: border-box;
  -ms-box-sizing: border-box;
  box-sizing: border-box;
}
.panel {
  overflow: hidden;
  text-align: left;
  margin: 0;
  border: 0;
  -moz-border-radius: 0 0 0 0;
  -webkit-border-radius: 0 0 0 0;
  border-radius: 0 0 0 0;
}
.panel-header,
.panel-body {
  border-width: 1px;
  border-style: solid;
}
.panel-header {
  padding: 5px;
  position: relative;
}
.panel-title {
  background: url('images/blank.gif') no-repeat;
}
.panel-header-noborder {
  border-width: 0 0 1px 0;
}
.panel-body {
  overflow: auto;
  border-top-width: 0;
  padding: 0;
}
.panel-body-noheader {
  border-top-width: 1px;
}
.panel-body-noborder {
  border-width: 0px;
}
.panel-body-nobottom {
  border-bottom-width: 0;
}
.panel-with-icon {
  padding-left: 18px;
}
.panel-icon,
.panel-tool {
  position: absolute;
  top: 50%;
  margin-top: -8px;
  height: 16px;
  overflow: hidden;
}
.panel-icon {
  left: 5px;
  width: 16px;
}
.panel-tool {
  right: 5px;
  width: auto;
}
.panel-tool a {
  display: inline-block;
  width: 16px;
  height: 16px;
  opacity: 0.6;
  filter: alpha(opacity=60);
  margin: 0 0 0 2px;
  vertical-align: top;
}
.panel-tool a:hover {
  opacity: 1;
  filter: alpha(opacity=100);
  background-color: #eee;
  -moz-border-radius: 2px 2px 2px 2px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}
.panel-loading {
  padding: 11px 0px 10px 30px;
}
.panel-noscroll {
  overflow: hidden;
}
.panel-fit,
.panel-fit body {
  height: 100%;
  margin: 0;
  padding: 0;
  border: 0;
  overflow: hidden;
}
.panel-loading {
  background: url('images/loading.gif') no-repeat 10px 10px;
}
.panel-tool-close {
  background: url('images/panel_tools.png') no-repeat -16px 0px;
}
.panel-tool-min {
  background: url('images/panel_tools.png') no-repeat 0px 0px;
}
.panel-tool-max {
  background: url('images/panel_tools.png') no-repeat 0px -16px;
}
.panel-tool-restore {
  background: url('images/panel_tools.png') no-repeat -16px -16px;
}
.panel-tool-collapse {
  background: url('images/panel_tools.png') no-repeat -32px 0;
}
.panel-tool-expand {
  background: url('images/panel_tools.png') no-repeat -32px -16px;
}
.panel-header,
.panel-body {
  border-color: #dfdfdf;
}
.panel-header {
  background-color: #fafafa;
  color: #404040;
}
.panel-body {
  background-color: #ffffff;
  color: #404040;
  font-size: 14px;
}
.panel-title {
  font-size: 14px;
  font-weight: bold;
  color: #404040;
  height: 20px;
  line-height: 20px;
}
.panel-footer {
  border: 1px solid #dfdfdf;
  overflow: hidden;
  background: #fafafa;
  color: #404040;
}
.panel-footer-noborder {
  border-width: 1px 0 0 0;
}
.panel-hleft,
.panel-hright {
  position: relative;
}
.panel-hleft>.panel-body,
.panel-hright>.panel-body {
  position: absolute;
}
.panel-hleft>.panel-header {
  float: left;
}
.panel-hright>.panel-header {
  float: right;
}
.panel-hleft>.panel-body {
  border-top-width: 1px;
  border-left-width: 0;
}
.panel-hright>.panel-body {
  border-top-width: 1px;
  border-right-width: 0;
}
.panel-hleft>.panel-body-nobottom {
  border-bottom-width: 1px;
  border-right-width: 0;
}
.panel-hright>.panel-body-nobottom {
  border-bottom-width: 1px;
  border-left-width: 0;
}
.panel-hleft>.panel-footer {
  position: absolute;
  right: 0;
}
.panel-hright>.panel-footer {
  position: absolute;
  left: 0;
}
.panel-hleft>.panel-header-noborder {
  border-width: 0 1px 0 0;
}
.panel-hright>.panel-header-noborder {
  border-width: 0 0 0 1px;
}
.panel-hleft>.panel-body-noborder {
  border-width: 0;
}
.panel-hright>.panel-body-noborder {
  border-width: 0;
}
.panel-hleft>.panel-body-noheader {
  border-left-width: 1px;
}
.panel-hright>.panel-body-noheader {
  border-right-width: 1px;
}
.panel-hleft>.panel-footer-noborder {
  border-width: 0 0 0 1px;
}
.panel-hright>.panel-footer-noborder {
  border-width: 0 1px 0 0;
}
.panel-hleft>.panel-header .panel-icon,
.panel-hright>.panel-header .panel-icon {
  margin-top: 0;
  top: 5px;
  left: 50%;
  margin-left: -8px;
}
.panel-hleft>.panel-header .panel-title,
.panel-hright>.panel-header .panel-title {
  position: absolute;
  min-width: 16px;
  left: 25px;
  top: 5px;
  bottom: auto;
  white-space: nowrap;
  word-wrap: normal;
  -webkit-transform: rotate(90deg);
  -webkit-transform-origin: 0 0;
  -moz-transform: rotate(90deg);
  -moz-transform-origin: 0 0;
  -o-transform: rotate(90deg);
  -o-transform-origin: 0 0;
  transform: rotate(90deg);
  transform-origin: 0 0;
}
.panel-hleft>.panel-header .panel-title-up,
.panel-hright>.panel-header .panel-title-up {
  position: absolute;
  min-width: 16px;
  left: 21px;
  top: auto;
  bottom: 0px;
  text-align: right;
  white-space: nowrap;
  word-wrap: normal;
  -webkit-transform: rotate(-90deg);
  -webkit-transform-origin: 0 0;
  -moz-transform: rotate(-90deg);
  -moz-transform-origin: 0 0;
  -o-transform: rotate(-90deg);
  -o-transform-origin: 0 0;
  transform: rotate(-90deg);
  transform-origin: 0 16px;
}
.panel-hleft>.panel-header .panel-with-icon.panel-title-up,
.panel-hright>.panel-header .panel-with-icon.panel-title-up {
  padding-left: 0;
  padding-right: 18px;
}
.panel-hleft>.panel-header .panel-tool,
.panel-hright>.panel-header .panel-tool {
  top: auto;
  bottom: 5px;
  width: 16px;
  height: auto;
  left: 50%;
  margin-left: -8px;
  margin-top: 0;
}
.panel-hleft>.panel-header .panel-tool a,
.panel-hright>.panel-header .panel-tool a {
  margin: 2px 0 0 0;
}
