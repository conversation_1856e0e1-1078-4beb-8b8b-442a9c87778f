.calendar {
  border-width: 1px;
  border-style: solid;
  padding: 1px;
  overflow: hidden;
}
.calendar table {
  table-layout: fixed;
  border-collapse: separate;
  font-size: 14px;
  width: 100%;
  height: 100%;
}
.calendar table td,
.calendar table th {
  font-size: 14px;
}
.calendar-noborder {
  border: 0;
}
.calendar-header {
  position: relative;
  height: 36px;
}
.calendar-title {
  text-align: center;
  height: 36px;
  line-height: 36px;
}
.calendar-title span {
  position: relative;
  display: inline-block;
  top: 0px;
  padding: 0 3px;
  height: 28px;
  line-height: 28px;
  font-size: 14px;
  cursor: pointer;
  -moz-border-radius: 2px 2px 2px 2px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}
.calendar-prevmonth,
.calendar-nextmonth,
.calendar-prevyear,
.calendar-nextyear {
  position: absolute;
  top: 50%;
  margin-top: -8px;
  width: 16px;
  height: 16px;
  cursor: pointer;
  font-size: 1px;
  -moz-border-radius: 2px 2px 2px 2px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}
.calendar-prevmonth {
  left: 30px;
  background: url('images/calendar_arrows.png') no-repeat -16px 0;
}
.calendar-nextmonth {
  right: 30px;
  background: url('images/calendar_arrows.png') no-repeat -32px 0;
}
.calendar-prevyear {
  left: 10px;
  background: url('images/calendar_arrows.png') no-repeat 0px 0;
}
.calendar-nextyear {
  right: 10px;
  background: url('images/calendar_arrows.png') no-repeat -48px 0;
}
.calendar-body {
  position: relative;
}
.calendar-body th,
.calendar-body td {
  text-align: center;
}
.calendar-day {
  border: 0;
  padding: 1px;
  cursor: pointer;
  -moz-border-radius: 2px 2px 2px 2px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}
.calendar-other-month {
  opacity: 0.3;
  filter: alpha(opacity=30);
}
.calendar-disabled {
  opacity: 0.6;
  filter: alpha(opacity=60);
  cursor: default;
}
.calendar-menu {
  position: absolute;
  top: 0;
  left: 0;
  width: 180px;
  height: 150px;
  padding: 5px;
  font-size: 14px;
  display: none;
  overflow: hidden;
}
.calendar-menu-year-inner {
  text-align: center;
  padding-bottom: 5px;
}
.calendar-menu-year {
  width: 80px;
  line-height: 26px;
  text-align: center;
  border-width: 1px;
  border-style: solid;
  outline-style: none;
  resize: none;
  margin: 0;
  padding: 0;
  font-weight: bold;
  font-size: 14px;
  -moz-border-radius: 2px 2px 2px 2px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}
.calendar-menu-prev,
.calendar-menu-next {
  display: inline-block;
  width: 25px;
  height: 28px;
  vertical-align: top;
  cursor: pointer;
  -moz-border-radius: 2px 2px 2px 2px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}
.calendar-menu-prev {
  margin-right: 10px;
  background: url('images/calendar_arrows.png') no-repeat 5px center;
}
.calendar-menu-next {
  margin-left: 10px;
  background: url('images/calendar_arrows.png') no-repeat -44px center;
}
.calendar-menu-month {
  text-align: center;
  cursor: pointer;
  font-weight: bold;
  -moz-border-radius: 2px 2px 2px 2px;
  -webkit-border-radius: 2px 2px 2px 2px;
  border-radius: 2px 2px 2px 2px;
}
.calendar-body th,
.calendar-menu-month {
  color: #8d8d8d;
}
.calendar-day {
  color: #404040;
}
.calendar-sunday {
  color: #CC2222;
}
.calendar-saturday {
  color: #00ee00;
}
.calendar-today {
  color: #0000ff;
}
.calendar-menu-year {
  border-color: #dfdfdf;
}
.calendar {
  border-color: #dfdfdf;
}
.calendar-header {
  background: #f5f5f5;
}
.calendar-body,
.calendar-menu {
  background: #ffffff;
}
.calendar-body th {
  background: #fafafa;
  padding: 4px 0;
}
.calendar-hover,
.calendar-nav-hover,
.calendar-menu-hover {
  background-color: #eee;
  color: #404040;
}
.calendar-hover {
  border: 1px solid #ccc;
  padding: 0;
}
.calendar-selected {
  background-color: #eee;
  color: #2196f3;
  border: 1px solid #2196f3;
  padding: 0;
}
.calendar-info {
  background-color: #f5f5f5;
  font-size: 28px;
  height: 70px;
  padding: 10px 20px;
}
.calendar-info .year {
  font-size: 16px;
}
