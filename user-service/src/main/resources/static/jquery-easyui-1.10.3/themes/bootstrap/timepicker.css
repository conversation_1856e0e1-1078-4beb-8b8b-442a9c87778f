.timepicker-panel .clock-wrap {
  position: relative;
}
.timepicker-panel .clock {
  position: relative;
  background: #F2F2F2;
  color: #777;
  border-radius: 50%;
  position: absolute;
  left: 50%;
  top: 50%;
}
.timepicker-panel .clock .item {
  width: 32px;
  height: 32px;
  left: 50%;
  top: 50%;
  margin-left: -16px;
  margin-top: -16px;
  position: absolute;
  user-select: none;
  border-radius: 50%;
  z-index: 9;
  cursor: pointer;
}
.timepicker-panel .clock .item-selected {
  background: #0070a9;
  color: #fff;
}
.timepicker-panel .clock .hand {
  width: 2px;
  bottom: 50%;
  left: 50%;
  margin-left: -1px;
  top: 20px;
  -webkit-transform-origin: center bottom;
  transform-origin: center bottom;
  position: absolute;
  will-change: transform;
  z-index: 1;
  background-color: #0070a9;
}
.timepicker-panel .clock .hand .drag {
  top: -16px;
  left: -15px;
  width: 4px;
  height: 4px;
  border: 14px solid #0070a9;
  position: absolute;
  box-sizing: content-box;
  border-radius: 100%;
  background-color: #fff;
}
.timepicker-panel .clock .center {
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  position: absolute;
  transform: translate(-50%,-50%);
  border-radius: 50%;
  background-color: #0070a9;
}
.timepicker-panel .panel-header {
  height: 70px;
  border: 0;
  font-size: 36px;
  position: relative;
}
.timepicker-panel .body {
  position: relative;
}
.timepicker-panel .panel-header .ampm {
  font-size: 16px;
  padding-left: 10px;
  position: absolute;
  right: 20px;
}
.timepicker-panel .panel-header .sep {
  opacity: 0.6;
}
.timepicker-panel .panel-header .title {
  cursor: pointer;
  opacity: 0.6;
}
.timepicker-panel .panel-header .title:hover {
  opacity: 1.0;
}
.timepicker-panel .panel-header .title-selected,
.timepicker-panel .panel-header .title-selected:hover {
  cursor: default;
  opacity: 1.0;
}
