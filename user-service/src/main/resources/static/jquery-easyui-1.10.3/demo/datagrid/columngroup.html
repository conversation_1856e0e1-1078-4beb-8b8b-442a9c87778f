<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Column Group - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Column Group</h2>
	<p>The header cells can be merged. Useful to group columns under a category.</p>
	<div style="margin:20px 0;"></div>
	<table class="easyui-datagrid" title="Column Group" style="width:700px;height:250px"
			data-options="rownumbers:true,singleSelect:true,url:'datagrid_data1.json',method:'get'">
		<thead>
			<tr>
				<th data-options="field:'itemid',width:80" rowspan="2">Item ID</th>
				<th data-options="field:'productid',width:100" rowspan="2">Product</th>
				<th colspan="4">Item Details</th>
			</tr>
			<tr>
				<th data-options="field:'listprice',width:80,align:'right'">List Price</th>
				<th data-options="field:'unitcost',width:80,align:'right'">Unit Cost</th>
				<th data-options="field:'attr1',width:240">Attribute</th>
				<th data-options="field:'status',width:60,align:'center'">Status</th>
			</tr>
		</thead>
	</table>

</body>
</html>