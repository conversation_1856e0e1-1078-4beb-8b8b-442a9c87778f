<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Show Item Icon in ComboBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Show Item Icon in ComboBox</h2>
	<p>This example shows how to display item icon in ComboBox.</p>
	<div style="margin:20px 0"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-combobox" style="width:100%;" data-options="
					showItemIcon: true,
					data: [
						{value:'add',text:'Add',iconCls:'icon-add'},
						{value:'del',text:'Delete',iconCls:'icon-remove'},
						{value:'save',text:'Save',iconCls:'icon-save',selected:true},
						{value:'cancel',text:'Cancel',iconCls:'icon-cancel'},
						{value:'undo',text:'Undo',iconCls:'icon-undo'},
						{value:'redo',text:'Redo',iconCls:'icon-redo'}
					],
					editable: false,
					panelHeight: 'auto',
					label: 'Perform Action:',
					labelPosition: 'top'
					">
		</div>
	</div>
</body>
