<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Load Dynamic ComboBox Data - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Load Dynamic ComboBox Data</h2>
	<p>Click the button below to load data.</p>
	
	<div style="margin:20px 0;">
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#language').combobox('reload', 'combobox_data1.json')">LoadData</a>
	</div>

	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input id="language" class="easyui-combobox" name="language" style="width:100%;" data-options="
					valueField: 'id',
					textField: 'text',
					label: 'Language:',
					labelPosition: 'top'
					">
		</div>
	</div>
</body>
</html>