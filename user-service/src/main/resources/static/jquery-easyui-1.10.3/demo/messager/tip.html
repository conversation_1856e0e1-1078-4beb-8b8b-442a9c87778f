<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Tip Messager - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Tip Messager</h2>
	<div style="margin:20px 0;">
		<a href="#" class="easyui-linkbutton" onclick="tip1()">Normal</a>
		<a href="#" class="easyui-linkbutton" onclick="tip2()">Error</a>
		<a href="#" class="easyui-linkbutton" onclick="tip3()">Info</a>
		<a href="#" class="easyui-linkbutton" onclick="tip4()">Question</a>
		<a href="#" class="easyui-linkbutton" onclick="tip5()">Warning</a>
	</div>
	<script>
		function tip1(){
			$.messager.tip('Here is a normal message!');
		}
		function tip2(){
			$.messager.tip({
				msg: 'Here is an error message!',
				icon: 'error'
			});
		}
		function tip3(){
			$.messager.tip({
				msg: 'Here is an info message!',
				icon: 'info'
			});
		}
		function tip4(){
			$.messager.tip({
				msg: 'Here is a question message!',
				icon: 'question'
			});
		}
		function tip5(){
			$.messager.tip({
				msg: 'Here is a warning message!',
				icon: 'warning'
			});
		}
	</script>
</body>
</html>