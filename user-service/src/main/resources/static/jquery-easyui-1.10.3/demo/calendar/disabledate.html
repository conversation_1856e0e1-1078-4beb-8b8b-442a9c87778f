<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Disable Calendar Date - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Disable Calendar Date</h2>
	<p>This example shows how to disable specified dates, only allows the user to select Mondays.</p>
	<div style="margin:20px 0"></div>
	
	<div class="easyui-calendar" style="width:250px;height:250px;" data-options="
			validator: function(date){
				if (date.getDay() == 1){
					return true;
				} else {
					return false;
				}
			}
			"></div>

</body>
</html>