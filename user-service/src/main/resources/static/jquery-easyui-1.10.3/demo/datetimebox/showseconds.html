<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Display Seconds - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Display Seconds</h2>
	<p>The user can decide to display seconds part or not.</p>
	<div style="margin:20px 0;">
		<span>Show Seconds: </span>
		<input type="checkbox" checked onchange="$('#dt').datetimebox({showSeconds:$(this).is(':checked')})">
	</div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input id="dt" class="easyui-datetimebox" label="Select DateTime:" labelPosition="top" style="width:100%;">
		</div>
	</div>
</body>
</html>