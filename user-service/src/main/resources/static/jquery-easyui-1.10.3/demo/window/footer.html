<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Window with a Footer - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Window with a Footer</h2>
	<p>This example shows how to attach a footer bar to the window.</p>
	<div style="margin:20px 0;">
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#w').window('open')">Open</a>
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="$('#w').window('close')">Close</a>
	</div>
	<div id="w" class="easyui-window" title="Window with a Footer" data-options="iconCls:'icon-save',footer:'#footer'" style="width:500px;height:200px;padding:10px;">
		The window content.
	</div>
	<div id="footer" style="padding:5px;">Footer Content.</div>
</body>
</html>