<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Format Tip Information - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Format Tip Information</h2>
	<p>This sample shows how to format tip information.</p>
	<div style="margin:20px 0 50px 0;"></div>
	<input class="easyui-slider" value="12" style="width:300px" data-options="
			showTip: true,
			rule: [0,'|',25,'|',50,'|',75,'|',100],
			tipFormatter: function(value){
				return value+'px';
			},
			onChange: function(value){
				$('#ff').css('font-size', value);
			}">
	<div id="ff" style="margin-top:50px;font-size:12px">jQuery EasyUI</div>

</body>
</html>