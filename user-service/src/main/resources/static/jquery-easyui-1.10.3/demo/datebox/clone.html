<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Clone DateBox - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Clone DateBox</h2>
	<p>Click the 'Clone' button to clone datebox components from the exiting datebox.</p>
	<div style="margin:20px 0;">
		<a href="javascript:void(0)" class="easyui-linkbutton" onclick="cloneDatebox()">Clone</a>
	</div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input id="dt" class="easyui-datebox" label="Select Date:" labelPosition="top" style="width:100%;">
		</div>
		<div id="cc" style="margin-top:10px"></div>
	</div>
	<script type="text/javascript">
		function cloneDatebox(){
			var dt = $('<input>').appendTo('#cc');
			dt.datebox('cloneFrom', '#dt');
		}
	</script>
</body>
</html>