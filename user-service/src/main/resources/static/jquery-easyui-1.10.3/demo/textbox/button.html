<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>TextBox with Button - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>TextBox with Button</h2>
	<p>The button can be attached to a textbox.</p>
	<div style="margin:20px 0 40px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-textbox" data-options="buttonText:'SEARCH',prompt:'Search...'" style="width:100%;height:32px;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-textbox" data-options="buttonText:'Search',buttonAlign:'left',buttonIcon:'icon-search',prompt:'Search...'" style="width:100%;height:32px;">
		</div>
	</div>
</body>
</html>