<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>Number Range - jQuery EasyUI Demo</title>
	<link rel="stylesheet" type="text/css" href="../../themes/default/easyui.css">
	<link rel="stylesheet" type="text/css" href="../../themes/icon.css">
	<link rel="stylesheet" type="text/css" href="../demo.css">
	<script type="text/javascript" src="../../jquery.min.js"></script>
	<script type="text/javascript" src="../../jquery.easyui.min.js"></script>
</head>
<body>
	<h2>Number Range</h2>
	<p>The value is constrained to a specified range.</p>
	<div style="margin:20px 0;"></div>
	<div class="easyui-panel" style="width:100%;max-width:400px;padding:30px 60px;">
		<div style="margin-bottom:20px">
			<input class="easyui-numberbox" data-options="label:'Amount:',labelPosition:'top',min:10,max:90,precision:2" style="width:100%;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-numberbox" data-options="label:'Weight:',labelPosition:'top',min:10,max:90" style="width:100%;">
		</div>
		<div style="margin-bottom:20px">
			<input class="easyui-numberbox" data-options="label:'Age:',labelPosition:'top',min:0,max:100" style="width:100%;">
		</div>
	</div>
</body>
</html>