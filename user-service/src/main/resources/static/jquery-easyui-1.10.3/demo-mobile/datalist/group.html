<!doctype html>
<html>
<head>
    <meta charset="UTF-8">  
	<meta name="viewport" content="initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Group DataList - jQuery EasyUI Mobile Demo</title>  
    <link rel="stylesheet" type="text/css" href="../../themes/metro/easyui.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/mobile.css">  
    <link rel="stylesheet" type="text/css" href="../../themes/icon.css">  
    <script type="text/javascript" src="../../jquery.min.js"></script>  
    <script type="text/javascript" src="../../jquery.easyui.min.js"></script> 
    <script type="text/javascript" src="../../jquery.easyui.mobile.js"></script> 
</head>
<body>
	<div class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<span class="m-title">Group DataList</span>
			</div>
		</header>
		<div id="dl" data-options="
				fit: true,
				border: false,
				lines: true
				">
		</div>
	</div>
	<div id="p2" class="easyui-navpanel">
		<header>
			<div class="m-toolbar">
				<span id="p2-title" class="m-title">Detail</span>
                <div class="m-left">
                    <a href="javascript:void(0)" class="easyui-linkbutton m-back" plain="true" outline="true" onclick="$.mobile.back()">Back</a>
                </div>
			</div>
		</header>
        <div style="margin:50px 0 0;text-align:center">
            <a href="javascript:void(0)" class="easyui-linkbutton" style="width:100px;height:30px" onclick="$.mobile.back()">Go Back</a>
        </div>
	</div>
	<script>
		var data = [
			{"group":"FL-DSH-01","item":"Tailless"},
			{"group":"FL-DSH-01","item":"With tail"},
			{"group":"FL-DSH-02","item":"Adult Female"},
			{"group":"FL-DSH-02","item":"Adult Male"}
		];
		$(function(){
			$('#dl').datalist({
				data: data,
				textField: 'item',
				groupField: 'group',
				textFormatter: function(value){
					return '<a href="javascript:void(0)" class="datalist-link">' + value + '</a>';
				},
				onClickRow: function(index,row){
					$('#p2-title').html(row.item);
					$.mobile.go('#p2');
				}
			})
		})
	</script>
</body>	
</html>
