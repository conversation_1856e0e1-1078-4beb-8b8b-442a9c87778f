<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <!--
        output format：
            %-5level  log level, Align to the left，make up with space if the length is less than 5，
            %d{yyyy-MM-dd HH:mm:ss.SSS}
            %c Class
            %M Method
            %L Line number
            %thread
            %m/msg
            %n new line
    -->
    <!--<property name="pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %level %thread %c %M %L %m %n"/>-->

    <!-- log with color -->
    <property name="pattern" value="${LOG_PATTERN:-%green(%d{yyyy-MM-dd HH:mm:ss.SSS}) %highlight(%level) %magenta(%thread) %yellow(%c) %cyan(%M) %highlight(%L) %msg %n}"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <target>System.out</target>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${pattern}</pattern>
        </encoder>
    </appender>

    <appender name="rollFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <file>${logback.path}/${logback.name}.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${pattern}</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${path}/${name}.%d{yyyy-MM-dd}.log%i.zip</fileNamePattern>
            <maxFileSize>10MB</maxFileSize> <!--the size of single log file can be up to 10MB-->
            <maxHistory>30</maxHistory> <!--keep 30 days' history logs-->
            <totalSizeCap>500MB</totalSizeCap> <!--the total size of all log files can be up to 500MB-->
        </rollingPolicy>
    </appender>

    <appender name="async" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="rollFile"/>
    </appender>

    <root level="ALL">
        <appender-ref ref="console"/>
        <appender-ref ref="async"/>
    </root>

    <!--customized logger
    additivity="true"  # true is to extend root logger
    -->
    <logger name="io" level="warn" additivity="true"/>
    <logger name="org.hibernate" level="warn" additivity="true"/>
    <logger name="org.apache" level="warn" additivity="true"/>
    <logger name="com.alibaba" level="warn" additivity="true"/>
    <logger name="javax" level="warn" additivity="true"/>
    <logger name="sun" level="warn" additivity="true"/>
    <logger name="org.springframework" level="info" additivity="true"/>
    <logger name="liquibase" level="info" additivity="true"/>
    <logger name="_org" level="info" additivity="true"/>
    <logger name="com.zaxxer" level="info" additivity="true"/>
    <logger name="org.reflections" level="warn" additivity="true"/>
    <logger name="org.aspectj" level="warn" additivity="true"/>
    <logger name="jdk" level="warn" additivity="true"/>
    <logger name="java.io" level="warn" additivity="true"/>

</configuration>