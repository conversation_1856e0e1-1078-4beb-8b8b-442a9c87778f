registry {
  type = "nacos"
  nacos {
    application = "seata-server"
    serverAddr = "host.docker.internal:8848" # lb-api-staging.apn-staging:8848  host.docker.internal:8848
    group = "DEFAULT_GROUP"
    namespace = "z-dev-xxx" # replace longfei with your firstname, e.g. z-dev-longfei
    cluster = "default"
    username = "nacos"
    password = "nacos"
  }
}

config {
  type = "nacos"
  nacos {
      serverAddr = "host.docker.internal:8848" # lb-api-staging.apn-staging:8848  host.docker.internal:8848
      group = "DEFAULT_GROUP"
      namespace = "z-dev-xxx" # replace longfei with your firstname, e.g. z-dev-longfei
      dataId = "apn_tx_group.properties"
      username = "nacos"
      password = "nacos"
  }
}