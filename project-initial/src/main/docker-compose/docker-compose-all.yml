# docker-compose -f docker-compose-all.yml up -d
version: '3.0'
services:
  redis: 
    image: redis
    container_name: apn-redis
    restart: always
    #volumes:
    #  - ../app-volumes/redis/data:/data
    environment:
      - REDIS_PASSWORD=
    ports:
      - "6379:6379"
    deploy:
      resources:
        limits:
          memory: 1024M
        #reservations:
        #  memory: 128M

  mysql:
    image: mysql/mysql-server:latest
    container_name: apn-mysql
    restart: always
    ports:
      - "3306:3306"
    volumes:
      - "../sql/apn-init.sql:/docker-entrypoint-initdb.d/apn-init.sql"
    #  - ../app-volumes/mysql:/var/lib/mysql
    environment:
      MYSQL_ALLOW_EMPTY_PASSWORD: 1
      MYSQL_ROOT_PASSWORD:
      MYSQL_ROOT_HOST: '%'

  mongodb:
    image: mongo
    container_name: apn-mongodb
    restart: always
    ports:
      - "27017:27017"
    #volumes:
    #  - ../app-volumes/mongodb:/data/db

  rabbitmq:
    image: rabbitmq:3-management
    container_name: apn-rabbitmq
    restart: always
    #volumes:
    #  - ../app-volumes/rabbitmq:/var/lib/rabbitmq/mnesia
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: apn
      RABBITMQ_DEFAULT_PASS: apn

  nacos-m1:
    image: zill057/nacos-server-apple-silicon:2.0.3
    container_name: apn-nacos
    restart: always
    #volumes:
    #  - ../app-volumes/nacos/:/home/<USER>/
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    environment:
      MODE: standalone
    deploy:
      resources:
        limits:
          memory: 2048M

  nacos:
    image: nacos/nacos-server:2.0.3
    container_name: apn-nacos
    restart: always
    #volumes:
    #  - ../app-volumes/nacos/:/home/<USER>/
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    environment:
      MODE: standalone
    deploy:
      resources:
        limits:
          memory: 2048M

  zipkin:
    image: openzipkin/zipkin
    container_name: apn-zipkin
    restart: always
    ports:
      - "9411:9411"
    deploy:
      resources:
        limits:
          memory: 1024M

  sentinel-m1:
    image: minghealtomni/sentinel-dashboard-m1:1.8.2
    container_name: apn-sentinel
    restart: always
    ports:
      - "8090:8080"
    deploy:
      resources:
        limits:
          memory: 1024M

  sentinel:
    image: minghealtomni/sentinel-dashboard:1.8.2
    container_name: apn-sentinel
    restart: always
    ports:
      - "8090:8080"
    deploy:
      resources:
        limits:
          memory: 1024M


  seata-m1:
    image: seataio/seata-server:latest # fancyfong/seata:1.4.1_arm64
    container_name: apn-seata
    restart: always
    volumes:
      - ../app-volumes/seata/conf:/root/seata-config
    ports:
      - "8091:8091"
    environment:
      - SEATA_IP=${SEATA_IP}
      - SEATA_PORT=8091
      - STORE_MODE=redis
      - SEATA_CONFIG_NAME=file:/root/seata-config/registry
    deploy:
      resources:
        limits:
          memory: 2048M

  seata:
    image: seataio/seata-server:1.4.2
    container_name: apn-seata
    restart: always
    volumes:
      - ../app-volumes/seata/conf:/root/seata-config
    ports:
      - "8091:8091"
    #depends_on:
    #  - nacos
    #entrypoint: "sh /init-nacos-config.sh -t dev -h host.docker.internal"
    #command: "sh seata/bin/seata-server.sh -p 8091 -h *********** -m redis"
    environment:
      - SEATA_IP=${SEATA_IP}
      - SEATA_PORT=8091
      - STORE_MODE=redis
      - SEATA_CONFIG_NAME=file:/root/seata-config/registry
    deploy:
      resources:
        limits:
          memory: 2048M
