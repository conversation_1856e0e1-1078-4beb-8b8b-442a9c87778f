#!/usr/bin/env bash

str="                       IMAP (601)
         E +----------------------+  +----------------------+
         m |   143 - IMAP (109)   |  |  993 - IMAPS (492)   |
         a +-----------------+----+  +-----------------+----+
         i | 98.35.70.186    | 2  |  | 139.226.155.246 | 4  |
         l | 119.129.75.77   | 3  |  | 18.143.13.57    | 4  |
           | 119.116.110.156 | 3  |  | 112.0.130.217   | 5  |
           | 58.38.120.117   | 3  |  | 76.28.153.254   | 6  |
           | 113.231.11.86   | 6  |  | 114.88.13.209   | 6  |
           | 119.236.128.180 | 8  |  | 54.255.103.240  | 6  |
           | 43.199.177.238  | 9  |  | 76.105.230.38   | 6  |
           | 18.167.217.64   | 14 |  | 38.42.104.80    | 8  |
           | 110.66.111.183  | 17 |  | 183.193.118.93  | 11 |
           | 76.28.153.254   | 28 |  | 18.138.155.139  | 15 |
           +-----------------+----+  +-----------------+----+"

LARK_URL="https://open.larksuite.com/open-apis/bot/v2/hook/fb5afbd8-4fcb-4741-be2c-24294baf5b**"
# Send lark alert
function alert(){
	title="$1"
	raw_data="$2"
	echo "$title"
	echo "$raw_data"
	# 使用 jq 来构建 JSON，有效避免转义问题
  payload=$(jq -n --arg title "$title" --arg raw_data "$raw_data" \
      '{msg_type: "text", content: {text: "\($title)\n\($raw_data)"}}')
  # 发送到 Lark
  curl -X POST -H "Content-Type: application/json" -d "$payload" "${LARK_URL}"
}

function check_db_status() {
    alert "IMAP 连接状态报告" "$str"
}

check_db_status
