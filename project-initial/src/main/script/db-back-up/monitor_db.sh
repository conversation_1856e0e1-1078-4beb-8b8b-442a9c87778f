#!/usr/bin/env bash

_m_vars='Slave_IO_Running|Slave_SQL_Running|Seconds_Behind_Master|Last_Errno'

_lark_webhook_url="$LARK_URL"

#apt-get update
#apt-get install -y curl
# Send lark alert
function alert(){
	title="$1"
	raw_data="$2"
	raw_data=$(echo $raw_data | tr -d '\n')
	raw_data=${raw_data//Slave/\\nSlave}
	raw_data=${raw_data//Last/\\nLast}
	raw_data=${raw_data//Seconds/\\nSeconds}
	echo "$title"
	echo "$raw_data"
  echo "#!/usr/bin/bash \n\ncurl -X POST -H \"Content-Type: application/json\" -d \"{\\\"msg_type\\\":\\\"text\\\",\\\"content\\\":{\\\"text\\\":\\\"$title \n $raw_data\\\"}}\" \"${_lark_webhook_url}\"" > /app/alert.sh
}

function check_db_status() {
    local vars=$(MYSQL_PWD="$DB_PWD" mysql -h $DB_HOST -P $DB_PORT -u $DB_USER --protocol=TCP  -e 'SHOW SLAVE STATUS \G;' | grep -E -i $_m_vars)
    echo "$vars"
    if [[ ${vars} != *"Slave_IO_Running"* ]];
    then
        alert "($APP_NAME) Cannot connect to slave db server." "${vars}"
        exit 1
    fi

    if [[ ${vars} == *": No"* ]];
    then
        alert "($APP_NAME) mariadb/mysql slave server error" "${vars}"
        exit 2
    fi
}

if [[ ${SKIP_CHECK_STATUS} == *"false"* ]];
then
    check_db_status ""
fi

# Dump mysql backup to gzip file
# --lock-tables=false
# s3 glacier
# --ignore-table=apn.persistent_audit_event --ignore-table=apn.persistent_audit_evt_data
## *** Change the compression level ***
# gzip allows you to specify a range of compression levels, from 1 to 9.
# -1 or --fast means fastest compression speed with minimal compression ratio,
# -9 or --best indicates the slowest compression speed with maximum compression ratio.
# The default compression level is -6.
MYSQL_PWD="$DB_PWD" mysqldump --lock-tables=$LOCK_TABLES -h $DB_HOST -P $DB_PORT -u $DB_USER --compress $DB_NAME | gzip -9 > /app/$DB_DUMP_FILENAME

