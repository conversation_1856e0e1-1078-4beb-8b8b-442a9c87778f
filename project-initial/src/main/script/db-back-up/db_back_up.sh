#!/usr/bin/bash

# ubuntu/mysql:8.0-21.04_beta
# mysql/mysql-server
# USAGE OPTION: [-i IMAGE] [-a APP_NAME] [-h DB_HOST] [-p DB_PORT] [-u DB_USER] [-w DB_PWD] [-d DB_NAME] [-f BAK_FOLDER] [-b AWS_BUCKET] [-l LARK_URL] [-c LOCK_TABLES] [-s SKIP_CHECK_STATUS]
# sh db_back_up.sh -i "ubuntu/mysql:8.0-21.04_beta" -a "APN-V3" -h "host.docker.internal" -p "4306" -u "root" -w "******" -d "apn" -f "/home/<USER>/db-back-up" -b "apnv3-db-staging-bak" -l "https://open.larksuite.com/open-apis/bot/v2/hook/ad04b427-7c17-4c3c-a364-c06031091fd1" -c false -s false

while getopts ":i:a:h:p:u:w:d:f:b:l:c:s:" opt
do
  case $opt in
  i)
    IMAGE=$OPTARG
    ;;
  a)
    APP_NAME=$OPTARG
    ;;
  h)
    DB_HOST=$OPTARG
    ;;
  p)
    DB_PORT=$OPTARG
    ;;
  u)
    DB_USER=$OPTARG
    ;;
  w)
    DB_PWD=$OPTARG
    ;;
  d)
    DB_NAME=$OPTARG
    ;;
  f)
    BAK_FOLDER=$OPTARG
    ;;
  b)
    AWS_BUCKET=$OPTARG
    ;;
  l)
    LARK_URL=$OPTARG
    ;;
  c)
    LOCK_TABLES=$OPTARG
    ;;
  s)
    SKIP_CHECK_STATUS=$OPTARG
    ;;
  ?)
    echo " USAGE OPTION: $0 [-i IMAGE] [-a APP_NAME] [-h DB_HOST] [-p DB_PORT] [-u DB_USER] [-w DB_PWD] [-d DB_NAME] [-f BAK_FOLDER] [-b AWS_BUCKET] [-l LARK_URL] [-c LOCK_TABLES] [-s SKIP_CHECK_STATUS] "
    exit 1
    ;;
  esac
done

if [ -z ${IMAGE} ]; then
    IMAGE=""
fi

if [ -z ${APP_NAME} ]; then
    APP_NAME="APP_NAME"
fi

if [ -z ${DB_HOST} ]; then
    DB_HOST=localhost
fi

if [ -z ${DB_PORT} ]; then
    DB_PORT=3306
fi

if [ -z ${DB_USER} ]; then
    DB_USER="root"
fi

if [ -z ${DB_PWD} ]; then
    DB_PWD=""
fi

if [ -z ${DB_NAME} ]; then
    DB_NAME=""
fi

if [ -z ${BAK_FOLDER} ]; then
    BAK_FOLDER=""
fi

if [ -z ${AWS_BUCKET} ]; then
    AWS_BUCKET=""
fi

if [ -z ${LARK_URL} ]; then
    LARK_URL="https://open.larksuite.com/open-apis/bot/v2/hook/ad04b427-7c17-4c3c-a364-c06031091fd1"
fi

if [ -z ${LOCK_TABLES} ]; then
    LOCK_TABLES="false"
fi

if [ -z ${SKIP_CHECK_STATUS} ]; then
    SKIP_CHECK_STATUS="false"
fi

DB_DUMP_FILENAME=$APP_NAME-db.`date +%Y-%m-%d_%H-%M`.sql.gz

# Send lark alert
function alert(){
	title="$1"
	raw_data="$2"
	raw_data=$(echo $raw_data | tr -d '\n')
	raw_data=${raw_data//Slave/\\nSlave}
	raw_data=${raw_data//Last/\\nLast}
	raw_data=${raw_data//Seconds/\\nSeconds}
	echo "$title"
	echo "$raw_data"
  curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"$title \n $raw_data\"}}" "${LARK_URL}"
}

docker run --rm -v "$BAK_FOLDER:/app" \
  -e "APP_NAME=$APP_NAME" \
  -e "DB_USER=$DB_USER" \
  -e "DB_PWD=$DB_PWD" \
  -e "DB_HOST=$DB_HOST" \
  -e "DB_PORT=$DB_PORT" \
  -e "DB_NAME=$DB_NAME" \
  -e "DB_DUMP_FILENAME=$DB_DUMP_FILENAME" \
  -e "LARK_URL=$LARK_URL" \
  -e "LOCK_TABLES=$LOCK_TABLES" \
  -e "SKIP_CHECK_STATUS=$SKIP_CHECK_STATUS" \
  "$IMAGE" "/app/monitor_db.sh"

result=$? # exit code. e.g. result=1 when exit 1 from last command.

#if [ $result -eq 0 ]; then
#    echo "DB dump created OK. File: $DB_DUMP_FILENAME"
#else
#    sleep 3
#    echo "DB dump FAILED!"
##    alert "DB dump FAILED!" "file: $DB_DUMP_FILENAME"
#    exit 1
#fi

case $result in
  0)
    echo "DB dump created OK. File: $DB_DUMP_FILENAME"
    ;;
  1)
    echo "($APP_NAME) Cannot connect to slave db server."
    sh alert.sh
    exit 1
    ;;
  2)
    echo "($APP_NAME) mariadb/mysql slave server error"
    sh alert.sh
    exit 1
    ;;
  ?)
    echo "Unknown exit code"
    alert "DB dump FAILED!" "file: $DB_DUMP_FILENAME"
    exit 1
    ;;
esac

### Upload dump file to S3
echo
echo $"Upload dump file to S3: $DB_DUMP_FILENAME"

docker run --rm -v $(pwd)/aws:/root/.aws -v $BAK_FOLDER:/aws amazon/aws-cli s3 cp /aws/$DB_DUMP_FILENAME s3://$AWS_BUCKET/$DB_DUMP_FILENAME

echo
echo "Now remove dump file: $DB_DUMP_FILENAME"
rm -rf $BAK_FOLDER/$DB_DUMP_FILENAME
echo "Operation Dump_and_Upload completed Successfully!"

