#!/usr/bin/env bash

# download and install liquibase https://www.liquibase.org/download
# python 3.9+
# set the paths of liquibase, mysql and python correctly

dbName=apn
dbPort=3306
dbUsername=root
dbPassword=

root=$(dirname "$PWD")
masterPath="$root"/liquibase/master.xml
filePath=config/liquibase/changelog/00000000000000_initial_schema.xml

liquibase --driver=com.mysql.cj.jdbc.Driver --classpath=../docker-files/seata/code/lib/jdbc/mysql-connector-java-8.0.19.jar --changeLogFile=../liquibase/master.xml --url="*********************************************************" --username=$dbUsername --password="$dbPassword" --includeObjects="null" generateChangeLog

liquibase --driver=com.mysql.cj.jdbc.Driver --classpath=../docker-files/seata/code/lib/jdbc/mysql-connector-java-8.0.19.jar --changeLogFile=../liquibase/changelog/00000000000000_initial_schema.xml --url="*********************************************************" --username=$dbUsername --password="$dbPassword" --includeObjects="user,role,user_role,persistent_audit_event,persistent_audit_evt_data,oauth_client_details" generateChangeLog

/usr/local/bin/python3 -c "from xml.etree import ElementTree;tree = ElementTree.parse('$masterPath');ElementTree.register_namespace('','http://www.liquibase.org/xml/ns/dbchangelog');root = tree.getroot();include=ElementTree.Element('include',attrib={'file': '$filePath','relativeToChangelogFile': 'false'});root.append(include);ElementTree.indent(tree);tree.write('$masterPath', encoding='utf-8', default_namespace=None, xml_declaration=True)"

if [[ -z "${dbPassword}" ]]; then
  tables=$(/usr/local/mysql/bin/mysql -u $dbUsername -D $dbName -e "SELECT table_name, create_time FROM information_schema.tables where table_schema='$dbName' and table_type='BASE TABLE';")
else
  tables=$(MYSQL_PWD="$dbPassword" /usr/local/mysql/bin/mysql -h127.0.0.1 --protocol=TCP -u $dbUsername -D $dbName -e "SELECT table_name, create_time FROM information_schema.tables where table_schema='$dbName' and table_type='BASE TABLE';")
fi

while IFS= read -r line
do
    tableName="${line%%$'\t'*}"
    createTime="${line#*$'\t'}"

    if [[ "${tableName}" != "TABLE_NAME"
        && ${tableName} != *"_bak"*
        && ${tableName} != *"delete"*
        && ${tableName} != "user"
        && ${tableName} != "authority"
        && ${tableName} != "user_authority"
        && ${tableName} != "persistent_audit_event"
        && ${tableName} != "persistent_audit_evt_data"
        && ${tableName} != "oauth_client_details"
        && ${tableName} != "oauth_client_token"
        && ${tableName} != "oauth_access_token"
        && ${tableName} != "oauth_refresh_token"
        && ${tableName} != "oauth_code"
        && ${tableName} != "oauth_approvals"
        && ${tableName} != "undo_log"
        && ${tableName} != "databasechangelog"
        && ${tableName} != "databasechangeloglock"
    ]]; then
    	createTime="${createTime//[\-]/}"
      createTime="${createTime//[\:]/}"
      createTime="${createTime//[\' \']/}"
      titleTableName="${tableName//[\_]/ }"
      titleTableName=$(python3 -c "print('$titleTableName'.title())")
      titleTableName="${titleTableName//[\' \']/}"
      fileName="$createTime"_added_entity_"$titleTableName".xml
      liquibase --driver=com.mysql.cj.jdbc.Driver --classpath=../docker-files/seata/code/lib/jdbc/mysql-connector-java-8.0.19.jar --changeLogFile=../liquibase/changelog/"$fileName" --url="*********************************************************" --username=$dbUsername --password="$dbPassword" --includeObjects="table:$tableName" generateChangeLog
      filePath=config/liquibase/changelog/"$fileName"
      /usr/local/bin/python3 -c "from xml.etree import ElementTree;tree = ElementTree.parse('$masterPath');ElementTree.register_namespace('','http://www.liquibase.org/xml/ns/dbchangelog');root = tree.getroot();include=ElementTree.Element('include',attrib={'file': '$filePath','relativeToChangelogFile': 'false'});root.append(include);ElementTree.indent(tree);tree.write('$masterPath', encoding='utf-8', default_namespace=None, xml_declaration=True)"
    fi
done <<< "$tables"
