#!/usr/bin/env bash

dbName=apn
dbPort=3306
dbUsername=root
dbPassword=

tables=$(/usr/local/mysql/bin/mysql -u $dbUsername -D $dbName -e "SELECT concat('ALTER TABLE ', TABLE_NAME, ' DROP FOREIGN KEY ', CONSTRAINT_NAME, ';') as drop_fk FROM information_schema.key_column_usage WHERE CONSTRAINT_SCHEMA = '$dbName' AND referenced_table_name IS NOT NULL;")

while IFS= read -r line
do
    /usr/local/mysql/bin/mysql -u $dbUsername -D $dbName -e "$line"
done <<< "$tables"
