#!/usr/bin/env bash

dbName=apnv3
dbPort=3306
dbUsername=root
dbPassword=

root=$(dirname "$PWD")

exclude_tables="('company_service_type', 'job_additional_info', 'job_bool_string', 'job_details_report', 'job_job_function_relation', 'job_minimum_degree_relation',
'job_preferred_degree_relation', 'job_preferred_languages_relation', 'job_required_languages_relation', 'talent_additional_info', 'talent_industry_relation', 'talent_job_function_relation', 'talent_language_relation',
'talent_recruitment_process_ipg_offer_letter_cost_rate')"

if [[ -z "${dbPassword}" ]]; then
  tables=$(/usr/local/mysql/bin/mysql -u $dbUsername -D $dbName -e "SELECT table_name, create_time FROM information_schema.tables where table_schema='$dbName' and table_type='BASE TABLE' and table_name not in $exclude_tables;")
else
  tables=$(MYSQL_PWD="$dbPassword" /usr/local/mysql/bin/mysql -h127.0.0.1 -P$dbPort --protocol=TCP -u $dbUsername -D $dbName -e "SELECT table_name, create_time FROM information_schema.tables where table_schema='$dbName' and table_type='BASE TABLE' and table_name not in $exclude_tables;")
fi

while IFS= read -r line
do
    tableName="${line%%$'\t'*}"
    createTime="${line#*$'\t'}"

    if [[ "${tableName}" = "company"*
        || ${tableName} = "invoice"*
        || ${tableName} = "start"*
        || ${tableName} = "talent"*
        || ${tableName} = "job"*
    ]]; then
    	#echo "$tableName"
    	sql="insert into permission_table(name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES ('$tableName', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');"
    	/usr/local/mysql/bin/mysql -u $dbUsername -D $dbName -e "$sql"
    fi
done <<< "$tables"
