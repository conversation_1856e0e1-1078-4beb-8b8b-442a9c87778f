#!/usr/bin/env bash

dbName=apn
dbPort=3306
dbUsername=root
dbPassword=

root=$(dirname "$PWD")

exclude_tables="('apn_param', 'async_bulk_parse_record', 'async_parse_record', 'async_record', 'authority', 'biz_dict',
'city_locations_cn', 'city_locations_en', 'college', 'column_dict', 'column_preference', 'currency_rate', 'databasechangelog',
'databasechangeloglock', 'degree', 'division', 'email_domain', 'enum_degree', 'entity_audit_event', 'enum_degree', 'enum_industry',
'enum_job_function', 'enum_language', 'enum_work_authorization', 'event', 'event_user', 'favorite_country', 'github_talent',
'industry', 'language', 'major', 'oauth_access_token', 'oauth_approvals', 'oauth_client_details', 'oauth_client_token', 'oauth_code',
'oauth_refresh_token', 'persistent_audit_event', 'persistent_audit_evt_data', 'role', 'sequence', 'skill', 'tag', 'task_record',
'tenant', 'tenant_admin_status', 'undo_log', 'unsubscribe_email_user', 'user')"

#include_tables="('activity', 'activity_details_report', 'activity_unique', 'application', 'company',
#'invoice', 'job', 'start', 'talent')"

if [[ -z "${dbPassword}" ]]; then
  tables=$(/usr/local/mysql/bin/mysql -u $dbUsername -D $dbName -e "SELECT table_name, create_time FROM information_schema.tables where table_schema='$dbName' and table_type='BASE TABLE' and table_name not in $exclude_tables;")
else
  tables=$(MYSQL_PWD="$dbPassword" /usr/local/mysql/bin/mysql -h127.0.0.1 -P$dbPort --protocol=TCP -u $dbUsername -D $dbName -e "SELECT table_name, create_time FROM information_schema.tables where table_schema='$dbName' and table_type='BASE TABLE' and table_name not in $exclude_tables;")
fi

while IFS= read -r line
do
    tableName="${line%%$'\t'*}"
    createTime="${line#*$'\t'}"

    if [[ "${tableName}" != "TABLE_NAME"
        && ${tableName} != *"_bak"*
        && ${tableName} != *"delete"*
        && ${tableName} != "permission_"*
        && ${tableName} != "user_"*
    ]]; then
    	#echo "$tableName"
    	echo "alter table $tableName add column puser_id bigint;"
    	#/usr/local/mysql/bin/mysql -u $dbUsername -D $dbName -e "alter table $tableName add column puser_id bigint;"
    	#/usr/local/mysql/bin/mysql -u $dbUsername -D $dbName -e "alter table $tableName add column pteam_id bigint;"
    fi
done <<< "$tables"
