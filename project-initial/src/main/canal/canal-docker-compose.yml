version: '3'
services:
  apnpublic-mysql:
    image: mysql/mysql-server:latest
    ports:
      - "3326:3306"
    expose:
      - "3306"
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_ROOT_HOST: '%'
    volumes:
      # mysql config, setting binlog
      #- ./apnpublic/conf:/etc/mysql/conf.d
      - ./apnpublic/conf/my.cnf:/etc/my.cnf
      - ./apnpublic/sql:/docker-entrypoint-initdb.d
  canal-mysql:
    image: mysql/mysql-server:latest
    ports:
      - "3316:3306"
    expose:
      - "3306"
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_ROOT_HOST: '%'
    volumes:
      # mysql init sql, create canal-manager database and tables used by canal-admin
      - ./canal:/docker-entrypoint-initdb.d
      # mysql config, setting binlog
      #- ./conf:/etc/mysql/conf.d
  canal-admin:
    restart: always
    ports:
      # 提供admin website界面，以及canal-server连接canal-admin的端口，不可暴露在公网
      - "8089:8089"
    expose:
      - "8089"
    environment:
      # 对应canal-server配置中的canal.admin.user/canal.admin.passwd，
      # 这是一个双向认证，canal-server会以这个密文和canal-admin做请求，
      # 同时canal-admin也会以密码原文生成加密串后和canal-server进行admin端口链接，
      # 所以这里一定要确保这两个密码内容的一致性
      - canal.adminUser=canal-acl-user
      - canal.adminPasswd=canal-acl-password

      # canal-admin 使用外部mysql数据库做存储，
      # 必须设置spring.datasource.address，不然canal-admin内部会启动一个mysql服务
      - spring.datasource.address=canal-mysql:3306
      - spring.datasource.database=canal_manager
      - spring.datasource.username=root
      - spring.datasource.password=123456
      - spring.datasource.url=jdbc:mysql://$${spring.datasource.address}/$${spring.datasource.database}?useUnicode=true&allowPublicKeyRetrieval=True&characterEncoding=UTF-8&useSSL=false
    image: canal/canal-admin:v1.1.6
    links:
      - canal-mysql
      - apnpublic-mysql
    depends_on:
      - canal-mysql
      - apnpublic-mysql
  canal-server:
    restart: always
    ports:
      - "11110:11110"
      - "11111:11111"
    expose:
      - "11110"
      - "11111"
    environment:
      - canal.admin.manager=canal-admin:8089
      # 提供给canal-admin交互的接口
      - canal.admin.port=11110
      # admin管理指令链接的ACL配置账号
      # 对应canal-admin配置中的canal.adminUser/canal.adminPasswd
      - canal.admin.user=canal-acl-user
      # admin管理指令链接的ACL配置密码，使用密文存储，可在mysql使用以下方法生成
      # mysql8.0以上版本，可以使用select upper(sha1(unhex(sha1('<canal-admin的环境变量canal.adminPasswd的值>'))))
      - canal.admin.passwd=052756E931D5F0BCCF5D2AB015CC9BC2A249933E
    links:
      - canal-mysql
      - canal-admin
      - apnpublic-mysql
    image: canal/canal-server:v1.1.6
    depends_on:
      - canal-admin