CREATE USER canal IDENTIFIED BY 'canal';
GRANT SELECT, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'canal'@'%';
ALTER USER 'canal'@'%' IDENTIFIED BY 'canal' PASSWORD EXPIRE NEVER;
ALTER USER 'canal'@'%' IDENTIFIED WITH mysql_native_password BY 'canal';
FLUSH PRIVILEGES;

create database apnpublic;
use apnpublic;
create table talent
(
    id bigint auto_increment
        primary key,
    first_name varchar(255) collate utf8_unicode_ci null,
    last_name varchar(255) collate utf8_unicode_ci null,
    full_name varchar(255) collate utf8_unicode_ci null,
    created_by varchar(50) collate utf8_unicode_ci not null,
    created_date timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by varchar(50) collate utf8_unicode_ci null,
    last_modified_date timestamp null
);

create table talent_skill
(
    id bigint auto_increment
        primary key,
    skill_name varchar(255) not null,
    regulated_name varchar(255) null,
    necessity int null,
    talent_id bigint null,
    created_by varchar(50) not null,
    created_date timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by varchar(50) null,
    last_modified_date timestamp null
);

create table job
(
    id bigint auto_increment
        primary key,
    title varchar(255) collate utf8_unicode_ci null,
    created_by varchar(50) collate utf8_unicode_ci not null,
    created_date timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by varchar(50) collate utf8_unicode_ci null,
    last_modified_date timestamp null
);