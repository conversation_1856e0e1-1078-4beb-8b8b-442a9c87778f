spring:
  cache:
    type: redis
    redis:
      cache-null-values: true
      use-key-prefix: true
  redis:
    host: localhost
    host-rater: localhost
    host-parser: localhost
    port: 6379
    port-rater: 6379
    port-parser: 6379
    database: 0
    timeout: 3000
    #password:
    jedis:
      pool:
        max-active: -1 # Maximum number of connections that can be allocated by the pool at a given time. Use a negative value for no limit.
        max-wait: -1 # ms, Maximum amount of time a connection allocation should block before throwing an exception when the pool is exhausted. Use a negative value to block indefinitely.
        max-idle: 8 # Maximum number of "idle" connections in the pool. Use a negative value to indicate an unlimited number of idle connections.
        min-idle: 0
  data:
    redis:
      repositories:
        enabled: false  
application:
  onlineUserStatisticPeriod: 480 # seconds