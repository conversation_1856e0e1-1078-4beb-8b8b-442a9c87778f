application:
  esfiller:
    baseUrl: http://*********:5061/filler
#   the es interface address of job, talent and other data synchronization update(v2 only)
    updateUrl: http://*********:5061/filler/v2/tenant/
#   the es interface address of job, talent and other data synchronization
    syncUrl: http://*********:5061/filler/v3/tenant/
    maxNumberOfThreads: 1
#   maximum number of retries for job and talent data synchronization es interface failure
    pauseSyncThreshold: 10
#   token of the lark alarm interface for job and talent data synchronization failure
    larkWebhookKey:
#   data synchronization of job and talent fails interface u of lark alarm interface
    larkWebhookUrl: https://open.larksuite.com/open-apis/bot/v2/hook/decac968-48ff-4f40-aa17-2d113b6ae455
#   the time interval of the job and talent data automatic synchronization es scheduled tasks, in seconds
    scheduledSyncRate: 20
  elasticrecord:
    url: http://*********:9210/
    only-read-ip-port: *********:9210
    talent-template: '{"index_patterns":["activity_talent_*"],"template":{"settings":{"index":{"lifecycle":{"name":"activity_talent_policy"},"number_of_replicas":"0"}},"mappings":{"dynamic_templates":[],"properties":{"@timestamp":{"type":"date"},"createdDate":{"type":"date"},"talentId":{"type":"keyword"},"createdBy":{"type":"keyword"},"changeFields":{"type":"nested","properties":{"changedTo":{"type":"text"},"changedFrom":{"type":"text"},"eventType":{"type":"keyword"},"key":{"type":"keyword"},"fieldId":{"type":"keyword"}}}}}}}'    
    hot-cold-policy: '{"policy":{"phases":{"hot":{"min_age":"0ms","actions":{"rollover":{"max_age":"400d","max_primary_shard_size":"50gb"},"set_priority":{"priority":100}}},"cold":{"min_age":"400d","actions":{"set_priority":{"priority":0}}}}}}'
    job-template: '{"index_patterns":["activity_job_*"],"template":{"settings":{"index":{"lifecycle":{"name":"activity_job_policy"},"number_of_replicas":"0"}},"mappings":{"dynamic_templates":[],"properties":{"@timestamp":{"type":"date"},"createdDate":{"type":"date"},"jobId":{"type":"keyword"},"createdBy":{"type":"keyword"},"changeFields":{"type":"nested","properties":{"changedTo":{"type":"text"},"changedFrom":{"type":"text"},"eventType":{"type":"keyword"},"key":{"type":"keyword"},"fieldId":{"type":"keyword"}}}}}}}'
    job-snapshot-template: '{"priority":100,"index_patterns":["snapshot_jobs_*"],"template":{"mappings":{"dynamic":"false","properties":{"jobId":{"type":"keyword"},"snapshotCreatedDate":{"type":"date","format":"date_optional_time"}}},"settings":{"index":{"refresh_interval":"2s","number_of_shards":"1","number_of_replicas":"0"}}}}'
    assignment-template: '{"index_patterns":["activity_assignment_*"],"template":{"settings":{"index":{"lifecycle":{"name":"activity_assignment_policy"},"number_of_replicas":"0"}},"mappings":{"dynamic_templates":[],"properties":{"@timestamp":{"type":"date"},"createdDate":{"type":"date"},"createdBy":{"type":"keyword"},"assignmentId":{"type":"keyword"},"recordType":{"type":"keyword"},"operationType":{"type":"text"},"changeFields":{"type":"nested","properties":{"changedTo":{"type":"text"},"changedFrom":{"type":"text"},"eventType":{"type":"keyword"},"key":{"type":"keyword"}}}}}}}'

  commonService: http://*********:8283
  commonServicePin: IPG888hq