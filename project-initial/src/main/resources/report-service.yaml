server:
  port: 9011
  servlet:
    context-path: /report
  compression:
    enabled: true
    mime-types: application/json
spring:
  jpa:
    properties:
      #hibernate.session_factory.interceptor: com.altomni.apn.common.interceptor.SecurityDataLevelInterceptor

report:
  download:
    month:
     partition: 4

region: '{"EU":[4,16,17,22,23,29,32,42,44,45,47,56,58,59,62,64,72,73,78,79,80,90,95,96,106,108,121,130,131,135,136,142,143,150,155,156,166],"SEA":[141,100,159,173,129,75],"US":[1],"CA":[2],"CN":[7],"UK":[3],"NL":[115],"IN":[74],"JP":[5]}'

application:
  report:
    e5:
      url: https://apn-v3-dev.hitalentech.com/reports/detail/82?insufficient=true&teamList=
  sync:
    threadNum: 10
  superset: 
    domain: http://10.0.0.20:9088
    username: admin
    password: admin
    datasourceId: 2
    enabled: false
  larkService:
    callbackUrl: https://open-sg.larksuite.com/anycross/trigger/callback/ODlkZTllMzNlMTcwNzQxYzQ3N2Y4Mzg3NDczNzg1ZGQw
    authenticationEndPoint: https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal
    sendMessageEndpoint: https://open.larksuite.com/open-apis/im/v1/messages
    messageNotificationService:
      appId: cli_a7b5073fa6b8d009
      appSecret: zbaZn4nyEmI8WzAY99kgHgbMb4VghHnX

api-report-properties:
  reportService:
    report_checkParam_paramNull:
      titleChina: "请选择一个日期范围。"
      titleEng: "Please select a date range."      
    report_checkInList_conditionLimit:
      titleChina: "查询 sql 中的条件列表 > 1000 大于 1"
      titleEng: "query sql in condition list > 1000 more than 1" 
    reportJob_checkParam_paramNull:
      titleChina: "参数为空"
      titleEng: "param is null" 
    reportJob_checkParam_jobNull:
      titleChina: "jobId 为空"
      titleEng: "jobId is null"
    reportJob_checkParam_activityIdNull:
      titleChina: "activityId 为 null"
      titleEng: "activityId is null"  
    reportPipeline_p1PipelineAnalyticsByUsers_userRoleNull:
      titleChina: "错误的请求参数 需要用户角色"
      titleEng: "Bad request parameter User role is required"
    reportSales_checkParam_jobTypeNull:
      titleChina: "jobType 为空"
      titleEng: "jobType is null"
    reportSales_checkParam_applicationIdsNull:
      titleChina: "applicationIds 为空"
      titleEng: "applicationIds is null"
    reportUserJob_findAllJobByIds_jobIdNull:
      titleChina: "jobIds 不能为空。"
      titleEng: "The jobIds cannot be empty."
    reportUserJob_findAllJobByIds_talentIdsNull:
      titleChina: "talentIds不能为空。"
      titleEng: "The talentIds cannot be empty."
    report_timesheet_checkParam_typeNull:
      titleChina: "过滤器类型为空"
      titleEng: "filter type is null"
    report_countAllDormantApplications_IllegalValue:
      titleChina: "非法状态枚举值！"
      titleEng: "Illegal status enum value!"
    reportUserJob_param_exception:
      titleChina: "参数异常。"
      titleEng: "Parameter exception."
    report_download_data_max_size:
      titleChina: "您尝试下载数据的数量已经超过10,000条, 请调整filter后重新尝试"
      titleEng: "The quantity of data you requested to download has exceeded 10,000 records. Please adjust the filter and try again."