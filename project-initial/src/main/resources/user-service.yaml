server:
  port: 9005
  servlet:
    context-path: /user
spring:
  jpa:
    properties:
      #hibernate.session_factory.interceptor: com.altomni.apn.common.interceptor.SecurityDataLevelInterceptor

application:
  security:
    aes:
      secretKey: 1234567890abcdef # The secret key used for encryption (must be 16, 24, or 32 bytes long)
  user:
    account:
      expire_time: 600
      isOpen: true

api-user-properties:
  userService:
    customconfig_getColumnConfigByUserId_userIdNull:
      titleChina: "用户ID无效"
      titleEng: "UserId is invalid"  
    customconfig_searchDashboardColumnConfigByUserId_paramNull:
      titleChina: "数据需要初始化"
      titleEng: "data need init"
    customconfig_getJobColumnDefaultConfig_error:
      titleChina: "未找到用户配置。请联系系统管理员寻求帮助。"
      titleEng: "We can not find the configuration of the user. Please reach out to the system administor for help."
    customconfig_getFormConfigByRecruitmentProcessId_recruitmentProcessIdNull:
      titleChina: "recruitmentProcessId 无效！"
      titleEng: "recruitmentProcessId is not valid!"
    customconfig_getJobFormSubCategory_responseStatusNotOk:
      titleChina: "您当前的招聘流程不包含职位表单模板。请确保您提供有效的招聘流程。"
      titleEng: "Your current recruitment process does not include a Job Form template. Please ensure you provide a valid recruitment process."
    customconfig_pipeline_findByUserId_userIdNull:
      titleChina: "用户信息无效。"
      titleEng: "The user information is invalid."
    customconfig_pipeline_deleteById_notFound:
      titleChina: "未找到 ID 为 #1 的管道列模板。"
      titleEng: "Pipeline column template with ID #1 not found."  
    customconfig_pipeline_createColumnConfig_templateNameNull:
      titleChina: "请输入一个模版名称。"
      titleEng: "Please enter a template name."  
    customconfig_pipeline_createColumnConfig_sametemplateName:
      titleChina: "模版名称已存在。"
      titleEng: "The template name exists."
    customconfig_pipeline_updateColumnConfig_invalidTemplate:
      titleChina: "模板 ID 无效！"
      titleEng: "Invalid Template Id!"
    customconfig_pipeline_updateColumnConfig_noPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions."  
    customconfig_getSystemConfigByCategory_notFound:
      titleChina: "未找到 #1 。"
      titleEng: "#1 cannot not find"
    customconfig_getFormConfigByTenantId_invalidTenantId:
      titleChina: "租户 ID 无效！"
      titleEng: "tenant id is not valid!"
    customconfig_saveTalentFormConfig_invalidTenant:
      titleChina: "租户信息无效。"
      titleEng: "The tenant information is invalid."  
    permission_findDataPermissionByUserId_invalidUser:
      titleChina: "无效用户"
      titleEng: "Invalid user" 
    permission_findDataPermissionByUserId_tenantNull:
      titleChina: "未找到租户。"
      titleEng: "No tenant found." 
    permission_validateTenantPrivilege_invalid:
      titleChina: "Apn配置的权限无法更新为不显示的状态。"
      titleEng: "Apn configured privileges cannot be updated to a status that is not displayed."
    permissionModule_create_invalidId:
      titleChina: "模块ID必须为空！"
      titleEng: "Module ID must be null!"
    permissionPrivilege_create_invalidId:
      titleChina: "ID 必须为空！"
      titleEng: "Id must be null!"
    permissionPrivilege_create_idNotNull:
      titleChina: "ID不能为空！"
      titleEng: "Id cannot be empty!"
    permissionRole_create_roleIdNotNull:
      titleChina: "角色ID必须为空！"
      titleEng: "Role ID must be null!"
    permissionRole_create_roleIdNull:
      titleChina: "角色ID不能为空！"
      titleEng: "Role ID cannot be null!"
    permissionRole_findRolesByUserId_invalidUser:
      titleChina: "无效用户 #1"
      titleEng: "Invalid user #1"
    permissionTable_create_tableIdMustNull:
      titleChina: "表 ID 必须为空！"
      titleEng: "Table ID must be null!"
    permissionTable_create_tableIdNull:
      titleChina: "表 ID 不能为空！"
      titleEng: "Table ID cannot be null!"
    permissionTeam_create_codeNull:
      titleChina: "生成队伍代码错误！"
      titleEng: "Generate team code error!"
    permissionTeam_checkTeamName_nameNull:
      titleChina: "请输入一个团队名字。"
      titleEng: "Please enter a team name."  
    permissionTeam_checkTeamName_nameExist:
      titleChina: "团队名称已存在。"
      titleEng: "Team name already exists."
    permissionTeam_checkTeamLevel_error:
      titleChina: "团队结构层次最多为5层。"
      titleEng: "The level of the team hierarchy is up to 5."
    permissionTeam_delete_invalidTeam:
      titleChina: "无效的队伍 #1"
      titleEng: "Invalid team #1"
    permissionTeam_delete_subTeam:
      titleChina: "在删除团队之前，请先删除团队下的所有子团队。"
      titleEng: "To delete team, please delete the all sub-teams under the team first."
    permissionTenant_findModulesByCurrentTenant_invalidTenant:
      titleChina: "租户 ID #1 无效"
      titleEng: "Invalid tenant id #1"
    permissionUserTeam_removeUsersFromTeam_notRemoved:
      titleChina: "无法将用户移出其主要团队。"
      titleEng: "The user can not removed from the primary team they assigned to."
    permissionUserTeam_checkoutExistUserTeam_userExist:
      titleChina: "以下用户已经在团队#3：#1，#2。"
      titleEng: "The following users are in Team #3： #1, #2."
    user_getUserCreditLimit_userCreditLimitNull:
      titleChina: "请为租户＃1 设置用户信用"
      titleEng: "Please setup user credit for tenant #1"
    user_getParamById_paramNotExist:
      titleChina: "参数不存在，请先设置"
      titleEng: "Param not exist, please setup first"  
    user_creditTransaction_create_talentNull:
      titleChina: "此人才没有需要购买的额外联系人！"
      titleEng: "There are no extra contacts with this talent need to purchase!"
    user_creditTransaction_updateBalance_userAccountNull:
      titleChina: "该用户没有账户，请编辑该用户"
      titleEng: "this user do not have account,please edit this user"
    user_creditTransaction_updateTalentId_idNull:
      titleChina: "CreditTransaction ID 不能为空。"
      titleEng: "CreditTransaction id can not be null ."
    user_creditTransaction_updateTalentId_talentIdNull:
      titleChina: "CreditTransaction talentId 不能为空。"
      titleEng: "CreditTransaction talentId can not be null ."
    user_creditTransaction_updateTalentId_notExist:
      titleChina: "要更新的信用交易不存在。"
      titleEng: "CreditTransaction to update dose not exist ."  
    user_creditTransaction_updateTalentId_notPermission:
      titleChina: "您无权更新其他租户的 CreditTransaction 。"
      titleEng: "You are not authorized to update other tenant's CreditTransaction ."
    user_creditTransaction_unlockTalent_internalError:
      titleChina: "内部服务器错误。"
      titleEng: "Internal Server Error"
    user_socialLogin_badCredential:
      titleChina: "内部服务器错误。"
      titleEng: "Internal Server Error"
    user_addUser_accountLimit:
      titleChina: "您的账号已满，如需更多账号，请通过页面右下角客服联系我们，感谢您的使用。"
      titleEng: "Your account number is fully saturated. If you need more accounts, Please contact us through customer service at the bottom right corner of the page. Thank you for your use." 
    user_linkedinLogin_failGetUser:
      titleChina: "无法获取 LinkedIn 用户信息"
      titleEng: "Failed to get linkedin user info"
    user_team_create_teamExist:
      titleChina: "团队名称已存在。"
      titleEng: "Team name already exists."
    user_teamUser_multiAddUsers_teamNull:
      titleChina: "未找到团队。"
      titleEng: "No team found."
    user_teamUser_checkPermission_userNull:
      titleChina: "未找到用户。"
      titleEng: "User not found"
    user_teamUser_checkPermission_noPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions."
    user_verifyEffectiveTime_timeError:
      titleChina: "请确保授权开始时间为未来日期。"
      titleEng: "Please make sure the authorization start time is in the future."
    user_verifyExpireTime_expireTimeNull:
      titleChina: "请输入授权过期时间。"
      titleEng: "Please enter an authorization expiration time."
    user_verifyExpireTime_expireTimeAfterEffectiveTime:
      titleChina: "请确保授权过期时间在授权开始时间之后。"
      titleEng: "Please make sure the expiration time is later than the authorization start time."
    user_verifyExpireTime_expireTimeError:
      titleChina: "请确保授权过期时间在开始时间至多7天之后。"
      titleEng: "Please make sure the authorization expiration time is at most 7 days from authorization start time."
    user_revokeMyImpersonation_notFind:
      titleChina: "未找到可用的模拟记录。"
      titleEng: "Did not find available impersonation record."  
    user_login_inactiveUser:
      titleChina: "不允许非活动用户登录"
      titleEng: "Inactive user is not allowed to login"
    user_login_deniedPermission:
      titleChina: "暂无权限查看该跟进备注。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    user_validateAccount_tenantNull:
      titleChina: "招聘人员需要租户"
      titleEng: "Tenant is required for recruiter"
    user_validateAccount_emailExist:
      titleChina: "邮件地址已存在。"
      titleEng: "Email address already exists."
    user_validateAccount_userNameExist:
      titleChina: "用户名已存在。"
      titleEng: "Username already exists"  
    user_registerAccount_tenantNotExist:
      titleChina: "找不到用于用户注册的租户"
      titleEng: "Can not find tenant for user registration" 
    user_addUser_accountMaxLimit:
      titleChina: "您的账号已满，如需更多账号，请通过页面右下角客服联系我们，感谢您的使用。"
      titleEng: "Your account number is fully saturated. If you need more accounts, Please contact us through customer service at the bottom right corner of the page. Thank you for your use." 
    user_createUser_totalUserCreditError:
      titleChina: "信用应该少一点#1"
      titleEng: "credit should be less #1"
    user_createTenantAdminUser_emailAlreadyReg:
      titleChina: "邮件地址已存在。"
      titleEng: "Email address already exists."
    user_createTenantAdminUser_userInfoDeficiency:
      titleChina: "用户管理信息缺陷"
      titleEng: "user admin information  deficiency"
    user_createTenantAdminUser_error:
      titleChina: "无法创建租户管理员，请确保用户信息正确。"
      titleEng: "Failed to create tenant admin, please make sure the user information is correct."
    user_updateUser_userIdNotExist:
      titleChina: "未找到用户。"
      titleEng: "No user found."
    user_updateUser_noPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions."
    user_checkRequiredFields_empty:
      titleChina: "必填字段不能为空。"
      titleEng: "The mandatory field cannot be empty ."
    user_checkPrimaryTeam_primaryTeamIdNull:
      titleChina: "必须分配主要团队 ID！"
      titleEng: "Primary team ID must be assigned!"
    user_checkPrimaryTeam_primaryTeamNotExist:
      titleChina: "未找到团队。"
      titleEng: "No team found."  
    user_validateCredit_creditNotEnough:
      titleChina: "剩余批量积分额度不足。"
      titleEng: "You've run out of your bulk credits." 
    user_validateCredit_monthlyCreditNotEnough:
      titleChina: "本月剩余的积分额度不足。请升级您的计划或购买更多批量信用额度。"
      titleEng: "You've run out of your monthly credits. You may upgrade your plan or buy more bulk credits." 
    user_validateCredit_monthEffectCreditNotEnough:
      titleChina: "剩余的下个月积分额度不足。"
      titleEng: "You've run out of the credits for next month."   
    user_createLimitUse_tenantIncorrect:
      titleChina: "未找到租户。"
      titleEng: "No tenant found." 
    user_checkLockedAccount_lockAccount:
      titleChina: "您的账号将被限制 #1 分钟。"
      titleEng: "Your account is restricted for #1 minutes." 
    user_createTenantAdminUser_userIdNotNull:
      titleChina: "新用户不能已经拥有 ID"
      titleEng: "A new user cannot already have an ID" 
    user_createTenantAdminUser_tenantNull:
      titleChina: "未找到租户。"
      titleEng: "No tenant found." 
    user_updateUserStatus_invalidTenantUser:
      titleChina: "未找到租户。"
      titleEng: "No tenant found."  
    user_updateUserStatus_userNotExist:
      titleChina: "未找到用户。"
      titleEng: "No user found."
    user_updateUserStatus_notSetStatus:
      titleChina: "被冻结的租户无法设置用户状态。"
      titleEng: "A frozen tenant cannot set the user status."  
    user_searchUserList_tenantNotExist:
      titleChina: "未找到租户。"
      titleEng: "No tenant found."  
    user_socialLogin_invalidUser:
      titleChina: "无效的用户对象"
      titleEng: "Invalid User Object"  
    user_createCreditTransaction_idNotNull:
      titleChina: "新的 creditTransaction 不能已经有一个 ID"
      titleEng: "A new creditTransaction cannot already have an ID"
    user_createTeam_teamIdNotNull:
      titleChina: "要创建的对象不能已经有一个 ID。"
      titleEng: "The object to create cannot already have an id."
    user_updateTenant_tenantIdNull:
      titleChina: "要更新的对象没有 ID。"
      titleEng: "The object to update do not  have an id."

      