spring:
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS-SERVICE-ADDR:localhost:8848}
        namespace: ${NAMESPACE:dev}
        username: ${NACOS-USERNAME:nacos}
        password: ${NACOS-PASSWORD:nacos}
        metadata:
          management:
            context-path: ${server.servlet.context-path}/actuator
    sentinel:
      transport:
        dashboard: sentinel:13052
        port: 8720
      datasource:
        flow:
          nacos:
            # cannot config username and password
            server-addr: ${spring.cloud.nacos.config.server-addr}
            namespace: ${NAMESPACE:dev}
            group-id: DEFAULT_GROUP
            data-id: sentinel-${spring.application.name}-flow-rules.json
            data-type: json
            rule-type: flow
        #degrade:
        #  nacos:
        #    server-addr: ${spring.cloud.nacos.config.server-addr}
        #    namespace: ${namespace:dev}
        #    group-id: DEFAULT_GROUP
        #    data-id: sentinel-${spring.application.name}-degrade-rules.json
        #    data-type: json
        #    rule-type: degrade
        #param-flow:
        #  nacos:
        #    server-addr: ${spring.cloud.nacos.config.server-addr}
        #    namespace: ${namespace:dev}
        #    group-id: DEFAULT_GROUP
        #    data-id: sentinel-${spring.application.name}-param-flow-rules.json
        #    data-type: json
        #    rule-type: param-flow
        #system:
        #  nacos:
        #    server-addr: ${spring.cloud.nacos.config.server-addr}
        #    namespace: ${namespace:dev}
        #    group-id: DEFAULT_GROUP
        #    data-id: sentinel-${spring.application.name}-system-rules.json
        #    data-type: json
        #    rule-type: system
        #authority:
        #  nacos:
        #    server-addr: ${spring.cloud.nacos.config.server-addr}
        #    namespace: ${namespace:dev}
        #    group-id: DEFAULT_GROUP
        #    data-id: sentinel-${spring.application.name}-authority-rules.json
        #    data-type: json
        #    rule-type: authority

  zipkin:
    base-url: http://zipkin:9411
  sleuth:
    sampler:
      probability: 1

  mvc:
    throw-exception-if-no-handler-found: true
  web:
    resources:
      add-mappings: false

  servlet:
    multipart:
      enabled: true
      max-file-size: 30MB
      max-request-size: 100MB

  http:
    multipart:
      enabled: true
      max-file-size: 30MB
      max-request-size: 100MB 
      
server:
  servlet:
    encoding:
      force: true 
  max-http-header-size: 102400
  tomcat:
    threads:
      min-spare: 20
      max: 2000
    accept-count: 1000
    max-connections: 10000
    keep-alive-timeout: 60000
    max-keep-alive-requests: 200
    connection-timeout: 60000
  compression:
    enabled: true
  http2:
    enabled: true 

feign:
  sentinel:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 600000
        readTimeout: 600000
  okhttp:
    enabled: true

application:
  oauth2:
    jwtSignKey: dKrGer9cn^NgHHkkxAJaj@$fenW6erCBa4g@z%v&RYo
  security:
    enableObjectLevelSecurity: true
    aes:
      secretKey: 1234567890abcdef # The secret key used for encryption (must be 16, 24, or 32 bytes long)
  apnInternalPin: 3hlo7PZn
  ipgApplicationRuleTenantIds: 4,41
  useGmailAlias: true
  activeUserPeriod: 72000  # the user who is inactive for 1 hour will be logged out
  crmUrl: https://api-crm-dev.hitalentech.com
publicSpringCloud:
  submitToJobPermissionKey: PERMISSION_SUBMIT_ONBOARDED_TALENT_TO_JOB

    