spring:  
  security:
    oauth2:
      resourceserver: # 资源服务器相关配置
        opaquetoken: # 使用不透明 token
          client-id: apn_management_app
          client-secret: g1vdcWjNT0Wj0h7v
          introspection-uri: https://api-sso-dev.hitalentech.com/oauth2/introspect
sso:
  address: https://api-sso-dev.hitalentech.com
# 配置 grpc
grpc:
  client:
    ipg-sso-grpc: 
      address: dns:/sso-server.apn-dev:9090
      enableKeepAlive: true
      negotiationType: plaintext