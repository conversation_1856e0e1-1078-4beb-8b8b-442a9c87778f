spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    url: ${DB_URL:*******************************************************************************************************************************************************************}
    username: ${DB_USERNAME:apn_dev_admin}
    password: ${DB_PASSWORD:HUty78_2iO}
    # url: ${DB_URL:*****************************************************************************************************************************************************************}
    # username: ${DB_USERNAME:apn_admin}
    # password: ${DB_PASSWORD:xw7#]!ZQ"nDnKa}
    driver-class-name: com.mysql.cj.jdbc.Driver
    starrocks:
      url: ***************************************************
      username: root
      password:
      enabled: false
    hikari:
      minimum-idle: 10
      maximum-pool-size: 450
      max-lifetime: 400000
      idle-timeout: 300000
      connection-timeout: 500000
      poolName: Hikari
      connection-init-sql: SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci
      leak-detection-threshold: 300000
      data-source-properties:
        cachePrepStmts: true
        prepStmtCacheSize: 250
        prepStmtCacheSqlLimit: 2048
        useServerPrepStmts: true
  jpa:
    database-platform: org.hibernate.dialect.MySQL5InnoDBDialect
    database: MYSQL
    show-sql: true
    properties:
      hibernate.id.new_generator_mappings: true
      hibernate.cache.use_second_level_cache: false
      hibernate.cache.use_query_cache: false
      hibernate.generate_statistics: true
  liquibase:
    enabled: false
    change-log: /config/liquibase/master-${spring.application.name}.xml
  data:
    voipMongo:
      uri: mongodb://apn:%21pgA9n%40%28N6%26834@*********:27017/voip?authSource=perm&ssl=false&connectTimeoutMS=10000&socketTimeoutMS=30000&maxIdleTimeMS=30000&maxWaitTimeMS=50000&maxPoolSize=50&minPoolSize=5&serverSelectionTimeoutMS=50000
    statsMongo:
      uri: *********************************************************************************************************
      connection-timeout: 10000
      socket-timeout: 30000
      max-connection-idle-time: 60000
      max-wait-time: 50000
      connections-per-host: 50