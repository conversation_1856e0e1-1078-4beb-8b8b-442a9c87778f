logging:
  pattern:
    #console: "%green(%d{yyyy-MM-dd HH:mm:ss.SSS}) %highlight(%level) ${spring.application.name:-} %X{traceId:-} %X{spanId:-} %magenta(%thread) %yellow(%c) %cyan(%M) %highlight(%L) %msg %n"
    # Truncate from the end if the logger name is longer than 1000 characters.
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %level ${spring.application.name:-xxx} %thread %c %M %L %X{traceId:-xxx} %X{spanId:-xxx} %.-4000m %n"
    #file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %level ${spring.application.name:-} %thread %c %M %L %X{traceId:-} %X{spanId:-} %X{X-B3-ParentSpanId:-} %m %n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %level ${spring.application.name:-xxx} %thread %c %M %L %X{traceId:-xxx} %X{spanId:-xxx} %.-4000m %n"
  level:
    io: INFO
    org.hibernate: WARN
    org.apache: INFO
    com.alibaba: INFO
    javax: INFO
    org.springframework: INFO
    sun: INFO
    liquibase: INFO
    liquibase.resource: WARN
    _org: INFO
    com.zaxxer: INFO
    org.reflections: INFO
    org.aspectj: INFO
    jdk: INFO
    java.io: INFO
    com.altomni.apn: DEBUG
    org.mongodb.driver.connection: WARN


  logback:
    rollingpolicy:
      file-name-pattern: logs/${spring.application.name}/%d{yyyy-MM-dd}-%i.log #.zip
      max-file-size: 50MB # the size of single log file is up to 50 MB
      max-history: 30 # keep 30 days' history logs
      total-size-cap: 500MB # the total size of all log files is up to 500 MB
      clean-history-on-start: false
  file:
    path: logs/${spring.application.name}