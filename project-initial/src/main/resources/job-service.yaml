server:
  port: 9003
  servlet:
    context-path: /job

spring:
  jpa:
    properties:
      hibernate.session_factory.interceptor: com.altomni.apn.common.interceptor.SecurityDataLevelInterceptor

application:
  sync:
    threadNum: 6
    total: 200
  job:
    searchKeywordHistory:
          maxSize: 100
          expireTime: 3600
  defaultJobFunctionsForIndustry: "{\"id\":\"-1\",\"children\":[{\"id\":\"1075\",\"children\":[{\"id\":\"1078\",\"children\":[{\"id\":\"1049\",\"children\":[{\"id\":\"1186\"},{\"id\":\"1185\"},{\"id\":\"1184\"},{\"id\":\"1183\"},{\"id\":\"1182\"},{\"id\":\"1181\"},{\"id\":\"1180\"},{\"id\":\"1179\"}]},{\"id\":\"1006\",\"children\":[{\"id\":\"1178\"},{\"id\":\"1177\"},{\"id\":\"1176\"},{\"id\":\"1175\"},{\"id\":\"1117\"},{\"id\":\"1116\"},{\"id\":\"1115\"},{\"id\":\"1114\"},{\"id\":\"1113\"},{\"id\":\"1112\"},{\"id\":\"1111\"},{\"id\":\"1110\"},{\"id\":\"1007\"},{\"id\":\"1008\"}]},{\"id\":\"1079\",\"children\":[{\"id\":\"1005\"}]},{\"id\":\"1050\"},{\"id\":\"1051\"}]},{\"id\":\"57\"},{\"id\":\"30\"},{\"id\":\"1127\",\"children\":[{\"id\":\"1128\",\"children\":[{\"id\":\"1002\"},{\"id\":\"1003\"},{\"id\":\"1004\"}]}]},{\"id\":\"1053\"},{\"id\":\"1126\"},{\"id\":\"1125\"},{\"id\":\"1124\"},{\"id\":\"381\",\"children\":[{\"id\":\"1123\"}]},{\"id\":\"29\",\"children\":[{\"id\":\"1040\"},{\"id\":\"1041\"},{\"id\":\"1042\"},{\"id\":\"1043\"}]},{\"id\":\"1037\",\"children\":[{\"id\":\"1122\"}]},{\"id\":\"1082\",\"children\":[{\"id\":\"1121\"},{\"id\":\"1120\"},{\"id\":\"1119\"},{\"id\":\"1118\"},{\"id\":\"60\"},{\"id\":\"1033\"},{\"id\":\"1034\"},{\"id\":\"1039\"}]},{\"id\":\"1108\",\"children\":[{\"id\":\"1109\"}]},{\"id\":\"1081\",\"children\":[{\"id\":\"1032\"}]},{\"id\":\"1080\",\"children\":[{\"id\":\"1107\"},{\"id\":\"1106\"},{\"id\":\"1105\"},{\"id\":\"1104\"},{\"id\":\"1103\"},{\"id\":\"1009\"}]},{\"id\":\"55\"},{\"id\":\"1018\"},{\"id\":\"1019\"},{\"id\":\"1035\"},{\"id\":\"1052\"},{\"id\":\"1067\",\"children\":[{\"id\":\"1036\"}]}]},{\"id\":\"34\",\"children\":[{\"id\":\"100\"},{\"id\":\"107\"},{\"id\":\"109\"},{\"id\":\"587\"}]},{\"id\":\"32\",\"children\":[{\"id\":\"511\",\"children\":[{\"id\":\"512\"}]},{\"id\":\"80\"},{\"id\":\"87\"},{\"id\":\"182\"},{\"id\":\"505\"},{\"id\":\"586\"},{\"id\":\"754\"},{\"id\":\"755\"}]},{\"id\":\"35\",\"children\":[{\"id\":\"4\"},{\"id\":\"152\"},{\"id\":\"323\"},{\"id\":\"336\"},{\"id\":\"751\"},{\"id\":\"752\"},{\"id\":\"3\"}]},{\"id\":\"37\",\"children\":[{\"id\":\"136\"},{\"id\":\"137\"},{\"id\":\"144\"},{\"id\":\"146\"},{\"id\":\"147\"},{\"id\":\"728\"},{\"id\":\"744\"},{\"id\":\"745\"},{\"id\":\"746\"},{\"id\":\"747\"},{\"id\":\"748\"},{\"id\":\"749\"},{\"id\":\"750\"},{\"id\":\"833\"},{\"id\":\"834\"},{\"id\":\"835\"},{\"id\":\"836\"},{\"id\":\"837\"},{\"id\":\"881\"},{\"id\":\"882\"}]},{\"id\":\"36\",\"children\":[{\"id\":\"1131\",\"children\":[{\"id\":\"740\"},{\"id\":\"1086\"},{\"id\":\"729\"},{\"id\":\"730\"},{\"id\":\"762\"},{\"id\":\"763\"},{\"id\":\"764\"},{\"id\":\"1048\"},{\"id\":\"832\"},{\"id\":\"831\"},{\"id\":\"830\"},{\"id\":\"829\"},{\"id\":\"828\"},{\"id\":\"1130\"},{\"id\":\"1129\"}]},{\"id\":\"1138\"},{\"id\":\"1137\"},{\"id\":\"1136\"},{\"id\":\"1135\"},{\"id\":\"1134\",\"children\":[{\"id\":\"868\"},{\"id\":\"871\"},{\"id\":\"870\"},{\"id\":\"869\"}]},{\"id\":\"7\",\"children\":[{\"id\":\"771\"},{\"id\":\"1001\"}]},{\"id\":\"8\"},{\"id\":\"123\"},{\"id\":\"128\",\"children\":[{\"id\":\"735\"},{\"id\":\"738\"},{\"id\":\"739\"}]},{\"id\":\"129\"},{\"id\":\"130\"},{\"id\":\"131\"},{\"id\":\"187\"},{\"id\":\"411\"},{\"id\":\"731\"},{\"id\":\"732\"},{\"id\":\"733\"},{\"id\":\"734\"},{\"id\":\"736\"},{\"id\":\"737\"},{\"id\":\"741\"},{\"id\":\"742\"},{\"id\":\"743\"},{\"id\":\"756\"},{\"id\":\"757\"},{\"id\":\"758\"},{\"id\":\"759\"},{\"id\":\"760\"},{\"id\":\"761\"},{\"id\":\"823\"},{\"id\":\"824\"},{\"id\":\"886\"},{\"id\":\"887\"},{\"id\":\"888\"},{\"id\":\"889\"},{\"id\":\"890\"},{\"id\":\"891\"},{\"id\":\"892\"},{\"id\":\"893\"},{\"id\":\"894\"},{\"id\":\"895\"},{\"id\":\"896\"},{\"id\":\"897\"},{\"id\":\"898\"},{\"id\":\"899\"},{\"id\":\"900\"},{\"id\":\"901\"},{\"id\":\"902\"},{\"id\":\"903\"},{\"id\":\"904\"},{\"id\":\"905\"},{\"id\":\"991\"},{\"id\":\"1010\"},{\"id\":\"1046\"},{\"id\":\"1047\"},{\"id\":\"3\"}]},{\"id\":\"388\",\"children\":[{\"id\":\"28\"},{\"id\":\"180\"},{\"id\":\"389\"},{\"id\":\"390\"},{\"id\":\"391\"},{\"id\":\"392\",\"children\":[{\"id\":\"393\"},{\"id\":\"394\"}]},{\"id\":\"395\"},{\"id\":\"396\"},{\"id\":\"397\"},{\"id\":\"398\"},{\"id\":\"399\"},{\"id\":\"400\"},{\"id\":\"781\"},{\"id\":\"794\"},{\"id\":\"795\"},{\"id\":\"796\"},{\"id\":\"798\"},{\"id\":\"799\"},{\"id\":\"992\"},{\"id\":\"993\"},{\"id\":\"994\"},{\"id\":\"995\"},{\"id\":\"996\"},{\"id\":\"997\"},{\"id\":\"998\"},{\"id\":\"999\"},{\"id\":\"1000\"},{\"id\":\"1038\"}]},{\"id\":\"15\",\"children\":[{\"id\":\"877\",\"children\":[{\"id\":\"878\"},{\"id\":\"879\"},{\"id\":\"880\"}]},{\"id\":\"875\"},{\"id\":\"876\"},{\"id\":\"883\"},{\"id\":\"884\"},{\"id\":\"885\"},{\"id\":\"530\",\"children\":[{\"id\":\"528\"},{\"id\":\"873\"},{\"id\":\"874\"},{\"id\":\"872\"}]}]},{\"id\":\"178\",\"children\":[{\"id\":\"172\"},{\"id\":\"173\"},{\"id\":\"174\"},{\"id\":\"175\"},{\"id\":\"176\"},{\"id\":\"177\"},{\"id\":\"851\",\"children\":[{\"id\":\"852\"},{\"id\":\"853\"},{\"id\":\"854\"}]},{\"id\":\"855\",\"children\":[{\"id\":\"858\"},{\"id\":\"857\"},{\"id\":\"856\"}]},{\"id\":\"859\"},{\"id\":\"860\",\"children\":[{\"id\":\"861\"},{\"id\":\"862\"},{\"id\":\"863\"},{\"id\":\"864\"}]},{\"id\":\"865\"},{\"id\":\"866\"},{\"id\":\"1045\"}]},{\"id\":\"498\",\"children\":[{\"id\":\"1090\"},{\"id\":\"1089\"},{\"id\":\"1088\"},{\"id\":\"1087\"},{\"id\":\"1084\"},{\"id\":\"499\"},{\"id\":\"500\"},{\"id\":\"501\"},{\"id\":\"825\"},{\"id\":\"826\"},{\"id\":\"827\"},{\"id\":\"1060\"}]},{\"id\":\"41\"},{\"id\":\"218\",\"children\":[{\"id\":\"197\",\"children\":[{\"id\":\"193\"},{\"id\":\"194\"},{\"id\":\"195\"},{\"id\":\"196\"}]},{\"id\":\"198\"},{\"id\":\"203\",\"children\":[{\"id\":\"200\"},{\"id\":\"201\"},{\"id\":\"202\"}]},{\"id\":\"204\"},{\"id\":\"209\",\"children\":[{\"id\":\"206\"},{\"id\":\"207\"},{\"id\":\"208\"}]},{\"id\":\"215\",\"children\":[{\"id\":\"211\"},{\"id\":\"212\"},{\"id\":\"213\"},{\"id\":\"214\"}]},{\"id\":\"216\"},{\"id\":\"217\"},{\"id\":\"753\"}]},{\"id\":\"560\",\"children\":[{\"id\":\"564\"},{\"id\":\"1031\"}]},{\"id\":\"576\"}]}"
  allJobFunctions: "{\"id\":\"-1\",\"children\":[{\"id\":\"1075\",\"children\":[{\"id\":\"1078\",\"children\":[{\"id\":\"1049\",\"children\":[{\"id\":\"1186\"},{\"id\":\"1185\"},{\"id\":\"1184\"},{\"id\":\"1183\"},{\"id\":\"1182\"},{\"id\":\"1181\"},{\"id\":\"1180\"},{\"id\":\"1179\"}]},{\"id\":\"1006\",\"children\":[{\"id\":\"1178\"},{\"id\":\"1177\"},{\"id\":\"1176\"},{\"id\":\"1175\"},{\"id\":\"1117\"},{\"id\":\"1116\"},{\"id\":\"1115\"},{\"id\":\"1114\"},{\"id\":\"1113\"},{\"id\":\"1112\"},{\"id\":\"1111\"},{\"id\":\"1110\"},{\"id\":\"1007\"},{\"id\":\"1008\"}]},{\"id\":\"1079\",\"children\":[{\"id\":\"1005\"}]},{\"id\":\"1050\"},{\"id\":\"1051\"}]},{\"id\":\"57\"},{\"id\":\"30\"},{\"id\":\"1127\",\"children\":[{\"id\":\"1128\",\"children\":[{\"id\":\"1002\"},{\"id\":\"1003\"},{\"id\":\"1004\"}]}]},{\"id\":\"1053\"},{\"id\":\"1126\"},{\"id\":\"1125\"},{\"id\":\"1124\"},{\"id\":\"381\",\"children\":[{\"id\":\"1123\"}]},{\"id\":\"29\",\"children\":[{\"id\":\"1040\"},{\"id\":\"1041\"},{\"id\":\"1042\"},{\"id\":\"1043\"}]},{\"id\":\"1037\",\"children\":[{\"id\":\"1122\"}]},{\"id\":\"1082\",\"children\":[{\"id\":\"1121\"},{\"id\":\"1120\"},{\"id\":\"1119\"},{\"id\":\"1118\"},{\"id\":\"60\"},{\"id\":\"1033\"},{\"id\":\"1034\"},{\"id\":\"1039\"}]},{\"id\":\"1108\",\"children\":[{\"id\":\"1109\"}]},{\"id\":\"1081\",\"children\":[{\"id\":\"1032\"}]},{\"id\":\"1080\",\"children\":[{\"id\":\"1107\"},{\"id\":\"1106\"},{\"id\":\"1105\"},{\"id\":\"1104\"},{\"id\":\"1103\"},{\"id\":\"1009\"}]},{\"id\":\"55\"},{\"id\":\"1018\"},{\"id\":\"1019\"},{\"id\":\"1035\"},{\"id\":\"1052\"},{\"id\":\"1067\",\"children\":[{\"id\":\"1036\"}]}]},{\"id\":\"34\",\"children\":[{\"id\":\"100\"},{\"id\":\"107\"},{\"id\":\"109\"},{\"id\":\"587\"}]},{\"id\":\"32\",\"children\":[{\"id\":\"511\",\"children\":[{\"id\":\"512\"}]},{\"id\":\"80\"},{\"id\":\"87\"},{\"id\":\"182\"},{\"id\":\"505\"},{\"id\":\"586\"},{\"id\":\"754\"},{\"id\":\"755\"}]},{\"id\":\"35\",\"children\":[{\"id\":\"4\"},{\"id\":\"152\"},{\"id\":\"323\"},{\"id\":\"336\"},{\"id\":\"751\"},{\"id\":\"752\"},{\"id\":\"3\"}]},{\"id\":\"37\",\"children\":[{\"id\":\"136\"},{\"id\":\"137\"},{\"id\":\"144\"},{\"id\":\"146\"},{\"id\":\"147\"},{\"id\":\"728\"},{\"id\":\"744\"},{\"id\":\"745\"},{\"id\":\"746\"},{\"id\":\"747\"},{\"id\":\"748\"},{\"id\":\"749\"},{\"id\":\"750\"},{\"id\":\"833\"},{\"id\":\"834\"},{\"id\":\"835\"},{\"id\":\"836\"},{\"id\":\"837\"},{\"id\":\"881\"},{\"id\":\"882\"}]},{\"id\":\"36\",\"children\":[{\"id\":\"1131\",\"children\":[{\"id\":\"740\"},{\"id\":\"1086\"},{\"id\":\"729\"},{\"id\":\"730\"},{\"id\":\"762\"},{\"id\":\"763\"},{\"id\":\"764\"},{\"id\":\"1048\"},{\"id\":\"832\"},{\"id\":\"831\"},{\"id\":\"830\"},{\"id\":\"829\"},{\"id\":\"828\"},{\"id\":\"1130\"},{\"id\":\"1129\"}]},{\"id\":\"1138\"},{\"id\":\"1137\"},{\"id\":\"1136\"},{\"id\":\"1135\"},{\"id\":\"1134\",\"children\":[{\"id\":\"868\"},{\"id\":\"871\"},{\"id\":\"870\"},{\"id\":\"869\"}]},{\"id\":\"7\",\"children\":[{\"id\":\"771\"},{\"id\":\"1001\"}]},{\"id\":\"8\"},{\"id\":\"123\"},{\"id\":\"128\",\"children\":[{\"id\":\"735\"},{\"id\":\"738\"},{\"id\":\"739\"}]},{\"id\":\"129\"},{\"id\":\"130\"},{\"id\":\"131\"},{\"id\":\"187\"},{\"id\":\"411\"},{\"id\":\"731\"},{\"id\":\"732\"},{\"id\":\"733\"},{\"id\":\"734\"},{\"id\":\"736\"},{\"id\":\"737\"},{\"id\":\"741\"},{\"id\":\"742\"},{\"id\":\"743\"},{\"id\":\"756\"},{\"id\":\"757\"},{\"id\":\"758\"},{\"id\":\"759\"},{\"id\":\"760\"},{\"id\":\"761\"},{\"id\":\"823\"},{\"id\":\"824\"},{\"id\":\"886\"},{\"id\":\"887\"},{\"id\":\"888\"},{\"id\":\"889\"},{\"id\":\"890\"},{\"id\":\"891\"},{\"id\":\"892\"},{\"id\":\"893\"},{\"id\":\"894\"},{\"id\":\"895\"},{\"id\":\"896\"},{\"id\":\"897\"},{\"id\":\"898\"},{\"id\":\"899\"},{\"id\":\"900\"},{\"id\":\"901\"},{\"id\":\"902\"},{\"id\":\"903\"},{\"id\":\"904\"},{\"id\":\"905\"},{\"id\":\"991\"},{\"id\":\"1010\"},{\"id\":\"1046\"},{\"id\":\"1047\"},{\"id\":\"3\"}]},{\"id\":\"388\",\"children\":[{\"id\":\"28\"},{\"id\":\"180\"},{\"id\":\"389\"},{\"id\":\"390\"},{\"id\":\"391\"},{\"id\":\"392\",\"children\":[{\"id\":\"393\"},{\"id\":\"394\"}]},{\"id\":\"395\"},{\"id\":\"396\"},{\"id\":\"397\"},{\"id\":\"398\"},{\"id\":\"399\"},{\"id\":\"400\"},{\"id\":\"781\"},{\"id\":\"794\"},{\"id\":\"795\"},{\"id\":\"796\"},{\"id\":\"798\"},{\"id\":\"799\"},{\"id\":\"992\"},{\"id\":\"993\"},{\"id\":\"994\"},{\"id\":\"995\"},{\"id\":\"996\"},{\"id\":\"997\"},{\"id\":\"998\"},{\"id\":\"999\"},{\"id\":\"1000\"},{\"id\":\"1038\"}]},{\"id\":\"15\",\"children\":[{\"id\":\"877\",\"children\":[{\"id\":\"878\"},{\"id\":\"879\"},{\"id\":\"880\"}]},{\"id\":\"875\"},{\"id\":\"876\"},{\"id\":\"883\"},{\"id\":\"884\"},{\"id\":\"885\"},{\"id\":\"530\",\"children\":[{\"id\":\"528\"},{\"id\":\"873\"},{\"id\":\"874\"},{\"id\":\"872\"}]}]},{\"id\":\"178\",\"children\":[{\"id\":\"172\"},{\"id\":\"173\"},{\"id\":\"174\"},{\"id\":\"175\"},{\"id\":\"176\"},{\"id\":\"177\"},{\"id\":\"851\",\"children\":[{\"id\":\"852\"},{\"id\":\"853\"},{\"id\":\"854\"}]},{\"id\":\"855\",\"children\":[{\"id\":\"858\"},{\"id\":\"857\"},{\"id\":\"856\"}]},{\"id\":\"859\"},{\"id\":\"860\",\"children\":[{\"id\":\"861\"},{\"id\":\"862\"},{\"id\":\"863\"},{\"id\":\"864\"}]},{\"id\":\"865\"},{\"id\":\"866\"},{\"id\":\"1045\"}]},{\"id\":\"498\",\"children\":[{\"id\":\"1090\"},{\"id\":\"1089\"},{\"id\":\"1088\"},{\"id\":\"1087\"},{\"id\":\"1084\"},{\"id\":\"499\"},{\"id\":\"500\"},{\"id\":\"501\"},{\"id\":\"825\"},{\"id\":\"826\"},{\"id\":\"827\"},{\"id\":\"1060\"}]},{\"id\":\"41\"},{\"id\":\"218\",\"children\":[{\"id\":\"197\",\"children\":[{\"id\":\"193\"},{\"id\":\"194\"},{\"id\":\"195\"},{\"id\":\"196\"}]},{\"id\":\"198\"},{\"id\":\"203\",\"children\":[{\"id\":\"200\"},{\"id\":\"201\"},{\"id\":\"202\"}]},{\"id\":\"204\"},{\"id\":\"209\",\"children\":[{\"id\":\"206\"},{\"id\":\"207\"},{\"id\":\"208\"}]},{\"id\":\"215\",\"children\":[{\"id\":\"211\"},{\"id\":\"212\"},{\"id\":\"213\"},{\"id\":\"214\"}]},{\"id\":\"216\"},{\"id\":\"217\"},{\"id\":\"753\"}]},{\"id\":\"560\",\"children\":[{\"id\":\"564\"},{\"id\":\"1031\"}]},{\"id\":\"576\"}]}"
  # aiSourcing: 
  #   pagePermission: PERMISSION_AI_SOURCING
  #   host: http://*********:5001
job_detail_url: https://apn-v3-staging-singapore.hitalentech.com/jobs/detail/
job_search_condition_generator_url: http://*************:5250
job_search_condition_generator_host_type: QDRANT_HOST_STAGING


grpc:
  client:
    gpu-grpc-server:  
      address: 'static://office-tunnel.default:8088'  
      negotiationType: PLAINTEXT
      
api-job-properties:
  jobService:
    elastic_getJobCategoryCount_responseNull:
      titleChina: "ES 类别计数响应或响应正文为空。"
      titleEng: "ES category count Response or response body is null."  
    elastic_getJobCategoryCount_responseStatusNotOk:
      titleChina: "ES 类别计数 意外响应状态 #1"
      titleEng: "ES category count Unexpected response status #1"    
    job_syncFromThirdParty_codeNull:
      titleChina: "职位代码不能为空或为空"
      titleEng: "Job Code cannot be empty or null"
    job_getIpgToken_responseStatusNotOk:
      titleChina: "登录IpgFiller错误，参数错误。"
      titleEng: "login to IpgFiller error, parameter error."
    job_validateClientContact_clientContactNull:
      titleChina: "客户联系方式无效！"
      titleEng: "Client Contact is not valid!"
    job_validateClientContact_companyNull:
      titleChina: "客户联系人的公司信息无效！"
      titleEng: "Company info for Client Contact is not valid!"
    job_common_noPermission:
      titleChina: "目前，我们在检索您的信息时遇到了一些困难。您能再试一次吗？"
      titleEng: "We're having some difficulty retrieving your information at the moment. Could you please try again?"
    job_common_invalidUser:
      titleChina: "无效用户"
      titleEng: "Invalid User"
    job_getFolder_failedFetchFolder:
      titleChina: "无法获取我的文件夹列表"
      titleEng: "Failed to fetch my folder List"
    job_getFolder_failedShareFetchFolder:
      titleChina: "无法获取共享文件夹列表"
      titleEng: "Failed to fetch shared folder List"  
    job_createJobFolder_commonFolderDtoNull:
      titleChina: "我们无法处理您的输入。您能否验证您的信息并重试？"
      titleEng: "We're having trouble processing your input. Could you please verify your information and try again?" 
    job_createJobFolder_commonFolderNull:
      titleChina: "文件夹名称已存在。"
      titleEng: "Folder name already exists." 
    job_deleteJobFolder_folderNull:
      titleChina: "未找到文件夹。"
      titleEng: "No folder found." 
    job_deleteJobFolder_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."     
    job_addJobsToFolders_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions." 
    job_addJobFolderSharingForTeams_noPermission:
      titleChina: "使用不同的权限时，您无法与同一团队共享文件夹."
      titleEng: "You are unable to share folder with the same team while using different permissions."
    job_addJobFolderSharingForUsers_noPermission:
      titleChina: "该用户已经拥有查看该文件夹的权限。"
      titleEng: "The user already has permissions to the folder."
    job_removeSharingForSharedFolder_noShare:
      titleChina: "暂无权限查看该职位。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    job_fetchAllFoldersIfExist_invalidFolder:
      titleChina: "该文件夹无效。"
      titleEng: "The folder is invalid."
    job_validateJobs_invalidJob:
      titleChina: "无效作业，作业不在当前租户下！"
      titleEng: "Invalid Jobs, Jobs are not under current Tenant!" 
    job_checkAllJobsUnderCurrentFolder_invalidJob:
      titleChina: "此文件夹中不包含所有作业"
      titleEng: "Jobs are not all in this folder"    
    job_setSearchCustomFolderSql_noShareFolder:
      titleChina: "无法使用当前搜索类型处理您的请求"
      titleEng: "Can't process your request with the current search type"
    jobNote_create_idNull:
      titleChina: "要创建的对象不能已经有一个 ID。"
      titleEng: "The object to create cannot already have an id."  
    jobNote_create_jobIdNull:
      titleChina: "请给岗位添加一条备注。"
      titleEng: "Please add a note to the job." 
    jobNote_create_noteNull:
      titleChina: "请提供有效说明"
      titleEng: "Please provide valid note" 
    jobNote_create_alertTimeNull:
      titleChina: "缺少同步提醒时间"
      titleEng: "Missing alert time to sync"
    jobNote_update_notExist:
      titleChina: "找不到要更新的说明"
      titleEng: "The note to update can not be found"
    jobNote_update_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."  
    jobNote_searchJobNoteByKeyword_dtoNull:
      titleChina: "输入信息无效。"
      titleEng: "The input is invalid."
    jobNote_searchJobNoteByKeyword_userError:
      titleChina: "无法获取用户信息"
      titleEng: "Cannot get user info"
    jobQuestion_create_idNotNull:
      titleChina: "要创建的对象不能已经有一个 ID。"
      titleEng: "The object to create cannot already have an id."
    jobQuestion_update_notFind:
      titleChina: "找不到要更新的问题"
      titleEng: "The question to update can not be found"  
    jobRelation_saveJobAssignedUsers_userNull:
      titleChina: "分配的用户信息无效"
      titleEng: "Assigned user information is not valid"
    jobRelation_saveJobAssignedUsers_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."
    JobSearchFolder_search_paramNull:
      titleChina: "输入信息无效。"
      titleEng: "The input is invalid."    
    JobSearchFolder_createJobSearchFolder_exist:
      titleChina: "文件夹名称已存在。"
      titleEng: "Folder name already exists." 
    JobSearchFolder_createJobSearchFolder_noPermission:
      titleChina: "文件夹名称已存在。"
      titleEng: "Folder name already exists."
    JobSearchFolder_updateJobSearchFolder_exist:
      titleChina: "文件夹名称已存在。"
      titleEng: "Folder name already exists."
    JobSearchFolder_updateJobSearchFolder_notFind:
      titleChina: "未找到文件夹。"
      titleEng: "No folder found."
    JobSearchFolder_deleteJobSearchFolder_idNull:
      titleChina: "需要查询的对象必须有id。"
      titleEng: "The object to query must have id."  
    job_validateInputValue_companyParamNull:
      titleChina: "公司信息无效！"
      titleEng: "Company info is not valid!" 
    job_validateInputValue_companyNull:
      titleChina: "与当前租户相关的公司信息无效！"
      titleEng: "Company info that associated with current tenant is not valid!"   
    job_validateInputValue_jobTitleNull:
      titleChina: "职务名称无效！"
      titleEng: "Job Title is not valid!" 
    job_validateInputValue_recruitmentProcessNull:
      titleChina: "招聘流程无效！"
      titleEng: "Recruitment Process is not valid!"
    job_validateInputValue_contactBriefNull:
      titleChina: "客户联系方式无效！"
      titleEng: "Client Contact is not valid!"
    job_checkEnumValid_minimumDegreeIdNull:
      titleChina: "最小度值无效。"
      titleEng: "Minimum Degree value is not valid."
    job_saveJobAssignedUsers_userNull:
      titleChina: "分配的用户信息无效"
      titleEng: "Assigned user information is not valid"
    job_saveJobAssignedUsers_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."
    job_validateSearchJob_noPermission:
      titleChina: "暂无权限查看该职位。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    job_translate_recruitmentProcessNull:
      titleChina: "缺少招聘流程信息。"
      titleEng: "Missing Recruitment Process Info."  
    job_translate_noPermission:
      titleChina: "暂无权限查看该职位。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission." 
    job_translate_jobTypeNull:
      titleChina: "工作类型为空。"
      titleEng: "Job Type is null."
    job_translate_billRateExist:
      titleChina: "请选择一个币种和发薪类型。"
      titleEng: "Please select a currency and pay type. "
    job_translate_jobOpenLimit:
      titleChina: "职位空缺超出了长度限制。"
      titleEng: "Job Openings exceeds the length limit ."
    job_translate_maxSubmissionsLimit:
      titleChina: "允许提交的内容超出长度限制"
      titleEng: "Allowed Submittals exceeds the length limit ."
    job_update_noPermission:
      titleChina: "没有编辑此在招岗位的权限。请请求所有者授予您权限。"
      titleEng: "No permission to edit this job profile. Please ask the owner to grant you the permission."
    job_removeEnumsNull_jobFunctionValueValid:
      titleChina: "职务职能值无效"
      titleEng: "Job Function value is not valid."
    job_removeEnumsNull_languageValueValid:
      titleChina: "所需语言值无效。"
      titleEng: "Required Language value is not valid."
    job_removeEnumsNull_preferredlanguageValueValid:
      titleChina: "首选语言值无效。"
      titleEng: "Preferred Language value is not valid."
    job_removeEnumsNull_preferredDegreeValueValid:
      titleChina: "首选学位值无效。"
      titleEng: "Preferred Degree value is not valid."
    job_updateStatus_invalidJob:
      titleChina: "该岗位无效。"
      titleEng: "The job is invalid." 
    job_updateStatus_noPermission:
      titleChina: "您无权访问此招聘流​​程！"
      titleEng: "You are not authorized to access this recruitment process!"
    job_updateStatus_payrollingJob:
      titleChina: "工资发放工作 #1 无法更新工作状态。"
      titleEng: "Payrolling job #1 can not update job status ."
    job_updateJobsStatus_paramNull:
      titleChina: "输入信息无效。"
      titleEng: "The input is invalid."
    job_updateJobsStatus_idError:
      titleChina: "请使用新创建的作业"
      titleEng: "Please use new created the Job"
    job_updateJobsStatus_recruitmentProcessNull:
      titleChina: "该岗位无效。"
      titleEng: "The job is invalid."
    job_updateJobsStatus_payrollJob:
      titleChina: "工资发放工作 #1 无法更新工作状态。"
      titleEng: "Payrolling job #1 can not update job status ."
    job_findOneWithEntity_noPermission:
      titleChina: "暂无权限查看该职位。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    job_numberOfOfferAcceptedEqualsOpenings_notExist:
      titleChina: "未找到岗位。"
      titleEng: "No job found."
    job_getJobWithoutEntity_noPermission:
      titleChina: "暂无权限查看该职位。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    job_syncJobToIpg_noPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions."
    job_syncJobToIpg_ipgFillerError:
      titleChina: "创建同步到Ipg Filler的作业错误，参数错误。该作业不存在或已被同步。"
      titleEng: "create a job synchronized to Ipg Filler error, parameter error. This job does not exist or has been synchronized."
    job_sendMailToAmOfJob_syncJobToIpgRelationNull:
      titleChina: "发送邮件至作业错误的 AM，参数错误。此作业不存在或不属于 APN。"
      titleEng: "send mail to AM of job error, parameter error. This job does not exist or does not belong to APN."
    job_sendEmailFromIpgUser_sendMailError:
      titleChina: "ipg 发送电子邮件错误"
      titleEng: "ipg send email error"
    job_getCompanyTeamUserByJobId_jobInvalid:
      titleChina: "Job #1 无效！"
      titleEng: "Job #1 is invalid！"
    job_myJobs_error:
      titleChina: "发生错误。请稍后再试"
      titleEng: "Something went wrong. Please try again later."
    job_searchPer_deleteById_noPermission:
      titleChina: "无需为用户设置权限值"
      titleEng: "No permission value need to set for user ."
    job_searchPer_configNull:
      titleChina: "SearchPreference 配置为空"
      titleEng: "SearchPreference config is Empty ."  
    job_uploadHTMLToS3_error:
      titleChina: "处理分享内容时无法处理分享功能，请重试"
      titleEng: "Cannot process sharing function during process the sharing content, please try again" 
    job_getUUIDFromS3Links_error:
      titleChina: "无法获取 s3 链接，请重试"
      titleEng: "Cannot get s3 links, please try again" 
    job_validateInput_jobIdNull:
      titleChina: "请使用共享有效工作，请重试"
      titleEng: "Ops, Please use share valid job, please try again"
    job_validateInput_languageNull:
      titleChina: "请选择语言并重试"
      titleEng: "Ops, Please select the language and try again"
    job_validateInput_userIdNull:
      titleChina: "发生错误。请稍后再试。"
      titleEng: "Something went wrong. Please try again later."
    job_validateInput_platformTypeNull:
      titleChina: "缺少某些内容，请重试"
      titleEng: "Ops, there are something missing, please try again"
    job_createPostJob_idNotNull:
      titleChina: "要创建的对象不能已经有一个 ID。"
      titleEng: "The object to create cannot already have an id."  
    job_createPostJob_noPrivilege:
      titleChina: "此项工作无特权。"
      titleEng: "No privilege for this job."
    job_createPostJob_noJazzHR:
      titleChina: "该用户没有 JazzHR 帐户。"
      titleEng: "This user does not have a JazzHR account."
    job_createJob_idNotNull:
      titleChina: "要创建的对象不能已经有一个 ID。"
      titleEng: "The object to create cannot already have an id."
    job_syncFailedTalentsToEs_wrong:
      titleChina: "APN xxl-job token 错误！"
      titleEng: "APN xxl-job token is wrong!"
    job_post_image_duplicate:
      titleChina: "存在相同的图片!"
      titleEng: "Exist duplicate image!"
    job_info_incomplete_for_ai_sourcing:
      titleChina: "职位信息不完整，请先更新职位信息!"
      titleEng: "Job information is incomplete. Please update the job information first!"
          