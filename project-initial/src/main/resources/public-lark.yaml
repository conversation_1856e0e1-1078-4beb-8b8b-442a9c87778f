application:
  notification:
    lark:
      mq:
        enabled: true
        webhookKey:
        webhookUrl: https://open.larksuite.com/open-apis/bot/v2/hook/c34253b0-0b1b-43ba-b49f-f7c3097f94cd
        threshold: 3
      xxl-job:
        webhookKey:
        webhookUrl: https://open.larksuite.com/open-apis/bot/v2/hook/8b0f9662-59d4-48d3-97ce-72b06b11893c
      apn-create-user:
        webhookKey:
        webhookUrl: https://open.larksuite.com/open-apis/bot/v2/hook/5ae0be7f-6d9e-42e0-b2d3-f15a8bfffeb4
      no-poaching-remind:
        webhookUrl: https://open-sg.larksuite.com/anycross/trigger/callback/MDRkNWY3ZGQxNTA0MTY5MzFmN2I0YmVmZDU0NmMzNGIw
      reportSubscription:
        e5: https://open-sg.larksuite.com/anycross/trigger/callback/NTViMzlhM2I5YmI0ZDhmM2U0YzcwZmE2MTY0NTRlZjQw
  translation:
    lark:
      url: https://open.larksuite.com/open-apis
      appId:
      appSecret:
  
  messageNotification:
    lark:
      authenticationEndPoint: https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal
      obtainUserIdsEndPoint: https://open.larksuite.com/open-apis/contact/v3/users/batch_get_id
      sendMessageEndpoint: https://open.larksuite.com/open-apis/message/v4/send/
      batchSendMessageEndpoint: https://open.larksuite.com/open-apis/message/v4/batch_send/
      appId:
      appSecret: