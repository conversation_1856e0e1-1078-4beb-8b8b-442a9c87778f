server:
  port: 9015
  servlet:
    context-path: /canal

canal:
  talentTable: talent
  talentRelatedTables: start,talent_ownership,talent_contact,talent_industry_relation,talent_job_function_relation,talent_language_relation,talent_work_authorization_relation,talent_resume_relation,talent_additional_info,talent_current_location,talent_note,talent_recruitment_process,talent_recruitment_process_kpi_user,talent_review_note,confidential_talent,agency_submit_application
  talentRecordTables: talent_additional_info,talent_contact,talent_current_location,talent_industry_relation,talent_resume_relation,talent_job_function_relation,talent_language_relation,talent_work_authorization_relation,talent_recruitment_process_node,talent_note,talent_ownership,talent_recruitment_process_interview,talent_recruitment_process_submit_to_job,confidential_talent,voip_contact
  jobTable: job
  jobRelatedTables: job_ipg_relation,user_favorite_job,user_job_relation,talent_recruitment_process,job_preferred_degree_relation,job_required_languages_relation,job_preferred_languages_relation,job_job_function_relation,job_company_contact_relation,job_note,agency_shared_job
  companyTable: company
  companyRelatedTables: account_business,company_industry_relation,business_flow_administrator,company_contract
  companyClientNoteTable: company_client_note
  companyProgressNoteTable: company_progress_note
  companyContactTable: company_sales_lead_client_contact
  jobRecordTables: job,job_ipg_relation,user_favorite_job,talent_recruitment_process,job_job_function_relation,job_preferred_degree_relation,job_required_languages_relation,job_preferred_languages_relation,job_location,job_note,job_additional_info,job_company_contact_relation,user_job_relation,agency_activity
  assigmentRecordTables: timesheet_talent_assignment,assignment_bill_info,assignment_contribution,assignment_location,assignment_pay_info,assignment_pay_rate,assignment_timesheet,timesheet_manager
  agencyJobRelatedTables: agency_shared_job,job_sharing_to_agency_info
  agencyTable: agency
  agencyRelatedTables: agency_sharing_user
  talentRecruitmentProcessRelatedTables: 
  apnIp: localhost
  apnPort: 11111
  instance: apn-dev
  subscribe: apn_dev.*
  skipBinlogChange: true
  frequency: 100 # milliseconds
  retryFailedBatchSize: 100
  error:
    sleepMillis: 60000 # milliseconds
  apn:
    loginUser: Synchronize_Talent
    loginPassword:
    apiBase: https://apiv3-dev.hitalentech.com
  notification:
    lark:
      webhookKey: dlkbLPvsQXQ6OyGnd4Aycf
      webhookUrl: https://open.larksuite.com/open-apis/bot/v2/hook/46399fec-0de9-4165-95f1-e8c76d9373e1
  sso:
    url_login: https://sso-singapore.hitalentech.com/oauth2/token
    client_id: canal_service
    client_secret: bC0q52jEiYJqq2Iy