server:
  port: 9006
  servlet:
    context-path: /finance

application:
  useCurrencyRate: false
  currencyRateKey: HuzQtse_f-OsL2MbrwBq
  larkService:
    callbackUrl: https://open-sg.larksuite.com/anycross/trigger/callback/NzExNjA1Mzc4NDk3YzlkNGI5YWIxYzc4OGM2ZDY4ZjMw
    uninvoiceUrl: https://open-sg.larksuite.com/anycross/trigger/callback/Y2JkNTNmOWRkNzM4OTRmZjc5Mzk1Njc1NGVmMDViZmMw
    overdueInvoiceUrl: https://open-sg.larksuite.com/anycross/trigger/callback/Y2Y1NmQwYjcwNWRjZGE3NWNiMWQzNjE5OTViMWM1OWYw
  invoice:
    viewInvoiceUrl: https://apn-v3-dev.hitalentech.com/InvoiceAttachment
    defaultInvoiceUrl: https://apn-v3-dev.hitalentech.com/finance/FTEchinaInvoice/detail
    createFteChinaInvoice: https://apn-v3-dev.hitalentech.com/finance/FTEchinaInvoice/createInvoice
    fteChinaInvoiceList: https://apn-v3-dev.hitalentech.com/finance/FTEchinaInvoice?invoicingStatusList=OVERDUE

api-prompt-properties:
  financeService:
    commission_save_startNull:
      titleChina: "未找到入职流程。"
      titleEng: "No onboarding start found."  
    commission_save_validateStartNull:
      titleChina: "需要Start ID。"
      titleEng: "Start id is required."
    commission_save_validateAmountNull:
      titleChina: "请填写收到的金额数。"
      titleEng: "Please enter the received amount."
    commission_saveFte_startIdAlready:
      titleChina: "此start id已创建佣金。如需更改接收金额，请调用更新API。"
      titleEng: "This start id already created commission. If you want to change the receive amount, please call update API."
    commission_saveFte_invoiceNull:
      titleChina: "未找到发票。"
      titleEng: "No invoice found."
    commission_saveFte_invoiceIdError:
      titleChina: "发票属于开始 #1，而不是当前开始 #2"
      titleEng: "The invoice belongs to start  #1  , not current start #2"
    commission_saveContract_grossMarginNull:
      titleChina: "需要毛利率。"
      titleEng: "Gross margin is required."  
    commission_saveContract_grossMarginError:
      titleChina: "收到的金额不能超过毛利的金额。"
      titleEng: "The amount received cannot exceed the amount of GM."
    commission_update_commissionNotExist:
      titleChina: "更新委托不存在。"
      titleEng: "Commission to update doesn't exists."
    commission_pay_commissionNotExist:
      titleChina: "不存在支付佣金"
      titleEng: "The Commission to pay doesn't exists."
    commission_pay_commissionAlreadyPaid:
      titleChina: "已支付佣金。"
      titleEng: "The commission is paid."  
    commission_findOne_commissionNotFind:
      titleChina: "未找到佣金记录。"
      titleEng: "No commission found."
    invoice_apply_InvoiceActivityExist:
      titleChina: "信用额度已经被使用。"
      titleEng: "Credits is already used." 
    invoice_checkInvoice_notFind:
      titleChina: "未找到发票。"
      titleEng: "No invoice found." 
    invoice_checkInvoice_noPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions."
    invoice_deduct_InvoiceClientCreditNotFind:
      titleChina: "该客户没有信用额度。"
      titleEng: "The client does not have any credits."  
    invoice_deduct_noEnoughCredit:
      titleChina: "该客户的信用额度不足。"
      titleEng: "The client does not have enough credits."
    invoice_checkCompany_notFind:
      titleChina: "未找到公司。"
      titleEng: "No company found."  
    invoice_checkCompany_noPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions."  
    invoice_payment_notFind:
      titleChina: "付款发票不存在"
      titleEng: "Invoice to paid dose not exist"
    invoice_payment_negativeValue:
      titleChina: "请确保付款金额大于或等于0。"
      titleEng: "Please make sure the paid amount is greater or equal to 0."
    invoice_createFteInvoice_startNotExist:
      titleChina: "未找到入职流程。"
      titleEng: "No onboarding start found."
    invoice_createFteInvoice_startupInvoiceNull:
      titleChina: "未找到起步费用发票。"
      titleEng: "No startup fee invoice found."
    invoice_createFteInvoice_startupFeeAlready:
      titleChina: "该起步费用发票已经被使用。"
      titleEng: "The startup fee invoice has already been applied."
    invoice_createFteInvoice_startupFeeNoPaid:
      titleChina: "该起步费用发票未回款。"
      titleEng: "The startup fee invoice is unpaid."
    invoice_createFteInvoice_noPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions."
    invoice_createFteInvoice_wrongTotal:
      titleChina: "发票总金额错误。(calculateTotalInvoiceAmount= #1，TotalInvoiceAmount= #2"
      titleEng: "Wrong total invoice amount. (calculateTotalInvoiceAmount= #1 , TotalInvoiceAmount= #2"
    invoice_createFteInvoice_totalAmountIncorrect:
      titleChina: "总金额不正确。请检查子发票应付金额。"
      titleEng: "Total amount incorrect. Please check sub invoice due amount." 
    invoice_createFteInvoice_dueDateNull:
      titleChina: "需要填写到期金额或发票日期或子发票的到期日。"
      titleEng: "The due amount or invoice date or due date of sub invoice is required."    
    invoice_getTalent_isNUll:
      titleChina: "未找到候选人。"
      titleEng: "No candidate found."
    invoice_getJob_isNUll:
      titleChina: "未找到岗位。"
      titleEng: "No job found."
    invoice_getCompany_isNUll:
      titleChina: "未查询到此公司。"
      titleEng: "No company found."
    invoice_voidInvoice_idNull:
      titleChina: "发票 ID 不能为空"
      titleEng: " Invoice id can not be null"
    invoice_voidInvoice_notFind:
      titleChina: "未找到发票。"
      titleEng: "No invoice found." 
    invoice_voidInvoice_isVoidStatus:
      titleChina: "此发票已作废，无法进行编辑。"
      titleEng: "This invoice is void and cannot be edited."
    invoice_voidInvoice_alreadyPaid:
      titleChina: "此发票有回款记录，无法作废。请先取消回款记录，再作废发票。"
      titleEng: "This invoice has payment records and cannot be void. Please unrecord the payment first, and then void the invoice."
    invoice_refundCredit_invoiceClientNoCredit:
      titleChina: "该客户没有信用额度。"
      titleEng: "The client does not have any credits."
    invoice_voidInvoiceByNo_numberNull:
      titleChina: "发票号码不能为空"
      titleEng: "Invoice number can not be null"
    invoice_voidInvoiceByNo_isVoidStatus:
      titleChina: "此发票已作废，无法进行编辑。"
      titleEng: "This invoice is void and cannot be edited."
    invoice_voidInvoiceByNo_isPaidStatus:
      titleChina: "此发票有回款记录，无法作废。请先取消回款记录，再作废发票。"
      titleEng: "This invoice has payment records and cannot be void. Please unrecord the payment first, and then void the invoice."
    invoice_downloadInvoice_invoiceNotExist:
      titleChina: "未找到发票。"
      titleEng: "No invoice found."
    start_findByStartId_startAddressIsnull:
      titleChina: "请填写工作地址。"
      titleEng: "Please enter the working address."
    start_findByStartId_startClientIsnull:
      titleChina: "请填写客户信息。"
      titleEng: "Please enter the client info."
    start_create_accruedPercentageNinety:
      titleChina: "佣金百分比不正确。此申请拥有 10% 的所有权。其余总百分比应等于 90%"
      titleEng: "Commission percentage incorrect. This application has 10% ownership. The rest total percentage should equal 90%"
    start_create_accruedPercentageHundred:
      titleChina: "佣金百分比不正确。总百分比应等于 100%"
      titleEng: "Commission percentage incorrect. The total percentage should equal 100%"
    start_replace_accruedPercentageNinety:
      titleChina: "佣金百分比不正确。此申请的 10% 所有权归 #1 所有。其余总百分比应等于 90%"
      titleEng: "Commission percentage incorrect. This application has 10% ownership by #1 . The rest total percentage should equal 90%"
    start_validateStart_startNoExist:
      titleChina: "Start ID 不存在"
      titleEng: "Start does not exists by id"
    start_validateStart_noPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions."
    start_rateChange_lastRateisNull:
      titleChina: "本次启动没有费率记录，请先创建。"
      titleEng: "This start don't have rate record, please create it first."
    start_validation_startDateIsNull:
      titleChina: "请填写开始日期。"
      titleEng: "Please enter a start date."
    start_validation_finalBillRateIsNull:
      titleChina: "请填写最终账单费率。"
      titleEng: "Please enter the final bill rate."  
    start_validation_finalPayRateIsNull:
      titleChina: "请填写最终工资费率。"
      titleEng: "Please enter the final pay rate."
    start_validation_taxBurdenRateCodeIsNull:
      titleChina: "税负率代码必填"
      titleEng: "Tax burden rate code is required" 
    start_validation_mspRateCodeIsNull:
      titleChina: "Msp 率是必需的代码"
      titleEng: "Msp rate is code required"
    start_validation_immigrationCostCode:
      titleChina: "需要移民费用代码"
      titleEng: "Immigration cost code is required"
    start_validation_estimatedWorkingHourPerWeek:
      titleChina: "请填写每周的预计工作小时数。"
      titleEng: "Please enter the estimated working hour per week."     
    start_checkTotalBillAmount_amountIncorrect:
      titleChina: "账单总金额不正确。应该相等 #1"
      titleEng: "Total bill amount incorrect. Should be equal #1"
    start_getOfferLetterCostRateValue_notExist:
      titleChina: "OfferLetterCostRate 不存在，代码 #1"
      titleEng: "OfferLetterCostRate not exists by code #1"
    start_update_lastRateNotExist:
      titleChina: "仅可更新最后一个的起始价格。"
      titleEng: "Start rate can be updated for the last one only."
    start_checkPermission_companyIdNull:
      titleChina: "未找到对应公司！"
      titleEng: "Didn't find corresponding company!"
    start_checkPermission_noPermission:
      titleChina: "只有管理员和候选人的客户经理可以更新入职信息。"
      titleEng: "Only Admin and candidate's AM can update onboarding information."
    start_common_internetError:
      titleChina: "网络错误，请稍后重试！"
      titleEng: "Internet error, please try again later!"
    start_delete_lastRateNoExist:
      titleChina: "仅能编辑最新一段的账单费率或工资费率。"
      titleEng: "Only the latest bill or pay rate can be modified."
    start_delete_extendStartContractRateIdNull:
      titleChina: "请在首个起始点处填写账单费率和工资费率。"
      titleEng: "Please enter a bill rate and pay rate for the initial start."
    start_findById_notExist:
      titleChina: "起始合同费率按 ID 不存在。"
      titleEng: "Start contract rate does not exists by id."
    start_findLastByStartId_notExist:
      titleChina: "起始合同费率不存在（根据起始 ID）。"
      titleEng: "Start contract rate does not exists by start id."
    start_createStartExtension_contractRatesIsNull:
      titleChina: "请填写账单费率和工资费率。"
      titleEng: "Please enter the bill rate and pay rate."  
    start_validationStart_startOptionalIsNull:
      titleChina: "Start id #1 不存在"
      titleEng: "Start not exists by id #1"
    start_validationStart_startDateError:
      titleChina: "请确保所填日期与其他任务摘要表彼此之间不存在重叠时段。"
      titleEng: "Please make sure the date you entered does not contain any overlapped time windows with other assignments."
    start_validationStart_startStatusError:
      titleChina: "仅拥有活跃入职流程的任务才能被延期。当前的开始状态是 #1。"
      titleEng: "Only assignment with an active start can be extended. This start status is #1"
    start_validationStart_jobNull:
      titleChina: "未找到相关信息"
      titleEng: "No data found."
    start_validationStart_jobTypeError:
      titleChina: "岗位类型需为合同工或薪酬外包。"
      titleEng: "Job type must be either contact or payrolling."
    start_updateStartExtension_startIsNull:
      titleChina: "未找到延期任务。"
      titleEng: "No extension found."  
    start_updateStartExtension_startDateError:
      titleChina: "请确保所填日期与其他任务摘要表彼此之间不存在重叠时段。"
      titleEng: "Please make sure the date you entered does not contain any overlapped time windows with other assignments."
    start_startFailedWarranty_startIdNull:
      titleChina: "Start id 不能是空"
      titleEng: "Start id can not be null"
    start_startFailedWarranty_endDateNull:
      titleChina: "Start失败结束日期不能为空"
      titleEng: "Start failed warranty end date can not be null"
    start_startFailedWarranty_jobTypeError:
      titleChina: "Start必须是一份全职工作。"
      titleEng: "Start must be a full time job."
    start_startFailedWarranty_totalAmountNull:
      titleChina: "若无替换，则需要新的总金额。"
      titleEng: "If there is no replacement, the new total amount is required."
    start_startFailedWarranty_notExist:
      titleChina: "未找到未过保证期记录。"
      titleEng: "No failed warranty found."
    start_startFailedWarranty_updateTotalAmountNull:
      titleChina: "若无替换，则需要新的总金额。"
      titleEng: "If there is no replacement, the new total amount is required."
    start_fteFindByStartId_startFteRateNull:
      titleChina: "Start全职员工费率不存在，起始 ID 为 #1"
      titleEng: "Start fte rate not exist by start id #1"  
    start_save_commissionsNull:
      titleChina: "佣金不能为空"
      titleEng: "Commissions can not be null" 
    start_save_warrantEndDateNull:
      titleChina: "保修结束日期不能为空"
      titleEng: "Warranty end date can not be null" 
    start_createForContract_contractRatesNull:
      titleChina: "合同费率不应为空"
      titleEng: "Contract rates should not null"
    start_validateExistsStart_startNull:
      titleChina: "该候选人已经就职于 #1 - #2。"
      titleEng: "This candidate has started working at Job #1 - #2"
    start_getTalentRecruitmentProcess_isNull:
      titleChina: "未找到职位申请。"
      titleEng: "No application found."
    start_getTalent_isNull:
      titleChina: "未找到候选人。"
      titleEng: "No candidate found."
    start_getJob_isNull:
      titleChina: "未找到岗位。"
      titleEng: "No job found."
    start_update_startIsNull:
      titleChina: "start id 不存在"
      titleEng: "Start does not exists by id."
    start_checkPermissionByCompanyId_companyIsNull:
      titleChina: "未找到对应公司！"
      titleEng: "Didn't find corresponding company!"
    start_checkPermissionByCompanyId_noPermission:
      titleChina: "只有管理员和候选人的客户经理可以更新入职信息。"
      titleEng: "Only Admin and candidate's AM can update onboarding information."  
    start_findById_startNoExist:
      titleChina: "start id 不存在"
      titleEng: "Start does not exists by id." 
    start_terminationSave_startIdIsNull:
      titleChina: "start id 不能为空"
      titleEng: "Start id can not be null" 
    start_terminationSave_dateIsNull:
      titleChina: "开始终止日期不能为空"
      titleEng: "Start termination date can not be null" 
    start_terminationSave_jobTypeError:
      titleChina: "岗位类型需为合同工或薪酬外包。"
      titleEng: "Job type must be either contact or payrolling." 
    start_syncTalentToES_error:
      titleChina: "TalentServiceImpl syncTalentToES 错误"
      titleEng: "TalentServiceImpl syncTalentToES error."
    start_rateChange_startContractRateIdNotNull:
      titleChina: "新的 startContractRate 不能已经有一个 ID StartContractRate id 已存在"
      titleEng: "A new startContractRate cannot already have an ID StartContractRate id exists"
    start_rateChange_param_null:
      titleChina: "入参确实"
      titleEng: "request param is missing"
    missing_input:
      titleChina: "缺少入参"
      titleEng: "Missing Input"
    not_permission:
      titleChina: "缺少数据权限"
      titleEng: "not data permission"
    invoicing_inputAmountGreaterThanGpAmount:
      titleChina: "输入金额大于应收金额"
      titleEng: "The input amount is greater than the receivable amount"
    invoicing_inputAmountGreaterThanAmountReceived:
      titleChina: "候选人#1预付金金额大于应收金额"
      titleEng: "Candidate #1 advance payment amount is greater than the amount due"   
    invoicing_getSequence_error:
      titleChina: "获取开票编码错误"
      titleEng: "Error in obtaining the invoicing code"   
    invoicing_mult_company:
      titleChina: "存在多个公司信息"
      titleEng: "There are multiple company information"    
    invoicing_prepayment_insufficient:
      titleChina: "预付金余额不足"
      titleEng: "Insufficient prepayment balance"
    invoicing_application_not_found:
      titleChina: "未找到数据信息"
      titleEng: "No data found"  
    invoicing_application_status_error:
      titleChina: "开票状态不允许操作"
      titleEng: "The billing status cannot be operated." 
    invoicing_application_type_error:
      titleChina: "开票类型不允许操作"
      titleEng: "The operation is not allowed for the billing type."  
    invoicing_application_payment_isuse:
      titleChina: "转为预付金金额已被使用，不允许取消。"
      titleEng: "The amount converted to prepayment has been used and cancellation is not allowed."
    invoicing_application_elec_exist:
      titleChina: "数电票编码已存在。"
      titleEng: "The digital ticket code already exists."
    invoicing_prepayment_amount_error:
      titleChina: "金额输入错误。"
      titleEng: "Amount input error."  
    invoicing_application_invoicingType_error:
      titleChina: "抵扣发票不能批量通过审批。"
      titleEng: "Deduction invoices cannot be approved in batches." 
    invoicing_void_application_exist:
      titleChina: "废票已经存在"
      titleEng: "Invalid votes already exist."
    invoice_type_not_exist:
      titleChina: "发票类型不存在"
      titleEng: "Invoice type does not exist."          