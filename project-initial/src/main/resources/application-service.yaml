server:
  port: 9004
  servlet:
    context-path: /application
    
index:
  cache:
    isOpen: false
    expire-time: 600
    
api-properties:
  appl:
    recruitmentprocessnotepage_configMiss:
      titleChina: "配置缺少一些键！"
      titleEng: "configs missing some keys!"
    recruitmentprocessnotepage_configNotFind:
      titleChina: "找不到所需密钥！错误 #1"
      titleEng: "cannot find required key! error #1"  
    recruitmentprocessnotepage_diffKey:
      titleChina: "不同的密钥！"
      titleEng: "different key!"  
    recruitmentprocessnotepage_cannotSetDisplay:
      titleChina: "您无法为输入字段设置仅显示 [ #1 ]"
      titleEng: "You cannot set display-only for input field [ #1 ]" 
    recruitmentprocessnotepage_mustSetDisplay:
      titleChina: "您必须设置输入字段是否为必填项 [ #1 ]"
      titleEng: "You must set it's required or not for input field [ #1 ]" 
    recruitmentprocessnotepage_cannotSetRequireDisplay:
      titleChina: "您无法设置仅显示字段是否需要 [ #1 ]"
      titleEng: "You cannot set require or not for display-only field [ #1 ]" 
    recruitmentprocessnotepage_mustSetRequireDisplay:
      titleChina: "对于仅显示字段，您必须将 [onlyDisplay] 设置为“true”[ #1 ]"
      titleEng: "You must set [onlyDisplay] to 'true' for display-only field [ #1 ]" 
    recruitmentprocessnote_idIsNull:
      titleChina: "无效 ID RecruitmentProcessNode ID 为 null"
      titleEng: "Invalid id RecruitmentProcessNode id null"
    recruitmentprocessnote_updateDiffId:
      titleChina: "recruitmentProcessId 错误！"
      titleEng: "recruitmentProcessId wrong!"  
    recruitmentprocessnote_findById:
      titleChina: "未找到 RecruitmentProcessNode，ID 为 #1"
      titleEng: "RecruitmentProcessNode not found by id is #1"
    recruitmentprocessnote_noPermission:
      titleChina: "RecruitmentProcessNode id 为 #1，无权限"
      titleEng: "No permission for RecruitmentProcessNode id is #1"  
    recruitmentprocess_createNoteIsnull:
      titleChina: "招聘流程节点无效！"
      titleEng: "Invalid recruitment process nodes!"
    recruitmentprocess_createNoteTypeIsnull:
      titleChina: "节点无效"
      titleEng: "Invalid node!"
    recruitmentprocess_createDuplicateNoteType:
      titleChina: "节点类型重复！"
      titleEng: "Duplicate node type!"
    recruitmentprocess_createMissNote:
      titleChina: "缺少所需节点！"
      titleEng: "Missing required node!"  
    recruitmentprocess_updateIdIsZero:
      titleChina: "您仍有正在招聘的职位，请先关闭它！"
      titleEng: "You still have active job which using current recruitment process, please close it first!"  
    recruitmentprocess_findById:
      titleChina: "未找到 RecruitmentProcessNode，ID 为 #1"
      titleEng: "RecruitmentProcessNode not found by id is #1" 
    recruitmentprocess_noPermission:
      titleChina: "您无权访问此招聘流​​程"
      titleEng: "You're not authorized to access this recruitment process" 
    recruitmentprocess_recruitmentProcessIsnull:  
      titleChina: "招聘流程尚未设置，请联系管理员寻求帮助。"
      titleEng: "RecruitmentProcess not setup yet, please contact administrator for help." 
    recruitmentprocess_configRecruitmentProcessIsnull:
      titleChina: "招聘流程已存在 id=#1, name=#2, tenant=#3, jobType=#4"
      titleEng: "Recruitment process already exists id=#1, name=#2, tenant=#3, jobType=#4" 
    ipg_validateAndUpdateJobStatusToFilled:
      titleChina: "该职位已经被填补/关闭/取消，并不再接受提交。"
      titleEng: "This job position is either filled, closed, or cancelled and no longer accept submissions." 
    ipg_validateTalentRecruitmentProcessIsnull:
      titleChina: "必须输入 talentRecruitmentProcessId"
      titleEng: "The input talentRecruitmentProcessId is required." 
    ipg_validateTalentRecruitmentProcessIdIsnull:
      titleChina: "id #1 未找到人才招聘流程"
      titleEng: "Talent recruitment process not found by id #1"
    ipg_validateTalentRecruitmentProcessNoPermission:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "No permission for this talent recruitment process."
    ipg_validate3DayProtectionRule:
      titleChina: "无法提交该候选人。请向所有者申请权限。"
      titleEng: "No permission to submit this candidate. Please ask the owner to grant you the permission."
    ipg_checkJobStatusNoPremission:
      titleChina: "您无权提交候选人担任其他租户的工作。"
      titleEng: "You are not authorized to submit candidate to other tenant's job."
    ipg_checkJobStatusIsNotOpen:
      titleChina: "该职位已经被填补/关闭/取消，并不再接受提交。"
      titleEng: "This job position is either filled, closed, or cancelled and no longer accept submissions." 
    talentrecruitmentprocess_interviewIsnull:
      titleChina: "未找到 ID #1 的采访信息"
      titleEng: "Not found interview information by id #1"
    talentrecruitmentprocess_offerNoPermission:
      titleChina: "您无权更新此信息。"
      titleEng: "You are not authorized to update this information."
    talentrecruitmentprocess_offerLetterCostRateIsnull:
      titleChina: ""
      titleEng: "Tax burden rate not exist #1"
    talentrecruitmentprocess_offerLetterCostRateNoPermission:
      titleChina: "税负率不存在 #1"
      titleEng: "No access to use this tax burden rate."
    talentrecruitmentprocess_offerLetterCostRateMapIsnull:
      titleChina: "Msp 率不存在 #1"
      titleEng: "Msp rate not exist #1"
    talentrecruitmentprocess_offerLetterCostRateMapNoPermission:
      titleChina: "无法使用此 msp 速率。"
      titleEng: "No access to use this msp rate."
    talentrecruitmentprocess_offerLetterCostImmigrationIsNull:
      titleChina: "移民成本不存在 #1"
      titleEng: "Immigration cost not exist #1"
    talentrecruitmentprocess_offerLetterCostRateImmigrationNoPermission:
      titleChina: "无权使用此移民费用。"
      titleEng: "No access to use this immigration cost."
    talentrecruitmentprocess_offerLetterCostValidateTenant:
      titleChina: "您无法访问不属于您的租户的数据。"
      titleEng: "You can't access the data that not belongs to your tenant."  
    talentrecruitmentprocess_completeCurrentNodeAndActiveNextNodeIsnull:
      titleChina: "节点配置有问题，请联系支持团队寻求帮助。"
      titleEng: "There is something wrong with the node configuration, please contact the support team for help."
    talentrecruitmentprocess_completeCurrentNodeAndActiveNextNodeNodeType:
      titleChina: "请先完成 #1 步骤。"
      titleEng: "Please complete the #1 step first."  
    talentrecruitmentprocess_eliminateNodeIsnull:
      titleChina: "系统出现了一些问题。请联系客服寻求帮助。"
      titleEng: "Something went wrong on our side. Please reach out to our customer support for assistance."
    talentrecruitmentprocess_findInfoIdIsnull:
      titleChina: "TalentRecruitmentProcessId 是必需的。"
      titleEng: "TalentRecruitmentProcessId is required."
    talentrecruitmentprocess_findInfoProcessIsnull:
      titleChina: "id #1 未找到人才招聘流程"
      titleEng: "Talent recruitment process not found by id #1"
    talentrecruitmentprocess_findInfoNoPermission:
      titleChina: "无权限查看该流程"
      titleEng: "No permission for this talent recruitment process."
    talentrecruitmentprocess_findAllProcessIsnull:
      titleChina: "未找到人才招聘流程，由 talentId #1 jobId #2 找到"
      titleEng: "Talent recruitment process not found by talentId #1 jobId #2"
    talentrecruitmentprocess_onboardNotFound:
      titleChina: "未找到入职信息。"
      titleEng: "No onboard information found."  
    talentrecruitmentprocess_submitToJobProcessIdIsnull:
      titleChina: "必须输入 recruitmentProcessId。"
      titleEng: "The input recruitmentProcessId is required."
    talentrecruitmentprocess_submitToJobProcessIsnull:
      titleChina: "未找到 ID #1 的招聘流程"
      titleEng: "Recruitment Process not found by id #1"
    talentrecruitmentprocess_submitToJobNoPermission:
      titleChina: "暂无权限查看该跟进备注。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    talentrecruitmentprocess_submitToJobTalentProcessIdIsnull:
      titleChina: "输入 talentRecruitmentProcessId 是必需的。"
      titleEng: "The input talentRecruitmentProcessId is required."
    talentrecruitmentprocess_submitToJobTalentProcessIsnull:
      titleChina: "id #1 未找到人才招聘流程"
      titleEng: "Talent recruitment process not found by id #1"
    talentrecruitmentprocess_toVoNodeIsnull:
      titleChina: "数据不完整，无法操作！"
      titleEng: "The process data is incomplete and cannot be operated!"
    talentrecruitmentprocess_submitToJobProcessExist:
      titleChina: "该候选人已经申请此职位。请在招岗位页面更新申请阶段的相关信息。"
      titleEng: "The candidate has applied this job already. Please go to the job profile page to make any updates of the application stage."    
    talentrecruitmentprocess_checkPermissionCompanyIdIsnull:
      titleChina: "只有第二方可以向客户提交候选人。"
      titleEng: "Only second party can submit candidate to client."
    talentrecruitmentprocess_checkPermissionisCurrentCompanyAm:
      titleChina: "未被授予此权限。只有管理员和客户经理才能向客户提交候选人。"
      titleEng: "No permission granted. Only Admin and the AM can submit candidates to the client."
    talentrecruitmentprocess_offerAcceptSendMailFailed:
      titleChina: "候选人接受offer邮件发送失败。"
      titleEng: "Failed to send the offer accepted email."
    talentrecruitmentprocess_validateExistsStart:
      titleChina: "该候选人已经就职于 #1 - #2。"
      titleEng: "This candidate has started working at Job #1 - #2"
    talentrecruitmentprocess_checkOtherRecruitmentProcessForTalent:
      titleChina: "该候选人已经就职于职位，无法被提交。"
      titleEng: "This candidate has started working at Job and is not available."    
    talentrecruitmentprocess_downloadPdfNotAcceptOffer:
      titleChina: "候选人尚未达到接受Offer的阶段。"
      titleEng: "Candidate has not reached the Offer Accepted stage."
    talentrecruitmentprocess_downloadPdfNotGottenOffer:
      titleChina: "候选人尚未达到Offer阶段。"
      titleEng: "Candidate has not reached the Offer stage."
    talentrecruitmentprocess_create:
      titleChina: "新的 recruitmentProcess 不能已经有一个 ID RecruitmentProcess id 已存在"
      titleEng: "A new recruitmentProcess cannot already have an ID RecruitmentProcess id exists"   
    talentrecruitmentprocess_onboardEliminate_noStartInfo:
      titleChina: "未找到start信息"
      titleEng: "No start information found"
    talentrecruitmentprocess_onboardEliminate_timeError:
      titleChina: "候选人已入职无法进行淘汰操作"
      titleEng: "The candidate has been hired and cannot be eliminated." 