server:
  port: 9002
  servlet:
    context-path: /company
  compression:
    enabled: true
    mime-types: application/json    

open:
  am:
    report:
      tenantIds: 4

application:
  company:
    defaultClientNote: "Market Expansion Plan:<br>- Countries where the customer has already entered.<br>- Countries the customer intends to enter.<br>- Estimated timeline for market entry in each country.<br>- Services they may require to support their expansion: Recruitment (long-term/full-time), Staffing (temporary/contract workers), Bulk Hiring/RPO, Human Resources Services (EOR, PEO, HRO), IT Outsourcing/Services, IT Call Center, etc."

api-company-properties:
  companyService:
    am_findAmReport_noPermisson:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."  
    am_common_paramNull:
      titleChina: "参数不能为空"
      titleEng: "param cannot be empty"
    location_common_companyNotExist:
      titleChina: "该客户联系人不存在。"
      titleEng: "The client contact does not exist."
    location_checkDuplicatedCompanyLocation_existed:
      titleChina: "位置已经存在。"
      titleEng: "location already existed."
    location_correctHistoricalCompanyLocation_apiNotExist:
      titleChina: "此 API 不存在。"
      titleEng: "this api does not exist."  
    note_createClientNote_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."   
    note_createClientNote_inactive:
      titleChina: "该客户联系人不存在。"
      titleEng: "The client contact does not exist." 
    note_updateClientNote_notExist:
      titleChina: "该跟进备注不存在。"
      titleEng: "The note does not exist."  
    note_migrateHistoryClientNote_apiNotExist:
      titleChina: "此 API 不存在。"
      titleEng: "this api does not exist."
    note_createProgressNote_upgraded:
      titleChina: "已赢得并成功达成销售线索。"
      titleEng: "The sales lead has been won and closed successfully."
    overview_checkCompanyLocation_notExist:
      titleChina: "未找到公司地址。"
      titleEng: "No company locations found."
    overview_checkCompanyLocation_notDel:
      titleChina: "该公司地址与至少一名客户联系人的地址相关联，请先到联系人档案中修改联系人地址。"
      titleEng: "This company location is used by at least one of the client contacts and cannot be deleted. Please edit the address in the contact's profiles first."
    overview_checkEnumCompanyTags_notExist:
      titleChina: "enumCompanyTags 不存在。"
      titleEng: "enumCompanyTags does not exist."
    overview_checkEnumCompanyTags_exception:
      titleChina: "enumCompanyTags 参数错误。消息 -> #1"
      titleEng: "enumCompanyTags param error. message -> #1"    
    overview_checkDuplicationCompanyContact_isDuplicate:
      titleChina: "该客户联系人联系方式已存在。"
      titleEng: "The client contact information already exists."  
    overview_checkCompanyActiveContact_contactExist:
      titleChina: "此客户联系人存在于以下公司下 id #1，公司名称 #2"
      titleEng: "This client contact exists under the following companies company id #1, company name #2." 
    overview_checkSalesLeadExist_notExist:
      titleChina: "未找到相应销售代表。"
      titleEng: "No sales leads found." 
    overview_checkSalesLeadUniqueType_nonPromoted:
      titleChina: "推广的潜在客户和非推广的潜在客户不能同时更新。"
      titleEng: "Promoted leads and non-promoted leads cannot be updated at the same time. " 
    overview_checkSalesLeadUniqueType_cannotDesign:
      titleChina: "salesLead 开发的参与者不能设计贡献。"
      titleEng: "Participants in the development of salesLead cannot design contributions."  
    overview_common_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions." 
    overview_checkEnumCompanyContactTags_notExist:
      titleChina: "enumCompanyContactTags 不存在。"
      titleEng: "enumCompanyContactTags does not exist." 
    overview_checkEnumCompanyContactTags_exception:
      titleChina: "enumCompanyContactTags 参数错误。消息 #1"
      titleEng: "enumCompanyContactTags param error. message #1"   
    overview_contact_information_conflict:
      titleChina: "联系信息冲突。"
      titleEng: "contact information conflict." 
    overview_checkDuplicationCompanyName_isNull:
      titleChina: "公司已存在。"
      titleEng: "company already exists"
    overview_checkSalesLeadSource_notExist:
      titleChina: "未找到销售代表来源。"
      titleEng: "No sales lead sources found."
    overview_checkSalesLeadUser_noExist:
      titleChina: "未找到用户。"
      titleEng: "No user found."
    overview_checkCompanyContact_noExist:
      titleChina: "未找到客户联系人。"
      titleEng: "No client contacts found."
    overview_checkCompanyExist_noExist:
      titleChina: "未找到公司。"
      titleEng: "No company found."
    project_common_noPermission:    
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."
    project_create_duplicateTeam:
      titleChina: "项目团队名称已存在。"
      titleEng: "Program team name already exists."  
    project_checkTeamPermission_notFound:
      titleChina: "未找到相应项目团队。"
      titleEng: "No prorgram team found."
    project_create_idExist:
      titleChina: "新的 projectTeam 不能已经有一个 ID 公司项目团队 ID 已存在"
      titleEng: "A new projectTeam cannot already have an ID Company Project Team id exists"  
    contact_common_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."     
    contact_checkTenantId_companyNotExist:
      titleChina: "未找到公司。"
      titleEng: "No company found."
    contact_save_contractNotExist:
      titleChina: "未找到客户联系人。"
      titleEng: "No client contacts found." 
    contact_save_previousContractNotExist:
      titleChina: "未找到以前的合同。"
      titleEng: "No previous contract found."
    contact_save_renewalContractNotExist:
      titleChina: "未找到续约合同。"
      titleEng: "No renewal contract found." 
    contact_checkSalesLeads_notExist:
      titleChina: "未找到销售代表。"
      titleEng: "No sales lead found." 
    contact_checkSalesLeads_upgraded:
      titleChina: "请把相关销售线索的BD进展更新成100%。"
      titleEng: "Please upgrade the BD process of relevant sales lead to 100%."
    contact_getContractUploadUrl_contractIsNull:
      titleChina: "未找到合同。"
      titleEng: "No contract found." 
    contact_downloadContractDisplayImg_noPreview:
      titleChina: "正在生成预览..."
      titleEng: "Generating preview...." 
    contact_downloadContractDisplayImg_errorImage:
      titleChina: "系统出现了一些问题。请联系客服寻求帮助。"
      titleEng: "Something went wrong on our side. Please reach out to our customer support for assistance."
    contact_addTextWatermarkToImage_error:
      titleChina: "合同添加水印错误。"
      titleEng: "contract add watermark error."
    contact_save_idExist:
      titleChina: "新合同不能已经有一个 ID 公司合同 ID 已存在"
      titleEng: "A new contract cannot already have an ID Company Contract id exists"
    searchCompanyCount_responseIsNull:
      titleChina: "从常见服务错误中获取公司数量并且响应为空。"
      titleEng: "get company count from common service error and response is null."  
    searchCompanyNote_responseIsNull:
      titleChina: "从常见服务错误中搜索公司说明并且响应为空。"
      titleEng: "search company note from common service error and response is null." 
    searchCustomerMetric_responseIsNull:
      titleChina: "从常见服务错误中获取客户指标并且响应为空"
      titleEng: "get customer metric from common service error and response is null"
    updateCompaniesFolder_responseIsNull:
      titleChina: "updateCompaniesFolder 到 EsFiller 错误。响应 #1"
      titleEng: "updateCompaniesFolder to EsFiller error. response #1"
    updateCompaniesFolder_responseError:
      titleChina: "updateCompaniesFolder 到 EsFiller 错误 #1"
      titleEng: "updateCompaniesFolder to EsFiller error #1"  
    folder_queryFolder_noExist:
      titleChina: "未找到搜索文件夹。"
      titleEng: "No search folder found."
    folder_deleteFolder_paramIsNull:
      titleChina: "foldIdList 为空"
      titleEng: "foldIdList is empty"  
    folder_deleteSearchFolder_DataIsNull:
      titleChina: "未找到搜索文件夹。"
      titleEng: "No search folder found."
    folder_createCustomFolder_categoryError:
      titleChina: "类别必须是 PROSPECT_ALL_COMPANY 或 CLIENT_ALL_COMPANY。"
      titleEng: "category must be PROSPECT_ALL_COMPANY or CLIENT_ALL_COMPANY."
    folder_deleteCustomFolder_paramIsNull:
      titleChina: "未找到客户文件夹。"
      titleEng: "No custom folder found."  
    folder_setSearchCustomFolderSql_folderTypeNotExist:
      titleChina: "folderType 不存在。"
      titleEng: "folderType does not exists."
    folder_common_folderNotExist:
      titleChina: "未找到文件夹。"
      titleEng: "No folder found."  
    folder_addCustomFolderSharedUser_userNotExist:
      titleChina: "未找到用户。"
      titleEng: "No user found." 
    folder_addCustomFolderSharedTeam_paramIsNull:
      titleChina: "未找到团队。"
      titleEng: "No team found."  
    folder_addCustomFolderSharedTeam_teamNotExist:
      titleChina: "未找到团队。"
      titleEng: "No team found."
    folder_checkCustomFolderName_exists:
      titleChina: "文件夹名称已存在。"
      titleEng: "Folder name already exists."
    folder_checkCompanyPermission_notExists:
      titleChina: "未找到公司。"
      titleEng: "No company found." 
    folder_checkCompanyCategory_mustSame:
      titleChina: "文件夹的类别必须相同。"
      titleEng: "folder's category must be the same."
    folder_checkCompanyCategory_noEscalated:
      titleChina: "请确保被选中数据里没有正在开发中的公司。"
      titleEng: "Please make sure no prospect companies are selected."
    folder_checkCompanyCategory_noUpgraded:
      titleChina: "请确保被选中数据里没有正在开发中的公司。"
      titleEng: "Please make sure no prospect companies are selected." 
    folder_checkInList_queryCondition:
      titleChina: "查询 sql 中的条件列表 > 1000 大于 1"
      titleEng: "query sql in condition list > 1000 more than 1"
    folder_checkFolderCompany_notExist:
      titleChina: "文件夹公司不存在。"
      titleEng: "folder company does not exists."
    folder_searchFolder_nameEmpty:
      titleChina: "请输入一个文件夹名称。"
      titleEng: "Please enter a folder name."
    folder_searchFolder_limitEmpty:
      titleChina: "限制为空"
      titleEng: "limit is empty"    
    saleslead_saveFromTalent_inactive:
      titleChina: "请确保销售线索关联的联系人在APN上是激活状态。"
      titleEng: "Please make sure the contact for the sales lead is active on APN."
    saleslead_saveFromTalent_talentIdNull:
      titleChina: "人才 ID 必须存在。"
      titleEng: "Talent id must be exist."
    saleslead_save_talentCompanyBriefNull:
      titleChina: "候选人已存在，详情见ID #1，姓名 #2"
      titleEng: "Candidate already exists, see id #1, name #2" 
    saleslead_updateTalentRelationData_talentNotExist:
      titleChina: "未找到候选人。"
      titleEng: "No candidate found."
    saleslead_updateSalesLeadClientContact_inactive:
      titleChina: "该联系人未被激活，无法编辑。请激活联系人后再重试。"
      titleEng: "The contact is inactive and cannot be edited. Please acitivate the contact and try again."
    saleslead_updateSalesLeadClientContact_talenNotExist:
      titleChina: "候选人已存在"
      titleEng: "Candidate already exists"
    saleslead_updateSalesLeadClientContact_talenExist:
      titleChina: "候选人已存在"
      titleEng: "Candidate already exists" 
    saleslead_checkEnumCompanyContactTags_notExist:
      titleChina: "enumCompanyContactTags 不存在。"
      titleEng: "enumCompanyContactTags does not exist."
    saleslead_checkEnumCompanyContactTags_error:
      titleChina: "enumCompanyContactTags 参数错误。消息 #1"
      titleEng: "enumCompanyContactTags param error. message #1 "
    saleslead_clientContactDetection_emailNull:
      titleChina: "timeSheetUser 电子邮件不能为空。"
      titleEng: "timeSheetUser email must be not empty."
    saleslead_clientContactDetection_emailExist:
      titleChina: "邮箱已存在。"
      titleEng: "Email address already exists."
    saleslead_setTalentContactInformation_notExist:
      titleChina: "候选人已存在，详情见ID #1，姓名 #2"
      titleEng: "Candidate already exists, see id #1, name #2"
    saleslead_approver_clientContactNotExist:
      titleChina: "未找到客户联系人。"
      titleEng: "No client contacts found."
    saleslead_approver_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions." 
    saleslead_approver_nopermission_contact_am:
      titleChina: "无权限，请联系AM允许您执行该操作。"
      titleEng: "No permission, please contact AM to allow you to perform this operation." 
    saleslead_approver_emailNotExist:
      titleChina: "未找到此客户联系人的电子邮件地址。"
      titleEng: "No email address found under this client contact."
    saleslead_approver_passwordIsEmpty:
      titleChina: "请输入密码。"
      titleEng: "Please enter a password."     
    saleslead_approver_emailInconsistent:
      titleChina: "联系邮箱地址与批准邮箱地址不一致，请在联系编辑页面检查联系邮箱。"
      titleEng: "The contact email address is inconsistent with the approve email address, Please check the contact email in the contact edit page" 
    saleslead_findBriefContactByIdAndReceiveEmail_contactNotExist:
      titleChina: "未找到客户联系人。"
      titleEng: "No client contacts found." 
    saleslead_createClientContactFromCommonPool_clientExist:
      titleChina: "联系人已存在于选中的公司。"
      titleEng: "Contact already exists under the selected company."
    saleslead_createClientContactFromCommonPool_paramNull:
      titleChina: "创建职位需要有esId和companyId。"
      titleEng: "The create job need have esId and companyId. "
    saleslead_updateContactInfo_notExist:
      titleChina: "要更新的客户联系人不存在"
      titleEng: "Client Contact to update dose not exist "
    saleslead_checkCompanyLocation_notExist:
      titleChina: "未找到公司地址。"
      titleEng: "No company location found."
    saleslead_checkDuplicationContactByCompanyIdAndTalentId_contactExist:
      titleChina: "公司联系人已存在，联系人 #1"
      titleEng: "company contact already exists, contact #1 "  
    saleslead_checkDuplicationContactByCompanyIdAndTalentId_error:
      titleChina: "候选人已存在。"
      titleEng: "Candidate already exists."  
    saleslead_checkActiveContact_contactAlready:
      titleChina: "联系人已存在于另一家公司，详情请看公司ID #1，公司名称 #2。"
      titleEng: "Contact already exists under another company, see company id #1, company name #2."
    saleslead_checkCompanyPermission_notExist:
      titleChina: "未查询到此公司。"
      titleEng: "No company found."
    skipsubmit_checkCompanyPermission_notFound:
      titleChina: "未找到公司。"
      titleEng: "No company found."
    skipsubmit_checkCompanyPermission_notPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."  
    skipsubmit_checkUserPermission_notPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions." 
    company_purchase_order_currency_error:
      titleChina: "同一客户经理名下已存在同币种 PO 组，请在同币种 PO 组下直接添加新的采购单数据"
      titleEng: "A purchase order group in the same currency with the selected AM name already exists. Please add new purchase order records to the existing group."
    company_purchase_order_currency_diff:
      titleChina: "币种不同请重新选择"
      titleEng: "If the currency is different, please choose again."
    company_purchase_order_amount_error:  
      titleChina: "剩余总金额不能为负数，请修改相关金额再次提交。"
      titleEng: "The total balance cannot be negative, please edit the amount and resubmit." 
    company_purchase_order_number_error:  
      titleChina: "该采购单号码在此客户下已存在"
      titleEng: "The purchase order number already exists under this company."    
    company_purchase_order_checkPermissionByCompanyId_companyIsNull:
      titleChina: "未找到对应公司！"
      titleEng: "Didn't find corresponding company!"
    company_purchase_order_checkPermissionByCompanyId_noPermission:
      titleChina: "只有管理员和公司的客户经理可以操作。"
      titleEng: "Only administrators and company account managers can operate." 
    company_client_invoicing_clientName_exist:
      titleChina: "客户名称已存在。"
      titleEng: "Customer information already exists." 
    company_client_invoicing_clientName_isUse:
      titleChina: "客户信息已经被使用，不允许修改。"
      titleEng: "The customer information has been used and cannot be modified."
    company_client_invoicing_socialCreditCode_exist:
      titleChina: "社会统一编码已存在。"
      titleEng: "Social Unicode already exists."   
    company_client_invoicing_bankAccount_exist:
      titleChina: "银行账号已存在。"
      titleEng: "Bank Account already exists."
    company_client_invoicing_socialCreditCode_length:
      titleChina: "统一社会信用代码长度错误。"
      titleEng: "Unified social credit code length error."           

crm-forward-url:
  urlMap:
    POST:
      - /account/api/v1/companies
      - /account/api/v1/companies/search
      - /account/api/v1/business/find-all-by-company-id
      - /contact/api/v1/contact
      - /common/api/v1/bd-record
      - /common/api/v1/bd-record/search
      - /account/api/v1/contracts
      - /account/api/v1/location
      - /common/api/v1/{module}/bd-record
      - /common/api/v1/bd-record/{module}/search
      - /account/api/v1/business/find-by-company-id/from-module/{module}
      - /account/api/v1/companies/find-by-business-name
      - /account/api/v1/business
      - /account/api/v1/contracts/search
      - /contact/api/v1/contact/duplicated-contact-list
      - /contact/api/v1/contact-apn
      - /account/api/v1/business/company/all-and-selected-business
      - /contact/api/v1/contact/validate-to-apn
      - /common/api/v1/bd-record/top/{id}/{top}
      - /account/api/v1/companies/calendar/company/list
      - /report/api/v1/bd-report/progress-notes/list
      - /report/api/v1/bd-report/progress-notes/list-excel
      - /lead/api/v1/contact/search/company/{id}
      - /account/api/v1/companies/{id}/relationship/msp
      - /account/api/v1/companies/{id}/relationship/parent
      - /account/api/v1/companies/{id}/relationship/msp/list
      - /account/api/v1/companies/{id}/relationship/parent/list
      - /contact/api/v1/contact/suspected-duplicate-phones-check
      - /lead/api/v1/search/businessName
      - /account/api/v1/contracts/get-contract-files-parse-result
      - /account/api/v1/contracts/affiliated-no-poaching-companies
      - /account/api/v1/companies/all-kinds-of-companies/search
      - /account/api/v1/companies/no-poaching-companies/list
    PUT:
      - /account/api/v1/companies/{companyId}/basic-info
      - /account/api/v1/companies/{companyId}/business
      - /account/api/v1/companies/{companyId}
      - /account/api/v1/companies/{companyId}/upgrade
      - /account/api/v1/contracts/{contractId}
      - /account/api/v1/contracts/status
      - /common/api/v1/bd-record/{id}
      - /contact/api/v1/contact/relation/{contactRelationId}/detail
      - /contact/api/v1/contact/relation/{relationId}/update-contact-account-status
      - /common/api/v1/{module}/bd-record/{id}
    GET:
      - /contact/api/v1/contact/company/{companyId}/relation
      - /contact/api/v1/contact/relation/{contactRelationId}/detail
      - /account/api/v1/companies/{companyId}
      - /account/api/v1/companies/category-folders/count
      - /common/api/v1/dict/business-progress
      - /common/api/v1/dict/client-level
      - /common/api/v1/dict/contact-category
      - /common/api/v1/dict/contact-type
      - /common/api/v1/dict/follow-up-contact-type
      - /account/api/v1/companies/{id}/stage
      - /common/api/v1/dict/user-responsibility
      - /account/api/v1/location/list
      - /account/api/v1/contracts/search
      - /account/api/v1/contracts/display/{contractId}
      - /account/api/v1/contracts/{contractId}
      - /account/api/v1/contracts/download-url/{contractId}
      - /contact/api/v1/contact/company/{companyId}/relation-name
      - /account/api/v1/companies/msp/list
      - /account/api/v1/companies/no-poaching-companies/list
      - /lead/api/v1/saleslead/get-latest-by-company-id/{id}
      - /account/api/v1/companies/{accountCompanyId}/affiliated-no-poaching-companies/list
    DELETE:
      - /account/api/v1/companies/{id}/relationship/msp
      - /account/api/v1/companies/{id}/relationship/parent
      - /account/api/v1/contracts/affiliated-no-poaching-companies/{id}

    

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher