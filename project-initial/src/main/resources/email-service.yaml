server:
  port: 9013
  servlet:
    context-path: /email

application:
  email:
    # url: https://ecam-api-staging.hitalentech.com/emailcampaign
    url: https://api-emaily-dev.hitalentech.com/emailcampaign
    system:
      username: sunny
      password:

api-properties:
  emailService:
    audience_createMongoAudienceGroup_nameEmpty:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "Invalid parameter!"
    audience_logAndReturn_error:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "calling Emaily.AI audience service error!"        
    campaign_common_invalidParam:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "Invalid parameter!"  
    campaign_sendHtmlMail_userNull:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "No from attribute and cannot find current user!"
    campaign_sendHtmlMail_noHtmlContent:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "No htmlContent!"
    campaign_sendHtmlMail_noSubject:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "No subject!"
    campaign_sendHtmlMail_noTo:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "No to!"    
    campaign_sendEmailBlast_notFoundUser:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "Cannot find default user!"  
    email_common_invalidParam:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "Invalid parameter!" 
    email_sendRichMailForSystemEmail_noPermission:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "You are not allowed to do this!"       
    attachment_uploadEmailAttachment_fileNull:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "File cannot be empty" 
    attachment_uploadEmailAttachment_fileLarge:
      titleChina: "无法找到相关工时表或报销数据。"
      titleEng: "The file to upload is too large ." 
      