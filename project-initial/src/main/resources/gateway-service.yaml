server:
  port: 8888
  servlet:
    context-path: /

spring:
  cloud:
    gateway:
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origin-patterns:
              - https://*.hitalentech.com
              - http://localhost:3000/
              - http://localhost:3001/
            allowedHeaders: "*"
            exposed-headers: "www-authenticate"
            allowCredentials: "true"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
              - PATCH
      discovery:
        locator:
          enabled: true
      routes:
        - id: job-routh
          uri: lb://job-service
          predicates:
            - Path=/job/**
            #- After=2017-01-20T17:42:47.789-07:00[America/Denver]
            #- Before=2017-01-20T17:42:47.789-07:00[America/Denver]
            #- Between=2017-01-20T17:42:47.789-07:00[America/Denver], 2017-01-21T17:42:47.789-07:00[America/Denver]
            #- Cookie=chocolate, ch.p
            #- Header=X-Request-Id, \d+
            #- Host=**.somehost.org,**.anotherhost.org
            #- Method=GET,POST
            #- Query=green
            #- RemoteAddr=***********/24
            #- Weight=group1, 2
        - id: company-routh
          uri: lb://company-service
          predicates:
            - Path=/company/**
        - id: talent-routh
          uri: lb://talent-service
          predicates:
            - Path=/talent/**
        - id: authority-routh
          uri: lb://authority-service
          predicates:
            - Path=/authority/**
        - id: admin-routh
          uri: lb://admin-service
          predicates:
            - Path=/admin/**
        - id: application-routh
          uri: lb://application-service
          predicates:
            - Path=/application/**
        - id: user-routh
          uri: lb://user-service
          predicates:
            - Path=/user/**
        - id: finance-routh
          uri: lb://finance-service
          predicates:
            - Path=/finance/**
        - id: location-routh
          uri: lb://location-service
          predicates:
            - Path=/location/**
        - id: store-routh
          uri: lb://store-service
          predicates:
            - Path=/store/**
        - id: parser-routh
          uri: lb://parser-service
          predicates:
            - Path=/parser/**
        - id: report-routh
          uri: lb://report-service
          predicates:
            - Path=/report/**
        - id: initiation-routh
          uri: lb://initiation-service
          predicates:
            - Path=/initiation/**
        - id: email-routh
          uri: lb://email-service
          predicates:
            - Path=/email/**
        - id: jobdiva-routh
          uri: lb://jobdiva-service
          predicates:
            - Path=/jobdiva/**
        - id: canal-routh
          uri: lb://canal-service
          predicates:
            - Path=/canal/**
        - id: management-routh
          uri: lb://management-service
          predicates:
            - Path=/management/**
        - id: common-routh
          uri: lb://common-service
          predicates:
            - Path=/common/**
        - id: voip-routh
          uri: lb://voip-service
          predicates:
            - Path=/voip/**
        - id: agency-routh
          uri: lb://agency-service
          predicates:
            - Path=/agency/**