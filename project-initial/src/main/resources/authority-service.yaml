server:
  port: 10000
  servlet:
    context-path: /authority

spring:
  main:
    allow-bean-definition-overriding: true
  mvc:
    throw-exception-if-no-handler-found: true

oauth2:
  secret: YXBpYXBwOkI1OmhSJ05eNSlKS04/RWo=

application:
  permission:
    privilegeIdForPrivateJob: 1017
    
api-properties:
  author:
    socialauthority_bad_credential:
      titleChina: "认证未通过"
      titleEng: "Bad Credential"  
    linkedinLogin_getUserFailed:
      titleChina: "无法获取 LinkedIn 用户信息"
      titleEng: "Failed to get linkedin user info"    