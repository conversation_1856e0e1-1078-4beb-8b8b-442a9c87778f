server:
  port: 11001
  servlet:
    context-path: /management

application:
  managementService:
    validateKey:
      expirationTime: 1800
    password:
      iv: aea1107bd43e4696
      key: 552341f0a024490a
      
api-management-properties:
  managementService:
    data_checkInList_conditionLimit:
      titleChina: "查询 sql 中的条件列表 > 1000 大于 1"
      titleEng: "query sql in condition list > 1000 more than 1"  
    product_updateErrorInfo_notExist:
      titleChina: "产品错误信息不存在。"
      titleEng: "product errorInfo does not exist."    
    product_updateErrorInfo_processed:
      titleChina: "产品错误信息已被处理。"
      titleEng: "product errorInfo has been processed."       
    tenant_create_exception:
      titleChina: "无法创建租户发送邮件"
      titleEng: "Failed to create tenant to send mail"       
    tenant_queryTenantByName_tenantNotExist:
      titleChina: "未找到租户。"
      titleEng: "No tenant found."
    tenant_updateTenant_moneyCreditNull:
      titleChina: "更新每月信用额度为空！"
      titleEng: "update monthly credit is null!"
    tenant_updateTenant_bulkCreditNull:
      titleChina: "批量信用为空！"
      titleEng: "bulk credit is null!"  
    tenant_updateTenant_moneyCreditLessTotal:
      titleChina: "更新月度信用不能少#1"
      titleEng: "update monthly credit can not be less #1"
    tenant_updateTenant_bulkCreditLessTotal:
      titleChina: "信用不应该少#1"
      titleEng: "credit should not be less #1"  
    tenant_validateTenantName_tenantAlreadyExist:
      titleChina: "租户名已存在。"
      titleEng: "Tenant name already exists."
    tenant_validateTenantName_emailAlreadyExist:
      titleChina: "邮件地址已存在。"
      titleEng: "Email address already exists."
    user_login_userNull:
      titleChina: "用户名或密码不正确。"
      titleEng: "The username or password is incorrect."
    user_checkLockedAccount_accountLock:
      titleChina: "您的账号将被限制 #1 分钟。"
      titleEng: "Your account is restricted for #1 minutes."
    user_findOneByUsername_userNull:
      titleChina: "用户未使用。"
      titleEng: "The user is not used."
    user_findOneByUsername_userInactive:
      titleChina: "不活跃的用户。"
      titleEng: "Inactive user."
    user_forgetPass_resetPasswordSend:
      titleChina: "我们已向提供的地址发送了一封电子邮件。单击电子邮件中的链接即可重置您的密码。"
      titleEng: "We've sent an email to the address provided. Click the link in the email to reset your password."
    user_forgetPass_emailSendFail:
      titleChina: "系统出现了一些问题。请稍后重新请求发送电子邮件。"
      titleEng: "Something is wrong on our side. Please request to resend the email again later."
    user_resetPassForForget_codeIsNull:
      titleChina: "code是空"
      titleEng: "code is null"  
    user_resetPassForForget_passwordIsNull:
      titleChina: "请输入一个新密码。"
      titleEng: "Please enter s new password."
    user_resetPassForForget_invalidCode:
      titleChina: "无效code"
      titleEng: "invalid code"   
    user_resetPassForForget_invalidCodeExpired:
      titleChina: "验证码已过期。"
      titleEng: "Validation code expired."
    user_resetPassword_userNotLogin:
      titleChina: "用户未登录"
      titleEng: "user is not login"
    user_resetPassword_passwordIsNull:
      titleChina: "请输入一个新密码。"
      titleEng: "Please enter a new password."
    user_resetPassword_oldPasswordNotMatch:
      titleChina: "密码不正确"
      titleEng: "The password is incorrect."
    user_login_inactiveUser:
      titleChina: "不活动的用户不允许登录。"
      titleEng: "Inactive user is not allowed to login."
    recruitmentProcess_create_idNotNull:
      titleChina: "新的 recruitmentProcess 不能已经有一个 ID RecruitmentProcess id 已存在"
      titleEng: "A new recruitmentProcess cannot already have an ID RecruitmentProcess id exists"

      