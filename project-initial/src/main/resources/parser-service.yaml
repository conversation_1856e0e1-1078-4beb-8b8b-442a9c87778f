server:
  port: 9010
  servlet:
    context-path: /parser
# spring:
#   parser-mq:
#     exchange: parser
#     resume-queue: parser-resume
#     jd-queue: parser-jd
#     resume-routing-key: resume
#     jd-routing-key: jd
#     max-priority: 2
application:
  sqsService:
    queueName: want-parse-staging
    region: us-west-2
  filesize:
    resume:
      # 1KB - 30MB
      min: 1024
      max: ********
    jd:
      # 1KB - 1MB
      min: 1024
      max: 1048576
  char:
    jd:
      # max plain text jd characters
      max: 262144

api-parser-properties:
  parserService:
    parser_downloadResume_talentResumeRelationNull:
      titleChina: "没有 TalentResumeRelation！"
      titleEng: "No TalentResumeRelation!"  
    parser_checkParseJdStatusOrGetUploadUrl_fileNameNull:
      titleChina: "文件名或内容类型非空！"
      titleEng: "Non-empty filename or content-type!" 
    parser_checkParseJdStatusOrGetUploadUrl_jdCannotHtml:
      titleChina: "仅支持pdf、doc、和 txt 格式。"
      titleEng: "Only pdf, doc, and txt are supported."
    parser_checkParseJdStatusOrGetUploadUrl_jdTextTooLong:
      titleChina: "仅支持pdf、doc、和 txt 格式。"
      titleEng: "Only pdf, doc, and txt are supported."
    parser_checkParseJdStatusOrGetUploadUrl_error:
      titleChina: "仅支持pdf、doc、和 txt 格式。"
      titleEng: "Only pdf, doc, and txt are supported."
    parser_checkResumeParseResultOrGetUploadUrl_uuidNull:
      titleChina: "UUID不能为空"
      titleEng: "UUID cannot be null"
    parser_checkResumeParseResultOrGetUploadUrl_fileNameNull:
      titleChina: "请输入文件名。"
      titleEng: "Please enter a file name."
      