spring:
  rabbitmq:
    addresses: localhost
    port: 5672
    virtual-host: /dev
    username: apn
    password: apn
    publisher-confirms: true

application:
  email-mq:
    addresses: localhost
    port: 5672
    virtual-host: /dev
    username: apn
    password: apn
    exchange: email.apn
    queue: sing.email-content
    routing-key: sing.email
  esfillerMQ:
    host: localhost
    port: 5672
    # esfilter 暂时和 staging signapore 一致
    username: apn
    password: apn
    # dev环境暂时不回填了，否则在dev环境把staging的回填消费了，导致staging回填有时候不成功
    virtual-host: /dev
    esFillerExchange: bussiness-profile
    toEsFillerRoutingKey: es-a
    toEsFillerQueue: profile-to-es-a
    toEsFillerMaximumMsgCount: 1000
    apnNormalizedTalentRoutingKey: sing.apn_normalized_talent
    apnNormalizedTalentQueue: sing.apn_normalized_talent
    apnNormalizedJobRoutingKey: sing.apn_normalized_job
    apnNormalizedJobQueue: sing.apn_normalized_job
    apnNormalizedCompanyRoutingKey: sing.apn_normalized_company
    apnNormalizedCompanyQueue: sing.apn_normalized_company
    retryThreshold: 2
    notification:
      lark:
        webhookKey:
        webhookUrl: https://open.larksuite.com/open-apis/bot/v2/hook/8ad65dbd-f030-4a6b-bcd1-1f1335d8bf62
  invoice-mq:
    exchange: invoice
    queue: sing.invoice-content
    routing-key: sing.invoice
  xxl-job-mq:
    host: localhost
    port: 5672
    virtual-host: /dev
    username: apn
    password: apn
    exchange: apn-xxl-job
    routingKey: apn-xxl-job-routing-key
    queue: apn-xxl-job-queue

  talent-tx-mq:
    exchange: talent.tx
    queue: sing.talent-tx-mq-content
    routing-key: sing.talentTx  

  update-talent-tx:
    exchange: talent-tx-update
    queue: sing.update-talent-queue
    routing-key: sing.update-talent-finance
    company-exchange: talent-tx-update-company
    company-queue: sing.update-talent-queue-company
    routing-company-key: sing.update-talent-company

  talent-onboard-tx:
    exchange: talent-onboard
    queue: sing.talent-onboard-queue
    routing-key: sing.talent-onboard-application  
  
  sso:
    host: localhost
    port: 5672
    virtual-host: /dev
    username: apn
    password: apn
    user-info-exchange: sso.userinfo.fanout
    user-info-queue: sing.apn-user-info
    management-user-info-queue: sing.management-user-info
    user-active-exchange: sso.userActive.fanout
    user-active-queue: sing.apn-user-active
    management-user-active-queue: sing.management-user-active
    user-binging-client-exchange: sso.userBinging.fanout
    user-binging-client-queue: sing.apn-user-binding-client
    management-user-binging-client-queue: sing.management-user-binging-client
    voip-user-binging-client-queue: sing.voip-user-binging-client
  account:
    backFillRoutingKey: crm-back-fill-sing
    queue: crm-account-sing
  socialProfileMq:
    socialProfileExchange: social_profile
    linkedinRoutingKey: sing.lnkd-request-routing-key
    linkedinRequestQueue: sing.lnkd-request
    linkedinResponseQueue: sing.lnkd-response
    linkedinDelayRoutingKey: sing.lnkd-delay-routing-key
    linkedinDelayQueue: sing.lnkd-delay-queue
    talentIdRoutingKey: sing.talent-id-routing-key
    talentIdQueue: sing.talent-id-queue
    socialProfileMaximumMsgCount: 1000
    msgExpire: 1728000 # 20 days

  jobdiva:
    apn-hr-exchange: jobdiva
    hr-to-apn-queue: hr-to-apn-queue  
    apn-to-hr-routingKey: apn-to-hr-routing-key
    apn-to-hr-queue: apn-to-hr-queue
  
  agencyPortal:
    agencyPortalExchange: agency-user
    agencyPortalRoutingKey: apn-agency-user-sing
    agencyPortalQueue: apn-agency-user-sing

  whisperVoicemail:
    host: localhost
    port: 5672
    virtual-host: /dev
    username: apn
    password: apn
    whisperVoicemailExchange: whisper-voicemail
    whisperVoicemailRoutingKey: sing.whisper-voicemail-routing-key
    whisperVoicemailQueue: sing.whipser-voicemail-queue
  sync-crm-currency:
    exchange: currencyExchange
  rater-ai-recommend:
    host: localhost
    port: 5672
    virtual-host: /dev
    username: apn
    password: apn
    routingKey: ai-recommend-routing-key
    queue: ai-recommend-queue
    exchange: ai-recommend-exchange      
  contract-parse-mq:
    exchange: parser-contract-exchange
    routingKey: parser-contract-routing-key
  note-enrich:
    notify-exchange: parsing-note-exchange
    notify-routingKey: parsing-note-routing-key
    result-queue: parsed-note