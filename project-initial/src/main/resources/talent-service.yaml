server:
  port: 9001
  servlet:
    context-path: /talent

# TODO  中文title
talent:
  profile:
    title-keywords: manager,lead,director,president,vp,svp,ceo,cfo,coo,cto,cmo,cxo,founder,cofounder,co-founder,hrd,head,generalist,partner,principal,fellow

application: 
  talent: 
    similarity: 0.8
  sync:
    threadNum: 36
    total: 360
  address-list-sqs:
    region: us-west-2
    queue: parsing-address-list-staging
chat-service:
  recommend-reason-url: https://gpt.hitalentech.com/chat/recommend_reason
  auth-key: J0IH8oH226zrWQvhFy3Zf
# 不需要本地和生产配置
excel:
  create_talent_by_excel_progress: "create_talent_by_excel_progress_sing:"
  talent_excel_sql_retry_flag: talent_excel_sql_retry_flag_sing
  create_talent_by_excel_queue: create_talent_by_excel_queue_sing
  create_talent_by_excel_node_queue: create_talent_by_excel_node_queue_sing

api-talent-properties:
  talentService:
    elastic_getTalentCategoryCount_responseNull:
      titleChina: "ES 类别计数响应或响应正文为空。"
      titleEng: "ES category count Response or response body is null." 
    elastic_getTalentCategoryCount_responseCodeError:
      titleChina: "ES 类别计数 意外响应状态 #1"
      titleEng: "ES category count Unexpected response status #1" 
    elastic_searchFromCommonPool_responseNull:
      titleChina: "查询talent错误"
      titleEng: "search talent from common service error ." 
    elastic_updateTalentsFolder_internalError:
      titleChina: "内部服务器错误。"
      titleEng: "Internal Server Error" 
    event_create_idNotNull:
      titleChina: "新事件不能已有 ID"
      titleEng: "A new event cannot already have an ID" 
    event_update_idNull:
      titleChina: "事件没有 ID"
      titleEng: "Event have not an ID" 
    event_update_eventNotExist:
      titleChina: "要更新的事件不存在"
      titleEng: "event to update does not exists"
    eventUser_create_idNotNull:
      titleChina: "新的 EventUser 不能已经有一个 ID"
      titleEng: "A new EventUser cannot already have an ID"  
    eventUser_create_eventIdNull:
      titleChina: "事件 ID 不存在"
      titleEng: "Event id does not exists"   
    folder_setSearchFolderSql_typeError:
      titleChina: "无法使用当前搜索类型处理您的请求"
      titleEng: "Can't process your request with the current search type" 
    folder_talentCustom_common_paramNull:
      titleChina: "输入信息无效。"
      titleEng: "The input is invalid."
    folder_talentCustom_createTalentFolder_invalidFolderName:
      titleChina: "文件夹名称无效。"
      titleEng: "The folder name is invalid."  
    folder_talentCustom_deleteFolder_invalidFolder:
      titleChina: "该文件夹无效。"
      titleEng: "The folder is invalid." 
    folder_talentCustom_deleteFolder_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."  
    folder_talentCustom_updateFolderSharingUser_noPermission:
      titleChina: "该用户已经拥有查看该文件夹的权限。"
      titleEng: "The user already has permissions to the folder." 
    folder_talentCustom_addTalentFolderSharingForTeams_noPermission:
      titleChina: "不能与具有不同权限的同一用户共享！"
      titleEng: "cannot share to same user with different permission!" 
    folder_talentCustom_removeSharingForSharedFolder_noShare:
      titleChina: "该用户没有此文件夹的访问权限。"
      titleEng: "The user doesn't have permissions to the folder."    
    folder_talentCustom_getCollaborativeTalentFolderList_failFetch:
      titleChina: "无法获取我的文件夹列表"
      titleEng: "Failed to fetch my folder List"  
    folder_talentCustom_getCollaborativeTalentFolderList_failShareFetch:
      titleChina: "无法获取共享文件夹列表"
      titleEng: "Failed to fetch shared folder List"      
    folder_talentCustom_getCustomAndSharedJobFolderWithPermissionList_noPermissio:
      titleChina: "发生错误。请稍后再试。"
      titleEng: "Something went wrong. Please try again later." 
    folder_talentCustom_getAllTalentsEmailInFolders_paramNull:
      titleChina: "发生错误。请稍后再试。"
      titleEng: "Something went wrong. Please try again later." 
    folder_talentCustom_getAllTalentsEmailInFolders_maxCountLimit:
      titleChina: "您选择的文件夹中的候选者数量似乎超出了我们的限制。请减少选择并重试。"
      titleEng: "It appears that the number of candidates in your selected folders exceeds our limit. Please reduce the selection and try again." 
    folder_talentCustom_getTalentFolderById_folderNotExist:
      titleChina: "未找到文件夹。"
      titleEng: "No folder found."
    folder_talentCustom_getCreatorName_responseNoOk:
      titleChina: "调用用户服务getUserById方法失败。"
      titleEng: "Invoke user service getUserById method failure."  
    folder_talentCustom_getValidateTalentFolderBeforeUpdate_nameExist:
      titleChina: "文件夹名称已存在。"
      titleEng: "Folder name already exists." 
    folder_talentCustom_checkRWPermissionOnSharedFolders_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."  
    folder_talentCustom_validateTalents_invalidTalents:
      titleChina: "无效的Talents，Talents不在当前租户之下！"
      titleEng: "Invalid Talents, Talents are not under current Tenant!"  
    folder_talentCustom_checkAllTalentsUnderCurrentFolder_notInFolder:
      titleChina: "Talents不全在这个文件夹中"
      titleEng: "Talents are not all in this folder"
    folder_talentSearch_createTalentSearchFolder_alreadyExist:
      titleChina: "文件夹名称已存在。"
      titleEng: "Folder name already exists."
    folder_talentSearch_validateCustomFolderAsSearchFolderParam_noPermission:
      titleChina: "仅有查看权限。若需其他操作，请请求管理员授予您相应的权限。"
      titleEng: "View permission only. For other Please request the admin to grant you the permission for other actions."
    folder_talentSearch_getSearchFolderById_notExist:
      titleChina: "未找到搜索文件夹。"
      titleEng: "No search folder found." 
    folder_talentSearch_folderIdNull:
      titleChina: "需要查询的对象必须有id。"
      titleEng: "The object to query must have id."   
    hotlist_createHotList_alreadyId:
      titleChina: "新的 hotList 不能已经有一个 ID"
      titleEng: "A new hotList can not already have an ID"   
    hotlist_validateTitle_titleNull:
      titleChina: "新的热门列表标题不能为空"
      titleEng: "A new hotList title can not empty"
    hotlist_validateTitle_alreadyExist:
      titleChina: "热门列表标题已存在！"
      titleEng: "A hotList title already exist!"  
    hotlist_checkHotListIfExists_notExist:
      titleChina: "hotList 不存在"
      titleEng: "HotList dose not exists" 
    hotlist_cappendHotListTalentsByIds_talentIdsNull:
      titleChina: "输入列表为 null 或为空"
      titleEng: "The input list is null or empty." 
    hotlist_validateHotList_paramNull:
      titleChina: "HotList id 不能为空"
      titleEng: "HotList id couldn't be null"
    hotlist_createHotListUser_alreadyId:
      titleChina: "新的 HotListUser 不能已经有一个 ID"
      titleEng: "A new HotListUser can not already have an ID"
    hotlist_createHotListUser_userNotExist:
      titleChina: "该用户不存在！"
      titleEng: "the user doesn't exist!"
    hotlist_createHotListUser_userExist:
      titleChina: "该热门列表中已存在该用户"
      titleEng: "User already exist in this hotlist"  
    hotlist_replaceHotListUsers_publicStatus:
      titleChina: "热门榜单 #1 已向公众开放"
      titleEng: "Hotlist #1 is open to public" 
    linked_verifyTenant_noPermission:
      titleChina: "暂无权限查看该跟进备注。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    linked_findOne_notfound:
      titleChina: "未找到项目。"
      titleEng: "No project found."
    linked_verifyLinkedinProjectTalent_linkedinProjectIdNull:
      titleChina: "需要 LinkedIn 项目 ID。"
      titleEng: "LinkedIn project id is required."
    linked_replace_linkedinTalentContactsNull:
      titleChina: "请输入至少一个候选人联系方式。"
      titleEng: "Please enter at least one contact information of the candidates."
    linked_replace_linkedinTalentNull:
      titleChina: "该链接无效或已过期。"
      titleEng: "The link is invalid or expired."
    linked_update_linkedinTalentNotExist:
      titleChina: "LinkedinTalent 不存在，ID 为 #1"
      titleEng: "LinkedinTalent not exist by id #1"
    record_checkInList_conditionLimit:
      titleChina: "查询 sql 中的条件列表 > 1000 大于 1"
      titleEng: "query sql in condition list > 1000 more than 1"  
    record_syncProTalentTrackingNoteToApnTalentNote_talentNull:
      titleChina: "未找到候选人。"
      titleEng: "No candidate found." 
    record_findAllTalentTrackingRecords_contactNull:
      titleChina: "联系人列表不能为空。"
      titleEng: "The contact list cannot be empty."  
    record_sendRichEmail_userNull:
      titleChina: "没有来自属性并且找不到当前用户！"
      titleEng: "No from attribute and cannot find current user!" 
    talent_checkTagExists_tagIdNull:
      titleChina: "TagId #1 不存在"
      titleEng: "TagId #1 is not exists"
    talent_validate_tagNameNull:
      titleChina: "#1 标签已存在。"
      titleEng: "Tag #1 exists."
    talent_createSpecialTag_idNull:
      titleChina: "新的特殊标签不能有 ID"
      titleEng: "A new special tag can not have an ID"
    talent_createSpecialTag_specialTagNull:
      titleChina: "名称为 #1 的特殊标签已存在！"
      titleEng: "A special tag with name #1 already exist!"
    talent_createSpecialTag_noPermission:
      titleChina: "暂无权限添加特殊标签。请联系管理员开放权限。"
      titleEng: "No permission to add a special tag. Please ask the admin to grant you the permission."
    talent_create_talentContactNull:
      titleChina: "请填写候选人档案。"
      titleEng: "Please fill the candidate profile."
    talent_create_talentContactIdNull:
      titleChina: "要创建的对象不能已经有一个 ID。"
      titleEng: "The object to create cannot already have an id."
    talent_create_talentNull:
      titleChina: "未找到候选人。"
      titleEng: "No candidate found."
    talent_common_noPermission:
      titleChina: "暂无权限查看该跟进备注。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    talent_update_talentContactNotExist:
      titleChina: "暂无权限查看该跟进备注。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    talentOwnership_create_userIdNull:
      titleChina: "需要人才共享用户。"
      titleEng: "Talent share user is required."
    talentOwnership_replace_userIdNull:
      titleChina: "需要用户 ID。"
      titleEng: "User ID is required."
    talentResume_createTalentResume_uuidNull:
      titleChina: "TalentResumeDTO uuid 不能为空"
      titleEng: "TalentResumeDTO uuid Can't be null"
    talentResume_findOne_resumeNull:
      titleChina: "未找到简历。"
      titleEng: "No resume found."
    talent_getResumeParseResponse_parserResumeDataNull:
      titleChina: "解析简历过程出现错误。请再次上传简历。"
      titleEng: "Something went wrong while parsing the resume. Please upload the resume again."
    talent_create_duplicateTalentData:
      titleChina: "候选人已存在。"
      titleEng: "Candidate already exists."
    talent_create_notFoundError:
      titleChina: "请联系我们的客户支持寻求帮助。"
      titleEng: "Please reach out to our customer support for assistance."
    talent_setTalentRelateData_contactsNull:
      titleChina: "请输入至少一个候选人联系方式。"
      titleEng: "Please enter at least one contact information of the candidates."
    talent_update_cannotOwnerAndShare:
      titleChina: "一个人不能同时是所有者和分享者。"
      titleEng: "A person cannot be both an owner and a share."
    talent_checkSharesPermission_noPermission:
      titleChina: "仅有查看权限。请请求所有者允许您执行其他操作。"
      titleEng: "View permission only. Please request the owner to grant you the permission for other actions."
    talent_getTalentIsClientContact_listResponseEntityNull:
      titleChina: "调用第三个接口错误。"
      titleEng: "invoke third interface error."
    talent_getTalentsByContacts_contactInfoNull:
      titleChina: "请输入至少一个候选人联系方式。"
      titleEng: "Please enter at least one contact information of the candidates."  
    talent_searchTalentEmailContacts_paramListNull:
      titleChina: "参数列表不能为空。"
      titleEng: "The parameter list cannot be empty." 
    talent_searchTalentEmailContacts_talentListNull:
      titleChina: "人才列表不能为空。"
      titleEng: "Talent list cannot be empty." 
    talent_searchTalentsByContactAndSimilarity_contactNull:
      titleChina: "联系人不为空。"
      titleEng: "contact is not null."
    talent_getRecommendedReason_jobNull:
      titleChina: "未找到岗位。"
      titleEng: "No job found."
    talent_getRecommendedReason_templateNull:
      titleChina: "未找到模版"
      titleEng: "No template found."
    talent_getRecommendedReason_talentNull:
      titleChina: "未找到候选人"
      titleEng: "No candidate found."
    talent_getRecommendationReport_userNull:
      titleChina: "未找到用户。"
      titleEng: "No user found."
    talent_getRecommendationReport_error:
      titleChina: "系统出现错误。请联系客服获取进一步的帮助。"
      titleEng: "Something went wrong on our side. Please reach out to our customer support for assistance."  
    talent_requestRecommendedReason_error:
      titleChina: "请求推荐原因错误"
      titleEng: "Request recommended reason error"
    talent_getRequestRecommendedReasonParam_paramNullValue:
      titleChina: "获取推荐原因请求参数存在空值"
      titleEng: "The get recommended reason request parameter exist null value"   
    talent_findTalentProgressByTaskIds_paramNull:
      titleChina: "参数是空"
      titleEng: "param is null"
    talent_common_internalError:
      titleChina: "内部服务器错误。"
      titleEng: "Internal Server Error"  
    talent_updateTaskStatus_taskFinish:
      titleChina: "任务完成"
      titleEng: "task is finished"
    talent_downloadCreateTalentResultByExcel_cloudFileObjectMetadataNull:
      titleChina: "未找到任务。"
      titleEng: "No task found."
    talent_downloadCreateTalentResultByExcel_notFinish:
      titleChina: "任务 id 未完成"
      titleEng: "The task id does not finished"  
    talent_downloadCreateTalentResultByExcel_dataExpired:
      titleChina: "导入文件已过期且无法下载。"
      titleEng: "The imported file has been expired and cannot be downloaded."
    talent_searchTalentFromES_interfaceError:
      titleChina: "远程接口错误"
      titleEng: "remote interface error"
    talent_checkFoldersPermission_folderNotExist:
      titleChina: "未找到文件夹"
      titleEng: "No folder found."  
    talent_searchTalentSourceFromES_inputEmpty:
      titleChina: "输入id为空！"
      titleEng: "input id is empty!"
    linked_saveLinkedinProject_idEmpty:
      titleChina: "该 ID 应为空。"
      titleEng: "The id should be empty."  
    linked_create_idExist:
      titleChina: "新的 linkedinProjectTalent 不能已有 ID"
      titleEng: "A new linkedinProjectTalent cannot already have an ID" 
    rater_recommendJobsForCommonTalent_esIdExist:
      titleChina: "搜索 ID 不能为空。"
      titleEng: "Search id can not be null."   
    talent_createAndUpdateCreditTransaction_talentIdExist:
      titleChina: "新人才不能已经拥有 ID"
      titleEng: "A new talent cannot already have an ID" 
    talent_no_permission_visit_client_contact:
      titleChina: "您暂无权限查看此候选人数据"
      titleEng: "Sorry, you don't have access this candidate's details." 
    talent_dodax_id_repeat:
      titleChina: "当前录入的ID与其他\"doda X\"候选人ID重复，请核实后重新输入正确的候选人ID。"
      titleEng: "The ID you entered is duplicated with another candidate ID in \"doda X\". Please verify and enter the correct candidate ID." 
    confidential_rule_duplicate:
      titleChina: "不能创建重复规则"
      titleEng: "Duplicate rule cannot be created."
    talent_not_match_confidential_rule:
      titleChina: "候选人不满足保密规则，保密失败"
      titleEng: "Candidate does not meet confidentiality rules, confidentiality failed."
    talent_auto_declassify:
      titleChina: "候选人不再满足保密规则，已自动解除保密状态"
      titleEng: "Candidate no longer meets confidentiality rules, confidentiality status has been automatically lifted."

  