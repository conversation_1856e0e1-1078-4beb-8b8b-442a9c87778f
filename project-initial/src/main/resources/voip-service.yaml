server:
  port: 9017
  servlet:
    context-path: /voip

application:
  amazonConnectService:
    accessKey:
    secretKey:
    region: us-west-2
    instanceId: 7c021f17-c66e-46e7-8e4d-58088c6406df
    maxConcurrentCall: 3
    voicemail:
      bucket: amazon-connect-bc4f59adb627
      phoneNumber: ******-974-0707
      folder: voicemail/

  larkService:
    authenticationEndPoint: https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal
    streamRecognitionEndpoint: https://open.larksuite.com/open-apis/speech_to_text/v1/speech/stream_recognize
    sendMessageEndpoint: https://open.larksuite.com/open-apis/im/v1/messages
    streamRecognitionService: 
      appId:
      appSecret:
    messageNotificationService:
      appId:
      appSecret:

  openAIService: 
    apiKey:
    chatGPTEndpoint: https://api.openai.com/v1/chat/completions
    chatGPTModel: gpt-4o-mini

  aliCloud:
    appKey:
    appEngKey:
    accessKey:
    secretKey:
    fileTranscriptionEndPoint: https://nls-gateway-cn-shanghai.aliyuncs.com/stream/v1/FlashRecognizer

apn:
  hostUrl: https://apn-v3-staging-singapore.hitalentech.com

sse:
  skip: false
  push:
    url: http://apiv3-singapore-staging.hitalentech.com/api/sse-voip/push
  access_token_key: VOIP-XXL-JOB-TOKEN
  access_token_value: