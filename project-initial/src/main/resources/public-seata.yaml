seata:
  enabled: false
  tx-service-group: apn_tx_group
  service:
    vgroup-mapping:
      apn_tx_group: default
  config:
    type: nacos
    nacos:
      server-addr: ${NACOS-SERVICE-ADDR:localhost:8848}
      namespace: ${NAMESPACE:dev}
      group: DEFAULT_GROUP
      username: nacos
      password: nacos
      data-id: apn_tx_group.properties
  registry:
    type: nacos
    nacos:
      application: seata-server
      server-addr: ${NACOS-SERVICE-ADDR:localhost:8848}
      namespace: ${NAMESPACE:dev}
      group: DEFAULT_GROUP
      username: nacos
      password: nacos