server:
  port: 9016
  servlet:
    context-path: /common

application:
  email:
    url: https://api-emaily-dev.hitalentech.com/emailcampaign
    system:
      username: sunny
      password:
  team-unOnboard-sendTime: 16
  team-unsubmit-talent-send-day: 2

  locationService:
    aws:
      accessKey:
      secretKey: oWILTc7HNnZpiYZFDuGtxzsPeUfLqXvz1aK/my7G
      region: us-west-2
      locationIndex: explore.place

  sqsService:
    queueName: want-parse-staging
    region: us-west-2
  filesize:
    resume:
      # 1KB - 30MB
      min: 1024
      max: 31457280
    jd:
      # 1KB - 1MB
      min: 1024
      max: 1048576
  char:
    jd:
      # max plain text jd characters
      max: 262144

  storeService:
    resume:
      bucket: resume-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
    resumeDisplays:
      bucket: resume-displays-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
    portrait:
      bucket: portrait-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 2097152
    jd:
      bucket: jd-staging-us-west-2
      minSizeInBytes: 1024
      maxSizeInBytes: 1048576
    cv:
      bucket: apn-cv-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
    logo:
      bucket: apn-doc-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 2097152
      folder: logo/
    emailAttachment:
      bucket: apn-email-info-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 10485760
    onBoarding:
      bucket: apn-onboarding-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 52428800
    groupinvoice:
      bucket: info-docs-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: contractor-invoice/
    confirmationLetter:
      bucket: info-docs-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: confirmation-letter/ 
    invoiceAttachment:
      bucket: info-docs-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: invoice-attachment/   
    fteChinaInvoice:
      bucket: info-docs-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: fte-china-invoice/ 
    receipt:
      bucket: info-docs-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 20480000
      folder: receipt/
    contract:
      bucket: apn-contract-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: origin/
    contractDisplays:
      bucket: apn-contract-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: display/
    doc:
      bucket: apn-doc-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 2097152
    emailInvoiceAttachment:
      bucket: apn-email-info-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: invoice-attachment/
    addressList:
      bucket: info-docs-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: address-list/
    oldContract:
      bucket: apn-contract-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
    apn-doc-staging:
      bucket: info-docs-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: shared-job-pages/
    jobSharing:
      bucket: apn-doc-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 104857600
      folder: shared-job-pages/
      s3url: https://apn-doc-staging.s3.us-west-1.amazonaws.com/shared-job-pages/
    noteImage:
      bucket: info-docs-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: note-image/
      signatureDuration: 60
    resumeWatermark:
      bucket: info-docs-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
    jobSharingBackgroundImage:
      bucket: apn-doc-staging
      minSizeInBytes: 1024
      maxSizeInBytes: 31457280
      folder: shared-job-background-image/
xxl:
  job:
    url: https://ecam-api-staging.hitalentech.com/xxl-job-admin
    groupid: 2
    access_token_key: APN-XXL-JOB-TOKEN
    access_token_value:
    skip: false
    job-process-not-submitted.reminder:
      day: 14
    job-interview-not-submitted.reminder:
      day: 14


sse:
  skip: false
  push:
    url: https://apiv3-singapore-staging.hitalentech.com/api/sse/push
  access_token_key: APN-XXL-JOB-TOKEN
  access_token_value:

lark:
  skip: false
  appId:
  appSecret:

api-properties:
  commonService:
    calender_calendarEventExists:
      titleChina: "事件已存在。"
      titleEng: "Event already exists."  
    calender_deleteCalendarEvent_noPermission:
      titleChina: "暂无权限查看该跟进备注。请向所有者申请查看权限。"
      titleEng: "No permission to access. Please request the owner to grant you the permission."
    email_common_invalidParameter:
      titleChina: "无效的参数！"
      titleEng: "Invalid parameter!"     
    email_audience_serviceError:
      titleChina: "调用Emaily.AI受众服务错误！"
      titleEng: "[EmailService AudienceServiceImpl] calling Emaily.AI audience service error!"
    email_company_sendHtmlMailNoUser:
      titleChina: "没有来自属性并且找不到当前用户！"
      titleEng: "No from attribute and cannot find current user!"
    email_company_sendHtmlMailNoContent:
      titleChina: "没有html内容！"
      titleEng: "No htmlContent!"
    email_company_sendHtmlMailNoSubject:
      titleChina: "请填写主题。"
      titleEng: "Please enter a subject."  
    email_company_sendHtmlMailNoRecipient:
      titleChina: "请填写收件人的邮箱地址。"
      titleEng: "Please enter the recipient's email addresses."
    email_company_sendHtmlMailGmailAccountExpired:
      titleChina: "请在帐户设置中设置Gmail alias后重试。"
      titleEng: "Please set up the gmail alias in account settings, then try again."
    email_company_sendEmailBlastNotFoundDefaultUser:
      titleChina: "找不到默认用户！"
      titleEng: "Cannot find default user!"  
    email_userEmail_needCheckGmailAliasBinding:
      titleChina: "请在帐户设置中设置Gmail alias后重试。"
      titleEng: "Please set up the gmail alias in account settings, then try again."
    email_userEmail_requestToBindingGmailAliasEmailNull:
      titleChina: "您不能绑定空的邮箱地址！"
      titleEng: "You cannot bind empty email address!"
    email_userEmail_requestToBindingGmailAliasError:
      titleChina: "系统出现了一些问题。请联系客服寻求帮助。"
      titleEng: "Something went wrong on our side. Please reach out to our customer support for assistance."
    email_userEmail_requestToBindingGmailAliasIoError:
      titleChina: "请求绑定 gmail 别名时出现 IOException！"
      titleEng: "IOException when request to bind gmail alias!"      
    email_userEmail_confirmBindingGmailAliasBindListNull:
      titleChina: "请先申请绑定gmail别名，然后刷新确认！"
      titleEng: "Please request to bind gmail-alias first, then refresh to confirm!"  
    email_userEmail_confirmBindingGmailAliasIoError:
      titleChina: "请求绑定 gmail 别名时出现 IOException！"
      titleEng: "IOException when confirm to bind gmail alias!" 
    email_userEmail_findCurrentUserEmailUserNull:
      titleChina: "找不到当前用户！"
      titleEng: "cannot find current user!"  
    email_userEmail_downloadResumeParamNull:
      titleChina: "没有 TalentResumeRelation！"
      titleEng: "No TalentResumeRelation!"
    email_userEmail_checkJdFilenameNotNull:
      titleChina: "文件名或内容类型非空！"
      titleEng: "Non-empty filename or content-type!"  
    email_userEmail_checkJdCannotHtml:
      titleChina: "jd文字不能是html格式！"
      titleEng: "jd text cannot be html format!"
    email_userEmail_checkJdTextLong:
      titleChina: "jd文字太长"
      titleEng: "jd text too long!"    
    email_userEmail_checkJdFilenameNull:
      titleChina: "文件名或内容类型为空！"
      titleEng: "Empty filename or content-type!"  
    store_s3Upload_fileNull:
      titleChina: "文件为空"
      titleEng: "file is empty" 
    store_s3Upload_exception:
      titleChina: "内部服务器错误。"
      titleEng: "Internal Server Error" 
  