package com.altomni.apn.admin.utils;

import com.alibaba.fastjson.JSONObject;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;

public class NotificationUtils {
    private static final Logger log = LoggerFactory.getLogger(NotificationUtils.class);
    // HMAC encryption algorithm.
    private static final String HMAC_SHA_256 = "HmacSHA256";
    private static final okhttp3.MediaType JSON_TYPE = okhttp3.MediaType.parse("application/json; charset=utf-8");

    public static void sendWithOkHttp(String webhookUrl, String bodyJson){
        try {
            // send with okhttp3
            okhttp3.RequestBody body = okhttp3.RequestBody.create(J<PERSON><PERSON>_TYPE, bodyJson);
            Request request = new Request.Builder().url(webhookUrl).post(body).build();
            new OkHttpClient().newCall(request).execute().close();
        }catch (IOException e){
            log.error("[APN: InstanceStatusChangedEvent] send msg to lark: {}", e.getMessage());
        }
    }

    private static String buildBasicLarkContent(String webhookKey, String msg){
        Long timestamp = Instant.now().getEpochSecond();
        String key = timestamp + "\n" + webhookKey;
        byte[] bytes = HmacUtils.getInitializedMac(HMAC_SHA_256, key.getBytes()).doFinal();
        String signature = Base64.getEncoder().encodeToString(bytes);

        JSONObject json = new JSONObject();
        json.fluentPut("timestamp", timestamp);
        json.fluentPut("sign", signature);
        json.fluentPut("msg_type", "text");
        json.fluentPut("content", new JSONObject(new HashMap<String, Object>(){{put("text", msg);}}));
        return json.toJSONString();
    }

    public static void sendAlertToLark(String webhookKey, String webhookUrl, String msg) {
        String json = buildBasicLarkContent(webhookKey, msg);
        sendWithOkHttp(webhookUrl, json);
    }
}

