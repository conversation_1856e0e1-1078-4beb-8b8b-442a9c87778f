package com.altomni.apn.admin.config;

import com.altomni.apn.admin.utils.NotificationUtils;
import de.codecentric.boot.admin.server.domain.entities.Instance;
import de.codecentric.boot.admin.server.domain.entities.InstanceRepository;
import de.codecentric.boot.admin.server.domain.events.InstanceEvent;
import de.codecentric.boot.admin.server.domain.events.InstanceStatusChangedEvent;
import de.codecentric.boot.admin.server.notify.AbstractEventNotifier;
import de.codecentric.boot.admin.server.notify.LoggingNotifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Set;

@Component
@RefreshScope
public class CustomNotifier extends AbstractEventNotifier {

  private static final Logger LOGGER = LoggerFactory.getLogger(LoggingNotifier.class);

  private static final String SEATA_SERVICE = "seata-server";

  private static final Set<String> DOWN = Set.of("DOWN", "OFFLINE");

  @Value("${application.notification.lark.webhookKey}")
  private String LARK_WEBHOOK_KEY;

  @Value("${application.notification.lark.webhookUrl}")
  private String LARK_WEBHOOK_URL;

  @Value("${application.notification.lark.enabled}")
  private boolean enabled;

  public CustomNotifier(InstanceRepository repository) {
    super(repository);
  }

  @Override
  protected Mono<Void> doNotify(InstanceEvent event, Instance instance) {
    return Mono.fromRunnable(() -> {
      if (event instanceof InstanceStatusChangedEvent) {
        String status = ((InstanceStatusChangedEvent) event).getStatusInfo().getStatus();
        String service = instance.getRegistration().getName();
        if (DOWN.contains(status)){
          LOGGER.warn("Instance {} is {}", service, status);
        }else {
          LOGGER.info("Instance {} is {}", service, status);
        }
        if (!enabled){
          return;
        }
        if (service.equals(SEATA_SERVICE)){
          return;
        }
        NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, String.format("%s is %s", service, status));
      }else {
        LOGGER.info("Instance {} {}", instance.getRegistration().getName(), event.getType());
      }
    });
  }

}