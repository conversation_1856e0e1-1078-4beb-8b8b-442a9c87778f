<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <property name="now" value="now()" dbms="h2"/>
    <property name="now" value="now()" dbms="mysql, mariadb"/>
    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb"/>
    <property name="clobType" value="clob" dbms="h2"/>
    <property name="clobType" value="clob" dbms="mysql, oracle, mssql, mariadb, postgresql"/>
    <property name="uuidType" value="varchar(36)" dbms="h2, mysql, mariadb"/>
    <property name="datetimeType" value="datetime(6)" dbms="mysql, mariadb"/>
    <property name="datetimeType" value="datetime" dbms="oracle, mssql, postgresql, h2"/>

    <include file="config/liquibase/changelog/init/1666262757664_added_entity_Tenant.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757665_added_entity_TenantAddress.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757665_added_entity_TenantAdminStatus.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757665_added_entity_AdminManagementUser.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757775_added_entity_AdminManagementUserRole.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1669262757661_added_entity_AdminManagementRole.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/1679262757661_added_entity_ProductErrorInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20241205152000_added_entity_TenantWatermarkConfig.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/20241205153000_added_entity_PlatformLaunchAnnouncement.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20241205153010_added_entity_PlatformLaunchAnnouncementUserRelation.xml" relativeToChangelogFile="false"/>
</databaseChangeLog>
