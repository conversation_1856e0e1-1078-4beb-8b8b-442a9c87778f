<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1679262757661-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="product_error_info"/>
            </not>
        </preConditions>
        <createTable tableName="product_error_info">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="email" type="VARCHAR(255)"/>
            <column name="format_phone" type="VARCHAR(255)"/>
            <column name="source" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="error_desc" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="error_process_note" type="TEXT"/>
            <column name="operator" type="BIGINT"/>
            <column name="error_type" type="TINYINT(3)"/>
            <column name="status" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
