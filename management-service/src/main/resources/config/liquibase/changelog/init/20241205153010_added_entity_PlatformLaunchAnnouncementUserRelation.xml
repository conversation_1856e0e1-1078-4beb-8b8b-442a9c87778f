<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20241205153010-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="platform_launch_announcement_user_relation"/>
            </not>
        </preConditions>
        <createTable tableName="platform_launch_announcement_user_relation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="platform_launch_announcement_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="email" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_platform_launch_announcement_id_email" tableName="platform_launch_announcement_user_relation" unique="true">
            <column name="platform_launch_announcement_id"/>
            <column name="email"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
