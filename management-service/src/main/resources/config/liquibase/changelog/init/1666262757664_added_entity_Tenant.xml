<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666*********-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="tenant"/>
            </not>
        </preConditions>
        <createTable tableName="tenant">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="industry" type="TINYINT(3)"/>
            <column name="website" type="VARCHAR(255)"/>
            <column name="organization_name" type="VARCHAR(100)"/>
            <column name="staff_size" type="TINYINT(3)"/>
            <column name="founded_date" type="timestamp"/>
            <column name="address_id" type="BIGINT"/>
            <column name="description" type="VARCHAR(500)"/>
            <column name="logo" type="VARCHAR(200)"/>
            <column defaultValueNumeric="1" name="status" type="TINYINT(3)"/>
            <column name="bulk_credit" remarks="bulk credit" type="INT"/>
            <column name="monthly_credit" remarks="monthly credit" type="INT"/>
            <column name="update_monthly_credit" remarks="update_monthly_credit" type="INT"/>
            <column name="tenant_email" remarks="tenant email" type="VARCHAR(100)"/>
            <column name="tenant_phone" remarks="tenant phone" type="VARCHAR(100)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="expire_date" type="timestamp"/>
            <column name="contact_name" type="VARCHAR(255)"/>
            <column name="contact_first_name" type="VARCHAR(255)"/>
            <column name="contact_last_name" type="VARCHAR(255)"/>
            <column name="user_max_limit" type="INT"/>
            <column name="note" type="TEXT"/>
            <column name="login_link" type="VARCHAR(255)"/>
            <column name="user_type" type="TINYINT(3)"/>
            <column name="reset_day_of_month" type="TINYINT(3)"/>
            <column name="owner_data_restriction" type="TINYINT(3)" defaultValue="1" />
            <column name="extended_info" type="json" />
        </createTable>
    </changeSet>
</databaseChangeLog>
