<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264699361-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="tenant_address"/>
            </not>
        </preConditions>
        <createTable tableName="tenant_address">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="address" type="VARCHAR(255)"/>
            <column name="address_2" type="VARCHAR(255)"/>
            <column name="city_id" type="BIGINT"/>
            <column name="zipcode" type="VARCHAR(255)"/>
        </createTable>

        <createIndex indexName="idx_fk_city_id" tableName="tenant_address">
            <column name="city_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
