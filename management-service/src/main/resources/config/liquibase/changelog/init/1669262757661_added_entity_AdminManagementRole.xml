<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1669262757661-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="admin_management_role"/>
            </not>
        </preConditions>
        <createTable tableName="admin_management_role">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(50)"/>
            <column name="data_scope" type="TINYINT(3)"/>
            <column name="is_internal" type="TINYINT(3)"/>
            <column defaultValueNumeric="0" name="status" type="INT"/>
            <column name="description" type="VARCHAR(100)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
