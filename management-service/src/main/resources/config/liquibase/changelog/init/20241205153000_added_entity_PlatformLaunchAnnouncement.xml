<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20241205153000-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="platform_launch_announcement"/>
            </not>
        </preConditions>
        <createTable tableName="platform_launch_announcement">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="platform" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="launch_start_time" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="launch_end_time" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="reminder_time" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="content_cn" type="TEXT"/>
            <column name="content_en" type="TEXT"/>
            <column name="upgrade_log_link_cn" type="TEXT"/>
            <column name="upgrade_log_link_en" type="TEXT"/>
            <column name="upgrade_log_title_cn" type="TEXT"/>
            <column name="upgrade_log_title_en" type="TEXT"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_platform_status_reminder_launch_end_time" tableName="platform_launch_announcement">
            <column name="platform"/>
            <column name="status"/>
            <column name="reminder_time"/>
            <column name="launch_end_time"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
