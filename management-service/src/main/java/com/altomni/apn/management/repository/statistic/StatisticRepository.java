package com.altomni.apn.management.repository.statistic;

import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.management.vo.statistic.StatisticVo;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.List;

@Repository
public class StatisticRepository {

    @Resource
    private EntityManager entityManager;

    public List<StatisticVo> jobStatisticsByType(TenantUserTypeEnum typeEnum) {
        String statisticSelectSql = "SELECT t.NAME as name,count( j.id ) as count,t.id as id FROM tenant t LEFT JOIN job j ON j.tenant_id = t.id WHERE t.user_type = " + typeEnum.toDbValue() + " GROUP BY t.id ORDER BY t.created_date DESC";
        return searchData(statisticSelectSql, StatisticVo.class);
    }

    public List<StatisticVo> talentStatisticsByType(TenantUserTypeEnum typeEnum) {
        String statisticSelectSql = "SELECT t.NAME as name,count( j.id ) as count,t.id as id FROM tenant t LEFT JOIN talent j ON j.tenant_id = t.id WHERE t.user_type = " + typeEnum.toDbValue() + " GROUP BY t.id ORDER BY t.created_date DESC";
        return searchData(statisticSelectSql, StatisticVo.class);
    }

    public List<StatisticVo> companyClientStatisticsByType(TenantUserTypeEnum typeEnum) {
        String statisticSelectSql = "SELECT t.NAME as name,count( c.id ) as count,t.id as id FROM tenant t LEFT JOIN company c ON c.tenant_id = t.id and EXISTS (SELECT 1 FROM account_business csl WHERE csl.company_id = c.id) WHERE t.user_type = " + typeEnum.toDbValue() + " GROUP BY t.id ORDER BY t.created_date DESC;";
        return searchData(statisticSelectSql, StatisticVo.class);
    }

    private <T> List<T> searchData(String query, Class<T> clazz) {
        Query dataQ = entityManager.createNativeQuery(query, clazz);
        return dataQ.getResultList();
    }
}
