package com.altomni.apn.management.service.statistic;

import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.management.vo.statistic.StatisticVo;

import java.util.List;

public interface StatisticService {

    List<StatisticVo> jobStatisticByType(TenantUserTypeEnum typeEnum);

    List<StatisticVo> talentStatisticByType(TenantUserTypeEnum typeEnum);

    List<StatisticVo> companyClientStatisticByType(TenantUserTypeEnum typeEnum);

}
