package com.altomni.apn.management.domain.announcement;

import cn.hutool.http.HtmlUtil;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.management.domain.enumeration.*;
import com.altomni.apn.management.dto.announcement.PlatformLaunchAnnouncementDTO;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementDetailVO;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementVO;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@Entity
@Data
@Table(name = "platform_launch_announcement")
public class PlatformLaunchAnnouncement extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "platform")
    @Convert(converter = LaunchPlatformConverter.class)
    private LaunchPlatform platform;

    @Column(name = "launch_start_time")
    private Instant launchStartTime;

    @Column(name = "launch_end_time")
    private Instant launchEndTime;

    @Column(name = "reminder_time")
    private Instant reminderTime;

    @Column(name = "content_cn")
    private String contentCn;

    @Column(name = "content_en")
    private String contentEn;

    @Column(name = "upgrade_log_link_cn")
    private String upgradeLogLinkCn;

    @Column(name = "upgrade_log_link_en")
    private String upgradeLogLinkEn;

    @Column(name = "upgrade_log_title_cn")
    private String upgradeLogTitleCn;

    @Column(name = "upgrade_log_title_en")
    private String upgradeLogTitleEn;

    @Column(name = "status")
    @Convert(converter = AnnouncementStatusConverter.class)
    private AnnouncementStatus status;

    public static PlatformLaunchAnnouncement fromPlatformLaunchAnnouncementDTO(PlatformLaunchAnnouncementDTO platformLaunchAnnouncementDTO) {
        PlatformLaunchAnnouncement platformLaunchAnnouncement = new PlatformLaunchAnnouncement();
        ServiceUtils.myCopyProperties(platformLaunchAnnouncementDTO, platformLaunchAnnouncement);
        return platformLaunchAnnouncement;
    }

    public static PlatformLaunchAnnouncementVO toPlatformLaunchAnnouncementVO(PlatformLaunchAnnouncement platformLaunchAnnouncement) {
        PlatformLaunchAnnouncementVO platformLaunchAnnouncementVO = new PlatformLaunchAnnouncementVO();
        ServiceUtils.myCopyProperties(platformLaunchAnnouncement, platformLaunchAnnouncementVO);
        platformLaunchAnnouncementVO.setPlainContentCn(HtmlUtil.unescape(HtmlUtil.cleanHtmlTag(platformLaunchAnnouncement.getContentCn())));
        platformLaunchAnnouncementVO.setPlainContentEn(HtmlUtil.unescape(HtmlUtil.cleanHtmlTag(platformLaunchAnnouncement.getContentEn())));
        return platformLaunchAnnouncementVO;
    }

    public static PlatformLaunchAnnouncementDetailVO toPlatformLaunchAnnouncementDetailVO(PlatformLaunchAnnouncement platformLaunchAnnouncement, LanguageEnum language) {
        PlatformLaunchAnnouncementDetailVO platformLaunchAnnouncementDetailVO = new PlatformLaunchAnnouncementDetailVO();
        ServiceUtils.myCopyProperties(platformLaunchAnnouncement, platformLaunchAnnouncementDetailVO);
        if (language.equals(LanguageEnum.ZH)) {
            platformLaunchAnnouncementDetailVO.setContent(platformLaunchAnnouncement.getContentCn());
            platformLaunchAnnouncementDetailVO.setUpgradeLogLink(platformLaunchAnnouncement.getUpgradeLogLinkCn());
            platformLaunchAnnouncementDetailVO.setUpgradeLogTitle(platformLaunchAnnouncement.getUpgradeLogTitleCn());
        } else {
            platformLaunchAnnouncementDetailVO.setContent(platformLaunchAnnouncement.getContentEn());
            platformLaunchAnnouncementDetailVO.setUpgradeLogLink(platformLaunchAnnouncement.getUpgradeLogLinkEn());
            platformLaunchAnnouncementDetailVO.setUpgradeLogTitle(platformLaunchAnnouncement.getUpgradeLogTitleEn());
        }
        return platformLaunchAnnouncementDetailVO;
    }

}
