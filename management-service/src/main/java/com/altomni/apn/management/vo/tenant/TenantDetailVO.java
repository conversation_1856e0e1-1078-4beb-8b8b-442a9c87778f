package com.altomni.apn.management.vo.tenant;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.dto.management.TenantPhoneDTO;
import com.altomni.apn.common.utils.PhoneNumberUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel(description = "Vo of management service tenant")
public class TenantDetailVO extends AbstractAuditingEntity implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "tenant id")
    private Long id;

    @ApiModelProperty(value = "tenant name")
    private String name;

    @ApiModelProperty(value = "tenant expireDate")
    private Instant expireDate;

    @ApiModelProperty(value = "monthly credit used by tenant")
    private Integer usedMonthlyCredit;

    @ApiModelProperty(value = "tenant monthly credit")
    private Integer monthlyCredit;

    @ApiModelProperty(value = "tenant update monthly credit")
    private Integer updateMonthlyCredit;

    @ApiModelProperty(value = "bulk credit used by tenant")
    private Integer usedBulkCredit;

    @ApiModelProperty(value = "tenant bulk credit")
    private Integer bulkCredit;

    @ApiModelProperty(value = "tenant contact name")
    private String contactName;

    @ApiModelProperty(value = "tenant contact first name")
    private String contactFirstName;

    @ApiModelProperty(value = "tenant contact last name")
    private String contactLastName;

    @ApiModelProperty(value = "tenant telephone partition")
    private Integer countryCode;

    @ApiModelProperty(value = "tenant phone")
    private String tenantPhone;

    @ApiModelProperty(value = "tenant phone number")
    private Long tenantPhoneNum;

    @ApiModelProperty(value = "tenant email")
    private String tenantEmail;

    @ApiModelProperty(value = "maximum number of users")
    private Integer userMaxLimit;

    @ApiModelProperty(value = "the current number of users of tenant")
    private Integer userAmount;

    @ApiModelProperty(value = "login url of tenant")
    private String loginLink;

    @ApiModelProperty(value = "note url of tenant")
    private String note;

    @ApiModelProperty(value = "status url of tenant, 0 INACTIVE, 1 ACTIVE")
    private Integer status;

    @ApiModelProperty(value = "type of tenant, EMPLOYER(0),HEADHUNTER(1)")
    private TenantUserTypeEnum userType;

    public static TenantDetailVO fromTenant(Tenant tenant) {
        TenantDetailVO tenantDetailVO = new TenantDetailVO();
        ServiceUtils.myCopyProperties(tenant, tenantDetailVO);
        if (tenant.getTenantPhone() != null) {
            TenantPhoneDTO tenantPhoneDTO = PhoneNumberUtils.parsePhoneNumber(tenant.getTenantPhone());
            tenantDetailVO.setCountryCode(tenantPhoneDTO.getCountryCode());
            tenantDetailVO.setTenantPhone(tenantPhoneDTO.getNationalPhone());
            tenantDetailVO.setTenantPhoneNum(tenantPhoneDTO.getPhone());
        }
        return tenantDetailVO;
    }
}
