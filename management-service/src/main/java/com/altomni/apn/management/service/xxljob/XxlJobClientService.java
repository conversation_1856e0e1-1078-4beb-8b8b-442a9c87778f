package com.altomni.apn.management.service.xxljob;

import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateBySendTimeForJobAdminDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Component
@FeignClient(value = "common-service")
public interface XxlJobClientService {

    @PostMapping("/common/api/v3/xxl-job")
    ResponseEntity<Integer> createXxlJob(@RequestBody XxlJobApnDTO xxlJobApnDTO);

    @PostMapping("/common/api/v3/xxl-jobs")
    ResponseEntity<Integer> createXxlJobS(@RequestBody List<XxlJobApnDTO> xxlJobApnDTOS);

    @PutMapping("/common/api/v3/xxl-job")
    ResponseEntity<Void> updateXxlJob(@RequestBody XxlJobApnDTO xxlJobApnDTO);

    @PutMapping("/common/api/v3/xxl-jobs-by-sendTime")
    ResponseEntity<Void> updateJobsBySendTime(@RequestBody List<XxlJobUpdateBySendTimeForJobAdminDTO> xxlJobList);

    @DeleteMapping("/common/api/v3/xxl-job/{xxlJobId}")
    ResponseEntity<Void> deleteXxlJobId(@PathVariable("xxlJobId") Integer xxlJobId);

    @DeleteMapping("/common/api/v3/xxl-job")
    ResponseEntity<Void> deleteXxlJobIdList(@RequestBody List<Integer> idList);

}
