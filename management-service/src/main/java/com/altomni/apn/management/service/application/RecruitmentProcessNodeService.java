package com.altomni.apn.management.service.application;

import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessNodeVO;
import com.altomni.apn.common.dto.application.recruitmentprocessnode.RecruitmentProcessNodeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Service Interface for managing RecruitmentProcess.
 */
@Component
@FeignClient(value = "application-service")
public interface RecruitmentProcessNodeService {

    /**
     * Get all the RecruitmentProcessNodes.
     *
     * @return the list of entities
     */
    @GetMapping("/application/api/v3/recruitment-process-nodes/admin/recruitmentProcessId/{recruitmentProcessId}")
    ResponseEntity<List<RecruitmentProcessNodeVO>> findAllNodesByRecruitmentProcessId(@PathVariable("recruitmentProcessId") Long recruitmentProcessId);

    @PutMapping("/application/api/v3/recruitment-process-nodes/recruitmentProcessId/{recruitmentProcessId}/nodeId/{nodeId}")
    ResponseEntity<RecruitmentProcessNodeDTO> updateNodeByRecruitmentProcessIdAndNodeId(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @PathVariable("nodeId") Long nodeId, @RequestBody RecruitmentProcessNodeDTO recruitmentProcessNode);

}
