package com.altomni.apn.management.service.authority;

import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.user.LoginVM;
import com.altomni.apn.management.dto.user.RefreshTokenDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;


@Component
@FeignClient(value = "authority-service")
public interface AuthorityService {

    @GetMapping("/authority/api/v3/logoutByUid")
    ResponseEntity<Void> logoutByUid(@RequestParam("uid") String uid);

    @PostMapping("/authority/api/v3/credential")
    ResponseEntity<CredentialDTO> findCredentialUser(@RequestBody LoginVM loginVM);

    @PostMapping("/authority/api/v3/credential/management")
    ResponseEntity<CredentialDTO> findCredential(@RequestBody LoginVM loginVm);

    @PostMapping(path = "/authority/api/v3/refresh-token", produces = {MediaType.APPLICATION_JSON_VALUE})
    ResponseEntity<CredentialDTO> refreshToken(@Valid @RequestBody RefreshTokenDTO refreshTokenDTO);
}
