package com.altomni.apn.management.vo.announcement;

import com.altomni.apn.management.domain.enumeration.AnnouncementStatus;
import com.altomni.apn.management.domain.enumeration.AnnouncementStatusConverter;
import com.altomni.apn.management.domain.enumeration.LaunchPlatform;
import com.altomni.apn.management.domain.enumeration.LaunchPlatformConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class PlatformLaunchAnnouncementVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "platform")
    @Convert(converter = LaunchPlatformConverter.class)
    private LaunchPlatform platform;

    @ApiModelProperty(value = "launchStartTime")
    private Instant launchStartTime;

    @ApiModelProperty(value = "launchEndTime")
    private Instant launchEndTime;

    @ApiModelProperty(value = "reminderTime")
    private Instant reminderTime;

    @ApiModelProperty(value = "contentCn")
    private String contentCn;

    @ApiModelProperty(value = "contentEn")
    private String contentEn;

    @ApiModelProperty(value = "upgradeLogLinkCn")
    private String upgradeLogLinkCn;

    @ApiModelProperty(value = "upgradeLogLinkEn")
    private String upgradeLogLinkEn;

    @ApiModelProperty(value = "upgradeLogTitleCn")
    private String upgradeLogTitleCn;

    @ApiModelProperty(value = "upgradeLogTitleEn")
    private String upgradeLogTitleEn;

    @ApiModelProperty(value = "status")
    @Convert(converter = AnnouncementStatusConverter.class)
    private AnnouncementStatus status;

    @ApiModelProperty(value = "plainContentCn")
    private String plainContentCn;

    @ApiModelProperty(value = "contentEn")
    private String plainContentEn;

}
