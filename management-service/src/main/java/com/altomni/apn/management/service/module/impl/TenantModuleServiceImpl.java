package com.altomni.apn.management.service.module.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.management.dto.module.TenantModuleDTO;
import com.altomni.apn.management.service.module.TenantModuleService;
import com.altomni.apn.management.service.user.UserClient;
import com.altomni.apn.management.vo.module.TenantModulePageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TenantModuleServiceImpl implements TenantModuleService {

    @Resource
    private UserClient userClient;

    @Override
    public List<TenantModulePageVO> searchTenantModuleList(Long tenantId) {
        List<TenantModulePageVO> result = userClient.searchTenantModuleList(tenantId).getBody();
        setModuleLevel(result);
        return result;
    }

    @Override
    public void updateTenantModuleList(Long tenantId, List<TenantModuleDTO> tenantModuleDTOList) {
        userClient.updateTenantModuleList(tenantId, tenantModuleDTOList);
    }

    private void setModuleLevel(List<TenantModulePageVO> tenantModulePageVOList) {
        if (CollUtil.isEmpty(tenantModulePageVOList)) {
            return;
        }

        Map<Long, List<TenantModulePageVO>> tenantModulePageVOMap = new HashMap<>(16);
        for (TenantModulePageVO tenantModulePageVO : tenantModulePageVOList) {
            if (!tenantModulePageVOMap.containsKey(tenantModulePageVO.getParentId())) {
                tenantModulePageVOMap.put(tenantModulePageVO.getParentId(), new ArrayList<>());
            }
            tenantModulePageVOMap.get(tenantModulePageVO.getParentId()).add(tenantModulePageVO);
        }

        for (TenantModulePageVO node : tenantModulePageVOList) {
            if (ObjectUtil.isEmpty(node.getParentId())) {
                node.setLevel(1);
                calculateLevelForNode(node, tenantModulePageVOMap);
            }
        }
    }

    private void calculateLevelForNode(TenantModulePageVO node, Map<Long, List<TenantModulePageVO>> parentToChildren) {
        List<TenantModulePageVO> children = parentToChildren.getOrDefault(node.getId(), new ArrayList<>());

        for (TenantModulePageVO child : children) {
            child.setLevel(node.getLevel() + 1);
            calculateLevelForNode(child, parentToChildren);
        }
    }

}
