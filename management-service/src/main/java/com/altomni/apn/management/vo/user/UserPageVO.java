package com.altomni.apn.management.vo.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "Vo of management service user")
public class UserPageVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "user id")
    private Long id;

    @ApiModelProperty(value = "user name")
    private String name;

    @ApiModelProperty(value = "user email")
    private String email;

    @ApiModelProperty(value = "user available monthly purchase currency")
    private Integer availableMonthlyCredit;

    @ApiModelProperty(value = "user available long-term purchase currency")
    private Integer availableBulkCredit;

    @ApiModelProperty(value = "user status, true: means enabled, false: means frozen")
    private Boolean activated;

}
