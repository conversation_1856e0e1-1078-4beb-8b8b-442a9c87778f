package com.altomni.apn.management.web.rest.application;

import com.altomni.apn.common.dto.application.recruitmentprocessnode.RecruitmentProcessNodeDTO;
import com.altomni.apn.management.service.application.ApplicationService;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessNodeVO;
import com.altomni.apn.common.utils.HeaderUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing RecruitmentProcessNode.
 */
@RestController
@RequestMapping("/api/v3/recruitment-process-nodes")
public class RecruitmentProcessNodeResource {

    private final Logger log = LoggerFactory.getLogger(RecruitmentProcessNodeResource.class);

    private static final String ENTITY_NAME = "RecruitmentProcessNode";

    @Resource
    private ApplicationService applicationService;

    /**
     * GET  /recruitment-process-nodes : get all the RecruitmentProcessNodes.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of RecruitmentProcessNodes in body
     */
    @GetMapping("/recruitmentProcessId/{recruitmentProcessId}")
    public ResponseEntity<List<RecruitmentProcessNodeVO>> getAllRecruitmentProcessNodes(@PathVariable Long recruitmentProcessId) {
        log.debug("REST request to get all RecruitmentProcessNodes");
        return new ResponseEntity<>(applicationService.findAllNodesByRecruitmentProcessId(recruitmentProcessId), HttpStatus.OK);
    }

    /**
     * PUT  /recruitment-process-nodes : Updates an existing recruitmentProcessNode.
     *
     * @param recruitmentProcessId the recruitment process id
     * @param nodeId node id
     * @param recruitmentProcessNode the recruitmentProcessNode to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated recruitmentProcessNode,
     * or with status 400 (Bad Request) if the recruitmentProcessNode is not valid,
     * or with status 500 (Internal Server Error) if the recruitmentProcessNode couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/recruitmentProcessId/{recruitmentProcessId}/nodeId/{nodeId}")
    public ResponseEntity<RecruitmentProcessNodeDTO> updateRecruitmentProcessNode(@PathVariable Long recruitmentProcessId, @PathVariable Long nodeId, @RequestBody RecruitmentProcessNodeDTO recruitmentProcessNode) throws URISyntaxException {
        log.debug("REST request to update RecruitmentProcessNode : {}", recruitmentProcessNode);
        RecruitmentProcessNodeDTO result = applicationService.updateNodeByRecruitmentProcessIdAndNodeId(recruitmentProcessId, nodeId, recruitmentProcessNode);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

}
