package com.altomni.apn.management.service.product;


import com.altomni.apn.management.dto.product.ErrorInfoDTO;
import com.altomni.apn.management.dto.product.ErrorInfoUpdateDTO;
import com.altomni.apn.management.vo.product.ErrorInfoVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ProductErrorInfoService {

    void createErrorInfo(ErrorInfoDTO errorInfoDTO);

    Page<ErrorInfoVO> searchErrorInfos(Pageable pageable);

    ErrorInfoVO queryErrorInfo(Long id);

    ErrorInfoVO updateErrorInfo(Long id, ErrorInfoUpdateDTO errorInfoUpdateDTO);

}
