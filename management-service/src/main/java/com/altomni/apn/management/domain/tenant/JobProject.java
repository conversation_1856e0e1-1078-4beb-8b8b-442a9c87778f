package com.altomni.apn.management.domain.tenant;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import lombok.Data;
import javax.persistence.*;
import java.io.Serializable;

/**
 * For private job use, each tenant has a record in table job_project, and its id is used as special team_id for private job.
 */
@Entity
@Data
@Table(name = "job_project")
public class JobProject extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "tenant_id")
    private Long tenantId;
}
