package com.altomni.apn.management.repository.product;

import com.altomni.apn.management.domain.product.ProductErrorInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import java.util.List;

/**
 * Spring Data JPA repository for the Tenant entity.
 */
@Repository
public interface ProductErrorInfoRepository extends JpaRepository<ProductErrorInfo,Long> {

    /**
     * 为什么这样写，因为 jsqlparser 解析 nativeQuery 的 sql 时
     * 遇到 CONVERT 函数可能会报错
     *
     * @param entityManager
     * @param pageable
     * @return
     */
    default Page<ProductErrorInfo> findAllOrderByTenantNameAsc(EntityManager entityManager, Pageable pageable) {
        String baseSql = "SELECT * FROM product_error_info p INNER JOIN tenant t ON p.tenant_id = t.id ORDER BY CASE WHEN t.name IS NULL OR t.name = '' THEN 0 ELSE 1 END DESC, CONVERT(t.name USING gbk) ASC limit :offset, :size";
        String countSql = "SELECT count(1) FROM product_error_info p INNER JOIN tenant t ON p.tenant_id = t.id ORDER BY CASE WHEN t.name IS NULL OR t.name = '' THEN 0 ELSE 1 END DESC, CONVERT(t.name USING gbk) ASC";
        List infos = entityManager.createNativeQuery(baseSql, ProductErrorInfo.class)
                .setParameter("offset", pageable.getOffset())
                .setParameter("size", pageable.getPageSize()).getResultList();
        int count = entityManager.createNativeQuery(countSql, Integer.class).getFirstResult();

        return new PageImpl<>(infos, pageable, count);
    }

    default Page<ProductErrorInfo> findAllOrderByTenantNameDesc(EntityManager entityManager, Pageable pageable) {
        String baseSql = "SELECT * FROM product_error_info p INNER JOIN tenant t ON p.tenant_id = t.id ORDER BY CASE WHEN t.name IS NULL OR t.name = '' THEN 0 ELSE 1 END DESC, CONVERT(t.name USING gbk) DESC limit :offset, :size";
        String countSql = "SELECT count(1) FROM product_error_info p INNER JOIN tenant t ON p.tenant_id = t.id ORDER BY CASE WHEN t.name IS NULL OR t.name = '' THEN 0 ELSE 1 END DESC, CONVERT(t.name USING gbk) DESC";
        List infos = entityManager.createNativeQuery(baseSql, ProductErrorInfo.class)
                .setParameter("offset", pageable.getOffset())
                .setParameter("size", pageable.getPageSize()).getResultList();
        int count = entityManager.createNativeQuery(countSql, Integer.class).getFirstResult();

        return new PageImpl<>(infos, pageable, count);
    }

}
