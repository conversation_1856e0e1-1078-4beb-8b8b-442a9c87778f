package com.altomni.apn.management.repository.announcement;

import com.altomni.apn.management.domain.announcement.PlatformLaunchAnnouncementUserRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


@Repository
public interface PlatformLaunchAnnouncementUserRelationRepository extends JpaRepository<PlatformLaunchAnnouncementUserRelation,Long> {

    PlatformLaunchAnnouncementUserRelation findFirstByplatformLaunchAnnouncementIdAndEmail(Long platformLaunchAnnouncementId, String email);

}
