package com.altomni.apn.management.repository.tenant;

import com.altomni.apn.management.domain.tenant.TenantAdminStatusBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the TenantAdminStatusBrief entity.
 * <AUTHOR>
 */
@Repository
public interface TenantAdminStatusRepository extends JpaRepository<TenantAdminStatusBrief, Long> {

    @Modifying
    @Query(value = "delete from tenant_admin_status where tenant_id =?1",nativeQuery = true)
    void deleteAllByTenantId(Long tenantId);

    List<TenantAdminStatusBrief> findByTenantId(Long tenantId);
}
