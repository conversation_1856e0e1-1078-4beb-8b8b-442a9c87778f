package com.altomni.apn.management.dto.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class ErrorInfoDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "email")
    private String email;

    @ApiModelProperty(value = "countryCode")
    private Integer countryCode;

    @ApiModelProperty(value = "phone")
    private Long phone;

    @NotEmpty(message = "source url must be not null")
    @ApiModelProperty(value = "source url")
    private String source;

    @NotEmpty(message = "error description must be not null")
    @ApiModelProperty(value = "error description")
    private String errorDesc;

    private Long tenantId;

    private Long userId;

}
