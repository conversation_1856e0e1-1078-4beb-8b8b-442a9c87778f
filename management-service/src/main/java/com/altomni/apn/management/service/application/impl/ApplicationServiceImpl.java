package com.altomni.apn.management.service.application.impl;

import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.nodepagesection.FieldConfigDTO;
import com.altomni.apn.common.dto.application.recruitmentprocess.*;
import com.altomni.apn.common.dto.application.recruitmentprocessnode.RecruitmentProcessNodeDTO;
import com.altomni.apn.management.service.application.ApplicationService;
import com.altomni.apn.management.service.application.RecruitmentProcessNodePageSectionService;
import com.altomni.apn.management.service.application.RecruitmentProcessNodeService;
import com.altomni.apn.management.service.application.RecruitmentProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
public class ApplicationServiceImpl implements ApplicationService {

    @Resource
    private RecruitmentProcessService recruitmentProcessService;

    @Resource
    private RecruitmentProcessNodeService recruitmentProcessNodeService;

    @Resource
    private RecruitmentProcessNodePageSectionService recruitmentProcessNodePageSectionService;

    @Override
    public Page<RecruitmentProcessVO> findAllByTenantId(Long tenantId, ActiveStatus status, Pageable pageable) {
        Object res = recruitmentProcessService.findAllByTenantId(tenantId, Objects.isNull(status)?null:status.name(), pageable).getBody();

        LinkedHashMap<String, Object> linkedHashMap = (LinkedHashMap<String, Object>) res;
        List<RecruitmentProcessVO> content = (List<RecruitmentProcessVO>) linkedHashMap.get("content");
        int totalElements = (Integer) linkedHashMap.get("totalElements");
        return new PageImpl<>(content, pageable, (long) totalElements);
    }

    @Override
    public RecruitmentProcessVO create(RecruitmentProcessVO recruitmentProcessVO) {
        return recruitmentProcessService.create(recruitmentProcessVO).getBody();
    }

    @Override
    public Void delete(Long id) {
        return recruitmentProcessService.delete(id).getBody();
    }

    @Override
    public RecruitmentProcessVO updateStatus(Long id, StatusDTO status) {
        return recruitmentProcessService.updateStatus(id, status).getBody();
    }

    @Override
    public RecruitmentProcessVO update(Long id, RecruitmentProcessVO recruitmentProcessVO) {
        return recruitmentProcessService.update(id, recruitmentProcessVO).getBody();
    }

    @Override
    public RecruitmentProcessVO findOne(Long id) {
        return recruitmentProcessService.findOne(id).getBody();
    }

    @Override
    public List<RecruitmentProcessVO> config(RecruitmentProcessConfigVO configVO) {
        return recruitmentProcessService.config(configVO).getBody();
    }

    @Override
    public RecruitmentProcessTalentAndJobStats getInProgressJobAndApplicationCountByRecruitmentProcessId(Long recruitmentProcessId) {
        return recruitmentProcessService.getInProgressJobAndApplicationCountByRecruitmentProcessId(recruitmentProcessId).getBody();
    }

//    @Override
//    public void downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(Long recruitmentProcessId, HttpServletResponse response) {
//        recruitmentProcessService.downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(recruitmentProcessId, response);
//    }

    @Override
    public List<RecruitmentProcessNodeVO> findAllNodesByRecruitmentProcessId(Long recruitmentProcessId) {
        return recruitmentProcessNodeService.findAllNodesByRecruitmentProcessId(recruitmentProcessId).getBody();
    }

    @Override
    public RecruitmentProcessNodeDTO updateNodeByRecruitmentProcessIdAndNodeId(Long recruitmentProcessId, Long nodeId, RecruitmentProcessNodeDTO recruitmentProcessNodeDTO) {
        return recruitmentProcessNodeService.updateNodeByRecruitmentProcessIdAndNodeId(recruitmentProcessId, nodeId, recruitmentProcessNodeDTO).getBody();
    }

    @Override
    public Object createNodePageSection(Long recruitmentProcessId, NodeType nodeType, List<FieldConfigDTO> fieldConfigDTOS) {
        return recruitmentProcessNodePageSectionService.create(recruitmentProcessId, nodeType, fieldConfigDTOS).getBody();
    }

    @Override
    public Object updateNodePageSection(Long recruitmentProcessId, NodeType nodeType, List<FieldConfigDTO> fieldConfigDTOS) {
        return recruitmentProcessNodePageSectionService.update(recruitmentProcessId, nodeType, fieldConfigDTOS).getBody();
    }

    @Override
    public List<Object> findGeneralRecruitmentProcessNodePageSectionsDefaultConfig() {
        return recruitmentProcessNodePageSectionService.findGeneralRecruitmentProcessNodePageSectionsDefaultConfig().getBody();
    }

    @Override
    public List<Object> findIpgRecruitmentProcessNodePageSectionsDefaultConfig(JobType jobType) {
        return recruitmentProcessNodePageSectionService.findIpgRecruitmentProcessNodePageSectionsDefaultConfig(jobType).getBody();
    }

    @Override
    public Object findGeneralRecruitmentProcessNodePageSectionsDefaultConfigByNodeType(NodeType nodeType) {
        return recruitmentProcessNodePageSectionService.findGeneralRecruitmentProcessNodePageSectionsDefaultConfigByNodeType(nodeType).getBody();
    }

    @Override
    public Object findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(JobType jobType, NodeType nodeType) {
        return recruitmentProcessNodePageSectionService.findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(jobType, nodeType).getBody();
    }

    @Override
    public List<Object> findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(Long recruitmentProcessId) {
        return recruitmentProcessNodePageSectionService.findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(recruitmentProcessId).getBody();
    }

    @Override
    public Object findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(Long recruitmentProcessId, NodeType nodeType) {
        return recruitmentProcessNodePageSectionService.findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(recruitmentProcessId, nodeType).getBody();
    }

    @Override
    public byte[] downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(Long recruitmentProcessId, String language) {
        return recruitmentProcessService.downloadInProgressJobAndApplicationCount(recruitmentProcessId, language).getBody();
    }

}
