package com.altomni.apn.management.service.module;

import com.altomni.apn.management.dto.module.TenantModuleDTO;
import com.altomni.apn.management.vo.module.TenantModulePageVO;

import java.util.List;

public interface TenantModuleService {

    List<TenantModulePageVO> searchTenantModuleList(Long tenantId);

    void updateTenantModuleList(Long tenantId, List<TenantModuleDTO> tenantModuleDTOList);

}
