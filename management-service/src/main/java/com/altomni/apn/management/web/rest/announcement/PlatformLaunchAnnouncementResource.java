package com.altomni.apn.management.web.rest.announcement;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.management.domain.announcement.PlatformLaunchAnnouncement;
import com.altomni.apn.management.domain.enumeration.LaunchPlatform;
import com.altomni.apn.management.dto.announcement.PlatformLaunchAnnouncementClosedDTO;
import com.altomni.apn.management.dto.announcement.PlatformLaunchAnnouncementDTO;
import com.altomni.apn.management.service.announcement.PlatformLaunchAnnouncementService;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementDetailVO;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@Api(tags = {"PlatformLaunchAnnouncement Controller"})
@RestController
@RequestMapping("/api/v3/platform-launch-announcement")
public class PlatformLaunchAnnouncementResource {

    @Resource
    private PlatformLaunchAnnouncementService platformLaunchAnnouncementService;

    @PostMapping("")
    @NoRepeatSubmit
    @ApiOperation(value = "create platformLaunchAnnouncement", tags = {"APN V3"})
    public ResponseEntity<PlatformLaunchAnnouncementVO> createPlatformLaunchAnnouncement(@Valid @RequestBody PlatformLaunchAnnouncementDTO platformLaunchAnnouncementDTO) {
        log.info("[APN Management: PlatformLaunchAnnouncement @{}] REST request to create platformLaunchAnnouncement.", SecurityUtils.getCurrentUserLogin());
        PlatformLaunchAnnouncementVO platformLaunchAnnouncementVO = platformLaunchAnnouncementService.createPlatformLaunchAnnouncement(platformLaunchAnnouncementDTO);
        return new ResponseEntity<>(platformLaunchAnnouncementVO, HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    @NoRepeatSubmit
    @ApiOperation(value = "update platformLaunchAnnouncement", tags = {"APN V3"})
    public ResponseEntity<PlatformLaunchAnnouncementVO> updatePlatformLaunchAnnouncement(@PathVariable("id") Long id, @Valid @RequestBody PlatformLaunchAnnouncementDTO platformLaunchAnnouncementDTO) {
        log.info("[APN Management: PlatformLaunchAnnouncement @{}] REST request to update platformLaunchAnnouncement.", SecurityUtils.getUserId());
        PlatformLaunchAnnouncementVO platformLaunchAnnouncementVO = platformLaunchAnnouncementService.updatePlatformLaunchAnnouncement(id, platformLaunchAnnouncementDTO);
        return new ResponseEntity<>(platformLaunchAnnouncementVO, HttpStatus.CREATED);
    }

    @GetMapping("/search")
    @NoRepeatSubmit
    @ApiOperation(value = "search platformLaunchAnnouncement", tags = {"APN V3"})
    public ResponseEntity<List<PlatformLaunchAnnouncementVO>> searchPlatformLaunchAnnouncement(@PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN Management: PlatformLaunchAnnouncement @{}] REST request to create platformLaunchAnnouncement.", SecurityUtils.getUserId());
        Page<PlatformLaunchAnnouncement> page = platformLaunchAnnouncementService.searchPlatformLaunchAnnouncement(pageable);
        List<PlatformLaunchAnnouncementVO> platformLaunchAnnouncementVOList = page.getContent().stream().map(PlatformLaunchAnnouncement::toPlatformLaunchAnnouncementVO).toList();
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/platform-launch-announcement/search");
        headers.add("Access-Control-Expose-Headers","Pagination-Count");
        return ResponseEntity.ok().headers(headers).body(platformLaunchAnnouncementVOList);
    }

    /**
     * 业务端获取最新公告,public接口
     * @param language
     * @param platform
     * @return
     */
    @GetMapping("/latest")
    @NoRepeatSubmit
    @ApiOperation(value = "query platformLaunchAnnouncement detail", tags = {"APN V3"})
    public ResponseEntity<PlatformLaunchAnnouncementDetailVO> queryPlatformLaunchAnnouncement(@RequestParam("language") LanguageEnum language, @RequestParam("platform") LaunchPlatform platform, @RequestParam(value = "email", required = false) String email) {
        log.info("[APN Management: PlatformLaunchAnnouncement @{}] REST request to query platformLaunchAnnouncement detail. language: {}, platform: {}", email, language, platform);
        PlatformLaunchAnnouncementDetailVO platformLaunchAnnouncementDetailVO = platformLaunchAnnouncementService.queryPlatformLaunchAnnouncement(language, platform, email);
        return new ResponseEntity<>(platformLaunchAnnouncementDetailVO, HttpStatus.OK);
    }

    /**
     * 用户关闭已读的公告,public接口
     */
    @PostMapping("/closed")
    ResponseEntity<HttpStatus> closedPlatformLaunchAnnouncement(@Valid @RequestBody PlatformLaunchAnnouncementClosedDTO platformLaunchAnnouncementClosedDTO) {
        log.info("[APN Management: PlatformLaunchAnnouncement @{}] REST request to closed platformLaunchAnnouncement detail. id: {}", platformLaunchAnnouncementClosedDTO.getEmail(), platformLaunchAnnouncementClosedDTO.getId());
        platformLaunchAnnouncementService.closedPlatformLaunchAnnouncement(platformLaunchAnnouncementClosedDTO);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }
}
