package com.altomni.apn.management.dto.tenant;

import com.altomni.apn.common.domain.enumeration.StaffSizeType;
import com.altomni.apn.common.domain.enumeration.company.IndustryType;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class TenantCommonDTO implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "data id")
    private Long id;

    @NotNull(message = "tenant name is null")
    @ApiModelProperty(value = "tenant name")
    private String name;

    @ApiModelProperty(value = "industry")
    private IndustryType industry;

    @ApiModelProperty(value = "website")
    private String  website;

    @ApiModelProperty(value = "organization name")
    private String organizationName;

    @ApiModelProperty(value = "staffSizeType")
    private StaffSizeType staffSizeType;

    @ApiModelProperty(value = "tenant found date")
    private Instant foundedDate;

    @ApiModelProperty(value = "tenant admin info")
    @NotNull(message = "tenant admin is null")
    List<UserBriefDTO> admin;

    @ApiModelProperty(value = "tenant bulk credit")
    private Integer bulkCredit;

    @ApiModelProperty(value = "tenant monthly credit")
    @NotNull(message = "monthly is null")
    private Integer monthlyCredit;

    @ApiModelProperty(value = "tenant monthly credit will be update")
    private Integer updateMonthlyCredit;

    @ApiModelProperty(value = "city id")
    private Long cityId;

    @ApiModelProperty(value = "detail address")
    private String  address;

    @ApiModelProperty(value = "data id")
    private String description;

    @ApiModelProperty(value = "tenant logo ")
    private String  logo;

    @ApiModelProperty(value = "tenant status")
    private Integer  status;

    @ApiModelProperty(value = "tenant email")
    private String tenantEmail;

    @ApiModelProperty(value = "tenant phone")
    private String tenantPhone;


    public TenantCommonDTO() {
    }
}
