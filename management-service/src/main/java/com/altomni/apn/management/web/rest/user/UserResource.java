package com.altomni.apn.management.web.rest.user;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.management.dto.user.UserStatusDTO;
import com.altomni.apn.management.service.user.UserService;
import com.altomni.apn.management.vo.user.UserPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Slf4j
@Api(tags = {"Management User"})
@RestController
@RequestMapping("/api/v3/users")
public class UserResource {

    @Resource
    private UserService userService;

    @GetMapping("/{tenantId}/page")
    @NoRepeatSubmit
    @ApiOperation(value = "Search user list", tags = {"APN V3"})
    public ResponseEntity<List<UserPageVO>> searchUserList(@PathVariable("tenantId") Long tenantId, @PageableDefault @SortDefault(sort = {"activated", "createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[APN Management: User @{}] REST request to search user list : {}", SecurityUtils.getUserId(), pageable);
        return userService.searchUserList(tenantId, pageable);
    }

    @PutMapping("/tenantId/{tenantId}/userId/{userId}/status")
    @NoRepeatSubmit
    @ApiOperation(value = "Update user status", tags = {"APN V3"})
    public ResponseEntity<HttpStatus> updateUserStatus(@PathVariable("userId") Long userId, @RequestBody UserStatusDTO userStatusDTO) {
        log.info("[APN Management: User @{}] REST request to update user status . userId: {},", SecurityUtils.getUserId(), userId);
        userService.updateUserStatus(userId, userStatusDTO);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }
}
