package com.altomni.apn.management.domain.user;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A UserAccount.
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserNameVM implements Serializable {

    private static final long serialVersionUID = 3524846326589097872L;

    @Id
    private Long id;

    private String firstName;

    private String lastName;

}
