package com.altomni.apn.management.valid;

import com.altomni.apn.management.dto.announcement.PlatformLaunchAnnouncementDTO;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.stream.Stream;

public class AnnouncementLaunchTimeValidator implements ConstraintValidator<AnnouncementLaunchTime, Object> {
    private static final Class<?>[] SUPPORTED_CLASSES = { PlatformLaunchAnnouncementDTO.class};

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null || Stream.of(SUPPORTED_CLASSES).noneMatch(cls -> cls.isInstance(value))) {
            return false;
        }

        if (value instanceof PlatformLaunchAnnouncementDTO) {
            PlatformLaunchAnnouncementDTO platformLaunchAnnouncementDTO = (PlatformLaunchAnnouncementDTO) value;
            if (platformLaunchAnnouncementDTO.getReminderTime().isAfter(platformLaunchAnnouncementDTO.getLaunchStartTime())) {
                context.buildConstraintViolationWithTemplate("reminderTime cannot be earlier than launchStartTime")
                        .addPropertyNode("reminderTime")
                        .addConstraintViolation();
                return false;
            }
            if (platformLaunchAnnouncementDTO.getReminderTime().isAfter(platformLaunchAnnouncementDTO.getLaunchEndTime())) {
                context.buildConstraintViolationWithTemplate("reminderTime cannot be earlier than launchEndTime")
                        .addPropertyNode("reminderTime")
                        .addConstraintViolation();
                return false;
            }
            if (platformLaunchAnnouncementDTO.getLaunchStartTime().isAfter(platformLaunchAnnouncementDTO.getLaunchEndTime())) {
                context.buildConstraintViolationWithTemplate("launchStartTime cannot be earlier than launchEndTime")
                        .addPropertyNode("reminderTime")
                        .addConstraintViolation();
                return false;
            }
        }
        return true;
    }
}