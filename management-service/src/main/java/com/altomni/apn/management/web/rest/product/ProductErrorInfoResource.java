package com.altomni.apn.management.web.rest.product;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.management.dto.product.ErrorInfoDTO;
import com.altomni.apn.management.dto.product.ErrorInfoUpdateDTO;
import com.altomni.apn.management.service.product.ProductErrorInfoService;
import com.altomni.apn.management.vo.product.ErrorInfoVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@Api(tags = {"ProductErrorInfo Controller"})
@RestController
@RequestMapping("/api/v3/product")
public class ProductErrorInfoResource {

    @Resource
    private ProductErrorInfoService productErrorInfoService;

    @PostMapping("/errors")
    @NoRepeatSubmit
    @ApiOperation(value = "create product error info", tags = {"APN V3"})
    public ResponseEntity<HttpStatus> createErrorInfo(@Valid @RequestBody ErrorInfoDTO errorInfoDTO) {
        log.info("[APN Management: Module @{}] REST request to create errorInfo.", SecurityUtils.getUserId());
        productErrorInfoService.createErrorInfo(errorInfoDTO);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @GetMapping("/errors/page")
    @NoRepeatSubmit
    @ApiOperation(value = "search product error info", tags = {"APN V3"})
    public ResponseEntity<List<ErrorInfoVO>> searchErrorInfos(@SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN Management: Module @{}] REST request to search errorInfo.", SecurityUtils.getUserId());
        Page<ErrorInfoVO> errorInfoVOPage = productErrorInfoService.searchErrorInfos(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(errorInfoVOPage, "/tenants");
        return new ResponseEntity<>(errorInfoVOPage.getContent(), headers, HttpStatus.OK);
    }

    @GetMapping("/errors/{id}")
    @NoRepeatSubmit
    @ApiOperation(value = "query product error info", tags = {"APN V3"})
    public ResponseEntity<ErrorInfoVO> queryErrorInfo(@PathVariable(value = "id") Long id) {
        log.info("[APN Management: Module @{}] REST request to query errorInfo. id: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(productErrorInfoService.queryErrorInfo(id), HttpStatus.OK);
    }

    @PutMapping("/errors/{id}")
    @NoRepeatSubmit
    @ApiOperation(value = "update product error info", tags = {"APN V3"})
    public ResponseEntity<ErrorInfoVO> updateErrorInfo(@PathVariable(value = "id") Long id, @Valid @RequestBody ErrorInfoUpdateDTO errorInfoUpdateDTO) {
        log.info("[APN Management: Module @{}] REST request to update errorInfo. id: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(productErrorInfoService.updateErrorInfo(id, errorInfoUpdateDTO), HttpStatus.CREATED);
    }


}
