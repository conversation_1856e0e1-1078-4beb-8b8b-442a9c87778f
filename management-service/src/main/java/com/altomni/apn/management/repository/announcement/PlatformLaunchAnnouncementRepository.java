package com.altomni.apn.management.repository.announcement;

import com.altomni.apn.management.domain.announcement.PlatformLaunchAnnouncement;
import com.altomni.apn.management.domain.enumeration.AnnouncementStatus;
import com.altomni.apn.management.domain.enumeration.LaunchPlatform;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

@Repository
public interface PlatformLaunchAnnouncementRepository extends JpaRepository<PlatformLaunchAnnouncement,Long> {

    /**
     * 根据reminderTime & launchEndTime查时间段内已有的公告
     * @param platform
     * @param status
     * @param reminderTime
     * @param launchEndTime
     * @return
     */
    @Query("SELECT p FROM PlatformLaunchAnnouncement p WHERE p.platform = :platform AND p.status = :status " +
            "AND ((p.reminderTime <= :reminderTime AND p.launchEndTime >= :reminderTime) " +
            "OR (p.reminderTime <= :launchEndTime AND p.launchEndTime >= :launchEndTime))")
    List<PlatformLaunchAnnouncement> findAllByPlatformAndStatusAndTimeRangeOverlap(@Param("platform") LaunchPlatform platform, @Param("status") AnnouncementStatus status, @Param("reminderTime") Instant reminderTime, @Param("launchEndTime") Instant launchEndTime);

    /**
     * 查当前时间内已经发布的公告
     * @param platform
     * @param status
     * @param now
     * @return
     */
    @Query("SELECT p FROM PlatformLaunchAnnouncement p WHERE p.platform = :platform AND p.status = :status AND p.reminderTime <= :now AND p.launchEndTime >= :now ORDER BY p.createdDate DESC")
    List<PlatformLaunchAnnouncement> findAllByPlatformAndStatus(@Param("platform") LaunchPlatform platform, @Param("status") AnnouncementStatus status, @Param("now") Instant now);
}
