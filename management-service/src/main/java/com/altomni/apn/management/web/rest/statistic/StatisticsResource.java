package com.altomni.apn.management.web.rest.statistic;

import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.management.service.statistic.StatisticService;
import com.altomni.apn.management.vo.statistic.StatisticVo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = {"statistics controller"})
@RestController
@RequestMapping("/api/v3/tenant")
public class StatisticsResource {

    @Resource
    private StatisticService statisticService;

    @GetMapping("/job-statistics/{type}")
    public ResponseEntity<List<StatisticVo>> jobStatisticsByType(@PathVariable("type") Integer type) {
        return ResponseEntity.ok(statisticService.jobStatisticByType(TenantUserTypeEnum.fromDbValue(type)));
    }

    @GetMapping("/talent-statistics/{type}")
    public ResponseEntity<List<StatisticVo>> talentStatisticsByType(@PathVariable("type") Integer type) {
        return ResponseEntity.ok(statisticService.talentStatisticByType(TenantUserTypeEnum.fromDbValue(type)));
    }

    @GetMapping("/company-statistics/{type}")
    public ResponseEntity<List<StatisticVo>> companyStatisticsByType(@PathVariable("type") Integer type) {
        return ResponseEntity.ok(statisticService.companyClientStatisticByType(TenantUserTypeEnum.fromDbValue(type)));
    }

}
