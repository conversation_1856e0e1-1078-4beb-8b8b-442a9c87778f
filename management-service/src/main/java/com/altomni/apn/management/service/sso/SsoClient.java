package com.altomni.apn.management.service.sso;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.management.dto.user.SsoCreateUserDto;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SsoClient {

    @Value("${sso.address}")
    private String ssoAddress;

    private final HttpService httpService;

    public SsoClient(HttpService httpService) {
        this.httpService = httpService;
    }

    public Long innerCreateApnUser(SsoCreateUserDto dto) {

        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            HttpResponse response = httpService.post(ssoAddress + "/inner/api/v1/users", headers, JsonUtil.toJson(dto));
            if (response.getCode() == 201) {
                log.info("User created in SSO");
                JSONObject jsonObject = JSONUtil.parseObj(response.getBody());
                Long userId = jsonObject.getLong("id");
                return userId;
            } else {
                log.error("Error while creating user in SSO. Response code: {}, response body: {}", response.getCode(), response.getBody());
                throw new RuntimeException("Error while creating user in SSO. Response code: " + response.getCode() + ", response body: " + response.getBody());
            }
        } catch (Exception e) {
            log.error("Error while creating user in SSO", e);
            throw new RuntimeException("Error while creating user in SSO", e);
        }
    }

    public void innerInactiveUsersByTenant(Long tenantId) {
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            HttpResponse response = httpService.post(ssoAddress + "/inner/api/v1/users/inactive-by-tenant/" + tenantId, headers, "");
            if (response.getCode() == 200) {
                log.info("User inactivated in SSO by tenantId: {}", tenantId);
            } else {
                log.error("Error while inactivating user in SSO. Response code: {}, response body: {}", response.getCode(), response.getBody());
                throw new RuntimeException("Error while inactivating user in SSO. Response code: " + response.getCode() + ", response body: " + response.getBody());
            }
        } catch (Exception e) {
            log.error("Error while inactivating user in SSO", e);
            throw new RuntimeException("Error while inactivating user in SSO", e);
        }
    }

    public void innerActiveUsersByTenant(Long tenantId, List<String> excludeUsers) {
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            HttpResponse response = httpService.post(ssoAddress + "/inner/api/v1/users/activate-by-tenant/" + tenantId, headers, JsonUtil.toJson(excludeUsers));
            if (response.getCode() == 200) {
                log.info("User activated in SSO by tenantId: {}", tenantId);
            } else {
                log.error("Error while activating user in SSO. Response code: {}, response body: {}", response.getCode(), response.getBody());
                throw new RuntimeException("Error while activating  user in SSO. Response code: " + response.getCode() + ", response body: " + response.getBody());
            }
        } catch (Exception e) {
            log.error("Error while activating user in SSO", e);
            throw new RuntimeException("Error while activating user in SSO", e);
        }
    }

    public void innerUpdateUserStatus(String email, Boolean activated) {
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            HttpResponse response = httpService.put(ssoAddress + "/inner/api/v1/users/" + email + "/status?" + "active=" + activated, headers, "");
            if (response.getCode() == 200) {
                log.info("User activated in SSO : {}", email);
            } else {
                log.error("Error while activating user in SSO. Response code: {}, response body: {}", response.getCode(), response.getBody());
                throw new RuntimeException("Error while activating  user in SSO. Response code: " + response.getCode() + ", response body: " + response.getBody());
            }
        } catch (Exception e) {
            log.error("Error while activating user in SSO", e);
            throw new RuntimeException("Error while activating user in SSO", e);
        }
    }

    public boolean innerCheckUserEmailExists(String email) {
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            HttpResponse response = httpService.get(ssoAddress + "/inner/api/v1/users/" + email, headers);
            return response.getCode() != 404;
        } catch (Exception e) {
            log.error("Error while checking user email in SSO", e);
            throw new RuntimeException("Error while checking user email in SSO", e);
        }
    }

    public void innerCreateTenant(Tenant tenant) {
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            HttpResponse response = httpService.post(ssoAddress + "/inner/api/v1/tenants", headers, JsonUtil.toJson(tenant));
            if (response.getCode() == 201) {
                log.info("Tenant created in SSO");
            } else {
                log.error("Error while creating tenant in SSO. Response code: {}, response body: {}", response.getCode(), response.getBody());
                throw new RuntimeException("Error while creating tenant in SSO. Response code: " + response.getCode() + ", response body: " + response.getBody());
            }
        } catch (Exception e) {
            log.error("Error while creating tenant in SSO", e);
            throw new RuntimeException("Error while creating tenant in SSO", e);
        }
    }

    public void innerUpdateTenant(Tenant tenant) {
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            HttpResponse response = httpService.put(ssoAddress + "/inner/api/v1/tenants/" + tenant.getId(), headers, JsonUtil.toJson(tenant));
            if (response.getCode() == 200) {
                log.info("Tenant updated in SSO");
            } else {
                log.error("Error while updating tenant in SSO. Response code: {}, response body: {}", response.getCode(), response.getBody());
                throw new RuntimeException("Error while updating tenant in SSO. Response code: " + response.getCode() + ", response body: " + response.getBody());
            }
        } catch (Exception e) {
            log.error("Error while updating tenant in SSO", e);
            throw new RuntimeException("Error while updating tenant in SSO", e);
        }
    }
}
