package com.altomni.apn.management.service.user;

import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.management.dto.user.UserStatusDTO;
import com.altomni.apn.management.vo.user.UserPageVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 */
public interface UserService {

    ResponseEntity<List<UserPageVO>> searchUserList(Long tenantId, Pageable pageable) throws ExecutionException, InterruptedException;

    void updateUserStatus(Long userId, UserStatusDTO userStatusDTO);

}
