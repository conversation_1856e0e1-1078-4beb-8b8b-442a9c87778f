package com.altomni.apn.management.service.announcement;

import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.management.domain.announcement.PlatformLaunchAnnouncement;
import com.altomni.apn.management.domain.enumeration.LaunchPlatform;
import com.altomni.apn.management.dto.announcement.PlatformLaunchAnnouncementClosedDTO;
import com.altomni.apn.management.dto.announcement.PlatformLaunchAnnouncementDTO;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementDetailVO;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface PlatformLaunchAnnouncementService {

    PlatformLaunchAnnouncementVO createPlatformLaunchAnnouncement(PlatformLaunchAnnouncementDTO platformLaunchAnnouncementDTO);

    PlatformLaunchAnnouncementVO updatePlatformLaunchAnnouncement(Long id, PlatformLaunchAnnouncementDTO platformLaunchAnnouncementDTO);

    Page<PlatformLaunchAnnouncement> searchPlatformLaunchAnnouncement(Pageable pageable);

    PlatformLaunchAnnouncementDetailVO queryPlatformLaunchAnnouncement(LanguageEnum language, LaunchPlatform platform, String email);

    void closedPlatformLaunchAnnouncement(PlatformLaunchAnnouncementClosedDTO platformLaunchAnnouncementClosedDTO);
}
