package com.altomni.apn.management.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ProductErrorInfoStatus enumeration.
 */
public enum ProductErrorInfoStatus implements ConvertedEnum<Integer> {
    UNSOLVED(10),
    RESOLVED(20);


    private final Integer dbValue;

    ProductErrorInfoStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ProductErrorInfoStatus, Integer> resolver =
        new ReverseEnumResolver<>(ProductErrorInfoStatus.class, ProductErrorInfoStatus::toDbValue);

    public static ProductErrorInfoStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
