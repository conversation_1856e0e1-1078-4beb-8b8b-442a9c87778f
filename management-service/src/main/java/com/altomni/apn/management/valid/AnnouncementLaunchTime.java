package com.altomni.apn.management.valid;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = AnnouncementLaunchTimeValidator.class)
public @interface AnnouncementLaunchTime {
    String message() default "launch time conflict";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}