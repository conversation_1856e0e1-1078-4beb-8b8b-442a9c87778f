package com.altomni.apn.management.service.user;

import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.sso.SsoUserActive;
import com.altomni.apn.common.dto.sso.SsoUserBinding;
import com.altomni.apn.common.dto.sso.SsoUserInfo;
import com.altomni.apn.common.dto.user.LoginVM;
import com.altomni.apn.common.dto.user.ManagementLoginUserDTO;
import com.altomni.apn.common.dto.user.ManagementUserDTO;
import com.altomni.apn.management.dto.user.ForgetPassDTO;
import com.altomni.apn.management.dto.user.ForgetPassResetDTO;
import com.altomni.apn.management.dto.user.RefreshTokenDTO;
import com.altomni.apn.management.dto.user.ResetPasswordDTO;

import java.security.GeneralSecurityException;

public interface ManagementUserService {

    ManagementLoginUserDTO login(LoginVM loginVM);

    ManagementLoginUserDTO findOneByUsername(String username);

    ManagementUserDTO findUserWithAuthorities(String login);

    void forgetPass(ForgetPassDTO forgetPassDTO);

    void resetPassForForget(ForgetPassResetDTO dto) throws GeneralSecurityException;

    ManagementLoginUserDTO resetPassword(ResetPasswordDTO resetPasswordDTO) throws GeneralSecurityException;

    CredentialDTO refreshToken(RefreshTokenDTO refreshTokenDTO);

    void test(ForgetPassResetDTO dto);

    void updateBYSsoUserInfo(SsoUserInfo ssoUserInfo);

    void updateUserActiveBySso(SsoUserActive userActive);

    void onSsoUserBinding(SsoUserBinding ssoUserBinding);
}
