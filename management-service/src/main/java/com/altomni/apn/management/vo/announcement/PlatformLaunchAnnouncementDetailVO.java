package com.altomni.apn.management.vo.announcement;

import com.altomni.apn.management.domain.enumeration.AnnouncementStatus;
import com.altomni.apn.management.domain.enumeration.AnnouncementStatusConverter;
import com.altomni.apn.management.domain.enumeration.LaunchPlatform;
import com.altomni.apn.management.domain.enumeration.LaunchPlatformConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PlatformLaunchAnnouncementDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "launchStartTime")
    private Instant launchStartTime;

    @ApiModelProperty(value = "launchEndTime")
    private Instant launchEndTime;

    @ApiModelProperty(value = "reminderTime")
    private Instant reminderTime;

    @ApiModelProperty(value = "content")
    private String content;

    @ApiModelProperty(value = "upgradeLogLink")
    private String upgradeLogLink;

    @ApiModelProperty(value = "upgradeLogTitle")
    private String upgradeLogTitle;

}
