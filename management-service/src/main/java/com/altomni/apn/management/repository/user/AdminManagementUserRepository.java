package com.altomni.apn.management.repository.user;

import com.altomni.apn.common.domain.user.AdminManagementUser;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


/**
 * Spring Data  repository for the AdminManagementUser entity.
 */
@Repository
public interface AdminManagementUserRepository extends JpaRepository<AdminManagementUser, Long> {

    @Query("select user from AdminManagementUser user where user.username = ?1 or user.email = ?1")
    AdminManagementUser findByUsernameOrEmail(String userName);

    @Query("select user from AdminManagementUser user where user.uid = :login or user.username = :login or user.email = :login")
    AdminManagementUser findUserWithAuthorities(@Param("login") String login);

    @EntityGraph(attributePaths = "managementRoles")
    AdminManagementUser findByEmail(String email);

    AdminManagementUser findByUid(String uid);

}
