package com.altomni.apn.management.service.tenant.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.config.email.EmailProperties;
import com.altomni.apn.common.domain.permission.PermissionModulePrivilegePageVM;
import com.altomni.apn.common.domain.permission.PermissionTenantPrivilege;
import com.altomni.apn.common.domain.user.QTenant;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.company.BriefCompanyDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.management.TenantPhoneDTO;
import com.altomni.apn.common.dto.management.TenantUpdateStatusDTO;
import com.altomni.apn.common.dto.user.TenantAdminUserDto;
import com.altomni.apn.common.enumeration.enums.ManagementAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.repository.permission.PermissionTenantPrivilegeRepository;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.enums.EnumCountryService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.talent.TenantPublicVO;
import com.altomni.apn.management.config.env.ApplicationProperties;
import com.altomni.apn.management.config.env.ManagementApiPromptProperties;
import com.altomni.apn.management.domain.tenant.JobProject;
import com.altomni.apn.management.domain.tenant.TenantAddressBrief;
import com.altomni.apn.management.domain.tenant.TenantAdminStatusBrief;
import com.altomni.apn.management.domain.vm.TenantCountVM;
import com.altomni.apn.management.domain.vm.TenantUserVM;
import com.altomni.apn.management.dto.tenant.TenantCommonDTO;
import com.altomni.apn.management.dto.tenant.TenantDTO;
import com.altomni.apn.management.dto.user.SsoCreateUserDto;
import com.altomni.apn.management.repository.tenant.JobProjectRepository;
import com.altomni.apn.management.repository.tenant.TenantAddressBriefRepository;
import com.altomni.apn.management.repository.tenant.TenantAdminStatusRepository;
import com.altomni.apn.management.repository.tenant.TenantRepository;
import com.altomni.apn.management.repository.user.UserAccountBriefRepository;
import com.altomni.apn.management.repository.user.UserBriefRepository;
import com.altomni.apn.management.service.application.RecruitmentProcessService;
import com.altomni.apn.management.service.authority.AuthorityService;
import com.altomni.apn.management.service.company.CompanyClient;
import com.altomni.apn.management.service.crm.CrmClient;
import com.altomni.apn.management.service.crm.CrmTenantDto;
import com.altomni.apn.management.service.data.impl.DataBaseServiceImpl;
import com.altomni.apn.management.service.mail.MailService;
import com.altomni.apn.management.service.sso.SsoClient;
import com.altomni.apn.management.service.tenant.TenantService;
import com.altomni.apn.management.service.user.UserClient;
import com.altomni.apn.management.vo.tenant.OnlineTenantVO;
import com.altomni.apn.management.vo.tenant.TenantDetailVO;
import com.altomni.apn.management.vo.tenant.TenantPageVO;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.impl.JPAQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TenantServiceImpl extends DataBaseServiceImpl implements TenantService {

    private final Long UNIVERSAL_TENANT_ID = -4L;

    @Resource
    private TenantRepository tenantRepository;

    @Resource
    private RecruitmentProcessService recruitmentProcessService;


    @Resource
    private UserClient userClient;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private MailService mailService;

    @Resource
    private EnumCountryService enumCountryService;

    @Resource
    private EmailProperties properties;

    @Resource
    AuthorityService authorityService;

    @Resource
    private TenantAdminStatusRepository tasRepository;

    @Resource
    private UserBriefRepository userRepository;

    @Resource
    private UserAccountBriefRepository accountRepository;

    @Resource
    private TenantAddressBriefRepository tenantAddressRepository;

    @Resource
    private CommonRedisService redisService;

    @Resource
    private CompanyClient companyClient;

    @Resource
    private EntityManager entityManager;

    @Resource
    private PermissionTenantPrivilegeRepository permissionTenantPrivilegeRepository;

    @Resource
    private SsoClient ssoClient;

    @Resource
    private CrmClient crmClient;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ManagementApiPromptProperties managementApiPromptProperties;

    @Resource
    private JobProjectRepository jobProjectRepository;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD).withZone(ZoneId.of("UTC"));

    @Override
    public TenantDetailVO create(TenantDTO dto) {
        validateTenantName(dto.getName());
        LoginUtil.simulateLoginWithClient();
        validateTenantEmail(dto);
        // 手动控制事务
        TransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);

        //crate tenant
        Tenant tenant = new Tenant();
        Tenant createTenant;
        try {
            ServiceUtils.myCopyProperties(dto, tenant);
            tenant.setStatus(Constants.ACTIVE);
            tenant.setUpdateMonthlyCredit(dto.getMonthlyCredit());
            tenant.setResetDayOfMonth(dto.getResetDayOfMonth() == null ? 1 : dto.getResetDayOfMonth());
            tenant.setContactName(CommonUtils.formatFullName(dto.getContactFirstName(), dto.getContactLastName()));
            tenant.setTenantPhone(PhoneNumberUtils.formatPhoneNumber(new TenantPhoneDTO(dto.getCountryCode(), dto.getTenantPhone())));
            tenant.setExpireDate(DateUtil.toInstantAtEndOfDay(dto.getExpireDate()));
            createTenant = tenantRepository.saveAndFlush(tenant);
            JobProject jobProject = new JobProject();
            jobProject.setName("private job");
            jobProject.setTenantId(createTenant.getId());
            jobProjectRepository.saveAndFlush(jobProject);
            // 在这里提交事务，后面其他服务才能查到数据
            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("create tenant error", e);
            transactionManager.rollback(status);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_CREATE_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), managementApiPromptProperties.getManagementService()));
        }

        // init tenant config
        try {
            saveTenantDefaultModuleConfig(tenant);
            recruitmentProcessService.initDefaultForGeneralRecruitingProcess(createTenant.getId());
        } catch (Exception e) {
            log.error("init tenant config error", e);
            resolveCreateTenantError(createTenant);
        }

        String password = RandomUtil.generatePassword();
        Long tenantAdminUserId = null;
        // create tenant admin user in sso
        try {
            SsoCreateUserDto ssoCreateUserDto = new SsoCreateUserDto();
            ssoCreateUserDto.setEmail(dto.getTenantEmail());
            ssoCreateUserDto.setFirstName(dto.getContactFirstName());
            ssoCreateUserDto.setLastName(dto.getContactLastName());
            ssoCreateUserDto.setPassword(password);
            ssoCreateUserDto.setTenantId(createTenant.getId());
            ssoCreateUserDto.setTenantName(createTenant.getName());
            if (dto.getUserType().equals(TenantUserTypeEnum.EMPLOYER)) {
                ssoCreateUserDto.setAutoAssignClients(Set.of("apn_app"));
            }
            ssoClient.innerCreateTenant(createTenant);
            tenantAdminUserId = ssoClient.innerCreateApnUser(ssoCreateUserDto);
        } catch (Exception e) {
            log.error("create tenant admin user in sso error", e);
            resolveCreateTenantError(createTenant);
        }

        TenantAdminUserDto user = new TenantAdminUserDto(tenantAdminUserId, dto.getContactFirstName(), dto.getContactLastName(), dto.getTenantEmail(), createTenant.getId());
        // create tenant admin user in apn and assigned authority
        try {
            userClient.createTenantAdminUser(user);
        } catch (Exception e) {
            log.error("create tenant admin user in apn error", e);
            resolveCreateTenantError(createTenant);
        }

        // create crm tenant
        try {
            CrmTenantDto crmTenantDto = new CrmTenantDto(createTenant.getId(), tenantAdminUserId, createTenant.getName(), user.firstName(), user.lastName(), user.email());
            crmClient.createTenant(crmTenantDto);
            // 雇主租户需要创建一个公司
            if (dto.getUserType().equals(TenantUserTypeEnum.EMPLOYER)) {
                BriefCompanyDTO briefCompanyDTO = new BriefCompanyDTO();
                briefCompanyDTO.setFullBusinessName(createTenant.getName());
                briefCompanyDTO.setTenantId(createTenant.getId());
                briefCompanyDTO.setPermissionUserId(1L);// system user
                companyClient.createEmployerTenantCompany(briefCompanyDTO);
            }
        } catch (Exception e) {
            log.error("create crm tenant error", e);
            resolveCreateTenantError(createTenant);
        }

        // send mail to tenant admin user
        try {
            String template = CommonUtils.readFileToString("templates/tenantCreatedTemplate.html");
            Map<String, Object> valueMap = new HashMap<>(16);
            valueMap.put("userName", user.firstName() + " " + user.lastName());
            valueMap.put("tenantName", createTenant.getName());
            valueMap.put("email", user.email());
            valueMap.put("password", password);
            valueMap.put("url", applicationProperties.getBaseUrl());
            template = Html2ImageUtils.convertHtmlTemplate(template, valueMap);
            List<String> to = new ArrayList<>();
            to.add(user.email());
            MailVM mailvm = new MailVM(applicationProperties.getSupportSender(), to, null, null, "Tenant create notice", template, null, true);
            mailService.sendHtmlMail(mailvm);
            log.info("Successfully sent mail after creating tenant");
        } catch (Exception e) {
            log.error("send email to tenant admin user error", e);
            resolveCreateTenantError(createTenant);
        }

        return TenantDetailVO.fromTenant(createTenant);
    }

    private void resolveCreateTenantError(Tenant tenant) {
        tenantRepository.delete(tenant);
        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_CREATE_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), managementApiPromptProperties.getManagementService()));
    }

    private void saveTenantDefaultModuleConfig(Tenant tenant) {

        List<PermissionModulePrivilegePageVM> permissionModulePrivilegeList = userClient.findTenantDefaultModuleList(tenant.getId(), tenant.getUserType()).getBody();
        if (CollUtil.isEmpty(permissionModulePrivilegeList)) {
            permissionModulePrivilegeList = userClient.findTenantDefaultModuleList(UNIVERSAL_TENANT_ID, tenant.getUserType()).getBody();
        }
        if (CollUtil.isNotEmpty(permissionModulePrivilegeList)) {
            List<PermissionTenantPrivilege> permissionTenantPrivileges = permissionModulePrivilegeList.stream().map(o -> new PermissionTenantPrivilege(tenant.getId(), o.getPrivilegeId(), true)).collect(Collectors.toList());
            permissionTenantPrivilegeRepository.saveAll(permissionTenantPrivileges);
        }
    }

    @Override
    public Page<Tenant> searchTenantList(TenantUserTypeEnum type, String nameSearch, Pageable pageable) {
        QTenant qTenant = QTenant.tenant;
        JPAQuery<Tenant> jpaQuery = new JPAQuery<>(entityManager);
        jpaQuery.from(qTenant);
        if (type != null) {
            jpaQuery = jpaQuery.where(qTenant.userType.eq(type));
        }
        if (StringUtils.isNotBlank(nameSearch)) {
            jpaQuery = jpaQuery.where(qTenant.name.likeIgnoreCase("%" + nameSearch + "%"));
        }
        if (pageable.getSort().isSorted()) {
            for (Sort.Order order : pageable.getSort()) {
                jpaQuery = jpaQuery.orderBy(new OrderSpecifier(order.isAscending() ? Order.ASC : Order.DESC,
                        Expressions.path(Object.class, qTenant, order.getProperty())));
            }
        }
        JPQLQuery<Tenant> finalQuery = jpaQuery.offset(pageable.getOffset()).limit(pageable.getPageSize());
        return new PageImpl<>(finalQuery.fetch(), pageable, finalQuery.fetchCount());
    }

    @Override
    public TenantDetailVO queryTenant(Long id) {
        Tenant tenant = tenantRepository.findById(id).orElseThrow(() -> new NotFoundException("This tenant does not exist"));
        return toVo(tenant);
    }

    @Override
    public TenantDetailVO queryTenantByName(String name) {
        Tenant tenant = tenantRepository.findByName(name);
        if (tenant == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_QUERYTENANTBYNAME_TENANTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), managementApiPromptProperties.getManagementService()));
        }
        return toVo(tenant);
    }

    @Override
    public TenantDetailVO updateTenant(Long id, TenantDTO dto) {
        Tenant existTenant = tenantRepository.findById(id).orElseThrow(() -> new NotFoundException("This tenant does not exist"));
        if (!existTenant.getName().equals(dto.getName())) {
            validateTenantName(dto.getName());
        }

        if (dto.getUpdateMonthlyCredit() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_UPDATETENANT_MONEYCREDITNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), managementApiPromptProperties.getManagementService()));
        }

        if (dto.getBulkCredit() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_UPDATETENANT_BULKCREDITNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), managementApiPromptProperties.getManagementService()));
        }

        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, existTenant.getId());
        paramMap.put(2, DateUtil.firstDayOfNextMonth());
        Integer nextMonthTotal = findTotalAssignNextMonthCreditByTenant(paramMap);
        Integer effectMonthTotal = findTotalNextMonthEffectCreditByTenant(paramMap);
        nextMonthTotal = nextMonthTotal == null ? 0 : nextMonthTotal;
        effectMonthTotal = effectMonthTotal == null ? 0 : effectMonthTotal;
        int total = nextMonthTotal + effectMonthTotal;
        if (dto.getUpdateMonthlyCredit() < total) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_UPDATETENANT_MONEYCREDITLESSTOTAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(total), managementApiPromptProperties.getManagementService()));
        }

        //CreditType: MONTHLY(0), BULK(1)
        paramMap.put(2, 1);
        Integer totalCredit = findTotalUsedCreditByTenant(paramMap);
        if (totalCredit != null && totalCredit > dto.getBulkCredit()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_UPDATETENANT_BULKCREDITLESSTOTAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(totalCredit), managementApiPromptProperties.getManagementService()));
        }

        existTenant.setTenantEmail(dto.getTenantEmail());
        existTenant.setUpdateMonthlyCredit(dto.getUpdateMonthlyCredit());
        existTenant.setBulkCredit(dto.getBulkCredit());
        existTenant.setUserMaxLimit(dto.getUserMaxLimit());
        existTenant.setLoginLink(dto.getLoginLink());
        existTenant.setNote(dto.getNote());
        existTenant.setContactName(CommonUtils.formatFullName(dto.getContactFirstName(), dto.getContactLastName()));
        existTenant.setTenantPhone(PhoneNumberUtils.formatPhoneNumber(new TenantPhoneDTO(dto.getCountryCode(), dto.getTenantPhone())));
        existTenant.setExpireDate(DateUtil.toInstantAtEndOfDay(dto.getExpireDate()));
        Tenant updateTenant = tenantRepository.save(existTenant);
        ssoClient.innerUpdateTenant(updateTenant);

        return toVo(updateTenant);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantStatusToActive(Long id, TenantUpdateStatusDTO dto) {
        Tenant existTenant = tenantRepository.findById(id).orElseThrow(() -> new NotFoundException("This tenant does not exist"));

        existTenant.setStatus(1);
        //recover user status
        existTenant.setExpireDate(DateUtil.toInstantAtEndOfDay(dto.getExpireDate()));
        List<TenantAdminStatusBrief> tas = tasRepository.findByTenantId(existTenant.getId());
        Map<Long, TenantAdminStatusBrief> tasMap = tas.stream().collect(Collectors.toMap(TenantAdminStatusBrief::getUserId, TenantAdminStatusBrief -> TenantAdminStatusBrief));
        List<User> userList = userRepository.findAllByTenantId(existTenant.getId());
        List<String> excludeUserEmails = new ArrayList<>();
        userList.forEach(user -> {
            if (tasMap.containsKey(user.getId())) {
                user.setActivated(tasMap.get(user.getId()).isActivated());
                if (!tasMap.get(user.getId()).isActivated()) {
                    excludeUserEmails.add(user.getEmail());
                }
            }
        });
        ssoClient.innerActiveUsersByTenant(existTenant.getId(), excludeUserEmails);
        ssoClient.innerUpdateTenant(existTenant);
        userRepository.saveAll(userList);
        tenantRepository.save(existTenant);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantStatusToInActive(Long id) {
        Tenant existTenant = tenantRepository.findById(id).orElseThrow(() -> new NotFoundException("This tenant does not exist"));

        existTenant.setStatus(0);
        //save user current status
        List<User> userBriefs = userRepository.findAllByTenantId(existTenant.getId());
        List<TenantAdminStatusBrief> tss = new LinkedList<>();
        userBriefs.forEach(item -> {
            TenantAdminStatusBrief ts = new TenantAdminStatusBrief(item.getTenantId(), item.getId(), item.isActivated());
            tss.add(ts);
        });

        tasRepository.deleteAllByTenantId(existTenant.getId());
        tasRepository.saveAll(tss);
        ssoClient.innerInactiveUsersByTenant(existTenant.getId());
        ssoClient.innerUpdateTenant(existTenant);
        userRepository.inActiveUserByTenant(0, existTenant.getId());
        tenantRepository.save(existTenant);
    }

    @Override
    public Set<Integer> searchAreaCodes() {
        return enumCountryService.findAll().stream().filter(o -> o.getValue() != null).map(o -> Integer.parseInt(o.getValue())).collect(Collectors.toSet());
    }

    @Override
    @Transactional
    public Object formatHistoryData() {
        Instant start = Instant.now();
        log.info("history tenants format update start : {}", start);
        int page = 0;
        int size = 1000;
        long total = 0L;
        while (true) {
            Pageable pageable = PageRequest.of(page, size);
            Page<Tenant> tenantPage = tenantRepository.findAll(pageable);
            List<Tenant> tenantList = tenantPage.getContent();
            Map<Long, TenantUserVM> tenantUserVMMap = searchEntityList(new HashMap<>(), getFindTenantNameSql(), TenantUserVM.class).
                    stream().collect(Collectors.toMap(TenantUserVM::getTenantId, TenantUserVM -> TenantUserVM));

            log.info("history tenants count = {}, size = {}", page + 1, tenantList.size());
            if (CollUtil.isEmpty(tenantList)) {
                break;
            }

            tenantList.forEach(tenant -> {
                if (tenant.getExpireDate() == null) {
                    tenant.setExpireDate(Instant.now().plus(365, ChronoUnit.DAYS));
                }
                if (tenant.getUserMaxLimit() == null) {
                    if (tenant.getId().equals(4L)) {
                        tenant.setUserMaxLimit(10000);
                    } else {
                        tenant.setUserMaxLimit(100);
                    }
                }
                if (tenant.getUserType() == null) {
                    tenant.setUserType(TenantUserTypeEnum.HEADHUNTER);
                }
                if (tenant.getResetDayOfMonth() == null) {
                    tenant.setResetDayOfMonth(1);
                }
                if (tenantUserVMMap.containsKey(tenant.getId())) {
                    TenantUserVM tenantUserVM = tenantUserVMMap.get(tenant.getId());
                    tenant.setContactFirstName(tenantUserVM.getFirstName());
                    tenant.setContactLastName(tenantUserVM.getLastName());
                    tenant.setContactName(CommonUtils.formatFullName(tenantUserVM.getFirstName(), tenantUserVM.getLastName()));
                    tenant.setTenantEmail(tenantUserVM.getEmail());
                }
            });

            tenantRepository.saveAll(tenantList);
            page++;
        }

        Instant end = Instant.now();
        log.info("history tenants format update end : {}", end);
        Map<String, Object> result = new HashMap<>();
        result.put("start", start);
        result.put("end", end);
        result.put("total", total);
        return result;
    }

    @Override
    public List<TenantDetailVO> getAllTenant(Integer status) {
        List<Tenant> tenantList = tenantRepository.findAllByStatus(status);
        return tenantList.stream().map(TenantDetailVO::fromTenant).collect(Collectors.toList());
    }

    @Override
    public List<OnlineTenantVO> getAllOnlineUsersSecondsInAndGroupByTenant(Integer seconds) {
        final ConcurrentMap<String, Long> tenantToCount = this.getValidUsersByTime(redisService.getAllOnlineUsers(), seconds)
                .collect(Collectors.groupingByConcurrent(k -> k.split(":")[2], Collectors.counting()));
        return tenantRepository.findAllSimpleTenant().stream()
                .map(t -> t.setOnlineUserCount(tenantToCount.getOrDefault(t.getTenantId().toString(), 0L)))
                .sorted((t1, t2) -> t2.getOnlineUserCount().compareTo(t1.getOnlineUserCount())).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenantCommonDTO update(Long id, TenantCommonDTO dto) {
        Tenant t = tenantRepository.findById(id).orElseThrow(() -> new NotFoundException("This tenant does not exist"));
        if (!t.getName().equals(dto.getName())) {
            validateTenantName(dto.getName());
        }

        Integer originStatus = t.getStatus();

        if (dto.getUpdateMonthlyCredit() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_UPDATETENANT_MONEYCREDITNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), managementApiPromptProperties.getManagementService()));
        }

        Integer nextMonthTotal = userRepository.findTotalAssignNextMonthCreditByTenant(id, DateUtil.lastDayOfCurrentMonth());
        Integer effectMonthTotal = userRepository.findTotalNextMonthEffectCreditByTenant(id, DateUtil.lastDayOfCurrentMonth());
        nextMonthTotal = nextMonthTotal == null ? 0 : nextMonthTotal;
        effectMonthTotal = effectMonthTotal == null ? 0 : effectMonthTotal;
        int total = nextMonthTotal + effectMonthTotal;
        if (dto.getUpdateMonthlyCredit() < total) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_UPDATETENANT_MONEYCREDITLESSTOTAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(total), managementApiPromptProperties.getManagementService()));
        }

        Integer totalCredit = userRepository.findTotalUsedCreditByTenant(id, 1);
        if (totalCredit != null && totalCredit > dto.getBulkCredit()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_UPDATETENANT_BULKCREDITLESSTOTAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(totalCredit), managementApiPromptProperties.getManagementService()));
        }

        ServiceUtils.myCopyProperties(dto, t);
        if (dto.getAddress() != null || dto.getCityId() != null) {
            TenantAddressBrief address = new TenantAddressBrief();
            address.setAddress(dto.getAddress());
            address.setCityId(dto.getCityId());
            Long addressId = tenantAddressRepository.save(address).getId();
            t.setAddressId(addressId);
        } else {
            if (t.getAddressId() != null && tenantAddressRepository.existsById(t.getAddressId())) {
                tenantAddressRepository.deleteById(t.getAddressId());
            }
            t.setAddressId(null);
        }

        t.setWebsite(dto.getWebsite());
        t.setTenantEmail(dto.getTenantEmail());
        t.setTenantPhone(dto.getTenantPhone());
        t.setFoundedDate(dto.getFoundedDate());
        t.setStaffSizeType(dto.getStaffSizeType());
        t.setDescription(dto.getDescription());

        tenantRepository.save(t);
        ssoClient.innerUpdateTenant(t);

        if (!originStatus.equals(dto.getStatus())) {
            if (Constants.INACTIVE.equals(dto.getStatus())) {
                //save user current status
                List<User> userBriefs = userRepository.findAllByTenantId(id);
                List<TenantAdminStatusBrief> tss = new LinkedList<>();
                userBriefs.stream().forEach(item -> {
                    TenantAdminStatusBrief ts = new TenantAdminStatusBrief(item.getTenantId(), item.getId(), item.isActivated());
                    tss.add(ts);
                });

                tasRepository.deleteAllByTenantId(id);
                tasRepository.saveAll(tss);
                ssoClient.innerInactiveUsersByTenant(t.getId());
            } else {
                //recover user status
                List<TenantAdminStatusBrief> tas = tasRepository.findByTenantId(id);
                Map<Long, TenantAdminStatusBrief> tasMap = tas.stream().collect(Collectors.toMap(TenantAdminStatusBrief::getUserId, TenantAdminStatusBrief -> TenantAdminStatusBrief));
                List<User> userList = userRepository.findAllByTenantId(id);
                List<String> excludeUserEmails = new ArrayList<>();
                userList.forEach(user -> {
                    if (tasMap.containsKey(user.getId()) && !tasMap.get(user.getId()).isActivated()) {
                        excludeUserEmails.add(user.getEmail());
                    }
                });
                ssoClient.innerActiveUsersByTenant(t.getId(), excludeUserEmails);
            }
        }
        return dto;
    }

    @Override
    public TenantPublicVO queryPublicTenant(Long id) {
        Tenant tenant = tenantRepository.findById(id).orElseThrow(() -> new NotFoundException("This tenant does not exist"));
        return TenantPublicVO.fromTenant(tenant);
    }

    @Override
    public TenantAddressBrief queryTenantAddress(Long id) {
        return tenantAddressRepository.findById(id).orElse(null);
    }

    @Override
    public List<Tenant> searchTenantListByIds(List<Long> ids) {
        return tenantRepository.findAllById(ids);
    }

    private Stream<String> getValidUsersByTime(List<String> keys, Integer seconds) {
        return keys.stream().filter(key -> {
            final String value = redisService.get(key);
            if (StringUtils.isEmpty(value)) {
                return Boolean.FALSE;
            }
            final String time = value.split(";")[0];
            LocalDateTime utcTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z));
            return utcTime.plusSeconds(seconds).isAfter(LocalDateTime.now(ZoneOffset.UTC));
        });
    }

    private void validateTenantName(String name) {
        Tenant t = tenantRepository.findByName(name);

        if (t != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_VALIDATETENANTNAME_TENANTALREADYEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), managementApiPromptProperties.getManagementService()));
        }
    }

    private void validateTenantEmail(TenantDTO tenantDTO) {
        boolean emailExists = ssoClient.innerCheckUserEmailExists(tenantDTO.getTenantEmail());
        if (emailExists) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.TENANT_VALIDATETENANTNAME_EMAILALREADYEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), managementApiPromptProperties.getManagementService()));
        }
    }

    private TenantDetailVO toVo(Tenant tenant) {
        TenantDetailVO tenantDetailVO = TenantDetailVO.fromTenant(tenant);

        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, Arrays.asList(tenant.getId()));

        Map<Long, Integer> usedMonthlyCreditMap = searchEntityList(paramMap, getQueryTenantUsedMonthlyCreditSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> usedBulkCreditMap = searchEntityList(paramMap, getQueryTenantUsedBulkCreditSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> userCountMap = searchEntityList(paramMap, getQueryTenantUserCountSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> inActiveUserUsedMonthlyCreditMap = searchEntityList(paramMap, getQueryTenantInActiveUserUsedMonthlyCreditSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> inActiveUserUsedBulkCreditMap = searchEntityList(paramMap, getQueryTenantInActiveUserUsedBulkCreditSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));

        tenantDetailVO.setUsedMonthlyCredit(usedMonthlyCreditMap.getOrDefault(tenant.getId(), 0) + inActiveUserUsedMonthlyCreditMap.getOrDefault(tenant.getId(), 0));
        tenantDetailVO.setUsedBulkCredit(usedBulkCreditMap.getOrDefault(tenant.getId(), 0) + inActiveUserUsedBulkCreditMap.getOrDefault(tenant.getId(), 0));
        tenantDetailVO.setUserAmount(userCountMap.get(tenant.getId()));

        return tenantDetailVO;
    }

    @Override
    public List<TenantPageVO> toVo(List<Tenant> tenantList) {
        if (CollUtil.isEmpty(tenantList)) {
            return new ArrayList<>();
        }

        List<TenantPageVO> resulList = new ArrayList<>(16);

        List<Long> tenantIds = tenantList.stream().map(Tenant::getId).distinct().collect(Collectors.toList());
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, tenantIds);

        Map<Long, Integer> jobCountMap = searchEntityList(paramMap, getQueryTenantJobCountSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> talentCountMap = searchEntityList(paramMap, getQueryTenantTalentCountSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> companyCountMap = searchEntityList(paramMap, getQueryTenantCompanyCountSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> usedMonthlyCreditMap = searchEntityList(paramMap, getQueryTenantUsedMonthlyCreditSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> usedBulkCreditMap = searchEntityList(paramMap, getQueryTenantUsedBulkCreditSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> userCountMap = searchEntityList(paramMap, getQueryTenantUserCountSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> inActiveUserUsedMonthlyCreditMap = searchEntityList(paramMap, getQueryTenantInActiveUserUsedMonthlyCreditSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));
        Map<Long, Integer> inActiveUserUsedBulkCreditMap = searchEntityList(paramMap, getQueryTenantInActiveUserUsedBulkCreditSql(), TenantCountVM.class).stream().collect(Collectors.toMap(TenantCountVM::getTenantId, item -> Optional.ofNullable(item.getCount()).orElse(0)));


        tenantList.forEach(tenant -> {
            TenantPageVO tenantPageVO = new TenantPageVO();
            ServiceUtils.myCopyProperties(tenant, tenantPageVO);
            tenantPageVO.setActiveJobCount(jobCountMap.get(tenant.getId()));
            tenantPageVO.setTalentCount(talentCountMap.get(tenant.getId()));
            tenantPageVO.setCompanyCount(companyCountMap.get(tenant.getId()));
            tenantPageVO.setUsedMonthlyCredit(usedMonthlyCreditMap.getOrDefault(tenant.getId(), 0) + inActiveUserUsedMonthlyCreditMap.getOrDefault(tenant.getId(), 0));
            tenantPageVO.setUsedBulkCredit(usedBulkCreditMap.getOrDefault(tenant.getId(), 0) + inActiveUserUsedBulkCreditMap.getOrDefault(tenant.getId(), 0));
            tenantPageVO.setUserAmount(userCountMap.get(tenant.getId()));
            resulList.add(tenantPageVO);
        });

        return resulList;
    }

    private Integer findTotalAssignNextMonthCreditByTenant(Map<Integer, Object> paramMap) {
        List<TenantCountVM> tenantCountVMList = searchEntityList(paramMap, getFindTotalAssignNextMonthCreditByTenantSql(), TenantCountVM.class);
        return tenantCountVMList.get(0) == null ? null : tenantCountVMList.get(0).getCount();
    }

    private Integer findTotalNextMonthEffectCreditByTenant(Map<Integer, Object> paramMap) {
        List<TenantCountVM> tenantCountVMList = searchEntityList(paramMap, getFindTotalNextMonthEffectCreditByTenantSql(), TenantCountVM.class);
        return tenantCountVMList.get(0) == null ? null : tenantCountVMList.get(0).getCount();
    }

    private Integer findTotalUsedCreditByTenant(Map<Integer, Object> paramMap) {
        List<TenantCountVM> tenantCountVMList = searchEntityList(paramMap, getFindTotalUsedCreditByTenantSql(), TenantCountVM.class);
        return tenantCountVMList.get(0) == null ? null : tenantCountVMList.get(0).getCount();
    }

    private String getQueryTenantJobCountSql() {
        return "SELECT COUNT(1) count, tenant_id FROM job WHERE tenant_id IN ?1 AND `status` = 0 GROUP BY tenant_id";
    }

    private String getQueryTenantTalentCountSql() {
        return "SELECT COUNT(1) count, tenant_id FROM talent WHERE tenant_id IN ?1 GROUP BY tenant_id";
    }

    private String getQueryTenantCompanyCountSql() {
        return "SELECT COUNT(1) count, tenant_id FROM company c WHERE tenant_id IN ?1 AND EXISTS (SELECT 1 FROM account_business csl WHERE csl.company_id = c.id) GROUP BY tenant_id";
    }

    private String getQueryTenantUsedMonthlyCreditSql() {
        return "SELECT SUM(ua.amount) count, u.tenant_id FROM user_account ua INNER JOIN `user` u ON ua.user_id = u.id INNER JOIN tenant t ON t.id = u.tenant_id WHERE u.tenant_id IN ?1 AND t.`status` = 1 AND u.activated = 1 AND u.tenant_id IS NOT NULL GROUP BY u.tenant_id UNION\n" +
                "SELECT SUM(ua.amount) count, u.tenant_id FROM user_account ua INNER JOIN `user` u ON ua.user_id = u.id INNER JOIN tenant t ON t.id = u.tenant_id WHERE u.tenant_id IN ?1 AND t.`status` = 0 AND u.tenant_id IS NOT NULL GROUP BY u.tenant_id";
    }

    private String getQueryTenantUsedBulkCreditSql() {
        return "SELECT SUM(ua.bulk_credit) count, u.tenant_id FROM user_account ua INNER JOIN `user` u ON ua.user_id = u.id INNER JOIN tenant t ON t.id = u.tenant_id WHERE u.tenant_id IN ?1 AND t.`status` = 1 AND u.activated = 1 AND u.tenant_id IS NOT NULL GROUP BY u.tenant_id UNION\n" +
                "SELECT SUM(ua.bulk_credit) count, u.tenant_id FROM user_account ua INNER JOIN `user` u ON ua.user_id = u.id INNER JOIN tenant t ON t.id = u.tenant_id WHERE u.tenant_id IN ?1 AND t.`status` = 0 AND u.tenant_id IS NOT NULL GROUP BY u.tenant_id";
    }

    private String getQueryTenantInActiveUserUsedMonthlyCreditSql() {
        return "SELECT SUM(ct.credit) count, ct.tenant_id FROM credit_transaction ct INNER JOIN `user` u ON ct.user_id = u.id INNER JOIN tenant t ON t.id = u.tenant_id WHERE t.`status` = 1 AND ct.tenant_id IN ?1 AND u.activated = 0 AND ct.created_date >= '" + DateUtil.firstDayOfCurrentMonth() + "' AND ct.created_date < '" + DateUtil.firstDayOfNextMonth() + "' AND ct.credit_type= 0 GROUP BY ct.tenant_id";
    }

    private String getQueryTenantInActiveUserUsedBulkCreditSql() {
        return "SELECT SUM(ct.credit) count, ct.tenant_id FROM credit_transaction ct INNER JOIN `user` u ON ct.user_id = u.id INNER JOIN tenant t ON t.id = u.tenant_id WHERE t.`status` = 1 AND u.activated = 0 AND ct.credit_type = 1 AND ct.tenant_id IN ?1 GROUP BY ct.tenant_id";
    }

    private String getQueryTenantUserCountSql() {
        return "SELECT COUNT(1) count, tenant_id FROM `user` WHERE tenant_id IN ?1 GROUP BY tenant_id;";
    }

    private String getFindTotalAssignNextMonthCreditByTenantSql() {
        return "select SUM(ua.effect_credit) count, u.tenant_id from user_account ua left join user u on u.id = ua.user_id where u.tenant_id =?1 and u.activated=1 and ua.effect_credit is not null and ua.expire_date = ?2";
    }

    private String getFindTotalNextMonthEffectCreditByTenantSql() {
        return "select SUM(ua.amount) count, u.tenant_id from user_account ua left join user u on u.id = ua.user_id where u.tenant_id =?1 and u.activated=1 and ua.effect_credit is null and ua.amount is not null and ua.expire_date = ?2";
    }

    private String getFindTotalUsedCreditByTenantSql() {
        return "select SUM(ct.credit) count, ct.tenant_id from credit_transaction ct  where ct.tenant_id = ?1 and ct.credit_type = ?2";
    }

    private String getFindTenantNameSql() {
        return "select u.tenant_id, u.first_name, u.last_name, u.email from user u\n" +
                "          left join user_role ur on u.id=ur.user_id\n" +
                "          left join role r on r.id=ur.role_id\n" +
                "          where r.name ='ROLE_TENANT_ADMIN' group by u.tenant_id";
    }

    private <T> List<T> searchEntityList(Map<Integer, Object> paramMap, String selectSql, Class<T> clazz) {
        List<T> entityList = searchData(selectSql, clazz, paramMap);
        if (CollUtil.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return entityList;
    }

    /**
     * Automatically update tenants/users credit at 00:01:00AMUTC on first day of every month.
     */
    @Scheduled(cron = "0 1 0 1 * ?", zone = "UTC")
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantAndActiveUserCredit() {
        LocalDate today = LocalDate.now(ZoneId.of("UTC"));
        LocalDate lastDayOfCurrentMonth = today.with(TemporalAdjusters.lastDayOfMonth());
        log.info("[UserAccountService: updateTenantAndActiveUserCredit] automatically update credits for all tenants and active users credit on {}, set expired date to {}", DATE_FORMATTER.format(today), DATE_FORMATTER.format(lastDayOfCurrentMonth));

        accountRepository.updateActiveUserMonthlyCredit(DATE_FORMATTER.format(lastDayOfCurrentMonth));
        //update all tenant credit
        tenantRepository.updateTenantMonthlyCredit();
    }

    /**
     * Update the tenant status that is 24 hours overdue at 0:1 every day.
     */
    @Scheduled(cron = "0 1 0 * * ?", zone = "UTC")
    @Transactional(rollbackFor = Exception.class)
    public void updateTenantStatus() {
        Instant time = Instant.now().minus(Duration.ofHours(24));
        List<Tenant> tenantList = tenantRepository.findExpireTenants(time);
        log.info("[UserService]  automatically update tenant status, time: {}, tenantList: {}", time, tenantList.stream().map(Tenant::getId).collect(Collectors.toList()));
        tenantList.forEach(tenant -> {
            try {
                updateTenantStatusToInActive(tenant.getId());
            } catch (Exception e) {
                log.error("[UserService] the scheduled task failed to expire the tenant. id: {}, message: {}", tenant.getId(), e.getMessage());
            }
        });
    }

}
