package com.altomni.apn.management.service.announcement.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.calendar.CalendarEventDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnParamDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.repository.xxljob.XxlJobRelationRepository;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.management.domain.announcement.PlatformLaunchAnnouncement;
import com.altomni.apn.management.domain.announcement.PlatformLaunchAnnouncementUserRelation;
import com.altomni.apn.management.domain.enumeration.AnnouncementStatus;
import com.altomni.apn.management.domain.enumeration.LaunchPlatform;
import com.altomni.apn.management.dto.announcement.PlatformLaunchAnnouncementClosedDTO;
import com.altomni.apn.management.dto.announcement.PlatformLaunchAnnouncementDTO;
import com.altomni.apn.management.repository.announcement.PlatformLaunchAnnouncementRepository;
import com.altomni.apn.management.repository.announcement.PlatformLaunchAnnouncementUserRelationRepository;
import com.altomni.apn.management.service.announcement.PlatformLaunchAnnouncementService;
import com.altomni.apn.management.service.xxljob.XxlJobClientService;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementDetailVO;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
@Transactional
public class PlatformLaunchAnnouncementServiceImpl implements PlatformLaunchAnnouncementService {

    @Resource
    private XxlJobClientService xxlJobClientService;

    @Resource
    private PlatformLaunchAnnouncementRepository platformLaunchAnnouncementRepository;

    @Resource
    private PlatformLaunchAnnouncementUserRelationRepository platformLaunchAnnouncementUserRelationRepository;

    @Resource
    private XxlJobRelationRepository xxlJobRelationRepository;


    private void addPlatformLaunchAnnouncementXxlJobRelation(PlatformLaunchAnnouncementVO platformLaunchAnnouncementVO) {
        XxlJobApnDTO xxlJobApnDTO = new XxlJobApnDTO();
        XxlJobApnParamDTO xxlJobApnParamDTO = new XxlJobApnParamDTO();
        xxlJobApnParamDTO.setXxlJobType(XxlJobRelationTypeEnum.APN_ANNOUNCEMENT_MESSAGE);
        xxlJobApnParamDTO.setReferenceId(platformLaunchAnnouncementVO.getId());
        xxlJobApnParamDTO.setUserId(SecurityUtils.getUserId());
        xxlJobApnParamDTO.setTenantId(-1L);
        xxlJobApnParamDTO.setSendTime(platformLaunchAnnouncementVO.getReminderTime());
        xxlJobApnParamDTO.setToken(SecurityUtils.getCurrentUserToken());
        xxlJobApnDTO.setXxlJobApnParamDTO(xxlJobApnParamDTO);
        Map<String, Object> paramMap = new JSONObject(xxlJobApnParamDTO);
        paramMap.put("platformAnnouncementId", platformLaunchAnnouncementVO.getId());
        paramMap.put("announcementEN", platformLaunchAnnouncementVO.getPlainContentEn());
        paramMap.put("announcementCN", platformLaunchAnnouncementVO.getPlainContentCn());
        xxlJobApnParamDTO.setXxlJobParam(paramMap);
        xxlJobApnDTO.setJobDesc(platformLaunchAnnouncementVO.getUpgradeLogTitleEn());
        xxlJobClientService.createXxlJob(xxlJobApnDTO);
    }

    @Override
    public PlatformLaunchAnnouncementVO createPlatformLaunchAnnouncement(PlatformLaunchAnnouncementDTO platformLaunchAnnouncementDTO) {
        checkDuplicateData(platformLaunchAnnouncementDTO.getPlatform(), platformLaunchAnnouncementDTO.getStatus(), platformLaunchAnnouncementDTO.getLaunchEndTime(), platformLaunchAnnouncementDTO.getLaunchEndTime(), null);
        PlatformLaunchAnnouncement platformLaunchAnnouncement = PlatformLaunchAnnouncement.fromPlatformLaunchAnnouncementDTO(platformLaunchAnnouncementDTO);
        platformLaunchAnnouncement = platformLaunchAnnouncementRepository.save(platformLaunchAnnouncement);
        PlatformLaunchAnnouncementVO platformLaunchAnnouncementVO = PlatformLaunchAnnouncement.toPlatformLaunchAnnouncementVO(platformLaunchAnnouncement);
        if(platformLaunchAnnouncementVO.getStatus().equals(AnnouncementStatus.LAUNCHED)) addPlatformLaunchAnnouncementXxlJobRelation(platformLaunchAnnouncementVO);
        return platformLaunchAnnouncementVO;
    }

    @Override
    public PlatformLaunchAnnouncementVO updatePlatformLaunchAnnouncement(Long id, PlatformLaunchAnnouncementDTO platformLaunchAnnouncementDTO) {
        PlatformLaunchAnnouncement platformLaunchAnnouncement = queryPlatformLaunchAnnouncementById(id);
        //如果系统已经发布，无法编辑之前的已发布公告
        if (AnnouncementStatus.LAUNCHED.equals(platformLaunchAnnouncement.getStatus()) && Instant.now().isAfter(platformLaunchAnnouncement.getLaunchStartTime())) {
            return PlatformLaunchAnnouncement.toPlatformLaunchAnnouncementVO(platformLaunchAnnouncement);
        }
        checkDuplicateData(platformLaunchAnnouncementDTO.getPlatform(), platformLaunchAnnouncementDTO.getStatus(), platformLaunchAnnouncementDTO.getLaunchEndTime(), platformLaunchAnnouncementDTO.getLaunchEndTime(), id);
        ServiceUtils.myCopyProperties(platformLaunchAnnouncementDTO, platformLaunchAnnouncement);
        platformLaunchAnnouncement = platformLaunchAnnouncementRepository.save(platformLaunchAnnouncement);
        PlatformLaunchAnnouncementVO platformLaunchAnnouncementVO = PlatformLaunchAnnouncement.toPlatformLaunchAnnouncementVO(platformLaunchAnnouncement);
        //remove any possible exist xxljob lark send platform notification
        List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceId(XxlJobRelationTypeEnum.APN_ANNOUNCEMENT_MESSAGE, id);
        if(xxlJobRelationList != null || !xxlJobRelationList.isEmpty()) {
            xxlJobClientService.deleteXxlJobIdList(xxlJobRelationList.stream().map(XxlJobRelation::getXxlJobId).toList());
        }
        if(platformLaunchAnnouncementVO.getStatus().equals(AnnouncementStatus.LAUNCHED)) {

            addPlatformLaunchAnnouncementXxlJobRelation(platformLaunchAnnouncementVO);
        }
        return platformLaunchAnnouncementVO;
    }

    @Override
    public Page<PlatformLaunchAnnouncement> searchPlatformLaunchAnnouncement(Pageable pageable) {
        return platformLaunchAnnouncementRepository.findAll(pageable);
    }

    @Override
    public PlatformLaunchAnnouncementDetailVO queryPlatformLaunchAnnouncement(LanguageEnum language, LaunchPlatform platform, String email) {
        //查询当前时间内平台已发布的公告，取最新的给前端
        List<PlatformLaunchAnnouncement> platformLaunchAnnouncementList = platformLaunchAnnouncementRepository.findAllByPlatformAndStatus(platform, AnnouncementStatus.LAUNCHED, Instant.now());
        if (CollUtil.isEmpty(platformLaunchAnnouncementList) || checkClosedPlatformLaunchAnnouncement(platformLaunchAnnouncementList.get(0), email)) {
            return new PlatformLaunchAnnouncementDetailVO();
        }
        return PlatformLaunchAnnouncement.toPlatformLaunchAnnouncementDetailVO(platformLaunchAnnouncementList.get(0), language);
    }

    /**
     * 标记用户已读公告
     * @param platformLaunchAnnouncementClosedDTO
     */
    @Override
    public void closedPlatformLaunchAnnouncement(PlatformLaunchAnnouncementClosedDTO platformLaunchAnnouncementClosedDTO) {
        PlatformLaunchAnnouncementUserRelation platformLaunchAnnouncementUserRelation = platformLaunchAnnouncementUserRelationRepository.findFirstByplatformLaunchAnnouncementIdAndEmail(platformLaunchAnnouncementClosedDTO.getId(), platformLaunchAnnouncementClosedDTO.getEmail());
        if (ObjectUtil.isNotEmpty(platformLaunchAnnouncementUserRelation)) {
            return;
        }
        platformLaunchAnnouncementUserRelationRepository.save(new PlatformLaunchAnnouncementUserRelation(platformLaunchAnnouncementClosedDTO.getId(), platformLaunchAnnouncementClosedDTO.getEmail()));
    }

    private boolean checkClosedPlatformLaunchAnnouncement(PlatformLaunchAnnouncement platformLaunchAnnouncement, String email) {
        if (ObjectUtil.isEmpty(email)) {
            return false;
        }
        PlatformLaunchAnnouncementUserRelation platformLaunchAnnouncementUserRelation = platformLaunchAnnouncementUserRelationRepository.findFirstByplatformLaunchAnnouncementIdAndEmail(platformLaunchAnnouncement.getId(), email);
        return ObjectUtil.isNotEmpty(platformLaunchAnnouncementUserRelation);
    }

    private PlatformLaunchAnnouncement queryPlatformLaunchAnnouncementById(Long id) {
        return platformLaunchAnnouncementRepository.findById(id).orElseThrow(() -> new NotFoundException("Announcements does not exist"));
    }

    //查重当前时间段内已有的公告
    private void checkDuplicateData(LaunchPlatform platform, AnnouncementStatus status, Instant reminderTime, Instant launchEndTime, Long id) {
        if (!AnnouncementStatus.LAUNCHED.equals(status)) {
            return;
        }
        // 查询状态为 LAUNCHED 的公告，检查是否有与传入时间区间重叠的记录
        List<PlatformLaunchAnnouncement> platformLaunchAnnouncementList = platformLaunchAnnouncementRepository
                .findAllByPlatformAndStatusAndTimeRangeOverlap(platform, status, reminderTime, launchEndTime);

        if (CollUtil.isNotEmpty(platformLaunchAnnouncementList) && platformLaunchAnnouncementList.stream()
                .noneMatch(announcement -> Objects.equals(announcement.getId(), id))) {
            throw new CustomParameterizedException("Announcements have been launched during this time period.");
        }
    }


}
