package com.altomni.apn.management.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class ProductErrorInfoTypeConverter extends AbstractAttributeConverter<ProductErrorInfoType, Integer> {
    public ProductErrorInfoTypeConverter() {
        super(ProductErrorInfoType::toDbValue, ProductErrorInfoType::fromDbValue);
    }
}
