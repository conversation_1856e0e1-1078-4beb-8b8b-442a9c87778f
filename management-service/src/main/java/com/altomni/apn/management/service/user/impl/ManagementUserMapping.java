package com.altomni.apn.management.service.user.impl;

import com.altomni.apn.common.domain.user.AdminManagementUser;
import com.altomni.apn.management.repository.user.AdminManagementUserRepository;
import com.ipg.resourceserver.user.SsoOidcUser;
import com.ipg.resourceserver.user.UserMapping;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ManagementUserMapping implements UserMapping {

    private final AdminManagementUserRepository adminManagementUserRepository;

    @Override
    public SsoOidcUser mapping(OidcUser originalUser) {

        String email = originalUser.getEmail();
        log.info("Mapping user with email: {}", email);
        AdminManagementUser adminUser = adminManagementUserRepository.findByEmail(email);
        adminUser.getAuthorities();
        adminUser.setOidcUser(originalUser);
        return adminUser;
    }
}
