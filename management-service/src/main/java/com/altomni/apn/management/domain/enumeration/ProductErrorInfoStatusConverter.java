package com.altomni.apn.management.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class ProductErrorInfoStatusConverter extends AbstractAttributeConverter<ProductErrorInfoStatus, Integer> {
    public ProductErrorInfoStatusConverter() {
        super(ProductErrorInfoStatus::toDbValue, ProductErrorInfoStatus::fromDbValue);
    }
}
