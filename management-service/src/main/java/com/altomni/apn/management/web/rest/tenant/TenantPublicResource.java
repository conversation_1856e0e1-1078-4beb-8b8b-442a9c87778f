package com.altomni.apn.management.web.rest.tenant;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.talent.TenantPublicVO;
import com.altomni.apn.management.domain.tenant.TenantAddressBrief;
import com.altomni.apn.management.service.tenant.TenantService;
import com.altomni.apn.management.vo.tenant.TenantDetailVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = {"ManagementTenant public tenant Controller"})
@RestController
@RequestMapping("/api/v3/public/tenants")
public class TenantPublicResource {

    @Resource
    private TenantService tenantService;

    /**
     *
     * @param id
     * @return the ResponseEntity with status 200 (OK) and with body the tenant details.
     */
    @GetMapping("/{id}")
    @NoRepeatSubmit
    @ApiOperation(value = "Query a tenant details", tags = {"APN V3"})
    public ResponseEntity<TenantPublicVO> queryTenant(@PathVariable("id") Long id) {
        log.info("[APN Management: Tenant @{}] REST request to query a tenant details : {}", SecurityUtils.getUserId(), id);
        TenantPublicVO result = tenantService.queryPublicTenant(id);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     *
     * @param
     * @return the ResponseEntity with status 200 (OK) and with body the tenant details.
     */
    @GetMapping("/list")
    @NoRepeatSubmit
    @ApiOperation(value = "Get all tenant details", tags = {"APN V3"})
    public ResponseEntity<List<TenantDetailVO>> getAllTenant(@RequestParam("status") Integer status) {
        log.info("[APN Management: Tenant @{}] REST request to get all tenant details.", SecurityUtils.getUserId());
        List<TenantDetailVO> result = tenantService.getAllTenant(status);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/address/{id}")
    @NoRepeatSubmit
    @ApiOperation(value = "Query a tenant address", tags = {"APN V3"})
    public ResponseEntity<TenantAddressBrief> queryTenantAddress(@PathVariable("id") Long id) {
        log.info("[APN Management: Tenant @{}] REST request to query a tenant address : {}", SecurityUtils.getUserId(), id);
        TenantAddressBrief result = tenantService.queryTenantAddress(id);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
