package com.altomni.apn.management.repository.tenant;

import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.management.vo.tenant.OnlineTenantVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * Spring Data JPA repository for the Tenant entity.
 */
@Repository
public interface TenantRepository extends JpaRepository<Tenant, Long> {

    Tenant findByName(String name);

    Page<Tenant> findAllByUserType(TenantUserTypeEnum type, Pageable pageable);

    @Modifying
    @Query(value = "UPDATE Tenant t SET t.monthlyCredit = t.updateMonthlyCredit")
    int updateTenantMonthlyCredit();

    @Query(value = "SELECT * FROM tenant WHERE expire_date <= ?1 AND status = 1", nativeQuery = true)
    List<Tenant> findExpireTenants(Instant time);

    List<Tenant> findAllByStatus(Integer status);

    @Query(value = "select new com.altomni.apn.management.vo.tenant.OnlineTenantVO(t.id, t.name) from Tenant t ")
    List<OnlineTenantVO> findAllSimpleTenant();

}
