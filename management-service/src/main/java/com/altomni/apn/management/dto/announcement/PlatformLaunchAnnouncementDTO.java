package com.altomni.apn.management.dto.announcement;

import com.altomni.apn.management.domain.enumeration.*;
import com.altomni.apn.management.valid.AnnouncementLaunchTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
@AnnouncementLaunchTime
public class PlatformLaunchAnnouncementDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "platform")
    @Convert(converter = LaunchPlatformConverter.class)
    @NotNull
    private LaunchPlatform platform;

    @ApiModelProperty(value = "launchStartTime")
    @NotNull
    private Instant launchStartTime;

    @ApiModelProperty(value = "launchEndTime")
    @NotNull
    private Instant launchEndTime;

    @ApiModelProperty(value = "reminderTime")
    @NotNull
    private Instant reminderTime;

    @ApiModelProperty(value = "contentCn")
    @NotEmpty
    private String contentCn;

    @ApiModelProperty(value = "contentEn")
    @NotEmpty
    private String contentEn;

    @ApiModelProperty(value = "upgradeLogLinkCn")
    private String upgradeLogLinkCn;

    @ApiModelProperty(value = "upgradeLogLinkEn")
    private String upgradeLogLinkEn;

    @ApiModelProperty(value = "upgradeLogTitleCn")
    private String upgradeLogTitleCn;

    @ApiModelProperty(value = "upgradeLogTitleEn")
    private String upgradeLogTitleEn;

    @ApiModelProperty(value = "status")
    @Convert(converter = AnnouncementStatusConverter.class)
    @NotNull
    private AnnouncementStatus status;

}
