package com.altomni.apn.management.service.application;

import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.nodepagesection.FieldConfigDTO;
import com.altomni.apn.common.dto.application.nodepagesection.RecruitmentProcessNodePageSectionDTO;
import com.altomni.apn.common.dto.application.recruitmentprocess.*;
import com.altomni.apn.common.dto.application.recruitmentprocessnode.RecruitmentProcessNodeDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Service Interface for managing.
 */
public interface ApplicationService {

    Page<RecruitmentProcessVO> findAllByTenantId(Long tenantId, ActiveStatus status, Pageable pageable);

    RecruitmentProcessVO create(RecruitmentProcessVO recruitmentProcess);

    Void delete(Long id);

    RecruitmentProcessVO updateStatus(Long id, StatusDTO status);

    RecruitmentProcessVO update(Long id, RecruitmentProcessVO recruitmentProcessVO);

    RecruitmentProcessVO findOne(Long id);

    List<RecruitmentProcessVO> config(RecruitmentProcessConfigVO configVO);

    RecruitmentProcessTalentAndJobStats getInProgressJobAndApplicationCountByRecruitmentProcessId(Long recruitmentProcessId);

//    void downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(Long recruitmentProcessId, HttpServletResponse response);


    List<RecruitmentProcessNodeVO> findAllNodesByRecruitmentProcessId(Long recruitmentProcessId);

    RecruitmentProcessNodeDTO updateNodeByRecruitmentProcessIdAndNodeId(Long recruitmentProcessId, Long nodeId, RecruitmentProcessNodeDTO recruitmentProcessNodeDTO);


    Object createNodePageSection(Long recruitmentProcessId, NodeType nodeType, List<FieldConfigDTO> fieldConfigDTOS);

    Object updateNodePageSection(Long recruitmentProcessId, NodeType nodeType, List<FieldConfigDTO> fieldConfigDTOS);

    List<Object> findGeneralRecruitmentProcessNodePageSectionsDefaultConfig();

    List<Object> findIpgRecruitmentProcessNodePageSectionsDefaultConfig(JobType jobType);

    Object findGeneralRecruitmentProcessNodePageSectionsDefaultConfigByNodeType(NodeType nodeType);

    Object findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(JobType jobType, NodeType nodeType);

    List<Object> findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(Long recruitmentProcessId);

    Object findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(Long recruitmentProcessId, NodeType nodeType);

    byte[] downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(Long recruitmentProcessId, String language);
}
