package com.altomni.apn.management.service.company;


import com.altomni.apn.common.dto.company.BriefCompanyDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Service Interface for managing.
 */
@Component
@FeignClient(value = "company-service")
public interface CompanyClient {
    @PostMapping("company/api/v3/companies/employer-company")
    ResponseEntity<BriefCompanyDTO> createEmployerTenantCompany(@RequestBody BriefCompanyDTO companyClientDTO);
}
