package com.altomni.apn.management.dto.product;

import com.altomni.apn.management.domain.enumeration.ProductErrorInfoType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class ErrorInfoUpdateDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "error note")
    private String errorProcessNote;

    @ApiModelProperty(value = "error type")
    private ProductErrorInfoType errorType;

}
