package com.altomni.apn.management.service.statistic.impl;

import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.management.repository.statistic.StatisticRepository;
import com.altomni.apn.management.service.statistic.StatisticService;
import com.altomni.apn.management.vo.statistic.StatisticVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("statisticsService")
public class StatisticServiceImpl implements StatisticService {

    @Resource
    private StatisticRepository statisticRepository;

    @Override
    public List<StatisticVo> jobStatisticByType(TenantUserTypeEnum typeEnum) {
        List<StatisticVo> result;
        switch (typeEnum) {
            case EMPLOYER:
                result = statisticRepository.jobStatisticsByType(TenantUserTypeEnum.EMPLOYER);
                break;
            case HEADHUNTER:
                result = statisticRepository.jobStatisticsByType(TenantUserTypeEnum.HEADHUNTER);
                break;
            default:
                result = new ArrayList<>();
                break;
        }
        return result;
    }

    @Override
    public List<StatisticVo> talentStatisticByType(TenantUserTypeEnum typeEnum) {
        List<StatisticVo> result;
        switch (typeEnum) {
            case EMPLOYER:
                result = statisticRepository.talentStatisticsByType(TenantUserTypeEnum.EMPLOYER);
                break;
            case HEADHUNTER:
                result = statisticRepository.talentStatisticsByType(TenantUserTypeEnum.HEADHUNTER);
                break;
            default:
                result = new ArrayList<>();
                break;
        }
        return result;
    }

    @Override
    public List<StatisticVo> companyClientStatisticByType(TenantUserTypeEnum typeEnum) {
        List<StatisticVo> result = new ArrayList<>();
        if (typeEnum == TenantUserTypeEnum.HEADHUNTER) {
            result = statisticRepository.companyClientStatisticsByType(TenantUserTypeEnum.HEADHUNTER);
        }
        return result;
    }

}
