package com.altomni.apn.management.config;

import com.altomni.apn.management.interceptor.SecurityManagementInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class WebSecurityConfig implements WebMvcConfigurer {

    private final SecurityManagementInterceptor securityManagementInterceptor;


    private static final String[] PUBLIC_ENDPOINTS = new String[]{
            "/api/v3/public/tenants/**",
            "/api/v3/admin-management/user/**",
            "/api/v3/product/errors",
            "/api/v3/tenants/phone/areaCodes",
            "/actuator/**",
            "/api/v3/users/liveness",
            "/api/v3/platform-launch-announcement/latest",
            "/api/v3/platform-launch-announcement/closed"
    };

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.oauth2ResourceServer().opaqueToken(Customizer.withDefaults());


        // 没有 token 或者 token 过期，返回 401
        http.exceptionHandling(exceptionHandling -> exceptionHandling.authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint()));

        // 无状态 session
        http.sessionManagement((session) -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        http.csrf().disable()
                .authorizeRequests()
                .requestMatchers(PUBLIC_ENDPOINTS).permitAll()
                .anyRequest().authenticated();

        return http.build();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(securityManagementInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/api/v3/public/tenants/**")
                .excludePathPatterns("/api/v3/admin-management/user/**")
                .excludePathPatterns("/api/v3/product/errors")
                .excludePathPatterns("/api/v3/tenants/phone/areaCodes")
                .excludePathPatterns("/api/v3/platform-launch-announcement/latest")
                .excludePathPatterns("/api/v3/platform-launch-announcement/closed");
    }


    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        return (web) -> web.ignoring().requestMatchers(PUBLIC_ENDPOINTS);
    }

}
