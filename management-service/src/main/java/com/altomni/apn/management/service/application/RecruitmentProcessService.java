package com.altomni.apn.management.service.application;

import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessConfigVO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessTalentAndJobStats;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.dto.application.recruitmentprocess.StatusDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * Service Interface for managing RecruitmentProcess.
 */
@Component
@FeignClient(value = "application-service")
public interface RecruitmentProcessService {

    @GetMapping("/application/api/v3/recruitment-processes/admin/tenantId/{tenantId}")
    ResponseEntity<Object> findAllByTenantId(@PathVariable("tenantId") Long tenantId, @RequestParam("status") String status, @SpringQueryMap Pageable pageable);

    @PostMapping("/application/api/v3/recruitment-processes/admin")
    ResponseEntity<RecruitmentProcessVO> create(@RequestBody RecruitmentProcessVO recruitmentProcess);

    @DeleteMapping("/application/api/v3/recruitment-processes/{id}")
    ResponseEntity<Void> delete(@PathVariable("id") Long id);

    @PutMapping("/application/api/v3/recruitment-processes/{recruitmentProcessId}/status")
    ResponseEntity<RecruitmentProcessVO> updateStatus(@PathVariable("recruitmentProcessId") Long id, @RequestBody StatusDTO status);

    @PutMapping("/application/api/v3/recruitment-processes/{recruitmentProcessId}")
    ResponseEntity<RecruitmentProcessVO> update(@PathVariable("recruitmentProcessId") Long id, @RequestBody RecruitmentProcessVO recruitmentProcessVO);

    @GetMapping("/application/api/v3/recruitment-processes/{id}")
    ResponseEntity<RecruitmentProcessVO> findOne(@PathVariable("id") Long id);

    @PostMapping("/application/api/v3/recruitment-processes/config")
    ResponseEntity<List<RecruitmentProcessVO>> config(@RequestBody RecruitmentProcessConfigVO configVO);

    @GetMapping("/application/api/v3/recruitment-processes/{recruitmentProcessId}/in-progress")
    ResponseEntity<RecruitmentProcessTalentAndJobStats> getInProgressJobAndApplicationCountByRecruitmentProcessId(@PathVariable("recruitmentProcessId") Long recruitmentProcessId);

    @GetMapping("/application/api/v3/recruitment-processes/{recruitmentProcessId}/in-progress/download")
//    ResponseEntity<Void> downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @RequestHeader HttpServletResponse response);
    ResponseEntity<byte[]> downloadInProgressJobAndApplicationCount(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @RequestParam("language") String language);

    @PostMapping("/application/api/v3/recruitment-processes/init/tenantId/{tenantId}")
    ResponseEntity<List<RecruitmentProcessVO>> initDefaultForGeneralRecruitingProcess(@PathVariable("tenantId") Long tenantId);
}
