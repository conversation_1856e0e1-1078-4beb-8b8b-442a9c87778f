package com.altomni.apn.management.service.product.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.dto.management.TenantPhoneDTO;
import com.altomni.apn.common.enumeration.enums.ManagementAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.PhoneNumberUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.management.config.env.ManagementApiPromptProperties;
import com.altomni.apn.management.domain.enumeration.ProductErrorInfoStatus;
import com.altomni.apn.management.domain.product.ProductErrorInfo;
import com.altomni.apn.management.domain.user.UserNameVM;
import com.altomni.apn.management.dto.product.ErrorInfoDTO;
import com.altomni.apn.management.dto.product.ErrorInfoUpdateDTO;
import com.altomni.apn.management.repository.product.ProductErrorInfoRepository;
import com.altomni.apn.management.repository.tenant.TenantRepository;
import com.altomni.apn.management.repository.user.UserBriefRepository;
import com.altomni.apn.management.service.product.ProductErrorInfoService;
import com.altomni.apn.management.vo.product.ErrorInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("productErrorInfoService")
@Slf4j
public class ProductErrorInfoServiceImpl implements ProductErrorInfoService {

    @Resource
    private ProductErrorInfoRepository productErrorInfoRepository;

    @Resource
    private UserBriefRepository userBriefRepository;

    @Resource
    private TenantRepository tenantRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ManagementApiPromptProperties managementApiPromptProperties;

    @Resource
    EntityManager entityManager;

    @Override
    public void createErrorInfo(ErrorInfoDTO errorInfoDTO) {
        String formatPhone = PhoneNumberUtils.formatPhoneNumber(new TenantPhoneDTO(errorInfoDTO.getCountryCode(), errorInfoDTO.getPhone()));
        ProductErrorInfo productErrorInfo = ProductErrorInfo.fromErrorInfoDTO(errorInfoDTO);
        productErrorInfo.setFormatPhone(formatPhone);
        productErrorInfo.setStatus(ProductErrorInfoStatus.UNSOLVED);
        productErrorInfo.setPermissionUserId(errorInfoDTO.getUserId());
        productErrorInfoRepository.save(productErrorInfo);
    }

    @Override
    public Page<ErrorInfoVO> searchErrorInfos(Pageable pageable) {
        Page<ProductErrorInfo> productErrorInfoPage;
        if (pageable.getSort().isSorted() && pageable.getSort().stream().anyMatch(o -> "tenantName".equals(o.getProperty()))) {
            Pageable translatePageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize());
            if (pageable.getSort().stream().anyMatch(o -> Sort.Direction.DESC.equals(o.getDirection()))) {
                productErrorInfoPage = productErrorInfoRepository.findAllOrderByTenantNameDesc(entityManager, translatePageable);
            } else {
                productErrorInfoPage = productErrorInfoRepository.findAllOrderByTenantNameAsc(entityManager, translatePageable);
            }
        } else {
            productErrorInfoPage = productErrorInfoRepository.findAll(pageable);
        }

        return new PageImpl<>(toVo(productErrorInfoPage.getContent()), pageable, productErrorInfoPage.getTotalElements());
    }

    @Override
    public ErrorInfoVO queryErrorInfo(Long id) {
        ProductErrorInfo productErrorInfo = productErrorInfoRepository.findById(id).orElseThrow(() ->
                new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.PRODUCT_UPDATEERRORINFO_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()))
        );
        return toVo(productErrorInfo);
    }

    @Override
    public ErrorInfoVO updateErrorInfo(Long id, ErrorInfoUpdateDTO errorInfoUpdateDTO) {
        ProductErrorInfo productErrorInfo = productErrorInfoRepository.findById(id).orElseThrow(() ->
                new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.PRODUCT_UPDATEERRORINFO_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()))
        );
        if (ProductErrorInfoStatus.RESOLVED.equals(productErrorInfo.getStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.PRODUCT_UPDATEERRORINFO_PROCESSED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        ServiceUtils.myCopyProperties(errorInfoUpdateDTO, productErrorInfo);
        productErrorInfo.setStatus(ProductErrorInfoStatus.RESOLVED);
        productErrorInfo.setOperator(SecurityUtils.getUserId());
        productErrorInfoRepository.save(productErrorInfo);
        return toVo(productErrorInfo);
    }

    private ErrorInfoVO toVo(ProductErrorInfo productErrorInfo) {
        TenantPhoneDTO tenantPhoneDTO = PhoneNumberUtils.parsePhoneNumber(productErrorInfo.getFormatPhone());
        UserNameVM userNameVM = userBriefRepository.findUserNameById(productErrorInfo.getPermissionUserId());
        Tenant tenant = tenantRepository.findById(productErrorInfo.getTenantId()).orElse(null);

        ErrorInfoVO errorInfoVO = ErrorInfoVO.fromErrorInfo(productErrorInfo);
        errorInfoVO.setUsername(userNameVM != null ? CommonUtils.formatFullName(userNameVM.getFirstName(), userNameVM.getLastName()) : null);
        errorInfoVO.setTenantName(tenant != null ? tenant.getName() : null);
        errorInfoVO.setCountryCode(tenantPhoneDTO.getCountryCode());
        errorInfoVO.setPhone(tenantPhoneDTO.getPhone());
        return errorInfoVO;
    }

    private List<ErrorInfoVO> toVo(List<ProductErrorInfo> productErrorInfos) {
        List<Long> userIds = productErrorInfos.stream().map(ProductErrorInfo::getPermissionUserId).distinct().collect(Collectors.toList());
        List<Long> tenantIds = productErrorInfos.stream().map(ProductErrorInfo::getTenantId).distinct().collect(Collectors.toList());

        List<UserNameVM> userNameVMList = userBriefRepository.findUserNameByIdIn(userIds);
        List<Tenant> tenantList = tenantRepository.findAllById(tenantIds);
        Map<Long, String> userNameMap = userNameVMList.stream().collect(Collectors.toMap(UserNameVM::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));
        Map<Long, String> tenantNameMap = tenantList.stream().collect(Collectors.toMap(Tenant::getId, Tenant::getName));

        List<ErrorInfoVO> errorInfoVOList = productErrorInfos.stream().map(o -> {
            ErrorInfoVO errorInfoVO = ErrorInfoVO.fromErrorInfo(o);
            errorInfoVO.setUsername(userNameMap.get(o.getPermissionUserId()));
            errorInfoVO.setTenantName(tenantNameMap.get(o.getTenantId()));
            return errorInfoVO;
        }).collect(Collectors.toList());

        return errorInfoVOList;
    }

}
