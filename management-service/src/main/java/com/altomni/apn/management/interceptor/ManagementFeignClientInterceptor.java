package com.altomni.apn.management.interceptor;

import com.altomni.apn.common.domain.user.AdminManagementUser;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * pass token between microservices
 *
 * <AUTHOR>
 */
@Component
@RefreshScope
public class ManagementFeignClientInterceptor implements RequestInterceptor {
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String APN_INTERNAL_PIN = "APN_INTERNAL_PIN";
    private static final String TOKEN_TYPE = "Bearer";

    @Value("${application.apnInternalPin:3hlo7PZn}")
    private String apnInternalPin;


    @Override
    public void apply(RequestTemplate requestTemplate) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return;
        }
        requestTemplate.header(APN_INTERNAL_PIN, apnInternalPin);
        if (authentication.getPrincipal() instanceof AdminManagementUser) {
            LoginUtil.simulateLoginWithClient();
            requestTemplate.removeHeader(AUTHORIZATION_HEADER);
        }
        Optional.ofNullable(SecurityUtils.getCurrentUserToken())
                .ifPresent(token -> requestTemplate.header(AUTHORIZATION_HEADER, String.format("%s %s", TOKEN_TYPE, token)));

    }
}
