package com.altomni.apn.management.web.rest.application;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.nodepagesection.FieldConfigDTO;
import com.altomni.apn.common.dto.application.nodepagesection.RecruitmentProcessNodePageSectionDTO;
import com.altomni.apn.management.service.application.ApplicationService;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.utils.HeaderUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

@RestController
@RequestMapping("/api/v3/recruitment-process-node-page-sections")
public class RecruitmentProcessNodePageSectionResource {

    private final Logger log = LoggerFactory.getLogger(RecruitmentProcessNodePageSectionResource.class);

    @Resource
    private ApplicationService applicationService;

    @PutMapping("/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
    public ResponseEntity<Object> create(@PathVariable Long recruitmentProcessId, @PathVariable NodeType nodeType, @RequestBody List<FieldConfigDTO> fieldConfigDTOS) throws URISyntaxException {
        log.debug("REST request to create RecruitmentProcessNodePageSections for recruitmentProcessId: {} and nodeType: {} with fieldConfigs: {}", recruitmentProcessId, nodeType, fieldConfigDTOS);
        Object result = applicationService.updateNodePageSection(recruitmentProcessId, nodeType, fieldConfigDTOS);
        return ResponseEntity.created(new URI("/api/v3/recruitment-process-node-page-sections/talentRecruitmentProcessId/" + recruitmentProcessId + "/nodeType/" + nodeType))
                .headers(HeaderUtil.createEntityCreationAlert("RecruitmentProcessNodePageSection", ""))
                .body(result);
    }

    @GetMapping("/default-config")
    public ResponseEntity<List<Object>> getGeneralRecruitmentProcessNodePageDefaultConfig() {
        log.debug("REST request to get all getGeneralRecruitmentProcessNodePageDefaultConfig");
        return new ResponseEntity<>(applicationService.findGeneralRecruitmentProcessNodePageSectionsDefaultConfig(), HttpStatus.OK);
    }

    @GetMapping("/default-config/ipg")
    public ResponseEntity<List<Object>> getIpgRecruitmentProcessNodePageDefaultConfig(@RequestParam JobType jobType) {
        log.debug("REST request to get all getGeneralRecruitmentProcessNodePageDefaultConfig");
        return new ResponseEntity<>(applicationService.findIpgRecruitmentProcessNodePageSectionsDefaultConfig(jobType), HttpStatus.OK);
    }

    @GetMapping("/default-config/nodeType/{nodeType}")
    public ResponseEntity<Object> getGeneralRecruitmentProcessNodePageDefaultConfigByNodeType(@PathVariable NodeType nodeType) {
        log.debug("REST request to get all getGeneralRecruitmentProcessNodePageDefaultConfig");
        return new ResponseEntity<>(applicationService.findGeneralRecruitmentProcessNodePageSectionsDefaultConfigByNodeType(nodeType), HttpStatus.OK);
    }

    @GetMapping("/default-config/jobType/{jobType}/nodeType/{nodeType}")
    public ResponseEntity<Object> getRecruitmentProcessNodePageDefaultConfigByJobTypeAndNodeType( @PathVariable JobType jobType, @PathVariable NodeType nodeType) {
        log.debug("REST request to get all getGeneralRecruitmentProcessNodePageDefaultConfig");
        return new ResponseEntity<>(applicationService.findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(jobType, nodeType), HttpStatus.OK);
    }

    @GetMapping("/recruitmentProcessId/{recruitmentProcessId}")
    public ResponseEntity<List<Object>> getAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(@PathVariable Long recruitmentProcessId) {
        log.debug("REST request to get all RecruitmentProcessNodePageSections by recruitmentProcessId: {}", recruitmentProcessId);
        return new ResponseEntity<>(applicationService.findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(recruitmentProcessId), HttpStatus.OK);
    }

    @GetMapping("/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
    public ResponseEntity<Object> getAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(@PathVariable Long recruitmentProcessId, @PathVariable NodeType nodeType) {
        log.debug("REST request to get all RecruitmentProcessNodePageSections by recruitmentProcessId: {}", recruitmentProcessId);
        return new ResponseEntity<>(applicationService.findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(recruitmentProcessId, nodeType), HttpStatus.OK);
    }

}
