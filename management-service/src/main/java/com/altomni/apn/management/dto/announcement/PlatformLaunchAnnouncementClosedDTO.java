package com.altomni.apn.management.dto.announcement;

import com.altomni.apn.management.domain.enumeration.AnnouncementStatus;
import com.altomni.apn.management.domain.enumeration.AnnouncementStatusConverter;
import com.altomni.apn.management.domain.enumeration.LaunchPlatform;
import com.altomni.apn.management.domain.enumeration.LaunchPlatformConverter;
import com.altomni.apn.management.valid.AnnouncementLaunchTime;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class PlatformLaunchAnnouncementClosedDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "platformLaunchAnnouncementId")
    @NotNull
    private Long id;

    /**
     * 兼容多平台user
     */
    @ApiModelProperty(value = "user email")
    @NotEmpty
    private String email;

}
