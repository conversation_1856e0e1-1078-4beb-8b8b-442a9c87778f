package com.altomni.apn.management.service.tenant;

import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.dto.management.TenantUpdateStatusDTO;
import com.altomni.apn.common.dto.parser.TenantWatermarkDTO;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.vo.talent.TenantPublicVO;
import com.altomni.apn.management.domain.tenant.TenantAddressBrief;
import com.altomni.apn.management.dto.tenant.TenantCommonDTO;
import com.altomni.apn.management.dto.tenant.TenantDTO;
import com.altomni.apn.management.vo.tenant.OnlineTenantVO;
import com.altomni.apn.management.vo.tenant.TenantDetailVO;
import com.altomni.apn.management.vo.tenant.TenantPageVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface TenantService {

    TenantDetailVO create(TenantDTO dto);

    Page<Tenant> searchTenantList(TenantUserTypeEnum type, String nameSearch, Pageable pageable);

    List<TenantPageVO> toVo(List<Tenant> tenantList);

    TenantDetailVO queryTenant(Long id);

    TenantDetailVO queryTenantByName(String name);

    TenantDetailVO updateTenant(Long id, TenantDTO tenantDTO);

    void updateTenantStatusToActive(Long id, TenantUpdateStatusDTO tenantUpdateStatusDTO);

    void updateTenantStatusToInActive(Long id);

    Set<Integer> searchAreaCodes();

    Object formatHistoryData();

    List<TenantDetailVO> getAllTenant(Integer status);

    List<OnlineTenantVO> getAllOnlineUsersSecondsInAndGroupByTenant(Integer seconds);

    TenantCommonDTO update(Long id, TenantCommonDTO tenant);

    TenantPublicVO queryPublicTenant(Long id);

    TenantAddressBrief queryTenantAddress(Long id);

    List<Tenant> searchTenantListByIds(List<Long> ids);

}
