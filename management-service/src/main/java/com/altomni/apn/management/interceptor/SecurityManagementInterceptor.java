package com.altomni.apn.management.interceptor;

import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.DispatcherType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Component
public class SecurityManagementInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {

        if (DispatcherType.REQUEST.name().equals(request.getDispatcherType().name())) {
            response.addHeader("responseTime", String.valueOf(System.currentTimeMillis()));
            if (SecurityUtils.isSystemAdmin()) {
                return true;
            }
            Boolean admin = SecurityUtils.getCurrentAdminManagementUser().map(user -> user.getUid() != null && user.getUid().contains("ADMIN"))
                    .orElse(false);
            if (admin) {
                return true;
            }
        }

        throw new NotFoundException("[Management] API does not exist!");
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }
}
