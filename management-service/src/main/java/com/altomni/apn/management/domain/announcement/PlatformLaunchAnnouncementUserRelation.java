package com.altomni.apn.management.domain.announcement;

import cn.hutool.http.HtmlUtil;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.management.domain.enumeration.AnnouncementStatus;
import com.altomni.apn.management.domain.enumeration.AnnouncementStatusConverter;
import com.altomni.apn.management.domain.enumeration.LaunchPlatform;
import com.altomni.apn.management.domain.enumeration.LaunchPlatformConverter;
import com.altomni.apn.management.dto.announcement.PlatformLaunchAnnouncementDTO;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementDetailVO;
import com.altomni.apn.management.vo.announcement.PlatformLaunchAnnouncementVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@Entity
@Data
@Table(name = "platform_launch_announcement_user_relation")
@NoArgsConstructor
@AllArgsConstructor
public class PlatformLaunchAnnouncementUserRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "platform_launch_announcement_id")
    private Long platformLaunchAnnouncementId;

    //兼容不同平台的user
    @Column(name = "email")
    private String email;

    public PlatformLaunchAnnouncementUserRelation(Long platformLaunchAnnouncementId, String email) {
        this.platformLaunchAnnouncementId = platformLaunchAnnouncementId;
        this.email = email;
    }
}
