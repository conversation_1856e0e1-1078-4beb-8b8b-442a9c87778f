package com.altomni.apn.management.domain.tenant;



import com.altomni.apn.management.domain.geoinfo.TenantGeoInfoENBrief;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A Address.
 */
@Entity
@Data
@Table(name = "tenant_address")
public class TenantAddressBrief implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "address")
    private String address;

    @Column(name = "address_2")
    private String address2;

    @Column(name = "zipcode")
    private String zipcode;

    @Column(name = "city_id")
    private Long cityId;

    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "city_id", referencedColumnName = "id", insertable = false, updatable = false)
    private TenantGeoInfoENBrief geoInfoEN;
}
