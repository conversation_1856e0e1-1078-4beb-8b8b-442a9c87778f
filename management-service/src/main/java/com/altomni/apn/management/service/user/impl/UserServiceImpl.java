package com.altomni.apn.management.service.user.impl;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.management.dto.user.UserStatusDTO;
import com.altomni.apn.management.service.sso.SsoClient;
import com.altomni.apn.management.service.user.UserClient;
import com.altomni.apn.management.service.user.UserService;
import com.altomni.apn.management.vo.user.UserPageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Service
@Slf4j
public class UserServiceImpl implements UserService {

    @Resource
    private UserClient userClient;
    @Resource
    private SsoClient ssoClient;

    @Override
    public ResponseEntity<List<UserPageVO>> searchUserList(Long tenantId, Pageable pageable) throws ExecutionException, InterruptedException {
        return userClient.searchUserList(tenantId, pageable);
    }

    @Override
    public void updateUserStatus(Long userId, UserStatusDTO userStatusDTO) {
        UserBriefDTO apnUser = userClient.findById(userId).getBody();
        ssoClient.innerUpdateUserStatus(apnUser.getEmail(), userStatusDTO.getActivated());
        userClient.updateUserStatus(userId, userStatusDTO);
    }

}
