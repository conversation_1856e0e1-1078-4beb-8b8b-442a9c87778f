package com.altomni.apn.management.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The LaunchPlatform enumeration.
 */
public enum LaunchPlatform implements ConvertedEnum<Integer> {
    CRM(10),
    APN(20);


    private final Integer dbValue;

    LaunchPlatform(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<LaunchPlatform, Integer> resolver =
        new ReverseEnumResolver<>(LaunchPlatform.class, LaunchPlatform::toDbValue);

    public static LaunchPlatform fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
