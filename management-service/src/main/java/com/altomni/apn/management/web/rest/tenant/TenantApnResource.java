package com.altomni.apn.management.web.rest.tenant;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.management.dto.tenant.TenantCommonDTO;
import com.altomni.apn.management.repository.tenant.TenantRepository;
import com.altomni.apn.management.service.tenant.TenantService;
import com.altomni.apn.management.vo.tenant.OnlineTenantVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@Api(tags = {"ManagementTenant apn tenant Controller"})
@RestController
@RequestMapping("/api/v3/common")
public class TenantApnResource {

    @Resource
    private TenantService tenantService;

    @Resource
    private TenantRepository tenantRepository;

    @GetMapping("/permissions/tenants")
    public ResponseEntity<List<Tenant>> getTenants(){
        log.info("[APN Management: Tenant @{}] REST request to get tenants", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(tenantRepository.findAll());
    }

    @GetMapping("/statistic/tenants/online-users/seconds-in/{seconds}/count")
    @NoRepeatSubmit
    public ResponseEntity<List<OnlineTenantVO>> getAllOnlineUsersCountGroupByTenant(@PathVariable("seconds") Integer seconds) {
        log.info("[APN Management: Tenant @{} REST] request to get all online users count group by tenant, seconds: {}", seconds);
        return ResponseEntity.ok(tenantService.getAllOnlineUsersSecondsInAndGroupByTenant(seconds));
    }

    @PutMapping("/tenants/{id}")
    @NoRepeatSubmit
    public ResponseEntity<TenantCommonDTO> updateTenant(@PathVariable("id") Long id, @Valid @RequestBody TenantCommonDTO tenant) {
        log.info("[APN: Tenant @{}] REST request to update Tenant : {}", SecurityUtils.getUserId(), tenant);
        TenantCommonDTO result = tenantService.update(id, tenant);
        return ResponseEntity.ok().body(result);
    }
}
