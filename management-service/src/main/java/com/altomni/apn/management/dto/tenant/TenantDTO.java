package com.altomni.apn.management.dto.tenant;

import com.altomni.apn.common.aop.validation.TodayOrFuture;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Future;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "Dto of management service tenant")
public class TenantDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    @NotNull(message = "tenant name is null")
    @ApiModelProperty(value = "tenant name")
    private String name;

    @ApiModelProperty(value = "tenant expireDate")
    @NotNull(message = "tenant expireDate is null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TodayOrFuture(message = "delivery date must be a future date")
    private LocalDate expireDate;

    @ApiModelProperty(value = "tenant monthly credit")
    @NotNull(message = "monthlyCredit is null")
    private Integer monthlyCredit;

    @ApiModelProperty(value = "tenant update monthly credit")
    private Integer updateMonthlyCredit;

    @ApiModelProperty(value = "tenant bulk credit")
    private Integer bulkCredit;

    @ApiModelProperty(value = "tenant contact first name")
    @NotNull(message = "tenant contactFirstName is null")
    private String contactFirstName;

    @ApiModelProperty(value = "tenant contact last name")
    @NotNull(message = "tenant contactLastName is null")
    private String contactLastName;

    @ApiModelProperty(value = "tenant telephone partition")
    @NotNull(message = "tenant countryCode is null")
    private Integer countryCode;

    @ApiModelProperty(value = "tenant phone")
    @NotNull(message = "tenant tenantPhone is null")
    private Long tenantPhone;

    @ApiModelProperty(value = "tenant email")
    @NotNull(message = "tenant tenantEmail is null")
    private String tenantEmail;

    @ApiModelProperty(value = "maximum number of users")
    @NotNull(message = "tenant userMaxLimit is null")
    private Integer userMaxLimit;

    @ApiModelProperty(value = "login url of tenant")
    @NotNull(message = "tenant loginLink is null")
    private String loginLink;

    @ApiModelProperty(value = "note url of tenant")
    @NotNull(message = "tenant note is null")
    private String note;

    @ApiModelProperty(value = "type of tenant, EMPLOYER(0),HEADHUNTER(1)")
    @NotNull(message = "tenant userType is null")
    private TenantUserTypeEnum userType;

    private Integer resetDayOfMonth;

    @ApiModelProperty(value = "tenant status")
    private Integer status;

    @ApiModelProperty(value = "city id")
    private Long cityId;

    @ApiModelProperty(value = "detail address")
    private String  address;

}
