package com.altomni.apn.management.domain.tenant;

import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A Tenant.
 */
@Entity
@Table(name = "tenant_admin_status")
@Data
public class TenantAdminStatusBrief implements Serializable
{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @Column(name = "user_id")
    private Long  userId;

    @Column(name = "activated")
    private boolean activated;

    public TenantAdminStatusBrief() {
    }

    public TenantAdminStatusBrief(Long tenantId, Long userId, boolean activated) {
        this.tenantId = tenantId;
        this.userId = userId;
        this.activated = activated;
    }
}
