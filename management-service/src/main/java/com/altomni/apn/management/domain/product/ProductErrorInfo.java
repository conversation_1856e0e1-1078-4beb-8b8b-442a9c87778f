package com.altomni.apn.management.domain.product;



import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.management.domain.enumeration.ProductErrorInfoStatus;
import com.altomni.apn.management.domain.enumeration.ProductErrorInfoStatusConverter;
import com.altomni.apn.management.domain.enumeration.ProductErrorInfoType;
import com.altomni.apn.management.domain.enumeration.ProductErrorInfoTypeConverter;
import com.altomni.apn.management.dto.product.ErrorInfoDTO;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A Address.
 */
@Entity
@Data
@Table(name = "product_error_info")
public class ProductErrorInfo extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "email")
    private String email;

    @Column(name = "format_phone")
    private String formatPhone;

    @Column(name = "source")
    private String source;

    @Column(name = "error_desc")
    private String errorDesc;

    @Column(name = "error_process_note")
    private String errorProcessNote;

    @Column(name = "operator")
    private Long operator;

    @Column(name = "error_type")
    @Convert(converter = ProductErrorInfoTypeConverter.class)
    private ProductErrorInfoType errorType;

    @Column(name = "status")
    @Convert(converter = ProductErrorInfoStatusConverter.class)
    private ProductErrorInfoStatus status;

    @Column(name = "tenant_id")
    private Long tenantId;

    public static ProductErrorInfo fromErrorInfoDTO(ErrorInfoDTO errorInfoDTO) {
        ProductErrorInfo productErrorInfo = new ProductErrorInfo();
        ServiceUtils.myCopyProperties(errorInfoDTO, productErrorInfo);
        return productErrorInfo;
    }

}
