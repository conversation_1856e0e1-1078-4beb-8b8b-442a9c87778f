package com.altomni.apn.management.config.rabbit;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@RefreshScope
public class SsoRabbitProperties {

    @Value("${application.sso.user-info-exchange}")
    String ssoUserInfoExchange;
    @Value("${application.sso.user-active-exchange}")
    String ssoUserActiveEchange;
    @Value("${application.sso.user-binging-client-exchange}")
    String ssoUserBingClientExchange;
    @Value("${application.sso.management-user-info-queue}")
    String ssoUserInfoQueue;
    @Value("${application.sso.management-user-active-queue}")
    String ssoUserActiveQueue;
    @Value("${application.sso.management-user-binging-client-queue}")
    String ssoUserBingClientQueue;
}
