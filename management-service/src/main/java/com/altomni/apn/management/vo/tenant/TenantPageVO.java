package com.altomni.apn.management.vo.tenant;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel(description = "Vo of management service tenant")
public class TenantPageVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "tenant id")
    private Long id;

    @ApiModelProperty(value = "tenant name")
    private String name;

    @ApiModelProperty(value = "tenant expireDate")
    private Instant expireDate;

    @ApiModelProperty(value = "number of active jobs of tenant")
    private Integer activeJobCount;

    @ApiModelProperty(value = "number of talent of tenant")
    private Integer talentCount;

    @ApiModelProperty(value = "number of company of tenant")
    private Integer companyCount;

    @ApiModelProperty(value = "monthly credit used by tenant")
    private Integer usedMonthlyCredit;

    @ApiModelProperty(value = "tenant monthly credit")
    private Integer monthlyCredit;

    @ApiModelProperty(value = "bulk credit used by tenant")
    private Integer usedBulkCredit;

    @ApiModelProperty(value = "tenant bulk credit")
    private Integer bulkCredit;

    @ApiModelProperty(value = "maximum number of users")
    private Integer userMaxLimit;

    @ApiModelProperty(value = "the current number of users of tenant")
    private Integer userAmount;

    @ApiModelProperty(value = "status url of tenant, 0 INACTIVE, 1 ACTIVE")
    private Integer status;

    @ApiModelProperty(value = "type of tenant, EMPLOYER(0),HEADHUNTER(1)")
    private TenantUserTypeEnum userType;

}
