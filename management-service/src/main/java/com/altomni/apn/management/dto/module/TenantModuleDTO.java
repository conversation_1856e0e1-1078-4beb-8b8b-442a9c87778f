package com.altomni.apn.management.dto.module;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(description = "Dto of management service update tenant module")
public class TenantModuleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "tenant module id is null")
    @ApiModelProperty(value = "tenant module id")
    private Long id;

    @NotNull(message = "tenant module isShow is null")
    @ApiModelProperty(value = "tenant module isShow")
    private Boolean isShow;

    @NotNull(message = "tenant module isInvolveDataPermission is null")
    @ApiModelProperty(value = "tenant module isInvolveDataPermission")
    private Boolean isInvolveDataPermission;

}
