package com.altomni.apn.management.vo.tenant;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class OnlineTenantVO {

    private Long tenantId;

    private String tenantName;

    private Long onlineUserCount;

    public OnlineTenantVO(Long tenantId, String name) {
        this.tenantId = tenantId;
        this.tenantName = name;
    }
}
