package com.altomni.apn.management.web.rest.user;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.AdminManagementUser;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.user.LoginVM;
import com.altomni.apn.common.dto.user.ManagementLoginUserDTO;
import com.altomni.apn.common.dto.user.ManagementUserDTO;
import com.altomni.apn.common.enumeration.enums.ManagementAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.management.config.env.ManagementApiPromptProperties;
import com.altomni.apn.management.dto.user.ForgetPassDTO;
import com.altomni.apn.management.dto.user.ForgetPassResetDTO;
import com.altomni.apn.management.dto.user.RefreshTokenDTO;
import com.altomni.apn.management.dto.user.ResetPasswordDTO;
import com.altomni.apn.management.repository.user.AdminManagementUserRepository;
import com.altomni.apn.management.service.user.ManagementUserService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.security.GeneralSecurityException;

@Slf4j
@Api(tags = {"ManagementUser Management"})
@RestController
@RequestMapping("/api/v3/admin-management/user")
public class ManagementUserResource {

    @Resource
    private AdminManagementUserRepository adminManagementUserRepository;

    @Resource
    private ManagementUserService managementUserService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ManagementApiPromptProperties managementApiPromptProperties;

    /**
     *
     * @param loginVM
     * @param request
     * @return
     */
    @PostMapping("/login")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<ManagementLoginUserDTO> login(@RequestBody LoginVM loginVM, HttpServletRequest request) {
        log.info("[Management: User] REST request to get login user, {}", loginVM);
        AdminManagementUser adminManagementUser = adminManagementUserRepository.findByUsernameOrEmail(loginVM.getUsername());
        if (ObjectUtil.isNull(adminManagementUser)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_LOGIN_USERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        if (!adminManagementUser.isActivated()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_LOGIN_INACTIVEUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        loginVM.setIp(ServletUtil.getClientIP(request));
        loginVM.setUid(adminManagementUser.getUid());
        ManagementLoginUserDTO managementLoginUserDTO = managementUserService.login(loginVM);
        return ResponseEntity.ok(managementLoginUserDTO);
    }

    @GetMapping("/findOneByUsername")
    public ResponseEntity<ManagementLoginUserDTO> findOneByUsername(@RequestParam("username") String username) {
        ManagementLoginUserDTO managementLoginUserDTO = managementUserService.findOneByUsername(username);
        return ResponseEntity.ok(managementLoginUserDTO);
    }

    @GetMapping("/findUserWithAuthorities")
    ResponseEntity<ManagementUserDTO> findUserWithAuthorities(@RequestParam("login") String login) {
        ManagementUserDTO managementUserDTO = managementUserService.findUserWithAuthorities(login);
        return ResponseEntity.ok(managementUserDTO);
    }

    /**
     * validate email for forget password;
     *
     * @param forgetPassDTO
     */
    @PostMapping("/password/forget")
    @Timed
    @NoRepeatSubmit
    public void forgetPass(@Valid @RequestBody ForgetPassDTO forgetPassDTO) {
        log.info("[Management: User] Forget password, {}", forgetPassDTO);
        managementUserService.forgetPass(forgetPassDTO);
    }

    /**
     * renew password for user forget password;
     *
     * @param resetPasswordDTO
     * @return
     */
    @PostMapping("/password/renew")
    @Timed
    @NoRepeatSubmit
    public void renewPassForForget(@Valid @RequestBody ForgetPassResetDTO resetPasswordDTO) throws GeneralSecurityException {
        log.info("[Management: User] Forget password renew, {}", resetPasswordDTO);
        managementUserService.resetPassForForget(resetPasswordDTO);
    }

    /**
     * Change the password when the user is logged in.
     *
     * @param resetPasswordDTO
     * @return
     */
    @PostMapping("/password/change")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<ManagementLoginUserDTO> updatePassword(@Valid @RequestBody ResetPasswordDTO resetPasswordDTO) throws GeneralSecurityException {
        log.info("[Management: User @{}] REST reset password:", SecurityUtils.getUserId());
        ManagementLoginUserDTO managementLoginUserDTO = managementUserService.resetPassword(resetPasswordDTO);
        return ResponseEntity.ok(managementLoginUserDTO);
    }

    @PostMapping(path = "/refresh-token", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<CredentialDTO> refreshToken(@Valid @RequestBody RefreshTokenDTO refreshTokenDTO) {
        log.info("Management {} request to refresh token", SecurityUtils.getUserId());
        return new ResponseEntity<>(managementUserService.refreshToken(refreshTokenDTO), HttpStatus.OK);
    }

    /**
     * renew password for user forget password;
     *
     * @param resetPasswordDTO
     * @return
     */
    @PostMapping("/password/test")
    @Timed
    @NoRepeatSubmit
    public void test(@Valid @RequestBody ForgetPassResetDTO resetPasswordDTO) {
        log.info("[Management: User] Forget password test, {}", resetPasswordDTO);
        managementUserService.test(resetPasswordDTO);
    }
}
