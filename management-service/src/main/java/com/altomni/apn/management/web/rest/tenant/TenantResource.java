package com.altomni.apn.management.web.rest.tenant;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.dto.management.TenantUpdateStatusDTO;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.management.dto.tenant.TenantDTO;
import com.altomni.apn.management.service.tenant.TenantService;
import com.altomni.apn.management.vo.tenant.TenantDetailVO;
import com.altomni.apn.management.vo.tenant.TenantPageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Set;

@Slf4j
@Api(tags = {"ManagementTenant Controller"})
@RestController
@RequestMapping("/api/v3")
public class TenantResource {

    @Resource
    private TenantService tenantService;

    /**
     * POST  /tenants : Create a new tenant.
     *
     * @param tenantDTO the tenant to create
     * @return the ResponseEntity with status 201 (Created) and with body the new tenant.
     */
    @PostMapping("/tenants")
    @NoRepeatSubmit
    @ApiOperation(value = "Create a new tenant", tags = {"APN V3"})
    public ResponseEntity<TenantDetailVO> createTenant(@Valid @RequestBody TenantDTO tenantDTO) {
        log.info("[APN Management: Tenant @{}] REST request to save tenant : {}", SecurityUtils.getUserId(), tenantDTO);
        TenantDetailVO result = tenantService.create(tenantDTO);
        return new ResponseEntity<>(result, HttpStatus.CREATED);

    }

    /**
     *
     * @param type
     * @param pageable
     * @return the ResponseEntity with status 200 (OK) and with body the tenant list.
     */
    @GetMapping("/tenants/page")
    @NoRepeatSubmit
    @ApiOperation(value = "Search tenant List", tags = {"APN V3"})
    public ResponseEntity<List<TenantPageVO>> searchTenantList(@RequestParam(value = "type", required = false) TenantUserTypeEnum type,
                                                               @RequestParam(value = "name", required = false) String nameSearch,
                                                               @PageableDefault @SortDefault(sort = {"status", "createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN Management: Tenant @{}] REST request to search tenant list : {}, searchName: {}", SecurityUtils.getUserId(), pageable, nameSearch);
        Page<Tenant> tenantPage = tenantService.searchTenantList(type, nameSearch, pageable);
        List<TenantPageVO> result = tenantService.toVo(tenantPage.getContent());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(tenantPage, "/tenants");
        return new ResponseEntity<>(result, headers, HttpStatus.OK);
    }

    @PostMapping("/tenants/list-by-ids")
    @NoRepeatSubmit
    public ResponseEntity<List<TenantDetailVO>> searchTenantListByIds(@RequestBody List<Long> ids) {
        log.info("[APN Management: Tenant @{}] REST request to search tenant list by ids : {}", SecurityUtils.getUserId(), ids);
        List<Tenant> tenants = tenantService.searchTenantListByIds(ids);
        List<TenantDetailVO> result = tenants.stream().map(TenantDetailVO::fromTenant).toList();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     *
     * @param id
     * @return the ResponseEntity with status 200 (OK) and with body the tenant details.
     */
    @GetMapping("/tenants/{id}")
    @NoRepeatSubmit
    @ApiOperation(value = "Query a tenant details", tags = {"APN V3"})
    public ResponseEntity<TenantDetailVO> queryTenant(@PathVariable("id") Long id) {
        log.info("[APN Management: Tenant @{}] REST request to query a tenant details : {}", SecurityUtils.getUserId(), id);
        TenantDetailVO result = tenantService.queryTenant(id);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     *
     * @param id
     * @param tenantDTO
     * @return the ResponseEntity with status 201 (Created) and with body the tenant details.
     */
    @PutMapping("/tenants/{id}")
    @NoRepeatSubmit
    @ApiOperation(value = "Update a tenant details", tags = {"APN V3"})
    public ResponseEntity<TenantDetailVO> updateTenant(@PathVariable("id") Long id, @Valid @RequestBody TenantDTO tenantDTO) {
        log.info("[APN Management: Tenant @{}] REST request to update a tenant details : {}", SecurityUtils.getUserId(), id);
        TenantDetailVO result = tenantService.updateTenant(id, tenantDTO);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    /**
     *
     * @param id
     * @param tenantUpdateStatusDTO
     * @return the ResponseEntity with status 201 (Created)
     */
    @PutMapping("/tenants/{id}/active")
    @NoRepeatSubmit
    @ApiOperation(value = "Update a tenant details status to active.", tags = {"APN V3"})
    public ResponseEntity<HttpStatus> updateTenantStatusToActive(@PathVariable("id") Long id, @Valid @RequestBody TenantUpdateStatusDTO tenantUpdateStatusDTO) {
        log.info("[APN Management: Tenant @{}] REST request to update a tenant status to active : {}", SecurityUtils.getUserId(), id);
        tenantService.updateTenantStatusToActive(id, tenantUpdateStatusDTO);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @PutMapping("/tenants/{id}/inActive")
    @NoRepeatSubmit
    @ApiOperation(value = "Update a tenant details status to inActive", tags = {"APN V3"})
    public ResponseEntity<HttpStatus> updateTenantStatusToInActive(@PathVariable("id") Long id) {
        log.info("[APN Management: Tenant @{}] REST request to update a tenant status to inActive : {}", SecurityUtils.getUserId(), id);
        tenantService.updateTenantStatusToInActive(id);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @GetMapping("/tenants/phone/areaCodes")
    @NoRepeatSubmit
    @ApiOperation(value = "Search phone areaCodes", tags = {"APN V3"})
    public ResponseEntity<Set<Integer>> searchAreaCodes() {
        log.info("[APN Management: Tenant @{}] REST request to search phone areaCodes.", SecurityUtils.getUserId());
        return new ResponseEntity<>(tenantService.searchAreaCodes(), HttpStatus.OK);
    }

    @GetMapping("/tenants/format/history-data")
    @NoRepeatSubmit
    @ApiOperation(value = "Format tenants history data", tags = {"APN V3"})
    public ResponseEntity<Object> formatHistoryData() {
        log.info("[APN Management: Tenant @{}] REST request to format tenants history data.", SecurityUtils.getUserId());
        return ResponseEntity.ok(tenantService.formatHistoryData());
    }

}
