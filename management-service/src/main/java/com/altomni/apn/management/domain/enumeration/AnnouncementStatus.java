package com.altomni.apn.management.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The AnnouncementStatus enumeration.
 */
public enum AnnouncementStatus implements ConvertedEnum<Integer> {
    DRAFT(10),
    LAUNCHED(20);


    private final Integer dbValue;

    AnnouncementStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<AnnouncementStatus, Integer> resolver =
        new ReverseEnumResolver<>(AnnouncementStatus.class, AnnouncementStatus::toDbValue);

    public static AnnouncementStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
