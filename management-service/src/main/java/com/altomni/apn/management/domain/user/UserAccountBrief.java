package com.altomni.apn.management.domain.user;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A UserAccount.
 */
@Entity
@Data
@Table(name = "user_account")
public class UserAccountBrief extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 3524846326589097872L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "amount")
    private Integer monthlyAmount;

    @Column(name = "bulk_credit")
    private Integer bulkCredit=0;

    @Column(name = "effect_credit")
    private Integer effectCredit;

    @Column(name = "frozen_amount")
    private Integer frozenAmount;

    @Column(name = "total_amount")
    private Integer totalAmount;

    @Column(name = "expire_date")
    private String expireDate;

    @Version
    @Column(name = "version")
    @JsonIgnore
    private Integer version;

}
