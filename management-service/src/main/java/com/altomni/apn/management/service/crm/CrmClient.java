package com.altomni.apn.management.service.crm;

import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.management.config.env.ApplicationProperties;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class CrmClient {

    private final ApplicationProperties applicationProperties;
    private final HttpService httpService;

    public CrmClient(ApplicationProperties applicationProperties, HttpService httpService) {
        this.applicationProperties = applicationProperties;
        this.httpService = httpService;
    }

    public void createTenant(CrmTenantDto crmTenantDto) {
        Headers headers = Headers.of(Map.of(HttpHeaders.AUTHORIZATION, "Bearer " + SecurityUtils.getCurrentUserToken()));
        try {
            HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + "/user/api/v1/permissions/tenants", headers, JsonUtil.toJson(crmTenantDto));
            if (response.getCode() == 201) {
                log.info("CRM Tenant created successfully");
            } else {
                log.error("Error while creating tenant in CRM. Response code: {}, response body: {}", response.getCode(), response.getBody());
                throw new RuntimeException("Error while creating tenant in CRM. Response code: " + response.getCode() + ", response body: " + response.getBody());
            }
        } catch (Exception e) {
            log.error("Error creating tenant", e);
            throw new RuntimeException("Error creating tenant", e);
        }
    }
}
