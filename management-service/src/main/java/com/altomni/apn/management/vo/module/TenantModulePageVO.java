package com.altomni.apn.management.vo.module;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel(description = "Vo of management service tenant module")
public class TenantModulePageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "module id")
    private Long id;

    @ApiModelProperty(value = "module name")
    private String name;

    @ApiModelProperty(value = "Show menu 1: Show 0: Don't show")
    private Boolean isShow;

    @ApiModelProperty(value = "display permission control button 1: Display 0: do not display")
    private Boolean isShowPermissionButton;

    @ApiModelProperty(value = "whether to participate in permission control 1: participate 0: do not participate")
    private Boolean isInvolveDataPermission;

    @ApiModelProperty(value = "can be edited 1. can be edited 0. can't be edited")
    private Boolean disable;

    @ApiModelProperty(value = "parent module id")
    private Long parentId;

    @ApiModelProperty(value = "level module id")
    private Integer level;


}
