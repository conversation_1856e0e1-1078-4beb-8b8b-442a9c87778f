package com.altomni.apn.management.web.rest.application;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.application.recruitmentprocess.StatusDTO;
import com.altomni.apn.common.enumeration.enums.ManagementAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.management.config.env.ManagementApiPromptProperties;
import com.altomni.apn.management.service.application.ApplicationService;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessConfigVO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessTalentAndJobStats;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.errors.BadRequestAlertException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.core.io.*;
import org.springframework.http.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing RecruitmentProcess.
 */
@RestController
@RequestMapping("/api/v3")
public class RecruitmentProcessResource {

    private final Logger log = LoggerFactory.getLogger(RecruitmentProcessResource.class);

    private static final String ENTITY_NAME = "RecruitmentProcess";

    @Resource
    private ApplicationService applicationService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ManagementApiPromptProperties managementApiPromptProperties;

    /**
     * GET  /recruitment-processes : get all the recruitmentProcess.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of recruitmentProcess in body
     */
    @GetMapping("/recruitment-processes/tenantId/{tenantId}")
    public ResponseEntity<List<RecruitmentProcessVO>> getAllByTenantId(@PathVariable("tenantId") Long tenantId, @RequestParam(required = false) ActiveStatus status, @PageableDefault @SortDefault(sort = "id", direction = Sort.Direction.ASC) Pageable pageable) {
        log.debug("REST request to get all RecruitmentProcess");

        Page<RecruitmentProcessVO> res = applicationService.findAllByTenantId(tenantId, status, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(res, "/management/api/v3/recruitment-processes");
        return new ResponseEntity<>(res.getContent(), headers, HttpStatus.OK);
    }

    /**
     * POST  /recruitment-processes : Create a new recruitmentProcess.
     *
     * @param recruitmentProcess the recruitmentProcess to create
     * @return the ResponseEntity with status 201 (Created) and with body the new recruitmentProcess, or with status 400 (Bad Request) if the recruitmentProcess has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/recruitment-processes")
    public ResponseEntity<RecruitmentProcessVO> create(@RequestBody RecruitmentProcessVO recruitmentProcess) throws URISyntaxException {
        log.debug("REST request to save RecruitmentProcess : {}", recruitmentProcess);
        if (recruitmentProcess.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.RECRUITMENTPROCESS_CREATE_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        RecruitmentProcessVO result = applicationService.create(recruitmentProcess);
        return ResponseEntity.created(new URI("/api/v3/recruitment-processes/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * DELETE  /recruitment-processes/:id : delete the "id" recruitmentProcess.
     *
     * @param id the id of the recruitmentProcess to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/recruitment-processes/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        log.debug("REST request to delete RecruitmentProcess : {}", id);
        applicationService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }

    /**
     * PUT  /recruitment-processes/:recruitmentProcessId/status : update the status of "id" recruitmentProcess.
     *
     * @param recruitmentProcessId the id of the recruitmentProcess to update the status
     * @return the ResponseEntity with status 200 (OK)
     */
    @PutMapping("/recruitment-processes/{recruitmentProcessId}/status")
    public ResponseEntity<RecruitmentProcessVO> updateStatus(@PathVariable Long recruitmentProcessId, @RequestBody @Valid StatusDTO status) {
        log.debug("REST request to delete RecruitmentProcess : {}", recruitmentProcessId);
        RecruitmentProcessVO result = applicationService.updateStatus(recruitmentProcessId, status);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, recruitmentProcessId.toString()))
                .body(result);
    }

    /**
     * PUT  /recruitment-processes : Updates an existing recruitmentProcess.
     *
     * @param recruitmentProcessVO the recruitmentProcess to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated recruitmentProcess,
     * or with status 400 (Bad Request) if the recruitmentProcess is not valid,
     * or with status 500 (Internal Server Error) if the recruitmentProcess couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/recruitment-processes/{recruitmentProcessId}")
    public ResponseEntity<RecruitmentProcessVO> update(@PathVariable Long recruitmentProcessId, @RequestBody RecruitmentProcessVO recruitmentProcessVO) throws URISyntaxException {
        log.debug("REST request to update RecruitmentProcess : {}", recruitmentProcessVO);
//        if (recruitmentProcess.getId() == null) {
//            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "id null");
//        }
        RecruitmentProcessVO result = applicationService.update(recruitmentProcessId, recruitmentProcessVO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * GET  /recruitment-processes/:id : get the "id" recruitmentProcess.
     *
     * @param id the id of the recruitmentProcess to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the recruitmentProcess, or with status 404 (Not Found)
     */
    @GetMapping("/recruitment-processes/{id}")
    public ResponseEntity<RecruitmentProcessVO> findOne(@PathVariable Long id) {
        log.debug("REST request to get RecruitmentProcess : {}", id);
        RecruitmentProcessVO recruitmentProcess = applicationService.findOne(id);
        return ResponseUtil.wrapOrNotFound(Optional.of(recruitmentProcess));
    }
    @PostMapping("/recruitment-processes/config")
    public ResponseEntity<List<RecruitmentProcessVO>> config(@RequestBody RecruitmentProcessConfigVO configVO) throws URISyntaxException {
        log.debug("REST request to config RecruitmentProcessConfigVO : {}", configVO);
        if (configVO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.RECRUITMENTPROCESS_CREATE_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        List<RecruitmentProcessVO> result = applicationService.config(configVO);
        return ResponseEntity.created(new URI("/api/v3/recruitment-processes/"))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, configVO.getTenantId().toString()))
                .body(result);
    }

    @GetMapping("/recruitment-processes/{recruitmentProcessId}/in-progress")
    public ResponseEntity<RecruitmentProcessTalentAndJobStats> getInProgressJobAndApplicationCountByRecruitmentProcessId(@PathVariable Long recruitmentProcessId) {
        log.debug("REST request to get all RecruitmentProcess");
        return new ResponseEntity<>(applicationService.getInProgressJobAndApplicationCountByRecruitmentProcessId(recruitmentProcessId), HttpStatus.OK);
    }

    @GetMapping("/recruitment-processes/{recruitmentProcessId}/in-progress/download")
    public ResponseEntity<org.springframework.core.io.Resource> downloadInProgressJobAndApplicationCountByRecruitmentProcessId(@PathVariable Long recruitmentProcessId, @RequestParam(value = "language", defaultValue = "en", required = false) String language) throws UnsupportedEncodingException {
        log.debug("REST request to get all RecruitmentProcess");
        byte[] fileData = applicationService.downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(recruitmentProcessId, language);
        org.springframework.core.io.Resource resource = new ByteArrayResource(fileData);

        String filename = "Unfinished Applications for RecruitmentProcess_" + recruitmentProcessId + "_" + new SimpleDateFormat("yyyyMMdd").format(Date.from(Instant.now())) + ".xlsx";
        if ("cn".equalsIgnoreCase(language)) {
            filename = "失效流程数据展示_职位_" + new SimpleDateFormat("yyyyMMdd").format(Date.from(Instant.now())) + ".xlsx";
        }

        String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());

//        HttpHeaders headers = new HttpHeaders();
//        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
//        headers.setContentDisposition(ContentDisposition.attachment().filename(filename, StandardCharsets.UTF_8).build());

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFilename + "\"")
                .body(resource);
//        return ResponseEntity.ok()
//                .headers(headers)
//                .body(resource);
    }
}
