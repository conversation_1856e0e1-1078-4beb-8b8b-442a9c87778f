package com.altomni.apn.management.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class AnnouncementStatusConverter extends AbstractAttributeConverter<AnnouncementStatus, Integer> {
    public AnnouncementStatusConverter() {
        super(AnnouncementStatus::toDbValue, AnnouncementStatus::fromDbValue);
    }
}
