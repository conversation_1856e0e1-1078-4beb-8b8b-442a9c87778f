package com.altomni.apn.management.service.rabbmitmq.listener;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.altomni.apn.common.domain.transactionrecord.CommonMqConsumeFailedRecord;
import com.altomni.apn.common.dto.sso.SsoUserActive;
import com.altomni.apn.common.dto.sso.SsoUserBinding;
import com.altomni.apn.common.dto.sso.SsoUserInfo;
import com.altomni.apn.common.enumeration.enums.MqTranRecordBusTypeEnums;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.management.repository.mqfailedrecord.UserMqTransactionFailedRecordRepository;
import com.altomni.apn.management.service.user.ManagementUserService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class SsoUserMqListener {

    private static final String SSO_USER_INFO_PREFIX = "MANAGEMENT:SSO_USER_INFO:";
    private static final String SSO_USER_ACTIVE_PREFIX = "MANAGEMENT:SSO_USER_ACTIVE:";
    private static final String SSO_USER_BINDING_PREFIX = "MANAGEMENT:SSO_USER_BINDING:";


    private final ManagementUserService managementUserService;
    private final CommonRedisService commonRedisService;
    private final UserMqTransactionFailedRecordRepository userMqTransactionFailedRecordRepository;
    private final OAuth2ResourceServerProperties oAuth2ResourceServerProperties;

    public SsoUserMqListener(ManagementUserService managementUserService, CommonRedisService commonRedisService,
                             UserMqTransactionFailedRecordRepository userMqTransactionFailedRecordRepository, OAuth2ResourceServerProperties oAuth2ResourceServerProperties) {
        this.managementUserService = managementUserService;
        this.commonRedisService = commonRedisService;
        this.userMqTransactionFailedRecordRepository = userMqTransactionFailedRecordRepository;
        this.oAuth2ResourceServerProperties = oAuth2ResourceServerProperties;
    }


    /**
     * 处理用户基本信息更新
     *
     * @param channel
     * @param message
     * @throws IOException
     */
    @RabbitListener(containerFactory = "userConsumerFactory", queues = {"${application.sso.management-user-info-queue}"})
    @RabbitHandler
    public void processUserInfoUpdate(Channel channel, Message message) throws IOException {
        log.info("user info update ,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        SsoUserInfo ssoUserInfo = JsonUtil.fromJson(json, SsoUserInfo.class);
        if (null == ssoUserInfo) {
            log.error("ssoUserInfo is null,param:{}", json);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        BigInteger busId = tooBigInteger(ssoUserInfo.getTransactionId());
        String key = SSO_USER_INFO_PREFIX.concat(ssoUserInfo.getTransactionId());
        if (!checkExits(key, busId, MqTranRecordBusTypeEnums.SSO_USERINFO_UPDATE)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        try {
            managementUserService.updateBYSsoUserInfo(ssoUserInfo);
            saveFailedRecord(busId, 1, ssoUserInfo, MqTranRecordBusTypeEnums.SSO_USERINFO_UPDATE);
            commonRedisService.delete(key);
            log.info("update user info success,param:{}", ssoUserInfo);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("update user info error: ", e);
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            saveFailedRecord(busId, 0, ssoUserInfo, MqTranRecordBusTypeEnums.SSO_USERINFO_UPDATE);
        }
    }

    /**
     * 处理用户在 sso 中 active 状态更新
     *
     * @param channel
     * @param message
     * @throws IOException
     */
    @RabbitListener(containerFactory = "userConsumerFactory", queues = {"${application.sso.management-user-active-queue}"})
    @RabbitHandler
    public void processUserActive(Channel channel, Message message) throws IOException {
        log.info("user active ,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        SsoUserActive userActive = JsonUtil.fromJson(json, SsoUserActive.class);
        if (null == userActive) {
            log.error("userActive is null,param:{}", json);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        String key = SSO_USER_ACTIVE_PREFIX.concat(userActive.getTransactionId());
        BigInteger busId = tooBigInteger(userActive.getTransactionId());
        if (!checkExits(key, busId, MqTranRecordBusTypeEnums.SSO_USERACTIVE_UPDATE)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        try {
            managementUserService.updateUserActiveBySso(userActive);
            saveFailedRecord(busId, 1, userActive, MqTranRecordBusTypeEnums.SSO_USERACTIVE_UPDATE);
            commonRedisService.delete(key);
            log.info("update user active success,param:{}", userActive);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("update user active error: ", e);
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            saveFailedRecord(busId, 0, userActive, MqTranRecordBusTypeEnums.SSO_USERACTIVE_UPDATE);
        }
    }

    /**
     * 处理用户在 sso 中绑定客户端，只处理自己客户端对应的client_id 的
     *
     * @param channel
     * @param message
     * @throws IOException
     */
    @RabbitListener(containerFactory = "userConsumerFactory", queues = {"${application.sso.management-user-binging-client-queue}"})
    @RabbitHandler
    public void processUserBinding(Channel channel, Message message) throws IOException {
        log.info("user binging ,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        SsoUserBinding ssoUserBinding = JsonUtil.fromJson(json, SsoUserBinding.class);
        if (null == ssoUserBinding) {
            log.error("ssoUserBinding is null,param:{}", json);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        String clientId = oAuth2ResourceServerProperties.getOpaquetoken().getClientId();
        if (!clientId.equals(ssoUserBinding.getClientId())) {
            log.info("client id not match,param:{}", json);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        String key = SSO_USER_BINDING_PREFIX.concat(ssoUserBinding.getTransactionId());
        BigInteger busId = tooBigInteger(ssoUserBinding.getTransactionId());
        if (!checkExits(key, busId, MqTranRecordBusTypeEnums.SSO_USER_BINGING)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        try {
            managementUserService.onSsoUserBinding(ssoUserBinding);
            saveFailedRecord(busId, 1, ssoUserBinding, MqTranRecordBusTypeEnums.SSO_USER_BINGING);
            commonRedisService.delete(key);
            log.info("update user success");
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("update user error: ", e);
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            saveFailedRecord(busId, 0, ssoUserBinding, MqTranRecordBusTypeEnums.SSO_USER_BINGING);
        }
    }


    /**
     * 幂等校验
     */
    private boolean checkExits(String cacheKey, BigInteger busId, MqTranRecordBusTypeEnums busType) {
        //redis verify exists
        String value = commonRedisService.get(cacheKey);
        if (StringUtils.isNotBlank(value)) {
            log.error("message key {} already exists in redis", cacheKey);
            return false;
        }

        //数据库是否记录
        CommonMqConsumeFailedRecord commonMqConsumeFailedRecord = userMqTransactionFailedRecordRepository.findByBusIdAndBusTypeAndReceiceStatus(busId, busType.toDbValue(), 1);
        if (null != commonMqConsumeFailedRecord) {
            log.error("message key {} already exists in database", cacheKey);
            return false;
        }
        commonRedisService.set(cacheKey, "1", 18000);
        return true;
    }


    /**
     * 保存消费记录
     */
    private void saveFailedRecord(BigInteger busId, Integer status, Object message, MqTranRecordBusTypeEnums busType) {
        CommonMqConsumeFailedRecord failedRecord = new CommonMqConsumeFailedRecord();
        failedRecord.setConsumeCount(1);
        failedRecord.setBusId(busId);
        failedRecord.setReceiveMessage(JsonUtil.toJson(message));
        failedRecord.setBusType(busType.toDbValue());
        failedRecord.setReceiceStatus(status);
        userMqTransactionFailedRecordRepository.save(failedRecord);
    }

    private static BigInteger tooBigInteger(String transactionId) {
        long transactionIdLong = Long.parseLong(transactionId);
        return BigInteger.valueOf(transactionIdLong);
    }

}
