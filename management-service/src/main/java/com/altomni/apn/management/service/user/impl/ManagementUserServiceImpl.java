package com.altomni.apn.management.service.user.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.config.email.EmailProperties;
import com.altomni.apn.common.domain.user.AdminManagementRole;
import com.altomni.apn.common.domain.user.AdminManagementUser;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.sso.SsoUserActive;
import com.altomni.apn.common.dto.sso.SsoUserBinding;
import com.altomni.apn.common.dto.sso.SsoUserInfo;
import com.altomni.apn.common.dto.user.LoginVM;
import com.altomni.apn.common.dto.user.ManagementLoginUserDTO;
import com.altomni.apn.common.dto.user.ManagementUserDTO;
import com.altomni.apn.common.enumeration.enums.ManagementAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.AES;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.management.config.env.ApplicationProperties;
import com.altomni.apn.management.config.env.ManagementApiPromptProperties;
import com.altomni.apn.management.dto.user.ForgetPassDTO;
import com.altomni.apn.management.dto.user.ForgetPassResetDTO;
import com.altomni.apn.management.dto.user.RefreshTokenDTO;
import com.altomni.apn.management.dto.user.ResetPasswordDTO;
import com.altomni.apn.management.repository.user.AdminManagementRoleRepository;
import com.altomni.apn.management.repository.user.AdminManagementUserRepository;
import com.altomni.apn.management.service.authority.AuthorityService;
import com.altomni.apn.management.service.mail.MailService;
import com.altomni.apn.management.service.user.ManagementUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.security.GeneralSecurityException;
import java.util.*;

@Service
@Slf4j
public class ManagementUserServiceImpl implements ManagementUserService {

    public static final long MAXIMUM_LOGIN_FAILED_COUNT = 5;

    private final String MANAGEMENT_USER_LABEL = "management:user:validate_key:";

    private final String MANAGEMENT_PASS_WORD_VALIDATE_KEY_PREFIX = "MANAGEMENT_PASS_WORD_VALIDATE_KEY_PREFIX";

    private static final String NON_EXISTENT = "NX";

    private static final String TIME_SECONDS = "EX";

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private EmailProperties properties;

    @Resource
    private AdminManagementUserRepository adminManagementUserRepository;

    @Resource
    private AdminManagementRoleRepository adminManagementRoleRepository;

    @Resource
    AuthorityService authorityService;

    @Resource
    private CommonRedisService redisService;

    @Resource
    private MailService mailService;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ManagementApiPromptProperties managementApiPromptProperties;

    @Override
    public ManagementLoginUserDTO login(LoginVM loginVM) {
        String username = loginVM.getUsername();
        this.checkLockedAccount(username);
        try {
            AdminManagementUser user = adminManagementUserRepository.findByUsernameOrEmail(loginVM.getUsername());
            if (ObjectUtil.isNull(user)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_LOGIN_USERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
            }
            log.info(loginVM.toString());
            loginVM.setUsername(user.getUid());
            loginVM.setPassword(AES.decrypt(loginVM.getPassword(), applicationProperties.getSecret()));

            CredentialDTO credentialDTO = authorityService.findCredential(loginVM).getBody();

            ManagementLoginUserDTO managementLoginUserDTO = ManagementLoginUserDTO.formatFromUser(user);
            managementLoginUserDTO.setCredential(credentialDTO);
            this.removeLoginFailedUser(username);
            this.saveActiveUser(user.getId(), user.getId(), credentialDTO.getAccess_token());
            return managementLoginUserDTO;
        } catch (Exception e) {
            loginVM.setUsername(username);
            log.error("[ManagementUserService.login] Login user {}, exception {}", loginVM, ExceptionUtils.getStackTrace(e));
            String errorMsg = this.countLoginFailed(username);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_LOGIN_USERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
    }

    /**
     * If the user login failed for 5 times in 24 hours, this account will be locked for 30 minutes.
     * @param username username for login
     * <AUTHOR>
     */
    private void checkLockedAccount(String username){
        String lockedUserKey = String.format(RedisConstants.DATA_KEY_LOCKED_USER_ACCOUNT, this.getClass().getSimpleName(), username);
        if (redisService.exists(lockedUserKey)){
            Long lockedInMinutes = redisService.getTTL(lockedUserKey)/60 + 1;
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_CHECKLOCKEDACCOUNT_ACCOUNTLOCK.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(lockedInMinutes),managementApiPromptProperties.getManagementService()));
        }
    }

    private void removeLoginFailedUser(String username){
        String loginFailedUserKey = String.format(RedisConstants.DATA_KEY_COUNT_LOGIN_FAILED, this.getClass().getSimpleName(), username);
        redisService.delete(loginFailedUserKey);
    }

    private void saveActiveUser(Long userIdFrom, Long userIdTo, String token){
        String activeUserKey = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, userIdFrom, userIdTo, token);
        redisService.set(activeUserKey, "1", properties.getActiveUserPeriod());
    }

    private String countLoginFailed(String username){
        String loginFailedUserKey = String.format(RedisConstants.DATA_KEY_COUNT_LOGIN_FAILED, this.getClass().getSimpleName(), username);
        String message = null;
        if (redisService.exists(loginFailedUserKey) && Long.valueOf(redisService.get(loginFailedUserKey)) < MAXIMUM_LOGIN_FAILED_COUNT){
            long failedCount = redisService.incr(loginFailedUserKey);
            if (failedCount < MAXIMUM_LOGIN_FAILED_COUNT){
                message = String.format("You have failed to login for %d times over the past %d hours. You have %d more chances left",
                        failedCount, RedisConstants.EXPIRE_IN_24_HOURS/3600, MAXIMUM_LOGIN_FAILED_COUNT  - failedCount);
            }else{
                String lockedUserKey = String.format(RedisConstants.DATA_KEY_LOCKED_USER_ACCOUNT, this.getClass().getSimpleName(), username);
                redisService.set(lockedUserKey, "1", RedisConstants.EXPIRE_IN_30_MINUTES);
                message = String.format("You have failed to login for %d times over the past %d hours. Your account was locked for %d minutes",
                        MAXIMUM_LOGIN_FAILED_COUNT, RedisConstants.EXPIRE_IN_24_HOURS/3600, RedisConstants.EXPIRE_IN_30_MINUTES/60);
            }
        }else{
            redisService.set(loginFailedUserKey, "1", RedisConstants.EXPIRE_IN_24_HOURS);
            message = String.format("You have failed to login for 1 time over the past %d hours. You have %d more chances left",
                    RedisConstants.EXPIRE_IN_24_HOURS/3600, MAXIMUM_LOGIN_FAILED_COUNT  - 1);
        }
        return message;
    }

    @Override
    public ManagementLoginUserDTO findOneByUsername(String username) {
        AdminManagementUser user = adminManagementUserRepository.findByUsernameOrEmail(username);
        if (ObjectUtil.isNull(user)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_FINDONEBYUSERNAME_USERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        if (!user.isActivated()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_FINDONEBYUSERNAME_USERINACTIVE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        return ManagementLoginUserDTO.formatFromUser(user);
    }

    @Override
    public ManagementUserDTO findUserWithAuthorities(String login) {
        AdminManagementUser user = adminManagementUserRepository.findUserWithAuthorities(login);
        if (ObjectUtil.isNull(user)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_FINDONEBYUSERNAME_USERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        if (!user.isActivated()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_FINDONEBYUSERNAME_USERINACTIVE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        return ManagementUserDTO.formatFromUser(user);
    }

    @Override
    public void forgetPass(ForgetPassDTO forgetPassDTO) {
        AdminManagementUser user = adminManagementUserRepository.findByEmail(forgetPassDTO.getEmail());
        if (user == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_FINDONEBYUSERNAME_USERINACTIVE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }

        String uuid = IdUtil.simpleUUID() + "@" + user.getUid();
        String resultValue = redisService.set(MANAGEMENT_USER_LABEL + MANAGEMENT_PASS_WORD_VALIDATE_KEY_PREFIX + user.getUid(), uuid, NON_EXISTENT, TIME_SECONDS, applicationProperties.getValidateKeyExpirationTime());

        if (resultValue == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_FORGETPASS_RESETPASSWORDSEND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }

        String subject = "reset password";
        String link = forgetPassDTO.getResetPassPath() + "?code=" + uuid;
        String content = "<html>\n" +
                "<body>\n" +
                "<br>Hello,<br/>\n" +
                "<br>Please click here to reset your system account password.<br/>\n" +
                "<a href=\"" + link + "\">" + link + "</a>\n" +
                "<br>If you have already requested a password change, please use the link provided in this email.<br/>\n" +
                "<br>If this is not you:<br/>\n" +
                "<br>Your system account may have been compromised. Please reset your password immediately. At the same time, we strongly recommend that you contact the company account administrator to report this information in order to enhance the security of the system account and prevent unauthorized access.<br/>\n" +
                "<br>Sincerely,<br/>\n" +
                "<br>Your APN development team<br/>\n" +
                "</body>\n" +
                "</html>";

        List<String> emails = new ArrayList<>();
        emails.add(user.getEmail());

        MailVM mailVM = new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, content, null, true);
        try {
            LoginUtil.simulateLoginWithClient();
            mailService.sendHtmlMail(mailVM);

        } catch (Exception e) {
            log.error("error", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_FORGETPASS_EMAILSENDFAIL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
    }

    @Override
    public void resetPassForForget(ForgetPassResetDTO dto) throws GeneralSecurityException {
        if (dto.getCode() == null || dto.getCode().trim().length() == 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_RESETPASSFORFORGET_CODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }

        if (dto.getNewPass() == null || dto.getNewPass().trim().length() == 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_RESETPASSFORFORGET_PASSWORDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }

        String uuid = dto.getCode().split("@")[1];
        if (uuid == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_RESETPASSFORFORGET_INVALIDCODE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }
        String redisCode = redisService.get(MANAGEMENT_USER_LABEL + MANAGEMENT_PASS_WORD_VALIDATE_KEY_PREFIX + uuid);
        if (redisCode == null || !redisCode.equals(dto.getCode())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_RESETPASSFORFORGET_INVALIDCODEEXPIRED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }

        dto.setNewPass(AES.decrypt(dto.getNewPass(), applicationProperties.getSecret()));

        redisService.delete(MANAGEMENT_USER_LABEL + MANAGEMENT_PASS_WORD_VALIDATE_KEY_PREFIX + uuid);

        AdminManagementUser user = adminManagementUserRepository.findByUid(uuid);
        String newPass = passwordEncoder.encode(dto.getNewPass());
        user.setPassword(newPass);
        adminManagementUserRepository.saveAndFlush(user);
    }

    @Override
    public ManagementLoginUserDTO resetPassword(ResetPasswordDTO resetPasswordDTO) throws GeneralSecurityException {
        AdminManagementUser user = adminManagementUserRepository.findByUid(SecurityUtils.getUserUid());
        if (user == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_RESETPASSWORD_USERNOTLOGIN.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }

        resetPasswordDTO.setOldPassword(AES.decrypt(resetPasswordDTO.getOldPassword(), applicationProperties.getSecret()));
        resetPasswordDTO.setNewPassword(AES.decrypt(resetPasswordDTO.getNewPassword(), applicationProperties.getSecret()));

        if (resetPasswordDTO.getNewPassword() == null || resetPasswordDTO.getNewPassword().trim().length() == 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_RESETPASSWORD_PASSWORDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }

        if (!passwordEncoder.matches(resetPasswordDTO.getOldPassword(), user.getPassword())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ManagementAPIMultilingualEnum.USER_RESETPASSWORD_OLDPASSWORDNOTMATCH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),managementApiPromptProperties.getManagementService()));
        }

        user.setPassword(passwordEncoder.encode(resetPasswordDTO.getNewPassword()));
        adminManagementUserRepository.saveAndFlush(user);
        return ManagementLoginUserDTO.formatFromUser(user);
    }

    @Override
    public CredentialDTO refreshToken(RefreshTokenDTO refreshTokenDTO) {
        return authorityService.refreshToken(refreshTokenDTO).getBody();
    }

    @Override
    public void test(ForgetPassResetDTO dto) {
        try {

        }
        catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    @Override
    @Transactional
    public void updateBYSsoUserInfo(SsoUserInfo ssoUserInfo) {
        AdminManagementUser adminUser = adminManagementUserRepository.findByEmail(ssoUserInfo.getEmail());
        if (adminUser == null) {
            log.error("sso user not exist");
            return;
        }
        adminUser.setFirstName(ssoUserInfo.getFirstName());
        adminUser.setLastName(ssoUserInfo.getLastName());
        adminUser.setUsername(ssoUserInfo.getUsername());
        adminManagementUserRepository.save(adminUser);
    }

    @Override
    @Transactional
    public void updateUserActiveBySso(SsoUserActive userActive) {
        AdminManagementUser adminUser = adminManagementUserRepository.findByEmail(userActive.getEmail());
        if (adminUser == null) {
            log.error("sso user not exist");
            return;
        }
        adminUser.setActivated(userActive.getActive());
        adminManagementUserRepository.save(adminUser);
    }

    @Override
    @Transactional
    public void onSsoUserBinding(SsoUserBinding ssoUserBinding) {
        AdminManagementUser adminUser = adminManagementUserRepository.findByEmail(ssoUserBinding.getEmail());
        if (adminUser == null) {
            if (!ssoUserBinding.getCreation()) {
                return;
            }
            AdminManagementRole rolePlatformAdmin = adminManagementRoleRepository.findByName("ROLE_PLATFORM_ADMIN");
            AdminManagementUser adminManagementUser = new AdminManagementUser();
            adminManagementUser.setEmail(ssoUserBinding.getEmail());
            adminManagementUser.setUsername(ssoUserBinding.getUsername());
            adminManagementUser.setActivated(true);
            adminManagementUser.setPassword("");
            adminManagementUser.setFirstName(ssoUserBinding.getFirstName());
            adminManagementUser.setLastName(ssoUserBinding.getLastName());
            adminManagementUser.setUid(ssoUserBinding.getUserId().toString());
            adminManagementUser.setManagementRoles(new HashSet<>(Collections.singletonList(rolePlatformAdmin)));
            AdminManagementUser savedUser = adminManagementUserRepository.save(adminManagementUser);
            savedUser.setUid(savedUser.getId() + ",ADMIN");
            adminManagementUserRepository.save(savedUser);
        } else {
            adminUser.setActivated(ssoUserBinding.getCreation());
            adminManagementUserRepository.save(adminUser);
        }

    }

}
