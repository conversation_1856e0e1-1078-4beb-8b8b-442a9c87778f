package com.altomni.apn.management.repository.user;

import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.management.domain.user.UserNameVM;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface UserBriefRepository extends JpaRepository<User, Long> {

    List<User> findAllByTenantId(Long tenantId);

    @Modifying
    @Query(value = "update user set activated=?1 WHERE tenant_id = ?2", nativeQuery = true)
    void inActiveUserByTenant(Integer active,Long id);

    @Query(value = "select SUM(ua.effect_credit) from user_account ua left join user u on u.id = ua.user_id where u.tenant_id =?1 and u.activated=1 and ua.effect_credit is not null and ua.expire_date = ?2",nativeQuery = true)
    Integer findTotalAssignNextMonthCreditByTenant(Long id, String expireDate);

    @Query(value = "select SUM(ua.amount) from user_account ua left join user u on u.id = ua.user_id where u.tenant_id =?1 and u.activated=1 and ua.effect_credit is null and ua.amount is not null and ua.expire_date = ?2",nativeQuery = true)
    Integer findTotalNextMonthEffectCreditByTenant(Long id, String expireDate);

    @Query(value = "select SUM(ct.credit) from credit_transaction ct  where ct.tenant_id = ?1 and ct.credit_type = ?2",nativeQuery = true)
    Integer findTotalUsedCreditByTenant(Long id,int creditType);

    @Query(value = "select new com.altomni.apn.management.domain.user.UserNameVM(u.id, u.firstName, u.lastName) from User u where u.id in ?1")
    List<UserNameVM> findUserNameByIdIn(List<Long> ids);

    @Query(value = "select new com.altomni.apn.management.domain.user.UserNameVM(u.id, u.firstName, u.lastName) from User u where u.id = ?1")
    UserNameVM findUserNameById(Long id);

}
