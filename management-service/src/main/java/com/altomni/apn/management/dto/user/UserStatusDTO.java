package com.altomni.apn.management.dto.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel(description = "Dto of management service user status")
public class UserStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "user activated is null")
    @ApiModelProperty(value = "user activated")
    private Boolean activated;

}
