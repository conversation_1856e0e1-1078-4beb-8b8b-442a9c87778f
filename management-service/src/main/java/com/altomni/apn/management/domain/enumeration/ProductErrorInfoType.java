package com.altomni.apn.management.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ProductErrorInfoStatus enumeration.
 */
public enum ProductErrorInfoType implements ConvertedEnum<Integer> {
    OPERATIONAL_ISSUES(10),
    PRODUCT_DESIGN(20),
    DEVELOPMENT_BUG(30),
    NEW_REQUIREMENT(40);


    private final Integer dbValue;

    ProductErrorInfoType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ProductErrorInfoType, Integer> resolver =
        new ReverseEnumResolver<>(ProductErrorInfoType.class, ProductErrorInfoType::toDbValue);

    public static ProductErrorInfoType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
