package com.altomni.apn.management.web.rest.module;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.management.dto.module.TenantModuleDTO;
import com.altomni.apn.management.service.module.TenantModuleService;
import com.altomni.apn.management.vo.module.TenantModulePageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@Api(tags = {"ManagementModule Controller"})
@RestController
@RequestMapping("/api/v3")
public class ModuleResource {

    @Resource
    private TenantModuleService tenantModuleService;

    @GetMapping("/modules/{tenantId}/list")
    @NoRepeatSubmit
    @ApiOperation(value = "search tenant module List", tags = {"APN V3"})
    public ResponseEntity<List<TenantModulePageVO>> searchTenantModuleList(@PathVariable("tenantId") Long tenantId) {
        log.info("[APN Management: Module @{}] REST request to search tenant module list : {}", SecurityUtils.getUserId(), tenantId);
        return new ResponseEntity<>(tenantModuleService.searchTenantModuleList(tenantId), HttpStatus.OK);
    }

    @PutMapping("/modules/{tenantId}")
    @NoRepeatSubmit
    @ApiOperation(value = "update tenant module List", tags = {"APN V3"})
    public ResponseEntity<HttpStatus> updateTenantModuleList(@PathVariable("tenantId") Long tenantId, @Valid @RequestBody List<TenantModuleDTO> tenantModuleDTOList) {
        log.info("[APN Management: Module @{}] REST request to update tenant module list : {}", SecurityUtils.getUserId(), tenantId);
        tenantModuleService.updateTenantModuleList(tenantId, tenantModuleDTOList);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

}
