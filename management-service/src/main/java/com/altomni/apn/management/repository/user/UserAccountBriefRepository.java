package com.altomni.apn.management.repository.user;

import com.altomni.apn.management.domain.user.UserAccountBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


/**
 * Spring Data  repository for the UserAccount entity.
 * <AUTHOR>
 */
@Repository
public interface UserAccountBriefRepository extends JpaRepository<UserAccountBrief, Long> {

    @Modifying
    @Query(value = "UPDATE UserAccountBrief ua SET ua.monthlyAmount = CASE WHEN ua.effectCredit IS NULL THEN ua.monthlyAmount ELSE ua.effectCredit END, ua.effectCredit = NULL, ua.expireDate = ?1 WHERE ua.userId IN (SELECT u.id FROM User u WHERE u.activated = TRUE) AND ua.expireDate <> ?1")
    int updateActiveUserMonthlyCredit(String expireDate);

}
