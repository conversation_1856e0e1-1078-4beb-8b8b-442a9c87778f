package com.altomni.apn.management.service.user;

import com.altomni.apn.common.domain.permission.PermissionModulePrivilegePageVM;
import com.altomni.apn.common.dto.user.TenantAdminUserDto;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.management.dto.module.TenantModuleDTO;
import com.altomni.apn.management.dto.user.UserStatusDTO;
import com.altomni.apn.management.vo.module.TenantModulePageVO;
import com.altomni.apn.management.vo.user.UserPageVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * Service Interface for managing.
 */
@Component
@FeignClient(value = "user-service")
public interface UserClient {

    @PostMapping("/user/api/v3/users/tenant-admin")
    ResponseEntity<Void> createTenantAdminUser(@RequestBody TenantAdminUserDto userDTO);

    @GetMapping("/user/api/v3/users/find-by-id")
    ResponseEntity<UserBriefDTO> findById(@RequestParam("id") Long id);

    @GetMapping("/user/api/v3/users/find-by-email")
    ResponseEntity<UserBriefDTO> findByEmail(@RequestParam("email") String email);

    @GetMapping("/user/api/v3/permissions/modules-privilege/{tenantId}/list")
    ResponseEntity<List<TenantModulePageVO>> searchTenantModuleList(@PathVariable("tenantId") Long tenantId);

    @PutMapping("/user/api/v3/permissions/modules-privilege/modules/{tenantId}")
    ResponseEntity<HttpStatus> updateTenantModuleList(@PathVariable("tenantId") Long tenantId, @RequestBody List<TenantModuleDTO> tenantModuleDTOList);

    @GetMapping("/user/api/v3/users/{tenantId}/page")
    ResponseEntity<List<UserPageVO>> searchUserList(@PathVariable("tenantId") Long tenantId, @PageableDefault @SortDefault(sort = {"id"}, direction = Sort.Direction.ASC) Pageable pageable) throws ExecutionException, InterruptedException;

    @PutMapping("/user/api/v3/users/{userId}/status")
    ResponseEntity<HttpStatus> updateUserStatus(@PathVariable("userId") Long userId, @RequestBody UserStatusDTO userStatusDTO);

    @GetMapping("/user/api/v3/permissions/modules-privilege/modules/defalut/{tenantId}/{type}")
    ResponseEntity<List<PermissionModulePrivilegePageVM>> findTenantDefaultModuleList(@PathVariable("tenantId") Long tenantId, @PathVariable("type") TenantUserTypeEnum type);

}
