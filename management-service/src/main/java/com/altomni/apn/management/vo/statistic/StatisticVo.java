package com.altomni.apn.management.vo.statistic;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class StatisticVo implements Serializable {

    @Id
    private Long id;

    private String name;

    private Long count;

}
