package com.altomni.apn.management.vo.product;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.management.domain.enumeration.ProductErrorInfoStatus;
import com.altomni.apn.management.domain.enumeration.ProductErrorInfoType;
import com.altomni.apn.management.domain.product.ProductErrorInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class ErrorInfoVO extends AbstractAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "email")
    private String email;

    @ApiModelProperty(value = "countryCode")
    private Integer countryCode;

    @ApiModelProperty(value = "phone")
    private Long phone;

    @ApiModelProperty(value = "source url")
    private String source;

    @ApiModelProperty(value = "error description")
    private String errorDesc;

    @ApiModelProperty(value = "tenant id")
    private Long tenantId;

    @ApiModelProperty(value = "tenant name")
    private String tenantName;

    @ApiModelProperty(value = "user id")
    private Long userId;

    @ApiModelProperty(value = "user name")
    private String username;

    @ApiModelProperty(value = "error type")
    private ProductErrorInfoType errorType;

    @ApiModelProperty(value = "error note")
    private String errorProcessNote;

    @ApiModelProperty(value = "error info status")
    private ProductErrorInfoStatus status;

    public static ErrorInfoVO fromErrorInfo(ProductErrorInfo productErrorInfo) {
        ErrorInfoVO errorInfoVO = new ErrorInfoVO();
        ServiceUtils.myCopyProperties(productErrorInfo, errorInfoVO);
        errorInfoVO.setUserId(productErrorInfo.getPermissionUserId());
        return errorInfoVO;
    }

}
