package com.altomni.apn.management.service.application;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.nodepagesection.FieldConfigDTO;
import com.altomni.apn.common.dto.application.nodepagesection.RecruitmentProcessNodePageSectionDTO;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Service Interface for managing {RecruitmentProcessNodePageSection}.
 */
@Component
@FeignClient(value = "application-service")
public interface RecruitmentProcessNodePageSectionService {

    @PostMapping("/application/api/v3/recruitment-process-node-page-sections/admin/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
    ResponseEntity<Object> create(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @PathVariable("nodeType") NodeType nodeType, @RequestBody List<FieldConfigDTO> fieldConfigDTOS);

    @PutMapping("/application/api/v3/recruitment-process-node-page-sections/admin/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
    ResponseEntity<Object> update(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @PathVariable("nodeType") NodeType nodeType, @RequestBody List<FieldConfigDTO> fieldConfigDTOS);

    @GetMapping("/application/api/v3/recruitment-process-node-page-sections/admin/default-config")
    ResponseEntity<List<Object>> findGeneralRecruitmentProcessNodePageSectionsDefaultConfig();

    @GetMapping("/application/api/v3/recruitment-process-node-page-sections/admin/default-config/ipg")
    ResponseEntity<List<Object>> findIpgRecruitmentProcessNodePageSectionsDefaultConfig(@RequestParam("jobType") JobType jobType);

    @GetMapping("/application/api/v3/recruitment-process-node-page-sections/admin/default-config/nodeType/{nodeType}")
    ResponseEntity<Object> findGeneralRecruitmentProcessNodePageSectionsDefaultConfigByNodeType(@PathVariable("nodeType") NodeType nodeType);

    @GetMapping("/application/api/v3/recruitment-process-node-page-sections/admin/default-config/jobType/{jobType}/nodeType/{nodeType}")
    ResponseEntity<Object> findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(@PathVariable("jobType") JobType jobTyp, @PathVariable("nodeType") NodeType nodeType);

    @GetMapping("/application/api/v3/recruitment-process-node-page-sections/admin/recruitmentProcessId/{recruitmentProcessId}")
    ResponseEntity<List<Object>> findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(@PathVariable("recruitmentProcessId") Long recruitmentProcessId);

    @GetMapping("/application/api/v3/recruitment-process-node-page-sections/admin/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
    ResponseEntity<Object> findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @PathVariable("nodeType") NodeType nodeType);

}
