package com.altomni.apn.management.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.emailService.supportSender}")
    private String supportSender;

    @Value("${application.mainPath.baseUrl}")
    private String baseUrl;

    @Value("${application.managementService.validateKey.expirationTime}")
    private Long validateKeyExpirationTime;

//    @Value("${application.managementService.password.iv}")
//    private String passwordIv;
//
//    @Value("${application.managementService.password.key}")
//    private String passwordKey;

    @Value("${application.security.aes.secretKey:1234567890abcdef}")
    private String secret;

    @Value("${application.crmUrl}")
    private String crmUrl;

}
