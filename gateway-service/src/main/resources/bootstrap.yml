spring:
  application:
    name: gateway-service
  cloud:
    nacos:
      config:
        server-addr: ${NACOS-SERVICE-ADDR:localhost:8848}
        username: ${NACOS-USERNAME:nacos}
        password: ${NACOS-PASSWORD:nacos}
        file-extension: yaml
        namespace: ${NAMESPACE:dev}
        refresh-enabled: true
        shared-configs:
          - data-id: public-log.yaml
            refresh: true
          - data-id: public-spring-cloud.yaml
            refresh: true
          - data-id: public-actuator.yaml
            refresh: true

  main:
    web-application-type: reactive
    allow-bean-definition-overriding: true
    allow-circular-references: true
