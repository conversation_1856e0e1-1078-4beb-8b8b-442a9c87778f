package com.altomni.apn.gateway.filter;

import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

//@Component
public class AuthFilter implements GlobalFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        /*Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (!(authentication instanceof OAuth2Authentication)){
            exchange.getResponse().setStatusCode(HttpStatus.BAD_REQUEST);
            return exchange.getResponse().setComplete();
        }

        OAuth2Authentication oAuth2Authentication = (OAuth2Authentication) authentication;

        Authentication userAuthentication = oAuth2Authentication.getUserAuthentication();

        //Object principal = userAuthentication.getPrincipal();
        String principal = userAuthentication.getName();

        List<String> authorities = userAuthentication.getAuthorities().stream().map(a-> a.getAuthority()).collect(Collectors.toList());

        OAuth2Request oAuth2Request = oAuth2Authentication.getOAuth2Request();

        Map<String, String> requestParameters = oAuth2Request.getRequestParameters();

        exchange.getRequest().getQueryParams().setAll(requestParameters);

        Map<String, Object> jsonToken = new HashMap<>(){{
            put("principal", principal);
            put("authorities", authorities);
        }};

        exchange.getResponse().getHeaders().add("json-token", JSON.toJSONString(jsonToken));*/

        
        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
