package cn.hutool.core.bean.copier.provider;

import cn.hutool.core.bean.BeanDesc.PropDesc;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.ValueProvider;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.UtilException;
import cn.hutool.core.util.StrUtil;

import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

/**
 * Bean的值提供者
 *
 * <AUTHOR>
 */
public class BeanValueProvider implements ValueProvider<String> {

    private Object source;
    private boolean ignoreError;
    final Map<String, PropDesc> sourcePdMap;

    /**
     * 构造
     *
     * @param bean        Bean
     * @param ignoreCase  是否忽略字段大小写
     * @param ignoreError 是否忽略字段值读取错误
     */
    public BeanValueProvider(Object bean, boolean ignoreCase, boolean ignoreError) {
        this.source = bean;
        this.ignoreError = ignoreError;
        sourcePdMap = BeanUtil.getBeanDesc(source.getClass()).getPropMap(ignoreCase);
    }

    @Override
    public Object value(String key, Type valueType) {
        PropDesc sourcePd = sourcePdMap.get(key);
        if (null == sourcePd && (Boolean.class == valueType || boolean.class == valueType)) {
            //boolean类型字段字段名支持两种方式
            sourcePd = sourcePdMap.get(StrUtil.upperFirstAndAddPre(key, "is"));
        }

        if (null != sourcePd) {
            final Method getter = sourcePd.getGetter();
            if (null != getter) {
                try {
                    Object value = getter.invoke(source);
                    //自行添加对集合泛型的支持
                    if (valueType instanceof ParameterizedType) {
                        ParameterizedType tmp = (ParameterizedType) valueType;
                        Class cl = Class.forName(tmp.getRawType().getTypeName());
                        if (Collection.class.isAssignableFrom(cl) && value instanceof Collection) {
                            Type[] actualTypeArguments = tmp.getActualTypeArguments();
                            if (actualTypeArguments != null && actualTypeArguments.length == 1) {
                                Type actualTypeArgument = actualTypeArguments[0];
                                List l = Convert.toList(Class.forName(actualTypeArgument.getTypeName()), value);
                                if (Set.class.isAssignableFrom(cl)) {
                                    return new HashSet(l);
                                } else {
                                    return l;
                                }
                            }
                        }
                    }
                    return value;
                } catch (Exception e) {
                    if (false == ignoreError) {
                        throw new UtilException(e, "Inject [{}] error!", key);
                    }
                }
            }
        }
        return null;
    }

    @Override
    public boolean containsKey(String key) {
        return sourcePdMap.containsKey(key) || sourcePdMap.containsKey(StrUtil.upperFirstAndAddPre(key, "is"));
    }


}
