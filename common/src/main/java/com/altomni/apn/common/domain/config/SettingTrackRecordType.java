package com.altomni.apn.common.domain.config;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum SettingTrackRecordType implements ConvertedEnum<Integer> {

    COMMON_TYPE(1),
    PERSONAL_TYPE(2);

    private final int dbValue;

    SettingTrackRecordType(int dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<SettingTrackRecordType, Integer> resolver =
            new ReverseEnumResolver<>(SettingTrackRecordType.class, SettingTrackRecordType::toDbValue);

    public static SettingTrackRecordType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
