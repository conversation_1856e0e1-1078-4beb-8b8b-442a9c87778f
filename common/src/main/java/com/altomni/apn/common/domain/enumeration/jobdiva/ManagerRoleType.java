package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * TimeSheetBreakTimeType
 */
public enum ManagerRoleType implements ConvertedEnum<Integer> {
    PRIMARY_CLIENT(0),
    NORMAL_CLIENT(1),
    CLIENT(2),
    AM(3);//ONLY FOR AM SIDE


    private final int dbValue;

    ManagerRoleType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<ManagerRoleType, Integer> resolver = new ReverseEnumResolver<>(ManagerRoleType.class, ManagerRoleType::toDbValue);

    public static ManagerRoleType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
