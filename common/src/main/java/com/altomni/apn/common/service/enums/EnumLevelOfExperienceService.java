package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollectionUtil;
import com.altomni.apn.common.domain.dict.EnumLevelOfExperience;
import com.altomni.apn.common.dto.enums.CmnEnumDictDTO;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnumLevelOfExperienceService {

    private final Logger log = LoggerFactory.getLogger(EnumLevelOfExperienceService.class);

    @Resource
    private EnumCommonService enumCommonService;


    public List<CmnEnumDictDTO> findAllOrderBySortType(SortType type) {
        log.info("[APN: EnumLevelOfExperienceService @{}] request to get level of experience enum data, type: {}", SecurityUtils.getUserId(), type);

        List<EnumLevelOfExperience> result = enumCommonService.findAllEnumLevelOfExperience();
        if (CollectionUtil.isNotEmpty(result)) {
            if (SortType.EN.equals(type)){
                result.forEach(r -> r.setCnLable(false));
            }else{
                result.forEach(r -> r.setCnLable(true));
            }
            //format EnumDictDTO for webSide
            return result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay())).map((EnumLevelOfExperience frontDisplay) -> CmnEnumDictDTO.fromBizDict(frontDisplay, type)).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

}
