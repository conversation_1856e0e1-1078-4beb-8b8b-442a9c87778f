package com.altomni.apn.common.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;


@Data
public class FolderSearchRequestDTO implements Serializable {

        @ApiModelProperty(value = "The type for customFolder.", allowableValues = "CUSTOMIZED, SHARED")
        @NotNull(message = "folderType is required")
        private FolderType folderType;

        @ApiModelProperty(value = "The name for customFolder.")
        private String name;

        @ApiModelProperty(value = "The note for customFolder.")
        private String note;

        @ApiModelProperty(value = "the last modified date class")
        private DateBetweenDTO lastModifiedDate;

        @ApiModelProperty(value = "The lastModifiedDateFrom for customFolder.")
        private Instant lastModifiedDateFrom;

        @ApiModelProperty(value = "The lastModifiedDateTo for customFolder.")
        private Instant lastModifiedDateTo;

        @ApiModelProperty(value = "the created date")
        private DateBetweenDTO createdDate;

        @ApiModelProperty(value = "The createdDateFrom for customFolder.")
        private Instant createdDateFrom;

        @ApiModelProperty(value = "The createdDateTo for customFolder.")
        private Instant createdDateTo;

        @ApiModelProperty(value = "The sharedUsers for customFolder.")
        @UniqueElements
        private List<Long> sharedUsers;

        @ApiModelProperty(value = "The sharedTeams for customFolder.")
        @UniqueElements
        private List<Long> sharedTeams;

        @ApiModelProperty(value = "The sharedUsers for customFolder.")
        @UniqueElements
        private List<Long> ownerUsers;

        @ApiModelProperty(value = "The sharedTeams for customFolder.")
        @UniqueElements
        private List<Long> ownerTeams;

        @ApiModelProperty(value = "The user who create the customFolder.")
        @UniqueElements
        private List<Long> creator;

        @ApiModelProperty(value = "The generalText for search customFolder.")
        @UniqueElements
        private List<String> generalText;
}
