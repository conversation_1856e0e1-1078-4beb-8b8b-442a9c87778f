package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class StartTerminationReasonConverter extends AbstractAttributeConverter<StartTerminationReason, Integer> {
    public StartTerminationReasonConverter() {
        super(StartTerminationReason::toDbValue, StartTerminationReason::fromDbValue);
    }
}
