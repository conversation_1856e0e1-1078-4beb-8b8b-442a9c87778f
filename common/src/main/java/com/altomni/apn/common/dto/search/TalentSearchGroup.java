package com.altomni.apn.common.dto.search;

import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class TalentSearchGroup {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "search condition in json string")
    private List<ComplexSearchParam> search;

    private SearchFilterDTO filter;

    private String module;

    private String index;

    private String timeZone;

    private String language;

    private OwnershipRestriction ownershipRestriction;

    public static TalentSearchGroup deepCopySearchGroup(TalentSearchGroup searchGroup) {
        var newSearchDTO = new TalentSearchGroup();
        ServiceUtils.myCopyProperties(searchGroup, newSearchDTO);
        return newSearchDTO;
    }
}
