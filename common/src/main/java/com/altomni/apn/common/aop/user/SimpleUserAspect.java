package com.altomni.apn.common.aop.user;

import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.repository.user.SimpleUserRepository;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/*
 * Created by <PERSON> on 9/25/2017.
 */

/**
 * Aspect for user
 * Attach CreatedUser when marked by annotation {@link SimpleUserAspect}
 */
@Aspect
@Component
public class SimpleUserAspect {

    public static final String CANDIDATE_MODIFIED = "last modified by candidate portal";
    public static final String CLIENT_MODIFIED = "last modified by client portal";

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final BiFunction<AbstractAuditingEntity, Function<AbstractAuditingEntity, String>, Optional<Long>> extractApnUserId = (entity, function) -> {
        String user = function.apply(entity);

        if(StringUtils.isEmpty(user)) {
            return Optional.empty();
        }

        if (!isAPNUser(user)) {
            return Optional.empty();
        }

        return Optional.ofNullable(user).map(by -> by.split(StrUtil.COMMA)[0]).map(Long::parseLong);
    };

    @Resource
    private SimpleUserRepository simpleUserRepository;

    @Pointcut("@annotation(AttachSimpleUser)")
    public void applicationPointcut() {
    }

    @Around("applicationPointcut()")
    public Object userAround(ProceedingJoinPoint joinPoint) throws Throwable {
        log.debug("Attach user in UserAspect");
        Object returnVal = joinPoint.proceed();
        if (!(returnVal instanceof ResponseEntity<?> responseEntity)) {
            return returnVal;
        }
        Object body = responseEntity.getBody();
        if (body instanceof AbstractAuditingEntity entity) {
            attachSimpleUsers(Collections.singletonList(entity));
        } else if (body instanceof Collection<?> collection && collection.stream().allMatch(item -> item instanceof AbstractAuditingEntity)) {
            attachSimpleUsers((Collection<? extends AbstractAuditingEntity>) collection);
        }
        return returnVal;
    }

    private void attachSimpleUsers(Collection<? extends AbstractAuditingEntity> entities) {
        List<Long> userIds = entities.stream().filter(Objects::nonNull).flatMap(entity -> {
            Optional<Long> creatorId = extractApnUserId.apply(entity, AbstractAuditingEntity::getCreatedBy);
            Optional<Long> modifierId = extractApnUserId.apply(entity, AbstractAuditingEntity::getLastModifiedBy);
            return Stream.of(creatorId, modifierId).filter(Optional::isPresent).map(Optional::get);
        }).toList();

        Map<Long, SimpleUser> simpleUserMap = simpleUserRepository.findAllById(userIds).stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));

        entities.forEach(entity -> {
            String createBy = entity.getCreatedBy();
            if (StrUtil.isNotBlank(createBy)) {
                extractApnUserId.apply(entity, AbstractAuditingEntity::getCreatedBy).map(simpleUserMap::get).ifPresent(entity::setCreatedUser);
            }

            String lastModifiedBy = entity.getLastModifiedBy();
            if (StrUtil.isNotBlank(lastModifiedBy)) {
                if (isTimesheetUser(lastModifiedBy)) {
                    String[] uid = lastModifiedBy.split(StrUtil.UNDERLINE);
                    if (uid.length != 3) {
                        return;
                    }
                    TimeSheetUserType timeSheetUserType = TimeSheetUserType.fromDbValue(Integer.valueOf(uid[2]));
                    SimpleUser timeSheetSimpleUser = new SimpleUser();
                    timeSheetSimpleUser.setUid(lastModifiedBy);
                    if (TimeSheetUserType.TALENT == timeSheetUserType) {
                        timeSheetSimpleUser.setFirstName(CANDIDATE_MODIFIED);
                    } else if (TimeSheetUserType.CLIENT == timeSheetUserType) {
                        timeSheetSimpleUser.setFirstName(CLIENT_MODIFIED);
                    }
                    timeSheetSimpleUser.setLastName("");
                    entity.setLastModifiedUser(timeSheetSimpleUser);
                } else {
                    extractApnUserId.apply(entity, AbstractAuditingEntity::getLastModifiedBy).map(simpleUserMap::get).ifPresent(entity::setLastModifiedUser);
                }
            }
        });
    }

    //判断是user还是timesheet user
    public static boolean isTimesheetUser(String str) {
        return str.contains(StrUtil.UNDERLINE);
    }

    //判断是user还是apn user
    public static boolean isAPNUser(String str) {
        return str.contains(StrUtil.COMMA);
    }
}
