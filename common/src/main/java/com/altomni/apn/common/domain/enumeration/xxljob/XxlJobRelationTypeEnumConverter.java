package com.altomni.apn.common.domain.enumeration.xxljob;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class XxlJobRelationTypeEnumConverter extends AbstractAttributeConverter<XxlJobRelationTypeEnum, Integer> {
    public XxlJobRelationTypeEnumConverter() {
        super(XxlJobRelationTypeEnum::toDbValue, XxlJobRelationTypeEnum::fromDbValue);
    }
}

