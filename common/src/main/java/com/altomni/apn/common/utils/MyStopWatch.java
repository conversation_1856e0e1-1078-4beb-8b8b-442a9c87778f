package com.altomni.apn.common.utils;

import org.springframework.util.StopWatch;

import java.text.NumberFormat;

/**
 * <AUTHOR>
 */
public class MyStopWatch extends StopWatch {

    public MyStopWatch() {
    }

    public MyStopWatch(String id) {
        super(id);
    }

    @Override
    public String prettyPrint() {
        StringBuilder sb = new StringBuilder("StopWatch '" + super.getId() + "': running time = " + super.getTotalTimeSeconds() + " s");
        sb.append('\n');
        sb.append("---------------------------------------------\n");
        sb.append("s         %     Task name\n");
        sb.append("---------------------------------------------\n");
        NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setGroupingUsed(false);
        NumberFormat pf = NumberFormat.getPercentInstance();
        pf.setGroupingUsed(false);
        for (TaskInfo task : getTaskInfo()) {
            sb.append(nf.format(task.getTimeSeconds())).append("  ");
            sb.append(pf.format(task.getTimeSeconds() / super.getTotalTimeSeconds())).append("  ");
            sb.append(task.getTaskName()).append('\n');
        }
        return sb.toString();
    }
}
