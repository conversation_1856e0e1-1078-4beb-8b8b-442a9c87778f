package com.altomni.apn.common.web.rest;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * REST controller for common api.
 */
@Slf4j
@RestController
public class CommonResource {

    public static final String BODY_CONTENT = "{ \"success\" : true }";
    private static final String CALLER_CLASS_KEY = "callerClass";
    private static final String CALLER_METHOD_KEY = "callerMethod";


    /**
     * GET  /liveness : get liveness information.
     *
     * @param log
     * @return the ResponseEntity with status 200 (OK) and the success info in body
     */
    public static ResponseEntity<String> getLiveness(Logger log){
        Map<String, String> callerInfo = getCallerInfo();
        String callerClass =  callerInfo.getOrDefault(CALLER_CLASS_KEY, "");
        log.info("[APN:{}] REST request gets liveness status.", callerClass);
        return ResponseEntity.ok().body(BODY_CONTENT);
    }
    /**
     * get caller info including className and classMethod
     * @return a hashmap with className and classMethod
     */
    public static Map<String, String> getCallerInfo() {
        Map<String, String> result = new HashMap<>();
        StackTraceElement[] stes = Thread.currentThread().getStackTrace();

        if (!Objects.isNull(stes) && stes.length > 2) {
            StackTraceElement ste = stes[2];
            result.put(CALLER_CLASS_KEY, ste.getClassName());
            result.put(CALLER_METHOD_KEY, ste.getMethodName());
        }
        return result;
    }
}
