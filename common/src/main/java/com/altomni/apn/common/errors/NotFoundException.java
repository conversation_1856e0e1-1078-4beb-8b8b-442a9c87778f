package com.altomni.apn.common.errors;

/**
 * Created by <PERSON> on 9/20/2017.
 */
public class NotFoundException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private final String message;

    private String description = null;

    public NotFoundException(String message) {
        super(message);
        this.message = message;
    }

    public NotFoundException(String message, Long id) {
        super(message);
        this.message = message;
        this.description = "id = " + id;
    }

    public ErrorVM getErrorVM() {
        return new ErrorVM(message, description);
    }

}
