package com.altomni.apn.common.domain.dict;

import com.altomni.apn.common.domain.enumeration.dict.EnumStatus;
import com.altomni.apn.common.domain.enumeration.dict.EnumStatusConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;

@ApiModel(description = "Enum user responsibility entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_user_responsibility")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumUserResponsibility implements Serializable {

    @Id
    private Long id;

    /** order column*/
    @Column(name = "label")
    private String label;

    @Column(name = "status")
    @Convert(converter = EnumStatusConverter.class)
    private EnumStatus status;

    @Size(max = 32)
    @Column(name = "talent_es_key", length = 32)
    private String talentEsKey;

    @Size(max = 32)
    @Column(name = "talent_note_es_key", length = 32)
    private String talentNoteEsKey;

    @Size(max = 32)
    @Column(name = "application_es_key", length = 32)
    private String applicationEsKey;

    @Size(max = 32)
    @Column(name = "company_es_key", length = 32)
    private String companyEsKey;

    @Size(max = 32)
    @Column(name = "job_es_key", length = 32)
    private String jobEsKey;

    @Size(max = 32)
    @Column(name = "folders_of_pre_submit_talents_es_key", length = 32)
    private String foldersOfPreSubmitTalentsEsKey;
}
