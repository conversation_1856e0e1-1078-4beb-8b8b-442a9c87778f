package com.altomni.apn.common.utils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CanadaProvinceTaxUtil {

    /**
     * 判断是否包含省
     * @param clientLocation
     * @param include
     * @return
     */
    public static String containsCanadaProvince(String clientLocation, List<String> include) {
        String[] location = clientLocation.split(",");
        for (String s : location) {
            if (include.contains(s.trim())) {
                return s.trim();
            }
        }
        return null;
    }
}
