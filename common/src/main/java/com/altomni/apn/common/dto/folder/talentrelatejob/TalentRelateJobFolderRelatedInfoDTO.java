package com.altomni.apn.common.dto.folder.talentrelatejob;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.user.RelateJobFolderUserInfo;
import com.altomni.apn.common.vo.talent.AddTalentsToFoldersOutput;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class TalentRelateJobFolderRelatedInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long jobId;

    private Long userId;


    private String folderId;

    private Instant expireTime;

    private Long tenantId;


    public TalentRelateJobFolderRelatedInfoDTO(Long jobId, Long userId, String folderId, Instant expireTime, Long tenantId) {
        this.jobId = jobId;
        this.userId = userId;
        this.folderId = folderId;
        this.expireTime = expireTime;
        this.tenantId = tenantId;
    }

}
