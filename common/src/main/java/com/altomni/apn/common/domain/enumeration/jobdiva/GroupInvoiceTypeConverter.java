package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class GroupInvoiceTypeConverter extends AbstractAttributeConverter<GroupInvoiceType, Integer> {
    public GroupInvoiceTypeConverter() {
        super(GroupInvoiceType::toDbValue, GroupInvoiceType::fromDbValue);
    }
}
