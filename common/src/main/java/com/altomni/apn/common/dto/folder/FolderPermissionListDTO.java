package com.altomni.apn.common.dto.folder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class FolderPermissionListDTO {

    private Long userId;

    @ApiModelProperty(value = "Folders I created")
    List<FolderNamePermissionDTO> myFolderList;


    @ApiModelProperty(value = "Folders shared to me")
    List<FolderNamePermissionDTO> sharedFolderList;

}
