package com.altomni.apn.common.service.enums;

import com.altomni.apn.common.domain.dict.EnumVoipCallType;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EnumVoipCallTypeService {

    @Resource
    EnumCommonService enumCommonService;


    public List<EnumDictVO> findAllOrderBySortType(SortType type) {
        List<EnumVoipCallType> enumVoipCallTypes = enumCommonService.findAllEnumVoipCallType();
        List<EnumDictVO> voList = enumVoipCallTypes.stream().map(callType -> new EnumDictVO(callType.getId(), SortType.CN.equals(type) ? callType.getCnDisplay() : callType.getEnDisplay(),
                callType.getName(), null, callType.getEnDisplayOrder() , callType.getCnDisplayOrder(), null)).toList();
        return sortNodes(voList, type);
    }


    public EnumDictVO findByNameAndSortType(String name, SortType type) {
        EnumVoipCallType callType = enumCommonService.findEnumVoipCallType(name);
        if(callType != null) return new EnumDictVO(callType.getId(), SortType.CN.equals(type) ? callType.getCnDisplay() : callType.getEnDisplay(), callType.getName(), null, callType.getEnDisplayOrder(), callType.getCnDisplayOrder(), null);
        else return null;
    }

    private List<EnumDictVO> sortNodes(List<EnumDictVO> nodes, SortType type) {
        return nodes.stream()
                .sorted(Comparator.comparingLong(SortType.CN.equals(type) ? EnumDictVO::getCnDisplayOrder : EnumDictVO::getEnDisplayOrder))
                .collect(Collectors.toList());
    }
}
