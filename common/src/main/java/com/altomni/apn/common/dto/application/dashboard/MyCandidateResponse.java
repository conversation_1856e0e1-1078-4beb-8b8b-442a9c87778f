package com.altomni.apn.common.dto.application.dashboard;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MyCandidateResponse implements Serializable {

    private static final long serialVersionUID = 4948887028012258882L;

    private List<Object> elements;

    private List<StatusCount> statusCounts;

    private Set<MyCandidateStatusFilter> status;

    private Integer totalCounts;
}
