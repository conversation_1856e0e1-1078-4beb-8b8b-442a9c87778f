package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentSingleSaveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 性别认同
     */
    @ApiModelProperty(value = "Talent gender enum name.")
    private String gender;

    /**
     * 所在地
     */
    @ApiModelProperty(value = "Talent current location. Save to talent basic information table.")
    private LocationDTO currentLocation;

    /**
     * 工作许可
     */
    @ApiModelProperty(value = "work authorization")
    private List<Integer> workAuthorization;

    /**
     * 职能
     */
    @ApiModelProperty(value = "job functions, load data in biz_dict service")
    private List<Integer> jobFunctions;

    /**
     * 语言
     */
    @ApiModelProperty(value = "languages, load data in biz_dict service")
    private List<Integer> languages;

    /**
     * 行业
     */
    @ApiModelProperty(value = "industries talent specified in same type")
    private List<Integer> industries;

    /**
     * 联系方式
     */
    @ApiModelProperty(value = "list of talent contacts.")
    private List<TalentContactDTO> contacts;


}
