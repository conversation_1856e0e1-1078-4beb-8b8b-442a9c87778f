package com.altomni.apn.common.dto.search;


import lombok.Data;

import java.util.List;

@Data
public class SearchParam {

    private Relation relation;

    private List<ConditionParam> condition;


    public static SearchParam of(Relation relation, List<ConditionParam> condition) {
        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(relation);
        searchParam.setCondition(condition);
        return searchParam;
    }

    public static SearchParam ofAnd(List<ConditionParam> condition) {
        return of(Relation.AND, condition);
    }

    public static SearchParam ofSimple(ConditionParam condition) {
        return of(Relation.AND, List.of(condition));
    }
}
