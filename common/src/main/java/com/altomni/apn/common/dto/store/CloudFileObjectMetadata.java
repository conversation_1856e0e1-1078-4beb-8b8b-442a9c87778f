package com.altomni.apn.common.dto.store;

import lombok.Data;

import java.util.Map;

@Data
public class CloudFileObjectMetadata {

    private String fileName;

    private String contentType;

    private long contentLength;

    private String key;

    private String bucketName;

    private String region;

    private String folder;

    private byte[] content;

    private String s3Link;

    private Map<String, String> userMetadata;

    public CloudFileObjectMetadata() {}

    public CloudFileObjectMetadata(String fileName, String contentType, long contentLength, String key, String bucketName, String region) {
        this.fileName = fileName;
        this.contentType = contentType;
        this.contentLength = contentLength;
        this.key = key;
        this.bucketName = bucketName;
        this.region = region;
    }

    public CloudFileObjectMetadata(String fileName, String contentType, long contentLength, String key, String bucketName, String region, Map<String, String> userMetadata) {
        this.fileName = fileName;
        this.contentType = contentType;
        this.contentLength = contentLength;
        this.key = key;
        this.bucketName = bucketName;
        this.region = region;
        this.userMetadata = userMetadata;
    }

    public CloudFileObjectMetadata(String fileName, String contentType, long contentLength, String key, String bucketName, String region, String folder, byte[] content, String s3Link, Map<String, String> userMetadata) {
        this.fileName = fileName;
        this.contentType = contentType;
        this.contentLength = contentLength;
        this.key = key;
        this.bucketName = bucketName;
        this.region = region;
        this.folder = folder;
        this.content = content;
        this.s3Link = s3Link;
        this.userMetadata = userMetadata;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public long getContentLength() {
        return contentLength;
    }

    public void setContentLength(long contentLength) {
        this.contentLength = contentLength;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public Map<String, String> getUserMetadata() {
        return userMetadata;
    }

    public void setUserMetadata(Map<String, String> userMetadata) {
        this.userMetadata = userMetadata;
    }

}