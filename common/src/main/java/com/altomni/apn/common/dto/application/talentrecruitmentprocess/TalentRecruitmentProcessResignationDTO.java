package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.altomni.apn.common.domain.enumeration.application.ResignationReason;
import com.altomni.apn.common.domain.enumeration.application.ResignationReasonConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * A TalentRecruitmentProcessResignationDTO.
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessResignationDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long talentRecruitmentProcessId;

    private LocalDate resignDate;

    @Convert(converter = ResignationReasonConverter.class)
    private ResignationReason resignationReason;

    @Column(name = "note")
    private String note;
}
