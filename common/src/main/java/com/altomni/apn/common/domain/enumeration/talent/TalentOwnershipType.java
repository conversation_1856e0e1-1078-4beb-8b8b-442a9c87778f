package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TalentOwnershipType enumeration.
 */
public enum TalentOwnershipType implements ConvertedEnum<Integer>{
    SHARE(0),
    OWNER(1), // application related owner
    TALENT_OWNER(2), // talent owner

    TENANT_SHARE(3),
    ;

    private final Integer dbValue;

    TalentOwnershipType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TalentOwnershipType, Integer> resolver =
        new ReverseEnumResolver<>(TalentOwnershipType.class, TalentOwnershipType::toDbValue);

    public static TalentOwnershipType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
