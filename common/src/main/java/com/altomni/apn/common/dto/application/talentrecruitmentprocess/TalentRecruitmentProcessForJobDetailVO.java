package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessForJobDetailVO implements Serializable {

    @Serial
    private static final long serialVersionUID = -3781212343322066152L;

    private Long id;

    private Long talentId;

    private String talentName;

    private NodeType lastNodeType;

    private NodeStatus lastNodeStatus;

    private String recruiter;

    private Long jobId;

    private String jobTitle;

    private Long companyId;

    private String companyName;

    private Instant lastModifiedDate;

    private String accountManager;

    private JobStatus jobStatus;

    private Instant postingTime;

    private TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate;

//    private RangeDTO preferredSalaryRange;
//
//    private RateUnitType preferredPayType;
//
//    private Integer preferredCurrency;

    private EliminateReason eliminateReason;

    private Integer interviewCount;

    /**
     * true: 已离职， false：未离职
     */
    private boolean resigned;

    private Long agencyId;

    private ConfidentialInfoDto confidentialInfo;
}
