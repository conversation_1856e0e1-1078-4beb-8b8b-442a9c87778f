package com.altomni.apn.common.vo.reportsubscription;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.enumeration.reportSubscriptions.*;
import com.altomni.apn.common.vo.calendar.CalendarEventAttendeeVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportSubscriptionVO {

    @Id
    private Long id;

    private Long tenantId;

    private String name;

    @Convert(converter = PushTimeTypeConverter.class)
    private PushTimeType pushTimeType;

    private Integer dayOfWeek;

    private Integer dayOfMonth;

    private String sendTime;

    @Convert(converter = DataPeriodConverter.class)
    private DataPeriod dataPeriod;

    private LocalDate startDate;

    private LocalDate endDate;

    @Convert(converter = ReportTypeConverter.class)
    private ReportType reportType;

    //创建自动激活
    @Convert(converter = ActiveTypeConverter.class)
    private ActiveType isActive = ActiveType.ACTIVE;

    private String recipients;

    private String pushMethods;

    @JsonIgnore
    private String dataPermissionJson;

    @Transient
    private List<Long> userIdList;

    @Transient
    private List<Long> teamIdList;

    @Transient
    private List<Long> recipientList;

    @Transient
    private List<PushMethod> pushMethodList;

    public List<Long> getUserIdList() {
        if (StrUtil.isBlank(dataPermissionJson)) {
            return new ArrayList<>();
        }
        JSONObject jsonObject = JSONUtil.parseObj(dataPermissionJson);
        if (jsonObject.containsKey("userIdList")) {
            return jsonObject.getJSONArray("userIdList").toList(Long.class);
        }
        return new ArrayList<>();
    }

    public List<Long> getTeamIdList() {
        if (StrUtil.isBlank(dataPermissionJson)) {
            return new ArrayList<>();
        }
        JSONObject jsonObject = JSONUtil.parseObj(dataPermissionJson);
        if (jsonObject.containsKey("teamIdList")) {
            return jsonObject.getJSONArray("teamIdList").toList(Long.class);
        }
        return new ArrayList<>();
    }

    public List<Long> getRecipientList() {
        if (StrUtil.isNotBlank(recipients)) {
            List<Long> list = Arrays.stream(recipients.split(",")).map(Long::parseLong).toList();
            return list;
        }
        return new ArrayList<>();
    }

    public List<PushMethod> getPushMethodList() {
        if (StrUtil.isNotBlank(pushMethods)) {
            List<PushMethod> pushMethods1 = Arrays.stream(pushMethods.split(",")).map(Integer::parseInt).map(PushMethod::fromDbValue).toList();
            return pushMethods1;
        }
        return new ArrayList<>();
    }
}
