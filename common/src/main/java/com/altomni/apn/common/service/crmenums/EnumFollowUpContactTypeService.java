package com.altomni.apn.common.service.crmenums;

import com.altomni.apn.common.domain.crmenums.EnumFollowUpContactType;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.crmenums.EnumCrmCommonService;
import com.altomni.apn.common.vo.crmenums.DictVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class EnumFollowUpContactTypeService {

    @Resource
    private EnumCrmCommonService enumCrmCommonService;

    public List<DictVO> findAllOrderBySortType(SortType type) {
        List<EnumFollowUpContactType> enumFollowUpContactTypeList = enumCrmCommonService.findAllEnumFollowUpContactType();
        return enumFollowUpContactTypeList.stream().map(t -> EnumFollowUpContactType.fromEnumContactType(t, type)).sorted(Comparator.comparing(DictVO::getDisplayOrder)).collect(Collectors.toList());
    }

    public Map<Integer, String> getAllFollowUpContactTypeMap() {
        List<EnumFollowUpContactType> enumFollowUpContactTypeList = enumCrmCommonService.findAllEnumFollowUpContactType();
        return enumFollowUpContactTypeList.stream().collect(Collectors.toMap(EnumFollowUpContactType::getId,EnumFollowUpContactType::getName));
    }
}
