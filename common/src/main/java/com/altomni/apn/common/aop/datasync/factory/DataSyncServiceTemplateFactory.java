package com.altomni.apn.common.aop.datasync.factory;

import com.altomni.apn.common.aop.datasync.template.DataSyncHandlerTemplate;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据同步服务模板工厂，在启动的时候扫描所有实现DataSyncHandlerTemplate接口的类，并放入map中
 * <AUTHOR>
 */
@Component
public class DataSyncServiceTemplateFactory implements BeanPostProcessor {

    private static final Map<SyncIdTypeEnum, DataSyncHandlerTemplate> dataSyncServiceTemplateHashMap = new HashMap<>();

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof DataSyncHandlerTemplate dataSyncHandlerTemplate) {
            SyncIdTypeEnum typeEnumList = dataSyncHandlerTemplate.getType();
            dataSyncServiceTemplateHashMap.put(typeEnumList, dataSyncHandlerTemplate);
        }
        return bean;
    }

    public static Map<SyncIdTypeEnum, DataSyncHandlerTemplate> getDataSyncServiceTemplateHashMap() {
        return dataSyncServiceTemplateHashMap;
    }

}
