package com.altomni.apn.common.dto.message;

import com.altomni.apn.common.enumeration.enums.NoPoachingRemindType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageCreateWithNoPoachingSubmitDTO {

    private Set<Long> needRemindUserIds;

    private NoPoachingRemindType remindType;

    private Long tenantId;

    private Long companyId;

    private String companyName;

    private String talentFullName;

    private Long talentId;

    private Long submitUserId;

    private String submitUserFullName;

    private Long jobId;

    private String jobTitle;


}
