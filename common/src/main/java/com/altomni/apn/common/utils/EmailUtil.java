package com.altomni.apn.common.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;


@Component
@RefreshScope
public class EmailUtil {

    private final static ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("async-send-email-pool-").build();

    public final static ExecutorService executorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 3,
            Runtime.getRuntime().availableProcessors() * 5, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100000), THREAD_FACTORY);

    @Value("${application.mainPath.baseUrl:}")
    private String baseUrl;

    private final static String EMAIL_HEAD = "<pre>You have received a new job application. Please review the submission.<br /><br /></pre>";

    private final static String EMAIL_TAIL = "<pre>Thank you,</pre><pre>The IntelliPro Group Team</pre>";

    private final static String EMAIL_CONTENT_FIRST_NAME = "<p><strong>First Name:</strong>";

    private final static String EMAIL_CONTENT_LAST_NAME = "<p><strong>Last Name:</strong>";

    private final static String EMAIL_CONTENT_EMAIL = "<p><strong>Email:</strong>";

    private final static String EMAIL_CONTENT_RESUME = "<p><strong>Resume:</strong>";

    private final static String EMAIL_CONTENT_JOB_ID = "<p><strong>Researcher Job #</strong>";

    private final static String EMAIL_SUBJECT = "Job # ";

    private final static char FILLED_CHAR = ' ';

    private final static int UNIFORM_LENGTH = 20;

    private final static String EMAIL_JOB_LINK_DETAIL = "/jobs/detail/";

    private final static String HTML_A_HREF_PREFIX = "<a href=\"" ;

    private final static String HTML_A_HREF_SUFFIX = "</a>";

    private final static String HTML_P_HREF_SUFFIX = "</p>";

    private final static String LEFT_PARENTHESIS = "(";

    private final static String RIGHT_PARENTHESIS = ")";

    private final static String RIGHT_BRACKET = "\">";

    public String getEmailContent(long jobId, String firstName, String lastName, String email, String resumeName, String resumeLink) {
        StringBuilder emailContentBuilder = new StringBuilder();
        emailContentBuilder.append(EMAIL_HEAD).append(getEmailJobId(jobId)).append(getEmailFirstName(firstName)).
            append(getEmailLastName(lastName)).append(getEmail(email)).append(getEmailResume(resumeLink, resumeName)).append(EMAIL_TAIL);
        return  emailContentBuilder.toString();
    }

    public String getEmailSubject(long jobId, String jobName) {
        return  EMAIL_SUBJECT + jobId + LEFT_PARENTHESIS + jobName + RIGHT_PARENTHESIS;
    }

    public String getJobUrlLink(long id) {
        return HTML_A_HREF_PREFIX + baseUrl + EMAIL_JOB_LINK_DETAIL + id + RIGHT_BRACKET + id + HTML_A_HREF_SUFFIX;
    }

    public String getResumeUrlLink(String resumeLink, String resumeName) {
        return HTML_A_HREF_PREFIX + resumeLink + RIGHT_BRACKET + resumeName + HTML_A_HREF_SUFFIX;
    }

    public String getEmailJobId(long jobId) {
        return EMAIL_CONTENT_JOB_ID + getJobUrlLink(jobId) + HTML_P_HREF_SUFFIX;
    }

    public String getEmailResume(String resumeLink, String resumeName) {
        return EMAIL_CONTENT_RESUME + getResumeUrlLink(resumeLink, resumeName) + HTML_P_HREF_SUFFIX;
    }

    public String getEmail(String email) {
        return EMAIL_CONTENT_EMAIL + email + HTML_P_HREF_SUFFIX;
    }

    public String getEmailLastName(String lastName) {
        return EMAIL_CONTENT_LAST_NAME + lastName + HTML_P_HREF_SUFFIX;
    }

    public String getEmailFirstName(String firstName) {
        return EMAIL_CONTENT_FIRST_NAME + firstName + HTML_P_HREF_SUFFIX;
    }

}
