package com.altomni.apn.common.service.enums;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.dict.CurrencyRateDay;
import com.altomni.apn.common.repository.enums.CurrencyRateDayRepository;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * currency rate day service impl
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class CurrencyRateDayService {

    @Resource
    CurrencyRateDayRepository currencyRateDayRepository;


    /**
     * save方法
     *
     * @param currencyRateDay
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(CurrencyRateDay currencyRateDay) {
        currencyRateDayRepository.save(currencyRateDay);
        log.info("save: userId:{},param:{}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(currencyRateDay));
    }

    /**
     * save方法
     *
     * @param currencyRateDayList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<CurrencyRateDay> currencyRateDayList) {
        if (!currencyRateDayList.isEmpty()) {
            currencyRateDayRepository.saveAll(currencyRateDayList);
            log.info("batch save: userId:{},param:{}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(currencyRateDayList));
        }
    }

    /**
     * 删除方法
     *
     * @param idList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> idList) {
        if (!idList.isEmpty()) {
            currencyRateDayRepository.deleteAllByIdInBatch(idList);
            log.info("delete batch: userId:{},param:{}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(idList));
        }
    }

    /**
     * 删除方法
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        currencyRateDayRepository.deleteById(id);
        log.info("delete by id: userId:{},param:{}", SecurityUtils.getUserId(), id);
    }
}