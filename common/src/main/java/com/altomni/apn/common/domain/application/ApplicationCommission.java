//package com.altomni.apn.common.domain.application;
//
//import com.alibaba.fastjson.JSONObject;
//import com.altomni.apn.common.domain.AbstractAuditingEntity;
//import com.altomni.apn.common.domain.enumeration.user.UserRole;
//import com.altomni.apn.common.domain.enumeration.user.UserRoleConverter;
//
//import javax.persistence.*;
//import javax.validation.constraints.NotNull;
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.Arrays;
//import java.util.HashSet;
//import java.util.Set;
//
///**
// * A ApplicationCommission.
// * <AUTHOR>
// */
//@Entity
//@Table(name = "application_commission")
//public class ApplicationCommission extends AbstractAuditingEntity implements Serializable {
//
//    private static final long serialVersionUID = 5352762482518639811L;
//
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//
//    @NotNull
//    @Column(name = "user_id")
//    private Long userId;
//
//    @Column(name = "user_full_name")
//    private String userFullName;
//
//    @NotNull
//    @Convert(converter = UserRoleConverter.class)
//    @Column(name = "user_role", nullable = false)
//    private UserRole userRole;
//
//    @Column(name = "percentage")
//    private BigDecimal percentage = new BigDecimal(0);
//
//    @NotNull
//    @Column(name = "application_id")
//    private Long applicationId;
//
//    public JSONObject toJSON() {
//        return new JSONObject()
//                .fluentPut("userId", userId)
//                .fluentPut("userRole", userRole)
//                .fluentPut("percentage", percentage)
//                .fluentPut("applicationId", applicationId);
//    }
//
//    public static Set<UserRole> RecruiterAndSourcer = new HashSet<>(Arrays.asList(UserRole.RECRUITER, UserRole.SOURCER));
//
//    public static Set<UserRole> allButExcludeOwner = new HashSet<>(Arrays.asList(UserRole.AM, UserRole.RECRUITER, UserRole.SOURCER, UserRole.DM, UserRole.AC, UserRole.PR));
//
//    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    public String getUserFullName() {
//        return userFullName;
//    }
//
//    public void setUserFullName(String userFullName) {
//        this.userFullName = userFullName;
//    }
//
//    public UserRole getUserRole() {
//        return userRole;
//    }
//
//    public void setUserRole(UserRole userRole) {
//        this.userRole = userRole;
//    }
//
//    public BigDecimal getPercentage() {
//        return percentage;
//    }
//
//    public void setPercentage(BigDecimal percentage) {
//        this.percentage = percentage;
//    }
//
//    public Long getApplicationId() {
//        return applicationId;
//    }
//
//    public void setApplicationId(Long applicationId) {
//        this.applicationId = applicationId;
//    }
//
//    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        }
//        if (!(o instanceof ApplicationCommission)) {
//            return false;
//        }
//        return id != null && id.equals(((ApplicationCommission) o).id);
//    }
//
//    @Override
//    public int hashCode() {
//        return 31;
//    }
//
//    @Override
//    public String toString() {
//        return "ApplicationCommission{" +
//                "id=" + id +
//                ", userId=" + userId +
//                ", userFullName='" + userFullName + '\'' +
//                ", userRole=" + userRole +
//                ", percentage=" + percentage +
//                ", applicationId=" + applicationId +
//                '}';
//    }
//}
