package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum InvoicingPaymentMethodType implements ConvertedEnum<Integer> {

    MONEY_ORDER(1,"汇票"),
    SAVINGS_CARD(2,"储蓄卡"),
    CREDIT_CARD(3,"信用卡"),
    ELECTRONIC_REMITTANCE(4,"电子汇款"),
    PREPAYMENT(5,"预付金");

    private final int dbValue;

    private final String description;

    InvoicingPaymentMethodType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoicingPaymentMethodType, Integer> resolver = new ReverseEnumResolver<>(InvoicingPaymentMethodType.class, InvoicingPaymentMethodType::toDbValue);

    public static InvoicingPaymentMethodType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
