package com.altomni.apn.common.domain.enumeration.application;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ActiveStatus enumeration.
 */
public enum ActiveStatus implements ConvertedEnum<Integer> {

    /**
     * 有效
     */
    ACTIVE(0),

    /**
     * 无效
     */
    INACTIVE(1);

    private final int dbValue;

    ActiveStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ActiveStatus, Integer> resolver =
        new ReverseEnumResolver<>(ActiveStatus.class, ActiveStatus::toDbValue);

    public static ActiveStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
