package com.altomni.apn.common.dto.user;

import com.altomni.apn.common.auth.LoginInformation;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.io.Serializable;
import java.util.Collection;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * A DTO representing a user, with only the public attributes.
 * <AUTHOR>
 */
@Data
public class AgencyLoginUserDTO implements Serializable, LoginInformation {

    //agency info
    private Long agencyId;

    private String agencyName;

    private String agencyContactName;


    //owner user info
    private Long id;

    private String uid;

    private String username;

    @ApiModelProperty(value = "first name")
    private String firstName;

    @ApiModelProperty(value = "last name")
    private String lastName;

    @ApiModelProperty(value = "email address. need to be unique.")
    private String email;

    @ApiModelProperty(value = "Whether user is activated. Default is true. Read Only.")
    private boolean activated;

    @ApiModelProperty(value = "url link to user's image")
    private String imageUrl;

    @ApiModelProperty(value = "phone number, this is used for consumer registered with phone.")
    private String phone;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    private Long tenantId;

    private String tenantName;

    private Set<String> roles;

    // to adapt InformationLogin
    private String timezone;

    @JsonIgnore
    public String getLogin() {
        if (this.username != null) {
            return username;
        }
        return this.email;
    }

//    @Override
    @JsonIgnore
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (roles == null) {
            return Collections.emptyList();
        }
        return roles.stream().map(SimpleGrantedAuthority::new).collect(Collectors.toSet());
    }

    public static AgencyLoginUserDTO fromOwnerUser(LoginUserDTO simpleUser) {
        AgencyLoginUserDTO agencyLoginUserDTO = new AgencyLoginUserDTO();
        agencyLoginUserDTO.setId(simpleUser.getId());
        agencyLoginUserDTO.setUid(simpleUser.getUid());
        agencyLoginUserDTO.setUsername(simpleUser.getUsername());
        agencyLoginUserDTO.setFirstName(simpleUser.getFirstName());
        agencyLoginUserDTO.setLastName(simpleUser.getLastName());
        agencyLoginUserDTO.setEmail(simpleUser.getEmail());
        agencyLoginUserDTO.setActivated(true);
        agencyLoginUserDTO.setImageUrl(simpleUser.getImageUrl());
        agencyLoginUserDTO.setPhone(simpleUser.getPhone());
        agencyLoginUserDTO.setTenantId(simpleUser.getTenantId());
        agencyLoginUserDTO.setTimezone(simpleUser.getTimezone());
        return agencyLoginUserDTO;
    }

    @Override
    public String getIp() {
        return "";
    }

    @Override
    public String getTimezone() {
        return timezone;
    }

    @Override
    public TenantUserTypeEnum getUserType() {
        return TenantUserTypeEnum.EMPLOYER;
    }
}
