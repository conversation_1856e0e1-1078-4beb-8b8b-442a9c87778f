package com.altomni.apn.common.domain.enumeration.job;

import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.*;
import java.util.stream.Collectors;

/**
 * the value in database is Math.pow(2, permission-1)
 * <AUTHOR>
 */
public enum JobPermission {

    RECRUITER(3, 4),

    DELIVERY_MANAGER(5, 8),

    PRIMARY_RECRUITER(5, 16),

    AC(7, 64),

    AM(9, 256);

    public static List<Integer> JobPermissionManages = Arrays.asList(JobPermission.AM.value, JobPermission.AC.value, JobPermission.DELIVERY_MANAGER.value);

    private static Map<String, JobPermission> strMap = new HashMap<>();

    static {
        for (JobPermission jobPermission: JobPermission.values()) {
            strMap.put(jobPermission.name(), jobPermission);
        }
    }

    public int code;
    public int value;
    JobPermission(int code, int value) {
        this.code = code;
        this.value = value;
    }

    public Integer toDbValue() {
        return value;
    }

    public static Set<String> parseJobPermission(int permission) {
        return strMap.values().stream()
            .filter(jobPermission -> (permission & jobPermission.value) != 0)
            .map(Enum::name)
            .collect(Collectors.toSet());
    }

    public static Integer getPermission(String permission) {
        JobPermission jobPermission=strMap.get(permission);
        if(jobPermission != null) {
            return jobPermission.value;
        }
        return null;
    }

    public static Integer makeupJobPermission(Set<String> permissionSet) {
        Integer permission = 0;
        for (String jobPermission : permissionSet) {
            if (strMap.get(jobPermission) != null) {
                permission |= strMap.get(jobPermission).value;
            }
        }
        return permission;
    }

    public static final ReverseEnumResolver<JobPermission, Integer> resolver =
            new ReverseEnumResolver<>(JobPermission.class, JobPermission::toDbValue);

    public static JobPermission fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    /**
     * HereBy greater means the user have higher permission.
     * 1100 > 100
     * 100 = 101
     * @param left
     * @param right
     * @return
     */
    public static boolean greaterThan(int left, int right) {
        return (left > right) && ((left ^ right) > right);
    }

    public static boolean containsAm(int left) {
        return left / 256 == 1;
    }

    public static boolean containsAc(int left) {
        if (containsAm(left)) {
            return (left % 256) / 64 == 1;
        }
        return left / 64 == 1;
    }

    public static boolean containsPrimaryRecruiter(int left) {
        if (containsAm(left)) {
            int minusAm = left % 256;
            if (containsAc(minusAm)) {
                return (minusAm % 64) / 16 == 1;
            }
            return minusAm / 16 == 1;
        }
        if (containsAc(left)) {
            return (left % 64) / 16 == 1;
        }
        return left / 16 == 1;
    }
}
