package com.altomni.apn.common.domain.enumeration.voip;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class PhoneCallStatusConverter extends AbstractAttributeConverter<PhoneCallStatus, Integer> {
    public PhoneCallStatusConverter() {
        super(PhoneCallStatus::toDbValue, PhoneCallStatus::fromDbValue);
    }
}
