package com.altomni.apn.common.vo.recruiting;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TeamAdoptionReportVO{
    private Long teamId;

    private String teamName;

    private Long numberOfUsers;

    private Long averageActiveDuration;

    private Long noUsageUsersCount;

    private Long lowAverageUserCount;
}
