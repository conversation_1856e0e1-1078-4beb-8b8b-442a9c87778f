package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoicingStatusConverter extends AbstractAttributeConverter<InvoicingStatus, Integer> {
    public InvoicingStatusConverter() {
        super(InvoicingStatus::toDbValue, InvoicingStatus::fromDbValue);
    }
}
