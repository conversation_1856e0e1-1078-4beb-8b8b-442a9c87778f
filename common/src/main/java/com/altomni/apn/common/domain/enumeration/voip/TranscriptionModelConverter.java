package com.altomni.apn.common.domain.enumeration.voip;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TranscriptionModelConverter extends AbstractAttributeConverter<TranscriptionModel, Integer> {
    public TranscriptionModelConverter() {
        super(TranscriptionModel::toDbValue, TranscriptionModel::fromDbValue);
    }
}
