package com.altomni.apn.common.dto.rater;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RaterRequestBody implements Serializable {

    private static final long serialVersionUID = 1135584130715181117L;

    @JsonProperty("applications")
    private List<RaterRequestApplication> applications;

    @SerializedName("uuid")
    private String uuid = null;

    @SerializedName("geoDistance")
    private String geoDistance = null;

    @SerializedName("restrictions")
    private Object restrictions = null;

    @SerializedName("minNResults")
    private Integer minNResults = 7;

    @SerializedName("maxNResults")
    private Integer maxNResults = 50;

    @SerializedName("minScore")
    private Double minScore = 0.5;

    @SerializedName("talentLastModifiedDate")
    private String talentLastModifiedDate;

    /**
     * type: string
     * format: YYYY-MM-DDTHH:MM:SS.SSSZ
     */
    @SerializedName("jobLastModifiedDate")
    private String jobLastModifiedDate;

    @SerializedName("talentId")
    private String talentId;

    @SerializedName("useTenantTalent")
    private Boolean useTenantTalent;

    @SerializedName("useCommonTalent")
    private Boolean useCommonTalent;

    public RaterRequestBody applications(List<RaterRequestApplication> applications) {
        this.applications = applications;
        return this;
    }

    public RaterRequestBody useCommonTalent(Boolean useCommonTalent) {
        this.useCommonTalent = useCommonTalent;
        return this;
    }

    public RaterRequestBody useTenantTalent(Boolean useTenantTalent) {
        this.useTenantTalent = useTenantTalent;
        return this;
    }

}
