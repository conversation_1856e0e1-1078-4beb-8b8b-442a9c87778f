package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CreationTalentTypeConverter extends AbstractAttributeConverter<CreationTalentType, Integer> {
    public CreationTalentTypeConverter() {
        super(CreationTalentType::toDbValue, CreationTalentType::fromDbValue);
    }
}
