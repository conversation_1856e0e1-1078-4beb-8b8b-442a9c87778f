package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CalendarEventTypeEnum implements ConvertedEnum<Integer> {

    CALENDAR_EVENT(0),
    TALENT_NOTE(1),
    JOB(2),
    COMPANY(3),
    OFFER(4),
    OFFER_ACCEPT(5 ),
    COMMISSION(6),
    ONBOARD(7),
    INTERVIEW(8);

    private Integer dbValue;
    //为null这不需要处理,goToId 为自己的 reference_id, 否则返回goToModuleSql查询的结果

    CalendarEventTypeEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CalendarEventTypeEnum, Integer> resolver = new ReverseEnumResolver<>(CalendarEventTypeEnum.class, CalendarEventTypeEnum::toDbValue);

    public static CalendarEventTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
