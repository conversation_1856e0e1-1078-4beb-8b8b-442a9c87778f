package com.altomni.apn.common.domain;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * Base abstract class for entities which will hold definitions for created, last modified, created by,
 * last modified by attributes.
 */
@MappedSuperclass
public abstract class ManualAbstractPermissionAuditingEntity extends ManualAbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(name = "puser_id")
    private Long permissionUserId;

    @Column(name = "pteam_id")
    private Long permissionTeamId;

    public Long getPermissionUserId() {
        return permissionUserId;
    }

    public void setPermissionUserId(Long permissionUserId) {
        this.permissionUserId = permissionUserId;
    }

    public Long getPermissionTeamId() {
        return permissionTeamId;
    }

    public void setPermissionTeamId(Long permissionTeamId) {
        this.permissionTeamId = permissionTeamId;
    }
}
