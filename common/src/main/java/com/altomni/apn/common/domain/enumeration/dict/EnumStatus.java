package com.altomni.apn.common.domain.enumeration.dict;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The NodeStatus enumeration.
 */
public enum EnumStatus implements ConvertedEnum<Integer> {
    ACTIVE(0),

    INACTIVE(1);

    private final Integer dbValue;

    EnumStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<EnumStatus, Integer> resolver =
        new ReverseEnumResolver<>(EnumStatus.class, EnumStatus::toDbValue);

    public static EnumStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
