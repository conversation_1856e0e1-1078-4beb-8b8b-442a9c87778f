package com.altomni.apn.common.dto.application.dashboard;

import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MyCandidateSearchParam {

    private String talentName;

    private Instant fromDate;

    private Instant toDate;

    private List<UserRole> userRoles;
}
