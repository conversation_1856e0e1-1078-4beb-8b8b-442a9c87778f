package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.dict.EnumWorkAuthorization;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class EnumWorkAuthorizationService {

    private final Logger log = LoggerFactory.getLogger(EnumWorkAuthorizationService.class);

    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumDictDTO> findAllOrderBySortType(SortType type) {
        log.info("[APN: EnumWorkAuthorizationService @{}] request to get workauthorization enum data, type: {}", SecurityUtils.getUserId(), type);

        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        Map<Long, EnumWorkAuthorization> enumWorkAuthorizationMap = enumWorkAuthorizationList.stream().collect(Collectors.toMap(EnumWorkAuthorization::getId, EnumWorkAuthorization -> EnumWorkAuthorization));
        List<EnumWorkAuthorization> result = new ArrayList<>();
        if(SortType.EN.equals(type)){
            enumWorkAuthorizationList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumWorkAuthorization::getEnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
        } else {
            enumWorkAuthorizationList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumWorkAuthorization::getCnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
        }

        List<EnumWorkAuthorization> parentCategoryList = findAllByParentCategoryIn();
        if (CollectionUtil.isNotEmpty(result) && CollectionUtil.isNotEmpty(parentCategoryList)) {
            if (SortType.EN.equals(type)){
                result.forEach(r -> r.setCnLable(false));
                parentCategoryList.forEach(p -> p.setCnLable(false));
            }else{
                result.forEach(r -> r.setCnLable(true));
                parentCategoryList.forEach(p -> p.setCnLable(true));
            }
            Map<String, String> displayMap = getUINameByNames(result.stream().map(EnumWorkAuthorization::getName).collect(Collectors.toList()));
            //set parent id by parentCategory and format EnumDictDTO for webSide
            List<EnumDictDTO> dtoList = result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay()))
                    .peek(s -> parentCategoryList.stream().filter(p ->ObjectUtil.isNotNull(s.getParentCategory()) && s.getParentCategory().equals(p.getName()))
                            .forEach(pc ->{
                                s.setParentCategory(StrUtil.toString(pc.getId()));
                            })).collect(Collectors.toList()).stream().map(EnumDictDTO::fromBizDict)
                    .peek(o -> {
                        o.setDisplay(displayMap.get(enumWorkAuthorizationMap.get(Long.parseLong(o.getId())).getName()));
                    }).collect(Collectors.toList());
            //format children node for webSide
            Map<String, List<EnumDictDTO>> pidListMap =
                    dtoList.stream().collect(Collectors.groupingBy(EnumDictDTO::getParentId));
            dtoList.forEach(item -> {
                List<EnumDictDTO> childrenNodes = pidListMap.get(item.getId());
                if (CollectionUtil.isNotEmpty(childrenNodes)) {
                    item.setChildren(childrenNodes);
                }
            });
            return pidListMap.get(Constants.PARENT_NODE_ID);
        } else {
            return new ArrayList<>();
        }
    }

    private List<EnumWorkAuthorization> findAllByParentCategoryIn() {
        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        Set<String> enumParentCategoryList = enumWorkAuthorizationList.stream().map(EnumWorkAuthorization::getParentCategory).filter(ObjectUtil::isNotEmpty).collect(Collectors.toSet());
        return enumWorkAuthorizationList.stream().filter(o -> enumParentCategoryList.contains(o.getName()) && ObjectUtil.isNotEmpty(o.getEnDisplay())).collect(Collectors.toList());

    }

    public List<String> transferWorkAuthorizationByIds(List<String> ids) {
        log.info("[APN: EnumWorkAuthorizationService @{}] request to transfer workauthorization by ids, ids: {}", SecurityUtils.getUserId(), ids);
        if (ObjectUtil.isEmpty(ids)) {
            return null;
        }

        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        List<EnumWorkAuthorization> list = enumWorkAuthorizationList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (EnumWorkAuthorization bd : list) {
            if (ObjectUtil.isNotEmpty(bd.getName())) {
                if (bd.getName().contains(StrUtil.DOT)) {
                    result.add(bd.getName());
                    result.add(StrUtil.subPre(bd.getName(), bd.getName().indexOf(StrUtil.DOT)));
                } else {
                    result.add(bd.getName());
                }
            }
        }
        return result.stream().distinct().collect(Collectors.toList());
    }

    public List<String> getWorkAuthorizationByIds(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return null;
        }

        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        return enumWorkAuthorizationList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).map(EnumWorkAuthorization::getName).collect(Collectors.toList());
    }

    public List<Long> transferWorkAuthorizationByNamesToId(Object nameObject) {
        log.info("[APN: EnumWorkAuthorizationService @{}] request to transfer workauthorization by itemTier, nameObject: {}", SecurityUtils.getUserId(), nameObject);
        if (ObjectUtil.isEmpty(nameObject)) {
            return null;
        }

        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        List<EnumWorkAuthorization> list = new ArrayList<>();
        if (nameObject instanceof String) {
            list = enumWorkAuthorizationList.stream().filter(o -> o.getName().equals(ObjectUtil.toString(nameObject))).collect(Collectors.toList());
        }
        if (nameObject instanceof List || nameObject instanceof String[]) {
            list = findAllByNameIn(Convert.toSet(String.class, nameObject));
            //filter unnecessary data
            String itemList = ObjectUtil.toString(JSONUtil.parseArray(list.stream().map(EnumWorkAuthorization::getName).collect(Collectors.toList())));
            list = list.stream().distinct().filter(s -> !itemList.contains(s.getName() + StrUtil.DOT)).collect(Collectors.toList());
        }
        return list.stream().map(EnumWorkAuthorization::getId).collect(Collectors.toList());
    }

    public List<Long> transferWorkAuthorizationByNamesToIdWithoutIgnoreParentClass(cn.hutool.json.JSONArray names) {
        if (ObjectUtil.isEmpty(names)) {
            return null;
        }

        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        Set<String> formatList = Convert.toSet(String.class, names);
        List<EnumWorkAuthorization> list = findAllByNameIn(formatList);
        return list.stream().map(EnumWorkAuthorization::getId).collect(Collectors.toList());
    }

    private List<EnumWorkAuthorization> findAllByNameIn(Set<String> nameList) {
        if (CollectionUtil.isEmpty(nameList)) {
            return new ArrayList<>();
        }

        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        Set<String> parentEnumNameSet = enumWorkAuthorizationList.stream().filter(o -> nameList.contains(o.getName())
                && ObjectUtil.isNotEmpty(o.getParentCategory())).map(EnumWorkAuthorization::getParentCategory).collect(Collectors.toSet());

        return enumWorkAuthorizationList.stream().filter(o -> nameList.contains(o.getName()) && !parentEnumNameSet.contains(o.getName())).collect(Collectors.toList());
    }

    public List<String> transferWorkAuthorizationByNames(Object itemTiers) {
        if (ObjectUtil.isEmpty(itemTiers)) {
            return null;
        }
        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        List<EnumWorkAuthorization> list = new ArrayList<>();
        if (itemTiers instanceof String) {
            list = enumWorkAuthorizationList.stream().filter(o -> o.getName().equals(ObjectUtil.toString(itemTiers))).collect(Collectors.toList());
        }
        if (itemTiers instanceof List) {
            List<String> itemList = Convert.toList(String.class, itemTiers);
            String itemJson = JSONUtil.toJsonStr(itemTiers);
            Set<String> formatList = itemList.stream().distinct().filter(s -> !itemJson.contains(s + StrUtil.DOT)).collect(Collectors.toSet());
            list = findAllByNameIn(formatList);
        }
        return list.stream().map(EnumWorkAuthorization::getName).collect(Collectors.toList());
    }

    public String getUINameByName(String name) {
        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        List<EnumWorkAuthorization> list = enumWorkAuthorizationList.stream().filter(o -> o.getName().equals(name)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(list)) {
            Long dictCode = list.get(0).getId();
            List<String> nameSet = new ArrayList<>();
            List<EnumWorkAuthorization> result = enumWorkAuthorizationList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumWorkAuthorization::getEnDisplayOrder)).collect(Collectors.toList());
            if (ObjectUtil.isNotEmpty(result)) {
                EnumWorkAuthorization aDict = result.stream().filter(s -> dictCode.equals(s.getId()) && StringUtils.isNotBlank(s.getEnDisplay())).findAny().get();
                nameSet.add(aDict.getEnDisplay());
                if (ObjectUtil.isNotNull(aDict.getParentCategory())) {
                    EnumWorkAuthorization bDict = result.stream().filter(s -> aDict.getParentCategory().equals(s.getName())).findAny().get();
                    nameSet.add(bDict.getEnDisplay());
                    if (ObjectUtil.isNotNull(bDict.getParentCategory())) {
                        EnumWorkAuthorization cDict = result.stream().filter(s -> bDict.getParentCategory().equals(s.getName())).findAny().get();
                        nameSet.add(cDict.getEnDisplay());
                    }
                }
                Collections.reverse(nameSet);
                return String.join(StrUtil.SPACE + StrUtil.DASHED + StrUtil.SPACE, nameSet);
            }
        }
        return null;
    }

    public Map<String, String> getUINameByNames(List<String> names) {
        Map<String, String> result = new HashMap<>(16);
        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        Map<String, EnumWorkAuthorization> enumWorkAuthorizationMap = enumWorkAuthorizationList.stream().collect(Collectors.toMap(EnumWorkAuthorization::getName, Function.identity(), (key1, key2) -> key2));
        List<EnumWorkAuthorization> list = new ArrayList<>();
        findAncestorByNameIn(list, enumWorkAuthorizationMap, names);
        Map<String, EnumWorkAuthorization> parentMap = list.stream().collect(Collectors.toMap(EnumWorkAuthorization::getName, a -> a));
        if (CollUtil.isNotEmpty(list)) {
            names.forEach(name -> {
                EnumWorkAuthorization enumWorkAuthorization = parentMap.get(name);
                if (enumWorkAuthorization == null) {
                    return;
                }
                List<String> nameSet = new ArrayList<>();
                nameSet.add(parentMap.get(name).getEnDisplay());
                getParentUiName(name, parentMap, nameSet);
                CollUtil.reverse(nameSet);
                result.put(name, String.join(StrUtil.SPACE + StrUtil.DASHED + StrUtil.SPACE, nameSet));
            });
        }
        return result;
    }

    private void getParentUiName(String name, Map<String, EnumWorkAuthorization> parentMap, List<String> nameSet) {
        String parentName = parentMap.get(name).getParentCategory();
        if (StrUtil.isNotBlank(parentName)) {
            nameSet.add(parentMap.get(parentName).getEnDisplay());
            getParentUiName(parentName, parentMap, nameSet);
        }
    }

    public JSONObject getUINameByName(List<EnumWorkAuthorization> list) {
        Long dictCode = null;
        Optional<EnumWorkAuthorization> workAuthorization = list.stream().findFirst();
        if(workAuthorization.isPresent()){
            dictCode = workAuthorization.get().getId();
        }
        JSONObject data = new JSONObject();
        List<String> nameSet = new ArrayList<>();
        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        List<EnumWorkAuthorization> result = enumWorkAuthorizationList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumWorkAuthorization::getEnDisplayOrder)).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(result)) {
            Long finalDictCode = dictCode;
            EnumWorkAuthorization aDict = result.stream().filter(s -> finalDictCode.equals(s.getId()) && StringUtils.isNotBlank(s.getEnDisplay())).findAny().get();
            nameSet.add(aDict.getEnDisplay());
            if (ObjectUtil.isNotNull(aDict.getParentCategory())) {
                EnumWorkAuthorization bDict = result.stream().filter(s -> aDict.getParentCategory().equals(s.getName())).findAny().get();
                nameSet.add(bDict.getEnDisplay());
                if (ObjectUtil.isNotNull(bDict.getParentCategory())) {
                    EnumWorkAuthorization cDict = result.stream().filter(s -> bDict.getParentCategory().equals(s.getName())).findAny().get();
                    nameSet.add(cDict.getEnDisplay());
                }
            }
            Collections.reverse(nameSet);
            data.put("data", String.join(StrUtil.SPACE + StrUtil.DASHED + StrUtil.SPACE, nameSet));
        }
        return data;
    }

    private void findAncestorByName(List<EnumWorkAuthorization> result, Map<String, EnumWorkAuthorization> enumWorkAuthorizationMap, String name) {

        EnumWorkAuthorization enumWorkAuthorization = enumWorkAuthorizationMap.get(name);
        if (ObjectUtil.isEmpty(enumWorkAuthorization)) {
            return;
        }

        if (ObjectUtil.isEmpty(enumWorkAuthorization.getParentCategory())) {
            result.add(enumWorkAuthorization);
        } else {
            if (ObjectUtil.isNotEmpty(enumWorkAuthorization.getEnDisplay())) {
                result.add(enumWorkAuthorization);
            } else {
                name = enumWorkAuthorization.getParentCategory();
                findAncestorByName(result, enumWorkAuthorizationMap, name);
            }
        }
    }

    private void findAncestorByNameIn(List<EnumWorkAuthorization> result, Map<String, EnumWorkAuthorization> enumWorkAuthorizationMap, List<String> nameList) {
        nameList.forEach(o -> findAncestorByName(result, enumWorkAuthorizationMap, o));
    }

    public List<EnumDictDTO> findAll() {
        List<EnumWorkAuthorization> enumWorkAuthorizationList = enumCommonService.findAllEnumWorkAuthorization();
        List<EnumWorkAuthorization> list = enumWorkAuthorizationList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay())).collect(Collectors.toList());
        Map<Long, EnumWorkAuthorization> enumWorkAuthorizationMap = enumWorkAuthorizationList.stream().collect(Collectors.toMap(EnumWorkAuthorization::getId, EnumWorkAuthorization -> EnumWorkAuthorization));
        Map<String, String> displayMap = getUINameByNames(list.stream().map(EnumWorkAuthorization::getName).collect(Collectors.toList()));
        return list.stream().map(EnumDictDTO::fromBizDict).peek(o -> o.setDisplay(displayMap.get(enumWorkAuthorizationMap.get(Long.parseLong(o.getId())).getName()))).collect(Collectors.toList());
    }

}
