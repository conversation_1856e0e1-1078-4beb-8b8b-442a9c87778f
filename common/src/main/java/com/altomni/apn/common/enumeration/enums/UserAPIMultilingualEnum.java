package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 user-service
 * <AUTHOR>
 */
public enum UserAPIMultilingualEnum {

    CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL("customconfig_getColumnConfigByUserId_userIdNull"),

    CUSTOMCONFIG_SEARCHDASHBOARDCOLUMNCONFIGBYUSERID_PARAMNULL("customconfig_searchDashboardColumnConfigByUserId_paramNull"),

    CUSTOMCONFIG_GETJOBCOLUMNDEFAULTCONFIG_ERROR("customconfig_getJobColumnDefaultConfig_error"),

    CUSTOMCONFIG_GETFORMCONFIGBYRECRUITMENTPROCESSID_RECRUITMENTPROCESSIDNULL("customconfig_getFormConfigByRecruitmentProcessId_recruitmentProcessIdNull"),

    CUSTOMCONFIG_GETJOBFORMSUBCATEGORY_RESPONSESTATUSNOTOK("customconfig_getJobFormSubCategory_responseStatusNotOk"),

    CUS<PERSON>MCONFIG_PIPELINE_FINDBYUSERID_USERIDNULL("customconfig_pipeline_findByUserId_userIdNull"),

    CUSTOMCONFIG_PIPELINE_DELETEBYID_NOTFOUND("customconfig_pipeline_deleteById_notFound"),

    CUSTOMCONFIG_PIPELINE_CREATECOLUMNCONFIG_TEMPLATENAMENULL("customconfig_pipeline_createColumnConfig_templateNameNull"),

    CUSTOMCONFIG_PIPELINE_CREATECOLUMNCONFIG_SAMETEMPLATENAME("customconfig_pipeline_createColumnConfig_sametemplateName"),

    CUSTOMCONFIG_PIPELINE_UPDATECOLUMNCONFIG_INVALIDTEMPLATE("customconfig_pipeline_updateColumnConfig_invalidTemplate"),

    CUSTOMCONFIG_PIPELINE_UPDATECOLUMNCONFIG_NOPERMISSION("customconfig_pipeline_updateColumnConfig_noPermission"),

    CUSTOMCONFIG_GETSYSTEMCONFIGBYCATEGORY_NOTFOUND("customconfig_getSystemConfigByCategory_notFound"),

    CUSTOMCONFIG_GETFORMCONFIGBYTENANTID_INVALIDTENANTID("customconfig_getFormConfigByTenantId_invalidTenantId"),

    CUSTOMCONFIG_SAVETALENTFORMCONFIG_INVALIDTENANT("customconfig_saveTalentFormConfig_invalidTenant"),

    PERMISSION_FINDDATAPERMISSIONBYUSERID_INVALIDUSER("permission_findDataPermissionByUserId_invalidUser"),

    PERMISSION_FINDDATAPERMISSIONBYUSERID_TENANTNULL("permission_findDataPermissionByUserId_tenantNull"),

    PERMISSION_VALIDATETENANTPRIVILEGE_INVALID("permission_validateTenantPrivilege_invalid"),

    PERMISSIONMODULE_CREATE_INVALIDID("permissionModule_create_invalidId"),

    PERMISSIONPRIVILEGE_CREATE_INVALIDID("permissionPrivilege_create_invalidId"),

    PERMISSIONPRIVILEGE_CREATE_IDNOTNULL("permissionPrivilege_create_idNotNull"),

    PERMISSIONROLE_CREATE_ROLEIDNOTNULL("permissionRole_create_roleIdNotNull"),

    PERMISSIONROLE_CREATE_ROLEIDNULL("permissionRole_create_roleIdNull"),

    PERMISSIONROLE_FINDROLESBYUSERID_INVALIDUSER("permissionRole_findRolesByUserId_invalidUser"),

    PERMISSIONTABLE_CREATE_TABLEIDMUSTNULL("permissionTable_create_tableIdMustNull"),

    PERMISSIONTABLE_CREATE_TABLEIDNULL("permissionTable_create_tableIdNull"),

    PERMISSIONTEAM_CREATE_CODENULL("permissionTeam_create_codeNull"),

    PERMISSIONTEAM_CHECKTEAMNAME_NAMENULL("permissionTeam_checkTeamName_nameNull"),

    PERMISSIONTEAM_CHECKTEAMNAME_NAMETOOLONG("permissionTeam_checkTeamName_nameTooLong"),

    PERMISSIONTEAM_CHECKTEAMNAME_NAMEEXIST("permissionTeam_checkTeamName_nameExist"),

    PERMISSIONTEAM_CHECKTEAMLEVEL_ERROR("permissionTeam_checkTeamLevel_error"),

    PERMISSIONTEAM_DELETE_INVALIDTEAM("permissionTeam_delete_invalidTeam"),

    PERMISSIONTEAM_DELETE_SUBTEAM("permissionTeam_delete_subTeam"),

    PERMISSIONTENANT_FINDMODULESBYCURRENTTENANT_INVALIDTENANT("permissionTenant_findModulesByCurrentTenant_invalidTenant"),

    PERMISSIONUSERTEAM_REMOVEUSERSFROMTEAM_NOTREMOVED("permissionUserTeam_removeUsersFromTeam_notRemoved"),

    PERMISSIONUSERTEAM_CHECKOUTEXISTUSERTEAM_USEREXIST("permissionUserTeam_checkoutExistUserTeam_userExist"),

    USER_GETUSERCREDITLIMIT_USERCREDITLIMITNULL("user_getUserCreditLimit_userCreditLimitNull"),

    USER_GETPARAMBYID_PARAMNOTEXIST("user_getParamById_paramNotExist"),

    USER_CREDITTRANSACTION_CREATE_TALENTNULL("user_creditTransaction_create_talentNull"),

    USER_CREDITTRANSACTION_UPDATEBALANCE_USERACCOUNTNULL("user_creditTransaction_updateBalance_userAccountNull"),

    USER_CREDITTRANSACTION_UPDATETALENTID_IDNULL("user_creditTransaction_updateTalentId_idNull"),

    USER_CREDITTRANSACTION_UPDATETALENTID_TALENTIDNULL("user_creditTransaction_updateTalentId_talentIdNull"),

    USER_CREDITTRANSACTION_UPDATETALENTID_NOTEXIST("user_creditTransaction_updateTalentId_notExist"),

    USER_CREDITTRANSACTION_UPDATETALENTID_NOTPERMISSION("user_creditTransaction_updateTalentId_notPermission"),

    USER_CREDITTRANSACTION_UNLOCKTALENT_INTERNALERROR("user_creditTransaction_unlockTalent_internalError"),

    USER_SOCIALLOGIN_BADCREDENTIAL("user_socialLogin_badCredential"),

    USER_ADDUSER_ACCOUNTLIMIT("user_addUser_accountLimit"),

    USER_LINKEDINLOGIN_FAILGETUSER("user_linkedinLogin_failGetUser"),

    USER_TEAM_CREATE_TEAMEXIST("user_team_create_teamExist"),

    USER_TEAMUSER_MULTIADDUSERS_TEAMNULL("user_teamUser_multiAddUsers_teamNull"),

    USER_TEAMUSER_CHECKPERMISSION_USERNULL("user_teamUser_checkPermission_userNull"),

    USER_TEAMUSER_CHECKPERMISSION_NOPERMISSION("user_teamUser_checkPermission_noPermission"),

    USER_VERIFYEFFECTIVETIME_TIMEERROR("user_verifyEffectiveTime_timeError"),

    USER_VERIFYEXPIRETIME_EXPIRETIMENULL("user_verifyExpireTime_expireTimeNull"),

    USER_VERIFYEXPIRETIME_EXPIRETIMEAFTEREFFECTIVETIME("user_verifyExpireTime_expireTimeAfterEffectiveTime"),

    USER_VERIFYEXPIRETIME_EXPIRETIMEERROR("user_verifyExpireTime_expireTimeError"),

    USER_REVOKEMYIMPERSONATION_NOTFIND("user_revokeMyImpersonation_notFind"),

    USER_LOGIN_INACTIVEUSER("user_login_inactiveUser"),

    USER_LOGIN_DENIEDPERMISSION("user_login_deniedPermission"),

    USER_VALIDATEACCOUNT_TENANTNULL("user_validateAccount_tenantNull"),

    USER_VALIDATEACCOUNT_EMAILEXIST("user_validateAccount_emailExist"),

    USER_VALIDATEACCOUNT_USERNAMEEXIST("user_validateAccount_userNameExist"),

    USER_REGISTERACCOUNT_TENANTNOTEXIST("user_registerAccount_tenantNotExist"),

    USER_ADDUSER_ACCOUNTMAXLIMIT("user_addUser_accountMaxLimit"),

    USER_CREATEUSER_TOTALUSERCREDITERROR("user_createUser_totalUserCreditError"),

    USER_CREATETENANTADMINUSER_EMAILALREADYREG("user_createTenantAdminUser_emailAlreadyReg"),

    USER_CREATETENANTADMINUSER_USERINFODEFICIENCY("user_createTenantAdminUser_userInfoDeficiency"),

    USER_CREATETENANTADMINUSER_ERROR("user_createTenantAdminUser_error"),

    USER_UPDATEUSER_USERIDNOTEXIST("user_updateUser_userIdNotExist"),

    USER_UPDATEUSER_NOPERMISSION("user_updateUser_noPermission"),

    USER_CHECKREQUIREDFIELDS_EMPTY("user_checkRequiredFields_empty"),

    USER_CHECKPRIMARYTEAM_PRIMARYTEAMIDNULL("user_checkPrimaryTeam_primaryTeamIdNull"),

    USER_CHECKPRIMARYTEAM_PRIMARYTEAMNOTEXIST("user_checkPrimaryTeam_primaryTeamNotExist"),

    USER_VALIDATECREDIT_CREDITNOTENOUGH("user_validateCredit_creditNotEnough"),

    USER_VALIDATECREDIT_MONTHLYCREDITNOTENOUGH("user_validateCredit_monthlyCreditNotEnough"),

    USER_VALIDATECREDIT_MONTHEFFECTCREDITNOTENOUGH("user_validateCredit_monthEffectCreditNotEnough"),

    USER_CREATELIMITUSE_TENANTINCORRECT("user_createLimitUse_tenantIncorrect"),

    USER_CHECKLOCKEDACCOUNT_LOCKACCOUNT("user_checkLockedAccount_lockAccount"),

    USER_CREATETENANTADMINUSER_USERIDNOTNULL("user_createTenantAdminUser_userIdNotNull"),

    USER_CREATETENANTADMINUSER_TENANTNULL("user_createTenantAdminUser_tenantNull"),

    USER_UPDATEUSERSTATUS_INVALIDTENANTUSER("user_updateUserStatus_invalidTenantUser"),

    USER_UPDATEUSERSTATUS_USERNOTEXIST("user_updateUserStatus_userNotExist"),

    USER_UPDATEUSERSTATUS_NOTSETSTATUS("user_updateUserStatus_notSetStatus"),

    USER_SEARCHUSERLIST_TENANTNOTEXIST("user_searchUserList_tenantNotExist"),

    USER_SOCIALLOGIN_INVALIDUSER("user_socialLogin_invalidUser"),

    USER_CREATECREDITTRANSACTION_IDNOTNULL("user_createCreditTransaction_idNotNull"),

    USER_CREATETEAM_TEAMIDNOTNULL("user_createTeam_teamIdNotNull"),

    USER_UPDATETENANT_TENANTIDNULL("user_updateTenant_tenantIdNull"),
    ;

    private final String key;

    UserAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}