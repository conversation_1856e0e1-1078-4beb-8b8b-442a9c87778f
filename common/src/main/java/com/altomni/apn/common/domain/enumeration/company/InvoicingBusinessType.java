package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * invoicing business type enum
 */
public enum InvoicingBusinessType implements ConvertedEnum<Integer> {

    FTE(0, "FTE"),
    RPO(1, "RPO"),
    STF_OUTSOURCING(2,"STF-outsourcing"),
    STF_PERSONNEL_AGENCY(3,"STF-Personnel Agency"),
    ;
    private final int dbValue;

    private final String description;

    InvoicingBusinessType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoicingBusinessType, Integer> resolver = new ReverseEnumResolver<>(InvoicingBusinessType.class, InvoicingBusinessType::toDbValue);

    public static InvoicingBusinessType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
