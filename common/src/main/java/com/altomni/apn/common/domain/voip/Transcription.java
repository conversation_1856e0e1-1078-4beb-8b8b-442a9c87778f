package com.altomni.apn.common.domain.voip;

;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSpeakerType;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSpeakerTypeConverter;
import lombok.*;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.Map;

@Data
@Document
public class Transcription implements Serializable {

    @Id
//    @Field("streamId")
    private String streamId;

    private Integer index;

    private String startTime;

    private String endTime;

    private String speaker;

    @Convert(converter = TranscriptionSpeakerTypeConverter.class)
    private TranscriptionSpeakerType speakerType;

    private String content;

    public Transcription() {}

    public Transcription(String streamId, String startTime, String endTime, String speaker, TranscriptionSpeakerType speakerType, String content) {
        this.streamId = streamId;
        this.startTime = startTime;
        this.endTime = endTime;
        this.speaker = speaker;
        this.speakerType = speakerType;
        this.content = content;
    }
}