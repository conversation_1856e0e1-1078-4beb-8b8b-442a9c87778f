package com.altomni.apn.common.domain.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A role (a security role) used by Spring Security.
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Entity
@Table(name = "admin_management_role")
public class AdminManagementRole implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @NotNull
    @Column(length = 50, name = "name")
    private String name;

    @NotNull
    @Column(name = "is_internal")
    private Boolean internal;

    @Column(name = "data_scope")
    private Integer dataScope;

    @Column(name = "status")
    private Boolean status;

    @Column(name = "description")
    private String description;

}
