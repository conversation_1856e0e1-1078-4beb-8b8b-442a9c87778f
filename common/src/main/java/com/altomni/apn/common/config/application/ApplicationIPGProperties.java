package com.altomni.apn.common.config.application;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RefreshScope
@Configuration
public class ApplicationIPGProperties {

    public static Set<Long> IPG_RULE_TENANT_IDS;

//    public static Boolean USE_GMAIL_ALIAS;

    @Value("${application.useGmailAlias}")
    private boolean useGmailAlias;

    @Value("${application.ipgApplicationRuleTenantIds}")
    private void setIpgRuleTenantIds(String ipgRuleTenantIds){
        ApplicationIPGProperties.IPG_RULE_TENANT_IDS = Stream.of(ipgRuleTenantIds.trim().split("\\s*,\\s*")).map(Long::valueOf).collect(Collectors.toSet());
    }

    public Set<Long> getIpgRuleTenantIds(){
        return IPG_RULE_TENANT_IDS;
    }

//    @Value("${application.useGmailAlias}")
//    private void setUseGmailAlias(boolean useGmailAlias) {
//        ApplicationIPGProperties.USE_GMAIL_ALIAS = useGmailAlias;
//    }


    public boolean isUseGmailAlias() {
        return useGmailAlias;
    }

    public void setUseGmailAlias(boolean useGmailAlias) {
        this.useGmailAlias = useGmailAlias;
    }
}
