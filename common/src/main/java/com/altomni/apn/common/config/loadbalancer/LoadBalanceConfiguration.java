package com.altomni.apn.common.config.loadbalancer;

import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.loadbalancer.core.ReactorLoadBalancer;
import org.springframework.cloud.loadbalancer.core.RoundRobinLoadBalancer;
import org.springframework.cloud.loadbalancer.core.ServiceInstanceListSupplier;
import org.springframework.cloud.loadbalancer.support.LoadBalancerClientFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.core.env.Environment;

import static com.altomni.apn.common.config.constants.Constants.APN_STAGING_HOST_ENV;

public class LoadBalanceConfiguration {

    @Bean
    public ReactorLoadBalancer<ServiceInstance> devReactorServiceInstanceLoadBalancer(Environment environment,
                                                                                      LoadBalancerClientFactory loadBalancerClientFactory) {
        String name = environment.getProperty(LoadBalancerClientFactory.PROPERTY_NAME);
        String apnStagingHost = System.getenv(APN_STAGING_HOST_ENV);
        if (apnStagingHost != null && !apnStagingHost.isBlank()) {
            return new DevLoadBalancer(
                    loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class), name);
        } else {
            return new RoundRobinLoadBalancer(
                    loadBalancerClientFactory.getLazyProvider(name, ServiceInstanceListSupplier.class), name);
        }
    }

}
