package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The IndustryType enumeration.
 */
public enum ContactCategoryType implements ConvertedEnum<Integer> {
    HIRING_MANAGER(1),
    HR_CONTACT(2),
    MSP(3),
    OTHER(4);
    private final int dbValue;

    ContactCategoryType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<ContactCategoryType, Integer> resolver = new ReverseEnumResolver<>(ContactCategoryType.class, ContactCategoryType::toDbValue);

    public static ContactCategoryType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
