package com.altomni.apn.common.dto.search;

import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class SearchGroup {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "search condition in json string")
    private List<SearchParam> search;

    private SearchFilterDTO filter;

    private String module;

    private String index;

    private String timeZone;

    private String language;

    private OwnershipRestriction ownershipRestriction;

    public static SearchGroup deepCopySearchGroup(SearchGroup searchGroup) {
        var newSearchDTO = new SearchGroup();
        ServiceUtils.myCopyProperties(searchGroup, newSearchDTO);
        return newSearchDTO;
    }
}
