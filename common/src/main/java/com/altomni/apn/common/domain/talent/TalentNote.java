package com.altomni.apn.common.domain.talent;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.NotePriority;
import com.altomni.apn.common.domain.enumeration.NotePriorityConverter;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteTypeConverter;
import com.altomni.apn.common.dto.talent.JsonToMapConverter;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.*;

/**
 * A TalentNote.
 */
@ApiModel(value = "User's note on talent")
@Entity
@Table(name = "talent_note")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
public class TalentNote extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "title")
    @Column(name = "title")
    private String title;

    @ApiModelProperty(value = "content", required = true)
    @NotNull
    @Column(name = "note", nullable = false)
    private String note;

    @ApiModelProperty("store raw json and convert it into class in DTO")
    @Column(name = "additional_info", columnDefinition = "json")
    @Convert(converter = JsonToMapConverter.class)
    private Map<String, Object> additionalInfo;

    @ApiModelProperty(value = "whether it is visible to other users. Default is true.")
    @Column(name = "visible")
    private Boolean visible = true;

    @ApiModelProperty(value = "the note priority. Default is Normal", allowableValues = "Normal, High")
    @Convert(converter = NotePriorityConverter.class)
    @Column(name = "priority")
    private NotePriority priority = NotePriority.NORMAL;

    @ApiModelProperty(value = "talent id for the note.")
    @Column(name = "talent_id", updatable = false)
    private Long talentId;

    @ApiModelProperty(value = "user id for the note. It is saved from the current user. Read Only.")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @ApiModelProperty(value = "talent note type")
    @Column(name = "note_type", nullable = true)
    @Convert(converter= TalentNoteTypeConverter.class)
    private TalentNoteType noteType;

    @ApiModelProperty(value = "talent note status(talent motivation)")
    @Column(name = "note_status", nullable = true)
    private Integer noteStatus;

    @ApiModelProperty(value = "if this note is made by external agency, store its id here; if not, this field should be null")
    @Column(name = "agency_id", nullable = true)
    private Long agencyId;

    @ApiModelProperty(value = "if this note is read by user, currently only user modify the note can be recognized as read")
    @Column(name = "read_status")
    private Boolean readStatus;

    @Column(name = "parsed_result")
    private String parsedResult;

    @Column(name = "enrich_result")
    private String enrichResult;


    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("userId", "talentId", "id", "createdDate"));

    public Map<String, Object> getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(Map<String, Object> additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public TalentNote title(String title) {
        this.title = title;
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getNote() {
        return note;
    }

    public TalentNote note(String note) {
        this.note = note;
        return this;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Boolean isVisible() {
        return visible;
    }

    public TalentNote visible(Boolean visible) {
        this.visible = visible;
        return this;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public NotePriority getPriority() {
        return priority;
    }

    public TalentNote priority(NotePriority priority) {
        this.priority = priority;
        return this;
    }

    public TalentNote priority(Integer value) {
        this.priority = NotePriority.fromDbValue(value);
        return this;
    }

    public void setPriority(NotePriority priority) {
        this.priority = priority;
    }

    public Boolean getVisible() {
        return visible;
    }

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TalentNote talentNote = (TalentNote) o;
        if (talentNote.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), talentNote.getId());
    }

    public TalentNoteType getNoteType() {
        return noteType;
    }

    public void setNoteType(TalentNoteType noteType) {
        this.noteType = noteType;
    }

    public Integer getNoteStatus() {
        return noteStatus;
    }

    public void setNoteStatus(Integer noteStatus) {
        this.noteStatus = noteStatus;
    }

    public Long getAgencyId() { return agencyId; }

    public void setAgencyId(Long agencyId) { this.agencyId = agencyId; }

    public Boolean getReadStatus() { return readStatus; }

    public void setReadStatus(Boolean readStatus) { this.readStatus = readStatus; }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }


    public String getParsedResult() {
        return parsedResult;
    }

    public void setParsedResult(String parsedResult) {
        this.parsedResult = parsedResult;
    }

    public String getEnrichResult() {
        return enrichResult;
    }

    public void setEnrichResult(String enrichResult) {
        this.enrichResult = enrichResult;
    }
}
