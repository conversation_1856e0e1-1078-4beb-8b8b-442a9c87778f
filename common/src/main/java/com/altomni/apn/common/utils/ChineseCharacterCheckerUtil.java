package com.altomni.apn.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 中文正则匹配
 * <AUTHOR>
 */
public class ChineseCharacterCheckerUtil {

    // 正则表达式匹配中文字符
    private static final String CHINESE_CHARACTERS_REGEX = ".*[\u4e00-\u9fff]+.*";

    public static boolean containsChineseCharacters(String input) {
        // 创建正则表达式模式对象
        Pattern pattern = Pattern.compile(CHINESE_CHARACTERS_REGEX);
        // 创建匹配器对象
        Matcher matcher = pattern.matcher(input);
        // 查找匹配
        return matcher.find();
    }
}