package com.altomni.apn.common.enumeration.enums;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/***
 * 禁猎消息提醒类型
 */
public enum NoPoachingRemindType implements ConvertedEnum<Integer> {

    TALENT(1, "talent"),
    CONTACT(2, "contact"),
    TALENT_AFFILIATE(3, "talentAffiliate")
    ;

    private final Integer dbValue;

    private final String display;

    NoPoachingRemindType(Integer dbValue, String display) {
        this.dbValue = dbValue;
        this.display = display;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDisplay() {
        return display;
    }

    // static resolving:
    public static final ReverseEnumResolver<MqTranRecordStatusEnums, Integer> resolver =
            new ReverseEnumResolver<>(MqTranRecordStatusEnums.class, MqTranRecordStatusEnums::toDbValue);

    public static MqTranRecordStatusEnums fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
