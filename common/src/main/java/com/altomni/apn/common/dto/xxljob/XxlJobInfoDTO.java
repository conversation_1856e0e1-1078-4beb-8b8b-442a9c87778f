package com.altomni.apn.common.dto.xxljob;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class XxlJobInfoDTO {
    // 主键ID
    private int id;
    // 执行器主键ID
    private int jobGroup;
    private String jobDesc;
    // 负责人
    private String author = "admin";
    // 调度类型
    private String scheduleType = "CRON";
    // 调度配置，值含义取决于调度类型
    private String scheduleConf;
    // 调度过期策略
    private String misfireStrategy = "DO_NOTHING";
    // 执行器路由策略
    private String executorRouteStrategy = "FIRST";
    // 执行器，任务Handler名称
    private String executorHandler;
    // 执行器，任务参数
    private String executorParam;
    // 阻塞处理策略
    private String executorBlockStrategy = "SERIAL_EXECUTION";
    // 任务执行超时时间，单位秒
    private int executorTimeout = 0;
    // 失败重试次数
    private int executorFailRetryCount = 0;
    // BEAN
    private String glueType = "BEAN";
    // 调度状态：0-停止，1-运行
    private int triggerStatus = 1;

    private String onlyId;

}
