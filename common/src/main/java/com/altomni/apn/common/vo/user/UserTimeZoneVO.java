package com.altomni.apn.common.vo.user;

import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.utils.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserTimeZoneVO implements Serializable {

    private Long userId;

    //自定义时区
    private String customTimezone;

    public String getUserTimezone() {
        if (StrUtil.isBlank(customTimezone)) {
            return DateUtil.US_LA_TIMEZONE;
        }
        return customTimezone;
    }

}
