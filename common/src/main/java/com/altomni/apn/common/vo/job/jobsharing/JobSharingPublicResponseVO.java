package com.altomni.apn.common.vo.job.jobsharing;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@NoArgsConstructor
@Data
public class JobSharingPublicResponseVO {

    private String uuid;
    private Instant expireTime;

    public JobSharingPublicResponseVO(String uuid, Instant expireTime){
        this.uuid = uuid;
        this.expireTime = expireTime;
    }

}
