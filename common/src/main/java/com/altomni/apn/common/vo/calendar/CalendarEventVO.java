package com.altomni.apn.common.vo.calendar;

import com.altomni.apn.common.domain.enumeration.calendar.*;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.Instant;
import java.util.*;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CalendarEventVO {

    @Id
    private Long id;

    private Long tenantId;

    //0.日历,1.talentNote,2.job,3.company,4.offer,5.offerAccept,6.onboard
    private Integer typeId;

    private Long referenceId;

    private Long goToId;

    private String title;

    private String description;

    private Instant startTime;

    private Instant endTime;

    private Integer reminderMinutes;

    @Convert(converter = CalendarTypeConverter.class)
    private CalendarTypeEnum calendarType;

    @Convert(converter = CalendarPriorityConverter.class)
    private CalendarPriorityEnum priority;

    @Convert(converter = CalendarStatusConverter.class)
    private CalendarStatusEnum status;

    private Integer reminderFlag;

    private String companyId;

    private String companyName;

    private String jobId;

    private String jobName;

    private String companyContactName;

    private String companyStatus;

    private String companyContactId;

    private String candidateName;

    private String candidateId;

    private Instant completedTime;

    @Transient
    private Boolean isPrivateJob;

    @Transient
    private List<CalendarEventAttendeeVO> attendees;

    @Transient
    private List<CalendarEventLogVO> logs;

    @Transient
    private List<CalendarEventRelationVO> relations;

    public List<Long> getTalentId() {
        if (candidateId == null || candidateId.trim().isEmpty()) {
            return Collections.emptyList();
        }
        String[] split = candidateId.split(",");
        return Arrays.stream(split)
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::parseLong)
                .toList();
    }

    public void encrypt(Map<Long, ConfidentialInfoDto> confidentialInfoMap, Set<Long> viewAbleConfidentialTalentIds) {
        if (candidateId == null || candidateName == null || candidateId.trim().isEmpty() || candidateName.trim().isEmpty()) {
            return;
        }
        String[] ids = candidateId.split(",");
        String[] names = candidateName.split(",");
        if (ids.length != names.length) {
            return;
        }
        for (int i = 0; i < ids.length; i++) {
            String idStr = ids[i].trim();
            if (idStr.isEmpty()) {
                continue;
            }
            Long talentId = Long.parseLong(idStr);
            if (!confidentialInfoMap.containsKey(talentId)) {
               continue;
            }
            if (!viewAbleConfidentialTalentIds.contains(talentId)) {
                names[i] = "***";
            }
        }
        this.candidateName = String.join(",", names);
    }
}
