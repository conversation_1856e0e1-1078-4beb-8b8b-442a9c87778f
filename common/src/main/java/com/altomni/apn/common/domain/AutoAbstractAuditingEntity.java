package com.altomni.apn.common.domain;

import com.altomni.apn.common.config.AutoAuditingEntityListener;
import com.altomni.apn.common.domain.user.SimpleUser;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;

/**
 * Base abstract class for entities which will hold definitions for created, last modified, created by,
 * last modified by attributes.
 */
@MappedSuperclass
@EntityListeners(AutoAuditingEntityListener.class)
public abstract class AutoAbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @CreatedBy
    @Column(name = "created_by", nullable = false, length = 50, updatable = false)
    private String createdBy;

    @CreatedDate
    @Column(name = "created_date", updatable = false)
    private Instant createdDate = Instant.now();

    @LastModifiedBy
    @Column(name = "last_modified_by", length = 50)
    private String lastModifiedBy;

    @LastModifiedDate
    @Column(name = "last_modified_date")
    private Instant lastModifiedDate = Instant.now();

    @Transient
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SimpleUser createdUser;

    @Transient
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SimpleUser lastModifiedUser;

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public String getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(String lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public Instant getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Instant lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public SimpleUser getCreatedUser() { return createdUser; }

    public void setCreatedUser(SimpleUser createdUser) { this.createdUser = createdUser; }

    public SimpleUser getLastModifiedUser() {
        return lastModifiedUser;
    }

    public void setLastModifiedUser(SimpleUser lastModifiedUser) {
        this.lastModifiedUser = lastModifiedUser;
    }

}
