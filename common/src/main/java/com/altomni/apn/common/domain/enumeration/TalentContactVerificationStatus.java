package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TalentContactVerificationStatus enumeration.
 */
public enum TalentContactVerificationStatus implements ConvertedEnum<Integer> {

    INVALID(0), //crm
    VERIFIED(1), //reachable contact info, apn & cr
    NOT_REQUIRED(2), //crm
    PENDING(4), //crm
    PATTERN_VERIFY(5), //crm
    CANNOT_VERIFY(6), //crm
    WRONG_CONTACT(9); //wrong contact, apn & crm

    private final int dbValue;

    TalentContactVerificationStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TalentContactVerificationStatus, Integer> resolver =
        new ReverseEnumResolver<>(TalentContactVerificationStatus.class, TalentContactVerificationStatus::toDbValue);

    public static TalentContactVerificationStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
