package com.altomni.apn.common.dto.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobPermissionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Boolean havePermission;

    private Boolean privateJob;
}
