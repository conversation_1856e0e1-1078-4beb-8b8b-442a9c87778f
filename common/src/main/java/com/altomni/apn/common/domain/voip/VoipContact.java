package com.altomni.apn.common.domain.voip;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.voip.PhoneCallStatus;
import com.altomni.apn.common.domain.enumeration.voip.PhoneCallStatusConverter;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModel;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModelConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "voip_contact")
public class VoipContact  extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "phone_call_id")
    private String phoneCallId;

    @Column(name = "instance_id")
    private String instanceId;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "talent_name")
    private String talentName;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "transcription_model")
    @Convert(converter = TranscriptionModelConverter.class)
    private TranscriptionModel transcriptionModel;

    @Column(name = "phone_call_status")
    @Convert(converter = PhoneCallStatusConverter.class)
    private PhoneCallStatus phoneCallStatus;

    @Column(name = "call_type_id")
    private Long callTypeId;

    @Column(name = "call_result_id")
    private Long callResultId;

    //phone call related job id
    @Column(name = "job_id")
    private Long jobId;

    @ApiModelProperty(value = "has phone recording been deleted?")
    @Column(name = "is_record_delete")
    private Boolean isRecordDelete = false;

    @Column(name = "summary")
    private String summary;

}
