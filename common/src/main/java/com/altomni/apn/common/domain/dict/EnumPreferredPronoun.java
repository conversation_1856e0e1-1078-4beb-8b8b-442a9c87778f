package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "enum_preferred_pronoun")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumPreferredPronoun implements EnumFrontDisplay {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Size(max = 128)
    @Column(name = "name", length = 128)
    private String name;

    @Size(max = 128)
    @Column(name = "cn_display", length = 128)
    private String cnDisplay;

    @Size(max = 128)
    @Column(name = "en_display", length = 128)
    private String enDisplay;

    @Override
    public Integer getEnumId() {
        return id.intValue();
    }

    @Override
    public String getEnumName() {
        return name;
    }

    @Override
    public String getEnumCnLabel() {
        return cnDisplay;
    }

    @Override
    public String getEnumEnLabel() {
        return enDisplay;
    }

    @Override
    public int getSort() {
        return id.intValue();
    }
}