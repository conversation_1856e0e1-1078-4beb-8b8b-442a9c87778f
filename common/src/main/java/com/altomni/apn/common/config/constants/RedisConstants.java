package com.altomni.apn.common.config.constants;

/**
 * Application constants.
 * <AUTHOR>
 */
public final class RedisConstants {

    public static final Integer EXPIRE_IN_30_MINUTES = 60 * 30;
    public static final Integer EXPIRE_IN_24_HOURS = 60 * 60 * 24;
    public static final int EXPIRE_IN_1_MINUTE = 60;
    public static final int EXPIRE_IN_1_HOUR = 3600;
    public static final int EXPIRE_IN_1_DAY = 3600 * 24;

    // request body key
    public static final String BODY_KEY_RESULT_UUID = "result_uuid";

    public static final String BODY_KEY_BLACKLISTED_ESIDS = "blacklisted_esIds";

    // redis storage data key
    public static final String DATA_KEY_RATER = "rater_v3:";

    public static final String DATA_KEY_DATA = ":data";

    public static final String DATA_KEY_STATUS = ":status";

    public static final String DATA_KEY_DOCUMENT_LANGUAGES = ":document_languages";

    public static final String DATA_KEY_METADATA = ":metadata";

    public static final String DATA_KEY_TOTAL = ":total";

    public static final String DATA_KEY_REPORT = "report:";

    public static final String DATA_KEY_IMAGES_INFO = ":image_info";

    /**
     * rater:tenant_{tenantId}:jobs_for_job_{jobId}_{lastModifiedDate}
     */
    public static final String DATA_KEY_RATER_SIMILAR_JOB = "rater:tenant_%s:jobs_for_job_%s_%s";
    public static final String DATA_KEY_RATER_TALENTS_FOR_JOB = "rater:tenant_%s:talents_for_job_%s_%s";

    /**
     * 修改job后自动触发ai推荐
     */
    public static final String DATA_KEY_RATER_TALENTS_FOR_JOB_BY_AUTO_TRIGGER = "rater:tenant_%s:talents_for_job_%s_*";

    public static final String DATA_KEY_DATA_RECOMMENDATIONS = ":recommendations";

    public static final String DATA_KEY_DATA_COMMON = ":recommendations_common";

    public static final String DATA_KEY_DATA_BIZ = ":recommendations_biz";


    public static final String DATA_KEY_TOTAL_COMMON = ":total_common";

    public static final String DATA_KEY_TOTAL_BIZ = ":total_biz";

    // parser
    public static final String DATA_KEY_PARSER = "parser:";

    public static final String DATA_KEY_RESUME = "resume:";

    public static final String DATA_KEY_LAST_UPDATE_TIME = ":last_update_time";

    public static final String DATA_KEY_JD = "jd:";

    // esfiller sync jobs and talents

    public static final String ESFILLER = "esfiller";

    public static final String SYNC_JOBS_TO_ES = ":sync_jobs_to_es";

    public static final String CONVERT_JOBS = ":convert_jobs";

    public static final String SYNC_TALENTS_TO_ES = ":sync_talents_to_es";

    public static final String CONVERT_TALENTS = ":convert_talents";

    public static final String SYNCED = ":synced:";

    public static final String CONVERTED = ":converted:";

    public static final String ERROR = ":error:";

    public static final String CODE = ":code";

    public static final String BODY = ":body";

    /**
     * Format: statistic:online-user:TenantId:UserId
     */
    public static final String STATISTIC_USER_ONLINE = "statistic:online-user:%s:%s";

    /**
     * ES synchronization
     */
    public static final String DATA_KEY_SYNCES_TALENT = "SyncES:APN:TalentIds";
    public static final String DATA_KEY_SYNCES_JOB = "SyncES:APN:JobIds";
    public static final String DATA_KEY_SYNCES_COMPANY = "SyncES:APN:CompanyIds";

    public static final String DATA_KEY_SYNCES_FAILED_TALENT = "SyncES:APN:FailedTalentIds";
    public static final String DATA_KEY_SYNCES_FAILED_JOB = "SyncES:APN:FailedJobIds";
    public static final String DATA_KEY_SYNCES_FAILED_AGENCY_JOB = "SyncES:APN:FailedAgencyJobIds";
    public static final String DATA_KEY_SYNCES_FAILED_COMPANY = "SyncES:APN:FailedCompanyIds";
    public static final String DATA_KEY_SYNCES_FAILED_COMPANY_CLIENT_NOTE = "SyncES:APN:FailedCompanyClientNoteIds";
    public static final String DATA_KEY_SYNCES_FAILED_COMPANY_PROGRESS_NOTE = "SyncES:APN:FailedCompanyProgressNoteIds";
    public static final String DATA_KEY_SYNCES_FAILED_AGENCY = "SyncES:APN:FailedAgencyIds";
    public static final String DATA_KEY_SYNCES_RETRIED_TALENT = "SyncES:APN:RetriedTalentIds";
    public static final String DATA_KEY_SYNCES_RETRIED_JOB = "SyncES:APN:RetriedJobIds";
    public static final String DATA_KEY_SYNCES_RETRIED_COMPANY = "SyncES:APN:RetriedCompanyIds";
    public static final String DATA_KEY_SYNCES_RETRIED_COMPANY_CLIENT_NOTE = "SyncES:APN:RetriedCompanyClientNoteIds";
    public static final String DATA_KEY_SYNCES_RETRIED_COMPANY_PROGRESS_NOTE = "SyncES:APN:RetriedCompanyProgressNoteIds";


    /**
     * Lock users' account
     * Login:ActiveUser:UserIdFrom:UserIdTo:Token
     */
    public static final String DATA_KEY_ACTIVE_USER = "Login:ActiveUser:%d:%d:%s";

    public static final String DATA_KEY_LOCKED_USER_ACCOUNT = "Login:APN:%s:LockedUser:%s";
    public static final String DATA_KEY_COUNT_LOGIN_FAILED = "Login:APN:%s:FailedUser:%s";

    public static final String DATA_KEY_TIMESHEET_LOCKED_USER_ACCOUNT = "Login:Timesheet:LockedUser:%s";
    public static final String DATA_KEY_TIMESHEET_COUNT_LOGIN_FAILED = "Login:Timesheet:FailedUser:%s";

    /**
     * Impersonation
     */
    public static final String DATA_KEY_IMPERSONATION_PRIVILEGES = "Login:Impersonation:Privileges";

    public static final String DATA_KEY_PRIVILEGES_PUBLIC = "Security:Privileges:Public";
    /**
     * %s = userId
     */
    public static final String DATA_KEY_PRIVILEGES_USER = "Security:Privileges:User:%s";

    public static final String DATA_KEY_PRIVILEGES_SET_USER = "Security:privilege-permission-set::user:%s";

    public static final String DATA_KEY_PRIVILEGES_TREE_USER = "Security:privilege-permission-tree::user:%s";

    public static final String DATA_KEY_COMPANY_SEARCH_HISTORY = "SearchHistory:Company:User:%s";
    public static final String DATA_KEY_FOLDER_COMPANY_SEARCH_HISTORY = "SearchHistory:Folder:Company:User:%s";

    public static final String DATA_KEY_JOB_SEARCH_HISTORY = "SearchHistory:Job:User:%s";

    public static final String DATA_KEY_TALENT_SEARCH_HISTORY = "SearchHistory:Talent:User:%s";

    /**
     * %d = TenantId
     */
    public static final String DATA_KEY_TENANT_CONFIG_UPDATE_LIMIT = "TenantConfigUpdateLimit:Tenant:%d";

    /**
     * invoice:preference:talent:userid
     */
    public static final String INVOICE_PREFERENCE = "invoice:preference:%s:%s";

    /**
     * Agency Redis Key
     */
    public static final String AGENCY_NEW_APPLICATION_KEY_PATTERN = "agency:newApplication:userId:%s:agencyId:%s:jobId:%s";

    public static final String AGENCY_NEW_APPLICATION_KEY_PATTERN_PREFIX_USER_ID = "agency:newApplication:userId:%s*";

    public static final String AGENCY_NEW_APPLICATION_KEY_PATTERN_PREFIX_USER_ID_AGENCY_ID = "agency:newApplication:userId:%s:agencyId:%s*";

    /**
     * Linkedin Project Talent Redis Key
     */
    public static final String LINKEDIN_PROJECT_NEW_TALENT_KEY_PATTERN = "linkedinProject:newTalent:projectId:%s";

    public static final String LINKEDIN_PROJECT_NEW_PROJECT_KEY_PATTERN = "linkedinProject:newProject:projectId:%s";

    // Monitor:User-Companies:{tenantId}:{email(userId)}
    public static final String DATA_KEY_MONITOR_COMPANY = "Monitor:User-Companies:%d:%s";

    // Monitor:User-Contacts:{tenantId}:{email(userId)}
    public static final String DATA_KEY_MONITOR_CONTACT = "Monitor:User-Contacts:%d:%s";

    public static String esfillerJobSyncedKey(Integer partitionId) {
        return ESFILLER + SYNC_JOBS_TO_ES + SYNCED + partitionId;
    }

    public static String esfillerJobConvertedKey(Integer partitionId) {
        return ESFILLER + CONVERT_JOBS + CONVERTED + partitionId;
    }

    public static String esfillerTalentSyncedKey(Integer partitionId) {
        return ESFILLER + SYNC_TALENTS_TO_ES + SYNCED + partitionId;
    }

    public static String esfillerTalentConvertedKey(Integer partitionId) {
        return ESFILLER + CONVERT_TALENTS + CONVERTED + partitionId;
    }

    public static final String DATA_KEY_RECOMMENDATIONS = ":recommendations";

    public static final String DATA_KEY_RECOMMENDATIONS_BIZ = ":recommendations_biz";

    public static final String DATA_KEY_RECOMMENDATIONS_COMMON = ":recommendations_common";
    /**
     * 禁猎客户上传服务合同
     */
    public static final String CONTRACT_DATA_PRE = "contract_parser:";
    public static final String CONTRACT_DATA_KEY_STATUS = ":status";
    public static final String CONTRACT_DATA_KEY_METADATA = ":metadata";

}
