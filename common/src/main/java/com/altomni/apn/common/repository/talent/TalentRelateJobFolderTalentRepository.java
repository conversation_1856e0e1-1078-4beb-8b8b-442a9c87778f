package com.altomni.apn.common.repository.talent;

import com.altomni.apn.common.domain.talent.TalentAssociationJobFolderTalent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TalentRelateJobFolderTalentRepository extends JpaRepository<TalentAssociationJobFolderTalent, Long> {
    List<TalentAssociationJobFolderTalent> findByTalentRelateJobFolderFolderIdIs(String folderId);
    List<TalentAssociationJobFolderTalent> findByTalentRelateJobFolderFolderIdIsAndTalentIdIn(String folderId, List<Long> talentIds);

    List<TalentAssociationJobFolderTalent> findByTalentIdIs(Long talentId);

    @Query(value = "SELECT COUNT(DISTINCT talent_id) FROM talent_association_job_folder_talent WHERE talent_association_job_folder_folder_id = ?1", nativeQuery = true)
    int countTalentByFolderId(String folderId);
}
