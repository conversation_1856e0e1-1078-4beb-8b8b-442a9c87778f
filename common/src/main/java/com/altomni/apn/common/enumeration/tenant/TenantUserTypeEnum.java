package com.altomni.apn.common.enumeration.tenant;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum TenantUserTypeEnum implements ConvertedEnum<Integer> {

    EMPLOYER(0),
    HEADHUNTER(1);

    private final int dbValue;

    TenantUserTypeEnum(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TenantUserTypeEnum, Integer> resolver =
            new ReverseEnumResolver<>(TenantUserTypeEnum.class, TenantUserTypeEnum::toDbValue);

    public static TenantUserTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
