package com.altomni.apn.common.dto.application.recruitmentprocess;

import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    private ActiveStatus status;
}
