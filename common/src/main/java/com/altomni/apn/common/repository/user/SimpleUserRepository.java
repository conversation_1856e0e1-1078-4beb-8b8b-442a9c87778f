package com.altomni.apn.common.repository.user;

import com.altomni.apn.common.domain.user.SimpleUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the SimpleUser entity.
 */
@Repository
public interface SimpleUserRepository extends JpaRepository<SimpleUser, Long> {

    List<SimpleUser> findAllByUidIn(List<String> uids);

}
