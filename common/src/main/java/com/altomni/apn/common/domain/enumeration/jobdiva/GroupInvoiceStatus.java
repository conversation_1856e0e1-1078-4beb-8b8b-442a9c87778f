package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * group invoice polymerization type enum
 */
public enum GroupInvoiceStatus implements ConvertedEnum<Integer> {

    INVOICED(1, "Invoiced"),
    UNPAID(5, "Unpaid"),
    OVERDUE(2, "Overdue"),
    PAID(3, "Paid"),
    VOID(6, "Void"),
    PARTIALLY_PAID(4, "Partially Paid");
    private final int dbValue;

    private final String description;

    GroupInvoiceStatus(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<GroupInvoiceStatus, Integer> resolver = new ReverseEnumResolver<>(GroupInvoiceStatus.class, GroupInvoiceStatus::toDbValue);

    public static GroupInvoiceStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
