package com.altomni.apn.common.service.cache;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.altomni.apn.common.domain.dict.*;
import com.altomni.apn.common.domain.dict.mapping.EnumIndustryMapping;
import com.altomni.apn.common.domain.dict.mapping.EnumJobFunctionMapping;
import com.altomni.apn.common.domain.enumeration.dict.EnumStatus;
import com.altomni.apn.common.domain.talent.TelephoneChatScript;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.domain.timezone.EnumTimezone;
import com.altomni.apn.common.repository.company.CompanyServiceTypeConnectTenantRepository;
import com.altomni.apn.common.repository.company.SalesLeadSourceConnectTenantRepository;
import com.altomni.apn.common.repository.enums.*;
import com.altomni.apn.common.repository.timezone.EnumTimezoneRepository;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@EnableCaching
@CacheConfig(cacheNames = {"enum_dict"}, cacheManager = "concurrentMapCacheManager")
public class EnumCommonService {

    private final Logger log = LoggerFactory.getLogger(EnumCommonService.class);

    @Resource
    private TelephoneChatScriptRepository telephoneChatScriptRepository;

    @Resource
    private EnumCurrencyRepository enumCurrencyRepository;

    @Resource
    private EnumDegreeRepository enumDegreeRepository;

    @Resource
    private EnumIndustryRepository enumIndustryRepository;

    @Resource
    private EnumIndustryMappingRepository enumIndustryMappingRepository;

    @Resource
    private EnumJobFunctionRepository enumJobFunctionRepository;

    @Resource
    private EnumJobFunctionMappingRepository enumJobFunctionMappingRepository;

    @Resource
    private EnumLanguageRepository enumLanguageRepository;

    @Resource
    private EnumLevelOfExperienceRepository enumLevelOfExperienceRepository;

    @Resource
    private EnumUserResponsibilityRepository enumUserResponsibilityRepository;

    @Resource
    private EnumWorkAuthorizationRepository enumWorkAuthorizationRepository;

    @Resource
    private EnumAreaCodeRepository enumAreaCodeRepository;

    @Resource
    private EnumCompanyTagRepository enumCompanyTagRepository;

    @Resource
    private EnumCompanyServiceTypeRepository enumCompanyServiceTypeRepository;

    @Resource
    private CompanyServiceTypeConnectTenantRepository companyServiceTypeConnectTenantRepository;

    @Resource
    private EnumSalesLeadSourceRepository enumSalesLeadSourceRepository;

    @Resource
    private SalesLeadSourceConnectTenantRepository salesLeadSourceConnectTenantRepository;

    @Resource
    private EnumMotivationRepository enumMotivationRepository;

    @Resource
    private EnumInvoiceReceivingAccountRepository enumInvoiceReceivingAccountRepository;

    @Resource
    private EnumJobPriorityRepository enumJobPriorityRepository;

    @Resource
    private EnumCompanyContactTagRepository enumCompanyContactTagRepository;

    @Resource
    private EnumTimezoneRepository enumTimezoneRepository;

    @Resource
    private EnumGenderIdentityRepository enumGenderIdentifyRepository;

    @Resource
    private EnumVeteranRepository enumVeteranRepository;

    @Resource
    private EnumEthnicityRepository enumEthnicityRepository;

    @Resource
    private EnumPreferredPronounRepository enumPreferredPronounRepository;

    @Resource
    private EnumVoipCallTypeRepository enumVoipCallTypeRepository;

    @Resource
    private EnumVoipCallResultRepository enumVoipCallResultRepository;

    private final Long UNIVERSAL_TENANT_ID = -4L;

    @Cacheable(key = "'timezone'", unless = "#result == null")
    public List<EnumTimezone> findAllTimezone() {
        log.info("[APN: EnumDictService @{}] request to get timezone enum data", SecurityUtils.getUserId());

        List<EnumTimezone> result = enumTimezoneRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }


    @Cacheable(key = "'gender'", unless = "#result == null")
    public List<EnumGenderIdentity> findAllEnumGender() {
        log.info("[APN: EnumDictService @{}] request to get gender enum data", SecurityUtils.getUserId());

        List<EnumGenderIdentity> result = enumGenderIdentifyRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'telephoneChatScript'", unless = "#result == null")
    public List<TelephoneChatScript> findAllTelephoneChatScript() {
        log.info("[APN: EnumDictService @{}] request to get motivation enum data", SecurityUtils.getUserId());

        List<TelephoneChatScript> result = telephoneChatScriptRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'motivation'", unless = "#result == null")
    public List<EnumMotivation> findAllEnumMotivation() {
        log.info("[APN: EnumDictService @{}] request to get motivation enum data", SecurityUtils.getUserId());

        List<EnumMotivation> result = enumMotivationRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

//    @Cacheable(key = "'currency'", unless = "#result == null")
    public List<EnumCurrency> findAllEnumCurrency() {
        log.info("[APN: EnumCurrencyService @{}] request to get currency enum data", SecurityUtils.getUserId());

        List<EnumCurrency> result = enumCurrencyRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'degree'", unless = "#result == null")
    public List<EnumDegree> findAllEnumDegree() {
        log.info("[APN: EnumCurrencyService @{}] request to get degrees enum data", SecurityUtils.getUserId());

        List<EnumDegree> result = enumDegreeRepository.findAllByOrderByScoreDesc();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'industry'", unless = "#result == null")
    public List<EnumIndustry> findAllEnumIndustry() {
        log.info("[APN: EnumIndustryService @{}] request to get industry enum data", SecurityUtils.getUserId());

        List<EnumIndustry> result = enumIndustryRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        Map<String, EnumIndustry> map = result.stream()
                .collect(Collectors.toMap(EnumIndustry::getName, enumIndustry -> enumIndustry));
        result.forEach(c -> c.setJobFunctionsForIndustryId(getJobFunctionsForIndustryId(c, map)));
        return result;
    }

    @Cacheable(key = "'industryMapping'", unless = "#result == null")
    public List<EnumIndustryMapping> findAllEnumIndustryMapping() {
        log.info("[APN: EnumIndustryMappingService @{}] request to get industry mapping enum data", SecurityUtils.getUserId());

        List<EnumIndustryMapping> result = enumIndustryMappingRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        Map<String, EnumIndustryMapping> map = result.stream()
                .collect(Collectors.toMap(EnumIndustryMapping::getName, enumIndustryMapping -> enumIndustryMapping));
        result.forEach(c -> c.setJobFunctionsForIndustryId(getJobFunctionsMappingForIndustryId(c, map)));

        return result;
    }


    public EnumIndustry getIndustryById(Long id) {
        return enumIndustryRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("Industry id not exist!"));
    }

    public EnumIndustryMapping getIndustryMappingById(Long id) {
        return enumIndustryMappingRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("Industry Mapping id not exist!"));
    }

    private Long getJobFunctionsForIndustryId(EnumIndustry c, Map<String, EnumIndustry> map) {
        EnumIndustry enumIndustry = map.get(c.getName());
        while(enumIndustry != null) {
            if(StringUtils.isNotEmpty(enumIndustry.getSubJobFunctions())) {
                return enumIndustry.getId();
            }
            enumIndustry = map.get(enumIndustry.getParentCategory());
        }
        return -1L;
    }

    private Long getJobFunctionsMappingForIndustryId(EnumIndustryMapping c, Map<String, EnumIndustryMapping> map) {
        EnumIndustryMapping enumIndustry = map.get(c.getName());
        while(enumIndustry != null) {
            if(StringUtils.isNotEmpty(enumIndustry.getSubJobFunctions())) {
                return enumIndustry.getId();
            }
            enumIndustry = map.get(enumIndustry.getParentCategory());
        }
        return -1L;
    }


    @Cacheable(key = "'jobfunction'", unless = "#result == null")
    public List<EnumJobFunction> findAllEnumJobfunction() {
        log.info("[APN: EnumJobFunctionService @{}] request to get jobFunction enum data", SecurityUtils.getUserId());

        List<EnumJobFunction> result = enumJobFunctionRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'jobfunctionMapping'", unless = "#result == null")
    public List<EnumJobFunctionMapping> findAllEnumJobfunctionMapping() {
        log.info("[APN: EnumJobFunctionMappingService @{}] request to get jobFunction Mapping enum data", SecurityUtils.getUserId());

        List<EnumJobFunctionMapping> result = enumJobFunctionMappingRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

//    @Cacheable(key = "'jobfunctionMap'", unless = "#result == null")
//    public Map<Long, EnumJobFunction> findAllEnumJobfunctionMap() {
//        log.info("[APN: EnumJobFunctionService @{}] request to get jobFunction enum data", SecurityUtils.getUserId());
//
//        Map<Long, EnumJobFunction> result = enumJobFunctionRepository.findAll().stream().collect(Collectors.toMap(EnumJobFunction::getId, Function.identity()));;
//        if (CollectionUtil.isEmpty(result)) {
//            return null;
//        }
//        return result;
//    }

    @Cacheable(key = "'languages'", unless = "#result == null")
    public List<EnumLanguage> findAllEnumLanguages() {
        log.info("[APN: EnumLanguageService @{}] request to get languages enum data", SecurityUtils.getUserId());

        List<EnumLanguage> result = enumLanguageRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'languagesMap'", unless = "#result == null")
    public Map<Long, EnumLanguage> findAllEnumLanguagesMap() {
        log.info("[APN: EnumLanguageService @{}] request to get languages enum data", SecurityUtils.getUserId());

        Map<Long, EnumLanguage> result = enumLanguageRepository.findAll().stream().collect(Collectors.toMap(EnumLanguage::getId, Function.identity()));
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'levelOfExperience'", unless = "#result == null")
    public List<EnumLevelOfExperience> findAllEnumLevelOfExperience() {
        log.info("[APN: EnumLevelOfExperienceService @{}] request to get level of experience enum data", SecurityUtils.getUserId());

        List<EnumLevelOfExperience> result = enumLevelOfExperienceRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }


    @Cacheable(key = "'user_responsibility'", unless = "#result == null")
    public List<EnumUserResponsibility> findAllEnumUserResponsibility() {
        log.info("[APN: EnumUserResponsibilityService @{}] request to get user_responsibility enum data", SecurityUtils.getUserId());

        List<EnumUserResponsibility> result = enumUserResponsibilityRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    public List<EnumUserResponsibility> findTalentEnumUserResponsibility() {
        log.info("[APN: findTalentEnumUserResponsibility @{}] request to get user_responsibility enum data", SecurityUtils.getUserId());

        List<EnumUserResponsibility> allEnumUserResponsibility = findAllEnumUserResponsibility();
        if (CollectionUtil.isEmpty(allEnumUserResponsibility)) {
            return null;
        }
        return allEnumUserResponsibility.stream().filter(u -> StringUtils.isNotEmpty(u.getTalentEsKey()) && !u.getStatus().equals(EnumStatus.INACTIVE)).collect(Collectors.toList());
    }

    public List<EnumUserResponsibility> findTalentNoteEnumUserResponsibility() {
        log.info("[APN: findApplicationsEnumUserResponsibility @{}] request to get user_responsibility enum data", SecurityUtils.getUserId());

        List<EnumUserResponsibility> allEnumUserResponsibility = findAllEnumUserResponsibility();
        if (CollectionUtil.isEmpty(allEnumUserResponsibility)) {
            return null;
        }
        return allEnumUserResponsibility.stream().filter(u -> StringUtils.isNotEmpty(u.getTalentNoteEsKey()) && !u.getStatus().equals(EnumStatus.INACTIVE)).collect(Collectors.toList());
    }

    public List<EnumUserResponsibility> findApplicationsEnumUserResponsibility() {
        log.info("[APN: findApplicationsEnumUserResponsibility @{}] request to get user_responsibility enum data", SecurityUtils.getUserId());

        List<EnumUserResponsibility> allEnumUserResponsibility = findAllEnumUserResponsibility();
        if (CollectionUtil.isEmpty(allEnumUserResponsibility)) {
            return null;
        }
        return allEnumUserResponsibility.stream().filter(u -> StringUtils.isNotEmpty(u.getApplicationEsKey()) && !u.getStatus().equals(EnumStatus.INACTIVE)).collect(Collectors.toList());
    }

    public List<EnumUserResponsibility> findFoldersOfPreSubmitTalentsEnumUserResponsibility() {
        log.info("[APN: findApplicationsEnumUserResponsibility @{}] request to get user_responsibility enum data", SecurityUtils.getUserId());

        List<EnumUserResponsibility> allEnumUserResponsibility = findAllEnumUserResponsibility();
        if (CollectionUtil.isEmpty(allEnumUserResponsibility)) {
            return null;
        }
        return allEnumUserResponsibility.stream().filter(u -> StringUtils.isNotEmpty(u.getFoldersOfPreSubmitTalentsEsKey()) && !u.getStatus().equals(EnumStatus.INACTIVE)).collect(Collectors.toList());
    }

    @Cacheable(key = "'user_responsibilityMap'", unless = "#result == null")
    public Map<String, EnumUserResponsibility> findAllEnumUserResponsibilityNameMap() {
        log.info("[APN: EnumUserResponsibilityService @{}] request to get user_responsibility enum data", SecurityUtils.getUserId());

        Map<String, EnumUserResponsibility> result = enumUserResponsibilityRepository.findAll()
                .stream().collect(Collectors.toMap(EnumUserResponsibility::getLabel, Function.identity()));
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'enumReceivingAccount'", unless = "#result == null")
    public List<EnumReceivingAccount> findAllEnumInvoiceReceivingAccount() {
        log.info("[APN: EnumReceivingAccount @{}] request to get enumReceivingAccount enum data", SecurityUtils.getUserId());
        List<EnumReceivingAccount> result = enumInvoiceReceivingAccountRepository.findAll();
        if (CollUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'workAuthorization'", unless = "#result == null")
    public List<EnumWorkAuthorization> findAllEnumWorkAuthorization() {
        log.info("[APN: EnumWorkAuthorizationService @{}] request to get workAuthorization enum data", SecurityUtils.getUserId());

        List<EnumWorkAuthorization> result = enumWorkAuthorizationRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'enumAreaCode'", unless = "#result == null")
    public List<EnumCountry> findAllEnumAreaCode() {
        log.info("[APN: EnumCountryService @{}] request to get enumAreaCode enum data", SecurityUtils.getUserId());

        List<EnumCountry> result = enumAreaCodeRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'enumCompanyTag'", unless = "#result == null")
    public List<EnumCompanyTag> findAllEnumCompanyTag() {
        log.info("[APN: EnumCompanyTagService @{}] request to get enumCompanyTag enum data", SecurityUtils.getUserId());

        List<EnumCompanyTag> result = enumCompanyTagRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'enumServiceType:' + #tenantId", unless = "#result == null")
    public List<EnumCompanyServiceType> findAllEnumCompanyServiceType(Long tenantId) {
        log.info("[APN: EnumCompanyServiceType @{}] request to get enumServiceType enum data", SecurityUtils.getUserId());

        List<CompanyServiceTypeConnectTenant> companyServiceTypeConnectTenantList = companyServiceTypeConnectTenantRepository.findAllByTenantId(tenantId);
        if (CollectionUtil.isEmpty(companyServiceTypeConnectTenantList)) {
            companyServiceTypeConnectTenantList = companyServiceTypeConnectTenantRepository.findAllByTenantId(UNIVERSAL_TENANT_ID);
        }
        if (CollectionUtil.isEmpty(companyServiceTypeConnectTenantList)) {
            return null;
        }
        List<EnumCompanyServiceType> result = enumCompanyServiceTypeRepository.findAllById(companyServiceTypeConnectTenantList.stream().map(CompanyServiceTypeConnectTenant::getEnumId).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'enumEnumSalesLeadSource:' + #tenantId", unless = "#result == null")
    public List<EnumSalesLeadSource> findAllEnumSalesLeadSource(Long tenantId) {
        log.info("[APN: EnumSalesLeadSource @{}] request to get enumEnumSalesLeadSource enum data", SecurityUtils.getUserId());

        List<CompanySalesLeadSourceConnectTenant> companySalesLeadSourceConnectTenantList = salesLeadSourceConnectTenantRepository.findAllByTenantId(tenantId);
        if (CollectionUtil.isEmpty(companySalesLeadSourceConnectTenantList)) {
            companySalesLeadSourceConnectTenantList = salesLeadSourceConnectTenantRepository.findAllByTenantId(UNIVERSAL_TENANT_ID);
        }
        if (CollectionUtil.isEmpty(companySalesLeadSourceConnectTenantList)) {
            return null;
        }
        List<EnumSalesLeadSource> result = enumSalesLeadSourceRepository.findAllById(companySalesLeadSourceConnectTenantList.stream().map(CompanySalesLeadSourceConnectTenant::getEnumId).collect(Collectors.toList()));
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'enumJobPriority'", unless = "#result == null")
    public List<EnumJobPriority> findAllEnumJobPriority() {
        log.info("[APN: EnumJobPriorityService @{}] request to get enumJobPriority enum data", SecurityUtils.getUserId());

        List<EnumJobPriority> result = enumJobPriorityRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'enumCompanyContactTag'", unless = "#result == null")
    public List<EnumCompanyContactTag> findAllEnumCompanyContactTag() {
        log.info("[APN: EnumCompanyTagService @{}] request to get enumCompanyTag enum data", SecurityUtils.getUserId());

        List<EnumCompanyContactTag> result = enumCompanyContactTagRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }

    @Cacheable(key = "'enumJobPriorityMap'", unless = "#result == null")
    public Map<Long, EnumJobPriority> findAllEnumJobPriorityMap() {
        log.info("[APN: EnumJobPriorityService @{}] request to get enumJobPriority enum data", SecurityUtils.getUserId());

        Map<Long, EnumJobPriority> result = enumJobPriorityRepository.findAll().stream().collect(Collectors.toMap(EnumJobPriority::getId, Function.identity()));
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        return result;
    }


    /***
     * general method to map the name and enumid from ids(enumid)
     * @param ids
     * @param entityList, must have getter method for integer id and string name
     * @return
     * @param <T> Enum Object with Id(integer) and Name(String)
     */
    public <T> List<EnumRelationDTO> getEntitiesByIds(List<String> ids, List<T> entityList) {
        if (ids.isEmpty()) {
            return null;
        }

        return entityList.stream()
                .filter(o -> {
                    try {
                        Method getId = o.getClass().getMethod("getId");
                        return ids.contains(String.valueOf(getId.invoke(o)));
                    } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                        throw new RuntimeException(e);
                    }
                })
                .map(o -> {
                    try {
                        Method getId = o.getClass().getMethod("getId");
                        Method getName = o.getClass().getMethod("getName");
                        return new EnumRelationDTO(String.valueOf(getId.invoke(o)), (String) getName.invoke(o));
                    } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                        throw new RuntimeException(e);
                    }
                })
                .collect(Collectors.toList());
    }

    @Cacheable(key = "'enumVeteran'", unless = "#result == null")
    public List<EnumVeteran> findAllEnumVeteran() {
        log.info("[APN: EnumDictService @{}] request to get gender enum data", SecurityUtils.getUserId());

        List<EnumVeteran> result = enumVeteranRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'enumPreferredPronoun'", unless = "#result == null")
    public List<EnumPreferredPronoun> findAllEnumPreferredPronoun() {
        log.info("[APN: EnumDictService @{}] request to get preferred pronoun enum data", SecurityUtils.getUserId());

        List<EnumPreferredPronoun> result = enumPreferredPronounRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'enumEthnicity'", unless = "#result == null")
    public List<EnumEthnicity> findAllEnumEthnicity() {
        log.info("[APN: EnumDictService @{}] request to get ethnicity enum data", SecurityUtils.getUserId());

        List<EnumEthnicity> result = enumEthnicityRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'enumVoipCallResult'", unless = "#result == null")
    public List<EnumVoipCallResult> findAllEnumVoipCallResult() {
        log.info("[APN: EnumDictService @{}] request to get voip call result enum data", SecurityUtils.getUserId());
        List<EnumVoipCallResult> result = enumVoipCallResultRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Cacheable(key = "'enumVoipCallType'", unless = "#result == null")
    public List<EnumVoipCallType> findAllEnumVoipCallType() {
        log.info("[APN: EnumDictService @{}] request to get voip call type enum data", SecurityUtils.getUserId());
        List<EnumVoipCallType> result = enumVoipCallTypeRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }

    @Transactional
    public EnumVoipCallType findEnumVoipCallType(String name) {
        return enumVoipCallTypeRepository.findByNameLike(name).orElse(null);
    }
}