package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum LanguageEnum implements ConvertedEnum<String> {

    EN("en"),
    ZH("zh");

    private String dbValue;

    LanguageEnum(String en) {
        this.dbValue = en;
    }

    @Override
    public String toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<LanguageEnum, String> resolver =
            new ReverseEnumResolver<>(LanguageEnum.class, LanguageEnum::toDbValue);

    public static LanguageEnum fromDbValue(String dbValue) {
        return resolver.get(dbValue);
    }

}
