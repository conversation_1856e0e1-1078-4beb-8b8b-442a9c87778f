package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class EmploymentCategoryTypeConverter extends AbstractAttributeConverter<EmploymentCategoryType, Integer> {
    public EmploymentCategoryTypeConverter() {
        super(EmploymentCategoryType::toDbValue, EmploymentCategoryType::fromDbValue);
    }
}
