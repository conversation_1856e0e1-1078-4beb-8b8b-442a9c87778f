package com.altomni.apn.common.repository.talent;

import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderStatus;
import com.altomni.apn.common.domain.talent.TalentAssociationJobFolder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TalentRelateJobFolderRepository extends JpaRepository<TalentAssociationJobFolder, Long> {

    TalentAssociationJobFolder getTalentRelateJobFolderByJobIdIsAndUserIdIsAndStatusIs(Long jobId, Long userId, RelateJobFolderStatus status);

    TalentAssociationJobFolder getTalentRelateJobFolderByJobIdIsAndFolderIdIsAndUserIdIs(Long jobId, String folderId, Long userId);

    TalentAssociationJobFolder getTalentRelateJobFolderByFolderIdIsAndUserIdIs(String folderId, Long userId);

    List<TalentAssociationJobFolder> getTalentRelateJobFoldersByFolderIdIs(String folderId);

    List<TalentAssociationJobFolder> getTalentRelateJobFoldersByUserIdIs(Long userId);

    List<TalentAssociationJobFolder> getTalentRelateJobFoldersByJobIdIs(Long jobId);

    List<TalentAssociationJobFolder> getTalentRelateJobFoldersByJobIdIsAndFolderIdIsNot(Long jobId, String folderId);

    List<TalentAssociationJobFolder> findAllByFolderId(String folderId);

    List<TalentAssociationJobFolder> findAllByUserIdAndJobId(Long userId, Long jobId);

    List<TalentAssociationJobFolder> findAllByFolderIdIn(List<String> folderIds);

    @Query(value = "select f.job_id from talent_association_job_folder f LEFT JOIN talent_association_job_folder_talent ft on f.folder_id = ft.talent_association_job_folder_folder_id where ft.talent_id = :talentId and f.user_id = :userId", nativeQuery = true)
    List<Long> findJobIdByTalentId(@Param("talentId")Long talentId, @Param("userId") Long userId);
}
