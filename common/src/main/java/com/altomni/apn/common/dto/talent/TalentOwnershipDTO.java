package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipUserRole;
import com.altomni.apn.common.dto.user.NameOnlyUser;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A TalentOwnershipDTO.
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentOwnershipDTO implements Serializable {

    private static final long serialVersionUID = -3780897616693721987L;

    private Long id;

    private Long talentId;

    private Long userId;

    private Long tenantId;

    private TalentOwnershipType ownershipType;

    private TalentOwnershipUserRole userRole;

    private Instant expireTime;

    private NameOnlyUser user;

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentId() {
        return talentId;
    }

    public TalentOwnershipDTO talentId(Long talentId) {
        this.talentId = talentId;
        return this;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getUserId() {
        return userId;
    }

    public TalentOwnershipDTO userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public TalentOwnershipType getOwnershipType() {
        return ownershipType;
    }

    public TalentOwnershipDTO ownershipType(TalentOwnershipType ownershipType) {
        this.ownershipType = ownershipType;
        return this;
    }

    public void setOwnershipType(TalentOwnershipType ownershipType) {
        this.ownershipType = ownershipType;
    }

    public TalentOwnershipUserRole getUserRole() {
        return userRole;
    }

    public TalentOwnershipDTO userRole(TalentOwnershipUserRole userRole) {
        this.userRole = userRole;
        return this;
    }

    public void setUserRole(TalentOwnershipUserRole userRole) {
        this.userRole = userRole;
    }

    public Instant getExpireTime() {
        return expireTime;
    }

    public TalentOwnershipDTO expireTime(Instant expireTime) {
        this.expireTime = expireTime;
        return this;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public NameOnlyUser getUser() {
        return user;
    }

    public void setUser(NameOnlyUser user) {
        this.user = user;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TalentOwnershipDTO talentOwnership = (TalentOwnershipDTO) o;
        if (talentOwnership.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), talentOwnership.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "TalentOwnershipDTO{" +
            "id=" + id +
            ", talentId=" + talentId +
            ", userId=" + userId +
            ", ownershipType=" + ownershipType +
            ", userRole=" + userRole +
            ", expireTime=" + expireTime +
            ", user=" + user +
            '}';
    }
}
