package com.altomni.apn.common.aop.confidential;

import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;

public interface AttachConfidentialTalent {

    Long getTalentId();

    ConfidentialInfoDto getConfidentialInfo();

    void setConfidentialInfo(ConfidentialInfoDto confidentialInfo);

    Boolean getConfidentialTalentViewAble();

    void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble);

    void encrypt();
}
