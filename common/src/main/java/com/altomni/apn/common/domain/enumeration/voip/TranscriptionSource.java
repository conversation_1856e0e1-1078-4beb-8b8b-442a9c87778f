package com.altomni.apn.common.domain.enumeration.voip;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TranscriptionSource enumeration.
 */
public enum TranscriptionSource implements ConvertedEnum<Integer> {

    LARK(0),
    WHISPER(1),
    AlICLOUD(2);

    private final int dbValue;

    TranscriptionSource(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TranscriptionSource, Integer> resolver =
        new ReverseEnumResolver<>(TranscriptionSource.class, TranscriptionSource::toDbValue);

    public static TranscriptionSource fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
