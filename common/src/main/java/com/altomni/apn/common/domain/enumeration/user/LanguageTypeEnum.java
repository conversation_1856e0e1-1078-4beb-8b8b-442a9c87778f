package com.altomni.apn.common.domain.enumeration.user;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum LanguageTypeEnum implements ConvertedEnum<Integer> {

    ZH(0),
    EN(1),
    ;

    private final int dbValue;

    LanguageTypeEnum(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<LanguageTypeEnum, Integer> resolver =
            new ReverseEnumResolver<>(LanguageTypeEnum.class, LanguageTypeEnum::toDbValue);

    public static LanguageTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }


}
