package com.altomni.apn.common.dto.email;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailLogOverviewDTO {

    //root message info
    private Long id;
    private String transmissionId;
    private String subject;
    private String from;
    private List<String> to;
    private Instant sentTime;
    private String customerId1;
    private String customerId2;
    private Long userId;
    private Long tenantId;

    //stats info
    private Long messageCount;

    private List<Long> talentIds;
    private List<Long> contactIds;

    private Long teamId;

}
