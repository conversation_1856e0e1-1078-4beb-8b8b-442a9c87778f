package com.altomni.apn.common.dto.message;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageCreateWithVoicemailDTO {

    private UserBriefDTO userBriefDTO;

    private String talentName;

    private Long talentId;

    private Long tenantId;

}
