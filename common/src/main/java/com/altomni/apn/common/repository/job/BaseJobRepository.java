package com.altomni.apn.common.repository.job;

import com.altomni.apn.common.domain.job.JobV3;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface BaseJobRepository extends JpaRepository<JobV3, Long> {

    @Query(value = "select id from job_project where id =:privateJobTeamId", nativeQuery = true)
    List<Long> checkPrivateJob(@Param("privateJobTeamId") Long privateJobTeamId);

    @Query(value = "select user_id from user_job_relation where job_id=:jobId and status = 1", nativeQuery = true)
    Set<Long> findAuthorizedUserIdsByJobId(@Param("jobId") Long jobId);
}
