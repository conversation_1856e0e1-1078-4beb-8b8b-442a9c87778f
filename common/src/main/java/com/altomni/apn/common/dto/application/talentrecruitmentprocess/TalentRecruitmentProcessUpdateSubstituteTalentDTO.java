package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessUpdateSubstituteTalentDTO extends AuditingUser implements Serializable {

    private static final long serialVersionUID = -3826204108918323653L;

    private Long talentRecruitmentProcessId;

    private Long substituteTalentId;

    private Long relationProcessId;

    private boolean isSubstituteCandidate;

    private String updatedBy;

}
