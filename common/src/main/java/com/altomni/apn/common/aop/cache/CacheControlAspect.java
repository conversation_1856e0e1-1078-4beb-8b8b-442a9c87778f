package com.altomni.apn.common.aop.cache;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;

@Aspect
@Slf4j
public class CacheControlAspect {

    @Pointcut(value = "(@annotation(com.altomni.apn.common.aop.cache.CacheControl))")
    public void dataCut() {}

    @Around(value = "dataCut()")
    public Object addCacheControlHeader(ProceedingJoinPoint joinPoint) throws Throwable {
        Object result = joinPoint.proceed(); // 执行目标方法
        log.info("AddHeader");
        if (result instanceof ResponseEntity) {
            ResponseEntity<?> responseEntity = (ResponseEntity<?>) result;
            HttpHeaders headers = new HttpHeaders();
            headers.addAll(responseEntity.getHeaders());
            headers.setCacheControl("public, max-age=86400"); // 允许缓存 1 天
            return new ResponseEntity<>(responseEntity.getBody(), headers, responseEntity.getStatusCode());
        }

        return result; // 如果不是 ResponseEntity，直接返回原结果
    }

}
