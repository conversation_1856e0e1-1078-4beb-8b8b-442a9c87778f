package com.altomni.apn.common.domain.transactionrecord;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;

/**
 * 消费者失败信息记录实体
 */
@Table(name="t_mq_consume_failed_record")
@ApiModel(value = "消费失败记录表",description = "")
@Entity
@Data
public class CommonMqConsumeFailedRecord extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id ;

    /** 业务类型 */
    @ApiModelProperty(name = "业务类型")
    @Column(name = "bus_type")
    private Integer busType ;

    /** 业务id */
    @ApiModelProperty(name = "业务id")
    @Column(name = "bus_id")
    private BigInteger busId ;

    /** 接收数据 */
    @ApiModelProperty(name = "接收数据")
    @Column(name = "receive_message")
    private String receiveMessage ;

    /** 处理次数 */
    @ApiModelProperty(name = "处理次数")
    @Column(name = "consume_count")
    private Integer consumeCount ;

    /** 消费状态 0失败 1成功 */
    @ApiModelProperty(name = "消费状态 0失败 1成功")
    @Column(name = "receice_status")
    private Integer receiceStatus ;
}