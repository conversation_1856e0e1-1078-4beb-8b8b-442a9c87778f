package com.altomni.apn.common.enumeration.reportSubscriptions;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum PushTimeType implements ConvertedEnum<Integer> {
    //每天
    EVERY_DAY(1),
    //每周
    EVERY_WEEK(2),
    //每月
    EVERY_MONTH(3),
    ;

    private final int dbValue;

    PushTimeType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<PushTimeType, Integer> resolver =
            new ReverseEnumResolver<>(PushTimeType.class, PushTimeType::toDbValue);

    public static PushTimeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
