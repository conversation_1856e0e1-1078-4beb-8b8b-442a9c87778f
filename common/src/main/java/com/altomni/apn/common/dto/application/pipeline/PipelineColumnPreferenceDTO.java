//package com.altomni.apn.common.dto.application.pipeline;
//
//import com.altomni.apn.common.domain.AbstractAuditingEntity;
//import com.altomni.apn.common.domain.enumeration.search.ModuleType;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import java.io.Serializable;
//
//@Deprecated
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//public class PipelineColumnPreferenceDTO extends AbstractAuditingEntity implements Serializable {
//
//    private static final long serialVersionUID = 8613329249162802678L;
//
//    @ApiModelProperty(value = "data id")
//    private Long id;
//
//    @ApiModelProperty(value = "temp name")
//    private String tempName;
//
//    @ApiModelProperty(value = "creation type")
//    private CreationTypeDTO creationType;
//
//    @ApiModelProperty(value = "sort for front-end")
//    private String itemSort;
//
//    @ApiModelProperty(value = "sort all for front-end")
//    private String itemSortAll;
//
//    @ApiModelProperty(value = "Which module do user Preferences belong to")
//    private ModuleType module;
//
//    @ApiModelProperty(value = "Record the number of pages saved by the user")
//    private Integer pageSize;
//}
