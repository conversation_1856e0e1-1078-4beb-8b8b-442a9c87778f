package com.altomni.apn.common.dto.address;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import kotlin.jvm.Transient;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@ApiModel
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LocationDTO implements Serializable {

    private static final long serialVersionUID = -1387288056631129913L;

    private Long id;

    private String location;

    private Long residentialLocation;

    private String addressLine;

    private String city;

    private String province;

    private String provinceCode;

    private String county;

    private String country;

    private String countryCode;

    private String zipcode;

    private String originDisplay;

    private String officialCountry;

    private String officialCity;

    private String officialProvince;

    private String officialCounty;

    private List<Double> coordinate;

    @Transient
    private String originalLoc;

    public LocationDTO() {
    }

    public LocationDTO(String city, String province, String country) {
        this.city = city;
        this.province = province;
        this.country = country;
    }

    public static LocationDTO fromOriginalLoc(Long id, String originalLoc) {
        LocationDTO locationDTO = JSONUtil.toBean(originalLoc, LocationDTO.class);
        locationDTO.setId(id);
        return locationDTO;
    }
}
