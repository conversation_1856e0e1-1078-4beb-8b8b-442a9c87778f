package com.altomni.apn.common.service.enums;

import com.altomni.apn.common.domain.dict.EnumCountry;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.repository.enums.EnumAreaCodeRepository;
import com.altomni.apn.common.service.cache.EnumCommonService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnumCountryService {

    @Resource
    private EnumCommonService enumCommonService;
    @Resource
    private EnumAreaCodeRepository enumAreaCodeRepository;

    public List<EnumDictDTO> findAll() {
        List<EnumCountry> areaCodeList = enumCommonService.findAllEnumAreaCode();
        return areaCodeList.stream().map(EnumDictDTO::fromEnumCountry).collect(Collectors.toList());
    }

    public List<EnumDictDTO> findDeliveryCountries() {
        List<EnumCountry> areaCodeList = enumAreaCodeRepository.findAllDeliveryCountry();
        return areaCodeList.stream().map(EnumDictDTO::fromEnumCountry).collect(Collectors.toList());
    }
}
