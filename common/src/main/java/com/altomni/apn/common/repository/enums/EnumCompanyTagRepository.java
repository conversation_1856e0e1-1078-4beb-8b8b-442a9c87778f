package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumCompanyTag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * enum_company_tag repository
 * <AUTHOR>
 */
@Repository
public interface EnumCompanyTagRepository extends JpaRepository<EnumCompanyTag, Integer>, QuerydslPredicateExecutor<EnumCompanyTag> {
    
}
