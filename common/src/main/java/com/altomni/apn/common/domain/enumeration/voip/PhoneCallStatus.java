package com.altomni.apn.common.domain.enumeration.voip;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The PhoneCallStatus enumeration.
 */
public enum PhoneCallStatus implements ConvertedEnum<Integer> {

    ANSWERED(0),

    UNANSWERED(1);

    private final int dbValue;

    PhoneCallStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<PhoneCallStatus, Integer> resolver =
        new ReverseEnumResolver<>(PhoneCallStatus.class, PhoneCallStatus::toDbValue);

    public static PhoneCallStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
