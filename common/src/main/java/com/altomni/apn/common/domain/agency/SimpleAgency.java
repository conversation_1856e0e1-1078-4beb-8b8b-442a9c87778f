package com.altomni.apn.common.domain.agency;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatusConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A RecruitmentProcess.
 */
@Entity
@Table(name = "agency")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SimpleAgency extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4442477104358727842L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "phone")
    private String phone;

    @Column(name = "email")
    private String email;

    @Column(name = "agency_contact_first_name")
    private String agencyContactFirstName;

    @Column(name = "agency_contact_last_name")
    private String agencyContactLastName;

    @NotNull
    @Column(name = "agency_owner_user_id")
    private Long agencyOwnerUserId;

    @NotNull
    @Column(name = "agency_creator_user_id")
    private Long agencyCreatorUserId;

    @Convert(converter = ActiveStatusConverter.class)
    @Column(name = "status")
    private ActiveStatus status;

    @Column(name = "description")
    private String description;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id"));

}
