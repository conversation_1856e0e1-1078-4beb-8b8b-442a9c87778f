package com.altomni.apn.common.config.sso;

import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.ipg.resourceserver.authorization.ClientAuthorityProvider;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ApnClientAuthorityProvider implements ClientAuthorityProvider {

    @Override
    public List<GrantedAuthority> providerAuthorities() {
        return List.of(new SimpleGrantedAuthority(AuthoritiesConstants.SUPER_ADMIN),
                new SimpleGrantedAuthority(AuthoritiesConstants.ADMIN),
                new SimpleGrantedAuthority(AuthoritiesConstants.TENANT_ADMIN),
                new SimpleGrantedAuthority(AuthoritiesConstants.PLATFORM_ADMIN),
                new SimpleGrantedAuthority(AuthoritiesConstants.TIMESHEET_ADMIN));
    }
}
