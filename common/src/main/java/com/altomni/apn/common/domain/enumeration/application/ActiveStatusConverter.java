package com.altomni.apn.common.domain.enumeration.application;



import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class ActiveStatusConverter extends AbstractAttributeConverter<ActiveStatus, Integer> {
    public ActiveStatusConverter() {
        super(ActiveStatus::toDbValue, ActiveStatus::fromDbValue);
    }
}
