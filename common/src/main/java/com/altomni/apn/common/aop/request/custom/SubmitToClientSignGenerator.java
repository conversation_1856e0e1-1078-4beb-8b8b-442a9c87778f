package com.altomni.apn.common.aop.request.custom;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.ArgsSignGenerator;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToClientVO;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

public class SubmitToClientSignGenerator implements ArgsSignGenerator {

    public static Instant truncateToDate(Instant instant, ZoneId zoneId) {
        LocalDate localDate = instant.atZone(zoneId).toLocalDate();
        return localDate.atStartOfDay(zoneId).toInstant();
    }

    @Override
    public String generateSign(Object[] args) {
        if (ArrayUtil.isEmpty(args)) {
            return "empty";
        }

        StringBuilder sb = new StringBuilder();
        for (Object arg : args) {
            if (arg != null) {
                if (arg instanceof TalentRecruitmentProcessSubmitToClientVO) {
                    String jsonStr = JSONUtil.toJsonStr(arg);
                    TalentRecruitmentProcessSubmitToClientVO bean = JSONUtil.toBean(jsonStr, TalentRecruitmentProcessSubmitToClientVO.class);
                    Instant submitTime = bean.getSubmitTime();
                    if(submitTime != null) {
                        bean.setSubmitTime(truncateToDate(submitTime, ZoneId.of("UTC")));
                    }
                    sb.append(JSONUtil.toJsonStr(bean));
                } else {
                    sb.append(JSONUtil.toJsonStr(arg));
                }
            }
        }
        return SecureUtil.md5(sb.toString());
    }
}
