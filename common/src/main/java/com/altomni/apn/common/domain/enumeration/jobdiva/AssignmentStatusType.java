package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum AssignmentStatusType implements ConvertedEnum<Integer>
{
    PENDING(0),
    APPROVED(1),
    CLOSE(2);


    private final int dbValue;

    AssignmentStatusType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<AssignmentStatusType, Integer> resolver = new ReverseEnumResolver<>(AssignmentStatusType.class, AssignmentStatusType::toDbValue);

    public static AssignmentStatusType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
