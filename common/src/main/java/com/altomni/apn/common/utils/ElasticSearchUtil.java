package com.altomni.apn.common.utils;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.ElasticSearchConstants;
import okhttp3.Headers;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class ElasticSearchUtil {

    public static JSONObject parseHitsObjectFromResponse(String response) {
        if (StringUtils.isEmpty(response)) {
            return null;
        }
        JSONObject searchResultJSON = new JSONObject(response);
        if (!CommonUtils.existsKey(searchResultJSON, ElasticSearchConstants.HITS)) {
            return null;
        }
        return searchResultJSON.getJSONObject(ElasticSearchConstants.HITS);
    }

    public static Integer getObjectCountFromResponse(String response) {
        JSONObject hitsJSON = parseHitsObjectFromResponse(response);
        if (CommonUtils.existsKey(hitsJSON, ElasticSearchConstants.TOTAL)) {
            String totalObject = null;
           Object total =  hitsJSON.get(ElasticSearchConstants.TOTAL);
            if(total instanceof Integer) {
                totalObject = CommonUtils.getString(hitsJSON, ElasticSearchConstants.TOTAL);
            }else{
                totalObject = JSONUtil.parseObj(hitsJSON.get(ElasticSearchConstants.TOTAL)).getStr( ElasticSearchConstants.VALUE);

            }
            return StringUtils.isNotEmpty(totalObject) ? Integer.valueOf(totalObject) : 0;
        }
        return 0;
    }

    public static Integer getObjectCountFromResponseHeader(Headers responseHeader) {
        String totalObject = responseHeader.get(ElasticSearchConstants.TOTAL);
        return totalObject == null ? 0 : Integer.parseInt(totalObject);
    }

    public static Integer getResultTotalFromResponseHeader(Headers responseHeader) {
        String totalObject = responseHeader.get(ElasticSearchConstants.RESULT_TOTAL);
        return totalObject == null ? 0 : Integer.parseInt(totalObject);
    }

    public static Integer getCountTotalFromResponseHeader(Headers responseHeader) {
        String totalObject = responseHeader.get(ElasticSearchConstants.COUNT_TOTAL);
        return totalObject == null ? 0 : Integer.parseInt(totalObject);
    }

    public static Integer getTotalOwnedDataCountFromResponseHeader(Headers responseHeader) {
        String totalObject = responseHeader.get(ElasticSearchConstants.TOTAL_OWNED_DATA);
        return totalObject == null ? 0 : Integer.parseInt(totalObject);
    }

    public static Integer getTotalNotOwnedDataCountFromResponseHeader(Headers responseHeader) {
        String totalObject = responseHeader.get(ElasticSearchConstants.TOTAL_NOT_OWNED_DATA);
        return totalObject == null ? 0 : Integer.parseInt(totalObject);
    }
}
