package com.altomni.apn.common.enumeration.reportSubscriptions;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class PushTimeTypeConverter extends AbstractAttributeConverter<PushTimeType, Integer> {
    public PushTimeTypeConverter() {
        super(PushTimeType::toDbValue, PushTimeType::fromDbValue);
    }
}