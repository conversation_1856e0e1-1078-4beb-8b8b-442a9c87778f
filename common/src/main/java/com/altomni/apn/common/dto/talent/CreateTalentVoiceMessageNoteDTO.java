package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.config.OwnedByTeam;
import com.altomni.apn.common.config.OwnedByUser;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.CreatedByAbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.NotePriority;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import java.time.Instant;
import java.util.Map;
/**
 * A CreateTalentVoiceMessageNoteDTO.
 * !!!! different from CreateTalentNoteDTO, extend CreatedByAbstractPermissionAuditingEntity, which not generate lastModifiedBy and lastModifiedDate
 */
@Data
public class CreateTalentVoiceMessageNoteDTO {
    private Long id;
    private String title;
    private String note;

    private Map<String, Object> additionalInfo;
    private Boolean visible = true;
    private NotePriority priority = NotePriority.NORMAL;
    private Long talentId;
    private Long userId;
    private TalentNoteType noteType;

    private Integer noteStatus;

    private Boolean readStatus = false;

    private Boolean isSystem = false;

    private Long permissionUserId;

    private Long permissionTeamId;

    private String createdBy;

    private Instant createdDate = Instant.now();

    private String lastModifiedBy;

    private Instant lastModifiedDate = Instant.now();

}
