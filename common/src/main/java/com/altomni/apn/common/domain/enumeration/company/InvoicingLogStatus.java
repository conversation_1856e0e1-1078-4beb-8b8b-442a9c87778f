package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum InvoicingLogStatus implements ConvertedEnum<Integer> {

    APPLICATION(0,"Application"),
    APPROVAL(1,"Approval"),
    INVOICING(2,"Invoicing"),
    PAYMENT(3,"Payment");

    private final int dbValue;

    private final String description;

    InvoicingLogStatus(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoicingLogStatus, Integer> resolver = new ReverseEnumResolver<>(InvoicingLogStatus.class, InvoicingLogStatus::toDbValue);

    public static InvoicingLogStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
