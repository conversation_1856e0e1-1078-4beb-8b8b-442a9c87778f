package com.altomni.apn.common.datapermission.rule;

import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.expression.Expression;

import java.io.Serializable;
import java.util.Set;

/**
 * The interface of data security rule
 * We can customize the rule by implementing this interface
 *
 * <AUTHOR>
 */
public interface DataPermissionRule extends Serializable {

    /**
     * Return all tables that got involved in data security
     *
     * @return tables
     */
    Set<String> getTableNames();

    /**
     * Generate relevant WHERE/OR filters in terms of the table name and alias
     * And ensure the purpose of the sql, if it is select sql, then consider read-only permission
     * if it is update or delete sql, then consider writable permission
     *
     * Data security strategy: By rewriting the sql and appending filter to where clause, only return valid data
     *
     * @param tableName
     * @param tableAlias
     * @param isSelect if or not it is a select sql
     * @return Expression filters with user owner or team owner
     */
    Expression getExpression(String tableName, <PERSON><PERSON> tableAlias, boolean isSelect, TeamDataPermissionRespDTO deptDataPermission);

}
