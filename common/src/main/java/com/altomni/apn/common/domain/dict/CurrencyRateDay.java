package com.altomni.apn.common.domain.dict;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Date;

@ApiModel(description = " currency rate. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "currency_rate_day")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CurrencyRateDay extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /** 币种id */
    @ApiModelProperty(name = "币种id")
    @Column(name = "currency_id")
    private Long currencyId ;

    /** 利率日期 */
    @ApiModelProperty(name = "利率日期")
    @Column(name = "rate_day")
    private Instant rateDay ;

    /** 当日高值 */
    @ApiModelProperty(name = "当日高值")
    @Column(name = "from_usd_rate_high")
    private float rateHigh ;

    /** 当然均值 */
    @ApiModelProperty(name = "当然均值")
    @Column(name = "from_usd_rate_mid")
    private float rateMid ;

    /** 当日低值 */
    @ApiModelProperty(name = "当日低值")
    @Column(name = "from_usd_rate_low")
    private float rateLow ;
}