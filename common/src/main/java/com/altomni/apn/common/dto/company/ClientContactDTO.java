package com.altomni.apn.common.dto.company;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

@ApiModel
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ClientContactDTO implements Serializable{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "contact id ")
    private Long id;

    @ApiModelProperty(value = "contact name ")
    private String name;

    @ApiModelProperty(value = "contact firstName ")
    @NotNull
    private String firstName;

    @ApiModelProperty(value = "contact lastName ")
    @NotNull
    private String lastName;

    @ApiModelProperty(value = "The title for the contact", required = true)
    private String title;

    @ApiModelProperty(value = "email address. need to be unique.")
    @NotNull
    private String email;

    @ApiModelProperty(value = "phone number")
    private String phone;

    @ApiModelProperty(value = "wechat id")
    private String wechat;

    @ApiModelProperty(value = "Company entity Id")
    @NotNull
    private Long companyEntityId;

    @ApiModelProperty(value = "Department Tier 1")
    private String departmentTier1;

    @ApiModelProperty(value = "Department Tier 2")
    private String departmentTier2;

    @ApiModelProperty(value = "Department Tier 3")
    private String departmentTier3;

    @ApiModelProperty(value = "Name of person report to. It may or may not exist in the client contact table.")
    private String reportTo;

    @ApiModelProperty(value = "JSON list of comments. Each comment should contain content, time, userId")
    private String comments;

    @ApiModelProperty(value = "Inactive means this is an old record, the person has left the company.")
    private boolean active = true;

    @ApiModelProperty(value = "contactCategory")
    @NotNull
    private Integer contactCategory;

    @ApiModelProperty(value = "other category")
    private String otherCategory;

    @ApiModelProperty(value = "address")
    private String address;

    @ApiModelProperty(value = "Address")
    private Long addressId;

    @ApiModelProperty(value = "businessGroup")
    private String businessGroup;

    @ApiModelProperty(value = "businessUnit")
    private String businessUnit;

    @ApiModelProperty(value = "lastFollowUpTime")
    private Instant lastFollowUpTime;

    @ApiModelProperty(value = "profile")
    private String profile;

    @ApiModelProperty(value = "zipcode")
    private String zipcode;

    @ApiModelProperty(value = "inactived")
    private Boolean inactived;

    @ApiModelProperty(value = "esId from common pool")
    private String esId;

    public ClientContactDTO() {
    }

    public ClientContactDTO(Long id, String name, String firstName, String lastName, String email) {
        this.id = id;
        this.name = name;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
    }

    public ClientContactDTO(Long companyEntityId, boolean active) {
        this.companyEntityId = companyEntityId;
        this.active = active;
    }
}
