package com.altomni.apn.common.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The InterviewType enumeration.
 */
public enum InterviewType implements ConvertedEnum<Integer> {

    PHONE(0),

    VIDEO(1),

    ONSITE(2),

    OTHER(3);

    private final Integer dbValue;

    InterviewType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<InterviewType, Integer> resolver =
        new ReverseEnumResolver<>(InterviewType.class, InterviewType::toDbValue);

    public static InterviewType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
