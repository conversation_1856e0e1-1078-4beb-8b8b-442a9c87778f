package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * TimeSheetBreakTimeType
 */
public enum ExpenseType implements ConvertedEnum<Integer> {
    MEALS(0,"Meals"),
    OFFICE_TOOLS(1,"Office-Tools ,Equipment,Supplies"),
    MILEAGE(2,"Mileage"),
    TRAVEL(3,"Travel"),
    LODGING(4,"Lodging"),
    CELL_PHONE(5,"Cell Phone"),
    MISC(6,"Mics."),
    PROFESSIONAL_FEE(7,"Professional fees/Membership");

    private final int dbValue;
    private final String description;

    ExpenseType(int dbValue,String description) {
        this.dbValue = dbValue;
        this.description = description;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDescription() {
        return description;
    }

    public static final ReverseEnumResolver<ExpenseType, Integer> resolver = new ReverseEnumResolver<>(ExpenseType.class, ExpenseType::toDbValue);

    public static ExpenseType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
