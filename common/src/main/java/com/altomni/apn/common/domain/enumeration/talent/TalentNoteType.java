package com.altomni.apn.common.domain.enumeration.talent;

import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.Objects;

/**
 * The TalentNoteType enumeration.
 */
public enum TalentNoteType implements ConvertedEnum<Integer> {
    //电话联系 Candidate Phone Call
    CALL_CANDIDATES(0, "Candidate Phone Call", "候选人电话沟通"),
    //顾问面试 Candidate In Person
    CONSULTANT_INTERVIEW(1, "Candidate In Person", "候选人面谈"),
    //其他 Others
    OTHERS(2, "Others", "其他"),
    //邮件联系  Candidate Email / Message
    EMAIL_CANDIDATES(3, "Candidate Email / Message", "候选人邮件/消息"),
    //视频联系  Candidate Video Call
    CALL_VIDEO(4, "Candidate Video Call", "候选人视频沟通"),

    ICI(5, "Initial Candidate Interview", "初次候选人面试"),

    CALL_CANDIDATE_VOICE_MESSAGE(6, "Candidate Voice Mail", "候选人电话留言")
    ;

    private final int dbValue;

    private final String enDisplay;

    private final String cnDisplay;

    TalentNoteType(int dbValue, String enDisplay, String cnDisplay) {
        this.dbValue = dbValue;
        this.enDisplay = enDisplay;
        this.cnDisplay = cnDisplay;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TalentNoteType, Integer> resolver =
        new ReverseEnumResolver<>(TalentNoteType.class, TalentNoteType::toDbValue);

    public static TalentNoteType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static TalentNoteType getNoteTypeByName(String name) {
        if (StrUtil.isBlank(name)) {
            return TalentNoteType.OTHERS;
        }
        for (TalentNoteType value : values()) {
            if (Objects.equals(value.enDisplay.toLowerCase(), name.toLowerCase())
                    || Objects.equals(value.cnDisplay.trim(), name.trim())) {
                return value;
            }
        }
        return TalentNoteType.OTHERS;
    }

    public String getEnDisplay() {
        return enDisplay;
    }

}
