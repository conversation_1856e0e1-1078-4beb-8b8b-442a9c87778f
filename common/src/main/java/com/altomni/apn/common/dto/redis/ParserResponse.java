package com.altomni.apn.common.dto.redis;

import com.altomni.apn.common.enumeration.ParseStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ParserResponse implements Serializable {

    private static final long serialVersionUID = 7547052176540537819L;

    private String uuid;

    private String fileName;

    private Long talentId;

    private String talentName;

    private String companyName;
    private String title;

    private String text;

    private String s3Link;

    /**
     * redis columns
     */
    private ParseStatus status;

    private String data;

    /**
     * imgagesInfo
     */
    private ParseStatus uploadStatus;

    private String portraitLink;

    private String displayLink;

    private String contentType;

    /**
     * parse progress columns, only valid for create first
     */
    private String lastUpdateTime;

    private Map<String, String> postPolicy;


}
