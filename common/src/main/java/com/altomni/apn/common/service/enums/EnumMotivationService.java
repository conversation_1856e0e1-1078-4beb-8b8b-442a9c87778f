package com.altomni.apn.common.service.enums;

import com.altomni.apn.common.domain.dict.EnumMotivation;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EnumMotivationService {

    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumMotivation> findAllEnumMotivation() {
        List<EnumMotivation> enumMotivationList = enumCommonService.findAllEnumMotivation();
        enumMotivationList = enumMotivationList.stream().sorted(Comparator.comparing(EnumMotivation::getDisplayOrder)).collect(Collectors.toList());
        return enumMotivationList;
    }

    public EnumMotivation findEnumMotivationById(Integer id) {
        List<EnumMotivation> enumMotivationList = enumCommonService.findAllEnumMotivation();
        Optional<EnumMotivation> enumMotivation = enumMotivationList.stream().filter(o -> o.getId().equals(id)).findFirst();
        if (enumMotivation.isEmpty()) {
            throw new CustomParameterizedException("Enum currency is null , id : " + id);
        }
        return enumMotivation.get();
    }

    public EnumMotivation findEnumMotivationByName(String name) {
        List<EnumMotivation> enumMotivationList = enumCommonService.findAllEnumMotivation();
        Optional<EnumMotivation> enumCurrency = enumMotivationList.stream().filter(o -> o.getName().equals(name)).findFirst();
        if (enumCurrency.isEmpty()) {
            throw new CustomParameterizedException("Enum currency is null , name : " + name);
        }
        return enumCurrency.get();
    }

}
