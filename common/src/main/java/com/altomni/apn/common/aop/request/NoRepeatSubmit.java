package com.altomni.apn.common.aop.request;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * @author: <PERSON><PERSON>.<PERSON>
 * @date: 2019-01-17 11:55
 * @description:
 **/
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface NoRepeatSubmit {
    /**
     * 过期时间，单位：毫秒
     * @return 过期时间
     */
    long expire() default 2000;
}
