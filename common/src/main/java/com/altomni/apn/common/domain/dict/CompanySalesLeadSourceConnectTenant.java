package com.altomni.apn.common.domain.dict;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "CompanyServiceTypeConnectTenant")
@Entity
@Data
@Table(name = "company_sales_lead_source_connect_tenant")
public class CompanySalesLeadSourceConnectTenant implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "enum_company_sale_lead_source_id")
    private Long enumId;

}
