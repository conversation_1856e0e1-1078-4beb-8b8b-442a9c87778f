package com.altomni.apn.common.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentTopEducationDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    private TalentCollegeInfoDTO collegeInfo;

    private String collegeName;

    private String degreeLevel;

    private String majorName;

    private LocalDate startDate;

    private LocalDate endDate;


}


