package com.altomni.apn.common.vo.talent;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentEmailContactVO {

    private Long talentId;

    private Long contactId;

    private String firstName;

    private String lastName;

    private String fullName;

    private String email;


}
