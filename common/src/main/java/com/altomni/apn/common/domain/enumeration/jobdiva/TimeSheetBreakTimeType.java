package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * TimeSheetBreakTimeType
 */
public enum TimeSheetBreakTimeType implements ConvertedEnum<Integer>
{
    TIME_IN(0),
    TIME_OUT(1),
    MEAL_BREAK_IN(2),
    MEAL_BREAK_OUT(3);


    private final int dbValue;

    TimeSheetBreakTimeType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TimeSheetBreakTimeType, Integer> resolver = new ReverseEnumResolver<>(TimeSheetBreakTimeType.class, TimeSheetBreakTimeType::toDbValue);

    public static TimeSheetBreakTimeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
