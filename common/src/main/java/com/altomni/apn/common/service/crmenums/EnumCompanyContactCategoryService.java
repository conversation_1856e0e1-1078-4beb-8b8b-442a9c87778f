package com.altomni.apn.common.service.crmenums;

import com.altomni.apn.common.domain.crmenums.EnumCompanyContactCategory;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.crmenums.EnumCrmCommonService;
import com.altomni.apn.common.vo.crmenums.EnumDictVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnumCompanyContactCategoryService {

    @Resource
    private EnumCrmCommonService enumCrmCommonService;

    public List<EnumCompanyContactCategory> findAllByIds(List<Integer> ids) {
        List<EnumCompanyContactCategory> enumCompanyContactCategoryList = enumCrmCommonService.findAllEnumCompanyContactCategory();
        return enumCompanyContactCategoryList.stream().filter(o -> ids.contains(o.getId())).collect(Collectors.toList());
    }

    public List<EnumDictVO> findAllCompanyContactCategory(SortType type) {
        List<EnumCompanyContactCategory> enumCompanyContactCategoryList = enumCrmCommonService.findAllEnumCompanyContactCategory();

        if (enumCompanyContactCategoryList == null) {
            return new ArrayList<>();
        }

        return enumCompanyContactCategoryList.stream().map(o -> {
            EnumDictVO dictVO = new EnumDictVO();
            dictVO.setId(o.getId());
            dictVO.setLabel(SortType.CN.equals(type) ? o.getCnDisplay() : o.getEnDisplay());
            dictVO.setName(o.getName());
            return dictVO;
        }).collect(Collectors.toList());
    }


}

