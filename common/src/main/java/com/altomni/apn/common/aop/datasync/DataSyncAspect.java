package com.altomni.apn.common.aop.datasync;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.aop.datasync.annotation.DataSyncAnnotation;
import com.altomni.apn.common.aop.datasync.factory.DataSyncServiceTemplateFactory;
import com.altomni.apn.common.aop.datasync.template.DataSyncHandlerTemplate;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.lang.reflect.Method;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 数据同步切面
 * <AUTHOR>
 */
@Slf4j
@Aspect
public class DataSyncAspect {

    public static final String SYNC_DATA_ID = "SYNC_DATA_ID";

    @Pointcut(value = "(@annotation(com.altomni.apn.common.aop.datasync.annotation.DataSyncAnnotation))")
    public void doDataSyncCut() {}

    
    /**
     * DataSync注解方法执行 Around 触发事件
     *
     * @param joinPoint
     * @param
     */
    @Around(value = "doDataSyncCut()")
    public Object syncData(ProceedingJoinPoint joinPoint) throws Throwable {
        log.info("[apn@{}] data sync aop is start time = {}", SecurityUtils.getUserId(), Instant.now());
        //真正的接口执行内容
        Object keys = joinPoint.proceed();
        String syncDataId = MDC.get(SYNC_DATA_ID);
        log.info("[apn@{}] data sync aop syncDataId = {}", SecurityUtils.getUserId(), syncDataId);
        if (StrUtil.isNotBlank(syncDataId)) {
            // 从切面织入点处通过反射机制获取织入点处的方法
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            // 获取切入点所在的方法
            Method method = signature.getMethod();
            DataSyncAnnotation dataSyncAnnotation = method.getAnnotation(DataSyncAnnotation.class);
            SyncIdTypeEnum syncIdTypeEnum = dataSyncAnnotation.dataType();
            log.info("[apn@{}] data sync aop type = {}", SecurityUtils.getUserId(), syncIdTypeEnum);
            Map<SyncIdTypeEnum, DataSyncHandlerTemplate> dataSyncHandlerMap = DataSyncServiceTemplateFactory.getDataSyncServiceTemplateHashMap();
            if (CollUtil.isEmpty(dataSyncHandlerMap)) {
                log.error("[apn@{}] data sync is fail, handler init is error, handler map is empty", SecurityUtils.getUserId());
            } else {
                //获取对应的处理器
                DataSyncHandlerTemplate dataSyncHandler = dataSyncHandlerMap.get(syncIdTypeEnum);
                if (ObjectUtil.isEmpty(dataSyncHandler)) {
                    log.error("[apn@{}] data sync is fail, handler is lack, dataIds = {}, type = {}", SecurityUtils.getUserId(), syncDataId, syncIdTypeEnum);
                } else {
                    //开始去同步数据
                    log.info("[apn@{}] data sync aop is finish , handler is start, syncDataId = {}, type = {}", SecurityUtils.getUserId(), syncDataId, syncIdTypeEnum);
                    SecurityContext securityContext = SecurityContextHolder.getContext();
                    CompletableFuture.runAsync(() -> {
                        //异步处理,避免影响主业务
                        SecurityContextHolder.setContext(securityContext);
                        dataSyncHandler.syncDataToMq(syncDataId, syncIdTypeEnum, true, 3);
                    });
                }
            }
        } else {
            log.error("[apn@{}]data sync is fail, sync data id is null", SecurityUtils.getUserId());
        }
        log.info("[apn@{}]data sync aop is end time = {}", SecurityUtils.getUserId(), Instant.now());
        //每次处理完需要清理,防止出现内存问题
        MDC.clear();
        return keys;
    }


}
