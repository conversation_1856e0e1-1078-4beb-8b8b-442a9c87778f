package com.altomni.apn.common.domain.application;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

 @Entity
@Table(name = "talent_recruitment_process")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
 public class TalentRecruitmentProcessForCanal extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -386368362649529657L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "recruitment_process_id")
    private Long recruitmentProcessId;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "note")
    private String note;
}
