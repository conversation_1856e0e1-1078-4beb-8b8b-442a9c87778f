package com.altomni.apn.common.config;

import com.altomni.apn.common.interceptor.SecurityObjectLevelInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {
    @Resource
    private SecurityObjectLevelInterceptor securityInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(securityInterceptor);
    }
}
