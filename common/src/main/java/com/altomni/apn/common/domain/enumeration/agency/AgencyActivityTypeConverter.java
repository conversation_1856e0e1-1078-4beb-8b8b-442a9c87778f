package com.altomni.apn.common.domain.enumeration.agency;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class AgencyActivityTypeConverter extends AbstractAttributeConverter<AgencyActivityType, Integer> {
    public AgencyActivityTypeConverter() {
        super(AgencyActivityType::toDbValue, AgencyActivityType::fromDbValue);
    }
}
