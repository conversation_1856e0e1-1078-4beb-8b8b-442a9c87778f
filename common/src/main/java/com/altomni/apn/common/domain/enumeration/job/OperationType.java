package com.altomni.apn.common.domain.enumeration.job;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum OperationType implements ConvertedEnum<Integer> {

    NULL(404),
    CREATE(0),
    UPDATE(1),
    CLOSE(2);

    private final int dbValue;

    OperationType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<OperationType, Integer> resolver =
        new ReverseEnumResolver<>(OperationType.class, OperationType::toDbValue);

    public static OperationType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
