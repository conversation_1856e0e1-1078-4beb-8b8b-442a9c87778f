package com.altomni.apn.common.aop.cache;

import com.altomni.apn.common.enumeration.KeyType;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.CacheType;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Aspect
@Component
public class CacheCleanAspect {

    @Autowired
    private CommonRedisService redisService;

    @After("@annotation(cacheClean)")
    public void cleanCache(JoinPoint point, CacheClean cacheClean) {
        List<KeyType> keyTypes = Arrays.asList(cacheClean.keyTypes());

        if (keyTypes.isEmpty()) {
            return;
        }

        Set<String> keys = keyTypes.stream()
                .map(this::handleKey)
                .collect(Collectors.toSet());
        if (cacheClean.async()) {
            asyncClean(keys);
        } else {
            redisService.deleteBatch(keys);
        }
    }

    private String handleKey(KeyType keyType) {
        String result = switch (keyType) {
            case TEAM_USER_TREE -> {
                Long tenantId = SecurityUtils.getTenantId();
                yield keyType.getPrefix().formatted(tenantId == null ? "*" : tenantId);
            }
            default -> throw new IllegalStateException("Unexpected value: " + keyType);
        };
        return result;
    }


    public void asyncClean(Collection<String> keys) {
        CompletableFuture.runAsync(()->{
            redisService.deleteBatch(keys);
        });
    }
}