package com.altomni.apn.common.domain.enumeration.company;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The AccountCompanyStatus enumeration.
 */
public enum AccountCompanyStatus implements ConvertedEnum<Integer> {
    PROSPECT(10),
    INACTIVE(20),
    ACTIVE(30);


    private final Integer dbValue;

    AccountCompanyStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<AccountCompanyStatus, Integer> resolver =
        new ReverseEnumResolver<>(AccountCompanyStatus.class, AccountCompanyStatus::toDbValue);

    public static AccountCompanyStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
