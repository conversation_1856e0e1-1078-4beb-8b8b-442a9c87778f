package com.altomni.apn.common.domain.report;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.ReportType;
import com.altomni.apn.common.domain.enumeration.ReportTypeConverter;
import com.altomni.apn.common.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Comparator;

@ApiModel(description = "report_summary storage the summary reports data")
@Entity
@Table(name = "summary_report")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SummaryReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonIgnore
    private Long id;

    @JsonIgnore
    @Column(name = "batch_id")
    private String batchId;

    @JsonIgnore
    @ApiModelProperty(value = "Report type", allowableValues = "COMPANY, RECRUITER, COMPANY_RECRUITER")
    @Convert(converter = ReportTypeConverter.class)
    @Column(name = "report_type")
    private ReportType reportType;

    @Column(name = "company")
	private String company;

    @Column(name = "count_of_job")
	private Integer countOfJob = 0;

    @Column(name = "job_id")
    private String jobId;

    @Column(name = "recruiter_user_id")
	private Long recruiterUserId;

    @Column(name = "recruiter")
    private String recruiter;

    @Column(name = "hiring_manager_id")
    private Long hiringManagerId;

    @Column(name = "hiring_manager")
    private String hiringManager;

    @Column(name = "opening_count")
	private Integer openingCount = 0;

    @Column(name = "applied_count")
    private Integer appliedCount = 0;

    @Column(name = "applied_activity_id")
    private String appliedActivityId;

    @Column(name = "submitted_count")
	private Integer submittedCount = 0;

    @Column(name = "submitted_activity_id")
    private String submittedActivityId;

    @Column(name = "interview_count")
	private Integer interviewCount = 0;

    @Column(name = "interview_activity_id")
    private String interviewActivityId;

    @Column(name = "offered_count")
	private Integer offeredCount = 0;

    @Column(name = "offered_activity_id")
    private String offeredActivityId;

    @Column(name = "offer_accepted_count")
    private Integer offerAcceptedCount = 0;

    @Column(name = "offer_accepted_activity_id")
    private String offerAcceptedActivityId;

    @Column(name = "started_count")
	private Integer startedCount = 0;

    @Column(name = "started_activity_id")
    private String startedActivityId;

    @Column(name = "offer_rejected_count")
    private Integer offerRejectedCount = 0;

    @Column(name = "offer_rejected_activity_id")
    private String offerRejectedActivityId;

    @Column(name = "client_rejected_count")
    private Integer clientRejectedCount = 0;

    @Column(name = "client_rejected_activity_id")
    private String clientRejectedActivityId;

    @Column(name = "shortlisted_by_client_count")
    private Integer shortlistedByClientCount = 0;

    @Column(name = "shortlisted_by_client_activity_id")
    private String shortlistedByClientActivityId;

    @Column(name = "candidate_quit_count")
    private Integer candidateQuitCount = 0;

    @Column(name = "candidate_quit_activity_id")
    private String candidateQuitActivityId;

    @Column(name = "so_percent")
    private String soPercent;

    @Column(name = "ho_percent")
    private String hoPercent;

    @Column(name = "job_as_pr_count")
    private Integer jobAsPrCount = 0;

    @CreatedBy
    @Column(name = "created_by", nullable = false, length = 50, updatable = false)
    private String createdBy;

    @CreatedDate
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;


	public SummaryReport() {

	}

	public SummaryReport(String company, Integer countOfJob, Integer openingCount) {
	    this.company = company;
	    this.countOfJob  = countOfJob;
	    this.openingCount = openingCount;
	}

    public SummaryReport(Integer appliedCount, String appliedActivityId, Integer submittedCount, String submittedActivityId, Integer interviewCount, String interviewActivityId, Integer offeredCount, String offeredActivityId, Integer offerAcceptedCount, String offerAcceptedActivityId, Integer startedCount, String startedActivityId, Integer offerRejectedCount, String offerRejectedActivityId, Integer clientRejectedCount, String clientRejectedActivityId, Integer shortlistedByClientCount, String shortlistedByClientActivityId, Integer candidateQuitCount, String candidateQuitActivityId) {
        this.appliedCount = appliedCount;
        this.appliedActivityId = appliedActivityId;
        this.submittedCount = submittedCount;
        this.submittedActivityId = submittedActivityId;
        this.interviewCount = interviewCount;
        this.interviewActivityId = interviewActivityId;
        this.offeredCount = offeredCount;
        this.offeredActivityId = offeredActivityId;
        this.offerAcceptedCount = offerAcceptedCount;
        this.offerAcceptedActivityId = offerAcceptedActivityId;
        this.startedCount = startedCount;
        this.startedActivityId = startedActivityId;
        this.offerRejectedCount = offerRejectedCount;
        this.offerRejectedActivityId = offerRejectedActivityId;
        this.clientRejectedCount = clientRejectedCount;
        this.clientRejectedActivityId = clientRejectedActivityId;
        this.shortlistedByClientCount = shortlistedByClientCount;
        this.shortlistedByClientActivityId = shortlistedByClientActivityId;
        this.candidateQuitCount = candidateQuitCount;
        this.candidateQuitActivityId = candidateQuitActivityId;
    }

    public JSONObject toJSON() {
        JSONObject result = new JSONObject();
        JsonUtil.fluentPut(result, "appliedCount", appliedCount);
        JsonUtil.fluentPutString(result, "appliedActivityId", appliedActivityId);
        JsonUtil.fluentPut(result, "submittedCount", submittedCount);
        JsonUtil.fluentPutString(result, "submittedActivityId", submittedActivityId);
        JsonUtil.fluentPut(result, "interviewCount", interviewCount);
        JsonUtil.fluentPutString(result, "interviewActivityId", interviewActivityId);
        JsonUtil.fluentPut(result, "offeredCount", offeredCount);
        JsonUtil.fluentPutString(result, "offeredActivityId", offeredActivityId);
        JsonUtil.fluentPut(result, "offerAcceptedCount", offerAcceptedCount);
        JsonUtil.fluentPutString(result, "offerAcceptedActivityId", offerAcceptedActivityId);
        JsonUtil.fluentPut(result, "startedCount", startedCount);
        JsonUtil.fluentPutString(result, "startedActivityId", startedActivityId);
        JsonUtil.fluentPut(result, "offerRejectedCount", offerRejectedCount);
        JsonUtil.fluentPutString(result, "offerRejectedActivityId", offerRejectedActivityId);
        JsonUtil.fluentPut(result, "clientRejectedCount", clientRejectedCount);
        JsonUtil.fluentPutString(result, "clientRejectedActivityId", clientRejectedActivityId);
        JsonUtil.fluentPut(result, "shortlistedByClientCount", shortlistedByClientCount);
        JsonUtil.fluentPutString(result, "shortlistedByClientActivityId", shortlistedByClientActivityId);
        JsonUtil.fluentPut(result, "candidateQuitCount", candidateQuitCount);
        JsonUtil.fluentPutString(result, "candidateQuitActivityId", candidateQuitActivityId);
        return result;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public ReportType getReportType() {
        return reportType;
    }

    public void setReportType(ReportType reportType) {
        this.reportType = reportType;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Integer getCountOfJob() {
        return countOfJob;
    }

    public void setCountOfJob(Integer countOfJob) {
        this.countOfJob = countOfJob;
    }

    public Long getRecruiterUserId() {
        return recruiterUserId;
    }

    public void setRecruiterUserId(Long recruiterUserId) {
        this.recruiterUserId = recruiterUserId;
    }

    public String getRecruiter() {
        return recruiter;
    }

    public void setRecruiter(String recruiter) {
        this.recruiter = recruiter;
    }

    public String getJobId() {
        return jobId;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public Integer getOpeningCount() {
        return openingCount;
    }

    public void setOpeningCount(Integer openingCount) {
        this.openingCount = openingCount;
    }

    public Integer getAppliedCount() {
        return appliedCount;
    }

    public void setAppliedCount(Integer appliedCount) {
        this.appliedCount = appliedCount;
    }

    public Integer getSubmittedCount() {
        return submittedCount;
    }

    public void setSubmittedCount(Integer submittedCount) {
        this.submittedCount = submittedCount;
    }

    public String getSubmittedActivityId() {
        return submittedActivityId;
    }

    public void setSubmittedActivityId(String submittedActivityId) {
        this.submittedActivityId = submittedActivityId;
    }

    public Integer getInterviewCount() {
        return interviewCount;
    }

    public void setInterviewCount(Integer interviewCount) {
        this.interviewCount = interviewCount;
    }

    public String getInterviewActivityId() {
        return interviewActivityId;
    }

    public void setInterviewActivityId(String interviewActivityId) {
        this.interviewActivityId = interviewActivityId;
    }

    public Integer getOfferedCount() {
        return offeredCount;
    }

    public void setOfferedCount(Integer offeredCount) {
        this.offeredCount = offeredCount;
    }

    public String getOfferedActivityId() {
        return offeredActivityId;
    }

    public void setOfferedActivityId(String offeredActivityId) {
        this.offeredActivityId = offeredActivityId;
    }

    public Integer getOfferAcceptedCount() {
        return offerAcceptedCount;
    }

    public void setOfferAcceptedCount(Integer offerAcceptedCount) {
        this.offerAcceptedCount = offerAcceptedCount;
    }

    public String getOfferAcceptedActivityId() {
        return offerAcceptedActivityId;
    }

    public void setOfferAcceptedActivityId(String offerAcceptedActivityId) {
        this.offerAcceptedActivityId = offerAcceptedActivityId;
    }

    public Integer getStartedCount() {
        return startedCount;
    }

    public void setStartedCount(Integer startedCount) {
        this.startedCount = startedCount;
    }

    public String getStartedActivityId() {
        return startedActivityId;
    }

    public void setStartedActivityId(String startedActivityId) {
        this.startedActivityId = startedActivityId;
    }

    public String getSoPercent() {
        return soPercent;
    }

    public void setSoPercent(String soPercent) {
        this.soPercent = soPercent;
    }

    public String getHoPercent() {
        return hoPercent;
    }

    public void setHoPercent(String hoPercent) {
        this.hoPercent = hoPercent;
    }

    public Integer getJobAsPrCount() {
        return jobAsPrCount;
    }

    public void setJobAsPrCount(Integer jobAsPrCount) {
        this.jobAsPrCount = jobAsPrCount;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public String getAppliedActivityId() {
        return appliedActivityId;
    }

    public void setAppliedActivityId(String appliedActivityId) {
        this.appliedActivityId = appliedActivityId;
    }

    public Long getHiringManagerId() {
        return hiringManagerId;
    }

    public void setHiringManagerId(Long hiringManagerId) {
        this.hiringManagerId = hiringManagerId;
    }

    public String getHiringManager() {
        return hiringManager;
    }

    public void setHiringManager(String hiringManager) {
        this.hiringManager = hiringManager;
    }

    public Integer getOfferRejectedCount() {
        return offerRejectedCount;
    }

    public void setOfferRejectedCount(Integer offerRejectedCount) {
        this.offerRejectedCount = offerRejectedCount;
    }

    public String getOfferRejectedActivityId() {
        return offerRejectedActivityId;
    }

    public void setOfferRejectedActivityId(String offerRejectedActivityId) {
        this.offerRejectedActivityId = offerRejectedActivityId;
    }

    public Integer getClientRejectedCount() {
        return clientRejectedCount;
    }

    public void setClientRejectedCount(Integer clientRejectedCount) {
        this.clientRejectedCount = clientRejectedCount;
    }

    public String getClientRejectedActivityId() {
        return clientRejectedActivityId;
    }

    public void setClientRejectedActivityId(String clientRejectedActivityId) {
        this.clientRejectedActivityId = clientRejectedActivityId;
    }

    public Integer getShortlistedByClientCount() {
        return shortlistedByClientCount;
    }

    public void setShortlistedByClientCount(Integer shortlistedByClientCount) {
        this.shortlistedByClientCount = shortlistedByClientCount;
    }

    public String getShortlistedByClientActivityId() {
        return shortlistedByClientActivityId;
    }

    public void setShortlistedByClientActivityId(String shortlistedByClientActivityId) {
        this.shortlistedByClientActivityId = shortlistedByClientActivityId;
    }

    public Integer getCandidateQuitCount() {
        return candidateQuitCount;
    }

    public void setCandidateQuitCount(Integer candidateQuitCount) {
        this.candidateQuitCount = candidateQuitCount;
    }

    public String getCandidateQuitActivityId() {
        return candidateQuitActivityId;
    }

    public void setCandidateQuitActivityId(String candidateQuitActivityId) {
        this.candidateQuitActivityId = candidateQuitActivityId;
    }

    public static Comparator<SummaryReport> getSubmittedComparator() {
        return submittedComparator;
    }

    public static void setSubmittedComparator(Comparator<SummaryReport> submittedComparator) {
        SummaryReport.submittedComparator = submittedComparator;
    }

    public static Comparator<SummaryReport> submittedComparator =  (s1, s2) -> {
            Integer summaryReport1 = s1.getSubmittedCount();
            Integer summaryReport2 = s2.getSubmittedCount();
            return summaryReport2.compareTo(summaryReport1);
	};

    @Override
    public String toString() {
        return "SummaryReport{" +
                "id=" + id +
                ", batchId='" + batchId + '\'' +
                ", reportType=" + reportType +
                ", company='" + company + '\'' +
                ", countOfJob=" + countOfJob +
                ", jobId='" + jobId + '\'' +
                ", recruiterUserId=" + recruiterUserId +
                ", recruiter='" + recruiter + '\'' +
                ", hiringManagerId=" + hiringManagerId +
                ", hiringManager='" + hiringManager + '\'' +
                ", openingCount=" + openingCount +
                ", appliedCount=" + appliedCount +
                ", appliedActivityId='" + appliedActivityId + '\'' +
                ", submittedCount=" + submittedCount +
                ", submittedActivityId='" + submittedActivityId + '\'' +
                ", interviewCount=" + interviewCount +
                ", interviewActivityId='" + interviewActivityId + '\'' +
                ", offeredCount=" + offeredCount +
                ", offeredActivityId='" + offeredActivityId + '\'' +
                ", offerAcceptedCount=" + offerAcceptedCount +
                ", offerAcceptedActivityId='" + offerAcceptedActivityId + '\'' +
                ", startedCount=" + startedCount +
                ", startedActivityId='" + startedActivityId + '\'' +
                ", offerRejectedCount=" + offerRejectedCount +
                ", offerRejectedActivityId='" + offerRejectedActivityId + '\'' +
                ", clientRejectedCount=" + clientRejectedCount +
                ", clientRejectedActivityId='" + clientRejectedActivityId + '\'' +
                ", shortlistedByClientCount=" + shortlistedByClientCount +
                ", shortlistedByClientActivityId='" + shortlistedByClientActivityId + '\'' +
                ", candidateQuitCount=" + candidateQuitCount +
                ", candidateQuitActivityId='" + candidateQuitActivityId + '\'' +
                ", soPercent='" + soPercent + '\'' +
                ", hoPercent='" + hoPercent + '\'' +
                ", jobAsPrCount=" + jobAsPrCount +
                ", createdBy='" + createdBy + '\'' +
                ", createdDate=" + createdDate +
                '}';
    }
}
