package com.altomni.apn.common.dto.search;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class SearchRaterGroup {

    private static final long serialVersionUID = 1L;

    private List<JSONObject> filter;

    private String module;

    private String index;

    private LinkedList<Map<String, Object>> ids;

    private List<String> source;

}
