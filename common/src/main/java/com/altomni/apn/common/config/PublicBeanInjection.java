package com.altomni.apn.common.config;

import com.altomni.apn.common.aop.cache.CacheCleanAspect;
import com.altomni.apn.common.aop.cache.CacheControlAspect;
import com.altomni.apn.common.aop.user.SimpleUserAspect;
import com.altomni.apn.common.errors.FeignDecoder;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import feign.codec.ErrorDecoder;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;
import org.zalando.problem.ProblemModule;
import org.zalando.problem.violations.ConstraintViolationProblemModule;

@Configuration
public class PublicBeanInjection {

    /**
     * to remove "stack trace" from response content
     * @return
     */
    @Bean
    public Jackson2ObjectMapperBuilderCustomizer problemObjectMapperModules() {
        return jacksonObjectMapperBuilder -> jacksonObjectMapperBuilder.modules(
                new ProblemModule(),
                new ConstraintViolationProblemModule()
        );
    }

    @Bean
    @LoadBalanced
    public RestTemplate getRestTemplate(){
        return new RestTemplate();
    }

    @Bean
    public ObjectMapper objectMapper(){
        ObjectMapper o = new ObjectMapper();
        o.registerModule(new JavaTimeModule());
        o.registerModule(new ProblemModule());
        o.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        o.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return o;
    }

    @Bean
    public ErrorDecoder feignDecoder() {
        return new FeignDecoder();
    }

    @Bean
    public SimpleUserAspect simpleUserAspect() {
        return new SimpleUserAspect();
    }

    @Bean
    public CacheControlAspect cacheControlAspect() {
        return new CacheControlAspect();
    }

    @Bean
    public CacheCleanAspect cacheCleanAspect(){
        return new CacheCleanAspect();
    }
}
