package com.altomni.apn.common.aop.request;

import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * @author: Lei.<PERSON>
 * @date: 2019-01-17 11:56
 * @description:
 **/
@Aspect
@Component
@Slf4j
public class NoRepeatSubmitAspect {

//    public static final String APN_INTERNAL_PIN = "APN_INTERNAL_PIN";

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private ArgsSignGeneratorFactory signGeneratorFactory;

//    @Resource
//    private CommonApplicationProperties commonApplicationProperties;

    @PostConstruct
    public void init() {
        System.out.println("NoRepeatSubmitAspect run");
    }

    @Around("@annotation(noRepeatSubmit)")
    public Object arround(ProceedingJoinPoint pjp, NoRepeatSubmit noRepeatSubmit) throws Throwable {
        // 获取请求参数
        Object[] args = pjp.getArgs();
        List<Object> filteredArgs = filterArgs(args);
        // 生成参数的唯一标识
        // 获取请求路径
        String path = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getRequest().getServletPath();
        String method = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getRequest().getMethod();
        ArgsSignGenerator generator = signGeneratorFactory.getGenerator(method, path);
        String argsSign = generator.generateSign(filteredArgs.toArray());

//        // if the request is from internal service, skip checking
//        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
//        final String internalServicePin = request.getHeader(APN_INTERNAL_PIN);
//        if (StringUtils.hasText(internalServicePin) && StringUtils.hasText(commonApplicationProperties.getApnInternalPin()) && internalServicePin.equals(commonApplicationProperties.getApnInternalPin())) {
//            return pjp.proceed();
//        }


        Long userId = SecurityUtils.getUserId();
        //系统操作直接放行
        if(userId < 0L) {
            return pjp.proceed();
        }

        // 组合key: 请求路径 + 参数签名
        String key = "repeat:submit:" + userId + ":" + method + path + ":" + argsSign;

        // 尝试设置key，1秒过期
        Boolean success = redisTemplate.opsForValue()
                .setIfAbsent(key, key, noRepeatSubmit.expire(), TimeUnit.MILLISECONDS);

        if (Boolean.FALSE.equals(success)) {
            log.warn("重复请求，path: {}, args: {}", path, argsSign);
            return ResponseEntity
                    .status(HttpStatus.TOO_MANY_REQUESTS)
                    .body("Too many requests, please try again later");
        }

        try {
            return pjp.proceed();
        } catch (Exception e) {
            // 发生异常时删除key，允许重试
            redisTemplate.delete(key);
            throw e;
        }
    }

    private List<Object> filterArgs(Object[] args) {
        //过滤入参中的HttpHeader
        List<Object> filteredArgs = new ArrayList<>();

        for (Object arg : args) {
            if (!(arg instanceof HttpHeaders)) {
                filteredArgs.add(arg);
            }
        }
        return filteredArgs;
    }
}
