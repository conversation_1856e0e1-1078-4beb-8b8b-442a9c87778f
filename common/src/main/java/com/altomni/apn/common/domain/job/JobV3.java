package com.altomni.apn.common.domain.job;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.dict.JobJobFunctionRelation;
import com.altomni.apn.common.domain.dict.JobPreferredDegreeRelation;
import com.altomni.apn.common.domain.dict.JobPreferredLanguagesRelation;
import com.altomni.apn.common.domain.dict.JobRequiredLanguagesRelation;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.dto.job.AdditionalInfoDTO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.utils.InfoUtils;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.*;

/**
 * A Job.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "Job entity. It comes from parser parsing JD, or user manually create job using ATS.")
@Entity
@Table(name = "job")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobV3 extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "tenantId"));

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO, generator = "jobIdAuto")
    @GenericGenerator(name = "jobIdAuto", strategy = "com.altomni.apn.common.domain.job.ManulInsertGenerator")
    private Long id;

    @ApiModelProperty(value = "The tenant id the job belongs. It is injected from the user entity when creating job. Read only.")
    @JsonIgnore
    @Column(name = "tenant_id", nullable = false, updatable = false)
    private Long tenantId;

    @ApiModelProperty(value = "Name of the company publish the job", required = true)
    @Column(name = "company_id")
    private Long companyId;

    @ApiModelProperty(value = "The title for the position", required = true)
    @NotNull
    @Column(name = "title", nullable = false)
    private String title;

    @ApiModelProperty(value = "the recruitment process id for current job")
    @NotNull
    @Column(name = "recruitment_process_id")
    private Long recruitmentProcessId;

    @ApiModelProperty(value = "currency")
    @Column(name = "currency")
    private Integer currency;

    @ApiModelProperty(value = "This is job internal code company used to identify the job")
    @Column(name = "code")
    private String code;

    @ApiModelProperty(value = "The date when the job starts")
    @Column(name = "start_date")
    private Instant startDate;

    @ApiModelProperty(value = "The date when the job ends")
    @Column(name = "end_date")
    private Instant endDate;

    @ApiModelProperty(value = "The date the job is posted to ATS or job boards. Read Only.")
    @Column(name = "posting_time")
    private Instant postingTime;

    @ApiModelProperty(value = "The last time the job was opened.")
    @Column(name = "open_time")
    private Instant openTime;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "Open, OnHold, Cancelled, Closed")
    @Convert(converter = JobStatusConverter.class)
    @Column(name = "status", nullable = false)
    private JobStatus status;

    @ApiModelProperty(value = "The url link to the JD. E.g. link to StoreService storage for the JD")
    @Column(name = "jd_url")
    private String jdUrl;

    @ApiModelProperty(value = "has parsed jd display link or not")
    @Column(name = "jd_has_display")
    private Boolean jdHasDisplay;

    @ApiModelProperty(value = "one or more required languages")
    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "job_id")
    private Set<JobRequiredLanguagesRelation> requiredLanguages;

    @ApiModelProperty(value = "one or more preferred languages")
    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "job_id")
    private Set<JobPreferredLanguagesRelation> preferredLanguages;

    @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "additional_info_id")
    private JobAdditionalInfo jobAdditionalInfo;

    @ApiModelProperty(value = "number of openings for the job. Default is 1.")
    @Column(name = "openings")
    private Integer openings = 1;

    @ApiModelProperty(value = "max application submissions")
    @Column(name = "max_submissions")
    private Integer maxSubmissions;

    @ApiModelProperty(value = "last edited time of job, userJobRelation, userFavoriteJob and companyContact")
    @Column(name = "last_edited_time")
    private Instant lastEditedTime;

    @ApiModelProperty(value = "last successful sync to ES")
    @Column(name = "last_sync_time")
    private Instant lastSyncTime;

    @ApiModelProperty(value = "weather sync to es has been paused")
    @Column(name = "sync_paused")
    private Boolean syncPaused = Boolean.FALSE;

    @ApiModelProperty(value = "one or more job functions")
    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "job_id")
    private Set<JobJobFunctionRelation> jobFunctions;

    @ApiModelProperty(value = "one or more preferred degree")
    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "job_id")
    private Set<JobPreferredDegreeRelation> preferredDegrees;

    @ApiModelProperty(value = "minimum degree id of job")
    @Column(name = "minimum_degree_id")
    private Long minimumDegreeId;

    @ApiModelProperty(value = "enum value of priority id")
    @Column(name = "enum_priority_id")
    private Long enumPriorityId;

    @ApiModelProperty(value = "if or not provides a flexible location")
    @Column(name = "flexible_location")
    private Boolean flexibleLocation;

    @ApiModelProperty(value = "Last time updated to a non-OPEN status")
    @Column(name = "last_non_open_time")
    private Instant lastNonOpenTime;

    @Column(name = "sales_lead_id")
    private Long salesLeadId;

    @Column(name = "cooperation_status")
    private String cooperationStatus;

    @Column(name = "is_need_sync_hr")
    private Boolean isNeedSyncHr;

    @ApiModelProperty(value = "Contract Duration")
    @Column(name = "contract_duration")
    private Integer contractDuration;

    @ApiModelProperty(value = "if or not this job is private")
    @Transient
    @JsonIgnore
    private Boolean isPrivateJob;

    @ApiModelProperty(value = "if this job is private, these users have permission to access this job")
    @Transient
    @JsonIgnore
    private Set<Long> authorizedUsersForPrivateJob;

    public String getJobExtendedInfo() {
        String extendedInfo = null;
        if (jobAdditionalInfo != null) {
            extendedInfo = jobAdditionalInfo.getExtendedInfo();
        }
        return extendedInfo == null ? "{}" : extendedInfo;
    }

    public void setJobExtendedInfo(String extendedInfo) {
        if (jobAdditionalInfo == null) {
            jobAdditionalInfo = new JobAdditionalInfo();
        }
        jobAdditionalInfo.setExtendedInfo(StringUtils.firstNonBlank(extendedInfo, "{}"));
    }

    public String getJobLocalExtendedInfo() {
        String extendedInfo = null;
        if (jobAdditionalInfo != null) {
            extendedInfo = jobAdditionalInfo.getLocalExtendedInfo();
        }
        return extendedInfo == null ? "{}" : extendedInfo;
    }

    public void setJobLocalExtendedInfo(String extendedInfo) {
        if (StrUtil.isNotEmpty(extendedInfo)) {
            if (jobAdditionalInfo == null) {
                jobAdditionalInfo = new JobAdditionalInfo();
            }
            jobAdditionalInfo.setLocalExtendedInfo(extendedInfo);
        }
    }

    public void setJD(String responsibilities, String summary, String requirements) {
        if (!StrUtil.isAllEmpty(responsibilities, summary, requirements)) {
            if (jobAdditionalInfo == null) {
                jobAdditionalInfo = new JobAdditionalInfo();
            }
            jobAdditionalInfo.setResponsibilities(responsibilities);
            jobAdditionalInfo.setSummary(summary);
            jobAdditionalInfo.setRequirements(requirements);
        }
    }

    public void setAdditionalInfoId(Long id) {
        if (id != null) {
            if (jobAdditionalInfo == null) {
                jobAdditionalInfo = new JobAdditionalInfo();
            }
            jobAdditionalInfo.setId(id);
        }
    }

    public Long getAdditionalInfoId() {
        if (jobAdditionalInfo == null) {
            return null;
        }
        return jobAdditionalInfo.getId();
    }

    public static JobV3 fromJobDTO(JobDTOV3 dto) {
        JobV3 job = cn.hutool.core.convert.Convert.convert(JobV3.class, dto);
        job.setAdditionalInfoId(dto.getAdditionalInfoId());
        if (ObjectUtil.isNotEmpty(dto.getMinimumDegreeLevel()) && ObjectUtil.isNotEmpty(dto.getMinimumDegreeLevel().getEnumId())) {
            job.setMinimumDegreeId(Long.parseLong(dto.getMinimumDegreeLevel().getEnumId()));
        }
        if(ObjectUtil.isNotEmpty(dto.getPriority()) && ObjectUtil.isNotEmpty(dto.getPriority().getEnumId())){
            job.setEnumPriorityId(Long.parseLong(dto.getPriority().getEnumId()));
        }
        if(ObjectUtil.isNotEmpty(dto.getCurrency()) && ObjectUtil.isNotEmpty(dto.getCurrency().getEnumId())){
            job.setCurrency(Integer.parseInt(dto.getCurrency().getEnumId()));
        }
        //String extendedInfo = generateExtendedInfo(dto);
        String extendedInfo =  dto.getExtendedInfo() != null ? dto.getExtendedInfo() : generateExtendedInfoV3(dto);

        job.setJobExtendedInfo(extendedInfo);
//        job.setJobLocalExtendedInfo(generateLocalExtendedInfo(dto));
        return job;
    }

    public static String generateExtendedInfoV3(JobDTOV3 dto) {
        AdditionalInfoDTO nonRelationData = AdditionalInfoDTO.getNonRelationData(dto);
        // Not need put into extension
//        Map<String, Object> locationsMap = new HashMap<String, Object>() {{
//            put("locations", dto.getLocations());
//        }};
        Map<String, Object> nonRelationDataMap = new ObjectMapper().convertValue(nonRelationData, Map.class);
        //nonRelationDataMap.putAll(locationsMap);
        String extendedInfo = JSONObject.toJSONString(nonRelationDataMap, (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1));
        return extendedInfo;

    }

    public static void mergeExtendedInfo(JobV3 source, JobV3 target) {
        String extendedInfo = mergeExtendedInfo(source.getJobExtendedInfo(), target.getJobExtendedInfo());
        source.setJobExtendedInfo(extendedInfo);
        target.setJobExtendedInfo(extendedInfo);
    }

    public static String mergeExtendedInfo(String source, String target) {
        JSONObject source1 = JSONObject.parseObject(source);
        InfoUtils.replenishNullField(source1, JobDTOV3.class);
        Object merge = InfoUtils.merge(source1, JSONObject.parseObject(target));
        return JSON.toJSONString(merge, (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JobV3 jobV3 = (JobV3) o;
        if (jobV3.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), jobV3.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

}
