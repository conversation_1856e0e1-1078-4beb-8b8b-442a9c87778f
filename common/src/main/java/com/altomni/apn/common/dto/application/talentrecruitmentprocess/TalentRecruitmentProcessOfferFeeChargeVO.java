package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.application.FeeType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOfferFeeChargeVO implements Serializable {

    private static final long serialVersionUID = 5130333806617438622L;

    private Long id;

    private Long talentRecruitmentProcessId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillableAmount;

    private FeeType feeType;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal feeAmount;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalAmount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentRecruitmentProcessId() {
        return talentRecruitmentProcessId;
    }

    public void setTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
    }

    public BigDecimal getTotalBillableAmount() {
        return totalBillableAmount == null ? null : new BigDecimal(Constants.DECIMAL_FORMAT.format(totalBillableAmount));
    }

    public void setTotalBillableAmount(BigDecimal totalBillableAmount) {
        this.totalBillableAmount = totalBillableAmount;
    }

    public FeeType getFeeType() {
        return feeType;
    }

    public void setFeeType(FeeType feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getFeeAmount() {
        return feeAmount == null ? null : new BigDecimal(Constants.DECIMAL_FORMAT.format(feeAmount));
    }

    public void setFeeAmount(BigDecimal feeAmount) {
        this.feeAmount = feeAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount == null ? null : new BigDecimal(Constants.DECIMAL_FORMAT.format(totalAmount));
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Override
    public String toString() {
        return "TalentRecruitmentProcessOfferFeeChargeVO{" +
                "id=" + id +
                ", talentRecruitmentProcessId=" + talentRecruitmentProcessId +
                ", totalBillableAmount=" + totalBillableAmount +
                ", feeType=" + feeType +
                ", feeAmount=" + feeAmount +
                ", totalAmount=" + totalAmount +
                '}';
    }
}
