package com.altomni.apn.common.utils;

import com.google.common.hash.Hashing;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil {

    public static String valueOf(BigDecimal value) {
        return value != null ? String.valueOf(value) : null;
    }

    public static String valueOf(Integer value) {
        return value != null ? String.valueOf(value) : null;
    }

    public static String valueOf(Object value) {
        return value != null ? String.valueOf(value) : null;
    }
    public static boolean isContainChinese(String str)
    {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        if (m.find()) {
                 return true;
            }
         return false;
     }

     public static String getHashString(String str) {
         long hash = Hashing.murmur3_128().hashBytes(str.getBytes()).asLong();
         String hashedString = Long.toHexString(hash);
         return hashedString;
     }
}
