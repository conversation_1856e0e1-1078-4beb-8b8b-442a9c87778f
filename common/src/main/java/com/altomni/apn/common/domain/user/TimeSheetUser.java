package com.altomni.apn.common.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserTypeConverter;
import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.common.dto.CredentialDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ipg.resourceserver.user.SsoOidcUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.BatchSize;
import org.hibernate.validator.constraints.Email;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * A user.
 */
@ApiModel(description = "User for timeSheet")
@Entity
@Data
@Table(name = "time_sheet_user")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TimeSheetUser extends AbstractAuditingEntity implements UserSecurityInterface, Serializable, SsoOidcUser, UserDetails {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @ApiModelProperty(value = "[Internal] uid is used for Spring security to identify user internally. It has the format of <userId>,<tenantId>")
    @Column(name = "uid")
    private String uid;

    @ApiModelProperty(value = "Optional username. If exists, need to be unique.")
    @Size(min = 4, max = 50)
    @Column(length = 50, unique = true, name = "user_name")
    private String username;


    @ApiModelProperty(value = "Optional username. If exists, need to be unique.")
    @JsonIgnore
    @NotNull
    @Size(min = 6, max = 80)
    @Column(length = 80, unique = true, name = "password")
    private String password;


    @ApiModelProperty(value = "email address. need to be unique.")
    @Email
    @Size(min = 5, max = 100)
    @Column(length = 100, unique = true, name = "email")
    private String email;


    @ApiModelProperty(value = "password is changed")
    @Column(name = "is_pass_changed")
    private boolean isPassChanged;

    @ApiModelProperty(value = "user type ")
    @Column(name = "user_type")
    @Convert(converter = TimeSheetUserTypeConverter.class)
    private TimeSheetUserType userType;

    @ApiModelProperty(value = "time sheet type ")
    @Column(name = "time_sheet_type")
    @Convert(converter = TimeSheetTypeConverter.class)
    private TimeSheetType timeSheetType;

    @ApiModelProperty(value = "Whether user is activated. Default is true. Read Only.")
    @NotNull
    @Column(nullable = false)
    private boolean activated = true;

    @ApiModelProperty(value = "credentials returned when user login")
    @Transient
    @JsonProperty
    public CredentialDTO credential;

    @ApiModelProperty(value = "url link to photo")
    @Transient
    @JsonProperty
    private String photoUrl;

    @ApiModelProperty(value = "candidate documents completion status")
    @Transient
    @JsonProperty
    private CompletionStatus completionStatus;

    @ApiModelProperty(value = "permission of accessing to timesheet")
    @Transient
    @JsonProperty
    private Boolean allowSubmitTimeSheet = false;

    @ApiModelProperty(value = "permission of accessing to expense")
    @Transient
    @JsonProperty
    private Boolean allowSubmitExpense = false;

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinTable(
            name = "timesheet_user_role",
            joinColumns = {@JoinColumn(name = "user_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "role_id", referencedColumnName = "id")})
    @BatchSize(size = 20)
    private Set<Role> roles = new HashSet<>();


    @ApiModelProperty(value = "first name")
    @Size(max = 50)
    @Column(name = "first_name", length = 50)
    private String firstName;

    @ApiModelProperty(value = "last name")
    @Size(max = 50)
    @Column(name = "last_name", length = 50)
    private String lastName;

    @ApiModelProperty(value = "primary team ID")
    @Transient
    private Long teamId;

    @Transient
    private OidcUser oidcUser;

    @JsonIgnore
    public String getLogin() {
        if (this.username != null) {
            return username;
        }
        return this.email;
    }

    @Override
    @JsonIgnore
    public Optional<OidcUser> getOriginalOidcUser() {
        return Optional.ofNullable(oidcUser);
    }

    @Override
    @JsonIgnore
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (roles == null) {
            return Collections.emptyList();
        }
        return roles.stream().map(role -> new SimpleGrantedAuthority(role.getName())).collect(Collectors.toSet());
    }

    @Override
    @JsonIgnore
    public boolean isAccountNonExpired() {
        return activated;
    }

    @Override
    @JsonIgnore
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    @JsonIgnore
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    @JsonIgnore
    public boolean isEnabled() {
        return activated;
    }
}
