package com.altomni.apn.common.config;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class ExternalServiceRestTemplateConfig {

    @Bean
    @Qualifier("externalServiceRestTemplate")
    public RestTemplate externalServiceRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();

        // 设置合理的超时
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(5000);
        // 请求超时
        factory.setConnectionRequestTimeout(5000);
        factory.setReadTimeout(120000);
        restTemplate.setRequestFactory(factory);
        return restTemplate;
    }
}