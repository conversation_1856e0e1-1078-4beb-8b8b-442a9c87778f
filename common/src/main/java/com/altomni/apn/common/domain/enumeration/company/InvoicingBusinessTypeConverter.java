package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoicingBusinessTypeConverter extends AbstractAttributeConverter<InvoicingBusinessType, Integer> {
    public InvoicingBusinessTypeConverter() {
        super(InvoicingBusinessType::toDbValue, InvoicingBusinessType::fromDbValue);
    }
}
