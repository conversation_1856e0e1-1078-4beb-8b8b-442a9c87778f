package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum AssignmentType implements ConvertedEnum<Integer>
{
    ASSIGNMENT(0),
    EXTENSION(1),
    RATE_CHANGE(2);

    private final int dbValue;

    AssignmentType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<AssignmentType, Integer> resolver = new ReverseEnumResolver<>(AssignmentType.class, AssignmentType::toDbValue);

    public static AssignmentType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
