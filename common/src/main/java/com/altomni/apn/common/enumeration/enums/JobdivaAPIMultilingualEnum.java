package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息
 * <AUTHOR>
 */
public enum JobdivaAPIMultilingualEnum {

    JOBDIVA_INVOICE_CREATE( "invocie_create"),
    J<PERSON>B<PERSON><PERSON>_INVOICE_CREATE_SEQUECE("invoice_create_sequece"),

    JOBDIVA_INVOICE_UPDATE_VOID("invoice_update_void"),

    JOBDIVA_INVOICE_NOT_FIND("invoice_not_find"),

    JOB<PERSON>VA_INVOICE_VOID("invoice_void"),

    JOBDIVA_INVOICE_UNGROUP("invoice_ungroup"),

    JOB<PERSON>VA_INVOICE_UNGROUP_SEND_CLIENT("invoice_ungroup_send_client"),

    INVOICE_GROUP_NOT_MODIFY("invoice_group_not_modify"),

    JOBDIVA_INVOICE_GROUP_CREATE("invoice_group_create"),

    INVOICE_GROUP_ADDRECORDPAYMENT_STATUSISPAID("invoice_group_addRecordPayment_statusIsPaid"),

    INVOICE_GROUP_ADDRECORDPAYMENT_STATUSISVOIDORINVOICED("invoice_group_addRecordPayment_statusIsVoidOrInvoiced"),

    INVOICE_GROUP_ADDRECORDPAYMENT_AMOUNTTHENDUE("invoice_group_addRecordPayment_amountThenDue"),

    INVOICE_GROUP_BULKRECORDPAYMENT_STATUSERROR("invoice_group_bulkRecordPayment_statusError"),

    JOBDIVA_INVOICE_GROUP_CREATE_CURRENCY("invoice_group_create_currency"),

    INVOICE_GROUP_CREATE_CHECK_FREQUENCY("invoice_group_create_check_frequency"),

    INVOICE_GROUP_CREATE_CHECK_FREQUENCY_ISNULL("invoice_group_create_check_frequency_isnull"),

    JOBDIVA_INVOICE_GROUP_UNGROUP("invoice_group_ungroup"),

    JOBDIVA_INVOICE_GROUP_RECORD("invoice_group_record"),

    JOBDIVA_INVOICE_GROUP_VOID("invoice_group_void"),

    JOBDIVA_INVOICE_GROUP_ATTACHMENT("invoice_group_attachment"),

    INVOICE_GROUP_ATTACHMENT_INVALID("invoice_group_attachment_invalid"),

    JOBDIVA_INVOICE_GROUP_ATTACHMENT_COMPANY("invoice_group_attachment_company"),

    S3_DOCUMENT_INVALID("invoice_group_s3_document_invalid"),

    ASSIGNMENT_DETAIL_UPDATE_ID_NULL("assignment_detail_update_id_null"),

    ASSIGNMENT_DETAIL_UPDATE_ID_NOT_CORRECT("assignment_detail_update_id_not_correct"),

    ASSIGNMENT_FIND_DETAIL_BY_START_ID("assignment_find_detail_by_start_id"),

    ASSIGNMENT_DELETE_STARTID_IS_NULL("assignment_delete_startId_is_null"),

    ASSIGNMENT_IS_NOT_EXIST("assignment_is_not_exist"),

    ASSIGNMENT_DELETE("assignment_delete"),

    ASSIGNMENT_SAVA_PARAM_CHECK("assignment_sava_param_check"),

    ASSIGNMENT_SAVA_START_DATE("assignment_sava_start_date"),

    ASSIGNMENT_SAVA_BILL_INFO("assignment_sava_bill_info"),

    ASSIGNMENT_SAVA_PAY_INFO("assignment_sava_pay_info"),

    ASSIGNMENT_SAVA_TIMESHEET_STATE("assignment_sava_timesheet_state"),

    ASSIGNMENT_SAVA_TIMESHEET_CALCULATE("assignment_sava_timesheet_calculate"),

    ASSIGNMENT_SAVA_CHECK_RATE("assignment_sava_check_rate"),

    ASSIGNMENT_SAVA_PAYRATE_DOUBLETIME_RATE_NULL("assignment_sava_payrate_doubletime_rate_null"),

    ASSIGNMENT_SAVA_PAYRATE_DOUBLERATE("assignment_sava_payrate_doublerate"),

    ASSIGNMENT_SAVA_PAYRATE_NULL("assignment_sava_payrate_null"),

    ASSIGNMENT_SAVA_PAYRATE_OVERTIME_RATE("assignment_sava_payrate_overtime_rate"),

    ASSIGNMENT_SAVA_PAYRATE_BILL_RATE("assignment_sava_payrate_bill_rate"),

    ASSIGNMENT_UPDATE_ALREADY_STARTED("assignment_update_already_started"),


    ASSIGNMENT_TIMESHEETTYPE_NOT_UPDATE("assignment_timesheettype_not_update"),
    ASSIGNMENT_FREQUENCY_NOT_UPDATE("assignment_Frequency_not_update"),
    ASSIGNMENT_WEEKENDING_NOT_UPDATE("assignment_weekending_not_update"),
    ASSIGNMENT_PERMISSION_NOT_UPDATE("assignment_permission_not_update"),
    ASSIGNMENT_LOCATION_NOT_UPDATE("assignment_location_not_update"),
    ASSIGNMENT_PAYISEXCEPT_NOT_UPDATE("assignment_payisexcept_not_update"),
    ASSIGNMENT_BILLISEXCEPT_NOT_UPDATE("assignment_billisexcept_not_update"),


    ASSIGNMENT_SAVE_UPDATE_RECRUITMENT_PROCESS_INACTIVE("assignment_save_update_recruitment_process_inactive"),

    ONBOARDING_SAVE_DRAFT("onboarding_save_draft"),

    ONBOARDING_SAVE_DRAFT_DISABLED("onboarding_save_draft_disabled"),

    ONBOARDING_NOT_FIND_DOCUMENT_BY_PACKAGE("onboarding_not_find_document_by_package"),

    ONBOARDING_FIND_DOCUMENT_NOT_PERMISSION("onboarding_find_document_not_permission"),

    ONBOARDING_TALENT_CONTACT_EMAIL("onboarding_talent_contact_email"),

    ONBOARDING_SAVE_APPROVAL_HISTORY("onboarding_save_approval_history"),

    ONBOARDING_SAVE_APPROVAL_APPLICATION("onboarding_save_approval_application"),

    ONBOARDING_SAVE_APPROVAL_DELETE_STATUS("onboarding_save_approval_delete_status"),

    ONBOARDING_SAVE_APPROVAL_REJECT_STATUS("onboarding_save_approval_reject_status"),

    ONBOARDING_SAVE_APPROVAL_PENDING_STATUS("onboarding_save_approval_pending_status"),

    ONBOARDING_SAVE_APPROVAL_NOTE_REJECT_STATUS("onboarding_save_approval_note_reject_status"),

    ONBOARDING_DOWNLOAD_FILE_TALENT("onboarding_download_file_talent"),

    ONBOARDING_PORTALS_APPLICATION("onboarding_portals_application"),

    ONBOARDING_DOWNLOAD_COMPLETION_DOCUMENTS("onboarding_download_completion_documents"),

    ONBOARDING_OPERATE_STATUS_COMPLETE_AND_SIGNED("onboarding_operate_status_complete_and_signed"),

    ONBOARDING_OPERATE_STATUS_UPLOAD("onboarding_operate_status_upload"),

    ONBOARDING_OPERATE_STATUS_SIGNATURE("onboarding_operate_status_signature"),

    ONBOARDING_OPERATE_STATUS_DELETE("onboarding_operate_status_delete"),

    ONBOARDING_OPERATE_STATUS_REJECT("onboarding_operate_status_reject"),

    ONBOARDING_OPERATE_STATUS_PENDING("onboarding_operate_status_pending"),

    ONBOARDING_OPERATE_STATUS_MISS("onboarding_operate_status_miss"),

    ONBOARDING_OPERATE_STATUS_S3LINK_INVALID("onboarding_operate_status_s3link_invalid"),

    ONBOARDING_OPERATE_STATUS_CREATE_S3_ERROR("onboarding_operate_status_create_s3_error"),

    ONBOARDING_ADD_SIGNATURE_INCORRECT("onboarding_add_signature_incorrect"),

    ONBOARDING_SEND_PORTAL_EMAIL_ISNULL("onboarding_send_portal_email_isnull"),

    ONBOARDING_COMMON_TALENT_ISNULL("onboarding_send_portal_email_talent_isnull"),

    ONBOARDING_RESET_PORTAL_ACCOUNT_PW("onboarding_reset_portal_account_pw"),

    ONBOARDING_RESET_PORTAL_ACCOUNT_NOT_FOUND_ACCOUNT("onboarding_reset_portal_account_not_found_account"),

    ONBOARDING_PREVIEW_DOCUMENT_ISNULL("onboarding_preview_document_isnull"),

    ONBOARDING_PREVIEW_DOCUMENT_APPLICATION("onboarding_preview_document_application"),

    ONBOARDING_PREVIEW_DOCUMENT_FILE_ERROR("onboarding_preview_document_file_error"),

    ONBOARDING_AM_EMAIL_ISNULL("onboarding_am_email_isnull"),

    ONBOARDING_PORTAL_EMAIL_ISNULL("onboarding_portal_email_isnull"),

    ONBOARDING_CREATE_JOBDIVA_ACCOUNT("onboarding_create_jobdiva_account"),

    ONBOARDING_START_EMAIL_INFO_DOCUMENTS("onboarding_start_email_info_documents"),

    ONBOARDING_NO_PERMISSION("onboarding_no_permission"),

    ONBOARDING_GET_DOCUMENTS_ID("onboarding_get_documents_id"),

    ONBOARDING_UPDATE_DOCUMENTS_ID_DUPLICATE("onboarding_update_documents_id_duplicate"),

    ONBOARDING_SAVE_DOCUMENTS_ID_ISNULL("onboarding_save_documents_id_isnull"),

    ONBOARDING_DELETE_PACKAGE_NOT_FOUND("onboarding_delete_package_not_found"),

    ONBOARDING_UPDATE_PACKAGE_NAME_DUPLICATE("onboarding_update_package_name_duplicate"),

    ONBOARDING_SAVE_PACKAGE_ID_ISNULL("onboarding_save_package_id_isnull"),

    ONBOARDING_SAVE_SIGNATURE_TEXT_ISNULL("onboarding_save_signature_text_isnull"),

    ADVANCE_COMPOSE_SQL_COLUMN_ERROR("advance_compose_sql_column_error"),

    ADVANCE_COMPOSE_SQL_DATA_ERROR("advance_compose_sql_data_error"),

    TIMESHEET_APPROVE_RECORD_ISNULL("timesheet_approve_record_isnull"),

    EXPENSE_RECORD_ISNULL("expense_record_isnull"),

    EXPENSE_UPDATE_RECORD_ID_ILLEGAL("expense_update_record_id_illegal"),

    TIMESHEET_DOWNLOAD_FAILED("timesheet_download_failed"),

    TIMESHEET_RECORD_ID_ISNULL("timesheet_record_id_isnull"),

    TIMESHEET_SAVE_FILTER_TYPE_ERROR("timesheet_save_filter_type_error"),

    TIMESHEET_DELETE_FILTER_ERROR("timesheet_delete_filter_error"),

    TIMESHEET_COMMENT_NOT_FIND_ASSIGNMENTID("timesheet_comment_not_find_assignmentId"),

    TIMESHEET_RECORD_ASSIGNMENTID_NOT_FIND("timesheet_record_assignmentId_not_find"),

    TIMESHEET_DATA_RECORD_ISNULL("timesheet_data_record_isnull"),

    TIMESHEET_DATA_NO_HOUR_DATE_ISNULL("timesheet_data_no_hour_date_isnull"),

    TIMESHEET_LOGIN_USER_ISNULL("timesheet_login_user_isnull"),

    TIMESHEET_LOGIN_USER_NOT_ACTIVE("timesheet_login_user_not_active"),

    TIMESHEET_LOGIN_USER_LOCK("timesheet_login_user_lock"),

    TIMESHEET_RESET_PASSWORD_USER_ISNULL("timesheet_reset_password_user_isnull"),

    TIMESHEET_RESET_PASSWORD_NOT_MATCH("timesheet_reset_password_not_match"),

    TIMESHEET_FORGET_PW_INVALID_EMAIL("timesheet_forget_pw_invalid_email"),

    TIMESHEET_FORGET_PW_ERROR("timesheet_forget_pw_error"),

    TIMESHEET_RESET_FORGET_PW_CODE_ISNULL("timesheet_reset_forget_pw_code_isnull"),

    TIMESHEET_RESET_FORGET_PW_INVALID_CODE("timesheet_reset_forget_pw_invalid_code"),

    TIMESHEET_RESET_FORGET_PW_VALIDATE_CODE("timesheet_reset_forget_pw_validate_code"),


    ASSIGNMENT_DATE_CHECK_CODE("assignment_date_check_code"),

    ASSIGNMENT_DATE_CHECK_INCLUDE_INVOICE("assignment_date_check_include_invoice"),

    INVOICE_GROUP_PURCHASE_ORDER_INVOICE_MULTIPLE_CURRENCIES("invoice_group_purchase_order_invoice_multiple_currencies"),
    INVOICE_GROUP_PURCHASE_ORDER_MULTIPLE_CURRENCIES("invoice_group_purchase_order_multiple_currencies"),

    INVOICE_GROUP_PURCHASE_ORDER_CONTAIN_DIFFERENT_CURRENCIES("invoice_group_purchase_order_contain_different_currencies"),

    INVOICE_GROUP_PURCHASE_ORDER_CONTAIN_DIFFERENT_COMPANY("invoice_group_purchase_order_contain_different_company"),

    MISSING_INPUT("missing_input"),
    TIMESHEET_GOOGLE_HOLIDAY_DATE_EXISTS("timesheet_google_holiday_date_exists"),
    NOT_PERMISSION("not_permission"),

    PARAM_INPUT_ERROR("param_input_error"),
    ;


    private final String key;

    JobdivaAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}