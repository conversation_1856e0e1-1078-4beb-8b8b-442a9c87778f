package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class AssignmentDivisionConverter extends AbstractAttributeConverter<AssignmentDivision, Integer> {
    public AssignmentDivisionConverter() {
        super(AssignmentDivision::toDbValue, AssignmentDivision::fromDbValue);
    }
}
