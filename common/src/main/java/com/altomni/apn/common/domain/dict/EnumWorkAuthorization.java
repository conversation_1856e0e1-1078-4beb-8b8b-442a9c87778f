package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ApiModel(description = "Enum industry entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_work_authorization")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumWorkAuthorization implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "parent_category")
    private String parentCategory;

    @Column(name = "en_display_order")
    private Long enDisplayOrder;

    @Column(name = "cn_display_order")
    private Long cnDisplayOrder;

    @Transient
    private Boolean cnLable = false;

    public static Set<EnumWorkAuthorization> transfer(List<Long> body) {
        if (body == null) {
            return null;
        } else {
            return body.stream().map(dictCode -> {
                EnumWorkAuthorization eumWorkAuthorization = new EnumWorkAuthorization();
                eumWorkAuthorization.setId(dictCode);
                return eumWorkAuthorization;
            }).collect(Collectors.toSet());
        }
    }

    public static List<Long> convert(Set<EnumWorkAuthorization> body) {
        if (body == null) {
            return null;
        } else {
            return body.stream().map(EnumWorkAuthorization::getId
            ).collect(Collectors.toList());
        }
    }

}
