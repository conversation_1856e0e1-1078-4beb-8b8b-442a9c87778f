package com.altomni.apn.common.utils;

import com.altomni.apn.common.domain.dict.EnumCountry;
import com.altomni.apn.common.dto.management.TenantPhoneDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

public class PhoneNumberUtils {

    /**
     * 将区号和电话号码组合成完整的电话号码，并使用国际格式化
     * @return 格式化后的电话号码，如果无法解析，则返回null
     */
    public static String formatPhoneNumber(TenantPhoneDTO tenantPhoneVM) {
        if (tenantPhoneVM.getCountryCode() == null || tenantPhoneVM.getPhone() == null) {
            return null;
        }
        if (!isValidNumber(tenantPhoneVM)) {
            throw new CustomParameterizedException("please check phone format.");
        }
        try {
            PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
            Phonenumber.PhoneNumber number = new Phonenumber.PhoneNumber();
            number.setCountryCode(tenantPhoneVM.getCountryCode());
            number.setNationalNumber(tenantPhoneVM.getPhone());
            return phoneNumberUtil.format(number, PhoneNumberUtil.PhoneNumberFormat.E164);
        } catch (Exception e) {
            return null;
        }
    }

    public static boolean isValidNumber(TenantPhoneDTO tenantPhoneVM) {
        try {
            PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
            Phonenumber.PhoneNumber number = new Phonenumber.PhoneNumber();
            number.setCountryCode(tenantPhoneVM.getCountryCode());
            number.setNationalNumber(tenantPhoneVM.getPhone());
            return phoneNumberUtil.isValidNumber(number);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 将电话号码解析成区号和number
     * @return 区号和number，如果无法解析，则返回null
     */
    public static TenantPhoneDTO parsePhoneNumber(String phoneNumber) {
        try {
            TenantPhoneDTO tenantPhoneVM = new TenantPhoneDTO();
            PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
            Phonenumber.PhoneNumber parsedNumber = phoneNumberUtil.parse(phoneNumber, "");
            String nationalNumber = phoneNumberUtil.format(parsedNumber, PhoneNumberUtil.PhoneNumberFormat.NATIONAL);
            tenantPhoneVM.setCountryCode(parsedNumber.getCountryCode());
            tenantPhoneVM.setPhone(parsedNumber.getNationalNumber());
            tenantPhoneVM.setNationalPhone(nationalNumber.replace(" ", "-"));
            return tenantPhoneVM;
        } catch (Exception e) {
            return new TenantPhoneDTO();
        }
    }

    /**
     * 查询常用的国家/地区区号
     * @return 地区区号的enum list
     */
    public static List<EnumCountry> searchPhoneAreaCode() {
        List<EnumCountry> areaCodeList = new ArrayList<>();
        PhoneNumberUtil phoneUtil = PhoneNumberUtil.getInstance();
        for (String regionCode : phoneUtil.getSupportedRegions()) {
            int areaCode = phoneUtil.getCountryCodeForRegion(regionCode);
            String regionNameEn = new Locale("", regionCode).getDisplayCountry(Locale.ENGLISH);
            String regionNameCn = new Locale("", regionCode).getDisplayCountry(Locale.CHINA);

            areaCodeList.add(new EnumCountry(regionCode, regionNameCn, regionNameEn, areaCode));
        }
        return areaCodeList;
    }

}
