package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * group invoice polymerization type enum
 */
public enum PaymentMethodType implements ConvertedEnum<Integer> {

    WIRE_TRANSFER(1,"Wire Transfer"),
    CREDIT_CARD(2,"Credit Card"),
    DEBIT_CARD(3,"Debit Card"),
    CHECK(4,"Check"),
    CASH(5,"Cash"),
    PURCHASE_ORDER(6,"Purchase Order");
    private final int dbValue;

    private final String description;

    PaymentMethodType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<PaymentMethodType, Integer> resolver = new ReverseEnumResolver<>(PaymentMethodType.class, PaymentMethodType::toDbValue);

    public static PaymentMethodType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
