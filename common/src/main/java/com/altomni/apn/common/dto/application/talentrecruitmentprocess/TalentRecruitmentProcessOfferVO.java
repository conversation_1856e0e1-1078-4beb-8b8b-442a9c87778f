package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOfferVO extends AuditingUser implements Serializable, FillSalaryPackages, FillFeeCharge {

    private static final long serialVersionUID = 6411433556179237166L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private LocalDate signedDate;

    private LocalDate estimateOnboardDate;

    private Integer currency;

    private RateUnitType rateUnitType;

    private String note;

    private Instant noteLastModifiedDate;

    private Long noteLastModifiedByUserId;

    private List<TalentRecruitmentProcessOfferSalaryPackageVO> salaryPackages;

    private TalentRecruitmentProcessOfferFeeChargeVO feeCharge;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

    private TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate;


}
