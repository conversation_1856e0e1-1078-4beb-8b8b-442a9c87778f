package com.altomni.apn.common.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;
import com.fasterxml.jackson.annotation.JsonValue;

public enum FolderPermission {
    // int represent three-bit binary, rwx
    NOPERMISSION(0),//000
    WRITE(2),// 010

    READONLY(4), //100

    READWRITE(6); // read and write permission 110


    private final int dbValue;

    FolderPermission(int dbValue) {
        this.dbValue = dbValue;
    }

    public Integer toDbValue() {
        return dbValue;
    }


    public static final ReverseEnumResolver<FolderPermission, Integer> resolver =
            new ReverseEnumResolver<>(FolderPermission.class, FolderPermission::toDbValue);

    public static FolderPermission fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @JsonValue
    public String getValue() {
        return name();
    }

    public static FolderPermission max(FolderPermission p1, FolderPermission p2) {
        return (p1.dbValue > p2.dbValue) ? p1 : p2;
    }

    /**
     * combine two permission by OR, if read and write, it will be readwrite permission.
     *
     * @param p1 one folder permission
     * @param p2 another folder permission
     * @return combined the folder permission
     */
    public static FolderPermission combine(FolderPermission p1, FolderPermission p2) {
        if (p1 == null) return p2;
        if (p2 == null) return p1;
        int combinedValue = p1.dbValue | p2.dbValue;
        for (FolderPermission p : FolderPermission.values()) {
            if (p.dbValue == combinedValue) {
                return p;
            }
        }
        throw new IllegalArgumentException("Invalid FolderPermission combined value: " + combinedValue);
    }
}

