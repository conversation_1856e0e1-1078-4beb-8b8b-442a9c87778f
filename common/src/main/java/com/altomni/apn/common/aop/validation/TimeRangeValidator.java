package com.altomni.apn.common.aop.validation;

import org.springframework.expression.spel.standard.SpelExpressionParser;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.time.Instant;

//@SupportedValidationTarget(ValidationTarget.PARAMETERS)
public class TimeRangeValidator implements ConstraintValidator<TimeRange, Object> {
    private static final SpelExpressionParser PARSER = new SpelExpressionParser();
    private String leftBound;
    private String rightBound;

    @Override
    public void initialize(TimeRange constraintAnnotation) {
        leftBound = constraintAnnotation.leftBound();
        rightBound = constraintAnnotation.rightBound();
    }

    @Override
    public boolean isValid(Object object, ConstraintValidatorContext context) {
        Object left = PARSER.parseExpression(leftBound).getValue(object);
        Object right = PARSER.parseExpression(rightBound).getValue(object);

        if(left == null){
            return false;
        }

        if(right == null){
            return true;
        }

        if (!(left instanceof Instant) || !(right instanceof Instant)) {
            throw new IllegalArgumentException("Illegal method signature, expected two parameters of type Instant.");
        }
        return ((Instant) left).isBefore((Instant) right);
    }

//    @Override
//    public boolean isValid(Object[] value, ConstraintValidatorContext context) {
//        if (value[0] == null || value[1] == null) {
//            return false;
//        }
//
//        if (!(value[0] instanceof Instant) || !(value[1] instanceof Instant)) {
//            throw new IllegalArgumentException("Illegal method signature, expected two parameters of type LocalDate.");
//        }
//        return ((Instant) value[0]).isBefore((Instant) value[1]);
//    }
}
