package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.company.CompanyType;
import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CalendarEventTypeConverter  extends AbstractAttributeConverter<CalendarEventTypeEnum, Integer> {
    public CalendarEventTypeConverter() {
        super(CalendarEventTypeEnum::toDbValue, CalendarEventTypeEnum::fromDbValue);
    }
}
