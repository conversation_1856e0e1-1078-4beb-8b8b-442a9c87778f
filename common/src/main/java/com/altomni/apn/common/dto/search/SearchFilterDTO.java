package com.altomni.apn.common.dto.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class SearchFilterDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "query filter with 'AND' relation, in json string")
    private List<SearchParam> queryFilter;

    //职位文件夹用filter条件
    private JobFolderSearchFilter jobFolderSearchFilter;

    private PurchaseFilterDTO purchaseFilter;

    private List<String> affiliations;

    private String affiliationForPrivateJob;

    private boolean privateJobPermission;

    private Long puserId;

    private List<String> folders;

    private List<String> source;

}
