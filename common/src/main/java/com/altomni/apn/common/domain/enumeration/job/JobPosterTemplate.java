package com.altomni.apn.common.domain.enumeration.job;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum JobPosterTemplate implements ConvertedEnum<Integer> {
    SIMPLE_ONE(10),

    SIMPLE_TWO(20),

    SIMPLE_THREE(30),

    DETAILED_ONE(40),

    DETAILED_TWO(50),

    DETAILED_THREE(60);
    private final int dbValue;

    JobPosterTemplate(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<JobPosterTemplate, Integer> resolver =
            new ReverseEnumResolver<>(JobPosterTemplate.class, JobPosterTemplate::toDbValue);

    public static JobPosterTemplate fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
