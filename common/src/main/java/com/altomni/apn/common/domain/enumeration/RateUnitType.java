package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The RateUnitType enumeration.
 */
public enum RateUnitType implements ConvertedEnum<Integer> {
    HOURLY(0, "Hourly"),
    DAILY(1, "Daily"),
    WEEKLY(2, "Weekly"),
    MONTHLY(3, "Monthly"),
    YEARLY(4, "Yearly");

    private final int dbValue;

    private final String description;

    RateUnitType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<RateUnitType, Integer> resolver =
            new ReverseEnumResolver<>(RateUnitType.class, RateUnitType::toDbValue);

    public static RateUnitType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static RateUnitType fromEnumValue(String val) {
        return RateUnitType.valueOf(val);
    }
}
