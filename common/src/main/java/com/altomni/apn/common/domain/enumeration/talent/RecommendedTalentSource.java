package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum RecommendedTalentSource implements ConvertedEnum<Integer> {

    TALENT_POOL(1),
    COMMON_POOL(2);

    // static resolving:
    public static final ReverseEnumResolver<RecommendedTalentSource, Integer> resolver =
        new ReverseEnumResolver<>(RecommendedTalentSource.class, RecommendedTalentSource::toDbValue);
    private final int dbValue;

    RecommendedTalentSource(int dbValue) {
        this.dbValue = dbValue;
    }

    public static RecommendedTalentSource fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }
}
