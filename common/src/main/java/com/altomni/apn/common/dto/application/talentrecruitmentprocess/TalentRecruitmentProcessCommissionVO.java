package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessCommissionVO extends AuditingUser implements Serializable, FillFeeCharge, FillClientInfo {

    private static final long serialVersionUID = 4929126004768195090L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private String note;

    private Instant noteLastModifiedDate;

    private Long noteLastModifiedByUserId;

    private TalentRecruitmentProcessOfferFeeChargeVO feeCharge;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

    private TalentRecruitmentProcessOnboardClientInfoVO clientInfo; //for general recruiting process to save clientContactId
}
