package com.altomni.apn.common.auth;

import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.security.oauth2.server.resource.web.DefaultBearerTokenResolver;

import javax.servlet.http.HttpServletRequest;

public class SkipOAuthTokenResolver implements BearerTokenResolver {

    private final DefaultBearerTokenResolver defaultBearerTokenResolver = new DefaultBearerTokenResolver();

    @Override
    public String resolve(HttpServletRequest request) {
        Object skip = request.getAttribute(Constants.SKIP_OAUTH2_TOKEN_VALIDATION);
        if (skip != null && skip.toString().equals("true")) {
            return null;
        }
        return defaultBearerTokenResolver.resolve(request);
    }
}
