package com.altomni.apn.common.dto.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * A JobSkillBrief.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobSkillBriefDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "Score in job skills means for this skill, how skillful the talent is; score in job means how important the skill is")
    private Float score;

    @ApiModelProperty(value = "skill name")
    private String skillName;

}
