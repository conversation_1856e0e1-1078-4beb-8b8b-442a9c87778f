package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 parser-service
 * <AUTHOR>
 */
public enum ParserAPIMultilingualEnum {

    PARSER_DOWNLOADRESUME_TALENTRESUMERELATIONNULL("parser_downloadResume_talentResumeRelationNull"),

    PARSER_CHECKPARSEJDSTATUSORGETUPLOADURL_FILENAMENULL("parser_checkParseJdStatusOrGetUploadUrl_fileNameNull"),

    PARSER_CHECKPARSEJDSTATUSORGETUPLOADURL_JDCANNOTHTML("parser_checkParseJdStatusOrGetUploadUrl_jdCannotHtml"),

    PARSER_CHECKPARSEJDSTATUSORGETUPLOADURL_JDTEXTTOOLONG("parser_checkParseJdStatusOrGetUploadUrl_jdTextTooLong"),

    PARSER_CHECKPARSEJDSTATUSORGETUPLOADURL_ERROR("parser_checkParseJdStatusOrGetUploadUrl_error"),

    PARSER_CHECKRESUMEPARSERESULTORGETUPLOADURL_UUIDNULL("parser_checkResumeParseResultOrGetUploadUrl_uuidNull"),

    PARSER_CHECKRESUMEPARSERESULTORGETUPLOADURL_FILENAMENULL("parser_checkResumeParseResultOrGetUploadUrl_fileNameNull"),

    ;

    private final String key;

    ParserAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}