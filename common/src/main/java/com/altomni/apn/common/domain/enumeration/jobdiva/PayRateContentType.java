package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum PayRateContentType implements ConvertedEnum<Integer>
{
    BILLING(0),
    PAY(1)
    ;

    private final int dbValue;

    PayRateContentType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<PayRateContentType, Integer> resolver = new ReverseEnumResolver<>(PayRateContentType.class, PayRateContentType::toDbValue);

    public static PayRateContentType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
