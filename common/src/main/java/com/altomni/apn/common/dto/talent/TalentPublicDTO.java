package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.dto.talent.TalentDTOV3;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class TalentPublicDTO extends TalentDTOV3 implements Serializable {

    private static final long serialVersionUID = -4643886969549095093L;

    @ApiModelProperty(value = "WechatUser id.")
    private Long userId;

    @ApiModelProperty(value = "talent's location")
    private String location;

    private String linkedInUrl;

    private String linkedInMemberId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getLinkedInUrl() {
        return linkedInUrl;
    }

    public void setLinkedInUrl(String linkedInUrl) {
        this.linkedInUrl = linkedInUrl;
    }

    public String getLinkedInMemberId() {
        return linkedInMemberId;
    }

    public void setLinkedInMemberId(String linkedInMemberId) {
        this.linkedInMemberId = linkedInMemberId;
    }

    @Override
    public String toString() {
        return "TalentPublicDTO{" +
                "userId=" + userId +
                ", location='" + location + '\'' +
                ", linkedInUrl='" + linkedInUrl + '\'' +
                ", linkedInMemberId='" + linkedInMemberId + '\'' +
                "} " + super.toString();
    }
}
