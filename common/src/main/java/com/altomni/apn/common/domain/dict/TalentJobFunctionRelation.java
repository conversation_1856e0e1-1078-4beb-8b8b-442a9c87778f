package com.altomni.apn.common.domain.dict;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@ApiModel
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "talent_job_function_relation")
public class TalentJobFunctionRelation  extends AbstractAuditingEntity implements EnumRelation{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "job_function_id")
    private Integer enumId;

    @Override
    public Integer getUniqueEnumId() {
        return enumId;
    }
}
