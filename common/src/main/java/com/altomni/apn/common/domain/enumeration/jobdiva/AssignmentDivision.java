package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum AssignmentDivision implements ConvertedEnum<Integer> {

    ALTOMNI(0, "Altomni"),
    IPG(1, "IPG"),
    INDEPENDENT(2, "Independent"),
    OTHER(3, "Other");
    private final int dbValue;

    private final String description;

    AssignmentDivision(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<AssignmentDivision, Integer> resolver = new ReverseEnumResolver<>(AssignmentDivision.class, AssignmentDivision::toDbValue);

    public static AssignmentDivision fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFormDbValue(Integer dbValue){
        return resolver.get(dbValue).name();
    }
}
