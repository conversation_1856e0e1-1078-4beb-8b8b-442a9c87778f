package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class AssignmentCurrencyTypeConverter extends AbstractAttributeConverter<AssignmentCurrencyType, Integer> {
    public AssignmentCurrencyTypeConverter() {
        super(AssignmentCurrencyType::toDbValue, AssignmentCurrencyType::fromDbValue);
    }
}
