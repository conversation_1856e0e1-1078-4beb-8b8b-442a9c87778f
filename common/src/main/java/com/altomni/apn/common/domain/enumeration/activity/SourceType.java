package com.altomni.apn.common.domain.enumeration.activity;

import com.altomni.apn.common.domain.enumeration.address.AddressType;
import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum SourceType implements ConvertedEnum<Integer> {
    COMPANY(0),
    TALENT(1),
    JOB(2);

    private final int dbValue;

    SourceType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<AddressType, Integer> resolver = new ReverseEnumResolver<>(AddressType.class, AddressType::toDbValue);

    public static AddressType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
