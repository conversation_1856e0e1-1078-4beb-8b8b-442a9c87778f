package com.altomni.apn.common.enumeration.enums;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ActivityArgsType implements ConvertedEnum<Integer> {
    NONE(0, "No Args"),
    //1 args, pass id to method to get string result;
    IDTYPEREMOTE(1, "remote 1 Args"),

    // 2 args, one enumId and DisplayType
    ENUMTYPE(2, "2 Args") ;

    private final int dbValue;
    private final String comment;

    public static final ReverseEnumResolver<ActivityArgsType, Integer> resolver =
            new ReverseEnumResolver<>(ActivityArgsType.class, ActivityArgsType::toDbValue);

    ActivityArgsType(int dbValue, String comment) {
        this.dbValue = dbValue;
        this.comment = comment;
    }

    public static ActivityArgsType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

}
