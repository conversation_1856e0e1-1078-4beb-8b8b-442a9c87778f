package com.altomni.apn.common.dto.activity;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ChangeFieldDTO {

    private Long fieldId;

    private String key;

    private String changedFrom;
    private String changedTo;

    private String operationType;

    public void setChangeFieldDTOFromActivityDTO(ActivityChangeDTO activityChangeDTO) {
        if (activityChangeDTO != null) {
            this.fieldId = activityChangeDTO.getFieldId();
            this.key = activityChangeDTO.getKey();
            this.changedFrom = activityChangeDTO.getChangedFrom();
            this.changedTo = activityChangeDTO.getChangedTo();
            this.operationType = activityChangeDTO.getEventType();
        }
    }

}
