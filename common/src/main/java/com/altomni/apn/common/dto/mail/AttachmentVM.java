package com.altomni.apn.common.dto.mail;

public class AttachmentVM {

    private String filename;

    private String link;

    public AttachmentVM() {
    }

    public AttachmentVM(String filename, String link) {
        this.filename = filename;
        this.link = link;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    @Override
    public String toString() {
        return "AttachmentVM{" +
            "filename='" + filename + '\'' +
            ", link='" + link + '\'' +
            '}';
    }
}
