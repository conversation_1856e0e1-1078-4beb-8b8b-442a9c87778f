package com.altomni.apn.common.service.crmenums;

import com.altomni.apn.common.domain.crmenums.EnumBusinessProgress;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.crmenums.EnumCrmCommonService;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.crmenums.EnumDictVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnumBusinessProgressService {

    @Resource
    private EnumCrmCommonService enumCrmCommonService;

    public List<EnumDictVO> findAllByIds(List<Integer> ids) {
        List<EnumBusinessProgress> enumBusinessProgressList = enumCrmCommonService.findAllEnumBusinessProgress();
        return enumBusinessProgressList.stream().filter(o -> ids.contains(o.getId())).map(item -> {
            EnumDictVO enumDictVO = new EnumDictVO();
            ServiceUtils.myCopyProperties(item, enumDictVO);
            return enumDictVO;
        }).collect(Collectors.toList());
    }

    public List<EnumDictVO> findAllBusinessProgress(SortType type) {
        List<EnumBusinessProgress> enumSalesLeadSourceList = enumCrmCommonService.findAllEnumBusinessProgress();
        if (enumSalesLeadSourceList == null) {
            return new ArrayList<>();
        }
        return enumSalesLeadSourceList.stream().map(o -> {
            EnumDictVO dictVO = new EnumDictVO();
            dictVO.setId(o.getId());
            dictVO.setName(o.getName());
            dictVO.setLabel(SortType.CN.equals(type) ? o.getCnDisplay() : o.getEnDisplay());
            dictVO.setCnDisplayOrder(o.getCnDisplayOrder());
            dictVO.setEnDisplayOrder(o.getEnDisplayOrder());
            return dictVO;
        }).collect(Collectors.toList());
    }

}
