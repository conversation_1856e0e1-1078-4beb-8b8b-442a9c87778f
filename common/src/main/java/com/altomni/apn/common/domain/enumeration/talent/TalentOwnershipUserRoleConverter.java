package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TalentOwnershipUserRoleConverter extends AbstractAttributeConverter<TalentOwnershipUserRole, Integer> {
    public TalentOwnershipUserRoleConverter() {
        super(TalentOwnershipUserRole::toDbValue, TalentOwnershipUserRole::fromDbValue);
    }
}
