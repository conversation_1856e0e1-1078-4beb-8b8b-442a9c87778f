package com.altomni.apn.common.utils;

import net.sf.jsqlparser.expression.Alias;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.schema.Table;

/**
 * Hibernate Utils
 * <AUTHOR>
 */
public class HibernateUtils {

    private static final String MYSQL_ESCAPE_CHARACTER = "`";

    /**
     * get clean table name
     *
     * @param table
     * @return TableName: clean table name
     */
    public static String getTableName(Table table) {
        String tableName = table.getName();
        if (tableName.startsWith(MYSQL_ESCAPE_CHARACTER) && tableName.endsWith(MYSQL_ESCAPE_CHARACTER)) {
            tableName = tableName.substring(1, tableName.length() - 1);
        }
        return tableName;
    }

    /**
     * Build Column Object
     *
     * @param tableAlias
     * @param column
     * @return Column: an object
     */
    public static Column buildColumn(Alias tableAlias, String column) {
        return new Column(tableAlias != null ? tableAlias.getName() + "." + column : column);
    }
}
