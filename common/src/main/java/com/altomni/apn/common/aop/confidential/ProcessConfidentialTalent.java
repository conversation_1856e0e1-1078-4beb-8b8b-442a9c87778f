package com.altomni.apn.common.aop.confidential;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ProcessConfidentialTalent {

    enum operation {
        FILTER, ENCRYPT, DO_NOTHING
    }

    operation operation() default operation.ENCRYPT;
}
