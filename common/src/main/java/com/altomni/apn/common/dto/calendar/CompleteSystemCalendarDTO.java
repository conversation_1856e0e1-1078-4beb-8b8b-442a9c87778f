package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.domain.enumeration.calendar.CalendarRelationEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import lombok.Data;

import java.util.List;

@Data
public class CompleteSystemCalendarDTO {
    private List<CalendarTypeEnum> type;
    private CalendarRelationEnum relationType;
    private Long uniqueReferenceId;

}
