package com.altomni.apn.common.domain.enumeration.user;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CompletionStatusConverter extends AbstractAttributeConverter<CompletionStatus, Integer> {
    public CompletionStatusConverter() {
        super(CompletionStatus::toDbValue, CompletionStatus::fromDbValue);
    }
}
