package com.altomni.apn.common.dto.application.nodepagesection;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;

/**
 * A DTO for RecruitmentProcessNodePageSection.
 */
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitmentProcessNodePageSectionDTO extends AbstractAuditingEntity implements Serializable {

    private Long recruitmentProcessId;

    private Long tenantId;

    @Convert(converter = NodeTypeConverter.class)
    private NodeType nodeType;

    private String description;

    @JsonRawValue
    private String fieldConfig;

    public Long getRecruitmentProcessId() {
        return recruitmentProcessId;
    }

    public void setRecruitmentProcessId(Long recruitmentProcessId) {
        this.recruitmentProcessId = recruitmentProcessId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public NodeType getNodeType() {
        return nodeType;
    }

    public void setNodeType(NodeType nodeType) {
        this.nodeType = nodeType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFieldConfig() {
        return fieldConfig;
    }

    public void setFieldConfig(String fieldConfig) {
        this.fieldConfig = fieldConfig;
    }

    @Override
    public String toString() {
        return "RecruitmentProcessNodePageSectionDTO{" +
                "recruitmentProcessId=" + recruitmentProcessId +
                ", tenantId=" + tenantId +
                ", nodeType=" + nodeType +
                ", description='" + description + '\'' +
                ", fieldConfig='" + fieldConfig + '\'' +
                '}';
    }
}
