package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class ExpenseInvoiceTypeConverter extends AbstractAttributeConverter<ExpenseInvoiceType, Integer> {
    public ExpenseInvoiceTypeConverter() {
        super(ExpenseInvoiceType::toDbValue, ExpenseInvoiceType::fromDbValue);
    }
}
