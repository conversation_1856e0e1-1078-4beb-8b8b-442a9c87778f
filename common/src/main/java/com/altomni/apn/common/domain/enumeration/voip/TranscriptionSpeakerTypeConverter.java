package com.altomni.apn.common.domain.enumeration.voip;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TranscriptionSpeakerTypeConverter extends AbstractAttributeConverter<TranscriptionSpeakerType, Integer> {
    public TranscriptionSpeakerTypeConverter() {
        super(TranscriptionSpeakerType::toDbValue, TranscriptionSpeakerType::fromDbValue);
    }
}
