package com.altomni.apn.common.dto.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
@JsonDeserialize(using = TalentSearchFolderConditionDeserializer.class)
public class TalentSearchFolderConditionDTO extends TalentSearchConditionDTO{
    private List<String> folders;
    private String searchType;
}
