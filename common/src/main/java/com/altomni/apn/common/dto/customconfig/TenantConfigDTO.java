package com.altomni.apn.common.dto.customconfig;

import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TenantConfigDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "must not be null")
    private TenantConfigCode configCode;

    @NotNull
    private String configValue;

}
