package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoicingBodyConverter extends AbstractAttributeConverter<InvoicingBody, Integer> {
    public InvoicingBodyConverter() {
        super(InvoicingBody::toDbValue, InvoicingBody::fromDbValue);
    }
}
