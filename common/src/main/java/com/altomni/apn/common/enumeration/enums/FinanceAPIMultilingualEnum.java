package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 finance-service
 * <AUTHOR>
 */
public enum FinanceAPIMultilingualEnum {

    COMMISSION_SAVE_STARTNULL("commission_save_startNull"),

    COMMISSION_SAVE_VALIDATESTARTNULL("commission_save_validateStartNull"),

    COMMISSION_SAVE_VALIDATEAMOUNTNULL("commission_save_validateAmountNull"),

    COMMISSION_SAVEFTE_STARTIDALREADY("commission_saveFte_startIdAlready"),

    COMMISSION_SAVEFTE_INVOICENULL("commission_saveFte_invoiceNull"),

    COMMISSION_SAVEFTE_INVOICEIDERROR("commission_saveFte_invoiceIdError"),

    COMMISSION_SAVECONTRACT_GROSSMARGINNULL("commission_saveContract_grossMarginNull"),

    COMMISSION_SAVECONTRACT_GROSSMARGINERROR("commission_saveContract_grossMarginError"),

    COMMISSION_UPDATE_COMMISSIONNOTEXIST("commission_update_commissionNotExist"),

    COMMISSION_PAY_COMMISSIONNOTEXIST("commission_pay_commissionNotExist"),

    COMMISSION_PAY_COMMISSIONALREADYPAID("commission_pay_commissionAlreadyPaid"),

    COMMISSION_FINDONE_COMMISSIONNOTFIND("commission_findOne_commissionNotFind"),

    INVOICE_APPLY_INVOICEACTIVITYEXIST("invoice_apply_InvoiceActivityExist"),

    INVOICE_CHECKINVOICE_NOTFIND("invoice_checkInvoice_notFind"),

    INVOICE_CHECKINVOICE_NOPERMISSION("invoice_checkInvoice_noPermission"),

    INVOICE_DEDUCT_INVOICECLIENTCREDITNOTFIND("invoice_deduct_InvoiceClientCreditNotFind"),

    INVOICE_DEDUCT_NOENOUGHCREDIT("invoice_deduct_noEnoughCredit"),

    INVOICE_CHECKCOMPANY_NOTFIND("invoice_checkCompany_notFind"),

    INVOICE_CHECKCOMPANY_NOPERMISSION("invoice_checkCompany_noPermission"),

    INVOICE_PAYMENT_NOTFIND("invoice_payment_notFind"),

    INVOICE_PAYMENT_NEGATIVEVALUE("invoice_payment_negativeValue"),

    INVOICE_CREATEFTEINVOICE_STARTNOTEXIST("invoice_createFteInvoice_startNotExist"),

    INVOICE_CREATEFTEINVOICE_STARTUPINVOICENULL("invoice_createFteInvoice_startupInvoiceNull"),

    INVOICE_CREATEFTEINVOICE_STARTUPFEEALREADY("invoice_createFteInvoice_startupFeeAlready"),

    INVOICE_CREATEFTEINVOICE_STARTUPFEENOPAID("invoice_createFteInvoice_startupFeeNoPaid"),

    INVOICE_CREATEFTEINVOICE_NOPERMISSION("invoice_createFteInvoice_noPermission"),

    INVOICE_CREATEFTEINVOICE_WRONGTOTAL("invoice_createFteInvoice_wrongTotal"),

    INVOICE_CREATEFTEINVOICE_TOTALAMOUNTINCORRECT("invoice_createFteInvoice_totalAmountIncorrect"),

    INVOICE_CREATEFTEINVOICE_DUEDATENULL("invoice_createFteInvoice_dueDateNull"),

    INVOICE_GETTALENT_ISNULL("invoice_getTalent_isNUll"),

    INVOICE_GETJOB_ISNULL("invoice_getJob_isNUll"),

    INVOICE_GETCOMPANY_ISNULL("invoice_getCompany_isNUll"),

    INVOICE_VOIDINVOICE_NOTFIND("invoice_voidInvoice_notFind"),

    INVOICE_VOIDINVOICE_IDNULL("invoice_voidInvoice_idNull"),

    INVOICE_VOIDINVOICE_ISVOIDSTATUS("invoice_voidInvoice_isVoidStatus"),

    INVOICE_VOIDINVOICE_ALREADYPAID("invoice_voidInvoice_alreadyPaid"),

    INVOICE_REFUNDCREDIT_INVOICECLIENTNOCREDIT("invoice_refundCredit_invoiceClientNoCredit"),

    INVOICE_VOIDINVOICEBYNO_NUMBERNULL("invoice_voidInvoiceByNo_numberNull"),

    INVOICE_VOIDINVOICEBYNO_ISVOIDSTATUS("invoice_voidInvoiceByNo_isVoidStatus"),

    INVOICE_VOIDINVOICEBYNO_ISPAIDSTATUS("invoice_voidInvoiceByNo_isPaidStatus"),

    INVOICE_DOWNLOADINVOICE_INVOICENOTEXIST("invoice_downloadInvoice_invoiceNotExist"),

    START_FINDBYSTARTID_STARTADDRESSISNULL("start_findByStartId_startAddressIsnull"),

    START_FINDBYSTARTID_STARTCLIENTISNULL("start_findByStartId_startClientIsnull"),

    START_CREATE_ACCRUEDPERCENTAGENINETY("start_create_accruedPercentageNinety"),

    START_CREATE_ACCRUEDPERCENTAGEHUNDRED("start_create_accruedPercentageHundred"),

    START_REPLACE_ACCRUEDPERCENTAGENINETY("start_replace_accruedPercentageNinety"),

    START_VALIDATESTART_STARTNOEXIST("start_validateStart_startNoExist"),

    START_VALIDATESTART_NOPERMISSION("start_validateStart_noPermission"),

    START_RATECHANGE_LASTRATEISNULL("start_rateChange_lastRateisNull"),

    START_VALIDATION_STARTDATEISNULL("start_validation_startDateIsNull"),

    START_VALIDATION_FINALBILLRATEISNULL("start_validation_finalBillRateIsNull"),

    START_VALIDATION_FINALPAYRATEISNULL("start_validation_finalPayRateIsNull"),

    START_VALIDATION_TAXBURDENRATECODEISNULL("start_validation_taxBurdenRateCodeIsNull"),

    START_VALIDATION_MSPRATECODEISNULL("start_validation_mspRateCodeIsNull"),

    START_VALIDATION_IMMIGRATIONCOSTCODE("start_validation_immigrationCostCode"),

    START_VALIDATION_ESTIMATEDWORKINGHOURPERWEEK("start_validation_estimatedWorkingHourPerWeek"),

    START_CHECKTOTALBILLAMOUNT_AMOUNTINCORRECT("start_checkTotalBillAmount_amountIncorrect"),

    START_GETOFFERLETTERCOSTRATEVALUE_NOTEXIST("start_getOfferLetterCostRateValue_notExist"),

    START_UPDATE_LASTRATENOTEXIST("start_update_lastRateNotExist"),

    START_CHECKPERMISSION_COMPANYIDNULL("start_checkPermission_companyIdNull"),

    START_CHECKPERMISSION_NOPERMISSION("start_checkPermission_noPermission"),

    START_COMMON_INTERNETERROR("start_common_internetError"),

    START_DELETE_LASTRATENOEXIST("start_delete_lastRateNoExist"),

    START_DELETE_EXTENDSTARTCONTRACTRATEIDNULL("start_delete_extendStartContractRateIdNull"),

    START_FINDBYID_NOTEXIST("start_findById_notExist"),

    START_FINDLASTBYSTARTID_NOTEXIST("start_findLastByStartId_notExist"),

    START_CREATESTARTEXTENSION_CONTRACTRATESISNULL("start_createStartExtension_contractRatesIsNull"),

    START_VALIDATIONSTART_STARTOPTIONALISNULL("start_validationStart_startOptionalIsNull"),

    START_VALIDATIONSTART_STARTDATEERROR("start_validationStart_startDateError"),

    START_VALIDATIONSTART_STARTSTATUSERROR("start_validationStart_startStatusError"),

    START_VALIDATIONSTART_JOBNULL("start_validationStart_jobNull"),

    START_VALIDATIONSTART_JOBTYPEERROR("start_validationStart_jobTypeError"),

    START_UPDATESTARTEXTENSION_STARTISNULL("start_updateStartExtension_startIsNull"),

    START_UPDATESTARTEXTENSION_STARTDATEERROR("start_updateStartExtension_startDateError"),

    START_STARTFAILEDWARRANTY_STARTIDNULL("start_startFailedWarranty_startIdNull"),

    START_STARTFAILEDWARRANTY_ENDDATENULL("start_startFailedWarranty_endDateNull"),

    START_STARTFAILEDWARRANTY_JOBTYPEERROR("start_startFailedWarranty_jobTypeError"),

    START_STARTFAILEDWARRANTY_TOTALAMOUNTNULL("start_startFailedWarranty_totalAmountNull"),

    START_STARTFAILEDWARRANTY_NOTEXIST("start_startFailedWarranty_notExist"),

    START_STARTFAILEDWARRANTY_UPDATETOTALAMOUNTNULL("start_startFailedWarranty_updateTotalAmountNull"),

    START_FTEFINDBYSTARTID_STARTFTERATENULL("start_fteFindByStartId_startFteRateNull"),

    START_SAVE_COMMISSIONSNULL("start_save_commissionsNull"),

    START_SAVE_WARRANTENDDATENULL("start_save_warrantEndDateNull"),

    START_CREATEFORCONTRACT_CONTRACTRATESNULL("start_createForContract_contractRatesNull"),

    START_VALIDATEEXISTSSTART_STARTNULL("start_validateExistsStart_startNull"),

    START_GETTALENTRECRUITMENTPROCESS_ISNULL("start_getTalentRecruitmentProcess_isNull"),

    START_GETTALENT_ISNULL("start_getTalent_isNull"),

    START_GETJOB_ISNULL("start_getJob_isNull"),

    START_UPDATE_STARTISNULL("start_update_startIsNull"),

    START_CHECKPERMISSIONBYCOMPANYID_COMPANYISNULL("start_checkPermissionByCompanyId_companyIsNull"),

    START_CHECKPERMISSIONBYCOMPANYID_NOPERMISSION("start_checkPermissionByCompanyId_noPermission"),

    START_FINDBYID_STARTNOEXIST("start_findById_startNoExist"),

    START_TERMINATIONSAVE_STARTIDISNULL("start_terminationSave_startIdIsNull"),

    START_TERMINATIONSAVE_DATEISNULL("start_terminationSave_dateIsNull"),

    START_TERMINATIONSAVE_JOBTYPEERROR("start_terminationSave_jobTypeError"),

    START_SYNCTALENTTOES_ERROR("start_syncTalentToES_error"),

    START_RATECHANGE_STARTCONTRACTRATEIDNOTNULL("start_rateChange_startContractRateIdNotNull"),

    START_RATECHANGE_PARAM_NULL("start_rateChange_param_null"),

    MISSING_INPUT("missing_input"),

    NOT_PERMISSION("not_permission"),

    INVOICING_INPUTAMOUNTGREATERTHANGPAMOUNT("invoicing_inputAmountGreaterThanGpAmount"),

    INVOICING_INPUTAMOUNTGREATERTHANAMOUNTRECEIVED("invoicing_inputAmountGreaterThanAmountReceived"),

    INVOICING_GETSEQUENCE_ERROR("invoicing_getSequence_error"),

    INVOICING_MULT_COMPANY("invoicing_mult_company"),

    INVOICING_PREPAYMENT_INSUFFICIENT("invoicing_prepayment_insufficient"),

    INVOICING_APPLICATION_NOT_FOUND("invoicing_application_not_found"),

    INVOICING_APPLICATION_STATUS_ERROR("invoicing_application_status_error"),

    INVOICING_APPLICATION_TYPE_ERROR("invoicing_application_type_error"),

    INVOICING_APPLICATION_PAYMENT_ISUSE("invoicing_application_payment_isuse"),

    INVOICING_APPLICATION_ELEC_EXIST("invoicing_application_elec_exist"),

    INVOICING_PREPAYMENT_AMOUNT_ERROR("invoicing_prepayment_amount_error"),

    INVOICING_APPLICATION_INVOICINGTYPE_ERROR("invoicing_application_invoicingType_error"),

    INVOICING_VOID_APPLICATION_EXIST("invoicing_void_application_exist"),

    INVOICE_TYPE_NOT_EXIST("invoice_type_not_exist");
    ;

    private final String key;

    FinanceAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}