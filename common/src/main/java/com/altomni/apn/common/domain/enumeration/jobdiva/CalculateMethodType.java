package com.altomni.apn.common.domain.enumeration.jobdiva;


import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.List;

/**
 * The CompanyType enumeration.
 */
public enum CalculateMethodType implements ConvertedEnum<Integer>
{
    FEDERAL(0),
    CALIFORNIA(1),
    COLORADO(2),
    PUERTO_RICO(3),
    KENTUCKY(4),
    US_VIRGIN_ISLAND(5),

    CANADA_BC(6),

    CANADA_ON(7),

    CANADA_QC(8),

    //Alberta
    CANADA_AB(9),


    //Manitoba
    CANADA_MB(10),

    //New Brunswick
    CANADA_NB(11),

    //Nova Scotia
    CANADA_NS(12),

    //Prince Edward Island
    CANADA_PE(13),

    //Newfoundland and Labrador
    CANADA_NL(14),

    //Yukon
    CANADA_YT(15),

    //Northwest Territories
    CANADA_NT(16),

    //Nunavut
    CANADA_NU(17),

    CANADA_FEDERAL(18),

    CANADA_SK_8(19),

    CANADA_SK_10(20),

    GB_FEDERAL(21),



    IN_FEDERAL(22),



    ;

    private final int dbValue;

    CalculateMethodType(int dbValue) {
        this.dbValue = dbValue;
    }

    public static final List<CalculateMethodType> dtList = CollUtil.newArrayList(CalculateMethodType.CALIFORNIA, CalculateMethodType.CANADA_BC);


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CalculateMethodType, Integer> resolver = new ReverseEnumResolver<>(CalculateMethodType.class, CalculateMethodType::toDbValue);

    public static CalculateMethodType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
