package com.altomni.apn.common.enumeration.enums;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The SortType enumeration.
 */
public enum SortType implements ConvertedEnum<Integer> {
    EN(0, "en_display_order"),
    CN(1, "cn_display_order");


    // static resolving:
    public static final ReverseEnumResolver<SortType, Integer> resolver =
            new ReverseEnumResolver<>(SortType.class, SortType::toDbValue);
    private final int dbValue;
    private final String name;

    SortType(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static SortType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
