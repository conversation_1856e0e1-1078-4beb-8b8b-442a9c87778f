package com.altomni.apn.common.dto.xxljob;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public interface SystemCalendarResult {
    Long getJobId();
    String getJobTitle();
    Long getTenantId();
    Long getCompanyId();
    String getCompanyName();
    Integer getActive();
    String getUserIds();// 直接接收逗号分隔的字符串
    Long getTalentId();
    String getTalentName();
    Long getCompanyContactId();
    String getCompanyContactName();
    Long getTalentRecruitmentProcessId();
    Long getBusinessId();
    String getBusinessName();
    Long getContractId();
    String getContractName();
    Long getInvoiceId();
    String getInvoiceName();

    // 默认方法，将逗号分隔的用户ID字符串转换为Long列表
    default List<Long> getUserIdList() {
        if (getUserIds() == null || getUserIds().isEmpty()) {
            return Collections.emptyList();
        }

        return Arrays.stream(getUserIds().split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }
}
