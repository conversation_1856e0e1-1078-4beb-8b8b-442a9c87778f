package com.altomni.apn.common.dto.address;


import com.altomni.apn.common.domain.enumeration.address.AddressType;
import com.altomni.apn.common.dto.geoinfo.GeoInfoENDTO;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * A Address.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public abstract class AddressDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private AddressType addressType;

    private String city;

    private String address;

    private String address2;

    private Long cityId;

    private String zipcode;

    private GeoInfoENDTO geoInfoENDTO;
}
