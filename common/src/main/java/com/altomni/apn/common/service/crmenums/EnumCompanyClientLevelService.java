package com.altomni.apn.common.service.crmenums;

import com.altomni.apn.common.domain.crmenums.EnumCompanyClientLevel;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.crmenums.EnumCrmCommonService;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.crmenums.EnumDictVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnumCompanyClientLevelService {

    @Resource
    private EnumCrmCommonService enumCrmCommonService;

    public List<EnumDictVO> findAllByIds(List<Integer> ids) {
        List<EnumCompanyClientLevel> enumCompanyClientLevelList = enumCrmCommonService.findAllEnumCompanyClientLevel();
        return enumCompanyClientLevelList.stream().filter(o -> ids.contains(o.getId())).map(item -> {
            EnumDictVO enumDictVO = new EnumDictVO();
            ServiceUtils.myCopyProperties(item, enumDictVO);
            return enumDictVO;
        }).collect(Collectors.toList());
    }

    public List<EnumDictVO> findAllCompanyClientLevel(SortType type) {
        List<EnumCompanyClientLevel> enumCompanyClientLevelList = enumCrmCommonService.findAllEnumCompanyClientLevel();

        if (enumCompanyClientLevelList == null) {
            return new ArrayList<>();
        }

        return enumCompanyClientLevelList.stream().map(o -> {
            EnumDictVO dictVO = new EnumDictVO();
            dictVO.setId(o.getId());
            dictVO.setLabel(SortType.CN.equals(type) ? o.getCnDisplay() : o.getEnDisplay());
            dictVO.setName(o.getName());
            return dictVO;
        }).collect(Collectors.toList());
    }


}

