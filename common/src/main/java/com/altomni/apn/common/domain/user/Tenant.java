package com.altomni.apn.common.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.StaffSizeType;
import com.altomni.apn.common.domain.enumeration.StaffSizeTypeConverter;
import com.altomni.apn.common.domain.enumeration.company.IndustryType;
import com.altomni.apn.common.domain.enumeration.company.IndustryTypeConverter;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnumConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

/**
 * A Tenant.
 */
@Entity
@Table(name = "tenant")
@Data
public class Tenant extends AbstractAuditingEntity implements Serializable
{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "name", nullable = false)
    private String name;

    @Convert(converter = IndustryTypeConverter.class)
    @Column(name = "industry")
    private IndustryType industry;

    @ApiModelProperty(name = "tenant website ")
    @Column(name = "website")
    private String  website;

    @ApiModelProperty(name = "organization name")
    @Column(name = "organization_name")
    private String organizationName;

    @ApiModelProperty(name = "staff size for prospect company")
    @Convert(converter = StaffSizeTypeConverter.class)
    @Column(name = "staff_size")
    private StaffSizeType staffSizeType;

    @ApiModelProperty(name = "tenant founded date")
    @Column(name = "founded_date")
    private Instant foundedDate;

    @Column(name = "address_id")
    private Long addressId;

    @Column(name = "description")
    private String description;

    @Column(name = "status")
    private Integer status;

    @Column(name = "logo")
    private String logo;

    @Column(name = "bulk_credit")
    private Integer bulkCredit;

    @Column(name = "monthly_credit")
    private Integer monthlyCredit;

    @Column(name = "update_monthly_credit")
    private Integer updateMonthlyCredit;

    @Column(name = "tenant_email")
    private String tenantEmail;

    @Column(name = "tenant_phone")
    private String tenantPhone;

    @Column(name = "expire_date")
    private Instant expireDate;

    @Column(name = "contact_name")
    private String contactName;

    @Column(name = "contact_first_name")
    private String contactFirstName;

    @Column(name = "contact_last_name")
    private String contactLastName;

    @Column(name = "user_max_limit")
    private Integer userMaxLimit;

    @Column(name = "note")
    private String note;

    @Column(name = "login_link")
    private String loginLink;

    @Column(name = "user_type")
    @Convert(converter = TenantUserTypeEnumConverter.class)
    private TenantUserTypeEnum userType;

    @Column(name = "reset_day_of_month")
    private Integer resetDayOfMonth;

    @Column(name = "owner_data_restriction")
    private boolean ownerDataRestriction;

    /**
     * 禁猎客户需求：用于存储不同租户下类似webhook URL的额外信息
     */
    @Column(name = "extended_info")
    private String extendedInfo;

    @Transient
    private Boolean isIpgRuleTenant = false;

}
