package com.altomni.apn.common.vo.voip;

import com.altomni.apn.common.domain.enumeration.voip.PhoneCallStatus;
import com.altomni.apn.common.domain.enumeration.voip.PhoneCallStatusConverter;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModel;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModelConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VoipContactDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @NotNull
    private String phoneCallId;

    private String instanceId;

    @NotNull
    private Long userId;

    private String userName;

    @NotNull
    private Long talentId;

    private String talentName;

    private String phoneNumber;

    @NotNull
    private Long tenantId;

    @Convert(converter = TranscriptionModelConverter.class)
    private TranscriptionModel transcriptionModel;

    @Convert(converter = PhoneCallStatusConverter.class)
    private PhoneCallStatus phoneCallStatus = PhoneCallStatus.ANSWERED;

    @NotNull
    private Long callTypeId;

    @NotNull
    private Long callResultId;

    @NotNull
    private Long jobId;

    private Boolean isRecordDelete = false;

    private String summary;

    private Instant createdDate;

    public VoipContactDetailVO(Long id, String phoneCallId, Long userId, String userName, Long talentId, String talentName, String phoneNumber, Long tenantId, Long callTypeId, Long callResultId, Long jobId) {
        this.id = id;
        this.phoneCallId = phoneCallId;
        this.userId = userId;
        this.userName = userName;
        this.talentId = talentId;
        this.talentName = talentName;
        this.phoneNumber = phoneNumber;
        this.tenantId = tenantId;
        this.callTypeId = callTypeId;
        this.callResultId = callResultId;
        this.jobId = jobId;
    }

}
