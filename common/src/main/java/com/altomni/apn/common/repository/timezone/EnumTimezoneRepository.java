package com.altomni.apn.common.repository.timezone;

import com.altomni.apn.common.domain.timezone.EnumTimezone;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EnumTimezoneRepository extends JpaRepository<EnumTimezone, Integer> {

    List<EnumTimezone> findByTimezone(String timezone);
}
