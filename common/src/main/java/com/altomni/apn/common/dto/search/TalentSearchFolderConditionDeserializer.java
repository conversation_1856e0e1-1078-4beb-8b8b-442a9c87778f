package com.altomni.apn.common.dto.search;

import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.search.CommonPoolTalentType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class TalentSearchFolderConditionDeserializer extends JsonDeserializer<TalentSearchFolderConditionDTO> {
    @Override
    public TalentSearchFolderConditionDTO deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
    ObjectMapper mapper = (ObjectMapper) p.getCodec();
    JsonNode node = mapper.readTree(p);

    TalentSearchFolderConditionDTO dto = new TalentSearchFolderConditionDTO();

    // 处理继承自 TalentSearchConditionDTO 的字段
    if (node.has("search")) {
        List<ComplexSearchParam> searchList = new ArrayList<>();
        JsonNode searchNodes = node.get("search");
        for (JsonNode searchNode : searchNodes) {
            ComplexSearchParam complexParam = new ComplexSearchParam();
            if (searchNode.has("relation")) {
                complexParam.setRelation(Relation.valueOf(searchNode.get("relation").asText()));
            }
            if (searchNode.has("condition")) {
                List<SearchCondition> conditions = new ArrayList<>();
                JsonNode conditionNodes = searchNode.get("condition");
                for (JsonNode conditionNode : conditionNodes) {
                    conditions.add(deserializeSearchCondition(conditionNode, mapper));
                }
                complexParam.setCondition(conditions);
            }
            searchList.add(complexParam);
        }
        dto.setSearch(searchList);
    }

    if (node.has("filter")) {
        List<SearchParam> filterList = new ArrayList<>();
        JsonNode filterNodes = node.get("filter");
        for (JsonNode filterNode : filterNodes) {
            filterList.add(mapper.treeToValue(filterNode, SearchParam.class));
        }
        dto.setFilter(filterList);
    }

    if (node.has("folderIds")) {
        dto.setFolderIds(mapper.convertValue(node.get("folderIds"), new TypeReference<List<String>>() {}));
    }

    if (node.has("belongToRelateJobFolder")) {
        dto.setBelongToRelateJobFolder(node.get("belongToRelateJobFolder").asBoolean());
    }

    if (node.has("module")) {
        dto.setModule(ModuleType.valueOf(node.get("module").asText()));
    }

    if (node.has("commonPoolType")) {
        dto.setCommonPoolType(CommonPoolTalentType.valueOf(node.get("commonPoolType").asText()));
    }

    if (node.has("timezone")) {
        dto.setTimezone(node.get("timezone").asText());
    }

    if (node.has("language")) {
        dto.setLanguage(LanguageEnum.valueOf(node.get("language").asText()));
    }

    // 处理 TalentSearchFolderConditionDTO 自身的字段
    if (node.has("folders")) {
        dto.setFolders(mapper.convertValue(node.get("folders"), new TypeReference<List<String>>() {}));
    }

    if (node.has("searchType")) {
        dto.setSearchType(node.get("searchType").asText());
    }

    return dto;
}

    private SearchCondition deserializeSearchCondition(JsonNode node, ObjectMapper mapper) throws IOException {
        if (node.has("relation") && node.has("condition")) {
            ComplexSearchParam complex = new ComplexSearchParam();
            complex.setRelation(Relation.valueOf(node.get("relation").asText()));

            List<SearchCondition> conditions = new ArrayList<>();
            JsonNode conditionNodes = node.get("condition");
            for (JsonNode conditionNode : conditionNodes) {
                if (conditionNode.has("relation") && conditionNode.has("condition")) {
                    conditions.add(deserializeSearchCondition(conditionNode, mapper));
                } else {
                    conditions.add(deserializeConditionParam(conditionNode, mapper));
                }
            }
            complex.setCondition(conditions);
            return complex;
        } else {
            return deserializeConditionParam(node, mapper);
        }
    }

    private ConditionParam deserializeConditionParam(JsonNode node, ObjectMapper mapper) throws IOException {
        ConditionParam param = new ConditionParam();
        if (node.has("key")) {
            param.setKey(node.get("key").asText());
        }
        if (node.has("value")) {
            param.setValue(mapper.treeToValue(node.get("value"), Object.class));
        }
        if (node.has("type")) {
            param.setType(node.get("type").asText());
        }
        return param;
    }
}
