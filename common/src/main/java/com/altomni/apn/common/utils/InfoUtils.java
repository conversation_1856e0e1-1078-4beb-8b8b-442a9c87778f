package com.altomni.apn.common.utils;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

public class InfoUtils {
    public static Object merge(Object source, Object target) {
        try {
            if (source == null) {
                return null;
            }
            if (target == null) {
                return source;
            }

            if (source instanceof JSONObject) {
                JSONObject sourceJSONObject = (JSONObject) source;
                JSONObject targetJSONObject = (JSONObject) target;

                if (sourceJSONObject.containsKey("gt") || sourceJSONObject.containsKey("lt") ||
                        sourceJSONObject.containsKey("gte") || sourceJSONObject.containsKey("lte")) {
                    return source;
                }
                JSONObject newJSONObject = new JSONObject();

                for (String key : targetJSONObject.keySet()) {
                    if (!sourceJSONObject.containsKey(key)) {
                        newJSONObject.put(key, targetJSONObject.get(key));
                    }
                }

                for (String key : sourceJSONObject.keySet()) {
                    Object value = sourceJSONObject.get(key);
                    Object result = merge(value, targetJSONObject.get(key));
                    if (ObjectUtil.isNotEmpty(result)) {
                        newJSONObject.put(key, result);
                    } else {
                        newJSONObject.remove(key);
                    }
                }
                return newJSONObject;

            } else if (source instanceof JSONArray) {
                Map map = new HashMap<>();
                JSONArray sourceJSONArray = (JSONArray) source;
                JSONArray targetJSONArray = (JSONArray) target;
                long maxId = 0;

                for (Object obj : targetJSONArray) {
                    if (obj instanceof JSONObject) {
                        JSONObject objJSONObject = (JSONObject) obj;
                        Long id = objJSONObject.getLong("id");
                        if (id != null) {
                            if(id > maxId) {
                                maxId = id;
                            }
                            map.put(id, objJSONObject);
                        }
                    }
                }

                JSONArray newJSONArray = new JSONArray();
                for (Object obj : sourceJSONArray) {
                    if (obj instanceof JSONObject) {
                        JSONObject objJSONObject = (JSONObject) obj;
                        Long id = objJSONObject.getLong("id");
                        Object result;
                        if (id != null) {
                            result = merge(objJSONObject, map.get(id));
                        } else {
                            maxId++;
                            id = maxId;
                            result = obj;
                        }
                        if (ObjectUtil.isNotEmpty(result)) {
                            JSONObject jsonObject = (JSONObject) result;
                            if (!jsonObject.containsKey("gt") && !jsonObject.containsKey("lt") &&
                                !jsonObject.containsKey("gte") && !jsonObject.containsKey("lte")) {
                                    jsonObject.put("id",id);
                                }
                            newJSONArray.add(jsonObject);
                        }
                    } else {
                        Object result = obj;
                        //和parser解析结果时，"yearRanges":[null,null,null],"proficiencies":[null,"EXPERT","EXPERT"]， 这种数组中的null不能过滤。所以下面不做非空判断
                        newJSONArray.add(result);
                    }
                }
                return newJSONArray;

            } else
                return source;

        } catch (Exception e) {
            return null;
        }
    }


    public static Object getSpecifiedInfo(Object fullProfile, Object requirements) {
        try {
            if (requirements == null) {
                return null;
            }

            if (fullProfile instanceof JSONObject) {
                JSONObject newJSONObject = new JSONObject();

                JSONObject sourceJSONObject = (JSONObject) fullProfile;
                JSONObject targetJSONObject = (JSONObject) requirements;
                for (String key : sourceJSONObject.keySet()) {

                    Object sourceValue = sourceJSONObject.get(key);
                    Object targetValue = targetJSONObject.get(key);

                    if (targetValue instanceof Boolean) {
                        if ((Boolean) targetValue) {
                            newJSONObject.put(key, sourceValue);
                        }
                    } else {
                        Object result = getSpecifiedInfo(sourceValue, targetValue);
                        if (ObjectUtil.isNotEmpty(result)) {
                            newJSONObject.put(key, result);
                        }
                    }
                }
                return newJSONObject;

            } else if (fullProfile instanceof JSONArray) {
                JSONArray sourceJSONArray = (JSONArray) fullProfile;
                JSONObject targetJSONObject = (JSONObject) requirements;

                JSONArray newJSONArray = new JSONArray();
                for (Object obj : sourceJSONArray) {
                    Object result = getSpecifiedInfo(obj, targetJSONObject);
                    if (ObjectUtil.isNotEmpty(result)) {
                        newJSONArray.add(result);
                    }
                }
                return newJSONArray;

            } else
                return (Boolean) requirements ? fullProfile : null;

        } catch (Exception e) {
            return null;
        }
    }


    public static void replenishNullField(JSONObject jsonObject, Class<?> clazz) {
        Field[] fields = ReflectUtil.getFieldsDirectly(clazz, true);
        if (ObjectUtil.isNotEmpty(fields)) {
            for (Field field : fields) {
                try {
                    String name = field.getName();
                    Object value = jsonObject.get(name);
                    if (ObjectUtil.isEmpty(value)) {
                        jsonObject.put(name, null);
                        continue;
                    }

                    Class<?> type = field.getType();
                    if (type.isArray()) {
                        JSONArray a = jsonObject.getJSONArray(name);
                        for (Object o : a) {
                            if (o instanceof JSONObject) {
                                replenishNullField((JSONObject) o, clazz.getComponentType());
                            }
                        }
                    } else if (Collection.class.isAssignableFrom(type)) {
                        Type genericType = field.getGenericType();
                        if (genericType == null) continue;
                        // 如果是泛型参数的类型
                        if (genericType instanceof ParameterizedType) {
                            ParameterizedType pt = (ParameterizedType) genericType;
                            //得到泛型里的class类型对象
                            Class<?> genericClazz = (Class<?>) pt.getActualTypeArguments()[0];
                            JSONArray a = jsonObject.getJSONArray(name);
                            for (Object o : a) {
                                if (o instanceof JSONObject) {
                                    replenishNullField((JSONObject) o, genericClazz);
                                }
                            }
                        }

                    } else if (!ClassUtil.isSimpleValueType(type)) {
                        if (value instanceof JSONObject) {
                            replenishNullField((JSONObject) value, type);
                        }
                    }
                } catch (Exception e) {
                    System.out.println(field.getName() + ":error:" + e.getMessage());
                }
            }
        }
    }
}
