package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum TimeSheetUserType implements ConvertedEnum<Integer> {
    TALENT(0),
    CLIENT(1);

    private final int dbValue;

    TimeSheetUserType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TimeSheetUserType, Integer> resolver = new ReverseEnumResolver<>(TimeSheetUserType.class, TimeSheetUserType::toDbValue);

    public static TimeSheetUserType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
