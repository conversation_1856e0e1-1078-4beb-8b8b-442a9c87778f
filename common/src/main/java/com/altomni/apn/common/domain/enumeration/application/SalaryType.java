package com.altomni.apn.common.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The SalaryType enumeration.
 */
public enum SalaryType implements ConvertedEnum<Integer> {

    BASE_SALARY(0),
    RETENTION_BONUS(1),
    SIGN_ON_BONUS(2),
    ANNUAL_BONUS(3),
    RELOCATION_PACKAGE(4),
    EXTRA_FEE(5),
    STOCK(6);

    private final Integer dbValue;

    SalaryType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<SalaryType, Integer> resolver =
        new ReverseEnumResolver<>(SalaryType.class, SalaryType::toDbValue);

    public static SalaryType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
