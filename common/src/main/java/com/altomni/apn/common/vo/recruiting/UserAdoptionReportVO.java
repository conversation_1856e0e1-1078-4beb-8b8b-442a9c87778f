package com.altomni.apn.common.vo.recruiting;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAdoptionReportVO extends TeamAdoptionReportVO{
    private Long userId;

    private String userName;

    private Long activationDuration;

    private Integer callCount;

    private Integer uniqueCalledTalentCount;

    private Long noteCount;

    private Integer uniqueNotedTalentCount;

    private Long emailCount;

    private Long uniqueEmailedTalentCount;

    private Long submitToJobCount;

    private Long interviewCount;

    private Long uniqueInterviewedTalentCount;

    private Long onboardTalentCount;

}
