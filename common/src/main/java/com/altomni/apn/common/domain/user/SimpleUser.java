package com.altomni.apn.common.domain.user;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.Email;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

/**
 * A user.
 */
@ApiModel(description = "<PERSON><PERSON><PERSON> is recruiter who use ATS")
@Entity
@Table(name = "user")
public class SimpleUser
//        extends AbstractAuditingEntity
        implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "[Internal] uid is used for Spring security to identify user internally. It has the format of <userId>,<tenantId>")
    @Column(name = "uid")
    @JsonIgnore
    private String uid;

    @ApiModelProperty(value = "Optional username. If exists, need to be unique.")
//    @Pattern(regexp = Constants.USERNAME_REGEX)
    @Size(min = 1, max = 50,message = "username length can not be exceed 50 characters")
    @Column(name = "username",length = 50, unique = true)
    private String username;

    @ApiModelProperty(value = "hashed password")
    @JsonIgnore
    @NotNull
    @Size(min = 60, max = 60,message ="password length can not be exceed 60 characters" )
    @Column(name = "password_hash",length = 60)
    private String password;

    @ApiModelProperty(value = "first name")
    @Size(max = 50)
    @Column(name = "first_name", length = 50)
    private String firstName;

    @ApiModelProperty(value = "last name")
    @Size(max = 50)
    @Column(name = "last_name", length = 50)
    private String lastName;

    @ApiModelProperty(value = "email address. need to be unique.")
    @Email(message = "email format is not correct")
    @Size(min = 5, max = 100)
    @Column(name = "email", length = 100, unique = true)
    private String email;

    @ApiModelProperty(value = "Whether user is activated. Default is true. Read Only.")
    @NotNull
    @Column(name = "activated", nullable = false)
    private boolean activated = true;

    @ApiModelProperty(value = "Preferred language. e.g. en-US, zh-CN")
    @Size(min = 2, max = 5)
    @Column(name = "lang_key", length = 5)
    private String langKey;

    @ApiModelProperty(value = "url link to user's image")
    @Size(max = 256)
    @Column(name = "image_url", length = 256)
    private String imageUrl;

    @ApiModelProperty(value = "phone number, this is used for consumer registered with phone.")
    @Column(name = "phone")
    private String phone;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @ApiModelProperty(value = "Note info.")
    @Column(name = "note")
    private String note;

//    @JsonIgnore
//    public String getLogin() {
//        if (this.username != null) {
//            return username;
//        }
//        return this.email;
//    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public boolean getActivated() {
        return activated;
    }

    public void setActivated(boolean activated) {
        this.activated = activated;
    }

    public String getLangKey() {
        return langKey;
    }

    public void setLangKey(String langKey) {
        this.langKey = langKey;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        SimpleUser simpleUser = (SimpleUser) o;
        return !(simpleUser.getId() == null || getId() == null) && Objects.equals(getId(), simpleUser.getId());
    }




    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "SimpleUser{" +
            "id=" + id +
            ", username='" + username + '\'' +
            ", password='" + password + '\'' +
            ", firstName='" + firstName + '\'' +
            ", lastName='" + lastName + '\'' +
            ", email='" + email + '\'' +
            ", activated=" + activated +
            ", langKey='" + langKey + '\'' +
            ", imageUrl='" + imageUrl + '\'' +
            ", phone='" + phone + '\'' +
            ", note='" + note + '\'' +
            "} " + super.toString();
    }
}
