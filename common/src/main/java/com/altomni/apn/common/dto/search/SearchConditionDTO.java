package com.altomni.apn.common.dto.search;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.search.CommonPoolTalentType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Pageable;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class SearchConditionDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "search condition in json string")
    private List<SearchParam> search;

    @ApiModelProperty(value = "search filter with 'AND' relation, in json string")
    private List<SearchParam> filter;

    private List<String> folderIds;

    //职位文件夹中的候选人使用，搜是否是folderIds中的候选人
    private Boolean belongToRelateJobFolder;

    private ModuleType module;

    private CommonPoolTalentType commonPoolType;

    private String timezone;

    private LanguageEnum language = LanguageEnum.EN;

    public static void checkPageable(Pageable pageable) {
        int from = 0;
        int size = 0;
        from = (pageable.getPageNumber() - 1) * pageable.getPageSize() <= -1 ? 0 : (pageable.getPageNumber() - 1) * pageable.getPageSize();
        size = pageable.getPageSize() <= 0 ? 10 : pageable.getPageSize();
        if (from > (Constants.LIST_SEARCH_AREA - size) || (from + size) > Constants.LIST_SEARCH_AREA) {
            throw new CustomParameterizedException("The search area cannot exceed " + Constants.LIST_SEARCH_AREA);
        }
    }

    public static void checkPageable(Pageable pageable, boolean isTenantOwnerDataRestriction) {
        int from = 0;
        int size = 0;
        from = (pageable.getPageNumber() - 1) * pageable.getPageSize() <= -1 ? 0 : (pageable.getPageNumber() - 1) * pageable.getPageSize();
        size = pageable.getPageSize() <= 0 ? 10 : pageable.getPageSize();
        if (isTenantOwnerDataRestriction && (from + size) > Constants.LIST_SEARCH_AREA) {
            throw new CustomParameterizedException("The search area cannot exceed " + Constants.LIST_SEARCH_AREA);
        }
    }
}

