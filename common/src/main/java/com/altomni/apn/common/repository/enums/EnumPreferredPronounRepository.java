package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumPreferredPronoun;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the Enum job functions entity.
 */
@Repository
public interface EnumPreferredPronounRepository extends JpaRepository<EnumPreferredPronoun, Long> {

}
