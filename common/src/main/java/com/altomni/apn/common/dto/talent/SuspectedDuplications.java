package com.altomni.apn.common.dto.talent;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SuspectedDuplications {
    private String _id;
    private String _index;
    private BigDecimal _similarity = new BigDecimal(0);
    private String fullName;
    private String companyName;
    private ConfidentialInfoDto confidentialInfo;
    private Boolean confidentialTalentViewAble = true;
    private String title;
    private String lastModifiedDate;
    private DuplicateContacts duplicateContacts = new DuplicateContacts();
    private DuplicateProfile duplicateProfile = new DuplicateProfile();
    private Boolean duplicateResume = false;
    //候选人拥有者
    private String talentOwner;
    //用于展示疑似重复的分机号，不影响查重判断
    private List<JSONObject> suspectedDuplicatedPhones;
    // 重复候选人的工作经历，用于前端展示候选人是否是禁猎客户的员工
    private List<TalentExperienceDTO> experiences;

    public void confidentialDuplicateInfo(String lastName) {
        this.fullName = "***" + lastName;
        this.confidentialTalentViewAble = false;
        this.companyName = null;
        this.title = null;
    }
}
