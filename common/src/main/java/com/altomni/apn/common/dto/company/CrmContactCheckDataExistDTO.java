package com.altomni.apn.common.dto.company;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class CrmContactCheckDataExistDTO implements Serializable {

    private Long contactId;

    private Long tenantId;

    private Long companyId;

}
