package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CommonDataStatus implements ConvertedEnum<Byte> {
    AVAILABLE((byte) 0),
    INVALID((byte) 1);

    private final Byte dbValue;

    CommonDataStatus(byte dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Byte toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<CommonDataStatus, Byte> resolver =
            new ReverseEnumResolver<>(CommonDataStatus.class, CommonDataStatus::toDbValue);

    public static CommonDataStatus fromDbValue(Byte dbValue) {
        return resolver.get(dbValue);
    }
}