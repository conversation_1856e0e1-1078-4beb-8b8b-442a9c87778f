package com.altomni.apn.common.vo.recruiting;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiApplicationCommonCountVO extends RecruitingKpiCommonCountVO {

    private Long currentCountNum;

    private Long aiRecommendNum;

    private Long currentAiRecommendNum;

    private Long precisionAiRecommendNum;

    private Long currentPrecisionAiRecommendNum;

    private String currentIds;

    private Instant companyCreatedDate;

    private Instant requestDate;

    private Integer active;

    private Integer stayedOver;

    private Integer currentStayedOver;

    @JsonIgnore
    private Integer salesLeadRole;

    @JsonIgnore
    private String businessInfo;

    @JsonIgnore
    private Long companyNoteCount;

    @JsonIgnore
    private Long companyUserId;

    @JsonIgnore
    private String companyUserName;


}
