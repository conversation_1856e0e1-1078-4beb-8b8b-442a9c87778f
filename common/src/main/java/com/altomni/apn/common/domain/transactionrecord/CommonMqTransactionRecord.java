package com.altomni.apn.common.domain.transactionrecord;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;

@Table(name="t_mq_transaction_record")
@ApiModel(value = "mq分布式事务记录表",description = "")
@Entity
@Data
public class CommonMqTransactionRecord extends AbstractAuditingEntity implements Serializable {

    /** id */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id ;

    /** 业务类型 */
    @ApiModelProperty(name = "业务类型")
    @Column(name = "bus_type")
    private Integer busType ;

    /** 业务id */
    @ApiModelProperty(name = "业务id")
    @Column(name = "bus_id")
    private BigInteger busId ;

    /** 发送状态 1发送中 2发送失败 3发送成功 */
    @ApiModelProperty(name = "发送状态 1发送中 2发送失败 3发送成功")
    @Column(name = "send_status")
    private Integer sendStatus ;

    /** 发送内容 */
    @ApiModelProperty(name = "发送内容")
    @Column(name = "send_content")
    private String sendContent ;

    /** 发送次数 */
    @ApiModelProperty(name = "发送次数")
    @Column(name = "send_count")
    private Integer sendCount ;
}