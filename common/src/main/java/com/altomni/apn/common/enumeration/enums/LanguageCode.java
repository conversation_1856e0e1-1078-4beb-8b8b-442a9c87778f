package com.altomni.apn.common.enumeration.enums;

public enum LanguageCode {
    ZH("zh", "汉语"),
    ZH_HANT("zh-Hant", "繁体汉语"),
    EN("en", "英语"),
    JA("ja", "日语"),
    RU("ru", "俄语"),
    DE("de", "德语"),
    FR("fr", "法语"),
    IT("it", "意大利语"),
    PL("pl", "波兰语"),
    TH("th", "泰语"),
    HI("hi", "印地语"),
    ID("id", "印尼语"),
    ES("es", "西班牙语"),
    PT("pt", "葡萄牙语"),
    KO("ko", "朝鲜语"),
    VI("vi", "越南语");

    private final String code;
    private final String name;

    LanguageCode(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }
}

