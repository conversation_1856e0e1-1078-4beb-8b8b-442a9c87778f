package com.altomni.apn.common.domain.enumeration.message;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum MessageDeleteEnum implements ConvertedEnum<Integer> {

    NOT_DELETED(0),
    DELETED(1);

    private Integer dbValue;

    MessageDeleteEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<MessageDeleteEnum, Integer> resolver = new ReverseEnumResolver<>(MessageDeleteEnum.class, MessageDeleteEnum::toDbValue);

    public static MessageDeleteEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}