package com.altomni.apn.common.domain.enumeration.application;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * The NodeType enumeration.
 */
public enum NodeType implements ConvertedEnum<Integer> {
    //以下枚举名被当做流程状态传入了talent es，修改枚举名字时需注意
    //talent同步es时，因为nodeStatus保存着终止状态，在终止时会将lastNoteTypeId传ELIMINATED(4) 实现一个字段可以搜索终止状态的流程
    UNKNOWN(-1, "Unknown"),

    SUBMIT_TO_JOB(10, "Submit to Job"),

    SUBMIT_TO_CLIENT(20, "Submit to Client"),

    INTERVIEW(30, "Interview"),

    OFFER(40, "Offer"),

    OFFER_ACCEPT(41, "Offer Accept"),

    COMMISSION(50, "Commission"),

    ON_BOARD(60, "On Board"),

    ELIMINATED(70, "eliminated"), //只用于记录 job snapshot

//    STOPPED_SHARING(80, "Stopped Sharing"), //只用于给agency c端返回，不存数据库

    OFF_BOARDED(100, "Off Boarded"),;

    private final Integer dbValue;

    private final String description;

    public static final List<Integer> ALL_NODE_TYPES = new ArrayList<>();

    static {
        for (NodeType nodeType: NodeType.values()) {
            ALL_NODE_TYPES.add(nodeType.toDbValue());
        }
    }

    NodeType(Integer dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDescription() {
        return description;
    }

    // static resolving:
    public static final ReverseEnumResolver<NodeType, Integer> resolver =
        new ReverseEnumResolver<>(NodeType.class, NodeType::toDbValue);

    public static NodeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static List<String> getAllNames() {
        return Arrays.stream(NodeType.values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }
}
