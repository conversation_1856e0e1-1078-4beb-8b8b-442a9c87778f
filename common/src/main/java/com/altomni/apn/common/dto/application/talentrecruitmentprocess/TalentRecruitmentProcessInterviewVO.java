package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.altomni.apn.common.domain.enumeration.application.InterviewType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessInterviewVO extends AuditingUser implements Serializable {

    private static final long serialVersionUID = -1312677872577027125L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private Integer progress;

    private Instant fromTime;

    private Instant toTime;

    private InterviewType interviewType;

    private String timeZone;

    private String note;

    private Integer finalRound;

    private Instant noteLastModifiedDate;

    private Long noteLastModifiedByUserId;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

    private TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate;
}
