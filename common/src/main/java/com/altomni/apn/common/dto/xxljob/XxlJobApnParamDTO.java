package com.altomni.apn.common.dto.xxljob;

import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnumConverter;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnumConverter;
import lombok.Data;

import javax.persistence.Convert;
import java.time.Instant;
import java.util.Map;

@Data
public class XxlJobApnParamDTO {

    private Long referenceId;

    private Long tenantId;

    private Long userId;

    private Instant sendTime;

    private String timezone;

    private String reminderConfig;

    private String token;

    private String cron;

    private Map<String, Object> xxlJobParam;

    @Convert(converter = XxlJobRelationTypeEnumConverter.class)
    private XxlJobRelationTypeEnum xxlJobType;

    @Convert(converter = MessageTypeEnumConverter.class)
    private MessageTypeEnum type;

    public MessageTypeEnum getType() {
        return xxlJobType.getMessageType();
    }

}
