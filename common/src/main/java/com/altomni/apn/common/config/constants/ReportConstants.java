package com.altomni.apn.common.config.constants;

import java.util.Collections;
import java.util.List;

import static java.util.Arrays.asList;

/**
 * Application constants.
 */
public final class ReportConstants {

    private ReportConstants() { }

    // ExcelUtils Constants
    /** excel default sheet name */
    public static final String SHEET_NAME = "sheet-1";

    /** grand total */
    public static final String GRAND_TOTAL = "Grand Total";

    public static final List<String> LINKEDIN_STATS_HEADERS =
        Collections.unmodifiableList(asList("username", "totalCnt"));

    public static final List<String> LINKEDIN_STATS_FIELDS =
        Collections.unmodifiableList(asList("username", "totalCnt"));

    public static final List<Integer> LINKEDIN_STATS_COL = Collections.unmodifiableList(Collections.singletonList(1));

    public static final List<String> ONBOARDING_COMPLETION_DOCUMENTS_HEADERS =
            Collections.unmodifiableList(asList("Document", "Status", "Assigned On", "Activity"));

    public static final List<String> ONBOARDING_COMPLETION_DOCUMENTS_FIELDS =
            Collections.unmodifiableList(asList("documentSourceName", "approvalDetails", "assignedOnDate", "operationDetails"));

}
