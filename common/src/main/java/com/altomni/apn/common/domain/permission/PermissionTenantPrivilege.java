package com.altomni.apn.common.domain.permission;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

/**
 *
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "permission_tenant_privilege")
@AllArgsConstructor
@NoArgsConstructor
public class PermissionTenantPrivilege extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "privilege_id")
    private Long privilegeId;

    @Column(name = "is_show")
    private Boolean isShow;

    public PermissionTenantPrivilege(Long tenantId, Long privilegeId, Boolean isShow) {
        this.tenantId = tenantId;
        this.privilegeId = privilegeId;
        this.isShow = isShow;
    }
}
