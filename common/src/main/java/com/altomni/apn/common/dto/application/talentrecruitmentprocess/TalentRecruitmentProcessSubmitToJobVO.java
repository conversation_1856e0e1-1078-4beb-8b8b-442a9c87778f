package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.dto.RecommendFeedback;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessSubmitToJobVO extends AuditingUser implements Serializable {

    private static final long serialVersionUID = -3826204108918323653L;

    private Long id;

    private Long talentId;

    private Long jobId;

    private Long recruitmentProcessId;

    private Long talentRecruitmentProcessId;

    private Long talentResumeRelationId;

    private String note;

    private String skills;

    private String recommendComments;

    private Instant noteLastModifiedDate;

    private Long noteLastModifiedByUserId;

    private TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

    //推荐候选人/工作时传入
    private RecommendFeedback recommendFeedback;

    //流程中包含该字段表示fte类型
    private Boolean isSubstituteTalent;
}
