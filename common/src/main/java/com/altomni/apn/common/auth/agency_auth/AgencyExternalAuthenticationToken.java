package com.altomni.apn.common.auth.agency_auth;

import com.altomni.apn.common.dto.user.AgencyLoginUserDTO;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

/**
 * agency 系统中，外部用户（代理）的登录认证信息
 */
public class AgencyExternalAuthenticationToken extends UsernamePasswordAuthenticationToken {

    public AgencyExternalAuthenticationToken(AgencyLoginUserDTO principal, String jwt) {
        super(principal, jwt, principal.getAuthorities());
    }

    @Override
    public AgencyLoginUserDTO getPrincipal() {
        return (AgencyLoginUserDTO) super.getPrincipal();
    }

    @Override
    public String getCredentials() {
        return (String) super.getCredentials();
    }
}
