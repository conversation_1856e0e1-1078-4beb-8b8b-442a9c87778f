package com.altomni.apn.common.aop.request.custom;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.ArgsSignGenerator;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;

public class RequestBodySignGenerator implements ArgsSignGenerator {
    @Override
    public String generateSign(Object[] args) {
        if (ArrayUtil.isEmpty(args)) {
            return "empty";
        }

        StringBuilder sb = new StringBuilder();
        for (Object arg : args) {
            if (arg != null) {
                if (arg instanceof HttpServletRequest) {
                    try {
                        HttpServletRequest request = (HttpServletRequest) arg;
                        String requestBody = getRequestBody(request);
                        JSONObject bodyObj = JSONUtil.parseObj(requestBody);
                        bodyObj.remove("resumes");
                        sb.append(JSONUtil.toJsonStr(bodyObj));
                    } catch (Exception e) {
                        //异常状态随机值赋值 避免彻底锁死创建talent功能
                        sb.append(RandomUtil.randomNumbers(16));
                    }
                } else {
                    sb.append(JSONUtil.toJsonStr(arg));
                }
            }
        }
        return SecureUtil.md5(sb.toString());
    }

    private String getRequestBody(HttpServletRequest request) throws IOException {
        // 首先检查是否已经缓存了请求体
        String cachedBody = (String) request.getAttribute("cachedRequestBody");
        if (cachedBody != null) {
            return cachedBody;
        }

        // 否则读取请求体
        StringBuilder buffer = new StringBuilder();
        BufferedReader reader = request.getReader();
        String line;
        while ((line = reader.readLine()) != null) {
            buffer.append(line);
        }

        // 缓存请求体
        cachedBody = buffer.toString();
        request.setAttribute("cachedRequestBody", cachedBody);

        return cachedBody;
    }
}
