package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.domain.enumeration.calendar.CalendarEventAttendeeReminderTypeConverter;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarEventAttendeeReminderTypeEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarEventAttendeeTypeEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarEventAttendeeTypeEnumConverter;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Convert;

@Data
public class CalendarEventAttendeeDTO {

    private Long userId;

    private String email;

    @Column(name = "is_organizer")
    @Convert(converter = CalendarEventAttendeeTypeEnumConverter.class)
    private CalendarEventAttendeeTypeEnum isOrganizer;

    @Column(name = "is_reminder")
    @Convert(converter = CalendarEventAttendeeReminderTypeConverter.class)
    private CalendarEventAttendeeReminderTypeEnum isReminder;

}
