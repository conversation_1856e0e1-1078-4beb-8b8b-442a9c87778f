package com.altomni.apn.common.domain.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderStatus;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderStatusConverter;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderUserRole;
import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderUserRoleConverter;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "talent_association_job_folder")
public class TalentAssociationJobFolder extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Column(name = "job_id", nullable = false)
    private Long jobId;

    @Size(max = 32)
    @NotNull
    @Column(name = "folder_id", nullable = false, length = 32)
    private String folderId;

    @NotNull
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Convert(converter = RelateJobFolderUserRoleConverter.class)
    @NotNull
    @Column(name = "role", nullable = false)
    private RelateJobFolderUserRole role;

    @Convert(converter = RelateJobFolderStatusConverter.class)
    @NotNull
    @Column(name = "status", nullable = false)
    private RelateJobFolderStatus status;

}