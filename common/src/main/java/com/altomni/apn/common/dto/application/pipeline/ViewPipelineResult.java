package com.altomni.apn.common.dto.application.pipeline;

import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ViewPipelineResult implements Serializable {

    private static final long serialVersionUID = -3997206396726775070L;

    private Set<FullNameUserDTO> hiringManagers;

    private Set<FullNameUserDTO> hrCoordinates;

    private Set<FullNameUserDTO> msps;

    private Set<FullNameUserDTO> recruiters;

    private Set<FullNameUserDTO> accountManagers;

    private Set<FullNameUserDTO> cooperateAccountManager;

    private List<MyCandidateStatusFilter> nodeTypes;

    private List<Object> elements;

}
