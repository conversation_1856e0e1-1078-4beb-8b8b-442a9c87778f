package com.altomni.apn.common.datapermission.config;

import com.altomni.apn.common.datapermission.db.DataPermissionDatabaseHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Inject the bean DataPermissionDatabaseHandler to Spring
 *
 * <AUTHOR>
 */
@Configuration
public class DataPermissionAutoConfiguration {
    @Bean
    public DataPermissionDatabaseHandler dataPermissionDatabaseHandler(){
        return DataPermissionDatabaseHandler.getInstance();
    }
}
