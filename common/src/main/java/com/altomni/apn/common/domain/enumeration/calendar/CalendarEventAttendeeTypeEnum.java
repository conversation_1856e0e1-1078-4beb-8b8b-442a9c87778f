package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CalendarEventAttendeeTypeEnum implements ConvertedEnum<Integer> {

    ORGANIZER(1),
    PARTICIPANT(0);

    private Integer dbValue;

    CalendarEventAttendeeTypeEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CalendarEventAttendeeTypeEnum, Integer> resolver = new ReverseEnumResolver<>(CalendarEventAttendeeTypeEnum.class, CalendarEventAttendeeTypeEnum::toDbValue);

    public static CalendarEventAttendeeTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
