package com.altomni.apn.common.vo.recruiting;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiCommonVO extends RecruitingKpiCommonCountVO {

    private Long openings;

    private String jobIds;

    private Long talentNum;

    private String talentIds;

    private Long submitToJobNum;

    private String submitToJobIds;

    private Long submitToJobCurrentNum;

    private String submitToJobCurrentIds;

    private Long submitToClientNum;

    private String submitToClientIds;

    private Long submitToClientCurrentNum;

    private String submitToClientCurrentIds;

    private Long firstInterviewNum;

    private Long currentFirstInterviewNum;

    private String firstInterviewIds;

    private Long secondInterviewNum;

    private Long currentSecondInterviewNum;

    private String secondInterviewIds;

    private Long finalInterviewNum;

    private Long currentFinalInterviewNum;

    private String finalInterviewIds;

    private Long interviewNum;

    private Long currentInterviewNum;

    private Long interviewNumProcess;

    private Long currentInterviewNumProcess;

    private Long interviewAppointments;

    private Long currentInterviewAppointments;

    private Long twoOrMoreInterviews;

    private Long currentTwoOrMoreInterviews;

    private String interviewIds;

    private Long uniqueInterviewTalentNum;

    private Long offerNum;

    private String offerIds;

    private Long offerCurrentNum;

    private String offerCurrentIds;

    private Long offerAcceptNum;

    private String offerAcceptIds;

    private Long offerAcceptCurrentNum;

    private String offerAcceptCurrentIds;

    private Long onboardNum;

    private String onboardIds;

    private Long onboardCurrentNum;

    private String onboardCurrentIds;

    private Long eliminateNum;

    private Long eliminateCurrentNum;

    private String eliminateIds;

    private Integer stayedOver24Hrs;

    private Integer stayedOver72Hrs;

    private Integer currentStayedOver24Hrs;

    private Integer currentStayedOver72Hrs;

    private Long submitToJobNumAIRecommend;

    private Long submitToClientNumAIRecommend;

    private Long firstInterviewNumAIRecommend;

    private Long secondInterviewNumAIRecommend;

    private Long finalInterviewNumAIRecommend;

    private Long interviewNumAIRecommend;

    private Long twoOrMoreInterviewsAIRecommend;

    private Long interviewAppointmentsAIRecommend;

    private Long offerNumAIRecommend;

    private Long offerAcceptNumAIRecommend;

    private Long onboardNumAIRecommend;

    private Long eliminateNumAIRecommend;

    private Long submitToJobNumPrecisionAIRecommend;

    private Long submitToClientNumPrecisionAIRecommend;

    private Long firstInterviewNumPrecisionAIRecommend;

    private Long secondInterviewNumPrecisionAIRecommend;

    private Long finalInterviewNumPrecisionAIRecommend;

    private Long interviewNumPrecisionAIRecommend;

    private Long twoOrMoreInterviewsPrecisionAIRecommend;

    private Long interviewAppointmentsPrecisionAIRecommend;

    private Long offerNumPrecisionAIRecommend;

    private Long offerAcceptNumPrecisionAIRecommend;

    private Long onboardNumPrecisionAIRecommend;

    private Long eliminateNumPrecisionAIRecommend;

    private String aiConversionRate;

    private String overallConversionRate;

    private String aiInterviewConversionRate;

    private String overallInterviewConversionRate;

    private Long interviewNumProcessPrecisionAIRecommend;

    private Long currentInterviewNumProcessPrecisionAIRecommend;

    private Long interviewNumProcessAIRecommend;

    private Long currentInterviewNumProcessAIRecommend;

}
