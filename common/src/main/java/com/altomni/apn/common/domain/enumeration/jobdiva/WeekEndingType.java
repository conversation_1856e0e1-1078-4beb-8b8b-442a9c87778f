package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum WeekEndingType implements ConvertedEnum<Integer>
{
    MONDAY(1),
    TUESDAY(2),
    WEDNESDAY(3),
    THURSDAY(4),
    FRIDAY(5),
    SATURDAY(6),
    SUNDAY(7)
    ;//SUNDAY,MONDAY,TUESDAY,WEDNESDAY,THURSDAY,FRIDAY,SATURDAY


    private final int dbValue;

    WeekEndingType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<WeekEndingType, Integer> resolver = new ReverseEnumResolver<>(WeekEndingType.class, WeekEndingType::toDbValue);

    public static WeekEndingType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
