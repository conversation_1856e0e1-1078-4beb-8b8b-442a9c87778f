package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * taxpayer type enum
 */
public enum TaxPayerType implements ConvertedEnum<Integer> {

    CLIENT(1, "客户方"),
    OUR_COMPANY(2, "我方")
    ;
    private final int dbValue;

    private final String description;

    TaxPayerType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TaxPayerType, Integer> resolver = new ReverseEnumResolver<>(TaxPayerType.class, TaxPayerType::toDbValue);

    public static TaxPayerType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
