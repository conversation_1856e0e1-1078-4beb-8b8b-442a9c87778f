package com.altomni.apn.common.dto.email;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MailSearchDTO {

    private String inReplyToMessageId;
    private List<String> customerId1;
    private List<String> customerId2;
    private String rootMessageId;
    private String uid;
    private LocalDate fromDate;
    private LocalDate toDate;
    private List<Long> userIds;
    private Long tenantId;

}
