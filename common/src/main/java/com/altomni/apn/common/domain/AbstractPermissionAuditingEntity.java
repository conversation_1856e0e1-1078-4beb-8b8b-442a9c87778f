package com.altomni.apn.common.domain;

import com.altomni.apn.common.config.OwnedByTeam;
import com.altomni.apn.common.config.OwnedByUser;
import com.altomni.apn.common.config.PermissionAuditingEntityListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;
import java.io.Serializable;

/**
 * Base abstract class for entities which will hold definitions for created, last modified, created by,
 * last modified by attributes.
 */
@MappedSuperclass
@EntityListeners({PermissionAuditingEntityListener.class})
public abstract class AbstractPermissionAuditingEntity extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;


    @OwnedByUser
    @Column(name = "puser_id", nullable = false, updatable = false)
    private Long permissionUserId;

    @OwnedByTeam
    @Column(name = "pteam_id", nullable = false, updatable = false)
    private Long permissionTeamId;

    public Long getPermissionUserId() {
        return permissionUserId;
    }

    public void setPermissionUserId(Long permissionUserId) {
        this.permissionUserId = permissionUserId;
    }

    public Long getPermissionTeamId() {
        return permissionTeamId;
    }

    public void setPermissionTeamId(Long permissionTeamId) {
        this.permissionTeamId = permissionTeamId;
    }
}
