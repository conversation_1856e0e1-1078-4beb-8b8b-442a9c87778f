package com.altomni.apn.common.vo.recruiting.v2;

import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonCountVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReserveInterviewVO  extends RecruitingKpiCommonCountVO {

    // 预约面试相关指标
    private Long reserveInterviewTotal;
    private Long reserveCurrentInterviewTotal;
    private Long reserveInterviewAiRecommendCountNum;
    private Long reserveInterviewCurrentAiRecommendNum;
    private Long reserveInterviewPrecisionAiRecommendNum;
    private Long reserveInterviewCurrentPrecisionAiRecommendNum;
}
