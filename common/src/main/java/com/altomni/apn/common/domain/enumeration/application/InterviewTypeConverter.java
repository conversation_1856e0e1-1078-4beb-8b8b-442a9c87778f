package com.altomni.apn.common.domain.enumeration.application;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InterviewTypeConverter extends AbstractAttributeConverter<InterviewType, Integer> {
    public InterviewTypeConverter() {
        super(InterviewType::toDbValue, InterviewType::fromDbValue);
    }
}
