package com.altomni.apn.common.datapermission.db;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.altomni.apn.common.datapermission.rule.DataPermissionRule;
import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.HibernateUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.ExistsExpression;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * Sql parse tool: https://github.com/JSQLParser/JSqlParser
 * The implement of data security interceptor.
 * Rewrite sql and add more filters to where clause in terms of data security rules.
 * <AUTHOR>
 */
@Slf4j
@Component
public class DataPermissionDatabaseHandler {

    private boolean isSelect = true;

    private static DataPermissionDatabaseHandler dataPermissionDatabaseHandler;

    public static DataPermissionDatabaseHandler getInstance(){
        if (dataPermissionDatabaseHandler == null){
            dataPermissionDatabaseHandler = new DataPermissionDatabaseHandler();
        }
        return dataPermissionDatabaseHandler;
    }

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    List<TeamDataPermissionRule> rules = null;

    TeamDataPermissionRespDTO deptDataPermission = null;

    /*
    @Resource
    private PermissionTableRepository permissionTableRepository;

    @Resource
    private TeamDataPermissionRule teamDataPermissionRule;

    @PostConstruct
    public void initPermissionRule(){
        List<PermissionTable> involvedPermissionTables = permissionTableRepository.findInvolvedPermissionByTenantId(4L);
        deptDataPermissionRule.clear();
        involvedPermissionTables.forEach(table -> {
            deptDataPermissionRule.addUserColumn(table.getName(), table.getUserOwnerColumn());
            deptDataPermissionRule.addDeptColumn(table.getName(), table.getDepartmentOwnerColumn());
        });

        deptDataPermissionRule.addUserColumn("job", "user_id");
        deptDataPermissionRule.addDeptColumn("job", "team_id");

        // 获得 Mapper 对应的数据权限的规则
        List<DataPermissionRule> rules = Arrays.asList(deptDataPermissionRule);
        ContextHolder.init(rules);
    }*/

    private void initDataPermission(){
        Long tenantId = SecurityUtils.getTenantId();
        Long userId = SecurityUtils.getUserId();
        rules = cachePermission.getPermissionRulesByTenantIdFromCacheOnly(tenantId);
        if (Objects.isNull(rules)){
            rules = initiationService.initiatePermissionRulesByTenantId(tenantId).getBody();
            /*try {
                TimeUnit.MILLISECONDS.sleep(50);
            } catch (InterruptedException e) {
                log.error("error", e);
            }
            rules = cachePermissionReadOnly.getPermissionRulesByTenantIdFromCacheOnly(SecurityUtils.getTenantId());*/
        }
        if (SecurityUtils.isSystemAdmin()){
            deptDataPermission = cachePermission.getTeamDataPermissionByAdminUserIdFromCacheOrDefault(userId);
        }else{
            deptDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(userId);
            if (Objects.isNull(deptDataPermission)){
                deptDataPermission = initiationService.initiateDataPermissionByUserId(tenantId, userId).getBody();
            }
        }
    }

    /**
     * Rewrite select sql
     */
    public String processSelect(Select select) {
        this.initDataPermission();
        this.isSelect = true;
        processSelectBody(select.getSelectBody());
        List<WithItem> withItemsList = select.getWithItemsList();
        if (!CollectionUtils.isEmpty(withItemsList)) {
            withItemsList.forEach(this::processSelectBody);
        }
        return select.toString();
    }

    protected void processSelectBody(SelectBody selectBody) {
        if (selectBody == null) {
            return;
        }
        if (selectBody instanceof PlainSelect) {
            processPlainSelect((PlainSelect) selectBody);
        } else if (selectBody instanceof WithItem) {
            WithItem withItem = (WithItem) selectBody;
            processSelectBody(withItem.getSubSelect().getSelectBody());
        } else {
            SetOperationList operationList = (SetOperationList) selectBody;
            List<SelectBody> selectBodys = operationList.getSelects();
            if (CollectionUtils.isNotEmpty(selectBodys)) {
                selectBodys.forEach(this::processSelectBody);
            }
        }
    }

    /**
     * rewrite update sql
     */
    public String processUpdate(Update update) {
        this.initDataPermission();
        this.isSelect = false;
        final Table table = update.getTable();
        update.setWhere(this.builderExpression(update.getWhere(), table));
        return update.toString();
    }

    /**
     * rewrite delete sql
     */
    public String processDelete(Delete delete) {
        this.initDataPermission();
        this.isSelect = false;
        delete.setWhere(this.builderExpression(delete.getWhere(), delete.getTable()));
        return delete.toString();
    }

    /**
     * deal with PlainSelect
     */
    protected void processPlainSelect(PlainSelect plainSelect) {
        FromItem fromItem = plainSelect.getFromItem();
        Expression where = plainSelect.getWhere();
        processWhereSubSelect(where);
        if (fromItem instanceof Table) {
            Table fromTable = (Table) fromItem;
            plainSelect.setWhere(builderExpression(where, fromTable));
        } else {
            processFromItem(fromItem);
        }
        List<SelectItem> selectItems = plainSelect.getSelectItems();
        if (CollectionUtils.isNotEmpty(selectItems)) {
            selectItems.forEach(this::processSelectItem);
        }
        List<Join> joins = plainSelect.getJoins();
        if (CollectionUtils.isNotEmpty(joins)) {
            processJoins(joins);
        }
    }

    /**
     * Handle the subselect in where clause
     *
     * Support the following operators:
     * 1. in
     * 2. =
     * 3. >
     * 4. <
     * 5. >=
     * 6. <=
     * 7. <>
     * 8. EXISTS
     * 9. NOT EXISTS
     *
     * requirements:
     * 1. The subselect must be put in parentheses
     * 2. The subselect must be put in the right side of the compare operator
     *
     * @param where the where clause
     */
    protected void processWhereSubSelect(Expression where) {
        if (where == null) {
            return;
        }
        if (where instanceof FromItem) {
            processFromItem((FromItem) where);
            return;
        }
        if (where.toString().indexOf("SELECT") > 0) {
            // Have subselect
            if (where instanceof BinaryExpression) {
                // Compare operators: and, or, etc.
                BinaryExpression expression = (BinaryExpression) where;
                processWhereSubSelect(expression.getLeftExpression());
                processWhereSubSelect(expression.getRightExpression());
            } else if (where instanceof InExpression) {
                // in
                InExpression expression = (InExpression) where;
                ItemsList itemsList = expression.getRightItemsList();
                if (itemsList instanceof SubSelect) {
                    processSelectBody(((SubSelect) itemsList).getSelectBody());
                }
            } else if (where instanceof ExistsExpression) {
                // exists
                ExistsExpression expression = (ExistsExpression) where;
                processWhereSubSelect(expression.getRightExpression());
            } else if (where instanceof NotExpression) {
                // not exists
                NotExpression expression = (NotExpression) where;
                processWhereSubSelect(expression.getExpression());
            } else if (where instanceof Parenthesis) {
                Parenthesis expression = (Parenthesis) where;
                processWhereSubSelect(expression.getExpression());
            }
        }
    }

    protected void processSelectItem(SelectItem selectItem) {
        if (selectItem instanceof SelectExpressionItem) {
            SelectExpressionItem selectExpressionItem = (SelectExpressionItem) selectItem;
            if (selectExpressionItem.getExpression() instanceof SubSelect) {
                processSelectBody(((SubSelect) selectExpressionItem.getExpression()).getSelectBody());
            } else if (selectExpressionItem.getExpression() instanceof Function) {
                processFunction((Function) selectExpressionItem.getExpression());
            }
        }
    }

    /**
     * Handle functions
     * Support: 1. select fun(args..) 2. select fun1(fun2(args..),args..)
     *
     * @param function
     */
    protected void processFunction(Function function) {
        ExpressionList parameters = function.getParameters();
        if (parameters != null) {
            parameters.getExpressions().forEach(expression -> {
                if (expression instanceof SubSelect) {
                    processSelectBody(((SubSelect) expression).getSelectBody());
                } else if (expression instanceof Function) {
                    processFunction((Function) expression);
                }
            });
        }
    }

    /**
     * Handle subjoin
     */
    protected void processFromItem(FromItem fromItem) {
        if (fromItem instanceof SubJoin) {
            SubJoin subJoin = (SubJoin) fromItem;
            if (subJoin.getJoinList() != null) {
                processJoins(subJoin.getJoinList());
            }
            if (subJoin.getLeft() != null) {
                processFromItem(subJoin.getLeft());
            }
        } else if (fromItem instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) fromItem;
            if (subSelect.getSelectBody() != null) {
                processSelectBody(subSelect.getSelectBody());
            }
        } else if (fromItem instanceof ValuesList) {
            log.info("Perform a sub-query, if you do not give us feedback");
        } else if (fromItem instanceof LateralSubSelect) {
            LateralSubSelect lateralSubSelect = (LateralSubSelect) fromItem;
            if (lateralSubSelect.getSubSelect() != null) {
                SubSelect subSelect = lateralSubSelect.getSubSelect();
                if (subSelect.getSelectBody() != null) {
                    processSelectBody(subSelect.getSelectBody());
                }
            }
        }
    }

    /**
     * Handle joins
     *
     * @param joins joins
     */
    private void processJoins(List<Join> joins) {
        // Record how many tables are joined
        Deque<Table> tables = new LinkedList<>();
        for (Join join : joins) {
            // handle 'on' expression
            FromItem fromItem = join.getRightItem();
            if (fromItem instanceof Table) {
                Table fromTable = (Table) fromItem;
                // get 'on' expressions
                Collection<Expression> originOnExpressions = join.getOnExpressions();
                // if there is only one 'on' expression, then process it immediately
                if (originOnExpressions.size() == 1) {
                    processJoin(join);
                    continue;
                }
                tables.push(fromTable);
                // if multiple 'on' expressions show up, then process them together
                if (originOnExpressions.size() > 1) {
                    Collection<Expression> onExpressions = new LinkedList<>();
                    for (Expression originOnExpression : originOnExpressions) {
                        Table currentTable = tables.poll();
                        onExpressions.add(builderExpression(originOnExpression, currentTable));
                    }
                    join.setOnExpressions(onExpressions);
                }
            } else {
                // handle the right sub-expressions
                processFromItem(fromItem);
            }
        }
    }

    /**
     * Handle join operator
     */
    protected void processJoin(Join join) {
        if (join.getRightItem() instanceof Table) {
            Table fromTable = (Table) join.getRightItem();
            Expression originOnExpression = CollUtil.getFirst(join.getOnExpressions());
            originOnExpression = builderExpression(originOnExpression, fromTable);
            join.setOnExpressions(CollUtil.newArrayList(originOnExpression));
        }
    }

    /**
     * Handle expressions
     */
    protected Expression builderExpression(Expression currentExpression, Table table) {
        // get data security filters by table name
        Expression equalsTo = buildDataPermissionExpression(table);
        // if no filters returned for this table, then return default expression
        if (equalsTo == null) {
            return currentExpression;
        }

        if (currentExpression == null) {
            return equalsTo;
        }
        // if currentExpression is OrExpression, then wrap it up and join with equalsTo. e.g. (currentExpression) AND equalsTo
        if (currentExpression instanceof OrExpression) {
            return new AndExpression(new Parenthesis(currentExpression), equalsTo);
        }
        // if currentExpression is AndExpression, then return currentExpression AND equalsTo right now.
        return new AndExpression(currentExpression, equalsTo);
    }

    /**
     * Build Expression with filters according to data owners, like job.user_id = 3, job.team_id = 5
     *
     * @param table
     * @return Expression filters
     */
    private Expression buildDataPermissionExpression(Table table) {
        // get permission rules
        //rules = ContextHolder.getRules();
        //System.out.println(rules);
        if (CollectionUtils.isEmpty(rules)){
            return null;
        }
        Expression allExpression = null;
        for (DataPermissionRule rule : rules) {
            // if current table is not got involved in data security, then skip building expressions
            if (!rule.getTableNames().contains(table.getName().toLowerCase())) {
                continue;
            }

            String tableName = HibernateUtils.getTableName(table).toLowerCase();
            Expression oneExpress = rule.getExpression(tableName, table.getAlias(), isSelect, deptDataPermission);
            // join allExpression
            allExpression =  Objects.isNull(allExpression) ? oneExpress
                    : new AndExpression(allExpression, oneExpress);
        }
        return allExpression;
    }

    /**
     * The context will be replaced by cache
     * <AUTHOR>
     */
    public static final class ContextHolder {

        private static final ThreadLocal<List<TeamDataPermissionRule>> RULES = new TransmittableThreadLocal<>();

        public static void init(List<TeamDataPermissionRule> rules) {
            RULES.set(rules);
        }

        public static void clear() {
            RULES.remove();
        }
        public static List<TeamDataPermissionRule> getRules() {
            return RULES.get();
        }
    }
}
