package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * The ContactType enumeration.
 */
public enum ContactType implements ConvertedEnum<Integer> {
    PRIMARY_EMAIL(0),
    PRIMARY_PHONE(1),
    EMAIL(2), //common use for email
    HOME_PHONE(3),
    WORK_PHONE(4),
    CELL_PHONE(5),
    FAX(6),
    LINKEDIN(7),
    <PERSON>ITHUB(8),
    <PERSON><PERSON><PERSON>(9),
    <PERSON>IE<PERSON><PERSON>(10),
    GOOGLE_SCHOLAR(11),
    FACEBOOK(12),
    TWITTER(13),
    WECHAT(14),
    WEIBO(15),
    PHONE(16), //common use for phone
    MEDIUM(17),
    ANGELLIST(18),
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(19),
    <PERSON><PERSON><PERSON><PERSON>N(20),
    <PERSON><PERSON><PERSON><PERSON>(21),
    <PERSON>G<PERSON><PERSON>(22),
    <PERSON>OSSZHIP<PERSON>(23),
    <PERSON><PERSON>(24),
    <PERSON>YP<PERSON>(25),
    W<PERSON><PERSON><PERSON>P(26),
    MAIMAI(27),
    PERSONAL_WEBSITE(28),
    DODAX(29);

    private final Integer dbValue;

    ContactType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ContactType, Integer> resolver =
        new ReverseEnumResolver<>(ContactType.class, ContactType::toDbValue);

    public static ContactType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static List<String> getAllNames() {
        return Arrays.stream(ContactType.values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }
}
