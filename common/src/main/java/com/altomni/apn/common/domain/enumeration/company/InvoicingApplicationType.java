package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * invoicing application type enum
 */
public enum InvoicingApplicationType implements ConvertedEnum<Integer> {

    INVOICING_APPLICATION(1, "全职招聘开票申请"),
    PREPAYMENT_INVOICING_APPLICATION(2, "预付金发票申请"),
    VOID_INVOICING_APPLICATION(3,"废票申请"),
    ;
    private final int dbValue;

    private final String description;

    InvoicingApplicationType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoicingApplicationType, Integer> resolver = new ReverseEnumResolver<>(InvoicingApplicationType.class, InvoicingApplicationType::toDbValue);

    public static InvoicingApplicationType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
