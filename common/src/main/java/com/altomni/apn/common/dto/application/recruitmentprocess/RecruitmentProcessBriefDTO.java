package com.altomni.apn.common.dto.application.recruitmentprocess;

import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitmentProcessBriefDTO implements Serializable {
    private Long id;

    @ApiModelProperty(value = "display job type")
    private String name;

    @ApiModelProperty(value = "job type")
    private JobType jobType;


    @ApiModelProperty(value = "current recruitment process status")
    private ActiveStatus status;
}
