package com.altomni.apn.common.config;

import com.altomni.apn.common.aop.datasync.DataSyncAspect;
import com.altomni.apn.common.aop.datasync.factory.DataSyncServiceTemplateFactory;
import com.altomni.apn.common.config.application.EsFillerMqBaseProperties;
import com.altomni.apn.common.config.application.LarkProperties;
import com.altomni.apn.common.service.rabbitmq.RabbitMqService;
import com.altomni.apn.common.service.rabbitmq.impl.RabbitMqServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "application.esfillerMQ", name = "host")
public class SyncToEsConfig {

    /**
     * esFiller 基础配置
     * @return
     */
    @Bean
    public EsFillerMqBaseProperties esFillerMqBaseProperties() {
        return new EsFillerMqBaseProperties();
    }

    /**
     * 同步数据需要的切面
     * @return
     */
    @Bean
    public DataSyncAspect dataSyncAspect() {
        return new DataSyncAspect();
    }

    /**
     * 同步数据需要的工厂类
     * @return
     */
    @Bean
    public DataSyncServiceTemplateFactory dataSyncServiceTemplateFactory() {
        return new DataSyncServiceTemplateFactory();
    }

    @Bean
    public LarkProperties larkProperties() {
        return new LarkProperties();
    }

    @Bean
    public RabbitMqService rabbitMqService() {
        return new RabbitMqServiceImpl();
    }

}
