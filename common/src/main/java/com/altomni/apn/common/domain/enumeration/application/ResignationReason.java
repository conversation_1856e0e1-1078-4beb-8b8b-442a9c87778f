package com.altomni.apn.common.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum ResignationReason implements ConvertedEnum<Integer> {


    CANDIDATE_RESIGNED(0),
    TERMINATED_PERFORMANCE_REASON(1),
    TERMINATED_OTHER_REASONS_FROM_CANDIDATE(2);

    private final int dbValue;
    ResignationReason(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ResignationReason, Integer> resolver =
        new ReverseEnumResolver<>(ResignationReason.class, ResignationReason::toDbValue);

    public static ResignationReason fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
