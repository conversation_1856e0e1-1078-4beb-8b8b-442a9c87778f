package com.altomni.apn.common.vo.settings;


import com.altomni.apn.common.domain.config.SettingTrackRecordType;

import java.math.BigInteger;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
public class SettingTrackRecordVO {

    private BigInteger id;

    private String tempName;

    private String tempRemark;

    private String tempContent;

    private String tempType;

    private String status;

    private String crowdName;

    private String createUser;

    private BigInteger createUserId;

    private Timestamp createdDate;

    public String getTempName() {
        return tempName;
    }

    public void setTempName(String tempName) {
        this.tempName = tempName;
    }

    public String getTempRemark() {
        return tempRemark;
    }

    public void setTempRemark(String tempRemark) {
        this.tempRemark = tempRemark;
    }

    public String getTempContent() {
        return tempContent;
    }

    public void setTempContent(String tempContent) {
        this.tempContent = tempContent;
    }

    public String getTempType() {
        return tempType;
    }

    public void setTempType(String tempType) {
        this.tempType = tempType != null ? SettingTrackRecordType.getNameFromDbValue(Integer.valueOf(tempType)) : "";
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCrowdName() {
        return crowdName;
    }

    public void setCrowdName(String crowdName) {
        this.crowdName = crowdName;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(BigInteger createUserId) {
        this.createUserId = createUserId;
    }
}
