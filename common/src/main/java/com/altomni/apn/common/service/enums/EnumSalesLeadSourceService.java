package com.altomni.apn.common.service.enums;

import com.altomni.apn.common.domain.dict.EnumSalesLeadSource;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnumSalesLeadSourceService {

    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumSalesLeadSource> findAllByIds(List<Long> ids) {
        List<EnumSalesLeadSource> enumSalesLeadSourceList = enumCommonService.findAllEnumSalesLeadSource(SecurityUtils.getTenantId());
        return enumSalesLeadSourceList.stream().filter(o -> ids.contains(o.getId())).collect(Collectors.toList());
    }

    public List<EnumDictVO> querySalesLeadSource(SortType type) {
        List<EnumSalesLeadSource> salesLeadSourceList = enumCommonService.findAllEnumSalesLeadSource(SecurityUtils.getTenantId());

        if (salesLeadSourceList == null) {
            return new ArrayList<>();
        }

        return salesLeadSourceList.stream().map(o -> {
            EnumDictVO dictVO = new EnumDictVO();
            dictVO.setId(o.getId());
            dictVO.setLabel(SortType.CN.equals(type) ? o.getCnDisplay() : o.getEnDisplay());
            return dictVO;
        }).collect(Collectors.toList());
    }

}

