package com.altomni.apn.common.service.sql;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.common.utils.SqlUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class QueryProcessService {

//    final static String SET_PARAMETER = "setParameter";
//
//    //TODO: move to individual repository
//    @PersistenceContext
//    private EntityManager entityManager;
//
//    public <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
//        entityManager.clear();
//        Integer key = SqlUtil.checkInList(map);
//        if (key == null) {
//            return doSearchData(queryStr, clazz, map);
//        } else {
//            return doPartitionSearchData(key, queryStr, clazz, map);
//        }
//    }
//
//    private <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
//        Query query = entityManager.createNativeQuery(queryStr, clazz);
//        Method method = ReflectUtil.getMethod(Query.class, SET_PARAMETER, Integer.class, Object.class);
//        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
//        return query.getResultList();
//    }
//
//    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
//        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
//            map.put(key, values);
//            return doSearchData(queryStr, clazz, map);
//        }).flatMap(Collection::stream).collect(Collectors.toList());
//    }
//
//    public Long searchCount(String queryStr, Map<Integer, Object> map) {
//        Query query = entityManager.createNativeQuery(queryStr);
//        Method method = ReflectUtil.getMethod(Query.class, SET_PARAMETER, Integer.class, Object.class);
//        map.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v));
//        return Long.parseLong(String.valueOf(query.getSingleResult()));
//    }

    /***
     * set the countSQL and dataSQL with column and column value
     * @param paramsMap map to hold all search condition
     * @param countSql
     * @param dataSql
     * @param columnName table column name to put in where clause
     * @param columnValue table column value to append
     */
    public void setColumnLikeParamFilter(Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql, String columnName, String columnValue) {
        if (columnName == null || columnName.isEmpty()) {
            return;
        }
        if (columnValue != null && StringUtils.isNotEmpty(columnValue)) {
            countSql.append(" AND ").append(columnName).append(" LIKE ?").append(paramsMap.size() + 1);
            dataSql.append(" AND ").append(columnName).append(" LIKE ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, String.format("%%%s%%", columnValue));
        }
    }

    public void setColumnIdsInParaFilter(Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql, String columnName, List<Long> columnValues){
        if (columnName == null || columnName.isEmpty()) {
            return;
        }
        if (columnValues != null && CollUtil.isNotEmpty(columnValues)) {
            countSql.append(" AND ").append(columnName).append(" IN ?").append(paramsMap.size() + 1);
            dataSql.append(" AND ").append(columnName).append(" IN ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, columnValues);
        }
    }


    public void setDateBetweenParamFilter(Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql, String columnName, Instant fromDate, Instant toDate) {
        if (columnName == null || columnName.isEmpty()) {
            return;
        }
        if (fromDate != null) {
            countSql.append(" AND ").append(columnName).append(" > ?").append(paramsMap.size() + 1);
            dataSql.append(" AND ").append(columnName).append(" > ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, fromDate);
        }
        if (toDate != null) {
            countSql.append(" AND ").append(columnName).append(" < ?").append(paramsMap.size() + 1);
            dataSql.append(" AND ").append(columnName).append(" < ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, toDate);
        }
    }

    /***
     * General text search filter generate
     * Set single general search param by representing "AND" with whitespace
     */
    public void setSingleGeneralSearchParamFilter(Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql, String columnName, String generalText) {
        String[] stringArray = generalText.split("\\s+");
        countSql.append(" AND (");
        dataSql.append(" AND (");
        StringJoiner joiner = new StringJoiner(" AND ");
        for (int i = 0; i < stringArray.length; i++) {
            joiner.add(columnName + " LIKE ?" + (paramsMap.size() + 1));
            paramsMap.put(paramsMap.size() + 1, String.format("%%%s%%", stringArray[i]));
        }
        countSql.append(joiner).append(")");
        dataSql.append(joiner).append(")");
    }

    /***
     * set pageable for folder secondary list search
     * @param pageable the pagination and sort info
     * @param dataSql the query string that query the data will be appended with page and sort
     */
    public void setQueryPaginationAndSort(Pageable pageable, StringBuffer dataSql) {
        int start = pageable.getPageSize() * pageable.getPageNumber();
        if (pageable.getSort().isSorted()) {
            dataSql.append(" ORDER BY ");
            StringJoiner joiner = new StringJoiner(", ");
            for (Sort.Order order : pageable.getSort()) {
                String column;
                switch (order.getProperty()) {
                    case "createdDate":
                        column = "created_date";
                        break;
                    case "lastModifiedDate":
                        column = "last_modified_date";
                        break;
                    case "creator":
                        column = "CASE WHEN creator IS NULL OR creator = '' THEN 0 ELSE 1 END DESC, CONVERT(creator USING gbk)";
                        break;
                    case "name":
                        column = "CONVERT(name USING gbk)";
                        break;
                    default:
                        column = order.getProperty();
                }
                joiner.add(column + " " + order.getDirection());
            }
            dataSql.append(joiner);
        }
        dataSql.append(" LIMIT ").append(start).append(", ").append(pageable.getPageSize());
    }
}
