package com.altomni.apn.common.config.application;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class EsFillerMqBaseProperties {

    /**
     * Esfiller MQ
     */
    @Value("${application.esfillerMQ.host:}")
    private String esfillerMQHost;

    @Value("${application.esfillerMQ.port:}")
    private int esfillerMQPort;

    @Value("${application.esfillerMQ.virtual-host:/}")
    private String esfillerMQVirtualHost;

    @Value("${application.esfillerMQ.username:}")
    private String esfillerMQUsername;

    @Value("${application.esfillerMQ.password:}")
    private String esfillerMQPassword;

    @Value("${application.esfillerMQ.esFillerExchange:}")
    private String esfillerMQExchange;

    @Value("${application.esfillerMQ.toEsFillerRoutingKey:}")
    private String toEsFillerRoutingKey;

    @Value("${application.esfillerMQ.toEsFillerQueue:}")
    private String toEsFillerQueue;

    @Value("${application.esfillerMQ.toEsFillerMaximumMsgCount:}")
    private Integer toEsFillerMaximumMsgCount;

    @Value("${application.esfillerMQ.retryThreshold:}")
    private int retryThreshold;

}
