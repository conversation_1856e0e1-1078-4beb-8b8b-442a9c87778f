package com.altomni.apn.common.errors;
/*
 * Created by <PERSON> on 9/29/2017.
 */

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

import java.util.HashMap;
import java.util.Map;

public class NoPermissionException extends AbstractThrowableProblem {

    private static final long serialVersionUID = 1L;

    private String message;

    private String description = null;

    private String entityName;

    private String errorKey;

    public NoPermissionException() {
        this(null);
        this.message = message;
    }

    public NoPermissionException(String message) {
        super(ErrorConstants.DEFAULT_TYPE, "NO PERMISSION", Status.FORBIDDEN, message);
        this.message = message;
    }

    /*public NoPermissionException(URI type, String defaultMessage) {
        super(type, "NO PERMISSION", Status.FORBIDDEN, null, null, null, getAlertParameters(defaultMessage));
        this.entityName = entityName;
        this.errorKey = errorKey;
    }*/

    /*public ErrorVM getErrorVM() {
        return new ErrorVM(message, description);
    }*/

    private static Map<String, Object> getAlertParameters(String defaultMessage) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("message", defaultMessage);
        //parameters.put("params", entityName);
        return parameters;
    }

}
