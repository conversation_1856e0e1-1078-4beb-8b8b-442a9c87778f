package com.altomni.apn.common.domain.address;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.address.AddressType;
import com.altomni.apn.common.domain.enumeration.address.AddressTypeConverter;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A Address.
 */
@MappedSuperclass
public abstract class Address extends AbstractAuditingEntity {

    private static final long serialVersionUID = 1L;

    @Column(name = "type")
    @Convert(converter = AddressTypeConverter.class)
    public AddressType addressType;

    @Column(name = "city")
    public String city;

    @Column(name = "address")
    public String address;

    @Column(name = "address_2")
    public String address2;

    @Column(name = "city_id")
    public Long cityId;

    @Column(name = "zipcode")
    public String zipcode;

    // jhipster-needle-entity-add-field - JHip<PERSON> will add fields here, do not remove

    public AddressType getAddressType() {
        return addressType;
    }

    public void setAddressType(AddressType addressType) {
        this.addressType = addressType;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }


    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        }
//        if (o == null || getClass() != o.getClass()) {
//            return false;
//        }
//        Address address = (Address) o;
//        if (address.getId() == null || getId() == null) {
//            return false;
//        }
//        return Objects.equals(getId(), address.getId());
//    }

//    @Override
//    public int hashCode() {
//        return Objects.hashCode(getId());
//    }

    @Override
    public String toString() {
        return "Address{" +
            ", addressType=" + addressType +
            ", city='" + city + '\'' +
            ", address='" + address + '\'' +
            ", address2='" + address2 + '\'' +
            ", cityId=" + cityId +
            ", zipcode='" + zipcode + '\'' +
            '}';
    }
}
