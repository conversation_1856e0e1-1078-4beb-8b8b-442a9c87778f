package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
public enum NodeTypeTableEnum {

    SUBMIT_TO_JOB(NodeType.SUBMIT_TO_JOB, "talent_recruitment_process_submit_to_job"),
    SUBMIT_TO_CLIENT(NodeType.SUBMIT_TO_CLIENT, "talent_recruitment_process_submit_to_client"),
    INTERVIEW(NodeType.INTERVIEW, "talent_recruitment_process_interview"),
    OFFER(NodeType.OFFER, "talent_recruitment_process_offer"),
    OFFER_ACCEPT(NodeType.OFFER_ACCEPT, "talent_recruitment_process_ipg_offer_accept"),
    COMMISSION(NodeType.COMMISSION, "talent_recruitment_process_commission"),
    ON_BOARD(NodeType.ON_BOARD, "talent_recruitment_process_onboard"),;

    private NodeType nodeType;

    private String tableName;

    NodeTypeTableEnum(NodeType nodeType, String tableName) {
        this.nodeType = nodeType;
        this.tableName = tableName;
    }

    public static String getTableNameByNodeType(NodeType nodeType) {
        NodeTypeTableEnum[] values = NodeTypeTableEnum.values();
        for (NodeTypeTableEnum value : values) {
            if (value.getNodeType() == nodeType) {
                return value.getTableName();
            }
        }
        return "";
    }

}
