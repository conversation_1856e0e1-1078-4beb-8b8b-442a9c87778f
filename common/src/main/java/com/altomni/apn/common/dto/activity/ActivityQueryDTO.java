package com.altomni.apn.common.dto.activity;


import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import lombok.Data;

import java.util.List;

@Data
public class ActivityQueryDTO {
    private JsonObject term;
    private JsonObject terms;

    public static ActivityQueryDTO toTerm(String key, Long value) {
        JsonObject valueObject = new JsonObject();
        valueObject.add("value", new JsonPrimitive(value));

        JsonObject keyObject = new JsonObject();
        keyObject.add(key, valueObject);

        ActivityQueryDTO activityQueryDTO = new ActivityQueryDTO();
        activityQueryDTO.setTerm(keyObject);
        return activityQueryDTO;
    }

    public static ActivityQueryDTO toTerms(String key, List<Long> values) {
        JsonArray valuesArray = new JsonArray();
        for (Long value : values) {
            valuesArray.add(new JsonPrimitive(value));
        }

        JsonObject keyObject = new JsonObject();
        keyObject.add(key, valuesArray);

        ActivityQueryDTO activityQueryDTO = new ActivityQueryDTO();
        activityQueryDTO.setTerms(keyObject);
        return activityQueryDTO;
    }
}
