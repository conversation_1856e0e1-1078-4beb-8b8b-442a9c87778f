package com.altomni.apn.common.dto.talent;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;


@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class TalentEducationDTO implements Serializable {

    private static final long serialVersionUID = -8724560422776148379L;

    private Long id;

    @Size(max = 255, message = "The college name is too long.")
    private String collegeName;

    private String location;

    @Size(max = 255, message = "The major name is too long.")
    private String majorName;

    private TalentCollegeInfoDTO collegeInfo;

    @ApiModelProperty(value = "degree level id from biz_dict data")
    private String degreeLevel;

    @ApiModelProperty(value = "degree name")
    private String degreeName;

    @ApiModelProperty(value = "The date enrolled in the college. At least year and month are required. And use 1st as day if not specified")
    private LocalDate startDate;

    @ApiModelProperty(value = "The date graduated from the college. At least year and month are required. And use 1st as day if not specified")
    private LocalDate endDate;

    @ApiModelProperty(value = "Is current graduated from school or not")
    private Boolean current;


    public static class TalentEducationComparator implements Comparator<TalentEducationDTO> {

        private final List<EnumDegree> enumDegreeList;

        public TalentEducationComparator(List<EnumDegree> enumDegreeList) {
            this.enumDegreeList = enumDegreeList;
        }

        @Override
        public int compare(TalentEducationDTO o1, TalentEducationDTO o2) {
            //sorting degree level
            if (ObjectUtil.isNotNull(o1.getDegreeLevel())) {
                if (ObjectUtil.isNull(o2.getDegreeLevel())) {
                    return -1;
                } else if (enumDegreeList.stream().filter(s -> s.getId().equals(Long.parseLong(o1.getDegreeLevel()))).findAny().get().getScore() < enumDegreeList.stream().filter(s -> s.getId().equals(Long.parseLong(o2.getDegreeLevel()))).findAny().get().getScore()) {
                    return 1;
                } else if (enumDegreeList.stream().filter(s -> s.getId().equals(Long.parseLong(o1.getDegreeLevel()))).findAny().get().getScore() > enumDegreeList.stream().filter(s -> s.getId().equals(Long.parseLong(o2.getDegreeLevel()))).findAny().get().getScore()) {
                    return -1;
                }
            } else if (ObjectUtil.isNotNull(o2.getDegreeLevel())) {
                return 1;
            }
            //sorting date
            if (ObjectUtil.isNotNull(o1.getCurrent()) && o1.getCurrent()) {
                //if one is current, ignore all end_date. end_dates are bad data.
                if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                    if (ObjectUtil.isNotNull(o1.getStartDate())) {
                        if (ObjectUtil.isNotNull(o2.getStartDate())) {
                            if (o1.getStartDate().isBefore(o2.getStartDate())) {
                                return 1;
                            } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                                return -1;
                            } else {
                                return 0;
                            }
                        } else {
                            //# the one without start date is smaller
                            return -1;
                        }
                    } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                        //the one without start date is smaller
                        return 1;
                    }
                } else {
                    return -1;
                }
            } else if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                return 1;
            }
            //if neither is current, use end_date
            if (ObjectUtil.isNotNull(o1.getEndDate())) {
                if (ObjectUtil.isNotNull(o2.getEndDate())) {
                    if (o1.getEndDate().isBefore(o2.getEndDate())) {
                        return 1;
                    } else if (o1.getEndDate().isAfter(o2.getEndDate())) {
                        return -1;
                    }
                    //if equals, use start_date
                } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                    if (o1.getEndDate().isBefore(o2.getStartDate())) {
                        return 1;
                    } else if (ObjectUtil.isNotNull(o1.getStartDate())) {
                        if (o1.getStartDate().isAfter(o2.getStartDate())) {
                            return -1;
                        } else {
                            return 1;
                        }
                    } else {
                        //end_date is larger than other.start_date
                        return -1;
                    }
                } else {
                    return -1;
                }
            } else if (ObjectUtil.isNotNull(o2.getEndDate())) {
                if (ObjectUtil.isNotNull(o1.getStartDate())) {
                    if (o1.getStartDate().isAfter(o2.getEndDate())) {
                        return -1;
                    } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                        if (o1.getStartDate().isBefore(o2.getStartDate())) {
                            return 1;
                        } else {
                            return -1;
                        }
                    } else {
                        return 1;
                    }
                } else {
                    return 1;
                }
            }
            //if neither exists end_date, use start_date as well
            //here, we have already dealt with one end date existing cases
            if (ObjectUtil.isNotNull(o1.getStartDate())) {
                if (ObjectUtil.isNotNull(o2.getStartDate())) {
                    if (o1.getStartDate().isBefore(o2.getStartDate())) {
                        return 1;
                    } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                        return -1;
                    }
                } else {
                    return -1;
                }
            } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                return 1;
            }
            //missing info or equals, unable to determine, return None
            return 0;
        }
    }

}
