package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceTypeConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Convert;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessIpgOfferAcceptVO extends AuditingUser implements Serializable, FillSalaryPackages, FillFeeCharge, FillContractFeeCharge, FillClientInfo {

    private static final long serialVersionUID = -6257873083011850085L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private LocalDate onboardDate;

    private LocalDate warrantyEndDate;

    private LocalDate endDate;

    private Integer currency;

    private RateUnitType rateUnitType;

    private String note;

    private Instant noteLastModifiedDate;

    private Long noteLastModifiedByUserId;

    @Convert(converter = ResumeSourceTypeConverter.class)
    private ResumeSourceType channelPlatform;

    private BigDecimal profitSharingRatio;

    private List<TalentRecruitmentProcessOfferSalaryPackageVO> salaryPackages;

    private TalentRecruitmentProcessOfferFeeChargeVO feeCharge;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

    private TalentRecruitmentProcessIpgContractFeeChargeVO contractFeeCharge;

    private TalentRecruitmentProcessOnboardClientInfoVO clientInfo;

    private TalentRecruitmentProcessSourceChannelProfitSharingVO profitSharing;

}
