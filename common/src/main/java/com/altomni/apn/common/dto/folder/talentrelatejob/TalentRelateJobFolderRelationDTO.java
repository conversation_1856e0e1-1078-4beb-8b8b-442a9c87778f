package com.altomni.apn.common.dto.folder.talentrelatejob;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;


@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRelateJobFolderRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "folderId for related folder")
    private String folderId;

    @ApiModelProperty(value = "job id for the related folder")
    private Long jobId;

    @ApiModelProperty(value = "shared links expiration time")
    private Instant sharedLinkExpireTime;



}
