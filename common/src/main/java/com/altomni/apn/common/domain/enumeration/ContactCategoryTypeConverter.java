package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class ContactCategoryTypeConverter extends AbstractAttributeConverter<ContactCategoryType, Integer> {
    public ContactCategoryTypeConverter() {
        super(ContactCategoryType::toDbValue, ContactCategoryType::fromDbValue);
    }
}
