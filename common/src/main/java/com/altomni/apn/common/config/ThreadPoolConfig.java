package com.altomni.apn.common.config;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.security.concurrent.DelegatingSecurityContextExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class ThreadPoolConfig {

    @Primary
    @Bean("commonThreadPool")
    public Executor commonThreadPool(){
        Executor executor = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 3,//核心数，一直都能工作的数量
                Runtime.getRuntime().availableProcessors() * 5,//请求处理大时，可以开放的最大工作数
                60,//开启最大工作数后，当无请求时，还让其存活的时间
                TimeUnit.SECONDS,//存活时间单位
                new LinkedBlockingDeque<>(1024),//阻塞队列，保存操作请求线程
                new ThreadFactoryBuilder().setNameFormat("async-common-pool-").build(),//创建线程的工厂类
                new ThreadPoolExecutor.AbortPolicy());
        return new DelegatingSecurityContextExecutor(executor);
    }
}