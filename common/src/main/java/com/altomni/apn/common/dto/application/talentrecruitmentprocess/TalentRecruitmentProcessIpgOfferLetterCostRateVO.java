package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.altomni.apn.common.domain.enumeration.application.OfferLetterCostRateType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessIpgOfferLetterCostRateVO implements Serializable {

    private static final long serialVersionUID = -1125115527462085686L;

    private Long id;

    private Long tenantId;

    private Integer currency;

    private OfferLetterCostRateType rateType;

    private String code;

    private String description;

    private BigDecimal value;

    private LocalDate expireDate;

}
