package com.altomni.apn.common.utils;

import gui.ava.html.image.generator.HtmlImageGenerator;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Map;

public class Html2ImageUtils {

    public static byte[] html2Image(String htmlTemplate) throws IOException {
        HtmlImageGenerator imageGenerator = new HtmlImageGenerator();
        imageGenerator.loadHtml(htmlTemplate);
        BufferedImage bufferedImage = imageGenerator.getBufferedImage();
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        ImageIO.write(bufferedImage,"png",os);
        return os.toByteArray();
    }

    public static String convertHtmlTemplate(String htmlTemplate, Map<String,Object> values) {
        if(htmlTemplate == null){
            return null;
        }
        for(Map.Entry<String,Object> entry : values.entrySet()) {
            htmlTemplate = htmlTemplate.replace("{{"+entry.getKey()+"}}",String.valueOf(entry.getValue()));
        }
        return htmlTemplate;
    }



}
