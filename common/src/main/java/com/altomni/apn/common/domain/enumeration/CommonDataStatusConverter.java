package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CommonDataStatusConverter extends AbstractAttributeConverter<CommonDataStatus, Byte> {
    public CommonDataStatusConverter() {
        super(CommonDataStatus::toDbValue, CommonDataStatus::fromDbValue);
    }
}
