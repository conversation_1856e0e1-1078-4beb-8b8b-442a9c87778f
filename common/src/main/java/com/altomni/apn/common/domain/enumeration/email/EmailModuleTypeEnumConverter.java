package com.altomni.apn.common.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class EmailModuleTypeEnumConverter extends AbstractAttributeConverter<EmailModuleTypeEnum, Integer> {
    public EmailModuleTypeEnumConverter() {
        super(EmailModuleTypeEnum::toDbValue, EmailModuleTypeEnum::fromDbValue);
    }
}
