package com.altomni.apn.common.utils;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Set;

@Slf4j
public class JsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    static {
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    }


    public static String toJson(Object value) {
        if (value == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(value);
        } catch (Exception e) {
            log.error("json write error! ", e);
            return null;
        }
    }

    public static <T> T fromJson(String content, Class<T> valueType) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        try {
            return objectMapper.readValue(content, valueType);
        } catch (Exception e) {
            return null;
        }
    }


    public static String toJsonIgnoreNullFields(Object value) {
        if (value == null) {
            return null;
        }
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            return mapper.writeValueAsString(value);
        } catch (Exception e) {
            return null;
        }
    }

    public static List<String> fromJsonArray(String content) {
        if (!StringUtils.hasText(content)) {
            return null;
        }
        Gson gson = new Gson();
        Type type = new TypeToken<List<String>>(){}.getType();
        return gson.fromJson(content, type);
    }

    public static Set<Long> fromJsonArrayLong(String content) {
        if (StringUtils.isEmpty(content)) {
            return null;
        }
        Gson gson = new Gson();
        Type type = new TypeToken<Set<Long>>(){}.getType();
        return gson.fromJson(content, type);
    }


    public static void fluentPut(JSONObject jsonObject, String key, Object value) {
        if (value != null) {
            jsonObject.put(key, value);
        }
    }

    public static void fluentPutString(JSONObject jsonObject, String key, String value) {
        if (StringUtils.hasText(value)) {
            jsonObject.put(key, value);
        }
    }

    public static String toJSONString(List<String> value) {
        if (CollectionUtils.isEmpty(value)) {
            return null;
        }

        return new JSONObject(value).toString();
    }

    public static String toJSONString(Set<Long> value) {
        if (CollectionUtils.isEmpty(value)) {
            return null;
        }
        return new JSONObject(value).toString();
    }

}
