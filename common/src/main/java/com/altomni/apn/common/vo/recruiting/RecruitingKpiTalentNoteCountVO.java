package com.altomni.apn.common.vo.recruiting;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiTalentNoteCountVO extends RecruitingKpiCommonCountVO {

    private Long callNoteNum;

    private Long emailNoteNum;

    private Long personNoteNum;

    private Long videoNoteNum;

    private Long otherNoteNum;

    private Long iciNum;

    private String callNoteIds;

    private String emailNoteIds;

    private String personNoteIds;

    private String videoNoteIds;

    private String otherNoteIds;

    private String uniqueTalentIds;

    private Long noteCount;

}
