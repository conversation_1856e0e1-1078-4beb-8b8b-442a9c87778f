package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ApiModel(description = "Enum job functions entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_degree")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumDegree implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "score")
    private Long score;

    @Transient
    private Boolean cnLable = false;

    public static Set<EnumDegree> transfer(List<Long> body) {
        if (body == null) {
            return null;
        } else {
            return body.stream().map(dictCode -> {
                EnumDegree enumDegree = new EnumDegree();
                enumDegree.setId(dictCode);
                return enumDegree;
            }).collect(Collectors.toSet());
        }
    }

    public static List<Long> convert(Set<EnumDegree> body) {
        if (body == null) {
            return null;
        } else {
            return body.stream().map(EnumDegree::getId
            ).collect(Collectors.toList());
        }
    }
}
