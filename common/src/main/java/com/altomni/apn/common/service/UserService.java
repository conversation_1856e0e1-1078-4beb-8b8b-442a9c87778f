package com.altomni.apn.common.service;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Component
@FeignClient(value = "user-service")
public interface UserService {
    @GetMapping("/user/api/v3/users/common/tenant-param/{paramKey}/string-value")
    ResponseEntity<String> getTenantParamStringValue(@PathVariable("paramKey") String paramKey);
}
