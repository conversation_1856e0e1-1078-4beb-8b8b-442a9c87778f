package com.altomni.apn.common.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class CommonApplicationProperties {
    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Value("${application.security.enableObjectLevelSecurity}")
    private boolean enableObjectLevelSecurity;

    @Value("${application.apnInternalPin}")
    private String apnInternalPin;

    // public-redis.yaml
    @Value("${application.onlineUserStatisticPeriod:480}")
    private Integer onlineUserStatisticPeriod;

    @Value("${application.activeUserPeriod:3600}")
    private Integer activeUserPeriod;

}
