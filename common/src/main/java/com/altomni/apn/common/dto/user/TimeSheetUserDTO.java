package com.altomni.apn.common.dto.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserTypeConverter;
import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.common.dto.CredentialDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Data
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TimeSheetUserDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long tenantId;

    private String uid;

    private String username;

    private String password;

    private String email;

    private boolean isPassChanged;

    @Convert(converter = TimeSheetUserTypeConverter.class)
    private TimeSheetUserType userType;

    @Convert(converter = TimeSheetTypeConverter.class)
    private TimeSheetType timeSheetType;

    private boolean activated = true;

    public CredentialDTO credential;

    private String photoUrl;

    private CompletionStatus completionStatus;

    private Boolean allowSubmitTimeSheet = false;

    private Boolean allowSubmitExpense = false;

    private Set<RoleDTO> roles = new HashSet<>();

    private String firstName;

    private String lastName;

    private Long teamId;

    @JsonIgnore
    public String getLogin() {
        if (this.username != null) {
            return username;
        }
        return this.email;
    }
}
