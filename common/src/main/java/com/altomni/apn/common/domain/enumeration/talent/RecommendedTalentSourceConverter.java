package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

public class RecommendedTalentSourceConverter extends AbstractAttributeConverter<RecommendedTalentSource, Integer> {
    public RecommendedTalentSourceConverter() {
        super(RecommendedTalentSource::toDbValue, RecommendedTalentSource::fromDbValue);
    }
}
