package com.altomni.apn.common.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentNoteDraftDTO implements Serializable {

    private Long id;

    private Long userId;

    private Long talentId;

    private String draftData;

}
