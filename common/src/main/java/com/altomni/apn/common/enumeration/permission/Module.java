package com.altomni.apn.common.enumeration.permission;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The Module enumeration.
 * <AUTHOR>
 */
public enum Module implements ConvertedEnum<Integer> {
    JOB(1),
    CLIENT_CONTACT(2),
    REPORT(3),

    HOME_AND_CALENDAR(4),

    CANDIDATE_PIPELINE_MANAGEMENT(5),

    CHINA_INVOICING(6);

    private final int dbValue;

    Module(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<Module, Integer> resolver =
        new ReverseEnumResolver<>(Module.class, Module::toDbValue);

    public static Module fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
