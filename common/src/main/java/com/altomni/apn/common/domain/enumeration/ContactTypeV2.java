package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContactType enumeration.
 */
public enum ContactTypeV2 implements ConvertedEnum<Integer> {
    EMAIL(1),
    PHONE(2),
    LINKEDIN(3),
    FACEBOOK(4),
    WECHAT(5),
    PERSONAL_WEBSITE(100);

    // static resolving:
    public static final ReverseEnumResolver<ContactTypeV2, Integer> resolver =
        new ReverseEnumResolver<>(ContactTypeV2.class, ContactTypeV2::toDbValue);
    private final Integer dbValue;

    ContactTypeV2(Integer dbValue) {
        this.dbValue = dbValue;
    }

    public static ContactTypeV2 fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }
}
