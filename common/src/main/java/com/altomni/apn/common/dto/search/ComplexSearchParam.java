package com.altomni.apn.common.dto.search;


import lombok.Data;

import java.util.List;

@Data
public class ComplexSearchParam implements SearchCondition{

    private Relation relation;

    private List<SearchCondition> condition;

    public static ComplexSearchParam ofAnd(List<SearchCondition> conditions) {
        ComplexSearchParam complexSearchParam = new ComplexSearchParam();
        complexSearchParam.setRelation(Relation.AND);
        complexSearchParam.setCondition(conditions);
        return complexSearchParam;
    }

    public static ComplexSearchParam ofOr(List<SearchCondition> conditions) {
        ComplexSearchParam complexSearchParam = new ComplexSearchParam();
        complexSearchParam.setRelation(Relation.OR);
        complexSearchParam.setCondition(conditions);
        return complexSearchParam;
    }

    public static ComplexSearchParam of(Relation relation, List<SearchCondition> conditions) {
        ComplexSearchParam complexSearchParam = new ComplexSearchParam();
        complexSearchParam.setRelation(relation);
        complexSearchParam.setCondition(conditions);
        return complexSearchParam;
    }


}
