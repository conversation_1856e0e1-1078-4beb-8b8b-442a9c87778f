package com.altomni.apn.common.dto.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@ApiModel
public class ApproverDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "contactId")
    @NotNull
    private Long contactId;

    @ApiModelProperty(value = "password")
    private String password;

    @ApiModelProperty(value = "inactived")
    @NotNull
    private Boolean inactived;

    @ApiModelProperty(value = "Receive email notifications when Timesheets or Expenses are submitted for my approval.")
    @NotNull
    private Boolean received;

    @NotNull
    private Long companyId;

}
