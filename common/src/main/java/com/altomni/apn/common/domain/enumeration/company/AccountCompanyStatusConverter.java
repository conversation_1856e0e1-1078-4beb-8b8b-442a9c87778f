package com.altomni.apn.common.domain.enumeration.company;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class AccountCompanyStatusConverter extends AbstractAttributeConverter<AccountCompanyStatus, Integer> {
    public AccountCompanyStatusConverter() {
        super(AccountCompanyStatus::toDbValue, AccountCompanyStatus::fromDbValue);
    }
}
