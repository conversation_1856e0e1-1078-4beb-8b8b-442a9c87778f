package com.altomni.apn.common.dto.recruiting;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.enumeration.enums.KpiReportByUserStayedOver;
import com.altomni.apn.common.enumeration.recruiting.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiReportSearchDto implements Serializable {

    private String startDate;

    private String endDate;

    private RecruitingKpiApplicationAiTalentType aiTalentType;

    //事件时间 EVENT(0), 添加时间 ADD(1)
    private RecruitingKpiDateType dateType;

    // 视图类型
    private RecruitingKpiViewType viewType;

    //groupBy 分组的 list, 根据顺序一次分组
    private List<RecruitingKpiGroupByFieldType> groupByFieldList;

    //application status search all/current
    private RecruitingKpiApplicationStatusType applicationStatusType;

    //teamIdList 和 userIdList 取并集
    private List<Long> teamIdList;

    private List<Long> userIdList;

    private String timezone;

    //在使用定时任务时,需要传入searchUserId和searchTenantId, 而且必须是header 判断内部调用
    private Long searchUserId;

    private Long searchTenantId;

    private SearchKpiJobDto job;

    private SearchKpiCompanyDto company;

    private SearchUserDto user;

    //设置一下私有职位
    private List<Long> privateJobIds = Collections.emptyList();

    private List<Long> allPrivateJobIds;

    private List<Long> permissionTeamIdList;

    private TeamDataPermissionRespDTO permissionRespDTO;

    private List<Long> companyIdList;

    private boolean isXxlJobFlag = false;

    private boolean isE5ReportFlag = false;

    private StageKpiReportDto jobCountPositions;

    private List<StageKpiReportDto> talentStageList;

    private List<StageKpiReportDto> notesStageList;

    private List<KpiReportByUserStayedOver> stayedOverList;

    // E1 team 视图下钻，需要传入父级 teamId
    private Long parentId;

    public String getStartDateUtc() {
        return getUtcByTimeZone(startDate + " 00:00:00");
    }

    public String getEndDateUtc() {
        return getUtcByTimeZone(endDate + " 23:59:59");
    }

    public String getUtcByTimeZone(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        return dateTime.atZone(ZoneId.of(timezone)).withZoneSameInstant(ZoneOffset.UTC).format(formatter);
    }

    public RecruitingKpiReportSearchDto deepCopy() {
        RecruitingKpiReportSearchDto copy = new RecruitingKpiReportSearchDto();
        copy.startDate = this.startDate;
        copy.endDate = this.endDate;
        copy.aiTalentType = this.aiTalentType;
        copy.dateType = this.dateType;
        copy.groupByFieldList = this.groupByFieldList == null ? Collections.emptyList() : new ArrayList<>(this.groupByFieldList);
        copy.applicationStatusType = this.applicationStatusType;
        copy.teamIdList = this.teamIdList == null ? Collections.emptyList() : new ArrayList<>(this.teamIdList);
        copy.userIdList = this.userIdList == null ? Collections.emptyList() : new ArrayList<>(this.userIdList);
        copy.timezone = this.timezone;
        copy.searchUserId = this.searchUserId;
        copy.searchTenantId = this.searchTenantId;
        copy.job = this.job == null ? null : JSONUtil.toBean(JSONUtil.parseObj(this.job), SearchKpiJobDto.class);
        copy.company = this.company == null ? null : JSONUtil.toBean(JSONUtil.parseObj(this.company), SearchKpiCompanyDto.class);
        copy.user = this.user == null ? null : JSONUtil.toBean(JSONUtil.parseObj(this.user), SearchUserDto.class);
//        copy.privateJobIds = this.privateJobIds == null ? Collections.emptyList() : new ArrayList<>(this.privateJobIds);
        copy.allPrivateJobIds = this.allPrivateJobIds == null ? Collections.emptyList() : new ArrayList<>(this.allPrivateJobIds);
        copy.permissionTeamIdList = this.permissionTeamIdList == null ? Collections.emptyList() : new ArrayList<>(this.permissionTeamIdList);
        copy.permissionRespDTO = this.permissionRespDTO == null ? null : JSONUtil.toBean(JSONUtil.parseObj(JSONUtil.toJsonStr(this.permissionRespDTO)), TeamDataPermissionRespDTO.class);
        copy.companyIdList = this.companyIdList == null ? Collections.emptyList() : new ArrayList<>(this.companyIdList);
        copy.isXxlJobFlag = this.isXxlJobFlag;
        copy.isE5ReportFlag = this.isE5ReportFlag;
        copy.jobCountPositions = this.jobCountPositions == null ? null : JSONUtil.toBean(JSONUtil.parseObj(JSONUtil.toJsonStr(this.jobCountPositions)), StageKpiReportDto.class);
        copy.talentStageList = this.talentStageList == null ? Collections.emptyList() : JSONUtil.toList(JSONUtil.parseArray(this.talentStageList), StageKpiReportDto.class);
        copy.notesStageList = this.notesStageList == null ? Collections.emptyList() : JSONUtil.toList(JSONUtil.parseArray(this.notesStageList), StageKpiReportDto.class);
        copy.stayedOverList = this.stayedOverList == null ? Collections.emptyList() : JSONUtil.toList(JSONUtil.parseArray(this.stayedOverList), KpiReportByUserStayedOver.class);
        copy.parentId = this.parentId;
        return copy;
    }

}
