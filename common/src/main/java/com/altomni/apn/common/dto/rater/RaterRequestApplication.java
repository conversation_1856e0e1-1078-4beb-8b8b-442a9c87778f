package com.altomni.apn.common.dto.rater;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RaterRequestApplication implements Serializable {

    private static final long serialVersionUID = -7226154274248910337L;

    @JsonProperty("_index")
    private String index;

    @JsonProperty("_id")
    private String id;

    @JsonProperty("_commonId")
    private String commonId;

    public String getCommonId() {
        return commonId;
    }

    public void setCommonId(String commonId) {
        this.commonId = commonId;
    }

    @JsonProperty("statusCode")
    private Integer statusCode;

    public static RaterRequestApplication fromApplicationForJob(TalentRecruitmentProcessVO application, String esId) {
        RaterRequestApplication result = new RaterRequestApplication();
        result.setIndex("talents_" + application.getTenantId());
        result.setId(String.valueOf(application.getTalentId()));
        result.setCommonId(esId);
        result.setStatusCode(Objects.nonNull(application.getLastNodeType()) ? application.getLastNodeType().toDbValue() : NodeType.UNKNOWN.toDbValue());
        return result;
    }

    public static RaterRequestApplication fromApplicationForTalent(TalentRecruitmentProcessVO application) {
        RaterRequestApplication result = new RaterRequestApplication();
        result.setIndex("jobs_" + application.getTenantId());
        result.setId(String.valueOf(application.getJobId()));
        result.setStatusCode(Objects.nonNull(application.getLastNodeType()) ? application.getLastNodeType().toDbValue() : NodeType.UNKNOWN.toDbValue());
        return result;
    }

    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(Integer statusCode) {
        this.statusCode = statusCode;
    }

    @Override
    public String toString() {
        return "RaterRequestApplication{" +
            "index='" + index + '\'' +
            ", id='" + id + '\'' +
            ", statusCode=" + statusCode +
            '}';
    }
}
