package com.altomni.apn.common.domain.enumeration.application;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EliminateReason implements ConvertedEnum<Integer> {

    REJECTED_BY_CANDIDATE(0, "Rejected By Candidate"),

    REJECTED_BY_CLIENT(1, "Eliminated by Client"),

    INTERNAL_REJECT(2, "Eliminated Internally"),

    ACCIDENTAL_OPERATION(3, "Accidental operation (Submitted to Job)"),

    AUTOMATIC_ELIMINATION(4, "Automatic elimination"),

    DUPLICATION(5, "Duplication in Client’s Database");

    public final int dbValue;

    public final String desc;

    EliminateReason(int dbValue, String desc) {
        this.dbValue = dbValue;
        this.desc = desc;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<EliminateReason, Integer> resolver =
        new ReverseEnumResolver<>(EliminateReason.class, EliminateReason::toDbValue);

    public static EliminateReason fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
