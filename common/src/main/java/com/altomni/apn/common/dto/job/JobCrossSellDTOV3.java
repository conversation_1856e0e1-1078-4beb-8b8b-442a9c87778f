package com.altomni.apn.common.dto.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobCrossSellDTOV3 implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    private Integer jobs;

    private Long businessId;

    private String jobStatus;
}
