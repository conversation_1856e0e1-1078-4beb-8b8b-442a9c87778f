package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class ManagerRoleConverter extends AbstractAttributeConverter<ManagerRoleType, Integer> {
    public ManagerRoleConverter() {
        super(ManagerRoleType::toDbValue, ManagerRoleType::fromDbValue);
    }
}
