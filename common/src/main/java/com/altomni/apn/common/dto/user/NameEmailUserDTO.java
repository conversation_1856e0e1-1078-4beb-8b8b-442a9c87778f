package com.altomni.apn.common.dto.user;

import com.altomni.apn.common.domain.user.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class NameEmailUserDTO implements Serializable {

        private static final long serialVersionUID = 1L;
        private Long id;
        private String fullName;
        private String email;

}
