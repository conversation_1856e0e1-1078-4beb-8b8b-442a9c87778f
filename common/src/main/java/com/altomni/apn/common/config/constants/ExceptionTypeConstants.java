package com.altomni.apn.common.config.constants;

/**
 *  talent contact type
 * <AUTHOR>
 */
public final class ExceptionTypeConstants {

    public static final Integer EXCEPTION_WITH_DATA_EXCEPTION = 1;

    public static final Integer EXCEPTION_USER_NOT_ACTIVATED_EXCEPTION = 2;

    public static final Integer EXCEPTION_USERNAME_ALREADY_USED_EXCEPTION = 3;

    public static final Integer EXCEPTION_NOT_FOUND_EXCEPTION = 4;

    public static final Integer EXCEPTION_NO_PERMISSION_EXCEPTION = 5;

    public static final Integer EXCEPTION_LOGIN_ALREADY_USED_EXCEPTION = 6;

    public static final Integer EXCEPTION_INVALID_PASSWORD_EXCEPTION = 7;

    public static final Integer EXCEPTION_FORBIDDEN_EXCEPTION = 8;

    public static final Integer EXCEPTION_EXTERNAL_SERVICE_INTERFACE_EXCEPTION = 9;

    public static final Integer EXCEPTION_EXTERNAL_INTERFACE_EXCEPTION = 10;

    public static final Integer EXCEPTION_EMAIL_ALREADY_USED_EXCEPTION = 11;

    public static final Integer EXCEPTION_DUPLICATED_EXCEPTION = 12;

    public static final Integer EXCEPTION_CUSTOM_PARAMETERIZED_EXCEPTION = 13;

    public static final Integer EXCEPTION_BAD_REQUEST_ALERT_EXCEPTION = 14;

}
