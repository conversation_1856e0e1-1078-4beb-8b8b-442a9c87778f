package com.altomni.apn.common.domain.dict;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.utils.StringUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    private String enumId;

    private String name;

    public EnumRelationDTO(String name) {
        this.name = name;
    }

    public EnumRelationDTO setEnumId(String enumId) {
        this.enumId = enumId;
        return this;
    }


    public static List<EnumRelationDTO> transfer(List<Long> body) {
        if (body == null) {
            return null;
        } else {
            return body.stream().map(dictCode -> {
                EnumRelationDTO enumRelationDTO = new EnumRelationDTO();
                enumRelationDTO.setEnumId(StringUtil.valueOf(dictCode));
                return enumRelationDTO;
            }).collect(Collectors.toList());
        }
    }

    public static List<String> convert(Set body) {
        List<String> list = new ArrayList<>();
        for (Object o : body) {
            JSONObject jsonObject = (JSONObject) JSON.toJSON(o);
            String enumId = jsonObject.getString("enumId");
            if (enumId != null) {
                list.add(enumId);
            }
        }
        return list;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EnumRelationDTO that = (EnumRelationDTO) o;
        return enumId.equals(that.enumId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(enumId);
    }
}

