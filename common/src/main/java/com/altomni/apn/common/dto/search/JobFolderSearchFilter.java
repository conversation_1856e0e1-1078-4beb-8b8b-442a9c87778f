package com.altomni.apn.common.dto.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 职位文件夹filter
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobFolderSearchFilter {
    private String userId;
    private JobFolder jobFolder;
    private List<SearchParam> search;
}
