package com.altomni.apn.common.domain.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_delivery_process_relation")
public class UserDeliveryProcessRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "process_id")
    private Long processId;

    @Column(name = "count")
    private Long count;

    @Column(name = "top")
    private Boolean top;

    @Column(name = "updated")
    private Boolean updated;

    public UserDeliveryProcessRelation(Long id, Long userId, Long processId, Long count, Boolean top) {
        this.id = id;
        this.userId = userId;
        this.processId = processId;
        this.count = count;
        this.top = top;
    }
}
