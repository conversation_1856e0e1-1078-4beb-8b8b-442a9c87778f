package com.altomni.apn.common.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractStatus enumeration.
 */
public enum SalesLeadRoleType implements ConvertedEnum<Integer> {
    ACCOUNT_MANAGER(0),
    SALES_LEAD_OWNER(1),
    // BD_OWNER
    BUSINESS_DEVELOPMENT(2),
    COOPERATE_ACCOUNT_MANAGER(3),
    RECRUITER(4);

    private final Integer dbValue;

    SalesLeadRoleType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<SalesLeadRoleType, Integer> resolver =
        new ReverseEnumResolver<>(SalesLeadRoleType.class, SalesLeadRoleType::toDbValue);

    public static SalesLeadRoleType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}