package com.altomni.apn.common.vo.recruiting;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.vo.recruiting.v2.KpiReportCreatedVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiApplicationCountVO;
import com.altomni.apn.common.vo.recruiting.v2.RecruitingKpiNoteCountVO;
import com.altomni.apn.common.vo.recruiting.v2.ReserveInterviewVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentMap;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiByCompanyVO extends RecruitingKpiCommonVO {

    private String contacts;

    private String industries;

    private String assignedUser;

    private JobStatus jobStatus;

    private String jobStartDate;

    private String jobEndDate;

    private String contractDuration;

    private Long jobNoteNum;

    private String jobCurrency;

    private String minimumPayRate;

    private String maximumPayRate;

    private String jobCooperationStatus;

    private Long companyNum;

    private boolean isPrivateJob;

    private List<KpiReportCompanyInfoVO> amList;

    private List<KpiReportCompanyInfoVO> coAmList;

    private List<KpiReportCompanyInfoVO> salesLeadList;

    private List<KpiReportCompanyInfoVO> bdOwnerList;

    private Long bdReportProgressNoteCount;

    private Instant companyCreatedDate;

    private String country;

    private Long companyNoteCount;

    private String thisWeek;

    private String lastWeek;

    private Long thisWeekCount;

    private Long lastWeekCount;

    private Long lastWeekCurrentCountNum;

    private Long thisWeekCurrentCountNum;

    private Long thisWeekCountAIRecommend;

    private Long lastWeekCountAIRecommend;

    private Long lastWeekCurrentCountNumAIRecommend;

    private Long thisWeekCurrentCountNumAIRecommend;

    private Long thisWeekCountPrecisionAIRecommend;

    private Long lastWeekCountPrecisionAIRecommend;

    private Long lastWeekCurrentCountNumPrecisionAIRecommend;

    private Long thisWeekCurrentCountNumPrecisionAIRecommend;

    private String clientStatus;

    private Instant requestDate;

    public void setNum(RecruitingKpiByCompanyVO vo,
                       ConcurrentMap<String, RecruitingKpiApplicationCountVO> applicationMap,
                       ConcurrentMap<String, RecruitingKpiCommonCountVO> jobMap, String key, RecruitingKpiReportSearchDto searchDto){
        // 获取默认值对象
        RecruitingKpiApplicationCountVO defaultApplication = new RecruitingKpiApplicationCountVO();

        // 获取实际数据
        RecruitingKpiApplicationCountVO appData = applicationMap.getOrDefault(key, defaultApplication);

        RecruitingKpiCommonCountVO jobData = jobMap.getOrDefault(key, new RecruitingKpiCommonCountVO());

        // 设置基础字段
        vo.setOpenings(jobData.getCountNum());
        vo.setCompanyNum(jobData.getCompanyNum());

        // 设置投递相关指标
        vo.setSubmitToJobNum(appData.getSubmitToJobCountNum());
        vo.setSubmitToJobCurrentNum(appData.getSubmitToJobCurrentCountNum());
        Integer submitToJobStayedOver = appData.getSubmitToJobStayedOver();
        Integer submitToJobCurrentStayedOver = appData.getSubmitToJobCurrentStayedOver();
        vo.setStayedOver24Hrs(submitToJobStayedOver != null && submitToJobStayedOver > 0 ? submitToJobStayedOver : null);
        vo.setCurrentStayedOver24Hrs(submitToJobCurrentStayedOver != null && submitToJobCurrentStayedOver > 0 ? submitToJobCurrentStayedOver : null);

        // 设置提交给客户相关指标
        vo.setSubmitToClientNum(appData.getSubmitToClientCountNum());
        vo.setSubmitToClientCurrentNum(appData.getSubmitToClientCurrentCountNum());
        Integer submitToClientStayedOver = appData.getSubmitToClientStayedOver();
        Integer submitToClientCurrentStayedOver = appData.getSubmitToClientCurrentStayedOver();
        vo.setStayedOver72Hrs(submitToClientStayedOver != null && submitToClientStayedOver > 0 ? submitToClientStayedOver : null);
        vo.setCurrentStayedOver72Hrs(submitToClientCurrentStayedOver != null && submitToClientCurrentStayedOver > 0 ? submitToClientCurrentStayedOver : null);

        // 设置面试相关指标
        vo.setFirstInterviewNum(appData.getInterview1());
        vo.setSecondInterviewNum(appData.getInterview2());
        vo.setFinalInterviewNum(appData.getInterviewFinal());
        vo.setInterviewNum(appData.getInterviewTotal());
        vo.setInterviewNumProcess(appData.getInterviewTotalProcess());
        vo.setCurrentFirstInterviewNum(appData.getCurrentInterview1());
        vo.setCurrentSecondInterviewNum(appData.getCurrentInterview2());
        vo.setCurrentFinalInterviewNum(appData.getCurrentInterviewFinal());
        vo.setCurrentInterviewNum(appData.getCurrentInterviewTotal());
        vo.setCurrentInterviewNumProcess(appData.getCurrentInterviewTotalProcess());
        vo.setTwoOrMoreInterviews(appData.getTwoOrMoreInterviews());
        vo.setCurrentTwoOrMoreInterviews(appData.getCurrentTwoOrMoreInterviews());
        vo.setUniqueInterviewTalentNum(appData.getUniqueInterviewTalents());

        // 设置预约面试相关指标
        // 当时间类型是 ADD 时，预约面试的数据就直接是 面试的数据相等
        vo.setInterviewAppointments(appData.getInterviewTotal());
        vo.setCurrentInterviewAppointments(appData.getCurrentInterviewTotal());

        // 设置Offer相关指标
        vo.setOfferNum(appData.getOfferCountNum());
        vo.setOfferCurrentNum(appData.getOfferCurrentCountNum());
        vo.setOfferAcceptNum(appData.getOfferAcceptCountNum());
        vo.setOfferAcceptCurrentNum(appData.getOfferAcceptCurrentCountNum());

        // 设置入职相关指标
        vo.setOnboardNum(appData.getOnboardCountNum());
        vo.setOnboardCurrentNum(appData.getOnboardCurrentCountNum());

        // 设置淘汰相关指标
        vo.setEliminateNum(appData.getEliminateCountNum());
        vo.setEliminateCurrentNum(appData.getEliminateCurrentCountNum());
    }
}
