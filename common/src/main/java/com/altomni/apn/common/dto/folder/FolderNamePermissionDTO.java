package com.altomni.apn.common.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;

@Data
public class FolderNamePermissionDTO {
    @Id
    private Long id;

    private String name;

    @ApiModelProperty(value = "permission of current folder")
    private FolderPermission folderPermission;

    public FolderNamePermissionDTO(Long id, String name, FolderPermission folderPermission) {
        this.id = id;
        this.name = name;
        this.folderPermission = folderPermission;
    }
}
