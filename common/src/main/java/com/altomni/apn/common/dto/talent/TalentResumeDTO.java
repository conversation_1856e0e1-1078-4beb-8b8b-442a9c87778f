package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.dto.redis.ImagesInfoDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentResumeDTO implements Serializable {

    private Long id;

    private String talentName;

    private Long talentId;

    private String companyName;
    private String title;

    private Long resumeId;

    private String fileName;

    private ResumeSourceType sourceType = ResumeSourceType.UNKNOWN;

    @JsonIgnore
    private String text;

    private String uuid;

    private ImagesInfoDTO imagesInfo;

    //改简历绑定了哪些submitToJob流程，简历替换时使用
    private List<TalentResumeBindSubmitToJob> bindSubmitToJobList;
    //编辑时，如果替换了简历，此值为原绑定简历的relation id
    private List<Long> originTalentResumeRelationIds;

    private boolean hasPermission;

    private String createdBy;

    private Instant createdDate;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    private SimpleUser createdUser;
    private SimpleUser lastModifiedUser;

}
