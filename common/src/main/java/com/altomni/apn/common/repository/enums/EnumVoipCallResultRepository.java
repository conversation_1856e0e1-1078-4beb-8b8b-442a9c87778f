package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumEthnicity;
import com.altomni.apn.common.domain.dict.EnumVoipCallResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the Enum VoipCallResult entity.
 */
@Repository
public interface EnumVoipCallResultRepository extends JpaRepository<EnumVoipCallResult, Long> {

}
