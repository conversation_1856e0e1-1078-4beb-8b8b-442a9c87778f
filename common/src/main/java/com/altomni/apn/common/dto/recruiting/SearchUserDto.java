package com.altomni.apn.common.dto.recruiting;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchUserDto implements Serializable {

    private List<Long> userIdList;

    private List<Long> teamIdList;

    private List<UserRole> userRoleList;

    private List<SalesLeadRoleType> salesLeadRoleList;

    private Boolean userActiveStatus;

    //流程创建者或修改者
    private Boolean processOperator;

    private boolean isEmpty() {
        return CollUtil.isEmpty(userIdList) && CollUtil.isEmpty(teamIdList) && CollUtil.isEmpty(userRoleList) && CollUtil.isEmpty(salesLeadRoleList);
    }

    public boolean isNotEmpty() {
        return !isEmpty();
    }
}
