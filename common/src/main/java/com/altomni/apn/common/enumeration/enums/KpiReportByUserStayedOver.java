package com.altomni.apn.common.enumeration.enums;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The StayedOver enumeration.
 */
public enum KpiReportByUserStayedOver implements ConvertedEnum<Integer> {
    TWENTY_FOUR_HRS(0, "twenty_four"),
    SEVENTY_TWO_HRS(1, "seventy_two");




    // static resolving:
    public static final ReverseEnumResolver<KpiReportByUserStayedOver, Integer> resolver =
            new ReverseEnumResolver<>(KpiReportByUserStayedOver.class, KpiReportByUserStayedOver::toDbValue);
    private final int dbValue;
    private final String name;

    KpiReportByUserStayedOver(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static KpiReportByUserStayedOver fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
