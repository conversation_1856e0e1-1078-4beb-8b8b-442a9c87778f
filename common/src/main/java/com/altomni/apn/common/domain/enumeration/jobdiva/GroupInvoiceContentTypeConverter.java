package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class GroupInvoiceContentTypeConverter extends AbstractAttributeConverter<GroupInvoiceContentType, Integer> {
    public GroupInvoiceContentTypeConverter() {
        super(GroupInvoiceContentType::toDbValue, GroupInvoiceContentType::fromDbValue);
    }
}
