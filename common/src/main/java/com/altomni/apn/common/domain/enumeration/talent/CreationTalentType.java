package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobType enumeration.
 */
public enum CreationTalentType implements ConvertedEnum<Integer> {
    UPLOAD_WITH_RESUME(0,"UPLOAD_WITH_RESUME"),
    CREATE_WITHOUT_RESUME(1,"CREATE_WITHOUT_RESUME"),
    BULK_UPLOAD_RESUMES(2,"BULK_UPLOAD_RESUMES"),
    BULK_CREATE_WITH_EXCEL(3, "BULK_CREATE_WITH_EXCEL");


    // static resolving:
    public static final ReverseEnumResolver<CreationTalentType, Integer> resolver =
        new ReverseEnumResolver<>(CreationTalentType.class, CreationTalentType::toDbValue);
    private final int dbValue;
    private final String name;

    CreationTalentType(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static CreationTalentType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
