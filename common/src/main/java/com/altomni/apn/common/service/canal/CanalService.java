package com.altomni.apn.common.service.canal;

import com.altomni.apn.common.domain.canal.CanalSyncRecord;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;

import java.util.Collection;
import java.util.List;

public interface CanalService {

    void insertAll(Collection<Long> taskIdList, SyncIdTypeEnum type, FailReasonEnum failReason, String message, Integer priority);

    List<CanalSyncRecord> findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum type, Integer limit, Integer failedCountLimit);

    void deleteByTaskIdAndType(Long taskId, SyncIdTypeEnum type);

}
