package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum CommentsType implements ConvertedEnum<Integer> {
    TIME_SHEET(0),
    EXPENSE(1);
    private final int dbValue;

    CommentsType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CommentsType, Integer> resolver = new ReverseEnumResolver<>(CommentsType.class, CommentsType::toDbValue);

    public static CommentsType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
