package com.altomni.apn.common.dto.linkedin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LinkedinUsageSearchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String from;

    private String to;

    private Set<Long> userIds;

    private String timeZone;

}
