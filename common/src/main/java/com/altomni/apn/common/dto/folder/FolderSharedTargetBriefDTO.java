package com.altomni.apn.common.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FolderSharedTargetBriefDTO {

    @ApiModelProperty(value = "The id of user/team that shared to")
    private Long id;

    @ApiModelProperty(value = "The user/team permission on current folder ")
    private FolderPermission folderPermission;

    @ApiModelProperty(value = "the name of shared target ")
    private String name;
}
