package com.altomni.apn.common.utils;


import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushTimeType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.*;
import java.time.temporal.ChronoField;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

import static java.util.Arrays.asList;

@Slf4j
public class DateUtil {

    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    public static final String YYYY_MM = "yyyy-MM";

    public static final String YYYY = "yyyy";

    public static final String MM_DD_YYYY = "MM/dd/yyyy";

    public static final String MM_DD_YYYY_ = "MM-dd-yyyy";

    public static final String YYYY_MM_DD_ = "yyyy/MM/dd";

    /** date format pattern */
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /** date format pattern */
    public static final String YYYY_MM_DD_HH_MM_SSS = "yyyy-MM-dd HH:mm:ss.S";

    /** date format pattern */
    public static final String MM_DD_YYYY_HH_MM_SS = "MM/dd/yyyy HH:mm:ss";

    public static final String MM_DD_YYYY_HH_MM_A_ZZZ = "MM/dd/yyyy HH:mm a zzz";

    public static final String MM_DD = "MM/dd";

    public static final String YYYY_MM_DD_T_HH_MM_SS_Z = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    public static final String YYYY_MM_DD_HH_MM_SS_A = "MM/dd/yyyy hh:mm:ss a";

    /** elasticSearch date format date_time or strict_date_time*/
    public static final String YYYY_MM_DD_T_HH_MM_SS_SSS_Z = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";

    /** utc */
    public static final String UTC = "UTC";

    public final static String MAX_DATE = "9999-12-31";

    /** begin time */
    public static final String BEGIN_TIME = " 00:00:00";

    public static final String US_LA_TIMEZONE = "America/Los_Angeles";

    public static final String CN_BJ_TIMEZONE = "Asia/Shanghai";

    private static final Integer OFFSET_DAYS = 2;

    private static List<Calendar> holidayList = new ArrayList<>();

    private static final List<String> HOLIDAYS = Collections.unmodifiableList(asList("01-01-2018", "01-15-2018",
            "05-28-2018", "07-04-2018", "09-03-2018", "11-22-2018", "11-23-2018", "12-25-2018", "12-26-2018", "12-27-2018",
            "12-28-2018", "12-29-2018", "12-30-2018", "12-31-2018", "01-01-2019", "01-21-2019", "05-27-2019", "07-04-2019",
            "09-02-2019", "11-28-2019", "11-29-2019", "12-25-2019", "12-26-2019", "12-27-2019", "12-28-2019", "12-29-2019",
            "12-30-2019", "12-31-2019", "01-01-2020"));

    public DateUtil() {

    }

    static {
        initHolidayList();
    }

    // ############################## holidays methods start #################################

    public static Instant getBackDate(int backDays) {
        if (backDays <= 0) {
            return Instant.now();
        }
        final Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -backDays);
        return cal.getTime().toInstant();
    }

    public static String getWorkDayByOffsetDays(String date) throws ParseException {
        Calendar ca = Calendar.getInstance();
        ca.setTime(new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).parse(date));
        return  addDateByWorkDay(ca);
    }

    private static String addDateByWorkDay(Calendar calendar) {
        for (int i = 0; i < OFFSET_DAYS; i++) {
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            if(checkHoliday(calendar)){
                i--;
            }
        }
        return  new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(calendar.getTime());
    }

    private static boolean checkHoliday(Calendar calendar) {
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
            return true;
        }

        for (Calendar ca : holidayList) {
            if (ca.get(Calendar.MONTH) == calendar.get(Calendar.MONTH)
                && ca.get(Calendar.DAY_OF_MONTH) == calendar.get(Calendar.DAY_OF_MONTH)
                && ca.get(Calendar.YEAR) == calendar.get(Calendar.YEAR)) {
                return true;
            }
        }

        return false;
    }

    private static void initHolidayList() {
        for (String string : HOLIDAYS) {
            String[] da = string.split("-");
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.MONTH, Integer.valueOf(da[0]) - 1);
            calendar.set(Calendar.DAY_OF_MONTH, Integer.valueOf(da[1]));
            calendar.set(Calendar.YEAR, Integer.valueOf(da[2]));
            holidayList.add(calendar);
        }
    }

    // ############################## holidays methods end #################################

    /**
     * cover string to Instant
     * @param date
     * @return
     */
    public static Instant stringToInstanct(String date) {
        if (StringUtils.isNotEmpty(date)) {
            Instant instant = null;
            try {
                instant = Instant.parse(date);
            }catch (DateTimeParseException e) {
                try {
                    instant = LocalDateTime.parse(date).toInstant(ZoneOffset.UTC);
                }catch (DateTimeParseException e1) {
                    try { // format from DB
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SSS);
                        instant = LocalDateTime.parse(date, formatter).toInstant(ZoneOffset.UTC);
                    }catch (DateTimeParseException e2) {
                        try {
                            LocalDate localDate = LocalDate.parse(date);
                            instant = LocalDateTime.of(localDate, LocalTime.MIDNIGHT).atZone(ZoneId.systemDefault()).toInstant();
                        }catch (DateTimeParseException e3) {
                            // print for debug
//                            e3.printStackTrace();
                        }
                    }
                }
            }
            return instant;
        }
        return null;
    }

    public static LocalDate maxDate() {
        return stringToLocalDate(MAX_DATE, YYYY_MM_DD);
    }

    /**
     * cover string to LocalDate
     */
    public static LocalDate stringToLocalDate(String date) {
        return stringToLocalDate(date, YYYY_MM_DD);
    }

    public static LocalDate stringToLocalDate(String date, String format) {
        if (StringUtils.isNotEmpty(date)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return LocalDate.parse(date, formatter);
        }
        return null;
    }

    public static LocalDate strToLocalDate(String date) {
        if (StringUtils.isNotEmpty(date)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);
            return LocalDate.parse(utcTimeToNormalTime(date), formatter);
        }
        return null;
    }

    public static LocalDateTime stringToLocalDateTime(String date, String format) {
        if (StringUtils.isNotEmpty(date)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
            return LocalDateTime.parse(date, formatter);
        }
        return null;
    }

    public static String generateDateString(LocalDate date) {
        if (null != date) {
            return date.format(DateTimeFormatter.ofPattern(MM_DD_YYYY));
        }
        return "";
    }

    public static String localDateToInstant(LocalDate date) {
        date.atStartOfDay().toInstant(ZoneOffset.UTC);
        if (null != date) {
            return date.format(DateTimeFormatter.ofPattern(MM_DD_YYYY));
        }
        return "";
    }

    public static Instant toInstantAtEndOfDay(LocalDate localDate) {
        LocalDateTime localDateTime = LocalDateTime.of(localDate, LocalTime.MAX);
        ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneOffset.UTC);
        return zonedDateTime.toInstant().truncatedTo(ChronoUnit.MILLIS);
    }

    public static String instantToDateString(Instant instant) {
        if (instant != null) {
            return new SimpleDateFormat(MM_DD_YYYY_).format(Date.from(instant));
        } else {
            return "";
        }
    }

    public static String instantToStringUSFormat(Instant instant) {
        if (instant != null) {
            return new SimpleDateFormat(MM_DD_YYYY).format(Date.from(instant));
        } else {
            return "";
        }
    }

    public static String instantToStringWithTime(Instant instant) {
        if (instant != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS).withZone(ZoneId.systemDefault());
            return formatter.format(instant);
        } else {
            return "";
        }
    }

    public static String currentTime() {
        return new SimpleDateFormat(MM_DD_YYYY_HH_MM_SS).format(Calendar.getInstance().getTime());
    }

    public static String currentTime(String format) {
        return new SimpleDateFormat(format).format(Calendar.getInstance().getTime());
    }

    public static String currentUtcTime() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone(UTC));
        return simpleDateFormat.format(Calendar.getInstance().getTime());
    }

    public static String getUtcTimeByOffset(Integer offset) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone(UTC));
        Calendar calendar = Calendar.getInstance(); // Get current time
        calendar.add(Calendar.SECOND, offset);        // Subtract 10 minutes
        Date date = calendar.getTime();   // Get updated time
        return simpleDateFormat.format(date);
    }

    public static String getFirstDayOfCurrentWeek() {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        Date date = cal.getTime();
        return new SimpleDateFormat(MM_DD_YYYY_HH_MM_SS).format(date.getTime());
    }

    public static String utcTimeToTargetZoneTime(String sourceUtcTime, String targetZone) {
        if (StringUtils.isEmpty(sourceUtcTime)) {
            return null;
        }

        try {
            SimpleDateFormat targetFormat = new SimpleDateFormat(MM_DD_YYYY_HH_MM_SS);
            SimpleDateFormat sourceFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
            sourceFormat.setTimeZone(TimeZone.getTimeZone(UTC));
            Date formatUtcTime = sourceFormat.parse(sourceUtcTime);
            targetFormat.setTimeZone(TimeZone.getTimeZone(targetZone));
            return targetFormat.format(formatUtcTime);
        } catch (ParseException e) {
            throw new CustomParameterizedException("Date format is not correct");
        }
    }

    public static String getFirstDayOfCurrentMonth() {
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        Date firstDayOfMonth = calendar.getTime();
        Date utcFirstDate;
        try {
            utcFirstDate = format.parse(format.format(firstDayOfMonth) + BEGIN_TIME);
        } catch (ParseException e) {
            throw new CustomParameterizedException("Date format is not correct");
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        dateFormat.setTimeZone(TimeZone.getTimeZone(UTC));
        return dateFormat.format(utcFirstDate);
    }

    public static String getToday() {
        SimpleDateFormat dateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        dateFormat.setTimeZone(TimeZone.getTimeZone(UTC));
        return dateFormat.format(new Date());
    }

    public static String utcTimeToNormalTime(String utcTime) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
            dateFormat.setTimeZone(TimeZone.getTimeZone(UTC));
            return dateFormat.format(Date.from(Instant.parse(utcTime)));
        } catch (DateTimeParseException e) {
            throw new CustomParameterizedException("Date format is not correct");
        }
    }

    public static String lastDayOfCurrentMonth() {
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
        Calendar ca = Calendar.getInstance();
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMaximum(Calendar.DAY_OF_MONTH));
        return format.format(ca.getTime());
    }

    public static String firstDayOfCurrentMonth() {
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
        Calendar ca = Calendar.getInstance();
        ca.set(Calendar.DAY_OF_MONTH, ca.getActualMinimum(Calendar.DAY_OF_MONTH));
        return format.format(ca.getTime());
    }

    public static String currentDateByInstant(Instant in) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_).withZone(ZoneId.systemDefault());
        return df.format(in);
    }


    public static String firstDayOfNextMonth() {
        SimpleDateFormat format = new SimpleDateFormat(YYYY_MM_DD);
        Calendar ca = Calendar.getInstance();
        ca.set(Calendar.DAY_OF_MONTH, 1);
        ca.add(Calendar.MONTH, 1);
        return format.format(ca.getTime());
    }

    public static String todayYearMonth() {
        return new SimpleDateFormat(YYYY_MM_DD).format(new Date());
    }

    public static String currentYear() {
        return new SimpleDateFormat(YYYY).format(new Date());
    }

    public static String currentYearAndMonth(String date) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(YYYY_MM_DD);
        LocalDate localDate = LocalDate.parse(date, df);
        String month = localDate.getMonthValue() + "";
        if (localDate.getMonthValue() < 10) {
            month = "0" + localDate.getMonthValue();
        }
        return localDate.getYear() + "-" + month;
    }

    public static LocalDate getFirstDayOfCurrentMonth(String date){
        DateTimeFormatter df = DateTimeFormatter.ofPattern(YYYY_MM_DD);
        LocalDate localDate =  LocalDate.parse(date, df);
        return localDate.with(TemporalAdjusters.firstDayOfMonth());
    }

    public static LocalDate getLastDayOfCurrentMonth(String date){
        DateTimeFormatter df = DateTimeFormatter.ofPattern(YYYY_MM_DD);
        LocalDate localDate =  LocalDate.parse(date, df);
        return localDate.with(TemporalAdjusters.lastDayOfMonth());
    }


    public static boolean afterNow(LocalDate date) {
        LocalDate now = LocalDate.now();
        return now.isBefore(date);
    }

    public static boolean beforeNow(LocalDate date) {
        LocalDate now = LocalDate.now();
        return now.isAfter(date);
    }

    public static String currentTimeOfTimezone(String timeZone) {
        return DateTimeFormatter.ofLocalizedDateTime(FormatStyle.FULL).withZone(ZoneId.of(timeZone)).format(Instant.now());
    }

    private static final DateTimeFormatter yearFormat = new DateTimeFormatterBuilder()
        .appendPattern("yyyy")
        .parseDefaulting(ChronoField.MONTH_OF_YEAR, 1)
        .parseDefaulting(ChronoField.DAY_OF_MONTH, 1)
        .toFormatter();
    private static final DateTimeFormatter yearAndMonthFormat = new DateTimeFormatterBuilder()
        .appendPattern("yyyy-MM")
        .parseDefaulting(ChronoField.DAY_OF_MONTH,1)
        .toFormatter();

    private static final DateTimeFormatter formatter = new DateTimeFormatterBuilder()
        .appendOptional(DateTimeFormatter.ISO_DATE)
        .appendOptional(yearAndMonthFormat)
        .appendOptional(yearFormat)
        .toFormatter();

    public static LocalDate esDateStrToLocalDate(String dateStr) {
        return LocalDate.parse(dateStr, formatter);
    }

    public static int compareLocalDateStr(String dateStr1, String dateStr2) {
        if(dateStr1 == null && dateStr2 == null){
            return 0;
        }else if(dateStr1 == null){
            return -1;
        }else if(dateStr2 == null){
            return 1;
        }else{
            LocalDate date1 = LocalDate.parse(dateStr1, formatter);
            LocalDate date2 = LocalDate.parse(dateStr2, formatter);
            return date1.compareTo(date2);
        }
    }

    public static String fromInstantToDate(Instant in) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD).withZone(ZoneId.systemDefault());
       return df.format(in);
    }

    public static String fromInstantToUtcDateTime(Instant in) {
        if (Objects.isNull(in)){
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z).withZone(ZoneId.systemDefault());
        return df.format(in);
    }

    /**
     * yyyy-MM-dd'T'HH:mm:ss.SSSZ
     * @param in
     * @return
     */
    public static String fromInstantToUtcDateTimeWithMillisecond(Instant in) {
        if (Objects.isNull(in)){
            return null;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_T_HH_MM_SS_SSS_Z).withZone(ZoneOffset.UTC);
        return df.format(in);
    }

    public static String fromInstantToDate(Instant in,String timeZone) {
        if (in == null) {
            return "";
        }
        if (timeZone == null) {
            timeZone = US_LA_TIMEZONE;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_MM_SS).withZone(TimeZone.getTimeZone(timeZone).toZoneId());
        return df.format(in);
    }

    public static String fromInstantToDate(Instant in,String timeZone, String format) {
        if (in == null) {
            return "";
        }
        if (timeZone == null) {
            timeZone = US_LA_TIMEZONE;
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format).withZone(TimeZone.getTimeZone(timeZone).toZoneId());
        return df.format(in);
    }


    public static Instant getLastWeekDate(int lastWeek,String timeZone)
    {
        if(timeZone == null){
            timeZone = US_LA_TIMEZONE;
        }
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timeZone));//获取当地时间
        calendar.add(Calendar.DAY_OF_WEEK,-7);//last week
        int offset =  lastWeek-calendar.get(Calendar.DAY_OF_WEEK);
        calendar.add(Calendar.DAY_OF_WEEK,offset);
       return calendar.toInstant();
    }



    public static Instant getCurrentWeekDate(int lastWeek,String timeZone)
    {
        if(timeZone == null){
            timeZone = US_LA_TIMEZONE;
        }
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone(timeZone));//获取当地时间
        int offset =  lastWeek-calendar.get(Calendar.DAY_OF_WEEK);
        calendar.add(Calendar.DAY_OF_WEEK,offset);
        return calendar.toInstant();
    }


    public static String calculateDays(Instant time1,Instant time2)
    {
        double daySeconds = 86400.0;
        Double second1 = Double.valueOf(time1.getEpochSecond());
        Double second2 = Double.valueOf(time2.getEpochSecond());
        double realDays = (second1-second2)/daySeconds;
        double d = Math.ceil(Math.abs(realDays));

        if(d == 0){
            return "0";
        }
        String days = String.valueOf(d).split("\\.")[0];
        return days;

    }

    public static Instant fromStringToInstant(String in){
        if(in == null){
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS);
        Date date = null;
        try {
            date = sdf.parse(in);
        } catch (ParseException e) {
            log.error("error", e);
        }
        return date.toInstant();
    }

    /**
     * format utc time to yyyy-MM-dd HH:mm:ss
     * @param toDate to date
     * @return yyyy-MM-dd HH:mm:ss format date String
     */
    public static String formatToDate(String toDate) {
        String formatTime;
        if (org.apache.commons.lang3.StringUtils.isNotBlank(toDate)) {
            formatTime = DateUtil.utcTimeToNormalTime(toDate);
        } else {
            formatTime = DateUtil.getToday();
        }
        return formatTime;
    }

    public static String fromInstantToDateStringBrief(Instant in, String timezone) {
        if (in == null) {
            return "";
        }
        if (timezone == null) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(MM_DD_YYYY_);
        return formatter.format(in.atZone(ZoneId.of(timezone)));
    }

    public static String fromInstantToDateString(Instant in, String timezone) {
        if (in == null) {
            return "";
        }
        if (timezone == null) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(MM_DD_YYYY_HH_MM_A_ZZZ);
        return formatter.format(in.atZone(ZoneId.of(timezone)));
    }

    public static Instant stringToInstanctTimezone(String date, String timezone) {
        if (StringUtils.isNotEmpty(date)) {
            Instant instant = null;
            try {
                instant = Instant.parse(date);
            } catch (DateTimeParseException e) {
                try {
                    instant = LocalDateTime.parse(date).toInstant(ZoneOffset.UTC);
                } catch (DateTimeParseException e1) {
                    try { // format from DB
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SSS);
                        instant = LocalDateTime.parse(date, formatter).toInstant(ZoneOffset.UTC);
                    } catch (DateTimeParseException e2) {
                        try {
                            LocalDate localDate = LocalDate.parse(date);
                            instant = LocalDateTime.of(localDate, LocalTime.MIDNIGHT).atZone(ZoneId.of(timezone)).toInstant();
                        } catch (DateTimeParseException e3) {
                            // print for debug
//                            e3.printStackTrace();
                        }
                    }
                }
            }
            return instant;
        }
        return null;
    }

    public static String localDateToString(LocalDate from) {
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern(YYYY_MM_DD);
        return from.format(fmt);
    }

    public static String getMonthDayYear(String dateStr) {
        return getDateStrInFormat(dateStr, MM_DD_YYYY);
    }

    private static String getDateStrInFormat(String dateStr, String pattern) {
        if (StrUtil.isBlank(dateStr)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDate date = LocalDate.parse(dateStr);
        return formatter.format(date);
    }

    public static String getDateTimeInCSTWithTimeZone(String dateStr) {
        return getDateTimeInCST(dateStr, YYYY_MM_DD_HH_MM_SS_A, true);
    }

    public static String getDateTimeInCST(String dateStr, String pattern, boolean appendTimeZone) {
        if (StringUtils.isBlank(dateStr)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
        LocalDateTime utcTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern(YYYY_MM_DD_T_HH_MM_SS_Z));
        LocalDateTime cstTime = utcTime.plusHours(8);
        String formattedStr = formatter.format(cstTime);
        if (appendTimeZone) {
            formattedStr += " CST";
        }
        return formattedStr;
    }

    public static String getMonthAndDay(String dateStr) {
        return getDateStrInFormat(dateStr, MM_DD);
    }

    public static String getStandardCurrentTime() {
        return new SimpleDateFormat(YYYY_MM_DD_HH_MM_SS).format(Calendar.getInstance().getTime());
    }

    public static Timestamp getDayAddition(Timestamp date,int i){
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.add(Calendar.DATE, i);  //  加一  天
        //c.add(Calendar.MONTH, 1); //  加一个月
        //c.add(Calendar.YEAR,1);   //  加一  年
        Timestamp time = new Timestamp(c.getTimeInMillis());
        return time;
    }

    /***
     * 生成 日期对应的  cron表达式
     * convert Date to cron ,eg.  "0 06 10 15 1 ? 2014"
     * @param triggerInstant  : 时间点
     * @return String
     */
    public static String getCron(Instant triggerInstant) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("ss mm HH dd MM ? yyyy")
                .withZone(ZoneOffset.UTC);
        return formatter.format(triggerInstant);
    }

    /**
     * 生成 Cron 表达式
     * @param pushTimeType 频率 (EVERY_DAY, EVERY_WEEK, EVERY_MONTH)
     * @param dayOfWeek 周几或者几号
     * @param dayOfMonth 每月的几号
     * @param time 时间 (格式: HH:mm)
     * @param userTimeZone 用户时区 (如 "Asia/Shanghai")
     * @return 对应的 Cron 表达式
     */
    public static String generateCronExpression(PushTimeType pushTimeType, Integer dayOfWeek, Integer dayOfMonth, String time, String userTimeZone) {
        log.info("[APN generateCronExpression] pushTimeType = {}, dayOfWeek = {}, dayOfMonth = {}, time = {}, userTimeZone = {}", pushTimeType, dayOfWeek, dayOfMonth, time, userTimeZone);
        // 解析时间
        String[] timeParts = time.split(":");
        int userHour = Integer.parseInt(timeParts[0]);
        int userMinute = Integer.parseInt(timeParts[1]);

        // 用户时间转换为 UTC 时间
        ZoneId userZoneId = ZoneId.of(userTimeZone);
        ZoneId utcZoneId = ZoneId.of("UTC");
        LocalTime userLocalTime = LocalTime.of(userHour, userMinute);
        ZonedDateTime userZonedDateTime = ZonedDateTime.of(LocalDate.now(), userLocalTime, userZoneId);
        ZonedDateTime utcZonedDateTime = userZonedDateTime.withZoneSameInstant(utcZoneId);

        String minute = String.format("%02d", utcZonedDateTime.getMinute());
        String hour = String.format("%02d", utcZonedDateTime.getHour());

        // 根据频率生成 Cron 表达式
        return switch (pushTimeType) {
            case EVERY_DAY -> String.format("0 %s %s * * ?", minute, hour);
            case EVERY_WEEK -> String.format("0 %s %s ? * %d", minute, hour, dayOfWeek == 7 ? 1 : (dayOfWeek + 1));
            case EVERY_MONTH -> {
                if (dayOfMonth == 32) {
                    yield String.format("0 %s %s L * ?", minute, hour); // 每个月最后一天
                } else {
                    yield String.format("0 %s %s %d * ?", minute, hour, dayOfMonth);
                }
            }
        };
    }

    public static String formatDate(String inputDate) {
        if (inputDate == null || inputDate.isEmpty()) {
            throw new IllegalArgumentException("输入的日期不能为空");
        }

        // 解析输入的 ISO-8601 日期字符串为 Instant
        Instant instant = Instant.parse(inputDate);

        // 转换为 ZonedDateTime (使用 UTC 时区)
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of("UTC"));

        // 定义目标格式
        DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");

        // 格式化并返回
        return zonedDateTime.format(outputFormatter);
    }

    public static boolean isMoreThanDays(Instant createdDate, Integer days) {
        Instant now = Instant.now();  // 获取当前时间
        Duration duration = Duration.between(createdDate, now);  // 计算两个时间点之间的时长
        return duration.toDays() > days;  // 判断是否超过days
    }

    public static String formatDateWithSuffix(LocalDate date) {
        String month = date.getMonth().getDisplayName(TextStyle.FULL, Locale.ENGLISH);
        int day = date.getDayOfMonth();
        int year = date.getYear();
        return String.format("%s %d%s, %d", month, day, getDaySuffix(day), year);
    }

    public static String getDaySuffix(int day) {
        if (day >= 11 && day <= 13) {
            return "th";
        }
        switch (day % 10) {
            case 1: return "st";
            case 2: return "nd";
            case 3: return "rd";
            default: return "th";
        }
    }
}
