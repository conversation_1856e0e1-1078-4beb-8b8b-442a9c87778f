package com.altomni.apn.common.domain.crmenums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.crmenums.DictVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "EnumFollowUpContactType Entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_follow_up_contact_type")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumFollowUpContactType implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Integer id;

    @Column(name = "name")
    private String name;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "cn_display_order")
    private Integer cnDisplayOrder;

    @Column(name = "en_display_order")
    private Integer enDisplayOrder;

    @Transient
    private Boolean cnLable = false;

    public static DictVO fromEnumContactType(EnumFollowUpContactType contactType, SortType sortType) {
        DictVO dto = new DictVO();
        ServiceUtils.myCopyProperties(contactType,dto);
        if (ObjectUtil.isNotEmpty(contactType.getId())) {
            dto.setId(contactType.getId());
        }
        if (sortType.equals(SortType.EN)) {
            dto.setLabel(contactType.getEnDisplay());
            dto.setDisplayOrder(contactType.getEnDisplayOrder());
        } else if (sortType.equals(SortType.CN)){
            dto.setLabel(contactType.getCnDisplay());
            dto.setDisplayOrder(contactType.getCnDisplayOrder());
        }
        if (ObjectUtil.isNotEmpty(contactType.getId())) {
            dto.setValue(StrUtil.toString(contactType.getId()));
        }
        dto.setChecked(false);
        return dto;
    }
}
