package com.altomni.apn.common.dto.xxljob;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XxlJobUpdateBySendTimeForJobAdminDTO {

    //修改的标识
    private Integer xxlJobId;

    //在入参内,用于下一个时间计算
    private String reminderConfig;

    private Instant sendTime;

    private String cron;

    private String timezone;

    private Integer triggerStatus = 1;



}
