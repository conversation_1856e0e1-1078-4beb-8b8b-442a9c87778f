package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class SearchConfigTypeConverter extends AbstractAttributeConverter<SearchConfigType, Integer> {
    public SearchConfigTypeConverter() {
        super(SearchConfigType::toDbValue, SearchConfigType::fromDbValue);
    }
}
