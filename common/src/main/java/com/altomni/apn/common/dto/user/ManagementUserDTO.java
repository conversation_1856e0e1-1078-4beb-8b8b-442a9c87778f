package com.altomni.apn.common.dto.user;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.user.AdminManagementUser;
import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.dto.CredentialDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.apache.cxf.management.ManagementConstants;
import org.springframework.beans.BeanUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * A DTO representing a user, with only the public attributes.
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ManagementUserDTO {

    private Long id;

    private String uid;

    private Long tenantId;

    private String username;

    private String password;

    @ApiModelProperty(value = "first name")
    private String firstName;

    @ApiModelProperty(value = "last name")
    private String lastName;

    @ApiModelProperty(value = "user name")
    private String name;

    @ApiModelProperty(value = "email address. need to be unique.")
    private String email;

    private Boolean activated;

    private CredentialDTO credential;

    private Set<Role> roles = new HashSet<>();

    public static ManagementUserDTO formatFromUser(AdminManagementUser user) {
        ManagementUserDTO managementUserDTO = new ManagementUserDTO();
        BeanUtils.copyProperties(user, managementUserDTO);
        managementUserDTO.setRoles(user.getRoles());
        return managementUserDTO;
    }

}
