package com.altomni.apn.common.dto.redis;

import com.altomni.apn.common.enumeration.ParseStatus;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class ImagesInfoDTO {

    private ParseStatus uploadStatus;

    private String portraitLink;

    private String displayLink;

    private String contentType;

}
