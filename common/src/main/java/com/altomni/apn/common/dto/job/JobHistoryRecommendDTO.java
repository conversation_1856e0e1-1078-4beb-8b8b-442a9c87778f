package com.altomni.apn.common.dto.job;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.math.BigInteger;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobHistoryRecommendDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigInteger jobId;

    private String title;

    private String jobType;

    private String companyName;

    public BigInteger getJobId() {
        return jobId;
    }

    public void setJobId(BigInteger jobId) {
        this.jobId = jobId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType != null ? JobType.fromDbValue(Integer.valueOf(jobType)).getName() : "";
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }
}
