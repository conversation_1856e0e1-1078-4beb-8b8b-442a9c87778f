package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.Instant;

@ApiModel(description = "Enum currency entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_currency")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumCurrency implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Integer id;

    @Column(name = "name")
    private String name;

    @Column(name = "from_usd_rate")
    private Float fromUsdRate;

    @Column(name = "to_usd_rate")
    private Float toUsdRate;

    @Column(name = "pattern")
    private String pattern;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "en_display_order")
    private Integer enDisplayOrder;

    @Column(name = "cn_display_order")
    private Integer cnDisplayOrder;

    @Column(name = "symbol")
    private String label1;

    @Column(name = "code_symbol")
    private String label2;

    @Column(name = "country_code_symbol")
    private String label3;

    @Column(name = "last_rate_update_date")
    private Instant lastRateUpdateDate = Instant.now();
}
