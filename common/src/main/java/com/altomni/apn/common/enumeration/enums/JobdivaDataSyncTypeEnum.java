package com.altomni.apn.common.enumeration.enums;

public enum JobdivaDataSyncTypeEnum {

    TIME_SHEET_USER(100),
    TIME_SHEET_HOLIDAY(110),

    ASSIGNMENT(120),
    ASSIGNMENT_DELETE(121),

    TIME_SHEET_RECORD(130),
    TIME_SHEET_EXPENSE(131),
    TIME_SHEET_APPROVE(132),
    TIME_SHEET_NO_HOURS(133),
    TIME_SHEET_RECORD_BY_EXCEL(134),

    USER(200),
    TALENT(210),
    COMPANY_CONTACT(220),
    COMPANY(221),
    JOB(230)

    ;

    private Integer code;

    JobdivaDataSyncTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }


}
