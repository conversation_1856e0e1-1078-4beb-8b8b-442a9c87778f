package com.altomni.apn.common.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class DataPermissionUtils {

    public static Set<String> getAllParentTeamCodes(Set<String> teamCodes){
        // 4_000900010002
        Set<String> parentTeamCodes = new HashSet<>();
        teamCodes.forEach(teamCode -> {
            parentTeamCodes.add(teamCode);
            while ((teamCode = getParentTeamCode(teamCode)) !=null){
                parentTeamCodes.add(teamCode);
            }
        });
        return parentTeamCodes;
    }

    private static String getParentTeamCode(String teamCode){
        String parentTeamCode = null;
        if (StringUtils.isNotEmpty(teamCode) && teamCode.length() > 5 && teamCode.charAt(teamCode.length() - 5) != '_'){
            parentTeamCode = teamCode.substring(0, teamCode.length() - 4);
        }
        return parentTeamCode;
    }
}
