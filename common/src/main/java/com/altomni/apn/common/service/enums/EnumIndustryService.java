package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.dict.EnumIndustry;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.DisplayType;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class EnumIndustryService {

    private final Logger log = LoggerFactory.getLogger(EnumIndustryService.class);

    @Resource
    private EnumCommonService enumCommonService;

    private final String MEDIA_INTERNET = "129";

    public List<EnumDictDTO> findAllOrderBySortType(SortType type) {
        log.info("[APN: EnumIndustryService @{}] request to get industries enum data, type: {}", SecurityUtils.getUserId(), type);

        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();
        List<EnumIndustry> result = new ArrayList<>();
        if (SortType.EN.equals(type)) {
            enumIndustryList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumIndustry::getEnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
        } else {
            enumIndustryList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumIndustry::getCnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
        }

        List<EnumIndustry> parentCategoryList = findAllParents();
        if (CollectionUtil.isNotEmpty(result) && CollectionUtil.isNotEmpty(parentCategoryList)) {
            if (SortType.EN.equals(type)){
                result.forEach(r -> r.setCnLable(false));
                parentCategoryList.forEach(p -> p.setCnLable(false));
            }else{
                result.forEach(r -> r.setCnLable(true));
                parentCategoryList.forEach(p -> p.setCnLable(true));
            }
            //set parent id by parentCategory and format EnumDictDTO for webSide
            List<EnumDictDTO> dtoList = result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay()))
                    .peek(s -> parentCategoryList.stream().filter(p ->ObjectUtil.isNotNull(s.getParentCategory()) && s.getParentCategory().equals(p.getName()))
                            .forEach(pc ->{
                                s.setParentCategory(StrUtil.toString(pc.getId()));
                            })).collect(Collectors.toList()).stream().map(EnumDictDTO::fromBizDict).collect(Collectors.toList());
            //format children node for webSide
            Map<String, List<EnumDictDTO>> pidListMap =
                    dtoList.stream().collect(Collectors.groupingBy(EnumDictDTO::getParentId));
            dtoList.forEach(item -> {
                List<EnumDictDTO> childrenNodes = pidListMap.get(item.getId());
                if (CollectionUtil.isNotEmpty(childrenNodes)) {
                    item.setChildren(childrenNodes);
                }
            });
            return pidListMap.get(Constants.PARENT_NODE_ID);
        } else {
            return new ArrayList<>();
        }
    }

    private List<EnumIndustry> findAllParents() {
        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();
        Set<String> enumParentCategoryList = enumIndustryList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).map(EnumIndustry::getParentCategory).filter(ObjectUtil::isNotEmpty).collect(Collectors.toSet());
        return enumIndustryList.stream().filter(o -> enumParentCategoryList.contains(o.getName())).collect(Collectors.toList());
    }

    public List<EnumDictDTO> findAllForCreationOrderBySortType(SortType type) {
        log.info("[APN: EnumIndustryService @{}] request to get industries enum data, type: {}", SecurityUtils.getUserId(), type);

        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();
        List<EnumIndustry> result = new ArrayList<>();
        if (SortType.EN.equals(type)) {
            enumIndustryList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumIndustry::getEnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
        } else {
            enumIndustryList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumIndustry::getCnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
        }

        List<EnumIndustry> parentCategoryList = findAllParents();
        if (CollectionUtil.isNotEmpty(result) && CollectionUtil.isNotEmpty(parentCategoryList)) {
            if (SortType.EN.equals(type)){
                result.forEach(r -> r.setCnLable(false));
                parentCategoryList.forEach(p -> p.setCnLable(false));
            }else{
                result.forEach(r -> r.setCnLable(true));
                parentCategoryList.forEach(p -> p.setCnLable(true));
            }
            //set parent id by parentCategory
            result.forEach(s -> {
                for (EnumIndustry p : parentCategoryList) {
                    p.setIsParent(true);
                    if (ObjectUtil.isNotNull(s.getParentCategory()) && s.getParentCategory().equals(p.getName())) {
                        s.setParentCategory(p.getId() + Constants.PARENT_EN);
                        break;
                    }
                }
            });
            //format EnumDictDTO for webSide
            List<EnumDictDTO> dtoList = result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay())).map(EnumDictDTO::formatForCreation).collect(Collectors.toList());
            //format children node for webSide
            Map<String, List<EnumDictDTO>> pidListMap =
                    dtoList.stream().collect(Collectors.groupingBy(EnumDictDTO::getParentId));
            dtoList.forEach(item -> {
                List<EnumDictDTO> childrenNodes = pidListMap.get(item.getId());
                if (CollectionUtil.isNotEmpty(childrenNodes)) {
                    EnumDictDTO otherNode = new EnumDictDTO();
                    otherNode.setId(StrUtil.removeSuffix(item.getId(), Constants.PARENT_EN));
                    if(SortType.CN.equals(type)){
                        otherNode.setLabel(item.getLabel() + Constants.OTHER_CN);
                    }else{
                        otherNode.setLabel(item.getLabel() + Constants.OTHER_EN);
                    }
                    otherNode.setLabelEn(item.getLabelEn() + Constants.OTHER_EN);
                    otherNode.setValue(StrUtil.removeSuffix(item.getId(), Constants.PARENT_EN));
                    otherNode.setParentId(item.getId());
                    otherNode.setChecked(false);
                    otherNode.setJobFunctionsForIndustryId(item.getJobFunctionsForIndustryId());
                    childrenNodes.add(otherNode);
                    item.setChildren(childrenNodes);
                }
            });
            return pidListMap.get(Constants.PARENT_NODE_ID);
        } else {
            return new ArrayList<>();
        }
    }

    public List<String> transferIndustriesByIds(List<String> ids) {
        log.info("[APN: EnumIndustryService @{}] request to transfer industries by ids, ids: {}", SecurityUtils.getUserId(), ids);
        if (ObjectUtil.isEmpty(ids)) {
            return null;
        }
        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();
        List<EnumIndustry> list = enumIndustryList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).collect(Collectors.toList());
        return list.stream().map(EnumIndustry::getName).distinct().collect(Collectors.toList());
    }

    public List<String> getIndustriesByIds(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return null;
        }

        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();
        return enumIndustryList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).map(EnumIndustry::getName).collect(Collectors.toList());
    }

    public Map<Long, String> getIndustriesMap() {
        return enumCommonService.findAllEnumIndustry().stream().collect(Collectors.toMap(EnumIndustry::getId, EnumIndustry::getName));
    }


    public List<Long> transferIndustriesByNamesToId(Object nameObject) {
        log.info("[APN: EnumIndustryService @{}] request to transfer industries by itemTier, nameObject: {}", SecurityUtils.getUserId(), nameObject);
        if (ObjectUtil.isEmpty(nameObject)) {
            return null;
        }
        return formatIndustriesByNames(nameObject).stream().map(EnumIndustry::getId).collect(Collectors.toList());
    }

    public List<Long> transferIndustriesByNamesToIdWithoutIgnoreParentClass(cn.hutool.json.JSONArray names) {
        if (ObjectUtil.isEmpty(names)) {
            return null;
        }

        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();
        Set<String> formatList = Convert.toSet(String.class, names);
        List<EnumIndustry> list = enumIndustryList.stream().filter(o -> formatList.contains(o.getName()) && ObjectUtil.isNotEmpty(o.getEnDisplay())).collect(Collectors.toList());
        return list.stream().map(EnumIndustry::getId).collect(Collectors.toList());
    }

    public List<String> getIndustriesUINameByIds(String ids) {
        log.info("[APN: EnumIndustryService @{}] request to get industries UINames by ids, nameObject: {}", SecurityUtils.getUserId(), ids);
        Set<String> idList = Convert.toSet(String.class, JSONUtil.parseArray(ids));
        if (CollectionUtil.isEmpty(idList)) {
            return null;
        }

        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();
        List<EnumIndustry> list = enumIndustryList.stream().filter(o -> idList.contains(String.valueOf(o.getId()))).collect(Collectors.toList());
        List<EnumIndustry> childrenList = findAllByParentCategoryIn(list.stream().map(EnumIndustry::getName).collect(Collectors.toSet()));
        List<String> parentNamesList = childrenList.stream().distinct().filter(d -> StringUtils.isNotBlank(d.getEnDisplay())).map(EnumIndustry::getParentCategory).collect(Collectors.toList());
        return list.stream().map(s -> {
            String enDisplay = s.getEnDisplay();
            if (parentNamesList.contains(s.getName())) {
                enDisplay = s.getEnDisplay() + Constants.OTHER_EN;
            }
            return enDisplay;
        }).filter(StrUtil::isNotBlank).collect(Collectors.toList());
    }

    private List<EnumIndustry> findAllByParentCategoryIn(Set<String> nameSet) {
        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();
        return enumIndustryList.stream().filter(o -> nameSet.contains(o.getParentCategory())).collect(Collectors.toList());
    }

    public List<String> transferIndustriesByNames(Object nameObject) {
        if (ObjectUtil.isEmpty(nameObject)) {
            return null;
        }
        return formatIndustriesByNames(nameObject).stream().map(EnumIndustry::getEnDisplay).collect(Collectors.toList());
    }

    private List<EnumIndustry> formatIndustriesByNames(Object nameObject) {
        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();
        Map<String, EnumIndustry> enumIndustryMap = enumIndustryList.stream().collect(Collectors.toMap(EnumIndustry::getName, Function.identity(), (key1, key2) -> key2));
        List<EnumIndustry> list = new ArrayList<>();
        if (nameObject instanceof String) {
            list = enumIndustryList.stream().filter(o -> o.getName().equals(ObjectUtil.toString(nameObject)) && ObjectUtil.isNotEmpty(o.getEnDisplay())).collect(Collectors.toList());
            //if this industry is inactived, try to find ancestor
            if (CollUtil.isEmpty(list)) {
                findAncestorByName(list, enumIndustryMap, ObjectUtil.toString(nameObject));
            }
        }
        if (nameObject instanceof List || nameObject instanceof String[]) {
            //if has any industry is inactived, try to find ancestor
            List<EnumIndustry> inactivedList = new ArrayList<>();
            findAncestorByNameIn(inactivedList, enumIndustryMap, Convert.toList(String.class, nameObject));
            List<String> parentNameList = inactivedList.stream().map(EnumIndustry::getParentCategory).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            //distinct and keep a subset
            list = inactivedList.stream().filter(s -> !parentNameList.contains(s.getName())).collect(Collectors.toList());
        }
        return list.stream().distinct().collect(Collectors.toList());
    }

    private void findAncestorByName(List<EnumIndustry> result, Map<String, EnumIndustry> enumIndustryMap, String name) {

        EnumIndustry enumIndustry = enumIndustryMap.get(name);
        if (ObjectUtil.isEmpty(enumIndustry)) {
            return;
        }

        if (ObjectUtil.isEmpty(enumIndustry.getParentCategory())) {
            result.add(enumIndustry);
        } else {
            if (ObjectUtil.isNotEmpty(enumIndustry.getEnDisplay())) {
                result.add(enumIndustry);
            } else {
                name = enumIndustry.getParentCategory();
                findAncestorByName(result, enumIndustryMap, name);
            }
        }
    }

    private void findAncestorByNameIn(List<EnumIndustry> result, Map<String, EnumIndustry> enumIndustryMap, List<String> nameList) {
        nameList.forEach(o -> findAncestorByName(result, enumIndustryMap, o));
    }

    /***
     * Activity Change function: get the UI name through the enumId;
     * @param id
     * @param displayType
     * @return
     */
    public String getIndustryUINameById(Long id, DisplayType displayType) {
        log.info("[APN: EnumIndustryService @{}] request to get industry UINames by ids, nameObject: {}", SecurityUtils.getUserId(), id);

        List<EnumIndustry> enumIndustryList = enumCommonService.findAllEnumIndustry();

        Optional<EnumIndustry> enumIndustryOpt = enumIndustryList.stream()
                .filter(enumIndustry -> enumIndustry.getId().equals(id))
                .findFirst();

        if(enumIndustryOpt.isPresent()) {
            EnumIndustry enumIndustry = enumIndustryOpt.get();
            if(displayType.equals(DisplayType.EN) && StringUtils.isNotBlank(enumIndustry.getEnDisplay())) {
                return enumIndustry.getEnDisplay();
            } else if (displayType.equals(DisplayType.CN) && StringUtils.isNotBlank(enumIndustry.getCnDisplay())) {
                return enumIndustry.getCnDisplay();
            } else {
                return enumIndustry.getName();
            }
        }
        return "";
    }


}
