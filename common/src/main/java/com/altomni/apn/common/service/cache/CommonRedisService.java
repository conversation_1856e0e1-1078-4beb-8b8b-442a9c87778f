package com.altomni.apn.common.service.cache;

import cn.hutool.core.convert.Convert;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.dto.redis.ImagesInfoParser;
import com.altomni.apn.common.dto.redis.ParserRedisResponse;
import com.altomni.apn.common.dto.redis.RedisResponse;
import com.altomni.apn.common.enumeration.ParseStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import redis.clients.jedis.*;

import java.nio.charset.Charset;
import java.util.*;

@Slf4j
@Service
@RefreshScope
public class CommonRedisService {

    private static JedisPool pool;

    private static JedisPoolConfig jedisPoolConfig;

    private static JedisPool raterPool;

    private static JedisPool parserPool;

    private static final int maxIdle = 10;

    private static final long maxWaitMillis = 50 * 1000;

    private static final int maxActive = 100;

    private static final int timeout = 2000;

    @Value("${spring.redis.host}")
    private String host;

    @Value("${spring.redis.port}")
    private Integer port;

    @Value("${spring.redis.database}")
    private Integer db;


    @Value("${spring.redis.host-rater}")
    private String redisHostRater;
    @Value("${spring.redis.port-rater}")
    private Integer redisPortRater;

    @Value("${spring.redis.host-parser}")
    private String redisHostParser;

    @Value("${spring.redis.port-parser}")
    private Integer redisPortParser;


    static {
        try {
            jedisPoolConfig = new JedisPoolConfig();
            jedisPoolConfig.setMaxTotal(maxActive);
            jedisPoolConfig.setMaxWaitMillis(maxWaitMillis);
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setTestOnBorrow(true);
            jedisPoolConfig.setTestOnReturn(true);
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    private void close(Jedis jedis) {
        if (jedis != null) {
            try {
                jedis.close();
            } catch (Exception e) {
                log.error("close jedis connection error: {}", e.getMessage());
                try {
                    jedis.disconnect();
                } catch (Exception e1) {
                    log.error("disconnect jedis connection error: {}" , e1.getMessage());
                }
            }
        }
    }

    private synchronized Jedis getRaterJedis() {
        if (raterPool == null) {
            raterPool = new JedisPool(jedisPoolConfig, redisHostRater, redisPortRater, timeout, null, db);
        }
        try {
            return raterPool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 1st error message: {}", e.getMessage());
        }
        try {
            return raterPool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 2nd error message: {}", e.getMessage());
        }
        return raterPool.getResource();
    }

    private synchronized Jedis getJedis() {
        if (pool == null) {
            pool = new JedisPool(jedisPoolConfig, host, port, timeout, null, db);
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 1st error message: {}", e.getMessage());
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 2nd error message: {}", e.getMessage());
        }
        return pool.getResource();
    }

    private synchronized Jedis getJedisForParser() {
        if (parserPool == null) {
            parserPool = new JedisPool(jedisPoolConfig, redisHostParser, redisPortParser, timeout, null, db);
        }
        try {
            return parserPool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 1st error message: {}", e.getMessage());
        }
        try {
            return parserPool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 2cnd error message: {}", e.getMessage());
        }
        return parserPool.getResource();
    }

    public Jedis getJedisInstance() {
        return getJedis();
    }

    public Jedis getJedisParserInstance() {
        return getJedisForParser();
    }

    public String get(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null && jedis.exists(key)) {
                return jedis.get(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    public Set<String> smembers(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null && jedis.exists(key)) {
                return jedis.smembers(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    public void set(String key, String value, Integer expireSecond) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null){
                jedis.set(key, value);
                if (Objects.nonNull(expireSecond)){
                    jedis.expire(key, expireSecond);
                }
            }
        } catch (Exception e){
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally{
            close(jedis);
        }
    }

    public void set(String key, String value) {
        this.set(key, value, null);
    }

    public Boolean exists(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.exists(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return Boolean.FALSE;
    }

    public boolean contains(String key, String value) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null){
                return jedis.sismember(key, value);
            }
        } catch (Exception e){
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally{
            close(jedis);
        }
        return Boolean.FALSE;
    }

    public void sadd(String key, Set<String> values, Integer expiration) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.sadd(key, values.toArray(new String[0]));
                if (Objects.nonNull(expiration)){
                    jedis.expire(key, expiration);
                }
            }
        } catch (Exception e){
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally{
            close(jedis);
        }
    }

    public void sadd(String key, Collection<Long> userIds, int expire) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                if (CollectionUtils.isNotEmpty(userIds)) {
                    // 将 userIds 转为 String[]
                    String[] userIdStrs = userIds.stream()
                            .map(String::valueOf)
                            .toArray(String[]::new);

                    jedis.sadd(key, userIdStrs);
                    jedis.expire(key, expire);
                }
            }
        } catch (Exception e) {
            log.error("Redis error in sadd: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public boolean sismember(String key, Long userId) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.sismember(key, userId.toString());
            }
        } catch (Exception e) {
            log.error("Redis error in sismember: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return false;
    }

    public void srem(String key, Long userId) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.srem(key, userId.toString());
            }
        } catch (Exception e) {
            log.error("Redis error in markAsRead: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    /**
     * get a set size by key
     * @param key
     */
    public Long scard(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null){
                return jedis.scard(key);
            }
        } catch (Exception e){
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally{
            close(jedis);
        }
        return -1L;
    }

    public Long delete(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.del(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    public void deleteBatch(Collection<String> keys ) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                Pipeline pipelined = jedis.pipelined();
                keys.forEach(pipelined::del);
                pipelined.sync();
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public void deleteByKeyPattern(String keyPattern) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.keys(keyPattern).forEach(jedis::del);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }


    public String set(String key, String value, String nxxx, String expx, long time) {
        Jedis jedis = null;
        String resultValue = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                resultValue = jedis.set(key, value, nxxx, expx, time);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return resultValue;
    }

    public void softExpireByKeyPattern(String keyPattern, Integer expireSeconds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null){
                final Set<String> keys = jedis.keys(keyPattern);
                for (String key : keys) {
                    final int preExpires = jedis.ttl(key).intValue();
                    if (preExpires < 0){
                        jedis.expire(key, expireSeconds);
                    }else{
                        jedis.expire(key, Math.min(preExpires, expireSeconds));
                    }
                }
            }
        } catch (Exception e){
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally{
            close(jedis);
        }
    }

    public void softExpire(String key, Integer expireSeconds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null){
                final int preExpires = jedis.ttl(key).intValue();
                if (preExpires < 0){
                    jedis.expire(key, expireSeconds);
                }else{
                    jedis.expire(key, Math.min(preExpires, expireSeconds));
                }
            }
        } catch (Exception e){
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally{
            close(jedis);
        }
    }

    public List<String> scan(String pattern) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                ScanParams params = new ScanParams();
                params.count(10_000_000);
                params.match(pattern);
                return jedis.scan("0", params).getResult();
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    public List<String> getAllOnlineUsersByTenantId(Long tenantId) {
        return this.scan(String.format(RedisConstants.STATISTIC_USER_ONLINE, tenantId, "*"));
    }

    public List<String> getAllOnlineUsers(){
        return this.scan(String.format(RedisConstants.STATISTIC_USER_ONLINE, "*", "*"));
    }

    public Long getTTL(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.ttl(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return -1L;
    }

    public Long incr(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.incr(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return -1L;
    }

    public List<String> getListData(String key, int startIndex, int endIndex) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.lrange(key, startIndex, endIndex);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return new ArrayList<>();
    }

    public void lPushWithMaxSizeAndExpire(String key, String value, int maxSize, int seconds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                Pipeline pipeline = jedis.pipelined();
                pipeline.lrem(key, 1, value);
                pipeline.lpush(key, value);
                pipeline.ltrim(key, 0, maxSize - 1);
                pipeline.expire(key, seconds);
                pipeline.sync();
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public void lPushStringListWithMaxSizeAndExpire(String key, List<String> values, int maxSize, int seconds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                Pipeline pipeline = jedis.pipelined();
                values.forEach(value -> pipeline.lpush(key, value));
                pipeline.ltrim(key, 0, maxSize - 1);
                pipeline.expire(key, seconds);
                pipeline.sync();
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public void hadd(String key, String field, Long value,Integer expireSeconds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.hincrBy(key, field, value);
                final int preExpires = jedis.ttl(key).intValue();
                if (preExpires < 0) {
                    jedis.expire(key, expireSeconds);
                } else {
                    jedis.expire(key, Math.min(preExpires, expireSeconds));
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public Long setNxAndExpire(String key, String value, Long seconds) {
        Jedis jedis = null;
        Long resultValue = 0L;
        try {
            jedis = getJedis();
            if (jedis != null) {
                String script = "local key = KEYS[1]\n" +
                        "local value = ARGV[1]\n" +
                        "local ttl = tonumber(ARGV[2])\n" +
                        "\n" +
                        "local exists = redis.call('SETNX', key, value)\n" +
                        "\n" +
                        "if exists == 1 then\n" +
                        "    if ttl > 0 then\n" +
                        "        redis.call('EXPIRE', key, ttl)\n" +
                        "    end\n" +
                        "end\n" +
                        "\n" +
                        "return exists\n";
                Object result = jedis.eval(script, 1, key, value, String.valueOf(seconds));
                resultValue = Long.parseLong(String.valueOf(result));
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return resultValue;
    }

    /**
     * 释放分布式锁
     *
     * @param key      锁的唯一标识
     * @param requestId 请求标识，用于验证是否持有锁
     * @return 是否成功释放锁
     */
    public boolean releaseLock(String key, String requestId) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            // Lua 脚本确保原子性：只有匹配 requestId 的锁才能被删除
            String luaScript = """
                        if redis.call('get', KEYS[1]) == ARGV[1] then
                            return redis.call('del', KEYS[1])
                        else
                            return 0
                        end
                    """;

            Object result = jedis.eval(luaScript, 1, key, requestId);
            return "1".equals(result.toString());
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return false;
    }

    public Integer batchDeleteByPattern(String pattern){
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                Set<String> matchingKeys = new HashSet<>();
                ScanParams params = new ScanParams();
                params.match(pattern);
                String nextCursor = "0";
                do {
                    ScanResult<String> scanResult = jedis.scan(nextCursor, params);
                    List<String> keys = scanResult.getResult();
                    nextCursor = scanResult.getStringCursor();
                    matchingKeys.addAll(keys);

                } while(!nextCursor.equals("0"));
                jedis.del(matchingKeys.toArray(new String[matchingKeys.size()]));
                return matchingKeys.size();
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return 0;
    }

    public Boolean checkRaterRedisKey(String prefix) {
        Jedis jedis = null;
        try {
            jedis = getRaterJedis();
            if (jedis != null) {
                // 设置 SCAN 参数
                ScanParams scanParams = new ScanParams();
                scanParams.match(prefix);
                scanParams.count(100); // 每次扫描的数量，可根据实际情况调整

                String cursor = "0";

                do {
                    // 执行 SCAN 命令
                    ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                    cursor = scanResult.getStringCursor();

                    // 检查返回的 key 列表
                    for (String key : scanResult.getResult()) {
                        // 检查 key 的类型是否为 sorted set
                        if ("zset".equals(jedis.type(key))) {
                            return true;
                        }
                    }

                } while (!cursor.equals(ScanParams.SCAN_POINTER_START));
            }
        } catch (Exception e) {
            log.error("************Get talent data error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return false;
    }

    public void hset(String key, Map<String, String> set, int expire) {
        if (set == null) {
            return;
        }
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Map.Entry<String, String> entry : set.entrySet()) {
                    jedis.hset(key, entry.getKey(), entry.getValue());
                }
                jedis.expire(key, expire);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public void hset(String key, String field, String value) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.hset(key, field, value);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public String hget(String key, String field) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.hget(key, field);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    public RedisResponse getDataForJob(String key, Pageable pageable) {
        RedisResponse result = new RedisResponse();
        log.info("************Get job data, redis query key: {}", key);
        Jedis jedis = null;
        try {
            jedis = getRaterJedis();
            if (jedis != null) {
                if (jedis.exists(key + RedisConstants.DATA_KEY_STATUS)) {
                    if (pageable == null) {
                        result.setData(jedis.zrevrangeByScoreWithScores(key + RedisConstants.DATA_KEY_RECOMMENDATIONS, 1, 0));
                    } else {
                        result.setData(jedis.zrevrangeByScoreWithScores(key + RedisConstants.DATA_KEY_RECOMMENDATIONS, 1, 0, pageable.getPageSize() * pageable.getPageNumber(), pageable.getPageSize()));
                    }
                }
                result.setStatus(jedis.get(key + RedisConstants.DATA_KEY_STATUS));
                result.setTotal(valueOf(jedis.get(key + RedisConstants.DATA_KEY_TOTAL)));
            }
        } catch (Exception e) {
            log.error("************Get job data error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    public RedisResponse getDataForJobByCommonTalent(String key, Pageable pageable) {
        RedisResponse result = new RedisResponse();
        Jedis jedis = null;
        try {
            jedis = getRaterJedis();
            if (jedis != null) {
                if (jedis.exists(key + RedisConstants.DATA_KEY_STATUS)) {
                    if (pageable == null) {
                        result.setData(jedis.zrevrangeByScoreWithScores(key + RedisConstants.DATA_KEY_RECOMMENDATIONS, 1, 0));
                    } else {
                        result.setData(jedis.zrevrangeByScoreWithScores(key + RedisConstants.DATA_KEY_RECOMMENDATIONS, 1, 0, pageable.getPageSize() * pageable.getPageNumber(), pageable.getPageSize()));
                    }
                }
                result.setStatus(jedis.get(key + RedisConstants.DATA_KEY_STATUS));
                result.setTotal(valueOf(jedis.get(key + RedisConstants.DATA_KEY_TOTAL)));
            }
        } catch (Exception e) {
            log.error("************Get job data error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    public void deleteRaterRedisKeyWildcard(String key) {
        Jedis jedis = null;
        try {
            jedis = getRaterJedis();
            if (jedis != null) {
                String pattern = key + ":*";
                ScanParams scanParams = new ScanParams().match(pattern).count(100);
                String cursor = ScanParams.SCAN_POINTER_START;

                do {
                    ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                    List<String> keys = scanResult.getResult();

                    if (!keys.isEmpty()) {
                        jedis.del(keys.toArray(new String[0]));
                    }

                    cursor = scanResult.getStringCursor();
                } while (!cursor.equals(ScanParams.SCAN_POINTER_START));
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public void saveFailedTalentIds(Collection<Long> talentIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long talentId : talentIds) {
                    jedis.sadd(RedisConstants.DATA_KEY_SYNCES_FAILED_TALENT, talentId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public void hsetForParserRedis(String key, Map<String, String> set, int expire) {
        if (set == null) {
            return;
        }
        Jedis jedis = null;
        try {
            jedis = getJedisForParser();
            if (jedis != null) {
                for (Map.Entry<String, String> entry : set.entrySet()) {
                    jedis.hset(key, entry.getKey(), entry.getValue());
                }
                jedis.expire(key, expire);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public ParserRedisResponse getParserResumeData(String uuid) {
        ParserRedisResponse result = new ParserRedisResponse();
        String key = com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_PARSER + com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_RESUME + uuid;
        Jedis jedis = null;
        try {
            jedis = getJedisForParser();
            if (jedis != null)
            {
                List<byte[]>  results = jedis.mget((key + com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_STATUS).getBytes(),(key + com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_DATA).getBytes());
                if(results == null || results.size() == 0) {
                    return result;
                }
                if(results.get(0) != null) {
                    result.setStatus(ParseStatus.valueOf(new String(results.get(0), Charset.forName("UTF-8"))));
                }
                if(results.get(1) != null) {
                    result.setData(new String(results.get(1), Charset.forName("UTF-8")));
                }
                Map<byte[], byte[]> map = jedis.hgetAll((key + com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_IMAGES_INFO).getBytes());
                if (map != null) {
                    Map nmap = new HashMap();
                    map.entrySet().stream().forEach(entry -> {
                                nmap.put(new String(entry.getKey(), Charset.forName("UTF-8")), new String(entry.getValue(), Charset.forName("UTF-8")));
                            }
                    );
                    result.setImagesInfo(Convert.convert(ImagesInfoParser.class, nmap));
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    public int getSetCount(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.scard(key).intValue();
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }

        return 0;
    }

    public boolean setExist(String key, String field) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            return jedis.sismember(key, field);
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return false;
    }

    public void addSetValue(String key, String field) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (!jedis.exists(key)) {
                // 新建 set 类型并设置过期时间
                Long sadd = jedis.sadd(key, field);
                // 1 天的过期时间
                jedis.expire(key, 24 * 60 * 60);
            } else {
                // 存入 field
                jedis.sadd(key, field);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public void saveTalentId(Collection<Long> talentIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long talentId : talentIds) {
                    jedis.sadd(com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_SYNCES_TALENT, talentId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    private Integer valueOf(String s) {
        return StringUtils.isEmpty(s) ? 0 : Integer.parseInt(s);
    }

    public ParserRedisResponse getParserJDData(String uuid) {
        ParserRedisResponse result = new ParserRedisResponse();
        String key = RedisConstants.DATA_KEY_PARSER + RedisConstants.DATA_KEY_JD + uuid;
        Jedis jedis = null;
        try {
            jedis = getJedisForParser();
            if (jedis != null)
            {
                List<String>  results = jedis.mget(key + RedisConstants.DATA_KEY_STATUS,key + RedisConstants.DATA_KEY_DATA,key + RedisConstants.DATA_KEY_LAST_UPDATE_TIME);
                if(results == null || results.size() == 0){
                    return result;
                }
                result.setStatus(ParseStatus.valueOf(results.get(0)));
                result.setData(results.get(1));
                result.setLastUpdateTime(results.get(2));
                Map<byte[], byte[]> map = jedis.hgetAll((key + RedisConstants.DATA_KEY_IMAGES_INFO).getBytes());
                if (map != null) {
                    Map nmap = new HashMap();
                    map.entrySet().stream().forEach(entry -> {
                                nmap.put(new String(entry.getKey(), Charset.forName("UTF-8")), new String(entry.getValue(), Charset.forName("UTF-8")));
                            }
                    );
                    result.setImagesInfo(Convert.convert(ImagesInfoParser.class, nmap));
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    public RedisResponse getRaterData(String prefixKey, String dataKey, String totalKey, Pageable pageable) {
        RedisResponse result = new RedisResponse();
        //String key = RedisConstants.DATA_KEY_RATER + uuid;
        Jedis jedis = null;
        try {
            jedis = getRaterJedis();
            if (jedis != null) {
                if (jedis.exists(prefixKey + dataKey)) {
                    if (pageable != null) {
                        result.setData(jedis.zrevrangeByScoreWithScores(prefixKey + dataKey, 1, 0, pageable.getPageSize() * pageable.getPageNumber(), pageable.getPageSize()));
                    } else {
                        result.setData(jedis.zrevrangeByScoreWithScores(prefixKey + dataKey, 1, 0));
                    }
                }
                result.setStatus(jedis.get(prefixKey + RedisConstants.DATA_KEY_STATUS));
                result.setTotal(valueOf(jedis.get(prefixKey + totalKey)));
            }
        } catch (Exception e) {
            log.error("************Get talent data error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    public void saveFailedJobIds(Collection<Long> jobIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long jobId : jobIds) {
                    jedis.sadd(RedisConstants.DATA_KEY_SYNCES_FAILED_JOB, jobId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public void saveFailedAgencyJobIds(Collection<Long> jobIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long jobId : jobIds) {
                    jedis.sadd(RedisConstants.DATA_KEY_SYNCES_FAILED_AGENCY_JOB, jobId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    public Set<String> getJobIds(long count) {
        Set<String> result = new HashSet<>();
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                if (jedis.exists(RedisConstants.DATA_KEY_SYNCES_JOB)){
                    result.addAll(jedis.spop(RedisConstants.DATA_KEY_SYNCES_JOB, count));
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    public void saveData(String key, String value, int expireSecond) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null)
            {
                jedis.set(key, value);
                jedis.expire(key, expireSecond);
            }
        } catch (Exception e)
        {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally
        {
            close(jedis);
        }
    }
}
