package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TalentContactVerificationStatusConverter extends AbstractAttributeConverter<TalentContactVerificationStatus, Integer> {
    public TalentContactVerificationStatusConverter() {
        super(TalentContactVerificationStatus::toDbValue, TalentContactVerificationStatus::fromDbValue);
    }
}
