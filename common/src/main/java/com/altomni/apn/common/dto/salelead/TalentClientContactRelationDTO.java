package com.altomni.apn.common.dto.salelead;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class TalentClientContactRelationDTO {

    private Long talentId;


    @ApiModelProperty(value = "flag to indicate current talent is client contact")
    private Long contactId;

    public TalentClientContactRelationDTO(Long talentId, Long contactId) {
        this.talentId = talentId;
        this.contactId = contactId;
    }
}
