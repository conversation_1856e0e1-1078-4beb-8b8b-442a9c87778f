package com.altomni.apn.common.service.activity;

import cn.hutool.core.collection.CollectionUtil;
import com.altomni.apn.common.domain.activity.ActivityConfig;
import com.altomni.apn.common.domain.enumeration.activity.SourceType;
import com.altomni.apn.common.dto.activity.ActivityChangeDTO;
import com.altomni.apn.common.dto.activity.ActivityESRequestDTO;
import com.altomni.apn.common.dto.activity.ActivityQueryDTO;
import com.altomni.apn.common.dto.activity.ChangeFieldDTO;
import com.altomni.apn.common.enumeration.enums.DisplayType;
import com.altomni.apn.common.repository.activity.ActivityConfigRepository;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@EnableCaching
@Slf4j
@CacheConfig(cacheNames = {"activity_config"}, cacheManager = "concurrentMapCacheManager")
public class ActivityConfigService {

    final static String UNKNOWN = "Unknown";
    @Resource
    private ActivityConfigRepository activityConfigRepository;

    @Cacheable(key = "'activityConfig'", unless = "#result == null")
    public List<ActivityConfig> findAllActivityConfig() {
        log.info("[APN: ActivityConfigService @{}] request to get all activity config data", SecurityUtils.getUserId());

        List<ActivityConfig> result = activityConfigRepository.findAll();
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        return result;
    }
    public Map<String, ActivityConfig> findAllJobActivityConfig(SourceType sourceType) {
        List<ActivityConfig> activityConfigs = findAllActivityConfig();

        return activityConfigs.stream().filter(config -> config.getSourceType().equals(sourceType))
                .collect(Collectors.toMap(ActivityConfig::getFieldName, Function.identity()));
    }

    /***
     * invoke the method call by get activity config
     * @param sourceClass the class has all injected service with corresponding method
     * @param serviceName
     * @param methodName method will be invoked to get UI name by identify
     * @param args args to be assigned into method
     * @return
     */
    public static Object callServiceMethodDynamically(Object sourceClass, String serviceName, String methodName, Object[] args) {
        try {
            // Get the field (service)
            Field field = sourceClass.getClass().getDeclaredField(serviceName);
            field.setAccessible(true);
            Object service = field.get(sourceClass);

            // Prepare method parameters
            Class<?>[] argClasses = Arrays.stream(args)
                    .map(Object::getClass)
                    .toArray(Class<?>[]::new);

            // Get the method
            Method method = service.getClass().getDeclaredMethod(methodName, argClasses);
            method.setAccessible(true);

            // Invoke the method
            Object result = method.invoke(service, args);
            return result;

        } catch (NoSuchFieldException | IllegalAccessException | NoSuchMethodException | InvocationTargetException e) {
            e.printStackTrace();
            return null;
        }
    }

    public ActivityESRequestDTO generateESRequest(String key, Long value, Pageable pageable, String sortKey, String sortOrder) {
        ActivityESRequestDTO activityESRequestDTO = new ActivityESRequestDTO();
        activityESRequestDTO.setSize(pageable.getPageSize());
        activityESRequestDTO.setFrom(pageable.getPageNumber() * pageable.getPageSize());
        ActivityQueryDTO queryDTO = ActivityQueryDTO.toTerm(key, value);

        activityESRequestDTO.setQuery(queryDTO);
        List<Map<String, String>> sortOptions = new ArrayList<>();
        Map<String, String> sortOption = new HashMap<>();
        sortOption.put(sortKey, sortOrder);
        sortOptions.add(sortOption);
        activityESRequestDTO.setSort(sortOptions);
        return activityESRequestDTO;
    }

    public ActivityESRequestDTO generateESRequest(String key, List<Long> value, Pageable pageable, String sortKey, String sortOrder) {
        ActivityESRequestDTO activityESRequestDTO = new ActivityESRequestDTO();
        activityESRequestDTO.setSize(pageable.getPageSize());
        activityESRequestDTO.setFrom(pageable.getPageNumber() * pageable.getPageSize());
        ActivityQueryDTO queryDTO = ActivityQueryDTO.toTerms(key, value);
        activityESRequestDTO.setQuery(queryDTO);
        List<Map<String, String>> sortOptions = new ArrayList<>();
        Map<String, String> sortOption = new HashMap<>();
        sortOption.put(sortKey, sortOrder);
        sortOptions.add(sortOption);
        activityESRequestDTO.setSort(sortOptions);
        return activityESRequestDTO;
    }

    /***
     * use the reflection to get corresponding string value from id/enum value based on different args type
     * @param activityChangeDTO
     * @param config
     * @param sourceClass
     * @param displayType
     * @return
     */
    @Deprecated
    public ChangeFieldDTO generateChangeDTO(ActivityChangeDTO activityChangeDTO, ActivityConfig config, Object sourceClass, DisplayType displayType) {
            return new ChangeFieldDTO();

//        if (activityChangeDTO == null || config == null) {
//            return new ChangeFieldDTO();
//        }
//        ChangeFieldDTO newChangeDTO = new ChangeFieldDTO();
//        try {
//            newChangeDTO.setKey(activityChangeDTO.getKey());
//            if (config.getArgsType().equals(ActivityArgsType.NONE)) {
//                newChangeDTO.setChangedFromBoth(activityChangeDTO.getChangedFrom());
//                newChangeDTO.setChangedToBoth(activityChangeDTO.getChangedTo());
//                return newChangeDTO;
//            } else if (config.getArgsType().equals(ActivityArgsType.IDTYPEREMOTE)) {
//                if (activityChangeDTO.getChangedFrom() != null && StringUtils.isNotBlank(activityChangeDTO.getChangedFrom())) {
//                    Object fromObj = ActivityConfigService.callServiceMethodDynamically(sourceClass, config.getServiceName(), config.getServiceMethodName(), new Object[]{Long.parseLong(activityChangeDTO.getChangedFrom())});
//                    newChangeDTO.setChangedFromBoth(activityChangeDTO.getChangedFrom());
//                    if(fromObj != null){
//                        ResponseEntity<String> response = (ResponseEntity<String>) fromObj;
//                        String fromObjStr = response.getBody();
//                        if(StringUtils.isNotBlank(fromObjStr)){
//                            newChangeDTO.setChangedFromBoth(fromObjStr);
//                        }
//                    }
//                }
//                if (StringUtils.isNotBlank(activityChangeDTO.getChangedTo())) {
//                    Object toObj = ActivityConfigService.callServiceMethodDynamically(sourceClass, config.getServiceName(), config.getServiceMethodName(), new Object[]{Long.parseLong(activityChangeDTO.getChangedTo())});
//                    newChangeDTO.setChangedToBoth(activityChangeDTO.getChangedTo());
//                    if (toObj != null) {
//                        ResponseEntity<String> response = (ResponseEntity<String>) toObj;
//                        String toObjStr = response.getBody();
//                        if (StringUtils.isNotBlank(toObjStr)) {
//                            newChangeDTO.setChangedFromBoth(toObjStr);
//                        }
//                    }
//                }
//
//                return newChangeDTO;
//
//            } else if (config.getArgsType().equals(ActivityArgsType.ENUMTYPE)) {
//                if (activityChangeDTO.getChangedFrom() != null && StringUtils.isNotBlank(activityChangeDTO.getChangedFrom())) {
//                    if( DisplayType.EN.equals(displayType) || DisplayType.BILINGUAL.equals(displayType)){
//                        Object fromObj = ActivityConfigService.callServiceMethodDynamically(sourceClass, config.getServiceName(), config.getServiceMethodName(), new Object[]{Long.parseLong(activityChangeDTO.getChangedFrom()), DisplayType.EN});
//                        newChangeDTO.setChangedFromEn(fromObj == null ? UNKNOWN : fromObj.toString());
//                    }
//                    if( DisplayType.CN.equals(displayType) || DisplayType.BILINGUAL.equals(displayType)){
//                        Object fromObj = ActivityConfigService.callServiceMethodDynamically(sourceClass, config.getServiceName(), config.getServiceMethodName(), new Object[]{Long.parseLong(activityChangeDTO.getChangedFrom()), DisplayType.CN});
//                        newChangeDTO.setChangedFromCn(fromObj == null ? UNKNOWN : fromObj.toString());
//                    }
//                }
//                if (activityChangeDTO.getChangedTo() != null && StringUtils.isNotBlank(activityChangeDTO.getChangedTo())) {
//                    if( DisplayType.EN.equals(displayType) || DisplayType.BILINGUAL.equals(displayType)){
//                        Object toObj = ActivityConfigService.callServiceMethodDynamically(sourceClass, config.getServiceName(), config.getServiceMethodName(), new Object[]{Long.parseLong(activityChangeDTO.getChangedTo()), DisplayType.EN});
//                        newChangeDTO.setChangedToEn(toObj == null ? UNKNOWN : toObj.toString());
//                    }
//                    if( DisplayType.CN.equals(displayType) || DisplayType.BILINGUAL.equals(displayType)){
//                        Object toObj = ActivityConfigService.callServiceMethodDynamically(sourceClass, config.getServiceName(), config.getServiceMethodName(), new Object[]{Long.parseLong(activityChangeDTO.getChangedTo()), DisplayType.CN});
//                        newChangeDTO.setChangedToCn(toObj == null ? UNKNOWN : toObj.toString());
//                    }
//
//                }
//                return newChangeDTO;
//            }else{
//                newChangeDTO.setChangedFromBoth(activityChangeDTO.getChangedFrom());
//                newChangeDTO.setChangedToBoth(activityChangeDTO.getChangedTo());
//                return newChangeDTO;
//            }
//        } catch (Exception ex){
//            log.error("[APN job Activity {}: format job Activity failed to process error: {}", activityChangeDTO.getKey(), ex.toString());
//            return newChangeDTO;
//        }

    }
}
