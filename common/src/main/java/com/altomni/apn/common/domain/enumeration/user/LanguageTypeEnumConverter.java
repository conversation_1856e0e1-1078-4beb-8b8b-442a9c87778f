package com.altomni.apn.common.domain.enumeration.user;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class LanguageTypeEnumConverter extends AbstractAttributeConverter<LanguageTypeEnum, Integer> {
    public LanguageTypeEnumConverter() {
        super(LanguageTypeEnum::toDbValue, LanguageTypeEnum::fromDbValue);
    }
}

