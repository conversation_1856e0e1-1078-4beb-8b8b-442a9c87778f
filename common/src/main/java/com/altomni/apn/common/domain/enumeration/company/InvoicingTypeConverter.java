package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoicingTypeConverter extends AbstractAttributeConverter<InvoicingType, Integer> {
    public InvoicingTypeConverter() {
        super(InvoicingType::toDbValue, InvoicingType::fromDbValue);
    }
}
