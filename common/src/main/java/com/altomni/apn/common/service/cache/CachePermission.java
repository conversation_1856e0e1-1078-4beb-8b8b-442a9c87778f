package com.altomni.apn.common.service.cache;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.dto.permission.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.repository.permission.PermissionRepository;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.ipg.resourceserver.authentication.ImpersonationAuthentication;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@EnableCaching
public class CachePermission {

    @Resource
    private PermissionRepository permissionRepository;

    @Resource
    private CommonRedisService commonRedisService;

    @Cacheable(cacheNames = {"Security:privilege-permission-tree"}, key = "'user:' + #userId", unless = "#result == null")
    public PermissionPrivilegeTreeDTO getPrivilegePermissionTreeByUserIdFromCacheOrDB(Long userId){
        List<PermissionPrivilegeData> privileges = SecurityUtils.isAdmin() ? permissionRepository.findAllPrivileges() : permissionRepository.findPrivilegesByUserId(userId);
        Map<Long, List<PermissionPrivilegeTreeDTO>> map = new HashMap<>();
        privileges.forEach(privilege -> {
            PermissionPrivilegeTreeDTO permissionPrivilegeDTO = new PermissionPrivilegeTreeDTO();
            BeanUtils.copyProperties(privilege, permissionPrivilegeDTO);
            permissionPrivilegeDTO.setParentId(privilege.getParentId());
            if (!map.containsKey(permissionPrivilegeDTO.getParentId())){
                map.put(permissionPrivilegeDTO.getParentId(), new ArrayList<>());
            }
            map.get(permissionPrivilegeDTO.getParentId()).add(permissionPrivilegeDTO);
            if (map.containsKey(permissionPrivilegeDTO.getId())){
                permissionPrivilegeDTO.setChildren(map.get(permissionPrivilegeDTO.getId()));
            }
        });
        List<PermissionPrivilegeTreeDTO> permissionPrivilegeTreeDTOS = map.getOrDefault(-1L, null);
        if (CollectionUtils.isEmpty(permissionPrivilegeTreeDTOS)){
            return null;
        }
        return permissionPrivilegeTreeDTOS.get(0);
    }


    @Cacheable(cacheNames = {"Security:privilege-permission-set"}, key = "'user:' + #userId", unless = "#result == null")
    public List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer> getPrivilegePermissionSetByUserIdFromCacheOrDB(Long userId){
//
//        In order to be compatible with the transitional version of the service, the management service temporarily cancels the tenant privilege related configuration. Subsequent V3 will reopen this business.
//
        List<PermissionPrivilegeDataSimple> privilegeDataSimples = new ArrayList<>();
        if (SecurityUtils.isAdmin()) {
//            List<Long> idList = permissionRepository.findAllIdByTenantId(SecurityUtils.getTenantId());
//            PermissionPrivilegeDataSimple permissionPrivilegeDataSimple = permissionRepository.findPrivilegeNamesByPrivilegeId(1L);
//            if (permissionPrivilegeDataSimple != null) {
//                privilegeDataSimples.add(permissionPrivilegeDataSimple);
//            }
//            if (CollectionUtils.isNotEmpty(idList)) {
//                privilegeDataSimples.addAll(permissionRepository.findPrivilegeNamesByTenantId(idList));
//            }
            List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer> result = getAllTenantPrivilege();
            return CollectionUtils.isEmpty(result) ? null : result;
        } else {
            privilegeDataSimples = permissionRepository.findPrivilegeNamesByUserId(userId);
        }
        final List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer> result = privilegeDataSimples.stream().map(data -> new PermissionPrivilegeDataSimple.PermissionPrivilegeContainer(data.getId(), data.getName())).collect(Collectors.toList());
        return CollectionUtils.isEmpty(result) ? null : result;

//        List<PermissionPrivilegeDataSimple> privilegeDataSimples = SecurityUtils.isAdmin() ?
//                permissionRepository.findAllPrivilegeNames() : permissionRepository.findPrivilegeNamesByUserId(userId);
//        final List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer> result = privilegeDataSimples
//                .stream()
//                .map(data -> new PermissionPrivilegeDataSimple.PermissionPrivilegeContainer(data.getId(), data.getName())).collect(Collectors.toList());
//        return CollectionUtils.isEmpty(result) ? null : result;
    }

    //@Cacheable(cacheNames = {"Security:privilege-APIs"}, key = "'user:' + #userId", unless = "#result == null")
    public Set<String> getPrivilegeApisByUserIdFromDB(Long userId){
        log.info("getPrivilegeApisByUserIdFromCacheOrDB, userId: {}, isAdmin: {}", userId, SecurityUtils.isAdmin());
        Set<String> privilegeApis = SecurityUtils.isAdmin() ? permissionRepository.findPrivilegeApisForAdminUser() : permissionRepository.findPrivilegeApisByUserId(userId);
        return this.flattenApis(privilegeApis);
    }

    //@Cacheable(cacheNames = {"Security:privilege-APIs"}, key = "'admin_user:' + #userId", unless = "#result == null")
//    public Set<String> getPrivilegeApisByAdminUserIdFromDB(Long userId){
//        Set<String> privilegeApisByAdminUserId = permissionRepository.findPrivilegeApisByAdminUserId(userId);
//        return this.flattenApis(privilegeApisByAdminUserId);
//    }

    //@Cacheable(cacheNames = {"security"}, key = "'publicAPIs'", unless = "#result == null")
    private Set<String> getPublicApisFromDB(){
        Set<String> publicPrivilegeApis = permissionRepository.findPublicPrivilegeApis();
        return this.flattenApis(publicPrivilegeApis);
    }

    private Set<String> getAllImpersonationPrivilegesFromDB(){
        return this.flattenApis(permissionRepository.findAllImpersonationPrivilegeApis());
    }

    public Set<String> flattenApis(Set<String> privilegeApis){
        Set<String> apis = new HashSet<>();
        privilegeApis.stream().forEach(apiArr -> {
            if (StringUtils.isNotEmpty(apiArr)){
                Set<String> subApis = JSONArray.parseArray(apiArr, String.class).stream().collect(Collectors.toSet());
                apis.addAll(subApis);
            }
        });
        return apis;
    }

    @Cacheable(cacheNames = {"Security:systemAPIs"}, key = "#service", unless = "#result == null")
    public Map<String, List<PermissionPrivilegeVM.Detail>> getSystemApis(String service){
        return new TreeMap<>();
    }

    @CachePut(cacheNames = {"Security:systemAPIs"}, key = "#service", unless = "#result == null")
    public Map<String, List<PermissionPrivilegeVM.Detail>> updateSystemApis(String service, Map<String, List<PermissionPrivilegeVM.Detail>> newMap){
        return newMap;
    }

    @Cacheable(cacheNames = {"Security:data-permission"}, key = "'user:' + #userId", unless = "#result == null")
    public TeamDataPermissionRespDTO getTeamDataPermissionFromCacheOnly(Long userId) {
        return null;
    }

    @Cacheable(cacheNames = {"Security:data-permission"}, key = "'admin_user:' + #adminUserId", unless = "#result == null")
    public TeamDataPermissionRespDTO getTeamDataPermissionByAdminUserIdFromCacheOrDefault(Long adminUserId) {
        TeamDataPermissionRespDTO teamDataPermissionRespDTO = new TeamDataPermissionRespDTO();
        teamDataPermissionRespDTO.setAll(Boolean.TRUE);
        return teamDataPermissionRespDTO;
    }

    @Cacheable(cacheNames = {"Security:data-permission-rules"}, key = "'tenant:' + #tenantId", unless = "#result == null")
    public List<TeamDataPermissionRule> getPermissionRulesByTenantIdFromCacheOnly(Long tenantId){
        return null;
    }

    public boolean isPublicApi(String uniformURI) {
        if (BooleanUtils.isFalse(commonRedisService.exists(RedisConstants.DATA_KEY_PRIVILEGES_PUBLIC))){
            Set<String> publicPrivileges = this.getPublicApisFromDB();
            commonRedisService.sadd(RedisConstants.DATA_KEY_PRIVILEGES_PUBLIC, publicPrivileges, RedisConstants.EXPIRE_IN_1_DAY);
        }
        return commonRedisService.contains(RedisConstants.DATA_KEY_PRIVILEGES_PUBLIC, uniformURI);
    }

    public void checkImpersonationLoginPermission(Long userId, String uniformURI,String method) {
        if (userId < 0) {
            return;
        }
        // Impersonation Login
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (!(authentication instanceof ImpersonationAuthentication)) {
            return;
        }
        if (BooleanUtils.isFalse(commonRedisService.exists(RedisConstants.DATA_KEY_IMPERSONATION_PRIVILEGES))){
            Set<String> privileges = this.getAllImpersonationPrivilegesFromDB();
            commonRedisService.sadd(RedisConstants.DATA_KEY_IMPERSONATION_PRIVILEGES, privileges, RedisConstants.EXPIRE_IN_1_DAY);
        }
        if (BooleanUtils.isFalse(commonRedisService.contains(RedisConstants.DATA_KEY_IMPERSONATION_PRIVILEGES, uniformURI))){
            if (method.equals("GET")) {
                throw new NotFoundException("[Impersonation] API " + uniformURI + " does not exist!");
            } else {
                throw new CustomParameterizedException(HttpStatus.FORBIDDEN.value(), "[Impersonation] API " + uniformURI + " does not exist!", "");
            }
        }
    }

    public void checkUserPrivileges(Long userId, String uniformURI,String method) {
        if (isPublicApi(uniformURI)) {
            return;
        }
        String redisKey = String.format(RedisConstants.DATA_KEY_PRIVILEGES_USER, userId);
        if (BooleanUtils.isFalse(commonRedisService.exists(redisKey))){
            Set<String> privileges = this.getPrivilegeApisByUserIdFromDB(userId);
            commonRedisService.sadd(redisKey, privileges, RedisConstants.EXPIRE_IN_1_DAY);
        }
        if (BooleanUtils.isFalse(commonRedisService.contains(redisKey, uniformURI))) {
            log.error("no permission, uniformURI = {}, userId = {}", uniformURI, userId);
            if (method.equals("GET")) {
                throw new NotFoundException("API " + uniformURI + " does not exist!");
            } else {
                throw new CustomParameterizedException(HttpStatus.FORBIDDEN.value(), "API " + uniformURI + " does not exist!", "");
            }
        }
    }

    public boolean hasUserPrivilegePermission(Long userId, String uniformURI) {
        if (isPublicApi(uniformURI)) {
            return true;
        }
        String redisKey = String.format(RedisConstants.DATA_KEY_PRIVILEGES_USER, userId);
        if (BooleanUtils.isFalse(commonRedisService.exists(redisKey))){
            Set<String> privileges = this.getPrivilegeApisByUserIdFromDB(userId);
            commonRedisService.sadd(redisKey, privileges, RedisConstants.EXPIRE_IN_1_DAY);
        }
        return commonRedisService.contains(redisKey, uniformURI);
    }

    private List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer> getAllTenantPrivilege(){
        log.info("({}) REST request to get tenant privileges cache", SecurityUtils.getCurrentUserLogin());
        List<PermissionPrivilegeData> privileges = permissionRepository.findAllPrivileges();
        Map<Long, List<PermissionPrivilegeTreeDTO>> map = new HashMap<>();
        Map<Long, PermissionPrivilegeTreeDTO> idPathMap = new HashMap<>();
        privileges.forEach(privilege -> {
            PermissionPrivilegeTreeDTO permissionPrivilegeDTO = new PermissionPrivilegeTreeDTO();
            BeanUtils.copyProperties(privilege, permissionPrivilegeDTO);
            if (!map.containsKey(permissionPrivilegeDTO.getParentId())){
                map.put(permissionPrivilegeDTO.getParentId(), new ArrayList<>());
            }
            idPathMap.put(privilege.getId(), permissionPrivilegeDTO);
            map.get(permissionPrivilegeDTO.getParentId()).add(permissionPrivilegeDTO);
            if (map.containsKey(permissionPrivilegeDTO.getId())){
                List<PermissionPrivilegeTreeDTO> privilegeDTOS = map.get(permissionPrivilegeDTO.getId());
                permissionPrivilegeDTO.setChildren(privilegeDTOS);
            }
        });

        List<Long> idList = permissionRepository.findAllIdByTenantId(SecurityUtils.getTenantId());
        List<PermissionPrivilegeTreeDTO> privilegeTreeDTOList = map.getOrDefault(-1L, new ArrayList<>());

        Set<Long> filterIds = new HashSet<>(idList);
        Set<Long> configIds = permissionRepository.findAllConfigPrivilegeIds();

        List<PermissionPrivilegeTreeDTO> filteredTree = filterTree(privilegeTreeDTOList, filterIds, configIds);

        List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer> result = new ArrayList<>();
        filteredTree.forEach(item -> {
            traverseTree(item, result);
        });
        return result;
    }

    private void traverseTree(PermissionPrivilegeTreeDTO node, List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer> result) {
        if (node == null) {
            return;
        }

        result.add(new PermissionPrivilegeDataSimple.PermissionPrivilegeContainer(node.getId(), node.getName()));

        if (CollUtil.isNotEmpty(node.getChildren())) {
            for (PermissionPrivilegeTreeDTO child : node.getChildren()) {
                traverseTree(child, result);
            }
        }
    }

    private static List<PermissionPrivilegeTreeDTO> filterTree(List<PermissionPrivilegeTreeDTO> result, Set<Long> filterIds, Set<Long> configIds) {
        List<PermissionPrivilegeTreeDTO> filteredNodes = new ArrayList<>();
        for (PermissionPrivilegeTreeDTO node : result) {
            PermissionPrivilegeTreeDTO filteredNode = filterTreeHelper(node, filterIds, configIds);
            if (filteredNode != null) {
                filteredNodes.add(filteredNode);
            }
        }
        return filteredNodes;
    }

    private static PermissionPrivilegeTreeDTO filterTreeHelper(PermissionPrivilegeTreeDTO node, Set<Long> filterIds, Set<Long> configIds) {
        if (node == null) {
            return null;
        }

        if (!configIds.contains(node.getId())) {
            return node;
        }

        if (filterIds.contains(node.getId()) && CollUtil.isNotEmpty(node.getChildren()) && node.getChildren().stream().noneMatch(o -> configIds.contains(o.getId()))) {
            return node;
        }

        PermissionPrivilegeTreeDTO filteredNode = null;
        if (filterIds.contains(node.getId())) {
            filteredNode = new PermissionPrivilegeTreeDTO();
            ServiceUtils.myCopyProperties(node, filteredNode, PermissionPrivilegeTreeDTO.UpdateSkipProperties);
            if (CollUtil.isNotEmpty(node.getChildren())) {
                List<PermissionPrivilegeTreeDTO> childrenList = new ArrayList<>();
                for (PermissionPrivilegeTreeDTO child : node.getChildren()) {
                    PermissionPrivilegeTreeDTO childPermissionPrivilegeDTO = filterTreeHelper(child, filterIds, configIds);
                    if (childPermissionPrivilegeDTO != null) {
                        childrenList.add(childPermissionPrivilegeDTO);
                    }
                }
                filteredNode.setChildren(childrenList);
            }

        }

        return filteredNode;
    }
}