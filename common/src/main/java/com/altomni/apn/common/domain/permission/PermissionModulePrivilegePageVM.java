package com.altomni.apn.common.domain.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionModulePrivilegePageVM implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    private Long moduleId;

    private Long privilegeId;

    private String name;

    private Boolean disable;

    private Boolean isShowPermissionButton;

    private Long parentId;

    public PermissionModulePrivilegePageVM(Long id, Long moduleId, Long privilegeId) {
        this.id = id;
        this.moduleId = moduleId;
        this.privilegeId = privilegeId;
    }
}
