package com.altomni.apn.common.config;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.SecurityUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.util.Objects;

@Configuration
public class PermissionAuditingEntityListener {

    @PrePersist
    public void touchForCreate(Object target) {
        Assert.notNull(target, "Entity must not be null!");
        if (target instanceof AbstractPermissionAuditingEntity){
            AbstractPermissionAuditingEntity entity = (AbstractPermissionAuditingEntity) target;
            if (entity.getPermissionUserId() == null) {
                entity.setPermissionUserId(SecurityUtils.getUserId());
            }
            if ((Objects.isNull(entity.getPermissionTeamId()))){
                entity.setPermissionTeamId(SecurityUtils.getTeamId());
            }
        }
    }

    @PreUpdate
    public void touchForUpdate(Object target){

    }
}
