package com.altomni.apn.common.aop.request;

import com.altomni.apn.common.aop.request.custom.RequestBodySignGenerator;
import com.altomni.apn.common.aop.request.custom.SubmitToClientSignGenerator;
import com.altomni.apn.common.aop.request.custom.TalentInfoInputSignGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@Slf4j
public class SignGeneratorConfig {
    private final TalentInfoInputSignGenerator talentInfoInputSignGenerator = new TalentInfoInputSignGenerator();
    private final SubmitToClientSignGenerator submitToClientSignGenerator = new SubmitToClientSignGenerator();
    private final RequestBodySignGenerator requestBodySignGenerator = new RequestBodySignGenerator();

    @Bean
    public ArgsSignGeneratorFactory argsSignGeneratorFactory() {
        log.info("argsSignGeneratorFactory init");
        ArgsSignGeneratorFactory argsSignGeneratorFactory = new ArgsSignGeneratorFactory();
        // 注册示例：支持路径变量的模式
        // registerGenerator("GET", "/api/v3/talents/{id}", new CustomArgsSignGenerator());
        argsSignGeneratorFactory.registerGenerator("PUT", "/api/v3/talents/{id}", requestBodySignGenerator);
        argsSignGeneratorFactory.registerGenerator("POST", "/api/v3/talents", requestBodySignGenerator);
        argsSignGeneratorFactory.registerGenerator("PUT", "/api/v3/jobs/{id}", requestBodySignGenerator);
        argsSignGeneratorFactory.registerGenerator("POST", "/api/v3/jobs", requestBodySignGenerator);
        argsSignGeneratorFactory.registerGenerator("POST", "/api/v3/talent-recruitment-processes/submit-to-client", submitToClientSignGenerator);
        return argsSignGeneratorFactory;
    }
}
