package com.altomni.apn.common.vo.crmenums;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DictVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "The dictCode for find special dict data")
    private Integer id;

    @ApiModelProperty(value = "The Chinese annotation for target value")
    private String label;

    @ApiModelProperty(value = "The English annotation for target value")
    private String labelCn;

    @ApiModelProperty(value = "The label words for web")
    private String value;

    @ApiModelProperty(value = "itemValue children list")
    private List<DictVO> children;

    @ApiModelProperty(value = "sign which itemValue is belongs to. when 0 then it's a parents itemValue")
    private Integer parentId;

    @ApiModelProperty(value = "sign checked for webSide")
    private Boolean checked;

    @ApiModelProperty(value = "The front display value of target value")
    private String display;

    @ApiModelProperty(value = "The name for target value")
    private String name;

    @JsonIgnore
    private Integer displayOrder;

    @JsonIgnore
    private Integer enDisplayOrder;

    @JsonIgnore
    private Integer cnDisplayOrder;

    private String parent;
}
