package com.altomni.apn.common.aop.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = JsonFieldValidator.class)
@Documented
public @interface ValidJsonField {

    String[] fields() default {};

    String message() default "Invalid JSON field";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
