package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TalentOwnershipUserRole enumeration.
 */
public enum TalentOwnershipUserRole implements ConvertedEnum<Integer>{
    OWNERSHIP_RECRUITER(0),
    OWNERSHIP_SOURCER(1),
    OWNERSHIP_RECRUITER_AND_SOURCER(2);

    private final Integer dbValue;

    TalentOwnershipUserRole(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TalentOwnershipUserRole, Integer> resolver =
        new ReverseEnumResolver<>(TalentOwnershipUserRole.class, TalentOwnershipUserRole::toDbValue);

    public static TalentOwnershipUserRole fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
