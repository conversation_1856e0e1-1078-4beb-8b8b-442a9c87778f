package com.altomni.apn.common.domain.enumeration.job;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum PlatformType implements ConvertedEnum<Integer> {
    LINKEDIN(7),

    FACEBOOK(12),
    WECHAT(14),

    WEB_LINKS(99);
    private final int dbValue;

    PlatformType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<PlatformType, Integer> resolver =
            new ReverseEnumResolver<>(PlatformType.class, PlatformType::toDbValue);

    public static PlatformType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
