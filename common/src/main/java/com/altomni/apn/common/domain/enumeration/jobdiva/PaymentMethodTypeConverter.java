package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class PaymentMethodTypeConverter extends AbstractAttributeConverter<PaymentMethodType,Integer> {

    public PaymentMethodTypeConverter(){
        super(PaymentMethodType::toDbValue,PaymentMethodType::fromDbValue);
    }
}
