package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 management-service
 * <AUTHOR>
 */
public enum ManagementAPIMultilingualEnum {

    DATA_CHECKINLIST_CONDITIONLIMIT("data_checkInList_conditionLimit"),

    PRODUCT_UPDATEERRORINFO_NOTEXIST("product_updateErrorInfo_notExist"),

    PRODUCT_UPDATEERRORINFO_PROCESSED("product_updateErrorInfo_processed"),

    TENANT_CREATE_EXCEPTION("tenant_create_exception"),

    TENANT_QUERYTENANTBYNAME_TENANTNOTEXIST("tenant_queryTenantByName_tenantNotExist"),

    TENANT_UPDATETENANT_MONEYCREDITNULL("tenant_updateTenant_moneyCreditNull"),

    TENANT_UPDATETENANT_BULKCREDITNULL("tenant_updateTenant_bulkCreditNull"),

    TENANT_UPDATETENANT_MONEYCREDITLESSTOTAL("tenant_updateTenant_moneyCreditLessTotal"),

    TENANT_UPDATETENANT_BULKCREDITLESSTOTAL("tenant_updateTenant_bulkCreditLessTotal"),

    TENANT_VALIDATETENANTNAME_TENANTALREADYEXIST("tenant_validateTenantName_tenantAlreadyExist"),

    TENANT_VALIDATETENANTNAME_EMAILALREADYEXIST("tenant_validateTenantName_emailAlreadyExist"),

    USER_LOGIN_USERNULL("user_login_userNull"),

    USER_CHECKLOCKEDACCOUNT_ACCOUNTLOCK("user_checkLockedAccount_accountLock"),

    USER_FINDONEBYUSERNAME_USERNULL("user_findOneByUsername_userNull"),

    USER_FINDONEBYUSERNAME_USERINACTIVE("user_findOneByUsername_userInactive"),

    USER_FORGETPASS_RESETPASSWORDSEND("user_forgetPass_resetPasswordSend"),

    USER_FORGETPASS_EMAILSENDFAIL("user_forgetPass_emailSendFail"),

    USER_RESETPASSFORFORGET_CODEISNULL("user_resetPassForForget_codeIsNull"),

    USER_RESETPASSFORFORGET_PASSWORDISNULL("user_resetPassForForget_passwordIsNull"),

    USER_RESETPASSFORFORGET_INVALIDCODE("user_resetPassForForget_invalidCode"),

    USER_RESETPASSFORFORGET_INVALIDCODEEXPIRED("user_resetPassForForget_invalidCodeExpired"),

    USER_RESETPASSWORD_USERNOTLOGIN("user_resetPassword_userNotLogin"),

    USER_RESETPASSWORD_PASSWORDISNULL("user_resetPassword_passwordIsNull"),

    USER_RESETPASSWORD_OLDPASSWORDNOTMATCH("user_resetPassword_oldPasswordNotMatch"),

    RECRUITMENTPROCESS_CREATE_IDNOTNULL("recruitmentProcess_create_idNotNull"),

    USER_LOGIN_INACTIVEUSER("user_login_inactiveUser"),
    ;

    private final String key;

    ManagementAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}