package com.altomni.apn.common.repository.company;

import com.altomni.apn.common.domain.dict.CompanyServiceTypeConnectTenant;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CompanyServiceTypeConnectTenantRepository extends JpaRepository<CompanyServiceTypeConnectTenant, Long> {

    List<CompanyServiceTypeConnectTenant> findAllByTenantId(Long tenantId);

}
