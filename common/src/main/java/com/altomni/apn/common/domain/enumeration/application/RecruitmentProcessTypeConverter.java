package com.altomni.apn.common.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class RecruitmentProcessTypeConverter extends AbstractAttributeConverter<RecruitmentProcessType, Integer> {
    public RecruitmentProcessTypeConverter() {
        super(RecruitmentProcessType::toDbValue, RecruitmentProcessType::fromDbValue);
    }
}
