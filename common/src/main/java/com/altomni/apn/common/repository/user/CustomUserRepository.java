package com.altomni.apn.common.repository.user;

import com.altomni.apn.common.domain.user.User;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomUserRepository extends JpaRepository<User, Long> {

    @Query(value = "select uid from user where username = ?1 or user.email = ?1", nativeQuery = true)
    String findOneWithRolesByUsername(@Param("login") String login);

    @Query("select user from User user left join fetch user.roles where user.id = :id")
    Optional<User> findOneWithRolesById(@Param("id") Long id);

    @Query(value = "select team_id from permission_user_team ut " +
            " where ut.user_id=:userId and ut.is_primary=1 limit 1", nativeQuery = true)
    Long findPrimaryTeamId(@Param("userId") Long userId);

    @Query(value = """
                select distinct c.id
                from company c
                left join business_flow_administrator csla on csla.company_id = c.id and csla.sales_lead_role in (0,3)
                where csla.user_id = ?1
            """, nativeQuery = true)
    List<Long> findCompanyIdsByUserIdWithAm(Long userId);

}
