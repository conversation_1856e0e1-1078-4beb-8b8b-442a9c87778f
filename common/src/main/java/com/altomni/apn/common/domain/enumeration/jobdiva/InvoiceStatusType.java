package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * invoice status type
 */
public enum InvoiceStatusType implements ConvertedEnum<Integer> {

    UNGROUPED(1, "Ungrouped"),
    GROUPED(2, "Grouped"),
    VOID(3,"Void");

    private final int dbValue;

    private final String description;

    InvoiceStatusType(int dbValue,String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoiceStatusType, Integer> resolver = new ReverseEnumResolver<>(InvoiceStatusType.class, InvoiceStatusType::toDbValue);

    public static InvoiceStatusType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
