package com.altomni.apn.common.dto.search;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConditionParam implements SearchCondition {

    private String key;

    private Object value;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String type;


    public static ConditionParam of(String key, Object data) {
        ConditionParam conditionParam = new ConditionParam();
        conditionParam.setKey(key);
        JSONObject dataJson = new JSONObject();
        dataJson.put("data", data);
        conditionParam.setValue(dataJson);
        return conditionParam;
    }
}
