package com.altomni.apn.common.vo.upgrade;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class UpgradeNotificationVO {

    private Long id;

    /**
     *  lark文档链接
     */
    private String link;

    /**
     * 升级日志内容
     */
    private String content;

    /**
     * 版本号
     */
    private String version;

    /**
     * 更新迭代发布时间
     */
    private Instant upgradeTime;

    /**
     * 是否全局可见
     */
    private Boolean global;

}
