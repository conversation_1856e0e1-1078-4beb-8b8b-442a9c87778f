package com.altomni.apn.common.dto.folder.talentrelatejob;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class TalentRelateJobFolderBasicInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long jobId;

    private Long userId;

    private Long tenantId;

}
