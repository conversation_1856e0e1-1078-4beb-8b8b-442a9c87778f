package com.altomni.apn.common.dto.company;

import com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class ContactInfoDTO implements Serializable {

    private Long id;

    private Long contactId;

    //private ContactType type;
    private Integer type;

    private String contact;

    private String details;

    private TalentContactVerificationStatus verificationStatus;

}
