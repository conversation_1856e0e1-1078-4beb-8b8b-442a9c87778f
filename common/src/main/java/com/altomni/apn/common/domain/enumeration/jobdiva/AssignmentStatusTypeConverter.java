package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class AssignmentStatusTypeConverter extends AbstractAttributeConverter<AssignmentStatusType, Integer> {
    public AssignmentStatusTypeConverter() {
        super(AssignmentStatusType::toDbValue, AssignmentStatusType::fromDbValue);
    }
}
