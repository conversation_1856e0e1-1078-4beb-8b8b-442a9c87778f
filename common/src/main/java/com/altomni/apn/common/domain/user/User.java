package com.altomni.apn.common.domain.user;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.permission.PermissionTeamSimple;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.enumeration.permission.DataScope;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;
import java.util.*;

/**
 * A user.
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "user")
public class User extends AbstractAuditingEntity implements UserSecurityInterface, Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "[Internal] uid is used for Spring security to identify user internally. It has the format of <userId>,<tenantId>")
    @Column(name = "uid")
    private String uid;

    @ApiModelProperty(value = "Optional username. If exists, need to be unique.")
    @Pattern(regexp = Constants.USERNAME_REGEX)
    @Size(min = 1, max = 50,message = "username length can not be exceed 50 characters")
    @Column(length = 50, unique = true)
    private String username;

    @ApiModelProperty(value = "hashed password")
    @JsonIgnore
    @NotNull
    @Size(min = 60, max = 60,message ="password length can not be exceed 60 characters" )
    @Column(name = "password_hash",length = 60)
    private String password;

    @ApiModelProperty(value = "first name")
    @Size(max = 50)
    @Column(name = "first_name", length = 50)
    private String firstName;

    @ApiModelProperty(value = "last name")
    @Size(max = 50)
    @Column(name = "last_name", length = 50)
    private String lastName;

    @ApiModelProperty(value = "email address. need to be unique.")
    @org.hibernate.validator.constraints.Email(message = "email format is not correct")
    @Size(min = 5, max = 100)
    @Column(length = 100, unique = true)
    private String email;

    @ApiModelProperty(value = "Whether user is activated. Default is true. Read Only.")
    @NotNull
    @Column(nullable = false)
    private boolean activated = true;

    @ApiModelProperty(value = "Preferred language. e.g. en-US, zh-CN")
    @Size(min = 2, max = 5)
    @Column(name = "lang_key", length = 5)
    private String langKey;

    @ApiModelProperty(value = "url link to user's image")
    @Size(max = 256)
    @Column(name = "image_url", length = 256)
    private String imageUrl;

    @Size(max = 20)
    @Column(name = "activation_key", length = 20)
    @JsonIgnore
    private String activationKey;

    @Size(max = 20)
    @Column(name = "reset_key", length = 20)
    @JsonIgnore
    private String resetKey;

    @Column(name = "reset_date")
    private Instant resetDate = null;

    @ApiModelProperty(value = "credit")
    @Column(name = "credit")
    private Integer monthlyCredit = 0;

    @ApiModelProperty(value = "bulk credit")
    @Column(name = "bulk_credit")
    private Integer bulkCredit = 0;

    @ApiModelProperty(value = "phone number, this is used for consumer registered with phone.")
    @Column(name = "phone")
    private String phone;

    @ApiModelProperty(value = "The tenant user belongs to. Read Only.")
    @ManyToOne
    @JoinColumn(name = "tenant_id", updatable = false, insertable = false)
    private Tenant tenant;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    //@ManyToMany(fetch = FetchType.LAZY)
    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinTable(
            name = "user_role",
            joinColumns = {@JoinColumn(name = "user_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "role_id", referencedColumnName = "id")})
    private Set<Role> roles = new HashSet<>();

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinTable(
            name = "permission_user_team",
            joinColumns = {@JoinColumn(name = "user_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "team_id", referencedColumnName = "id")})
    private Set<PermissionTeamSimple> teams = new HashSet<>();

    @ApiModelProperty(value = "Note info.")
    @Column(name = "note")
    private String note;

    @ApiModelProperty(value = "job title for user.")
    @Column(name = "job_title")
    private String jobTitle;

    @ApiModelProperty(value = "credentials returned when user login")
    @Transient
    @JsonProperty
    public CredentialDTO credential;


    @ApiModelProperty(value = "used monthly credit")
    @Transient
    @JsonProperty
    public Integer usedMonthlyCredit;

    @ApiModelProperty(value = "used bulk credit")
    @Transient
    @JsonProperty
    public Integer usedBulkCredit ;

    @Column(name = "data_scope")
    private Integer dataScope;

    @Column(name = "client_contact_data_scope")
    private Integer clientContactDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "report_data_scope")
    private Integer reportDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "home_and_calendar_data_scope")
    private Integer homeAndCalendarDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "candidate_pipeline_management_data_scope")
    private Integer candidatePipelineManagementDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @Column(name = "china_invoicing_data_scope")
    private Integer chinaInvoicingDataScope = DataScope.PERMISSION_SELF.toDbValue();

    @ApiModelProperty(value = "自定义时区")
    @Column(name = "custom_timezone")
    private String customTimezone;

    @ApiModelProperty(value = "0.不同步,1.同步")
    @Column(name = "sync_lark")
    private Integer syncLark = 0;

    @ApiModelProperty(value = "primary team ID")
    @Transient
    private Long teamId;

    @Transient
    private Long primaryTeamId;

    @Transient
    private Set<Long> secondaryTeamIds;

    @Column(name = "cancellation_time")
    private Instant cancellationTime;

    @Column(name = "enum_level_of_experience_id")
    private Long enumLevelOfExperienceId;

    @ApiModelProperty(value = "one or more languages")
    @OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "user_id")
    private List<UserLanguageRelation> languageRelations;

    @Column(name = "last_push_time")
    private Instant lastPushTime;

    @JsonIgnore
    public String getLogin() {
        if (this.username != null) {
            return username;
        }
        return this.email;
    }

    public String getStringId() {
        return String.valueOf(id);
    }

    public void setUserLanguageRelation(Collection<Long> languages) {
        // 获取或初始化 languageRelations 集合
        List<UserLanguageRelation> currentRelations = Optional.ofNullable(this.getLanguageRelations())
                .orElseGet(() -> {
                    List<UserLanguageRelation> newList = new ArrayList<>();
                    this.setLanguageRelations(newList);
                    return newList;
                });
        // 清空当前集合
        currentRelations.clear();

        // 如果传入的 languages 不为空，添加新的关系
        if (CollUtil.isNotEmpty(languages)) {
            languages.stream()
                    .map(languageId -> new UserLanguageRelation(null, null, languageId))
                    .forEach(currentRelations::add);
        }
    }
}
