package com.altomni.apn.common.enumeration.enums;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum DisplayType implements ConvertedEnum<Integer> {
    DEFAULT(0, "default_display"),

    EN(1, "en_display"),

    CN(2, "cn_display"),

    BILINGUAL(3, "Bilingual_display");


    private final int dbValue;
    private final String name;

    // static resolving:
    public static final ReverseEnumResolver<DisplayType, Integer> resolver =
            new ReverseEnumResolver<>(DisplayType.class, DisplayType::toDbValue);

    DisplayType(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static DisplayType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }
}
