package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 company-service
 * <AUTHOR>
 */
public enum CompanyAPIMultilingualEnum {

    AM_FINDAMREPORT_NOPERMISSON("am_findAmReport_noPermisson"),

    AM_COMMON_PARAMNULL("am_common_paramNull"),

    LOCATION_COMMON_COMPANYNOTEXIST("location_common_companyNotExist"),

    LOCATION_CHECKDUPLICATEDCOMPANYLOCATION_EXISTED("location_checkDuplicatedCompanyLocation_existed"),

    LOCATION_CORRECTHISTORICALCOMPANYLOCATION_APINOTEXIST("location_correctHistoricalCompanyLocation_apiNotExist"),

    NOTE_CREATECLIENTNOTE_NOPERMISSION("note_createClientNote_noPermission"),

    NOTE_CREATECLIENTNOTE_INACTIVE("note_createClientNote_inactive"),

    NOTE_UPDATECLIENTNOTE_NOTEXIST("note_updateClientNote_notExist"),

    NOTE_MIGRATEHISTORYCLIENTNOTE_APINOTEXIST("note_migrateHistoryClientNote_apiNotExist"),

    NOTE_CREATEPROGRESSNOTE_UPGRADED("note_createProgressNote_upgraded"),

    OVERVIEW_CHECKCOMPANYLOCATION_NOTEXIST("overview_checkCompanyLocation_notExist"),

    OVERVIEW_CHECKCOMPANYLOCATION_NOTDEL("overview_checkCompanyLocation_notDel"),

    OVERVIEW_CHECKENUMCOMPANYTAGS_NOTEXIST("overview_checkEnumCompanyTags_notExist"),

    OVERVIEW_CHECKENUMCOMPANYTAGS_EXCEPTION("overview_checkEnumCompanyTags_exception"),

    OVERVIEW_CHECKDUPLICATIONCOMPANYCONTACT_ISDUPLICATE("overview_checkDuplicationCompanyContact_isDuplicate"),

    OVERVIEW_CHECKCOMPANYACTIVECONTACT_CONTACTEXIST("overview_checkCompanyActiveContact_contactExist"),

    OVERVIEW_CHECKSALESLEADEXIST_NOTEXIST("overview_checkSalesLeadExist_notExist"),

    OVERVIEW_CHECKSALESLEADUNIQUETYPE_NONPROMOTED("overview_checkSalesLeadUniqueType_nonPromoted"),

    OVERVIEW_CHECKSALESLEADUNIQUETYPE_CANNOTDESIGN("overview_checkSalesLeadUniqueType_cannotDesign"),

    OVERVIEW_COMMON_NOPERMISSION("overview_common_noPermission"),

    OVERVIEW_CHECKENUMCOMPANYCONTACTTAGS_NOTEXIST("overview_checkEnumCompanyContactTags_notExist"),

    OVERVIEW_CHECKENUMCOMPANYCONTACTTAGS_EXCEPTION("overview_checkEnumCompanyContactTags_exception"),

    OVERVIEW_CONTACT_INFORMATION_CONFLICT("overview_contact_information_conflict"),

    OVERVIEW_CHECKDUPLICATIONCOMPANYNAME_ISNULL("overview_checkDuplicationCompanyName_isNull"),

    OVERVIEW_CHECKSALESLEADSOURCE_NOTEXIST("overview_checkSalesLeadSource_notExist"),

    OVERVIEW_CHECKSALESLEADUSER_NOEXIST("overview_checkSalesLeadUser_noExist"),

    OVERVIEW_CHECKCOMPANYCONTACT_NOEXIST("overview_checkCompanyContact_noExist"),

    OVERVIEW_CHECKCOMPANYEXIST_NOEXIST("overview_checkCompanyExist_noExist"),

    PROJECT_COMMON_NOPERMISSION("project_common_noPermission"),

    PROJECT_CREATE_DUPLICATETEAM("project_create_duplicateTeam"),

    PROJECT_CREATE_IDEXIST("project_create_idExist"),

    PROJECT_CHECKTEAMPERMISSION_NOTFOUND("project_checkTeamPermission_notFound"),

    CONTACT_COMMON_NOPERMISSION("contact_common_noPermission"),

    CONTACT_CHECKTENANTID_COMPANYNOTEXIST("contact_checkTenantId_companyNotExist"),

    CONTACT_SAVE_CONTRACTNOTEXIST("contact_save_contractNotExist"),

    CONTACT_SAVE_PREVIOUSCONTRACTNOTEXIST("contact_save_previousContractNotExist"),

    CONTACT_SAVE_RENEWALCONTRACTNOTEXIST("contact_save_renewalContractNotExist"),

    CONTACT_CHECKSALESLEADS_NOTEXIST("contact_checkSalesLeads_notExist"),

    CONTACT_CHECKSALESLEADS_UPGRADED("contact_checkSalesLeads_upgraded"),

    CONTACT_GETCONTRACTUPLOADURL_CONTRACTISNULL("contact_getContractUploadUrl_contractIsNull"),

    CONTACT_DOWNLOADCONTRACTDISPLAYIMG_NOPREVIEW("contact_downloadContractDisplayImg_noPreview"),

    CONTACT_DOWNLOADCONTRACTDISPLAYIMG_ERRORIMAGE("contact_downloadContractDisplayImg_errorImage"),

    CONTACT_ADDTEXTWATERMARKTOIMAGE_ERROR("contact_addTextWatermarkToImage_error"),

    CONTACT_SAVE_IDEXIST("contact_save_idExist"),

    SEARCHCOMPANYCOUNT_RESPONSEISNULL("searchCompanyCount_responseIsNull"),

    SEARCHCOMPANYNOTE_RESPONSEISNULL("searchCompanyNote_responseIsNull"),

    SEARCHCUSTOMERMETRIC_RESPONSEISNULL("searchCustomerMetric_responseIsNull"),

    UPDATECOMPANIESFOLDER_RESPONSEISNULL("updateCompaniesFolder_responseIsNull"),

    UPDATECOMPANIESFOLDER_RESPONSEERROR("updateCompaniesFolder_responseError"),

    FOLDER_QUERYFOLDER_NOEXIST("folder_queryFolder_noExist"),

    FOLDER_DELETEFOLDER_PARAMISNULL("folder_deleteFolder_paramIsNull"),

    FOLDER_DELETESEARCHFOLDER_DATAISNULL("folder_deleteSearchFolder_DataIsNull"),

    FOLDER_CREATECUSTOMFOLDER_CATEGORYERROR("folder_createCustomFolder_categoryError"),

    FOLDER_DELETECUSTOMFOLDER_PARAMISNULL("folder_deleteCustomFolder_paramIsNull"),

    FOLDER_SETSEARCHCUSTOMFOLDERSQL_FOLDERTYPENOTEXIST("folder_setSearchCustomFolderSql_folderTypeNotExist"),

    FOLDER_COMMON_FOLDERNOTEXIST("folder_common_folderNotExist"),

    FOLDER_ADDCUSTOMFOLDERSHAREDUSER_USERNOTEXIST("folder_addCustomFolderSharedUser_userNotExist"),

    FOLDER_ADDCUSTOMFOLDERSHAREDTEAM_PARAMISNULL("folder_addCustomFolderSharedTeam_paramIsNull"),

    FOLDER_ADDCUSTOMFOLDERSHAREDTEAM_TEAMNOTEXIST("folder_addCustomFolderSharedTeam_teamNotExist"),

    FOLDER_CHECKCUSTOMFOLDERNAME_EXISTS("folder_checkCustomFolderName_exists"),

    FOLDER_CHECKCOMPANYPERMISSION_NOTEXISTS("folder_checkCompanyPermission_notExists"),

    FOLDER_CHECKCOMPANYCATEGORY_MUSTSAME("folder_checkCompanyCategory_mustSame"),

    FOLDER_CHECKCOMPANYCATEGORY_NOESCALATED("folder_checkCompanyCategory_noEscalated"),

    FOLDER_CHECKCOMPANYCATEGORY_NOUPGRADED("folder_checkCompanyCategory_noUpgraded"),

    FOLDER_CHECKINLIST_QUERYCONDITION("folder_checkInList_queryCondition"),

    FOLDER_CHECKFOLDERCOMPANY_NOTEXIST("folder_checkFolderCompany_notExist"),

    FOLDER_SEARCHFOLDER_NAMEEMPTY("folder_searchFolder_nameEmpty"),

    FOLDER_SEARCHFOLDER_LIMITEMPTY("folder_searchFolder_limitEmpty"),

    SALESLEAD_SAVEFROMTALENT_INACTIVE("saleslead_saveFromTalent_inactive"),

    SALESLEAD_SAVEFROMTALENT_TALENTIDNULL("saleslead_saveFromTalent_talentIdNull"),

    SALESLEAD_SAVE_TALENTCOMPANYBRIEFNULL("saleslead_save_talentCompanyBriefNull"),

    SALESLEAD_UPDATETALENTRELATIONDATA_TALENTNOTEXIST("saleslead_updateTalentRelationData_talentNotExist"),

    SALESLEAD_UPDATESALESLEADCLIENTCONTACT_INACTIVE("saleslead_updateSalesLeadClientContact_inactive"),

    SALESLEAD_UPDATESALESLEADCLIENTCONTACT_TALENNOTEXIST("saleslead_updateSalesLeadClientContact_talenNotExist"),

    SALESLEAD_UPDATESALESLEADCLIENTCONTACT_TALENEXIST("saleslead_updateSalesLeadClientContact_talenExist"),

    SALESLEAD_CHECKENUMCOMPANYCONTACTTAGS_NOTEXIST("saleslead_checkEnumCompanyContactTags_notExist"),

    SALESLEAD_CHECKENUMCOMPANYCONTACTTAGS_ERROR("saleslead_checkEnumCompanyContactTags_error"),

    SALESLEAD_CLIENTCONTACTDETECTION_EMAILNULL("saleslead_clientContactDetection_emailNull"),

    SALESLEAD_CLIENTCONTACTDETECTION_EMAILEXIST("saleslead_clientContactDetection_emailExist"),

    SALESLEAD_SETTALENTCONTACTINFORMATION_NOTEXIST("saleslead_setTalentContactInformation_notExist"),

    SALESLEAD_APPROVER_CLIENTCONTACTNOTEXIST("saleslead_approver_clientContactNotExist"),

    SALESLEAD_APPROVER_NOPERMISSION("saleslead_approver_noPermission"),

    SALESLEAD_APPROVER_NOPERMISSION_CONTACT_AM("saleslead_approver_nopermission_contact_am"),

    SALESLEAD_APPROVER_EMAILNOTEXIST("saleslead_approver_emailNotExist"),

    SALESLEAD_APPROVER_PASSWORDISEMPTY("saleslead_approver_passwordIsEmpty"),

    SALESLEAD_APPROVER_EMAILINCONSISTENT("saleslead_approver_emailInconsistent"),

    SALESLEAD_FINDBRIEFCONTACTBYIDANDRECEIVEEMAIL_CONTACTNOTEXIST("saleslead_findBriefContactByIdAndReceiveEmail_contactNotExist"),

    SALESLEAD_CREATECLIENTCONTACTFROMCOMMONPOOL_CLIENTEXIST("saleslead_createClientContactFromCommonPool_clientExist"),

    SALESLEAD_CREATECLIENTCONTACTFROMCOMMONPOOL_PARAMNULL("saleslead_createClientContactFromCommonPool_paramNull"),

    SALESLEAD_UPDATECONTACTINFO_NOTEXIST("saleslead_updateContactInfo_notExist"),

    SALESLEAD_CHECKCOMPANYLOCATION_NOTEXIST("saleslead_checkCompanyLocation_notExist"),

    SALESLEAD_CHECKDUPLICATIONCONTACTBYCOMPANYIDANDTALENTID_CONTACTEXIST("saleslead_checkDuplicationContactByCompanyIdAndTalentId_contactExist"),

    SALESLEAD_CHECKDUPLICATIONCONTACTBYCOMPANYIDANDTALENTID_ERROR("saleslead_checkDuplicationContactByCompanyIdAndTalentId_error"),

    SALESLEAD_CHECKACTIVECONTACT_CONTACTALREADY("saleslead_checkActiveContact_contactAlready"),

    SALESLEAD_CHECKCOMPANYPERMISSION_NOTEXIST("saleslead_checkCompanyPermission_notExist"),

    SKIPSUBMIT_CHECKCOMPANYPERMISSION_NOTFOUND("skipsubmit_checkCompanyPermission_notFound"),

    SKIPSUBMIT_CHECKCOMPANYPERMISSION_NOTPERMISSION("skipsubmit_checkCompanyPermission_notPermission"),

    SKIPSUBMIT_CHECKUSERPERMISSION_NOTPERMISSION("skipsubmit_checkUserPermission_notPermission"),

    COMPANY_PURCHASE_ORDER_CURRENCY_ERROR("company_purchase_order_currency_error"),

    COMPANY_PURCHASE_ORDER_CURRENCY_DIFF("company_purchase_order_currency_diff"),

    COMPANY_PURCHASE_ORDER_AMOUNT_ERROR("company_purchase_order_amount_error"),

    COMPANY_PURCHASE_ORDER_NUMBER_ERROR("company_purchase_order_number_error"),

    COMPANY_PURCHASE_ORDER_CHECKPERMISSIONBYCOMPANYID_COMPANYISNULL("company_purchase_order_checkPermissionByCompanyId_companyIsNull"),

    COMPANY_PURCHASE_ORDER_CHECKPERMISSIONBYCOMPANYID_NOPERMISSION("company_purchase_order_checkPermissionByCompanyId_noPermission"),

    COMPANY_CLIENT_INVOICING_CLIENTNAME_EXIST("company_client_invoicing_clientName_exist"),
    COMPANY_CLIENT_INVOICING_CLIENTNAME_ISUSE("company_client_invoicing_clientName_isUse"),
    COMPANY_CLIENT_INVOICING_SOCIALCREDITCODE_EXIST("company_client_invoicing_socialCreditCode_exist"),
    COMPANY_CLIENT_INVOICING_BANKACCOUNT_EXIST("company_client_invoicing_bankAccount_exist"),
    COMPANY_CLIENT_INVOICING_SOCIALCREDITCODE_LENGTH("company_client_invoicing_socialCreditCode_length"),
    ;

    private final String key;

    CompanyAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}