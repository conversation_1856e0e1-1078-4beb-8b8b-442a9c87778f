package com.altomni.apn.common.dto.application.dashboard;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The NodeType enumeration.
 */
public enum MyCandidateStatusFilter implements ConvertedEnum<Integer> {

    SUBMIT_TO_JOB(10),

    SUBMIT_TO_CLIENT(20),

    INTERVIEW(30),

    OFFER(40),

    OFFER_ACCEPT(41),

    COMMISSION(50),

    ON_BOARD(60),

    OFF_BOARDED(100),

    RESIGNED(110),


    ELIMINATED(-1);

    private final Integer dbValue;

    MyCandidateStatusFilter(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<MyCandidateStatusFilter, Integer> resolver =
        new ReverseEnumResolver<>(MyCandidateStatusFilter.class, MyCandidateStatusFilter::toDbValue);

    public static MyCandidateStatusFilter fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
