package com.altomni.apn.common.domain.enumeration.config;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TenantConfigCodeConverter extends AbstractAttributeConverter<TenantConfigCode, Integer> {
    public TenantConfigCodeConverter() {
        super(TenantConfigCode::toDbValue, TenantConfigCode::fromDbValue);
    }
}
