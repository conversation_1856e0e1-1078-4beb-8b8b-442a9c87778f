package com.altomni.apn.common.domain.application;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.ActivityStatus;
import com.altomni.apn.common.domain.enumeration.support.ActivityStatusConverter;
import com.altomni.apn.common.domain.enumeration.EventStage;
import com.altomni.apn.common.domain.enumeration.EventStageConverter;
import com.altomni.apn.common.domain.enumeration.EventType;
import com.altomni.apn.common.domain.enumeration.EventTypeConverter;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * A Activity.
 */
@ApiModel(description = "Activity tracks recruiting process. It is associated with Talent, Job, User and Application.")
@Entity
@Table(name = "activity")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
public class Activity extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The memo associated with the activity")
    @Column(name = "memo")
    private String memo;

    @ApiModelProperty(required = true, value = "Activity Status",
        allowableValues = "Watching, Applied, Qualified, Submitted, Interview, Offered, Offer_Accepted, Offer_Rejected, Started, Called_Candidate, Meet_Candidate_In_Person")
    @NotNull
    @Convert(converter = ActivityStatusConverter.class)
    @Column(name = "status", nullable = false)
    private ActivityStatus status;

    @ApiModelProperty(value = "The event date when this activity happens.")
    @Column(name= "event_date")
    private Instant eventDate;

    @ApiModelProperty(value = "The event date belongs time zone.")
    @Column(name= "event_time_zone")
    private String eventTimeZone;

    @ApiModelProperty(value = "The event type when this activity happens. eg: when interview,the value is phone interview, onsite interview etc.")
    @Convert(converter = EventTypeConverter.class)
    @Column(name= "event_type")
    private EventType eventType;

    @ApiModelProperty(value = "The event stage")
    @Convert(converter = EventStageConverter.class)
    @Column(name= "event_stage")
    private EventStage eventStage;

    @ApiModelProperty(value = "The location this activity takes place. This is useful for event type activity such as Interview.")
    @Column(name = "location")
    private String location;

    @ApiModelProperty(value = "Talent Id. This is for easy search. Update is not allowed", required = true)
    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    @ApiModelProperty(value = "User Id. This is for easy search", required = true)
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @ApiModelProperty(value = "Job Id. This is for easy search. Update is not allowed", required = true)
    @Column(name = "job_id", nullable = false)
    private Long jobId;

    @ApiModelProperty(value = "Job Application Id. This is for easy search. Update is not allowed", required = true)
    @Column(name = "application_id", nullable = false)
    private Long applicationId;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("talentId", "user_id", "jobId", "applicationId", "status", "tenantId"));

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMemo() {
        return memo;
    }

    public Activity memo(String memo) {
        this.memo = memo;
        return this;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public ActivityStatus getStatus() {
        return status;
    }

    public Activity status(ActivityStatus status) {
        this.status = status;
        return this;
    }

    public Instant getEventDate() {
        return eventDate;
    }

    public Activity eventDate(Instant eventDate) {
        this.eventDate = eventDate;
        return this;
    }

    public void setEventDate(Instant eventDate) {
        this.eventDate = eventDate;
    }

    public String getEventTimeZone() {
        return eventTimeZone;
    }

    public Activity eventTimeZone(String eventTimeZone) {
        this.eventTimeZone = eventTimeZone;
        return this;
    }

    public void setEventTimeZone(String eventTimeZone) {
        this.eventTimeZone = eventTimeZone;
    }

    public EventType getEventType() {
        return eventType;
    }

    public Activity eventType(EventType eventType) {
        this.eventType = eventType;
        return this;
    }


    public Activity eventStage(EventStage eventStage) {
        this.eventStage = eventStage;
        return this;
    }

    public void setEventType(EventType eventType) {
        this.eventType = eventType;
    }

    public String getLocation() {
        return location;
    }

    public Activity location(String location) {
        this.setLocation(location);
        return this;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public void setStatus(ActivityStatus status) {
        this.status = status;
    }

    public Long getTalentId() {
        return talentId;
    }

    public Activity talentId(Long talentId) {
        this.talentId = talentId;
        return this;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getJobId() {
        return jobId;
    }

    public Activity jobId(Long jobId) {
        this.jobId = jobId;
        return this;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getUserId() {
        return userId;
    }

    public Activity userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public Activity applicationId(Long applicationId) {
        this.applicationId = applicationId;
        return this;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public Activity tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Activity activity = (Activity) o;
        if (activity.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), activity.getId());
    }

    public EventStage getEventStage() {
        return eventStage;
    }

    public void setEventStage(EventStage eventStage) {
        this.eventStage = eventStage;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "Activity{" +
            "id=" + id +
            ", memo='" + memo + '\'' +
            ", status=" + status +
            ", eventDate=" + eventDate +
            ", eventTimeZone='" + eventTimeZone + '\'' +
            ", eventType=" + eventType +
            ", location='" + location + '\'' +
            ", talentId=" + talentId +
            ", userId=" + userId +
            ", jobId=" + jobId +
            ", applicationId=" + applicationId +
            ", tenantId=" + tenantId +
            '}';
    }
}
