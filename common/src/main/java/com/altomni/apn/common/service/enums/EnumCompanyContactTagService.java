package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.dict.EnumCompanyContactTag;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EnumCompanyContactTagService {

    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumDictVO> findAll(SortType sortType) {
        List<EnumCompanyContactTag> enumCompanyContactTagList = enumCommonService.findAllEnumCompanyContactTag();
        if (CollUtil.isEmpty(enumCompanyContactTagList)) {
            return new ArrayList<>();
        }

        if (SortType.EN.equals(sortType)) {
            return enumCompanyContactTagList.stream().sorted(Comparator.comparing(EnumCompanyContactTag::getEnDisplayOrder, Comparator.nullsLast(Comparator.naturalOrder()))).map(item -> {
                EnumDictVO enumDictVO = new EnumDictVO();
                enumDictVO.setId(item.getId());
                enumDictVO.setName(item.getName());
                enumDictVO.setLabel(item.getEnDisplay());
                return enumDictVO;
            }).collect(Collectors.toList());
        } else {
            return enumCompanyContactTagList.stream().sorted(Comparator.comparing(EnumCompanyContactTag::getCnDisplayOrder, Comparator.nullsLast(Comparator.naturalOrder()))).map(item -> {
                EnumDictVO enumDictVO = new EnumDictVO();
                enumDictVO.setId(item.getId());
                enumDictVO.setName(item.getName());
                enumDictVO.setLabel(item.getCnDisplay());
                return enumDictVO;
            }).collect(Collectors.toList());
        }
    }

    public List<EnumCompanyContactTag> findAllByIds(List<Long> ids) {
        List<EnumCompanyContactTag> enumCompanyContactTagList = enumCommonService.findAllEnumCompanyContactTag();
        if (CollUtil.isEmpty(enumCompanyContactTagList)) {
            return new ArrayList<>();
        }

        Set<Long> idSet = new HashSet<>(ids);
        return enumCompanyContactTagList.stream().filter(o -> idSet.contains(o.getId())).collect(Collectors.toList());
    }
}
