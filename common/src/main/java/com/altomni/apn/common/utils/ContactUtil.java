package com.altomni.apn.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.HashSet;
import java.util.HexFormat;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class ContactUtil {

    public static final String LINKEDIN_REGEX = "https?://(www\\\\.)?linkedin\\.com/in/([^/]+)";

    public static final String MAIMAI_REGEX = "https://www\\.maimai\\.cn/detail\\?dstu=([0-9]+)";

    public static boolean isPureChinese(String str) {
        String regex = "^[\u4e00-\u9fa5]+$"; // 正则表达式：匹配纯中文字符
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    public static String extractSocialWebUserIdentify(String url, String regex) {

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            return matcher.group(1);
        } else {
            return StringUtils.EMPTY;
        }
    }

    public static String convertStringToSha256(String originalStr){
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hashInBytes = md.digest(originalStr.getBytes(StandardCharsets.UTF_8));
            String shaHex = HexFormat.of().formatHex(hashInBytes);

            return shaHex;

        } catch (NoSuchAlgorithmException e) {
            log.error("No such {} algorithm is available");
        }
        return StringUtils.EMPTY;
    }

    public static int maxSimilarityFromEnd(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return 0; // 处理空字符串的情况
        }

        int len1 = str1.length();
        int len2 = str2.length();
        int minLen = Math.min(len1, len2);
        int count = 0;

        // 从字符串的尾部开始比较
        for (int i = 1; i <= minLen; i++) {
            if (str1.charAt(len1 - i) == str2.charAt(len2 - i)) {
                count++;
            } else {
                break; // 遇到不同字符时停止
            }
        }

        return count;
    }

    // **1~3 位的合法国际区号**
    private static final Set<String> VALID_PHONE_CODES = new HashSet<>(Arrays.asList(
            "1", "7", "20", "27", "30", "31", "32", "33", "34", "36", "39", "40", "41", "43", "44",
            "45", "46", "47", "48", "49", "51", "52", "53", "54", "55", "56", "57", "58", "60", "61",
            "62", "63", "64", "65", "66", "81", "82", "84", "86", "90", "91", "92", "93", "94", "95",
            "98", "211", "212", "213", "216", "218", "220", "221", "222", "223", "224", "225", "226",
            "227", "228", "229", "230", "231", "232", "233", "234", "235", "236", "237", "238", "239",
            "240", "241", "242", "243", "244", "245", "246", "248", "249", "250", "251", "252", "253",
            "254", "255", "256", "257", "258", "260", "261", "262", "263", "264", "265", "266", "267",
            "268", "269", "290", "291", "297", "298", "299", "350", "351", "352", "353", "354", "355",
            "356", "357", "358", "359", "370", "371", "372", "373", "374", "375", "376", "377", "378",
            "379", "380", "381", "382", "383", "385", "386", "387", "389", "420", "421", "423", "500",
            "501", "502", "503", "504", "505", "506", "507", "508", "509", "590", "591", "592", "593",
            "594", "595", "596", "597", "598", "599", "670", "672", "673", "674", "675", "676", "677",
            "678", "679", "680", "681", "682", "683", "685", "686", "687", "688", "689", "690", "691",
            "692", "850", "852", "853", "855", "856", "880", "886", "960", "961", "962", "963", "964",
            "965", "966", "967", "968", "970", "971", "972", "973", "974", "975", "976", "977", "992",
            "993", "994", "995", "996", "998"
    ));

    // **4 位的合法国际区号**
    private static final Set<String> FOUR_DIGIT_CODES = new HashSet<>(Arrays.asList(
            "1242", "1246", "1264", "1268", "1284", "1340", "1345", "1441", "1473",
            "1649", "1664", "1670", "1671", "1684", "1758", "1767", "1784", "1787",
            "1809", "1829", "1849", "1868", "1869", "1876"
    ));

    // **正则匹配基本格式**
    private static final Pattern PHONE_CODE_PATTERN = Pattern.compile("^(\\+|00)?\\d{1,4}$");

    public static boolean isValidPhoneCode(String code) {
        if (code == null || code.isEmpty()) {
            return false;
        }

        // **格式校验**
        if (!PHONE_CODE_PATTERN.matcher(code).matches()) {
            return false;
        }

        // **去掉前缀（+ 或 00）**
        code = code.replaceAll("^(\\+|00)", "");

        // **判断是否合法**
        return VALID_PHONE_CODES.contains(code) || FOUR_DIGIT_CODES.contains(code);
    }

    public static boolean similarityCheck(String str1, String str2) {
        if (str1 == null || str2 == null) {
            return false;
        }
        str1 = str1.toLowerCase().trim().replaceFirst("^(\\+|00)", "");
        str2 = str2.toLowerCase().trim().replaceFirst("^(\\+|00)", "");

        if (str1.isEmpty() || str2.isEmpty()) {
            return false;
        }

        if (str1.length() == str2.length()) {
            return StringUtils.equals(str1, str2);
        }

        int commonStrLength = ContactUtil.maxSimilarityFromEnd(str1, str2);
        if (commonStrLength <= 7) { //一般电话长度至少7位
            return false;
        }
        int maxLength = Math.max(str1.length(), str2.length());
        if (maxLength - commonStrLength >= 5) { //00852
            return false;
        }
        int minLength = Math.min(str1.length(), str2.length());

        // 选择最长的字符串
        String longest = str1.length() >= str2.length() ? str1 : str2;

        String potentialCountryCode = longest.substring(0, maxLength-commonStrLength);
        return ContactUtil.isValidPhoneCode(potentialCountryCode);
    }



}
