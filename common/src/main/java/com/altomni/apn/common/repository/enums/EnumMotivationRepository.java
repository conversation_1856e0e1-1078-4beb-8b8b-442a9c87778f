package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumLanguage;
import com.altomni.apn.common.domain.dict.EnumMotivation;
import org.mapstruct.Mapper;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface EnumMotivationRepository  extends JpaRepository<EnumMotivation, Integer>, QuerydslPredicateExecutor<EnumMotivation> {
}
