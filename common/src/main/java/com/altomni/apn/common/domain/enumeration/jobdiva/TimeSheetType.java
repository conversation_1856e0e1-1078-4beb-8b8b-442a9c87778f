package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum TimeSheetType implements ConvertedEnum<Integer> {
    WEEK(0),
    WEEK_HOUR(1),
    WEEK_AM_PM(2);


    private final int dbValue;

    TimeSheetType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TimeSheetType, Integer> resolver = new ReverseEnumResolver<>(TimeSheetType.class, TimeSheetType::toDbValue);

    public static TimeSheetType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
