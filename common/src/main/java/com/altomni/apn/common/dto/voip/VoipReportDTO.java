package com.altomni.apn.common.dto.voip;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VoipReportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private LocalDate startTime;

    private LocalDate endTime;

    private List<Long> teamIdList;

    private List<Long> userIdList;

    private Set<Long> callResultList;

    private Set<Long> callTypeList;

    private String timeZone;

    private Long tenantId;

}
