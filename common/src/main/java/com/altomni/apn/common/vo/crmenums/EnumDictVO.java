package com.altomni.apn.common.vo.crmenums;

import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@ApiModel(description = "Vo for company serviceType")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class EnumDictVO implements Serializable {

    @ApiModelProperty(value = "the id for company serviceType.")
    private Integer id;

    @ApiModelProperty(value = "the label for company serviceType.")
    private String label;

    @ApiModelProperty(value = "the name for company serviceType")
//    @JsonIgnore
    private String name;

    @ApiModelProperty(value = "the parentCategory for company serviceType")
    @JsonIgnore
    private String parentCategory;

    private Integer enDisplayOrder;

    private Integer cnDisplayOrder;

    private List<EnumDictVO> children;


    public static EnumDictVO fromDictVO(DictVO dictVO, SortType type) {
        EnumDictVO enumDictVO = new EnumDictVO();
        ServiceUtils.myCopyProperties(dictVO, enumDictVO);
        enumDictVO.setId(dictVO.getId());
        enumDictVO.setName(dictVO.getName());
        enumDictVO.setLabel(SortType.CN.equals(type) ? dictVO.getLabelCn() : dictVO.getLabel());
        return enumDictVO;
    }

}
