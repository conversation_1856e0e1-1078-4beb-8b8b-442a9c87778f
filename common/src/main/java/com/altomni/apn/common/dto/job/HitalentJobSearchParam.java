package com.altomni.apn.common.dto.job;

import org.apache.xmpbox.type.JobType;

import java.io.Serializable;

public class HitalentJobSearchParam implements Serializable {

    private static final long serialVersionUID = 1L;

    private JobType type;

    private String location;

    private String keywords;

    private int page;

    private int size;

    public HitalentJobSearchParam() {
    }

    public JobType getType() {
        return type;
    }

    public void setType(JobType type) {
        this.type = type;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    @Override
    public String toString() {
        return "HitalentJobSearchParam{" +
            "type=" + type +
            ", location='" + location + '\'' +
            ", keywords='" + keywords + '\'' +
            ", page=" + page +
            ", size=" + size +
            '}';
    }
}
