package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class TalentAutoDeclassifyDto {

    private Long talentId;
    private Long recruitmentProcessId;
    private NodeType currentNode;
    private Long talentRecruitmentProcessId;
    private TalentDeclassifyType declassifyType;
}
