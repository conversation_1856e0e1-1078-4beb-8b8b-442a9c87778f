package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.domain.enumeration.dict.EnumStatus;
import com.altomni.apn.common.service.cache.EnumCommonService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class EnumUserResponsibilityService {

    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumUserResponsibility> findAllUserResponsibility() {
        List<EnumUserResponsibility> userResponsibilityList = enumCommonService.findAllEnumUserResponsibility();
        if (CollUtil.isNotEmpty(userResponsibilityList)) {
            userResponsibilityList = userResponsibilityList.stream().filter(userResponsibility -> Objects.equals(EnumStatus.ACTIVE, userResponsibility.getStatus())).collect(Collectors.toList());
        }
        return userResponsibilityList;
    }

    public Map<String, EnumUserResponsibility> findActiveUserResponsibilityMap() {
        List<EnumUserResponsibility> userResponsibilityList = enumCommonService.findAllEnumUserResponsibility();
        if (CollUtil.isNotEmpty(userResponsibilityList)) {
            return userResponsibilityList.stream()
                    .filter(userResponsibility -> Objects.equals(EnumStatus.ACTIVE, userResponsibility.getStatus()))
                    .collect(Collectors.toMap(EnumUserResponsibility::getLabel, Function.identity()));
        }
        return null;
    }
    public EnumUserResponsibility findUserResponsibilityByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }

        Map<String, EnumUserResponsibility> userResponsibilityMap = enumCommonService.findAllEnumUserResponsibilityNameMap();
        if (CollUtil.isNotEmpty(userResponsibilityMap) && userResponsibilityMap.containsKey(name) && Objects.equals(EnumStatus.ACTIVE, userResponsibilityMap.get(name).getStatus())) {
            return userResponsibilityMap.get(name);
        }
        return null;
    }

    public EnumUserResponsibility findById(Long id) {
        List<EnumUserResponsibility> userResponsibilityList = enumCommonService.findAllEnumUserResponsibility();
        Optional<EnumUserResponsibility> enumUserResponsibilityOptional = userResponsibilityList.stream().filter(o -> o.getId().equals(id)).findFirst();
        return enumUserResponsibilityOptional.isEmpty() ? null : enumUserResponsibilityOptional.get();
    }

}
