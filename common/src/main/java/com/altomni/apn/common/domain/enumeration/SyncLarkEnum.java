package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum SyncLarkEnum implements ConvertedEnum<Integer> {
    NO_SYNCHRONIZED(0),
    SYNCHRONIZED(1);

    private final int dbValue;

    SyncLarkEnum(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<SyncLarkEnum, Integer> resolver =
            new ReverseEnumResolver<>(SyncLarkEnum.class, SyncLarkEnum::toDbValue);

    public static SyncLarkEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}

