package com.altomni.apn.common.repository.permission;

import com.altomni.apn.common.domain.permission.PermissionTenantPrivilege;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface PermissionTenantPrivilegeRepository extends JpaRepository<PermissionTenantPrivilege, Long> {

    List<PermissionTenantPrivilege> findAllByTenantIdAndIsShow(Long tenantId, Boolean isShow);

    List<PermissionTenantPrivilege> findAllByTenantIdAndPrivilegeIdIn(Long tenantId, List<Long> privilegeIdList);

}
