package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * invoicing type enum
 */
public enum InvoicingType implements ConvertedEnum<Integer> {

    VAT_ELECTRONIC_PROFESSIONAL_INVOICE(0, "增值税电子专业发票"),
    VAT_ELECTRONIC_ORDINARY_INVOICE(1, "增值税电子普通发票"),
    DEDUCTION_INVOICE(2,"抵扣发票"),
    ;
    private final int dbValue;

    private final String description;

    InvoicingType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoicingType, Integer> resolver = new ReverseEnumResolver<>(InvoicingType.class, InvoicingType::toDbValue);

    public static InvoicingType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).description;
    }
}
