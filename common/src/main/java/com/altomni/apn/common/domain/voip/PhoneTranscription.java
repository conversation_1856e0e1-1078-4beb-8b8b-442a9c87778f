package com.altomni.apn.common.domain.voip;


import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSource;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSourceConverter;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.persistence.Convert;
import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Data
@Document(collection = "phone_transcription")
public class PhoneTranscription implements Serializable {

    @Id
//    @Field("contact_id")
    private String phoneCallId;

    @Field("account_id")
    private String accountId;

    @Field("user_id")
    private Long userId;

    @Field("tenant_id")
    private Long tenantId;

    @Field("reference_callee_id")
    private Long referenceCalleeId;

    private String phone;

    @Convert(converter = TranscriptionSourceConverter.class)
    private TranscriptionSource source;

    @Field("start_time")
    private Instant startTime;

    @Field("end_time")
    private Instant endTime;

    @Field("total_time")
    private Long totalTime;

    private List<Transcription> transcriptions;

    public PhoneTranscription() {
        this.transcriptions = new ArrayList<>();
    }

}
