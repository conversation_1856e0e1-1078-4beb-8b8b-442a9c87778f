package com.altomni.apn.common.domain.enumeration.message;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum MessageTypeEnum implements ConvertedEnum<Integer> {

    SYSTEM_MESSAGE(0),
    PERSONAL_WARNING(1),
    TEAM_WARNING(2),
    ADMIN_MESSAGE(3),

    FAVORITE(4);

    private Integer dbValue;

    MessageTypeEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<MessageTypeEnum, Integer> resolver = new ReverseEnumResolver<>(MessageTypeEnum.class, MessageTypeEnum::toDbValue);

    public static MessageTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
