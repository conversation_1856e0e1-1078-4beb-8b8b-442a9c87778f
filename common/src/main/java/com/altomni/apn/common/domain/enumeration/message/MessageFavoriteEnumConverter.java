package com.altomni.apn.common.domain.enumeration.message;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class MessageFavoriteEnumConverter extends AbstractAttributeConverter<MessageFavoriteEnum, Integer> {
    public MessageFavoriteEnumConverter() {
        super(MessageFavoriteEnum::toDbValue, MessageFavoriteEnum::fromDbValue);
    }
}