package com.altomni.apn.common.service.talent;

import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;
import java.util.Set;

@Component
@FeignClient(value = "talent-service")
public interface TalentFeignClient {

    @GetMapping("/talent/api/v3/talents/{talentId}/confidential/view-able")
    ResponseEntity<Boolean> confidentialTalentViewAble(@PathVariable("talentId") Long talentId);

    @PostMapping("/talent/api/v3/talents/confidential/view-able")
    ResponseEntity<Set<Long>> filterConfidentialTalentViewAble(@RequestBody Set<Long> talentIds);

    @PostMapping("/talent/api/v3/talents/confidential/info")
    ResponseEntity<Map<Long, ConfidentialInfoDto>> getTalentConfidentialInfo(@RequestBody Set<Long> talentIds);
}
