package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CalculateMethodTypeConverter extends AbstractAttributeConverter<CalculateMethodType, Integer> {
    public CalculateMethodTypeConverter() {
        super(CalculateMethodType::toDbValue, CalculateMethodType::fromDbValue);
    }
}
