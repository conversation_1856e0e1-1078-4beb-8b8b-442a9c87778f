package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoicingFormatConverter extends AbstractAttributeConverter<InvoicingFormat, Integer> {
    public InvoicingFormatConverter() {
        super(InvoicingFormat::toDbValue, InvoicingFormat::fromDbValue);
    }
}
