package com.altomni.apn.common.errors;
/*
 * Created by <PERSON> on 9/29/2017.
 */

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

public class ForbiddenException extends AbstractThrowableProblem {

    private static final long serialVersionUID = 1L;

    private final String message;

    private String description = null;

    public ForbiddenException(String message) {
        super(ErrorConstants.DEFAULT_TYPE, "Forbidden", Status.FORBIDDEN, message);
        this.message = message;
    }

    public ForbiddenException(String message, String description) {
        super(ErrorConstants.DEFAULT_TYPE, "Forbidden", Status.FORBIDDEN, message);
        this.message = message;
        this.description = description;
    }

    public ErrorVM getErrorVM() {
        return new ErrorVM(message, description);
    }

}
