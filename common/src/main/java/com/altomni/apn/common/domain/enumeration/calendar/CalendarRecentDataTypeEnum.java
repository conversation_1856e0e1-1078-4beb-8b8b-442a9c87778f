package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CalendarRecentDataTypeEnum implements ConvertedEnum<Integer> {

    COMPANY(0),
    TALENT(2);

    private Integer dbValue;

    CalendarRecentDataTypeEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CalendarRecentDataTypeEnum, Integer> resolver = new ReverseEnumResolver<>(CalendarRecentDataTypeEnum.class, CalendarRecentDataTypeEnum::toDbValue);

    public static CalendarRecentDataTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
