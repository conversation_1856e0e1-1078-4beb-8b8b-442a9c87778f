package com.altomni.apn.common.dto.permission;

import lombok.Data;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

/**
 * data scope
 */
@Data
public class TeamDataPermissionRespDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * self only
     */
    private Boolean self;

    /**
     * the permission ids for self
     */
    private Set<Long> selfPermissionIds;

    /**
     * the team belonged to
     */
    private Set<Long> readableTeamIds;

    /**
     * the teamId list with write permission
     */
    private Set<Long> writableTeamIds;

    /**
     * the teamId list with permission, including sub-team ids
     */
    private Set<Long> nestedTeamIds;

    /**
     * the teamId for private jobs
     */
    private Long teamIdForPrivateJob;

    /**
     * if the current user has private job permission or not
     */
    private boolean privateJobPermission;


    /**
     * all permission
     */
    private Boolean all;

    public TeamDataPermissionRespDTO() {
        this.all = false;
        this.self = false;
        this.privateJobPermission = false;
        this.selfPermissionIds = new HashSet<>();
        this.readableTeamIds = new HashSet<>();
        this.writableTeamIds = new HashSet<>();
        this.nestedTeamIds = new HashSet<>();
    }

}
