package com.altomni.apn.common.config.constants;

import com.altomni.apn.common.domain.enumeration.ContactType;

import java.util.Arrays;
import java.util.List;

/**
 *  talent contact type
 * <AUTHOR>
 */
public final class ContactTypeConstants {

    public static final List<ContactType> EXCLUSIVE_CONTACT_TYPES = Arrays.asList(ContactType.EMAIL, ContactType.PHONE, ContactType.WECHAT, ContactType.LINKEDIN, ContactType.FACEBOOK, ContactType.PERSONAL_WEBSITE, ContactType.WHATSAPP, ContactType.DODAX);

    public static final List<ContactType> COMPREHENSIVE_CONTACT_TYPES = Arrays.asList(ContactType.PRIMARY_EMAIL, ContactType.PRIMARY_PHONE, ContactType.EMAIL, ContactType.HOME_PHONE,
            ContactType.WORK_PHONE, ContactType.CELL_PHONE, ContactType.FAX, ContactType.LINKEDIN, ContactType.GITHUB, ContactType.DICE, ContactType.LIEPIN, ContactType.GOOGLE_SCHOLAR, ContactType.FACEBOOK, ContactType.TWITTER, ContactType.WECHAT, ContactType.WEIBO, ContactType.PHONE, ContactType.MEDIUM,
            ContactType.ANGELLIST, ContactType.YIFENGJIANLI, ContactType.ZHILIAN, ContactType.MONSTER, ContactType.LAGOU, ContactType.BOSSZHIPIN, ContactType.QQ, ContactType.SKYPE, ContactType.WHATSAPP, ContactType.MAIMAI, ContactType.PERSONAL_WEBSITE, ContactType.DODAX);

    public static final List<ContactType> EXTRA_CONTACT_TYPES_PHONES = Arrays.asList(ContactType.PRIMARY_PHONE, ContactType.HOME_PHONE, ContactType.WORK_PHONE, ContactType.CELL_PHONE);



}
