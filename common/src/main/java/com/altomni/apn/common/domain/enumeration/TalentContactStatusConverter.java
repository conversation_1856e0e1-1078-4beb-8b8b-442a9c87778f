package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TalentContactStatusConverter extends AbstractAttributeConverter<TalentContactStatus, Integer> {
    public TalentContactStatusConverter() {
        super(TalentContactStatus::toDbValue, TalentContactStatus::fromDbValue);
    }
}
