package com.altomni.apn.common.domain.dict;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "CompanyServiceTypeConnectTenant")
@Entity
@Data
@Table(name = "company_service_type_connect_tenant")
public class CompanyServiceTypeConnectTenant implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "enum_company_service_type_id")
    private Integer enumId;

}
