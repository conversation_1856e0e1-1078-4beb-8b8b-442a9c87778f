package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.enumeration.ReviewedByType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentReviewNoteDTO  extends AbstractAuditingEntity {
    private Long id;
    @NotNull(message = "reviewedByType is null")
    private ReviewedByType reviewedByType;
    @NotNull(message = "talentId is null")
    private Long talentId;
    @NotNull(message = "reviewedBy is null")
    private Long reviewedBy;
    private String reviewedByName;
    @NotNull(message = "reviewedDate is null")
    private String reviewedDate;
    private String timezone;
    private String note;
}
