package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum PayRateType implements ConvertedEnum<Integer>
{
    OVER_TIME(0),
    DOUBLE_TIME(1),
    BILL_RATE(2),
    PAY_RATE(3)
    ;

    private final int dbValue;

    PayRateType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<PayRateType, Integer> resolver = new ReverseEnumResolver<>(PayRateType.class, PayRateType::toDbValue);

    public static PayRateType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
