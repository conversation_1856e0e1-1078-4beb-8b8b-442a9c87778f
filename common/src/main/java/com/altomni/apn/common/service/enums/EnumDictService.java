package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollectionUtil;
import com.altomni.apn.common.constants.CommonOptionEnum;
import com.altomni.apn.common.domain.dict.EnumFrontDisplay;
import com.altomni.apn.common.domain.dict.EnumMotivation;
import com.altomni.apn.common.dto.enums.CmnEnumDictDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnumDictService {
    private final Logger log = LoggerFactory.getLogger(EnumDictService.class);

    @Resource
    private EnumCommonService enumCommonService;

    public List<CmnEnumDictDTO> findJobIntentionEnum(SortType type) {
        log.info("[APN: EnumDictService @{}] request to get job intention enum data, type: {}", SecurityUtils.getUserId(), type);

        List<EnumMotivation> result = enumCommonService.findAllEnumMotivation();
        return transfer2CmnEnumDictDTO(type, result);
    }


    public List<CmnEnumDictDTO> findGenderEnum(SortType type) {
        log.info("[APN: EnumDictService @{}] request to get gender enum data, type: {}", SecurityUtils.getUserId(), type);

        return transfer2CmnEnumDictDTO(type, enumCommonService.findAllEnumGender());
    }

    private List<CmnEnumDictDTO> transfer2CmnEnumDictDTO(SortType type, List<? extends EnumFrontDisplay> frontDisplay) {
        if (CollectionUtil.isNotEmpty(frontDisplay)) {
            frontDisplay.sort(Comparator.comparing(EnumFrontDisplay::getSort));
            return frontDisplay.stream().map(r -> CmnEnumDictDTO.fromBizDict(r, type)).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public List<CmnEnumDictDTO> findVeteran(SortType type) {
        log.info("[APN: EnumDictService @{}] request to get veteran enum data, type: {}", SecurityUtils.getUserId(), type);

        return transfer2CmnEnumDictDTO(type, enumCommonService.findAllEnumVeteran());
    }

    public List<CmnEnumDictDTO> findPreferredPronoun(SortType type) {
        log.info("[APN: EnumDictService @{}] request to get preferred pronou  enum data, type: {}", SecurityUtils.getUserId(), type);

        return transfer2CmnEnumDictDTO(type, enumCommonService.findAllEnumPreferredPronoun());
    }

    public List<CmnEnumDictDTO> findEthnicity(SortType type) {
        log.info("[APN: EnumDictService @{}] request to get ethnicity enum data, type: {}", SecurityUtils.getUserId(), type);

        return transfer2CmnEnumDictDTO(type, enumCommonService.findAllEnumEthnicity());
    }

    public List<CmnEnumDictDTO> findCommonOption(SortType type) {
        List<CmnEnumDictDTO> ret = new ArrayList<>();
        for(CommonOptionEnum commonOption : CommonOptionEnum.values()) {
            CmnEnumDictDTO dto = new CmnEnumDictDTO();
            dto.setId(commonOption.getId().intValue());
            dto.setName(commonOption.getName());
            if(SortType.EN.equals(type)) {
                dto.setLabel(commonOption.getEnDisplay());
            } else {
                dto.setLabel(commonOption.getCnDisplay());
            }
            dto.setSort(0);
            ret.add(dto);
        }
        return ret;
    }
}
