package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoicingApplicationTypeConverter extends AbstractAttributeConverter<InvoicingApplicationType, Integer> {
    public InvoicingApplicationTypeConverter() {
        super(InvoicingApplicationType::toDbValue, InvoicingApplicationType::fromDbValue);
    }
}
