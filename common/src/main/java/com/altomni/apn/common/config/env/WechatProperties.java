package com.altomni.apn.common.config.env;


import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class WechatProperties {

    @Value("${application.wechat.officialAccount.appid}")
    private String OfficialAccountAppid;

    @Value("${application.wechat.officialAccount.secret}")
    private String OfficialAccountSecret;

    @Value("${application.wechat.officialAccount.ticketExpireTime}")
    private Integer JSAPITicketExpireTime;
}
