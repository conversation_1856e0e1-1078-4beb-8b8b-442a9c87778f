package com.altomni.apn.common.enumeration.reportSubscriptions;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum PushMethod implements ConvertedEnum<Integer> {
    //1-<PERSON><PERSON>, 2-<PERSON>ail
    LARK(1),
    EMAIL(2)
    ;

    private final int dbValue;

    PushMethod(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<PushMethod, Integer> resolver =
            new ReverseEnumResolver<>(PushMethod.class, PushMethod::toDbValue);

    public static PushMethod fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}

