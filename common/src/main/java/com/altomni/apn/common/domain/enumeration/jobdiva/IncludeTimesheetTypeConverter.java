package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class IncludeTimesheetTypeConverter extends AbstractAttributeConverter<IncludeTimesheetType,Integer> {

    public IncludeTimesheetTypeConverter(){
        super(IncludeTimesheetType::toDbValue,IncludeTimesheetType::fromDbValue);
    }
}
