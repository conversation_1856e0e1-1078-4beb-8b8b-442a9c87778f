package com.altomni.apn.common.dto.job;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * A JobNote.
 */
public class JobBriefDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "job id", required = true)
    private Long id;

    @ApiModelProperty(value = "title", required = true)
    @NotNull
    private String title;

    @ApiModelProperty(value = "job status", required = true)
    @NotNull
    private JobStatus status;

    public JobBriefDTO() {}

    public JobBriefDTO(Long id, String title, JobStatus status) {
        this.id = id;
        this.title = title;
        this.status = status;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public JobBriefDTO title(String title) {
        this.title = title;
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public JobStatus getStatus() { return status; }

    public JobBriefDTO status(JobStatus status) {
        this.status = status;
        return this;
    }
    public void setStatus(JobStatus status) { this.status = status; }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JobBriefDTO jobNote = (JobBriefDTO) o;
        if (jobNote.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), jobNote.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "JobBriefDTO{" +
            "id=" + id +
            ", title='" + title + '\'' +
            ", status=" + status +
            '}';
    }
}
