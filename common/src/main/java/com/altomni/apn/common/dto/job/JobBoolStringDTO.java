package com.altomni.apn.common.dto.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Objects;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Id;
import java.io.Serializable;
import java.util.List;

/**
 * A JobBoolString.
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobBoolStringDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @ApiModelProperty(value = "Job id")
    private Long jobId;

    @ApiModelProperty(value = "The relationship among the skills in string key")
    private String relationship;

    @ApiModelProperty(value = "From where the skills come")
    private String section;

    @ApiModelProperty(value = "Parser's score on the talent's qualification for the job. User should not change this.")
    private Double score;

    @ApiModelProperty(value = "One skill string. Parser return string array. eg: [\"java\"]")
    private List<String> strings;

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public List<String> getStrings() {
        return strings;
    }

    public void setStrings(List<String> strings) {
        this.strings = strings;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        JobBoolStringDTO that = (JobBoolStringDTO) o;
        return Objects.equal(id, that.id) &&
                Objects.equal(jobId, that.jobId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id, jobId);
    }

    @Override
    public String toString() {
        return "JobBoolString{" +
                "id=" + id +
                ", jobId=" + jobId +
                ", relationship=" + relationship +
                ", section=" + section +
                ", score=" + score +
                ", strings='" + strings + '\'' +
                '}';
    }
}
