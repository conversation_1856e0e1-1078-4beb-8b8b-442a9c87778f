package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.CurrencyRateDay;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Repository
public interface CurrencyRateDayRepository extends JpaRepository<CurrencyRateDay, Long>, QuerydslPredicateExecutor<CurrencyRateDay> {

    //去 currencyId 相同, rate_day 最大的数据, 并取一个
    Optional<CurrencyRateDay> findFirstByCurrencyIdOrderByRateDayDesc(Long currencyId);

}