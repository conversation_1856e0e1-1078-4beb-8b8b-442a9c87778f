package com.altomni.apn.common.domain.enumeration.message;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class MessageTypeEnumConverter extends AbstractAttributeConverter<MessageTypeEnum, Integer> {
    public MessageTypeEnumConverter() {
        super(MessageTypeEnum::toDbValue, MessageTypeEnum::fromDbValue);
    }
}