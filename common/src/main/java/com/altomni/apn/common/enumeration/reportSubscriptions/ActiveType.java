package com.altomni.apn.common.enumeration.reportSubscriptions;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ActiveType implements ConvertedEnum<Integer> {
    //0-无效, 1-有效
    INACTIVE(0),
    ACTIVE(1),
    ;

    private final int dbValue;

    ActiveType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ActiveType, Integer> resolver =
            new ReverseEnumResolver<>(ActiveType.class, ActiveType::toDbValue);

    public static ActiveType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}