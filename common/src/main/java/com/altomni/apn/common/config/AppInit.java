package com.altomni.apn.common.config;

import com.altomni.apn.common.dto.permission.PermissionPrivilegeVM;
import com.altomni.apn.common.service.cache.CachePermission;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 */
@Component
public class AppInit {

    @Resource
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    @Resource
    private CachePermission cachePermission;

    @Value("${server.servlet.context-path}")
    private String contextPath;

    @EventListener(ApplicationReadyEvent.class)
    public void initPrivilege(){

        String serviceName = contextPath.substring(1);
        Map<String, List<PermissionPrivilegeVM.Detail>> map = new TreeMap<>();
        requestMappingHandlerMapping.getHandlerMethods().entrySet().forEach(entry -> {
            RequestMappingInfo info = entry.getKey();
            HandlerMethod method = entry.getValue();
            String privilege = method.getMethod().getName();
            if (method.hasMethodAnnotation(PrivilegeName.class)) {
                privilege = method.getMethodAnnotation(PrivilegeName.class).value();
            }
            String uri = contextPath + info.getPatternValues().toArray(new String[]{})[0];
            String declaringClass = method.getMethod().getDeclaringClass().getSimpleName();
            if (!map.containsKey(declaringClass)){
                map.put(declaringClass, new ArrayList<>());
            }
            String requestMethod = info.getMethodsCondition().toString().replaceAll("(\\[|\\])", "");
            String api = requestMethod + uri.replaceAll("\\{[\\w-]+\\}", "{}");
            map.get(declaringClass).add(new PermissionPrivilegeVM.Detail(privilege, requestMethod, uri, api));
        });
        System.out.println(contextPath + ": APIs - " + requestMappingHandlerMapping.getHandlerMethods().size());
        cachePermission.updateSystemApis(serviceName, map);
    }


    /*@PostConstruct
    public void initPermissionRule(){
        System.out.println("initPermissionRule");
        // 获得数据权限的规则
        cachePermissionReadOnly.getPermissionRulesByTenantId(4L);
    }*/

    /*@Bean
    public CommandLineRunner CommandLineRunnerBean() {
        return (args) -> {
            System.out.println("Int cache [PermissionRulesByTenantIds]");
            // 获得数据权限的规则
            cachePermissionReadOnly.getPermissionRulesByTenantIdFromCacheOnly(-1L);
            cachePermissionReadOnly.getPermissionRulesByTenantIdFromCacheOnly(4L);
        };
    }*/
}
