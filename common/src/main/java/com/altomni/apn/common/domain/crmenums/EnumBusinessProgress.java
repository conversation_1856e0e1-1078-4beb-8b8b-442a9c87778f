package com.altomni.apn.common.domain.crmenums;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "enum_business_progress")
public class EnumBusinessProgress implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Size(max = 128)
    @Column(name = "name", length = 128)
    private String name;

    @Size(max = 128)
    @Column(name = "en_display", length = 128)
    private String enDisplay;

    @Size(max = 128)
    @Column(name = "cn_display", length = 128)
    private String cnDisplay;

    @Column(name = "en_sort_order")
    private Integer enSortOrder;

    @Column(name = "cn_sort_order")
    private Integer cnSortOrder;

    @Column(name = "en_display_order")
    private Integer enDisplayOrder;

    @Column(name = "cn_display_order")
    private Integer cnDisplayOrder;

}