package com.altomni.apn.common.domain.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "talent_association_job_folder_talent")
public class TalentAssociationJobFolderTalent extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @NotNull
    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    @Size(max = 32)
    @NotNull
    @Column(name = "talent_association_job_folder_folder_id", nullable = false, length = 32)
    private String talentRelateJobFolderFolderId;

    @NotNull
    @Column(name = "added_by", nullable = false)
    private Long addedBy;
}