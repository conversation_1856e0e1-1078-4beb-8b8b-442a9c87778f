package com.altomni.apn.common.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The NodeStatus enumeration.
 */
public enum NodeStatus implements ConvertedEnum<Integer> {

    ACTIVE(1), // last node with data (OR: last completed node; for interview, it may be completed or in progress)

    COMPLETED(2), // already complete

    INACTIVE(3), // node doesn't have any data

    ELIMINATED(4);

    private final Integer dbValue;

    NodeStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<NodeStatus, Integer> resolver =
        new ReverseEnumResolver<>(NodeStatus.class, NodeStatus::toDbValue);

    public static NodeStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
