package com.altomni.apn.common.service.cache;

import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.service.user.SsoUserLoadByFeign;
import com.ipg.resourceserver.user.SsoOidcUser;
import com.ipg.resourceserver.user.UserMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Component;

import javax.validation.constraints.NotNull;
import java.util.Optional;

@Slf4j
@Component
public class CachedFeignSsoUserMapping implements UserMapping {

    private final SsoUserLoadByFeign ssoUserLoadByFeign;

    public CachedFeignSsoUserMapping(SsoUserLoadByFeign ssoUserLoadByFeign) {
        this.ssoUserLoadByFeign = ssoUserLoadByFeign;
    }

    @Override
    public SsoOidcUser mapping(OidcUser originalUser) {
        log.debug("Mapping user: {}", originalUser.getEmail());
        String email = originalUser.getEmail();
        try {
            LoginUserDTO userDto = ssoUserLoadByFeign.findByEmail(email).getBody();
            log.debug("User found: {}", userDto);
            LoginUserDTO loginUserDTO = Optional.ofNullable(userDto).map(userBriefDTO -> getLoginUserDTO(userBriefDTO, originalUser)).orElse(null);
            return loginUserDTO;
        } catch (Exception e) {
            log.error("Error finding user by email: {}", e.getMessage(), e);
            return null;
        }
    }


    @NotNull
    public static LoginUserDTO getLoginUserDTO(LoginUserDTO apnUser, OidcUser user) {
        apnUser.setEmail(user.getEmail());
        apnUser.setUsername(user.getName());
        apnUser.setOidcUser(user);
        return apnUser;
    }
}
