package com.altomni.apn.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Slf4j
public class FileTypeValidateUtil
{

    private static Map<String,String> FILE_TYPE_MAP;
    static
    {
        FILE_TYPE_MAP = getAllFileType();
    }

    private static Map<String,String> getAllFileType()
    {
        Map<String, String> FILE_TYPE_MAP = new HashMap<String, String>();
        FILE_TYPE_MAP.put("jpg", "FFD8FF"); //JPEG (jpg)
        FILE_TYPE_MAP.put("png", "89504E47");  //PNG (png)
//        FILE_TYPE_MAP.put("gif", "47494638");  //GIF (gif)
//        FILE_TYPE_MAP.put("tif", "49492A00");  //TIFF (tif)
//        FILE_TYPE_MAP.put("bmp", "424D"); //Windows Bitmap (bmp)
//        FILE_TYPE_MAP.put("dwg", "41433130"); //CAD (dwg)
        FILE_TYPE_MAP.put("html", "68746D6C3E");  //HTML (html)
        //FILE_TYPE_MAP.put("rtf", "7B5C727466");  //Rich Text Format (rtf)
        //FILE_TYPE_MAP.put("xml", "3C3F786D6C");
//        FILE_TYPE_MAP.put("zip", "504B0304");
        //FILE_TYPE_MAP.put("rar", "52617221");
        //FILE_TYPE_MAP.put("psd", "38425053");  //Photoshop (psd)
        //FILE_TYPE_MAP.put("eml", "44656C69766572792D646174653A");  //Email [thorough only] (eml)
        //FILE_TYPE_MAP.put("dbx", "CFAD12FEC5FD746F");  //Outlook Express (dbx)
        //FILE_TYPE_MAP.put("pst", "2142444E");  //Outlook (pst)
        //FILE_TYPE_MAP.put("xls", "D0CF11E0");  //MS Word
        FILE_TYPE_MAP.put("txt", "E4B88AE4");  //MS Word
        FILE_TYPE_MAP.put("docx", "504B03040A");  //MS Word
        FILE_TYPE_MAP.put("doc", "D0CF11E0");  //MS Excel 注意：word 和 excel的文件头一样
        //FILE_TYPE_MAP.put("mdb", "5374616E64617264204A");  //MS Access (mdb)
        //FILE_TYPE_MAP.put("wpd", "FF575043"); //WordPerfect (wpd)
        //FILE_TYPE_MAP.put("eps", "252150532D41646F6265");
        //FILE_TYPE_MAP.put("ps", "252150532D41646F6265");
        FILE_TYPE_MAP.put("pdf", "255044462D312E");  //Adobe Acrobat (pdf)
        //FILE_TYPE_MAP.put("qdf", "AC9EBD8F");  //Quicken (qdf)
        //FILE_TYPE_MAP.put("pwl", "E3828596");  //Windows Password (pwl)
        //FILE_TYPE_MAP.put("wav", "57415645");  //Wave (wav)
        //FILE_TYPE_MAP.put("avi", "41564920");
        //FILE_TYPE_MAP.put("ram", "2E7261FD");  //Real Audio (ram)
        //FILE_TYPE_MAP.put("rm", "2E524D46");  //Real Media (rm)
        //FILE_TYPE_MAP.put("mpg", "000001BA");  //
        //FILE_TYPE_MAP.put("mov", "6D6F6F76");  //Quicktime (mov)
        //FILE_TYPE_MAP.put("asf", "3026B2758E66CF11"); //Windows Media (asf)
        //FILE_TYPE_MAP.put("mid", "4D546864");  //MIDI (mid)
        return FILE_TYPE_MAP;
    }

    /**
     * Created on 2021-01-27
     * <p>Discription:[getFileByFile,获取文件类型,包括图片,若格式不是已配置的,则返回null]</p>
     * @param file
     * @return fileType
     * @author:[<EMAIL>]
     */
    public final static String getFileTypeByFile(File file)
    {
        String filetype = null;
        byte[] b = new byte[50];
        try
        {
            InputStream is = new FileInputStream(file);
            is.read(b);
            filetype = getFileTypeByStream(b);
            if(filetype == null)
            {
                filetype = file.getName();
                filetype = filetype.substring(filetype.lastIndexOf(".")+1);
                filetype = FILE_TYPE_MAP.get(filetype);
            }

            is.close();
        }
        catch (FileNotFoundException e)
        {
            log.error("error", e);
        }
        catch (IOException e)
        {
            log.error("error", e);
        }
        return filetype;
    }

    public final static String getFileTypeByFile(MultipartFile file)
    {
        String filetype = null;
        byte[] b = new byte[50];
        try
        {
            InputStream is =file.getInputStream();
            is.read(b);
            filetype = getFileTypeByStream(b);
            if(filetype == null)
            {
                String filename = file.getOriginalFilename();
                if(filename != null)
                {
                    filename = filename.substring(filename.lastIndexOf(".")+1);
                    filetype  =FILE_TYPE_MAP.get(filename);
                }
            }
            is.close();
        }
        catch (FileNotFoundException e)
        {
            log.error("error", e);
        }
        catch (IOException e)
        {
            log.error("error", e);
        }
        return filetype;
    }




    /**
     * Created on 2021-01-27
     * <p>Discription:[getFileTypeByStream]</p>
     * @param b
     * @return fileType
     * @author:[<EMAIL>]
     */
    public final static String getFileTypeByStream(byte[] b)
    {
        String filetypeHex = String.valueOf(getFileHexString(b));
        System.out.println("filetypeHex:"+filetypeHex);
        Iterator<Map.Entry<String, String>> entryiterator = FILE_TYPE_MAP.entrySet().iterator();
        while (entryiterator.hasNext())
        {
            Map.Entry<String,String> entry =  entryiterator.next();
            String fileTypeHexValue = entry.getValue();
            if (filetypeHex.toUpperCase().startsWith(fileTypeHexValue))
            {
                return entry.getKey();
            }
        }
        return null;
    }


    /**
     * Created on 2021-01-27
     * <p>Discription:[getFileHexString]</p>
     * @param b
     * @return fileTypeHex
     * @author:[<EMAIL>]
     */
    public final static String getFileHexString(byte[] b)
    {
        StringBuilder stringBuilder = new StringBuilder();
        if (b == null || b.length <= 0)
        {
            return null;
        }
        for (int i = 0; i < b.length; i++)
        {
            int v = b[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2)
            {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }


//    public static void main(String[] args) {
//        System.out.println(getFileTypeByFile(new File("C:\\Users\\<USER>\\Desktop\\简历\\Google 谷歌 US_Test Engineer II.doc")));
//    }
//

}
