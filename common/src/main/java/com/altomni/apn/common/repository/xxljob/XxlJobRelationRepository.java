package com.altomni.apn.common.repository.xxljob;

import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.xxljob.SystemCalendarResult;
import com.altomni.apn.common.dto.xxljob.XxlJobForTeamDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobOnboardDateByTeamDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUnSumbitTalentForTeamDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

@Repository
public interface XxlJobRelationRepository extends JpaRepository<XxlJobRelation, Long> {

    XxlJobRelation findXxlJobRelationByTypeAndReferenceId(XxlJobRelationTypeEnum type, Long referenceId);

    List<XxlJobRelation> findAllByTypeAndReferenceId(XxlJobRelationTypeEnum type, Long referenceId);

    List<XxlJobRelation> findAllByTypeAndUserId(XxlJobRelationTypeEnum type, Long userId);

    XxlJobRelation findXxlJobRelationByXxlJobId(Integer xxlJobId);

    List<XxlJobRelation> findAllByTypeAndTenantId(XxlJobRelationTypeEnum type, Long tenantId);

    List<XxlJobRelation> findAllByType(XxlJobRelationTypeEnum type);

    Page<XxlJobRelation> findAllByType(XxlJobRelationTypeEnum type, Pageable pageable);

    List<XxlJobRelation> findAllByTypeAndReferenceIdIn(XxlJobRelationTypeEnum type, List<Long> referenceIdList);

    @Modifying
    @Transactional
    @Query(value = " delete from xxl_job_relation where xxl_job_id = ?1 ", nativeQuery = true)
    void deleteByXxlJobId(Integer xxlJobId);

    @Modifying
    @Transactional
    @Query(value = " delete from xxl_job_relation where xxl_job_id in ?1 ", nativeQuery = true)
    void deleteByXxlJobIdList(List<Integer> xxlJobId);

    @Query(value = " SELECT xjr.* FROM xxl_job_relation xjr INNER JOIN user u on u.id = xjr.user_id where u.tenant_id = ?2 and xjr.type in ?1 and xjr.send_time >= now() ", nativeQuery = true)
    List<XxlJobRelation> findAllByTypeAndTenantIdAndSendTime(List<Integer> typeList, Long tenantId);

    @Query(value = " SELECT xjr.* FROM xxl_job_relation xjr where xjr.send_time >= now() and xjr.user_id = ?1 and xjr.type != 10 ", nativeQuery = true)
    List<XxlJobRelation> findAllByUserIdAndSendTime(Long userId);


    @Query(value = " select id from tenant ", nativeQuery = true)
    List<Long> findAllTenant();

    @Modifying
    @Transactional
    @Query(value = " update xxl_job_relation set send_time = ?2, reminder_config = ?3, timezone = ?4," +
            "xxl_job_param = JSON_SET(xxl_job_param, '$.reminderConfig',?3,'$.sendTime', ?5, '$.timezone', ?4) where xxl_job_id = ?1 ", nativeQuery = true)
    void updateTriggerByXxlJobId(Integer xxlJobId, Instant triggerInstant, String reminderConfig, String timezone, String triggerTimeFormat);

    @Query(value = " SELECT rl.user_id userId, u.tenant_id tenantId, put.team_id teamId, u.custom_timezone as timezone " +
            " FROM user_role rl inner join role r on r.id = rl.role_id " +
            " inner join permission_role_privilege prp on prp.role_id = rl.role_id " +
            " inner join permission_user_team put on put.user_id = rl.user_id and put.is_primary = 1 " +
            " inner join user u on u.id = rl.user_id and u.activated = 1 " +
            " where prp.privilege_id = ?1 " +
            " group by rl.user_id ", nativeQuery = true)
    List<XxlJobForTeamDTO> findUserTeamDTOByPrivilegeId(Long privilegeId);

    @Query(value = " SELECT rl.user_id userId, u.tenant_id tenantId, put.team_id teamId, u.custom_timezone as timezone " +
            " FROM user_role rl inner join role r on r.id = rl.role_id " +
            " inner join permission_role_privilege prp on prp.role_id = rl.role_id " +
            " inner join permission_user_team put on put.user_id = rl.user_id and put.is_primary = 1 " +
            " inner join user u on u.id = rl.user_id and u.activated = 1 " +
            " where prp.privilege_id = ?1 and u.tenant_id in (?2) " +
            " group by rl.user_id ", nativeQuery = true)
    List<XxlJobForTeamDTO> findUserTeamDTOByPrivilegeIdAndTenantIdList(Long privilegeId, List<Long> tenantIdList);

    @Query(value = " SELECT kpi.user_id userId, kpi.talent_recruitment_process_id talentRecruitmentProcessId, trp.talent_id talentId, date.onboard_date onboardDate, " +
            " t.full_name fullName, u.first_name firstName, u.last_name lastName " +
            " FROM talent_recruitment_process_kpi_user kpi " +
            " INNER JOIN permission_user_team put ON put.user_id = kpi.user_id " +
            " INNER JOIN USER u ON u.id = put.user_id " +
            " INNER JOIN talent_recruitment_process_onboard_date date ON date.talent_recruitment_process_id = kpi.talent_recruitment_process_id " +
            " INNER JOIN talent_recruitment_process_node trpn ON trpn.talent_recruitment_process_id = kpi.talent_recruitment_process_id " +
            " INNER JOIN talent_recruitment_process trp ON trp.id = kpi.talent_recruitment_process_id " +
            " INNER JOIN talent t ON t.id = trp.talent_id  " +
            " where trpn.node_type IN ( 41, 50 ) AND trpn.node_status = 1 AND put.team_id = ?1 AND kpi.user_role != 4  " +
            " GROUP BY kpi.user_id,kpi.talent_recruitment_process_id ", nativeQuery = true)
    List<XxlJobOnboardDateByTeamDTO> findOnboardDateByTeamId(Long teamId);

    @Query(value = " SELECT uu.id userId,count(t.id) count,uu.first_name firstName,uu.last_name lastName " +
            " from user uu " +
            " left join talent t on t.puser_id = uu.id and t.created_date BETWEEN ?2 AND ?3 " +
            " inner join permission_user_team put on put.user_id = uu.id " +
            " WHERE exists (select 1 from user u inner join user_role ur on u.id = ur.user_id " +
            " inner join permission_role_privilege r on r.role_id = ur.role_id " +
            " inner join permission_user_team ppm on ppm.user_id = u.id and ppm.is_primary = 1 " +
            " where r.privilege_id = ?4 and u.id = ?1 and ppm.team_id = put.team_id) and uu.activated = 1 " +
            " group by uu.id " +
            " having count(1) < ?5 ", nativeQuery = true)
    List<XxlJobUnSumbitTalentForTeamDTO> findUnSubmitToJobCountByUserIdAndCreatedDateAndPrivilegeId(Long userId, Instant startDate, Instant endDate, Long privilegeId, Integer unSubmitCount);


    @Query(value = """
            SELECT
                j.id AS jobId,
                j.title AS jobTitle,
                j.tenant_id AS tenantId,
                j.company_id AS companyId,
                c.full_business_name AS companyName,
                c.active AS active,
                 CASE
            WHEN j.sales_lead_id IS NOT NULL THEN (
                    SELECT GROUP_CONCAT(DISTINCT user_id SEPARATOR ',')
                    FROM (
                            SELECT ujr.user_id
                            FROM user_job_relation ujr
                            WHERE ujr.job_id = j.id AND ujr.status = 1
                        
                            UNION
                        
                            SELECT csla.user_id
                            FROM business_flow_administrator csla
                            WHERE csla.sales_lead_role IN (0, 3)
                            AND csla.account_business_id = j.sales_lead_id
                    ) combined_users
            )
            ELSE (
                    SELECT GROUP_CONCAT(DISTINCT user_id SEPARATOR ',')
                    FROM (
                            SELECT ujr.user_id
                            FROM user_job_relation ujr
                            WHERE ujr.job_id = j.id AND ujr.status = 1
                        
                            UNION
                        
                            SELECT csla.user_id
                            FROM business_flow_administrator csla
                            WHERE csla.sales_lead_role IN (0, 3)
                            AND csla.company_id = j.company_id
                    ) combined_users
            )
            END AS userIds
            FROM job j
            LEFT JOIN (
                -- 获取每个职位的最新流程创建时间
                SELECT
                    job_id,
                    MAX(created_date) AS latest_process_date
                FROM talent_recruitment_process
                GROUP BY job_id
            ) latest_trp ON j.id = latest_trp.job_id
            LEFT JOIN company c ON j.company_id = c.id
            WHERE j.status = 0
            AND j.last_modified_date >= ?1
            AND DATEDIFF(NOW(), COALESCE(latest_trp.latest_process_date, j.created_date)) > ?2
            AND j.tenant_id = ?3
            GROUP BY j.id
            HAVING userIds IS NOT NULL AND userIds != ''
            """, nativeQuery = true)
    List<SystemCalendarResult> findNotSubmitTalentJob(String beginDateTime, Long interval, Long tenantId);


    @Query(value = """
            SELECT
                j.id AS jobId,
                j.title AS jobTitle,
                j.tenant_id AS tenantId,
                j.company_id AS companyId,
                c.full_business_name AS companyName,
                c.active AS active,
                 CASE
            WHEN j.sales_lead_id IS NOT NULL THEN (
                    SELECT GROUP_CONCAT(DISTINCT user_id SEPARATOR ',')
                    FROM (
                            SELECT ujr.user_id
                            FROM user_job_relation ujr
                            WHERE ujr.job_id = j.id AND ujr.status = 1
                        
                            UNION
                        
                            SELECT csla.user_id
                            FROM business_flow_administrator csla
                            WHERE csla.sales_lead_role IN (0, 3)
                            AND csla.account_business_id = j.sales_lead_id
                    ) combined_users
            )
            ELSE (
                    SELECT GROUP_CONCAT(DISTINCT user_id SEPARATOR ',')
                    FROM (
                            SELECT ujr.user_id
                            FROM user_job_relation ujr
                            WHERE ujr.job_id = j.id AND ujr.status = 1
                        
                            UNION
                        
                            SELECT csla.user_id
                            FROM business_flow_administrator csla
                            WHERE csla.sales_lead_role IN (0, 3)
                            AND csla.company_id = j.company_id
                    ) combined_users
            )
            END AS userIds
            FROM job j
            LEFT JOIN company c ON j.company_id = c.id
            LEFT JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
            LEFT JOIN (
                -- 获取每个职位的最新流程创建时间/修改时间
                SELECT
                    job_id,
                    MAX(created_date) AS latest_process_date,
                    MAX(last_modified_date) AS latest_modified_date
                FROM talent_recruitment_process
                GROUP BY job_id
            ) latest_trp ON j.id = latest_trp.job_id
            LEFT JOIN (
                -- 获取每个流程的最新面试记录时间
                SELECT
                    trp.job_id,
                    MAX(trpi.last_interview_date) AS latest_interview_date
                FROM talent_recruitment_process trp
                JOIN (
                    -- 获取每个流程的最新面试记录
                    SELECT
                        talent_recruitment_process_id,
                        MAX(created_date) AS last_interview_date
                    FROM talent_recruitment_process_interview
                    GROUP BY talent_recruitment_process_id
                ) trpi ON trp.id = trpi.talent_recruitment_process_id
                GROUP BY trp.job_id
            ) latest_interview ON j.id = latest_interview.job_id
            WHERE j.status = 0
            AND j.last_modified_date >= ?1
            AND DATEDIFF(NOW(), COALESCE(latest_interview.latest_interview_date, j.created_date)) > ?2
            AND j.tenant_id = ?3
            AND rp.id IN (
                    SELECT DISTINCT recruitment_process_id
                    FROM recruitment_process_node
                    WHERE node_type = 30
            )
            GROUP BY j.id
            HAVING userIds IS NOT NULL AND userIds != ''
            """, nativeQuery = true)
    List<SystemCalendarResult> findNotInterviewJob(String beginDateTime, Long interval, Long tenantId);

    @Query(value = """
            SELECT trp.id talentRecruitmentProcessId, t.id talentId, t.full_name talentName, j.id jobId, j.title jobName, trp.tenant_id tenantId, GROUP_CONCAT(DISTINCT trpku.user_id SEPARATOR ',') AS userIds
            FROM talent_recruitment_process trp
            left join recruitment_process rp on rp.id = trp.recruitment_process_id
            LEFT JOIN talent t ON trp.talent_id = t.id
            LEFT JOIN job j ON trp.job_id = j.id
            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id
            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id
            WHERE
            trp.last_modified_date >= ?1
            AND DATEDIFF(NOW(), trp.last_modified_date) > ?2
            AND trpn.node_status = 1
            AND trpn.node_type = ?3
            AND trpku.user_role in (0,7)
            AND rp.id NOT IN (
                    SELECT recruitment_process_id
                    FROM recruitment_process_node
                    GROUP BY recruitment_process_id
                    HAVING COUNT(*) = 1
            )
            GROUP BY trp.id
            HAVING userIds IS NOT NULL AND userIds != ''
            """, nativeQuery = true)
    List<SystemCalendarResult> findStopApplication(String beginDateTime, Long interval, Integer nodeType);


    @Query(value = """
                SELECT trp.id talentRecruitmentProcessId, t.id talentId, t.full_name talentName, j.id jobId, j.title jobName, trp.tenant_id tenantId, GROUP_CONCAT(DISTINCT trpku.user_id SEPARATOR ',') AS userIds
                FROM talent_recruitment_process trp
                LEFT JOIN talent t ON trp.talent_id = t.id
                LEFT JOIN job j ON trp.job_id = j.id
                LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id
                LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id
                            LEFT JOIN talent_recruitment_process_onboard_date trpod ON trp.id = trpod.talent_recruitment_process_id
                WHERE
                trp.last_modified_date >= ?1
                AND trpn.node_status = 1
                AND trpn.node_type = ?2
                AND trpku.user_role in (0,7)
                AND trpod.onboard_date <= CURRENT_DATE()
                GROUP BY trp.id
                HAVING userIds IS NOT NULL AND userIds != ''
            """, nativeQuery = true)
    List<SystemCalendarResult> findNotOnBoardingApplication(String beginDateTime, Integer nodeType);


    @Query(value = """
		SELECT i.talent_id talentId, i.talent_name talentName, i.job_id jobId, i.job_title jobName, i.tenant_id tenantId, GROUP_CONCAT(DISTINCT trpku.user_id SEPARATOR ',') AS userIds,
		       i.id invoiceId, i.invoice_no invoiceName
        FROM invoice i
        LEFT JOIN start s ON i.start_id = s.id
        LEFT JOIN talent_recruitment_process_kpi_user trpku ON s.talent_recruitment_process_id = trpku.talent_recruitment_process_id
        WHERE
        i.last_modified_date >= ?1
        AND i.`status` not in (0,7) 
        AND trpku.user_role = 0
        AND DATE(i.due_date) <= CURRENT_DATE()
        GROUP BY i.id
        HAVING userIds IS NOT NULL AND userIds != ''
            """, nativeQuery = true)
    List<SystemCalendarResult> findInvoiceUnpaid(String beginDateTime);

    @Query(value = """
            		SELECT ici.talent_id talentId, ici.talent_name talentName, ici.job_id jobId, ici.job_name jobName, iai.tenant_id tenantId, GROUP_CONCAT(DISTINCT trpku.user_id SEPARATOR ',') AS userIds,
            		       iai.id invoiceId, iai.code_number invoiceName
            	FROM invoicing_application_info iai
            	LEFT JOIN invoicing_candidate_info ici ON iai.id = ici.invoice_application_id
            	LEFT JOIN start s ON ici.start_id = s.id
            	LEFT JOIN talent_recruitment_process_kpi_user trpku ON s.talent_recruitment_process_id = trpku.talent_recruitment_process_id
            	WHERE
            	iai.last_modified_date >= ?1
            	AND iai.invoicing_status not in (1,7,8,9)
            	AND trpku.user_role = 0
            	AND DATE(iai.payment_due_date) <= CURRENT_DATE()
            	GROUP BY iai.id
            	HAVING userIds IS NOT NULL AND userIds != ''
            """, nativeQuery = true)
    List<SystemCalendarResult> findInvoicingApplicationInfoUnpaid(String beginDateTime);

    @Query(value = """		
SELECT   tci.talent_id talentId, tci.talent_name talentName, tci.job_id jobId, tci.job_title jobName, tgi.tenant_id tenantId, GROUP_CONCAT(DISTINCT trpku.user_id SEPARATOR ',') AS userIds,
         tgi.id invoiceId, tgi.group_number invoiceName
	FROM t_group_invoice tgi
	LEFT JOIN t_group_invoice_record tgir ON tgir.group_invoice_id = tgi.id
	LEFT JOIN t_contractor_invoice tci on tgir.invoice_id = tci.id
	LEFT JOIN timesheet_talent_assignment tta ON tci.assignment_id = tta.id
	LEFT JOIN start s ON tta.start_id = s.id 
	LEFT JOIN talent_recruitment_process_kpi_user trpku ON s.talent_recruitment_process_id = trpku.talent_recruitment_process_id
	WHERE
	tgi.last_modified_date >= ?1
	AND tgir.`status` = 1
	AND tgi.group_invoice_status not in (3,6)
	AND trpku.user_role = 0
	AND DATE(tgi.invoice_date) <= CURRENT_DATE()
	GROUP BY tgi.id
	HAVING userIds IS NOT NULL AND userIds != ''
            """, nativeQuery = true)
    List<SystemCalendarResult> findTGroupInvoiceUnpaid(String beginDateTime);

}
