package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
public class AuditingUser {

    private String createdBy;

    private Instant createdDate;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    private UserBriefDTO createdUser;

    private UserBriefDTO lastModifiedUser;


}
