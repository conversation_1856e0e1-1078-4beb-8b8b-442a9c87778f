package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "Enum Level Of Experience entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_level_of_experience")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumLevelOfExperience implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "en_display_order")
    private Long enDisplayOrder;

    @Column(name = "cn_display_order")
    private Long cnDisplayOrder;

    @Transient
    private Boolean cnLable = false;
}
