package com.altomni.apn.common.aop.request;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import org.springframework.web.multipart.MultipartFile;

public class DefaultArgsSignGenerator implements ArgsSignGenerator {
    @Override
    public String generateSign(Object[] args) {
        if (ArrayUtil.isEmpty(args)) {
            return "empty";
        }

        StringBuilder sb = new StringBuilder();
        for (Object arg : args) {
            if (arg != null) {
                if (arg instanceof MultipartFile) {
                    MultipartFile file = (MultipartFile) arg;
                    sb.append(file.getOriginalFilename())
                            .append(file.getSize());
                } else {
                    sb.append(JSONUtil.toJsonStr(arg));
                }
            }
        }
        return SecureUtil.md5(sb.toString());
    }
}
