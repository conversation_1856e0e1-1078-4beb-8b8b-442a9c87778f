package com.altomni.apn.common.dto.folder;

import lombok.Data;

import java.util.List;

@Data
public class TeamUserSetRollList {
    private List<Long> userIds;
    private Long currentTeamId;
    private Long teamId;

    public TeamUserSetRollList(List<Long> userIds, Long teamId, Long currentTeamId) {
        this.userIds = userIds;
        this.teamId = teamId;
        this.currentTeamId = currentTeamId;
    }

    public TeamUserSetRollList() {
    }
}
