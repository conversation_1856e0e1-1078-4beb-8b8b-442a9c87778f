package com.altomni.apn.common.repository.crmenums;

import com.altomni.apn.common.domain.crmenums.EnumFollowUpContactType;
import com.altomni.apn.common.domain.crmenums.EnumTeamCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;


@Repository
public interface EnumTeamCategoryRepository extends JpaRepository<EnumTeamCategory, Integer> {

}
