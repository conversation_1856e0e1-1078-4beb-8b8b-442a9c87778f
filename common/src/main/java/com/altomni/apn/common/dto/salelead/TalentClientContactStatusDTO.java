package com.altomni.apn.common.dto.salelead;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
public class TalentClientContactStatusDTO {

    private Long talentId;


    @ApiModelProperty(value = "flag to indicate current talent is client contact")
    private Boolean isClientContact;

    public TalentClientContactStatusDTO(Long talentId, Boolean isClientContact) {
        this.talentId = talentId;
        this.isClientContact = isClientContact;
    }
}
