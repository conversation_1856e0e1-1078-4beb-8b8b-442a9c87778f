package com.altomni.apn.common.service.log.impl;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.constants.StatisticConstants;
import com.altomni.apn.common.interceptor.SecurityObjectLevelInterceptor;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.log.LoggingService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Component
@RefreshScope
public class LoggingServiceImpl implements LoggingService {

    private final Logger log = LoggerFactory.getLogger(LoggingServiceImpl.class);

    @Value("${application.statistic.url}")
    private String statisticUrl;

    @Value("${application.statistic.enabled}")
    private boolean statisticEnabled;

    @Resource
    private HttpService httpService;

    private final Set<String> SKIPPED_API_SUFFIX = Set.of("update-event", "linkedin-stats", "my-notifications",
            "refresh-token", "oauth/token", "actuator", "actuator/info", "actuator/health", "sync-jobs-to-es",
            "sync-talents-to-es", "sync-am-report-send-email", "sync-talents-to-mq", "sync-jobs-to-mq",
            "check-job-mq-message-count", "check-company-progress-note-mq-message-count", "check-talent-mq-message-count",
            "check-company-client-note-mq-message-count", "check-company-mq-message-count", "check-talent-note-mq-message-count",
            "check-user-mq-message-count");

    @Override
    public void logRequest(HttpServletRequest httpServletRequest, Object body) { }

    public void callStatistic(String body) {
        httpService.asyncPost(statisticUrl + StatisticConstants.LOGGING_APN_URL, body);
    }

    public static void fluentPut(JSONObject jsonObject, String key, Object value) {
        if (value != null) {
            jsonObject.fluentPut(key, value);
        }
    }

    @Override
    public void logResponse(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object responseBody) {
        String path = httpServletRequest.getRequestURI();
        if (statisticEnabled) {
            String[] split = path.split("/");
            if (split.length > 0 && SKIPPED_API_SUFFIX.contains(split[split.length-1])){
                return;
            }
            final String internalServicePin = httpServletRequest.getHeader(SecurityObjectLevelInterceptor.APN_INTERNAL_PIN);
            if(StringUtils.isNotBlank(internalServicePin)){
                log.debug("internal call {}", path);
                return;
            }
            Long userId = SecurityUtils.getUserId();
            if (userId < 0){
                return;
            }
            JSONObject result = new JSONObject();
            fluentPut(result, "userId", userId);
            fluentPut(result, "tenantId", SecurityUtils.getTenantId());
            final String userLoginIp = SecurityUtils.getUserLoginIp();
            if (StringUtils.isNotEmpty(userLoginIp)){
                fluentPut(result, "loginIp", userLoginIp);
            }
            final String userTimeZone = SecurityUtils.getUserTimeZone();
            if (StringUtils.isNotEmpty(userTimeZone)){
                fluentPut(result, "loginTimezone", userTimeZone);
            }
            String uniformURI = httpServletResponse.getHeader("URI");
            fluentPut(result, "url", httpServletRequest.getRequestURL());
            fluentPut(result, "method", httpServletRequest.getMethod());
            //fluentPut(result, "uri", StringUtils.isNotEmpty(uniformURI) ? uniformURI.substring(uniformURI.indexOf("/")) : httpServletRequest.getRequestURI());
            fluentPut(result, "uri", StringUtils.isNotEmpty(uniformURI) ? uniformURI : httpServletRequest.getMethod() + httpServletRequest.getRequestURI());
            fluentPut(result, "service", httpServletRequest.getRequestURI().split("/")[1]);

            StringBuilder request = new StringBuilder();
            //request.append("Remote Address=[").append(userLoginIp).append("] ");
            //request.append("Remote Host=[").append(userLoginIp).append("] ");
            //request.append("path=[").append(httpServletRequest.getRequestURI()).append("] ");
            request.append("headers=[").append(buildHeadersMap(httpServletRequest)).append("] ");
            Map<String, String> parameters = buildParametersMap(httpServletRequest);
            if (!parameters.isEmpty()) {
                request.append("parameters=[").append(parameters).append("] ");
            }
            Object requestBody = httpServletRequest.getAttribute("body");
            String reqBodyStr = Objects.isNull(requestBody) ? "" : requestBody.toString();
            int limitBodyLength = 4000;
            if (reqBodyStr.length() > limitBodyLength) {
                reqBodyStr = reqBodyStr.substring(0, limitBodyLength);
            }
            if (StringUtils.isNotEmpty(reqBodyStr)){
                request.append("body=[" + reqBodyStr + "]");
            }

            String respBodyStr = Objects.isNull(responseBody) ? "" : responseBody.toString();
            if (respBodyStr.length() > limitBodyLength){
                respBodyStr = respBodyStr.substring(0, limitBodyLength);
            }

            fluentPut(result, "request", request.toString());
            fluentPut(result, "responseBody", "responseHeaders=[" + buildHeadersMap(httpServletResponse) + "] " + "responseBody=[" + respBodyStr + "] ");
            fluentPut(result, "responseCode", httpServletResponse.getStatus());
            String responseTime = httpServletResponse.getHeader("responseTime");
            if (StringUtils.isNotEmpty(responseTime)){
                fluentPut(result, "responseTime", System.currentTimeMillis() - Long.valueOf(responseTime));
            }
            callStatistic(result.toJSONString());
        }
    }

    private Map<String, String> buildParametersMap(HttpServletRequest httpServletRequest) {
        Map<String, String> resultMap = new HashMap<>();
        Enumeration<String> parameterNames = httpServletRequest.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String key = parameterNames.nextElement();
            String value = httpServletRequest.getParameter(key);
            resultMap.put(key, value);
        }
        return resultMap;
    }

    private Map<String, String> buildHeadersMap(HttpServletRequest request) {
        Map<String, String> map = new HashMap<>();
        Enumeration headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = (String) headerNames.nextElement();
            String value = request.getHeader(key);
            map.put(key, value);
        }
        return map;
    }

    private Map<String, String> buildHeadersMap(HttpServletResponse response) {
        Map<String, String> map = new HashMap<>();
        Collection<String> headerNames = response.getHeaderNames();
        for (String header : headerNames) {
            map.put(header, response.getHeader(header));
        }
        return map;
    }
}
