package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 report-service
 * <AUTHOR>
 */
public enum ReportAPIMultilingualEnum {

    REPORT_CHECKPARAM_PARAMNULL("report_checkParam_paramNull"),

    REPORT_CHECKINLIST_CONDITIONLIMIT("report_checkInList_conditionLimit"),

    REPORTJOB_CHECKPARAM_PARAMNULL("reportJob_checkParam_paramNull"),

    REPORTJOB_CHECKPARAM_JOBNULL("reportJob_checkParam_jobNull"),

    REPORTJOB_CHECKPARAM_ACTIVITYIDNULL("reportJob_checkParam_activityIdNull"),

    REP<PERSON><PERSON>IPELINE_P1PIPELINEANALYTICSBYUSERS_USERROLENULL("reportPipeline_p1PipelineAnalyticsByUsers_userRoleNull"),

    REPORTSALES_CHECKPARAM_JOBTYPENULL("reportSales_checkParam_jobTypeNull"),

    REPOR<PERSON>ALES_CHECKPARAM_APPLICATIONIDSNULL("reportSales_checkParam_applicationIdsNull"),

    REPORTUSERJOB_FINDALLJOBBYIDS_JOBIDNULL("reportUserJob_findAllJobByIds_jobIdNull"),

    REPORTUSERJOB_FINDALLJOBBYIDS_TALENTIDSNULL("reportUserJob_findAllJobByIds_talentIdsNull"),

    REPORT_TIMESHEET_CHECKPARAM_TYPENULL("report_timesheet_checkParam_typeNull"),

    REPORT_COUNTALLDORMANTAPPLICATIONS_ILLEGALVALUE("report_countAllDormantApplications_IllegalValue"),

    REPORTUSERJOB_PARAM_EXCEPTION("reportUserJob_param_exception"),

    REPORT_DOWNLOAD_DATA_MAX_SIZE("report_download_data_max_size"),
    ;

    private final String key;

    ReportAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}