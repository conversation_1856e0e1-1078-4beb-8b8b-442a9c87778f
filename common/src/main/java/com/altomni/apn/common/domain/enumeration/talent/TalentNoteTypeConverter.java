package com.altomni.apn.common.domain.enumeration.talent;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TalentNoteTypeConverter extends AbstractAttributeConverter<TalentNoteType, Integer> {
    public TalentNoteTypeConverter() {
        super(TalentNoteType::toDbValue, TalentNoteType::fromDbValue);
    }
}
