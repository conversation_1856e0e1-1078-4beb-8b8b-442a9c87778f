package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum CompanyType implements ConvertedEnum<Integer> {
    CLIENT(0),
    POTENTIAL_CLIENT(1),
    MIXED(2);

    private final int dbValue;

    CompanyType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CompanyType, Integer> resolver = new ReverseEnumResolver<>(CompanyType.class, CompanyType::toDbValue);

    public static CompanyType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
