package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.application.SalaryType;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;

public class TalentRecruitmentProcessOfferSalaryPackageVO implements Serializable {

    private static final long serialVersionUID = 8710619910850374801L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private SalaryType salaryType;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal amount;

    private Boolean needCharge;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentRecruitmentProcessId() {
        return talentRecruitmentProcessId;
    }

    public void setTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
    }

    public SalaryType getSalaryType() {
        return salaryType;
    }

    public void setSalaryType(SalaryType salaryType) {
        this.salaryType = salaryType;
    }

    public BigDecimal getAmount() {
        return amount == null ? null : new BigDecimal(Constants.DECIMAL_FORMAT.format(amount));
    }

    public void setAmount(BigDecimal amount) {
        if (amount != null) {
            this.amount = amount;
        } else {
            this.amount = BigDecimal.ZERO;
        }
    }

    public Boolean getNeedCharge() {
        return needCharge;
    }

    public void setNeedCharge(Boolean needCharge) {
        this.needCharge = needCharge;
    }


    @Override
    public String toString() {
        return "TalentRecruitmentProcessOfferSalaryPackageVO{" +
                "id=" + id +
                ", talentRecruitmentProcessId=" + talentRecruitmentProcessId +
                ", salaryType=" + salaryType +
                ", amount=" + amount +
                ", needCharge=" + needCharge +
                '}';
    }
}
