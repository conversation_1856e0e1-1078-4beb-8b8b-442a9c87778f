package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class GroupInvoiceStatusConverter extends AbstractAttributeConverter<GroupInvoiceStatus,Integer> {

    public GroupInvoiceStatusConverter(){
        super(GroupInvoiceStatus::toDbValue,GroupInvoiceStatus::fromDbValue);
    }
}
