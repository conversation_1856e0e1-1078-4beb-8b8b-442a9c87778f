package com.altomni.apn.common.dto.company;

import com.altomni.apn.common.domain.enumeration.ContactCategoryType;
import com.altomni.apn.common.domain.enumeration.ContactCategoryTypeConverter;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.UniqueElements;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
*
* <AUTHOR>
* date:2023-04-21
*/
@Data
@NoArgsConstructor
@ApiModel(description = "dto for company contact")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SalesLeadClientContactDTO implements Serializable {

    @ApiModelProperty(value = "id for contact")
    private Long id;

    @ApiModelProperty(value = "id for company")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "firstName for contact")
    @NotEmpty
    private String firstName;

    @ApiModelProperty(value = "lastName for contact")
    @NotEmpty
    private String lastName;

    @ApiModelProperty(value = "name for contact")
    private String name;

    @ApiModelProperty(value = "lastFollowUpTime for contact")
    private Instant lastFollowUpTime;

    @ApiModelProperty(value = "type for contact")
    @NotNull
    private Integer contactCategory;

    @ApiModelProperty(value = "title for the contact")
    @NotEmpty
    private String title;

    @ApiModelProperty(value = "department for the contact")
    private String department;

    @ApiModelProperty(value = "Contact status (active/inactive)")
    private Boolean active;

    @ApiModelProperty(value = "contact list")
    @NotEmpty
    @UniqueElements
    private List<TalentContactDTO> contacts;

    @ApiModelProperty(value = "id for the company location")
    private Long companyLocationId;

    @ApiModelProperty(value = "note for the contact")
    private String note;

    @ApiModelProperty(value = "zipcode for the contact, custom version exclusive and required")
    private String zipcode;

    @ApiModelProperty(value = "businessUnit for the contact, custom version exclusive")
    private String businessUnit;

    @ApiModelProperty(value = "businessGroup for the contact, custom version exclusive")
    private String businessGroup;

    @ApiModelProperty(value = "linkedinProfile for the contact, custom version exclusive")
    private List<String> linkedinProfile;

    @ApiModelProperty(value = "esId for the contact.")
    private String esId;

    @ApiModelProperty(value = "only use for common pool")
    private Long creditTransactionId;

    @ApiModelProperty(value = "linkedinProfile for the contact, custom version exclusive")
    @UniqueElements
    private List<Long> tags;

    @ApiModelProperty(value = "talentId for contact")
    private Long talentId;

    public static Set<String> additionalInfoSkipProperties = new HashSet<>(Arrays.asList("id", "companyId", "firstName", "lastName", "name", "lastFollowUpTime", "contactCategory",
            "title", "department", "active", "email", "phone", "wechat", "companyLocationId", "zipcode", "linkedinProfile", "esId", "tags"));

}
