package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum AssignmentCategoryType implements ConvertedEnum<Integer> {

    CONTRACTOR(0, "Contractor"),
    PAYROLLING(1, "Payrolling"),
    C2C_CONTRACTOR(2, "C2C - Contractor"),
    C2C_PAYROLLING(3, "C2C - Payrolling"),
    MSP(4, "MSP"),
    C2C_MSP(5, "C2C - MSP");

    private final int dbValue;

    private final String description;

    AssignmentCategoryType(int dbValue,String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<AssignmentCategoryType, Integer> resolver = new ReverseEnumResolver<>(AssignmentCategoryType.class, AssignmentCategoryType::toDbValue);

    public static AssignmentCategoryType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
