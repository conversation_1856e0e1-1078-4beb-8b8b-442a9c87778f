package com.altomni.apn.common.domain.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum RelateJobFolderUserRole  implements ConvertedEnum<Integer> {
    OWNER(0), SHARER(1);

    private final int dbValue;

    RelateJobFolderUserRole(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<RelateJobFolderUserRole, Integer> resolver =
            new ReverseEnumResolver<>(RelateJobFolderUserRole.class, RelateJobFolderUserRole::toDbValue);

    public static RelateJobFolderUserRole fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
