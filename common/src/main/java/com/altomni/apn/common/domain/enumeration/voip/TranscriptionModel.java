package com.altomni.apn.common.domain.enumeration.voip;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TranscriptionSource enumeration.
 */
public enum TranscriptionModel implements ConvertedEnum<Integer> {

    MANDARIN_ENGLISH_MIX(0),
    ENGLISH(1);

    private final int dbValue;

    TranscriptionModel(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TranscriptionModel, Integer> resolver =
        new ReverseEnumResolver<>(TranscriptionModel.class, TranscriptionModel::toDbValue);

    public static TranscriptionModel fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
