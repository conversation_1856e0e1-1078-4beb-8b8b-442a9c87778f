package com.altomni.apn.common.aop.cache;

import com.altomni.apn.common.enumeration.KeyType;
import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheClean {

    /**
     * 指定要清理的缓存键类型
     */
    KeyType[] keyTypes() default {};

    /**
     * 是否异步清理
     */
    boolean async() default true;
}