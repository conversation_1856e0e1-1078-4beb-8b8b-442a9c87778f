package com.altomni.apn.common.domain.enumeration.talent;

public enum TalentDeclassifyType {

    // 用户手动解除
    PROACTIVELY("proactively", false),
    // 保密到期系统自动解除
    MATURITY("maturity", true),
    // 流程自动解除
    DECLASSIFY_PROCESS("declassifyProcess", true),
    // 候选人简历不匹配解除
    RESUME_NOMATCH("resumeNoMatch", true),
    // 规则删除解除
    RULE_DELETE("ruleDelete", true);


    private final String value;
    private final boolean system;

    TalentDeclassifyType(String value, boolean system) {
        this.value = value;
        this.system = system;
    }

    public String getValue() {
        return value;
    }

    public boolean isSystem() {
        return system;
    }
}
