package com.altomni.apn.common.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import javax.persistence.Column;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DtoToJsonUtil {

    /**
     * 将 DTO 对象转为 JSON，使用数据库字段名作为 JSON 的键名
     *
     * @param dto DTO 对象
     * @return JSON 字符串
     */
    public static JSONObject toJsonWithColumnNames(Object dto) {
        JSONObject jsonObject = new JSONObject();
        Class<?> clazz = dto.getClass();

        // 遍历所有字段
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true); // 允许访问私有字段
            try {
                // 获取字段上的 @Column 注解
                Column column = field.getAnnotation(Column.class);
                String fieldName = (column != null && !column.name().isEmpty()) ? column.name() : field.getName();
                Object value = field.get(dto);

                // 将字段名和值放入 JSONObject
                if (value != null) {
                    jsonObject.put(fieldName, value);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to access field: " + field.getName(), e);
            }
        }

        return jsonObject;
    }

    /**
     * 将 DTO 对象列表转为 JSONArray，使用数据库字段名作为 JSON 的键名
     *
     * @param dtoCollection DTO 对象列表
     * @return JSONArray
     */
    public static JSONArray toJsonArrayWithColumnNames(Collection<?> dtoCollection) {
        JSONArray jsonArray = new JSONArray();
        if (dtoCollection == null || dtoCollection.isEmpty()) {
            return jsonArray;
        }

        // 遍历集合中的每个对象
        for (Object dto : dtoCollection) {
            // 将每个对象转为 JSONObject
            JSONObject jsonObject = toJsonWithColumnNames(dto);
            // 将 JSONObject 添加到 JSONArray
            jsonArray.add(jsonObject);
        }

        return jsonArray;
    }

}
