package com.altomni.apn.common.dto.linkedin;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LinkedinUsageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer usersWithSeat;

    private Integer usersWithSeatNoAPN;

    private Integer usersWithSeatLowAPN;

    private Integer apnViews;

    private Integer lnkdViews;

    private Integer numMessagesSent;

    private Integer numMessagesAccepted;

}
