package com.altomni.apn.common.domain.dict.mapping;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@ApiModel(description = " job functions mapping relation entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "mapping_job_function")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MappingJobFunction implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "job_function_id")
    private Long jobFunctionId;

    @Column(name = "mapping_id")
    private Long mappingId;

}
