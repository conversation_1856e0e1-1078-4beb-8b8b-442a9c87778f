package com.altomni.apn.common.domain.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_delivery_country_relation")
public class UserDeliveryCountryRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "official_country")
    private String officialCountry;

    @Column(name = "enum_country_id")
    private Integer enumCountryId;

    @Column(name = "count")
    private Long count;

    @Column(name = "top")
    private Boolean top;

    @Column(name = "updated")
    private Boolean updated;

    public UserDeliveryCountryRelation(Long id, Long userId, String officialCountry, Integer enumCountryId, Long count, Boolean top) {
        this.id = id;
        this.userId = userId;
        this.officialCountry = officialCountry;
        this.enumCountryId = enumCountryId;
        this.count = count;
        this.top = top;
    }
}
