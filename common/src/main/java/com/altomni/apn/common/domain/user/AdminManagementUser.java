package com.altomni.apn.common.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ipg.resourceserver.user.SsoOidcUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * A admin_management_user.
 */
@ApiModel(description = "User for management")
@Entity
@Data
@Table(name = "admin_management_user")
public class AdminManagementUser extends AbstractAuditingEntity implements UserSecurityInterface, Serializable, SsoOidcUser {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "[Internal] uid is used for Spring security to identify user internally. It has the format of <userId>,<tenantId>")
    @NotNull
    @Column(name = "uid", unique = true)
    private String uid;

    @ApiModelProperty(value = "Optional firstName.")
    @NotNull
    @Column(name = "first_name")
    private String firstName;

    @ApiModelProperty(value = "Optional lastName.")
    @NotNull
    @Column(name = "last_name")
    private String lastName;

    @ApiModelProperty(value = "Optional username. If exists, need to be unique.")
    @NotNull
    @Column(name = "username")
    private String username;

    @ApiModelProperty(value = "Optional username. If exists, need to be unique.")
    @JsonIgnore
    @NotNull
    @Column(name = "password")
    private String password;

    @ApiModelProperty(value = "email address. need to be unique.")
    @NotNull
    @Column(name = "email")
    private String email;

    @ApiModelProperty(value = "Whether user is activated. Default is true. Read Only.")
    @NotNull
    @Column(name = "activated")
    private boolean activated;

    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE}, fetch = FetchType.LAZY)
    @JoinTable(
            name = "admin_management_user_role",
            joinColumns = {@JoinColumn(name = "user_id", referencedColumnName = "id")},
            inverseJoinColumns = {@JoinColumn(name = "role_id", referencedColumnName = "id")})
    private Set<AdminManagementRole> managementRoles = new HashSet<>();

    @Override
    public Set<Role> getRoles() {
        return this.getManagementRoles().stream().map(o -> {
            Role role = new Role();
            role.setName(o.getName());
            return role;
        }).collect(Collectors.toSet());
    }

    @Transient
    private Long tenantId;

    @Transient
    private Long teamId;

    @Transient
    private OidcUser oidcUser;

    @Override
    public Optional<OidcUser> getOriginalOidcUser() {
        return Optional.ofNullable(oidcUser);
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        if (getRoles() == null) {
            return Collections.emptyList();
        }
        return getRoles().stream().map(role -> new SimpleGrantedAuthority(role.getName())).toList();
    }
}
