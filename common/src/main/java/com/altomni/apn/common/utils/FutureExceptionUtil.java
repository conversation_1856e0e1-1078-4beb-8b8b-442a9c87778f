package com.altomni.apn.common.utils;

import com.altomni.apn.common.errors.*;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class FutureExceptionUtil {

    public static Void handleFutureException(Throwable e) {
        log.error("Exception when get data use CompletableFuture with error {}", e.getLocalizedMessage(), e);
        Throwable cause = e.getCause();
        if (cause == null) {
            throw new CustomParameterizedException(e.getMessage());
        }

        if (cause instanceof CustomParameterizedException customParameterizedException) {
            throw customParameterizedException;
        } else if (cause instanceof BadRequestAlertException badRequestAlertException) {
            throw badRequestAlertException;
        } else if (cause instanceof ForbiddenException forbiddenException) {
            throw forbiddenException;
        } else if (cause instanceof NoPermissionException noPermissionException) {
            throw noPermissionException;
        } else if (cause instanceof NotFoundException notFoundException) {
            throw notFoundException;
        } else {
            throw new CustomParameterizedException(cause.getMessage());
        }
    }
}
