package com.altomni.apn.common.utils;

import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.time.Instant;
import java.time.LocalDate;

public class PdfUtil {

    private final Logger log = LoggerFactory.getLogger(PdfUtil.class);

    private static final String ROUTING_NO = "Routing #: *********";

    private static final String ACCOUNT_NO = "Account #: **********";

    private static final String ACCOUNT_NAME = "Account name: Intellipro Group Inc.";

    private static final String BANK_ADDRESS_1 = "Bank Address: 2900 S. EI Camino Real,";

    private static final String BANK_ADDRESS_2 = "                        San Mateo CA 94403";

    private static final String ACCOUNT_TYPE = "Account Type: Checking";

    private static final String MAILING_ADDRESS_1 = "Mailing Address: P.O. BOX 4364";

    private static final String MAILING_ADDRESS_2 = "                           4601 LAFAYETTE ST";

    private static final String MAILING_ADDRESS_3 = "                           SANTA CLARA";

    private static final String MAILING_ADDRESS_4 = "                           CA 95056";

    private static final String NOTE = "Payment due within 30 days";

    public void createInvoicePdf(Document document, String subInvoiceNo, String customerName, String customerAddress,
                                 String clientContactName, Instant createdDate, String talentName, LocalDate startDate,
                                 BigDecimal dueAmount, BigDecimal totalAmount) throws DocumentException {
        document.newPage();
        PdfPTable table = new PdfPTable(7);
        table.setWidthPercentage(85);
        table.setSpacingBefore(5f);
        table.setSpacingAfter(5f);

        Font bold10Font = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 10, BaseColor.WHITE);
        Font bold11Font = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 11, BaseColor.BLACK);
        Font regular10Font = FontFactory.getFont(FontFactory.HELVETICA, 10, BaseColor.BLACK);
        Font bold13Font = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 13, BaseColor.BLACK);

        // logo
        Image img = readImage();
        PdfPCell cell;
        if (img != null) {
            cell = new PdfPCell(img, true);
        } else {
            cell = new PdfPCell(new Phrase("Intellipro Group"));
        }
        cell.setColspan(2);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // invoice before space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Invoice No
        cell = new PdfPCell(new Phrase("Invoice No." + subInvoiceNo, bold13Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // invoice after space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // invoice after space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // invoice after space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Name and Address
        cell = new PdfPCell(new Phrase("Customer Name and Address", bold11Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // between Customer and Remit space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Remit To
        cell = new PdfPCell(new Phrase("Remit To", bold11Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Name
        cell = new PdfPCell(new Phrase(customerName, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // between Customer Came and Please space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Please make payment via direct deposit to:
        cell = new PdfPCell(new Phrase("Please make payment via direct deposit to:", regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Address
        cell = new PdfPCell(new Phrase(customerAddress, regular10Font));
        cell.setColspan(3);
        cell.setRowspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // between Customer Address and Bank space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Bank of America
        cell = new PdfPCell(new Phrase("Bank of America", regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // between Customer Address and Routing space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Routing #
        cell = new PdfPCell(new Phrase(ROUTING_NO, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // between Customer Address and Account # space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Account #
        cell = new PdfPCell(new Phrase(ACCOUNT_NO, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // before Account name space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Account name
        cell = new PdfPCell(new Phrase(ACCOUNT_NAME, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Reference # title
        cell = new PdfPCell(new Phrase("Customer Reference #: ", bold11Font));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Reference # value
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Bank Address before part
        cell = new PdfPCell(new Phrase(BANK_ADDRESS_1, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // PO # title
        cell = new PdfPCell(new Phrase("PO#: ", bold11Font));
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        cell.setColspan(2);
        table.addCell(cell);

        // PO # value
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Bank Address after part
        cell = new PdfPCell(new Phrase(BANK_ADDRESS_2, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Contact title
        cell = new PdfPCell(new Phrase("Customer Contact: ", bold11Font));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Contact value
        cell = new PdfPCell(new Phrase(clientContactName, regular10Font));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Account Type
        cell = new PdfPCell(new Phrase(ACCOUNT_TYPE, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Invoice Date title
        cell = new PdfPCell(new Phrase("Invoice Date: ", bold11Font));
        cell.setRowspan(4);
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Invoice Date value
        cell = new PdfPCell(new Phrase(DateUtil.instantToStringUSFormat(createdDate), regular10Font));
        cell.setColspan(2);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Mailing Address
        cell = new PdfPCell(new Phrase(MAILING_ADDRESS_1, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Mailing Address
        cell = new PdfPCell(new Phrase(MAILING_ADDRESS_2, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Mailing Address
        cell = new PdfPCell(new Phrase(MAILING_ADDRESS_3, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Mailing Address
        cell = new PdfPCell(new Phrase(MAILING_ADDRESS_4, regular10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        // before Item Description space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        //Item Description title
        cell = new PdfPCell(new Phrase("Item", bold10Font));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Quantity title
        cell = new PdfPCell(new Phrase("Quantity", bold10Font));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Billing Rate title
        cell = new PdfPCell(new Phrase("Billing Rate", bold10Font));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Unit title
        cell = new PdfPCell(new Phrase("Unit", bold10Font));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Amount Due title
        cell = new PdfPCell(new Phrase("Amount Due", bold10Font));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // before Item Description value space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        //Item Description value
        talentName = "Placement fee for " + talentName;
        String startDateStr =  ". \n\nStart Date: " + DateUtil.generateDateString(startDate);
        cell = new PdfPCell(new Phrase(talentName + startDateStr, regular10Font));
        cell.setColspan(3);
        cell.setRowspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Quantity value
        cell = new PdfPCell(new Phrase(" ", regular10Font));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setRowspan(3);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Billing Rate value
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setRowspan(3);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Unit value
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setRowspan(3);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Amount Due value
        cell = new PdfPCell(new Phrase("$" + dueAmount, regular10Font));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setRowspan(3);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        // before subtotal above space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.BOTTOM);
        table.addCell(cell);

        // before subtotal above space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.TOP);
        table.addCell(cell);

        // before subtotal space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Subtotal
        cell = new PdfPCell(new Phrase("Subtotal: ", regular10Font));
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setRowspan(2);
        cell.setColspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Subtotal value
        cell = new PdfPCell(new Phrase("$" + dueAmount, regular10Font));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // after subtotal down space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(4);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // after subtotal down space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(3);
        cell.setBorder(PdfPCell.BOTTOM);
        table.addCell(cell);

        // after subtotal down space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(4);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // after subtotal down space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(3);
        cell.setBorder(PdfPCell.TOP);
        table.addCell(cell);

        // before total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Total Amount
        cell = new PdfPCell(new Phrase("Total Amount: ", bold11Font));
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setColspan(2);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Total Amount value
        cell = new PdfPCell(new Phrase("$" + totalAmount, bold11Font));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // after total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(4);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // after total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(3);
        cell.setBorder(PdfPCell.BOTTOM);
        table.addCell(cell);

        // after total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(4);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // after total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(3);
        cell.setBorder(PdfPCell.TOP);
        table.addCell(cell);

        // after total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.BOTTOM);
        table.addCell(cell);

        // Payment Due within 30 days
        cell = new PdfPCell(new Phrase(NOTE, regular10Font));
        cell.setColspan(7);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.TOP);
        table.addCell(cell);

        document.add(table);
    }

    private Image readImage() {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        String logoPath = "images/logo.png";
        String logoPathSlash = "/images/logo.png";
        Image image = null;
        try {
            File folderInput = new File(logoPath);
            log.debug("[PdfUtil.readImage][{}] read by File: {}", logoPath, folderInput.exists());
            if (folderInput.exists()) {
                BufferedImage folderImage = ImageIO.read(folderInput);
                ImageIO.write(folderImage, "png", out);
                byte[] byteArray = out.toByteArray();
                image =  Image.getInstance(byteArray);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by File error.", logoPath);
        }

        try {
            ClassPathResource imgFile = new ClassPathResource(logoPath);
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource: ", logoPath, imgFile.exists());
            if (imgFile.exists()) {
                byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
                image = Image.getInstance(bytes);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource error.", logoPath);
        }

        try {
            URL url = PdfUtil.class.getClassLoader().getResource(logoPath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource(): {}", logoPath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() not found", logoPath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() error.", logoPath);
        }

        try {
            URL url = this.getClass().getResource(logoPath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource(): {}", logoPath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() not found", logoPath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() error.", logoPath);
        }

        try {
            URL url = Thread.currentThread().getContextClassLoader().getResource(logoPath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource(): {}", logoPath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() not found", logoPath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() error.", logoPath);
        }

        try {
            URL url = ClassLoader.getSystemClassLoader().getResource(logoPath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource(): {}", logoPath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() not found", logoPath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() error.", logoPath);
        }

        // ********************************logoPathSlash************************************/

        try {
            File folderInput = new File(logoPathSlash);
            log.debug("[PdfUtil.readImage][{}] read by File: {}", logoPathSlash, folderInput.exists());
            if (folderInput.exists()) {
                BufferedImage folderImage = ImageIO.read(folderInput);
                ImageIO.write(folderImage, "png", out);
                byte[] byteArray = out.toByteArray();
                image = Image.getInstance(byteArray);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by File error.", logoPathSlash);
        }

        try {
            ClassPathResource imgFile = new ClassPathResource(logoPathSlash);
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource: ", logoPathSlash, imgFile.exists());
            if (imgFile.exists()) {
                byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
                image = Image.getInstance(bytes);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource error.", logoPathSlash);
        }

        try {
            URL url = PdfUtil.class.getClassLoader().getResource(logoPathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource(): {}", logoPathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() not found", logoPathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() error.", logoPathSlash);
        }

        try {
            URL url = this.getClass().getResource(logoPathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource(): {}", logoPathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() not found", logoPathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() error.", logoPathSlash);
        }

        try {
            URL url = Thread.currentThread().getContextClassLoader().getResource(logoPathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource(): {}", logoPathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() not found", logoPathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() error.", logoPathSlash);
        }

        try {
            URL url = ClassLoader.getSystemClassLoader().getResource(logoPathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource(): {}", logoPathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() not found", logoPathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() error.", logoPathSlash);
        }


        try {
            out.close();
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage] close ByteArrayOutputStream error.");
        }

        return image;
    }

    public static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len = 0;
        while ((len = inStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, len);
        }
        inStream.close();
        return outStream.toByteArray();
    }
}
