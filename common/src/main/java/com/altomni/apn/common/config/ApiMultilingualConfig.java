package com.altomni.apn.common.config;

import com.altomni.apn.common.dto.TitleInfoDTO;

import java.util.List;
/**
 * <AUTHOR>
 */
public abstract class ApiMultilingualConfig {

    public abstract TitleInfoDTO getTitleInfoByKey(String key);

    public abstract String getTitleInfoByKeyAndLanguage(String key,String lang);
    
    public abstract String getTitleInfoByKeyAndLanguage(String key, String lang, List<Object> params);
}
