package com.altomni.apn.common.errors;

import lombok.Data;

@Data
public class ExternalInterfaceException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private Integer code;

    private String message;

    public ExternalInterfaceException(String message, Integer code) {
        super(message);
        this.message = message;
        this.code = code;
    }

}
