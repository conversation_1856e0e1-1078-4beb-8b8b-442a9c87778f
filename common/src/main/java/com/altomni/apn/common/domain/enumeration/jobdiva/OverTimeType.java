package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum OverTimeType implements ConvertedEnum<Integer> {
    MANUALLY(0),
    AUTO(1);

    private final int dbValue;

    OverTimeType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<OverTimeType, Integer> resolver = new ReverseEnumResolver<>(OverTimeType.class, OverTimeType::toDbValue);

    public static OverTimeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
