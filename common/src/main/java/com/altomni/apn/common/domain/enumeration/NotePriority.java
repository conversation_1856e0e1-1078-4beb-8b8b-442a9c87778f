package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobPriority enumeration.
 */
public enum NotePriority implements ConvertedEnum<Integer> {
    NORMAL(0), HIGH(1);

    private final int dbValue;

    NotePriority(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<NotePriority, Integer> resolver =
        new ReverseEnumResolver<>(NotePriority.class, NotePriority::toDbValue);

    public static NotePriority fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
