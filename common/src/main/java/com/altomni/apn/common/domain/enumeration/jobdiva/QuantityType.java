package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * quantity type @link invoiceTimesheetInfo
 */
public enum QuantityType implements ConvertedEnum<Integer> {
    RT(1, "RT"),
    OT(2, "OT"),
    DT(3,"DT"),
    HT(4,"HT"),
    ADJUSTMENT(5,"ADJUSTMENT");
    private final int dbValue;

    private final String description;

    QuantityType(int dbValue,String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<QuantityType, Integer> resolver = new ReverseEnumResolver<>(QuantityType.class, QuantityType::toDbValue);

    public static QuantityType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
