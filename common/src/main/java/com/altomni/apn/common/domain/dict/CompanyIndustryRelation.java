package com.altomni.apn.common.domain.dict;

import com.altomni.apn.common.domain.ManualAbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@ApiModel
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "company_industry_relation")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CompanyIndustryRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "company_id")
    private Long accountCompanyId;

    @Column(name = "industry_id")
    private Integer enumId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompanyIndustryRelation that = (CompanyIndustryRelation) o;
        return accountCompanyId.equals(that.accountCompanyId) && enumId.equals(that.enumId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(accountCompanyId, enumId);
    }
}
