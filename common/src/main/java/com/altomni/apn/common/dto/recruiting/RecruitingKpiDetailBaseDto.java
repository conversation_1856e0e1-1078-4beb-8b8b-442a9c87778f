package com.altomni.apn.common.dto.recruiting;

import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiDetailBaseDto extends RecruitingKpiReportSearchDto {

    private SortType type;

    private SearchSortDTO sort;

    // 用于详情查询
    private Long jobId;

    private Long companyId;

    private Long teamId;

    private Long userId;

    private String timezone;

    private String downloadExcelTimezone;

    private String startDate;

    private String endDate;

    public String getStartDateUtc() {
        return getUtcByTimeZone(startDate + " 00:00:00");
    }

    public String getEndDateUtc() {
        return getUtcByTimeZone(endDate + " 23:59:59");
    }

    public String getUtcByTimeZone(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        return dateTime.atZone(ZoneId.of(timezone)).withZoneSameInstant(ZoneOffset.UTC).toString();
    }

    public static LocalDate parseDate(String dateStr, RecruitingKpiGroupByFieldType groupByDate) {
        switch (groupByDate) {
            case DAY, WEEK, MONTH:
                return LocalDate.parse(dateStr);
            case QUARTER:
                String[] parts = dateStr.split("-");
                int year = Integer.parseInt(parts[0]);
                int quarter = Integer.parseInt(parts[1]);
                int month = (quarter - 1) * 3 + 1; // 计算季度的开始月份
                return LocalDate.of(year, month, 1);
            case YEAR:
                return Year.parse(dateStr).atDay(1);
            default:
                throw new IllegalArgumentException("Invalid groupByDate: " + groupByDate);
        }
    }

    public static LocalDate getStartDateFormat(String dateStr, RecruitingKpiGroupByFieldType groupByDate) {
        LocalDate date = parseDate(dateStr, groupByDate);
        return switch (groupByDate) {
            case DAY -> date;
            case WEEK -> date.with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
            case MONTH -> date.with(TemporalAdjusters.firstDayOfMonth());
            case QUARTER -> date; // 解析时已经是季度的开始日期
            case YEAR -> date.with(TemporalAdjusters.firstDayOfYear());
            default -> throw new IllegalArgumentException("Invalid groupByDate: " + groupByDate);
        };
    }

    public static LocalDate getEndDateFormat(String dateStr, RecruitingKpiGroupByFieldType groupByDate) {
        LocalDate date = parseDate(dateStr, groupByDate);
        return switch (groupByDate) {
            case DAY -> date;
            case WEEK -> date.with(TemporalAdjusters.nextOrSame(java.time.DayOfWeek.SUNDAY));
            case MONTH -> date.with(TemporalAdjusters.lastDayOfMonth());
            case QUARTER -> date.plusMonths(2).with(TemporalAdjusters.lastDayOfMonth());
            case YEAR -> date.with(TemporalAdjusters.lastDayOfYear());
            default -> throw new IllegalArgumentException("Invalid groupByDate: " + groupByDate);
        };
    }

}
