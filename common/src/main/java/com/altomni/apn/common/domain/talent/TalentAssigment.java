package com.altomni.apn.common.domain.talent;

import com.altomni.apn.common.domain.AutoAbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * A record
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Getter
@Setter
@Table(name = "timesheet_talent_assignment")
public class TalentAssigment extends AutoAbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @Column(name = "talent_id", nullable = false)
    @JsonIgnore
    private Long talentId;

    @Column(name = "tenant_id", nullable = false)
    @JsonIgnore
    private Long tenantId;

    @Column(name = "talent_recruitment_process_id", nullable = false)
    private Long talentRecruitmentProcessId;

    @Column(name = "start_id", nullable = false)
    @JsonIgnore
    private Long startId;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;


    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "created_user_id")
    private Long createdUserId;

    @Column(name = "type")
    @Convert(converter = AssignmentTypeConverter.class )
    private AssignmentType type;

    @Column(name = "status")
    @Convert(converter = AssignmentStatusTypeConverter.class )
    private AssignmentStatusType status;

    @Column(name = "calculate_method")
    @Convert(converter = CalculateMethodTypeConverter.class)
    private CalculateMethodType method;

    @Column(name = "working_hours")
    private Float workingHours;

    /**
     * 用于兼容历史数据字段,timesheet 历史数据不做切割，全部按照完整周处理
     * 0:历史数据
     * 1:新数据
     */
    @Column(name = "is_week_end")
    private Integer isWeekEnd;


}
