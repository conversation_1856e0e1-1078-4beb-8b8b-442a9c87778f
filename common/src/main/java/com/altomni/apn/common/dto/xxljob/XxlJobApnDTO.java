package com.altomni.apn.common.dto.xxljob;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnumConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class XxlJobApnDTO {
    // 主键ID
    private int id;
    // 任务描述
    @NotBlank(message = "jobDesc is required")
    private String jobDesc;
    // 执行器，任务Handler名称
    private String executorHandler;
    // 执行器，任务参数
    private Map<String, Object> executorParam;
    // 执行时间
    private Instant triggerTime;

    private XxlJobApnParamDTO xxlJobApnParamDTO;

    private Long userId;

    private Long tenantId;

    private Long referenceId;

    private String cron;

    private String onlyId;

    public void setXxlJobApnParamDTO(XxlJobApnParamDTO xxlJobApnParamDTO) {
        this.xxlJobApnParamDTO = xxlJobApnParamDTO;
        this.userId = xxlJobApnParamDTO.getUserId();
        this.tenantId = xxlJobApnParamDTO.getTenantId();
        this.referenceId = xxlJobApnParamDTO.getReferenceId();
        this.executorHandler = xxlJobApnParamDTO.getXxlJobType().getXxlJobHandler();
        this.triggerTime = xxlJobApnParamDTO.getSendTime();
        this.cron = xxlJobApnParamDTO.getCron();
        this.executorParam = new HashMap<>(){{
            put("xxlJobType", xxlJobApnParamDTO.getXxlJobType());
        }};
    }

    public String getOnlyId() {
        return getReferenceId() + "-" + getUserId();
    }
}
