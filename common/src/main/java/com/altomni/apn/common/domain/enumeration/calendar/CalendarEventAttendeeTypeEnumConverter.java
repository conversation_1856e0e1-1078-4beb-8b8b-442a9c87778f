package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CalendarEventAttendeeTypeEnumConverter extends AbstractAttributeConverter<CalendarEventAttendeeTypeEnum, Integer> {
    public CalendarEventAttendeeTypeEnumConverter() {
        super(CalendarEventAttendeeTypeEnum::toDbValue, CalendarEventAttendeeTypeEnum::fromDbValue);
    }
}