package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessEliminateVO extends AuditingUser implements Serializable {

    private static final long serialVersionUID = 8250643155536732563L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private EliminateReason eliminateReason;

    private String note;

    private Instant noteLastModifiedDate;

    private Long noteLastModifiedByUserId;
}
