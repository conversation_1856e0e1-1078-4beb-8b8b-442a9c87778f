package com.altomni.apn.common.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class JobdivaRabbitProperties {

    @Value("${spring.rabbitmq.addresses}")
    private String host;

    @Value("${spring.rabbitmq.port}")
    private int port;

    @Value("${spring.rabbitmq.username}")
    private String userName;

    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;

    @Value("${spring.rabbitmq.password}")
    private String password;

    @Value("${application.jobdiva.apn-hr-exchange}")
    private String apnToJobdivaExchange;

    @Value("${application.jobdiva.apn-to-hr-queue}")
    private String apnToJobdivaQueue;

    @Value("${application.jobdiva.apn-to-hr-routingKey}")
    private String apnToJobdivaRoutingKey;

    @Value("${application.jobdiva.hr-to-apn-queue}")
    private String jobdivaToApnQueue;

}
