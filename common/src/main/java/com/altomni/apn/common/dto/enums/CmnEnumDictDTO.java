package com.altomni.apn.common.dto.enums;

import com.altomni.apn.common.domain.dict.EnumFrontDisplay;
import com.altomni.apn.common.domain.dict.EnumLevelOfExperience;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CmnEnumDictDTO {
    @ApiModelProperty(value = "The id for target enum")
    private int id;

    @ApiModelProperty(value = "The name for target enum")
    private String name;

    @ApiModelProperty(value = "The display label for target enum")
    private String label;

    @ApiModelProperty(value = "The display sort for target enum")
    private int sort;

    public static CmnEnumDictDTO fromBizDict(EnumFrontDisplay frontDisplay, SortType sortType) {
        CmnEnumDictDTO dto = new CmnEnumDictDTO();
        dto.setId(frontDisplay.getEnumId());
        dto.setSort(frontDisplay.getSort());
        dto.setName(frontDisplay.getEnumName());
        switch (sortType) {
            case CN:
                dto.setLabel(frontDisplay.getEnumCnLabel());
                break;
            case EN:
                dto.setLabel(frontDisplay.getEnumEnLabel());
                break;
        }

        return dto;
    }

    public static CmnEnumDictDTO fromBizDict(EnumLevelOfExperience levelOfExperience, SortType sortType) {
        CmnEnumDictDTO dto = new CmnEnumDictDTO();
        dto.setId(levelOfExperience.getId().intValue());
        dto.setName(levelOfExperience.getName());
        switch (sortType) {
            case CN:
                dto.setLabel(levelOfExperience.getCnDisplay());
                break;
            case EN:
                dto.setLabel(levelOfExperience.getEnDisplay());
                break;
        }

        return dto;
    }
}
