package com.altomni.apn.common.service.user;

import com.altomni.apn.common.dto.LoginUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(value = "authority-service")
public interface SsoUserLoadByFeign {

    @GetMapping("/authority/api/v3/login-users")
    ResponseEntity<LoginUserDTO> findByEmail(@RequestParam("email") String email);

}
