package com.altomni.apn.common.aop.validation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.time.LocalDate;

public class TodayOrFutureValidator implements ConstraintValidator<TodayOrFuture, LocalDate> {

    @Override
    public void initialize(TodayOrFuture constraintAnnotation) {
    }

    @Override
    public boolean isValid(LocalDate date, ConstraintValidatorContext context) {
        if (date == null) {
            return true;
        }
        LocalDate today = LocalDate.now();
        return date.isEqual(today) || date.isAfter(today);
    }

}
