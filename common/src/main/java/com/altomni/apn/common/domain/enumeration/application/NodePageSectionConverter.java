package com.altomni.apn.common.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class NodePageSectionConverter extends AbstractAttributeConverter<NodePageSection, Integer> {
    public NodePageSectionConverter() {
        super(NodePageSection::toDbValue, NodePageSection::fromDbValue);
    }
}