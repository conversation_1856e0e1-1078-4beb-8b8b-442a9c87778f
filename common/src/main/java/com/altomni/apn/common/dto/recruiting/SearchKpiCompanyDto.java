package com.altomni.apn.common.dto.recruiting;

import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchKpiCompanyDto implements Serializable {

    private List<Long> idList;

    private String companyName;

    private List<Long> industries;

    private List<AccountCompanyStatus> companyStatuses;

//    private List<SalesLeadRoleType> roleList;

    private String createStartDate;

    private String createEndDate;

    private String requestStartDate;

    private String requestEndDate;

    private List<String> country;

}
