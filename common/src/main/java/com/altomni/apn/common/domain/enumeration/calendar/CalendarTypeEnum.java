package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CalendarTypeEnum implements ConvertedEnum<Integer> {

    TASK(0, "任务"),
    CALL(1, "电话"),
    MEETING(2, "会议"),
    DEADLINE(3, "到期日"),
    EMAIL(4, "邮件"),
    LUNCH(5, "午餐"),
    NOT_SUBMIT_TO_CLIENT(20, "候选人流程跟进-24小时未推荐至客户/拒绝"),
    SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS(21, "候选人流程跟进-推荐至客户后72小时未更新状态"),
    OFFER_PASS_NOT_UPDATE_STATUS(22, "候选人流程跟进-已超过入职日期，流程状态未更新"),
    PAYMENT_OVERDUE(23, "候选人流程跟进-回款逾期"),
    FOLLOW_UP_RECORD_UPDATE(24, "客户联系人跟进-客户联系人跟进记录更新"),
    EXPECTED_ORDER_EXPIRATION(25, "客户联系人跟进-预计成单到期日"),
    CONTRACT_NEARING_EXPIRATION(26, "客户联系人跟进-合同即将到期"),
    CONTRACT_EXPIRED(27, "客户联系人跟进-合同已过期"),
    CONTACT_JOB_CHANGE(28, "客户联系人跟进-客户联系人职位变动"),
    NO_SUBMIT_TALENT(29, "职位跟进-未提交候选人"),
    NO_INTERVIEW(30, "职位跟进-无面试安排"),
    ALL_SYSTEM_TASK(99, "所有系统任务");


    private final int dbValue;
    private final String comment;

    public static final ReverseEnumResolver<CalendarTypeEnum, Integer> resolver =
            new ReverseEnumResolver<>(CalendarTypeEnum.class, CalendarTypeEnum::toDbValue);

    CalendarTypeEnum(int dbValue, String comment) {
        this.dbValue = dbValue;
        this.comment = comment;
    }

    public static CalendarTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getComment() {
        return comment;
    }
}
