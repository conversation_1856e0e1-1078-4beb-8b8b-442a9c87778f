package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * invoice type enum
 */
public enum InvoiceType implements ConvertedEnum<Integer> {

    REGULAR(2, "Regular"),
    EXPENSE(1, "Expense"),
    ALL(0,"All");
    private final int dbValue;

    private final String description;

    InvoiceType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoiceType, Integer> resolver = new ReverseEnumResolver<>(InvoiceType.class, InvoiceType::toDbValue);

    public static InvoiceType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
