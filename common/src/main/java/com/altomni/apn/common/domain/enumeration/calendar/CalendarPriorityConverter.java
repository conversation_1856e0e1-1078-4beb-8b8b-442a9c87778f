package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CalendarPriorityConverter extends AbstractAttributeConverter<CalendarPriorityEnum, Integer> {
    public CalendarPriorityConverter() {
        super(CalendarPriorityEnum::toDbValue, CalendarPriorityEnum::fromDbValue);
    }
}
