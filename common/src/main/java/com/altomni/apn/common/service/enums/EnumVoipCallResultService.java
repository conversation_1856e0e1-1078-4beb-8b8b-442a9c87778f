package com.altomni.apn.common.service.enums;

import com.altomni.apn.common.domain.dict.EnumVoipCallResult;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EnumVoipCallResultService {

    @Resource
    EnumCommonService enumCommonService;


    public List<EnumDictVO> findAllOrderBySortType(SortType type) {
        List<EnumVoipCallResult> enumBulkUploadHeaders = enumCommonService.findAllEnumVoipCallResult();
        List<EnumDictVO> voList = enumBulkUploadHeaders.stream().map(result -> new EnumDictVO(result.getId(), SortType.CN.equals(type) ? result.getCnDisplay() : result.getEnDisplay(),
                result.getName(), null, result.getEnDisplayOrder() , result.getCnDisplayOrder(), null)).toList();
        return sortNodes(voList, type);
    }

    private List<EnumDictVO> sortNodes(List<EnumDictVO> nodes, SortType type) {
        return nodes.stream()
                .sorted(Comparator.comparingLong(SortType.CN.equals(type) ? EnumDictVO::getCnDisplayOrder : EnumDictVO::getEnDisplayOrder))
                .collect(Collectors.toList());
    }
}
