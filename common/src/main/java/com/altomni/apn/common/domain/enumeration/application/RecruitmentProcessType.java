package com.altomni.apn.common.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The RecruitmentProcessType enumeration.
 */
public enum RecruitmentProcessType implements ConvertedEnum<Integer> {

    FIRST_PARTY(1),

    SECOND_PARTY(2);

    private final Integer dbValue;

    RecruitmentProcessType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<RecruitmentProcessType, Integer> resolver =
        new ReverseEnumResolver<>(RecruitmentProcessType.class, RecruitmentProcessType::toDbValue);

    public static RecruitmentProcessType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
