package com.altomni.apn.common.dto.settings;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.config.SettingTrackRecordType;
import lombok.Data;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SettingTrackRecordCreateDTO {

    private BigInteger id;

    private String tempName;

    private SettingTrackRecordType tempType;

    private JSONObject tempContent ;

    private String tempRemark ;

    private List<Long> userId ;

    private List<String> userName ;

    private Integer status ;
}
