package com.altomni.apn.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StopWatch;


/**
 * <AUTHOR>
 * @package com.mbyx.component.stopwatch
 * @description 支持多线程的代码执行耗时监控类，以空间换时间，多线程情况下，为每个线程提供一个StopWatch对象，支持更高的并发，相比synchronized的以时间换空间方式，不需要加锁，性能更好
 * @date 2019/3/27 16:51
 */
@Slf4j
public class ConcurrentStopWatch {

    private ThreadLocal<StopWatch> stopWatch;

    public ConcurrentStopWatch() {
        this.stopWatch = ThreadLocal.withInitial(StopWatch::new);
    }


    public ConcurrentStopWatch(final String name) {
        this.stopWatch = ThreadLocal.withInitial(() -> new StopWatch(name));
    }

    public void start(String taskName) {
        try {
            if (!this.stopWatch.get().isRunning()) {
                this.stopWatch.get().start(taskName);
            } else {
                this.stopWatch.get().stop();
            }
        } catch (Exception e) {
            log.error("开始监控任务{}失败,原因:{}", taskName, e.getMessage());
        }
    }

    public void stop() {
        try {
            if (this.stopWatch.get().isRunning()) {
                this.stopWatch.get().stop();
            }
        } catch (Exception e) {
            log.error("停止监控任务失败,原因:{}", e.getMessage());
        }
    }

    public String prettyPrint() {
        try {
            return this.stopWatch.get().prettyPrint();
        } catch (Exception e) {
            log.error("打印监控日志失败,原因:{}", e.getMessage());
        }
        return "";
    }

    public long getTotalTimeMillis() {
        try {
            return this.stopWatch.get().getTotalTimeMillis();
        } catch (Exception e) {
            log.error("打印监控日志MS失败,原因:{}", e.getMessage());
        }
        return 0L;
    }

}

