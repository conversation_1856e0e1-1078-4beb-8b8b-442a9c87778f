package com.altomni.apn.common.enumeration.reportSubscriptions;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum DataPeriod implements ConvertedEnum<Integer> {
    TODAY(1),
    YESTERDAY(2),
    THIS_WEEK(3),
    LAST_WEEK(4),
    THIS_MONTH(5),
    LAST_MONTH(6),
    ;

    private final int dbValue;

    DataPeriod(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<DataPeriod, Integer> resolver =
            new ReverseEnumResolver<>(DataPeriod.class, DataPeriod::toDbValue);

    public static DataPeriod fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}