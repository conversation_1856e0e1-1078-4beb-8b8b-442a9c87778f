package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CalendarEventAttendeeReminderTypeEnum implements ConvertedEnum<Integer> {

    REMINDER(1),
    NO_REMINDER(0);

    private Integer dbValue;

    CalendarEventAttendeeReminderTypeEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CalendarEventAttendeeReminderTypeEnum, Integer> resolver = new ReverseEnumResolver<>(CalendarEventAttendeeReminderTypeEnum.class, CalendarEventAttendeeReminderTypeEnum::toDbValue);

    public static CalendarEventAttendeeReminderTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
