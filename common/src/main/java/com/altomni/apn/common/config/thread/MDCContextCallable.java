package com.altomni.apn.common.config.thread;

import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.Callable;

/**
 * MDC 上下文传递的 Callable 包装器
 * 用于在异步执行中保持 traceId 等 MDC 上下文信息
 * 
 * <AUTHOR>
 */
public class MDCContextCallable<T> implements Callable<T> {
    
    private final Callable<T> delegate;
    private final Map<String, String> contextMap;
    
    public MDCContextCallable(Callable<T> delegate) {
        this.delegate = delegate;
        // 获取当前线程的 MDC 上下文
        this.contextMap = MDC.getCopyOfContextMap();
    }
    
    public MDCContextCallable(Callable<T> delegate, Map<String, String> contextMap) {
        this.delegate = delegate;
        this.contextMap = contextMap;
    }
    
    @Override
    public T call() throws Exception {
        Map<String, String> previousContext = MDC.getCopyOfContextMap();
        try {
            // 清除当前线程的 MDC 并设置父线程的上下文
            MDC.clear();
            if (contextMap != null) {
                MDC.setContextMap(contextMap);
            }
            // 执行原始任务
            return delegate.call();
        } finally {
            // 恢复之前的 MDC 上下文
            MDC.clear();
            if (previousContext != null) {
                MDC.setContextMap(previousContext);
            }
        }
    }
    
    /**
     * 静态工厂方法，用于包装 Callable
     */
    public static <T> Callable<T> wrap(Callable<T> callable) {
        if (callable instanceof MDCContextCallable) {
            return callable;
        }
        return new MDCContextCallable<>(callable);
    }
}
