package com.altomni.apn.common.config.thread;

import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.Executor;

/**
 * MDC 上下文传递的 Executor 装饰器
 * 用于在异步执行中保持 traceId 等 MDC 上下文信息
 * 
 * <AUTHOR>
 */
public class MDCContextExecutor implements Executor {
    
    private final Executor delegate;
    
    public MDCContextExecutor(Executor delegate) {
        this.delegate = delegate;
    }
    
    @Override
    public void execute(@NotNull Runnable runnable) {
        // 获取当前线程的 MDC 上下文
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        
        // 创建包装的 Runnable，在执行时设置 MDC 上下文
        Runnable wrappedRunnable = () -> {
            Map<String, String> previousContext = MDC.getCopyOfContextMap();
            try {
                // 清除当前线程的 MDC 并设置父线程的上下文
                MDC.clear();
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                // 执行原始任务
                runnable.run();
            } finally {
                // 恢复之前的 MDC 上下文
                MDC.clear();
                if (previousContext != null) {
                    MDC.setContextMap(previousContext);
                }
            }
        };
        
        delegate.execute(wrappedRunnable);
    }
}
