package com.altomni.apn.common.domain.activity;

import com.altomni.apn.common.domain.enumeration.activity.SourceType;
import com.altomni.apn.common.enumeration.enums.ActivityArgsType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;

@ApiModel(description = "Activity config to process different type of page's activity.")
@Entity
@Table(name = "activity_config")
public class ActivityConfig {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The memo associated with the activity")
    @Column(name = "source_type")
    private SourceType sourceType;

    @ApiModelProperty(value = "The memo associated with the activity")
    @Column(name = "args_type")
    private ActivityArgsType argsType;

    @ApiModelProperty(value = "The name of the domain field")
    @Column(name = "field_name")
    private String fieldName;

    @ApiModelProperty(value = "The name of table column")
    @Column(name = "column_name")
    private String columnName;

    @ApiModelProperty(value = "Service which includes the method to get the value")
    @Column(name = "service_name")
    private String serviceName;

    @ApiModelProperty(value = "The method name in service to fetch the value by id")
    @Column(name = "service_method_name")
    private String serviceMethodName;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public SourceType getSourceType() {
        return sourceType;
    }

    public void setSourceType(SourceType sourceType) {
        this.sourceType = sourceType;
    }

    public ActivityArgsType getArgsType() {
        return argsType;
    }

    public void setArgsType(ActivityArgsType argsType) {
        this.argsType = argsType;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getServiceMethodName() {
        return serviceMethodName;
    }

    public void setServiceMethodName(String serviceMethodName) {
        this.serviceMethodName = serviceMethodName;
    }
}
