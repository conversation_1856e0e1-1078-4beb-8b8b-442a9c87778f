package com.altomni.apn.common.aop.datasync.template;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.application.EsFillerMqBaseProperties;
import com.altomni.apn.common.config.application.LarkProperties;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.service.rabbitmq.RabbitMqService;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.vo.canal.MqMessageCountVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.web.client.HttpClientErrorException;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;

/**
 * 数据同步模板方法
 */
@Slf4j
public abstract class DataSyncHandlerTemplate {

    /**
     * 处理失败情况的service
     */
    protected CanalService canalService;

    /**
     * 模板对应的数据处理类型
     */
    protected SyncIdTypeEnum type;

    /**
     * 发送通知的配置
     */
    protected LarkProperties larkProperties;

    /**
     * public mq 的一些必要配置
     */
    protected EsFillerMqBaseProperties esFillerMqBaseProperties;

    /**
     * 集成公共的 rabbitMqService, 实现了查询队列长度和发送通知功能
     */
    protected RabbitMqService rabbitMqService;

    //同步数据使用通用线程池
    private volatile ExecutorService executorService;

    /**
     * 本地处理器数量
     */
    private static final Integer localThreadNum = Runtime.getRuntime().availableProcessors();

    /**
     * 定义一个同步数据使用通用线程池
     */
    protected ExecutorService getExecutorService() {
        if (executorService == null) {
            synchronized (DataSyncHandlerTemplate.class) {
                if (executorService == null) {
                    executorService = new ThreadPoolExecutor(
                            localThreadNum * 3,
                            localThreadNum * 5,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(50000),
                            ThreadUtil.newNamedThreadFactory("api-scheduled-sync-data-to-es", false));
                }
            }
        }
        return executorService;
    }

    /**
     * 构造函数
     *
     * @param canalService   canalService 处理失败的情况用户重试
     * @param type           判断是否支持对应的处理类型
     * @param larkProperties 用于链接lark的账号
     * @param esFillerMqBaseProperties esFiller 的基础配置
     */
    public DataSyncHandlerTemplate(CanalService canalService,
                                   SyncIdTypeEnum type,
                                   LarkProperties larkProperties,
                                   EsFillerMqBaseProperties esFillerMqBaseProperties,
                                   RabbitMqService rabbitMqService) {
        this.canalService = canalService;
        this.type = type;
        this.larkProperties = larkProperties;
        this.esFillerMqBaseProperties = esFillerMqBaseProperties;
        this.rabbitMqService = rabbitMqService;
    }

    /**
     * 模板的整体方法
     * @param dataIds 需要同步数据的ids，有多个则使用逗号分隔
     * @param type 同步数据类型，talent,job,company,user
     * @param needCheckMq 是否需要内部检查mq队列,aop触发时需要,批量刷数据时需要在外部自己校验mq数量
     * @param priority 同步数据优先级,越大优先级越高, aop中的优先级高，重试的情况下优先级低
     */
    public void syncDataToMq(String dataIds, SyncIdTypeEnum type, boolean needCheckMq, int priority) {
        log.info("sync data to mq is start, dataIds = {}, type = {}", dataIds, type);
        //入参校验
        if (StrUtil.isBlank(dataIds) || ObjectUtil.isNull(type)) {
            log.info("同步数据结束,入参有误 dataIds = {}, type = {}", dataIds, type);
            return;
        }
        Collection<Long> dataIdList = Arrays.stream(dataIds.split(",")).map(Long::valueOf).toList();
        CollUtil.split(dataIdList, 50).forEach(partition -> partitionSyncDataToMq(partition, type, needCheckMq, priority, 0));
    }

    private void partitionSyncDataToMq(List<Long> dataIdList, SyncIdTypeEnum type, boolean needCheckMq, int priority, int deep) {
        try {
            //检查是否到达mq设置的上线
            if (needCheckMq && !isQueueAvailable()) {
                //到达上线,保存起来等到定时任务自动重试
                log.warn("同步数据结束, mq消息数量已达到最大值,保存数据到db,等待重试, dataIds = {}, type = {}", dataIdList, type);
                canalService.insertAll(dataIdList, type, FailReasonEnum.QUEUE_REACH_UPPER_LIMIT, "The number of queues has reached the upper limit", priority);
                return;
            }
            //真正去构建数据,发送给mq的方法
            doSyncDataToMQ(dataIdList, priority);
        } catch (HttpClientErrorException e) {
            //网络问题直接重试一次,可能是token问题,也可能是网络波动
            if (deep < 1) {
                partitionSyncDataToMq(dataIdList, type, needCheckMq, priority, 1);
            } else {
                log.error("同步数据结束, 发生异常, dataIds = {}, type = {}, message = {}", dataIdList, type, ExceptionUtils.getStackTrace(e));
                canalService.insertAll(dataIdList, type, FailReasonEnum.QUEUE_REACH_UPPER_LIMIT, "The number of queues has reached the upper limit", 3);
            }
        } catch (Exception e) {
            log.error("同步数据结束, 发生异常, dataIds = {}, type = {}, message = {}", dataIdList, type, ExceptionUtils.getStackTrace(e));
            canalService.insertAll(dataIdList, type, FailReasonEnum.QUEUE_REACH_UPPER_LIMIT, "The number of queues has reached the upper limit", 3);
        }
    }

    /**
     * 拆分数据,并进行并发处理
     * @param dataIdList
     * @param priority
     */
    public void doSyncDataToMQ(Collection<Long> dataIdList, int priority) {
        log.info("[syncDataToMq] dataIdList = {}, type = {}, start at = {}", dataIdList, SyncIdTypeEnum.USER, Instant.now());
        List<List<Long>> dataGroupList = CollUtil.split(dataIdList, 5);
        CountDownLatch countDownLatch = new CountDownLatch(dataGroupList.size());
        dataGroupList.forEach(ids -> getExecutorService().execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                //需要去实现的方法
                extractDataToMq(ids, priority, countDownLatch);
            }
        }));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(dataIdList, getType(), FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "CountDownLatch Error" +
                    "\n\t" + getType() + " IDs: " + dataIdList +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(larkProperties.getLarkWebhookKey(), larkProperties.getLarkWebhookUrl(), message);
        }
        log.info("[syncTalentsToMQ @-1] Scheduled sync {} to MQ Done! end at = {}", getType(), Instant.now());
    }

    /**
     * 检查是否到达mq设置的对大上线，如果到达则保存到db等待重试
     * @return true
     */
    protected boolean isQueueAvailable() {
        //获取user队列的数量
        Integer messageCount = rabbitMqService.checkMessageCount(esFillerMqBaseProperties.getToEsFillerQueue());
        Integer queueMessageCount = checkMessageCountByTypeQueueName();
        //判断队列数量和配置上线数量是否相同
        int maxMessageCount = Math.max(messageCount, queueMessageCount);
        Integer configEsFillerMqCount = esFillerMqBaseProperties.getToEsFillerMaximumMsgCount();
        configEsFillerMqCount = ObjectUtil.isEmpty(configEsFillerMqCount)? 1000: configEsFillerMqCount;
        log.debug("{} isQueueAvailable : {}", type, configEsFillerMqCount > maxMessageCount);
        return configEsFillerMqCount > maxMessageCount;
    }

    public MqMessageCountVM checkMqMessageCount() {
        Integer messageCount = rabbitMqService.checkMessageCount(esFillerMqBaseProperties.getToEsFillerQueue());
        Integer dataMessageCount = checkMessageCountByTypeQueueName();
        log.debug("{} messageCount = {}", type, messageCount);
        return new MqMessageCountVM(Math.max(messageCount, dataMessageCount), esFillerMqBaseProperties.getToEsFillerMaximumMsgCount(), null);
    }

    /**
     * 同步数据类型
     * @return SyncIdTypeEnum
     */
    public SyncIdTypeEnum getType() {
        return type;
    }

    /**
     * 构建数据并发送
     * @param ids                  需要同步的数据ids
     * @param priority             优先级
     * @param countDownLatch       countDownLatch
     */
    protected abstract void extractDataToMq(Collection<Long> ids, int priority, CountDownLatch countDownLatch);

    /**
     * 根据rabbitmq 队列名称获取队列长度，如果不需要回填，则实现的是返回0
     * @return integer
     */
    protected abstract Integer checkMessageCountByTypeQueueName();


}
