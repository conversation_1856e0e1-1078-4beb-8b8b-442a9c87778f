package com.altomni.apn.common.vo.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.StaffSizeType;
import com.altomni.apn.common.domain.enumeration.StaffSizeTypeConverter;
import com.altomni.apn.common.domain.enumeration.company.IndustryType;
import com.altomni.apn.common.domain.enumeration.company.IndustryTypeConverter;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Convert;
import java.io.Serializable;
import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel(description = "Vo of management service tenant")
public class TenantPublicVO extends AbstractAuditingEntity implements Serializable {

    private Long id;

    private String name;

    @Convert(converter = IndustryTypeConverter.class)
    private IndustryType industry;

    @ApiModelProperty(name = "tenant website ")
    private String  website;

    @ApiModelProperty(name = "organization name")
    private String organizationName;

    @ApiModelProperty(name = "staff size for prospect company")
    @Convert(converter = StaffSizeTypeConverter.class)
    private StaffSizeType staffSizeType;

    @ApiModelProperty(name = "tenant founded date")
    private Instant foundedDate;

    private Long addressId;

    private String description;

    private Integer status;

    private String logo;

    private Integer bulkCredit;

    private Integer monthlyCredit;

    private Integer updateMonthlyCredit;

    private String tenantEmail;

    private String tenantPhone;

    private Instant expireDate;

    private String contactName;

    private String contactFirstName;

    private String contactLastName;

    private Integer userMaxLimit;

    private String note;

    private String loginLink;

    private TenantUserTypeEnum userType;

    private Integer resetDayOfMonth;

    private boolean ownerDataRestriction;

    /**
     * 禁猎客户需求：用于存储不同租户下类似webhook URL的额外信息
     */
    @Column(name = "extended_info")
    private String extendedInfo;

    public static TenantPublicVO fromTenant(Tenant tenant) {
        TenantPublicVO tenantPublicVO = new TenantPublicVO();
        ServiceUtils.myCopyProperties(tenant, tenantPublicVO);
        return tenantPublicVO;
    }
}
