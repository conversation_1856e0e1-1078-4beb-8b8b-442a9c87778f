package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class WeekEndingTypeConverter extends AbstractAttributeConverter<WeekEndingType, Integer> {
    public WeekEndingTypeConverter() {
        super(WeekEndingType::toDbValue, WeekEndingType::fromDbValue);
    }
}
