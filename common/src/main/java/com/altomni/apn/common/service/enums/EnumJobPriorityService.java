package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.dict.EnumJobPriority;
import com.altomni.apn.common.enumeration.enums.DisplayType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class EnumJobPriorityService {

    private final Logger log = LoggerFactory.getLogger(EnumJobPriorityService.class);
    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumJobPriority> findAllEnumJobPriority() {
        List<EnumJobPriority> enumJobPriorityList = enumCommonService.findAllEnumJobPriority();
        if (CollUtil.isNotEmpty(enumJobPriorityList)) {
            enumJobPriorityList = enumJobPriorityList.stream()
            .sorted(Comparator.comparingInt(EnumJobPriority::getLevel)).collect(Collectors.toList());
        }
        return enumJobPriorityList;
    }

    public EnumJobPriority findEnumJobPriorityById(Long id) {
        Map<Long, EnumJobPriority> enumJobPriorityMap = enumCommonService.findAllEnumJobPriorityMap();

        if (!enumJobPriorityMap.containsKey(id)) {
            throw new CustomParameterizedException("Enum Priority is null , id : " + id);
        }
        return enumJobPriorityMap.get(id);
    }

    public EnumJobPriority findEnumJobPriorityByLevel(Integer level) {
        List<EnumJobPriority> enumJobPriorityList = enumCommonService.findAllEnumJobPriority();
        Optional<EnumJobPriority> enumJobPriority = enumJobPriorityList.stream().filter(o -> o.getLevel().equals(level)).findFirst();
        if (enumJobPriority.isEmpty()) {
            throw new CustomParameterizedException("Enum Priority is null , level : " + level);
        }
        return enumJobPriority.get();
    }

    /***
     * Activity Change function: get the UI name through the enumId;
     * @param id
     * @param displayType
     * @return
     */
    public String getJobPriorityUINameById(Long id, DisplayType displayType) {
        log.info("[APN: EnumJobPriorityService @{}] request to get job priorities UINames by ids, nameObject: {}", SecurityUtils.getUserId(), id);

        Map<Long, EnumJobPriority> enumJobPriorityMap = enumCommonService.findAllEnumJobPriorityMap();

        if(enumJobPriorityMap.containsKey(id)) {
            EnumJobPriority enumJobPriority = enumJobPriorityMap.get(id);
            if(displayType.equals(DisplayType.EN) && StringUtils.isNotBlank(enumJobPriority.getEnDisplay())) {
                return enumJobPriority.getEnDisplay();
            } else if (displayType.equals(DisplayType.CN) && StringUtils.isNotBlank(enumJobPriority.getCnDisplay())) {
                return enumJobPriority.getCnDisplay();
            } else {
                return enumJobPriority.getName();
            }
        }
        return "Unknown Job Priority";
    }


}
