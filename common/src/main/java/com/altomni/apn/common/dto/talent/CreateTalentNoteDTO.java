package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.NotePriority;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import lombok.Data;

import java.util.Map;

@Data
public class CreateTalentNoteDTO {
    private Long id;
    private String title;
    private String note;

    private Map<String, Object> additionalInfo;
    private Boolean visible = true;
    private NotePriority priority = NotePriority.NORMAL;
    private Long talentId;
    private Long userId;
    private TalentNoteType noteType;

    private Integer noteStatus;

    private Long agencyId;

    private Boolean readStatus = false;

    private Boolean isSystem = false;
}
