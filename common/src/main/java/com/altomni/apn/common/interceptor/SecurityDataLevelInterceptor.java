package com.altomni.apn.common.interceptor;

import com.altomni.apn.common.datapermission.db.DataPermissionDatabaseHandler;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.select.Select;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.EmptyInterceptor;
import org.hibernate.Transaction;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
public class SecurityDataLevelInterceptor extends EmptyInterceptor {

    public static final String SQL_TYPE_INSERT = "INSERT";
    public static final String SQL_TYPE_DELETE = "DELETE";
    public static final String SQL_TYPE_UPDATE = "UPDATE";
    public static final String SQL_TYPE_SELECT = "SELECT";

    /**
     * If the current select sql is passed to SaveOrUpdate, set enableSelectFilter = false. Or update operation will be changed to insert.
     */
    private boolean isPassedToUpdateForDataSecurity = false;

    private DataPermissionDatabaseHandler dataPermissionDatabaseHandler = DataPermissionDatabaseHandler.getInstance();

    /**
     * The sql passed in this method is produced by Hibernate, then it will be sent to DB.
     * Our data security interceptor works here to analyze the sql and refactor it.
     * @param sql
     * @return
     */
    @Override
    public String onPrepareStatement(String sql) {
        if (SecurityUtils.isSystemAdmin() || SecurityUtils.isAdmin()){
            return super.onPrepareStatement(sql);
        }
        String sqlType = this.extractSqlType(sql);
        if (StringUtils.isEmpty(sqlType) || sqlType.equals(SQL_TYPE_INSERT)){
            return super.onPrepareStatement(sql);
        }

        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (sqlType.equals(SQL_TYPE_SELECT)){
                if (Objects.nonNull(SecurityUtils.getAgencyId())) { // for agency c portal user to get job (agency C端用户查询job不需要验证数据权限)
                    return super.onPrepareStatement(sql);
                }
                if (!isPassedToUpdateForDataSecurity){
                    Select select = (Select) statement;
                    sql = dataPermissionDatabaseHandler.processSelect(select);
                }
            }else if (sqlType.equals(SQL_TYPE_UPDATE)){
                Update update = (Update) statement;
                sql = dataPermissionDatabaseHandler.processUpdate(update);
            }else if (sqlType.equals(SQL_TYPE_DELETE)){
                Delete delete = (Delete) statement;
                sql = dataPermissionDatabaseHandler.processDelete(delete);
            }
        } catch (JSQLParserException e) {
            log.error("error", e);
        }
        log.debug("after : " + sql);
        return super.onPrepareStatement(sql);
    }

    private String extractSqlType(String sql) {
        String sqlType = null;
        int len = sql.length();
        for(int i=0; i<len; i++){
            if (Character.isAlphabetic(sql.charAt(i))){
                sqlType = sql.substring(i, i+6).toUpperCase();
                break;
            }
        }
        return sqlType;
    }

    /**
     * Called when a transient entity is passed to SaveOrUpdate.
     * @param entity
     * @return
     */
    @Override
    public Boolean isTransient(Object entity) {
        isPassedToUpdateForDataSecurity = true;
        return null;
    }

    @Override
    public void afterTransactionCompletion(Transaction tx) {
        isPassedToUpdateForDataSecurity = false;
    }
}