package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "enum_motivation")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumMotivation implements EnumFrontDisplay{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Size(max = 64)
    @Column(name = "name", length = 64)
    private String name;

    @Size(max = 64)
    @Column(name = "cn_display", length = 64)
    private String cnDisplay;

    @Size(max = 64)
    @Column(name = "en_display", length = 64)
    private String enDisplay;

    @Column(name = "display_order")
    private Integer displayOrder;

    @Override
    public Integer getEnumId() {
        return id;
    }

    @Override
    public String getEnumName() {
        return name;
    }

    @Override
    public String getEnumCnLabel() {
        return cnDisplay;
    }

    @Override
    public String getEnumEnLabel() {
        return enDisplay;
    }

    @Override
    public int getSort() {
        return displayOrder;
    }
}