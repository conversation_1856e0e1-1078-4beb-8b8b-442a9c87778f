package com.altomni.apn.common.vo.reportsubscription;

import com.altomni.apn.common.enumeration.reportSubscriptions.ActiveType;
import com.altomni.apn.common.enumeration.reportSubscriptions.ActiveTypeConverter;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushTimeType;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushTimeTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class ReportSubscriptionSimplifyVO {

    @Id
    private Long id;

    private Long tenantId;

    private String name;

    @Convert(converter = PushTimeTypeConverter.class)
    private PushTimeType pushTimeType;

    private Integer dayOfWeek;

    private Integer dayOfMonth;

    private String sendTime;

    //创建自动激活
    @Convert(converter = ActiveTypeConverter.class)
    private ActiveType isActive = ActiveType.ACTIVE;

}
