package com.altomni.apn.common.constants;

public enum CommonOptionEnum {
    YES(1L, "010_YES", "是", "Yes"),
    NO(2L, "020_NO", "否", "No"),
    PREFER_NOT_TO_SAY(3L, "990_PREFER_NOT_TO_SAY", "不愿透露", "Prefer not to say");

    CommonOptionEnum(Long id, String name, String cnDisplay, String enDisplay) {
        this.id = id;
        this.name = name;
        this.cnDisplay = cnDisplay;
        this.enDisplay = enDisplay;
    }

    private Long id;
    private String name;
    private String cnDisplay;
    private String enDisplay;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCnDisplay() {
        return cnDisplay;
    }

    public void setCnDisplay(String cnDisplay) {
        this.cnDisplay = cnDisplay;
    }

    public String getEnDisplay() {
        return enDisplay;
    }

    public void setEnDisplay(String enDisplay) {
        this.enDisplay = enDisplay;
    }
}
