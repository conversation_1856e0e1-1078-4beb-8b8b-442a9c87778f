package com.altomni.apn.common.enumeration.enums;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * mq 事务业务类型枚举
 */
public enum MqTranRecordBusTypeEnums implements ConvertedEnum<Integer> {

    CREATE_TALENT_FROM_COMMONPOOL(1, "talent-Create a new talent form commonPool"),

    UPDATE_TALENT_FINANCE(2, "talent-updateTalent-finance"),

    UPDATE_TALENT_COMPANY(3, "talent-updateTalent-company"),

    TALENT_ONBOARD(4, "talent-onboard"),

    SSO_USERINFO_UPDATE(5, "sso-update-userinfo"),

    SSO_USERACTIVE_UPDATE(6, "sso-update-active"),

    SSO_USER_BINGING(7, "sso-user-binding");

    private final Integer dbValue;

    private final String desc;

    MqTranRecordBusTypeEnums(Integer dbValue, String desc) {
        this.dbValue = dbValue;
        this.desc = desc;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDesc() {
        return desc;
    }

    // static resolving:
    public static final ReverseEnumResolver<MqTranRecordBusTypeEnums, Integer> resolver =
            new ReverseEnumResolver<>(MqTranRecordBusTypeEnums.class, MqTranRecordBusTypeEnums::toDbValue);

    public static MqTranRecordBusTypeEnums fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}