package com.altomni.apn.common.domain.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class RelateJobFolderUserRoleConverter extends AbstractAttributeConverter<RelateJobFolderUserRole, Integer> {
    public RelateJobFolderUserRoleConverter() {
        super(RelateJobFolderUserRole::toDbValue, RelateJobFolderUserRole::fromDbValue);
    }
}
