package com.altomni.apn.common.dto.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class OwnershipRestriction {

    private static final long serialVersionUID = 1L;

    private Boolean searchUnlimitedOwnedData;

    private Long tenantId;

    private Long userId;

}
