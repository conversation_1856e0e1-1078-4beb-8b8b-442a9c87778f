package com.altomni.apn.common.dto.search;

import com.altomni.apn.common.domain.enumeration.search.CommonPoolTalentType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class PurchaseFilterDTO {

    private static final long serialVersionUID = 1L;

    private CommonPoolTalentType type;

    private List<Long> tenantIds;
}
