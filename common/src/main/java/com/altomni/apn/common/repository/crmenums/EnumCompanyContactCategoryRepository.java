package com.altomni.apn.common.repository.crmenums;

import com.altomni.apn.common.domain.crmenums.EnumCompanyContactCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface EnumCompanyContactCategoryRepository extends JpaRepository<EnumCompanyContactCategory, Integer>, QuerydslPredicateExecutor<EnumCompanyContactCategory> {
}
