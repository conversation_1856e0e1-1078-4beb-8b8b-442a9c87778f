package com.altomni.apn.common.auth.timesheet_auth;

import com.altomni.apn.common.auth.Constants;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.JsonUtil;
import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jwt.JWTClaimNames;
import com.nimbusds.jwt.JWTClaimsSet;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.LinkedHashMap;
import java.util.Map;

@Service
@RequiredArgsConstructor
public class JwtTimesheetUserTokenStore implements TimesheetUserTokenStore {

    @Value("${application.oauth2.jwtSignKey}")
    private String SIGN_KEY;

    private final CommonRedisService redisService;

    @Override
    public String generateToken(TimeSheetUser user, Integer expire) throws JOSEException, ParseException {
        Map<String, Object> claims = JsonUtil.fromJson(JsonUtil.toJson(user), LinkedHashMap.class);
        claims.put(JWTClaimNames.EXPIRATION_TIME, expire);
        // 创建JWT头部
        JWSHeader header = new JWSHeader(JWSAlgorithm.HS256);

        // 创建JWT有效载荷
        JWTClaimsSet claimSet = JWTClaimsSet.parse(claims);

        // 创建并签名JWT
        JWSObject jwsObject = new JWSObject(header, new Payload(claimSet.toJSONObject()));

        // 使用HMAC密钥进行签名
        jwsObject.sign(new MACSigner(SIGN_KEY));

        // 生成JWT字符串
        String jwt = jwsObject.serialize();
        redisService.set(Constants.TIMESHEET_TOKEN_KEY_PREFIX + jwt, "1", expire);
        redisService.set(Constants.TIMESHEET_USER_TOKEN_MAPPING + user.getUid(), jwt, expire);
        return jwt;
    }

    @Override
    public TimeSheetUser getUser(String token) throws ParseException {
        if (!tokenExist(token)) {
            return null;
        }
        JWSObject jwsObject = JWSObject.parse(token);
        Map<String, Object> claimsMap = jwsObject.getPayload().toJSONObject();
        return JsonUtil.fromJson(JsonUtil.toJson(claimsMap), TimeSheetUser.class);
    }

    @Override
    public boolean tokenExist(String token) {
        return redisService.exists(Constants.TIMESHEET_TOKEN_KEY_PREFIX + token);
    }

    @Override
    public boolean removeToken(String userUid) {
        String uidKey = Constants.TIMESHEET_USER_TOKEN_MAPPING + userUid;
        String jwtToken = redisService.get(uidKey);
        if (StringUtils.isNotBlank(jwtToken)) {
            redisService.delete(Constants.TIMESHEET_TOKEN_KEY_PREFIX + jwtToken);
            redisService.delete(uidKey);
            return true;
        } else {
            return false;
        }
    }


}
