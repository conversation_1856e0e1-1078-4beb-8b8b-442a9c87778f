package com.altomni.apn.common.repository.talent;


import com.altomni.apn.common.domain.folder.JobSharingPlatformTalentRelatedJobFolderRelation;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderRelatedInfoDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

@Repository
public interface JobSharingPlatformRelatedFolderRepository extends JpaRepository<JobSharingPlatformTalentRelatedJobFolderRelation, Long> {
//    @Query("SELECT r.relatedFolderId FROM JobSharingPlatformTalentRelatedFolderRelation rr " +
//            "" +
//            "WHERE r.userId = :userId AND r.jobId = :jobId")
//    Long getFolderIdByUserIdAndJobId(@Param("userId") Long userId, @Param("jobId") Long jobId);

    @Query(value = "SELECT r.shared_link_expire_time " +
            "FROM job_sharing_platform_talent_related_folder_relation r " +
            "JOIN talent_association_job_folder tajf ON tajf.folder_id = r.talent_associated_job_folder_folder_id " +
            "JOIN job_sharing_platform jsp ON jsp.user_id = tajf.user_id AND jsp.job_id = tajf.job_id " +
            "WHERE jsp.uuid = :uuid", nativeQuery = true)
    Instant findExpireTimeByJobSharingPlatformUuid(@Param("uuid") String uuid);

    @Query(value = "SELECT tajf.folder_id " +
            "FROM talent_association_job_folder tajf " +
            "JOIN job_sharing_platform jsp ON jsp.user_id = tajf.user_id AND jsp.job_id = tajf.job_id " +
            "WHERE jsp.uuid = :uuid", nativeQuery = true)
    String findTalentAssociationJobFolderFolderIdByJobSharingPlatformUuid(@Param("uuid") String uuid);

    Optional<JobSharingPlatformTalentRelatedJobFolderRelation> findByTalentAssociatedJobFolderFolderId(String folderId);

    @Query("SELECT r FROM JobSharingPlatformTalentRelatedJobFolderRelation r " +
            "JOIN TalentAssociationJobFolder tajf ON tajf.folderId = r.talentAssociatedJobFolderFolderId " +
            "WHERE tajf.userId = :userId and tajf.jobId = :jobId")
    Optional<JobSharingPlatformTalentRelatedJobFolderRelation> findByJobIdAndUserId(@Param("jobId") Long jobId, @Param("userId") Long userId);

    @Query("SELECT new com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderRelatedInfoDTO(tajf.jobId, tajf.userId, relation.talentAssociatedJobFolderFolderId, relation.sharedLinkExpireTime, u.tenantId) " +
            "FROM JobSharingPlatformTalentRelatedJobFolderRelation relation " +
            "JOIN TalentAssociationJobFolder tajf ON tajf.folderId = relation.talentAssociatedJobFolderFolderId " +
            "JOIN JobSharingPlatform jsp ON jsp.userId = tajf.userId AND jsp.jobId = tajf.jobId " +
            "JOIN User u ON u.id = tajf.userId " +
            "WHERE jsp.uuid = :uuid")
    Optional<TalentRelateJobFolderRelatedInfoDTO> findRelatedInfoByJobSharingPlatformUuid(@Param("uuid") String uuid);

    @Modifying
    @Transactional
    @Query("delete from JobSharingPlatformTalentRelatedJobFolderRelation where talentAssociatedJobFolderFolderId = :talentAssociatedJobFolderFolderId")
    void deleteByTalentAssociatedJobFolderFolderId(@Param("talentAssociatedJobFolderFolderId") String talentAssociatedJobFolderFolderId);
}