//package com.altomni.apn.common.domain.application;
//
//import com.altomni.apn.common.domain.AbstractAuditingEntity;
//import com.altomni.apn.common.domain.enumeration.EventStage;
//import com.altomni.apn.common.domain.enumeration.EventStageConverter;
//import com.altomni.apn.common.domain.enumeration.EventType;
//import com.altomni.apn.common.domain.enumeration.EventTypeConverter;
//import com.altomni.apn.common.domain.enumeration.application.ActivityStatus;
//import com.altomni.apn.common.domain.enumeration.support.ActivityStatusConverter;
//import com.fasterxml.jackson.annotation.JsonIdentityInfo;
//import com.fasterxml.jackson.annotation.ObjectIdGenerators;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//
//import javax.persistence.*;
//import javax.validation.constraints.NotNull;
//import java.io.Serializable;
//import java.time.Instant;
//import java.util.*;
//
///**
// * A Application.
// */
//@ApiModel(description = "Job Application. It represents the most current state of a talent's application on a job. The " +
//    "user for the application is the one who applied / watched the job for the talent. It has many activities to track the " +
//    "recruiting process")
//@Entity
//@Table(name = "application")
//@JsonIdentityInfo(
//    generator = ObjectIdGenerators.PropertyGenerator.class,
//    property = "id")
//public class Application extends AbstractAuditingEntity implements Serializable {
//
//    private static final long serialVersionUID = 3934898938266132960L;
//
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//
//    @ApiModelProperty(value = "memo text to describe the application")
//    @Column(name = "memo")
//    private String memo;
//
//    @ApiModelProperty(required = true, value = "The current Activity Status",
//        allowableValues = "Watching, Applied, Qualified, Submitted, Interview, Offered, Offer_Accepted, Offer_Rejected, Started")
//    @NotNull
//    @Convert(converter = ActivityStatusConverter.class)
//    @Column(name = "status", nullable = false)
//    private ActivityStatus status;
//
//    @ApiModelProperty(value = "Parser's score on the talent's qualification for the job. User should not change this.")
//    @Column(name = "score")
//    private Double score;
//
//    @ApiModelProperty(value = "User's score on the talent's qualification for the job")
//    @Column(name = "custom_score")
//    private Integer customScore;
//
//    @ApiModelProperty(value = "Resume Id. This is for easy search. Update is allowed")
//    @Column(name = "resume_id")
//    private Long resumeId;
//
//    @ApiModelProperty(value = "Talent Id. This is for easy search. Update is not allowed", required = true)
//    @Column(name = "talent_id", nullable = false)
//    private Long talentId;
//
//    @ApiModelProperty(value = "The user who created this application. ", required = true)
//    @Column(name = "user_id")
//    private Long userId;
//
//    @ApiModelProperty(value = "Job Id. This is for easy search. Update is not allowed", required = true)
//    @Column(name = "job_id", nullable = false)
//    private Long jobId;
//
//    @ApiModelProperty(value = "The event date when this application happens.")
//    @Column(name= "event_date")
//    private Instant eventDate;
//
//    @ApiModelProperty(value = "The event date belongs time zone.")
//    @Column(name= "event_time_zone")
//    private String eventTimeZone;
//
//    @ApiModelProperty(value = "The event type when this application happens. eg: when interview,the value is phone interview, onsite interview etc.")
//    @Convert(converter = EventTypeConverter.class)
//    @Column(name= "event_type")
//    private EventType eventType;
//
//    @Convert(converter = EventStageConverter.class)
//    @Column(name= "event_stage")
//    private EventStage eventStage;
//
//    @Column(name= "highlighted_experience")
//    private String highlightedExperience;
//
//
//    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
//    @Column(name = "tenant_id", nullable = false)
//    private Long tenantId;
//
//    public static final Set<String> UPDATE_SKIP_PROPERTIES = new HashSet<>(Arrays.asList("id", "userId", "talentId", "jobId", "tenantId"));
//
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//    public String getMemo() {
//        return memo;
//    }
//
//    public Application memo(String memo) {
//        this.memo = memo;
//        return this;
//    }
//
//    public void setMemo(String memo) {
//        this.memo = memo;
//    }
//
//    public ActivityStatus getStatus() {
//        return status;
//    }
//
//    public Application status(ActivityStatus status) {
//        this.status = status;
//        return this;
//    }
//
//    public void setStatus(ActivityStatus status) {
//        this.status = status;
//    }
//
//    public Double getScore() {
//        return score;
//    }
//
//    public Application score(Double score) {
//        this.score = score;
//        return this;
//    }
//
//    public void setScore(Double score) {
//        this.score = score;
//    }
//
//    public Integer getCustomScore() {
//        return customScore;
//    }
//
//    public Application customScore(Integer customScore) {
//        this.customScore = customScore;
//        return this;
//    }
//
//    public String getHighlightedExperience() {
//        return highlightedExperience;
//    }
//
//    public void setHighlightedExperience(String highlightedExperience) {
//        this.highlightedExperience = highlightedExperience;
//    }
//
//    public void setCustomScore(Integer customScore) {
//        this.customScore = customScore;
//    }
//
//    public Long getResumeId() {
//        return resumeId;
//    }
//
//    public void setResumeId(Long resumeId) {
//        this.resumeId = resumeId;
//    }
//
//    public Long getTalentId() {
//        return talentId;
//    }
//
//    public void setTalentId(Long talentId) {
//        this.talentId = talentId;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    public Long getJobId() {
//        return jobId;
//    }
//
//    public void setJobId(Long jobId) {
//        this.jobId = jobId;
//    }
//
//    public Long getTenantId() {
//        return tenantId;
//    }
//
//    public void setTenantId(Long tenantId) {
//        this.tenantId = tenantId;
//    }
//
//    public Application tenantId(Long tenantId) {
//        this.tenantId = tenantId;
//        return this;
//    }
//
//    public EventStage getEventStage() {
//        return eventStage;
//    }
//
//    public void setEventStage(EventStage eventStage) {
//        this.eventStage = eventStage;
//    }
//
//    public Instant getEventDate() {
//        return eventDate;
//    }
//
//    public void setEventDate(Instant eventDate) {
//        this.eventDate = eventDate;
//    }
//
//    public String getEventTimeZone() {
//        return eventTimeZone;
//    }
//
//    public void setEventTimeZone(String eventTimeZone) {
//        this.eventTimeZone = eventTimeZone;
//    }
//
//    public EventType getEventType() {
//        return eventType;
//    }
//
//    public void setEventType(EventType eventType) {
//        this.eventType = eventType;
//    }
//
//    public Activity toActivity() {
//        //activity user is current user, it is injected when saving the activity
//        return new Activity().applicationId(this.id).talentId(this.talentId)
//            .jobId(this.jobId)
//            .memo(this.memo)
//            .status(this.status)
//            .userId(this.userId)
//            .tenantId(this.tenantId)
//            .eventDate(this.eventDate)
//            .eventTimeZone(this.eventTimeZone)
//            .eventType(this.eventType)
//            .eventStage(this.eventStage);
//
//
//    }
//
//    public boolean isChanged(Application that) {
//        if (this == that) {
//            return false;
//        }
//        if (status != that.status) {
//            return true;
//        }
//        if (score != null && that.score!=null && !score.equals(that.score)) {
//            return true;
//        }
//        if (customScore != null && that.customScore != null && !customScore.equals(that.customScore)) {
//            return true;
//        }
//        if (resumeId != null && that.resumeId!=null && !resumeId.equals(that.resumeId)) {
//            return true;
//        }
//        if (memo != null ? !memo.equals(that.memo) : that.memo != null) {
//            return true;
//        }
//        if (eventDate != null ? !eventDate.equals(that.eventDate) : that.eventDate != null) {
//            return true;
//        }
//        return !talentId.equals(that.talentId) || !jobId.equals(that.jobId);
//    }
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        }
//        if (o == null || getClass() != o.getClass()) {
//            return false;
//        }
//        Application application = (Application) o;
//        if (application.getId() == null || getId() == null) {
//            return false;
//        }
//        return Objects.equals(getId(), application.getId());
//    }
//
//    @Override
//    public int hashCode() {
//        return Objects.hashCode(getId());
//    }
//
//    public Map<String, String> analytics() {
//        return new HashMap<String, String>(){{
//           put("talentId", talentId.toString());
//           put("userId", userId.toString());
//           put("jobId", jobId.toString());
//           put("status", status.name());
//        }};
//    }
//
//    public Application() { }
//
//    @Override
//    public String toString() {
//        return "Application{" +
//            "id=" + id +
//            ", memo='" + memo + '\'' +
//            ", status=" + status +
//            ", score='" + score + '\'' +
//            ", customScore=" + customScore +
//            ", resumeId=" + resumeId +
//            ", talentId=" + talentId +
//            ", userId=" + userId +
//            ", jobId=" + jobId +
//            ", eventDate=" + eventDate +
//            ", eventTimeZone='" + eventTimeZone + '\'' +
//            ", eventType=" + eventType +
//            ", tenantId=" + tenantId +
//            '}';
//    }
//}
