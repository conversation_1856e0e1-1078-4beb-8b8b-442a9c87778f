package com.altomni.apn.common.domain.crmenums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.crmenums.DictVO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "enum_team_category")
public class EnumTeamCategory implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Integer id;

    @Size(max = 128)
    @Column(name = "name", length = 128)
    private String name;

    @Size(max = 128)
    @Column(name = "en_display", length = 128)
    private String enDisplay;

    @Size(max = 128)
    @Column(name = "cn_display", length = 128)
    private String cnDisplay;

    @Column(name = "en_display_order")
    private Integer enDisplayOrder;

    @Column(name = "cn_display_order")
    private Integer cnDisplayOrder;

    public static DictVO fromEnumTeamCategory(EnumTeamCategory entity, SortType sortType) {
        DictVO dto = new DictVO();
        ServiceUtils.myCopyProperties(entity,dto);
        if (ObjectUtil.isNotEmpty(entity.getId())) {
            dto.setId(entity.getId());
        }
        if (sortType.equals(SortType.EN)) {
            dto.setLabel(entity.getEnDisplay());
            dto.setDisplayOrder(entity.getEnDisplayOrder());
        } else if (sortType.equals(SortType.CN)){
            dto.setLabel(entity.getCnDisplay());
            dto.setDisplayOrder(entity.getCnDisplayOrder());
        }
        if (ObjectUtil.isNotEmpty(entity.getId())) {
            dto.setValue(StrUtil.toString(entity.getId()));
        }
        if (ObjectUtil.isNotEmpty(entity.getName())) {
            dto.setName(StrUtil.toString(entity.getName()));
        }
        dto.setChecked(false);
        return dto;
    }

}