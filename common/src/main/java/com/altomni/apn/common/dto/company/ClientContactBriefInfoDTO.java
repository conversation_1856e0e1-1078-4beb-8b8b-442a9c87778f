package com.altomni.apn.common.dto.company;


import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@ApiModel(value = "client contact dto for job contact")
public class ClientContactBriefInfoDTO {
    Long salesLeadClientContactId;
    String firstName;
    String lastName;

    Long talentId;

    public ClientContactBriefInfoDTO(Long salesLeadClientContactId, String firstName, String lastName, Long talentId){
        this.salesLeadClientContactId = salesLeadClientContactId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.talentId = talentId;
    }
}
