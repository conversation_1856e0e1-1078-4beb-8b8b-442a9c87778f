package com.altomni.apn.common.config.application;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class LarkProperties {

    @Value("${application.esfillerMQ.notification.lark.webhookKey:}")
    private String larkWebhookKey;

    @Value("${application.esfillerMQ.notification.lark.webhookUrl:}")
    private String larkWebhookUrl;

    @Value("${application.messageNotification.lark.authenticationEndPoint}")
    private String larkAuthenticationEndpoint;

    @Value("${application.messageNotification.lark.obtainUserIdsEndPoint}")
    private String larkObtainUserIdsEndpoint;

    @Value("${application.messageNotification.lark.sendMessageEndpoint}")
    private String larkSendMessageEndpoint;

    @Value("${application.messageNotification.lark.batchSendMessageEndpoint}")
    private String larkBatchSendMessageEndpoint;

    @Value("${application.messageNotification.lark.appId}")
    private String larkSendMessageServiceAppId;

    @Value("${application.messageNotification.lark.appSecret}")
    private String larkSendMessageServiceAppSecret;


}
