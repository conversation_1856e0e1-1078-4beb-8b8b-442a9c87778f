package com.altomni.apn.common.enumeration.tenant;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TenantUserTypeEnumConverter extends AbstractAttributeConverter<TenantUserTypeEnum, Integer> {
    public TenantUserTypeEnumConverter() {
        super(TenantUserTypeEnum::toDbValue, TenantUserTypeEnum::fromDbValue);
    }
}
