package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CalendarTypeConverter extends AbstractAttributeConverter<CalendarTypeEnum, Integer> {
    public CalendarTypeConverter() {
        super(CalendarTypeEnum::toDbValue, CalendarTypeEnum::fromDbValue);
    }
}
