package com.altomni.apn.common.dto.company;

import com.altomni.apn.common.dto.address.LocationDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class CrmContactDTO implements Serializable {

    @ApiModelProperty(value = "id for contact")
    private Long id;

    @ApiModelProperty(value = "firstName for contact")
    @NotEmpty
    private String firstName;

    @ApiModelProperty(value = "lastName for contact")
    @NotEmpty
    private String lastName;

    @ApiModelProperty(value = "lastFollowUpTime for contact")
    private Instant lastFollowUpTime;

    @ApiModelProperty(value = "note for contact")
    private String note;

    //contact owner
    @ApiModelProperty(value = "contacts owners for contact")
    private List<Long> contactOwners;

    @ApiModelProperty(value = "contacts share user for contact")
    private List<ContactShareUserDTO> contactShareUsers;

    @ApiModelProperty(value = "contacts share contact for contact")
    private Long contactShareTenant;

    //contact info

    @ApiModelProperty(value = "contact info for contact")
    @NotNull
    private List<ContactInfoDTO> contacts;
//
    @ApiModelProperty(value = "verifiedContacts for contact")
    private List<ContactInfoDTO> verifiedContacts;

//    @ApiModelProperty(value = "linkedinProfile for contact")
//    private List<String> linkedinProfile;


    //relation: account company/business <-> contact

    @ApiModelProperty(value = "account company id for contact")
    private Long accountCompanyId;

    @ApiModelProperty(value = "account company Name for contact")
    private String accountCompanyName;

    @ApiModelProperty(value = "account company Name for contact")
    private String accountCompanyFullBusinessName;

    @ApiModelProperty(value = "account Business under account company ids for the contact")
    private List<Long> accountBusiness;

    // to keep in followup log; the id from es will be string
    @ApiModelProperty(value = "sales Lead Company Contact Id for lead")
    private String salesLeadCompanyContactId;

    @ApiModelProperty(value = "contactCategory for contact")
    private Integer contactCategory;

    @ApiModelProperty(value = "title for contact")
    private String title;

    @ApiModelProperty(value = "businessUnit for contact")
    private String businessUnit;

    @ApiModelProperty(value = "department for contact")
    private String department;

    @ApiModelProperty(value = "businessGroup for contact")
    private String businessGroup;

//    @ApiModelProperty(value = "jobState for the contact")
//    private JobState jobState;

    @ApiModelProperty(value = "location for contact")
    private List<LocationDTO> contactLocation;

    //merge to contact if duplication
    @ApiModelProperty(value = "user picked contact id for merging contact")
    private Long selectedContactId;

    private Boolean shareToAll;


    //tags to company

    @ApiModelProperty(value = "tags for contact")
    private List<String> tags;

    @ApiModelProperty(value = "apnTags for contact")
    @UniqueElements
    private List<Integer> apnTags;

    @ApiModelProperty(value = "id for apnTalent")
    private Long apnTalentId;

    @ApiModelProperty(value = "tenantId for contact")
    private Long tenantId;

    @ApiModelProperty(value = "userId for crm contact")
    private Long crmUserId;

    @ApiModelProperty(value = "id for apnCommonTalent")
    private String esId;

    @ApiModelProperty(value = "service type relation")
    private List<Integer> associatedServiceTypes;

    /**
     * 关键联系人需求增加
     */
    @ApiModelProperty(value = "is key contact")
    private Boolean isKeyContact;

}
