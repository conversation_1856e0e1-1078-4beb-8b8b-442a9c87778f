package com.altomni.apn.common.dto.folder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class FolderListDTO {

    private Long userId;

    @ApiModelProperty(value = "Folders I created")
    List<FolderNameDTO> myFolderList;


    @ApiModelProperty(value = "Folders shared to me")
    List<FolderNameDTO> sharedFolderList;

}
