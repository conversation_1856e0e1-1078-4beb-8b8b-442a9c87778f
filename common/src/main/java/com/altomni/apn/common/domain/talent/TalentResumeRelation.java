package com.altomni.apn.common.domain.talent;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.CommonDataStatus;
import com.altomni.apn.common.domain.enumeration.CommonDataStatusConverter;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.TalentContactStatusConverter;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceTypeConverter;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;


@Data
@ApiModel(description = "Talent's resume relation")
@Entity
@Table(name = "talent_resume_relation")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentResumeRelation extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -5995736025769321575L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "resume_id")
    private Long resumeId;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "file_name")
    private String fileName;

    @ApiModelProperty(value = "resume source")
    @Convert(converter = ResumeSourceTypeConverter.class)
    @Column(name = "source_type")
    private ResumeSourceType sourceType = ResumeSourceType.UNKNOWN;

    @Convert(converter = CommonDataStatusConverter.class)
    @Column(name = "status")
    private CommonDataStatus status;

}
