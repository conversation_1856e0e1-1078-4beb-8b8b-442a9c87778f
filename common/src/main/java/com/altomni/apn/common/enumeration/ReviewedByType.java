package com.altomni.apn.common.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The WorkAuth enumeration.
 */
public enum ReviewedByType implements ConvertedEnum<Integer>  {
    USER(0), TEAM(1);

    private final int dbValue;

    ReviewedByType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ReviewedByType, Integer> resolver =
        new ReverseEnumResolver<>(ReviewedByType.class, ReviewedByType::toDbValue);

    public static ReviewedByType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
