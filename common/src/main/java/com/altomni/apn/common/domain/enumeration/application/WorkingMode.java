package com.altomni.apn.common.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The FeeType enumeration.
 */
public enum WorkingMode implements ConvertedEnum<Integer> {

    REMOTE(1),

    ONSITE(2),

    MIXED(3);

    private final int dbValue;

    WorkingMode(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<WorkingMode, Integer> resolver = new ReverseEnumResolver<>(WorkingMode.class, WorkingMode::toDbValue);

    public static WorkingMode fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
