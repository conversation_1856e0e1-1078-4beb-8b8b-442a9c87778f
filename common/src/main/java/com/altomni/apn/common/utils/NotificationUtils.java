package com.altomni.apn.common.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.errors.CustomParameterizedException;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

public class NotificationUtils {

    private static final Logger log = LoggerFactory.getLogger(NotificationUtils.class);
    // HMAC encryption algorithm.
    private static final String HMAC_SHA_256 = "HmacSHA256";
    private static final okhttp3.MediaType JSON_TYPE = okhttp3.MediaType.parse("application/json; charset=utf-8");

    private static final String TAG = "tag";
    private static final String TEXT = "text";
    private static final String HREF = "href";
    private static final String TAG_A = "a";
    private static final String DETAIL_CN = "查看详情";




    private static void sendWithOkHttp(String url, String bodyJson){
        log.info("sendWithOkHttp with url: {}", url);
        try {
            // send with okhttp3
            okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON_TYPE, bodyJson);
            Request request = new Request.Builder().url(url).post(body).build();
            okhttp3.Response response = new OkHttpClient().newCall(request).execute();
            String responseBody = response.body() != null ? response.body().string() : null;
            log.info("[APN: sendLarkNotification @-1] get response from lark: {}", responseBody);
            if (!response.isSuccessful()) {
                throw new CustomParameterizedException(responseBody);
            }
        }catch (IOException e){
            log.error("[APN: sendLarkNotification @-1] send msg to lark error: {}", e.getMessage());
        }
    }

    private static String buildBasicLarkContent(String webhookKey, String msg){
        Long timestamp = Instant.now().getEpochSecond();
        String key = timestamp + "\n" + webhookKey;
        byte[] bytes = HmacUtils.getInitializedMac(HMAC_SHA_256, key.getBytes()).doFinal();
        String signature = Base64.getEncoder().encodeToString(bytes);

        JSONObject json = new JSONObject();
        json.put("timestamp", timestamp);
        json.put("sign", signature);
        json.put("msg_type", "text");
        json.put("content", new JSONObject(new HashMap<String, Object>(){{put("text", msg);}}));
        return json.toString();
    }

    private static String buildRichTextLarkContent(String webhookKey, JSONObject msg){
        Long timestamp = Instant.now().getEpochSecond();
        String key = timestamp + "\n" + webhookKey;
        byte[] bytes = HmacUtils.getInitializedMac(HMAC_SHA_256, key.getBytes()).doFinal();
        String signature = Base64.getEncoder().encodeToString(bytes);

        JSONObject json = new JSONObject();
        json.put("timestamp", timestamp);
        json.put("sign", signature);
        json.put("msg_type", "post");
        JSONObject content = new JSONObject();
        JSONObject post = new JSONObject();
        post.put("en_us", msg);
        content.put("post", post);
        json.put("content", content);
        return json.toString();
    }

    public static void sendAlertToLark(String webhookKey, String webhookUrl, String msg) {
        String json = buildBasicLarkContent(webhookKey, msg);

        log.info("[APN: sendAlertToLark @-1] send msg to lark: {}", json);

        sendWithOkHttp(webhookUrl, json);
    }

    public static void sendRichTextMessage(String webhookKey, String webhookUrl, JSONObject msg) {
        String json = buildRichTextLarkContent(webhookKey, msg);
        log.info("[APN: sendAlertToLark @-1] send rich text msg to lark: {}", json);
        sendWithOkHttp(webhookUrl, json);
    }
}

