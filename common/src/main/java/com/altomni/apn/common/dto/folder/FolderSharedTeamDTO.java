package com.altomni.apn.common.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FolderSharedTeamDTO {
    private Long folderId;
    private Long teamId;

    private String teamName;

    @ApiModelProperty(value = "owner of the folder")
    private Long ownerUserId;

    @ApiModelProperty(value = "permission of current folder for the team")
    private FolderPermission folderPermission;

    public FolderSharedTeamDTO(Long folderId, Long teamId, String teamName, Long ownerUserId) {
        this.folderId = folderId;
        this.teamId = teamId;
        this.teamName = teamName;
        this.ownerUserId = ownerUserId;
    }

    public FolderSharedTeamDTO(Long teamId, String teamName) {
        this.folderId = null;
        this.teamId = teamId;
        this.teamName = teamName;
        this.ownerUserId = null;
    }

    public FolderSharedTeamDTO(Long folderId, Long teamId, String teamName, Long ownerUserId, FolderPermission permission) {
        this.folderId = folderId;
        this.teamId = teamId;
        this.teamName = teamName;
        this.ownerUserId = ownerUserId;
        this.folderPermission = permission;
    }
}
