package com.altomni.apn.common.dto.recruiting;

import com.altomni.apn.common.enumeration.enums.KpiReportByUserStageType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StageKpiReportDto implements Serializable {

    private KpiReportByUserStageType stageName;

    private Long minValue;

    private Long maxValue;
}
