package com.altomni.apn.common.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

/**
 * <AUTHOR>
 */
@Converter
public class ResignationReasonConverter extends AbstractAttributeConverter<ResignationReason, Integer> {
    public ResignationReasonConverter() {
        super(ResignationReason::toDbValue, ResignationReason::fromDbValue);
    }
}
