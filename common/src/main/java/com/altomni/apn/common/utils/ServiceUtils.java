package com.altomni.apn.common.utils;

import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.FeatureDescriptor;
import java.beans.PropertyDescriptor;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public class ServiceUtils {

    public static String[] getNullPropertyNames (Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        return Stream.of(src.getPropertyDescriptors()).filter(pd -> {
            Object obj = src.getPropertyValue(pd.getName());
            boolean result = (obj == null);
            if (!result && obj instanceof Collection) {
                result = ((Collection) obj).isEmpty();
            }
            return result;
        })
            .map(FeatureDescriptor::getName)
            .distinct()
            .toArray(String[]::new);
    }

    public static String[] getNullPropertyNames (Object source, Set<String> skips) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        return Stream.of(src.getPropertyDescriptors()).filter(pd -> {
            if (skips.contains(pd.getName())) return true;
            Object obj = src.getPropertyValue(pd.getName());
            boolean result = (obj == null);
            if (!result && obj instanceof Collection) {
                result = ((Collection) obj).isEmpty();
            }
            return result;
        })
            .map(FeatureDescriptor::getName)
            .distinct()
            .toArray(String[]::new);
    }

    public static boolean hasNullProperties(Object source, List<String> properties) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        if (properties != null) {
            Map<String, Boolean> nullValues = new HashMap<>();
            return properties.stream().filter(name -> {
                if (name.contains("||")) {
                    String[] ps = name.split("\\|\\|");
                    Arrays.asList(ps).forEach(p -> {
                        Object obj = src.getPropertyValue(p.trim());
                        boolean result = (obj == null);
                        if (!result && obj instanceof Collection) {
                            result = ((Collection) obj).isEmpty();
                        }
                        Boolean prev = nullValues.get(name);
                        if (prev == null) nullValues.put(name, result);
                        else nullValues.put(name, prev && result);
                    });
                    return nullValues.get(name);
                } else {
                    Object obj = src.getPropertyValue(name);
                    boolean result = (obj == null);
                    if (!result && obj instanceof Collection) {
                        result = ((Collection) obj).isEmpty();
                    }
                    return result;
                }
            }).count() > 0;
        } else {
            return Stream.of(src.getPropertyDescriptors()).filter(pd -> {
                Object obj = src.getPropertyValue(pd.getName());
                boolean result = (obj == null);
                if (!result && obj instanceof Collection) {
                    result = ((Collection) obj).isEmpty();
                }
                return result;
            }).count() > 0;
        }
    }

    public static String nullErrorMessage(List<String> properties) {
        return properties.stream().collect(Collectors.joining(",")) + " are required to create entity";
    }

    public static JSONObject setNullPropertyNames(Object source, List<String> propertiesToReturn) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        JSONObject obj = new JSONObject();
        for (int i = 0; i < src.getPropertyDescriptors().length; i++){
            PropertyDescriptor pd = src.getPropertyDescriptors()[i];
            if (propertiesToReturn.contains(pd.getName())) {
                Object value = src.getPropertyValue(pd.getName());
                obj.put(pd.getName(), value);
            }
        }
        return obj;
    }

    /**
     * use Spring BeanUtils to copy and ignore null
     * @param src new object
     * @param target old object to update
     */
    public static void myCopyProperties(Object src, Object target) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
    }

    /**
     *
     * use Spring BeanUtils to copy and ignore null
     * @param src new object
     * @param skips attributes want to skip when copy
     */
    public static void myCopyProperties(Object src, Object target, Set<String> skips) {
        BeanUtils.copyProperties(src, target, getNullPropertyNames(src, skips));
    }

    public static <S, T> List<T> convert2DTOList(Collection<S> src, Class<T> clazz) {
        List<T> targetList = new ArrayList<>(src.size());
        try {
            for(S s : src){
                T t = clazz.newInstance();
                BeanUtils.copyProperties(s, t);
                targetList.add(t);
            }
        } catch (InstantiationException | IllegalAccessException e) {
            log.error("error", e);
        }
        return targetList;
    }

    public static <S, T> List<T> convert2DTOList(Iterable<S> src, Class<T> clazz) {
        List<T> targetList = new ArrayList<>();
        try {
            for(S s : src){
                T t = clazz.newInstance();
                BeanUtils.copyProperties(s, t);
                targetList.add(t);
            }
        } catch (InstantiationException | IllegalAccessException e) {
            log.error("error", e);
        }
        return targetList;
    }

    public static <T> T convert2DTO(Object src, Class<T> clazz) {
        T t = null;
        try {
                t = clazz.newInstance();
                BeanUtils.copyProperties(src, t);
        } catch (InstantiationException | IllegalAccessException e) {
            log.error("error", e);
        }
        return t;
    }
}
