package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CalendarPriorityEnum implements ConvertedEnum<Integer> {

    HIGH(0, "高"),
    MEDIUM(1, "中"),
    LOW(2, "低");


    private final int dbValue;
    private final String comment;

    public static final ReverseEnumResolver<CalendarPriorityEnum, Integer> resolver =
            new ReverseEnumResolver<>(CalendarPriorityEnum.class, CalendarPriorityEnum::toDbValue);

    CalendarPriorityEnum(int dbValue, String comment) {
        this.dbValue = dbValue;
        this.comment = comment;
    }

    public static CalendarPriorityEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }
}
