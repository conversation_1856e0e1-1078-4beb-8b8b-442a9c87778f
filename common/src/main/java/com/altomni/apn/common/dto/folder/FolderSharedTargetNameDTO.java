package com.altomni.apn.common.dto.folder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FolderSharedTargetNameDTO {
    @ApiModelProperty(value = "The id of user/team that shared to")
    private Long id;

    @ApiModelProperty(value = "the name of shared target ")
    private String name;
}
