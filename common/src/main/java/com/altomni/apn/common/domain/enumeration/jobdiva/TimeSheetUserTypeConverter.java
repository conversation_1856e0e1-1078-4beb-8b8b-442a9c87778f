package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TimeSheetUserTypeConverter extends AbstractAttributeConverter<TimeSheetUserType, Integer> {
    public TimeSheetUserTypeConverter() {
        super(TimeSheetUserType::toDbValue, TimeSheetUserType::fromDbValue);
    }
}
