package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessForTalentVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 3859144837158086029L;

    private Long id;

    private Long talentId;

    private String talentName;

    @JsonRawValue
    private String skills;

    private Long jobId;

    private String jobCode;

    private String jobTitle;

    private JobStatus jobStatus;

    private Instant openTime;

    private JobType jobType;

    private Long companyId;

    private String companyName;

    private NodeType lastNodeType;

    private NodeStatus lastNodeStatus;

    private Instant lastModifiedDate;

    private BigDecimal agreedPayRate;

    private String accountManager;

    private List<Long> accountManagerIds;

    private String coAccountManager;

    private List<Long> coAmIds;

    private List<CoAmUserCountryVO> coAmList;

    private String recruiter;

    private List<Long> recruiterIds;

    private List<Long> bdOwnerIds;

    private List<Long> saleLeadOwnerIds;

    private String kpiUsers;

    private List<Long> kpiUserIds;

    private LocalDate onboardDate;

    private List<TalentRecruitmentProcessNodeVO> talentRecruitmentProcessNodes;

    private EliminateReason eliminateReason;

    private String eliminateNote;

    private boolean isPrivateJob;

    private boolean hasPermission; //前端在保密职位处用到

    /**
     * true: 已离职，false:未离职
     */
    private Boolean resigned;

    private Long agencyId;

    private String agencyName;

    private Instant talentRecruitmentProcessSubmittedTime;

}
