package com.altomni.apn.common.domain.enumeration.message;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum MessageFavoriteEnum implements ConvertedEnum<Integer> {

    FAVORITE(1),
    NOT_FAVORITE(0);

    private Integer dbValue;

    MessageFavoriteEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<MessageFavoriteEnum, Integer> resolver = new ReverseEnumResolver<>(MessageFavoriteEnum.class, MessageFavoriteEnum::toDbValue);

    public static MessageFavoriteEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}