package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TimeSheetTypeConverter extends AbstractAttributeConverter<TimeSheetType, Integer> {
    public TimeSheetTypeConverter() {
        super(TimeSheetType::toDbValue, TimeSheetType::fromDbValue);
    }
}
