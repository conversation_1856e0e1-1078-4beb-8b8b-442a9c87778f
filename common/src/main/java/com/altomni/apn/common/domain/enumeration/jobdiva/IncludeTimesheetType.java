package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * group invoice contain or not contain timesheet enum
 */
public enum IncludeTimesheetType implements ConvertedEnum<Integer> {

    CONTAIN(1, "Contain"),
    DOES_NOT_CONTAIN(2, "DoseNotContain");
    private final int dbValue;

    private final String description;

    IncludeTimesheetType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<IncludeTimesheetType, Integer> resolver = new ReverseEnumResolver<>(IncludeTimesheetType.class, IncludeTimesheetType::toDbValue);

    public static IncludeTimesheetType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
