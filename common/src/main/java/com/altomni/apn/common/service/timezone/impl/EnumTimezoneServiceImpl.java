package com.altomni.apn.common.service.timezone.impl;

import com.altomni.apn.common.domain.timezone.EnumTimezone;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.timezone.EnumTimezoneService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("enumTimezoneService")
public class EnumTimezoneServiceImpl implements EnumTimezoneService {

    @Resource
    private EnumCommonService enumCommonService;

    /**
     * 这些是有字段固化的时区，其他的直接使用实时计算
     */
    private final List<String> solidifyTimezoneList = List.of("America/Los_Angeles", "Asia/Shanghai", "America/New_York");

    @Override
    public Map<String, String> getColumnSuffix() {
        List<EnumTimezone> timezoneList = enumCommonService.findAllTimezone();
        //找到对应这3个的时区数据
        Map<Integer, String> idMap = new HashMap<>(16);
        timezoneList.stream()
                .filter(timezone -> solidifyTimezoneList.contains(timezone.getTimezone()))
                .forEach(timezone -> {
                    if (timezone.getParent() == 0) {
                        idMap.put(timezone.getId(), timezone.getTimezone().split("/")[1].toLowerCase());
                    } else {
                        idMap.put(timezone.getParent(), timezone.getTimezone().split("/")[1].toLowerCase());
                    }
                });
        //获取一个综合的map, 映射到我们已经固化的3个timezone搜索
        return timezoneList.stream()
                .filter(timezone -> idMap.containsKey(timezone.getId()) || idMap.containsKey(timezone.getParent()))
                .collect(Collectors.toMap(EnumTimezone::getTimezone,
                        timezone -> timezone.getParent() == 0? idMap.get(timezone.getId()): idMap.get(timezone.getParent())));
    }

}
