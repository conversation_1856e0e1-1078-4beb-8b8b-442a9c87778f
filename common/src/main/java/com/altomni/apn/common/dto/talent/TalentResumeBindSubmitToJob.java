package com.altomni.apn.common.dto.talent;

import lombok.Data;
import java.time.Instant;

@Data
public class TalentResumeBindSubmitToJob {
    private Long talentResumeRelationId;
    private Long talentRecruitmentProcessId;
    private Long talentRecruitmentProcessSubmitToJobId;
    private Long jobId;
    private String jobName;
    private Long talentRecruitmentSubmitToJobId;
    private Instant lastModifiedDate;

    public TalentResumeBindSubmitToJob() {
    }

    public TalentResumeBindSubmitToJob(Long talentResumeRelationId, Long talentRecruitmentProcessId, Long talentRecruitmentProcessSubmitToJobId, Long jobId, String jobName, Long talentRecruitmentSubmitToJobId, Instant lastModifiedDate) {
        this.talentResumeRelationId = talentResumeRelationId;
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
        this.talentRecruitmentProcessSubmitToJobId = talentRecruitmentProcessSubmitToJobId;
        this.jobId = jobId;
        this.jobName = jobName;
        this.talentRecruitmentSubmitToJobId = talentRecruitmentSubmitToJobId;
        this.lastModifiedDate = lastModifiedDate;
    }
}
