package com.altomni.apn.common.errors;

import com.altomni.apn.common.config.constants.ExceptionTypeConstants;
import com.altomni.apn.common.utils.JsonUtil;
import com.google.common.io.CharStreams;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.*;

import java.io.IOException;
import java.io.Reader;
import java.nio.charset.Charset;
import java.util.Objects;

public class FeignDecoder implements ErrorDecoder {

    private final ErrorDecoder errorDecoder = new Default();

    @Override
    public Exception decode(String s, Response response) {

        ExceptionMessage exceptionMessage = null;
        if (response.body() == null) {
            return errorDecoder.decode(s, response);
        }

        try (Reader reader = response.body().asReader(Charset.defaultCharset())) {
            //Easy way to read the stream and get a String object
            String result = CharStreams.toString(reader);
            //just in case you missed an attribute in the Pojo
            //init the Pojo
            exceptionMessage = JsonUtil.fromJson(result, ExceptionMessage.class);

            if (exceptionMessage != null && Objects.nonNull(exceptionMessage.getInternalErrorClassCode())) {
                if (ExceptionTypeConstants.EXCEPTION_WITH_DATA_EXCEPTION.equals(exceptionMessage.getInternalErrorClassCode())) {

                    return new WithDataException(exceptionMessage.getMessage(), exceptionMessage.getStatus(), exceptionMessage.getData());
                }
//                else if (ExceptionTypeConstants.EXCEPTION_CUSTOM_PARAMETERIZED_EXCEPTION.equals(exceptionMessage.getInternalErrorClassCode())) {
//                    return JsonUtil.fromJson(result, CustomParameterizedException.class);
//                }
            }

        } catch (IOException ignored) {
            ignored.printStackTrace();
            //ignored
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (exceptionMessage != null) {
            return new CustomParameterizedException(exceptionMessage.getStatus(), exceptionMessage.getTitle(), exceptionMessage.getMessage());
        }
        return errorDecoder.decode(s, response);

    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class ExceptionMessage {

        private Integer internalErrorClassCode;

        private int status;
        private String title;
        private String detail;
        private String message;

        private Object data;

    }
}
