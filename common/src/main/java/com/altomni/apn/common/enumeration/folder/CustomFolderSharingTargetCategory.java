package com.altomni.apn.common.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;
import com.fasterxml.jackson.annotation.JsonValue;

public enum CustomFolderSharingTargetCategory {
    USER(1),
    TEAM(2);

    private final int dbValue;

    CustomFolderSharingTargetCategory(int dbValue) {
        this.dbValue = dbValue;

    }

    public Integer toDbValue() {
        return dbValue;
    }


    public static final ReverseEnumResolver<CustomFolderSharingTargetCategory, Integer> resolver =
            new ReverseEnumResolver<>(CustomFolderSharingTargetCategory.class, CustomFolderSharingTargetCategory::toDbValue);

    public static CustomFolderSharingTargetCategory fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @JsonValue
    public String getValue() {
        return this.name();
    }
}
