package com.altomni.apn.common.auth.agency_auth;

import com.altomni.apn.common.auth.Constants;
import com.altomni.apn.common.dto.user.AgencyLoginUserDTO;
import com.altomni.apn.common.dto.user.TokenAndAgencyLoginUserDTO;
import com.nimbusds.jose.JOSEException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.context.SecurityContextHolderStrategy;
import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.security.oauth2.server.resource.web.DefaultBearerTokenResolver;
import org.springframework.security.web.context.NullSecurityContextRepository;
import org.springframework.security.web.context.SecurityContextRepository;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.ParseException;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
public class AgencyTokenFilter extends OncePerRequestFilter {

    private final AgencyUserTokenStore agencyUserTokenStore;

    private final List<AntPathRequestMatcher> pathRequestMatchers;

    private final BearerTokenResolver bearerTokenResolver = new DefaultBearerTokenResolver();

    private final SecurityContextHolderStrategy securityContextHolderStrategy = SecurityContextHolder
            .getContextHolderStrategy();
    private final SecurityContextRepository securityContextRepository = new NullSecurityContextRepository();

//    public AgencyTokenFilter(AgencyUserTokenStore agencyUserTokenStore) {
//        this.agencyUserTokenStore = agencyUserTokenStore;
//        this.pathRequestMatchers = Stream.of("/**").map(AntPathRequestMatcher::new).toList();
//    }

    public AgencyTokenFilter(AgencyUserTokenStore agencyUserTokenStore, String... pathPatten) {
        this.agencyUserTokenStore = agencyUserTokenStore;
        this.pathRequestMatchers = Stream.of(pathPatten).map(AntPathRequestMatcher::new).toList();
    }

    public AgencyTokenFilter(AgencyUserTokenStore agencyUserTokenStore, List<AntPathRequestMatcher> pathRequestMatchers) {
        this.agencyUserTokenStore = agencyUserTokenStore;
        this.pathRequestMatchers = pathRequestMatchers;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        if (pathRequestMatchers.stream().noneMatch(matcher -> matcher.matches(request))) {
            filterChain.doFilter(request, response);
            return;
        }

        // 从 header 获取 bearer token
        String token = bearerTokenResolver.resolve(request);
        if (StringUtils.isBlank(token)) {
            filterChain.doFilter(request, response);
            return;
        }

        AgencyLoginUserDTO user = null;
        try {
            // 解析 token, 获取用户信息
            TokenAndAgencyLoginUserDTO tokenAndAgencyLoginUserDTO = agencyUserTokenStore.getUser(token);
            if (tokenAndAgencyLoginUserDTO != null) {
                user = tokenAndAgencyLoginUserDTO.getLoginUser();
                token = tokenAndAgencyLoginUserDTO.getToken(); // for agency-module, return token is generated to replace external agency portal token; for non-agency microservice, this token will not be changed;
            }
        } catch (ParseException | JOSEException e) {
//            log.debug("jwt verify error!", e);
            //for sso-token parse token will produce error here, just ignore
        }

        if (user == null) {
            filterChain.doFilter(request, response);
            return;
        }
        // token 解析成功，设置用户的登录状态
        UsernamePasswordAuthenticationToken authenticationResult = new AgencyExternalAuthenticationToken(user, token);
        SecurityContext context = this.securityContextHolderStrategy.createEmptyContext();
        context.setAuthentication(authenticationResult);
        this.securityContextHolderStrategy.setContext(context);
        this.securityContextHolderStrategy.setDeferredContext(() -> context);
        this.securityContextRepository.saveContext(context, request, response);
        // 跳过 oauth2 资源服务器的 token 校验, 因为我们使用自己签发的 token 已经验证过了
        request.setAttribute(Constants.SKIP_OAUTH2_TOKEN_VALIDATION, "true");
        filterChain.doFilter(request, response);
    }


}
