package com.altomni.apn.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import org.apache.commons.validator.routines.EmailValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import javax.servlet.http.HttpServletRequest;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.net.UnknownHostException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.util.List;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.time.temporal.ChronoUnit.DAYS;

public class CommonUtils {

    private static final Logger log = LoggerFactory.getLogger(CommonUtils.class);

    public static final String CHINESE_CHARACTERS = "[\u4e00-\u9fa5]";

    private static final String LINKEDIN_PRE = "https://www.linkedin.com/in/";

    private static final String FACEBOOK_PRE = "https://www.facebook.com/";

    public final static DecimalFormat DECIMAL_FORMAT_PDF_AMOUNT= new DecimalFormat("#,##0.00");

    public final static String USER_LANGUAGE_SET = "Config:UserLanguage:TenantId:";

    public final static String TEAM_USER_TREE_TENANT = "Team:User:Tree:TenantId:";

    public static String userLanguageSetConcat(String uid) {
        if (StrUtil.isBlank(uid)) {
            uid = "-1,-1";
        }
        return CommonUtils.USER_LANGUAGE_SET.concat(uid);
    }

    public static String maskPhone(String phone) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(phone)) {
            return phone;
        }

        phone = phone.replaceAll("[^\\d.]", "");

        if (phone.length() <= 3) {
            return phone;
        }

        return phone.substring(0, 3) + org.apache.commons.lang3.StringUtils.repeat('*', phone.length() - 3);
    }

    public static String maskEmail(String email) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(email)) {
            return email;
        }

        return org.apache.commons.lang3.StringUtils.repeat('*', 7) + email.substring(email.indexOf('@'), email.length());
    }

    public static String maskLinkedin(String linkedIn) {
        if (ObjectUtil.isNull(linkedIn)) {
            return linkedIn;
        }
        return LINKEDIN_PRE + org.apache.commons.lang3.StringUtils.repeat('*', linkedIn.length());
    }

    public static String maskFacebook(String faceBook) {
        if (ObjectUtil.isNull(faceBook)) {
            return faceBook;
        }
        return FACEBOOK_PRE + org.apache.commons.lang3.StringUtils.repeat('*', faceBook.length());
    }

    public static boolean existsKey(JSONObject object, String key) {
        if (object == null) {
            return false;
        }
        return (object.containsKey(key) && object.getStr(key) != null);
    }

    public static String getString(JSONObject object, String key) {
        if (object == null) {
            return null;
        }
        if (existsKey(object, key)) {
            return object.getStr(key);
        }
        return null;
    }

    public static boolean isValidEmail(String email) {
        // create the EmailValidator instance
        EmailValidator validator = EmailValidator.getInstance();

        // check for valid email addresses using isValid method
        return validator.isValid(email);
    }

    public static final String[] first = new String[]{"gmail.com", "hotmail.com", "outlook.com", "yahoo.com", "msn.com", "live.com", "mac.com", "me.com", "icloud.com"};

    public static final String[] second = new String[]{
        "aol.com", "att.net", "comcast.net", "gmx.com", "googlemail.com", "home.nl", "planet.nl", "arcor.de", "hotmail.co.uk",
        "mail.com", "aim.com", "sohu.com", "yahoo.es", "bigpond.com", "optonline.net", "centurytel.net", "chello.nl", "live.ca",
        "bigpond.net.au", "tom.com", "sbcglobal.net", "yahoo.co.uk", "email.com", "fastmail.fm", "games.com", "gmx.net", "hush.com",
        "hushmail.com", "frontiernet.net", "hetnet.nl", "iname.com", "inbox.com", "lavabit.com", "love.com", "pobox.com",
        "protonmail.ch", "protonmail.com", "tutanota.de", "tutanota.com", "tutamail.com", "tuta.io", "zonnet.nl", "club-internet.fr",
        "keemail.me", "rocketmail.com", "safe-mail.net", "wow.com", "ygm.com", "ymail.com", "zoho.com", "yandex.com", "optusnet.com.au",
        "bluewin.ch", "bellsouth.net", "charter.net", "cox.net", "earthlink.net", "juno.com", "btinternet.com", "virginmedia.com",
        "blueyonder.co.uk", "windstream.net", "live.co.uk", "ntlworld.com", "talktalk.co.uk", "tiscali.co.uk", "sky.com", "bt.com",
        "sina.com", "sina.cn", "qq.com", "naver.com", "hanmail.net", "daum.net", "nate.com", "yahoo.co.jp", "yahoo.co.kr", "yahoo.co.id",
        "yahoo.com.sg", "yahoo.com.ph", "yahoo.co.in", "foxmail.com", "163.com", "yeah.net", "126.com", "21cn.com", "aliyun.com",
        "139.com", "263.net", "live.cn", "huiseo.cn", "csoftmail.cn", "189.cn", "hotmail.fr", "live.fr", "laposte.net", "yahoo.fr",
        "wanadoo.fr", "orange.fr", "gmx.fr", "sfr.fr", "neuf.fr", "free.fr", "gmx.de", "hotmail.de", "live.de", "online.de", "t-online.de",
        "web.de", "yahoo.de", "libero.it", "virgilio.it", "hotmail.it", "aol.it", "tiscali.it", "alice.it", "live.it", "yahoo.it", "email.it",
        "tin.it", "poste.it", "teletu.it", "mail.ru", "rambler.ru", "yandex.ru", "ya.ru", "list.ru", "hotmail.be", "live.be", "skynet.be",
        "voo.be", "tvcablenet.be", "telenet.be", "fibertel.com.ar", "hotmail.com.ar", "live.com.ar", "yahoo.com.ar", "speedy.com.ar",
        "arnet.com.ar", "yahoo.com.mx", "live.com.mx", "hotmail.es", "yahoo.ca", "hotmail.ca", "bell.net", "shaw.ca", "sympatico.ca",
        "rogers.com", "uol.com.br", "bol.com.br", "outlook.com.br", "hotmail.com.br", "yahoo.com.br", "terra.com.br", "ig.com.br",
        "r7.com", "zipmail.com.br", "globo.com", "globomail.com", "oi.com.br"};

    public static final Set<String> DIC1 = new HashSet<>(Arrays.asList(first));

    public static final Set<String> DIC2 = new HashSet<>(Arrays.asList(second));

    public static String getDomainName(String email) {
        return email.substring(email.indexOf("@") + 1);
    }

    public static boolean isChineseCharacter(String str) {
        String regEx = "[\\u4e00-\\u9fa5]+";
        Pattern p = Pattern.compile(regEx);
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        } else {
            return false;
        }
    }

    public static BigDecimal convertToHourlyRate(BigDecimal rate, RateUnitType rateUnitType) {
        BigDecimal result;
        if (RateUnitType.YEARLY.equals(rateUnitType)) {
            result = rate.divide(new BigDecimal(2080), 10, RoundingMode.HALF_UP);
        } else if (RateUnitType.MONTHLY.equals(rateUnitType)) {
            result = rate.multiply(new BigDecimal(12)).divide(new BigDecimal(2080), 10, RoundingMode.HALF_UP);
        } else if (RateUnitType.WEEKLY.equals(rateUnitType)) {
            result = rate.divide(new BigDecimal(40), 10, RoundingMode.HALF_UP);
        } else if (RateUnitType.DAILY.equals(rateUnitType)) {
            result = rate.divide(new BigDecimal(8), 10, RoundingMode.HALF_UP);
        } else {
            result = rate;
        }
        return result;
    }

    public static BigDecimal calculateRevenue(BigDecimal rate, RateUnitType rateUnitType, LocalDate startDate, LocalDate endDate, BigDecimal estimatedWorkingHours) {
        BigDecimal hourlyFinalBillRate = CommonUtils.convertToHourlyRate(rate, rateUnitType);
        long noOfDaysBetween = DAYS.between(startDate, endDate) + 1;
        BigDecimal noOfWeeksBetween = new BigDecimal(noOfDaysBetween).divide(new BigDecimal(7), 10, RoundingMode.HALF_UP);
        return hourlyFinalBillRate.multiply(noOfWeeksBetween).multiply(estimatedWorkingHours);
    }

    public static String convertListToString(List<Long> ids) {
        if (CollUtil.isEmpty(ids)){
            return null;
        }
        StringBuilder result = new StringBuilder();
        for (Long id : ids) {
            result.append(",").append(id);

        }
        return result.substring(1);
    }

    public static String convertListToString(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) {return null;}
        StringBuilder result = new StringBuilder();
        for (Long id : ids) {
            result.append(",").append(id);

        }
        return result.substring(1);
    }


    public static List<Long> convertStringToList(String s) {

        List<Long> result = new LinkedList<>();
        if (s == null){
            return result;
        }
        String[] ids = s.split(",");
        for (String id : ids) {
            result.add(Long.parseLong(id));
        }
        return result;
    }

    public static String readFileToString(String path) {
        Resource resource = new ClassPathResource(path);
        InputStream is = null;
        InputStreamReader isr = null;

        try {
            is = resource.getInputStream();
            isr = new InputStreamReader(is);

            BufferedReader br = new BufferedReader(isr);
            String data = null;
            StringBuilder sb = new StringBuilder();
            while ((data = br.readLine()) != null) {
                sb.append(data);
            }
            return sb.toString();
        } catch (Exception e) {
            log.error("error", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                    isr.close();
                } catch (IOException e) {
                    log.error("error", e);
                }
            }
        }
        return null;
    }

    public static String formatFullName(String firstName, String lastName) {
        return (ReUtil.count(CHINESE_CHARACTERS, firstName) > 0 || ReUtil.count(CHINESE_CHARACTERS, lastName) > 0) ? lastName + firstName : firstName + StrUtil.SPACE + lastName;
    }

    public static String formatFullName(String firstName, String lastName, String fullName) {
        if(fullName != null) {
            return fullName;
        }else {
            return (ReUtil.count(CHINESE_CHARACTERS, firstName) > 0 || ReUtil.count(CHINESE_CHARACTERS, lastName) > 0) ? lastName + firstName : firstName + StrUtil.SPACE + lastName;
        }
    }

    public static String formatFullNameWithBlankCheck(String firstName, String lastName) {
        if (StrUtil.isBlank(firstName)) {
            firstName = "";
        }
        if (StrUtil.isBlank(lastName)) {
            lastName = "";
        }
        String fullName = (ReUtil.count(CHINESE_CHARACTERS, firstName) > 0 || ReUtil.count(CHINESE_CHARACTERS, lastName) > 0) ? lastName + firstName : firstName + StrUtil.SPACE + lastName;
        return fullName.trim();
    }

    public static String urlEncodeIgnoreDuplicate(String msg) {
        try {
            String encoded = URLEncoder.encode(msg, "UTF-8");
            if (org.apache.commons.lang3.StringUtils.equals(msg, encoded)) {
                return null;
            } else {
                return encoded;
            }
        } catch (UnsupportedEncodingException e) {
            log.error("[CommonUtils: urlEncodeIgnoreDuplicate] UnsupportedEncodingException when encode: {} with 'UTF-8' Charset", msg);
            return null;
        }
    }

    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            if (ip.indexOf(",") != -1) {
                ip = ip.split(",")[0];
            }
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if ("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    log.error("error", e);
                }
                ip = inet.getHostAddress();
            }
        }
        return ip;
    }

    public static BufferedImage drawTranslucentStringPic(Font font, int width, int height, String drawStr) {
        try {
            // ------------ 关键修改 1：启用抗锯齿和高质量渲染 ------------
            BufferedImage buffImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
            Graphics2D gd = buffImg.createGraphics();

            // 设置抗锯齿和文本渲染优化
            gd.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            gd.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_LCD_HRGB);
            gd.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);

            // ------------ 关键修改 2：动态计算图片尺寸（可选） ------------
            // 如果允许调整尺寸，可注释掉以下逻辑，改为根据文本宽度/高度自动设置 width/height
            FontMetrics metrics = gd.getFontMetrics(font);
            int textWidth = metrics.stringWidth(drawStr);
            int textHeight = metrics.getHeight();

            // 如果传入的 width/height 过小，强制调整为文本实际尺寸（避免压缩）
            if (width < textWidth) width = textWidth + 20; // 增加边距
            if (height < textHeight) height = textHeight + 20;

            // 重新创建适配尺寸的图片（仅在需要调整尺寸时启用）
            if (buffImg.getWidth() != width || buffImg.getHeight() != height) {
                buffImg = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
                gd = buffImg.createGraphics();
                // 重新设置抗锯齿
                gd.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                gd.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_LCD_HRGB);
            }

            // ------------ 透明背景和字体设置 ------------
            gd.setComposite(AlphaComposite.Clear);
            gd.fillRect(0, 0, width, height);
            gd.setComposite(AlphaComposite.SrcOver);
            gd.setFont(font);
            gd.setColor(Color.BLACK);

            // ------------ 居中计算 ------------
            int x = (width - textWidth) / 2;
            int y = (height - textHeight) / 2 + metrics.getAscent();

            gd.drawString(drawStr, x, y);
            gd.dispose();

            return buffImg;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String splitS3LinkFromUrl(String url) {
        if (ObjectUtil.isNull(url)) {
            return null;
        }
        String[] s = url.split("/");
        return s[s.length - 1];
    }

    public static InputStream getResource(String path) {
        Resource resource = new ClassPathResource(path);
        InputStream is = null;
        try {
            is = resource.getInputStream();
        } catch (IOException e) {
            log.error("error", e);
        }
        return is;
    }
    
    public static String formatDecimalwithComma(BigDecimal number){
        return DECIMAL_FORMAT_PDF_AMOUNT.format(number);
    }


}
