package com.altomni.apn.common.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EmailModuleTypeEnum implements ConvertedEnum<Integer> {

    SUBMIT_IPG_TO_APN(1);

    private Integer dbValue;

    EmailModuleTypeEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<EmailModuleTypeEnum, Integer> resolver =
            new ReverseEnumResolver<>(EmailModuleTypeEnum.class, EmailModuleTypeEnum::toDbValue);

    public static EmailModuleTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
