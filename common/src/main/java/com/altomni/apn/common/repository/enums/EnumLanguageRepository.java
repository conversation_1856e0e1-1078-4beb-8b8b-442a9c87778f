package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumLanguage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the Enum job functions entity.
 */
@Repository
public interface EnumLanguageRepository extends JpaRepository<EnumLanguage, Long>, QuerydslPredicateExecutor<EnumLanguage> {

}
