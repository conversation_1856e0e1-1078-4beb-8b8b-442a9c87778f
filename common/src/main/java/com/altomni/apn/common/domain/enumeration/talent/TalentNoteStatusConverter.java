package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TalentNoteStatusConverter extends AbstractAttributeConverter<TalentNoteStatus, Integer> {
    public TalentNoteStatusConverter() {
        super(TalentNoteStatus::toDbValue, TalentNoteStatus::fromDbValue);
    }
}
