package com.altomni.apn.common.dto.xxljob;

import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
public class XxlJobForTalentReminderDTO {

    private Long talentRecruitmentProcessId;

    private Long talentId;

    private String fullName;

    private LocalDate onboardDate;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

}
