package com.altomni.apn.common.errors;

import lombok.Data;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Data
public class ExternalServiceInterfaceException extends AbstractThrowableProblem {

    private static final long serialVersionUID = 1L;

    private Integer code;

    private String message;

    public ExternalServiceInterfaceException() {
        this(ErrorConstants.DEFAULT_TYPE, null, null);
    }

    public ExternalServiceInterfaceException(String message) {
        this(ErrorConstants.DEFAULT_TYPE, message, null);
    }

    public ExternalServiceInterfaceException(String message, Integer code) {
        this(ErrorConstants.DEFAULT_TYPE, message, code);
    }

    public ExternalServiceInterfaceException(URI type, String message, Integer code) {
        super(type, "External Service Interface Exception ", code != null ? Status.valueOf(code) : Status.EXPECTATION_FAILED, null, null, null, getAlertParameters(message, code));
        this.message = message;
        this.code = code;
    }

    private static Map<String, Object> getAlertParameters(String message, Integer code) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("message", message);
        return parameters;
    }

}

