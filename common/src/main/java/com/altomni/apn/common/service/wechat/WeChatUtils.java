package com.altomni.apn.common.service.wechat;


import com.altomni.apn.common.config.env.WechatProperties;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONException;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.wechat.WechatJSAPIConfig;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.http.HttpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Console;
import java.io.IOException;
import java.io.PrintStream;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Formatter;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
@Service
public class WeChatUtils {


    private final static String JSAPI_TICKET_KEY = "Config:Wechat:APITicket";

    private final static String WECHAT_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=APPID&secret=APPSECRET";

    private final static String WECHAT_STABLE_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/stable_token";

    private final static String WECHAT_JSAPI_TICKET_URL = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=ACCESS_TOKEN&type=jsapi";

    private final static Integer SUCCESS_ERROR_CODE = 0; // means success in wechat service

    @Resource
    private HttpService httpService;

    @Resource
    private WechatProperties wechatProperties;

    @Resource
    private CommonRedisService commonRedisService;


    public WechatJSAPIConfig getWechatSDKConfig(String url) {

        String apiTicket = getJSAPITicket();
        Long timestamp = (System.currentTimeMillis() / 1000);
        String nonceStr = generateNonceStr(16);

        String signature = generateAPIConfigSignature(nonceStr, apiTicket, timestamp.toString(), url);

        WechatJSAPIConfig wechatJSAPIConfig = new WechatJSAPIConfig();
        wechatJSAPIConfig.setUrl(url);
        wechatJSAPIConfig.setTimestamp(timestamp.toString());
        wechatJSAPIConfig.setAppId(wechatProperties.getOfficialAccountAppid());
        wechatJSAPIConfig.setNonceStr(nonceStr);
        wechatJSAPIConfig.setSignature(signature);

        return wechatJSAPIConfig;

    }


    public String generateAPIConfigSignature(String nonceStr, String jsapiTicket, String timestamp, String url) {
        try {

            Map<String, String> params = new TreeMap<>();
            params.put("jsapi_ticket", jsapiTicket);
            params.put("noncestr", nonceStr);
            params.put("timestamp", timestamp);
            params.put("url", url);

            StringBuilder stringBuilder = new StringBuilder();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                stringBuilder.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            String string1 = stringBuilder.toString().substring(0, stringBuilder.length() - 1); // Remove the trailing "&"

            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(string1.getBytes("UTF-8"));
            byte[] signatureBytes = crypt.digest();

            // Convert the byte array to a hexadecimal string
            Formatter formatter = new Formatter();
            for (byte b : signatureBytes) {
                formatter.format("%02x", b);
            }
            String signature = formatter.toString();
            formatter.close();


            MessageDigest md = MessageDigest.getInstance("SHA-1");

            // Update the MessageDigest with the bytes of the string
            md.update(string1.getBytes());

            // Perform the hash computation and get the result as a byte array
            byte[] result = md.digest();

            // Convert the byte array to a hex string
            StringBuilder sb = new StringBuilder();
            for (byte b : result) {
                sb.append(String.format("%02x", b));
            }

            System.out.println(sb.toString());
            return signature;
        } catch (java.io.UnsupportedEncodingException e) {
            e.printStackTrace();
            log.error("[APN WeChat Utils]error: {}", e );
            return null;
        } catch (NoSuchAlgorithmException e) {
            log.error("[APN WeChat Utils]error: {}", e );
            throw new RuntimeException(e);
        }
    }


    public static String generateNonceStr(int length) {
        boolean useLetters = true;
        boolean useNumbers = true;
        String generatedString = RandomStringUtils.random(length, useLetters, useNumbers);

        //System.out.println(generatedString);
        return generatedString;
    }


    private String getWechatAccessToken(String appId, String secret) {
        String url = WECHAT_ACCESS_TOKEN_URL.replace("APPID", appId).replace("APPSECRET", secret);
        try {
            HttpResponse response = httpService.get(url);

            if (response.getCode() == 200) {

                JSONObject jsonResponse = JSONUtil.parseObj(response.getBody());
                if (jsonResponse.containsKey("errcode")) {
                    int errorCode = jsonResponse.getInt("errcode");
                    String errorMessage = jsonResponse.getStr("errmsg");
                    log.error("[APN WeChat Utils] Failed to retrieve WeChat access token. Wechat Service Error status code: {}, Body: {}", errorCode, errorMessage );
                    return null;
                }


                String accessToken = jsonResponse.getStr("access_token");
                int expiresIn = jsonResponse.getInt("expires_in");

                return accessToken;
            } else {

                log.error("[APN WeChat Utils] Failed to retrieve WeChat Access Token. HTTP status code: {}, Body: {}", response.getCode(), response.getBody() );
                return null;
            }

        } catch (IOException e) {
            log.error("[APN WeChat Utils]error: {}", e);
            throw new RuntimeException(e.getMessage());
        }
    }


    private String getJSAPITicket() {
        String apiTicket = commonRedisService.get(JSAPI_TICKET_KEY);
        if (apiTicket != null) {
            return apiTicket;
        }

        apiTicket = getJSAPITicketKeyByWechatAPI();
        commonRedisService.set(JSAPI_TICKET_KEY, apiTicket, wechatProperties.getJSAPITicketExpireTime());

        return apiTicket;

    }


    private String getJSAPITicketKeyByWechatAPI() {
        String appId = wechatProperties.getOfficialAccountAppid();
        String secret = wechatProperties.getOfficialAccountSecret();
        String accessToken = getWechatStableAccessToken(appId, secret);
        String apiTicketURL = WECHAT_JSAPI_TICKET_URL.replace("ACCESS_TOKEN", accessToken);
        try {
            HttpResponse response = httpService.get(apiTicketURL);
            if (response.getCode() == 200) {

                JSONObject jsonResponse = JSONUtil.parseObj(response.getBody());
                if (jsonResponse.containsKey("errcode") && !jsonResponse.getInt("errcode").equals(SUCCESS_ERROR_CODE)) {
                    int errorCode = jsonResponse.getInt("errcode");
                    String errorMessage = jsonResponse.getStr("errmsg");
                    log.error("[APN WeChat Utils] Failed to retrieve WeChat JS API token. Wechat Service Error status code: {}, Body: {}", errorCode, errorMessage );
                    return null;
                }

                String apiTicket = jsonResponse.getStr("ticket");
                int expiresIn = jsonResponse.getInt("expires_in");

                return apiTicket;
            } else {
                log.error("[APN WeChat Utils] Failed to retrieve WeChat JS API token. HTTP status code: {}, Body: {}", response.getCode(), response.getBody() );
                return null;
            }

        } catch (IOException ex) {
            log.error("[APN WeChat Utils] JsAPI Ticket error: {}", ex );
            throw new CustomParameterizedException("Unable to fetch a valid Wechat API Ticket.");
        }
    }


    private String getWechatStableAccessToken(String appId, String secret) {
        try {
            String requestBody = generateStableAccessTokenRequestBody(appId, secret, true).toString();
            HttpResponse response = httpService.post(WECHAT_STABLE_ACCESS_TOKEN_URL, requestBody);

            if (response.getCode() == 200) {

                JSONObject jsonResponse = JSONUtil.parseObj(response.getBody());
                if (jsonResponse.containsKey("errcode") && !jsonResponse.getInt("errcode").equals(SUCCESS_ERROR_CODE)) {
                    int errorCode = jsonResponse.getInt("errcode");
                    String errorMessage = jsonResponse.getStr("errmsg");
                    log.error("[APN WeChat Utils] Failed to retrieve WeChat Stable access token. Wechat Service Error status code: {}, Body: {}", errorCode, errorMessage );
                    return null;
                }

                String accessToken = jsonResponse.getStr("access_token");
                int expiresIn = jsonResponse.getInt("expires_in");

                return accessToken;
            } else {
                log.error("[APN WeChat Utils] Failed to retrieve WeChat Stable access token. HTTP status code: {}, Body: {}", response.getCode(), response.getBody() );

                return null;
            }
        } catch (IOException e) {
            log.error("[APN WeChat Utils]Stable access token error: {}", e );
            throw new CustomParameterizedException("Unable to connect with Wechat Service");

        }
    }


    public JSONObject generateStableAccessTokenRequestBody(String appId, String secret, boolean forceRefresh) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("grant_type", "client_credential");
        requestBody.put("appid", appId);
        requestBody.put("secret", secret);
        requestBody.put("force_refresh", forceRefresh);

        return requestBody;
    }


}
