package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * group invoice polymerization type enum
 */
public enum GroupType implements ConvertedEnum<Integer> {

    COMPANY(1, "Company"),
    EMPLOYEE(2, "Employee");
    private final int dbValue;

    private final String description;

    GroupType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<GroupType, Integer> resolver = new ReverseEnumResolver<>(GroupType.class, GroupType::toDbValue);

    public static GroupType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
