package com.altomni.apn.common.domain.enumeration.address;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum AddressType implements ConvertedEnum<Integer> {
    COMPANY(0),
    TALENT(1),
    JOB(2),
    CLIENT_CONTACT(3),
    TENANT(4);

    private final int dbValue;

    AddressType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<AddressType, Integer> resolver = new ReverseEnumResolver<>(AddressType.class, AddressType::toDbValue);

    public static AddressType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
