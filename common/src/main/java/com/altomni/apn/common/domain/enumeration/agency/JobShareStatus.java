package com.altomni.apn.common.domain.enumeration.agency;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ActiveStatus enumeration.
 */
public enum JobShareStatus implements ConvertedEnum<Integer> {

    /**
     * 有效
     */
    ACTIVE(1),

    /**
     * 无效
     */
    INACTIVE(0);

    private final int dbValue;

    JobShareStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<JobShareStatus, Integer> resolver =
        new ReverseEnumResolver<>(JobShareStatus.class, JobShareStatus::toDbValue);

    public static JobShareStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
