package com.altomni.apn.common.datapermission.rule.team;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.datapermission.rule.DataPermissionRule;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.utils.HibernateUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.*;
import net.sf.jsqlparser.expression.operators.conditional.OrExpression;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.InExpression;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * The implement of DataPermissionRule
 * <AUTHOR>
 */
@NoArgsConstructor
@Slf4j
@Component
public class TeamDataPermissionRule implements DataPermissionRule, Serializable {

    private static final long serialVersionUID = 1L;

    static final Expression EXPRESSION_NULL = new NullValue();

    /**
     * Hold the key:value, ensure which column stands for team owner
     * key：table name
     * value：column name
     * e.g. {job: team_id, company: team_id}
     */
    private final Map<String, String> teamColumns = new HashMap<>();

    /**
     * Hold the key:value, ensure which column stands for user owner
     * key：table name
     * value：column name
     * e.g. {job: user_id, company: user_id}
     */
    private final Map<String, String> userColumns = new HashMap<>();

    /**
     * All the tables that got involved in data security, the union set of deptColumns' keys and userColumns' keys
     */
    private final Set<String> TABLE_NAMES = new HashSet<>();

    @Override
    public Set<String> getTableNames() {
        return TABLE_NAMES;
    }

    @Override
    public Expression getExpression(String tableName, Alias tableAlias, boolean isSelect, TeamDataPermissionRespDTO teamDataPermission) {
        // Only deal with the situation when the user logged in
        Long loginUserId = SecurityUtils.getUserId();
        if (loginUserId.equals(-1L)) {
            return null;
        }

        // Get data permission
        if (teamDataPermission == null) {
            log.error("[getExpression][LoginUser({}) 获取数据权限为 null]", SecurityUtils.getCurrentUserLogin());
            throw new NullPointerException(String.format("LoginUser(%d) Table(%s/%s) 未返回数据权限",
                    SecurityUtils.getUserId(), tableName, tableAlias.getName()));
        }
        log.info("DataPermission ({}) = {}", SecurityUtils.getUserId(), teamDataPermission);

        // Case 1, if the user has all the permission, then no need to rewrite sql
        if (teamDataPermission.getAll()) {
            return null;
        }

        // Case 2, if the user does not have teams permission nor self permission, then he/she does not have any permission
        if (CollUtil.isEmpty(teamDataPermission.getReadableTeamIds())
                && Boolean.FALSE.equals(teamDataPermission.getSelf())
        ) {
            // WHERE null = null，return nothing
            return new EqualsTo(null, null);
        }

        // Case 3, combine teams' permission and user's permission

        Expression teamExpression = buildTeamExpression(tableName, tableAlias, isSelect ? teamDataPermission.getReadableTeamIds() : teamDataPermission.getWritableTeamIds(), loginUserId, teamDataPermission.getSelfPermissionIds());
        Expression userExpression = buildUserExpression(tableName, tableAlias, teamDataPermission.getSelf(), loginUserId, teamDataPermission.getSelfPermissionIds());
        if (teamExpression == null && userExpression == null) {
            // TODO Return nothing instead of throwing exception when Expression is empty
            log.warn("[getExpression][LoginUser({}) Table({}/{}) DeptDataPermission({}) Built Expression is empty]",
                    SecurityUtils.getCurrentUserLogin(), tableName, tableAlias, JSON.toJSON(teamDataPermission));
            // throw new NullPointerException(String.format("LoginUser(%d) Table(%s/%s) Built Expression is empty",
            //        loginUser.getId(), tableName, tableAlias.getName()));
            return EXPRESSION_NULL;
        }

        /*if (teamExpression == null) {
            return userExpression;
        }*/

        /*
        if (userExpression == null) {
            return deptExpression;
        }
        // If we have more complex combination in the future, like, assigned teams + self(read-only).
        // We can use OR condition, e.g. WHERE team_id IN ? OR user_id = ?
        return new OrExpression(teamExpression, userExpression);
        */

        return ObjectUtils.firstNonNull(teamExpression, userExpression);
    }

    /**
     * Build expression with team owner filters
     * @param tableName
     * @param tableAlias
     * @param deptIds
     * @return
     */
    private Expression buildTeamExpression(String tableName, Alias tableAlias, Set<Long> deptIds, Long userId, Set<Long> selfPermissionIds) {
        String columnName = teamColumns.get(tableName);
        if (StrUtil.isEmpty(columnName)) {
            return null;
        }
        if (CollUtil.isEmpty(deptIds)) {
            return null;
        }
        // Combine expressions
        // 本team的数据
        Expression teamIn = new InExpression(HibernateUtils.buildColumn(tableAlias, columnName),
                new ExpressionList(deptIds.stream().map(LongValue::new).collect(Collectors.toList())));

        // 本team或本人为岗位负责人的数据
        Expression teamExp = CollUtil.isEmpty(selfPermissionIds) ?
                teamIn :
                new Parenthesis(new OrExpression(teamIn,
                        new InExpression(HibernateUtils.buildColumn(tableAlias, "id"),
                                new ExpressionList(selfPermissionIds.stream().map(LongValue::new).collect(Collectors.toList())))));

        String userColumnName = userColumns.get(tableName);
        if (StrUtil.isEmpty(userColumnName)) {
            return teamExp;
        }
        // 本人创建的数据
        Expression puserIdEquals = new EqualsTo(HibernateUtils.buildColumn(tableAlias, userColumnName), new LongValue(userId));

        return new Parenthesis(new OrExpression(puserIdEquals, teamExp));
    }

    /**
     * Build expression with user owner filters
     * @param tableName
     * @param tableAlias
     * @param self
     * @param userId
     * @return
     */
    private Expression buildUserExpression(String tableName, Alias tableAlias, Boolean self, Long userId, Set<Long> selfPermissionIds) {
        if (Boolean.FALSE.equals(self)) {
            return null;
        }
        String columnName = userColumns.get(tableName);
        if (StrUtil.isEmpty(columnName)) {
            return null;
        }
        Expression puserIdEquals = new EqualsTo(HibernateUtils.buildColumn(tableAlias, columnName), new LongValue(userId));
        return CollUtil.isEmpty(selfPermissionIds) ?
                puserIdEquals :
                new Parenthesis(new OrExpression(puserIdEquals,
                        new InExpression(HibernateUtils.buildColumn(tableAlias, "id"),
                                new ExpressionList(selfPermissionIds.stream().map(LongValue::new).collect(Collectors.toList())))));
    }

    public void addTeamColumn(String tableName, String columnName) {
        tableName = tableName.toLowerCase();
        teamColumns.put(tableName, columnName);
        TABLE_NAMES.add(tableName);
    }

    public void addUserColumn(String tableName, String columnName) {
        tableName = tableName.toLowerCase();
        userColumns.put(tableName, columnName);
        TABLE_NAMES.add(tableName);
    }

    public void clear(){
        TABLE_NAMES.clear();
        userColumns.clear();
        teamColumns.clear();
    }
}
