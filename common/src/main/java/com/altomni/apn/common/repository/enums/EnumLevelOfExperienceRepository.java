package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumLanguage;
import com.altomni.apn.common.domain.dict.EnumLevelOfExperience;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the Enum Level Of Experience entity.
 */
@Repository
public interface EnumLevelOfExperienceRepository extends JpaRepository<EnumLevelOfExperience, Long> {

}
