package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.dict.EnumLanguage;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.DisplayType;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EnumLanguageService {

    private final Logger log = LoggerFactory.getLogger(EnumLanguageService.class);

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    CommonRedisService commonRedisService;

    private static final String POPULAR_LANGUAGE = "Popular Language";
    private static final String POPULAR_LANGUAGE_CN = "常用语言";

    private static final String FULL_LANGUAGE_LIST = "Full Language List";
    private static final String FULL_LANGUAGE_LIST_CN = "全部语言";

    private static final String POPULAR_LANGUAGE_ID = "-1";

    private static final String FULL_LANGUAGE_LIST_ID = "-2";

    public List<EnumDictDTO> findAllOrderBySortType(SortType type) {
        log.info("[APN: EnumLanguageService @{}] request to get languages enum data, type: {}", SecurityUtils.getUserId(), type);
        List<EnumLanguage> enumLanguageList = enumCommonService.findAllEnumLanguages();
        List<EnumLanguage> result = new ArrayList<>();
        boolean cnLabel = false;
        if (SortType.EN.equals(type)) {
            CollectionUtils.addAll(result, enumLanguageList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumLanguage::getEnDisplayOrder)).collect(Collectors.toList()));
        } else {
            CollectionUtils.addAll(result, enumLanguageList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumLanguage::getCnDisplayOrder)).collect(Collectors.toList()));
            cnLabel = true;
        }
        if (CollectionUtil.isNotEmpty(result)) {
            if (cnLabel) {
                result.forEach(r -> r.setCnLable(true));
            }else{
                result.forEach(r->r.setCnLable(false));
            }
            //format children node for webSide
            List<EnumDictDTO> languageList = new ArrayList<>();
            //Popular Language
            EnumDictDTO popularLanguage = new EnumDictDTO();
            popularLanguage.setId(StrUtil.toString(POPULAR_LANGUAGE_ID));
            popularLanguage.setLabel(cnLabel ? POPULAR_LANGUAGE_CN : POPULAR_LANGUAGE);
            popularLanguage.setParentId(Constants.PARENT_NODE_ID);
            popularLanguage.setChildren(result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay()) && s.getEnDisplayOrder() < 0).map(a -> {
                EnumDictDTO dto = new EnumDictDTO();
                if (ObjectUtil.isNotEmpty(a.getId())) {
                    dto.setId(StrUtil.toString(a.getId()));
                }
                if (a.getCnLable()) {
                    dto.setLabel(a.getCnDisplay());
                } else {
                    dto.setLabel(a.getEnDisplay());
                }
                if (ObjectUtil.isNotEmpty(a.getId())) {
                    dto.setValue(StrUtil.toString(a.getId()));
                }
                dto.setParentId(POPULAR_LANGUAGE_ID);
                dto.setChecked(false);
                dto.setName(a.getName());
                return dto;
            }).collect(Collectors.toList()));
            //Full Language List
            EnumDictDTO fullLanguage = new EnumDictDTO();
            fullLanguage.setId(FULL_LANGUAGE_LIST_ID);
            fullLanguage.setLabel(cnLabel ? FULL_LANGUAGE_LIST_CN : FULL_LANGUAGE_LIST);
            fullLanguage.setParentId(Constants.PARENT_NODE_ID);
            fullLanguage.setChildren(result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay()) && s.getEnDisplayOrder() > 0).map(a -> {
                EnumDictDTO dto = new EnumDictDTO();
                if (ObjectUtil.isNotEmpty(a.getId())) {
                    dto.setId(StrUtil.toString(a.getId()));
                }
                if (a.getCnLable()) {
                    dto.setLabel(a.getCnDisplay());
                } else {
                    dto.setLabel(a.getEnDisplay());
                }
                if (ObjectUtil.isNotEmpty(a.getId())) {
                    dto.setValue(StrUtil.toString(a.getId()));
                }
                dto.setParentId(FULL_LANGUAGE_LIST_ID);
                dto.setChecked(false);
                dto.setName(a.getName());
                return dto;
            }).collect(Collectors.toList()));
            languageList.add(popularLanguage);
            languageList.add(fullLanguage);
            return languageList;
        } else {
            return new ArrayList<>();
        }
    }

    public List<String> transferLanguagesByIds(List<String> ids) {
        log.info("[APN: EnumLanguageService @{}] request to transfer languages by ids, ids: {}", SecurityUtils.getUserId(), ids);
        if (CollectionUtil.isEmpty(ids)) {
            return null;
        }
        List<EnumLanguage> enumLanguageList = enumCommonService.findAllEnumLanguages();
        List<EnumLanguage> list = enumLanguageList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).collect(Collectors.toList());
        return list.stream().map(EnumLanguage::getName).distinct().collect(Collectors.toList());
    }

    public Map<Long, String> getLanguageMap(){
        return enumCommonService.findAllEnumLanguages().stream().collect(Collectors.toMap(EnumLanguage::getId, EnumLanguage::getName));
    }

    public List<String> getLanguagesByIds(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return null;
        }

        List<EnumLanguage> enumLanguageList = enumCommonService.findAllEnumLanguages();

        return enumLanguageList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).map(EnumLanguage::getName).collect(Collectors.toList());
    }

    public List<Long> transferLanguagesByNamesToId(Object nameObject) {
        log.info("[APN: EnumLanguageService @{}] request to transfer languages by itemTier, nameObject: {}", SecurityUtils.getUserId(), nameObject);
        if (ObjectUtil.isEmpty(nameObject)) {
            return null;
        }

        List<EnumLanguage> enumLanguageList = enumCommonService.findAllEnumLanguages();

        List<EnumLanguage> list = new ArrayList<>();
        if (nameObject instanceof String) {
            list = enumLanguageList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && ObjectUtil.toString(nameObject).equals(o.getName())).collect(Collectors.toList());
        }
        if (nameObject instanceof List || nameObject instanceof String[]) {
            list = enumLanguageList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && Convert.toSet(String.class, nameObject).contains(o.getName())).collect(Collectors.toList());
        }
        return list.stream().map(EnumLanguage::getId).collect(Collectors.toList());
    }

    public List<Long> transferLanguagesByNamesToIdWithoutIgnoreParentClass(cn.hutool.json.JSONArray names) {
        if (ObjectUtil.isEmpty(names)) {
            return null;
        }

        List<EnumLanguage> enumLanguageList = enumCommonService.findAllEnumLanguages();

        Set<String> formatList = Convert.toSet(String.class, names);
        List<EnumLanguage> list = enumLanguageList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && formatList.contains(o.getName())).collect(Collectors.toList());

        return list.stream().map(EnumLanguage::getId).collect(Collectors.toList());
    }

    public List<String> transferLanguagesByNames(Object itemTiers) {
        if (ObjectUtil.isEmpty(itemTiers)) {
            return null;
        }

        List<EnumLanguage> enumLanguageList = enumCommonService.findAllEnumLanguages();

        List<EnumLanguage> list = new ArrayList<>();
        if (itemTiers instanceof String) {
            list = enumLanguageList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && ObjectUtil.toString(itemTiers).equals(o.getName())).collect(Collectors.toList());
        }
        if (itemTiers instanceof List) {
            List<String> itemList = Convert.toList(String.class, itemTiers);
            String itemJson = JSONUtil.toJsonStr(itemTiers);
            Set<String> formatList = itemList.stream().distinct().filter(s -> !itemJson.contains(s + StrUtil.DOT)).collect(Collectors.toSet());
            list = enumLanguageList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && formatList.contains(o.getName())).collect(Collectors.toList());
        }
        return list.stream().map(EnumLanguage::getName).collect(Collectors.toList());
    }

    public List<String> getLanguagesUINameByIds(String ids) {
        log.info("[APN: EnumLanguageService @{}] request to get languages UINames by ids, nameObject: {}", SecurityUtils.getUserId(), ids);
        Set<String> idList = Convert.toSet(String.class, JSONUtil.parseArray(ids));
        if (CollectionUtil.isEmpty(idList)) {
            return null;
        }

        List<EnumLanguage> enumLanguageList = enumCommonService.findAllEnumLanguages();

        List<EnumLanguage> list = enumLanguageList.stream().filter(o -> idList.contains(String.valueOf(o.getId()))).collect(Collectors.toList());
        return list.stream().map(EnumLanguage::getEnDisplay).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
    }

    public Map<String, Long> getLanguagesNameToIdMap() {
        log.info("[APN: EnumLanguageService @{}] request to get languages NameToId map", SecurityUtils.getUserId());

        List<EnumLanguage> enumLanguageList = enumCommonService.findAllEnumLanguages();

        Map<String, Long> map = enumLanguageList.stream().collect(Collectors.toMap(
                enumLanguage -> enumLanguage.getName(),
                enumLanguage -> enumLanguage.getId()
        ));
        return map;
    }

    /***
     * Activity Change function: get the UI name through the enumId;
     * @param id
     * @param displayType
     * @return
     */
    public String getLanguagesUINameById(Long id, DisplayType displayType) {
        log.info("[APN: EnumLanguageService @{}] request to get languages UINames by ids, nameObject: {}", SecurityUtils.getUserId(), id);

        Map<Long,EnumLanguage> enumLanguageMap = enumCommonService.findAllEnumLanguagesMap();

        if(enumLanguageMap.containsKey(id)) {
            EnumLanguage enumLanguage = enumLanguageMap.get(id);
            if(displayType.equals(DisplayType.EN) && StringUtils.isNotBlank(enumLanguage.getEnDisplay())) {
                return enumLanguage.getEnDisplay();
            } else if (displayType.equals(DisplayType.CN) && StringUtils.isNotBlank(enumLanguage.getCnDisplay())) {
                return enumLanguage.getCnDisplay();
            } else {
                return enumLanguage.getName();
            }
        }
        return "Unknown Languages";
    }

    public Set<Long> findValidEnumLanguagesIds(){
        List<EnumLanguage> enumLanguageList = enumCommonService.findAllEnumLanguages();
        return enumLanguageList.stream().map(EnumLanguage::getId).collect(Collectors.toSet());
    }

    /**
     * 设置当前登录用户语言选择
     * @param language
     */
    public void setLanguages(String language){
        if(!language.equals("EN") && !language.equals("CN")){
            log.info("[APN: set languages @{}] Unsupported language type, type:{}", SecurityUtils.getUserId(), language);
            return;
        }
        commonRedisService.set(CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),language);
    }

}
