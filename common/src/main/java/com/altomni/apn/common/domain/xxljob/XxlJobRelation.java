package com.altomni.apn.common.domain.xxljob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnumConverter;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.Instant;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "xxl_job_relation")
public class XxlJobRelation extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "reference_id")
    private Long referenceId;

    @Column(name = "type")
    @Convert(converter = XxlJobRelationTypeEnumConverter.class)
    private XxlJobRelationTypeEnum type;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "xxl_job_id")
    private Integer xxlJobId;

    @Column(name = "xxl_job_param")
    private String xxlJobParam;

    @Column(name = "send_time")
    private Instant sendTime;

    @Column(name = "reminder_config")
    private String reminderConfig;

    @Column(name = "timezone")
    private String timezone;

    public XxlJobRelation(XxlJobApnDTO xxlJobApnDTO, Integer xxlJobId) {
        this.xxlJobId = xxlJobId;
        this.tenantId = xxlJobApnDTO.getXxlJobApnParamDTO().getTenantId();
        this.referenceId = xxlJobApnDTO.getXxlJobApnParamDTO().getReferenceId();
        this.type = xxlJobApnDTO.getXxlJobApnParamDTO().getXxlJobType();
        this.userId = xxlJobApnDTO.getXxlJobApnParamDTO().getUserId();
        this.sendTime = xxlJobApnDTO.getXxlJobApnParamDTO().getSendTime();
        this.reminderConfig = xxlJobApnDTO.getXxlJobApnParamDTO().getReminderConfig();
        this.timezone = xxlJobApnDTO.getXxlJobApnParamDTO().getTimezone();
        this.xxlJobParam = CollUtil.isEmpty(xxlJobApnDTO.getXxlJobApnParamDTO().getXxlJobParam())? null: JSONUtil.toJsonStr(xxlJobApnDTO.getXxlJobApnParamDTO().getXxlJobParam());
    }
}
