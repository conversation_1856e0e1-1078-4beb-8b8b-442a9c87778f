package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 finance-service
 * <AUTHOR>
 */
public enum JobAPIMultilingualEnum {

    ELASTIC_GETJOBCATEGORYCOUNT_RESPONSENULL("elastic_getJobCategoryCount_responseNull"),

    ELASTIC_GETJOBCATEGORYCOUNT_RESPONSESTATUSNOTOK("elastic_getJobCategoryCount_responseStatusNotOk"),

    JOB_SYNCFROMTHIRDPARTY_CODENULL("job_syncFromThirdParty_codeNull"),

    JOB_GETIPGTOKEN_RESPONSESTATUSNOTOK("job_getIpgToken_responseStatusNotOk"),

    JOB_VALIDATECLIENTCONTACT_CLIENTCONTACTNULL("job_validateClientContact_clientContactNull"),

    JOB_VALIDATECLIENTCONTACT_COMPANYNULL("job_validateClientContact_companyNull"),

    JO<PERSON>_COMMON_NOPERMISSION("job_common_noPermission"),

    JOB_COMMON_INVALIDUSER("job_common_invalidUser"),

    J<PERSON>B_GETFOLDER_FAILEDFETCHFOLDER("job_getFolder_failedFetchFolder"),

    JOB_GETFOLDER_FAILEDSHAREFETCHFOLDER("job_getFolder_failedShareFetchFolder"),

    JOB_CREATEJOBFOLDER_COMMONFOLDERDTONULL("job_createJobFolder_commonFolderDtoNull"),

    JOB_CREATEJOBFOLDER_COMMONFOLDERNULL("job_createJobFolder_commonFolderNull"),

    JOB_DELETEJOBFOLDER_FOLDERNULL("job_deleteJobFolder_folderNull"),

    JOB_DELETEJOBFOLDER_NOPERMISSION("job_deleteJobFolder_noPermission"),

    JOB_ADDJOBSTOFOLDERS_NOPERMISSION("job_addJobsToFolders_noPermission"),

    JOB_ADDJOBFOLDERSHARINGFORTEAMS_NOPERMISSION("job_addJobFolderSharingForTeams_noPermission"),

    JOB_ADDJOBFOLDERSHARINGFORUSERS_NOPERMISSION("job_addJobFolderSharingForUsers_noPermission"),

    JOB_REMOVESHARINGFORSHAREDFOLDER_NOSHARE("job_removeSharingForSharedFolder_noShare"),

    JOB_FETCHALLFOLDERSIFEXIST_INVALIDFOLDER("job_fetchAllFoldersIfExist_invalidFolder"),

    JOB_VALIDATEJOBS_INVALIDJOB("job_validateJobs_invalidJob"),

    JOB_CHECKALLJOBSUNDERCURRENTFOLDER_INVALIDJOB("job_checkAllJobsUnderCurrentFolder_invalidJob"),

    JOB_SETSEARCHCUSTOMFOLDERSQL_NOSHAREFOLDER("job_setSearchCustomFolderSql_noShareFolder"),

    JOBNOTE_CREATE_IDNULL("jobNote_create_idNull"),

    JOBNOTE_CREATE_JOBIDNULL("jobNote_create_jobIdNull"),

    JOBNOTE_CREATE_NOTENULL("jobNote_create_noteNull"),

    JOBNOTE_CREATE_ALERTTIMENULL("jobNote_create_alertTimeNull"),

    JOBNOTE_UPDATE_NOTEXIST("jobNote_update_notExist"),

    JOBNOTE_UPDATE_NOPERMISSION("jobNote_update_noPermission"),

    JOBNOTE_SEARCHJOBNOTEBYKEYWORD_DTONULL("jobNote_searchJobNoteByKeyword_dtoNull"),

    JOBNOTE_SEARCHJOBNOTEBYKEYWORD_USERERROR("jobNote_searchJobNoteByKeyword_userError"),

    JOBQUESTION_CREATE_IDNOTNULL("jobQuestion_create_idNotNull"),

    JOBQUESTION_UPDATE_NOTFIND("jobQuestion_update_notFind"),

    JOBRELATION_SAVEJOBASSIGNEDUSERS_USERNULL("jobRelation_saveJobAssignedUsers_userNull"),

    JOBRELATION_SAVEJOBASSIGNEDUSERS_NOPERMISSION("jobRelation_saveJobAssignedUsers_noPermission"),

    JOBSEARCHFOLDER_SEARCH_PARAMNULL("JobSearchFolder_search_paramNull"),

    JOBSEARCHFOLDER_CREATEJOBSEARCHFOLDER_EXIST("JobSearchFolder_createJobSearchFolder_exist"),

    JOBSEARCHFOLDER_CREATEJOBSEARCHFOLDER_NOPERMISSION("JobSearchFolder_createJobSearchFolder_noPermission"),

    JOBSEARCHFOLDER_UPDATEJOBSEARCHFOLDER_EXIST("JobSearchFolder_updateJobSearchFolder_exist"),

    JOBSEARCHFOLDER_UPDATEJOBSEARCHFOLDER_NOTFIND("JobSearchFolder_updateJobSearchFolder_notFind"),

    JOBSEARCHFOLDER_DELETEJOBSEARCHFOLDER_IDNULL("JobSearchFolder_deleteJobSearchFolder_idNull"),

    JOB_VALIDATEINPUTVALUE_COMPANYPARAMNULL("job_validateInputValue_companyParamNull"),

    JOB_VALIDATEINPUTVALUE_COMPANYNULL("job_validateInputValue_companyNull"),

    JOB_VALIDATEINPUTVALUE_JOBTITLENULL("job_validateInputValue_jobTitleNull"),

    JOB_VALIDATEINPUTVALUE_RECRUITMENTPROCESSNULL("job_validateInputValue_recruitmentProcessNull"),

    JOB_VALIDATEINPUTVALUE_CONTACTBRIEFNULL("job_validateInputValue_contactBriefNull"),

    JOB_VALIDDATEINPUTVALUE_CONTRACTDURATION_WRONG("job_validateInputValue_contract"),

    JOB_CHECKENUMVALID_MINIMUMDEGREEIDNULL("job_checkEnumValid_minimumDegreeIdNull"),

    JOB_SAVEJOBASSIGNEDUSERS_USERNULL("job_saveJobAssignedUsers_userNull"),

    JOB_SAVEJOBASSIGNEDUSERS_NOPERMISSION("job_saveJobAssignedUsers_noPermission"),

    JOB_VALIDATESEARCHJOB_NOPERMISSION("job_validateSearchJob_noPermission"),

    JOB_TRANSLATE_RECRUITMENTPROCESSNULL("job_translate_recruitmentProcessNull"),

    JOB_TRANSLATE_NOPERMISSION("job_translate_noPermission"),

    JOB_TRANSLATE_JOBTYPENULL("job_translate_jobTypeNull"),

    JOB_TRANSLATE_BILLRATEEXIST("job_translate_billRateExist"),

    JOB_TRANSLATE_JOBOPENLIMIT("job_translate_jobOpenLimit"),

    JOB_TRANSLATE_MAXSUBMISSIONSLIMIT("job_translate_maxSubmissionsLimit"),

    JOB_UPDATE_NOPERMISSION("job_update_noPermission"),

    JOB_REMOVEENUMSNULL_JOBFUNCTIONVALUEVALID("job_removeEnumsNull_jobFunctionValueValid"),

    JOB_REMOVEENUMSNULL_LANGUAGEVALUEVALID("job_removeEnumsNull_languageValueValid"),

    JOB_REMOVEENUMSNULL_PREFERREDLANGUAGEVALUEVALID("job_removeEnumsNull_preferredlanguageValueValid"),

    JOB_REMOVEENUMSNULL_PREFERREDDEGREEVALUEVALID("job_removeEnumsNull_preferredDegreeValueValid"),

    JOB_UPDATESTATUS_INVALIDJOB("job_updateStatus_invalidJob"),

    JOB_UPDATESTATUS_NOPERMISSION("job_updateStatus_noPermission"),

    JOB_UPDATESTATUS_PAYROLLINGJOB("job_updateStatus_payrollingJob"),

    JOB_UPDATEJOBSSTATUS_PARAMNULL("job_updateJobsStatus_paramNull"),

    JOB_UPDATEJOBSSTATUS_IDERROR("job_updateJobsStatus_idError"),

    JOB_UPDATEJOBSSTATUS_RECRUITMENTPROCESSNULL("job_updateJobsStatus_recruitmentProcessNull"),

    JOB_UPDATEJOBSSTATUS_PAYROLLJOB("job_updateJobsStatus_payrollJob"),

    JOB_FINDONEWITHENTITY_NOPERMISSION("job_findOneWithEntity_noPermission"),

    JOB_NUMBEROFOFFERACCEPTEDEQUALSOPENINGS_NOTEXIST("job_numberOfOfferAcceptedEqualsOpenings_notExist"),

    JOB_GETJOBWITHOUTENTITY_NOPERMISSION("job_getJobWithoutEntity_noPermission"),

    JOB_SYNCJOBTOIPG_NOPERMISSION("job_syncJobToIpg_noPermission"),

    JOB_SYNCJOBTOIPG_IPGFILLERERROR("job_syncJobToIpg_ipgFillerError"),

    JOB_SENDMAILTOAMOFJOB_SYNCJOBTOIPGRELATIONNULL("job_sendMailToAmOfJob_syncJobToIpgRelationNull"),

    JOB_SENDEMAILFROMIPGUSER_SENDMAILERROR("job_sendEmailFromIpgUser_sendMailError"),

    JOB_GETCOMPANYTEAMUSERBYJOBID_JOBINVALID("job_getCompanyTeamUserByJobId_jobInvalid"),

    JOB_MYJOBS_ERROR("job_myJobs_error"),

    JOB_SEARCHPER_DELETEBYID_NOPERMISSION("job_searchPer_deleteById_noPermission"),

    JOB_SEARCHPER_CONFIGNULL("job_searchPer_configNull"),

    JOB_UPLOADHTMLTOS3_ERROR("job_uploadHTMLToS3_error"),

    JOB_GETUUIDFROMS3LINKS_ERROR("job_getUUIDFromS3Links_error"),

    JOB_VALIDATEINPUT_JOBIDNULL("job_validateInput_jobIdNull"),

    JOB_VALIDATEINPUT_LANGUAGENULL("job_validateInput_languageNull"),

    JOB_VALIDATEINPUT_USERIDNULL("job_validateInput_userIdNull"),

    JOB_VALIDATEINPUT_PLATFORMTYPENULL("job_validateInput_platformTypeNull"),

    JOB_CREATEPOSTJOB_IDNOTNULL("job_createPostJob_idNotNull"),

    JOB_CREATEPOSTJOB_NOPRIVILEGE("job_createPostJob_noPrivilege"),

    JOB_CREATEPOSTJOB_NOJAZZHR("job_createPostJob_noJazzHR"),

    JOB_CREATEJOB_IDNOTNULL("job_createJob_idNotNull"),

    JOB_SYNCFAILEDTALENTSTOES_WRONG("job_syncFailedTalentsToEs_wrong"),

    JOB_POST_IMAGE_DUPLICATE("job_post_image_duplicate"),

    JOB_INFO_INCOMPLETE_FOR_AI_SOURCING("job_info_incomplete_for_ai_sourcing")
    ;

    private final String key;

    JobAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}