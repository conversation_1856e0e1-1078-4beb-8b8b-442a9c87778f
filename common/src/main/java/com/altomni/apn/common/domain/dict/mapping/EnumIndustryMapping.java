package com.altomni.apn.common.domain.dict.mapping;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ApiModel(description = "Enum industry Mapping entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_industry_mapping")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumIndustryMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display")
    private String enDisplay;


    @Column(name = "en_display_order")
    private Long enDisplayOrder;

    @Column(name = "cn_display_order")
    private Long cnDisplayOrder;

    @Column(name = "sub_job_functions")
    private String subJobFunctions;

    @Column(name = "parent_category")
    private String parentCategory;

    @Transient
    private Boolean isParent = false;

    @Transient
    private Boolean cnLable = false;

    @Transient
    private Long jobFunctionsForIndustryId;

}
