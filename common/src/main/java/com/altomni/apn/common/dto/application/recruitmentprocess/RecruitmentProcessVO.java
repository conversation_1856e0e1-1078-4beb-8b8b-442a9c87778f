package com.altomni.apn.common.dto.application.recruitmentprocess;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.RecruitmentProcessType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * A RecruitmentProcessVO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitmentProcessVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long tenantId;

    private JobType jobType;

    private String name;

    private String description;

    private ActiveStatus status;

    private List<RecruitmentProcessNodeVO> recruitmentProcessNodes;

    //流程中包含该字段表示fte类型
    private Boolean isSubstituteTalent;
}
