package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TimeSheetFrequencyTypeConverter extends AbstractAttributeConverter<TimeSheetFrequencyType, Integer> {
    public TimeSheetFrequencyTypeConverter() {
        super(TimeSheetFrequencyType::toDbValue, TimeSheetFrequencyType::fromDbValue);
    }
}
