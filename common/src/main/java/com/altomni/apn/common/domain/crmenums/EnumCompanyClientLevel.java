package com.altomni.apn.common.domain.crmenums;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "EnumCompanyClientLevel")
@Entity
@Data
@Table(name = "enum_company_client_level")
public class EnumCompanyClientLevel implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name")
    private String name;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display_order")
    private Integer enDisplayOrder;

    @Column(name = "cn_display_order")
    private Integer cnDisplayOrder;

    @Column(name = "en_sort_order")
    private Integer enSortOrder;

    @Column(name = "cn_sort_order")
    private Integer cnSortOrder;
}
