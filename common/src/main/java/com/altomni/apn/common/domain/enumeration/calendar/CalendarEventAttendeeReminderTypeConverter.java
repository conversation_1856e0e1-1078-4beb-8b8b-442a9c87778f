package com.altomni.apn.common.domain.enumeration.calendar;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CalendarEventAttendeeReminderTypeConverter extends AbstractAttributeConverter<CalendarEventAttendeeReminderTypeEnum, Integer> {
    public CalendarEventAttendeeReminderTypeConverter() {
        super(CalendarEventAttendeeReminderTypeEnum::toDbValue, CalendarEventAttendeeReminderTypeEnum::fromDbValue);
    }
}
