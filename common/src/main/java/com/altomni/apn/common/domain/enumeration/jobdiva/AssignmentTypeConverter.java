package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class AssignmentTypeConverter extends AbstractAttributeConverter<AssignmentType, Integer> {
    public AssignmentTypeConverter() {
        super(AssignmentType::toDbValue, AssignmentType::fromDbValue);
    }
}
