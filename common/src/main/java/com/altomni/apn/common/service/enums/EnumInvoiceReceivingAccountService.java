package com.altomni.apn.common.service.enums;

import com.altomni.apn.common.domain.dict.EnumReceivingAccount;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.dict.EnumReceivingAccountVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EnumInvoiceReceivingAccountService {

    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumReceivingAccountVO> findAll() {
        List<EnumReceivingAccount> enumCompanyServiceTypeList = enumCommonService.findAllEnumInvoiceReceivingAccount();
        if (enumCompanyServiceTypeList == null) {
            return new ArrayList<>();
        }
       return enumCompanyServiceTypeList.stream().sorted(Comparator.comparing(EnumReceivingAccount::getId, Comparator.nullsLast(Comparator.naturalOrder()))).map(item -> {
           EnumReceivingAccountVO dto = new EnumReceivingAccountVO();
           ServiceUtils.myCopyProperties(item, dto);
           return dto;
        }).collect(Collectors.toList());
    }

}

