package com.altomni.apn.common.domain.enumeration.message;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class MessageStatusEnumConverter extends AbstractAttributeConverter<MessageStatusEnum, Integer> {
    public MessageStatusEnumConverter() {
        super(MessageStatusEnum::toDbValue, MessageStatusEnum::fromDbValue);
    }
}