package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOnboardClientInfoVO implements Serializable {

    private static final long serialVersionUID = 1121577886701419293L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private Long clientContactId;

    private String clientName;

    private String clientDivision;

    private String clientAddress;

    private String clientEmail;

    private String clientLocation;

    private Long clientInfoId;

    private Long invoiceTypeId;

    private String socialCreditCode ;

    private String bankName ;

    private String bankAccount ;

    private String invoicingAddress ;

    private String phone ;

}
