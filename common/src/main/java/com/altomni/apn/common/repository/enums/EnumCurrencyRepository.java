package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumCurrency;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

/**
 * enum_currency repository
 * <AUTHOR>
 */
@Repository
public interface EnumCurrencyRepository extends JpaRepository<EnumCurrency, Integer>, QuerydslPredicateExecutor<EnumCurrency> {
    
}
