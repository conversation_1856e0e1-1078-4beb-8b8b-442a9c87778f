package com.altomni.apn.common.domain.enumeration.xxljob;

import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import static com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum.SYSTEM_MESSAGE;

public enum XxlJobRelationTypeEnum implements ConvertedEnum<Integer> {

    CALENDAR(10, "messageCommonReminderHandler", SYSTEM_MESSAGE, null, null),

    JOB_NO_SUMMIT_CANDIDATE_WARN(20, "messageCommonReminderHandler", MessageTypeEnum.PERSONAL_WARNING, "【预警：职位{jobTitle}超过{day}天数未提交候选人,请您关注。】", "【Alert: Position {} has not submitted candidates for over {} days, please pay attention.】"),
    JOB_NO_INTERVIEW_WARN(21, "messageCommonReminderHandler", MessageTypeEnum.PERSONAL_WARNING, "【预警：职位{jobTitle}超过{day}天内未有面试记录,请您关注。】", "【Alert: Position {} has no interview records within {} days, please pay attention.】"),
    TALENT_NO_ONBOARD_WARN(30, "messageCommonReminderHandler", MessageTypeEnum.PERSONAL_WARNING, "【候选人{talentName}入职逾期,请您关注。】", "【Alert: Candidate {} is overdue for onboarding, please pay attention.】"),
    TALENT_ONBOARD_NO_INVOICE_WARN(31, "messageCommonReminderHandler", MessageTypeEnum.PERSONAL_WARNING, "【预警：候选人{talentName}在今天完成入职,请注意开票。】", "【Alert: Candidate {} completed onboarding today, please pay attention to invoicing.】"),

    TALENT_FTE_INVOICE_OVERDUE_WARN(32, "messageCommonReminderHandler", MessageTypeEnum.PERSONAL_WARNING, "【预警：候选人{talentName}的发票(Invoice Number: {invoiceNo})在今天逾期,请注意跟进回款情况。】", "【Alert: Candidate {}'s invoice ( Number: {}) is overdue today. Please take note and follow up on the payment status.】"),
    TALENT_CONTRACT_INVOICE_OVERDUE_WARN(33, "messageCommonReminderHandler", MessageTypeEnum.PERSONAL_WARNING, "【预警：候选人{talentName}的发票(Invoice Number: {invoiceNo})在今天逾期,请注意跟进回款情况。】", "【Alert: Candidate {}'s invoice ( Number: {}) is overdue today. Please take note and follow up on the payment status.】"),

    TALENT_INFO_UPDATE_WARN(34, "messageCommonReminderHandler", MessageTypeEnum.PERSONAL_WARNING, "【预警：候选人{talentName}的当前LinkedIn页面职位和所在公司更新, 请注意跟进候选人是否有招聘合作需求情况。】", "【Alert: Candidate {} updated the LinkedIn page for their position and company. Please pay attention to whether the candidate has recruitment cooperation needs.】"),

    TEAM_UN_SUBMIT_TO_JOB_TALENT_COUNT_WARN(40, "messageCommonReminderHandler", MessageTypeEnum.TEAM_WARNING, "【预警：团队成员{userName}在上周录入候选人不足{number}人,请您关注。】", "【Alert: Team member {} entered fewer than {} candidates last week, please pay attention.】"),

    TEAM_OVERDUE_ONBOARD_WARN(41, "reminderForUnOnboardForTeamHandler", MessageTypeEnum.TEAM_WARNING, "【预警：团队成员{userName}的候选人{talentName}逾期未入职,请您关注。】", "【Alert: Team member {}'s candidate {} is overdue for onboarding, please pay attention.】"),

    ADMIN_MESSAGE(50, "messageCommonReminderHandler", MessageTypeEnum.ADMIN_MESSAGE, null, null),

    APPLICATION_NO_UPDATE_REMINDER(60, "messageCommonReminderHandler", MessageTypeEnum.PERSONAL_WARNING, "请注意：您相关的岗位“{jobTitle}”中候选人“{talentName}”在“{applicationStatus}”状态停留时间超过{day}个工作日，请您重点关注！", "Please note: Regarding the position '{}' with which you are involved, the candidate '{}' has remained in the '{}' status for more than {} working days. Please pay attention to this!"),


    REPORT_SUBSCRIPTION(70, "messageCommonReminderHandler", null, null, null),

    //定期清理消息任务
    REGULARLY_CLEAN_UP_MESSAGES(80, "messageCommonReminderHandler", null, null, null),

    //apn通过lark发送发布消息
    APN_ANNOUNCEMENT_MESSAGE(90, "messageCommonReminderHandler", SYSTEM_MESSAGE, null, null),

    //语音留言消息系统通知
    VOICEMAIL_MESSAGE(100, "messageCommonReminderHandler", SYSTEM_MESSAGE, null, null),

    RETRY_FAIL_XXL_JOB(9999, "messageCommonReminderHandler", null, null, null),

    // 保密候选人自动解除保密
    TALENT_DECLASSIFY(101, "messageCommonReminderHandler", null, null, null),

    //禁猎客户消息提醒
    NO_POACHING_REMINDER(110, "messageCommonReminderHandler", SYSTEM_MESSAGE, null, null),

    //系统日程定时任务-职位跟进
    SYSTEM_CALENDAR_JOB_FOLLOW(120, "messageCommonReminderHandler", SYSTEM_MESSAGE, null, null),

    //系统日程定时任务-候选人流程跟进
    SYSTEM_CALENDAR_TALENT_APPLICATION_FOLLOW(121, "messageCommonReminderHandler", SYSTEM_MESSAGE, null, null);

    private final int dbValue;

    private final String xxlJobHandler;

    private final MessageTypeEnum messageType;

    private final String cnMessage;

    private final String enMessage;

    XxlJobRelationTypeEnum(int dbValue, String xxlJobHandler, MessageTypeEnum messageType, String cnMessage, String enMessage) {
        this.dbValue = dbValue;
        this.xxlJobHandler = xxlJobHandler;
        this.messageType = messageType;
        this.cnMessage = cnMessage;
        this.enMessage = enMessage;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getXxlJobHandler() {
        return xxlJobHandler;
    }

    public String getCnMessage() {
        return cnMessage;
    }

    public String getEnMessage() {
        return enMessage;
    }

    public MessageTypeEnum getMessageType() {
        return messageType;
    }

    // static resolving:
    public static final ReverseEnumResolver<XxlJobRelationTypeEnum, Integer> resolver =
            new ReverseEnumResolver<>(XxlJobRelationTypeEnum.class, XxlJobRelationTypeEnum::toDbValue);

    public static XxlJobRelationTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }



}
