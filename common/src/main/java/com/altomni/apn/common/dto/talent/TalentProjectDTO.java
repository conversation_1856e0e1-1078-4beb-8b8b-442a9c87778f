package com.altomni.apn.common.dto.talent;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Comparator;


@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentProjectDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private LocalDate startDate;

    private LocalDate endDate;

    @Size(max = 200, message = "The project name is too long.")
    private String projectName;

    private String companyName;

    @Size(max = 200, message = "The title is too long.")
    private String title;

    @Size(max = 16380, message = "The description is too long.")
    private String description;

    private Boolean current;


    public static final Comparator<TalentProjectDTO> COMPARATOR = (o1, o2) -> {
        //sorting date
        if (ObjectUtil.isNotNull(o1.getCurrent()) && o1.getCurrent()) {
            //if one is current, ignore all end_date. end_dates are bad data.
            if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                if (ObjectUtil.isNotNull(o1.getStartDate())) {
                    if (ObjectUtil.isNotNull(o2.getStartDate())) {
                        if (o1.getStartDate().isBefore(o2.getStartDate())) {
                            return 1;
                        } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                            return -1;
                        } else {
                            return 0;
                        }
                    } else {
                        //# the one without start date is smaller
                        return -1;
                    }
                } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                    //the one without start date is smaller
                    return 1;
                }
            } else {
                return -1;
            }
        } else if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
            return 1;
        }
        //if neither is current, use end_date
        if (ObjectUtil.isNotNull(o1.getEndDate())) {
            if (ObjectUtil.isNotNull(o2.getEndDate())) {
                if (o1.getEndDate().isBefore(o2.getEndDate())) {
                    return 1;
                } else if (o1.getEndDate().isAfter(o2.getEndDate())) {
                    return -1;
                }
                //if equals, use start_date
            } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                if (o1.getEndDate().isBefore(o2.getStartDate())) {
                    return 1;
                } else if (ObjectUtil.isNotNull(o1.getStartDate())) {
                    if (o1.getStartDate().isAfter(o2.getStartDate())) {
                        return -1;
                    } else {
                        return 1;
                    }
                } else {
                    //end_date is larger than other.start_date
                    return -1;
                }
            } else {
                return -1;
            }
        } else if (ObjectUtil.isNotNull(o2.getEndDate())) {
            if (ObjectUtil.isNotNull(o1.getStartDate())) {
                if (o1.getStartDate().isAfter(o2.getEndDate())) {
                    return -1;
                } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                    if (o1.getStartDate().isBefore(o2.getStartDate())) {
                        return 1;
                    } else {
                        return -1;
                    }
                } else {
                    return 1;
                }
            } else {
                return 1;
            }
        }
        //if neither exists end_date, use start_date as well
        //here, we have already dealt with one end date existing cases
        if (ObjectUtil.isNotNull(o1.getStartDate())) {
            if (ObjectUtil.isNotNull(o2.getStartDate())) {
                if (o1.getStartDate().isBefore(o2.getStartDate())) {
                    return 1;
                } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                    return -1;
                }
            } else {
                return -1;
            }
        } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
            return 1;
        }
        //missing info or equals, unable to determine, return None
        return 0;
    };

}
