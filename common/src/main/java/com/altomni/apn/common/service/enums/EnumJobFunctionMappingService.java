package com.altomni.apn.common.service.enums;

import cn.hutool.core.util.PinyinUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.dict.EnumJobFunction;
import com.altomni.apn.common.domain.dict.mapping.EnumIndustryMapping;
import com.altomni.apn.common.domain.dict.mapping.EnumJobFunctionMapping;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.dto.enums.JobFunctionsDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.UserService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class EnumJobFunctionMappingService {

    private final Logger log = LoggerFactory.getLogger(EnumJobFunctionMappingService.class);

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private UserService userService;

    private static Map<String, Map<Long, String>> jobFunctionsForIndustryCache = new ConcurrentHashMap<>();


    public JobFunctionsDTO getJobFunctionsMapping(Long id, SortType type, Boolean mapping) {
        JobFunctionsDTO jobFunctionsDTO = new JobFunctionsDTO();
        jobFunctionsDTO.setTree(getJobFunctionTree(id, type));
        if(Boolean.TRUE.equals(mapping)) {
            jobFunctionsDTO.setMapping(getJobFunctionsMapping(type));
        }
        return jobFunctionsDTO;
    }

    private String getJobFunctionTree(Long id, SortType type) {
        Map<Long, String> typeMap = jobFunctionsForIndustryCache.get(type.name());
        if(typeMap == null) {
            typeMap = new ConcurrentHashMap<>();
            jobFunctionsForIndustryCache.put(type.name(), typeMap);
        }
        String json = typeMap.get(id);
        if(json != null) {
            return json;
        }
        String subJobFunctions = "";
        //-1为默认job functions
        if(-1L == id) {
            subJobFunctions = userService.getTenantParamStringValue(Constants.DEFAULT_JOB_FUNCTIONS_MAPPING).getBody();
        } else if(-2L == id) {
            //-2为all job functions
            subJobFunctions = userService.getTenantParamStringValue(Constants.ALL_JOB_FUNCTIONS_MAPPING).getBody();
        } else {
            EnumIndustryMapping industryMapping = enumCommonService.getIndustryMappingById(id);
            subJobFunctions = industryMapping.getSubJobFunctions();
        }
        subJobFunctions = getJobFunctionsMapping(subJobFunctions, type);
        typeMap.put(id, subJobFunctions);

        return subJobFunctions;
    }

    private String getJobFunctionsMapping(String subJobFunctions, SortType type) {
        JSONObject jsonObject = JSONUtil.parseObj(subJobFunctions);
        Map<Long, EnumJobFunctionMapping> map = enumCommonService.findAllEnumJobfunctionMapping().stream().collect(Collectors.toMap(EnumJobFunctionMapping::getId, Function.identity()));
        addLabel(jsonObject.getJSONArray("children"), map, type);
        sortJobFunction(jsonObject, map, type);
        return JSONUtil.toJsonStr(jsonObject);
    }

    private void sortJobFunction(JSONObject jsonObject, Map<Long, EnumJobFunctionMapping> map, SortType type) {
        JSONArray children = jsonObject.getJSONArray("children");
        if(children != null) {
            //同级别有子类的在前， 按首字母排序， 其次英文在前，中文灾后
            children.sort(Comparator.comparing(f -> {
                Long id = ((JSONObject) f).getLong("id");
                boolean existChildren = ((JSONObject) f).containsKey("children");
                EnumJobFunctionMapping enumJobFunction = map.get(id);
                if(enumJobFunction == null) {
                    return "z";
                }
                if(SortType.CN.equals(type)) {
                    String cnDisplay = enumJobFunction.getCnDisplay();
                    if(StringUtils.isEmpty(cnDisplay)) {
                        return "z";
                    }
                    String sortDisplay = PinyinUtil.getPinYin(cnDisplay);
                    return getSortDisplay(existChildren, sortDisplay);
                } else {
                    String sortDisplay = enumJobFunction.getEnDisplay();
                    if(StringUtils.isEmpty(sortDisplay)) {
                        return "z";
                    }
                    return existChildren ? "0" + sortDisplay : "1" + sortDisplay;
                }
            }));
            for(int i = 0; i< children.size(); i++) {
                sortJobFunction(children.getJSONObject(i), map, type);
            }
        }
    }

    private String getSortDisplay(boolean existChildren, String sortDisplay) {
        if(sortDisplay.substring(0,1).matches("^[a-zA-Z]*$")) {
            return existChildren ? "00" + sortDisplay : "10" + sortDisplay;
        } else {
            return existChildren ? "01" + sortDisplay : "11" + sortDisplay;
        }
    }

    private void addLabel(JSONArray jsonArray, Map<Long, EnumJobFunctionMapping> map, SortType type) {
        for(int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Long id = jsonObject.getLong("id");
            if(SortType.EN.equals(type)) {
                EnumJobFunctionMapping enumJobFunction = map.get(id);
                if(enumJobFunction != null) {
                    jsonObject.put("label", enumJobFunction.getEnDisplay());
                }
            } else if(SortType.CN.equals(type)) {
                EnumJobFunctionMapping enumJobFunction = map.get(id);
                if(enumJobFunction != null) {
                    jsonObject.put("label", enumJobFunction.getCnDisplay());
                    jsonObject.put("labelEn", enumJobFunction.getEnDisplay());
                }
            }
            JSONArray children = jsonObject.getJSONArray("children");
            if(children != null) {
                addLabel(children, map, type);
            }
        }
    }

    private List<EnumDictDTO> getJobFunctionsMapping(SortType type) {
        List<EnumJobFunctionMapping> allEnumJobfunctionMapping = enumCommonService.findAllEnumJobfunctionMapping();
        return allEnumJobfunctionMapping.stream().filter(c -> StringUtils.isNotEmpty(c.getEnDisplay()) || StringUtils.isNotEmpty(c.getCnDisplay())).map(c -> {
            EnumDictDTO enumDictDTO = new EnumDictDTO();
            enumDictDTO.setId(c.getId().toString());
            if(SortType.EN.equals(type)) {
                enumDictDTO.setLabel(c.getEnDisplay());
            } else if(SortType.CN.equals(type)) {
                enumDictDTO.setLabel(c.getCnDisplay());
            }
            enumDictDTO.setLabelEn(c.getEnDisplay());
            return enumDictDTO;
        }).toList();
    }



    public Set<Long> findValidEnumJobFunctionsIds(){
        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
        return enumJobFunctionList.stream().map(EnumJobFunction::getId).collect(Collectors.toSet());
    }

}
