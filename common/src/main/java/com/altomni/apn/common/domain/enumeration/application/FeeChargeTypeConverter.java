package com.altomni.apn.common.domain.enumeration.application;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class FeeChargeTypeConverter extends AbstractAttributeConverter<FeeChargeType, Integer> {
    public FeeChargeTypeConverter() {
        super(FeeChargeType::toDbValue, FeeChargeType::fromDbValue);
    }
}
