package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CalendarRelationEnum implements ConvertedEnum<Integer> {

    COMPANY(0, "客户"),
    COMPANY_CONTACT(1, "客户联系人"),
    CANDIDATE(2, "候选人"),
    JOB(3, "职位"),
    TALENT_RECRUITMENT_PROCESS(4, "流程"),
    BUSINESS(6,"商机"),
    CONTRACT(7, "合同"),
    INVOICE(8,"发票");


    private final int dbValue;
    private final String comment;

    public static final ReverseEnumResolver<CalendarRelationEnum, Integer> resolver =
            new ReverseEnumResolver<>(CalendarRelationEnum.class, CalendarRelationEnum::toDbValue);

    CalendarRelationEnum(int dbValue, String comment) {
        this.dbValue = dbValue;
        this.comment = comment;
    }

    public static CalendarRelationEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }
}
