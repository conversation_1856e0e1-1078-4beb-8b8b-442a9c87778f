package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.enumeration.enums.DisplayType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetExternalAuthenticationToken;
import com.altomni.apn.common.utils.LoginUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * enum_currency service impl
 * <AUTHOR>
 */
@Service
public class EnumCurrencyService {

    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumCurrency> findAllEnumCurrency() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // timesheet 中调用 currency 时，使用客户端模式身份，避免数据权限
        if (authentication instanceof TimesheetExternalAuthenticationToken) {
            LoginUtil.simulateLoginWithClient();
        }
        List<EnumCurrency> enumCurrencyList = enumCommonService.findAllEnumCurrency();
        if (CollUtil.isNotEmpty(enumCurrencyList)) {
            enumCurrencyList = enumCurrencyList.stream().filter(currency ->
                StrUtil.isNotBlank(currency.getLabel1()) || StrUtil.isNotBlank(currency.getLabel2()) || StrUtil.isNotBlank(currency.getLabel3())
            ).sorted(Comparator.comparingInt(EnumCurrency::getEnDisplayOrder)).collect(Collectors.toList());
        }
        return enumCurrencyList;
    }

    public EnumCurrency findEnumCurrencyById(Integer id) {
        List<EnumCurrency> enumCurrencyList = enumCommonService.findAllEnumCurrency();
        Optional<EnumCurrency> enumCurrency = enumCurrencyList.stream().filter(o -> o.getId().equals(id)).findFirst();
        if (enumCurrency.isEmpty()) {
            throw new CustomParameterizedException("Enum currency is null , id : " + id);
        }
        return enumCurrency.get();
    }

    public EnumCurrency findEnumCurrencyByName(String name) {
        List<EnumCurrency> enumCurrencyList = enumCommonService.findAllEnumCurrency();
        Optional<EnumCurrency> enumCurrency = enumCurrencyList.stream().filter(o -> o.getName().equals(name)).findFirst();
        if (enumCurrency.isEmpty()) {
            throw new CustomParameterizedException("Enum currency is null , name : " + name);
        }
        return enumCurrency.get();
    }

    /***
     * Activity Change function: get the UI name through the enumId;
     * @param id
     * @param displayType
     * @return
     */
    public String getCurrencyUINameById(Long id, DisplayType displayType) {

        List<EnumCurrency> enumCurrencyList = enumCommonService.findAllEnumCurrency();

        Optional<EnumCurrency> enumCurrencyOpt = enumCurrencyList.stream()
                .filter(enumCurrency -> enumCurrency.getId().equals(id.intValue()))
                .findFirst();

        if(enumCurrencyOpt.isPresent()) {
            EnumCurrency enumCurrency = enumCurrencyOpt.get();
            if(displayType.equals(DisplayType.EN) && StringUtils.isNotBlank(enumCurrency.getEnDisplay())) {
                return enumCurrency.getEnDisplay();
            } else if (displayType.equals(DisplayType.CN) && StringUtils.isNotBlank(enumCurrency.getCnDisplay())) {
                return enumCurrency.getCnDisplay();
            } else {
                return enumCurrency.getName();
            }
        }
        return "Unknown Currency";
    }


}
