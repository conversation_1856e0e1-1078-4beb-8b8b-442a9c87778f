package com.altomni.apn.common.domain.user;

import java.util.Set;

/**
 * <AUTHOR>
 */
public interface UserSecurityInterface {

    Long getId();

    String getUid();

    //AccountType getAccountType();

    boolean isActivated();

    Set<Role> getRoles();

    String getPassword();

    Long getTenantId();

    Long getTeamId();

    //String getTenantName();

    String getFirstName();

    String getLastName();

    String getUsername();
}
