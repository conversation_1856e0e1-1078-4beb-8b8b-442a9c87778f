package com.altomni.apn.common.config.rabbitmq;

import com.altomni.apn.common.config.application.EsFillerMqBaseProperties;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.SerializerMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;

@Configuration
@ConditionalOnBean(EsFillerMqBaseProperties.class)
public class EsFillerRabbitMqConfig {

    @Resource
    private EsFillerMqBaseProperties esFillerMqBaseProperties;

    public CachingConnectionFactory connectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        return connectionFactory;
    }

    @Bean(name = "esfillerFactory")
    public SimpleRabbitListenerContainerFactory esfillerFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "esfillerConnectionFactory")
    @Primary
    public ConnectionFactory esfillerConnectionFactory() {
        return connectionFactory (esFillerMqBaseProperties.getEsfillerMQHost(), esFillerMqBaseProperties.getEsfillerMQPort(), esFillerMqBaseProperties.getEsfillerMQVirtualHost(), esFillerMqBaseProperties.getEsfillerMQUsername(), esFillerMqBaseProperties.getEsfillerMQPassword());
    }

    @Bean(name = "esfillerRabbitTemplate")
    public RabbitTemplate esfillerRabbitTemplate(@Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate parsersRabbitTemplate = new RabbitTemplate(connectionFactory);
        parsersRabbitTemplate.setMandatory(true);
        parsersRabbitTemplate.setMessageConverter(new SerializerMessageConverter());
        return parsersRabbitTemplate;
    }

    @Bean(name = "esfillerAmqpAdmin")
    public AmqpAdmin esfillerAmqpAdmin(@Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory) {
        AmqpAdmin amqpAdmin = new RabbitAdmin(connectionFactory);
        return amqpAdmin;
    }

}
