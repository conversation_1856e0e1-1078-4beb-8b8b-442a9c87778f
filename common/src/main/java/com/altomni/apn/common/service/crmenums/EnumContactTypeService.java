package com.altomni.apn.common.service.crmenums;

import com.altomni.apn.common.domain.crmenums.EnumContactType;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.crmenums.EnumCrmCommonService;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.crmenums.DictVO;
import com.altomni.apn.common.vo.crmenums.EnumContactTypeVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EnumContactTypeService {

    @Resource
    private EnumCrmCommonService enumCrmCommonService;

    public List<DictVO> findAllOrderBySortType(SortType type) {
        List<EnumContactType> enumContactTypeList = enumCrmCommonService.findAllEnumContactType();
        return enumContactTypeList.stream().map(t -> EnumContactType.fromEnumContactType(t, type)).sorted(Comparator.comparing(DictVO::getDisplayOrder)).collect(Collectors.toList());
    }

    public List<DictVO> findEnumContactTypeByIds(List<Integer> contactTypeIds) {
        Map<Integer, EnumContactType> enumContactTypeMap = enumCrmCommonService.findAllEnumContactTypeMap();

        List<DictVO> dictVOList = contactTypeIds.stream()
                .filter(enumContactTypeMap::containsKey)
                .map(id -> {
                    EnumContactType enumContactType = enumContactTypeMap.get(id);
                    DictVO enumDictVO = new DictVO();
                    ServiceUtils.myCopyProperties(enumContactType, enumDictVO);
                    return enumDictVO;
                })
                .collect(Collectors.toList());
        return dictVOList;
    }

    public Map<Integer, String> getAllContactTypeIdsToDupCheck() {
        List<EnumContactType> enumContactTypeList = enumCrmCommonService.findAllEnumContactType();
        Map<Integer, String> contactTypeToCheckMap = enumContactTypeList.stream().filter(EnumContactType::getDuplicationCheck).collect(Collectors.toMap(
                EnumContactType::getId,
                EnumContactType::getName
        ));

        return contactTypeToCheckMap;
    }

    public Map<Integer, EnumContactTypeVO> getAllDuplicationCheckContactTypeMap() {
        List<EnumContactType> enumContactTypeList = enumCrmCommonService.findAllEnumContactType();
        Map<Integer, Set<Integer>> duplicationCheckGroup = new HashMap<>();

        enumContactTypeList.stream()
                .filter(enumContactType ->  enumContactType.getDuplicationCheck() && enumContactType.getCheckGroup() != null)
                .forEach(contactType -> {
                    duplicationCheckGroup.computeIfAbsent(contactType.getCheckGroup(), k -> new HashSet<>()).add(contactType.getId());
                });
        Map<Integer, EnumContactTypeVO> contactTypeToCheckMap = enumContactTypeList.stream().filter(EnumContactType::getDuplicationCheck)
                .collect(Collectors.toMap(
                        EnumContactType::getId,
                        item -> new EnumContactTypeVO(item.getId(), item.getName(), true, item.getCheckGroup(), duplicationCheckGroup.getOrDefault(item.getCheckGroup(), new HashSet<>()))
                ));
        return contactTypeToCheckMap;
    }

    public Map<Integer, String> getAllContactTypeIdsToNameMap() {
        List<EnumContactType> enumContactTypeList = enumCrmCommonService.findAllEnumContactType();
        Map<Integer, String> contactTypeMap = enumContactTypeList.stream().collect(Collectors.toMap(
                EnumContactType::getId,
                EnumContactType::getName
        ));

        return contactTypeMap;
    }
}
