package com.altomni.apn.common.dto.message;

import com.altomni.apn.common.domain.enumeration.message.MessageFavoriteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageFavoriteEnumConverter;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageSearchPageDTO {

    private Integer page;

    private Integer size;

    private MessageTypeEnum type;

    @Convert(converter = MessageFavoriteEnumConverter.class)
    private MessageFavoriteEnum isFavorite;

}
