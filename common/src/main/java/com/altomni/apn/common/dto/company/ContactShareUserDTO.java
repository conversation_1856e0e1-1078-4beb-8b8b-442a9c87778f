package com.altomni.apn.common.dto.company;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class ContactShareUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long contactId;

    private Long userId;

    private Boolean autoAssigned;


    public ContactShareUserDTO(Long userId) {
        this.userId = userId;
        this.autoAssigned = Boolean.FALSE;
    }
}
