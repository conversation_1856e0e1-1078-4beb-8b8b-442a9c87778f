package com.altomni.apn.common.dto.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionPrivilegeTreeDTO implements Serializable {

    private Long id;

    private String name;

    private Long parentId;

    private List<PermissionPrivilegeTreeDTO> children;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("children"));

}
