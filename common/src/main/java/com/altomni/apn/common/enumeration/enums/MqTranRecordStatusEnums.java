package com.altomni.apn.common.enumeration.enums;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * mq 事务业务状态枚举
 */
public enum MqTranRecordStatusEnums implements ConvertedEnum<Integer> {

    PENDING(1, "发送中"),
    SEND_FAIL(2, "发送失败"),
    SEND_SUCCESS(3, "发送成功"),
    NOT_ARRIVE_QUEUE(4, "消息发送失败，未送达队列"),
    ;

    private final Integer dbValue;

    private final String desc;

    MqTranRecordStatusEnums(Integer dbValue, String desc) {
        this.dbValue = dbValue;
        this.desc = desc;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDesc() {
        return desc;
    }

    // static resolving:
    public static final ReverseEnumResolver<MqTranRecordStatusEnums, Integer> resolver =
            new ReverseEnumResolver<>(MqTranRecordStatusEnums.class, MqTranRecordStatusEnums::toDbValue);

    public static MqTranRecordStatusEnums fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}