package com.altomni.apn.common.service.voipserver;

import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientResponseException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class VoipProxyAPIHttp {

    @Resource
    @Qualifier("externalServiceRestTemplate")
    private RestTemplate restTemplate;

    @Value("${application.voipServer.url}")
    private String BASE_VOIP_SERVICE;

    /**
     * 通用API代理方法 - 使用指定的响应类型
     *
     * @param <T> 请求体类型
     * @param <R> 响应类型
     * @param endpoint API端点路径
     * @param requestBody 请求体对象
     * @param httpMethod HTTP方法
     * @param responseType 响应类型的Class对象
     * @return 第三方API的响应
     */
    public <T, R> ResponseEntity<R> proxyApiRequest(
            String endpoint,
            T requestBody,
            HttpMethod httpMethod,
            Class<R> responseType) {

        try {
            log.info("Proxying request to {}", endpoint);
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setBearerAuth(SecurityUtils.getCurrentUserToken());
            // 根据需要添加其他通用头信息

            HttpEntity<?> entity = createHttpEntity(requestBody, headers);

            ResponseEntity<R> response = restTemplate.exchange(
                    BASE_VOIP_SERVICE + endpoint,
                    httpMethod,
                    entity,
                    responseType
            );

            log.info("Successfully received response from {}", endpoint);

            return response;

        } catch (RestClientResponseException e) {
            log.error("HTTP error from API: {} - {}", e.getRawStatusCode(), e.getResponseBodyAsString(), e);

            String responseBody = e.getResponseBodyAsString();

            // 解析错误响应并抛出自定义异常
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                JsonNode errorNode = objectMapper.readTree(responseBody);

                // 从JSON响应中提取信息
                Integer status = errorNode.has("status") ? errorNode.get("status").asInt() : e.getRawStatusCode();
                String title = errorNode.has("title") ? errorNode.get("title").asText() : "API Error";
                String message = errorNode.has("message") ? errorNode.get("message").asText() :
                        (errorNode.has("detail") ? errorNode.get("detail").asText() : responseBody);

                // 提取params字段（如果存在）
                Map<String, Object> params = new HashMap<>();
                if (errorNode.has("params") && errorNode.get("params").isObject()) {
                    JsonNode paramsNode = errorNode.get("params");
                    Iterator<Map.Entry<String, JsonNode>> fields = paramsNode.fields();
                    while (fields.hasNext()) {
                        Map.Entry<String, JsonNode> entry = fields.next();
                        params.put(entry.getKey(), parseJsonValue(entry.getValue()));
                    }
                }
                // 抛出自定义异常
                throw new CustomParameterizedException(status, title, message, params);

            } catch (CustomParameterizedException customEx) {
                // 直接重新抛出自定义异常
                throw customEx;
            } catch (Exception jsonEx) {
                // 解析失败时，创建一个基本的自定义异常
                log.warn("Failed to parse error JSON: {}", jsonEx.getMessage());
                throw new CustomParameterizedException(
                        e.getRawStatusCode(),
                        "API Error",
                        responseBody,
                        Collections.emptyMap()
                );
            }
        }
    }

    /**
     * 辅助方法：将JsonNode转换为Java对象
     */
    private Object parseJsonValue(JsonNode node) {
        if (node.isTextual()) {
            return node.asText();
        } else if (node.isNumber()) {
            return node.isInt() ? node.asInt() :
                    node.isLong() ? node.asLong() :
                            node.isDouble() ? node.asDouble() : node.asText();
        } else if (node.isBoolean()) {
            return node.asBoolean();
        } else if (node.isArray()) {
            List<Object> list = new ArrayList<>();
            for (JsonNode item : node) {
                list.add(parseJsonValue(item));
            }
            return list;
        } else if (node.isObject()) {
            Map<String, Object> map = new HashMap<>();
            Iterator<Map.Entry<String, JsonNode>> fields = node.fields();
            while (fields.hasNext()) {
                Map.Entry<String, JsonNode> entry = fields.next();
                map.put(entry.getKey(), parseJsonValue(entry.getValue()));
            }
            return map;
        } else if (node.isNull()) {
            return null;
        } else {
            return node.asText();
        }
    }

    private <T> HttpEntity<?> createHttpEntity(T requestBody, HttpHeaders headers) {
        if (requestBody == null) {
            return new HttpEntity<>(headers);
        } else {
            return new HttpEntity<>(requestBody, headers);
        }
    }

}
