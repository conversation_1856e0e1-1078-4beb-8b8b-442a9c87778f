package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TimeSheetStatusConverter extends AbstractAttributeConverter<TimeSheetStatus, Integer> {
    public TimeSheetStatusConverter() {
        super(TimeSheetStatus::toDbValue, TimeSheetStatus::fromDbValue);
    }
}
