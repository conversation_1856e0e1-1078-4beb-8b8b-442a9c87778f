package com.altomni.apn.common.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.NotePriority;
import com.altomni.apn.common.domain.enumeration.NotePriorityConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * A JobNote.
 */
@ApiModel(description = "User's note on a job")
@Entity
@Table(name = "job_note")
public class JobNote extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "note content", required = true)
    @NotNull
    @Column(name = "note", nullable = false)
    private String note;

//TODO: remove
    @Deprecated
    @ApiModelProperty(value = "note title", required = true)
    @NotNull
    @Column(name = "title", nullable = false)
    private String title = "deprecated column";

    @ApiModelProperty(value = "Soft delete flag.")
    @Column(name = "visible")
    private Boolean visible = true;

    @Deprecated
    @ApiModelProperty(value = "the note priority. Default is Normal", allowableValues = "Normal, High")
    @Convert(converter = NotePriorityConverter.class)
    @Column(name = "priority")
    private NotePriority priority = NotePriority.NORMAL;

    @ApiModelProperty(value = "The user id (current user from access token) who created the note. Ready only. change this will be ignored.")
    @Column(name = "user_id")
    private Long userId = null;

    //end of remove;

    @ApiModelProperty(value = "The job id for the note", required = true)
    @NotNull
    @Column(name = "job_id", updatable = false)
    private Long jobId;

    @ApiModelProperty(value = "flag to sync to calendar ")
    @NotNull
    @Column(name = "sync_to_calendar")
    private Boolean syncToCalendar;

    @ApiModelProperty(value = "the time to send alert")
    @Column(name = "alert_time")
    private Instant alertTime;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("userId", "jobId", "id"));

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public JobNote title(String title) {
        this.title = title;
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getNote() {
        return note;
    }

    public JobNote note(String note) {
        this.note = note;
        return this;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Boolean isVisible() {
        return visible;
    }

    public JobNote visible(Boolean visible) {
        this.visible = visible;
        return this;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public NotePriority getPriority() {
        return priority;
    }

    public JobNote priority(NotePriority priority) {
        this.priority = priority;
        return this;
    }

    public JobNote priority(Integer value) {
        this.priority = NotePriority.fromDbValue(value);
        return this;
    }

    public void setPriority(NotePriority priority) {
        this.priority = priority;
    }

    public Boolean getVisible() {
        return visible;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Boolean getSyncToCalendar() {
        return syncToCalendar;
    }

    public void setSyncToCalendar(Boolean syncToCalendar) {
        this.syncToCalendar = syncToCalendar;
    }

    public Instant getAlertTime() {
        return alertTime;
    }

    public void setAlertTime(Instant alertTime) {
        this.alertTime = alertTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JobNote jobNote = (JobNote) o;
        if (jobNote.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), jobNote.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "JobNote{" +
            "id=" + id +
            ", title='" + title + '\'' +
            ", note='" + note + '\'' +
            ", visible=" + visible +
            ", priority=" + priority +
            ", userId=" + userId +
            ", jobId=" + jobId +
            "} " + super.toString();
    }
}
