package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.math.BigDecimal;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessIpgAgreedPayRateVO implements Serializable {

    private static final long serialVersionUID = -2449433446313322640L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private Integer currency = CurrencyConstants.USD;

    private RateUnitType rateUnitType;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal agreedPayRate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentRecruitmentProcessId() {
        return talentRecruitmentProcessId;
    }

    public void setTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public RateUnitType getRateUnitType() {
        return rateUnitType;
    }

    public void setRateUnitType(RateUnitType rateUnitType) {
        this.rateUnitType = rateUnitType;
    }

    public BigDecimal getAgreedPayRate() {
        return agreedPayRate == null ? null : new BigDecimal(Constants.DECIMAL_FORMAT.format(agreedPayRate));
    }

    public void setAgreedPayRate(BigDecimal agreedPayRate) {
        this.agreedPayRate = agreedPayRate;
    }

    @Override
    public String toString() {
        return "TalentRecruitmentProcessIpgAgreedPayRateVO{" +
                "id=" + id +
                ", talentRecruitmentProcessId=" + talentRecruitmentProcessId +
                ", currency=" + currency +
                ", rateUnitType=" + rateUnitType +
                ", agreedPayRate=" + agreedPayRate +
                '}';
    }
}
