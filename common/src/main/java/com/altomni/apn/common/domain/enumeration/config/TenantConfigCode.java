package com.altomni.apn.common.domain.enumeration.config;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TenantConfigCode enumeration.
 */
public enum TenantConfigCode implements ConvertedEnum<Integer> {

    GENERAL_CONFIG(1),

    HOME_PAGE_CONFIG(2),

    MESSAGE_CONFIG(3);

    private final int dbValue;

    TenantConfigCode(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TenantConfigCode, Integer> resolver =
        new ReverseEnumResolver<>(TenantConfigCode.class, TenantConfigCode::toDbValue);

    public static TenantConfigCode fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
