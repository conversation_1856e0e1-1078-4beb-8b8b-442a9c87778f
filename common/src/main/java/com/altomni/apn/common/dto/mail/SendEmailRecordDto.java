package com.altomni.apn.common.dto.mail;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import java.io.Serializable;

@Data
public class SendEmailRecordDto extends AbstractAuditingEntity implements Serializable {

    private Long id;

    private Integer status;

    private Integer type;

    private Integer responseCode;

    private String requestParam;

    private String responseMessage;

}
