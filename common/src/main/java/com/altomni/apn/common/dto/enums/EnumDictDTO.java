package com.altomni.apn.common.dto.enums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.dict.*;
import com.altomni.apn.common.domain.dict.mapping.EnumIndustryMapping;
import com.altomni.apn.common.domain.dict.mapping.EnumJobFunctionMapping;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumDictDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "The dictCode for find special dict data")
    private String id;

    @ApiModelProperty(value = "The Chinese annotation for target value")
    private String label;

    @ApiModelProperty(value = "The English annotation for target value")
    private String labelCn;

    @ApiModelProperty(value = "The English annotation for target value")
    private String labelEn;

    @ApiModelProperty(value = "The label words for web")
    private String value;

    @ApiModelProperty(value = "itemValue children list")
    private List<EnumDictDTO> children;

    @ApiModelProperty(value = "sign which itemValue is belongs to. when 0 then it's a parents itemValue")
    private String parentId;

    @ApiModelProperty(value = "sign checked for webSide")
    private Boolean checked;

    @ApiModelProperty(value = "The front display value of target value")
    private String display;

    @ApiModelProperty(value = "The name for target value")
    private String name;

    @JsonIgnore
    private Integer enDisplayOrder;

    @JsonIgnore
    private Integer cnDisplayOrder;

    @ApiModelProperty(value = "该industries和谁的jobFunctions一样")
    private Long jobFunctionsForIndustryId;

    private String countryCode;

    public static EnumDictDTO fromBizDict(EnumIndustry b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        dto.setLabelEn(b.getEnDisplay());
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        if (ObjectUtil.isNotEmpty(b.getParentCategory())) {
            dto.setParentId(b.getParentCategory());
        } else {
            dto.setParentId(Constants.PARENT_NODE_ID);
        }
        dto.setChecked(false);
        dto.setJobFunctionsForIndustryId(b.getJobFunctionsForIndustryId());
        return dto;
    }

    public static EnumDictDTO fromBizDict(EnumIndustryMapping b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        dto.setLabelEn(b.getEnDisplay());
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        if (ObjectUtil.isNotEmpty(b.getParentCategory())) {
            dto.setParentId(b.getParentCategory());
        } else {
            dto.setParentId(Constants.PARENT_NODE_ID);
        }
        dto.setChecked(false);
        dto.setJobFunctionsForIndustryId(b.getJobFunctionsForIndustryId());
        return dto;
    }

    public static EnumDictDTO formatForCreation(EnumIndustry b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            if (b.getIsParent()) {
                dto.setId(b.getId() + Constants.PARENT_EN);
                dto.setValue(b.getId() + Constants.PARENT_EN);
            } else {
                dto.setId(StrUtil.toString(b.getId()));
                dto.setValue(StrUtil.toString(b.getId()));
            }
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        dto.setLabelEn(b.getEnDisplay());
        if (ObjectUtil.isNotEmpty(b.getParentCategory())) {
            dto.setParentId(b.getParentCategory());
        } else {
            dto.setParentId(Constants.PARENT_NODE_ID);
        }
        dto.setChecked(false);
        dto.setJobFunctionsForIndustryId(b.getJobFunctionsForIndustryId());
        return dto;
    }

    public static EnumDictDTO fromBizDict(EnumWorkAuthorization b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        if (ObjectUtil.isNotEmpty(b.getParentCategory())) {
            dto.setParentId(b.getParentCategory());
        } else {
            dto.setParentId(Constants.PARENT_NODE_ID);
        }
        dto.setName(b.getName());
        dto.setChecked(false);
        return dto;
    }

    public static EnumDictDTO fromBizDict(EnumDegree b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        dto.setParentId(Constants.PARENT_NODE_ID);
        dto.setChecked(false);
        dto.setName(b.getName());
        return dto;
    }

    public static EnumDictDTO fromEnumCountry(EnumCountry enumCountry) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(enumCountry.getId())) {
            dto.setId(StrUtil.toString(enumCountry.getId()));
        }
        dto.setLabel(enumCountry.getEnDisplay());
        dto.setLabelCn(enumCountry.getCnDisplay());
        if (ObjectUtil.isNotEmpty(enumCountry.getAreaCode())) {
            dto.setValue(StrUtil.toString(enumCountry.getAreaCode()));
        }
        dto.setName(enumCountry.getName());
        dto.setCountryCode(enumCountry.getCountryCode());
        return dto;
    }

    public static EnumDictDTO fromBizDict(EnumLevelOfExperience b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        return dto;
    }


    public static EnumDictDTO fromEnumIndustryMapping(EnumIndustryMapping b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        dto.setChecked(false);
        return dto;
    }


    public static EnumDictDTO fromEnumJobFunctionMapping(EnumJobFunctionMapping b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        dto.setChecked(false);
        return dto;
    }
}
