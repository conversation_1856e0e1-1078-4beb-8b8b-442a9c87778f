package com.altomni.apn.common.dto.xxljob;

import com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum;
import com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnumConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Convert;

@Data
@EqualsAndHashCode
public class XxlJobUpdateByTenantMessageConfigDTO {

    @Convert(converter = TenantMessageMinderConfigFieldCodeEnumConverter.class)
    private TenantMessageMinderConfigFieldCodeEnum type;

    private String oldValue;

    private String newValue;

}
