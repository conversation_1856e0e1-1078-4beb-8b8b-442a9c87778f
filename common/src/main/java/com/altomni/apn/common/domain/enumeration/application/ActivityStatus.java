package com.altomni.apn.common.domain.enumeration.application;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.Arrays;
import java.util.List;

/**
 * The ActivityStatus enumeration.
 */
public enum ActivityStatus implements ConvertedEnum<Integer> {
    Watching(0, null, "Watching"),
    Applied(1, 72, "Applied"), // Submitted to AM
    Qualified(2, 72, "Qualified"), // Qualified by AM
    Submitted(3, 30 * 24, "Submitted"), // Submitted to Client
    Interview(4, 30 * 24, "Interview"),
    Offered(5, null, "Offered"),
    Offer_Accepted(6, null, "Offer Accepted"),
    Offer_Rejected(7, null, "Offer Rejected"),
    Client_Rejected(8, null, "Client Rejected"),
    Started(9, null, "Started"),
    Called_Candidate(10, null, "Called Candidate"),
    Meet_Candidate_In_Person(11, null, "Meet Candidate in Person"),
    Shortlisted_By_Client(12, null, "Shortlisted by Client"),
    Internal_Rejected(13, null, "Internal Rejected"), // Rejected by AM
    Candidate_Quit(14, null, "Candidate Quit"),
    FAIL_TO_ONBOARD(90, null, "Fail to Onboard"),
    START_TERMINATED(91, null, "Start Terminated"),
    START_EXTENSION(92, null, "Start Extension"),
    START_FAIL_WARRANTY(94, null, "Start Fail Warranty");


    private final Integer dbValue;

    private final Integer dormantTime; // hours

    private final String description;

    public static List<Integer> startApplications = Arrays.asList(ActivityStatus.Started.toDbValue(), ActivityStatus.START_TERMINATED.toDbValue(), ActivityStatus.START_EXTENSION.toDbValue());

    ActivityStatus(Integer dbValue, Integer dormantTime, String description) {
        this.dbValue = dbValue;
        this.dormantTime = dormantTime;
        this.description = description;
    }

    public Integer getDormantTime() { return dormantTime; }

    public boolean hasDormantTime() { return dormantTime != null; }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDescription() {
        return description;
    }

    // static resolving:
    public static final ReverseEnumResolver<ActivityStatus, Integer> resolver =
        new ReverseEnumResolver<>(ActivityStatus.class, ActivityStatus::toDbValue);

    public static ActivityStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static boolean notWatchingAndApplied(ActivityStatus status) {
        return !equalsWatching(status) && !equalsApplied(status);
    }

    public static boolean equalsWatching(ActivityStatus status) {
        return ActivityStatus.Watching.equals(status);
    }

    public static boolean equalsApplied(ActivityStatus status) {
        return ActivityStatus.Applied.equals(status);
    }

    public static boolean equalsStarted(ActivityStatus status) {
        return ActivityStatus.Started.equals(status);
    }

    public static boolean equalsSubmitted(ActivityStatus status) {
        return ActivityStatus.Submitted.equals(status);
    }
    public static boolean equalsInterview(ActivityStatus status) {
        return ActivityStatus.Interview.equals(status);
    }
    public static boolean equalsOfferAccepted(ActivityStatus status) {
        return ActivityStatus.Offer_Accepted.equals(status);
    }
    public static boolean equalsOffered(ActivityStatus status) {
        return ActivityStatus.Offered.equals(status);
    }

    public static boolean equalsQualified(ActivityStatus status) {
        return ActivityStatus.Qualified.equals(status);
    }

    public static boolean equalsInternalRejected(ActivityStatus status) {
        return ActivityStatus.Internal_Rejected.equals(status);
    }

}
