package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TimeSheetBreakTimeTypeConverter extends AbstractAttributeConverter<TimeSheetBreakTimeType, Integer> {
    public TimeSheetBreakTimeTypeConverter() {
        super(TimeSheetBreakTimeType::toDbValue, TimeSheetBreakTimeType::fromDbValue);
    }
}
