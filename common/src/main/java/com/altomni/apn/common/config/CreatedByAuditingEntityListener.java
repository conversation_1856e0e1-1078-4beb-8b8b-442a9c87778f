package com.altomni.apn.common.config;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.AutoAbstractAuditingEntity;
import com.altomni.apn.common.domain.CreatedByAbstractAuditingEntity;
import com.altomni.apn.common.utils.SecurityUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.time.Instant;
import java.util.Optional;

/**
 * Custom Auditing Entity Listener to handle createdBy and lastModifiedBy fields.
 */
@Component
public class CreatedByAuditingEntityListener {

    /**
     * Populate the auditing fields before persisting an entity.
     */
    @PrePersist
    public void setCreatedFields(CreatedByAbstractAuditingEntity entity) {
        // If createdBy already has a value, retain it; otherwise set it to the current user
        if (StrUtil.isBlank(entity.getCreatedBy())) {
            entity.setCreatedBy(getCurrentAuditor());
        }
        if (ObjectUtil.isEmpty(entity.getCreatedDate())) {
            entity.setCreatedDate(Instant.now());
        }
        entity.setLastModifiedBy(null);
        entity.setLastModifiedDate(null);
    }

    /**
     * Populate the auditing fields before updating an entity.
     */
    @PreUpdate
    public void setLastModifiedFields(CreatedByAbstractAuditingEntity entity) {
        // Update lastModified fields
        entity.setLastModifiedBy(getCurrentAuditor());
        entity.setLastModifiedDate(Instant.now());
    }

    public String getCurrentAuditor() {
        String loginUserUid = SecurityUtils.getUserUid();
        if (!StringUtils.hasText(loginUserUid)) {
            return Optional.of(Constants.SYSTEM).get();
        }
        return Optional.of(loginUserUid).orElse(Constants.SYSTEM);
    }
}
