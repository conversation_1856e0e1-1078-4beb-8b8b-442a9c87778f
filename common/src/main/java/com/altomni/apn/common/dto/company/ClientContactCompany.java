package com.altomni.apn.common.dto.company;

import com.altomni.apn.common.enumeration.enums.ClientContactCompanyType;
import lombok.Data;

import java.util.Objects;

@Data
public class ClientContactCompany {
    private Long id;
    private String name;
    private ClientContactCompanyType type;
    //在候选人处使用
    private Long clientContactId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ClientContactCompany that = (ClientContactCompany) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
