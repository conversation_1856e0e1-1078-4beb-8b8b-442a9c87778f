package com.altomni.apn.common.vo.user;

import com.altomni.apn.common.domain.enumeration.user.LanguageTypeEnum;
import com.altomni.apn.common.domain.enumeration.user.LanguageTypeEnumConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserLanguageVO {

    private Long userId;

    @Convert(converter = LanguageTypeEnumConverter.class)
    private LanguageTypeEnum language;

}
