package com.altomni.apn.common.config;

import com.altomni.apn.common.dto.TitleInfoDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * api通用方法
 *
 * <AUTHOR>
 */
@Configuration
public class CommonApiMultilingualConfig {

    @Resource
    CommonRedisService commonRedisService;

    public String getTitleInfoByKeyAndLanguage(String key, String redisKey, Map<String, TitleInfoDTO> map) {
        String title = check(key, redisKey, map);
        return title;
    }

    public String getTitleInfoByKeyAndLanguage(String key, String redisKey, List<Object> params, Map<String, TitleInfoDTO> map) {

        String title = check(key, redisKey, map);

        if (!params.isEmpty()) {
            int z = 1;
            for (Object obj : params) {
                title = title.replace("#" + z, obj.toString());
                z++;
            }
        }
        return title;
    }

    private String check(String key, String redisKey, Map<String, TitleInfoDTO> map) {

        if (!map.containsKey(key)) {
            throw new CustomParameterizedException("The key is invalid");
        }

        String lang = commonRedisService.get(redisKey);
        String title = null;
        if (StringUtils.isBlank(lang) || lang.equals("EN")) {
            title = map.get(key).getTitleEng();
        } else {
            title = map.get(key).getTitleChina();
        }
        return title;
    }
}