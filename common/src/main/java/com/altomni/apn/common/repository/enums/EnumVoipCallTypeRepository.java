package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumEthnicity;
import com.altomni.apn.common.domain.dict.EnumVoipCallType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;


/**
 * Spring Data JPA repository for the Enum VoipCallType entity.
 */
@Repository
public interface EnumVoipCallTypeRepository extends JpaRepository<EnumVoipCallType, Long> {

    Optional<EnumVoipCallType> findByNameLike(String name);

}
