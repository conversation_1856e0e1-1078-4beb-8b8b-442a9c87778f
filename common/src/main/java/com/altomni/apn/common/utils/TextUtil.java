package com.altomni.apn.common.utils;

import org.apache.tika.exception.TikaException;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.metadata.TikaMetadataKeys;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.parser.Parser;
import org.apache.tika.sax.BodyContentHandler;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import java.io.IOException;
import java.io.InputStream;

public class TextUtil {

    private static String extractPlainText(MultipartFile file) throws IOException, TikaException, SAXException {
        InputStream input = file.getInputStream();
        BodyContentHandler textHandler = new BodyContentHandler();
        Metadata metadata = new Metadata();
        metadata.set(TikaMetadataKeys.RESOURCE_NAME_KEY, file.getOriginalFilename());

        AutoDetectParser parser = new AutoDetectParser();
        ParseContext context = new ParseContext();
        context.set(Parser.class, parser);
        parser.parse(input, textHandler, metadata, context);

        return textHandler.toString();
    }

    public static String extractSimpleText(MultipartFile file) throws TikaException, IOException, SAXException {
        return extractPlainText(file);
    }

    public static String extractSimpleText(String fileName, InputStream fileInputStream) throws TikaException, SAXException, IOException {
        BodyContentHandler textHandler = new BodyContentHandler();
        Metadata metadata = new Metadata();
        metadata.set(TikaMetadataKeys.RESOURCE_NAME_KEY, fileName);

        AutoDetectParser parser = new AutoDetectParser();
        ParseContext context = new ParseContext();
        context.set(Parser.class, parser);
        parser.parse(fileInputStream, textHandler, metadata, context);

        return textHandler.toString();
    }
}
