package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.enumeration.user.UserRoleConverter;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Convert;
import java.io.Serializable;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessKpiUserVO implements Serializable {

    private static final long serialVersionUID = -8220679430448476503L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private Long userId;

    //userRole是co-am时使用
    private String country;

    @ApiModelProperty(value = "For IPG version only.")
    @Convert(converter = UserRoleConverter.class)
    private UserRole userRole;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal percentage = new BigDecimal(0);

    @ApiModelProperty(value = "For general version only.")
    private Integer currency;

    @ApiModelProperty(value = "For general version only.")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal amount;

//    @Getter
//    @Setter
//    private UserBriefDTO user;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentRecruitmentProcessId() {
        return talentRecruitmentProcessId;
    }

    public void setTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public UserRole getUserRole() {
        return userRole;
    }

    public void setUserRole(UserRole userRole) {
        this.userRole = userRole;
    }

    public BigDecimal getPercentage() {
        return percentage == null ? null : new BigDecimal(Constants.DECIMAL_FORMAT.format(percentage));
    }

    public void setPercentage(BigDecimal percentage) {
        this.percentage = percentage;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public BigDecimal getAmount() {
        return amount == null ? null : new BigDecimal(Constants.DECIMAL_FORMAT.format(amount));
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Override
    public String toString() {
        return "TalentRecruitmentProcessKpiUserVO{" +
                "id=" + id +
                ", talentRecruitmentProcessId=" + talentRecruitmentProcessId +
                ", userId=" + userId +
                ", userRole=" + userRole +
                ", percentage=" + percentage +
                ", currency=" + currency +
                ", amount=" + amount +
//                ", user=" + user +
                '}';
    }
}
