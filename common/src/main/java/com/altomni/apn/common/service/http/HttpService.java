package com.altomni.apn.common.service.http;

import com.altomni.apn.common.dto.http.HttpResponse;
import okhttp3.Headers;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.apache.http.client.entity.UrlEncodedFormEntity;

import java.io.IOException;

/**
 * Http Service
 *
 * <AUTHOR>
 */
public interface HttpService {

    HttpResponse get(String url) throws IOException;

    byte[] getBytes(String url) throws IOException;

    HttpResponse get(String url, Headers headers) throws IOException;

    HttpResponse post(String url, String content) throws IOException;

    HttpResponse asyncPost(String url, String requestBody);

    HttpResponse postPlainText(String url, String content) throws IOException;

    HttpResponse post(String url) throws IOException;

    HttpResponse post(String url, Headers headers, String requestBody) throws IOException;

    HttpResponse post(String url, Headers headers, MultipartBody requestBody) throws IOException;

    HttpResponse patch(String url, Headers headers, String requestBody) throws IOException;

    HttpResponse put(String url) throws IOException;

    HttpResponse put(String url, String content) throws IOException;

    HttpResponse put(String url, Headers headers, String content) throws IOException;

    String post(String url, UrlEncodedFormEntity param, String serviceName) throws IOException;

    HttpResponse delete(String url) throws IOException;

    HttpResponse delete(String url, Headers headers) throws IOException;
}
