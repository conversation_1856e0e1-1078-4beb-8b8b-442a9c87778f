package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class PayRateContentTypeConverter extends AbstractAttributeConverter<PayRateContentType, Integer> {
    public PayRateContentTypeConverter() {
        super(PayRateContentType::toDbValue, PayRateContentType::fromDbValue);
    }
}
