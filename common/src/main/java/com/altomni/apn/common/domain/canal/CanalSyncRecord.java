package com.altomni.apn.common.domain.canal;

import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "canal_sync_record")
public class CanalSyncRecord implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "task_id")
    private Long taskId;

    @Column(name = "type")
    private SyncIdTypeEnum type;

    @Column(name = "retry_count")
    private Integer retryCount;

    @Column(name = "reason")
    private FailReasonEnum reason;

    @Column(name = "error_message")
    private String errorMessage;

    @Column(name = "created_date")
    private Instant createdDate;

    @Column(name = "priority")
    private Integer priority;

}
