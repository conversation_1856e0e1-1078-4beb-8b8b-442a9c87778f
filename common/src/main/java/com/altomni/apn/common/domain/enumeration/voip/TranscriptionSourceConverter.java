package com.altomni.apn.common.domain.enumeration.voip;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TranscriptionSourceConverter extends AbstractAttributeConverter<TranscriptionSource, Integer> {
    public TranscriptionSourceConverter() {
        super(TranscriptionSource::toDbValue, TranscriptionSource::fromDbValue);
    }
}
