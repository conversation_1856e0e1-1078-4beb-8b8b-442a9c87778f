package com.altomni.apn.common.dto.job;


import com.altomni.apn.common.domain.job.JobNote;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A JobNote.
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobNoteDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;


    @ApiModelProperty(value = "note content", required = true)
    @NotNull
    private String note;

    @ApiModelProperty(value = "The job id for the note", required = true)
    @NotNull
    private Long jobId;

    @ApiModelProperty(value = "flag to sync to calendar ")
    @NotNull
    private Boolean syncToCalendar;

    @ApiModelProperty(value = "the time to send alert")
    private Instant alertTime;

    @ApiModelProperty(value = "the last modified time")
    private Instant lastModifiedDate;

    @ApiModelProperty(value = "the last modified user")
    private FullNameUserDTO lastModifiedBy;

    @ApiModelProperty(value = "the created time")
    private Instant createdDate;

    @ApiModelProperty(value = "the creater user")
    private FullNameUserDTO createdBy;

    //TODO: remove

//    @JsonIgnore
//    @ApiModelProperty(value = "The user id (current user from access token) who created the note. Ready only. change this will be ignored.")
//    private Long userId;
//
//    @JsonIgnore
//    @ApiModelProperty(value = "note title", required = true)
//    @NotNull
//    private String title = "empty";
//
//    @JsonIgnore
//    @ApiModelProperty(value = "If the note is visible to all users. Default is true")
//    private Boolean visible = true;
//
//    @JsonIgnore
//    @ApiModelProperty(value = "the note priority. Default is Normal", allowableValues = "Normal, High")
//    private NotePriority priority = NotePriority.NORMAL;
//
//    // ************************************relate entity*********************************************
//    @JsonIgnore
//    @ApiModelProperty(value = "The user entity who created the note. Read Only.")
//    private User user;
    //end of remove

    public static JobNoteDTO fromJobNote(JobNote j) {
        JobNoteDTO dto = new JobNoteDTO();
        ServiceUtils.myCopyProperties(j, dto);
        return dto;
    }

    public static JobNote toJobNote(JobNoteDTO j) {
        JobNote entity = new JobNote();
        ServiceUtils.myCopyProperties(j, entity);
        return entity;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNote() {
        return note;
    }

    public JobNoteDTO note(String note) {
        this.note = note;
        return this;
    }

    public void setNote(String note) {
        this.note = note;
    }


    //    public String getTitle() {
//        return title;
//    }
//
//    public JobNoteDTO title(String title) {
//        this.title = title;
//        return this;
//    }
//
//    public void setTitle(String title) {
//        this.title = title;
//    }
//    public Boolean isVisible() {
//        return visible;
//    }
//
//    public JobNoteDTO visible(Boolean visible) {
//        this.visible = visible;
//        return this;
//    }
//
//    public void setVisible(Boolean visible) {
//        this.visible = visible;
//    }
//
//    public NotePriority getPriority() {
//        return priority;
//    }
//
//    public JobNoteDTO priority(NotePriority priority) {
//        this.priority = priority;
//        return this;
//    }
//
//    public JobNoteDTO priority(Integer value) {
//        this.priority = NotePriority.fromDbValue(value);
//        return this;
//    }
//
//    public void setPriority(NotePriority priority) {
//        this.priority = priority;
//    }
//
//    public Boolean getVisible() {
//        return visible;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//    public User getUser() {
//        return user;
//    }
//
//    public JobNoteDTO user(User user) {
//        this.user = user;
//        if (ObjectUtil.isNotNull(user)) {
//            this.userId = user.getId();
//        }
//        return this;
//    }
//
//    public void setUser(User user) {
//        this.user = user;
//        if (ObjectUtil.isNotNull(user)) {
//            this.userId = user.getId();
//        }
//    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Boolean getSyncToCalendar() {
        return syncToCalendar;
    }

    public void setSyncToCalendar(Boolean syncToCalendar) {
        this.syncToCalendar = syncToCalendar;
    }

    public Instant getAlertTime() {
        return alertTime;
    }

    public void setAlertTime(Instant alertTime) {
        this.alertTime = alertTime;
    }

    public Instant getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Instant lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public FullNameUserDTO getLastModifiedBy() {
        return lastModifiedBy;
    }

    public void setLastModifiedBy(FullNameUserDTO lastModifiedBy) {
        this.lastModifiedBy = lastModifiedBy;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public FullNameUserDTO getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(FullNameUserDTO createdBy) {
        this.createdBy = createdBy;
    }

    //    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JobNoteDTO jobNote = (JobNoteDTO) o;
        if (jobNote.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), jobNote.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "JobNoteDTO{" +
                "id=" + id +
                ", note='" + note + '\'' +
                ", jobId=" + jobId +
                ", syncToCalendar=" + syncToCalendar +
                ", alertTime=" + alertTime +
                '}';
    }
}
