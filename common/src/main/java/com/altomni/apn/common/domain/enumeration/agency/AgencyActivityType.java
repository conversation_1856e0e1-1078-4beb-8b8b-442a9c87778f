package com.altomni.apn.common.domain.enumeration.agency;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ActiveStatus enumeration.
 */
public enum AgencyActivityType implements ConvertedEnum<Integer> {

    AGENCY_STATUS(0),

    JOB_SHARE(1),

    APPLICATION_UPDATE(2),

    EMAIL_CONTACT(3);

    private final int dbValue;

    AgencyActivityType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<AgencyActivityType, Integer> resolver =
        new ReverseEnumResolver<>(AgencyActivityType.class, AgencyActivityType::toDbValue);

    public static AgencyActivityType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
