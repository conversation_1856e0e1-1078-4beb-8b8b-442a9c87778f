package com.altomni.apn.common.domain.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_delivery_industry_relation")
public class UserDeliveryIndustryRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "enum_industry_mapping_id")
    private Long enumIndustryMappingId;

    @Column(name = "count")
    private Long count;

    @Column(name = "top")
    private Boolean top;

    @Column(name = "updated")
    private Boolean updated;

    public UserDeliveryIndustryRelation(Long id, Long userId, Long enumIndustryMappingId, Long count, Boolean top) {
        this.id = id;
        this.userId = userId;
        this.enumIndustryMappingId = enumIndustryMappingId;
        this.count = count;
        this.top = top;
    }
}
