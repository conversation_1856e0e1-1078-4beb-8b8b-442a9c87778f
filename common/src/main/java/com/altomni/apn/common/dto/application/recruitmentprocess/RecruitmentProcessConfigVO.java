package com.altomni.apn.common.dto.application.recruitmentprocess;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.RecruitmentProcessType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * A RecruitmentProcessConfigVO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitmentProcessConfigVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4404089243568703692L;

    private Long id;

    private RecruitmentProcessType recruitmentProcessType;

    private Long tenantId;

    private JobType jobType;

    private String name;

    private String description;

    private ActiveStatus status;

    private List<NodeType> nodeTypes;

    private List<JobType> jobTypes;
}
