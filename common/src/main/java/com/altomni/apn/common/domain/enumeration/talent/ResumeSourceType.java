package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ResumeSourceType implements ConvertedEnum<Integer> {
    //未知
    UNKNOWN(0),
    //领英
    LINKEDIN(1),
    //猎聘
    LIEPIN(2),
    //Dice
    DICE(3),
    //Career builder
    CAREER_BUILDER(4),
    //Indeed
    INDEED(5),
    //Monster
    MONSTER(6),
    //Jazz HR
    JAZZHR(7),
    //推荐
    REFERRAL(8),
    //社交媒体
    SOCIAL_MEDIA(9),
    //大学招聘
    CAMPUS_RECRUITING(10),
    //营销活动
    MARKETING_EVENT(11),
    //Intellipro
    INTELLIPRO_WEBSITE(12),
    //Contractor
    CONTRACTOR_PORTAL(13),
    //Common pool
    COMMON_POOL(14),
    //boss直聘
    ZHIPIN(15),
    //拉钩
    LAGOU(16),
    //脉脉
    MAIMAI(17),
    //Cold call
    COLD_CALL(18),
    //智联招聘
    ZHAOPIN(19),
    //前程无忧
    JOB51(20),
    //Job street
    JOB_STREET(21),
    //Personal Connection
    PERSONAL_CONNECTION(22),


    //user shared link(fb/wechat/linkedin/direct link)
    SHARED_LINK(23),

    DODA_X(24),

    AGENCY(25),
    // Uoffer
    UOFFER(26),
    ARRIVALS(31),
    SOFT_SOURCE(32),
    LHP_CO_LTD(33),
    DATA_CENTER_XPERT_INC(34),
    IOETECH_KK(35),

    OTHER(30);

    private final int dbValue;

    ResumeSourceType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ResumeSourceType, Integer> resolver =
        new ReverseEnumResolver<>(ResumeSourceType.class, ResumeSourceType::toDbValue);

    public static ResumeSourceType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
