package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessIpgOfferLetterCostRateList implements Serializable {

    private static final long serialVersionUID = -2762018308813246950L;

    private List<TalentRecruitmentProcessIpgOfferLetterCostRateVO> taxBurdenRate;

    private List<TalentRecruitmentProcessIpgOfferLetterCostRateVO> mspRate;

    private List<TalentRecruitmentProcessIpgOfferLetterCostRateVO> immigrationCost;

}
