package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessForJobCountByStatusVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Integer total;

    private Integer submitToJob;

    private Integer submitToClient;

    private Integer interview;

    private Integer offer;

    private Integer offerAccept;

    private Integer commission;

    private Integer onboard;

    private Integer eliminated;
}
