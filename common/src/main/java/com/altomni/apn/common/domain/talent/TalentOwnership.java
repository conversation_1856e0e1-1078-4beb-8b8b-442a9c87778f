package com.altomni.apn.common.domain.talent;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipTypeConverter;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipUserRole;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipUserRoleConverter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A TalentOwnership.
 */
@Entity
@Table(name = "talent_ownership")
public class TalentOwnership extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -8936022325066725626L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "talent_id", updatable = false)
    private Long talentId;

    @NotNull
    @Column(name = "user_id")
    private Long userId;

//    @Column(name = "tenant_id")
//    private Long tenantId;

    @NotNull
    @Convert(converter = TalentOwnershipTypeConverter.class)
    @Column(name = "ownership_type")
    private TalentOwnershipType ownershipType;

    @Convert(converter = TalentOwnershipUserRoleConverter.class)
    @Column(name = "user_role")
    private TalentOwnershipUserRole userRole;

    @NotNull
    @Column(name = "expire_time")
    private Instant expireTime;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    //crm contact share user property
    @Column(name = "auto_assigned")
    private Boolean autoAssigned = false;

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentId() {
        return talentId;
    }

    public TalentOwnership talentId(Long talentId) {
        this.talentId = talentId;
        return this;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getUserId() {
        return userId;
    }

    public TalentOwnership userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public TalentOwnershipType getOwnershipType() {
        return ownershipType;
    }

    public TalentOwnership ownershipType(TalentOwnershipType ownershipType) {
        this.ownershipType = ownershipType;
        return this;
    }

    public void setOwnershipType(TalentOwnershipType ownershipType) {
        this.ownershipType = ownershipType;
    }

    public TalentOwnershipUserRole getUserRole() {
        return userRole;
    }

    public TalentOwnership userRole(TalentOwnershipUserRole userRole) {
        this.userRole = userRole;
        return this;
    }

    public void setUserRole(TalentOwnershipUserRole userRole) {
        this.userRole = userRole;
    }

    public Instant getExpireTime() {
        return expireTime;
    }

    public TalentOwnership expireTime(Instant expireTime) {
        this.expireTime = expireTime;
        return this;
    }

    public void setExpireTime(Instant expireTime) {
        this.expireTime = expireTime;
    }

    public TalentOwnership talentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
        return this;
    }

    public Long getTalentRecruitmentProcessId() {
        return talentRecruitmentProcessId;
    }

    public void setTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
    }

    public Boolean getAutoAssigned() {
        return autoAssigned;
    }

    public void setAutoAssigned(Boolean autoAssigned) {
        this.autoAssigned = autoAssigned;
    }

    //    public Long getTenantId() {
//        return tenantId;
//    }
//
//    public void setTenantId(Long tenantId) {
//        this.tenantId = tenantId;
//    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TalentOwnership talentOwnership = (TalentOwnership) o;
        if (talentOwnership.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), talentOwnership.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "TalentOwnership{" +
            "id=" + id +
            ", talentId=" + talentId +
            ", userId=" + userId +
            ", ownershipType=" + ownershipType +
            ", userRole=" + userRole +
            ", expireTime=" + expireTime +
            ", talentRecruitmentProcessId=" + talentRecruitmentProcessId +
            '}';
    }
}
