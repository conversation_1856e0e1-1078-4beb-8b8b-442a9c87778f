package com.altomni.apn.common.aop.datasync.annotation;

import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.enumeration.enums.PlatformType;
import lombok.NonNull;

import java.lang.annotation.*;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface DataSyncAnnotation {

    /**
     * 同步数据类型
     * @return
     */
    @NonNull
    SyncIdTypeEnum dataType();

    /**
     * 同步数据平台
     * @return
     */
    PlatformType platformType() default PlatformType.ES;

}
