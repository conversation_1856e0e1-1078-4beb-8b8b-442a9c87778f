package com.altomni.apn.common.utils;

import com.altomni.apn.common.config.thread.MDCContextCallable;
import com.altomni.apn.common.config.thread.MDCContextExecutor;
import com.altomni.apn.common.config.thread.MDCContextRunnable;
import org.slf4j.MDC;

import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * MDC 上下文传递工具类
 * 提供便捷的方法来在异步执行中保持 traceId 等 MDC 上下文信息
 * 
 * <AUTHOR>
 */
public class MDCUtils {
    
    /**
     * 包装 Runnable，使其在执行时保持当前线程的 MDC 上下文
     */
    public static Runnable wrapRunnable(Runnable runnable) {
        return MDCContextRunnable.wrap(runnable);
    }
    
    /**
     * 包装 Callable，使其在执行时保持当前线程的 MDC 上下文
     */
    public static <T> Callable<T> wrapCallable(Callable<T> callable) {
        return MDCContextCallable.wrap(callable);
    }
    
    /**
     * 包装 Executor，使其在执行任务时自动传递 MDC 上下文
     */
    public static Executor wrapExecutor(Executor executor) {
        if (executor instanceof MDCContextExecutor) {
            return executor;
        }
        return new MDCContextExecutor(executor);
    }
    
    /**
     * 创建一个保持 MDC 上下文的 CompletableFuture.runAsync
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable) {
        return CompletableFuture.runAsync(wrapRunnable(runnable));
    }
    
    /**
     * 创建一个保持 MDC 上下文的 CompletableFuture.runAsync，使用指定的 Executor
     */
    public static CompletableFuture<Void> runAsync(Runnable runnable, Executor executor) {
        return CompletableFuture.runAsync(wrapRunnable(runnable), executor);
    }
    
    /**
     * 创建一个保持 MDC 上下文的 CompletableFuture.supplyAsync
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return CompletableFuture.supplyAsync(() -> {
            Map<String, String> previousContext = MDC.getCopyOfContextMap();
            try {
                MDC.clear();
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                return supplier.get();
            } finally {
                MDC.clear();
                if (previousContext != null) {
                    MDC.setContextMap(previousContext);
                }
            }
        });
    }
    
    /**
     * 创建一个保持 MDC 上下文的 CompletableFuture.supplyAsync，使用指定的 Executor
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> supplier, Executor executor) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return CompletableFuture.supplyAsync(() -> {
            Map<String, String> previousContext = MDC.getCopyOfContextMap();
            try {
                MDC.clear();
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                return supplier.get();
            } finally {
                MDC.clear();
                if (previousContext != null) {
                    MDC.setContextMap(previousContext);
                }
            }
        }, executor);
    }
    
    /**
     * 包装 Function，使其在执行时保持当前线程的 MDC 上下文
     */
    public static <T, R> Function<T, R> wrapFunction(Function<T, R> function) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return (input) -> {
            Map<String, String> previousContext = MDC.getCopyOfContextMap();
            try {
                MDC.clear();
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                return function.apply(input);
            } finally {
                MDC.clear();
                if (previousContext != null) {
                    MDC.setContextMap(previousContext);
                }
            }
        };
    }
    
    /**
     * 获取当前 MDC 上下文的副本
     */
    public static Map<String, String> getCopyOfContextMap() {
        return MDC.getCopyOfContextMap();
    }
    
    /**
     * 设置 MDC 上下文
     */
    public static void setContextMap(Map<String, String> contextMap) {
        MDC.clear();
        if (contextMap != null) {
            MDC.setContextMap(contextMap);
        }
    }
}
