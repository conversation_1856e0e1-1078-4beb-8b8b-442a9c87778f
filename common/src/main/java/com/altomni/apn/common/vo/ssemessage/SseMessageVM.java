package com.altomni.apn.common.vo.ssemessage;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class SseMessageVM implements Serializable {

    private Long userId;

    private Long tenantId;

    private Map<String, Object> data = new HashMap<>();

    public SseMessageVM() {}

    public SseMessageVM(Long userId, Long tenantId, Map<String, Object> data) {
        this.userId = userId;
        this.tenantId = tenantId;
        this.data = data;
    }
}
