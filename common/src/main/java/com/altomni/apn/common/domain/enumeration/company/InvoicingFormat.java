package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * invoicing body enum
 */
public enum InvoicingFormat implements ConvertedEnum<Integer> {

    PDF(0, "Pdf"),
    OFD(1, "Ofd"),
    XML(2,"Xml"),
    ;
    private final int dbValue;

    private final String description;

    InvoicingFormat(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoicingFormat, Integer> resolver = new ReverseEnumResolver<>(InvoicingFormat.class, InvoicingFormat::toDbValue);

    public static InvoicingFormat fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
