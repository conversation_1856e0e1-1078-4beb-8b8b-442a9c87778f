package com.altomni.apn.common.enumeration.enums;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum UploadTypeEnum implements ConvertedEnum<Integer> {
    RESUME(1, "resume"),
    RESUME_DISPLAYS(2, "resumeDisplays"),
    PORTRAIT(3, "portrait"),
    JD(4, "jd"),
    <PERSON><PERSON>(5, "cv"),
    LOGO(6, "logo"),
    EMAIL_ATTACHMENT(7, "emailAttachment"),
    ONBOARDING(8, "onBoarding"),
    GROUP_INVOICE(9, "groupinvoice"),
    RECEIPT(10, "receipt"),
    CONTRACT(11, "contract"),
    DOC(12, "doc"),
    ADDRESS_LIST(13, "addressList"),
    EMAIL_INVOICE_ATTACHMENT(14, "emailInvoiceAttachment"),
    CONTRACT_DISPLAY(15, "contractDisplays"),
    JOB_SHARING_CONTENT(16, "jobSharing"),
    RESUME_WATERMARK(17, "resumeWatermark"),
    JOB_SHARING_POSTER_IMAGE(18, "jobSharingBackgroundImage");


    private final Integer dbValue;

    private final String key;

    UploadTypeEnum(Integer dbValue, String key) {
        this.dbValue = dbValue;
        this.key = key;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getKey() {
        return key;
    }

    // static resolving:
    public static final ReverseEnumResolver<UploadTypeEnum, Integer> resolver =
            new ReverseEnumResolver<>(UploadTypeEnum.class, UploadTypeEnum::toDbValue);

    public static UploadTypeEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}