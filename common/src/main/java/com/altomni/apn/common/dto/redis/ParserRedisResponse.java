package com.altomni.apn.common.dto.redis;

import com.altomni.apn.common.enumeration.ParseStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ParserRedisResponse implements Serializable {

    private static final long serialVersionUID = 7547052176540537819L;

    private ParseStatus status;

    private String data;

    private ImagesInfoParser imagesInfo;

    /**
     * parse progress columns, only valid for create first
     */
    private String lastUpdateTime;

    private Long ttl; //redis key ttl (the remaining time to live in seconds of a key)

    private Set<String> documentLanguages;
}
