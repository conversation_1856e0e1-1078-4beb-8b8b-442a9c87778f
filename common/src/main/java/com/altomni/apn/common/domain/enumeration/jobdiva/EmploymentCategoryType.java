package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum EmploymentCategoryType implements ConvertedEnum<Integer>
{
    CONTRACTOR(0),
    CORP_TO_CORP(1);

    private final int dbValue;

    EmploymentCategoryType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<EmploymentCategoryType, Integer> resolver = new ReverseEnumResolver<>(EmploymentCategoryType.class, EmploymentCategoryType::toDbValue);

    public static EmploymentCategoryType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
