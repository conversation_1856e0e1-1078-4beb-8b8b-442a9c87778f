package com.altomni.apn.common.dto;

import com.altomni.apn.common.auth.LoginInformation;
import com.altomni.apn.common.dto.user.RoleDTO;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.ipg.resourceserver.user.SsoOidcUser;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;

import java.io.Serializable;
import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * A DTO representing a user, with only the public attributes.
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
@Accessors(chain = true)
public class LoginUserDTO implements SsoOidcUser, Serializable, LoginInformation {

    private Long id;

    private String uid;

    private String username;

    @ApiModelProperty(value = "first name")
    private String firstName;

    @ApiModelProperty(value = "last name")
    private String lastName;

    @ApiModelProperty(value = "email address. need to be unique.")
    private String email;

    @ApiModelProperty(value = "Whether user is activated. Default is true. Read Only.")
    private boolean activated;

    @ApiModelProperty(value = "url link to user's image")
    private String imageUrl;

    @ApiModelProperty(value = "phone number, this is used for consumer registered with phone.")
    private String phone;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    private Long tenantId;

    private Long teamId;

    private String tenantName;

    @ApiModelProperty(value = "Tenant Type in general")
    private TenantUserTypeEnum userType;

    private CredentialDTO credential;

    private List<RoleDTO> roles;

    private List<Long> companyIdsWithAm;

    private String ip;

    private String timezone;

    private List<Impersonation> availableImpersonations;

    @JsonIgnore
    private OidcUser oidcUser;

    @Override
    @JsonIgnore
    public String getName() {
        return username;
    }

    @Override
    @JsonIgnore
    public Optional<OidcUser> getOriginalOidcUser() {
        return Optional.ofNullable(oidcUser);
    }

    @JsonIgnore
    public List<? extends GrantedAuthority> getAuthorities() {
        if (roles == null) {
            return Collections.emptyList();
        }
        return roles.stream().map(role -> new SimpleGrantedAuthority(role.getName())).toList();
    }


    @Data
    @AllArgsConstructor
    public static class Impersonation{
        private Long userId;
        private String email;
        private Instant expireAt;
    }
}