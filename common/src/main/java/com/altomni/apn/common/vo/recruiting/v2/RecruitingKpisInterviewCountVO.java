package com.altomni.apn.common.vo.recruiting.v2;

import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonCountVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpisInterviewCountVO  extends RecruitingKpiCommonCountVO {
    // 面试相关指标
    private Long interview1;
    private Long interview2;
    private Long currentInterview1;
    private Long currentInterview2;
    private Long twoOrMoreInterviews;
    private Long currentTwoOrMoreInterviews;
    private Long interviewFinal;
    private Long currentInterviewFinal;
    private Long interviewTotal;
    private Long currentInterviewTotal;
    private Long uniqueInterviewTalents;
    private Long interviewTotalProcess;
    private Long currentInterviewTotalProcess;

    // 面试AI推荐相关指标
    private Long interviewTotalAiRecommendNum;
    private Long currentInterviewTotalAiRecommendNum;
    private Long interviewTotalProcessAiRecommendNum;
    private Long currentInterviewTotalProcessAiRecommendNum;
    private Long interview1AiRecommendNum;
    private Long interview2AiRecommendNum;
    private Long currentInterview1AiRecommendNum;
    private Long currentInterview2AiRecommendNum;
    private Long twoOrMoreInterviewsAiRecommendNum;
    private Long currentTwoOrMoreInterviewsAiRecommendNum;
    private Long interviewFinalAiRecommendNum;
    private Long currentInterviewFinalAiRecommendNum;

    // 面试精准推荐相关指标
    private Long interviewTotalPrecisionAiRecommendNum;
    private Long currentInterviewTotalPrecisionAiRecommendNum;
    private Long interviewNumProcessPrecisionAIRecommend;
    private Long currentInterviewNumProcessPrecisionAIRecommend;
    private Long interview1PrecisionAiRecommendNum;
    private Long currentInterview1PrecisionAiRecommendNum;
    private Long interview2PrecisionAiRecommendNum;
    private Long currentInterview2PrecisionAiRecommendNum;
    private Long twoOrMoreInterviewsPrecisionAiRecommendNum;
    private Long currentTwoOrMoreInterviewsPrecisionAiRecommendNum;
    private Long interviewFinalPrecisionAiRecommendNum;
    private Long currentInterviewFinalPrecisionAiRecommendNum;
}
