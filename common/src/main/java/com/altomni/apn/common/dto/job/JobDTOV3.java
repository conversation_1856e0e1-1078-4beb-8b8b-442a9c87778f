package com.altomni.apn.common.dto.job;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.OperationType;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.dto.FeeDTO;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessBriefDTO;
import com.altomni.apn.common.dto.company.CompanyBriefDTO;
import com.altomni.apn.common.dto.company.SalesLeadClientContactDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Transient;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobDTOV3 implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @ApiModelProperty(value = "The tenant id the job belongs. It is injected from the user entity when creating job. Read only.")
    private Long tenantId;

    private Long companyId;

    @ApiModelProperty(value = "companyId, companyName and industry")
    private CompanyBriefDTO company;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    @ApiModelProperty(value = "This is job internal code company used to identify the job")
    private String code;

    @ApiModelProperty(value = "Client contact entity, previous is clientContactName")
    private SalesLeadClientContactDTO clientContact;

    @ApiModelProperty(value = "The category of client contact")
    private Integer clientContactCategory;

    @ApiModelProperty(value = "only for application send email")
    private String clientContactEmails;

    @ApiModelProperty(value = "The date when the job starts")
    private Instant startDate;

    @ApiModelProperty(value = "The date when the job ends")
    private Instant endDate;

    @ApiModelProperty(value = "The date the job is posted to ATS or job boards. Read Only.")
    private Instant postingTime;

    @ApiModelProperty(value = "The last time the job was opened.")
    private Instant openTime;

    @ApiModelProperty(value = "The last time the job was published to tenant website.")
    private Instant tenantWebsitePostingTime;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
    private JobStatus status;


    @ApiModelProperty(value = "job lacation strings, one or more official city names ")
    private List<LocationDTO> locations;

    @ApiModelProperty(value = "job function strings, one or more official function names ")
    private List<EnumRelationDTO> jobFunctions;

    @ApiModelProperty(value = "The group of assigned user ids, one or more user role ")
    private List<AssignedUserDTO> assignedUsers;

    @ApiModelProperty(value = "The group of application assigned user ids")
    private List<AssignedUserDTO> accountManagers;

    @ApiModelProperty(value = "one or more required languages")
    private List<EnumRelationDTO> requiredLanguages;

    @ApiModelProperty(value = "one or more preferred languages")
    private List<EnumRelationDTO> preferredLanguages;

    private Boolean isPrivateJob;

    private Boolean favorite;

    //private Integer currency;
    private EnumRelationDTO currency;

    @ApiModelProperty(value = "number of openings for the job. Default is 1.")
    private Integer openings;

    @ApiModelProperty(value = "max application submissions")
    private Integer maxSubmissions;

    @ApiModelProperty(value = "Parser return bool string and save to job searchString column.")
    private List<JobBoolStringDTO> boolstr;

    private List<EnumRelationDTO> preferredDegrees;

    private EnumRelationDTO minimumDegreeLevel;

    @ApiModelProperty(value = "All user created notes on the job. Read only.")
    private List<JobNoteDTO> notes;

    private Long additionalInfoId;

    @ApiModelProperty(value = "Ipg job status. Default is no_published, the newly post job to ipg.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
    private JobStatus ipgJobStatus = JobStatus.NO_PUBLISHED;

    @ApiModelProperty(value = "Ipg job type", allowableValues = "CONTRACT, FULL_TIME, FULL_TIME_OR_PART_TIME, INTERNSHIP")
    private JobType ipgJobType;

    @Size(max = 16380, message = "The ipg JD text is too long.")
    @ApiModelProperty(value = "The ipg JD in text format, to save in DB.Jdtext does not transfer text content, directly transfer HTML content")
    private String ipgJobDescription;

    @ApiModelProperty(value = "IPG job operation type, allowing CREATE, UPDATE, CLOSE and NULL")
    private OperationType operationType;

    private List<Long> companyAmIds;

    @ApiModelProperty(value = "Brief information of recruitment process")
    private RecruitmentProcessBriefDTO recruitmentProcess;

    @ApiModelProperty(value = "job priority")
    private EnumRelationDTO priority;

    @ApiModelProperty(value = "jd file information")
    private RawJdFileDTO rawJdFile;

    @ApiModelProperty(value = "1：flexible location, 0：fixed work location")
    private boolean flexibleLocation;

    @ApiModelProperty(value = "If toIpg fails, the interface will report an error, but if toIpg succeeds, no echo will be displayed. only to ipg used.")
    private JSONObject ipgResponse;

    @ApiModelProperty(value = "IPG job requirements")
    private String ipgRequirements;

    @ApiModelProperty(value = "IPG job description")
    private String ipgResponsibilities;

    @ApiModelProperty(value = "IPG job summary")
    private String ipgSummary;

    @ApiModelProperty(value = "IPG job locations")
    private List<LocationDTO> ipgLocations;

    @ApiModelProperty(value = "IPG job 1：flexible location, 0：fixed work location")
    private boolean ipgFlexibleLocation;

    @ApiModelProperty(value = "IPG job Salary Range/Rate (Currency)")
    private IpgJobSalaryDTO ipgJobSalaryDTO;

    @ApiModelProperty(value = "IPG job 1: include default content, 0: nothing")
    private boolean ipgIncludeDefaultContent;

    @ApiModelProperty(value = "IPG job posting default content")
    private String ipgDefaultContent;


    private Float defaultCommission;

    private Long salesLeadId;
    private String salesLeadName;

    private String cooperationStatus;


    //AdditionalInfo begin
    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<SkillDTO> requiredSkills;

    @ApiModelProperty(value = "one or more preferred skills ")
    private List<SkillDTO> preferredSkills;

    @ApiModelProperty(value = "range of experience year")
    private RangeDTO experienceYearRange;

    @ApiModelProperty(value = "bill rate range")
    private RangeDTO billRange;

    @ApiModelProperty(value = "salary rate range")
    private RangeDTO salaryRange;

    @ApiModelProperty(value = "salary rate unit")
    private RateUnitType payType;

    @ApiModelProperty(value = "The job of department .")
    private String department;

    private String logo;

    @ApiModelProperty(value = "job requirements")
    private String requirements;

    @ApiModelProperty(value = "job summary")
    private String summary;

    @ApiModelProperty(value = "job description")
    private String responsibilities;

    @ApiModelProperty(value = "Reason for Recruitment")
    private String reasonForRecruitment;

    @ApiModelProperty(value = "Team Composition")
    private String teamComposition;

    @ApiModelProperty(value = "Preferred Companies")
    private String preferredCompanies;

    @ApiModelProperty(value = "Preferred Industry")
    private Long preferredIndustry;

    @ApiModelProperty(value = "Suggestions for Prospecting")
    private String suggestionsForProspecting;

    @ApiModelProperty(value = "Recommended Approach")
    private String recommendedApproach;

    @ApiModelProperty(value = "Estimated Job Fee & Currency")
    private FeeDTO estimatedJobFee;

    @ApiModelProperty(value = "Fee Structure")
    private String feeStructure;

    @ApiModelProperty(value = "Contract Signing Party")
    private String contractSigningParty;

    @ApiModelProperty(value = "Contract Duration")
    private Integer contractDuration;

    @JsonIgnore
    private String extendedInfo;
    //AdditionalInfo end

    private String createdBy;
    private Instant createdDate;
    private String lastModifiedBy;
    private Instant lastModifiedDate;
    @Transient
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SimpleUser createdUser;
    @Transient
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private SimpleUser lastModifiedUser;

    private boolean hasAiSourcingPermission;

    private boolean refreshAiSourcingAvailable;

    private boolean aiSourcingEnabled;

    public static JobDTOV3 fromJob(JobV3 job) {
        JobDTOV3 dto = Convert.convert(JobDTOV3.class, job);

        // set additional info
        JobDTOV3 additionalInfoExpand = Convert.convert(JobDTOV3.class, com.alibaba.fastjson.JSONObject.parseObject(job.getJobExtendedInfo()));
        Set<String> skips = Arrays.stream(ReflectUtil.getFieldsDirectly(JobV3.class, true)).map(Field::getName).collect(Collectors.toSet());
        if (additionalInfoExpand != null) {
            ServiceUtils.myCopyProperties(additionalInfoExpand, dto, skips);
            //puiblicDesc
//            dto.setPublicDesc(job.getJobAdditionalInfo().getPublicDesc());
        }

        JobDTOV3 additionalInfoLocalExpand = Convert.convert(JobDTOV3.class, com.alibaba.fastjson.JSONObject.parseObject(job.getJobLocalExtendedInfo()));
        if (additionalInfoLocalExpand != null) {
            ServiceUtils.myCopyProperties(additionalInfoLocalExpand, dto, skips);
        }

//        dto.setSummary(job.getJobAdditionalInfo().getSummary());
//        dto.setResponsibilities(job.getJobAdditionalInfo().getResponsibilities());
//        dto.setRequirements(job.getJobAdditionalInfo().getRequirements());
        // end

        dto.setAdditionalInfoId(job.getAdditionalInfoId());
        if (ObjectUtil.isNotEmpty(job.getMinimumDegreeId())) {
            EnumRelationDTO enumRelationDTO = new EnumRelationDTO();
            enumRelationDTO.setEnumId(String.valueOf(job.getMinimumDegreeId()));
            dto.setMinimumDegreeLevel(enumRelationDTO);
        }
        if (ObjectUtil.isNotEmpty(job.getEnumPriorityId())) {
            EnumRelationDTO enumRelationDTO = new EnumRelationDTO();
            enumRelationDTO.setEnumId(String.valueOf(job.getEnumPriorityId()));
            dto.setPriority(enumRelationDTO);
        }
        if (ObjectUtil.isNotEmpty(job.getCurrency())) {
            EnumRelationDTO enumRelationDTO = new EnumRelationDTO();
            enumRelationDTO.setEnumId(String.valueOf(job.getCurrency()));
            dto.setCurrency(enumRelationDTO);
        }
        if (ObjectUtil.isNotEmpty(job.getRecruitmentProcessId())){
            RecruitmentProcessBriefDTO recruitmentProcessBriefDTO = new RecruitmentProcessBriefDTO();
            recruitmentProcessBriefDTO.setId(job.getRecruitmentProcessId());
            dto.setRecruitmentProcess(recruitmentProcessBriefDTO);
        }

        return dto;
    }

    public void byDTOSetAdditionalInfo(AdditionalInfoDTO additionalInfoDTO) {
        requiredSkills = additionalInfoDTO.getRequiredSkills();

        preferredSkills = additionalInfoDTO.getPreferredSkills();

        experienceYearRange = additionalInfoDTO.getExperienceYearRange();

        billRange = additionalInfoDTO.getBillRange();

        salaryRange = additionalInfoDTO.getSalaryRange();

        payType = additionalInfoDTO.getPayType();

        department = additionalInfoDTO.getDepartment();

        logo = additionalInfoDTO.getLogo();

        requirements = additionalInfoDTO.getRequirements();

        summary = additionalInfoDTO.getSummary();

        responsibilities = additionalInfoDTO.getResponsibilities();

        reasonForRecruitment = additionalInfoDTO.getReasonForRecruitment();

        teamComposition = additionalInfoDTO.getTeamComposition();

        preferredCompanies = additionalInfoDTO.getPreferredCompanies();

        preferredIndustry = additionalInfoDTO.getPreferredIndustry();

        suggestionsForProspecting = additionalInfoDTO.getSuggestionsForProspecting();

        recommendedApproach = additionalInfoDTO.getRecommendedApproach();

        estimatedJobFee = additionalInfoDTO.getEstimatedJobFee();

        feeStructure = additionalInfoDTO.getFeeStructure();

        contractSigningParty = additionalInfoDTO.getContractSigningParty();
    }
}
