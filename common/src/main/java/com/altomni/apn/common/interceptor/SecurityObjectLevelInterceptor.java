package com.altomni.apn.common.interceptor;

import com.altomni.apn.common.auth.LoginInformation;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.config.env.CommonApplicationProperties;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.DispatcherType;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class SecurityObjectLevelInterceptor implements HandlerInterceptor {

    public static final String APN_INTERNAL_PIN = "APN_INTERNAL_PIN";

    @Resource
    private CachePermission cachePermission;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private CommonApplicationProperties commonApplicationProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (DispatcherType.REQUEST.name().equals(request.getDispatcherType().name())) {
            response.addHeader("responseTime", String.valueOf(System.currentTimeMillis()));
            // 1. GET /user/api/v3/tenant/4/user/100
            // 2. GET /user/api/v3/tenant/{tenantId}/user/{userId}
            // 3. GET /user/api/v3/tenant/{}/user/{}
            // 4. check permission
            // ******** 1. Output the original API path*********
            log.debug("requestURI: {} {}", request.getMethod(), request.getRequestURI());
            log.debug("enableObjectLevelSecurity: {}", commonApplicationProperties.isEnableObjectLevelSecurity());

            // ******** 2. Get formatted API path *********
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            String classRequestMapping = "";
            if (method.getDeclaringClass().isAnnotationPresent(RequestMapping.class)) {
                classRequestMapping = method.getDeclaringClass().getAnnotation(RequestMapping.class).value()[0];
            }
            RequestMapping methodAnnotation = handlerMethod.getMethodAnnotation(RequestMapping.class);
            String methodRequestMapping = methodAnnotation.value()[0];
            // /user/api/v3/tenant/{tenantId}/user/{userId}
            String requestURI = request.getMethod() + commonApplicationProperties.getContextPath() + classRequestMapping + methodRequestMapping;

            // ********* 3. Uniform API path ***********
            // /user/api/v3/tenant/{}/user/{}
            String uniformURI = requestURI.replaceAll("[{].*?[}]", "{}");
            response.addHeader("URI", uniformURI);

            // if the request is from internal service, skip checking api permission
            final String internalServicePin = request.getHeader(APN_INTERNAL_PIN);
            if (StringUtils.hasText(internalServicePin) && StringUtils.hasText(commonApplicationProperties.getApnInternalPin()) && internalServicePin.equals(commonApplicationProperties.getApnInternalPin())) {
                return Boolean.TRUE;
            }

            final LoginInformation loginUserDTO = SecurityUtils.getCurrentLoginInformation().orElse(new LoginUserDTO().setId(-1L));
            final Long userId = loginUserDTO.getId();
            this.countApi(loginUserDTO);
            //this.checkUserAuthentication(request, uniformURI, loginUserDTO);

            if (Boolean.FALSE.equals(commonApplicationProperties.isEnableObjectLevelSecurity())) {
                return Boolean.TRUE;
            }

            // check public APIs
            if (cachePermission.isPublicApi(uniformURI)) {
                return true;
            }

            cachePermission.checkImpersonationLoginPermission(loginUserDTO.getId(), uniformURI, request.getMethod());

            // if the request is from jobdiva service, skip checking api permission
            if ("/jobdiva".equals(commonApplicationProperties.getContextPath())) {
                return Boolean.TRUE;
            }

            if (SecurityUtils.isSystemAdmin() || SecurityUtils.isAdmin()) {
                return Boolean.TRUE;
            }

            cachePermission.checkUserPrivileges(userId, uniformURI, request.getMethod());
        }
        return true;
    }

    private void countApi(LoginInformation loginUserDTO) {
        final Long userId = loginUserDTO.getId();
        if (!userId.equals(-1L)) {
            final Long tenantId = loginUserDTO.getTenantId();
            commonRedisService.set(String.format(RedisConstants.STATISTIC_USER_ONLINE, tenantId, userId),
                    DateUtil.currentTime(DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z) + ";" + loginUserDTO.getTimezone(),
                    commonApplicationProperties.getOnlineUserStatisticPeriod());
        }
    }

//    /**
//     * if the current user is calling the logout API, then remove current user's key from redis
//     *
//     * @param request      http request from frontend
//     * @param uniformURI   uniform URI in which all path variables were replaced with {}
//     * @param loginUserDTO the current user's context
//     */
//    private void checkUserAuthentication(HttpServletRequest request, String uniformURI, LoginUserDTO loginUserDTO) {
//        String bearerToken = request.getHeader("Authorization");
//        String token = TokenUtil.extractToken(bearerToken);
//        Long userId = loginUserDTO.getId();
//        Long originalUserId = loginUserDTO.getOriginalUserId();
//        if (Objects.isNull(originalUserId) || originalUserId.longValue() < 0) {
//            originalUserId = userId;
//        }
//        if (uniformURI.endsWith("/logout")) {
//            this.logoutHandler(token, userId, originalUserId);
//        } else if (!userId.equals(-1L) && StringUtils.hasLength(token)) {
//            String activeUserKey = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, userId, originalUserId, token);
//            if (!commonRedisService.exists(activeUserKey)) {
//                throw new CustomParameterizedException(Status.UNAUTHORIZED.getStatusCode(), "Unauthorized", "The session expired or you logged out, please go to login!", null);
//            }
//            if (userId.equals(originalUserId)) {
//                commonRedisService.set(activeUserKey, "1", commonApplicationProperties.getActiveUserPeriod());
//            } else {
//                // impersonation login
//                commonRedisService.softExpire(activeUserKey, commonApplicationProperties.getActiveUserPeriod());
//            }
//        }
//    }

//    private void logoutHandler(String token, Long userIdFrom, Long userIdTo) {
//        if (!StringUtils.hasLength(token)) {
//            throw new CustomParameterizedException("Access token cannot be empty, please go check!");
//        }
//        String activeUserKey = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, userIdFrom, userIdTo, token);
//        if (commonRedisService.exists(activeUserKey)) {
//            commonRedisService.delete(activeUserKey);
//        }
//    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }
}