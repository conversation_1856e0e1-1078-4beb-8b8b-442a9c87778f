package com.altomni.apn.common.dto.application.pipeline;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CreationTypeDTO implements Serializable {

    private static final long serialVersionUID = 4545397032135935363L;

    private String jobType;

    private String creationTalentType;
}
