package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

public class TalentRecruitmentProcessIpgContractFeeChargeVO implements Serializable {

    private static final long serialVersionUID = -7954953234177933998L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private BigDecimal finalBillRate;

    private BigDecimal finalPayRate;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    private String taxBurdenRateCode;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    private String mspRateCode;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    private String immigrationCostCode;

    private BigDecimal extraCost;

    private BigDecimal estimatedWorkingHourPerWeek;

    private BigDecimal gp;

    private TalentRecruitmentProcessIpgOfferLetterCostRateVO taxBurdenRate;

    private TalentRecruitmentProcessIpgOfferLetterCostRateVO mspRate;

    private TalentRecruitmentProcessIpgOfferLetterCostRateVO immigrationCost;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentRecruitmentProcessId() {
        return talentRecruitmentProcessId;
    }

    public void setTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
    }

    public BigDecimal getFinalBillRate() {
        return finalBillRate;
    }

    public void setFinalBillRate(BigDecimal finalBillRate) {
        if (finalBillRate != null) {
            this.finalBillRate = finalBillRate;
        } else {
            this.finalBillRate = BigDecimal.ZERO;
        }
    }

    public BigDecimal getFinalPayRate() {
        return finalPayRate;
    }

    public void setFinalPayRate(BigDecimal finalPayRate) {
        if (finalPayRate != null) {
            this.finalPayRate = finalPayRate;
        } else {
            this.finalPayRate = BigDecimal.ZERO;
        }
    }

    public String getTaxBurdenRateCode() {
        return taxBurdenRateCode;
    }

    public void setTaxBurdenRateCode(String taxBurdenRateCode) {
        this.taxBurdenRateCode = taxBurdenRateCode;
    }

    public String getMspRateCode() {
        return mspRateCode;
    }

    public void setMspRateCode(String mspRateCode) {
        this.mspRateCode = mspRateCode;
    }

    public String getImmigrationCostCode() {
        return immigrationCostCode;
    }

    public void setImmigrationCostCode(String immigrationCostCode) {
        this.immigrationCostCode = immigrationCostCode;
    }

    public BigDecimal getExtraCost() {
        if (extraCost != null) {
            return extraCost;
        }
        return BigDecimal.ZERO;
    }

    public void setExtraCost(BigDecimal extraCost) {
        if (extraCost != null) {
            this.extraCost = extraCost;
        } else {
            this.extraCost = BigDecimal.ZERO;
        }
    }

    public BigDecimal getEstimatedWorkingHourPerWeek() {
        return estimatedWorkingHourPerWeek;
    }

    public void setEstimatedWorkingHourPerWeek(BigDecimal estimatedWorkingHourPerWeek) {
        if (estimatedWorkingHourPerWeek != null) {
            this.estimatedWorkingHourPerWeek = estimatedWorkingHourPerWeek;
        } else {
            this.estimatedWorkingHourPerWeek = BigDecimal.ZERO;
        }
    }

    public BigDecimal getGp() {
        return gp;
    }

    public void setGp(BigDecimal gp) {
        if (gp != null) {
            this.gp = gp;
        } else {
            this.gp = BigDecimal.ZERO;
        }
    }

    public TalentRecruitmentProcessIpgOfferLetterCostRateVO getTaxBurdenRate() {
        return taxBurdenRate;
    }

    public void setTaxBurdenRate(TalentRecruitmentProcessIpgOfferLetterCostRateVO taxBurdenRate) {
        this.taxBurdenRate = taxBurdenRate;
    }

    public TalentRecruitmentProcessIpgOfferLetterCostRateVO getMspRate() {
        return mspRate;
    }

    public void setMspRate(TalentRecruitmentProcessIpgOfferLetterCostRateVO mspRate) {
        this.mspRate = mspRate;
    }

    public TalentRecruitmentProcessIpgOfferLetterCostRateVO getImmigrationCost() {
        return immigrationCost;
    }

    public void setImmigrationCost(TalentRecruitmentProcessIpgOfferLetterCostRateVO immigrationCost) {
        this.immigrationCost = immigrationCost;
    }

        @Override
    public String toString() {
        return "TalentRecruitmentProcessIpgContractFeeChargeVO{" +
                "id=" + id +
                ", talentRecruitmentProcessId=" + talentRecruitmentProcessId +
                ", finalBillRate=" + finalBillRate +
                ", finalPayRate=" + finalPayRate +
                ", taxBurdenRateCode='" + taxBurdenRateCode + '\'' +
                ", mspRateCode='" + mspRateCode + '\'' +
                ", immigrationCostCode='" + immigrationCostCode + '\'' +
                ", extraCost=" + extraCost +
                ", estimatedWorkingHourPerWeek=" + estimatedWorkingHourPerWeek +
                ", gp=" + gp +
                ", taxBurdenRate=" + taxBurdenRate +
                ", mspRate=" + mspRate +
                ", immigrationCost=" + immigrationCost +
                '}';
    }
}
