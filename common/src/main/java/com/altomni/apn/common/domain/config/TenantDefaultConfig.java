package com.altomni.apn.common.domain.config;

import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCodeConverter;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * Tenant Default Config.
 */
@ApiModel(description = "TenantDefaultConfig is used to save tenant default config")
@Data
@Entity
@Table(name = "tenant_default_config")
public class TenantDefaultConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "must not be null")
    @Column(name = "config_code")
    @Convert(converter = TenantConfigCodeConverter.class)
    private TenantConfigCode configCode;

    @Column(name = "config_value")
    @JsonRawValue
    private String configValue;

}
