package com.altomni.apn.common.utils;

import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.ipg.resourceserver.authentication.OAuth2ClientAuthenticationToken;
import com.ipg.resourceserver.authorization.ClientAuthorityProvider;
import com.ipg.resourceserver.client.ClientTokenHolder;
import com.ipg.resourceserver.instrospection.GrpcOpaqueTokenIntrospector;
import com.ipg.resourceserver.instrospection.SsoAuthenticationConverter;
import com.ipg.sso.authentication.Credential;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthenticationToken;
import org.springframework.security.oauth2.server.resource.authentication.OpaqueTokenAuthenticationProvider;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;


/**
 * simulate login, set token to context and validate password
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class LoginUtil {
    public static final int PASSWORD_MIN_LENGTH = 8;
    public static final int PASSWORD_MAX_LENGTH = 100;
    public static final String ERROR_MESSAGE_INVALID_PASSWORD_LENGTH = "The length of the password must be between 8 and 100";
    public static final String ERROR_MESSAGE_INVALID_PASSWORD_COMPOSITION = "The password must contain upper case letter(s), lower case letter(s), and digit(s).";

    public static String USER_ACCOUNT_REDIS_EXPIRE_PREFIX = "account_redis_expire:";

    private static volatile ClientAuthorityProvider clientAuthorityProvider;
    private static volatile OpaqueTokenAuthenticationProvider opaqueTokenAuthenticationProvider;

    private static ClientAuthorityProvider getClientAuthorityProvider() {
        if (clientAuthorityProvider == null) {
            synchronized (LoginUtil.class) {
                if (clientAuthorityProvider == null) {
                    clientAuthorityProvider = SpringUtil.getBean(ClientAuthorityProvider.class);
                }
            }
        }
        return clientAuthorityProvider;
    }


    private static OpaqueTokenAuthenticationProvider getOpaqueTokenAuthenticationProvider() {
        if (opaqueTokenAuthenticationProvider == null) {
            synchronized (LoginUtil.class) {
                if (opaqueTokenAuthenticationProvider == null) {
                    GrpcOpaqueTokenIntrospector tokenIntrospector = SpringUtil.getBean(GrpcOpaqueTokenIntrospector.class);
                    opaqueTokenAuthenticationProvider = new OpaqueTokenAuthenticationProvider(tokenIntrospector);
                    opaqueTokenAuthenticationProvider.setAuthenticationConverter(SpringUtil.getBean(SsoAuthenticationConverter.class));
                }
            }
        }
        return opaqueTokenAuthenticationProvider;
    }


    public void simulateLoginWithClient() {
        String clientId = ClientTokenHolder.getInstance().getClientId();
        Credential credential = ClientTokenHolder.getInstance().getClientToken();
        OAuth2AccessToken accessToken = new OAuth2AccessToken(OAuth2AccessToken.TokenType.BEARER, credential.access_token(),
                Instant.now(), Instant.now().plus(credential.expires_in(), ChronoUnit.SECONDS));
        List<GrantedAuthority> authorities = getClientAuthorityProvider().providerAuthorities();
        Authentication authentication = new OAuth2ClientAuthenticationToken(clientId, accessToken, authorities);
        SecurityContext context = SecurityContextHolder.createEmptyContext();
        context.setAuthentication(authentication);
        SecurityContextHolder.setContext(context);
        log.info("[apn] ======================================== setSecurityContext ======================================== ");
        log.info("[apn] setSecurityContext success ");
        log.info("[apn] ======================================== setSecurityContext ======================================== ");
    }

    public void simulateLoginWithUser(Long userId) {
        Credential credential = ClientTokenHolder.getInstance().getUserToken(userId);
        setSecurityContext(credential.access_token());
    }


    /**
     * 根据token 设置securityContent，使后台任务或者异步线程可以在本线程内使用 securityUtil工具类
     * 如果要在feign 使用需要保证token 的信息的有效性才能设置
     *
     * @param token
     */
    public void setSecurityContext(String token) {
        if (StrUtil.isBlank(token)) {
            log.error("setSecurityContextWithoutFeign is error, token is null");
            return;
        }
        BearerTokenAuthenticationToken authenticationRequest = new BearerTokenAuthenticationToken(token);
        Authentication authentication = getOpaqueTokenAuthenticationProvider().authenticate(authenticationRequest);
        SecurityContext context = SecurityContextHolder.createEmptyContext();
        context.setAuthentication(authentication);
        SecurityContextHolder.setContext(context);
    }

    public void clearSecurityContext() {
        SecurityContextHolder.clearContext();
    }

    public String validatePassword(String password, String decryptSecret) {
        String decryptedPassword = AES.decrypt(password, decryptSecret);
        // Validate the length of the password. The length of the password must be between 8 and 100.
        boolean validLength = !StringUtils.isEmpty(decryptedPassword) &&
                decryptedPassword.length() >= PASSWORD_MIN_LENGTH &&
                decryptedPassword.length() <= PASSWORD_MAX_LENGTH;
        if (BooleanUtils.isFalse(validLength)) {
            throw new CustomParameterizedException(ERROR_MESSAGE_INVALID_PASSWORD_LENGTH);
        }
        // Validate the composition of the password. The password must contain upper case letter(s), lower case letter(s), and digit(s).
        if (BooleanUtils.isFalse(decryptedPassword.matches(".*[A-Z].*"))
                || BooleanUtils.isFalse(decryptedPassword.matches(".*[a-z].*"))
                || BooleanUtils.isFalse(decryptedPassword.matches(".*\\d.*"))) {
            throw new CustomParameterizedException(ERROR_MESSAGE_INVALID_PASSWORD_COMPOSITION);
        }
        return decryptedPassword;
    }

}