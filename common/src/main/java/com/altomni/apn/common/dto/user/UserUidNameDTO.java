package com.altomni.apn.common.dto.user;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserUidNameDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String uid;

    private String firstName;

    private String lastName;
}
