package com.altomni.apn.common.enumeration.enums;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The StageType enumeration.
 */
public enum KpiReportByUserStageType implements ConvertedEnum<Integer> {
    TALENT_NUM(0, "talent_num"),
    SUBMIT_TO_JOB_NUM(1, "submit_to_job_num"),
    SUBMIT_TO_CLIENT_NUM(2, "submit_to_client_num"),
    FIRST_INTERVIEW_NUM(3, "first_interview_num"),
    SECOND_INTERVIEW_NUM(4, "second_interview_num"),
    FINAL_INTERVIEW_NUM(5, "final_interview_num"),
    INTERVIEW_NUM(6, "interview_num"),
    OFFER_NUM(7, "offer_num"),
    OFFER_ACCEPT_NUM(8, "offer_accept_num"),
    ONBOARD_NUM(9, "onboard_num"),
    ELIMINATE_NUM(10, "eliminate_num"),
    CALL_NOTE_NUM(11, "call_note_num"),
    EMAIL_NOTE_NUM(12, "email_note_num"),
    PERSON_NOTE_NUM(13, "person_note_num"),
    VIDEO_NOTE_NUM(14, "video_note_num"),
    OTHER_NOTE_NUM(15, "other_note_num"),
    PIPELINE_NOTE_NUM(16, "pipeline_note_num"),
    APN_PRO_NOTE_NUM(17, "apn_pro_note_num"),
    ICI_NUM(18, "ici_num"),
    JOB_COUNT(19, "job_count");




    // static resolving:
    public static final ReverseEnumResolver<KpiReportByUserStageType, Integer> resolver =
            new ReverseEnumResolver<>(KpiReportByUserStageType.class, KpiReportByUserStageType::toDbValue);
    private final int dbValue;
    private final String name;

    KpiReportByUserStageType(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static KpiReportByUserStageType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
