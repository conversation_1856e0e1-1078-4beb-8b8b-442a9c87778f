package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TalentResumeStatus enumeration.
 */
public enum TalentResumeStatus implements ConvertedEnum<Integer> {
    AVAILABLE(0),
    INVALID(1);

    private final int dbValue;

    TalentResumeStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TalentResumeStatus, Integer> resolver =
        new ReverseEnumResolver<>(TalentResumeStatus.class, TalentResumeStatus::toDbValue);

    public static TalentResumeStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
