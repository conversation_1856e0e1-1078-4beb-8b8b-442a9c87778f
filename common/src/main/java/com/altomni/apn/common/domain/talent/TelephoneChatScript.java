package com.altomni.apn.common.domain.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import javax.validation.constraints.Size;

@Getter
@Setter
@Entity
@Table(name = "telephone_chat_script")
public class TelephoneChatScript extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "language")
    private Integer language;

    @Size(max = 32)
    @Column(name = "subject", length = 32)
    private String subject;

    @Lob
    @Column(name = "script")
    private String script;

}