package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.application.WorkingMode;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceTypeConverter;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Convert;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOnboardVO extends AuditingUser implements Serializable, FillSalaryPackages, FillFeeCharge, FillContractFeeCharge, FillClientInfo {

    private static final long serialVersionUID = 1712390491113462396L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private LocalDate onboardDate;

    private LocalDate warrantyEndDate;

    private LocalDate endDate;

    private Integer currency;

    private RateUnitType rateUnitType;

    private WorkingMode workingMode;

    private String note;

    private LocationDTO workLocation;

    private Instant noteLastModifiedDate;

    private Long noteLastModifiedByUserId;

    private String chargeNumber;

    private String tvcNumber;

    private Boolean corpToCorp;

    private Boolean isSubstituteTalent;

    private Long substituteTalentId;

    private String substituteTalentName;

    private Long relationProcessId;

    @Convert(converter = ResumeSourceTypeConverter.class)
    private ResumeSourceType channelPlatform;

    private BigDecimal profitSharingRatio;

    /**
     * true: 已离职， false: 未离职
     */
    private Boolean resigned;

    private List<TalentRecruitmentProcessOfferSalaryPackageVO> salaryPackages;

    private TalentRecruitmentProcessOfferFeeChargeVO feeCharge;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

    private TalentRecruitmentProcessIpgContractFeeChargeVO contractFeeCharge;

    private TalentRecruitmentProcessOnboardClientInfoVO clientInfo;

    private TalentRecruitmentProcessSourceChannelProfitSharingVO profitSharing;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id"));
}
