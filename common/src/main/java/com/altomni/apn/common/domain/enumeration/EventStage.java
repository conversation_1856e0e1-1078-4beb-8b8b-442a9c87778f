package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The RateUnitType enumeration.
 */
public enum EventStage implements ConvertedEnum<Integer> {
    ROUND_1(1),
    ROUND_2(2),
    ROUND_3(3),
    ROUND_4(4),
    ROUND_5(5);

    private final int dbValue;

    EventStage(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<EventStage, Integer> resolver =
        new ReverseEnumResolver<>(EventStage.class, EventStage::toDbValue);

    public static EventStage fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
