package com.altomni.apn.common.utils;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.config.constants.ReportConstants;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Stream;

@Slf4j
public class ExcelUtil {

    public static String valueOf(Object obj) {
        return (obj == null) ? "" : obj.toString();
    }

//    public static Resource getResource(List<String> header, List<String> fields, List<Object> sourceData, String[] otherData, List<Integer> numberColumns, List<Integer> dateColumns)
//        throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
//        ByteArrayOutputStream bos = null;
//        try {
//            Workbook workbook = createWorkBook(header, fields, sourceData, otherData, numberColumns, dateColumns);
//            bos = new ByteArrayOutputStream();
//            workbook.write(bos);
//            workbook.close();
//            return new InputStreamResource(new ByteArrayInputStream(bos.toByteArray()));
//        } catch (IOException e) {
//            try {
//                bos.close();
//            } catch (IOException e1) {
//                return null;
//            }
//        }
//        return null;
//    }


    public static void getResource(HttpServletResponse response,List<String> header, List<String> fields, List<Object> sourceData, String[] otherData, List<Integer> numberColumns, List<Integer> dateColumns)
        throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        ByteArrayOutputStream bos = null;
        OutputStream outputStream = null;
        try
        {
            Workbook workbook = createWorkBook(header, fields, sourceData, otherData, numberColumns, dateColumns);
            bos = new ByteArrayOutputStream();
            workbook.write(bos);
            HeaderUtil.setFileDownloadHeader(response);
            outputStream  = response.getOutputStream();
            IOUtils.write(bos.toByteArray(),outputStream);
        } catch (IOException e)
        {
			log.error("error", e);
        }finally {
                try {
                        if (bos != null) { bos.close();}
                        if (outputStream != null){ outputStream.close();}
                    } catch(IOException e){
						log.error("error", e);
                    }
                }
    }

	public static void getResource(HttpServletResponse response ,String fileName ,List<String> header, List<String> fields, List<Object> sourceData, String[] otherData, List<Integer> numberColumns, List<Integer> dateColumns)
			throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
		ByteArrayOutputStream bos = null;
		OutputStream outputStream = null;
		try
		{
			Workbook workbook = createWorkBook(header, fields, sourceData, otherData, numberColumns, dateColumns);
			bos = new ByteArrayOutputStream();
			workbook.write(bos);
			HeaderUtil.setFileDownloadHeader(response, fileName);
			outputStream  = response.getOutputStream();
			IOUtils.write(bos.toByteArray(),outputStream);
		} catch (IOException e)
		{
			log.error("error", e);
		}finally {
			try {
				if (bos != null) { bos.close();}
				if (outputStream != null){ outputStream.close();}
			} catch(IOException e){
				log.error("error", e);
			}
		}
	}



    /**
     * create work book
	 */
	private static Workbook createWorkBook(List<String> columnHeaders, List<String> fieldNames, List<Object> ObjectList, String[] otherData, List<Integer> numberColIndex, List<Integer> dateColumns) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
            if (ObjectList.isEmpty()) {
                throw new CustomParameterizedException("No data to download.");
            }

			XSSFWorkbook workbook = new XSSFWorkbook();
			XSSFSheet sheet = workbook.createSheet(ReportConstants.SHEET_NAME);
            sheet.setDefaultColumnWidth(20);
			setColumnTitles(sheet, columnHeaders,0);
			List<String[]> rowList = javaBean2StringArrays(ObjectList, fieldNames);
			setSheetRows(workbook, sheet, rowList, numberColIndex, dateColumns,1);

			if (otherData != null && otherData.length > 0) {
				setOtherDataRows(workbook, sheet, otherData, 2 + rowList.size(), numberColIndex, dateColumns);
			}
			return workbook;
	}

	/**
	 * 第一行是创建标题
	 * @param response
	 * @param fileName
	 * @param fields
	 * @param sourceData
	 * @param headTitle
	 * @throws NoSuchMethodException
	 * @throws IllegalAccessException
	 * @throws InvocationTargetException
	 */
	public static void getResourceMergedRegionCowAndCol(HttpServletResponse response , String fileName ,
														List<String> fields, List<Object> sourceData,
														String headTitle,
														Pair<Integer,Integer> firstMergeCell,
														Pair<Integer,Integer> secondMergeCell)
			throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
		ByteArrayOutputStream bos = null;
		OutputStream outputStream = null;
		try
		{
			Workbook workbook = createWorkBookByMergedRegionCowAndCol(fields, sourceData,headTitle,firstMergeCell,secondMergeCell);
			bos = new ByteArrayOutputStream();
			workbook.write(bos);
			HeaderUtil.setFileDownloadHeader(response, fileName);
			outputStream  = response.getOutputStream();
			IOUtils.write(bos.toByteArray(),outputStream);
		} catch (IOException e)
		{
			log.error("error", e);
		}finally {
			try {
				if (bos != null) { bos.close();}
				if (outputStream != null){ outputStream.close();}
			} catch(IOException e){
				log.error("error", e);
			}
		}
	}

	/**
	 * 第一行是创建标题
	 * @param response
	 * @param fileName
	 * @param header
	 * @param fields
	 * @param sourceData
	 * @param otherData
	 * @param numberColumns
	 * @param dateColumns
	 * @param headTitle
	 * @throws NoSuchMethodException
	 * @throws IllegalAccessException
	 * @throws InvocationTargetException
	 */
	public static void getResource(HttpServletResponse response ,String fileName ,List<String> header,
								   List<String> fields, List<Object> sourceData, String[] otherData,
								   List<Integer> numberColumns, List<Integer> dateColumns,String headTitle)
			throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
		ByteArrayOutputStream bos = null;
		OutputStream outputStream = null;
		try
		{
			Workbook workbook = createWorkBookByMergedRegion(header, fields, sourceData, otherData, numberColumns, dateColumns,headTitle);
			bos = new ByteArrayOutputStream();
			workbook.write(bos);
			HeaderUtil.setFileDownloadHeader(response, fileName);
			outputStream  = response.getOutputStream();
			IOUtils.write(bos.toByteArray(),outputStream);
		} catch (IOException e)
		{
			log.error("error", e);
		}finally {
			try {
				if (bos != null) { bos.close();}
				if (outputStream != null){ outputStream.close();}
			} catch(IOException e){
				log.error("error", e);
			}
		}
	}

	/**
	 * create work book
	 */
	private static Workbook createWorkBookByMergedRegion(List<String> columnHeaders, List<String> fieldNames, List<Object> ObjectList, String[] otherData, List<Integer> numberColIndex, List<Integer> dateColumns,String headTitle) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
		if (ObjectList.isEmpty()) {
			throw new CustomParameterizedException("No data to download.");
		}

		XSSFWorkbook workbook = new XSSFWorkbook();
		XSSFSheet sheet = workbook.createSheet(ReportConstants.SHEET_NAME);
		sheet.setDefaultColumnWidth(20);
		setHeadTitles(workbook, sheet, headTitle, columnHeaders.size() - 1);
		setColumnTitlesByStyle(workbook, sheet, columnHeaders, 1);
		List<String[]> rowList = javaBean2StringArrays(ObjectList, fieldNames);
		setSheetRows(workbook, sheet, rowList, numberColIndex, dateColumns, 2);
		if (rowList.size() > 1) {
			// 合并列
			sheet.addMergedRegion(new CellRangeAddress(2, rowList.size() + 1, 0, 0));
		}
		if (otherData != null && otherData.length > 0) {
			setOtherDataRows(workbook, sheet, otherData, 3 + rowList.size(), numberColIndex, dateColumns);
		}
		return workbook;
	}

	/**
	 * create G2 report onboard work book
	 */
	private static Workbook createWorkBookByMergedRegionCowAndCol(List<String> fieldNames, List<Object> ObjectList, String headTitle,Pair<Integer,Integer> firstMergeCell,
																  Pair<Integer,Integer> secondMergeCell) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
		if (ObjectList.isEmpty()) {
			throw new CustomParameterizedException("No data to download.");
		}

		XSSFWorkbook workbook = new XSSFWorkbook();
		XSSFSheet sheet = workbook.createSheet(ReportConstants.SHEET_NAME);
		sheet.setDefaultColumnWidth(20);
		setHeadTitlesByOnboard(workbook, sheet, headTitle);
		List<String[]> rowList = javaBean2StringArrays(ObjectList, fieldNames);
		setSheetRows(workbook, sheet, rowList, null,null, 3);
		// 合并列
		// Merge first column for first three rows
		if(null != firstMergeCell){
			mergeCells(sheet, firstMergeCell.getKey(), firstMergeCell.getValue(), 0, 0, rowList.get(0)[0],false);
		}
		// Merge first column for rows 5-9
		if(null != secondMergeCell){
			mergeCells(sheet, secondMergeCell.getKey(), secondMergeCell.getValue(), 0, 0, "Contractor Renewals",false);
		}
		return workbook;
	}

	/**
	 * set column titles
	 */
	private static void setColumnTitlesByStyle(XSSFWorkbook workbook,XSSFSheet sheet, List<String> columnHeaders, Integer index) {
		if (columnHeaders.isEmpty()) {
			throw new RuntimeException("column headers is empty.");
		}

		CellStyle headerStyle = workbook.createCellStyle();
		headerStyle.setAlignment(HorizontalAlignment.CENTER);

		XSSFRow row = sheet.createRow(index);
		row.setHeight((short) 360);
		for (int i = 0; i < columnHeaders.size(); i++) {
			XSSFCell cell = row.createCell(i);
			cell.setCellValue(columnHeaders.get(i));
			cell.setCellStyle(headerStyle);
		}
	}

	/**
     * set column titles
	 */
	private static void setColumnTitles(XSSFSheet sheet, List<String> columnHeaders, Integer index) {
		if (columnHeaders.isEmpty()) {
			throw new RuntimeException("column headers is empty.");
		}

		XSSFRow row = sheet.createRow(index);
		row.setHeight((short) 360);
		for (int i = 0; i < columnHeaders.size(); i++) {
			XSSFCell cell = row.createCell(i);
			cell.setCellValue(columnHeaders.get(i));
		}
	}

	/**
	 * set column titles
	 */
	private static void setHeadTitles(XSSFWorkbook workbook,XSSFSheet sheet, String headString,Integer col) {

		// 创建字体样式（用于标题）
		CellStyle titleStyle = workbook.createCellStyle();
		titleStyle.setAlignment(HorizontalAlignment.CENTER);


		XSSFRow row = sheet.createRow(0);
		row.setHeight((short) 360);
		XSSFCell cell = row.createCell(0);
		//cell.setCellType(CellType.STRING); // 定义单元格为字符串类型
		cell.setCellValue(new XSSFRichTextString(headString));
		cell.setCellStyle(titleStyle);
		sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, col)); // 指定标题合并区域

	}

	/**
	 * set column titles
	 */
	private static void setHeadTitlesByOnboard(XSSFWorkbook workbook,
											   XSSFSheet sheet, String headString) {

		// 创建字体样式（用于标题）
		CellStyle titleStyle = workbook.createCellStyle();
		titleStyle.setAlignment(HorizontalAlignment.CENTER);

		// Create first, second, and third header rows
		Row row1 = sheet.createRow(0);
		Row row2 = sheet.createRow(1);
		Row row3 = sheet.createRow(2);

		// Set row heights
		row1.setHeightInPoints((short) 16);
		row2.setHeightInPoints((short) 16);
		row3.setHeightInPoints((short) 16);

		// Merge cells for the first row
		mergeCells(sheet, 0, 1, 0, 9, headString, true);
		mergeCells(sheet, 0, 0, 10, 18, "Gp Change", true);

		// Merge cells for the second row
		mergeCells(sheet, 1, 1, 10, 13, "Original", true);
		mergeCells(sheet, 1, 1, 14, 17, "New", true);
		mergeCells(sheet, 1, 2, 18, 18, "Notes", true);

		// Merge cells for the third row
		mergeCells(sheet, 2, 2, 10, 11, "Bill Rate", true);
		mergeCells(sheet, 2, 2, 12, 13, "Pay Rate", true);
		mergeCells(sheet, 2, 2, 14, 15, "Bill Rate", true);
		mergeCells(sheet, 2, 2, 16, 17, "Pay Rate", true);

		// Set third-row headers
		String[] headers = {"Employee Type","No.","Recruiter", "Client Account", "Candidate Name", "Start Day", "Week Day", "Hourly Gp", "Total Gp", "Total Revenue", "", "", "", "", "", "", "", "", ""};
		for (int i = 0; i < headers.length; i++) {
			if (!headers[i].isEmpty()) {
				row3.createCell(i).setCellValue(headers[i]);
			}
		}
	}

	private static void mergeCells(Sheet sheet, int rowStart, int rowEnd, int colStart, int colEnd, String title, Boolean flag) {
		if (rowStart != rowEnd || colStart != colEnd) { // 防止单元格合并错误
			sheet.addMergedRegion(new CellRangeAddress(rowStart, rowEnd, colStart, colEnd));
		}
		Row row = sheet.getRow(rowStart);
		if (row == null) {
			row = sheet.createRow(rowStart);
		}
		Cell cell = row.createCell(colStart);
		cell.setCellValue(title);
		CellStyle style = sheet.getWorkbook().createCellStyle();
		style.setAlignment(HorizontalAlignment.CENTER);
		style.setVerticalAlignment(VerticalAlignment.CENTER);
		if (flag) {
			cell.setCellStyle(style);
		}
	}

	/**
	 * set data to sheet rows
	 */
	private static void setSheetRows(XSSFWorkbook workbook, XSSFSheet sheet, List<String[]> rowList, List<Integer> numberColIndex, List<Integer> dateColumns,Integer index) {
		if (rowList.isEmpty()) {
			throw new RuntimeException("row data can not empty.");
		}

		for (int i = 0; i < rowList.size(); i++) {
			XSSFRow row = sheet.createRow(index + i);
			row.setHeight((short) 320);
			String[] rowData = rowList.get(i);
			for (int j = 0; j < rowData.length; j++) {
                setCellValue(workbook, row, rowData[j], j, numberColIndex, dateColumns);
			}
		}
	}

    /**
     * create other data rows
	 */
	private static void setOtherDataRows(XSSFWorkbook workbook, XSSFSheet sheet, String[] otherData, int rowIndex, List<Integer> numberColIndex, List<Integer> dateColumns) {
		XSSFRow row = sheet.createRow(rowIndex);
		for (int j = 0; j < otherData.length; j++) {
            setCellValue(workbook, row, otherData[j], j, numberColIndex, dateColumns);
		}
	}

    /**
     * create other data rows
     */
    private static void setDateCellStyle(XSSFWorkbook workbook, XSSFCell cell, String value) throws ParseException {
        CellStyle cellStyle = workbook.createCellStyle();
        CreationHelper createHelper = workbook.getCreationHelper();
        cellStyle.setDataFormat(createHelper.createDataFormat().getFormat(DateUtil.MM_DD_YYYY_HH_MM_SS));
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.MM_DD_YYYY_HH_MM_SS);
        Date date = sdf.parse(value);
        cell.setCellValue(date);
        cell.setCellStyle(cellStyle);
    }

    /**
     * set cell type and value
     */
    private static void setCellValue(XSSFWorkbook workbook, XSSFRow row, String value, int j, List<Integer> numberColIndex, List<Integer> dateColumns) {
        XSSFCell cell = row.createCell(j);
        if (StringUtils.isBlank(value)) {
            cell.setCellValue(value);
            cell.setCellType(CellType.BLANK);
            return;
        }

        try {
            if (dateColumns != null && dateColumns.contains(j)) {
                setDateCellStyle(workbook, cell, value);
            } else if (numberColIndex != null && numberColIndex.contains(j)) {
                cell.setCellValue(Double.parseDouble(value));
                cell.setCellType(CellType.NUMERIC);
            } else {
                cell.setCellValue(value);
                cell.setCellType(CellType.STRING);
            }
        } catch (Exception e) {
            cell.setCellType(CellType.STRING);
            cell.setCellValue(value);
        }
    }

	/**
     * build entity from ArrayList
	 */
	private static List<String[]> javaBean2StringArrays(List<Object> dataList, List<String> fieldNames) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
		if (dataList.isEmpty()) {
			throw new RuntimeException("source data is empty.");
		}

		List<String[]> rows = new ArrayList<>();
		Class<?> clazz = dataList.get(0).getClass();
		if (fieldNames.isEmpty()) {
			throw new RuntimeException("filed name can not empty.");
		}

		Method[] methods = getMethods(clazz, fieldNames);
		for (Object data : dataList) {
			int length = methods.length;
			String[] row = new String[length];
			for (int i = 0; i < length; i++) {
				Object obj = methods[i].invoke(data);
				if (obj != null) {
				    if(obj instanceof JobType)
                    {
                        row[i] = ((JobType) obj).getName();
                    }else
                    {
                        row[i] = obj.toString();
                    }

				} else {
					row[i] = "";
				}
			}
			rows.add(row);
		}
		return rows;
	}

	/**
     * get method from class
	 */
	private static Method[] getMethods(Class<?> clazz, List<String> fieldNames) throws NoSuchMethodException {
		int length = fieldNames.size();
		Method[] methods = new Method[length];
		for (int i = 0; i < length; i++) {
			String fieldName = fieldNames.get(i);
			String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
			methods[i] = clazz.getMethod(methodName);
		}
		return methods;
	}

	public static void maskConfidentialTalentData(Object object) {
		Field[] fields = ReflectUtil.getFields(object.getClass());
		Stream.of(fields)
				.filter(field -> field.getAnnotation(ExcelProperty.class) != null)
				.forEach(field -> {
					try {
						field.setAccessible(true);
						Class<?> type = field.getType();
						if (type.getName().equalsIgnoreCase("java.lang.String")) {
							field.set(object, "***");
						} else {
							field.set(object, null);
						}
					} catch (IllegalAccessException e) {
						throw new RuntimeException(e);
					}
				});
	}

}
