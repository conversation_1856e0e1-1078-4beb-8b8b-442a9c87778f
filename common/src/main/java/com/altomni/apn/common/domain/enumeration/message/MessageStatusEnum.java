package com.altomni.apn.common.domain.enumeration.message;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum MessageStatusEnum implements ConvertedEnum<Integer> {

    READ(1),
    UNREAD(0);

    private Integer dbValue;

    MessageStatusEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<MessageStatusEnum, Integer> resolver = new ReverseEnumResolver<>(MessageStatusEnum.class, MessageStatusEnum::toDbValue);

    public static MessageStatusEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}