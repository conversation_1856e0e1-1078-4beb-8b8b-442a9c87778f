package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum AssignmentCurrencyType implements ConvertedEnum<Integer> {
    USD(0),
    CNY(1),
    CAD(2),
    EUR(3),
    GBP(4);

    private final int dbValue;

    AssignmentCurrencyType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<AssignmentCurrencyType, Integer> resolver = new ReverseEnumResolver<>(AssignmentCurrencyType.class, AssignmentCurrencyType::toDbValue);

    public static AssignmentCurrencyType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
