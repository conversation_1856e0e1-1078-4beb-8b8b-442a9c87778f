package com.altomni.apn.common.auth.timesheet_auth;

import com.altomni.apn.common.domain.user.TimeSheetUser;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

/**
 * timesheet 系统中，外部用户（候选人、客户联系人）的登录认证信息
 */
public class TimesheetExternalAuthenticationToken extends UsernamePasswordAuthenticationToken {

    public TimesheetExternalAuthenticationToken(TimeSheetUser principal, String jwt) {
        super(principal, jwt, principal.getAuthorities());
    }

    @Override
    public TimeSheetUser getPrincipal() {
        return (TimeSheetUser) super.getPrincipal();
    }

    @Override
    public String getCredentials() {
        return (String) super.getCredentials();
    }
}
