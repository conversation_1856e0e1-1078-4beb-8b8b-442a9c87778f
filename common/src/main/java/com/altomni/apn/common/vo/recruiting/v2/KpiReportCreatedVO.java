package com.altomni.apn.common.vo.recruiting.v2;

import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonCountVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class KpiReportCreatedVO extends RecruitingKpiCommonCountVO {

    private Long createdCompanyCount;

    private Long upgradeCompanyCount;

    private Long createdTalentCount;

}
