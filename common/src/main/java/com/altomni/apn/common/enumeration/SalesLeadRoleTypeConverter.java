package com.altomni.apn.common.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class SalesLeadRoleTypeConverter extends AbstractAttributeConverter<SalesLeadRoleType, Integer> {
    public SalesLeadRoleTypeConverter() {
        super(SalesLeadRoleType::toDbValue, SalesLeadRoleType::fromDbValue);
    }
}
