package com.altomni.apn.common.dto.search;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class SearchConditionDeserializer extends JsonDeserializer<SearchCondition> {
    @Override
    public SearchCondition deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        ObjectMapper mapper = (ObjectMapper) p.getCodec();
        JsonNode node = mapper.readTree(p);

        if (node.has("relation") && node.has("condition")) {
            ComplexSearchParam complex = new ComplexSearchParam();
            complex.setRelation(Relation.valueOf(node.get("relation").asText()));

            List<SearchCondition> conditions = new ArrayList<>();
            JsonNode conditionNodes = node.get("condition");
            for (JsonNode conditionNode : conditionNodes) {
                // 根据节点特征判断类型
                if (conditionNode.has("relation") && conditionNode.has("condition")) {
                    // 递归处理ComplexSearchParam
                    conditions.add(deserialize(mapper.treeAsTokens(conditionNode), ctxt));
                } else {
                    // 处理ConditionParam
                    ConditionParam param = new ConditionParam();
                    if (conditionNode.has("key")) {
                        param.setKey(conditionNode.get("key").asText());
                    }
                    if (conditionNode.has("value")) {
                        param.setValue(mapper.treeToValue(conditionNode.get("value"), Object.class));
                    }
                    if (conditionNode.has("type")) {
                        param.setType(conditionNode.get("type").asText());
                    }
                    conditions.add(param);
                }
            }
            complex.setCondition(conditions);
            return complex;
        }

        // 处理普通的ConditionParam
        ConditionParam param = new ConditionParam();
        if (node.has("key")) {
            param.setKey(node.get("key").asText());
        }
        if (node.has("value")) {
            param.setValue(mapper.treeToValue(node.get("value"), Object.class));
        }
        if (node.has("type")) {
            param.setType(node.get("type").asText());
        }
        return param;
    }
}
