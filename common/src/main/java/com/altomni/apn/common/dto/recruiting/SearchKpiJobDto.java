package com.altomni.apn.common.dto.recruiting;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchKpiJobDto implements Serializable {

    private String title;

    private List<JobStatus> statusList;

    private String country;

    private List<Long> jobFunctions;

    private List<Long> industries;

    private List<JobType> typeList;

    private List<Long> userIdList;

    private List<Long> teamIdList;

    private List<Long> jobIdList;
}
