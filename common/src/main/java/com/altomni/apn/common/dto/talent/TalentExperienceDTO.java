package com.altomni.apn.common.dto.talent;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Comparator;


@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Accessors(chain = true)
@ApiModel
public class TalentExperienceDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 这个字段必须返回给前端，因为编辑候选人 experience 时要判断是否是系统自动加的 experience
     * 在配置表talent_form_config中不能过滤掉该字段
     *  在workExperience下配置上 {"field": "talentRecruitmentProcessId", "visible": true, "readOnly": true, "required": false, "cnDisplay": "流程id", "enDisplay": "talentRecruitmentProcessId", "customizable": false, "additionalInfoKeys": ["talentRecruitmentProcessId"]},
     */
    private Long talentRecruitmentProcessId;

    @ApiModelProperty(value = "apn customer company id, contact is active")
    private Long activeCompanyId;

    @ApiModelProperty(value = "apn customer company id, contact is inActive")
    private Long companyId;

    @ApiModelProperty(value = "crm customer company id, contact is active")
    private Long activeCRMAccountId;

    @ApiModelProperty(value = "crm customer company id, contact is inActive")
    private Long crmAccountId;


    //禁猎客户衍生需求，增加人->客户、线索、拓客/工商数据关联关系字段
    /**
     *   - id类型：bd_companies_business_information
     *   - 含义：这段经历对应的工商数据公司id，由esfiller自动识别
     */
    private String businessInfoCompanyId;
    /**
     *   - id类型：bd company
     *   - 含义：这段经历对应的拓客公司id，由esfiller自动识别
     */
    private String bdCompanyId;
    /**
     *   - id类型：lead company
     *   - 含义：这段经历对应的线索公司id，可能来自于esfiller自动识别，也可能因为拓客/工商公司转线索由后端同步
     */
    private String recogLeadsCompanyId;
    /**
     *   - id类型：crm account
     *   - 含义：这段经历对应的crm客户公司id，可能来自于esfiller自动识别，也可能因为线索转客户由后端同步
     */
    private String recogCRMAccountId;
    /**
     *   - id类型：apn company
     *   - 含义：这段经历对应的apn正式客户公司id，可能来自于esfiller自动识别，也可能因为apn发展中客户转正式客户由后端同步
     */
    private String recogCompanyId;


    @Size(max = 255, message = "The company name is too long.")
    private String companyName;

    private Boolean current;

    @Size(max = 255, message = "The title is too long.")
    private String title;

    private String description;

    //汇报对象
    private String reportTo;
    //下属人数
    private Long numberOfSubordinates;


    private LocalDate startDate;

    private LocalDate endDate;

    private String location;

    private String department;

    private String currency;
    private RangeDTO salaryRange;
    private Double payTimes;
    private RateUnitType payType;

    // 序列化时保留字段，但反序列化时忽略字段
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Boolean deletable;

    public TalentExperienceDTO(Long id, String companyName, Boolean current, String title) {
        this.id = id;
        this.companyName = companyName;
        this.current = current;
        this.title = title;
    }

    public TalentExperienceDTO(Long id, String companyName, String title) {
        this.id = id;
        this.companyName = companyName;
        this.title = title;
    }

    public TalentExperienceDTO setActiveCompanyId(Long activeCompanyId) {
        this.activeCompanyId = activeCompanyId;
        return this;
    }

    public TalentExperienceDTO setCompanyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }

    public static Comparator<TalentExperienceDTO> COMPARATOR = (o1, o2) -> {
        //sorting date
        if (ObjectUtil.isNotNull(o1.getCurrent()) && o1.getCurrent()) {
            //if one is current, ignore all end_date. end_dates are bad data.
            if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
                if (ObjectUtil.isNotNull(o1.getStartDate())) {
                    if (ObjectUtil.isNotNull(o2.getStartDate())) {
                        if (o1.getStartDate().isBefore(o2.getStartDate())) {
                            return 1;
                        } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                            return -1;
                        } else {
                            return 0;
                        }
                    } else {
                        //# the one without start date is smaller
                        return -1;
                    }
                } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                    //the one without start date is smaller
                    return 1;
                }
            } else {
                return -1;
            }
        } else if (ObjectUtil.isNotNull(o2.getCurrent()) && o2.getCurrent()) {
            return 1;
        }
        //if neither is current, use end_date
        if (ObjectUtil.isNotNull(o1.getEndDate())) {
            if (ObjectUtil.isNotNull(o2.getEndDate())) {
                if (o1.getEndDate().isBefore(o2.getEndDate())) {
                    return 1;
                } else if (o1.getEndDate().isAfter(o2.getEndDate())) {
                    return -1;
                }
                //if equals, use start_date
            } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                if (o1.getEndDate().isBefore(o2.getStartDate())) {
                    return 1;
                } else if (ObjectUtil.isNotNull(o1.getStartDate())) {
                    if (o1.getStartDate().isAfter(o2.getStartDate())) {
                        return -1;
                    } else {
                        return 1;
                    }
                } else {
                    //end_date is larger than other.start_date
                    return -1;
                }
            } else {
                return -1;
            }
        } else if (ObjectUtil.isNotNull(o2.getEndDate())) {
            if (ObjectUtil.isNotNull(o1.getStartDate())) {
                if (o1.getStartDate().isAfter(o2.getEndDate())) {
                    return -1;
                } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
                    if (o1.getStartDate().isBefore(o2.getStartDate())) {
                        return 1;
                    } else {
                        return -1;
                    }
                } else {
                    return 1;
                }
            } else {
                return 1;
            }
        }
        //if neither exists end_date, use start_date as well
        //here, we have already dealt with one end date existing cases
        if (ObjectUtil.isNotNull(o1.getStartDate())) {
            if (ObjectUtil.isNotNull(o2.getStartDate())) {
                if (o1.getStartDate().isBefore(o2.getStartDate())) {
                    return 1;
                } else if (o1.getStartDate().isAfter(o2.getStartDate())) {
                    return -1;
                }
            } else {
                return -1;
            }
        } else if (ObjectUtil.isNotNull(o2.getStartDate())) {
            return 1;
        }
        //missing info or equals, unable to determine, return None
        return 0;
    };


}
