package com.altomni.apn.common.dto.user;

import com.altomni.apn.common.enumeration.enums.SortType;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class ReportUserLabelSearchDTO {

    //teamIdList 和 userIdList 取并集
    private List<Long> teamIdList;

    private List<Long> userIdList;

    private List<Long> levelOfExperiences;

    private List<Long> languages;

    private Set<Long> permissionTeamIdList;

    private String timezone;

    private SortDTO sort;

}