package com.altomni.apn.common.enumeration.reportSubscriptions;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ReportType implements ConvertedEnum<Integer> {
    //1-by user, 2-by company
    BY_USER(1),
    BY_COMPANY(2),
    USER_ADOPTION(3),
    LINKEDIN_USAGE(4),
    ;

    private final int dbValue;

    ReportType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ReportType, Integer> resolver =
            new ReverseEnumResolver<>(ReportType.class, ReportType::toDbValue);

    public static ReportType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}