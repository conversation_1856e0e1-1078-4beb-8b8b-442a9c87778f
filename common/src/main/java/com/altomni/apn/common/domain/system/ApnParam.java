package com.altomni.apn.common.domain.system;

//import com.altomni.apn.common.domain.enumeration.system.Status;
//import com.altomni.apn.common.domain.enumeration.system.StatusConverter;
//
//import javax.persistence.*;
//import java.io.Serializable;
//import java.util.Objects;
//
///**
// * A ApnParam.
// */
//@Entity
//@Table(name = "apn_param")
//public class ApnParam implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//
//    @Column(name = "param_name")
//    private String paramName;
//
//    @Column(name = "param_key")
//    private String paramKey;
//
//    @Column(name = "param_value")
//    private String paramValue;
//
//    @Column(name = "tenant_id")
//    private Long tenantId;
//
//    @Column(name = "user_id")
//    private Long userId;
//
//    @Convert(converter = StatusConverter.class)
//    @Column(name = "status")
//    private Status status;
//
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//    public String getParamName() {
//        return paramName;
//    }
//
//    public void setParamName(String paramName) {
//        this.paramName = paramName;
//    }
//
//    public String getParamKey() {
//        return paramKey;
//    }
//
//    public void setParamKey(String paramKey) {
//        this.paramKey = paramKey;
//    }
//
//    public String getParamValue() {
//        return paramValue;
//    }
//
//    public void setParamValue(String paramValue) {
//        this.paramValue = paramValue;
//    }
//
//    public Long getTenantId() {
//        return tenantId;
//    }
//
//    public void setTenantId(Long tenantId) {
//        this.tenantId = tenantId;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    public Status getStatus() {
//        return status;
//    }
//
//    public void setStatus(Status status) {
//        this.status = status;
//    }
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        }
//        if (o == null || getClass() != o.getClass()) {
//            return false;
//        }
//        ApnParam apnParam = (ApnParam) o;
//        if (apnParam.getId() == null || getId() == null) {
//            return false;
//        }
//        return Objects.equals(getId(), apnParam.getId());
//    }
//
//    @Override
//    public int hashCode() {
//        return Objects.hashCode(getId());
//    }
//
//    @Override
//    public String toString() {
//        return "ApnParam{" +
//            "id=" + id +
//            ", paramName='" + paramName + '\'' +
//            ", paramKey='" + paramKey + '\'' +
//            ", paramValue='" + paramValue + '\'' +
//            ", tenantId=" + tenantId +
//            ", userId=" + userId +
//            ", status=" + status +
//            '}';
//    }
//}
