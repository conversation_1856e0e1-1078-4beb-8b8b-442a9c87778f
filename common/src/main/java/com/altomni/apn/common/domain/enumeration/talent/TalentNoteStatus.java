package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TalentNoteType enumeration.
 */
public enum TalentNoteStatus implements ConvertedEnum<Integer>
{
    //已和创建候选人的工作意向motivation合并，并存在数据库中
    OPEN_TO_NEW_OPPORTUNITIES(0),
    NOT_ACTIVELY_LOOKING_FOR_NEW_OPPORTUNITIES(1),
    BLACKLIST(2),
    ACTIVE_OVERSEAS_RETURNEE(3);

    private final int dbValue;

    TalentNoteStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TalentNoteStatus, Integer> resolver =
        new ReverseEnumResolver<>(TalentNoteStatus.class, TalentNoteStatus::toDbValue);

    public static TalentNoteStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
