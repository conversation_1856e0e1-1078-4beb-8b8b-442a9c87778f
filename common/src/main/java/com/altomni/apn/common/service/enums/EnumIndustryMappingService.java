package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.dict.mapping.EnumIndustryMapping;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EnumIndustryMappingService {

    private final Logger log = LoggerFactory.getLogger(EnumIndustryMappingService.class);

    @Resource
    private EnumCommonService enumCommonService;


    public List<EnumDictDTO> findAllOrderBySortType(SortType type) {
        log.info("[APN: EnumIndustryMappingService @{}] request to get industries mapping enum data, type: {}", SecurityUtils.getUserId(), type);

        List<EnumIndustryMapping> enumIndustryList = enumCommonService.findAllEnumIndustryMapping();
        List<EnumIndustryMapping> result = new ArrayList<>();
        if (SortType.EN.equals(type)) {
            enumIndustryList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumIndustryMapping::getEnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
        } else {
            enumIndustryList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumIndustryMapping::getCnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
        }

        List<EnumIndustryMapping> parentCategoryList = findAllParents();
        if (CollectionUtil.isNotEmpty(result) ) {
            if (SortType.EN.equals(type)){
                result.forEach(r -> r.setCnLable(false));
                if (CollectionUtil.isNotEmpty(parentCategoryList)){
                parentCategoryList.forEach(p -> p.setCnLable(false));
                }
            }else{
                result.forEach(r -> r.setCnLable(true));
                if (CollectionUtil.isNotEmpty(parentCategoryList)){
                    parentCategoryList.forEach(p -> p.setCnLable(true));
                }
            }
            if (CollUtil.isEmpty(parentCategoryList)){
                return result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay())).map(EnumDictDTO::fromBizDict).collect(Collectors.toList());
            }
            //set parent id by parentCategory and format EnumDictDTO for webSide
            List<EnumDictDTO> dtoList = result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay()))
                    .peek(s -> parentCategoryList.stream().filter(p ->ObjectUtil.isNotNull(s.getParentCategory()) && s.getParentCategory().equals(p.getName()))
                            .forEach(pc ->{
                                s.setParentCategory(StrUtil.toString(pc.getId()));
                            })).collect(Collectors.toList()).stream().map(EnumDictDTO::fromBizDict).collect(Collectors.toList());
            //format children node for webSide
            Map<String, List<EnumDictDTO>> pidListMap =
                    dtoList.stream().collect(Collectors.groupingBy(EnumDictDTO::getParentId));
            dtoList.forEach(item -> {
                List<EnumDictDTO> childrenNodes = pidListMap.get(item.getId());
                if (CollectionUtil.isNotEmpty(childrenNodes)) {
                    item.setChildren(childrenNodes);
                }
            });
            return pidListMap.get(Constants.PARENT_NODE_ID);
        } else {
            return new ArrayList<>();
        }
    }

    private List<EnumIndustryMapping> findAllParents() {
        List<EnumIndustryMapping> enumIndustryList = enumCommonService.findAllEnumIndustryMapping();
        Set<String> enumParentCategoryList = enumIndustryList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).map(EnumIndustryMapping::getParentCategory).filter(ObjectUtil::isNotEmpty).collect(Collectors.toSet());
        return enumIndustryList.stream().filter(o -> enumParentCategoryList.contains(o.getName())).collect(Collectors.toList());
    }


}
