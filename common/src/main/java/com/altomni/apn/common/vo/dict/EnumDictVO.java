package com.altomni.apn.common.vo.dict;

import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@ApiModel(description = "Vo for company serviceType")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class EnumDictVO implements Serializable {

    @ApiModelProperty(value = "the id for company serviceType.")
    private Long id;

    @ApiModelProperty(value = "the label for company serviceType.")
    private String label;

    @ApiModelProperty(value = "the name for company serviceType")
//    @JsonIgnore
    private String name;

    @ApiModelProperty(value = "the parentCategory for company serviceType")
    @JsonIgnore
    private String parentCategory;

    @JsonIgnore
    private Integer enDisplayOrder;

    @JsonIgnore
    private Integer cnDisplayOrder;

    private List<EnumDictVO> children;

    public static EnumDictVO fromCompanyServiceType(EnumCompanyServiceType enumCompanyServiceType, SortType type) {
        EnumDictVO enumDictVO = new EnumDictVO();
        ServiceUtils.myCopyProperties(enumCompanyServiceType, enumDictVO);
        enumDictVO.setLabel(SortType.CN.equals(type) ? enumCompanyServiceType.getCnDisplay() : enumCompanyServiceType.getEnDisplay());
        return enumDictVO;
    }

    public static EnumDictVO fromEnumDictDTO(EnumDictDTO enumDictDTO, SortType type) {
        EnumDictVO enumDictVO = new EnumDictVO();
        ServiceUtils.myCopyProperties(enumDictDTO, enumDictVO);
        enumDictVO.setId(Long.parseLong(enumDictDTO.getId()));
        enumDictVO.setName(enumDictDTO.getName());
        enumDictVO.setLabel(SortType.CN.equals(type) ? enumDictDTO.getLabelCn() : enumDictDTO.getLabel());
        return enumDictVO;
    }

}
