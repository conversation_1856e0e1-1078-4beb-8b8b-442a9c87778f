package com.altomni.apn.common.aop.validation;

import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class Json<PERSON>ieldValidator implements ConstraintValidator<ValidJsonField, JSONObject> {

    private String[] fields;

    @Override
    public void initialize(Valid<PERSON>son<PERSON>ield constraintAnnotation) {
        this.fields = constraintAnnotation.fields();
    }

    @Override
    public boolean isValid(JSONObject value, ConstraintValidatorContext context) {
        if (value == null || value.isEmpty()) {
            return false;
        }

        try {
            for (String field : fields) {
                if (!value.containsKey(field)) {
                    return false;
                }
            }
            return true;
        } catch (JSONException e) {
            return false;
        }
    }

}
