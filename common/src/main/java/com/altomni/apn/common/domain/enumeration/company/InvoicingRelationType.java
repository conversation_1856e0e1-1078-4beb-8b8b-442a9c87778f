package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * invoicing application relation type enum
 */
public enum InvoicingRelationType implements ConvertedEnum<Integer> {

    ADD_RELATION(0, "添加预付金"),
    USE_RELATION(1, "使用预付金"),
    ;
    private final int dbValue;

    private final String description;

    InvoicingRelationType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoicingRelationType, Integer> resolver = new ReverseEnumResolver<>(InvoicingRelationType.class, InvoicingRelationType::toDbValue);

    public static InvoicingRelationType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
