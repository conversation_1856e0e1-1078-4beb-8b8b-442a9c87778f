
package com.altomni.apn.common.domain.enumeration.calendar;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CalendarStatusEnum implements ConvertedEnum<Integer> {

    COMPLETED(0, "已完成"),

    TO_BE_COMPLETED(1, "待完成"),

    OVERDUE(2, "逾期");


    private final int dbValue;
    private final String comment;

    public static final ReverseEnumResolver<CalendarStatusEnum, Integer> resolver =
            new ReverseEnumResolver<>(CalendarStatusEnum.class, CalendarStatusEnum::toDbValue);

    CalendarStatusEnum(int dbValue, String comment) {
        this.dbValue = dbValue;
        this.comment = comment;
    }

    public static CalendarStatusEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }
}
