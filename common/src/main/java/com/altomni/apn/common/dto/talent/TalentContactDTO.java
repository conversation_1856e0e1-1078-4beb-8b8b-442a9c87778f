package com.altomni.apn.common.dto.talent;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentContactDTO implements Serializable {

    private static final long serialVersionUID = -359001722869208228L;

    private Long id;

    private ContactType type;

    private Integer contactTypeValue;

    private String contact;

    private Boolean verified;

    private String details;

    private String info;

    private Long talentId;

    private Long tenantId;

    private TalentContactStatus status;

    private TalentContactVerificationStatus verificationStatus;

    private Integer sort;

    private Boolean approverEmail = false;

    private Boolean timesheetUserEmail; //set default to null


    private Long permissionUserId;
    private Long permissionTeamId;

    private String createdBy;

    private Instant createdDate;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    private SimpleUser createdUser;
    private SimpleUser lastModifiedUser;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TalentContactDTO talentContact = (TalentContactDTO) o;
        return (this.getType().equals(talentContact.getType()) &&
                this.getContact().equals(talentContact.getContact()));
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getType().name() + getContact());
    }

    public Integer getSort() {
        if(ObjectUtil.isNull(sort)){
            return 0;
        }
        return sort;
    }

    public String getTypeAndContact() {
        return type + contact;
    }

    public TalentContactDTO(ContactType type, String contact) {
        this.type = type;
        this.contact = contact;
    }

    public TalentContactDTO(ContactType type, String contact, String details) {
        this.type = type;
        this.contact = contact;
        this.details = details;
    }

    public TalentContactDTO(ContactType type, String contact, TalentContactVerificationStatus verificationStatus, Long talentId, Long tenantId, TalentContactStatus status, Integer sort) {
        this.type = type;
        this.contact = contact;
        this.verificationStatus = verificationStatus;
        this.talentId = talentId;
        this.tenantId = tenantId;
        this.status = status;
        this.sort = sort;
    }

    public static TalentContactDTO fromTalentContact(TalentContact talentContact) {
        TalentContactDTO talentContactDTO = new TalentContactDTO();
        ServiceUtils.myCopyProperties(talentContact, talentContactDTO);
        talentContactDTO.setContactTypeValue(talentContact.getType().toDbValue());
        return talentContactDTO;
    }
}
