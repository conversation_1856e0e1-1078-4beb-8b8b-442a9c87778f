package com.altomni.apn.common.vo.message;

import com.altomni.apn.common.domain.enumeration.message.MessageFavoriteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageFavoriteEnumConverter;
import com.altomni.apn.common.domain.enumeration.message.MessageStatusEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageStatusEnumConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.Instant;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class MessageVO {

    @Id
    private Long id;

    private String cnTitle;

    private String enTitle;

    private String content;

    @Column(name = "is_read")
    @Convert(converter = MessageStatusEnumConverter.class)
    private MessageStatusEnum isRead;

    @Column(name = "is_favorite")
    @Convert(converter = MessageFavoriteEnumConverter.class)
    private MessageFavoriteEnum isFavorite;

    private Instant createdDate;

}
