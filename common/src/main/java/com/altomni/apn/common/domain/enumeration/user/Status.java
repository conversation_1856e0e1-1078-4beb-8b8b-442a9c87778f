package com.altomni.apn.common.domain.enumeration.user;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobStatus enumeration.
 */
public enum Status implements ConvertedEnum<Integer> {
    Available(0), Invalid(1), Failed(9);

    private final int dbValue;

    Status(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<Status, Integer> resolver =
        new ReverseEnumResolver<>(Status.class, Status::toDbValue);

    public static Status fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
