package com.altomni.apn.common.domain.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "user_delivery_job_function_relation")
public class UserDeliveryJobFunctionRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "enum_job_function_mapping_id")
    private Long enumJobFunctionMappingId;

    @Column(name = "count")
    private Long count;

    @Column(name = "top")
    private Boolean top;

    @Column(name = "updated")
    private Boolean updated;

    public UserDeliveryJobFunctionRelation(Long id, Long userId, Long enumJobFunctionMappingId, Long count, Boolean top) {
        this.id = id;
        this.userId = userId;
        this.enumJobFunctionMappingId = enumJobFunctionMappingId;
        this.count = count;
        this.top = top;
    }

}
