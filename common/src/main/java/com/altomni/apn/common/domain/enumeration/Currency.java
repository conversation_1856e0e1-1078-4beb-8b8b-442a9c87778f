package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * The Currency enumeration.
 */
public enum Currency implements ConvertedEnum<Integer>{
    USD(0),
    CNY(1),
    EUR(2),
    CAD(3),
    GBP(4),
    AUD(7),
    JPY(16),
    SGD(28),
    NON_CHINA(999);

    public static final List<Integer> CURRENCIES_NON_CHINA = new ArrayList<>();

    static {
        for (Currency currency: Currency.values()) {
            if (!Currency.CNY.equals(currency)) {
                CURRENCIES_NON_CHINA.add(currency.toDbValue());
            }
        }
    }

    private final Integer dbValue;

    Currency(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<Currency, Integer> resolver =
            new ReverseEnumResolver<>(Currency.class, Currency::toDbValue);

    public static Currency fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
