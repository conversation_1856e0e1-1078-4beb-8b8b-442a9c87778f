package com.altomni.apn.common.domain.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum RelateJobFolderStatus implements ConvertedEnum<Integer> {
    CONFIRM(0), PENDING(1);

    private final int dbValue;

    RelateJobFolderStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<RelateJobFolderStatus, Integer> resolver =
            new ReverseEnumResolver<>(RelateJobFolderStatus.class, RelateJobFolderStatus::toDbValue);

    public static RelateJobFolderStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
