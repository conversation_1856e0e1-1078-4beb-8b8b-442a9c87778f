package com.altomni.apn.common.domain.enumeration.config;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TenantMessageMinderConfigFieldCodeEnumConverter extends AbstractAttributeConverter<TenantMessageMinderConfigFieldCodeEnum, String> {
    public TenantMessageMinderConfigFieldCodeEnumConverter() {
        super(TenantMessageMinderConfigFieldCodeEnum::toDbValue, TenantMessageMinderConfigFieldCodeEnum::fromDbValue);
    }
}
