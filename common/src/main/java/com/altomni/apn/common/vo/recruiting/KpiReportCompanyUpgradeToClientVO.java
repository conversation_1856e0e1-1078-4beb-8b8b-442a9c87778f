package com.altomni.apn.common.vo.recruiting;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatusConverter;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Entity
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KpiReportCompanyUpgradeToClientVO {

    @Id
    @JsonIgnore
    private String id;

    private Long userId;

    private String username;

    @JsonIgnore
    private Long companyCount;

    @JsonIgnore
    private Long teamId;

    @JsonIgnore
    private String teamName;

    private String groupByDate;

}
