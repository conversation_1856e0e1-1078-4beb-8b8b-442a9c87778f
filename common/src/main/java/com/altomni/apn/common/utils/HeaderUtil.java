package com.altomni.apn.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
public final class HeaderUtil {

    private static final String APPLICATION_NAME = "apiApp";

    private static final List<String> EXPOSED_HEADERS = Arrays.asList("X-apiApp-alert", "X-apiApp-params", HttpHeaders.LINK, HttpHeaders.LOCATION);

    private static final List<String> EXPOSED_USER_ACTIVE_DURATION_HEADERS = Arrays.asList("X-User-Active-Threshold", "X-Current-User-Active-Duration");

    private HeaderUtil() {
    }

    public static HttpHeaders generateUserActiveAlertHttpHeaders(Long userActiveThreshold, Long currentUserActiveDuration) {

        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(EXPOSED_USER_ACTIVE_DURATION_HEADERS);
        if (Objects.nonNull(userActiveThreshold)) {
            headers.set("X-User-Active-Threshold", String.valueOf(userActiveThreshold));
        }
        if (Objects.nonNull(currentUserActiveDuration)) {
            headers.set("X-Current-User-Active-Duration", String.valueOf(currentUserActiveDuration));
        }
        return headers;
    }

    public static void generateUserActiveAlertHttpHeaders(HttpHeaders headers, Long userActiveThreshold, Long currentUserActiveDuration) {

        List<String> exposed = headers.getAccessControlExposeHeaders();
        exposed.addAll(EXPOSED_USER_ACTIVE_DURATION_HEADERS);
        headers.setAccessControlExposeHeaders(exposed);
        if (Objects.nonNull(userActiveThreshold)) {
            headers.set("X-User-Active-Threshold", String.valueOf(userActiveThreshold));
        }
        if (Objects.nonNull(currentUserActiveDuration)) {
            headers.set("X-Current-User-Active-Duration", String.valueOf(currentUserActiveDuration));
        }
    }

    public static HttpHeaders createAlert(String applicationName, String message, String param) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-" + applicationName + "-alert", message);

        try {
            headers.add("X-" + applicationName + "-params", URLEncoder.encode(param, StandardCharsets.UTF_8.toString()));
        } catch (UnsupportedEncodingException var5) {
        }

        return headers;
    }

    public static HttpHeaders createAlert(String message, String param) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-apiApp-alert", message);
        headers.add("X-apiApp-params", param);
        headers.setAccessControlExposeHeaders(EXPOSED_HEADERS);
        return headers;
    }

    public static HttpHeaders createEntityCreationAlert(String applicationName, boolean enableTranslation, String entityName, String param) {
        String message = enableTranslation ? applicationName + "." + entityName + ".created" : "A new " + entityName + " is created with identifier " + param;
        return createAlert(applicationName, message, param);
    }

    public static HttpHeaders createEntityCreationAlert(String entityName, String param) {
        return createAlert(APPLICATION_NAME + "." + entityName + ".created", param);
    }

    public static HttpHeaders createEntityUpdateAlert(String applicationName, boolean enableTranslation, String entityName, String param) {
        String message = enableTranslation ? applicationName + "." + entityName + ".updated" : "A " + entityName + " is updated with identifier " + param;
        return createAlert(applicationName, message, param);
    }

    public static HttpHeaders createEntityUpdateAlert(String entityName, String param) {
        return createAlert(APPLICATION_NAME + "." + entityName + ".updated", param);
    }

    public static HttpHeaders createEntityDeletionAlert(String applicationName, boolean enableTranslation, String entityName, String param) {
        String message = enableTranslation ? applicationName + "." + entityName + ".deleted" : "A " + entityName + " is deleted with identifier " + param;
        return createAlert(applicationName, message, param);
    }

    public static HttpHeaders createEntityDeletionAlert(String entityName, String param) {
        return createAlert(APPLICATION_NAME + "." + entityName + ".deleted", param);
    }

    public static HttpHeaders createFailureAlert(String applicationName, boolean enableTranslation, String entityName, String errorKey, String defaultMessage) {
        log.error("Entity processing failed, {}", defaultMessage);
        String message = enableTranslation ? "error." + errorKey : defaultMessage;
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-" + applicationName + "-error", message);
        headers.add("X-" + applicationName + "-params", entityName);
        return headers;
    }

    public static HttpHeaders createFailureAlert(String entityName, String errorKey, String defaultMessage) {
        log.error("Entity processing failed, {}", defaultMessage);
        HttpHeaders headers = new HttpHeaders();
        headers.add("X-apiApp-error", "error." + errorKey);
        headers.add("X-apiApp-params", entityName);
        return headers;
    }

    public static HttpHeaders getXLSXHeader() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
        headers.add("Pragma", "no-cache");
        headers.add("Expires", "0");
        headers.add("charset", "utf-8");
        headers.add("Content-Disposition", "attachment;filename=\"report.xls\"");
        headers.setContentType(MediaType.parseMediaType("application/x-msdownload"));
        return headers;
    }
    public static void setFileDownloadHeader(HttpServletResponse response)
    {
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("charset", "utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=\"report.xls\"");
    }

    public static void setFileDownloadHeader(HttpServletResponse response, String fileName)
    {
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("charset", "utf-8");
        try {
            response.setHeader("Access-Control-Expose-Headers","Content-Disposition");
            response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode(fileName, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            log.error("error", e);
        }
        response.setContentType("application/x-msdownload");
    }

    public static void setPDFHeader(HttpServletResponse response, String fileName) {
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("charset", "utf-8");
        response.setHeader("Content-Disposition", "filename=\"" + fileName +".pdf\"");
        response.setHeader("Content-Type", "application/pdf");
    }

    public static void setInvoiceHeader(HttpServletResponse response, String fileName,String contentType) throws Exception{
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("charset", "utf-8");
        response.setHeader("Content-Type", contentType);
        response.setHeader("Content-Disposition", "filename=" + new String(fileName.getBytes("UTF-8"), "iso-8859-1"));

    }
}