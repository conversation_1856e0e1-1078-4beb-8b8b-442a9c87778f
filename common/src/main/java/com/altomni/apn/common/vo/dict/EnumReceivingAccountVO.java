package com.altomni.apn.common.vo.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@ApiModel(description = "Vo for invoice receiving account")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class EnumReceivingAccountVO implements Serializable {

    private Long id;

    private String enDisplay;

    private String accountName;

    private Integer currency;

    private String accountInfo;

    private Integer enDisplayOrder;

}
