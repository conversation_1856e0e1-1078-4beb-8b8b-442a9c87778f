package com.altomni.apn.common.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The RateUnitType enumeration.
 */
public enum ParseStatus implements ConvertedEnum<Integer> {
    NONE(0),
    EDIT(1),
    STARTED(2),
    FINISHED(3),
    NOT_ACCEPT(4),
    ERROR(5),
    QUEUED(6),
    TIMEOUT(7),
    UPLOADING(8);


    private final int dbValue;

    ParseStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ParseStatus, Integer> resolver =
        new ReverseEnumResolver<>(ParseStatus.class, ParseStatus::toDbValue);

    public static ParseStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
