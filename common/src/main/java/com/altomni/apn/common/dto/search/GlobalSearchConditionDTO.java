package com.altomni.apn.common.dto.search;

import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class GlobalSearchConditionDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "general search condition")
    private String search;

    @ApiModelProperty(value = "user's timezone")
    private String timezone;

    @ApiModelProperty(value = "user's language")
    private LanguageEnum language = LanguageEnum.EN;

    @ApiModelProperty(value = "for job search, filter recruitment process ids")
    private List<String> recruitmentProcessIds;

    public static SearchConditionDTO constructFromGlobalSearch(GlobalSearchConditionDTO globalSearchDTO, ModuleType moduleType) {
        SearchConditionDTO conditionDTO = new SearchConditionDTO();
        conditionDTO.setModule(moduleType);
        conditionDTO.setTimezone(globalSearchDTO.getTimezone());
        conditionDTO.setLanguage(globalSearchDTO.getLanguage());

        //construct search param
        ConditionParam generalSearchCondition = ConditionParam.of("generalText", globalSearchDTO.getSearch());
        List<ConditionParam> searchConditions = new ArrayList<>();
        searchConditions.add(generalSearchCondition);

        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.AND);
        searchParam.setCondition(searchConditions);

        List<SearchParam> search = new ArrayList<>();
        search.add(searchParam);

        conditionDTO.setSearch(search);

        if (ModuleType.JOB.equals(moduleType) && CollectionUtils.isNotEmpty(globalSearchDTO.getRecruitmentProcessIds())) {
            //construct filter search for job
            ConditionParam recruitmentProcessFilterCondition = ConditionParam.of("recruitmentProcessId", globalSearchDTO.getRecruitmentProcessIds());
            List<ConditionParam> filterConditions = new ArrayList<>();
            filterConditions.add(recruitmentProcessFilterCondition);

            SearchParam filterParam = new SearchParam();
            filterParam.setRelation(Relation.AND);
            filterParam.setCondition(filterConditions);

            List<SearchParam> filter = new ArrayList<>();
            filter.add(filterParam);

            conditionDTO.setFilter(filter);
        }

        return conditionDTO;
    }

}

