package com.altomni.apn.common.domain.enumeration.user;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The SpecialDocumentType enumeration.
 */
public enum CompletionStatus implements ConvertedEnum<Integer> {
    COMPLETE(0,"COMPLETE"),
    INCOMPLETE(1,"INCOMPLETE"),
    UNINVOLVED(2,"UNINVOLVED");

    // static resolving:
    public static final ReverseEnumResolver<CompletionStatus, Integer> resolver =
        new ReverseEnumResolver<>(CompletionStatus.class, CompletionStatus::toDbValue);
    private final int dbValue;
    private final String name;

    CompletionStatus(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static CompletionStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
