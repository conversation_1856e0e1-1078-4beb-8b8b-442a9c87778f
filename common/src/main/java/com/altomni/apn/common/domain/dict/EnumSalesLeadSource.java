package com.altomni.apn.common.domain.dict;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "saleslead source")
@Entity
@Data
@Table(name = "enum_company_sale_lead_source")
public class EnumSalesLeadSource implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display_order")
    private Integer enDisplayOrder;

    @Column(name = "cn_display_order")
    private Integer cnDisplayOrder;

}
