package com.altomni.apn.common.vo.recruiting;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiJobDetailCommonCountVO extends RecruitingKpiCommonCountVO {

    private String contacts;

    private String assignedUser;

    private JobStatus jobStatus;

    private String jobStartDate;

    private String jobEndDate;

    private String contractDuration;

    private String jobCurrency;

    private String maximumPayRate;

    private String minimumPayRate;

    private String jobCooperationStatus;

    private Long pteamId;

    private boolean isPrivateJob;

}
