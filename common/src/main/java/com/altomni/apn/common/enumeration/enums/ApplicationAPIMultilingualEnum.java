package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 application-service
 * <AUTHOR>
 */
public enum ApplicationAPIMultilingualEnum {

    RECRUITMENTPROCESSNOTEPAGE_CONFIGMISS("recruitmentprocessnotepage_configMiss"),
    RECRUITMENTPROCESSNOTEPAGE_CONFIGNOTFIND("recruitmentprocessnotepage_configNotFind"),
    RECRUITMENTPROCESSNOTEPAGE_DIFFKEY("recruitmentprocessnotepage_diffKey"),
    RECR<PERSON><PERSON>EN<PERSON>ROCESSNOTEPAGE_CANNOTSETDISPLAY("recruitmentprocessnotepage_cannotSetDisplay"),
    RECR<PERSON>TM<PERSON>TPROCESSNOTEPAGE_MUSTSETDISPLAY("recruitmentprocessnotepage_mustSetDisplay"),

    RECR<PERSON>TMENTPROCESSNOTEPAGE_CANNOTSETREQUIREDISPLAY("recruitmentprocessnotepage_cannotSetRequireDisplay"),
    RECRUITMENTPROCESSNOTEPAGE_MUSTSETREQUIREDISPLAY("recruitmentprocessnotepage_mustSetRequireDisplay"),
    RECR<PERSON>TMENTPROCESSNOTE_UPDATEDIFFID("recruitmentprocessnote_updateDiffId"),
    RECRUITMENTPROCESSNOTE_FINDBYID( "recruitmentprocessnote_findById"),
    RECRUITMENTPROCESSNOTE_NOPERMISSION("recruitmentprocessnote_noPermission"),
    RECRUITMENTPROCESS_CREATENOTEISNULL("recruitmentprocess_createNoteIsnull"),
    RECRUITMENTPROCESS_CREATENOTETYPEISNULL("recruitmentprocess_createNoteTypeIsnull"),
    RECRUITMENTPROCESS_CREATEDUPLICATENOTETYPE("recruitmentprocess_createDuplicateNoteType"),
    RECRUITMENTPROCESS_CREATEMISSNOTE("recruitmentprocess_createMissNote"),
    RECRUITMENTPROCESS_UPDATEIDISZERO("recruitmentprocess_updateIdIsZero"),

    RECRUITMENTPROCESS_FINDBYID( "recruitmentprocess_findById"),
    RECRUITMENTPROCESS_NOPERMISSION("recruitmentprocess_noPermission"),
    RECRUITMENTPROCESS_RECRUITMENTPROCESSISNULL("recruitmentprocess_recruitmentProcessIsnull"),

    RECRUITMENTPROCESS_CONFIGRECRUITMENTPROCESSISNULL("recruitmentprocess_configRecruitmentProcessIsnull"),

    IPG_VALIDATEANDUPDATEJOBSTATUSTOFILLED("ipg_validateAndUpdateJobStatusToFilled"),

    IPG_VALIDATETALENTRECRUITMENTPROCESSISNULL("ipg_validateTalentRecruitmentProcessIsnull"),

    IPG_VALIDATETALENTRECRUITMENTPROCESSIDISNULL("ipg_validateTalentRecruitmentProcessIdIsnull"),

    IPG_VALIDATETALENTRECRUITMENTPROCESSNOPERMISSION("ipg_validateTalentRecruitmentProcessNoPermission"),

    IPG_VALIDATE3DAYPROTECTIONRULE("ipg_validate3DayProtectionRule"),

    IPG_CHECKJOBSTATUSNOPREMISSION("ipg_checkJobStatusNoPremission"),

    IPG_CHECKJOBSTATUSISNOTOPEN("ipg_checkJobStatusIsNotOpen"),

    TALENTRECRUITMENTPROCESS_INTERVIEWISNULL("talentrecruitmentprocess_interviewIsnull"),

    TALENTRECRUITMENTPROCESS_OFFERNOPERMISSION("talentrecruitmentprocess_offerNoPermission"),

    TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATEISNULL("talentrecruitmentprocess_offerLetterCostRateIsnull"),

    TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATENOPERMISSION("talentrecruitmentprocess_offerLetterCostRateNoPermission"),

    TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATEMAPISNULL("talentrecruitmentprocess_offerLetterCostRateMapIsnull"),

    TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATEMAPNOPERMISSION("talentrecruitmentprocess_offerLetterCostRateMapNoPermission"),

    TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTIMMIGRATIONISNULL("talentrecruitmentprocess_offerLetterCostImmigrationIsNull"),

    TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATEIMMIGRATIONNOPERMISSION("talentrecruitmentprocess_offerLetterCostRateImmigrationNoPermission"),

    TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTVALIDATETENANT("talentrecruitmentprocess_offerLetterCostValidateTenant"),

    TALENTRECRUITMENTPROCESS_COMPLETECURRENTNODEANDACTIVENEXTNODEISNULL("talentrecruitmentprocess_completeCurrentNodeAndActiveNextNodeIsnull"),

    TALENTRECRUITMENTPROCESS_COMPLETECURRENTNODEANDACTIVENEXTNODENODETYPE("talentrecruitmentprocess_completeCurrentNodeAndActiveNextNodeNodeType"),

    TALENTRECRUITMENTPROCESS_ELIMINATENODEISNULL("talentrecruitmentprocess_eliminateNodeIsnull"),

    TALENTRECRUITMENTPROCESS_FINDINFOIDISNULL("talentrecruitmentprocess_findInfoIdIsnull"),

    TALENTRECRUITMENTPROCESS_FINDINFOPROCESSISNULL("talentrecruitmentprocess_findInfoProcessIsnull"),

    TALENTRECRUITMENTPROCESS_FINDINFONOPERMISSION("talentrecruitmentprocess_findInfoNoPermission"),

    TALENTRECRUITMENTPROCESS_FINDALLPARAMISNULL("talentrecruitmentprocess_findAllParamIsnull"),

    TALENTRECRUITMENTPROCESS_FINDALLPROCESSISNULL("talentrecruitmentprocess_findAllProcessIsnull"),

    TALENTRECRUITMENTPROCESS_ONBOARDNOTFOUND("talentrecruitmentprocess_onboardNotFound"),

    TALENTRECRUITMENTPROCESS_SUBMITTOJOBPROCESSIDISNULL("talentrecruitmentprocess_submitToJobProcessIdIsnull"),

    TALENTRECRUITMENTPROCESS_SUBMITTOJOBPROCESSISNULL("talentrecruitmentprocess_submitToJobProcessIsnull"),

    TALENTRECRUITMENTPROCESS_SUBMITTOJOBNOPERMISSION("talentrecruitmentprocess_submitToJobNoPermission"),

    TALENTRECRUITMENTPROCESS_SUBMITTOJOBTALENTPROCESSIDISNULL("talentrecruitmentprocess_submitToJobTalentProcessIdIsnull"),

    TALENTRECRUITMENTPROCESS_SUBMITTOJOBTALENTPROCESSISNULL("talentrecruitmentprocess_submitToJobTalentProcessIsnull"),

    TALENTRECRUITMENTPROCESS_TOVONODEISNULL("talentrecruitmentprocess_toVoNodeIsnull"),

    TALENTRECRUITMENTPROCESS_SUBMITTOJOBPROCESSEXIST("talentrecruitmentprocess_submitToJobProcessExist"),

    TALENTRECRUITMENTPROCESS_SUBMITTOCLIENTSECONDPARTY("talentrecruitmentprocess_submitToClientSecondParty"),

    TALENTRECRUITMENTPROCESS_CHECKPERMISSIONCOMPANYIDISNULL("talentrecruitmentprocess_checkPermissionCompanyIdIsnull"),

    TALENTRECRUITMENTPROCESS_CHECKPERMISSIONISCURRENTCOMPANYAM("talentrecruitmentprocess_checkPermissionisCurrentCompanyAm"),

    TALENTRECRUITMENTPROCESS_OFFERACCEPTSENDMAILFAILED("talentrecruitmentprocess_offerAcceptSendMailFailed"),

    TALENTRECRUITMENTPROCESS_VALIDATEEXISTSSTART("talentrecruitmentprocess_validateExistsStart"),

    TALENTRECRUITMENTPROCESS_CHECKOTHERRECRUITMENTPROCESSFORTALENT("talentrecruitmentprocess_checkOtherRecruitmentProcessForTalent"),

    TALENTRECRUITMENTPROCESS_DOWNLOADPDFNOTACCEPTOFFER("talentrecruitmentprocess_downloadPdfNotAcceptOffer"),

    TALENTRECRUITMENTPROCESS_DOWNLOADPDFNOTGOTTENOFFER("talentrecruitmentprocess_downloadPdfNotGottenOffer"),

    TALENTRECRUITMENTPROCESS_CREATE("talentrecruitmentprocess_create"),

    TALENTRECRUITMENTPROCESS_ONBOARDELIMINATE_NOSTARTINFO("talentrecruitmentprocess_onboardEliminate_noStartInfo"),

    TALENTRECRUITMENTPROCESS_ONBOARDELIMINATE_TIMEERROR("talentrecruitmentprocess_onboardEliminate_timeError"),

    ;

    private final String key;

    ApplicationAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}