package com.altomni.apn.common.service.rabbitmq.impl;

import com.altomni.apn.common.config.application.EsFillerMqBaseProperties;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.rabbitmq.RabbitMqService;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.QueueInformation;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service("userRabbitMqService")
public class RabbitMqServiceImpl implements RabbitMqService {

    @Resource
    private EsFillerMqBaseProperties esFillerMqBaseProperties;

    @Resource(name = "esfillerRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource(name = "esfillerAmqpAdmin")
    private AmqpAdmin esfillerAmqpAdmin;
    @Override
    public void saveDataProfile(String profile, int priority, SyncIdTypeEnum syncIdTypeEnum) {
        log.info("[EsFillerService: syncDataToMQ @{}] save {} profile to rabbitMQ : {}", SecurityUtils.getUserId(), syncIdTypeEnum, profile);
        try {
            rabbitTemplate.convertAndSend(esFillerMqBaseProperties.getEsfillerMQExchange(), esFillerMqBaseProperties.getToEsFillerRoutingKey(), profile, message -> {
                message.getMessageProperties().setPriority(priority);
                return message;
            });
        } catch (Exception e) {
            log.error("[EsFillerService: syncDataToMQ @{}] send {} profile to rabbitMQ error: {}", SecurityUtils.getUserId(), syncIdTypeEnum, e.getMessage());
        }
    }

    @Override
    public Integer checkMessageCount(String queue) {
        final QueueInformation queueInfo = esfillerAmqpAdmin.getQueueInfo(queue);
        int messageCount = queueInfo.getMessageCount();
        log.info("check queue: {} message count: {}", queue, messageCount);
        return messageCount;
    }

}
