package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 email-service
 * <AUTHOR>
 */
public enum EmailAPIMultilingualEnum {

    AUDIENCE_CREATEMONGOAUDIENCEGROUP_NAMEEMPTY("audience_createMongoAudienceGroup_nameEmpty"),

    AUDIENCE_LOGANDRETURN_ERROR("audience_logAndReturn_error"),

    CAMPAIGN_COMMON_INVALIDPARAM("campaign_common_invalidParam"),

    CAMPAIGN_SENDHTMLMAIL_USERNULL("campaign_sendHtmlMail_userNull"),

    CAMPAIGN_SENDHTMLMAIL_NOHTMLCONTENT("campaign_sendHtmlMail_noHtmlContent"),

    CAMPAIGN_SENDHTMLMAIL_NOSUBJECT("campaign_sendHtmlMail_noSubject"),

    CAMPAIGN_SENDHTMLMAIL_NOTO("campaign_sendHtmlMail_noTo"),

    CAMPAIGN_SENDEMAILBLAST_NOTFOUNDUSER("campaign_sendEmailBlast_notFoundUser"),

    EMAIL_COMMON_INVALIDPARAM("email_common_invalidParam"),

    EMAIL_SENDRICHMAILFORSYSTEMEMAIL_NOPERMISSION("email_sendRichMailForSystemEmail_noPermission"),

    ATTACHMENT_UPLOADEMAILATTACHMENT_FILENULL("attachment_uploadEmailAttachment_fileNull"),

    ATTACHMENT_UPLOADEMAILATTACHMENT_FILELARGE("attachment_uploadEmailAttachment_fileLarge"),

    ;

    private final String key;

    EmailAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}