package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 common-service
 * <AUTHOR>
 */
public enum CommonAPIMultilingualEnum {

    CALENDER_CALENDAREVENTEXISTS("calender_calendarEventExists"),

    CA<PERSON><PERSON>ER_DELETECALENDAREVENT_NOPERMISSION("calender_deleteCalendarEvent_noPermission"),

    EMAIL_COMMON_INVALIDPARAMETER("email_common_invalidParameter"),

    EMAIL_AUDIENCE_SERVICEERROR("email_audience_serviceError"),

    EMAIL_COMPANY_SENDHTMLMAILNOUSER("email_company_sendHtmlMailNoUser"),

    EMAIL_COMPANY_SENDHTMLMAILNOCONTENT("email_company_sendHtmlMailNoContent"),

    EMAIL_COMPANY_SENDHTMLMAILNOSUBJECT("email_company_sendHtmlMailNoSubject"),

    EMAIL_COMPANY_SENDHTMLMAILNORECIPIENT("email_company_sendHtmlMailNoRecipient"),

    EMAIL_COMPANY_SENDHTMLMAILGMAILACCOUNTEXPIRED("email_company_sendHtmlMailGmailAccountExpired"),

    EMAIL_COMPANY_SENDEMAILBLASTNOTFOUNDDEFAULTUSER("email_company_sendEmailBlastNotFoundDefaultUser"),

    EMAIL_USEREMAIL_NEEDCHECKGMAILALIASBINDING("email_userEmail_needCheckGmailAliasBinding"),

    EMAIL_USEREMAIL_REQUESTTOBINDINGGMAILALIASEMAILNULL("email_userEmail_requestToBindingGmailAliasEmailNull"),

    EMAIL_USEREMAIL_REQUESTTOBINDINGGMAILALIASERROR("email_userEmail_requestToBindingGmailAliasError"),

    EMAIL_USEREMAIL_REQUESTTOBINDINGGMAILALIASIOERROR("email_userEmail_requestToBindingGmailAliasIoError"),

    EMAIL_USEREMAIL_CONFIRMBINDINGGMAILALIASBINDLISTNULL("email_userEmail_confirmBindingGmailAliasBindListNull"),

    EMAIL_USEREMAIL_CONFIRMBINDINGGMAILALIASIOERROR("email_userEmail_confirmBindingGmailAliasIoError"),

    EMAIL_USEREMAIL_FINDCURRENTUSEREMAILUSERNULL("email_userEmail_findCurrentUserEmailUserNull"),

    EMAIL_USEREMAIL_DOWNLOADRESUMEPARAMNULL("email_userEmail_downloadResumeParamNull"),

    EMAIL_USEREMAIL_CHECKJDFILENAMENULL("email_userEmail_checkJdFilenameNull"),

    EMAIL_USEREMAIL_CHECKJDCANNOTHTML("email_userEmail_checkJdCannotHtml"),

    EMAIL_USEREMAIL_CHECKJDTEXTLONG("email_userEmail_checkJdTextLong"),

    EMAIL_USEREMAIL_CHECKJDFILENAMENOTNULL("email_userEmail_checkJdFilenameNotNull"),

    STORE_S3UPLOAD_FILENULL("store_s3Upload_fileNull"),

    STORE_S3UPLOAD_EXCEPTION("store_s3Upload_exception"),

    ;

    private final String key;

    CommonAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}