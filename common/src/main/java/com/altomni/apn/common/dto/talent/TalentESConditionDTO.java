package com.altomni.apn.common.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentESConditionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String esId;

    private List<String> domains;
}
