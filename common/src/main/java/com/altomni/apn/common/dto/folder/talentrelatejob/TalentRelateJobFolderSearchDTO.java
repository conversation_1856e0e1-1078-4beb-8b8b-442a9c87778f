package com.altomni.apn.common.dto.folder.talentrelatejob;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;


@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRelateJobFolderSearchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "job id for the related folder")
    private Long jobId;

    @ApiModelProperty(value = "user id for the related folder")
    private Long userId;



}
