package com.altomni.apn.common.utils;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.experimental.UtilityClass;

import java.util.HashMap;
import java.util.Map;

@UtilityClass
public class TenantConfigUtil {

    public Map<String, String> getMapFiledAndValueFromConfig(String configValue) {
        JSONArray jsonArray = JSONUtil.parseArray(configValue);
        Map<String, String> fieldToValueMap = new HashMap<>(16);
        jsonArray.forEach(obj -> {
            JSONObject jsonObj = (JSONObject) obj;
            fieldToValueMap.put(jsonObj.getStr("field"), jsonObj.getStr("value"));
        });
        return fieldToValueMap;
    }

}
