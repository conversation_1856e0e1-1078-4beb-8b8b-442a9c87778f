package com.altomni.apn.common.domain.folder;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@Entity
@Data
@Table(name = "job_sharing_platform_talent_related_job_folder_relation")
public class JobSharingPlatformTalentRelatedJobFolderRelation extends AbstractPermissionAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "talent_association_job_folder_folder_id",unique = true, nullable = false)
    private String talentAssociatedJobFolderFolderId;

    @Column(name = "shared_link_expire_time", nullable = false)
    private Instant sharedLinkExpireTime;

}