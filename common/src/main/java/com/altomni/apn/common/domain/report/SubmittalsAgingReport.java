package com.altomni.apn.common.domain.report;

import com.altomni.apn.common.domain.enumeration.application.ActivityStatus;
import com.altomni.apn.common.domain.enumeration.support.ActivityStatusConverter;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@ApiModel(description = "submittals_aging_report storage the submittals aging reports data")
@Entity
@Table(name = "submittals_aging_report")
public class SubmittalsAgingReport implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "batch_id", required = true)
    @Column(name = "batch_id")
    private String batchId;

    @ApiModelProperty(value = "company", required = true)
    @Column(name = "company")
	private String company;

    @ApiModelProperty(value = "recruiter_user_id", required = true)
    @Column(name = "recruiter_user_id")
    private Long recruiterUserId;

    @ApiModelProperty(value = "recruiter", required = true)
    @Column(name = "recruiter")
    private String recruiter;

    @Convert(converter = ActivityStatusConverter.class)
    @Column(name = "activity_status")
    private ActivityStatus activityStatus;

    @ApiModelProperty(value = "The activity status description.")
    @Column(name = "activity_status_desc")
    private String activityStatusDesc;

    @ApiModelProperty(value = "The count of activity status have no update exceed 7 days", required = true)
    @Column(name = "less_seven_count")
    private Integer lessSevenCount = 0;

    @ApiModelProperty(value = "The activity id of activity status have no update exceed 7 days ")
    @Column(name = "less_seven_activity_id")
    private String lessSevenActivityId;

    @ApiModelProperty(value = "The count of activity status have no update exceed 7 days", required = true)
    @Column(name = "seven_count")
    private Integer sevenCount = 0;

    @ApiModelProperty(value = "The activity id of activity status have no update exceed 7 days ")
    @Column(name = "seven_activity_id")
    private String sevenActivityId;

    @ApiModelProperty(value = "The count of activity status have no update exceed 14 days", required = true)
    @Column(name = "fourteen_count")
    private Integer fourteenCount = 0;

    @ApiModelProperty(value = "The activity id of activity status have no update exceed 14 days ")
    @Column(name = "fourteen_activity_id")
    private String fourteenActivityId;

    @ApiModelProperty(value = "The count of activity status have no update exceed 21 days", required = true)
    @Column(name = "twenty_one_count")
    private Integer twentyOneCount = 0;

    @ApiModelProperty(value = "The activity id of activity status have no update exceed 21 days ")
    @Column(name = "twenty_one_activity_id")
    private String twentyOneActivityId;

    @ApiModelProperty(value = "The count of activity status have no update exceed 30 days", required = true)
    @Column(name = "thirty_count")
    private Integer thirtyCount = 0;

    @ApiModelProperty(value = "The activity id of activity status have no update exceed 30 days ")
    @Column(name = "thirty_activity_id")
    private String thirtyActivityId;

    @ApiModelProperty(value = "The count of activity status have no update exceed 60 days", required = true)
    @Column(name = "sixty_count")
    private Integer sixtyCount = 0;

    @ApiModelProperty(value = "The activity id of activity status have no update exceed 60 days ")
    @Column(name = "sixty_activity_id")
    private String sixtyActivityId;

    @ApiModelProperty(value = "The count of activity status have no update exceed 90 days", required = true)
    @Column(name = "ninety_count")
    private Integer ninetyCount = 0;

    @ApiModelProperty(value = "The activity id of activity status have no update exceed 90 days ")
    @Column(name = "ninety_activity_id")
    private String ninetyActivityId;

    @CreatedBy
    @Column(name = "created_by", nullable = false, length = 50, updatable = false)
    private String createdBy;

    @CreatedDate
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Long getRecruiterUserId() {
        return recruiterUserId;
    }

    public void setRecruiterUserId(Long recruiterUserId) {
        this.recruiterUserId = recruiterUserId;
    }

    public String getRecruiter() {
        return recruiter;
    }

    public void setRecruiter(String recruiter) {
        this.recruiter = recruiter;
    }

    public ActivityStatus getActivityStatus() {
        return activityStatus;
    }

    public void setActivityStatus(ActivityStatus activityStatus) {
        this.activityStatus = activityStatus;
    }

    public String getActivityStatusDesc() {
        return activityStatusDesc;
    }

    public void setActivityStatusDesc(String activityStatusDesc) {
        this.activityStatusDesc = activityStatusDesc;
    }

    public Integer getLessSevenCount() {
        return lessSevenCount;
    }

    public void setLessSevenCount(Integer lessSevenCount) {
        this.lessSevenCount = lessSevenCount;
    }

    public String getLessSevenActivityId() {
        return lessSevenActivityId;
    }

    public void setLessSevenActivityId(String lessSevenActivityId) {
        this.lessSevenActivityId = lessSevenActivityId;
    }

    public Integer getSevenCount() {
        return sevenCount;
    }

    public void setSevenCount(Integer sevenCount) {
        this.sevenCount = sevenCount;
    }

    public String getSevenActivityId() {
        return sevenActivityId;
    }

    public void setSevenActivityId(String sevenActivityId) {
        this.sevenActivityId = sevenActivityId;
    }

    public Integer getFourteenCount() {
        return fourteenCount;
    }

    public void setFourteenCount(Integer fourteenCount) {
        this.fourteenCount = fourteenCount;
    }

    public String getFourteenActivityId() {
        return fourteenActivityId;
    }

    public void setFourteenActivityId(String fourteenActivityId) {
        this.fourteenActivityId = fourteenActivityId;
    }

    public Integer getTwentyOneCount() {
        return twentyOneCount;
    }

    public void setTwentyOneCount(Integer twentyOneCount) {
        this.twentyOneCount = twentyOneCount;
    }

    public String getTwentyOneActivityId() {
        return twentyOneActivityId;
    }

    public void setTwentyOneActivityId(String twentyOneActivityId) {
        this.twentyOneActivityId = twentyOneActivityId;
    }

    public Integer getThirtyCount() {
        return thirtyCount;
    }

    public void setThirtyCount(Integer thirtyCount) {
        this.thirtyCount = thirtyCount;
    }

    public String getThirtyActivityId() {
        return thirtyActivityId;
    }

    public void setThirtyActivityId(String thirtyActivityId) {
        this.thirtyActivityId = thirtyActivityId;
    }

    public Integer getSixtyCount() {
        return sixtyCount;
    }

    public void setSixtyCount(Integer sixtyCount) {
        this.sixtyCount = sixtyCount;
    }

    public String getSixtyActivityId() {
        return sixtyActivityId;
    }

    public void setSixtyActivityId(String sixtyActivityId) {
        this.sixtyActivityId = sixtyActivityId;
    }

    public Integer getNinetyCount() {
        return ninetyCount;
    }

    public void setNinetyCount(Integer ninetyCount) {
        this.ninetyCount = ninetyCount;
    }

    public String getNinetyActivityId() {
        return ninetyActivityId;
    }

    public void setNinetyActivityId(String ninetyActivityId) {
        this.ninetyActivityId = ninetyActivityId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    @Override
    public String toString() {
        return "SubmittalsAgingReport{" +
            "id=" + id +
            ", batchId='" + batchId + '\'' +
            ", company='" + company + '\'' +
            ", recruiterUserId=" + recruiterUserId +
            ", recruiter='" + recruiter + '\'' +
            ", activityStatus=" + activityStatus +
            ", activityStatusDesc='" + activityStatusDesc + '\'' +
            ", lessSevenCount=" + lessSevenCount +
            ", lessSevenActivityId='" + lessSevenActivityId + '\'' +
            ", sevenCount=" + sevenCount +
            ", sevenActivityId='" + sevenActivityId + '\'' +
            ", fourteenCount=" + fourteenCount +
            ", fourteenActivityId='" + fourteenActivityId + '\'' +
            ", twentyOneCount=" + twentyOneCount +
            ", twentyOneActivityId='" + twentyOneActivityId + '\'' +
            ", thirtyCount=" + thirtyCount +
            ", thirtyActivityId='" + thirtyActivityId + '\'' +
            ", sixtyCount=" + sixtyCount +
            ", sixtyActivityId='" + sixtyActivityId + '\'' +
            ", ninetyCount=" + ninetyCount +
            ", ninetyActivityId='" + ninetyActivityId + '\'' +
            ", createdBy='" + createdBy + '\'' +
            ", createdDate=" + createdDate +
            '}';
    }
}
