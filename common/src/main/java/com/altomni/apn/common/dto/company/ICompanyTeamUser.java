package com.altomni.apn.common.dto.company;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

public interface ICompanyTeamUser {
    Long getUserId();
    String getFirstName();
    String getLastName();
    String getRole();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class CompanyTeamUserDTO{
        private Long userId;
        private String firstName;
        private String lastName;
        private String role;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof CompanyTeamUserDTO)) return false;
            CompanyTeamUserDTO that = (CompanyTeamUserDTO) o;
            return Objects.equals(userId, that.userId) &&
                    Objects.equals(role, that.role);
        }

        @Override
        public int hashCode() {
            return Objects.hash(userId, role);
        }
    }
}
