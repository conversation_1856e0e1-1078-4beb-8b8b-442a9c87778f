package com.altomni.apn.common.enumeration.permission;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobStatus enumeration.
 */
public enum DataScope implements ConvertedEnum<Integer> {
    PERMISSION_NO(0),
    PERMISSION_SELF(1),
    PERMISSION_TEAM(2),
    PERMISSION_EXTRA_TEAM(3),
    PERMISSION_ALL(99);

    private final int dbValue;

    DataScope(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<DataScope, Integer> resolver =
        new ReverseEnumResolver<>(DataScope.class, DataScope::toDbValue);

    public static DataScope fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
