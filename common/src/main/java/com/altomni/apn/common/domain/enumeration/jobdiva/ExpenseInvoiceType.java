package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum ExpenseInvoiceType implements ConvertedEnum<Integer>
{

    EXPENSE_DETAIL(0),
    CATEGORY_TOTALS(1);

    private final int dbValue;

    ExpenseInvoiceType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<ExpenseInvoiceType, Integer> resolver = new ReverseEnumResolver<>(ExpenseInvoiceType.class, ExpenseInvoiceType::toDbValue);

    public static ExpenseInvoiceType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
