package com.altomni.apn.common.domain.enumeration.job;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.ArrayList;
import java.util.List;

/**
 * The JobType enumeration.
 */
public enum JobType implements ConvertedEnum<Integer> {
    DIRECT_PLACEMENT(0,"DIRECT_PLACEMENT"),
    CONTRACT(1,"General Staffing (Contract)"),
    RIGHT_TO_HIRE(2,"RIGHT_TO_HIRE"),
    FULL_TIME(3,"General Recruiting (FTE)"),
    PART_TIME(4,"PART_TIME"),
    PAY_ROLL(5,"Payrolling"),

    //FULL_ TIME_ OR_ PART_ Time & intersthip is a new job type in the IPG system.
    FULL_TIME_OR_PART_TIME(6,"FULL_TIME/PART_TIME"),
    INTERNSHIP(7,"Intern"),
    OTHERS(8, "OTHERS/GENERAL_RECRUITING"),
    MSP(9, "MSP"),
    ;

    private final int dbValue;
    private final String name;

    public static final List<Integer> ALL_JOB_TYPES = new ArrayList<>();

    static {
        for (JobType jobType: JobType.values()) {
            ALL_JOB_TYPES.add(jobType.toDbValue());
        }
    }

    JobType(int dbValue,String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }



    // static resolving:
    public static final ReverseEnumResolver<JobType, Integer> resolver =
        new ReverseEnumResolver<>(JobType.class, JobType::toDbValue);

    public static JobType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
