package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class EnumCompanyServiceTypeService {

    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumCompanyServiceType> findAllByIds(List<Long> ids) {
        List<EnumCompanyServiceType> enumCompanyServiceTypeList = enumCommonService.findAllEnumCompanyServiceType(SecurityUtils.getTenantId());
        return enumCompanyServiceTypeList.stream().filter(o -> ids.contains(o.getId())).collect(Collectors.toList());
    }

    public List<EnumCompanyServiceType> findAll(SortType type) {
        List<EnumCompanyServiceType> enumCompanyServiceTypeList = enumCommonService.findAllEnumCompanyServiceType(SecurityUtils.getTenantId());
        if (enumCompanyServiceTypeList == null) {
            return new ArrayList<>();
        }
        if (SortType.CN.equals(type)) {
            return enumCompanyServiceTypeList.stream().sorted(Comparator.comparing(EnumCompanyServiceType::getCnDisplayOrder)).collect(Collectors.toList());
        }
        return enumCompanyServiceTypeList.stream().sorted(Comparator.comparing(EnumCompanyServiceType::getEnDisplayOrder)).collect(Collectors.toList());
    }

    public List<EnumDictVO> findAllTypeList(SortType type) {
        List<EnumDictDTO> enumCompanyServiceTypeList = findAllTypes(type);
        List<EnumDictVO> result = new ArrayList<>();
        traverseCompanyServiceTypeList(enumCompanyServiceTypeList, result, type);
        return result;
    }

    private void traverseCompanyServiceTypeList(List<EnumDictDTO> enumCompanyServiceTypeList, List<EnumDictVO> result, SortType type) {
        enumCompanyServiceTypeList.forEach(item -> {
            EnumDictVO enumDictVO = EnumDictVO.fromEnumDictDTO(item, type);
            if (CollUtil.isNotEmpty(item.getChildren())) {
                List<EnumDictVO> children = new ArrayList<>();
                traverseCompanyServiceTypeList(item.getChildren(), children, type);
                enumDictVO.setChildren(children);
            }
            result.add(enumDictVO);
        });
    }

    public List<EnumDictDTO> findAllTypes(SortType type) {
        List<EnumCompanyServiceType> enumCompanyServiceTypeList = this.findAll(type);
        if (enumCompanyServiceTypeList == null || CollUtil.isEmpty(enumCompanyServiceTypeList)) {
            return new ArrayList<>();
        }

        List<EnumDictDTO> allServiceTypeList = enumCompanyServiceTypeList.stream().map(this::fromEnumCompanyServiceType).collect(Collectors.toList());
        Set<String> serviceFilterTypeSet = allServiceTypeList.stream().filter(o -> o.getParentId() == null).map(EnumDictDTO::getId).collect(Collectors.toSet());;
        List<EnumDictDTO> result = allServiceTypeList.stream().filter(o -> o.getParentId() == null).collect(Collectors.toList());
        result.forEach(item -> findChildren(item, allServiceTypeList, serviceFilterTypeSet));
        allServiceTypeList.forEach(o -> {
            if (!serviceFilterTypeSet.contains(o.getId())) {
                result.add(o);
            }
        });
        if (SortType.CN.equals(type)) {
            return result.stream().sorted(Comparator.comparing(EnumDictDTO::getCnDisplayOrder)).collect(Collectors.toList());
        }
        return result.stream().sorted(Comparator.comparing(EnumDictDTO::getEnDisplayOrder)).collect(Collectors.toList());
    }

    private EnumDictDTO fromEnumCompanyServiceType(EnumCompanyServiceType enumCompanyServiceType) {
        EnumDictDTO enumDictDTO = new EnumDictDTO();
        ServiceUtils.myCopyProperties(enumCompanyServiceType, enumDictDTO);
        enumDictDTO.setId(enumCompanyServiceType.getId().toString());
        enumDictDTO.setName(enumCompanyServiceType.getName());
        enumDictDTO.setParentId(enumCompanyServiceType.getParentCategory());
        enumDictDTO.setLabel(enumCompanyServiceType.getEnDisplay());
        enumDictDTO.setLabelCn(enumCompanyServiceType.getCnDisplay());
        return enumDictDTO;
    }

    private void findChildren(EnumDictDTO enumDictDTO,  List<EnumDictDTO> allServiceTypeList, Set<String> serviceFilterTypeSet) {
        List<EnumDictDTO> childrenTypeList = allServiceTypeList.stream().filter(o -> enumDictDTO.getName().equals(o.getParentId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(childrenTypeList)) {
            return;
        }
        serviceFilterTypeSet.addAll(childrenTypeList.stream().map(EnumDictDTO::getId).collect(Collectors.toSet()));
        enumDictDTO.setChildren(childrenTypeList);
        childrenTypeList.forEach(child -> findChildren(child, allServiceTypeList, serviceFilterTypeSet));
    }
}

