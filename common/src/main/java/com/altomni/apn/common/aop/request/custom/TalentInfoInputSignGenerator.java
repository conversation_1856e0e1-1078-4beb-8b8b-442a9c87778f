package com.altomni.apn.common.aop.request.custom;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.ArgsSignGenerator;
import com.altomni.apn.common.dto.redis.ImagesInfoDTO;
import com.altomni.apn.common.dto.talent.TalentInfoInput;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;

import java.util.List;

/**
 * 针对入参是TalentInfoInput resumes里的displayLink两个tab页要生成2个不同的临时链接 导致生成签名不对 无法拦截
 */
public class TalentInfoInputSignGenerator implements ArgsSignGenerator {
    @Override
    public String generateSign(Object[] args) {
        if (ArrayUtil.isEmpty(args)) {
            return "empty";
        }

        StringBuilder sb = new StringBuilder();
        for (Object arg : args) {
            if (arg != null) {
                if (arg instanceof TalentInfoInput) {
                    String jsonStr = JSONUtil.toJsonStr(arg);
                    TalentInfoInput bean = JSONUtil.toBean(jsonStr, TalentInfoInput.class);
                    List<TalentResumeDTO> resumes = bean.getResumes();
                    if(CollUtil.isNotEmpty(resumes)) {
                        for(TalentResumeDTO resume : resumes) {
                            ImagesInfoDTO imagesInfo = resume.getImagesInfo();
                            if(imagesInfo != null) {
                                //临时display 每个都不一样 生成sign跳过该值
                                imagesInfo.setDisplayLink(null);
                                imagesInfo.setPortraitLink(null);
                            }
                        }
                    }
                    sb.append(JSONUtil.toJsonStr(bean));
                } else {
                    sb.append(JSONUtil.toJsonStr(arg));
                }
            }
        }
        return SecureUtil.md5(sb.toString());
    }
}
