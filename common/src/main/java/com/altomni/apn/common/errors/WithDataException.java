package com.altomni.apn.common.errors;

import com.altomni.apn.common.config.constants.ExceptionTypeConstants;
import com.fasterxml.jackson.annotation.JsonIgnore;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

import static cn.hutool.http.Status.HTTP_INTERNAL_ERROR;

public class WithDataException extends AbstractThrowableProblem {

    private static final long serialVersionUID = 1L;

    private String message;

    private Object data;

    private Integer internalErrorClassCode;

    public WithDataException() {
        this(ErrorConstants.DEFAULT_TYPE, null, null, null);
    }

    public WithDataException(String message) {
        this(ErrorConstants.DEFAULT_TYPE, message, null, null);
        this.internalErrorClassCode = ExceptionTypeConstants.EXCEPTION_WITH_DATA_EXCEPTION;
    }

    public WithDataException(String message, Integer code, Object data) {
        this(ErrorConstants.DEFAULT_TYPE, message, code, data);
        this.internalErrorClassCode = ExceptionTypeConstants.EXCEPTION_WITH_DATA_EXCEPTION;
    }

    public WithDataException(URI type, String message, Integer code, Object data) {
        super(type, "Service Interface Exception ", Status.valueOf(code == null ? HTTP_INTERNAL_ERROR : code), null, null, null, getAlertParameters(message, code, data));
        this.message = message;
        this.data = data;
        this.internalErrorClassCode = ExceptionTypeConstants.EXCEPTION_WITH_DATA_EXCEPTION;
    }

    private static Map<String, Object> getAlertParameters(String message, Integer code, Object data) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("message", message);
        parameters.put("data", data);
        return parameters;
    }

    @JsonIgnore
    public Object getData() { return data; }

    @Override
    public String getMessage() { return message; }

    public Integer getInternalErrorClassCode() { return internalErrorClassCode; }
}
