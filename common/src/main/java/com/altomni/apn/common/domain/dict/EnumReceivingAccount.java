package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@ApiModel(description = "Enum receiving account entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_receiving_account")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumReceivingAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "account_name")
    private String accountName;

    @Column(name = "currency")
    private Integer currency;

    @Column(name = "account_info")
    private String accountInfo;

    @Column(name = "en_display_order")
    private Integer enDisplayOrder;

}
