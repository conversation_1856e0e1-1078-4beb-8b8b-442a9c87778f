package com.altomni.apn.common.vo.store;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class StoreGetUploadUrlVO {

    private String fileName;

    private String uuid;

    private Map<String, String> postPolicy;

    private String s3Link;

    private String status;
}