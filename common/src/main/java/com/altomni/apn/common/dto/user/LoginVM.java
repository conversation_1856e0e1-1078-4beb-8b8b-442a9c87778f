package com.altomni.apn.common.dto.user;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * View Model object for storing a user's credentials.
 */
@Data
@NoArgsConstructor
public class LoginVM implements Cloneable{

    @NotNull
    @Size(min = 1, max = 50)
    private String username;

    @NotNull
    @Size(min = 4, max = 100)
    private String password;

    private String timeZone;

    private String ip;

    private String userAgent;

    private boolean rememberMe;

    /**
     * for impersonation login purpose
     */
    private Long targetUserId;

    private String uid;

    @Override
    public String toString() {
        return "LoginVM{" +
                "username='" + username + '\'' +
                ", timeZone='" + timeZone + '\'' +
                ", ip='" + ip + '\'' +
                ", userAgent='" + userAgent + '\'' +
                ", targetUserId='" + targetUserId + '\'' +
                ", rememberMe=" + rememberMe +
                '}';
    }
}