package com.altomni.apn.common.service.enums;

import cn.hutool.core.util.PinyinUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.dict.EnumIndustry;
import com.altomni.apn.common.domain.dict.EnumJobFunction;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.dto.enums.JobFunctionsDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.UserService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class EnumJobFunctionService {

    private final Logger log = LoggerFactory.getLogger(EnumJobFunctionService.class);

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private UserService userService;

    private static Map<String, Map<Long, String>> jobFunctionsForIndustryCache = new ConcurrentHashMap<>();

    public JobFunctionsDTO getJobFunctions(Long id, SortType type, Boolean mapping) {
        JobFunctionsDTO jobFunctionsDTO = new JobFunctionsDTO();
        jobFunctionsDTO.setTree(getJobFunctionTree(id, type));
        if(Boolean.TRUE.equals(mapping)) {
            jobFunctionsDTO.setMapping(getJobFunctionsMapping(type));
        }
        return jobFunctionsDTO;
    }

    private String getJobFunctionTree(Long id, SortType type) {
        Map<Long, String> typeMap = jobFunctionsForIndustryCache.get(type.name());
        if(typeMap == null) {
            typeMap = new ConcurrentHashMap<>();
            jobFunctionsForIndustryCache.put(type.name(), typeMap);
        }
        String json = typeMap.get(id);
        if(json != null) {
            return json;
        }
        String subJobFunctions = "";
        //-1为默认job functions
        if(-1L == id) {
            subJobFunctions = userService.getTenantParamStringValue(Constants.DEFAULT_JOB_FUNCTIONS).getBody();
        } else if(-2L == id) {
            //-2为all job functions
            subJobFunctions = userService.getTenantParamStringValue(Constants.ALL_JOB_FUNCTIONS).getBody();
        } else {
            EnumIndustry industry = enumCommonService.getIndustryById(id);
            subJobFunctions = industry.getSubJobFunctions();
        }
        subJobFunctions = getJobFunctions(subJobFunctions, type);
        typeMap.put(id, subJobFunctions);

        return subJobFunctions;
    }

    private String getJobFunctions(String subJobFunctions, SortType type) {
        JSONObject jsonObject = JSONUtil.parseObj(subJobFunctions);
        Map<Long, EnumJobFunction> map = enumCommonService.findAllEnumJobfunction().stream().collect(Collectors.toMap(EnumJobFunction::getId, Function.identity()));
        addLabel(jsonObject.getJSONArray("children"), map, type);
        sortJobFunction(jsonObject, map, type);
        return JSONUtil.toJsonStr(jsonObject);
    }

    private void sortJobFunction(JSONObject jsonObject, Map<Long, EnumJobFunction> map, SortType type) {
        JSONArray children = jsonObject.getJSONArray("children");
        if(children != null) {
            //同级别有子类的在前， 按首字母排序， 其次英文在前，中文灾后
            children.sort(Comparator.comparing(f -> {
                Long id = ((JSONObject) f).getLong("id");
                boolean existChildren = ((JSONObject) f).containsKey("children");
                EnumJobFunction enumJobFunction = map.get(id);
                if(enumJobFunction == null) {
                    return "z";
                }
                if(SortType.CN.equals(type)) {
                    String cnDisplay = enumJobFunction.getCnDisplay();
                    if(StringUtils.isEmpty(cnDisplay)) {
                        return "z";
                    }
                    String sortDisplay = PinyinUtil.getPinYin(cnDisplay);
                    return getSortDisplay(existChildren, sortDisplay);
                } else {
                    String sortDisplay = enumJobFunction.getEnDisplay();
                    if(StringUtils.isEmpty(sortDisplay)) {
                        return "z";
                    }
                    return existChildren ? "0" + sortDisplay : "1" + sortDisplay;
                }
            }));
            for(int i = 0; i< children.size(); i++) {
                sortJobFunction(children.getJSONObject(i), map, type);
            }
        }
    }

    private String getSortDisplay(boolean existChildren, String sortDisplay) {
        if(sortDisplay.substring(0,1).matches("^[a-zA-Z]*$")) {
            return existChildren ? "00" + sortDisplay : "10" + sortDisplay;
        } else {
            return existChildren ? "01" + sortDisplay : "11" + sortDisplay;
        }
    }

    private void addLabel(JSONArray jsonArray, Map<Long, EnumJobFunction> map, SortType type) {
        for(int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            Long id = jsonObject.getLong("id");
            if(SortType.EN.equals(type)) {
                EnumJobFunction enumJobFunction = map.get(id);
                if(enumJobFunction != null) {
                    jsonObject.put("label", enumJobFunction.getEnDisplay());
                }
            } else if(SortType.CN.equals(type)) {
                EnumJobFunction enumJobFunction = map.get(id);
                if(enumJobFunction != null) {
                    jsonObject.put("label", enumJobFunction.getCnDisplay());
                    jsonObject.put("labelEn", enumJobFunction.getEnDisplay());
                }
            }
            JSONArray children = jsonObject.getJSONArray("children");
            if(children != null) {
                addLabel(children, map, type);
            }
        }
    }

    private List<EnumDictDTO> getJobFunctionsMapping(SortType type) {
        List<EnumJobFunction> allEnumJobfunction = enumCommonService.findAllEnumJobfunction();
        return allEnumJobfunction.stream().filter(c -> StringUtils.isNotEmpty(c.getEnDisplay()) || StringUtils.isNotEmpty(c.getCnDisplay())).map(c -> {
            EnumDictDTO enumDictDTO = new EnumDictDTO();
            enumDictDTO.setId(c.getId().toString());
            if(SortType.EN.equals(type)) {
                enumDictDTO.setLabel(c.getEnDisplay());
            } else if(SortType.CN.equals(type)) {
                enumDictDTO.setLabel(c.getCnDisplay());
            }
            enumDictDTO.setLabelEn(c.getEnDisplay());
            return enumDictDTO;
        }).toList();
    }

//    public List<EnumDictDTO> findAllOrderBySortType(SortType type) {
//        log.info("[APN: EnumJobFunctionService @{}] request to get jobFunctions enum data, type: {}", SecurityUtils.getUserId(), type);
//
//        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
//        List<EnumJobFunction> result = new ArrayList<>();
//        if (SortType.EN.equals(type)) {
//            enumJobFunctionList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumJobFunction::getEnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
//        } else {
//            enumJobFunctionList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumJobFunction::getCnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
//        }
//
//        List<EnumJobFunction> parentCategoryList = findAllParents();
//        if (CollectionUtil.isNotEmpty(result) && CollectionUtil.isNotEmpty(parentCategoryList)) {
//            if (SortType.EN.equals(type)){
//                result.forEach(r -> r.setCnLable(false));
//                parentCategoryList.forEach(p -> p.setCnLable(false));
//            }else{
//                result.forEach(r -> r.setCnLable(true));
//                parentCategoryList.forEach(p -> p.setCnLable(true));
//            }
//            //set parent id by parentCategory and format EnumDictDTO for webSide
//            List<EnumDictDTO> dtoList = result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay()))
//                    .peek(s -> parentCategoryList.stream().filter(p ->ObjectUtil.isNotNull(s.getParentCategory()) && s.getParentCategory().equals(p.getName()))
//                    .forEach(pc ->{
//                        s.setParentCategory(StrUtil.toString(pc.getId()));
//                    })).collect(Collectors.toList()).stream().map(EnumDictDTO::fromBizDict).collect(Collectors.toList());
//            //format children node for webSide
//            Map<String, List<EnumDictDTO>> pidListMap =
//                    dtoList.stream().collect(Collectors.groupingBy(EnumDictDTO::getParentId));
//            dtoList.forEach(item -> {
//                List<EnumDictDTO> childrenNodes = pidListMap.get(item.getId());
//                if (CollectionUtil.isNotEmpty(childrenNodes)) {
//                    item.setChildren(childrenNodes);
//                }
//            });
//            return pidListMap.get(Constants.PARENT_NODE_ID);
//        } else {
//            return new ArrayList<>();
//        }
//    }

//    private List<EnumJobFunction> findAllParents() {
//        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
//        Set<String> enumParentCategoryList = enumJobFunctionList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).map(EnumJobFunction::getParentCategory).filter(ObjectUtil::isNotEmpty).collect(Collectors.toSet());
//        return enumJobFunctionList.stream().filter(o -> enumParentCategoryList.contains(o.getName())).collect(Collectors.toList());
//    }

//    public List<EnumDictDTO> findAllForCreationOrderBySortType(SortType type) {
//        log.info("[APN: EnumJobFunctionService @{}] request to get jobFunctions enum data, type: {}", SecurityUtils.getUserId(), type);
//
//        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
//        List<EnumJobFunction> result = new ArrayList<>();
//        if (SortType.EN.equals(type)) {
//            enumJobFunctionList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumJobFunction::getEnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
//        } else {
//            enumJobFunctionList.stream().filter(s -> ObjectUtil.isNotEmpty(s.getEnDisplay())).sorted(Comparator.comparingLong(EnumJobFunction::getCnDisplayOrder)).collect(Collectors.toList()).forEach(o -> result.add(SerializationUtils.clone(o)));
//        }
//
//        List<EnumJobFunction> parentCategoryList = findAllParents();
//        if (CollectionUtil.isNotEmpty(result) && CollectionUtil.isNotEmpty(parentCategoryList)) {
//            if (SortType.EN.equals(type)){
//                result.forEach(r -> r.setCnLable(false));
//                parentCategoryList.forEach(p -> p.setCnLable(false));
//            }else{
//                result.forEach(r -> r.setCnLable(true));
//                parentCategoryList.forEach(p -> p.setCnLable(true));
//            }
//            //set parent id by parentCategory
//            result.forEach(s -> {
//                for (EnumJobFunction p : parentCategoryList) {
//                    p.setIsParent(true);
//                    if (ObjectUtil.isNotNull(s.getParentCategory()) && s.getParentCategory().equals(p.getName())) {
//                        s.setParentCategory(p.getId() + Constants.PARENT_EN);
//                        break;
//                    }
//                }
//            });
//            //format EnumDictDTO for webSide
//            List<EnumDictDTO> dtoList = result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay())).map(EnumDictDTO::formatForCreation).collect(Collectors.toList());
//            //format children node for webSide
//            Map<String, List<EnumDictDTO>> pidListMap =
//                    dtoList.stream().collect(Collectors.groupingBy(EnumDictDTO::getParentId));
//            dtoList.forEach(item -> {
//                List<EnumDictDTO> childrenNodes = pidListMap.get(item.getId());
//                if (CollectionUtil.isNotEmpty(childrenNodes)) {
//                    EnumDictDTO otherNode = new EnumDictDTO();
//                    otherNode.setId(StrUtil.removeSuffix(item.getId(), Constants.PARENT_EN));
//                    if(SortType.CN.equals(type)){
//                        otherNode.setLabel(item.getLabel() + Constants.OTHER_CN);
//                    }else{
//                        otherNode.setLabel(item.getLabel() + Constants.OTHER_EN);
//                    }
//                    otherNode.setValue(StrUtil.removeSuffix(item.getId(), Constants.PARENT_EN));
//                    otherNode.setParentId(item.getId());
//                    otherNode.setChecked(false);
//                    childrenNodes.add(otherNode);
//                    item.setChildren(childrenNodes);
//                }
//            });
//            return pidListMap.get(Constants.PARENT_NODE_ID);
//        } else {
//            return new ArrayList<>();
//        }
//    }

//    public List<String> transferJobFunctionsByIds(List<String> ids) {
//        log.info("[APN: EnumJobFunctionService @{}] request to transfer jobFunctions by ids, ids: {}", SecurityUtils.getUserId(), ids);
//        if (ObjectUtil.isEmpty(ids)) {
//            return null;
//        }
//        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
//        List<EnumJobFunction> list = enumJobFunctionList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).collect(Collectors.toList());
//        return list.stream().map(EnumJobFunction::getName).distinct().collect(Collectors.toList());
//    }

//    public List<String> getJobFunctionsByIds(List<String> ids) {
//        if (CollUtil.isEmpty(ids)) {
//            return null;
//        }
//        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
//        return enumJobFunctionList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).map(EnumJobFunction::getName).collect(Collectors.toList());
//    }
//
//    public List<Long> transferJobFunctionsByNamesToId(Object nameObject) {
//        log.info("[APN: EnumJobFunctionService @{}] request to transfer jobFunctions by itemTier, nameObject: {}", SecurityUtils.getUserId(), nameObject);
//        if (ObjectUtil.isEmpty(nameObject)) {
//            return null;
//        }
//        return formatJobFunctionsByNames(nameObject).stream().map(EnumJobFunction::getId).collect(Collectors.toList());
//    }
//
//    public List<Long> transferJobFunctionsByNamesToIdWithoutIgnoreParentClass(cn.hutool.json.JSONArray names) {
//        if (ObjectUtil.isEmpty(names)) {
//            return null;
//        }
//        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
//        Set<String> formatList = Convert.toSet(String.class, names);
//        return enumJobFunctionList.stream().filter(o -> formatList.contains(o.getName()) && ObjectUtil.isNotEmpty(o.getEnDisplay())).map(EnumJobFunction::getId).collect(Collectors.toList());
//    }

//    public List<String> getJobFunctionsUINameByIds(String ids) {
//        log.info("[APN: EnumJobFunctionService @{}] request to get jobFunctions UINames by ids, nameObject: {}", SecurityUtils.getUserId(), ids);
//        Set<String> idList = Convert.toSet(String.class, JSONUtil.parseArray(ids));
//        if (CollectionUtil.isEmpty(idList)) {
//            return null;
//        }
//
//        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
//        List<EnumJobFunction> list = enumJobFunctionList.stream().filter(o -> idList.contains(String.valueOf(o.getId()))).collect(Collectors.toList());
//        List<EnumJobFunction> childrenList = enumJobFunctionList.stream().filter(o -> list.stream().map(EnumJobFunction::getName).collect(Collectors.toSet()).contains(o.getParentCategory())).collect(Collectors.toList());
//        List<String> parentNamesList = childrenList.stream().distinct().filter(d -> StringUtils.isNotBlank(d.getEnDisplay())).map(EnumJobFunction::getParentCategory).collect(Collectors.toList());
//        return list.stream().map(s -> {
//            String enDisplay = s.getEnDisplay();
//            if (parentNamesList.contains(s.getName())) {
//                enDisplay = s.getEnDisplay() + Constants.OTHER_EN;
//            }
//            return enDisplay;
//        }).filter(StrUtil::isNotBlank).collect(Collectors.toList());
//    }

//    public List<String> transferJobFunctionsByNames(Object nameObject) {
//        if (ObjectUtil.isEmpty(nameObject)) {
//            return null;
//        }
//        return formatJobFunctionsByNames(nameObject).stream().map(EnumJobFunction::getEnDisplay).collect(Collectors.toList());
//    }

//    private List<EnumJobFunction> formatJobFunctionsByNames(Object nameObject) {
//        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
//        Map<String, EnumJobFunction> enumJobFunctionMap = enumJobFunctionList.stream().collect(Collectors.toMap(EnumJobFunction::getName, Function.identity(), (key1, key2) -> key2));
//        List<EnumJobFunction> list = new ArrayList<>();
//        if (nameObject instanceof String) {
//            list = enumJobFunctionList.stream().filter(o -> o.getName().equals(ObjectUtil.toString(nameObject)) && ObjectUtil.isNotEmpty(o.getEnDisplay())).collect(Collectors.toList());
//            //if this jobfunction is inactived, try to find ancestor
//            if (CollUtil.isEmpty(list)) {
//                findAncestorByName(list, enumJobFunctionMap, ObjectUtil.toString(nameObject));
//            }
//        }
//        if (nameObject instanceof List || nameObject instanceof String[]) {
//            //if has any jobfunction is inactived, try to find ancestor
//            List<EnumJobFunction> inactivedList = new ArrayList<>();
//            findAncestorByNameIn(inactivedList, enumJobFunctionMap, Convert.toList(String.class, nameObject));
//            List<String> parentNameList = inactivedList.stream().map(EnumJobFunction::getParentCategory).distinct().filter(StringUtils::isNotBlank).collect(Collectors.toList());
//            //distinct and keep a subset
//            list = inactivedList.stream().filter(s -> !parentNameList.contains(s.getName())).collect(Collectors.toList());
//        }
//        return list.stream().distinct().collect(Collectors.toList());
//    }
//
//    private void findAncestorByName(List<EnumJobFunction> result, Map<String, EnumJobFunction> enumJobFunctionMap, String name) {
//
//        EnumJobFunction enumJobFunction = enumJobFunctionMap.get(name);
//        if (ObjectUtil.isEmpty(enumJobFunction)) {
//            return;
//        }
//
//        if (ObjectUtil.isEmpty(enumJobFunction.getParentCategory())) {
//            result.add(enumJobFunction);
//        } else {
//            if (ObjectUtil.isNotEmpty(enumJobFunction.getEnDisplay())) {
//                result.add(enumJobFunction);
//            } else {
//                name = enumJobFunction.getParentCategory();
//                findAncestorByName(result, enumJobFunctionMap, name);
//            }
//        }
//    }

//    private void findAncestorByNameIn(List<EnumJobFunction> result, Map<String, EnumJobFunction> enumJobFunctionMap, List<String> nameList) {
//        nameList.forEach(o -> findAncestorByName(result, enumJobFunctionMap, o));
//    }

    public Set<Long> findValidEnumJobFunctionsIds(){
        List<EnumJobFunction> enumJobFunctionList = enumCommonService.findAllEnumJobfunction();
        return enumJobFunctionList.stream().map(EnumJobFunction::getId).collect(Collectors.toSet());
    }

//    public Map<Long, String> getJobFunctionsMap() {
//        return enumCommonService.findAllEnumJobfunction().stream().collect(Collectors.toMap(EnumJobFunction::getId, EnumJobFunction::getName));
//    }

    /***
     * Activity Change function: get the UI name through the enumId;
     * @param id
     * @param displayType
     * @return
     */

//    public String getJobFunctionUINameById(Long id, DisplayType displayType) {
//        log.info("[APN: EnumJobFunctionService @{}] request to get job functions UINames by ids, nameObject: {}", SecurityUtils.getUserId(), id);
//
//        Map<Long, EnumJobFunction> enumJobFunctionMap = enumCommonService.findAllEnumJobfunctionMap();
//
//        if(enumJobFunctionMap.containsKey(id)) {
//            EnumJobFunction enumJobFunction = enumJobFunctionMap.get(id);
//            if(displayType.equals(DisplayType.EN) && StringUtils.isNotBlank(enumJobFunction.getEnDisplay())) {
//                return enumJobFunction.getEnDisplay();
//            } else if (displayType.equals(DisplayType.CN) && StringUtils.isNotBlank(enumJobFunction.getCnDisplay())) {
//                return enumJobFunction.getCnDisplay();
//            } else {
//                return enumJobFunction.getName();
//            }
//        }
//        return "Unknown Job Function";
//    }


}
