package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentPreference {
    private Long id;
    private String  title;

    private List<Integer> industries;

    private String currency;
    private RateUnitType payType;
    private RangeDTO salaryRange;

    private List<LocationDTO> locations;
    //仅在生成猎聘简历时使用
    private Double payTimes;
}
