package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "Enum currency entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_country")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumCountry implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "name")
    private String name;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "phone_area_code")
    private Integer areaCode;

    @Column(name = "country_code_3")
    private String countryCode;

    public EnumCountry(String name, String cnDisplay, String enDisplay, Integer areaCode) {
        this.name = name;
        this.cnDisplay = cnDisplay;
        this.enDisplay = enDisplay;
        this.areaCode = areaCode;
    }
}
