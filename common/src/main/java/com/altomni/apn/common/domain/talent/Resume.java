package com.altomni.apn.common.domain.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A Resume.
 */
@Data
@ApiModel(description = "Talent's resume metadata")
@Entity
@Table(name = "resume")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class Resume extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -5995736025769321575L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "uuid")
    private String uuid;

    @Column(name = "data_md5")
    private String dataMD5;

    @Column(name = "text")
    private String text;

    @Column(name = "parse_result")
    private String parseResult;

    @Column(name = "has_portrait")
    private Boolean hasPortrait;

//    @Column(name = "has_display")
//    private Boolean hasDisplay;

    @Column(name = "n_pages")
    private Integer nPages;

    @Column(name = "skills_text")
    private String skillsText;

    public Boolean getHasPortrait() {
        return hasPortrait != null ? hasPortrait : false;
    }

    public void setHasPortrait(Boolean hasPortrait) {
        this.hasPortrait = hasPortrait;
    }

}
