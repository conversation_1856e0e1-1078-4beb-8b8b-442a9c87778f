package com.altomni.apn.common.domain.enumeration.job;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobStatus enumeration.
 */
public enum JobStatus implements ConvertedEnum<Integer> {
    //Keep OPEN/ONHOLD/CLOSED/FILLED in SearchCategory with the same value
    O<PERSON><PERSON>(0, "00:OPEN"),
    ONHOLD(2, "20:ONHOLD"),
    CANCELLED(3, "40:CANCELLED"),
    CLOSED(4, "30:CLOSED"),
    FILLED(5, "10:FILLED"),
    EXPIRED(6, "50:EXPIRED"),
    IGNORED(7, "60:IGNORED"),
    OFFER_MADE(8, "70:OFFER_MADE"),
    LOST(9,"25:LOST"),
    NO_STATUS(100, "80:NO_STATUS"),
    NO_PUBLISHED(200, "90:NO_PUBLISHED");

    private final int dbValue;
    private final String name;

    JobStatus(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }
    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }

    // static resolving:
    public static final ReverseEnumResolver<JobStatus, Integer> resolver =
        new ReverseEnumResolver<>(JobStatus.class, JobStatus::toDbValue);

    public static JobStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
