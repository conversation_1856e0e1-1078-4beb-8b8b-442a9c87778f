package com.altomni.apn.common.domain.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;


@ApiModel
@Entity
@Data
@Table(name = "talent_additional_info")
@AllArgsConstructor
@NoArgsConstructor
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentAdditionalInfo extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "extended_info")
    private String extendedInfo;

    @Column(name = "local_extended_info")
    private String localExtendedInfo;

    public TalentAdditionalInfo(String extendedInfo) {
        this.extendedInfo = extendedInfo;
    }
}
