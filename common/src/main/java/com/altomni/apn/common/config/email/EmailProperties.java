package com.altomni.apn.common.config.email;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@Component
@RefreshScope
public class EmailProperties {

    @Value("${oauth2.username}")
    private String username;

    @Value("${oauth2.password}")
    private String password;

    @Value("${application.activeUserPeriod:3600}")
    private Integer activeUserPeriod;

}
