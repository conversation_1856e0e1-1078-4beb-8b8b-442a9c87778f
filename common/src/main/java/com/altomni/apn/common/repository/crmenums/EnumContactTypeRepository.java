package com.altomni.apn.common.repository.crmenums;

import com.altomni.apn.common.domain.crmenums.EnumContactType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;


@Repository
public interface EnumContactTypeRepository extends JpaRepository<EnumContactType, Integer>, QuerydslPredicateExecutor<EnumContactType> {

}
