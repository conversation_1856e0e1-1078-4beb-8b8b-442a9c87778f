package com.altomni.apn.common.dto.job;

import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class IpgJobSalaryDTO implements Serializable {

    @ApiModelProperty(value = "bill rate range")
    private RangeDTO billRange;

    @ApiModelProperty(value = "salary rate range")
    private RangeDTO salaryRange;

    @ApiModelProperty(value = "salary rate unit")
    private RateUnitType payType;

    @ApiModelProperty(value = "currency")
    private EnumRelationDTO currency;

}
