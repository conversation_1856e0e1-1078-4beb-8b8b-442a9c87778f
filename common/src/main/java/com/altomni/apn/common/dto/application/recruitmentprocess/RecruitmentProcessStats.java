package com.altomni.apn.common.dto.application.recruitmentprocess;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import lombok.Data;

import javax.persistence.Convert;

@Data
public class RecruitmentProcessStats {

    private Long jobId;

    private Integer count;

    @Convert(converter = NodeTypeConverter.class)
    private NodeType node;

}
