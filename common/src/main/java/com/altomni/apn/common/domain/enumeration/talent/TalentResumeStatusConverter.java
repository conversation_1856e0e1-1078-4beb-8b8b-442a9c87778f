package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TalentResumeStatusConverter extends AbstractAttributeConverter<TalentResumeStatus, Integer> {
    public TalentResumeStatusConverter() {
        super(TalentResumeStatus::toDbValue, TalentResumeStatus::fromDbValue);
    }
}
