package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.domain.enumeration.calendar.CalendarPriorityEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

@Data
public class CalendarEventDTO {
    private Long id;

    //0.日历,1.talentNote,2.job,3.company,4.offer,5.offerAccept, 6.commission 7.onboard, 8.interview , 9.系统自动创建interview日程 , 10.系统自动创建onboard日程
    //20.系统日程
    private Integer typeId;

    private Long tenantId;

    private Long referenceId;

    @NotBlank(message = "title is required")
    private String title;

    private String description;

    private CalendarTypeEnum calendarType;

    private CalendarPriorityEnum priority;

    private CalendarStatusEnum status;

    @NotNull(message = "startTime is required")
    private Instant startTime;

    @NotNull(message = "startTime is required")
    private Instant endTime;

    private Integer reminderMinutes;

    @NotNull(message = "goToId is required")
    private Long goToId;

    @NotEmpty(message = "attendees cannot be empty")
    private List<CalendarEventAttendeeDTO> attendees;

    private List<CalendarEventRelationDTO> relationList;

}
