package com.altomni.apn.common.domain.dict;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "talent_industry_relation")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentIndustryRelation  extends AbstractAuditingEntity implements Serializable, EnumRelation {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "industry_id")
    private Integer enumId;

    @Override
    public Integer getUniqueEnumId() {
        return enumId;
    }
}
