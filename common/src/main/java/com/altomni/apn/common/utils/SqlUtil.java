package com.altomni.apn.common.utils;

import cn.hutool.core.collection.CollUtil;
import lombok.experimental.UtilityClass;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * sqlUtil
 * <AUTHOR>
 */
@UtilityClass
public class SqlUtil {

    public static final Integer PARTITION_COUNT_999 = 999;

    public <T,K> List<T> findByIdInByPartition1000(Function<List<K>, List<T>> function, List<K> idList) {
        if (CollUtil.isEmpty(idList) || function == null) {
            return new ArrayList<>();
        }
        idList = idList.stream().distinct().collect(Collectors.toList());
        return CollUtil.split(idList, PARTITION_COUNT_999).stream().map(function).flatMap(List::stream).collect(Collectors.toList());
    }

    public static Integer checkInList(Map<Integer, Object> map) {
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new RuntimeException("query sql in condition list > 1000 more than 1");
        }
        return keyList.get(0);
    }

}
