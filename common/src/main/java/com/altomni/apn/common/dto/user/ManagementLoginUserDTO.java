package com.altomni.apn.common.dto.user;

import com.altomni.apn.common.domain.user.AdminManagementUser;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.utils.CommonUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

/**
 * A DTO representing a user, with only the public attributes.
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class ManagementLoginUserDTO {

    private Long id;

    private String uid;

    private String username;

    @ApiModelProperty(value = "user firstName")
    private String firstName;

    @ApiModelProperty(value = "user lastName")
    private String lastName;

    @ApiModelProperty(value = "user name")
    private String name;

    @ApiModelProperty(value = "email address. need to be unique.")
    private String email;

    private Boolean activated;

    private CredentialDTO credential;

    public static ManagementLoginUserDTO formatFromUser(AdminManagementUser user) {
        ManagementLoginUserDTO managementLoginUserDTO = new ManagementLoginUserDTO();
        BeanUtils.copyProperties(user, managementLoginUserDTO);
        managementLoginUserDTO.setName(CommonUtils.formatFullName(user.getFirstName(), user.getLastName()));
        return managementLoginUserDTO;
    }

}
