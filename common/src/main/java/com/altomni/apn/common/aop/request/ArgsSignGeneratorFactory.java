package com.altomni.apn.common.aop.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.util.AntPathMatcher;

import java.util.HashMap;
import java.util.Map;

public class ArgsSignGeneratorFactory {
    private final Map<PathPattern, ArgsSignGenerator> generatorMap = new HashMap<>();
    private final DefaultArgsSignGenerator defaultGenerator = new DefaultArgsSignGenerator();
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Data
    @AllArgsConstructor
    private static class PathPattern {
        private String method;
        private String pattern;
    }

    public ArgsSignGenerator getGenerator(String method, String actualPath) {
        // 移除API版本前缀，统一转换成模式路径

        for (Map.Entry<PathPattern, ArgsSignGenerator> entry : generatorMap.entrySet()) {
            PathPattern pattern = entry.getKey();
            if (pattern.getMethod().equalsIgnoreCase(method) &&
                    pathMatcher.match(pattern.getPattern(), actualPath)) {
                return entry.getValue();
            }
        }
        return defaultGenerator;
    }

    public void registerGenerator(String method, String pathPattern, ArgsSignGenerator generator) {
        generatorMap.put(new PathPattern(method, pathPattern), generator);
    }
}
