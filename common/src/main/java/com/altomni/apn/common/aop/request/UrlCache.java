package com.altomni.apn.common.aop.request;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * @author: <PERSON><PERSON>.<PERSON>
 * @date: 2019-01-17 12:03
 * @description:
 **/
@Configuration
public class UrlCache {
    @Bean
    public Cache<String, Integer> getCache() {
        return CacheBuilder.newBuilder().expireAfterWrite(1L, TimeUnit.SECONDS).build();// cache valid date : 2s
    }
}
