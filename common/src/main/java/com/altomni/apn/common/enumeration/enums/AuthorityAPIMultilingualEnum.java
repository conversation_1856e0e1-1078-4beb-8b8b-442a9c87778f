package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 authority-service
 * <AUTHOR>
 */
public enum AuthorityAPIMultilingualEnum {

    SOCIALAUTHORITY_BAD_CREDENTIAL("socialauthority_bad_credential"),

    LINKEDINLOGIN_GETUSERFAILED("linkedinLogin_getUserFailed"),
    ;

    private final String key;

    AuthorityAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}