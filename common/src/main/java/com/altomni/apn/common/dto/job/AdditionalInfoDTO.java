package com.altomni.apn.common.dto.job;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.FeeDTO;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.SkillDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class AdditionalInfoDTO implements Serializable {
    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<SkillDTO> requiredSkills;

    @ApiModelProperty(value = "one or more preferred skills ")
    private List<SkillDTO> preferredSkills;

    @ApiModelProperty(value = "range of experience year")
    private RangeDTO experienceYearRange;

    @ApiModelProperty(value = "bill rate range")
    private RangeDTO billRange;

    @ApiModelProperty(value = "salary rate range")
    private RangeDTO salaryRange;

    @ApiModelProperty(value = "salary rate unit")
    private RateUnitType payType;

    @ApiModelProperty(value = "The job of department .")
    private String department;

    private String logo;

    @ApiModelProperty(value = "job requirements")
    private String requirements;

    @ApiModelProperty(value = "job summary")
    private String summary;

    @ApiModelProperty(value = "job description")
    private String responsibilities;

    @ApiModelProperty(value = "Reason for Recruitment")
    private String reasonForRecruitment;

    @ApiModelProperty(value = "Team Composition")
    private String teamComposition;

    @ApiModelProperty(value = "Preferred Companies")
    private String preferredCompanies;

    @ApiModelProperty(value = "Preferred Industry")
    private Long preferredIndustry;

    @ApiModelProperty(value = "Suggestions for Prospecting")
    private String suggestionsForProspecting;

    @ApiModelProperty(value = "Recommended Approach")
    private String recommendedApproach;

    @ApiModelProperty(value = "Estimated Job Fee & Currency")
    private FeeDTO estimatedJobFee;

    @ApiModelProperty(value = "Fee Structure")
    private String feeStructure;

    @ApiModelProperty(value = "Contract Signing Party")
    private String contractSigningParty;


    public static AdditionalInfoDTO getNonRelationData(JobDTOV3 dto){
        if (dto == null) {
            return null;
        }

        AdditionalInfoDTO info = new AdditionalInfoDTO();
        info.setRequiredSkills(dto.getRequiredSkills());
        info.setPreferredSkills(dto.getPreferredSkills());
        info.setExperienceYearRange(dto.getExperienceYearRange());
        info.setBillRange(dto.getBillRange());
        info.setSalaryRange(dto.getSalaryRange());
        info.setPayType(dto.getPayType());
        info.setDepartment(dto.getDepartment());
        info.setLogo(dto.getLogo());
        info.setRequirements(dto.getRequirements());
        info.setSummary(dto.getSummary());
        info.setResponsibilities(dto.getResponsibilities());
//        info.setReasonForRecruitment(dto.getReasonForRecruitment());
//        info.setTeamComposition(dto.getTeamComposition());
//        info.setPreferredCompanies(dto.getPreferredCompanies());
//        info.setPreferredIndustry(dto.getPreferredIndustry());
//        info.setSuggestionsForProspecting(dto.getSuggestionsForProspecting());
//        info.setRecommendedApproach(dto.getRecommendedApproach());
//        info.setEstimatedJobFee(dto.getEstimatedJobFee());
//        info.setFeeStructure(dto.getFeeStructure());
//        info.setContractSigningParty(dto.getContractSigningParty());
        return info;

    }


    public static AdditionalInfoDTO getNonRelationData(AdditionalInfoDTO infoDTO){
        if (infoDTO == null) {
            return null;
        }

        AdditionalInfoDTO info = new AdditionalInfoDTO();
        info.setRequiredSkills(infoDTO.getRequiredSkills());
        info.setPreferredSkills(infoDTO.getPreferredSkills());
        info.setExperienceYearRange(infoDTO.getExperienceYearRange());
        info.setBillRange(infoDTO.getBillRange());
        info.setSalaryRange(infoDTO.getSalaryRange());
        info.setPayType(infoDTO.getPayType());
        info.setDepartment(infoDTO.getDepartment());
        info.setLogo(infoDTO.getLogo());
        info.setReasonForRecruitment(infoDTO.getReasonForRecruitment());
        info.setTeamComposition(infoDTO.getTeamComposition());
        info.setPreferredCompanies(infoDTO.getPreferredCompanies());
        info.setPreferredIndustry(infoDTO.getPreferredIndustry());
        info.setSuggestionsForProspecting(infoDTO.getSuggestionsForProspecting());
        info.setRecommendedApproach(infoDTO.getRecommendedApproach());
        info.setEstimatedJobFee(infoDTO.getEstimatedJobFee());
        info.setFeeStructure(infoDTO.getFeeStructure());
        info.setContractSigningParty(infoDTO.getContractSigningParty());
        return info;

    }



}
