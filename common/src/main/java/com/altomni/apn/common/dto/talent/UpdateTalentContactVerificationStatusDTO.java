package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

@Getter
@Setter
public class UpdateTalentContactVerificationStatusDTO {

    private Long talentId;
    private String contactInfo;
    private ContactType contactType;
    private TalentContactVerificationStatus verificationStatus;

}
