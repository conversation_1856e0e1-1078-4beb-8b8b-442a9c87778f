package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * invoicing body enum
 */
public enum InvoicingBody implements ConvertedEnum<Integer> {

    SH(0, "英特利普（上海）信息技术有限公司"),
    SZ(1, "英特利普（深圳）科技有限公司"),
    YT(2,"尹泰（上海）劳务派遣有限公司"),
    ;
    private final int dbValue;

    private final String description;

    InvoicingBody(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoicingBody, Integer> resolver = new ReverseEnumResolver<>(InvoicingBody.class, InvoicingBody::toDbValue);

    public static InvoicingBody fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
