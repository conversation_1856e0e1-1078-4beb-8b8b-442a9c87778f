package com.altomni.apn.common.domain.enumeration.jobdiva;

/**
 * The time sheet table order type.
 */
public enum TimeSheetTableOrderType {
//    WORK_DATE("  order by ending_date ","ending_date"),
    WORK_DATE("  order by week_end ","week_end"),
    WEEK_END("  order by week_end ","week_end"),
    HOURS(" order by total_hours ","total_hours"),
    REGULAR_HOURS(" order by regular_hours ","regular_hours"),
    OT(" order by over_time ","over_time"),
    DT(" order by double_time ","double_time"),
    AMOUNT(" order by amount" ,"amount"),
    SUBMITTED_DATE(" order by applied_date ","applied_date"),
    COMPANY_NAME(" order by CONVERT(company_name USING gbk) ","company_name"),
    JOB_TITLE(" order by CONVERT(job_title USING gbk) ","job_title"),
    MANAGER(" order by (CASE WHEN ( STATUS = 4 AND ( regular_hours = 0 OR regular_hours IS NULL ) AND am_approver IS NOT NULL ) THEN CONVERT ( am_approver USING gbk ) WHEN (status = 4 and ( regular_hours = 0 OR regular_hours IS NULL )) or status = 5 or status = 1 or status = 0 or status = 3 THEN CONVERT(primary_manager USING gbk) ELSE CASE WHEN status = 2 or status = 4 THEN CASE WHEN am_approver is not null THEN CONVERT(am_approver USING gbk) ELSE CASE WHEN manager is not null THEN CONVERT(manager USING gbk) ELSE CASE WHEN am is not null THEN CONVERT(am USING gbk) ELSE CONVERT(primary_manager USING gbk) END END END END END) ","manager"),
    MANAGER_CLIENT(" order by (CASE WHEN (STATUS = 4 AND ( regular_hours = 0 OR regular_hours IS NULL ) AND am IS NOT NULL) THEN CONVERT ( am USING gbk ) WHEN (status = 4 and ( regular_hours = 0 OR regular_hours IS NULL )) or status = 5 or status = 1 or status = 0 or status = 3 THEN CONVERT(primary_manager USING gbk) ELSE CASE WHEN status = 2 or status = 4 THEN CASE WHEN am is not null THEN CONVERT(am USING gbk) ELSE CASE WHEN manager is not null THEN CONVERT(manager USING gbk) ELSE CONVERT(primary_manager USING gbk) END END END END) ","manager"),
    MANAGER_EXPENSE_AM(" order by (CASE WHEN manager is not null THEN CONVERT(manager USING gbk) ELSE CASE WHEN am_approver is not null THEN CONVERT(am_approver USING gbk) ELSE CONVERT(primary_manager USING gbk) END END) ","manager"),
    MANAGER_EXPENSE_CLIENT(" order by (CASE WHEN manager is not null THEN CONVERT(manager USING gbk) ELSE CASE WHEN am is not null THEN CONVERT(am USING gbk) ELSE CONVERT(primary_manager USING gbk) END END) ","manager"),
    TALENT_NAME(" order by CONVERT(talent_name USING gbk) ","talent_name"),
    STATUS(" order by status  ","status"),
    JOB_ID(" order by job_id  ","job_id"),
    AM(" order by CONVERT(am USING gbk)   ","am"),
    ON(" order by approved_date  ","approved_date");

    private  String  orderSql;
    private String columnName;

    TimeSheetTableOrderType(String  orderSql,String columnName) {
        this. orderSql = orderSql;
        this.columnName = columnName;
    }

    public String getOrderSql() {
        return orderSql;
    }
}
