package com.altomni.apn.common.domain.enumeration.application;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The FeeChargeType enumeration.
 */
public enum FeeChargeType implements ConvertedEnum<Integer> {

    DEPOSIT(0),

    REFERRAL_BONUS(1);

    private final int dbValue;

    FeeChargeType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<FeeChargeType, Integer> resolver = new ReverseEnumResolver<>(FeeChargeType.class, FeeChargeType::toDbValue);

    public static FeeChargeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
