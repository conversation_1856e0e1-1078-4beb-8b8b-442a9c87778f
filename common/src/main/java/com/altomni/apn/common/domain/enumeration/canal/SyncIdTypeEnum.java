package com.altomni.apn.common.domain.enumeration.canal;

public enum SyncIdTypeEnum {

    JOB(0),
    TALENT(1),
    COMPANY(2),
    COMPANY_CLIENT_NOTE(3),
    COMPANY_PROGRESS_NOTE(4),
    TALENT_NOTE(5),
    USER(6),

    HR_TALENT(20),
    HR_COMPANY_CONTACT(21),
    HR_COMPANY(22),
    HR_JOB(23),

    AGENCY_JOB(30),
    AGENCY(31)

    ;

    private Integer code;

    SyncIdTypeEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

}
