package com.altomni.apn.common.interceptor;

import com.altomni.apn.common.utils.SecurityUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * pass token between microservices
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class FeignClientInterceptor implements RequestInterceptor {
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String APN_INTERNAL_PIN = "APN_INTERNAL_PIN";
    private static final String TOKEN_TYPE = "Bearer";

    @Value("${application.apnInternalPin:3hlo7PZn}")
    private String apnInternalPin;

    @Override
    public void apply(RequestTemplate requestTemplate) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        requestTemplate.header(APN_INTERNAL_PIN, apnInternalPin);
        log.debug("FeignClientInterceptor apply, authentication: {}", authentication);
        if (authentication == null || !authentication.isAuthenticated()) {
            return;
        }
        String token = SecurityUtils.getCurrentUserToken();
        log.debug("FeignClientInterceptor apply, token: {}", token);
        requestTemplate.header(AUTHORIZATION_HEADER, String.format("%s %s", TOKEN_TYPE, token));

    }
}