package com.altomni.apn.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.auth.LoginInformation;
import com.altomni.apn.common.auth.agency_auth.AgencyExternalAuthenticationToken;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.domain.user.AdminManagementUser;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.dto.user.AgencyLoginUserDTO;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetExternalAuthenticationToken;
import com.ipg.resourceserver.authentication.OAuth2ClientAuthenticationToken;
import com.ipg.resourceserver.authentication.SsoOAuth2AuthenticationToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.AbstractOAuth2Token;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthentication;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * Utility class for Spring Security.
 */
@Slf4j
public final class SecurityUtils {

    public static final String OPERATOR_UID = "operatorUid";

    private SecurityUtils() {
    }

    /**
     * Get the login of the apn current user.
     *
     * @return the login of the current user.
     */
//    public static Optional<LoginUserDTO> getCurrentUserLogin() {
//        SecurityContext securityContext = SecurityContextHolder.getContext();
//        Authentication authentication = securityContext.getAuthentication();
//        if (authentication == null || authentication.getPrincipal() == null) {
//            LoginUserDTO apnUser = new LoginUserDTO();
//            apnUser.setId(-1L);
//            apnUser.setTenantId(-1L);
//            return Optional.of(apnUser);
//        }
//        if (authentication.getPrincipal() instanceof LoginUserDTO loginUserDTO) {
//            return Optional.of(loginUserDTO);
//        } else {
//            return Optional.empty();
//        }
//    }

    public static Optional<LoginInformation> getCurrentUserLogin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication == null || authentication.getPrincipal() == null || !authentication.isAuthenticated()) {
//            throw new IllegalStateException("No authentication found in SecurityContext");
            LoginUserDTO apnUser = new LoginUserDTO();
            apnUser.setId(-1L);
            apnUser.setTenantId(-1L);
            return Optional.of(apnUser);
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof LoginUserDTO loginInformation) {
            return Optional.of(loginInformation);
        } else if(principal instanceof AgencyLoginUserDTO loginInformation) {
            return Optional.of(loginInformation);
        } else {
            return Optional.empty();
        }
    }

    public static Optional<LoginInformation> getCurrentLoginInformation() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication == null || authentication.getPrincipal() == null || !authentication.isAuthenticated()) {
//            throw new IllegalStateException("No authentication found in SecurityContext");
            LoginUserDTO apnUser = new LoginUserDTO();
            apnUser.setId(-1L);
            apnUser.setTenantId(-1L);
            return Optional.of(apnUser);
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof LoginUserDTO loginInformation) {
            return Optional.of(loginInformation);
        } else if(principal instanceof AgencyLoginUserDTO loginInformation) {
            return Optional.of(loginInformation);
        } else {
            return Optional.empty();
        }
    }


    public static Optional<AdminManagementUser> getCurrentAdminManagementUser() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication == null || authentication.getPrincipal() == null) {
            AdminManagementUser adminManagementUser = new AdminManagementUser();
            adminManagementUser.setId(-1L);
            adminManagementUser.setTenantId(-1L);
            return Optional.of(adminManagementUser);
        }
        if (authentication.getPrincipal() instanceof AdminManagementUser adminManagementUser) {
            return Optional.of(adminManagementUser);
        } else {
            return Optional.empty();
        }
    }

    public static Optional<TimeSheetUser> getCurrentTimesheetUser() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication == null || authentication.getPrincipal() == null) {
            TimeSheetUser timeSheetUser = new TimeSheetUser();
            timeSheetUser.setId(-1L);
            timeSheetUser.setTenantId(-1L);
            return Optional.of(timeSheetUser);
        }
        if (authentication.getPrincipal() instanceof TimeSheetUser timeSheetUser) {
            return Optional.of(timeSheetUser);
        } else {
            return Optional.empty();
        }

    }

    public static Optional<AgencyLoginUserDTO> getCurrentAgencyToCUser() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication == null || authentication.getPrincipal() == null) {
            AgencyLoginUserDTO agencyLoginUserDTO = new AgencyLoginUserDTO();
            agencyLoginUserDTO.setId(-1L);
            agencyLoginUserDTO.setTenantId(-1L);
            return Optional.of(agencyLoginUserDTO);
        }
        if (authentication.getPrincipal() instanceof AgencyLoginUserDTO agencyLoginUserDTO) {
            return Optional.of(agencyLoginUserDTO);
        } else {
            return Optional.empty();
        }

    }

    public static String getUserName() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof LoginUserDTO apnUser) {
            return apnUser.getUsername();
        }
        if (principal instanceof AdminManagementUser adminManagementUser) {
            return adminManagementUser.getUsername();
        }
        if (principal instanceof TimeSheetUser timeSheetUser) {
            return timeSheetUser.getUsername();
        }
        return null;
    }

    public static String getFullName() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof LoginUserDTO apnUser) {
            return CommonUtils.formatFullName(apnUser.getFirstName(), apnUser.getLastName());
        }
        if (principal instanceof AdminManagementUser adminManagementUser) {
            return CommonUtils.formatFullName(adminManagementUser.getFirstName(), adminManagementUser.getLastName());
        }
        if (principal instanceof TimeSheetUser timeSheetUser) {
            return CommonUtils.formatFullName(timeSheetUser.getFirstName(), timeSheetUser.getLastName());
        }
        return "";
    }

    public static String getUserUid() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof LoginUserDTO apnUser) {
            return apnUser.getUid();
        }
        if (principal instanceof AdminManagementUser adminManagementUser) {
            return adminManagementUser.getUid();
        }
        if (principal instanceof TimeSheetUser timeSheetUser) {
            return timeSheetUser.getUid();
        }
        if (principal instanceof AgencyLoginUserDTO agencyLoginUserDTO) {
            return agencyLoginUserDTO.getUid();
        }

        return null;
    }

    public static String getUserLoginIp() {
//        return getCurrentUserLogin().map(LoginUserDTO::getIp).orElse(null);
        return getCurrentUserLogin().map(LoginInformation::getIp).orElse(null);
    }

    public static String getUserTimeZone() {
//        return getCurrentUserLogin().map(LoginUserDTO::getTimezone).orElse(null);
        return getCurrentUserLogin().map(LoginInformation::getTimezone).orElse(null);
    }

    public static TenantUserTypeEnum getUserType() {
//        return getCurrentUserLogin().map(LoginUserDTO::getUserType).orElse(null);
        return getCurrentUserLogin().map(LoginInformation::getUserType).orElse(null);
    }

    public static Long getUserId() {
        String uid = getUserUid();
        if (StrUtil.isBlank(uid)) {
//            return getCurrentUserLogin().map(LoginUserDTO::getId)
//                    .or(() -> getCurrentAdminManagementUser().map(AdminManagementUser::getId))
//                    .or(() -> getCurrentTimesheetUser().map(TimeSheetUser::getId))
//                    .or(() -> getCurrentAgencyToCUser().map(AgencyLoginUserDTO::getId)).orElse(-1L);
            return getCurrentUserLogin().map(LoginInformation::getId).orElse(-1L);
        }
        if (uid.contains(StrUtil.COMMA)) {
            return Long.valueOf(uid.split(StrUtil.COMMA)[0]);
        } else {
            return Long.valueOf(uid.split(StrUtil.UNDERLINE)[0]);
        }
    }

    public static Long getTimeSheetUserId() {
        return Long.valueOf(getUserUid().split("_")[0]);
    }


    /**
     * Check if a user is authenticated.
     *
     * @return true if the user is authenticated, false otherwise.
     */
    public static boolean isAuthenticated() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && getAuthorities(authentication).noneMatch(AuthoritiesConstants.ANONYMOUS::equals);
    }

    /**
     * Checks if the current user has a specific authority.
     *
     * @param authority the authority to check.
     * @return true if the current user has the authority, false otherwise.
     */
    public static boolean hasCurrentUserThisAuthority(String authority) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && getAuthorities(authentication).anyMatch(authority::equals);
    }

    private static Stream<String> getAuthorities(Authentication authentication) {
        return authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority);
    }

    public static Long getTenantId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof LoginUserDTO apnUser) {
            return apnUser.getTenantId();
        }
        if (principal instanceof AdminManagementUser adminManagementUser) {
            return adminManagementUser.getTenantId();
        }
        if (principal instanceof TimeSheetUser timeSheetUser) {
            return timeSheetUser.getTenantId();
        }
        if (principal instanceof AgencyLoginUserDTO agencyLoginUserDTO) {
            return agencyLoginUserDTO.getTenantId();
        }
        return null;
    }

    public static Long getTeamId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof LoginUserDTO apnUser) {
            return apnUser.getTeamId();
        }
        if (principal instanceof AdminManagementUser adminManagementUser) {
            return adminManagementUser.getTeamId();
        }
        if (principal instanceof TimeSheetUser timeSheetUser) {
            return timeSheetUser.getTeamId();
        }
        return null;
    }

    public static Long getAgencyId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof AgencyLoginUserDTO apnUser) {
            return apnUser.getAgencyId();
        }
        return null;
    }

    public static String getEmail() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return null;
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof LoginUserDTO apnUser) {
            return apnUser.getEmail();
        }
        if (principal instanceof AdminManagementUser adminManagementUser) {
            return adminManagementUser.getEmail();
        }
        if (principal instanceof TimeSheetUser timeSheetUser) {
            return timeSheetUser.getEmail();
        }
        if (principal instanceof AgencyLoginUserDTO agencyLoginUserDTO) {
            return agencyLoginUserDTO.getEmail();
        }
        return null;
    }

    public static boolean hasLogin(long userId) {
        return userId != -1;
    }

    public static boolean hasPermission(Long tenantId) {
        return Objects.nonNull(tenantId) && tenantId.equals(SecurityUtils.getTenantId());
    }

    /**
     * If the current user has a specific authority (security role).
     * <p>
     * The name of this method comes from the isUserInRole() method in the Servlet API
     *
     * @param authority the authority to check
     * @return true if the current user has the authority, false otherwise
     */
    public static boolean isCurrentUserInRole(String authority) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication != null) {
            return authentication.getAuthorities().stream()
                    .anyMatch(grantedAuthority -> grantedAuthority.getAuthority().equals(authority));
        }
        return false;
    }

    public static boolean isCurrentTenant(Long tenantId) {
        return tenantId.equals(getTenantId());
    }

    public static Long getTenantIdFromCreatedBy(String createdBy) {
        if (createdBy.contains(StrUtil.COMMA)) {
            return Long.parseLong(createdBy.split(StrUtil.COMMA)[1]);
        } else if (createdBy.contains(StrUtil.UNDERLINE)) {
            return Long.parseLong(createdBy.split(StrUtil.UNDERLINE)[1]);
        } else {
            return null;
        }
    }

    public static boolean creatorBelongToCurrentTenant(String createdBy) {
        return SecurityUtils.getTenantIdFromCreatedBy(createdBy).equals(getTenantId());
    }

    /**
     * If the current user has a specific authority (security role).
     * <p>
     * The name of this method comes from the isUserInRole() method in the Servlet API
     *
     * @return true if the current user is admin role
     */
    public static boolean isAdmin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        // 客户端 token，拥有 admin 权限
        if (authentication instanceof OAuth2ClientAuthenticationToken) {
            return true;
        }
        if (authentication != null) {
            return authentication.getAuthorities().stream()
                    .anyMatch(grantedAuthority ->
                            //(grantedAuthority.getAuthority().equals(AuthoritiesConstants.ADMIN))||
                            (grantedAuthority.getAuthority().equals(AuthoritiesConstants.TENANT_ADMIN))
                    );
        }
        return false;
    }

    public static boolean isTimesheetAdmin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        // 客户端 token，拥有 admin 权限
        if (authentication instanceof OAuth2ClientAuthenticationToken) {
            return true;
        }
        if (authentication != null) {
            return authentication.getAuthorities().stream()
                    .anyMatch(grantedAuthority ->
                            (grantedAuthority.getAuthority().equals(AuthoritiesConstants.TIMESHEET_ADMIN))
                    );
        }
        return false;
    }

    public static boolean isAm() {
        return hasRoles(List.of(AuthoritiesConstants.ACCOUNT_MANAGER));
    }

    private static boolean hasRoles(List<String> roles) {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (ObjectUtil.isNotNull(authentication)) {
            return authentication.getAuthorities().stream()
                    .anyMatch(grantedAuthority -> roles.stream()
                            .anyMatch(role -> grantedAuthority.getAuthority().equals(role)));
        }
        return false;
    }

    public static boolean isSystemAdmin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        // 客户端 token，拥有 admin 权限
        if (authentication instanceof OAuth2ClientAuthenticationToken) {
            return true;
        }
        if (authentication != null) {
            return authentication.getAuthorities().stream()
                    .anyMatch(grantedAuthority ->
                            (grantedAuthority.getAuthority().equals(AuthoritiesConstants.SUPER_ADMIN))
                                    || (grantedAuthority.getAuthority().equals(AuthoritiesConstants.PLATFORM_ADMIN))
                    );
        }
        return false;
    }

    /**
     * get user id from create_by
     *
     * @param createdBy
     * @return
     */
    public static Long getUserIdFromCreatedBy(String createdBy) {
        if (createdBy.contains(StrUtil.COMMA)) {
            return Long.parseLong(createdBy.split(StrUtil.COMMA)[0]);
        } else if (createdBy.contains(StrUtil.UNDERLINE)) {
            return Long.parseLong(createdBy.split(StrUtil.UNDERLINE)[0]);
        } else {
            return null;
        }
    }

    public static boolean isCreatedByCurrentUser(String createdBy) {
        return SecurityUtils.getUserIdFromCreatedBy(createdBy).equals(getUserId());
    }

    /**
     * *******ONLY FOR REPORT!*******
     * If the current user has a specific authority (security role).
     * <p>
     * The name of this method comes from the isUserInRole() method in the Servlet API
     *
     * @return true if the current user is admin role
     */
    public static boolean isSuperUser() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        // 客户端 token，拥有 admin 权限
        if (authentication instanceof OAuth2ClientAuthenticationToken) {
            return true;
        }
        if (authentication != null) {
            return authentication.getAuthorities().stream()
                    .anyMatch(grantedAuthority ->
                            (grantedAuthority.getAuthority().equals(AuthoritiesConstants.PRIVILEGE_REPORT))
                                    || (grantedAuthority.getAuthority().equals(AuthoritiesConstants.TENANT_ADMIN))
                    );
        }
        return false;
    }

    /**
     * *******ONLY FOR REPORT!*******
     * If the current user has a specific authority (security role).
     * <p>
     * The name of this method comes from the isUserInRole() method in the Servlet API
     *
     * @return true if the current user is admin role
     */
    public static boolean isTeamLeader() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        if (authentication != null) {
            return authentication.getAuthorities().stream()
                    .anyMatch(grantedAuthority ->
                            (grantedAuthority.getAuthority().equals(AuthoritiesConstants.PRIMARY_RECRUITER))
                    );
        }
        return false;
    }

    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    public static void setAuthentication(Authentication authentication) {
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }


    public static boolean isCompanyAm(Long companyId) {
//        LoginUserDTO loginUserDTO = getCurrentUserLogin().orElse(null);
        LoginInformation loginInformation = getCurrentUserLogin().orElse(null);
        if (loginInformation == null) {
            return false;
        }
        if (loginInformation instanceof LoginUserDTO loginUserDTO) {
            if (CollUtil.isEmpty(loginUserDTO.getCompanyIdsWithAm())) {
                return false;
            }
            return loginUserDTO.getCompanyIdsWithAm().stream().anyMatch(companyIdWithAm -> Objects.equals(companyId, companyIdWithAm));
        } else {
            return false;
        }
    }

    public static String getCurrentUserToken() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication instanceof BearerTokenAuthentication bearerTokenAuthentication) {
            return Optional.ofNullable(bearerTokenAuthentication.getToken()).map(AbstractOAuth2Token::getTokenValue).orElse(null);
        }
        if (authentication instanceof SsoOAuth2AuthenticationToken ssoOAuth2AuthenticationToken) {
            return Optional.ofNullable(ssoOAuth2AuthenticationToken.getAccessToken()).map(AbstractOAuth2Token::getTokenValue).orElse(null);
        }
        if (authentication instanceof OAuth2ClientAuthenticationToken oAuth2ClientAuthenticationToken) {
            return Optional.ofNullable((OAuth2AccessToken) oAuth2ClientAuthenticationToken.getCredentials()).map(AbstractOAuth2Token::getTokenValue).orElse(null);
        }
        if (authentication instanceof TimesheetExternalAuthenticationToken timesheetExternalAuthenticationToken) {
            return Optional.ofNullable(timesheetExternalAuthenticationToken.getCredentials()).orElse(null);
        }
        if (authentication instanceof AgencyExternalAuthenticationToken agencyExternalAuthenticationToken) {
            return Optional.ofNullable(agencyExternalAuthenticationToken.getCredentials()).orElse(null);
        }
        return null;
    }

    public static Instant getTokenIssueTime() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        // 方法1
        if (authentication instanceof BearerTokenAuthentication bearerTokenAuthentication) {
            return bearerTokenAuthentication.getToken().getIssuedAt();
        }
        // 方法2
        Object principal = authentication.getPrincipal();
        if (principal instanceof LoginUserDTO apnUser) {
            return apnUser.getOidcUser().getIssuedAt();
        }
        return null;
    }
}
