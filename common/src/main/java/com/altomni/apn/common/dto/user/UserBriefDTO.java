package com.altomni.apn.common.dto.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.utils.CommonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO representing a user, with his authorities.
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserBriefDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String username;

    private String firstName;

    private String lastName;

    private String fullName;

    private String email;

    private String phone;

    private boolean activated;

    private Long crmUserId;

    private Long tenantId;

    private Instant lastPushTime;

    private Integer jobPermission;

    private String customTimezone;

    public UserBriefDTO(Long id, String username, String firstName, String lastName, String email, String phone, boolean activated, Long tenantId, Long crmUserId, String customTimezone) {
        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.fullName = this.firstName + " " + this.lastName;
        this.email = email;
        this.phone = phone;
        this.activated = activated;
        this.crmUserId = crmUserId;
        this.tenantId = tenantId;
        this.customTimezone = customTimezone;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFullName() {
        return Objects.requireNonNullElseGet(fullName, () -> CommonUtils.formatFullName(firstName, lastName));
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public boolean isActivated() {
        return activated;
    }

    public void setActivated(boolean activated) {
        this.activated = activated;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getJobPermission() {
        return jobPermission;
    }

    public void setJobPermission(Integer jobPermission) {
        this.jobPermission = jobPermission;
    }


    public Long getCrmUserId() {
        return crmUserId;
    }

    public void setCrmUserId(Long crmUserId) {
        this.crmUserId = crmUserId;
    }

    public String getCustomTimezone() { return customTimezone; }

    public void setCustomTimezone(String customTimezone) { this.customTimezone = customTimezone; }

    public UserBriefDTO() {
        // Empty constructor needed for Jackson.
    }

    public UserBriefDTO(User user) {
        this(user.getId(), user.getUsername(), user.getFirstName(), user.getLastName(), user.getEmail(), user.isActivated());
    }

    public UserBriefDTO(Long id, String username, String firstName, String lastName, String email, boolean activated) {
        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.activated = activated;
    }

    public UserBriefDTO(Long id, String username, String firstName, String lastName, String email, Long tenantId, boolean activated) {
        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.tenantId = tenantId;
        this.activated = activated;
    }


    public UserBriefDTO(Long id, String username, String firstName, String lastName, String email, Long tenantId, boolean activated, Instant lastPushTime) {
        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.tenantId = tenantId;
        this.activated = activated;
        this.lastPushTime = lastPushTime;
    }

    public UserBriefDTO(Integer jobPermission, String firstName, String lastName) {
        this.jobPermission = jobPermission;
        this.firstName = firstName;
        this.lastName = lastName;
    }

    public UserBriefDTO(String firstName, String lastName, String email, Long tenantId) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.tenantId = tenantId;
    }

    public UserBriefDTO(Long id, String firstName, String lastName) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
    }
}
