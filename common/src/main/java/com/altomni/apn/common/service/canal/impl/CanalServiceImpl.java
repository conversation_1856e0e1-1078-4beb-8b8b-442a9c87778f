package com.altomni.apn.common.service.canal.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.canal.CanalSyncRecord;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.repository.canal.CanalSyncRecordRepository;
import com.altomni.apn.common.service.canal.CanalService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service("canalService")
public class CanalServiceImpl implements CanalService {

    @Resource
    private CanalSyncRecordRepository canalSyncRecordRepository;

    @Override
    public void insertAll(Collection<Long> taskIdList, SyncIdTypeEnum type, FailReasonEnum failReason, String message, Integer priority) {
        if (CollUtil.isEmpty(taskIdList)) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordExistsList = canalSyncRecordRepository.findAllByTypeAndTaskIdIn(type, taskIdList);
        Map<Long, CanalSyncRecord> map = canalSyncRecordExistsList.stream().collect(Collectors.toMap(CanalSyncRecord::getTaskId, a -> a, (a1, a2) -> a2));
        List<CanalSyncRecord> canalSyncRecordList = taskIdList.stream().distinct().map(taskId -> {
            CanalSyncRecord canalSyncRecord = new CanalSyncRecord();
            if (map.containsKey(taskId)) {
                canalSyncRecord.setId(map.get(taskId).getId());
                canalSyncRecord.setRetryCount(map.get(taskId).getRetryCount() + 1);
            } else {
                canalSyncRecord.setRetryCount(0);
            }
            canalSyncRecord.setTaskId(taskId);
            canalSyncRecord.setType(type);
            canalSyncRecord.setReason(failReason);
            canalSyncRecord.setCreatedDate(Instant.now());
            canalSyncRecord.setErrorMessage(message);
            canalSyncRecord.setPriority(priority);
            return canalSyncRecord;
        }).collect(Collectors.toList());
        canalSyncRecordRepository.saveAll(canalSyncRecordList);
    }

    @Override
    public List<CanalSyncRecord> findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum type, Integer limit, Integer failedCountLimit) {
        return canalSyncRecordRepository.findAllByTypeOrderByCreatedDateAsc(type.getCode(), limit, failedCountLimit);
    }

    @Override
    public void deleteByTaskIdAndType(Long taskId, SyncIdTypeEnum type) {
        canalSyncRecordRepository.deleteByTypeAndTaskId(taskId, type.getCode());
    }


}
