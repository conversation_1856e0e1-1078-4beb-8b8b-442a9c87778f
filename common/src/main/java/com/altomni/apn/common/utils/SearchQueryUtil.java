package com.altomni.apn.common.utils;

import com.altomni.apn.common.dto.job.HitalentJobSearchParam;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.Operator;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.ScoreSortBuilder;
import org.elasticsearch.search.sort.SortOrder;

public class SearchQueryUtil {

    private static SearchSourceBuilder searchSourceBuilder;

    private SearchQueryUtil() { }

    public static SearchSourceBuilder buildJobSearchQuery(HitalentJobSearchParam searchParams) {
        searchSourceBuilder = new SearchSourceBuilder();
        int page = searchParams.getPage();
        int size = searchParams.getSize();
        //set search request start
        searchSourceBuilder.from(page * size);
        //set search request size
        searchSourceBuilder.size(size);
        searchSourceBuilder.sort(new FieldSortBuilder("postingDate").order(SortOrder.DESC));
        searchSourceBuilder.sort(new ScoreSortBuilder().order(SortOrder.DESC));
        searchSourceBuilder.sort(new FieldSortBuilder("id").order(SortOrder.DESC));

        //add search parameters to query
        BoolQueryBuilder mainBuilder = QueryBuilders.boolQuery();
        //String stringQuery = trimQuery(searchParams.getKeywords());

        BoolQueryBuilder subBuilder = QueryBuilders.boolQuery();
        subBuilder.should().add(QueryBuilders.matchQuery("title", searchParams.getKeywords()).fuzziness(Fuzziness.AUTO).operator(Operator.OR));
        subBuilder.should().add(QueryBuilders.matchQuery("company", searchParams.getKeywords()).fuzziness(Fuzziness.AUTO).operator(Operator.OR));
        mainBuilder.must().add(subBuilder);

        subBuilder = QueryBuilders.boolQuery();
        subBuilder.should().add(QueryBuilders.matchQuery("city", searchParams.getLocation()).fuzziness(Fuzziness.AUTO).operator(Operator.OR));
        subBuilder.should().add(QueryBuilders.matchQuery("province", searchParams.getLocation()).fuzziness(Fuzziness.AUTO).operator(Operator.OR));
        subBuilder.should().add(QueryBuilders.matchQuery("country", searchParams.getLocation()).fuzziness(Fuzziness.AUTO).operator(Operator.OR));
        mainBuilder.must().add(subBuilder);

        //queryBuilder = queryBuilder.must(QueryBuilders.matchQuery("company", searchParams.getKeywords()).fuzziness(Fuzziness.AUTO).operator(Operator.OR));
        //queryBuilder = queryBuilder.must(QueryBuilders.queryStringQuery(stringQuery).field("keywords").defaultOperator(Operator.OR));
        //queryBuilder = queryBuilder.must(QueryBuilders.matchPhraseQuery("city", searchParams.getLocation()));
        searchSourceBuilder.query(mainBuilder);

        return searchSourceBuilder;
    }

    private static String trimQuery(String query) {
        if (query == null || query.length() == 0) {
            return query;
        }
        char[] chars = query.toCharArray();
        int fast = 0;
        int symbolCount = 0;
        while (fast < chars.length) {
            if (chars[fast] == '+' || chars[fast] == '/' || chars[fast] == '-') {
                symbolCount++;
                char curr = chars[fast];
                if (fast + 1 < chars.length && chars[fast + 1] != curr) {
                    if (symbolCount == 1) {
                        chars[fast] = ' ';
                    }
                    symbolCount = 0;
                }
            } else {
                symbolCount = 0;
            }
            fast++;
        }
        fast = 0;
        int slow = 0;
        while (fast < chars.length) {
            if (chars[fast] == ' ') {
                while (fast < chars.length && chars[fast] == ' ') {
                    fast++;
                }
                chars[slow++] = ' ';
            } else {
                chars[slow++] = chars[fast++];
            }
        }
        if (slow < chars.length && chars[slow] == ' ') {
            slow--;
        }
        return new String(chars, 0, slow);
    }
}
