package com.altomni.apn.common.dto.application.dashboard;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class MyCandidateStatusFilterConverter extends AbstractAttributeConverter<MyCandidateStatusFilter, Integer> {
    public MyCandidateStatusFilterConverter() {
        super(MyCandidateStatusFilter::toDbValue, MyCandidateStatusFilter::fromDbValue);
    }
}
