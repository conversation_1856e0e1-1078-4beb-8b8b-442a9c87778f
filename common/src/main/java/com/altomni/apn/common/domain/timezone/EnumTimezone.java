package com.altomni.apn.common.domain.timezone;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "enum_timezone")
@AllArgsConstructor
@NoArgsConstructor
public class EnumTimezone {

    @Id
    private Integer id;

    private String timezone;

    /**
     * 标准时间偏移量
     */
    private String standardOffset;

    /**
     * 夏令时偏移量
     */
    private String daylightOffset;

    private String suffix;

    private Integer parent;

}
