package com.altomni.apn.common.domain.enumeration.application;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The NodeType enumeration.
 */
public enum NodePageSection implements ConvertedEnum<Integer> {

    /**
     * Public version
     * Base page
     */
    DEFAULT_BASE_PAGE_SUBMIT_TO_JOB(1),

    DEFAULT_BASE_PAGE_SUBMIT_TO_CLIENT(2),

    DEFAULT_BASE_PAGE_INTERVIEW(3),

    DEFAULT_BASE_PAGE_OFFER(4),

    DEFAULT_BASE_PAGE_COMMISSION(6),

    DEFAULT_BASE_PAGE_ON_BOARD(7),

    /**
     * Common modules, can be reused by multiple pages
     */
    DEFAULT_KPI_USERS(100),

    DEFAULT_SALARY(102),

    DEFAULT_ALL_PACKAGE(103),

    DEFAULT_FEE_CHARGE(104),

    DEFAULT_COMMISSION_USER(105),

    DEFAULT_NOTE_OPTIONAL(106),

    DEFAULT_BASE_INFO_COMPANY_JOB_TALENT(107),

    DEFAULT_UPDATE_JOB_STATUS_AND_TALENT_EXPERIENCE(108),

    DEFAULT_SEND_EMAIL_TO_CLIENT_CONTACT(109),

    DEFAULT_SEND_EMAIL_TO_TALENT(110),

    DEFAULT_ADD_TO_CALENDAR(111),

    DEFAULT_GENERATE_STATEMENT(112),

    /**
     * IPG customize features
     * Common modules, can be reused by multiple pages
     */
    IPG_KPI_USER_WITH_USER_ROLE(410),

    IPG_NOTE_REQUIRED(411),

    IPG_OFFER_INFO(412),

    IPG_GENERATE_STATEMENT(413),

    IPG_KPI_USER_WITH_USER_ROLE_AND_PERCENTAGE(414),

    IPG_KPI_USER_WITH_USER_ROLE_AND_PERCENTAGE_DISABLED(415),

    IPG_CLIENT_INFO(416),

    IPG_SKILLS(417),

    IPG_RECOMMEND_COMMENTS(418),

    IPG_FTE_ALL_PACKAGE(420),

    IPG_FTE_FEE_CHARGE(421),


    IPG_CONTRACT_AGREED_PAY_RATE(430),

    IPG_CONTRACT_FEE_CHARGE(431);

    private final Integer dbValue;

    NodePageSection(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<NodePageSection, Integer> resolver =
        new ReverseEnumResolver<>(NodePageSection.class, NodePageSection::toDbValue);

    public static NodePageSection fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
