package com.altomni.apn.common.domain.enumeration.calendar;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CalendarRelationEnumConverter extends AbstractAttributeConverter<CalendarRelationEnum, Integer> {
    public CalendarRelationEnumConverter() {
        super(CalendarRelationEnum::toDbValue, CalendarRelationEnum::fromDbValue);
    }
}
