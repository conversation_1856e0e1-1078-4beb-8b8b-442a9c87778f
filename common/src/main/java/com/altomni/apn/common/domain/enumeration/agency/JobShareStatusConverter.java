package com.altomni.apn.common.domain.enumeration.agency;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;
import javax.persistence.Converter;

@Converter
public class JobShareStatusConverter extends AbstractAttributeConverter<JobShareStatus, Integer> {
    public JobShareStatusConverter() {
        super(JobShareStatus::toDbValue, JobShareStatus::fromDbValue);
    }
}
