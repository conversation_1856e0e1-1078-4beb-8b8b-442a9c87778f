package com.altomni.apn.common.vo.recruiting.v2;

import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonCountVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiNoteCountVO extends RecruitingKpiCommonCountVO {
    // 基础note指标
    private Long callNoteNum;
    private Long personNoteNum;
    private Long otherNoteNum;
    private Long emailNoteNum;
    private Long videoNoteNum;
    private Long iciNum;
    private Long noteCount;
    
    // 人才相关指标
    private String uniqueTalentIds;
    
    // 应用笔记指标
    private Long applicationNoteCountNum;
    
    // 人才跟踪笔记指标
    private Long talentTrackingNoteCountNum;
    private String talentTrackingNoteIds;
} 