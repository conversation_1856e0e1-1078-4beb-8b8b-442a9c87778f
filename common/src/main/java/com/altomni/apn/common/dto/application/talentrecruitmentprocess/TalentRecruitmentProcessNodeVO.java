package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessNodeVO implements Serializable {

    private static final long serialVersionUID = -6069022257970557790L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private Long nodeId;

    private NodeType nodeType;

    private NodeStatus nodeStatus;

    private Long nextNodeId;


}
