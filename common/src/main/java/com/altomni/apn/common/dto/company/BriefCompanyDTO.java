package com.altomni.apn.common.dto.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@Data
@ApiModel(description = "detail for employer type company")
public class BriefCompanyDTO implements Serializable {

    @ApiModelProperty(value = "The id on current company")
    private Long id;

    @ApiModelProperty(value = "The name for company.")
    @NotEmpty
    private String fullBusinessName;

    @ApiModelProperty(value = "The logo for company.")
    private String logo;

    @ApiModelProperty(value = "The id of tenant")
    private Long tenantId;

    private Long permissionUserId;

    private Long permissionTeamId;
}
