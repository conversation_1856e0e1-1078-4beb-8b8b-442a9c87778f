package com.altomni.apn.common.domain.enumeration.company;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.ArrayList;
import java.util.List;

/**
 * The IndustryType enumeration.
 */
public enum IndustryType implements ConvertedEnum<Integer> {
    IT_SOFTWARE(1),
    ELETRONICS_AND_HARDWARE(2),
    BANKING_AND_FINANCE_SERVICES(3),
    REAL_ESTATE(4),
    MEDICAL_HEALTH_CARE(5),
    PROFESSIONAL_SERVICES(6),
    INTERNET(7),
    MANUFACTURING_AND_INDUSTRIAL(8),
    AI(9),
    AUTOMOTIVE(10),
    EDUCATION(11),
    ROBOTICS(12),
    RETAIL(13),
    LOGISTICS_AND_SHIPPING(14),
    ENERGY(15),
    GOVERNMENT_AND_NON_PROFIT(16),
    STAFFING_AND_RECRUITMENT(17),
    CONSUMER_GOODS(18),
    AGRICULTURE(19),
    COMPUTER_GAMING_AND_E_SPORTS(20),
    ENTERTAINMENT(21),
    HOSPITALITY_AND_TRAVEL(22),
    REAL_ESTATE_AND_CONSTRUCTION(23),
    ADVERTISING_AND_MEDIA(24),
    OTHER(99999),
    NODATA(0);

    private final int dbValue;

    IndustryType(int dbValue) {
        this.dbValue = dbValue;
    }

    public static List<String> translate(IndustryType industryType) {
        List<String> industiesList = new ArrayList<>();
        switch (industryType) {
            case IT_SOFTWARE :
                industiesList.add("SOFTWARE");
                break;
            case ELETRONICS_AND_HARDWARE :
                industiesList.add("MANUFACTURING.ELECTRONICS");
                break;
            case BANKING_AND_FINANCE_SERVICES :
                industiesList.add("FINANCE");
                break;
            case REAL_ESTATE :
                industiesList.add("REAL_ESTATE");
                break;
            case MEDICAL_HEALTH_CARE :
                industiesList.add("HEALTHCARE_SERVICES");
                break;
            case PROFESSIONAL_SERVICES :
                industiesList.add("BUSINESS_SERVICES");
                industiesList.add("EDUCATION.TRAINING");
                break;
            case INTERNET :
                industiesList.add("TELECOMMUNICATIONS.INTERNET_SERVICE_PROVIDERS_OR_WEBSITE_HOSTING_AND_INTERNET_RELATED_SERVICES");
                break;
            case MANUFACTURING_AND_INDUSTRIAL :
                industiesList.add("MANUFACTURING");
                break;
            case AI :
                industiesList.add("SOFTWARE.ARTIFICIAL_INTELLIGENCE");
                break;
            case AUTOMOTIVE :
                industiesList.add("SOFTWARE.AUTONOMOUS_DRIVING");
                break;
            case EDUCATION :
                industiesList.add("EDUCATION");
                break;
            case ROBOTICS :
                industiesList.add("SOFTWARE.ROBOTICS");
                break;
            case RETAIL :
                industiesList.add("RETAIL");
                break;
            case LOGISTICS_AND_SHIPPING :
                industiesList.add("TRANSPORTATION.FREIGHT_AND_LOGISTICS_SERVICES");
                break;
            case ENERGY :
                industiesList.add("ENERGY_OR_UTILITIES_AND_WASTE");
                break;
            case GOVERNMENT_AND_NON_PROFIT :
                industiesList.add("GOVERNMENT");
                industiesList.add("ORGANIZATIONS.NON_PROFIT_AND_CHARITABLE_ORGANIZATIONS");

                break;
            case STAFFING_AND_RECRUITMENT :
                industiesList.add("BUSINESS_SERVICES.HR_AND_STAFFING");
                break;
            case CONSUMER_GOODS :
                industiesList.add("RETAIL");
                break;
            case AGRICULTURE :
                industiesList.add("AGRICULTURE");
                break;
            case COMPUTER_GAMING_AND_E_SPORTS :
                industiesList.add("GAMES.ONLINE");
                break;
            case ENTERTAINMENT :
                industiesList.add("HOSPITALITY");
                break;
            case HOSPITALITY_AND_TRAVEL :
                industiesList.add("HOSPITALITY");
                break;
            case REAL_ESTATE_AND_CONSTRUCTION :
                industiesList.add("CONSTRUCTION");
                break;
            case ADVERTISING_AND_MEDIA :
                industiesList.add("BUSINESS_SERVICES.ADVERTISING_AND_MARKETING");
                break;
            case OTHER :
//                industiesList.add("BLOCK_CHAIN");
//                industiesList.add("ORGANIZATIONS");
//                industiesList.add("MEDIA");
//                industiesList.add("RETAIL");
//                industiesList.add("CONSTRUCTION");
//                industiesList.add("LAW_FIRMS_AND_LEGAL_SERVICES");
//                industiesList.add("ENERGY_OR_UTILITIES_AND_WASTE");
//                industiesList.add("HOSPITALITY");
//                industiesList.add("INSURANCE");
//                industiesList.add("GOVERNMENT");
//                industiesList.add("AGRICULTURE");
//                industiesList.add("MANUFACTURING.MECHANICAL_OR_INDUSTRIAL_ENGINEERING");
//                industiesList.add("MANUFACTURING.VEHICLE_MANUFACTURING");
//                industiesList.add("MANUFACTURING.CHEMICALS_AND_RELATED_PRODUCTS");
//                industiesList.add("MANUFACTURING.PHOTOGRAPHIC_AND_OPTICAL_EQUIPMENT");
//                industiesList.add("MANUFACTURING.CHEMICALS_AND_RELATED_PRODUCTS");
//                industiesList.add("RETAIL.E-COMMERCE");
//                industiesList.add("GAMES");
//                industiesList.add("TELECOMMUNICATIONS");
//                industiesList.add("MANUFACTURING.TELECOMMUNICATION_EQUIPMENT");
//                industiesList.add("MANUFACTURING.TEST_AND_MEASUREMENT_EQUIPMENT");
                break;
        }
        return industiesList;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<IndustryType, Integer> resolver = new ReverseEnumResolver<>(IndustryType.class, IndustryType::toDbValue);

    public static IndustryType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
