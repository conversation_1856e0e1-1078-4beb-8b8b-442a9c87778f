package com.altomni.apn.common.vo.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * A VO representing a brief talent.
 */
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TalentBriefVO implements Serializable {

    private Long id;

    private String firstName;

    private String lastName;

    private String fullName;

}
