package com.altomni.apn.common.auth.agency_auth;

import com.altomni.apn.common.dto.user.AgencyLoginUserDTO;
import com.altomni.apn.common.dto.user.TokenAndAgencyLoginUserDTO;
import com.nimbusds.jose.JOSEException;

import java.text.ParseException;

public interface AgencyUserTokenStore {

//    AgencyLoginUserDTO getUser(String token) throws JOSEException, ParseException;

    TokenAndAgencyLoginUserDTO getUser(String token) throws JOSEException, ParseException;

}
