package com.altomni.apn.common.domain.enumeration.user;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.ArrayList;
import java.util.List;

/**
 * The UserRole enumeration.
 */
public enum UserRole implements ConvertedEnum<Integer>{

    /**
     * Account Manager
     */
    AM(0),

    /**
     * Recruiter
     */
    RECRUITER(1),

    /**
     * Sourcer
     */
    SOURCER(2),

    /**
     * Delivery Manager
     */
    DM(3),

    /**
     * Talent Ownership
     */
    OWNER(4),

    /**
     * Account Coordinator
     */
    AC(5),

    /**
     * Primary Recruiter
     */
    PR(6),

    /**
     * Cooperate Account Manager
     */
    CO_AM(7),

    /**
     * Business Develop Owner
     */
    BD_OWNER(8),

    /**
     * Sales Lead Owner
     */
    SALES_LEAD_OWNER(9),

    /**
     * 节点创建者，用于talent_recruitment_process_user_relation表
     *
     */
    NODE_CREATED_BY(10);

    private final Integer dbValue;

    public static final List<Integer> ALL_USER_ROLES = new ArrayList<>();

    static {
        for (UserRole userRole: UserRole.values()) {
            ALL_USER_ROLES.add(userRole.toDbValue());
        }
    }

    UserRole(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<UserRole, Integer> resolver =
        new ReverseEnumResolver<>(UserRole.class, UserRole::toDbValue);

    public static UserRole fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
