package com.altomni.apn.common.vo.settings;

import com.altomni.apn.common.domain.config.SettingTrackRecordType;
import lombok.Data;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SettingTrackRecordViewVO {

    private BigInteger id;

    private String tempName;

    private SettingTrackRecordType tempType;

    private String tempContent ;

    private String tempRemark ;

    private List<BigInteger> userId ;

    private List<String> userName ;

    private Integer status ;

    private Long createUserId;
}
