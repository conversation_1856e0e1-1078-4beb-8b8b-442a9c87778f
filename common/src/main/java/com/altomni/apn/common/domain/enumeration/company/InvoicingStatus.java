package com.altomni.apn.common.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * invoicing status enum 待审批、审批驳回、待开票、已开票、未回款、部分回款、逾期、全部回款、已作废
 */
public enum InvoicingStatus implements ConvertedEnum<Integer> {

    PENDING_APPROVAL(0, "Pending approval"),
    REJECTED_APPROVAL(1, "Rejected approval"),
    PENDING_INVOICING(2, "Pending invoicing"),
    INVOICED(3, "Invoiced"),
    UNCOLLECTED(4, "Uncollected"),
    PARTIALLY_COLLECTED(5, "Partially collected"),
    OVERDUE(6, "Overdue"),
    FULLY_COLLECTED(7, "Fully collected"),
    VOIDED(8, "Voided"),
    OUTSTANDING_CLOSE(9, "Outstanding Close"),
    ;
    private final int dbValue;

    private final String description;

    InvoicingStatus(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<InvoicingStatus, Integer> resolver = new ReverseEnumResolver<>(InvoicingStatus.class, InvoicingStatus::toDbValue);

    public static InvoicingStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static String getNameFromDbValue(Integer dbValue) {
        if (null == resolver.get(dbValue)) {
            return null;
        }
        return resolver.get(dbValue).name();
    }
}
