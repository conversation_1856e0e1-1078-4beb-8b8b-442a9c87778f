package com.altomni.apn.common.dto.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AssignedUserDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "The user role")
    private String permission;

    @ApiModelProperty(value = "user id ")
    private Long userId;

    @ApiModelProperty(value = "user name of user")
    private String username;

    @ApiModelProperty(value = "first name of user")
    private String firstName;

    @ApiModelProperty(value = "last name of user")
    private String lastName;

    @ApiModelProperty(value = "email of user")
    private String email;

    /**   no meaning only for web  */
    private Boolean activated;

    private Long id;

    private String label;

    private String value;


}
