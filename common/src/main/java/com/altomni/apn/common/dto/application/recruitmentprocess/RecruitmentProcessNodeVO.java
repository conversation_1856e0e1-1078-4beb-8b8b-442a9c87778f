package com.altomni.apn.common.dto.application.recruitmentprocess;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitmentProcessNodeVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5153086447847187913L;

    private Long id;

    private Long recruitmentProcessId;

    private Long tenantId;

    private NodeType nodeType;

    private Long nextNodeId;

    private String name;

    private String description;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("recruitmentProcessId", "tenantId", "nodeType", "nextNodeId"));

    public RecruitmentProcessNodeVO tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public RecruitmentProcessNodeVO recruitmentProcessId(Long recruitmentProcessId) {
        this.recruitmentProcessId = recruitmentProcessId;
        return this;
    }

    public RecruitmentProcessNodeVO nodeType(NodeType nodeType) {
        this.nodeType = nodeType;
        return this;
    }

    public RecruitmentProcessNodeVO name(String name) {
        this.name = name;
        return this;
    }

    public RecruitmentProcessNodeVO description(String description) {
        this.description = description;
        return this;
    }

    public RecruitmentProcessNodeVO nextNodeId(Long nextNodeId) {
        this.nextNodeId = nextNodeId;
        return this;
    }
}
