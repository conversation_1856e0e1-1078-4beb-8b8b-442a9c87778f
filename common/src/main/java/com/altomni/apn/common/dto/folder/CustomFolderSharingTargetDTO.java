package com.altomni.apn.common.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.common.enumeration.folder.CustomFolderSharingTargetCategory;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CustomFolderSharingTargetDTO {
    @JsonIgnore
    private Long id;

    @NotNull(message = "targetCategory is required")
    private CustomFolderSharingTargetCategory targetCategory;

    @NotNull(message = "targetId is required")
    private Long targetId;

    @ApiModelProperty(value = "The permission of Folder.", allowableValues = "READONLY, READWRITE")
    @NotNull(message = "permission is required")
    private FolderPermission permission;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public CustomFolderSharingTargetCategory getTargetCategory() {
        return targetCategory;
    }

    public void setTargetCategory(CustomFolderSharingTargetCategory targetCategory) {
        this.targetCategory = targetCategory;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public FolderPermission getPermission() {
        return permission;
    }

    public void setPermission(FolderPermission permission) {
        this.permission = permission;
    }
}
