package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.utils.CommonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TalentBriefDTO implements Serializable {

    private Long talentId;
    private String fullName;
    private String createUserName;
    private Long createUserId;
    private Instant createdAt;
    private ResumeSourceType source;

    private Long tenantId;

    //禁猎客户功能：需要根据experience中的公司ID显示标签
    List<TalentExperienceDTO> experiences;

    public TalentBriefDTO(Long id, String fullName, String createUserFistName, String createUserLastName, Long createUserId, Instant createdAt, Long tenantId) {
        this.talentId = id;
        this.fullName = fullName;
        this.createUserId = createUserId;
        this.createUserName = CommonUtils.formatFullNameWithBlankCheck(createUserFistName, createUserLastName);
        this.createdAt = createdAt;
        this.tenantId = tenantId;
    }
}
