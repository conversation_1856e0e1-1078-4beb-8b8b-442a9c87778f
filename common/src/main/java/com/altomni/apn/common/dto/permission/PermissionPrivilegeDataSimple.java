package com.altomni.apn.common.dto.permission;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

public interface PermissionPrivilegeDataSimple extends Serializable {

    Long getId();

    String getName();

    @Data
    @AllArgsConstructor
    class PermissionPrivilegeContainer implements Serializable{
        private Long id;
        private String name;
    }
}
