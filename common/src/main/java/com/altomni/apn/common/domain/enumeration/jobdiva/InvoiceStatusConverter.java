package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoiceStatusConverter extends AbstractAttributeConverter<InvoiceStatusType, Integer> {
    public InvoiceStatusConverter() {
        super(InvoiceStatusType::toDbValue, InvoiceStatusType::fromDbValue);
    }
}
