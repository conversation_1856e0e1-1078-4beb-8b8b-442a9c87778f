package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum SearchConfigType implements ConvertedEnum<Integer> {
    TIME_SHEET_FILTER(0),
    TIME_SHEET_TABLE(1),
    EXPENSE_FILTER(2),
    EXPENSE_TABLE(3),
    ADVANCE_FILTER(4),
    COMMON_FILTER(5);

    private final int dbValue;

    SearchConfigType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<SearchConfigType, Integer> resolver = new ReverseEnumResolver<>(SearchConfigType.class, SearchConfigType::toDbValue);

    public static SearchConfigType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
