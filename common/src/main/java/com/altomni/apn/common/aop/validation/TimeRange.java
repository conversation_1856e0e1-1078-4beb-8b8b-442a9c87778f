package com.altomni.apn.common.aop.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

@Constraint(validatedBy = TimeRangeValidator.class)
//@Target({METHOD, CONSTRUCTOR})
@Target(TYPE)
@Retention(RUNTIME)
@Documented
public @interface TimeRange {

    String leftBound();
    String rightBound();

    String message() default
        "End date must be after begin date";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
