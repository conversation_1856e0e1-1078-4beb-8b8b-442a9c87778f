package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum GroupInvoiceContentType implements ConvertedEnum<Integer>
{
    TIMESHEET(0),
    TIMESHEET_EXPENSE(1);


    private final int dbValue;

    GroupInvoiceContentType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<GroupInvoiceContentType, Integer> resolver = new ReverseEnumResolver<>(GroupInvoiceContentType.class, GroupInvoiceContentType::toDbValue);

    public static GroupInvoiceContentType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
