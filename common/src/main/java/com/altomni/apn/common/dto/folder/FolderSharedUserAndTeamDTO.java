package com.altomni.apn.common.dto.folder;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "List of distinct shared user and team list on all shared to me folders for searching filter")
public class FolderSharedUserAndTeamDTO {
    @ApiModelProperty(value = "all teams that have been shared with the folder(s) as these shared to the current user.")
    List<FolderSharedTargetNameDTO> sharedTeamList;

    @ApiModelProperty(value = "all users that have been shared with the folder(s) as these shared to the current user.")
    List<FolderSharedTargetNameDTO> sharedUserList;
}
