package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * TimeSheetBreakTimeType
 */
public enum TimeSheetStatus implements ConvertedEnum<Integer> {

    DRAFT(0,"Draft"),
    APPLIED_APPROVE(1,"Pending Approval"),
    REJECTED(2,"Rejected"),
    NO_RECORD(3,"No record"),
    APPROVED(4,"Approved"),
    MISSING(5,"missing");

    private final int dbValue;
    private final String description;

    TimeSheetStatus(int dbValue,String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }



    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TimeSheetStatus, Integer> resolver = new ReverseEnumResolver<>(TimeSheetStatus.class, TimeSheetStatus::toDbValue);

    public static TimeSheetStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
