package com.altomni.apn.common.dto.application.talentrecruitmentprocess;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessSubmitToClientVO extends AuditingUser implements Serializable {

    private static final long serialVersionUID = -3759555729112733019L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private Instant submitTime;

    private String note;

    private String emailTrackingNumber;

    private Boolean theFirstJobToChangeToSubmitToClient = Boolean.FALSE;

    private Instant noteLastModifiedDate;

    private Long noteLastModifiedByUserId;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

    private TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate;
}
