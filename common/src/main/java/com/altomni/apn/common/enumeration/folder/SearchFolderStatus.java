package com.altomni.apn.common.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum SearchFolderStatus implements ConvertedEnum<Integer> {

    UNKNOWN_INACTIVE(0, "unknown invalid"),
    FOLDER_ACTIVE(1, "search folder is valid"),
    FOLDER_DELETED(21, "Custom Folder is deleted"),

    SHARED_FOLDER_DELETED(22, "Shared Folder is deleted"),

    FOLDER_SHARE_REVOKE(31, "The share of folder is revoked");

    private final int dbValue;

    private final String text;

    SearchFolderStatus(Integer dbValue, String text) {
        this.dbValue = dbValue;
        this.text = text;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getText() {
        return text;
    }

    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<SearchFolderStatus, Integer> resolver = new ReverseEnumResolver<>(SearchFolderStatus.class, SearchFolderStatus::toDbValue);

    public static SearchFolderStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
