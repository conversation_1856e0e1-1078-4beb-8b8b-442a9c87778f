package com.altomni.apn.common.domain.enumeration.jobdiva;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum DiscountType implements ConvertedEnum<Integer> {

    NO_DISCOUNT(0,0),
    ADOBE(1,0.0225),
    PINTEREST(2,0.0300),
    NEW_GOOGLE(3,0.0165),
    EQUINIX(4,0.0300),

    //disabled
    ZEROCHAOS(5, 0.0300),
    PONTOON_MSP_FEE(6, 0.0250),
    INTUIT_MSP_FEE(7, 0.0285),
    GOOGLE_MSP_FEE(8, 0.0240),
    EQUINIX_PRO_JOBDIVA(9, 0.0200),

    ZEROCHAOS_LINKEDIN_MSP_FEE(10, 0.0250),
    LINKEDIN_MSP_FEE(11, 0.0250),

    <PERSON><PERSON>_GOOGLE_MSP_225(12, 0.0225),
    GOOGLE_MSP_165(13, 0.0165),
    ZEROC<PERSON><PERSON>_HUAWEI_MSP_FEE_165(14, 0.0165),
    LINKEDIN_MSP_FEE_165(15, 0.0165),
    ADOBE_165(16, 0.0165),
    ZEROCHAOS_LINKEDIN_MSP_FEE_165(17, 0.0165),

    ZEROCHAOS_LINKEDIN_MSP_FEE_300(18, 0.0300),
    ZEROCHAOS_HUAWEI_MSP_FEE_300(19, 0.0300),
    NEW_GOOGLE_MSP_300(20, 0.0300),

    ZEROCHAOS_HUAWEI_MSP_FEE_250(21, 0.0250),
    EQUINIX_250(22, 0.0250),
    GOOGLE_MSP_250(23, 0.0250),
    NEW_GOOGLE_MSP_250(24, 0.0250),

    NEW_GOOGLE_MSP_240(25, 0.0240),
    ZEROCHAOS_HUAWEI_MSP_FEE_240(26, 0.0240),
    PONTOON_MSP_FEE_240(27, 0.0240),

    ZEROCHAOS_LINKEDIN_MSP_FEE_200(28, 0.0200),
    ZEROCHAOS_HUAWEI_MSP_FEE_200(29, 0.0200),

    EQUINIX_PRO_JOBDIVA_150(30, 0.0150),
    LINKEDIN_MSP_FEE_200(31,0.0200),

    COMBINED_DISCOUNT_JOBDIVA_A(32, 0.0500),
    COMBINED_DISCOUNT_JOBDIVA_B(33, 0.0510);

    private final int dbValue;
    private final double value;

    DiscountType(int dbValue,double value) {
        this.dbValue = dbValue;
        this.value = value;
    }


    public double getValue() {
        return value;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<DiscountType, Integer> resolver = new ReverseEnumResolver<>(DiscountType.class, DiscountType::toDbValue);

    public static DiscountType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}