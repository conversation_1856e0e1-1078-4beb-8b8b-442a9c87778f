package com.altomni.apn.common.domain.dict;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ApiModel(description = "Enum job priority entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_job_priority")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumJobPriority implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "level", nullable = false)
    private Integer level;

    @Column(name = "name", nullable = false, length = 10)
    private String name;

    @Column(name = "en_display", nullable = false, length = 10)
    private String enDisplay;

    @Column(name = "cn_display", length = 10)
    private String cnDisplay;


    public static Set<EnumJobPriority> transfer(List<Long> body) {
        if (body == null) {
            return null;
        } else {
            return body.stream().map(dictCode -> {
                EnumJobPriority enumJobPriority = new EnumJobPriority();
                enumJobPriority.setId(dictCode);
                return enumJobPriority;
            }).collect(Collectors.toSet());
        }
    }

    public static List<Long> convert(Set<EnumJobPriority> body) {
        if (body == null) {
            return null;
        } else {
            return body.stream().map(EnumJobPriority::getId
            ).collect(Collectors.toList());
        }
    }

}
