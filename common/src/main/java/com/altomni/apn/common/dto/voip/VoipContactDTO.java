package com.altomni.apn.common.dto.voip;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus;
import com.altomni.apn.common.domain.enumeration.voip.PhoneCallStatus;
import com.altomni.apn.common.domain.enumeration.voip.PhoneCallStatusConverter;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModel;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModelConverter;
import com.altomni.apn.common.domain.voip.VoipContact;
import com.altomni.apn.common.domain.voip.VoipContactWithTalentContact;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Convert;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

@Data
@Getter
@Setter
@NoArgsConstructor
public class VoipContactDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @NotNull
    private String phoneCallId;

    private String instanceId;

    private Long userId;

    private String userName;

    private Long talentId;

    private String talentName;

    private Long tenantId;

    @Convert(converter = TranscriptionModelConverter.class)
    private TranscriptionModel transcriptionModel;

    @Convert(converter = PhoneCallStatusConverter.class)
    private PhoneCallStatus phoneCallStatus = PhoneCallStatus.ANSWERED;

    private Long callTypeId;

    private Long callResultId;

    private Long jobId;

    private ContactType contactType;

    private String phoneNumber;

    private Boolean isWrongNumber;

    private Boolean isRecordDelete = false;

    private String summary;

    private Instant createdDate;

    private Long teamId;

    public VoipContactDTO(VoipContactWithTalentContact voipContact, TalentContactVerificationStatus verificationStatus) {
        this.id = voipContact.getId();
        this.phoneCallId = voipContact.getPhoneCallId();
        this.instanceId = voipContact.getInstanceId();
        this.userId = voipContact.getUserId();
        this.userName = voipContact.getUserName();
        this.talentId = voipContact.getTalentId();
        this.talentName = voipContact.getTalentName();
        this.tenantId = voipContact.getTenantId();
        this.transcriptionModel = voipContact.getTranscriptionModel();
        this.phoneCallStatus = PhoneCallStatus.ANSWERED;
        this.callTypeId = voipContact.getCallTypeId();
        this.callResultId = voipContact.getCallResultId();
        this.jobId = voipContact.getJobId();
//        this.contactType = ContactType.PHONE;
        this.phoneNumber = voipContact.getPhoneNumber();
        this.isWrongNumber = TalentContactVerificationStatus.WRONG_CONTACT.equals(verificationStatus);
        this.isRecordDelete = voipContact.getIsRecordDelete();
        this.summary = voipContact.getSummary();
        this.createdDate = voipContact.getCreatedDate();
    }


}
