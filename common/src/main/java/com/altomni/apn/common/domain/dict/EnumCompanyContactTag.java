package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

@ApiModel(description = "Enum company tag entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_company_contact_tag")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumCompanyContactTag implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display_order")
    private Integer enDisplayOrder;

    @Column(name = "cn_display_order")
    private Integer cnDisplayOrder;

}
