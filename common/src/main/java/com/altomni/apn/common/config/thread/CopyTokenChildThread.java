package com.altomni.apn.common.config.thread;

import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Objects;

/**
 * async thread with token thread
 * <AUTHOR>
 */
@Slf4j
public abstract class CopyTokenChildThread implements Runnable {

    private final SecurityContext context;

    protected CopyTokenChildThread() {
        //parent thread get context
        SecurityContext parentContext = SecurityContextHolder.getContext();
        if (parentContext == null) {
            throw new RuntimeException("CopyTokenChildThread parentContext is must");
        }
        this.context = parentContext;
    }

    @Override
    public void run() {
        //pool thread run
        initSecurityContext();
        runTask();
    }

    private void initSecurityContext() {
        // clear SecurityContext
        String oldToken = "";
        try {
            oldToken = SecurityUtils.getCurrentUserToken();
        } catch (Exception e) {
            log.info("CopyTokenChildThread before token is null");
        }
        SecurityContextHolder.clearContext();
        // set SecurityContext
        SecurityContextHolder.setContext(context);
        String token;
        try {
            token = SecurityUtils.getCurrentUserToken();
            log.info(" token refresh = [{}], currentChildToken = [{}]", !Objects.equals(oldToken, token), token);
        } catch (Exception e) {
            log.info("CopyTokenChildThread currentChildToken is null");
        }
    }

    /**
     * do something
     */
    public abstract void runTask();

}
