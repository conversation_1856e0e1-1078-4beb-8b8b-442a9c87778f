package com.altomni.apn.common.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.PlatformTypeConverter;
import com.altomni.apn.common.domain.enumeration.job.PlatformType;


import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "job_sharing_platform")
public class JobSharingPlatform extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "job_id", nullable = false)
    private Long jobId;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "platformType", nullable = false)
    @Convert(converter = PlatformTypeConverter.class)
    private PlatformType platformType;

    @Column(name = "display_language_id", nullable = false)
    private Integer displayLanguageId;

    @Column(name = "uuid", nullable = false)
    private String uuid;

    @Column(name = "job_sharing_content")
    private String jobSharingContent;

    public JobSharingPlatform() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }


    public PlatformType getPlatformType() {
        return platformType;
    }

    public void setPlatformType(PlatformType platformType) {
        this.platformType = platformType;
    }

    public String getJobSharingContent() {
        return jobSharingContent;
    }

    public void setJobSharingContent(String jobSharingContent) {
        this.jobSharingContent = jobSharingContent;
    }

    public Integer getDisplayLanguageId() {
        return displayLanguageId;
    }

    public void setDisplayLanguageId(Integer displayLanguageId) {
        this.displayLanguageId = displayLanguageId;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

}

