package com.altomni.apn.common.dto.application.dashboard;


import com.altomni.apn.common.constants.TalentRecruitmentProcessConstants;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

public class MyCandidate implements Serializable {

    private static final long serialVersionUID = 4948887028012258882L;

    private Long talentRecruitmentProcessId;

    private Long talentId;

    private String talentName;

    private Long jobId;

    private String jobTitle;

    private JobType jobType;

    private Long companyId;

    private String companyName;

    private MyCandidateStatusFilter status;

    private Instant lastModifiedDate;

    private Integer interviewCount;

    private EliminateReason eliminateReason;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillAmount = BigDecimal.ZERO;

    private Integer currency;

    public Long getTalentRecruitmentProcessId() {
        return talentRecruitmentProcessId;
    }

    public void setTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
    }

    public EliminateReason getEliminateReason() {
        return eliminateReason;
    }

    public void setEliminateReason(EliminateReason eliminateReason) {
        this.eliminateReason = eliminateReason;
    }

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public String getTalentName() {
        return talentName;
    }

    public void setTalentName(String talentName) {
        this.talentName = talentName;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public JobType getJobType() {
        return jobType;
    }

    public void setJobType(JobType jobType) {
        this.jobType = jobType;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public MyCandidateStatusFilter getStatus() {
        return status;
    }

    public void setStatus(MyCandidateStatusFilter status) {
        this.status = status;
    }

    public Instant getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Instant lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public Integer getInterviewCount() {
        return interviewCount;
    }

    public void setInterviewCount(Integer interviewCount) {
        this.interviewCount = interviewCount;
    }

    public BigDecimal getTotalBillAmount() {
        return totalBillAmount == null ? null : new BigDecimal(TalentRecruitmentProcessConstants.DECIMAL_FORMAT.format(totalBillAmount));
    }

    public void setTotalBillAmount(BigDecimal totalBillAmount) {
        this.totalBillAmount = totalBillAmount;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "MyCandidate{" +
                "talentRecruitmentProcessId=" + talentRecruitmentProcessId +
                ", talentId=" + talentId +
                ", talentName='" + talentName + '\'' +
                ", jobId=" + jobId +
                ", jobTitle='" + jobTitle + '\'' +
                ", jobType=" + jobType +
                ", companyId=" + companyId +
                ", companyName='" + companyName + '\'' +
                ", status=" + status +
                ", lastModifiedDate=" + lastModifiedDate +
                ", interviewCount=" + interviewCount +
                ", totalBillAmount=" + totalBillAmount +
                ", currency=" + currency +
                '}';
    }
}
