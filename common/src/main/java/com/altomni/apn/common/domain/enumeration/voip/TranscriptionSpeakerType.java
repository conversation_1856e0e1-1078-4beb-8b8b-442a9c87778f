package com.altomni.apn.common.domain.enumeration.voip;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TranscriptionSource enumeration.
 */
public enum TranscriptionSpeakerType implements ConvertedEnum<Integer> {

    CANDIDATE(0),
    RECRUITER(1);

    private final int dbValue;

    TranscriptionSpeakerType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TranscriptionSpeakerType, Integer> resolver =
        new ReverseEnumResolver<>(TranscriptionSpeakerType.class, TranscriptionSpeakerType::toDbValue);

    public static TranscriptionSpeakerType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
