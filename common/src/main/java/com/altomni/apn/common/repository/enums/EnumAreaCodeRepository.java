package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumCountry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface EnumAreaCodeRepository extends JpaRepository<EnumCountry, Integer> {


    @Query(value = "select ec.* from enum_country ec where ec.id in (select distinct enum_country_id from user_delivery_country_relation)", nativeQuery = true)
    List<EnumCountry> findAllDeliveryCountry();
}
