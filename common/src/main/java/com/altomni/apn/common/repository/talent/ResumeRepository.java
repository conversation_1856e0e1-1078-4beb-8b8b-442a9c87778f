package com.altomni.apn.common.repository.talent;

import com.altomni.apn.common.domain.talent.Resume;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data JPA repository for the Resume entity.
 */
@Repository
public interface ResumeRepository extends JpaRepository<Resume, Long> {

    Resume findByDataMD5(String dataMD5);

    @Query(value = "select t from Resume t where t.uuid = ?1")
    Resume findByUuid(String uuid);

    List<Resume> findAllByIdIn(List<Long> ids);

    @Query(value = "select r.* from resume r left join talent_resume_relation trr on trr.resume_id = r.id and trr.status = ?2 left join talent t on t.id = trr.talent_id where t.id = ?1 ORDER BY r.created_date desc limit 1", nativeQuery = true)
    Resume findOneByTalentId(Long talentId, byte status);

    @Modifying
    @Transactional
    @Query(value = " update resume set has_display = ?1, has_portrait = ?2, n_pages = ?3 where id = ?4", nativeQuery = true)
    void updateInfosById(Integer hasDisplay, Integer hasPortrait, Integer nPages, Long id);


    @Query(value = "select trr.file_name from talent_resume_relation trr left join resume r on trr.resume_id = r.id where r.uuid = ?1 and trr.tenant_id = ?2 limit 1 ", nativeQuery = true)
    String getFileName(String uuid, Long tenantId);

}
