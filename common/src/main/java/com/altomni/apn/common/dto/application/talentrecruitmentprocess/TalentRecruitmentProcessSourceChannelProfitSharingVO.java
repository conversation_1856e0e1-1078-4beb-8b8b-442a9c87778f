package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessSourceChannelProfitSharingVO implements Serializable {

    private static final long serialVersionUID = 5130333806617438622L;

    private ResumeSourceType channelPlatform;

    private BigDecimal profitSharingRatio;

    public ResumeSourceType getChannelPlatform() {
        return channelPlatform;
    }

    public void setChannelPlatform(ResumeSourceType channelPlatform) {
        this.channelPlatform = channelPlatform;
    }

    public BigDecimal getProfitSharingRatio() {
        return profitSharingRatio;
    }

    public void setProfitSharingRatio(BigDecimal profitSharingRatio) {
        this.profitSharingRatio = profitSharingRatio;
    }
}
