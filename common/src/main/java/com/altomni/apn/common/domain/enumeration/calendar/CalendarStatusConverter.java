package com.altomni.apn.common.domain.enumeration.calendar;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CalendarStatusConverter extends AbstractAttributeConverter<CalendarStatusEnum, Integer> {
    public CalendarStatusConverter() {
        super(CalendarStatusEnum::toDbValue, CalendarStatusEnum::fromDbValue);
    }
}
