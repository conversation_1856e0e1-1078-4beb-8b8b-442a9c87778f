package com.altomni.apn.common.config.audit;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.utils.SecurityUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Optional;

/**
 * Implementation of AuditorAware based on Spring Security.
 */
@Component("springSecurityAuditorAware")
public class SpringSecurityAuditorAware implements AuditorAware<String> {

    @NotNull
    @Override
    public Optional<String> getCurrentAuditor() {
        // 优先从 ThreadLocal 中获取操作用户
        String threadLocalUid = AuditUserHolder.get();
        if (StringUtils.hasText(threadLocalUid)) {
            return Optional.of(threadLocalUid);
        }
        String loginUserUid = SecurityUtils.getUserUid();
        if (!StringUtils.hasText(loginUserUid)) {
            return Optional.of(Constants.SYSTEM);
        }
        return Optional.of(loginUserUid);
    }
}
