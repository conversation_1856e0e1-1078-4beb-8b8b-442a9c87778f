package com.altomni.apn.common.domain.enumeration.search;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CommonPoolTalentType implements ConvertedEnum<Integer> {
    UNLOCKED_CANDIDATE(0),
    LOCKED_CANDIDATE(1);

    // static resolving:
    public static final ReverseEnumResolver<CommonPoolTalentType, Integer> resolver =
        new ReverseEnumResolver<>(CommonPoolTalentType.class, CommonPoolTalentType::toDbValue);
    private final int dbValue;

    CommonPoolTalentType(int dbValue) {
        this.dbValue = dbValue;
    }

    public static CommonPoolTalentType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }
}
