package com.altomni.apn.common.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;


@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentCertificateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private LocalDate startDate;

    private LocalDate endDate;

    private String name;

    private String url;

    private String description;

    private Long talentId;
}
