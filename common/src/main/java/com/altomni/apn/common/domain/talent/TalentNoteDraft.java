package com.altomni.apn.common.domain.talent;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentNoteDraft.
 */
@ApiModel(value = "User's note draft on talent")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "talent_note_draft")
public class TalentNoteDraft implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "talent_id")
    private Long talentId;

    @ApiModelProperty(value = "talent note draft in JSON String type")
    @Column(name = "draft_data")
    private String draftData;


}
