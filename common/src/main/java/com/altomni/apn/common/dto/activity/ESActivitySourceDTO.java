package com.altomni.apn.common.dto.activity;

import lombok.Data;

import java.util.List;

@Data
public class ESActivitySourceDTO {
    private String createdDate;
    private String createdBy;

    private List<ActivityChangeDTO> changeFields;

    public List<ActivityChangeDTO> getChangeFields() {
        return changeFields;
    }

    public void setChangeFields(List<ActivityChangeDTO> changeFields) {
        this.changeFields = changeFields;
    }




}
