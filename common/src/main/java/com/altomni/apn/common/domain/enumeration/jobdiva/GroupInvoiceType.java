package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum GroupInvoiceType implements ConvertedEnum<Integer>
{
    EMPLOYEE(0),
    COMPANY(1),
    CUSTOM(2),
    PO(3),
    CUSTOM_REF(4),
    BILL_CONTACT(5),
    JOB(6);

    private final int dbValue;

    GroupInvoiceType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<GroupInvoiceType, Integer> resolver = new ReverseEnumResolver<>(GroupInvoiceType.class, GroupInvoiceType::toDbValue);

    public static GroupInvoiceType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
