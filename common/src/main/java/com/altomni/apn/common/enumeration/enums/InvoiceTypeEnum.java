package com.altomni.apn.common.enumeration.enums;

public enum InvoiceTypeEnum {
    INVOICE, INVOICING_APPLICATION_INFO, T_GROUP_INVOICE;

    public static Long getTypeInvoiceId(Long origin, InvoiceTypeEnum type) {
        switch (type) {
            case INVOICE:
                return 100000000L + origin;
            case INVOICING_APPLICATION_INFO:
                return 200000000L + origin;
            case T_GROUP_INVOICE:
                return 300000000L + origin;
        }
        return origin;
    }
}
