package com.altomni.apn.common.domain.enumeration.application;



import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class EliminateReasonConverter extends AbstractAttributeConverter<EliminateReason, Integer> {
    public EliminateReasonConverter() {
        super(EliminateReason::toDbValue, EliminateReason::fromDbValue);
    }
}
