package com.altomni.apn.common.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum FolderType implements ConvertedEnum<Integer> {
    CUSTOMIZED(10),

    SHARED(20),

    SEARCHED(30);

    private final int dbValue;

    FolderType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<FolderType, Integer> resolver =
            new ReverseEnumResolver<>(FolderType.class, FolderType::toDbValue);

    public static FolderType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}