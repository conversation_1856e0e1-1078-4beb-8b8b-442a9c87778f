package com.altomni.apn.common.dto.http;

import okhttp3.Headers;

import java.util.regex.Pattern;


public class HttpResponse {

    private static final Pattern ERROR_DESCRIPTION_PATTERN = Pattern.compile("error_description=\"([^\"]*)\"");

    private int code;

    private String body;

    public Headers headers;

    public HttpResponse() {
    }

    public HttpResponse(int code, String body) {
        this.code = code;
        this.body = body;
    }

    public HttpResponse(int code, String body, Headers headers) {
        this.code = code;
        this.body = body;
        this.headers = headers;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public Headers getHeaders() { return headers; }

    public void setHeaders(Headers headers) { this.headers = headers; }

    @Override
    public String toString() {
        return "HttpResponse{" +
                "code=" + code +
                ", body='" + body + '\'' +
                ", headers=" + headers +
                '}';
    }
}
