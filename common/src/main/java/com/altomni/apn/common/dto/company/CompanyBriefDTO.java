package com.altomni.apn.common.dto.company;

import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.company.CompanyType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Set;

/**
 * A Company.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CompanyBriefDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(required = true, value = "Company name")
    private String name;

    private Set<EnumRelationDTO> industries;

    private Long salesLeadId;
    private BigDecimal accountProgress;
    private CompanyType type;
    private Boolean active;
}
