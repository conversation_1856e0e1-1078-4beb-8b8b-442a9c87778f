package com.altomni.apn.common.dto.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.NotePriority;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentNoteDTO extends AbstractAuditingEntity implements Serializable {

    private Long id;

    private String title;

    private String note;

    private Map<String, Object> additionalInfo;

    private Boolean visible = true;

    private NotePriority priority = NotePriority.NORMAL;

    private Long talentId;

    private Long userId;

    private TalentNoteType noteType;

    private Integer noteStatus;

    private Boolean readStatus;

    private String enrichResult;

    private String parsedResult;

    private Long agencyId;

    private String phoneCallId;

    private String enrichStatus;

    public TalentNoteDTO(Long id, String title, String note, Map<String, Object> additionalInfo, String phoneCallId) {
        this.id = id;
        this.title = title;
        this.note = note;
        this.additionalInfo = additionalInfo;
        this.phoneCallId = phoneCallId;
    }

}
