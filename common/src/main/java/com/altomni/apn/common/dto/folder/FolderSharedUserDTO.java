package com.altomni.apn.common.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.common.utils.CommonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FolderSharedUserDTO {
    private Long folderId;

    @ApiModelProperty(value = "User is being shared with the folder")
    private Long userId;


    private String firstName;

    private String lastName;

    private String fullName;

    @ApiModelProperty(value = "owner of the folder")
    private Long ownerUserId;

    @ApiModelProperty(value = "permission of current folder for the user")
    private FolderPermission folderPermission;

    public FolderSharedUserDTO(Long folderId, Long userId, String fullName, Long ownerUserId) {
        this.folderId = folderId;
        this.userId = userId;
        this.fullName = fullName;
        this.ownerUserId = ownerUserId;
    }

    public FolderSharedUserDTO(Long userId, String fullName) {
        this.folderId = null;
        this.userId = userId;
        this.fullName = fullName;
        this.ownerUserId = null;
    }

    public FolderSharedUserDTO(Long userId, String firstName, String lastName) {
        this.userId = userId;
        this.firstName = firstName;
        this.lastName = lastName;
    }

    public FolderSharedUserDTO(Long folderId, Long userId, String firstName, String lastName) {
        this.folderId = folderId;
        this.userId = userId;
        this.firstName = firstName;
        this.lastName = lastName;
    }

    public FolderSharedUserDTO(Long folderId, Long userId, String fullName, Long ownerUserId, FolderPermission permission) {
        this.folderId = folderId;
        this.userId = userId;
        this.fullName = fullName;
        this.ownerUserId = ownerUserId;
        this.folderPermission = permission;
    }

    public FolderSharedUserDTO(Long folderId, Long userId, String firstName, String lastName, Long ownerUserId, FolderPermission folderPermission) {
        this.folderId = folderId;
        this.userId = userId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.ownerUserId = ownerUserId;
        this.folderPermission = folderPermission;
    }

    public String getFullName() {
        if(fullName != null) {
            return fullName;
        }
        return CommonUtils.formatFullName(firstName, lastName);
    }
}
