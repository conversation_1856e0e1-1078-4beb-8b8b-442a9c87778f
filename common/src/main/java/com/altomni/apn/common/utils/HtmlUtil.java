package com.altomni.apn.common.utils;

public class HtmlUtil {

    /** append tag
     * @param sb
     * @param tag
     * @param contents
     */
    public static void appendTag(StringBuilder sb, String tag, String contents) {
        sb.append('<').append(tag).append('>');
        sb.append(contents);
        sb.append("</").append(tag).append('>');
    }

    /** append url link
     * @param sb
     * @param contents
     * @param link
     */
    public static void appendLinkTag(StringBuilder sb, String contents, String link){
        sb.append("<a href=\"").append(link).append("\">");
        sb.append(contents);
        sb.append("</a>");
    }

    /** append email link
     * @param sb
     * @param contents
     * @param email
     */
    public static void appendEmailLinkTag(StringBuilder sb, String contents, String email){
        sb.append("<a href=\"mailto:").append(email).append("\">");
        sb.append(contents);
        sb.append("</a>");
    }

    /** append break
     * @param sb
     */
    public static void appendBreakCell(StringBuilder sb) {
        sb.append("<br>");
    }

    /** append Data for table
     * @param sb
     * @param contents
     */
    public static void appendDataCell(StringBuilder sb, String contents) {
        appendTag(sb, "td", contents);
    }

    /** append Header for table
     * @param sb
     * @param contents
     */
    public static void appendHeaderCell(StringBuilder sb, String contents) {
        appendTag(sb, "th", contents);
    }

    /** append paragraph
     * @param sb
     * @param contents
     */
    public static void appendParagraphCell(StringBuilder sb, String contents) {
        appendTag(sb, "p", contents);
    }

    /** append paragraph with padding, usually 30px/per unit
     * @param sb
     * @param contents
     */
    public static void appendParagraphCellWithPad(StringBuilder sb, Integer pad, String contents) {
        sb.append("<p style=\"padding-left:");
        sb.append(pad.toString());
        sb.append("px;\">");
        sb.append(contents);
        sb.append("</").append("p").append('>');
        sb.append("<br>");
    }

    /** append li
     * @param sb
     * @param contents
     */
    public static void appendLiCell(StringBuilder sb, String contents) {
        appendTag(sb, "li", contents);
    }

    /** append br tags
     * @param sb
     * @param times
     */
    public static void appendBrTags(StringBuilder sb, Integer times) {
        if(times != 0){
            for(int i = 0;i< times; i ++){
                sb.append("<br/>");
            }
        }
    }

    /** append strike contents
     * @param sb
     * @param contents
     */
    public static void appendStrikeCell(StringBuilder sb, String contents) {
        appendTag(sb, "strike", contents);
    }

    /** append big contents
     * @param sb
     * @param contents
     */
    public static void appendBigCell(StringBuilder sb, String contents) {
        appendTag(sb, "big", contents);
    }

    /** append bold contents
     * @param sb
     * @param contents
     */
    public static void appendBoldCell(StringBuilder sb, String contents) {
        appendTag(sb, "b", contents);
    }

    /** append Italic contents
     * @param sb
     * @param contents
     */
    private static void appendItalicCell(StringBuilder sb, String contents) {
        appendTag(sb, "i", contents);
    }
}
