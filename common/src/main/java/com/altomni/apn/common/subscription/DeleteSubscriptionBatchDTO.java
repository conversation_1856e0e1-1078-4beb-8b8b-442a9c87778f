package com.altomni.apn.common.subscription;

import com.altomni.apn.common.enumeration.reportSubscriptions.ReportType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.repository.query.Param;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeleteSubscriptionBatchDTO {
    private List<Long> userIds;
    private ReportType reportType;
    private Long tenantId;
}
