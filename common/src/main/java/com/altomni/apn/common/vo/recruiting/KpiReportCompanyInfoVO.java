package com.altomni.apn.common.vo.recruiting;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatusConverter;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Entity
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KpiReportCompanyInfoVO {

    @Id
    @JsonIgnore
    private String id;

    @JsonIgnore
    private Long companyId;

    @JsonIgnore
    @Convert(converter = AccountCompanyStatusConverter.class)
    private AccountCompanyStatus active;

    @JsonIgnore
    private String country;

    //用于business_flow_administrator co-am 角色展示国家
    private String roleCountry;

    @JsonIgnore
    private Instant companyCreatedDate;

    private Instant requestDate;

    private Long userId;

    private String username;

    @JsonIgnore
    private SalesLeadRoleType salesLeadRole;

    @JsonIgnore
    private String businessInfo;

    @JsonIgnore
    private Long companyNoteCount;

    private String industries;
    public List<KpiReportBusinessInfoVO> getBusinessInfoList() {
        if (StrUtil.isNotBlank(businessInfo)) {
            try {
                List<KpiReportBusinessInfoVO> businessInfoList = new ArrayList<>();
                Map<Long, List<Long>> businessServiceTypeMap = new HashMap<>(16);
                String[] businessArray = businessInfo.split(",");
                for (String business : businessArray) {
                    String[] businesses = business.split("\\|\\|\\|\\|\\|");
                    List<Long> serviceTypeList = CollUtil.newArrayList(Long.parseLong(businesses[2]));
                    if (businessServiceTypeMap.containsKey(Long.parseLong(businesses[0]))) {
                        businessServiceTypeMap.get(Long.parseLong(businesses[0])).add(Long.parseLong(businesses[2]));
                    } else {
                        KpiReportBusinessInfoVO businessInfoVO = new KpiReportBusinessInfoVO();
                        businessInfoVO.setBusinessId(Long.parseLong(businesses[0]));
                        businessInfoVO.setBusinessName(StrUtil.blankToDefault(businesses[1], null));
                        businessInfoList.add(businessInfoVO);
                        businessServiceTypeMap.put(Long.parseLong(businesses[0]), serviceTypeList);
                    }
                }
                businessInfoList.forEach(businessInfoVO -> {
                    businessInfoVO.setServiceTypeList(businessServiceTypeMap.get(businessInfoVO.getBusinessId()));
                });
                return businessInfoList;
            } catch (Exception e) {
                log.error("KpiReportCompanyInfoVO format error", e);
                e.printStackTrace();
            }
        }
        return List.of();
    }

}
