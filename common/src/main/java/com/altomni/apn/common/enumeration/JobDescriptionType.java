package com.altomni.apn.common.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum JobDescriptionType implements ConvertedEnum<Integer> {

    PLAIN_TEXT(0,"PLAIN_TEXT"),
    HTML(1,"HTML");
    private final int dbValue;
    private final String name;

    JobDescriptionType(int dbValue,String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<JobDescriptionType, Integer> resolver =
        new ReverseEnumResolver<>(JobDescriptionType.class, JobDescriptionType::toDbValue);

    public static JobDescriptionType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
