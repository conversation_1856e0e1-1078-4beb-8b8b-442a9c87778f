package com.altomni.apn.common.config.constants;

public interface EsFillerConstants {

    String RESPONSIBILITY = "responsibility";

    String USER_RESPONSIBILITY = "userResponsibility";

    String ES_POSTFIX = ".id";
    
    String ES_AM = RESPONSIBILITY + 1;

    String ES_DM = RESPONSIBILITY + 2;

    String ES_RECRUITER = RESPONSIBILITY + 3;

    String ES_SOURCER = RESPONSIBILITY + 4;

    String ES_CREATED_BY = RESPONSIBILITY + 5;

    String ES_OWNER = RESPONSIBILITY + 6;

    String ES_SHARED_BY = RESPONSIBILITY + 7;

    String ES_AC = RESPONSIBILITY + 8;

    String ES_PR = RESPONSIBILITY + 9;
    String ES_PARTICIPANTS = RESPONSIBILITY + 10;
    //String ES_ASSIGNED_USER = RESPONSIBILITY + 10;
    String ES_BD_OWNER = RESPONSIBILITY + 11;

    String ES_APPLICATION_PARTICIPANT = RESPONSIBILITY + 12;

    String ES_PTEAM = "pteam_";

    String ES_PUSER = "puser_";

}
