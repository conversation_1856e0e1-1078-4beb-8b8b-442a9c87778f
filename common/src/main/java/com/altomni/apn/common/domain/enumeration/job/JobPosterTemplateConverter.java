package com.altomni.apn.common.domain.enumeration.job;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class JobPosterTemplateConverter extends AbstractAttributeConverter<JobPosterTemplate, Integer> {
    public JobPosterTemplateConverter() {
        super(JobPosterTemplate::toDbValue, JobPosterTemplate::fromDbValue);
    }
}
