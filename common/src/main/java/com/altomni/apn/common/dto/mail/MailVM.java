package com.altomni.apn.common.dto.mail;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
public class MailVM {

    //email basic info
    private Long id;
    @NotEmpty
    private String from;
    @NotEmpty
    private List<String> to;
    private List<String> bcc;
    private List<String> cc;
    @NotEmpty
    private String subject;
    @NotEmpty
    private String content;
    private List<AttachmentVM> attachments;

    //additional settings
    private String baseUrl; //for other internal service, including its base url for any links in mail
    private Boolean isSystemEmail;
    private Boolean transactional;

    //related id
    private Long replyToThreadId;
    private String customerId1;
    private String customerId2;

    //deprecated
    private List<String> links;
    private List<MultipartFile> files;

    public MailVM(String from, List<String> to, List<String> bcc, List<String> cc, String subject, String content, List<MultipartFile> files) {
        this.from = from;
        this.to = to;
        this.bcc = bcc;
        this.cc = cc;
        this.subject = subject;
        this.content = content;
        this.files = files;
    }

    public MailVM(String from, List<String> to, List<String> bcc, List<String> cc, String subject, String content, List<String> links, Boolean isSystemEmail) {
        this.from = from;
        this.to = to;
        this.bcc = bcc;
        this.cc = cc;
        this.subject = subject;
        this.content = content;
        this.links = links;
        this.isSystemEmail = isSystemEmail;
    }

    public MailVM(String from, List<String> to, List<String> bcc, List<String> cc, String subject, String content, List<String> links, Boolean isSystemEmail, Boolean transactional) {
        this.from = from;
        this.to = to;
        this.bcc = bcc;
        this.cc = cc;
        this.subject = subject;
        this.content = content;
        this.links = links;
        this.isSystemEmail = isSystemEmail;
        this.transactional = transactional;
    }

}