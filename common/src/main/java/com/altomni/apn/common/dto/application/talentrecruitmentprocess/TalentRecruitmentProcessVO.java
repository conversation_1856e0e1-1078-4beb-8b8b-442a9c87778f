package com.altomni.apn.common.dto.application.talentrecruitmentprocess;

import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 6244691076239820949L;

    private Long id;

    private Long tenantId;

    private Long recruitmentProcessId;

    private Long talentId;

    private Long jobId;

    private NodeType lastNodeType;

    private NodeStatus lastNodeStatus;

    private Instant lastNodeDate;

    private String note;

    private Instant createdDate;

    private String createdBy;

    private UserBriefDTO createdUser;

    private Instant lastModifiedDate;

    private String lastModifiedBy;

    private UserBriefDTO lastModifiedUser;

    private List<TalentRecruitmentProcessNodeVO> talentRecruitmentProcessNodes;

    private TalentRecruitmentProcessEliminateVO eliminate;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

    private TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate;

    private JobDTOV3 job;

    private TalentBriefDTO talent;

    private Float defaultCommission;

    @ApiModelProperty(value = "true: 已离职， false:未离职")
    private Boolean resigned;

    @Transient
    private Boolean firstTimeToThisNode;
}
