package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TalentContactStatus enumeration.
 */
public enum TalentContactStatus implements ConvertedEnum<Integer> {
    AVAILABLE(0),
    INVALID(1);

    private final int dbValue;

    TalentContactStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TalentContactStatus, Integer> resolver =
        new ReverseEnumResolver<>(TalentContactStatus.class, TalentContactStatus::toDbValue);

    public static TalentContactStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
