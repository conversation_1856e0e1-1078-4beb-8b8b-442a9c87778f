package com.altomni.apn.common.vo.recruiting;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class KpiReportBusinessInfoVO {

    private Long businessId;

    private String businessName;

    private List<Long> serviceTypeList;

}
