package com.altomni.apn.common.vo.calendar;

import com.altomni.apn.common.domain.enumeration.calendar.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Convert;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CalendarEventAttendeeVO {

    private Long userId;

    @Convert(converter = CalendarEventAttendeeTypeEnumConverter.class)
    private CalendarEventAttendeeTypeEnum isOrganizer;

    @Convert(converter = CalendarEventAttendeeReminderTypeConverter.class)
    private CalendarEventAttendeeReminderTypeEnum isReminder;

    /** 0待完成 1已完成 3 逾期 */
    @Column(name = "status")
    @Convert(converter = CalendarStatusConverter.class)
    private CalendarStatusEnum status ;
}
