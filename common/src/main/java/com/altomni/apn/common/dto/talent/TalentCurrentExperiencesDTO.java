package com.altomni.apn.common.dto.talent;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentCurrentExperiencesDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String companyName;

    private String title;

    private String startDate;

    private String endDate;

    //禁猎客户衍生需求，增加人->客户、线索、拓客/工商数据关联关系字段
    private Long id;
    /**
     *   - id类型：bd_companies_business_information
     *   - 含义：这段经历对应的工商数据公司id，由esfiller自动识别
     */
    private String businessInfoCompanyId;
    /**
     *   - id类型：bd company
     *   - 含义：这段经历对应的拓客公司id，由esfiller自动识别
     */
    private String bdCompanyId;
    /**
     *   - id类型：lead company
     *   - 含义：这段经历对应的线索公司id，可能来自于esfiller自动识别，也可能因为拓客/工商公司转线索由后端同步
     */
    private String recogLeadsCompanyId;
    /**
     *   - id类型：crm account
     *   - 含义：这段经历对应的crm客户公司id，可能来自于esfiller自动识别，也可能因为线索转客户由后端同步
     */
    private String recogCRMAccountId;
    /**
     *   - id类型：apn company
     *   - 含义：这段经历对应的apn正式客户公司id，可能来自于esfiller自动识别，也可能因为apn发展中客户转正式客户由后端同步
     */
    private String recogCompanyId;

    public String getStartDate() {
        if(ObjectUtil.isNull(startDate)) {
            return "";
        }
        return startDate;
    }
}
