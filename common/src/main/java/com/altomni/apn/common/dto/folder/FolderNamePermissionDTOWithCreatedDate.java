package com.altomni.apn.common.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import lombok.Getter;

import java.time.Instant;

@Getter
public class FolderNamePermissionDTOWithCreatedDate extends FolderNamePermissionDTO{
    private Instant folderCreatedDate;
    public FolderNamePermissionDTOWithCreatedDate(Long id, String name, FolderPermission folderPermission, Instant folderCreatedDate) {
        super(id, name, folderPermission);
        this.folderCreatedDate = folderCreatedDate;
    }
}
