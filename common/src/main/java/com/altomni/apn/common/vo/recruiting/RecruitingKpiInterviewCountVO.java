package com.altomni.apn.common.vo.recruiting;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiInterviewCountVO extends RecruitingKpiCommonCountVO {

    private Long interview1;

    private String interview1Ids;

    private Long interview2;

    private String interview2Ids;

    private Long interviewFinal;

    private String interviewFinalIds;

    private Long interviewTotal;

    private String interviewIds;

    private Long twoOrMoreInterviews;

    private Long uniqueInterviewTalents;

    private Long currentInterview1;

    private Long currentInterview2;

    private Long currentInterviewFinal;

    private Long currentTwoOrMoreInterviews;

    private Long currentInterviewTotal;

    private Long interviewTotalProcess;

    private Long currentInterviewTotalProcess;


    private Long interview1AiRecommendNum;

    private Long currentInterview1AiRecommendNum;

    private Long interview2AiRecommendNum;

    private Long currentInterview2AiRecommendNum;

    private Long interviewFinalAiRecommendNum;

    private Long currentInterviewFinalAiRecommendNum;

    private Long twoOrMoreInterviewsAiRecommendNum;

    private Long currentTwoOrMoreInterviewsAiRecommendNum;

    private Long interviewTotalAiRecommendNum;

    private Long currentInterviewTotalAiRecommendNum;

    private Long aiRecommendNum;

    private Long currentAiRecommendNum;


    private Long precisionAiRecommendNum;

    private Long currentPrecisionAiRecommendNum;

    private Long interview1PrecisionAiRecommendNum;

    private Long currentInterview1PrecisionAiRecommendNum;

    private Long interview2PrecisionAiRecommendNum;

    private Long currentInterview2PrecisionAiRecommendNum;

    private Long interviewFinalPrecisionAiRecommendNum;

    private Long currentInterviewFinalPrecisionAiRecommendNum;

    private Long twoOrMoreInterviewsPrecisionAiRecommendNum;

    private Long currentTwoOrMoreInterviewsPrecisionAiRecommendNum;

    private Long interviewTotalPrecisionAiRecommendNum;

    private Long currentInterviewTotalPrecisionAiRecommendNum;

    private Long interviewNumProcessPrecisionAIRecommend;

    private Long currentInterviewNumProcessPrecisionAIRecommend;

    private Long interviewTotalProcessAiRecommendNum;

    private Long currentInterviewTotalProcessAiRecommendNum;
}
