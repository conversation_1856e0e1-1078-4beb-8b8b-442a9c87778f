package com.altomni.apn.common.vo.crmenums;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumContactTypeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "The Id for find contact Type")
    private Integer id;

    @ApiModelProperty(value = "The name for target value")
    private String name;

    @ApiModelProperty(value = "Flag for the duplication check")
    private Boolean duplicationCheck;

    @ApiModelProperty(value = "duplication check group for the cross duplication check")
    private Integer checkGroupId;

    @ApiModelProperty(value = "duplication check group for the cross duplication check")
    private Set<Integer> checkGroupSet;


    public EnumContactTypeVO(Integer id, String name, Boolean duplicationCheck, Integer checkGroupId) {
        this.id = id;
        this.name = name;
        this.duplicationCheck = duplicationCheck;
        this.checkGroupId = checkGroupId;
    }
}
