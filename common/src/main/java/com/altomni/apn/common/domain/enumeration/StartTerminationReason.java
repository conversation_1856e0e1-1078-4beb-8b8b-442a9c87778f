package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum StartTerminationReason implements ConvertedEnum<Integer> {

    CONTRACT_ENDED_AS_SCHEDULED(0, "Contract ended as scheduled"),
    CONTRACT_ENDED_EARLY_PROJECT_END(1, "Contract ended early: project end"),
    CONTRACT_ENDED_EARLY_POOR_PERFORMANCE(2, "Contract ended early: poor performance"),
    CONVERTED_TO_FTE(3, "Converted to FTE"),
    CONVERTED_TO_VENDOR(4, "Converted to vendor"),
    EMPLOYEE_RESIGNED_TOOK_ANOTHER_JOB_OFFER(5, "Employee resigned: took another job offer"),
    EMPLOYEE_RESIGNED_OTHER_REASON(6, "Employee resigned: other reason"),
    QUIT_WITHOUT_GIVING_PROPER_NOTICE(7, "Quit without giving proper notice"),
    CONTRACT_ENDED_EARLY_UNEXPECTED(8, "Contract Terminated Early:Unexpected"),

    CONTRACT_ENDED_AS_SCHEDULED_NO_RENEWAL(9, "Contract Ended as Scheduled - No Renewal"),
    CANDIDATE_RESIGNED(10, "Candidate Resigned"),
    TERMINATION_CONTRACT_ENDED_EARLY(11, "Termination - Contract Ended Early"),
    OTHERS(12, "Others");

    private final int dbValue;

    private final String name;
    StartTerminationReason(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<StartTerminationReason, Integer> resolver =
            new ReverseEnumResolver<>(StartTerminationReason.class, StartTerminationReason::toDbValue);

    public static StartTerminationReason fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
