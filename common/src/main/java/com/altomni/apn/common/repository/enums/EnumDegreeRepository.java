package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.dict.EnumDegree;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the Enum job functions entity.
 */
@Repository
public interface EnumDegreeRepository extends JpaRepository<EnumDegree, Long>, QuerydslPredicateExecutor<EnumDegree> {

    List<EnumDegree> findAllByOrderByScoreDesc();

}
