package com.altomni.apn.common.domain.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class RelateJobFolderStatusConverter extends AbstractAttributeConverter<RelateJobFolderStatus, Integer> {
    public RelateJobFolderStatusConverter() {
        super(RelateJobFolderStatus::toDbValue, RelateJobFolderStatus::fromDbValue);
    }
}