package com.altomni.apn.common.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractType enumeration.
 */
public enum EventType implements ConvertedEnum<Integer> {
    APN_INJECTION_FAILURE(0, 0), //APN relay request to SparkPost server failed.
    APN_INJECTION_SUCCESS(1, 1), //APN relay request to SparkPost server succeed.
    BOUNCE(2, 32), //Recipient mailbox provider permanently rejected the email.
    DELIVERY(3 ,30), //Recipient mailbox provider acknowledged receipt of email.
    INJECTION(4, 10), //Email successfully generated or relayed to SparkPost.
    SPAM_COMPLAINT(5, 60), //Recipient marked the email as spam.
    OUT_OF_BAND(6, 31), // Recipient mailbox provider rejected the email after initially accepting it.
    POLICY_REJECTION(7, 13), //SparkPost rejected the email due to a policy reason.
    DELAY(8, 20), //Recipient mailbox provider temporarily rejected the email.
    CLICK(9, 50), //Recipient clicked a tracked link in the email.
    OPEN(10, 41), //Recipient opened the email. Recorded by a tracking pixel at the bottom of the email.
    INITIAL_OPEN(11, 40), //Recipient opened the email. Recorded by a tracking pixel at the top of the email.
    AMP_CLICK(12, 51),
    AMP_OPEN(13, 43),
    AMP_INITIAL_OPEN(14, 42),
    GENERATION_FAILURE(15, 11), //Email generation failed due to a technical reason.
    GENERATION_REJECTION(16, 12), //Email generation failed due to a policy reason.
    LIST_UNSUBSCRIBE(17, 61), //Recipient unsubscribed using a mailbox provider’s list unsubscribe feature.
    LINK_UNSUBSCRIBE(18, 62), //User clicked a tagged unsubscribe link.

    SOFT_BOUNCE(21, 33),
    BLOCK_BOUNCE(22, 34),
    ADMIN_BOUNCE(23, 35),
    UNDETERMINED_BOUNCE(24, 36),
    HARD_BOUNCE(25, 37);

    private final Integer dbValue;

    private final Integer priority;

    EventType(Integer dbValue, Integer priority) {
        this.dbValue = dbValue;
        this.priority = priority;
    }

    @Override
    public Integer toDbValue() { return dbValue; }

    public Integer getPriority() { return priority; }

    // static resolving:
    public static final ReverseEnumResolver<EventType, Integer> resolver =
        new ReverseEnumResolver<>(EventType.class, EventType::toDbValue);

    public static EventType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
