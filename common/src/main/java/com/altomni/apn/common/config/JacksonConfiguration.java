package com.altomni.apn.common.config;

import com.fasterxml.jackson.datatype.hibernate5.Hibernate5Module;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JacksonConfiguration {

    /*
     * Support for Hibernate types in Jackson.
     */
    @Bean
    public Hibernate5Module hibernate5Module() {
        Hibernate5Module hibernate5Module = new Hibernate5Module();
        hibernate5Module.configure(Hibernate5Module.Feature.FORCE_LAZY_LOADING, true)
                .configure(Hibernate5Module.Feature.REPLACE_PERSISTENT_COLLECTIONS, true)
                .configure(Hibernate5Module.Feature.USE_TRANSIENT_ANNOTATION, false);

        return hibernate5Module;
    }
}
