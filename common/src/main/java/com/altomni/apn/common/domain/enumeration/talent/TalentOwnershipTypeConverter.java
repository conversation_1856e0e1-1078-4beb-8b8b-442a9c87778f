package com.altomni.apn.common.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TalentOwnershipTypeConverter extends AbstractAttributeConverter<TalentOwnershipType, Integer> {
    public TalentOwnershipTypeConverter() {
        super(TalentOwnershipType::toDbValue, TalentOwnershipType::fromDbValue);
    }
}
