package com.altomni.apn.common.repository.enums;

import com.altomni.apn.common.domain.talent.TelephoneChatScript;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface TelephoneChatScriptRepository extends JpaRepository<TelephoneChatScript, Long> {
    List<TelephoneChatScript> findTelephoneChatScriptsByLanguage(Integer language);
}