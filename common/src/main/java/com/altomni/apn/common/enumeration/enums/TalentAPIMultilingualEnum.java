package com.altomni.apn.common.enumeration.enums;

/**
 * 多语言枚举类信息 talent-service
 * <AUTHOR>
 */
public enum TalentAPIMultilingualEnum {

    ELASTIC_GETTALENTCATEGORYCOUNT_RESPONSENULL("elastic_getTalentCategoryCount_responseNull"),

    ELASTIC_GETTALENTCATEGORYCOUNT_RESPONSECODEERROR("elastic_getTalentCategoryCount_responseCodeError"),

    ELASTIC_SEARCHFROMCOMMONPOOL_RESPONSENULL("elastic_searchFromCommonPool_responseNull"),

    ELASTIC_UPDATETALENTSFOLDER_INTERNALERROR("elastic_updateTalentsFolder_internalError"),

    EVENT_CREATE_IDNOTNULL("event_create_idNotNull"),

    EVENT_UPDATE_IDNULL("event_update_idNull"),

    EVENT_UPDATE_EVENTNOTEXIST("event_update_eventNotExist"),

    EVENTUSER_CREATE_IDNOTNULL("eventUser_create_idNotNull"),

    EVENTUSER_CREATE_EVENTIDNULL("eventUser_create_eventIdNull"),

    FOLDER_SETSEARCHFOLDERSQL_TYPEERROR("folder_setSearchFolderSql_typeError"),

    FOLDER_TALENTCUSTOM_COMMON_PARAMNULL("folder_talentCustom_common_paramNull"),

    FOLDER_TALENTCUSTOM_CREATETALENTFOLDER_INVALIDFOLDERNAME("folder_talentCustom_createTalentFolder_invalidFolderName"),

    FOLDER_TALENTCUSTOM_DELETEFOLDER_INVALIDFOLDER("folder_talentCustom_deleteFolder_invalidFolder"),

    FOLDER_TALENTCUSTOM_DELETEFOLDER_NOPERMISSION("folder_talentCustom_deleteFolder_noPermission"),

    FOLDER_TALENTCUSTOM_UPDATEFOLDERSHARINGUSER_NOPERMISSION("folder_talentCustom_updateFolderSharingUser_noPermission"),

    FOLDER_TALENTCUSTOM_ADDTALENTFOLDERSHARINGFORTEAMS_NOPERMISSION("folder_talentCustom_addTalentFolderSharingForTeams_noPermission"),

    FOLDER_TALENTCUSTOM_REMOVESHARINGFORSHAREDFOLDER_NOSHARE("folder_talentCustom_removeSharingForSharedFolder_noShare"),

    FOLDER_TALENTCUSTOM_GETCOLLABORATIVETALENTFOLDERLIST_FAILFETCH("folder_talentCustom_getCollaborativeTalentFolderList_failFetch"),

    FOLDER_TALENTCUSTOM_GETCOLLABORATIVETALENTFOLDERLIST_FAILSHAREFETCH("folder_talentCustom_getCollaborativeTalentFolderList_failShareFetch"),

    FOLDER_TALENTCUSTOM_GETCUSTOMANDSHAREDJOBFOLDERWITHPERMISSIONLIST_NOPERMISSIO("folder_talentCustom_getCustomAndSharedJobFolderWithPermissionList_noPermissio"),

    FOLDER_TALENTCUSTOM_GETALLTALENTSEMAILINFOLDERS_PARAMNULL("folder_talentCustom_getAllTalentsEmailInFolders_paramNull"),

    FOLDER_TALENTCUSTOM_GETALLTALENTSEMAILINFOLDERS_MAXCOUNTLIMIT("folder_talentCustom_getAllTalentsEmailInFolders_maxCountLimit"),

    FOLDER_TALENTCUSTOM_GETTALENTFOLDERBYID_FOLDERNOTEXIST("folder_talentCustom_getTalentFolderById_folderNotExist"),

    FOLDER_TALENTCUSTOM_GETCREATORNAME_RESPONSENOOK("folder_talentCustom_getCreatorName_responseNoOk"),

    FOLDER_TALENTCUSTOM_GETVALIDATETALENTFOLDERBEFOREUPDATE_NAMEEXIST("folder_talentCustom_getValidateTalentFolderBeforeUpdate_nameExist"),

    FOLDER_TALENTCUSTOM_CHECKRWPERMISSIONONSHAREDFOLDERS_NOPERMISSION("folder_talentCustom_checkRWPermissionOnSharedFolders_noPermission"),

    FOLDER_TALENTCUSTOM_VALIDATETALENTS_INVALIDTALENTS("folder_talentCustom_validateTalents_invalidTalents"),

    FOLDER_TALENTCUSTOM_CHECKALLTALENTSUNDERCURRENTFOLDER_NOTINFOLDER("folder_talentCustom_checkAllTalentsUnderCurrentFolder_notInFolder"),

    FOLDER_TALENTSEARCH_CREATETALENTSEARCHFOLDER_ALREADYEXIST("folder_talentSearch_createTalentSearchFolder_alreadyExist"),

    FOLDER_TALENTSEARCH_VALIDATECUSTOMFOLDERASSEARCHFOLDERPARAM_NOPERMISSION("folder_talentSearch_validateCustomFolderAsSearchFolderParam_noPermission"),

    FOLDER_TALENTSEARCH_GETSEARCHFOLDERBYID_NOTEXIST("folder_talentSearch_getSearchFolderById_notExist"),

    HOTLIST_CREATEHOTLIST_ALREADYID("hotlist_createHotList_alreadyId"),

    HOTLIST_VALIDATETITLE_TITLENULL("hotlist_validateTitle_titleNull"),

    HOTLIST_VALIDATETITLE_ALREADYEXIST("hotlist_validateTitle_alreadyExist"),

    HOTLIST_CHECKHOTLISTIFEXISTS_NOTEXIST("hotlist_checkHotListIfExists_notExist"),

    HOTLIST_CAPPENDHOTLISTTALENTSBYIDS_TALENTIDSNULL("hotlist_cappendHotListTalentsByIds_talentIdsNull"),

    HOTLIST_VALIDATEHOTLIST_PARAMNULL("hotlist_validateHotList_paramNull"),

    HOTLIST_CREATEHOTLISTUSER_ALREADYID("hotlist_createHotListUser_alreadyId"),

    HOTLIST_CREATEHOTLISTUSER_USERNOTEXIST("hotlist_createHotListUser_userNotExist"),

    HOTLIST_CREATEHOTLISTUSER_USEREXIST("hotlist_createHotListUser_userExist"),

    HOTLIST_REPLACEHOTLISTUSERS_PUBLICSTATUS("hotlist_replaceHotListUsers_publicStatus"),

    LINKED_VERIFYTENANT_NOPERMISSION("linked_verifyTenant_noPermission"),

    LINKED_FINDONE_NOTFOUND("linked_findOne_notfound"),

    LINKED_VERIFYLINKEDINPROJECTTALENT_LINKEDINPROJECTIDNULL("linked_verifyLinkedinProjectTalent_linkedinProjectIdNull"),

    LINKED_REPLACE_LINKEDINTALENTCONTACTSNULL("linked_replace_linkedinTalentContactsNull"),

    LINKED_REPLACE_LINKEDINTALENTNULL("linked_replace_linkedinTalentNull"),

    LINKED_UPDATE_LINKEDINTALENTNOTEXIST("linked_update_linkedinTalentNotExist"),

    RECORD_CHECKINLIST_CONDITIONLIMIT("record_checkInList_conditionLimit"),

    RECORD_SYNCPROTALENTTRACKINGNOTETOAPNTALENTNOTE_TALENTNULL("record_syncProTalentTrackingNoteToApnTalentNote_talentNull"),

    RECORD_FINDALLTALENTTRACKINGRECORDS_CONTACTNULL("record_findAllTalentTrackingRecords_contactNull"),

    RECORD_SENDRICHEMAIL_USERNULL("record_sendRichEmail_userNull"),

    TALENT_CHECKTAGEXISTS_TAGIDNULL("talent_checkTagExists_tagIdNull"),

    TALENT_VALIDATE_TAGNAMENULL("talent_validate_tagNameNull"),

    TALENT_CREATESPECIALTAG_IDNULL("talent_createSpecialTag_idNull"),

    TALENT_CREATESPECIALTAG_SPECIALTAGNULL("talent_createSpecialTag_specialTagNull"),

    TALENT_CREATESPECIALTAG_NOPERMISSION("talent_createSpecialTag_noPermission"),

    TALENT_CREATE_TALENTCONTACTNULL("talent_create_talentContactNull"),

    TALENT_CREATE_TALENTCONTACTIDNULL("talent_create_talentContactIdNull"),

    TALENT_CREATE_TALENTNULL("talent_create_talentNull"),

    TALENT_COMMON_NOPERMISSION("talent_common_noPermission"),

    TALENT_UPDATE_TALENTCONTACTNOTEXIST("talent_update_talentContactNotExist"),

    TALENTOWNERSHIP_CREATE_USERIDNULL("talentOwnership_create_userIdNull"),

    TALENTOWNERSHIP_REPLACE_USERIDNULL("talentOwnership_replace_userIdNull"),

    TALENTRESUME_CREATETALENTRESUME_UUIDNULL("talentResume_createTalentResume_uuidNull"),

    TALENTRESUME_FINDONE_RESUMENULL("talentResume_findOne_resumeNull"),

    TALENT_GETRESUMEPARSERESPONSE_PARSERRESUMEDATANULL("talent_getResumeParseResponse_parserResumeDataNull"),

    TALENT_CREATE_DUPLICATETALENTDATA("talent_create_duplicateTalentData"),

    TALENT_CREATE_NOTFOUNDERROR("talent_create_notFoundError"),

    TALENT_SETTALENTRELATEDATA_CONTACTSNULL("talent_setTalentRelateData_contactsNull"),

    TALENT_UPDATE_CANNOTOWNERANDSHARE("talent_update_cannotOwnerAndShare"),

    TALENT_CHECKSHARESPERMISSION_NOPERMISSION("talent_checkSharesPermission_noPermission"),

    TALENT_GETTALENTISCLIENTCONTACT_LISTRESPONSEENTITYNULL("talent_getTalentIsClientContact_listResponseEntityNull"),

    TALENT_GETTALENTSBYCONTACTS_CONTACTINFONULL("talent_getTalentsByContacts_contactInfoNull"),

    TALENT_SEARCHTALENTEMAILCONTACTS_PARAMLISTNULL("talent_searchTalentEmailContacts_paramListNull"),

    TALENT_SEARCHTALENTEMAILCONTACTS_TALENTLISTNULL("talent_searchTalentEmailContacts_talentListNull"),

    TALENT_SEARCHTALENTSBYCONTACTANDSIMILARITY_CONTACTNULL("talent_searchTalentsByContactAndSimilarity_contactNull"),

    TALENT_GETRECOMMENDEDREASON_JOBNULL("talent_getRecommendedReason_jobNull"),

    TALENT_GETRECOMMENDEDREASON_TEMPLATENULL("talent_getRecommendedReason_templateNull"),

    TALENT_GETRECOMMENDEDREASON_TALENTNULL("talent_getRecommendedReason_talentNull"),

    TALENT_GETRECOMMENDATIONREPORT_USERNULL("talent_getRecommendationReport_userNull"),

    TALENT_GETRECOMMENDATIONREPORT_ERROR("talent_getRecommendationReport_error"),

    TALENT_REQUESTRECOMMENDEDREASON_ERROR("talent_requestRecommendedReason_error"),

    TALENT_GETREQUESTRECOMMENDEDREASONPARAM_PARAMNULLVALUE("talent_getRequestRecommendedReasonParam_paramNullValue"),

    TALENT_FINDTALENTPROGRESSBYTASKIDS_PARAMNULL("talent_findTalentProgressByTaskIds_paramNull"),

    TALENT_COMMON_INTERNALERROR("talent_common_internalError"),

    TALENT_UPDATETASKSTATUS_TASKFINISH("talent_updateTaskStatus_taskFinish"),

    TALENT_DOWNLOADCREATETALENTRESULTBYEXCEL_CLOUDFILEOBJECTMETADATANULL("talent_downloadCreateTalentResultByExcel_cloudFileObjectMetadataNull"),

    TALENT_DOWNLOADCREATETALENTRESULTBYEXCEL_NOTFINISH("talent_downloadCreateTalentResultByExcel_notFinish"),

    TALENT_DOWNLOADCREATETALENTRESULTBYEXCEL_DATAEXPIRED("talent_downloadCreateTalentResultByExcel_dataExpired"),

    TALENT_SEARCHTALENTFROMES_INTERFACEERROR("talent_searchTalentFromES_interfaceError"),

    TALENT_CHECKFOLDERSPERMISSION_FOLDERNOTEXIST("talent_checkFoldersPermission_folderNotExist"),

    TALENT_SEARCHTALENTSOURCEFROMES_INPUTEMPTY("talent_searchTalentSourceFromES_inputEmpty"),

    LINKED_SAVELINKEDINPROJECT_IDEMPTY("linked_saveLinkedinProject_idEmpty"),

    LINKED_CREATE_IDEXIST("linked_create_idExist"),

    RATER_RECOMMENDJOBSFORCOMMONTALENT_ESIDEXIST("rater_recommendJobsForCommonTalent_esIdExist"),

    TALENT_CREATEANDUPDATECREDITTRANSACTION_TALENTIDEXIST("talent_createAndUpdateCreditTransaction_talentIdExist"),

    FOLDER_TALENTSEARCH_FOLDERIDNULL("folder_talentSearch_folderIdNull"),

    TALENT_NO_PERMISSION_VISIT_CLIENT_CONTACT("talent_no_permission_visit_client_contact"),

    TALENT_DODAX_ID_REPEAT("talent_dodax_id_repeat"),

    CONFIDENTIAL_RULE_DUPLICATE("confidential_rule_duplicate"),

    TALENT_NOT_MATCH_CONFIDENTIAL_RULE("talent_not_match_confidential_rule"),

    TALENT_AUTO_DECLASSIFY("talent_auto_declassify")
    ;

    private final String key;

    TalentAPIMultilingualEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

}