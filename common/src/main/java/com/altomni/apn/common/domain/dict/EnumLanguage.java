package com.altomni.apn.common.domain.dict;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@ApiModel(description = "Enum job functions entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "enum_language")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumLanguage implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "cn_display")
    private String cnDisplay;

    @Column(name = "en_display")
    private String enDisplay;

    @Column(name = "en_display_order")
    private Long enDisplayOrder;

    @Column(name = "cn_display_order")
    private Long cnDisplayOrder;

    @Transient
    private Boolean cnLable = false;

    public static Set<EnumLanguage> transfer(List<Long> body) {
        if (body == null) {
            return null;
        } else {
            return body.stream().map(dictCode -> {
                EnumLanguage language = new EnumLanguage();
                language.setId(dictCode);
                return language;
            }).collect(Collectors.toSet());
        }
    }

    public static List<Long> convert(Set<EnumLanguage> body) {
        if (body == null) {
            return null;
        } else {
            return body.stream().map(EnumLanguage::getId
            ).collect(Collectors.toList());
        }
    }

}
