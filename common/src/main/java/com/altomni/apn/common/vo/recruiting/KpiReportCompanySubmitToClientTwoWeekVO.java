package com.altomni.apn.common.vo.recruiting;

import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatusConverter;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.Convert;
import java.time.Instant;
import java.util.List;

@Data
public class KpiReportCompanySubmitToClientTwoWeekVO{

    private Long companyId;

    private String companyName;

    private Long teamId;

    private String teamName;

    private Long jobId;

    private String jobTitle;

    private String thisWeek;

    private String lastWeek;

    private Long thisWeekCount;

    private Long lastWeekCount;

    private Long thisWeekCountAiRecommendNum;

    private Long lastWeekCountAiRecommendNum;

    private Long lastCurrentCountNumAiRecommendNum;

    private Long thisCurrentCountNumAiRecommendNum;

    private Long thisWeekCountPrecisionAiRecommendNum;

    private Long lastWeekCountPrecisionAiRecommendNum;

    private Long lastCurrentCountNumPrecisionAiRecommendNum;

    private Long thisCurrentCountNumPrecisionAiRecommendNum;

    private Long lastCurrentCountNum;

    private Long thisCurrentCountNum;

    private Instant companyCreatedDate;

    private Instant requestDate;

    @Convert(converter = AccountCompanyStatusConverter.class)
    private AccountCompanyStatus active;

    private String industries;

    private String country;

    private Long userId;

    private String username;

    private Long tenantId;

    @JsonIgnore
    private Long companyNoteCount;

    List<KpiReportCompanySubmitToClientTwoWeekBusinessInfoVO> businessInfo;
}
