package com.altomni.apn.common.auth.timesheet_auth;

import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.nimbusds.jose.JOSEException;

import java.text.ParseException;

public interface TimesheetUserTokenStore {

    String generateToken(TimeSheetUser user, Integer expire) throws J<PERSON>EEx<PERSON>, ParseException;

    TimeSheetUser getUser(String token) throws ParseException;

    boolean tokenExist(String token);

    boolean removeToken(String userUid);
}
