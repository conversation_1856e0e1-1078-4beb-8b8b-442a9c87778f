package com.altomni.apn.common.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentCollegeInfoDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    private String englishCollegeName;

    private String chineseCollegeName;

    @JsonProperty("QS2021Rank")
    private Integer QS2021Rank;
    @JsonProperty("QSRank")
    private Integer QSRank;
    private List<String> categories;
}


