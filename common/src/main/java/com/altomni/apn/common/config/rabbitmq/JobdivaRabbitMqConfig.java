package com.altomni.apn.common.config.rabbitmq;

import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.SerializerMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class JobdivaRabbitMqConfig {

    @Resource
    private JobdivaRabbitProperties jobdivaRabbitProperties;

    public CachingConnectionFactory connectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        return connectionFactory;
    }

    @Bean(name = "jobdivaFactory")
    public SimpleRabbitListenerContainerFactory jobdivaFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("jobdivaConnectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, connectionFactory);
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);
        factory.setPrefetchCount(10);
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "jobdivaConnectionFactory")
    public ConnectionFactory jobdivaConnectionFactory() {
        return connectionFactory (jobdivaRabbitProperties.getHost(), jobdivaRabbitProperties.getPort(), jobdivaRabbitProperties.getVirtualHost(), jobdivaRabbitProperties.getUserName(), jobdivaRabbitProperties.getPassword());
    }

    @Bean(name = "jobdivaRabbitTemplate")
    public RabbitTemplate jobdivaRabbitTemplate(@Qualifier("jobdivaConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate jobdivaRabbitTemplate = new RabbitTemplate(connectionFactory);
        jobdivaRabbitTemplate.setMessageConverter(new SerializerMessageConverter());
        return jobdivaRabbitTemplate;
    }

    @Bean(name = "jobdivaAmqpAdmin")
    public AmqpAdmin esfillerAmqpAdmin(@Qualifier("jobdivaConnectionFactory") ConnectionFactory connectionFactory) {
        AmqpAdmin amqpAdmin = new RabbitAdmin(connectionFactory);
        return amqpAdmin;
    }


}
