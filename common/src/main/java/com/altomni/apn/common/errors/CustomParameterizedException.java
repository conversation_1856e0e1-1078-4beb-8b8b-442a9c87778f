package com.altomni.apn.common.errors;

import com.altomni.apn.common.config.constants.ExceptionTypeConstants;
import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;

import java.util.HashMap;
import java.util.Map;

import static org.zalando.problem.Status.BAD_REQUEST;

/**
 * Custom, parameterized exception, which can be translated on the client side.
 * For example:
 *
 * <pre>
 * throw new CustomParameterizedException(&quot;myCustomError&quot;, &quot;hello&quot;, &quot;world&quot;);
 * </pre>
 *
 * Can be translated with:
 *
 * <pre>
 * "error.myCustomError" :  "The server says {{param0}} to {{param1}}"
 * </pre>
 */
public class CustomParameterizedException extends AbstractThrowableProblem {

    private static final long serialVersionUID = 1L;

    private static final String PARAM = "param";

    private String message;

    private Integer internalErrorClassCode;

    public CustomParameterizedException(String message, String... params) {
        this(message, toParamMap(params));
    }

    public CustomParameterizedException(Integer statusCode, String title, String message, Map<String, Object> paramMap) {
        super(ErrorConstants.PARAMETERIZED_TYPE, title, Status.valueOf(statusCode), message, null, null, toProblemParameters(message, paramMap));
        this.message = message;
        this.internalErrorClassCode = ExceptionTypeConstants.EXCEPTION_CUSTOM_PARAMETERIZED_EXCEPTION;
    }

    public CustomParameterizedException(Integer statusCode, String title, String message) {
        super(ErrorConstants.PARAMETERIZED_TYPE, title, Status.valueOf(statusCode), message, null, null, null);
        this.message = message;
        this.internalErrorClassCode = ExceptionTypeConstants.EXCEPTION_CUSTOM_PARAMETERIZED_EXCEPTION;
    }

    public CustomParameterizedException(String message, Map<String, Object> paramMap) {
        super(ErrorConstants.PARAMETERIZED_TYPE, "Parameterized Exception", BAD_REQUEST, message, null, null, toProblemParameters(message, paramMap));
        this.message = message;
        this.internalErrorClassCode = ExceptionTypeConstants.EXCEPTION_CUSTOM_PARAMETERIZED_EXCEPTION;
    }

    public static Map<String, Object> toParamMap(String... params) {
        Map<String, Object> paramMap = new HashMap<>();
        if (params != null && params.length > 0) {
            for (int i = 0; i < params.length; i++) {
                paramMap.put(PARAM + i, params[i]);
            }
        }
        return paramMap;
    }

    public static Map<String, Object> toProblemParameters(String message, Map<String, Object> paramMap) {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("message", message);
        parameters.put("params", paramMap);
        return parameters;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public Integer getInternalErrorClassCode() {
        return internalErrorClassCode;
    }
}