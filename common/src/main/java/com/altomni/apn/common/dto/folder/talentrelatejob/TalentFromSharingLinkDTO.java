package com.altomni.apn.common.dto.folder.talentrelatejob;

import com.altomni.apn.common.dto.talent.TalentInfoInput;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;


@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentFromSharingLinkDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String sharedLinkUUID;

    private TalentInfoInput talent;


}
