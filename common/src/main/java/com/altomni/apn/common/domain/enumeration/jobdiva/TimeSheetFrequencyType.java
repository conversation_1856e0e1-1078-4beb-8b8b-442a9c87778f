package com.altomni.apn.common.domain.enumeration.jobdiva;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum TimeSheetFrequencyType implements ConvertedEnum<Integer> {
    WEEKLY(0, "Weekly"),
    BI_WEEKLY(1, "Biweekly"),
    SEMI_MONTHLY(2, "Semi-monthly"),
    MONTHLY(3, "Monthly"),
    QUARTERLY(4, "Quarterly");

    private final int dbValue;

    private final String description;

    TimeSheetFrequencyType(int dbValue,String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TimeSheetFrequencyType, Integer> resolver = new ReverseEnumResolver<>(TimeSheetFrequencyType.class, TimeSheetFrequencyType::toDbValue);

    public static TimeSheetFrequencyType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
