package com.altomni.apn.common.aop.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = TodayOrFutureValidator.class)
public @interface TodayOrFuture {

    String message() default "The delivery date must be today or in the future";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

}
