package com.altomni.apn.common.service.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.altomni.apn.common.domain.dict.EnumLanguage;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.DisplayType;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EnumDegreeService {

    private final Logger log = LoggerFactory.getLogger(EnumDegreeService.class);

    @Resource
    private EnumCommonService enumCommonService;

    public List<EnumDictDTO> findAllOrderBySortType(SortType type) {
        log.info("[APN: EnumDegreeService @{}] request to get degrees enum data, type: {}", SecurityUtils.getUserId(), type);

        List<EnumDegree> result = enumCommonService.findAllEnumDegree();
        if (CollectionUtil.isNotEmpty(result)) {
            if (SortType.EN.equals(type)){
                result.forEach(r -> r.setCnLable(false));
            }else{
                result.forEach(r -> r.setCnLable(true));
            }
            //format EnumDictDTO for webSide
            return result.stream().filter(s -> StringUtils.isNotBlank(s.getEnDisplay())).map(EnumDictDTO::fromBizDict).collect(Collectors.toList());
        } else {
            return new ArrayList<>();
        }
    }

    public List<String> transferDegreesByIds(List<String> ids) {
        log.info("[APN: EnumDegreeService @{}] request to transfer degrees by ids, ids: {}", SecurityUtils.getUserId(), ids);

        if (CollectionUtil.isEmpty(ids)) {
            return null;
        }

        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();
        List<EnumDegree> list = enumDegreeList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).collect(Collectors.toList());
        List<String> result = new ArrayList<>();
        for (EnumDegree bd : list) {
            if (ObjectUtil.isNotEmpty(bd.getName())) {
                if (bd.getName().contains(StrUtil.DOT)) {
                    result.add(bd.getName());
                    result.add(StrUtil.subPre(bd.getName(), bd.getName().indexOf(StrUtil.DOT)));
                } else {
                    result.add(bd.getName());
                }
            }
        }
        return result.stream().distinct().collect(Collectors.toList());
    }

    public List<String> getDegreesByIds(List<String> ids) {
        if (CollUtil.isEmpty(ids)) {
            return null;
        }

        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();
        return enumDegreeList.stream().filter(o -> new HashSet<>(ids).contains(String.valueOf(o.getId()))).map(EnumDegree::getName).collect(Collectors.toList());
    }

    public Map<Long, String> getDegreeMap(){
        return enumCommonService.findAllEnumDegree().stream().collect(Collectors.toMap(EnumDegree::getId, EnumDegree::getName));
    }

    public List<Long> transferDegreesByNamesToId(Object nameObject) {
        log.info("[APN: EnumDegreeService @{}] request to transfer degrees by itemTier, nameObject: {}", SecurityUtils.getUserId(), nameObject);

        if (ObjectUtil.isEmpty(nameObject)) {
            return null;
        }

        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();
        List<EnumDegree> list = new ArrayList<>();
        if (nameObject instanceof String) {
            list = enumDegreeList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && o.getName().equals(ObjectUtil.toString(nameObject))).collect(Collectors.toList());
        }
        if (nameObject instanceof List || nameObject instanceof String[]) {
            list = enumDegreeList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && Convert.toSet(String.class, nameObject).contains(o.getName())).collect(Collectors.toList());
        }
        return list.stream().map(EnumDegree::getId).collect(Collectors.toList());
    }

    public List<Long> transferDegreesByNamesToIdWithoutIgnoreParentClass(cn.hutool.json.JSONArray names) {
        if (ObjectUtil.isEmpty(names)) {
            return null;
        }

        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();
        Set<String> formatList = Convert.toSet(String.class, names);
        List<EnumDegree> list = enumDegreeList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && formatList.contains(o.getName())).collect(Collectors.toList());
        return list.stream().map(EnumDegree::getId).collect(Collectors.toList());
    }

    public List<EnumDegree> transferDegreesByNamesToList(List<String> itemTiers) {
        if (ObjectUtil.isEmpty(itemTiers)) {
            return null;
        }

        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();
        List<String> itemList = Convert.toList(String.class, itemTiers);
        String itemJson = JSONUtil.toJsonStr(itemTiers);
        Set<String> formatList = itemList.stream().distinct().filter(s -> !itemJson.contains(s + StrUtil.DOT)).collect(Collectors.toSet());
        return enumDegreeList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && formatList.contains(o.getName())).collect(Collectors.toList());
    }

    public List<String> transferDegreesByNames(Object itemTiers) {
        if (ObjectUtil.isEmpty(itemTiers)) {
            return null;
        }

        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();
        List<EnumDegree> list = new ArrayList<>();
        if (itemTiers instanceof String) {
            list = enumDegreeList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && o.getName().equals(ObjectUtil.toString(itemTiers))).collect(Collectors.toList());
        }
        if (itemTiers instanceof List) {
            List<String> itemList = Convert.toList(String.class, itemTiers);
            String itemJson = JSONUtil.toJsonStr(itemTiers);
            Set<String> formatList = itemList.stream().distinct().filter(s -> !itemJson.contains(s + StrUtil.DOT)).collect(Collectors.toSet());
            list = enumDegreeList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && formatList.contains(o.getName())).collect(Collectors.toList());
        }
        return list.stream().map(EnumDegree::getEnDisplay).collect(Collectors.toList());
    }

    public Map<String, List<String>> transferDegreesByNamesToMap(List<String> itemTiers) {
        if (CollectionUtil.isEmpty(itemTiers)) {
            return new HashMap<>(16);
        }

        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();
        List<EnumDegree> list = enumDegreeList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getEnDisplay()) && new HashSet<>(itemTiers).contains(o.getName())).collect(Collectors.toList());
        return list.stream().collect(Collectors.groupingBy(EnumDegree::getName, Collectors.mapping(EnumDegree::getEnDisplay, Collectors.toList())));
    }

    public EnumDegree findEnumDegreeById(Long id) {
        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();
        Optional<EnumDegree> enumDegree = enumDegreeList.stream().filter(o -> o.getId().equals(id)).findFirst();
        if (enumDegree.isEmpty()) {
            throw new CustomParameterizedException("Enum Degree is null , id : " + id);
        }
        return enumDegree.get();
    }

    public Set<Long> findValidEnumDegreesIds() {
        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();
        return enumDegreeList.stream().map(EnumDegree::getId).collect(Collectors.toSet());
    }

    /***
     * Activity Change function: get the UI name through the enumId;
     * @param id
     * @param displayType
     * @return
     */
    public String getDegreeUINameById(Long id, DisplayType displayType) {
        log.info("[APN: EnumDegreeService @{}] request to get degree UINames by ids, nameObject: {}", SecurityUtils.getUserId(), id);

        List<EnumDegree> enumDegreeList = enumCommonService.findAllEnumDegree();

        Optional<EnumDegree> enumDegreeOpt = enumDegreeList.stream()
                .filter(enumDegree -> enumDegree.getId().equals(id))
                .findFirst();

        if(enumDegreeOpt.isPresent()) {
            EnumDegree enumDegree = enumDegreeOpt.get();
            if(displayType.equals(DisplayType.EN) && StringUtils.isNotBlank(enumDegree.getEnDisplay())) {
                return enumDegree.getEnDisplay();
            } else if (displayType.equals(DisplayType.CN) && StringUtils.isNotBlank(enumDegree.getCnDisplay())) {
                return enumDegree.getCnDisplay();
            } else {
                return enumDegree.getName();
            }
        }
        return "Unknown Degree";
    }

}
