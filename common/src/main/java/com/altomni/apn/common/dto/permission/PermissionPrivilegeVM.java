package com.altomni.apn.common.dto.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionPrivilegeVM implements Serializable{

    private String declaringClass;

    private List<Detail> details;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Detail implements Serializable {
        private String privilegeName;
        private String requestMethod;
        private String uri;
        private String api;
    }
}
