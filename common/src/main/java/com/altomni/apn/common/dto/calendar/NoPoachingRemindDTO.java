package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.enumeration.enums.NoPoachingRemindType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NoPoachingRemindDTO {

    private String url;

    private List<String> emails;

    private NoPoachingRemindType remindType;

    private Long companyId;

    private String companyName;

    private Long talentId;

    private String talentFullName;

    private Long jobId;

    private String jobTitle;

    private String submitUserFullName;

    private String talentDetailLink;


}
