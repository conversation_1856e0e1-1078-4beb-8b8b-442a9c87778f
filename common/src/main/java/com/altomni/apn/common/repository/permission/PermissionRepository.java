package com.altomni.apn.common.repository.permission;

import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.dto.permission.PermissionPrivilegeData;
import com.altomni.apn.common.dto.permission.PermissionPrivilegeDataSimple;
import com.altomni.apn.common.dto.permission.PermissionTableDTO;
import com.altomni.apn.common.dto.permission.PermissionTeamCodeDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * Spring Data JPA repository for the SimpleUser entity.
 */
@Repository
public interface PermissionRepository extends JpaRepository<SimpleUser, Long> {

    /**  Object Level Security   **/

    @Query(value = "SELECT DISTINCT p.id, p.name FROM permission_privilege p " +
            " INNER JOIN permission_role_privilege rp ON rp.privilege_id=p.id" +
            " INNER JOIN user_role ur ON ur.role_id= rp.role_id" +
            " INNER JOIN role r ON ur.role_id= r.id" +
            " WHERE ur.user_id =:userId and r.status=1", nativeQuery = true)
    List<PermissionPrivilegeDataSimple> findPrivilegeNamesByUserId(@Param("userId") Long userId);

    @Query(value = "SELECT p.id, p.name FROM permission_privilege p", nativeQuery = true)
    List<PermissionPrivilegeDataSimple> findAllPrivilegeNames();

    @Query(value = "SELECT p.id, p.name, p.parent_id as parentId FROM permission_privilege p " +
            " INNER JOIN permission_role_privilege rp ON rp.privilege_id=p.id" +
            " INNER JOIN user_role ur ON ur.role_id= rp.role_id" +
            " INNER JOIN role r ON ur.role_id= r.id" +
            " WHERE ur.user_id =:userId and r.status=1 order by level desc ",nativeQuery = true)
    List<PermissionPrivilegeData> findPrivilegesByUserId(@Param("userId") Long userId);

    @Query(value = "SELECT p.id, p.name, p.parent_id as parentId FROM permission_privilege p " +
            " order by level desc ",nativeQuery = true)
    List<PermissionPrivilegeData> findAllPrivileges();

    @Query(value = "SELECT p.api FROM permission_privilege p" +
            " WHERE p.is_public=1 and p.api is not null ",nativeQuery = true)
    Set<String> findPublicPrivilegeApis();

    @Query(value = "SELECT p.api FROM permission_privilege p " +
            " INNER JOIN permission_role_privilege rp ON rp.privilege_id = p.id" +
            " WHERE rp.role_id =:roleId", nativeQuery = true)
    Set<String> findPrivilegeApisByRoleId(@Param("roleId") Long roleId);

    @Query(value = "SELECT p.api FROM permission_privilege p " +
            " LEFT JOIN permission_role_privilege rp ON rp.privilege_id=p.id" +
            " LEFT JOIN user_role ur ON ur.role_id= rp.role_id" +
            " INNER JOIN role r ON ur.role_id= r.id" +
            " WHERE ur.user_id =:userId and r.status=1 and p.api is not null ",nativeQuery = true)
    Set<String> findPrivilegeApisByUserId(@Param("userId") Long userId);

    @Query(value = "SELECT p.api FROM permission_privilege p " +
            " WHERE p.api IS NOT NULL AND p.is_public = 0 ",nativeQuery = true)
    Set<String> findPrivilegeApisForAdminUser();

    @Query(value = "SELECT DISTINCT p.api FROM permission_privilege p " +
            " LEFT JOIN permission_role_privilege rp ON rp.privilege_id=p.id" +
            " LEFT JOIN user_admin_role ur ON ur.role_id= rp.role_id" +
            " WHERE ur.user_id =:userId and p.api is not null ",nativeQuery = true)
    Set<String> findPrivilegeApisByAdminUserId(@Param("userId") Long userId);

    @Query(value = "SELECT p.api FROM permission_privilege p " +
            " LEFT JOIN permission_impersonation_privilege rp ON rp.privilege_id=p.id" +
            " WHERE p.api is not null and (p.is_public=1 or rp.id is not null)",nativeQuery = true)
    Set<String> findAllImpersonationPrivilegeApis();

    /**  Data Level Security   **/

    @Query(value = "select max(r.data_scope) from role r" +
            " left join user_role on user_role.role_id=r.id" +
            " where user_role.user_id=:userId and r.status=1", nativeQuery = true)
    Integer getMaxDataScopeByUserId(@Param("userId") Long userId);

    @Query(value = "select max(r.client_contact_data_scope) from role r" +
            " left join user_role on user_role.role_id=r.id" +
            " where user_role.user_id=:userId and r.status=1", nativeQuery = true)
    Integer getMaxClientContactDataScopeByUserId(@Param("userId") Long userId);

    @Query(value = "select max(r.report_data_scope) from role r" +
            " left join user_role on user_role.role_id=r.id" +
            " where user_role.user_id=:userId and r.status=1", nativeQuery = true)
    Integer getMaxReportDataScopeByUserId(@Param("userId") Long userId);

    @Query(value = "select max(r.home_and_calendar_data_scope) from role r" +
            " left join user_role on user_role.role_id=r.id" +
            " where user_role.user_id=:userId and r.status=1", nativeQuery = true)
    Integer getMaxHomeAndCalendarDataScopeByUserId(@Param("userId") Long userId);

    @Query(value = "select max(r.china_invoicing_data_scope) from role r" +
            " left join user_role on user_role.role_id=r.id" +
            " where user_role.user_id=:userId and r.status=1", nativeQuery = true)
    Integer getMaxChinaInvoicingDataScopeByUserId(@Param("userId") Long userId);

    @Query(value = "select max(r.candidate_pipeline_management_data_scope) from role r" +
            " left join user_role on user_role.role_id=r.id" +
            " where user_role.user_id=:userId and r.status=1", nativeQuery = true)
    Integer getMaxCandidatePipelineManagementDataScopeByUserId(@Param("userId") Long userId);

    @Query(value = "select u.data_scope from user u where u.id=:userId", nativeQuery = true)
    Integer findUserDataScopeById(@Param("userId") Long userId);

    @Query(value = "select u.client_contact_data_scope from user u where u.id=:userId", nativeQuery = true)
    Integer findUserClientContactDataScopeById(@Param("userId") Long userId);

    @Query(value = "select u.report_data_scope from user u where u.id=:userId", nativeQuery = true)
    Integer findUserReportDataScopeById(@Param("userId") Long userId);

    @Query(value = "select u.home_and_calendar_data_scope from user u where u.id=:userId", nativeQuery = true)
    Integer findUserHomeAndCalendarDataScopeById(@Param("userId") Long userId);

    @Query(value = "select u.china_invoicing_data_scope from user u where u.id=:userId", nativeQuery = true)
    Integer findUserChinaInvoicingDataScopeById(@Param("userId") Long userId);

    @Query(value = "select u.candidate_pipeline_management_data_scope from user u where u.id=:userId", nativeQuery = true)
    Integer findUserCandidatePipelineManagementDataScopeById(@Param("userId") Long userId);

    @Query(value = "select ud.team_id from permission_user_team ud" +
            " where ud.user_id =:userId", nativeQuery = true)
    Set<Long> findTeamIdsByUserId(@Param("userId") Long userId);

    @Query(nativeQuery = true,
            value = "select team_id from permission_extra_role_team rd " +
                    " left join role r on r.id = rd.role_id" +
                    " left join user_role ur on ur.role_id=r.id" +
                    " where ur.user_id=:userId and r.status=1 and rd.module=:module")
    Set<Long> getAllTeamIdsFromExtraRoleByUserId(@Param("userId") Long userId, @Param("module") Integer module);

    @Query(nativeQuery = true,
            value = "select team_id from permission_extra_role_team rd " +
                    " left join role r on r.id = rd.role_id" +
                    " left join user_role ur on ur.role_id=r.id" +
                    " where ur.user_id=:userId and rd.writable=1 and r.status=1 and rd.module=:module")
    Set<Long> getWritableTeamIdsFromExtraRoleByUserId(@Param("userId") Long userId, @Param("module") Integer module);

    @Query(nativeQuery = true,
            value = "select team_id from permission_extra_user_team rd " +
                    " where rd.user_id=:userId and rd.module=:module")
    Set<Long> getAllTeamIdsFromExtraUserByUserId(@Param("userId") Long userId, @Param("module") Integer module);

    @Query(nativeQuery = true,
            value = "select team_id from permission_extra_user_team rd " +
                    " where rd.user_id=:userId and rd.writable=1 and rd.module=:module")
    Set<Long> getWritableTeamIdsFromExtraUserByUserId(@Param("userId") Long userId, @Param("module") Integer module);

    @Query(value = "select t.name, t.user_owner_column as userOwnerColumn, t.team_owner_column as teamOwnerColumn from permission_table t" +
            " left join permission_module_table mt on mt.table_id=t.id" +
            " left join permission_module m on m.id=mt.module_id" +
            " left join permission_tenant_module tm on tm.module_id=m.id" +
            " where tm.tenant_id=:tenantId and tm.involve_data_permission=true", nativeQuery = true)
    List<PermissionTableDTO> findInvolvedPermissionByTenantId(@Param("tenantId") Long tenantId);

    @Query(value = "SELECT mp.privilege_id FROM permission_module_privilege mp INNER JOIN permission_tenant_privilege tp ON mp.privilege_id = tp.privilege_id WHERE tp.tenant_id = ?1 " +
            "AND tp.is_show = 1 " +
            "UNION SELECT id privilege_id FROM permission_privilege WHERE id = 1 OR (parent_id = 1 AND id NOT IN (SELECT privilege_id FROM permission_module_privilege)) ORDER BY privilege_id ASC", nativeQuery = true)
    List<Long> findAllIdByTenantId(Long tenantId);

    @Query(value = "WITH RECURSIVE tree_path AS (\n" +
            "  SELECT id, name, parent_id, CAST(name AS CHAR(200)) AS path FROM permission_privilege WHERE id IN ?1\n" +
            "  UNION ALL\n" +
            "  SELECT t.id, t.name, t.parent_id, CONCAT(tp.path, ' -> ', t.name)\n" +
            "  FROM permission_privilege t\n" +
            "  JOIN tree_path tp ON t.parent_id = tp.id\n" +
            ")\n" +
            "SELECT id, name, parent_id, path, LENGTH(path) - LENGTH(REPLACE(path, '->', '')) AS level\n" +
            "FROM tree_path\n" +
            "GROUP BY id ORDER BY id ASC", nativeQuery = true)
    List<PermissionPrivilegeDataSimple> findPrivilegeNamesByTenantId(List<Long> privilegeIdList);

    @Query(value = "SELECT id, name FROM permission_privilege WHERE id = ?1", nativeQuery = true)
    PermissionPrivilegeDataSimple findPrivilegeNamesByPrivilegeId(Long privilegeId);

    @Query(value = "SELECT DISTINCT privilege_id FROM permission_module_privilege", nativeQuery = true)
    Set<Long> findAllConfigPrivilegeIds();

    @Query(value = "SELECT id FROM job_project where tenant_id =:tenantId", nativeQuery = true)
    List<Long> findAllJobProjectIdsByTenantId(@Param("tenantId") Long tenantId);

    @Query(value = "select 1 from permission_privilege p " +
            "inner join permission_role_privilege prp on p.id = prp.privilege_id " +
            "inner join role r on prp.role_id = r.id " +
            "inner join user_role ur on r.id = ur.role_id " +
            "where p.id=:privilegeIdForPrivateJob and ur.user_id=:userId", nativeQuery = true)
    List<Integer> existPrivateJobPermission(@Param("userId") Long userId, @Param("privilegeIdForPrivateJob") Long privilegeIdForPrivateJob);

    @Query(value = "SELECT code, level FROM permission_team WHERE id in :teamIds order by code", nativeQuery = true)
    List<PermissionTeamCodeDTO> findByTeamIds(@Param("teamIds") Set<Long> teamIds);

    @Query(value = "select t.id, t.code from permission_team t where tenant_id =:tenantId and deleted = 0", nativeQuery = true)
    List<PermissionTeamCodeDTO> findTeamsByTenantId(@Param("tenantId") Long tenantId);

    @Query(value = "select uj.job_id from user_job_relation uj where uj.user_id=:userId and uj.status = 1", nativeQuery = true)
    Set<Long> findJobIdsByAssignedUserId(@Param("userId") Long userId);
}
