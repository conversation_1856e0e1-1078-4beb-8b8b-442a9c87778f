package com.altomni.apn.common.dto.redis;

import com.altomni.apn.common.enumeration.ParseStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class ImagesInfoParser {

    private ParseStatus upload_status;

    private Integer has_portrait;

    private Integer has_display;

    private Integer n_pages;

}
