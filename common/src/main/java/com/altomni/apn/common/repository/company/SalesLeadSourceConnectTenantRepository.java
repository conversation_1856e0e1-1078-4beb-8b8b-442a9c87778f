package com.altomni.apn.common.repository.company;

import com.altomni.apn.common.domain.dict.CompanySalesLeadSourceConnectTenant;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface SalesLeadSourceConnectTenantRepository extends JpaRepository<CompanySalesLeadSourceConnectTenant, Long> {

    List<CompanySalesLeadSourceConnectTenant> findAllByTenantId(Long tenantId);

}
