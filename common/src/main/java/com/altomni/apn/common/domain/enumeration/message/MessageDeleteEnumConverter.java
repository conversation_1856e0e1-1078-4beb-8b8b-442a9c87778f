package com.altomni.apn.common.domain.enumeration.message;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class MessageDeleteEnumConverter extends AbstractAttributeConverter<MessageDeleteEnum, Integer> {
    public MessageDeleteEnumConverter() {
        super(MessageDeleteEnum::toDbValue, MessageDeleteEnum::fromDbValue);
    }
}