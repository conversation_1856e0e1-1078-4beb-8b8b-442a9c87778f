package com.altomni.apn.common.dto.management;

import com.altomni.apn.common.aop.validation.TodayOrFuture;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Future;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "Dto of management service tenant update status")
public class TenantUpdateStatusDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "tenant expireDate")
    @NotNull(message = "tenant expireDate is null")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @TodayOrFuture
    private LocalDate expireDate;

    public TenantUpdateStatusDTO() {
    }
}
