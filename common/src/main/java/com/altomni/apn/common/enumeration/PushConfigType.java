package com.altomni.apn.common.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum PushConfigType implements ConvertedEnum<Integer> {
    NOT_SUBMIT_TO_CLIENT(0,20),//24小时未推荐至客户/拒绝
    SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS(1,21),//推荐至客户后72小时未更新状态
    OFFER_PASS_NOT_UPDATE_STATUS(2,22),//已超过入职日期，流程状态未更新
    PAYMENT_OVERDUE(3,23),//回款逾期
    FOLLOW_UP_RECORD_UPDATE(4,24),//客户联系人跟进记录更新
    EXPECTED_ORDER_EXPIRATION(5,25),//预计成单到期日
    CONTRACT_NEARING_EXPIRATION(6,26),//合同即将到期
    CONTRACT_EXPIRED(7,27),//合同已逾期提醒
    CONTACT_JOB_CHANGE(8,28),//合同已逾期提醒
    NO_SUBMIT_TALENT(9,29),//未提交候选人,
    NO_INTERVIEW(10,30),//无面试记录
    USER_CUSTOM(100,null);


    private final int dbValue;
    private final Integer calendarTypeId;

    PushConfigType(int dbValue, Integer calendarTypeId) {
        this.dbValue = dbValue;
        this.calendarTypeId = calendarTypeId;
    }

    public Integer getCalendarTypeId() {
        return calendarTypeId;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<PushConfigType, Integer> resolver =
            new ReverseEnumResolver<>(PushConfigType.class, PushConfigType::toDbValue);

    public static PushConfigType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
