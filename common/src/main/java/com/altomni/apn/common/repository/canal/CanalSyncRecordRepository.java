package com.altomni.apn.common.repository.canal;

import com.altomni.apn.common.domain.canal.CanalSyncRecord;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

public interface CanalSyncRecordRepository extends JpaRepository<CanalSyncRecord, Long> {

    @Query(nativeQuery = true, value = " select csr.* from canal_sync_record csr where type = ?1 and retry_count <= ?3 order by priority desc,created_date asc  limit ?2 ")
    List<CanalSyncRecord> findAllByTypeOrderByCreatedDateAsc(Integer type, Integer limit, Integer failedCountLimit);

    List<CanalSyncRecord> findAllByTypeAndTaskIdIn(SyncIdTypeEnum type, Collection<Long> taskId);

    @Modifying
    @Transactional
    @Query(value = " delete from canal_sync_record where task_id = ?1 and type = ?2 ", nativeQuery = true)
    void deleteByTypeAndTaskId(Long taskId, Integer type);

}
