package com.altomni.apn.common.domain.enumeration.config;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;

public enum TenantMessageMinderConfigFieldCodeEnum implements ConvertedEnum<String> {

    /**
     * 职位未提交候选人天数
     */
    POSITION_UNSUBMITTED_CANDIDATE_DAYS("POSITION_UNSUBMITTED_CANDIDATE_DAYS", true, XxlJobRelationTypeEnum.JOB_NO_SUMMIT_CANDIDATE_WARN),

    /**
     * 职位未面试天数
     */
    POSITION_UNINTERVIEWED_DAYS("POSITION_UNINTERVIEWED_DAYS", true, XxlJobRelationTypeEnum.JOB_NO_INTERVIEW_WARN),

    /**
     * 候选人未入职提醒时间
     */
    CANDIDATE_NOT_ONBOARDED_REMINDER_TIME("CANDIDATE_NOT_ONBOARDED_REMINDER_TIME", true, XxlJobRelationTypeEnum.TALENT_NO_ONBOARD_WARN),

    /**
     * 候选人入职未开票提醒
     */
    CANDIDATE_ONBOARDING_INVOICE_REMINDER_TIME("CANDIDATE_ONBOARDING_INVOICE_REMINDER_TIME", true, XxlJobRelationTypeEnum.TALENT_ONBOARD_NO_INVOICE_WARN),

    /**
     * 逾期未回款
     */
    CANDIDATE_OVERDUE_PAYMENT_REMINDER_TIME("CANDIDATE_OVERDUE_PAYMENT_REMINDER_TIME", true, null),

    /**
     * 团队未提交候选人数量
     */
    TEAM_MEMBER_UNSUBMITTED_CANDIDATES_COUNT("TEAM_MEMBER_UNSUBMITTED_CANDIDATES_COUNT", false, XxlJobRelationTypeEnum.TEAM_UN_SUBMIT_TO_JOB_TALENT_COUNT_WARN),

    /**
     * 团队未提交候选人提醒时间
     */
    TEAM_MEMBER_UNSUBMITTED_CANDIDATE_REMINDER_TIME("TEAM_MEMBER_UNSUBMITTED_CANDIDATE_REMINDER_TIME", true, XxlJobRelationTypeEnum.TEAM_UN_SUBMIT_TO_JOB_TALENT_COUNT_WARN),

    /**
     * 团队逾期未入职天数
     */
    TEAM_MEMBER_OVERDUE_ONBOARDING_DAYS("TEAM_MEMBER_OVERDUE_ONBOARDING_DAYS", false, XxlJobRelationTypeEnum.TEAM_OVERDUE_ONBOARD_WARN);

    final private String desc;
    final boolean needUpdateXxlJob;

    final XxlJobRelationTypeEnum xxlJobType;

    TenantMessageMinderConfigFieldCodeEnum(String desc, boolean needUpdateXxlJob, XxlJobRelationTypeEnum xxlJobTypeEnum) {
        this.desc = desc;
        this.needUpdateXxlJob = needUpdateXxlJob;
        this.xxlJobType = xxlJobTypeEnum;
    }

    public static final ReverseEnumResolver<TenantMessageMinderConfigFieldCodeEnum, String> resolver = new ReverseEnumResolver<>(TenantMessageMinderConfigFieldCodeEnum.class, TenantMessageMinderConfigFieldCodeEnum::toDbValue);


    public static TenantMessageMinderConfigFieldCodeEnum fromDbValue(String dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public String toDbValue() {
        return desc;
    }

    public boolean isNeedUpdateXxlJob() {
        return needUpdateXxlJob;
    }

    public XxlJobRelationTypeEnum getXxlJobType() {
        return xxlJobType;
    }
}