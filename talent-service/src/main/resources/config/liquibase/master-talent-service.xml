<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <property name="now" value="now()" dbms="h2"/>
    <property name="now" value="now()" dbms="mysql, mariadb"/>
    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb"/>
    <property name="clobType" value="clob" dbms="h2"/>
    <property name="clobType" value="clob" dbms="mysql, oracle, mssql, mariadb, postgresql"/>
    <property name="uuidType" value="varchar(36)" dbms="h2, mysql, mariadb"/>
    <property name="datetimeType" value="datetime(6)" dbms="mysql, mariadb"/>
    <property name="datetimeType" value="datetime" dbms="oracle, mssql, postgresql, h2"/>

    <include file="config/liquibase/changelog/init/1666262757661_added_entity_Resume.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_Talent.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_TalentAdditionalInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_TalentContact.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_TalentCurrentLocation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_TalentIndustryRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_TalentJobFunctionRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentLanguageRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentNote.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_TalentResumeRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_TalentWorkAuthorizationRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20190109192243_added_entity_Event.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20190109192335_added_entity_EventUser.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20200530045441_added_entity_TalentTrackingRecord.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20200615160428_added_entity_TalentTrackingNote.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1668672803663_added_entity_WatchList.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1689542679123_added_entity_TalentFolder.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1689542679123_added_entity_TalentFolderRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1689542679123_added_entity_TalentFolderSharingTeam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1689542679123_added_entity_TalentFolderSharingUser.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1689542679123_added_entity_TalentSearchFolder.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/202311021627_added_entity_createUpdateTalentRecord.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/202311281004_added_entity_TalentAssociationJobFolderTalent.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/202311280813_added_entity_TalentAssociationJobFolder.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20240513100700_added_entity_TalentReviewNote.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20240513151600_added_entity_TalentSearchFolderSharingUser.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20240513152100_added_entity_TalentSearchFolderSharingTeam.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/init/20241129163600_added_entity_TalentSearchFolderOwnerUser.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/init/20241203094500_added_entity_TalentSearchFolderOwnerTeam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20241127151100_added_entity_UserDailyCandidateRollListConfig.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/202410100922_added_entity_JobTalentRecommendFeedback.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/20250218094500_added_entity_TalentTrackingLinkedinGroup.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250218094510_added_entity_TalentTrackingLinkedinGroupMember.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250218094520_added_entity_TalentTrackingLinkedinMessageTemplate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250218094530_added_entity_TalentTrackingLinkedinPending.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250722120000_added_entity_LinkedinAccountInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250722130000_added_entity_LinkedinAssistantEventTracking.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250723090000_added_entity_TalentTrackingLinkedinPendingGroup.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250723085700_added_entity_TalentTrackingLinkedinPendingGroupMember.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/20250707093900_added_entity_TalentNoteDraft.xml" relativeToChangelogFile="false"/>

</databaseChangeLog>
