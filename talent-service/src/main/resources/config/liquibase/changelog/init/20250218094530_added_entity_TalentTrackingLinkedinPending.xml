<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20250218094530-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_tracking_linkedin_pending"/>
            </not>
        </preConditions>
        <createTable tableName="talent_tracking_linkedin_pending">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="first_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="last_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="photo_url" type="VARCHAR(1000)"/>
            <column name="title" type="VARCHAR(255)"/>
            <column name="link_url" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="talent_linkedin_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="connection_degree" type="INT"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="last_interaction_time" type="timestamp"/>
            <column name="category" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="operator_linkedin_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>
    </changeSet>

</databaseChangeLog>
