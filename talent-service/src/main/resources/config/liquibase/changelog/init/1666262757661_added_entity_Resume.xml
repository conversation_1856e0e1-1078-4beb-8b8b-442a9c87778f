<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264165884-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="resume"/>
            </not>
        </preConditions>
        <createTable tableName="resume">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="uuid" type="VARCHAR(255)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="data_md5" type="VARCHAR(255)"/>
            <column name="text" type="MEDIUMTEXT"/>
            <column name="parse_result" type="MEDIUMTEXT"/>
            <column name="has_portrait" type="BIT(1)"/>
            <column name="has_display" type="BIT(1)"/>
            <column name="n_pages" type="INT"/>
            <column name="skills_text" type="MEDIUMTEXT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
