<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264364674-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_contact"/>
            </not>
        </preConditions>
        <createTable tableName="talent_contact">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="jhi_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="verified" type="BIT(1)"/>
            <column name="details" type="VARCHAR(400)"/>
            <column name="info" type="TEXT"/>
            <column name="talent_id" type="BIGINT"/>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="verification_status" type="tinyint" />
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="sort" type="INT"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_talent_contact_contact" tableName="talent_contact">
            <column name="contact"/>
        </createIndex>
        <createIndex indexName="idx_talent_contact_talent_id" tableName="talent_contact">
            <column name="talent_id"/>
        </createIndex>
        <createIndex indexName="idx_talent_contact_tenant_id" tableName="talent_contact">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
