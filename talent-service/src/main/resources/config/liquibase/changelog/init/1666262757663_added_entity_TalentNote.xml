<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264420593-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_note"/>
            </not>
        </preConditions>
        <createTable tableName="talent_note">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="title" type="VARCHAR(255)"/>
            <column name="note" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="additional_info" type="json"/>
            <column name="visible" type="BIT(1)"/>
            <column name="priority" type="INT"/>
            <column name="talent_id" type="BIGINT"/>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="agency_id" type="BIGINT" />
            <column name="read_status" type="tinyint(1)" defaultValue="1" />
            <column name="parsed_result" type="TEXT"/>
            <column name="enrich_result" type="TEXT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="note_type" type="TINYINT(3)"/>
            <column name="note_status" type="TINYINT(3)"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="fk_talent_note_talent_id" tableName="talent_note">
            <column name="talent_id"/>
        </createIndex>
        <createIndex indexName="fk_talent_note_user_id" tableName="talent_note">
            <column name="user_id"/>
        </createIndex>
        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="talent_note"
                                 constraintName="fk_talent_note_user_id" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="id"
                                 referencedTableName="user" validate="true"/>
    </changeSet>

</databaseChangeLog>
