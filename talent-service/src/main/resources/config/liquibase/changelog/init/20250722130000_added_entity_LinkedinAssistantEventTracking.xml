<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <changeSet author="jhipster" id="20250722130000-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="linkedin_assistant_event_tracking"/>
            </not>
        </preConditions>
        <createTable tableName="linkedin_assistant_event_tracking">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="event_name" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="operator_linkedin_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="linkedin_id" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="detail" type="VARCHAR(255)"/>
            <column name="event_datetime" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
    </changeSet>

</databaseChangeLog>