<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">


    <!--
        Added the entity Event_user.
    -->
    <changeSet id="20190109192335-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="event_user"/>
            </not>
        </preConditions>
        <createTable tableName="event_user">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>

            <column name="event_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="first_name" type="varchar(100)">
                <constraints nullable="false" />
            </column>

            <column name="last_name" type="varchar(100)">
                <constraints nullable="false" />
            </column>

            <column name="full_name" type="varchar(100)">
                <constraints nullable="false" />
            </column>

            <column name="email" type="varchar(255)">
                <constraints nullable="false" />
            </column>

            <column name="phone" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="college_name" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="company" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="linked_in" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="facebook" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="twitter" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="subscribe" type="bit">
                <constraints nullable="true" />
            </column>

            <column name='created_by' type='varchar(50)'>
                <constraints nullable='false'/>
            </column>

            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>

            <column name='last_modified_by' type='varchar(50)'/>
            <column name='last_modified_date' type='timestamp(3)'/>

            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here, do not remove-->
        </createTable>

    </changeSet>
    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here, do not remove-->
</databaseChangeLog>
