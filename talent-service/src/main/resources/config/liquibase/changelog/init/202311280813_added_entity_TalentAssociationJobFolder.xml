<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity TalentTrackingNote.
    -->
    <changeSet id="202311280813" author="yehaoyu">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_association_job_folder"/>
            </not>
        </preConditions>
        <createTable tableName="talent_association_job_folder">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="job_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="folder_id" type="varchar(32)">
                <constraints nullable="false" />
            </column>
            <column name="user_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="role" type="TINYINT(3)">
                <constraints nullable="false" />
            </column>
            <column name="status" type="TINYINT(3)">
                <constraints nullable="false" />
            </column>
            <column name='created_by' type='varchar(50)'>
                <constraints nullable='false'/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name='last_modified_by' type='varchar(50)'/>
            <column name='last_modified_date' type='timestamp(3)'/>
        </createTable>
        <createIndex indexName="idx_uni" tableName="talent_association_job_folder" unique="true">
            <column name="job_id" type="bigint"/>
            <column name="folder_id" type="bigint"/>
            <column name="user_id" type="bigint"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
