<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity TalentTrackingNote.
    -->
    <changeSet id="20200615160428-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_tracking_note"/>
            </not>
        </preConditions>
        <createTable tableName="talent_tracking_note">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>

            <column name="tenant_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="user_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="tracking_platform" type="integer">
                <constraints nullable="false" />
            </column>

            <column name='platform_id' type='varchar(100)'>
                <constraints nullable='false'/>
            </column>

            <column name='note' type='varchar(5000)'>
                <constraints nullable='false'/>
            </column>

            <column name="synced_talent_id" type="bigint">
                <constraints nullable="true" />
            </column>

            <column name='created_by' type='varchar(50)'>
                <constraints nullable='false'/>
            </column>

            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>

            <column name='last_modified_by' type='varchar(50)'/>
            <column name='last_modified_date' type='timestamp(3)'/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here, do not remove-->
        </createTable>
        <createIndex indexName="idx_talent_tracking_note_pid" tableName="talent_tracking_note">
            <column name="platform_id" type="varchar(100)"/>
        </createIndex>
        <createIndex indexName="idx_talent_tracking_note_tenant_id" tableName="talent_tracking_note">
            <column name="tenant_id" type="bigint"/>
        </createIndex>
        <sql>ALTER TABLE talent_tracking_note CONVERT TO CHARACTER SET utf8 COLLATE utf8_unicode_ci;</sql>
    </changeSet>
    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here, do not remove-->
</databaseChangeLog>
