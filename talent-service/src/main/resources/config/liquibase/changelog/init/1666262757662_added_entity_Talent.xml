<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264343161-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent"/>
            </not>
        </preConditions>
        <createTable tableName="talent">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="first_name" type="VARCHAR(255)"/>
            <column name="last_name" type="VARCHAR(255)"/>
            <column name="full_name" type="VARCHAR(255)"/>
            <column name="photo_url" type="VARCHAR(1000)"/>
            <column name="motivation_id" type="INT"/>

            <column name="active" type="BIT(1)"/>
            <column name="owned_by_tenants" type="BIGINT"/>

            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="last_edited_time" type="timestamp"/>
            <column name="last_sync_time" type="timestamp"/>
            <column defaultValueNumeric="0" name="sync_paused" type="INT">
                <constraints nullable="false"/>
            </column>

            <column name="additional_info_id" type="BIGINT"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_talent_last_sync_time_index" tableName="talent">
            <column name="last_sync_time"/>
        </createIndex>
        <createIndex indexName="idx_talent_tenant_id_index" tableName="talent">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
