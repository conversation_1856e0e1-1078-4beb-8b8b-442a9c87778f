<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    
    <changeSet author="jhipster" id="**************-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="linkedin_account_info"/>
            </not>
        </preConditions>
        <createTable tableName="linkedin_account_info">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="linkedin_id" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="friend_count" type="BIGINT"/>
            <column name="match_friend_datetime" type="timestamp"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name='last_modified_by' type='varchar(50)'/>
            <column name='last_modified_date' type='timestamp(3)'/>
        </createTable>
    </changeSet>

</databaseChangeLog>