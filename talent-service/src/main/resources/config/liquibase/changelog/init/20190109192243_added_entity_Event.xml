<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="jhipster" id="1666263578332-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="event"/>
            </not>
        </preConditions>
        <createTable tableName="event">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>

            <column name="title" type="varchar(255)">
                <constraints nullable="false" />
            </column>

            <column name="description" type="varchar(500)">
                <constraints nullable="true" />
            </column>

            <column name="start_time" type="timestamp">
                <constraints nullable="true" />
            </column>

            <column name="end_time" type="timestamp">
                <constraints nullable="true" />
            </column>

            <column name="status" type="integer" defaultValue="0">
                <constraints nullable="false" />
            </column>

            <column name="address_line_one" type="varchar(300)">
                <constraints nullable="true" />
            </column>

            <column name="address_line_two" type="varchar(300)">
                <constraints nullable="true" />
            </column>

            <column name="city" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="province" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="country" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="zipcode" type="varchar(255)">
                <constraints nullable="true" />
            </column>

            <column name="latitude" type="float">
                <constraints nullable="true" />
            </column>

            <column name="longitude" type="float">
                <constraints nullable="true" />
            </column>

            <column name='created_by' type='varchar(50)'>
                <constraints nullable='false'/>
            </column>

            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>

            <column name='last_modified_by' type='varchar(50)'/>
            <column name='last_modified_date' type='timestamp(3)'/>

        </createTable>
    </changeSet>
</databaseChangeLog>
