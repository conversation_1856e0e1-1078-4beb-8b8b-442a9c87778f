<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity TalentTrackingNote.
    -->
    <changeSet id="202311021627" author="yushan">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="excel_update_talent_record"/>
            </not>
        </preConditions>
        <createTable tableName="excel_update_talent_record">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="md5" type="varchar(255)">
                <constraints nullable="false" />
            </column>
            <column name="talent_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="new_data" type="MEDIUMTEXT"/>
            <column name='old_data' type="MEDIUMTEXT"/>
            <column name='created_by' type='varchar(50)'>
                <constraints nullable='false'/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name='last_modified_by' type='varchar(50)'/>
            <column name='last_modified_date' type='timestamp(3)'/>
        </createTable>
        <createIndex indexName="idx_excel_update_talent_record_talent_id" tableName="excel_update_talent_record">
            <column name="talent_id" type="bigint"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
