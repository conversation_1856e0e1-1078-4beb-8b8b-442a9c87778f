<?xml version="1.0" encoding="utf-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity JobFolderRelation.
    -->
    <changeSet id="1689542679123-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_folder_relation"/>
            </not>
        </preConditions>
        <createTable tableName="talent_folder_relation">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="talent_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="talent_folder_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <createIndex indexName="index_talent_folder_relation_talent_id" tableName="talent_folder_relation">
            <column name="talent_id"/>
        </createIndex>
        <createIndex indexName="index_talent_folder_relation_talent_folder_id" tableName="talent_folder_relation">
            <column name="talent_folder_id"/>
        </createIndex>
        <createIndex indexName="index_talent_folder_relation_puser_id" tableName="talent_folder_relation">
            <column name="puser_id"/>
        </createIndex>
        <createIndex indexName="index_talent_folder_relation_pteam_id" tableName="talent_folder_relation">
            <column name="pteam_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>