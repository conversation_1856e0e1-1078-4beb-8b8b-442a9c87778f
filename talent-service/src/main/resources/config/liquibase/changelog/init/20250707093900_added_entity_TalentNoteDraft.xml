<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20250707093900-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_note_draft"/>
            </not>
        </preConditions>
        <createTable tableName="talent_note_draft">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" type="BIGINT" remarks="用户ID">
                <constraints nullable="false"/>
            </column>
            <column name="talent_id" type="BIGINT" remarks="候选人ID">
                <constraints nullable="false"/>
            </column>
            <column name="draft_data" type="JSON" remarks="草稿内容JSON"/>
        </createTable>

        <!-- 添加唯一约束 -->
        <addUniqueConstraint
                columnNames="talent_id, user_id"
                constraintName="uk_talent_user"
                tableName="talent_note_draft"/>
    </changeSet>

</databaseChangeLog>
