<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.5.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity TalentTrackingRecord.
    -->
    <changeSet id="20200530045441-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_tracking_record"/>
            </not>
        </preConditions>
        <createTable tableName="talent_tracking_record">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>

            <column name="tenant_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="user_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="tracking_type" type="integer">
                <constraints nullable="false" />
            </column>

            <column name='contact' type='varchar(55)'>
                <constraints nullable='false'/>
            </column>

            <column name="touch_time" type="timestamp">
                <constraints nullable="true" />
            </column>

            <column name='created_by' type='varchar(50)'>
                <constraints nullable='false'/>
            </column>

            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>

            <column name='last_modified_by' type='varchar(50)'/>
            <column name='last_modified_date' type='timestamp(3)'/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here, do not remove-->
        </createTable>
        <createIndex indexName="idx_talent_tracking_record_uid" tableName="talent_tracking_record">
            <column name="user_id" type="bigint"/>
        </createIndex>
        <createIndex indexName="idx_talent_tracking_record_contact" tableName="talent_tracking_record">
            <column name="contact" type="varchar(55)"/>
        </createIndex>
        <createIndex indexName="idx_talent_tracking_record_tenant_id" tableName="talent_tracking_record">
            <column name="tenant_id" type="bigint"/>
        </createIndex>
        <createIndex indexName="idx_talent_tracking_record_all" tableName="talent_tracking_record" unique = 'true'>
            <column name="tenant_id" type="bigint"/>
            <column name="user_id" type="bigint"/>
            <column name="tracking_type" type="integer"/>
            <column name='contact' type='varchar(55)'/>
            <column name="touch_time" type="timestamp"/>
        </createIndex>
    </changeSet>
    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here, do not remove-->
</databaseChangeLog>
