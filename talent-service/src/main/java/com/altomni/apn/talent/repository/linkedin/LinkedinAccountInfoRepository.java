package com.altomni.apn.talent.repository.linkedin;


import com.altomni.apn.talent.domain.linkedin.LinkedinAccountInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LinkedinAccountInfoRepository extends JpaRepository<LinkedinAccountInfo, Long> {
    Optional<LinkedinAccountInfo> findByLinkedinId(String linkedinId);
}