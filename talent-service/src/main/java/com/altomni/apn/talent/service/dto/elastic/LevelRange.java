package com.altomni.apn.talent.service.dto.elastic;

import java.util.Objects;

public class LevelRange {

    private Level gte;

    private Level lte;

    public static boolean isNotEmpty(LevelRange levelRange) {
        return Objects.nonNull(levelRange.getGte()) || Objects.nonNull(levelRange.getLte());
    }

    public Level getGte() {
        return gte;
    }

    public void setGte(Level gte) {
        this.gte = gte;
    }

    public Level getLte() {
        return lte;
    }

    public void setLte(Level lte) {
        this.lte = lte;
    }

    @Override
    public String toString() {
        return "LevelRange{" +
            "gte=" + gte +
            ", lte=" + lte +
            '}';
    }
}
