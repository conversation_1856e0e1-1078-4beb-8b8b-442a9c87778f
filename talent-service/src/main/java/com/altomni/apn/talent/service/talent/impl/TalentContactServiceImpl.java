package com.altomni.apn.talent.service.talent.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.ContactTypeConstants;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.UpdateTalentContactVerificationStatusDTO;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.repository.talent.TalentContactRepository;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.CompanyService;
import com.altomni.apn.talent.service.confidential.TalentConfidentialService;
import com.altomni.apn.talent.service.query.ContactSearch;
import com.altomni.apn.talent.service.talent.TalentContactService;
import com.altomni.apn.talent.service.talent.TalentOwnershipService;
import com.altomni.apn.talent.web.rest.vm.TalentContactVM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TalentContactServiceImpl implements TalentContactService {

    private final Logger log = LoggerFactory.getLogger(TalentContactServiceImpl.class);

    @Resource
    private TalentContactRepository talentContactRepository;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Resource
    private CompanyService companyService;

    @Resource
    private TalentOwnershipService talentOwnershipService;

    @Resource
    private TalentConfidentialService talentConfidentialService;


    @Override
    public void delete(Long id) throws IOException {
        TalentContact talentContact = talentContactRepository.findByIdAndStatus(id, TalentContactStatus.AVAILABLE);
        if (talentContact != null) {
            if (!SecurityUtils.isCurrentTenant(talentContact.getTenantId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
            }
            talentContact.setStatus(TalentContactStatus.INVALID);
            talentContactRepository.save(talentContact);
            talentRepository.updateTalentLastEditedTime(talentContact.getTalentId());
            syncTalent(talentContact.getTalentId());
        }
    }

    private void syncTalent(Long talentId) throws IOException {
        //esFillerTalentService.syncTalentToEs(talentId);
    }

    @Override
    public TalentContact findOne(Long id) {
        return talentContactRepository.getById(id);
    }

    @Override
    public TalentContact findByIdAndStatus(Long id, Status status) {
        return talentContactRepository.findByIdAndStatus(id, TalentContactStatus.AVAILABLE);
    }

    @Override
    public List<TalentContact> getAllTalentContacts(ContactType type, String contact, Pageable pageable) {
        return talentContactRepository.findAll(ContactSearch.getContacts(type, contact), pageable).getContent();
    }


    @Override
    @Transactional(readOnly = true)
    public Set<Long> getTalentIdsByContactsIgnoreWrongContact(Long ignoreTalentId, List<TalentContactVM> contacts) {
        if (CollectionUtils.isEmpty(contacts)) {
            return Collections.emptySet();
        }

        Set<TalentContactVM> identifiedContacts = contacts.stream().filter(c -> StringUtils.isNotBlank(c.getContact()) && !StringUtils.equals("None", c.getContact()) && ContactTypeConstants.EXCLUSIVE_CONTACT_TYPES.contains(c.getType())).map(tc -> {
            Set<TalentContactVM> set = new HashSet<>();
            set.add(tc);
            if (ContactType.LINKEDIN.equals(tc.getType())) {
                String encoded = CommonUtils.urlEncodeIgnoreDuplicate(tc.getContact());
                if (StringUtils.isNotEmpty(encoded)) {
                    set.add(new TalentContactVM(ContactType.LINKEDIN, encoded));
                }
            }
            return set;
        }).flatMap(Collection::stream).collect(Collectors.toSet());

        List<TalentContact> existingContacts = talentContactRepository.findAll(getTalentContactsByIdentifiedContactsIgnoreWrongContact(identifiedContacts, SecurityUtils.getTenantId()));

        return existingContacts.stream().map(TalentContact::getTalentId).filter(id -> !Objects.equals(id, ignoreTalentId)).collect(Collectors.toSet());
    }

    @Override
    public Set<TalentContact> getTalentDuplicationsByContacts(Long ignoreTalentId, List<TalentContactVM> contacts) {
        if (CollectionUtils.isEmpty(contacts)) {
            return Collections.emptySet();
        }

        Set<TalentContactVM> identifiedContacts = contacts.stream().filter(c -> StringUtils.isNotBlank(c.getContact()) && !StringUtils.equals("None", c.getContact()) && ContactTypeConstants.EXCLUSIVE_CONTACT_TYPES.contains(c.getType())).map(tc -> {
            Set<TalentContactVM> set = new HashSet<>();
            set.add(tc);
            if (ContactType.LINKEDIN.equals(tc.getType())) {
                String encoded = CommonUtils.urlEncodeIgnoreDuplicate(tc.getContact());
                if (StringUtils.isNotEmpty(encoded)) {
                    set.add(new TalentContactVM(ContactType.LINKEDIN, encoded));
                }
            }
            return set;
        }).flatMap(Collection::stream).collect(Collectors.toSet());

        List<TalentContact> existingContacts = talentContactRepository.findAll(getTalentContactsByIdentifiedContactsIgnoreWrongContact(identifiedContacts, SecurityUtils.getTenantId()));

        return existingContacts.stream().filter(c -> !Objects.equals(c.getTalentId(), ignoreTalentId)).collect(Collectors.toSet());
    }

    @Override
    public List<TalentContact> findAllByTalentIdInAndTypeAndStatus(List<Long> talentIds, ContactType email, TalentContactStatus available) {
        return talentContactRepository.findAllByTalentIdInAndTypeAndStatus(talentIds, email, available);
    }

    @Override
    public List<TalentContact> findAllByTalentIdInAndTypeAndStatusWithPermission(List<Long> talentIds, ContactType email, TalentContactStatus available) {
        return talentContactRepository.findAllByTalentIdInAndTypeAndStatus(getAllViewableTalentIds(talentIds), email, available);
    }

    @Override
    public List<Long> getAllViewableTalentIds(List<Long> talentIds){
        var contactStatusList = companyService.getTalentClientContactStatus(talentIds).getBody();
        Map<Long, Boolean> talentClientContactStatusMap =  contactStatusList
                .stream()
                .collect(Collectors.toMap(
                        TalentClientContactStatusDTO::getTalentId,
                        TalentClientContactStatusDTO::getIsClientContact
                ));
        List<Long> pureTalentIds = talentIds.stream()
                .filter(talentId -> Boolean.FALSE.equals(talentClientContactStatusMap.get(talentId)))
                .collect(Collectors.toList());

        List<Long> contactTalentIds = talentIds.stream()
                .filter(talentId -> Boolean.TRUE.equals(talentClientContactStatusMap.get(talentId)))
                .collect(Collectors.toList());

//        List<Long> viewableContactTalentIds = talentOwnershipService.getViewableTalentIdsOnUser(contactTalentIds, SecurityUtils.getUserId());
        Set<Long> viewableContactTalentIds = talentOwnershipService.filterValidTalents(contactTalentIds);
        List<Long> viewableTalentIds = new ArrayList<>(pureTalentIds);
        viewableTalentIds.addAll(viewableContactTalentIds);
        return viewableTalentIds;
    }

    @Override
    public List<TalentContact> findPrimaryEmailAllByTalentIdInAndTypeAndStatus(List<Long> talentIds, ContactType email, TalentContactStatus available) {
        return talentContactRepository.findAllByTalentIdInAndTypeAndStatusAndSort(talentIds, email, available, 0);
    }

    @Override
    public List<TalentContact> findAllByTalentId(Long id) {
        if (!talentConfidentialService.confidentialTalentViewAble(id)) {
            return Collections.emptyList();
        }
        return talentContactRepository.findAllByTalentIdAndStatus(id, TalentContactStatus.AVAILABLE);
    }

    private Specification<TalentContact> getTalentContactsByIdentifiedContacts(Set<TalentContactVM> identifiedContacts, Long tenantId) {
        Specification<TalentContact> specification = new Specification<TalentContact>() {
            @Override
            public Predicate toPredicate(Root<TalentContact> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                Predicate disjunction = criteriaBuilder.disjunction();
                for (TalentContactVM tc : identifiedContacts) {
                    Predicate predicate1 = null;
                    if (ContactType.PHONE.equals(tc.getType()) || ContactType.WECHAT.equals(tc.getType()) || ContactType.WHATSAPP.equals(tc.getType())) {
                        predicate1 = criteriaBuilder.or(
                                criteriaBuilder.equal(root.get("type"), ContactType.PHONE),
                                criteriaBuilder.equal(root.get("type"), ContactType.WECHAT),
                                criteriaBuilder.equal(root.get("type"), ContactType.WHATSAPP)
                        );
                    } else {
                        predicate1 = criteriaBuilder.equal(root.get("type"), tc.getType());
                    }
                    Predicate predicate2 = criteriaBuilder.equal(root.get("contact"), tc.getContact());
                    Predicate and = criteriaBuilder.and(predicate1, predicate2);
                    disjunction.getExpressions().add(and);
                }
                Predicate tenantPredicate = criteriaBuilder.equal(root.get("tenantId"), tenantId);
                Predicate statusPredicate = criteriaBuilder.equal(root.get("status"), TalentContactStatus.AVAILABLE);
                return criteriaBuilder.and(disjunction, tenantPredicate, statusPredicate);
            }
        };
        return specification;
    }

    private Specification<TalentContact> getTalentContactsByIdentifiedContactsIgnoreWrongContact(Set<TalentContactVM> identifiedContacts, Long tenantId) {
        Specification<TalentContact> specification = new Specification<TalentContact>() {
            @Override
            public Predicate toPredicate(Root<TalentContact> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                Predicate disjunction = criteriaBuilder.disjunction();
                for (TalentContactVM tc : identifiedContacts) {
                    Predicate predicate1 = null;
                    if (ContactType.PHONE.equals(tc.getType()) || ContactType.WECHAT.equals(tc.getType()) || ContactType.WHATSAPP.equals(tc.getType())) {
                        predicate1 = criteriaBuilder.or(
                                criteriaBuilder.equal(root.get("type"), ContactType.PHONE),
                                criteriaBuilder.equal(root.get("type"), ContactType.WECHAT),
                                criteriaBuilder.equal(root.get("type"), ContactType.WHATSAPP)
                        );
                    } else {
                        predicate1 = criteriaBuilder.equal(root.get("type"), tc.getType());
                    }
                    Predicate predicate2 = criteriaBuilder.equal(root.get("contact"), tc.getContact());
                    Predicate and = criteriaBuilder.and(predicate1, predicate2);
                    disjunction.getExpressions().add(and);
                }
                Predicate tenantPredicate = criteriaBuilder.equal(root.get("tenantId"), tenantId);
                Predicate statusPredicate = criteriaBuilder.equal(root.get("status"), TalentContactStatus.AVAILABLE);
                Predicate verificationStatusPredicate = criteriaBuilder.or(
                        criteriaBuilder.notEqual(root.get("verificationStatus"), TalentContactVerificationStatus.WRONG_CONTACT),
                        criteriaBuilder.isNull(root.get("verificationStatus"))
                );
                return criteriaBuilder.and(disjunction, tenantPredicate, statusPredicate, verificationStatusPredicate);
            }
        };
        return specification;
    }

    @Override
    public List<TalentContactDTO> findAllContactByContactAndTypes(String contact, List<ContactType> types) {
        List<TalentContactDTO> contacts = talentContactRepository.findAllByContactAndTypeIn(contact, types).stream().map(TalentContactDTO::fromTalentContact).toList();
        return contacts;
    }

    @Override
    public TalentContact updateTalentContactVerificationStatus(UpdateTalentContactVerificationStatusDTO updateContactVerificationStatusDTO) {
        TalentContact talentContact = talentContactRepository.findByTenantIdAndTalentIdAndTypeAndContactAndStatus(SecurityUtils.getTenantId(), updateContactVerificationStatusDTO.getTalentId(), updateContactVerificationStatusDTO.getContactType(), updateContactVerificationStatusDTO.getContactInfo(), TalentContactStatus.AVAILABLE).orElseThrow(() -> new CustomParameterizedException("Cannot find talentId: " + updateContactVerificationStatusDTO.getTalentId() + " with contact info: " + updateContactVerificationStatusDTO.getContactInfo()));
        talentContact.setVerificationStatus(updateContactVerificationStatusDTO.getVerificationStatus());
        talentContact.setSort(99);
        TalentV3 dbTalent = talentRepository.findById(talentContact.getTalentId()).orElseThrow(() -> new CustomParameterizedException("The talent to update dose not exist"));
        return talentContactRepository.saveAndFlush(talentContact);
    }
}
