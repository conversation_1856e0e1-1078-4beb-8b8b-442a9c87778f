package com.altomni.apn.talent.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The InvoiceActivityType enumeration.
 */
public enum InvoiceActivityType implements ConvertedEnum<Integer>{
    CREATE(0),
    VOID(1),
    DOWNLOAD(2),
    PAYMENT(3),
    STARTUP_FEE_USED(4),
    STARTUP_FEE_ROLL_BACK(5);


    private final Integer dbValue;

    InvoiceActivityType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<InvoiceActivityType, Integer> resolver =
        new ReverseEnumResolver<>(InvoiceActivityType.class, InvoiceActivityType::toDbValue);

    public static InvoiceActivityType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
