package com.altomni.apn.talent.service.dto.activitylog;

import com.altomni.apn.common.dto.activity.ChangeFieldDTO;
import com.altomni.apn.talent.domain.user.SimpleUserInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TalentActivityDTO implements Serializable {

    private Long talentId;
    private List<ChangeFieldDTO> changeFields;
    private SimpleUserInfoDTO createdBy;
    private String createdDate;

}
