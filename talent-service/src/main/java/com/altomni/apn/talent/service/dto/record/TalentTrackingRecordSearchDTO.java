package com.altomni.apn.talent.service.dto.record;

import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.record.TrackingType;
import com.altomni.apn.talent.domain.record.TalentTrackingRecord;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
public class TalentTrackingRecordSearchDTO implements Serializable {

    private static final long serialVersionUID = -956520387234760691L;

    private List<String> contacts;

    public List<String> getContacts() {
        return contacts;
    }

    public void setContacts(List<String> contacts) {
        this.contacts = contacts;
    }
}
