package com.altomni.apn.talent.repository.tracking;

import com.altomni.apn.talent.domain.enumeration.linkedinproject.GroupMemberStatus;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroupMember;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TalentTrackingLinkedinGroupMemberRepository extends JpaRepository<TalentTrackingLinkedinGroupMember, Long>, JpaSpecificationExecutor<TalentTrackingLinkedinGroupMember> {

    Integer countDistinctByGroupIdAndStatus(Long groupId, GroupMemberStatus status);

    List<TalentTrackingLinkedinGroupMember> findAllByGroupIdInAndStatus(List<Long> groupIds, GroupMemberStatus status);

    List<TalentTrackingLinkedinGroupMember> findAllByGroupIdAndStatus(Long id, GroupMemberStatus status);

    List<TalentTrackingLinkedinGroupMember> findAllByGroupIdInAndTalentLinkedinIdIn(List<Long> groupIds, List<String> talentLinkedinIds);

    List<TalentTrackingLinkedinGroupMember> findAllByTalentLinkedinIdInAndStatus(List<String> talentLinkedinIds, GroupMemberStatus status);

    @Query(value = """
            SELECT gm FROM TalentTrackingLinkedinGroupMember gm
            LEFT JOIN TalentTrackingLinkedinGroup g ON gm.groupId = g.id
             WHERE gm.status = com.altomni.apn.talent.domain.enumeration.linkedinproject.GroupMemberStatus.WAITING
             AND g.operatorLinkedinId = ?1 AND gm.talentLinkedinId IN (?2) AND g.status = com.altomni.apn.talent.domain.enumeration.tracking.TrackingGroupStatus.ACTIVE
        """)
    List<TalentTrackingLinkedinGroupMember> findWaitingGroupMember(String operatorLinkedinId, List<String> linkedinIdList);

    @Query(value = """
            SELECT gm.talentLinkedinId FROM TalentTrackingLinkedinGroupMember gm
            LEFT JOIN TalentTrackingLinkedinGroup g ON gm.groupId = g.id
             WHERE gm.status = com.altomni.apn.talent.domain.enumeration.linkedinproject.GroupMemberStatus.WAITING
             AND g.operatorLinkedinId = ?1 AND g.status = com.altomni.apn.talent.domain.enumeration.tracking.TrackingGroupStatus.ACTIVE
        """)
    List<String> findWaitingGroupMemberByOperator(String operatorLinkedinId);
}
