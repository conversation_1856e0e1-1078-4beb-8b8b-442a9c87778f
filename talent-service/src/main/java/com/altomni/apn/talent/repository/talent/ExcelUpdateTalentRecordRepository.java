package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.talent.domain.record.ExcelUpdateTalentRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ExcelUpdateTalentRecordRepository extends JpaRepository<ExcelUpdateTalentRecord, Long> {

    ExcelUpdateTalentRecord findExcelUpdateTalentRecordByTalentId(Long talentId);

}

