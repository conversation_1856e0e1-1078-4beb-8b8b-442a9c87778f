package com.altomni.apn.talent.service.event.impl;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.event.EventUser;
import com.altomni.apn.talent.repository.event.EventUserRepository;
import com.altomni.apn.talent.service.dto.event.EventUserDTO;
import com.altomni.apn.talent.service.event.EventUserService;
import com.altomni.apn.talent.service.vo.event.EventUserVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class EventUserServiceImpl implements EventUserService {

    private final EventUserRepository eventUserRepository;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final TalentApiPromptProperties talentApiPromptProperties;

    public EventUserServiceImpl(EventUserRepository eventUserRepository,CommonApiMultilingualConfig commonApiMultilingualConfig,
                                TalentApiPromptProperties talentApiPromptProperties) {
        this.eventUserRepository = eventUserRepository;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.talentApiPromptProperties = talentApiPromptProperties;
    }

    @Override
    public EventUserVO create(EventUserDTO eventUserDTO) {
        if (eventUserDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.EVENTUSER_CREATE_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        if (eventUserDTO.getEventId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.EVENTUSER_CREATE_EVENTIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }

        List<EventUser> eventUserList = eventUserRepository.findAllByEventIdAndEmail(eventUserDTO.getEventId(), eventUserDTO.getEmail());
        if (CollectionUtils.isNotEmpty(eventUserList)) {
            return EventUserVO.fromEventUser(eventUserList.get(0));
        } else {
            return EventUserVO.fromEventUser(eventUserRepository.save(EventUser.fromEventUserDTO(eventUserDTO)));
        }
    }

    @Override
    public EventUserVO findOne(Long id) {
        EventUser eventUser = eventUserRepository.findById(id).orElse(new EventUser());
        return EventUserVO.fromEventUser(eventUser);
    }

    @Override
    public List<EventUserVO> findAllByEventId(Long eventId) {
        return eventUserRepository.findAllByEventId(eventId).stream().map(EventUserVO::fromEventUser).collect(Collectors.toList());
    }
}
