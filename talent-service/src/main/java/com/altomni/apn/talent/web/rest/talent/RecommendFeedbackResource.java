package com.altomni.apn.talent.web.rest.talent;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.talent.service.feedback.RecommendFeedbackService;
import com.altomni.apn.talent.web.rest.talent.dto.RecommendFeedbackDTO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;

@Api(tags = {"Feedback"})
@RestController
@RequestMapping("/api/v3")
public class RecommendFeedbackResource {

    private final Logger log = LoggerFactory.getLogger(RecommendFeedbackResource.class);

    @Resource
    private RecommendFeedbackService feedbackService;

    @PostMapping(value = "/recommend-feedback")
    @Timed
    public ResponseEntity<Void> recommendFeedback(@RequestBody RecommendFeedbackDTO recommendFeedbackDTO) throws IOException {
        log.info("REST request to recommend feedback : {} ",  recommendFeedbackDTO);
        feedbackService.recommendFeedback(recommendFeedbackDTO);
        return ResponseEntity.ok().build();
    }
}
