package com.altomni.apn.talent.web.rest.talent;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.UpdateTalentContactVerificationStatusDTO;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.service.talent.TalentContactService;
import com.altomni.apn.talent.service.talent.TalentService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing TalentContact.
 */
@Api(tags = {"Talent Contact"})
@RestController
@RequestMapping("/api/v3")
public class TalentContactResource {

    private final Logger log = LoggerFactory.getLogger(TalentContactResource.class);

    private static final String ENTITY_NAME = "talentContact";

    @Resource
    private TalentContactService talentContactService;

    @Resource
    private TalentService talentService;


    @ApiOperation(value = "Get talent's all contacts based on search for contact, e.g. LinkedIn", tags = {"APN-Pro"})
    @GetMapping("/contacts-for-talent")
    @Timed
    public List<TalentContact> getAllTalentContacts(
        @ApiParam(value = "contact type, e.g. LinkedIn") @RequestParam(value = "type") ContactType type,
        @ApiParam(value = "contact, e.g. LinkedIn ID") @RequestParam(value = "contact") String contact, Pageable pageable) {
        log.info("[APN: TalentContact @{}] REST request to get Contacts for talent with contact {}, {}", SecurityUtils.getUserId(), type, contact);
        return talentContactService.getAllTalentContacts(type, contact, pageable);
    }

    /**
     * GET  /talent-contacts/:id : get the "id" talentContact.
     *
     * @param id the id of the talentContact to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the talentContact, or with status 404 (Not Found)
     */
    @GetMapping("/talent-contacts/{id}")
    @Timed
    public ResponseEntity<TalentContact> getTalentContact(@PathVariable Long id) {
        log.info("[APN: TalentContact @{}] REST request to get TalentContact : {}", SecurityUtils.getUserId(), id);
        TalentContact talentContact = talentContactService.findByIdAndStatus(id, Status.Available);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentContact));
    }

    /**
     * DELETE  /talent-contacts/:id : delete the "id" talentContact.
     *
     * @param id the id of the talentContact to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "Delete a TalentContact")
    @DeleteMapping("/talent-contacts/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteTalentContact(@ApiParam(value = "Talent contact id", required = true) @PathVariable Long id) throws IOException {
        log.info("[APN: TalentContact @{}] REST request to delete TalentContact : {}", SecurityUtils.getUserId(), id);
        talentContactService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }

    /**
     * POST  /talent-contacts/by-talentId-and-type-and-status
     *
     * @return TalentContact
     */
    @PostMapping("/talent-contacts/by-talentId-and-type-and-status")
    @Timed
    public ResponseEntity<List<TalentContact>> findAllByTalentIdInAndTypeAndStatus(@RequestBody List<Long> talentIds) {
        log.info("[APN: TalentContact @{}] REST request to findAllByTalentIdInAndTypeAndStatus : {}", SecurityUtils.getUserId(), talentIds);
        List<TalentContact> talentContactList = talentContactService.findAllByTalentIdInAndTypeAndStatus(talentIds, ContactType.EMAIL, TalentContactStatus.AVAILABLE);
        return ResponseEntity.ok(talentContactList);
    }

    /**
     * Same function as findAllByTalentIdInAndTypeAndStatus, with permission check the talent is viewable by current user;
     * @param talentIds
     * @return
     */
    @PostMapping("/talent-contacts/by-talentId-and-type-and-status-with-permission")
    @Timed
    public ResponseEntity<List<TalentContact>> findAllByTalentIdInAndTypeAndStatusWithPermission(@RequestBody List<Long> talentIds) {
        log.info("[APN: TalentContact @{}] REST request to findAllByTalentIdInAndTypeAndStatus with permission: {}", SecurityUtils.getUserId(), talentIds);
        List<TalentContact> talentContactList = talentContactService.findAllByTalentIdInAndTypeAndStatusWithPermission(talentIds, ContactType.EMAIL, TalentContactStatus.AVAILABLE);
        return ResponseEntity.ok(talentContactList);
    }

    @GetMapping("/talent-contacts/talent/{id}")
    @Timed
    public ResponseEntity<List<TalentContact>> findAllByTalentId(@PathVariable Long id) {
        log.info("[APN: TalentContact @{}] REST request to findAllByTalentId : {}", SecurityUtils.getUserId(), id);
        List<TalentContact> talentContactList = talentContactService.findAllByTalentId(id);
        return ResponseEntity.ok(talentContactList);
    }

    /**
     * POST  /talent-contacts/by-talentId-and-type-and-status
     *
     * @return TalentContact
     */
    @PostMapping("/talent-primary-email/by-talentId-and-type-and-status")
    @Timed
    public ResponseEntity<List<TalentContact>> findPrimaryEmailAllByTalentIdInAndTypeAndStatus(@RequestBody List<Long> talentIds) {
        log.info("[APN: TalentContact @{}] REST request to findAllByTalentIdInAndTypeAndStatus : {}", SecurityUtils.getUserId(), talentIds);
        List<TalentContact> talentContactList = talentContactService.findPrimaryEmailAllByTalentIdInAndTypeAndStatus(talentIds, ContactType.EMAIL, TalentContactStatus.AVAILABLE);
        return ResponseEntity.ok(talentContactList);
    }

    @GetMapping("/talent-contacts/by-contact-types")
    public ResponseEntity<List<TalentContactDTO>> findTalentContactByContactAndTypes(@RequestParam(value = "contact") String contact, @RequestParam(value = "types") List<ContactType> types) {
        log.info("[APN: TalentResource @{}] REST request to find talent contact by contact: {} and contact types: {}", SecurityUtils.getUserId(), contact, types);
        List<TalentContactDTO> contacts = talentContactService.findAllContactByContactAndTypes(contact, types);
        return ResponseEntity.ok().body(contacts);
    }

    @PostMapping("/talent-contacts/verification-status")
    @Timed
    public ResponseEntity<TalentContact> updateTalentContactVerificationStatus(@RequestBody UpdateTalentContactVerificationStatusDTO updateContactVerificationStatusDTO) {
        log.info("[APN: TalentContact @{}] REST request to updateTalentContactVerificationStatus : {}", SecurityUtils.getUserId(), updateContactVerificationStatusDTO);
        TalentContact talentContact = talentContactService.updateTalentContactVerificationStatus(updateContactVerificationStatusDTO);
        talentService.syncClientContactToCrm(talentContact.getTalentId());
        return ResponseEntity.ok(talentContact);
    }

}
