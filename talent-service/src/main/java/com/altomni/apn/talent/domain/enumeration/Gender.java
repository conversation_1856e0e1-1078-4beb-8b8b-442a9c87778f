package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The Gender enumeration.
 */
public enum Gender implements ConvertedEnum<Integer> {
    MALE(0),
    FEMALE(1);

    private final int dbValue;

    Gender(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<Gender, Integer> resolver =
        new ReverseEnumResolver<>(Gender.class, Gender::toDbValue);

    public static Gender fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
