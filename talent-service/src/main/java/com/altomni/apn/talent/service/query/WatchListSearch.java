package com.altomni.apn.talent.service.query;

import com.altomni.apn.common.domain.talent.QTalentV3;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.talent.QWatchList;
import com.altomni.apn.talent.domain.talent.WatchList;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.JPAExpressions;
import org.apache.commons.lang3.StringUtils;


public class WatchListSearch {

    public static BooleanExpression allWatchlist(String fullName, String company, String title) {
        QWatchList qWatchList = QWatchList.watchList;
        PathBuilder<WatchList> entityPath = new PathBuilder<>(WatchList.class, "watchList");
        MyPredicateBuilder builder = new MyPredicateBuilder();
        BooleanExpression baseCondition = qWatchList.userId.eq(SecurityUtils.getUserId());
        QTalentV3 qTalent = QTalentV3.talentV3;
        BooleanExpression fullNameCondition = null;
        if (StringUtils.isNotEmpty(fullName)) {
            fullNameCondition = qWatchList.talentId.in(JPAExpressions.selectFrom(qTalent)
                .where(qTalent.id.eq(qWatchList.talentId).and(qTalent.fullName.contains(fullName))).select(qWatchList.talentId));
        }
        return baseCondition.and(fullNameCondition).and(builder.build(entityPath));
    }
}
