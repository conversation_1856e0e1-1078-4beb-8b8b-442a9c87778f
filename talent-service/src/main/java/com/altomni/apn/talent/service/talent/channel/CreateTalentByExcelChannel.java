package com.altomni.apn.talent.service.talent.channel;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.domain.enumeration.talent.TalentExcelStatusEnum;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.constants.RedisConstants;
import com.altomni.apn.talent.service.dto.start.TalentExcelUploadUrlDto;
import com.altomni.apn.talent.service.store.StoreService;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class CreateTalentByExcelChannel {

    @Resource
    private StoreService storeService;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private ApplicationProperties applicationProperties;

    public void initParserRedisAndSendSqlByS3(String uuid, boolean sqsFlag, boolean progressFlag, String hotListId) {
        CloudFileObjectMetadata cloudFileObject = storeService.getFileDetailWithoutFileFromS3(uuid, UploadTypeEnum.ADDRESS_LIST.getKey()).getBody();
        if (cloudFileObject == null) {
            return;
        }
        if (BooleanUtil.isTrue(sqsFlag)) {
            // parser 解析必须的数据
            initRedisForExcelParser(uuid, cloudFileObject);
            // 用于 sql 的数据拼装
            String body = generateSqsRequestJsonStr(cloudFileObject.getFolder() + uuid, cloudFileObject);
            sendSqs(cloudFileObject.getFolder() + uuid, body);
        }
        if (BooleanUtil.isTrue(progressFlag)) {
            // 用于 apn 任务进度的初始化数据
            initTaskProgressRedis(uuid, cloudFileObject, hotListId);
        }
    }

    public void initParserRedisAndSendSqlByUploadUrlDto(TalentExcelUploadUrlDto uploadUrlDto) {
        CloudFileObjectMetadata cloudFileObject = new CloudFileObjectMetadata();
        cloudFileObject.setContentType(uploadUrlDto.getContentType());
        cloudFileObject.setFileName(uploadUrlDto.getFileName());
        initRedisForExcelParser(uploadUrlDto.getUuid(), cloudFileObject);
    }

    public void initTaskProgressRedis(String uuid, CloudFileObjectMetadata cloudFileObject, String hotListId) {
        Map<String, String> map = new HashMap<>(8);
        map.put(RedisConstants.METADATA_KEY_FILENAME, cloudFileObject.getFileName());
        map.put(RedisConstants.METADATA_KEY_STATUS, TalentExcelStatusEnum.NOT_STARTED.name());
        map.put(RedisConstants.METADATA_KEY_CONTENT_TYPE, cloudFileObject.getContentType());
        map.put(RedisConstants.METADATA_KEY_TOKEN, SecurityUtils.getCurrentUserToken());
        commonRedisService.hsetForParserRedis(applicationProperties.getCreateTalentByExcelProgress() + taskIdWithTenantIdAndHotListId(uuid, hotListId) + RedisConstants.DATA_KEY_METADATA, map, RedisConstants.REDIS_EXPIRE_TIME);
    }

    public String taskIdWithTenantIdAndHotListId(String taskId, String hotListId) {
        return taskId + "-" + SecurityUtils.getTenantId() + (StrUtil.isNotBlank(hotListId)? "-" + hotListId: "");
    }

    public void initRedisForExcelParser(String uuid, CloudFileObjectMetadata cloudFileObject) {
        initializeRedisForExcelParser(uuid, cloudFileObject.getFileName(), cloudFileObject.getContentType());
    }

    public void initializeRedisForExcelParser(String uuid, String filename, String contentType) {
        String key = "parser:address_list:" + uuid + ":metadata";
        Map<String, String> set = new HashMap<>();
        set.put("requester", "apn");
        if (StringUtils.isNotEmpty(filename)) {
            set.put("filename", filename);
        }
        if (StringUtils.isNotEmpty(contentType)) {
            set.put("ContentType", contentType);
        }
        set.put("terminate", "0");
        commonRedisService.hsetForParserRedis(key, set, RedisConstants.REDIS_EXPIRE_TIME);
        log.info("[uuid: {}, initializeRedisForExcelParser] Write metadata(filename: {} and contentType: {}) into redis with key: {} and ttl: {}", uuid, filename, contentType, key, RedisConstants.REDIS_EXPIRE_TIME);
    }

    public String generateSqsRequestJsonStr(String uuid, CloudFileObjectMetadata metadata) {
        com.alibaba.fastjson.JSONObject object = new com.alibaba.fastjson.JSONObject();
        object.fluentPut("key", uuid)
                .fluentPut("contentType", metadata.getContentType())
                .fluentPut("size", metadata.getContentLength());

        com.alibaba.fastjson.JSONObject bucket = new com.alibaba.fastjson.JSONObject();
        bucket.fluentPut("name", metadata.getBucketName());

        com.alibaba.fastjson.JSONObject s3 = new com.alibaba.fastjson.JSONObject();
        s3.fluentPut("bucket", bucket).fluentPut("object", object);

        com.alibaba.fastjson.JSONObject record = new com.alibaba.fastjson.JSONObject();
        record.fluentPut("awsRegion", applicationProperties.getAddressListSqsRegion());
        record.fluentPut("s3", s3);

        JSONArray recordArray = new JSONArray();
        recordArray.add(record);

        com.alibaba.fastjson.JSONObject res = new com.alibaba.fastjson.JSONObject();
        res.fluentPut("Records", recordArray);

        return res.toJSONString();
    }

    private void sendSqs(String taskId, String body) {
        AmazonSQS sqs = AmazonSQSClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(applicationProperties.getAccessKey(), applicationProperties.getSecretKey())))
                .withRegion(applicationProperties.getAddressListSqsRegion())
                .build();
        String queueUrl = sqs.getQueueUrl(applicationProperties.getAddressListSqsQueue()).getQueueUrl();
        SendMessageRequest request = new SendMessageRequest()
                .withQueueUrl(queueUrl)
                .withMessageBody(body);
        SendMessageResult result = sqs.sendMessage(request);
        log.info("uuid: {}, sendMessageToSQS] send message to SQS, return result: {}", taskId, result);
    }

}
