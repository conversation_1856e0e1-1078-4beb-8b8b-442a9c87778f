package com.altomni.apn.talent.service.vo.linkedinproject;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.ContactTypeConverter;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinTalentContact;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LinkedinTalentContactVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -7571338464020633550L;

    private Long id;

    private String linkedinTalentId;

    @Convert(converter = ContactTypeConverter.class)
    private ContactType type;

    private String contact;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLinkedinTalentId() {
        return linkedinTalentId;
    }

    public void setLinkedinTalentId(String linkedinTalentId) {
        this.linkedinTalentId = linkedinTalentId;
    }

    public ContactType getType() {
        return type;
    }

    public void setType(ContactType type) {
        this.type = type;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinTalentContactVO linkedinTalentContact = (LinkedinTalentContactVO) o;
        if (linkedinTalentContact.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinTalentContact.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinTalentContact{" +
            "id=" + id +
            ", linkedinTalentId='" + linkedinTalentId + '\'' +
            ", type=" + type +
            ", contact='" + contact + '\'' +
            '}';
    }

    public static LinkedinTalentContactVO fromLinkedinTalentContact(LinkedinTalentContact linkedinTalentContact) {
        LinkedinTalentContactVO linkedinTalentContactVO = new LinkedinTalentContactVO();
        ServiceUtils.myCopyProperties(linkedinTalentContact, linkedinTalentContactVO);
        return linkedinTalentContactVO;
    }
}
