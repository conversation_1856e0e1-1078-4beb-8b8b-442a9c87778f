package com.altomni.apn.talent.web.rest.vm;

import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.dict.TalentLanguageRelation;
import com.altomni.apn.common.domain.talent.TalentAdditionalInfo;
import lombok.Data;

import java.sql.Timestamp;
import java.time.Instant;
import java.util.Set;

@Data
public class HotlistTalentVM {

    private Long id;

    private Long tenantId;

    private String firstName;

    private String lastName;

    private String fullName;

    private TalentAdditionalInfo talentAdditionalInfo;

    private Set<TalentLanguageRelation> languages;

    private String createdBy;

    private Instant createdDate = Instant.now();

    private String lastModifiedBy;

    private Instant lastModifiedDate = Instant.now();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getTalentExtendedInfo() {
        String extendedInfo = null;
        if (talentAdditionalInfo != null) {
            extendedInfo = talentAdditionalInfo.getExtendedInfo();
        }
        return extendedInfo == null ? "" : extendedInfo;
    }

    public void setTalentExtendedInfo(String extendedInfo) {
        if (StrUtil.isNotEmpty(extendedInfo)) {
            if (talentAdditionalInfo == null) {
                talentAdditionalInfo = new TalentAdditionalInfo();
            }
            talentAdditionalInfo.setExtendedInfo(extendedInfo);
        }
    }

    public void setAdditionalInfoId(Long id) {
        if (id != null) {
            if (talentAdditionalInfo == null) {
                talentAdditionalInfo = new TalentAdditionalInfo();
            }
            talentAdditionalInfo.setId(id);
        }
    }

    public Long getAdditionalInfoId() {
        if (talentAdditionalInfo == null) {
            return null;
        }
        return talentAdditionalInfo.getId();
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        if (createdDate != null) {
            this.createdDate = createdDate.toInstant();
        }
    }

    public Instant getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Timestamp lastModifiedDate) {
        if (lastModifiedDate != null) {
            this.lastModifiedDate = lastModifiedDate.toInstant();
        }
    }

    public Set<TalentLanguageRelation> getLanguages() {
        return languages;
    }

    public void setLanguages(Set<TalentLanguageRelation> languages) {
        this.languages = languages;
    }

    public HotlistTalentVM(Long id, Long tenantId, String firstName, String lastName, String fullName, String createdBy, Instant createdDate, String lastModifiedBy, Instant lastModifiedDate) {
        this.id = id;
        this.tenantId = tenantId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.fullName = fullName;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.lastModifiedBy = lastModifiedBy;
        this.lastModifiedDate = lastModifiedDate;
    }
}
