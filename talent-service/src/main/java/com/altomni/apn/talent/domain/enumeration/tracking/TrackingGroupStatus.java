package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TrackingGroupStatus enumeration.
 */
public enum TrackingGroupStatus implements ConvertedEnum<Integer> {
    IN_ACTIVE(10),
    ACTIVE(20);


    private final Integer dbValue;

    TrackingGroupStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TrackingGroupStatus, Integer> resolver =
        new ReverseEnumResolver<>(TrackingGroupStatus.class, TrackingGroupStatus::toDbValue);

    public static TrackingGroupStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
