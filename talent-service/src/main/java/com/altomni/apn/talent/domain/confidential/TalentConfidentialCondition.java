package com.altomni.apn.talent.domain.confidential;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.altomni.apn.common.domain.dict.TalentIndustryRelation;
import com.altomni.apn.common.domain.dict.TalentJobFunctionRelation;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.utils.JsonUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Getter
@Setter
public class TalentConfidentialCondition {

    private Long id;

    private String currency;

    private RateUnitType payType;

    private JSONObject salaryRange;

    private List<TalentEducationDTO> educations;

    private Set<TalentJobFunctionRelation> jobFunctions;

    private Set<TalentIndustryRelation> industries;


    private final List<EnumDegree> allDegrees;
    private final List<EnumCurrency> allCurrencies;

    public TalentConfidentialCondition(List<EnumDegree> allDegrees, List<EnumCurrency> allCurrencies) {
        this.allDegrees = allDegrees;
        this.allCurrencies = allCurrencies;
    }


    // Extracts the highest degree from the educations list.
    public Optional<EnumDegree> extractHighestDegree() {
        if (educations == null || educations.isEmpty()) {
            return Optional.empty();
        }
        List<String> degreeNames = educations.stream().map(TalentEducationDTO::getDegreeLevel).toList();
        return allDegrees.stream().filter(degree -> degreeNames.contains(degree.getName()))
                .max(Comparator.comparing(EnumDegree::getScore));
    }

    // Extracts the USD yearly salary from the salary range.
    public Optional<BigDecimal> extractUSDYearlySalary() {
        if (salaryRange == null || currency == null) {
            return Optional.empty();
        }
        // 按照薪资范围最小值
        String gte = salaryRange.getStr("gte");
        if (gte == null) {
            return Optional.empty();
        }
        BigDecimal getSalary = new BigDecimal(gte);
        // 先转换成USD
        Optional<BigDecimal> usdGetSalary = allCurrencies.stream().filter(c -> c.getName().equals(currency))
                .findFirst()
                .map(enumCurrency -> getSalary.multiply(new BigDecimal(enumCurrency.getFromUsdRate().toString())));
        // 再转换成年薪
        Integer yearlySalary = switch (payType) {
            case HOURLY -> 2000;
            case DAILY -> 250;
            case WEEKLY -> 50;
            case MONTHLY -> 12;
            case YEARLY -> 1;
        };
        return usdGetSalary.map(salary -> salary.multiply(new BigDecimal(yearlySalary.toString())));
    }

    public Set<Long> extractJobFunctionIds() {
        return jobFunctions.stream().map(TalentJobFunctionRelation::getUniqueEnumId)
                .map(Integer::longValue).collect(java.util.stream.Collectors.toSet());
    }


    public Set<Long> extractIndustryIds() {
        return industries.stream().map(TalentIndustryRelation::getUniqueEnumId)
                .map(Integer::longValue).collect(java.util.stream.Collectors.toSet());
    }

    // 薪资规则是否匹配, 调用方法前必须需要验证有薪资规则，没设置规则和规则不匹配是两种情况
    public boolean matchSalaryRule(ConfidentialRule rule) {
        BigDecimal salaryRule = rule.getUSDYearlySalary(allCurrencies)
                .orElseThrow(() -> new IllegalArgumentException("No USD yearly salary rule found."));
        Optional<BigDecimal> userSalary = extractUSDYearlySalary();
        return userSalary.filter(salary -> salary.compareTo(salaryRule) >= 0).isPresent();
    }

    // 行业是否匹配, 调用方法前必须需要验证有薪资规则，没设置规则和规则不匹配是两种情况
    public boolean matchIndustriesRule(ConfidentialRule rule) {
        Set<Long> industryRule = Set.copyOf(rule.getIndustries());
        if (industryRule.isEmpty()) {
            throw new IllegalArgumentException("No industry rule found.");
        }
        Set<Long> industryIds = extractIndustryIds();
        return industryIds.stream().anyMatch(industryRule::contains);
    }


    // 职能是否匹配, 调用方法前必须需要验证有薪资规则，没设置规则和规则不匹配是两种情况
    public boolean matchJobFunctionsRule(ConfidentialRule rule) {
        Set<Long> jobFunctionRule = Set.copyOf(rule.getJobFunctions());
        if (jobFunctionRule.isEmpty()) {
            throw new IllegalArgumentException("No jobFunction rule found.");
        }
        Set<Long> jobFunctionIds = extractJobFunctionIds();
        return jobFunctionIds.stream().anyMatch(jobFunctionRule::contains);
    }

    // 学历是否匹配, 调用方法前必须需要验证有薪资规则，没设置规则和规则不匹配是两种情况
    public boolean matchDegreeRule(ConfidentialRule rule) {
        EnumDegree ruleDegreeLevel = allDegrees.stream().filter(d -> d.getName().equals(rule.getDegreeLevel()))
                .findFirst().orElseThrow(() -> new IllegalArgumentException("No degree level rule found."));
        Optional<EnumDegree> highestDegreeUserOpt = extractHighestDegree();
        return highestDegreeUserOpt.filter(degree -> degree.getScore() >= ruleDegreeLevel.getScore()).isPresent();
    }

    // 是否匹配所有的保密规则，保密的规则之间是且的关系
    public boolean matchRules(ConfidentialRule rule) {
        if (rule.hasSalaryRule() && !matchSalaryRule(rule)) {
            log.info("Talent {} salary not match salary rule: {}", id, JsonUtil.toJson(rule.getConfidentialityRules().getSalary()));
            return false;
        }
        if (rule.hasIndustryRule() && !matchIndustriesRule(rule)) {
            log.info("Talent {} industry not match industry rule: {}", id, JsonUtil.toJson(rule.getConfidentialityRules().getIndustries()));
            return false;
        }
        if (rule.hasJobFunctionRule() && !matchJobFunctionsRule(rule)) {
            log.info("Talent {} jobFunction not match jobFunction rule: {}", id, JsonUtil.toJson(rule.getConfidentialityRules().getJobFunctions()));
            return false;
        }
        if (rule.hasDegreeLevelRule() && !matchDegreeRule(rule)) {
            log.info("Talent {} degree not match degree rule: {}", id, JsonUtil.toJson(rule.getConfidentialityRules().getDegreeLevel()));
            return false;
        }
        return true;
    }
}
