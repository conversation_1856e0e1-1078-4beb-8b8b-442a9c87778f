package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.talent.domain.talent.QWatchList;
import com.altomni.apn.talent.domain.talent.WatchList;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


/**
 * Spring Data JPA repository for the WatchList entity.
 */
@Repository
public interface WatchListRepository extends JpaRepository<WatchList,Long>, QuerydslPredicateExecutor<WatchList>, QuerydslBinderCustomizer<QWatchList> {

    @Override
    default public void customize(QuerydslBindings bindings, QWatchList root) {
        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    }

    Optional<WatchList> findOneByUserIdAndTalentIdAndJobId(Long userId, Long talentId, Long jobId);

    void deleteByTalentIdIn(Collection<Long> talentIds);

    void deleteByIdIn(Collection<Long> ids);

    List<WatchList> findAllByUserIdAndTalentId(Long userId, Long talentId);
 }
