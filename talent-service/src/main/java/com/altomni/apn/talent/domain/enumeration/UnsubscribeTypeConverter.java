package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class UnsubscribeTypeConverter extends AbstractAttributeConverter<UnsubscribeType, Integer> {
    public UnsubscribeTypeConverter() {
        super(UnsubscribeType::toDbValue, UnsubscribeType::fromDbValue);
    }
}
