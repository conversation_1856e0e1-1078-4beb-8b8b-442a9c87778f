package com.altomni.apn.talent.service.dto.folder;

import com.altomni.apn.common.dto.folder.CustomFolderSharingTargetDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;


@ApiModel

public class TalentFolderDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "talent folder id")
        private Long id;

        @ApiModelProperty(value = "talent folder name")
        private String name;

        @ApiModelProperty(value = "talent folder note")
        private String folderNote;


        @ApiModelProperty(value = "talent folder sharing target")
        @Valid
        private List<CustomFolderSharingTargetDTO> shareTo;

        @ApiModelProperty(value = "Candidates added during creation")
        private List<Long> talentIds;

        public Long getId() {
                return id;
        }

        public void setId(Long id) {
                this.id = id;
        }

        public String getName() {
                return name;
        }

        public void setName(String name) {
                this.name = name;
        }

        public List<CustomFolderSharingTargetDTO> getShareTo() {
                return shareTo;
        }

        public void setShareTo(List<CustomFolderSharingTargetDTO> shareTo) {
                this.shareTo = shareTo;
        }

        public String getFolderNote() {
                return folderNote;
        }

        public void setFolderNote(String folderNote) {
                this.folderNote = folderNote;
        }

        public List<Long> getTalentIds() {
                return talentIds;
        }

        public void setTalentIds(List<Long> talentIds) {
                this.talentIds = talentIds;
        }
}
