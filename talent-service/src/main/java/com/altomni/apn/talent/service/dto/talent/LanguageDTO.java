package com.altomni.apn.talent.service.dto.talent;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.talent.domain.enumeration.Necessity;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LanguageDTO implements Serializable {

    private static final long serialVersionUID = -1387288056631129913L;

    private Necessity necessity;

    private Float score;

    private String regulatedName;

    public LanguageDTO() {
    }

    public JSONObject toJSON() {
        return new JSONObject().fluentPut("necessity", necessity).fluentPut("score", score).fluentPut("regulatedName", regulatedName);
    }

    public Necessity getNecessity() {
        return necessity;
    }

    public void setNecessity(Necessity necessity) {
        this.necessity = necessity;
    }

    public Float getScore() {
        return score;
    }

    public void setScore(Float score) {
        this.score = score;
    }

    public String getRegulatedName() {
        return regulatedName;
    }

    public void setRegulatedName(String regulatedName) {
        this.regulatedName = regulatedName;
    }

    @Override
    public String toString() {
        return "LanguageDTO{" +
                "necessity=" + necessity +
                ", score=" + score +
                ", regulatedName='" + regulatedName + '\'' +
                '}';
    }
}
