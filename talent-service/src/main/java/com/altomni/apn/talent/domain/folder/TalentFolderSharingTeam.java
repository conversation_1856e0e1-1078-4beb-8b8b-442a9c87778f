package com.altomni.apn.talent.domain.folder;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.common.enumeration.folder.FolderPermissionConverter;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@Table(name = "talent_folder_sharing_team")
public class TalentFolderSharingTeam extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_folder_id", nullable = false)
    private Long talentFolderId;


    @Column(name = "team_id", nullable = false)
    private Long teamId;

    @Convert(converter = FolderPermissionConverter.class)
    @Column(name = "permission", nullable = false)
    private FolderPermission permission;

    @ApiModelProperty("store raw json with list of id with permission")
    @Column(name = "excluded_user_ids")
    @JsonRawValue
    private String excludedUserIds;

    public boolean hasWritePermission(Long teamId) {
        return this.teamId.equals(teamId) && this.permission.equals(FolderPermission.READWRITE);

    }

    public boolean isSameFolderSharing(TalentFolderSharingTeam talentFolderSharingTeam) {
        return this.getTeamId().equals(talentFolderSharingTeam.getTeamId())
                && this.getPermission().equals(talentFolderSharingTeam.getPermission());
    }

    public boolean hasReadPermission(){
        return (this.permission.toDbValue() & 0x6) != 0;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentFolderId() {
        return talentFolderId;
    }

    public void setTalentFolderId(Long talentFolderId) {
        this.talentFolderId = talentFolderId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public FolderPermission getPermission() {
        return permission;
    }

    public void setPermission(FolderPermission permission) {
        this.permission = permission;
    }

    public String getExcludedUserIds() {
        return excludedUserIds;
    }

    public void setExcludedUserIds(String excludedUserIds) {
        this.excludedUserIds = excludedUserIds;
    }
}
