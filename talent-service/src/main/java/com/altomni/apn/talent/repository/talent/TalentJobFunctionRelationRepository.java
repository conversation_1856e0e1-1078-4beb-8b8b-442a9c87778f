package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.dict.TalentJobFunctionRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface TalentJobFunctionRelationRepository extends JpaRepository<TalentJobFunctionRelation, Long> {

    @Modifying
    @Transactional
    @Query(value = "delete from talent_job_function_relation where talent_id = ?1", nativeQuery = true)
    void deleteAllByTalentId(Long talentId);

    List<TalentJobFunctionRelation> findAllByTalentId(Long talentId);

}
