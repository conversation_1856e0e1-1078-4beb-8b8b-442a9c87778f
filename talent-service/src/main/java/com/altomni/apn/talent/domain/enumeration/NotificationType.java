package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum NotificationType implements ConvertedEnum<Integer> {
    JobUpdate(1),
    ApplicationUpdate(2);

    private Integer dbValue;

    NotificationType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<NotificationType, Integer> resolver =
        new ReverseEnumResolver<>(NotificationType.class, NotificationType::toDbValue);

    public static NotificationType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
