package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.talent.QTalentV3;
import com.altomni.apn.common.domain.talent.TalentAdditionalInfo;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import com.altomni.apn.common.vo.talent.TalentToHrVO;
import com.altomni.apn.job.service.dto.redis.RecommendedTalent;
import com.altomni.apn.talent.service.dto.talent.TalentExperienceInfoDTO;
import com.altomni.apn.talent.service.dto.talent.TalentLastModifiedTimeDTO;
import com.altomni.apn.talent.service.dto.talent.TalentLinkedinDTO;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;


/**
 * Spring Data JPA repository for the Talent entity.
 */
@Repository
public interface TalentRepository extends JpaRepository<TalentV3, Long> , QuerydslPredicateExecutor<TalentV3>, QuerydslBinderCustomizer<QTalentV3> {

    @Override
    default void customize(QuerydslBindings bindings, QTalentV3 root) {
        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    }

    @Override
    @NotNull
    @EntityGraph(attributePaths = {"languages", "industries", "jobFunctions", "workAuthorization", "talentAdditionalInfo"})
    @Query(value = "SELECT t FROM TalentV3 t WHERE t.id = :id")
    Optional<TalentV3> findById(@NotNull @Param("id") Long id);


    @Query(value = "SELECT full_name FROM talent where id=?1", nativeQuery = true)
    String findTalentFullName(Long id);

    @Query(value = "select t.* from talent t where t.id not in(select data_id from async_record where data_type = 15 and response_code = 200) limit 10000 ", nativeQuery = true)
    List<TalentV3> findNoSyncTalents();

    @Query(value = "SELECT t FROM TalentV3 t INNER JOIN AsyncRecord r ON t.id = r.dataId AND r.asyncType = com.altomni.apn.job.domain.enumeration.AsyncEnum.ASYNC_TYPE_SINGLE AND r.dataType = com.altomni.apn.job.domain.enumeration.AsyncEnum.DATA_TYPE_TALENT AND r.status = com.altomni.apn.common.domain.enumeration.user.Status.Failed")
    List<TalentV3> findSyncFailedTalents();

    List<TalentV3> findAllByIdIn(List<Long> ids);

    @EntityGraph(attributePaths = { "industries", "jobFunctions", "talentAdditionalInfo"})
    List<TalentV3> findAllByIdIsIn(Collection<Long> ids);

    @Query(value = "SELECT t.id FROM TalentV3 t WHERE MOD(t.id, ?1) = ?2")
    List<Long> findTalentIdsByPartitionIdSync(Integer threads, Integer partitionId);

    @Query(value = "SELECT t.id FROM TalentV3 t " +
            "LEFT JOIN AsyncRecord ar " +
            "   ON ar.asyncType = com.altomni.apn.job.domain.enumeration.AsyncEnum.ASYNC_TYPE_ESFILLER_BATCH AND ar.dataType = com.altomni.apn.job.domain.enumeration.AsyncEnum.DATA_TYPE_TALENT AND ar.status = com.altomni.apn.common.domain.enumeration.user.Status.Available AND ar.dataId = t.id " +
            "WHERE t.lastEditedTime > ar.createdDate OR ar.id IS NULL " +
            "GROUP BY t.id")
    List<Long> findBatchSyncOutOfSyncTalentIds();

    @Query(value = "SELECT t.id FROM TalentV3 t WHERE t.syncPaused = FALSE AND (t.lastSyncTime IS NULL OR t.lastEditedTime > t.lastSyncTime)")
    List<Long> findOutOfSyncTalentIds();

    @Query(value = "SELECT t.id FROM talent t WHERE t.sync_paused = FALSE AND (t.last_sync_time IS NULL OR t.last_edited_time > t.last_sync_time) order by t.last_edited_time desc limit ?1, ?2", nativeQuery = true)
    List<Long> findOutOfSyncTalentIds(Integer lineNum, Integer pageNum);

    @Query(value = "SELECT count(t.id) FROM TalentV3 t WHERE t.syncPaused = FALSE AND (t.lastSyncTime IS NULL OR t.lastEditedTime > t.lastSyncTime)")
    Long countOutOfSyncTalentIds();

    @Modifying
    @Transactional
    @Query(value = "UPDATE talent t SET t.additional_info_id = ?2 WHERE t.id = ?1", nativeQuery = true)
    void updateTalenAdditionalInfoIdById(Long talentId, Long additionalInfoId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentV3 t SET t.lastSyncTime = current_timestamp WHERE t.id = ?1")
    void updateTalentLastSyncTime(Long talentId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentV3 t SET t.lastEditedTime = current_timestamp WHERE t.id = ?1")
    void updateTalentLastEditedTime(Long talentId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentV3 t SET t.lastEditedTime = current_timestamp, t.ownedByTenants = ?2 WHERE t.id = ?1")
    void updateTalentLastEditedTimeAndOwnedDate(Long talentId, Long tenantId);

    @Modifying
    @Transactional
    @Query(value = " UPDATE talent t SET t.last_sync_time = ?2 WHERE t.id = ?1 ", nativeQuery = true)
    void updateTalentLastSyncTime(Long talentId, Instant lastSyncTime);

    @Modifying
    @Transactional
    @Query(value = " UPDATE talent t SET t.first_name = ?2, t.last_name = ?3, t.full_name = ?4 WHERE t.id = ?1 ", nativeQuery = true)
    void updateTalentNameInfo(Long talentId, String firstName, String lastName, String fullName);


    @Query(value = "select crm_contact_id from company_sales_lead_client_contact " +
            "where talent_id=:talentId", nativeQuery = true)
    Set<Long> findCrmContactIds(@Param("talentId") Long talentId);

    @Query(value = "select id from talent where tenant_id = ?1",nativeQuery = true)
    List<Long> findIdsByTenant(Long tenantId);

    @Query(value = "SELECT photo_url FROM talent where id=?1", nativeQuery = true)
    String findTalentPhotoUrl(Long id);


    @Query(value = "SELECT id, first_name firstName, last_name lastName, full_name fullName, tenant_id tenantId FROM talent WHERE id IN (?1)", nativeQuery = true)
    List<Map<String, Object>> findAllTalentNameByIdIn(List<Long> idList);

    @Query(value = "SELECT id FROM talent WHERE id IN (?1)", nativeQuery = true)
    List<Long> findAllTalentIdByIdIn(List<Long> idList);

    @Query(value = "select team_id from permission_user_team where user_id = ?1 " +
            "UNION ALL " +
            "select team_id from permission_extra_user_team where user_id = ?1 " +
            "UNION ALL " +
            "(select pert.team_id as team_id from permission_extra_role_team pert left join user_role ur on ur.role_id = pert.role_id where ur.user_id = ?1)", nativeQuery = true)
    List<Long> getTeamIdsByUserId(Long userId);

    @Query(value = " select data_scope from user where id = ?1", nativeQuery = true)
    Integer getDataScope(Long userId);

    @Query(value = " select max(r.data_scope) from user_role ur left join role r on ur.role_id = r.id where ur.user_id = ?1 group by ur.user_id ", nativeQuery = true)
    Integer getRoleDateScope(Long userId);

    @Query(value = "select t.id from TalentV3 t")
    List<Long> findAllIds();

    Integer countByTenantId(Long tenantId);

    @Modifying
    @Transactional
    @Query(value = "update talent t SET t.motivation_id = ?2 WHERE t.id = ?1", nativeQuery = true)
    void updateMotivation(Long talentId, Integer noteStatus);


    @Query("select new com.altomni.apn.job.service.dto.redis.RecommendedTalent(t.id, t.fullName, t.talentAdditionalInfo.extendedInfo, t.createdDate) from TalentV3 t " +
            "where t.id in :talentIds")
    List<RecommendedTalent> findTalentsForAiRecommendationByIds(@Param("talentIds") Collection<Long> talentIds);

    List<TalentV3> findAllByIdInAndTenantId(List<Long> talentIds, Long tenantId);


    @Query(value = "SELECT new com.altomni.apn.common.dto.talent.TalentBriefDTO(t.id,t.fullName,u.firstName,u.lastName, u.id, t.createdDate, t.tenantId) FROM TalentV3  t" +
            " left join User u  on t.createdBy = u.uid " +
            "where t.id in :ids")
    List<TalentBriefDTO> getTalentsByIdsWithoutEntity(@Param("ids") Set<Long> ids);

    @Query(value = "SELECT new com.altomni.apn.common.vo.talent.TalentBriefVO(t.id, t.firstName, t.lastName, t.fullName) FROM TalentV3 t where t.id in :ids")
    Set<TalentBriefVO> getAllBriefTalentsByIds(@Param("ids") Set<Long> ids);

    @Query(value = "SELECT    MAX(last_modified_date) AS latest_last_modified_date FROM" +
            "( SELECT last_modified_date FROM talent WHERE id = ?1" +
            "    UNION ALL SELECT tai.last_modified_date FROM talent_additional_info tai LEFT JOIN talent t on tai.id = t.additional_info_id WHERE t.id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM start WHERE talent_id = ?1        " +
            "    UNION ALL SELECT last_modified_date FROM talent_ownership WHERE talent_id = ?1    " +
            "    UNION ALL SELECT last_modified_date FROM talent_contact WHERE talent_id = ?1  " +
            "    UNION ALL SELECT last_modified_date FROM talent_industry_relation WHERE talent_id = ?1    " +
            "    UNION ALL SELECT last_modified_date FROM talent_job_function_relation WHERE talent_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM talent_language_relation WHERE talent_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM talent_work_authorization_relation WHERE talent_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM talent_resume_relation WHERE talent_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM talent_current_location WHERE talent_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM talent_note WHERE talent_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM talent_recruitment_process WHERE talent_id = ?1 " +
            ") AS dates;", nativeQuery = true)
    Instant getTalentEsLastModifiedTime(Long talentId);

    @Query(value = """
            SELECT    MAX(last_modified_date) AS lastModifiedDate ,id as talentId FROM\s
             ( SELECT last_modified_date,id FROM talent WHERE id in ?1
                 UNION ALL SELECT tai.last_modified_date,t.id FROM talent_additional_info tai LEFT JOIN talent t on tai.id = t.additional_info_id WHERE t.id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM start WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_ownership WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_contact WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_industry_relation WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_job_function_relation WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_language_relation WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_work_authorization_relation WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_resume_relation WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_current_location WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_note WHERE talent_id in ?1
                 UNION ALL SELECT last_modified_date,talent_id FROM talent_recruitment_process WHERE talent_id in ?1
             ) AS dates group by id
            """, nativeQuery = true)
    List<TalentLastModifiedTimeDTO> getTalentEsLastModifiedTimeList(List<Long> talentId);

    @Query(value = "select info.id as talentInfoId, info.extended_info -> '$.experiences' as experiences from talent_additional_info info where id > :talentInfoId limit :pageSize", nativeQuery = true)
    List<TalentExperienceInfoDTO> getTalentExperiencesByPage(@Param("talentInfoId") Long talentInfoId, @Param("pageSize") Integer pageSize);

    @Query(value = "select id as talentContactId, talent_id as talentId, contact as linkedinId from talent_contact " +
            "where talent_id in (select id from talent where additional_info_id=:talentInfoId) and jhi_type=7 and status=0", nativeQuery = true)
    List<TalentLinkedinDTO> getTalentLinkedin(@Param("talentInfoId") Long talentInfoId);

    @Query(value = "select id from talent " +
            "where id in :talentsIds and puser_id=:permissionUserId", nativeQuery = true)
    Set<Long> filterByPermissionUserId(@Param("talentsIds") Set<Long> talentsIds, @Param("permissionUserId") Long permissionUserId);

    @Query(value = "select tal.id from permission_team t " +
            "inner join permission_user_team ut on ut.team_id=t.id " +
            "inner join talent tal on ut.user_id = tal.puser_id " +
            "where tal.id in :talentIds and ut.is_primary=1 and t.id in :teamIds", nativeQuery = true)
    Set<Long> filterTalentsByTalentIdInAndTeamIdIn(@Param("talentIds") Set<Long> talentIds, @Param("teamIds") Collection<Long> teamIds);

    @Query(value = "select distinct c.talent_id from company_sales_lead_client_contact c " +
            "inner join talent t on t.id=c.talent_id " +
            "left join talent_ownership o on o.talent_id=t.id " +
            "where t.puser_id=:userId or (o.ownership_type=2 and o.user_id=:userId)", nativeQuery = true)
    List<Long> getTalentIdsByOwnerId(@Param("userId") Long userId);

    @Query("select t.talentAdditionalInfo from TalentV3 t where t.id=:talentId")
    Optional<TalentAdditionalInfo> getTalentAdditionalInfo(@Param("talentId") Long talentId);

    @Modifying
    @Transactional
    @Query(value = "update talent t SET t.is_need_sync_hr = 1 WHERE t.id = ?1", nativeQuery = true)
    void updateTalentNeedSyncToHr(Long talentId);


    @Query(value = """
        SELECT
    	t.id,
    	t.tenant_id tenantId,
    	t.full_name fullName,
    	t.photo_url photoUrl,
    	(select tc.contact from talent_contact tc where tc.talent_id = t.id and tc.jhi_type = 2 and tc.status = 0 order by sort asc limit 1) email
    FROM
    	talent t
    where t.id in ?1
    """, nativeQuery = true)
    List<TalentToHrVO> getTalentDataSyncToHr(List<Long> talentIdList);

    @Query("select t.tenantId from TalentV3 t where t.id=:talentId")
    Long findTenantIdById(@Param("talentId")Long talentId);
}
