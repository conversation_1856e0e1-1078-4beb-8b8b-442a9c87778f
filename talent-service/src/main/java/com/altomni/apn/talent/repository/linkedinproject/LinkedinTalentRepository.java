package com.altomni.apn.talent.repository.linkedinproject;

import com.altomni.apn.talent.domain.linkedinproject.LinkedinTalent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the LinkedinTalent entity.
 */
@Repository
public interface LinkedinTalentRepository extends JpaRepository<LinkedinTalent, String> {

    Page<LinkedinTalent> findAllByTenantId(Long tenantId, Pageable pageable);

    Page<LinkedinTalent> findAllByTenantIdAndIdIn(Long tenantId, List<String> ids, Pageable pageable);
}
