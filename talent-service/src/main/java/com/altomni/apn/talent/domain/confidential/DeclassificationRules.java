package com.altomni.apn.talent.domain.confidential;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Collections;
import java.util.Objects;
import java.util.Set;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DeclassificationRules {
    /**
     * 自动解除保密天数
     */
    private Integer declassifyDays;
    /**
     * 自动解除保密流程类型
     */
    private Set<Long> declassifyProcessIds = Collections.emptySet();
    /**
     * 自动解除保密流程节点
     */
    private Set<NodeType> declassifyProcessTypes = Collections.emptySet();
    /**
     * 流程自动解密延迟天数
     */
    private Integer declassifyProcessDelayDays;


    public boolean sameRule(DeclassificationRules other) {
        if (this == other) {
            return true;
        }
        if (other == null) {
            return false;
        }

        return this.declassifyDays.equals(other.declassifyDays) &&
                this.declassifyProcessIds.equals(other.declassifyProcessIds) &&
                this.declassifyProcessTypes.equals(other.declassifyProcessTypes) &&
                ((this.declassifyProcessDelayDays == null && other.declassifyProcessDelayDays == null)
                    || (Objects.equals(this.declassifyProcessDelayDays, other.declassifyProcessDelayDays)));
    }


    @Converter
    public static class DeclassificationRulesConverter implements AttributeConverter<DeclassificationRules, String> {

        private final static ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public String convertToDatabaseColumn(DeclassificationRules attribute) {
            if (attribute == null) {
                return "{}";
            }
            try {
                return objectMapper.writeValueAsString(attribute);
            } catch (Exception e) {
                throw new RuntimeException("Conversion error", e);
            }
        }

        @Override
        public DeclassificationRules convertToEntityAttribute(String dbData) {
            if (dbData == null || dbData.isBlank() || dbData.equals("{}")) {
                return null;
            }
            try {
                return objectMapper.readValue(dbData, new TypeReference<>() {
                });
            } catch (Exception e) {
                throw new RuntimeException("Conversion error", e);
            }
        }
    }
}
