package com.altomni.apn.talent.repository.hotlist;

import com.altomni.apn.talent.domain.user.HotList;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the HotList entity.
 */
@SuppressWarnings("unused")
@Repository
public interface HotListRepository extends JpaRepository<HotList, Long> {
//    @Query(value = "SELECT l.* FROM hot_list l JOIN hot_list_user lu ON l.id = lu.hot_list_id " +
//        "WHERE (l.access_option = 0 OR lu.user_id = ?1) and l.tenant_id = ?2 ", nativeQuery = true)
    @Query(value = "SELECT l FROM HotList l JOIN HotListUser lu ON l.id = lu.hotListId " +
        "WHERE (l.accessOption = com.altomni.apn.talent.domain.enumeration.talent.HotlistAccessOption.PUBLIC OR lu.userId = ?1) and l.tenantId = ?2")
    List<HotList> findMyHotList(Long userId, Long tenantId);

    List<HotList> findAllByTitleIgnoreCaseContaining(String titleName);

    List<HotList> findAllByTitle(String titleName);

    @Query(value = "select t.* from hot_list t where title = ?1 and id <>?2 ", nativeQuery = true)
    List<HotList> findAllByTitleAndId(String titleName, Long id);
}
