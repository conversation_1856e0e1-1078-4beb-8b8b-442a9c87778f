package com.altomni.apn.talent.service.mapper.folder;


import com.altomni.apn.talent.domain.folder.TalentFolder;
import com.altomni.apn.talent.service.dto.folder.TalentFolderDTO;
import com.altomni.apn.talent.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {})
public interface TalentFolderMapper extends EntityMapper<TalentFolderDTO, TalentFolder> {
    default TalentFolder fromId(Long id) {
        if (id == null) {
            return null;
        }
        TalentFolder jobFolder = new TalentFolder();
        jobFolder.setId(id);
        return jobFolder;
    }
}
