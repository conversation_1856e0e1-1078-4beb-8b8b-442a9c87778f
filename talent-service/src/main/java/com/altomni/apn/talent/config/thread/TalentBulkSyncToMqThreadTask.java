package com.altomni.apn.talent.config.thread;

import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.EsfillerMQProperties;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class TalentBulkSyncToMqThreadTask extends CopyTokenChildThread {

    private final EsFillerTalentService esFillerTalentService;

    private final CountDownLatch countDownLatch;

    private final List<Long> ids;

    private final int priority;

    private final EsfillerMQProperties esfillerMQProperties;

    private final CanalService canalService;

    public TalentBulkSyncToMqThreadTask(EsFillerTalentService esFillerTalentService, CountDownLatch countDownLatch, List<Long> ids, int priority, EsfillerMQProperties esfillerMQProperties, CanalService canalService) {
        super();
        this.esFillerTalentService = esFillerTalentService;
        this.countDownLatch = countDownLatch;
        this.ids = ids;
        this.priority = priority;
        this.esfillerMQProperties = esfillerMQProperties;
        this.canalService = canalService;
    }

    @Override
    public void runTask() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //List<Long> failedIds = new ArrayList<>();
        try {
            esFillerTalentService.extractBulkTalentToMq(ids, priority);
        } catch (Exception e) {
            log.error("[EsFillerTalentService: syncTalentToMQ @{}] scheduledSyncTalentsToMQ is error, talentIds: {}, error {}", SecurityUtils.getUserId(), ids, ExceptionUtils.getStackTrace(e));
            canalService.insertAll(ids, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "Extract Talent Error" +
                    "\n\tTalent IDs: " + ids +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        } finally {
            countDownLatch.countDown();
            stopWatch.stop();
            //log.info("[EsFillerTalentService: syncTalentToMQ @{}] syncTalentToMqFinished time = [{} ms], successIds = [{}], failIds = [{}]", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), CollUtil.disjunction(ids, failedIds), failedIds);
        }
    }

}
