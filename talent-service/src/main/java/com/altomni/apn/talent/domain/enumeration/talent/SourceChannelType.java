package com.altomni.apn.talent.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum SourceChannelType implements ConvertedEnum<Integer> {
    UNKNOWN(0),
    LINKEDIN(1),
    LIEPIN(2),
    <PERSON>TH<PERSON>(30);

    // static resolving:
    public static final ReverseEnumResolver<SourceChannelType, Integer> resolver =
        new ReverseEnumResolver<>(SourceChannelType.class, SourceChannelType::toDbValue);
    private final int dbValue;

    SourceChannelType(int dbValue) {
        this.dbValue = dbValue;
    }

    public static SourceChannelType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }
}
