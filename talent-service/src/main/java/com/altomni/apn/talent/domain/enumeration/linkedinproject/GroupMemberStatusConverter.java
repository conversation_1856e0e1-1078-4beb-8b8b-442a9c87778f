package com.altomni.apn.talent.domain.enumeration.linkedinproject;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class GroupMemberStatusConverter extends AbstractAttributeConverter<GroupMemberStatus, Integer> {
    public GroupMemberStatusConverter() {
        super(GroupMemberStatus::toDbValue, GroupMemberStatus::fromDbValue);
    }
}
