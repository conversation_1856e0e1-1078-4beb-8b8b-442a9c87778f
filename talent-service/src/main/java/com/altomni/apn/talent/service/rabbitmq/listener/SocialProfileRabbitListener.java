package com.altomni.apn.talent.service.rabbitmq.listener;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.talent.config.env.TalentProfileMQProperties;
import com.altomni.apn.talent.constants.RedisConstants;
import com.altomni.apn.talent.repository.talent.TalentContactRepository;
import com.altomni.apn.talent.service.dto.talent.SocialProfileMQResponseDTO;
import com.altomni.apn.talent.service.dto.talent.TalentLinkedinDTO;
import com.altomni.apn.talent.service.rabbitmq.RabbitMqService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Optional;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class SocialProfileRabbitListener {

    private static final String KEY_TALENT = "talent";
    private static final String KEY_IDENTIFIER = "identifier";
    private static final String KEY_EXPERIENCES = "experiences";
    private static final String KEY_COMPANY = "company";
    private static final String KEY_TITLE = "title";

    @Resource
    private TalentContactRepository talentContactRepository;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private TalentProfileMQProperties talentProfileMQProperties;

    @RabbitListener(queues = {"${application.socialProfileMq.linkedinResponseQueue}"})
    @RabbitHandler
    public void processLinkedInSearchResponseMsg(Message message, Channel channel) throws IOException {
        try{
            String json = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("Process Profile MQ Response: LinkedIn Search Response: start processing message: {}", json);
            SocialProfileMQResponseDTO socialProfileMqResponse =  JSONUtil.toBean(json, SocialProfileMQResponseDTO.class);
            commonRedisService.set(String.format(RedisConstants.DATA_KEY_LINKEDIN_STATUS, socialProfileMqResponse.getLinkedinId()),
                    String.valueOf(socialProfileMqResponse.getStatus()),
                    talentProfileMQProperties.getMsgExpire());
            if(socialProfileMqResponse.getStatus().equals(HttpStatus.OK.value())){
                log.info("Process Profile MQ Response: LinkedIn Search Response: response status: {}", socialProfileMqResponse.getStatus());
                String requestJson = commonRedisService.get(String.format(RedisConstants.DATA_KEY_LINKEDIN_REQUEST, socialProfileMqResponse.getLinkedinId()));
                String resultJson = commonRedisService.get(String.format(RedisConstants.DATA_KEY_LINKEDIN_RESULT, socialProfileMqResponse.getLinkedinId()));
                this.detectCompanyChange(requestJson, resultJson);
//                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }else {
                log.warn("Process Profile MQ Response: LinkedIn Search Response: response status: {}", socialProfileMqResponse.getStatus());
                String requestJson = commonRedisService.get(String.format(RedisConstants.DATA_KEY_LINKEDIN_REQUEST, socialProfileMqResponse.getLinkedinId()));
                log.warn("get data from redis: " + requestJson);
                rabbitMqService.sendTalentLinkedinToDelayQueue(requestJson);
            }
            log.info("Process Profile MQ Response: LinkedIn Search Response: end processing message: {}", json);
        } catch (Exception e) {
            log.info("Process Profile MQ Response: LinkedIn Search Response: fail to process message: {}", e.getMessage());
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, true);
            throw e;
        }
    }

    private void detectCompanyChange(String requestJson, String resultJson){
        log.info("requestJson: {}", requestJson);
        log.info("resultJson: {}", resultJson);
        TalentLinkedinDTO.TalentLinkedinRequest talentLinkedinRequest = JSONUtil.toBean(requestJson, TalentLinkedinDTO.TalentLinkedinRequest.class);
        JSONObject result = new JSONObject(resultJson);
        log.info("result= {}", result);
        if (result.isEmpty()){
            return;
        }
        if (!result.containsKey(KEY_TALENT)){
            return;
        }
        JSONObject talent = result.getJSONObject(KEY_TALENT);
        log.info("talent= {}", talent);
        if (Objects.isNull(talent) || talent.isEmpty()){
            return;
        }

        String identifier = talent.getStr(KEY_IDENTIFIER, null);
        log.info("identifier= {}", identifier);
        if (StringUtils.isNotEmpty(identifier)){
            Optional<TalentContact> talentContact = talentContactRepository.findById(talentLinkedinRequest.getTalentContactId());
            if (talentContact.isPresent()){
                String linkedinInfo = talentContact.get().getInfo();
                JSONObject linkedinObject = StringUtils.isNotEmpty(linkedinInfo) ? new JSONObject(linkedinInfo) : new JSONObject();
                linkedinObject.put("publicIdentifier", identifier);
                talentContactRepository.updateLinkedinIdentifier(talentLinkedinRequest.getTalentContactId(), JSONUtil.toJsonStr(linkedinObject));
            }
        }

        if (!talent.containsKey(KEY_EXPERIENCES)){
            return;
        }
        JSONArray experiences = talent.getJSONArray(KEY_EXPERIENCES);
        log.info("experiences= {}", experiences);
        if (Objects.isNull(experiences) || experiences.isEmpty()){
            return;
        }
        JSONObject latestExperience = experiences.getJSONObject(0);
        log.info("latestExperience= {}", latestExperience);
        String latestCompany = latestExperience.getStr(KEY_COMPANY, null);
        if (StringUtils.isNotEmpty(latestCompany)
                && StringUtils.isNotEmpty(talentLinkedinRequest.getCompany())
                && !latestCompany.strip().equalsIgnoreCase(talentLinkedinRequest.getCompany().strip())){
            Long talentId = talentLinkedinRequest.getTalentId();
            rabbitMqService.sendTalentId(talentId);
            this.checkMessageCount();
            return;
        }
        String latestTitle = latestExperience.getStr(KEY_TITLE, null);
        if (StringUtils.isNotEmpty(latestTitle)
                && StringUtils.isNotEmpty(talentLinkedinRequest.getTitle())
                && !latestTitle.strip().equalsIgnoreCase(talentLinkedinRequest.getTitle().strip())){
            Long talentId = talentLinkedinRequest.getTalentId();
            rabbitMqService.sendTalentId(talentId);
            this.checkMessageCount();
        }
    }

    private void checkMessageCount(){
        rabbitMqService.checkMessageCount(talentProfileMQProperties.getTalentIdQueue());
        Integer messageCount = rabbitMqService.checkMessageCount(talentProfileMQProperties.getTalentIdQueue());
        if (messageCount.compareTo(talentProfileMQProperties.getSocialProfileMaximumMsgCount()) > 0){
            try {
                Thread.sleep(3000000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
    }
}
