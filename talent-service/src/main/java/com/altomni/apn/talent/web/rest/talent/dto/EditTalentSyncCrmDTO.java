package com.altomni.apn.talent.web.rest.talent.dto;

import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EditTalentSyncCrmDTO {
    private String firstName;
    private String lastName;
    //候选人优化增：fullName
    private String fullName;
    private List<TalentContactDTO> contacts;
    private List<TalentExperienceDTO> experiences;
    private LocationDTO currentLocation;
    private boolean shareToAll;
    private List<Long> ownerUserIds;
    private List<Long> shareUserIds;
}
