package com.altomni.apn.talent.service.mapper.folder;


import com.altomni.apn.common.dto.folder.ListPageFolderDTO;
import com.altomni.apn.talent.domain.vm.folder.SearchTalentFolderVM;
import com.altomni.apn.talent.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {})
public interface SearchTalentFolderMapper extends EntityMapper<ListPageFolderDTO, SearchTalentFolderVM> {
    default SearchTalentFolderVM fromId(Long id) {
        if (id == null) {
            return null;
        }
        SearchTalentFolderVM talentFolder = new SearchTalentFolderVM();
        talentFolder.setId(id);
        return talentFolder;
    }

    SearchTalentFolderVM toEntity(ListPageFolderDTO dto);

    ListPageFolderDTO toDto(SearchTalentFolderVM entity);
}

