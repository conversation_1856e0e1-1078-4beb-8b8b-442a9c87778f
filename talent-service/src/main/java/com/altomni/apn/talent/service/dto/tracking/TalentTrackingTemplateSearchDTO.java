package com.altomni.apn.talent.service.dto.tracking;


import com.altomni.apn.talent.domain.enumeration.tracking.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;


@Data
@ApiModel(value = "talent tracking search")
@NoArgsConstructor
@AllArgsConstructor
public class TalentTrackingTemplateSearchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "category")
    @Convert(converter = TrackingTemplateCategoryConverter.class)
    private TrackingTemplateCategory category;

    @ApiModelProperty(value = "status")
    @Convert(converter = TrackingTemplateStatusConverter.class)
    private TrackingTemplateStatus status;

}
