package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TrackingCategory enumeration.
 */
public enum TrackingTemplateCategory implements ConvertedEnum<Integer> {
    INVITATIONAL(10),
    MASS(20);


    private final Integer dbValue;

    TrackingTemplateCategory(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TrackingTemplateCategory, Integer> resolver =
        new ReverseEnumResolver<>(TrackingTemplateCategory.class, TrackingTemplateCategory::toDbValue);

    public static TrackingTemplateCategory fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
