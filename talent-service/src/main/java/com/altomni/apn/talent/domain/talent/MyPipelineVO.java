package com.altomni.apn.talent.domain.talent;


import cn.hutool.json.JSONObject;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.domain.enumeration.application.ActivityStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@ApiModel
@Data
public class MyPipelineVO {

    @ApiModelProperty(value = "candidate name")
    private String candidateName;
    @ApiModelProperty(value = "job title")
    private String jobTitle;
    @ApiModelProperty(value = "company name")
    private String companyName;
    @ApiModelProperty(value = "current status")
    private ActivityStatus status;
    @ApiModelProperty(value = "current status last update time")
    private Instant lastUpdate;
    @ApiModelProperty(value = "current status note info")
    private String currentStatusNote;
    @ApiModelProperty(value = "candidate email")
    private List<String> email;
    @ApiModelProperty(value = "job location")
    private List<LocationDTO> jobLocation;
    @ApiModelProperty(value = "latest candidate note")
    private String candidateNote;

    @ApiModelProperty(value = "recruiterName")
    private List<String> recruiterName;
    @ApiModelProperty(value = "hr name")
    private String hrName;
    @ApiModelProperty(value = "hm name")
    private String hmName;
    @ApiModelProperty(value = "msp name")
    private String mspName;
    @ApiModelProperty(value = "job skill ")
    private List<JSONObject> jobSkill;

    @ApiModelProperty(value = "job ID ")
    private Long jobId;



    private Long applicationId;
    private Long talentId;

    private String locations;
    private String skills;


    private String jobCountry;
    private String jobCity;
    private String jobProvince;


    public MyPipelineVO(Long talentId,Long applicationId,String candidateName,String skills, String jobTitle,String companyName, ActivityStatus status,String hmName,String hrName,String mspName,Long jobId,String locations)
    {
        this.candidateName = candidateName;
        this.jobTitle = jobTitle;
        this.companyName = companyName;
        this.status = status;
        this.applicationId= applicationId;
        this.skills = skills;
        this.talentId = talentId;
        this.hmName = hmName;
        this.hrName =hrName;
        this.mspName = mspName;
        this.jobId = jobId;
        this.locations = locations;
    }
}
