package com.altomni.apn.talent.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.transactionrecord.CommonMqTransactionRecord;
import com.altomni.apn.common.enumeration.enums.MqTranRecordBusTypeEnums;
import com.altomni.apn.common.enumeration.enums.MqTranRecordStatusEnums;
import com.altomni.apn.talent.config.env.EsfillerMQProperties;
import com.altomni.apn.talent.config.env.TalentTxMQProperties;
import com.altomni.apn.talent.service.transactionrecord.TalentMqTransactionRecordService;
import com.altomni.apn.user.service.dto.user.CreditTransactionDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.SerializerMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RabbitMqConfig {

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource
    TalentTxMQProperties talentTxMQProperties;

    @Autowired
    private TalentMqTransactionRecordService talentMqTransactionRecordService;

    private static RabbitMqConfig rabbitMqConfig;

    @PostConstruct
    public void init() {
        rabbitMqConfig = this;
        rabbitMqConfig.talentMqTransactionRecordService = this.talentMqTransactionRecordService;
    }

    private static final String AUTHORIZATION_HEADER = "Authorization";

    @Bean(name = "esfillerConnectionFactory")
    @Primary
    public ConnectionFactory esfillerConnectionFactory() {
        return connectionFactory(esfillerMQProperties.getEsfillerMQHost(), esfillerMQProperties.getEsfillerMQPort(), esfillerMQProperties.getEsfilterMQVirtualHost(), esfillerMQProperties.getEsfillerMQUsername(), esfillerMQProperties.getEsfillerMQPassword());
    }

    public CachingConnectionFactory connectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPublisherConfirms(true);
        return connectionFactory;
    }

    @Bean(name = "esfillerRabbitTemplate")
    @Primary
    public RabbitTemplate esfillerRabbitTemplate(@Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate parsersRabbitTemplate = new RabbitTemplate(connectionFactory);
        parsersRabbitTemplate.setMandatory(true);
        parsersRabbitTemplate.setMessageConverter(new SerializerMessageConverter());
        return parsersRabbitTemplate;
    }

    @Bean(name = "esfillerFactory")
    public SimpleRabbitListenerContainerFactory secondFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(10);
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "esfillerAmqpAdmin")
    public AmqpAdmin esfillerAmqpAdmin(@Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory) {
        AmqpAdmin amqpAdmin = new RabbitAdmin(connectionFactory);
        return amqpAdmin;
    }

    @Bean
    Queue normalizedTalentQueue() {
        return new Queue(esfillerMQProperties.getApnNormalizedTalentQueue(), true, false, false);
    }

    @Bean
    DirectExchange toEsFillerExchange() {
        DirectExchange directExchange = new DirectExchange(esfillerMQProperties.getEsfillerMQExchange());
        directExchange.setDelayed(true);
        return directExchange;
    }

    @Bean
    Binding normalizedTalentBinding(Queue normalizedTalentQueue, DirectExchange toEsFillerExchange) {
        return BindingBuilder.bind(normalizedTalentQueue).to(toEsFillerExchange).with(esfillerMQProperties.getApnNormalizedTalentRoutingKey());
    }

    /****** start talnet tx mq config  *******/
    @Bean(name = "talentTxConnectionFactory")
    public ConnectionFactory talentTxConnectionFactory() {
        return talentTxConnectionFactory(talentTxMQProperties.getHost(), talentTxMQProperties.getPort(), talentTxMQProperties.getVirtualHost(), talentTxMQProperties.getUserName(), talentTxMQProperties.getPassword());
    }

    public CachingConnectionFactory talentTxConnectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        connectionFactory.setPublisherReturns(true);
        connectionFactory.setPublisherConfirms(true);
        return connectionFactory;
    }

    @Bean(name = "talentTxFactory")
    public SimpleRabbitListenerContainerFactory talentTxFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("talentTxConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(1);
        configurer.configure(factory, connectionFactory);
        return factory;
    }


    @Bean(name = "talentTxRabbitTemplate")
    public RabbitTemplate talentTxRabbitTemplate(
            @Qualifier("talentTxConnectionFactory") ConnectionFactory connectionFactory
    ) {
        RabbitTemplate talentTxRabbitTemplate = new RabbitTemplate(connectionFactory);
        talentTxRabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        //设置开启Mandatory,才能触发回调函数,无论消息推送结果怎么样都强制调用回调函数
        talentTxRabbitTemplate.setMandatory(true);

        //消息发送成功的回调
        talentTxRabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            log.info("talent tx rabbit, correlationData:{}, ack:{}, cause:{}", correlationData, ack, cause);
            if (ack) {
                if (StringUtils.isNotBlank(correlationData.getId())) {
                    CommonMqTransactionRecord record = getRecordInfo(Long.valueOf(correlationData.getId()));
                    if (record.getSendCount() > 1) {
                        rabbitMqConfig.talentMqTransactionRecordService.updateStatusById(record.getId().longValue(), MqTranRecordStatusEnums.SEND_SUCCESS.toDbValue());
                        log.info("talent tx rabbit, modify mq transaction record status is success");
                    } else if (record.getSendCount() == 1 && record.getSendStatus().equals(MqTranRecordStatusEnums.PENDING.toDbValue())) {
                        rabbitMqConfig.talentMqTransactionRecordService.updateStatusById(record.getId().longValue(), MqTranRecordStatusEnums.SEND_SUCCESS.toDbValue());
                        log.info("talent tx rabbit, modify mq transaction record status is success");
                    }
                }
                log.info("talent tx rabbit, send message to rabbit success");
            } else {
                log.error("talent tx rabbit, send message to rabbit error, error message = [{}], Data = {}", cause, correlationData);
            }
        });

        //发生异常时的消息返回提醒
        talentTxRabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            log.error("talent tx rabbit, send message to rabbit error, return call back message = {}, reply code = {}, reply text = {}", message, replyCode, replyText);
            try {
                Thread.sleep(3000);
                JSONObject json = JSON.parseObject(new String(message.getBody()));
                json.remove(AUTHORIZATION_HEADER);
                CreditTransactionDTO creditTransactionDTO = JSONObject.parseObject(json.toJSONString(), CreditTransactionDTO.class);
                rabbitMqConfig.talentMqTransactionRecordService.updateStatusById(json.getLong("mqRecordId"), MqTranRecordStatusEnums.NOT_ARRIVE_QUEUE.toDbValue());
                //rabbitMqConfig.talentMqTransactionRecordService.updateStatusByBusIdAndBusType(creditTransactionDTO.getId(), MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.toDbValue(), MqTranRecordStatusEnums.NOT_ARRIVE_QUEUE.toDbValue());
                log.info("talent tx rabbit, modify mq transaction record status is {}, busId:{}", MqTranRecordStatusEnums.NOT_ARRIVE_QUEUE.toDbValue(), creditTransactionDTO.getId());
                rabbitMqConfig.talentMqTransactionRecordService.sendExceptionByLark(String.format(" Sending mq tx message abnormal, tx type is ' %s ' , replyText: %s , talentId is %s , id is %s",
                        MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.getDesc(),
                        replyText,
                        creditTransactionDTO.getTalentId(),
                        creditTransactionDTO.getId()));
            } catch (Exception e) {
                log.error("talent tx rabbit, {}", e.getMessage());
            }
        });
        return talentTxRabbitTemplate;
    }

    @Bean(name = "updateTalentRabbitTemplate")
    public RabbitTemplate updateTalentRabbitTemplate(
            @Qualifier("talentTxConnectionFactory") ConnectionFactory connectionFactory
    ) {
        RabbitTemplate financeRabbitTemplate = new RabbitTemplate(connectionFactory);
        financeRabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        //设置开启Mandatory,才能触发回调函数,无论消息推送结果怎么样都强制调用回调函数
        financeRabbitTemplate.setMandatory(true);

        //消息发送成功的回调
        financeRabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            log.info("update talent tx rabbit, correlationData:{}, ack:{}, cause:{}", correlationData, ack, cause);
            if (ack) {
                if (StringUtils.isNotBlank(correlationData.getId())) {
                    CommonMqTransactionRecord record = getRecordInfo(Long.valueOf(correlationData.getId()));
                    if (record.getSendCount() > 1) {
                        rabbitMqConfig.talentMqTransactionRecordService.updateStatusById(record.getId().longValue(), MqTranRecordStatusEnums.SEND_SUCCESS.toDbValue());
                        log.info("update talent tx rabbit, modify mq transaction record status is success");
                    } else if (record.getSendCount() == 1 && record.getSendStatus().equals(MqTranRecordStatusEnums.PENDING.toDbValue())) {
                        rabbitMqConfig.talentMqTransactionRecordService.updateStatusById(record.getId().longValue(), MqTranRecordStatusEnums.SEND_SUCCESS.toDbValue());
                        log.info("update talent tx rabbit, modify mq transaction record status is success");
                    }
                }
                log.info("update talent tx rabbit, send message to rabbit success");
            } else {
                log.error("update talent tx rabbit, send message to rabbit error, error message = [{}], Data = {}", cause, correlationData);
            }
        });

        //发生异常时的消息返回提醒
        financeRabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            log.error("update talent tx rabbit, send message to rabbit error, return call back message = {}, reply code = {}, reply text = {}", message, replyCode, replyText);
            try {
                Thread.sleep(3000);
                JSONObject json = JSON.parseObject(new String(message.getBody()));
                json.remove(AUTHORIZATION_HEADER);
                rabbitMqConfig.talentMqTransactionRecordService.updateStatusById(json.getLong("mqRecordId"), MqTranRecordStatusEnums.NOT_ARRIVE_QUEUE.toDbValue());
                log.info("update talent tx rabbit, modify mq transaction record status is {}, busId:{}", MqTranRecordStatusEnums.NOT_ARRIVE_QUEUE.toDbValue(), json.getLong("talentId"));
                rabbitMqConfig.talentMqTransactionRecordService.sendExceptionByLark(String.format(" Sending mq tx message abnormal, tx type is ' %s ' , replyText: %s , talentId is %s",
                        MqTranRecordBusTypeEnums.fromDbValue(json.getInteger("mqRecordType")).getDesc(),
                        replyText,
                        json.getLong("talentId")));
            } catch (Exception e) {
                log.error("update talent tx rabbit, {}", e.getMessage());
            }
        });
        return financeRabbitTemplate;
    }

    /**
     * 获取数据信息
     * @param id
     * @return
     */
    private CommonMqTransactionRecord getRecordInfo(Long id) {
        CommonMqTransactionRecord record = rabbitMqConfig.talentMqTransactionRecordService.findById(BigInteger.valueOf(id));
        try {
            if (null == record) {
                Thread.sleep(3000);
            }
            record = rabbitMqConfig.talentMqTransactionRecordService.findById(BigInteger.valueOf(id));
        } catch (Exception e) {

        }
        return record;
    }
}