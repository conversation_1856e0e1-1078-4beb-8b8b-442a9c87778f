package com.altomni.apn.talent.service.store;

import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.store.UploadUrlDto;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(value = "common-service")
public interface StoreService {

    @GetMapping("/common/api/v3/s3/store/url/{uuid}/{uploadType}")
    ResponseEntity<String> getUrlFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @GetMapping("/common/api/v3/s3/store/url-with-content-type/{uuid}/{uploadType}")
    ResponseEntity<String> getDisplayUrlFromS3WithContentType(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType, @RequestParam("contentType") String contentType);

    @GetMapping("/common/api/v3/s3/store/detail-without-file-byte/{uuid}/{uploadType}")
    ResponseEntity<CloudFileObjectMetadata> getFileDetailWithoutFileFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @GetMapping("/common/api/v3/s3/store/file/{uuid}/{uploadType}")
    ResponseEntity<CloudFileObjectMetadata> getFileFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @GetMapping("/common/api/v3/s3/store/is-exists/{uuid}/{uploadType}")
    ResponseEntity<Boolean> exists(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @PostMapping("/common/api/v3/s3/store/upload-url")
    ResponseEntity<StoreGetUploadUrlVO> getPresignedCommonUploadUrlFromS3WithPostPolicy(@RequestBody UploadUrlDto uploadUrlDto);

}
