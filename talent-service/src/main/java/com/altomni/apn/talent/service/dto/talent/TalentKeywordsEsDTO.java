package com.altomni.apn.talent.service.dto.talent;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentKeywordsEsDTO {

    private String firstName;

    private String lastName;

    private String fullName;

    // chinese email phone contacts website industries job_functions  title company educations experiences languages skills
    private Boolean chinese;

    private String email;

    private String phone;

    private List<JSONObject> contacts;

    private String website;

    private List<String> industries;

    private List<String> jobFunctions;

    private String title;

    private String company;

    private List<JSONObject> educations;

    private List<JSONObject> experiences;

    private List<String> languages;

    private List<JSONObject> skills;

}
