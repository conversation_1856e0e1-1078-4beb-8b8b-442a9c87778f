package com.altomni.apn.talent.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.talent.domain.enumeration.talent.HotlistAccessOption;
import com.altomni.apn.talent.domain.enumeration.talent.HotlistSourceType;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A HotList.
 */
@ApiModel(description = "HotList is user create a group which contains Users and Talents")
@Entity
@Table(name = "hot_list")
public class HotList extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "hot list title", required = true)
    @Column(name = "title")
    private String title;

    @ApiModelProperty(value = "hot list notes", required = true)
    @Column(name = "notes")
    private String notes;

    @ApiModelProperty(value = "hot list source type", required = true)
    @Column(name = "source_type")
    private HotlistSourceType sourceType;

    @ApiModelProperty(value = "hot list access option : public/private", required = true)
    @Column(name = "access_option")
    private HotlistAccessOption accessOption;

    @ApiModelProperty(value = "The tenant id talent belongs to. This is saved from the user's tenant when creating talent.")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @ApiModelProperty(value = "last edited time of hotListTalent")
    @Column(name = "last_edited_time")
    private Instant lastEditedTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public HotList title(String title) {
        this.title = title;
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getNotes() {
        return notes;
    }

    public HotList notes(String notes) {
        this.notes = notes;
        return this;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public HotList tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public HotlistSourceType getSourceType() {
        return sourceType;
    }

    public void setSourceType(HotlistSourceType sourceType) {
        this.sourceType = sourceType;
    }

    public HotlistAccessOption getAccessOption() {
        return accessOption;
    }

    public void setAccessOption(HotlistAccessOption accessOption) {
        this.accessOption = accessOption;
    }

    public Instant getLastEditedTime() {
        return lastEditedTime;
    }

    public void setLastEditedTime(Instant lastEditedTime) {
        this.lastEditedTime = lastEditedTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HotList hotList = (HotList) o;
        if (hotList.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), hotList.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "HotList{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", notes='" + notes + '\'' +
                ", sourceType=" + sourceType +
                ", accessOption=" + accessOption +
                ", tenantId=" + tenantId +
                ", lastEditedTime=" + lastEditedTime +
                '}';
    }
}
