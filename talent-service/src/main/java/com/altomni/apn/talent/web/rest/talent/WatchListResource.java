package com.altomni.apn.talent.web.rest.talent;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.aop.user.AttachSimpleUser;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.talent.WatchList;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.talent.service.dto.talent.WatchListDTO;
import com.altomni.apn.talent.service.query.WatchListSearch;
import com.altomni.apn.talent.service.talent.WatchListService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing WatchList.
 */
@Api(hidden = true, tags = {"Under Development"})
@RestController
@RequestMapping("/api/v3")
public class WatchListResource {

    private final Logger log = LoggerFactory.getLogger(WatchListResource.class);

    private static final String ENTITY_NAME = "watchList";

    @Resource
    private WatchListService watchListService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    /**
     * POST  /watch-lists : Create a new watchList.
     *
     * @param watchList the watchList to create
     * @return the ResponseEntity with status 201 (Created) and with body the new watchList, or with status 400 (Bad Request) if the watchList has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/watchlist")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<WatchList> createWatchList(@Valid @RequestBody WatchList watchList) throws URISyntaxException {
        log.info("[APN: TalentWatchList @{}] REST request to save WatchList : {}", SecurityUtils.getUserId(), watchList);
        if (watchList.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CREATE_TALENTCONTACTIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        WatchList result = watchListService.create(watchList);
        return ResponseEntity.created(new URI("/api/v3/watch-lists/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @PostMapping("/watchlist/delete-by-id")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> removeTalentWatchListByIds(@RequestBody List<Long> ids) {
        log.info("[APN: TalentWatchList @{}] REST request to delete WatchList by ids: {}", SecurityUtils.getUserId(), ids);
        watchListService.removeTalentWatchListByIds(ids);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/watchlist/delete-by-talent-id")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> removeTalentWatchList(@RequestBody List<Long> talentIds) {
        log.info("[APN: TalentWatchList @{}] REST request to delete WatchList by talent ids: {}", SecurityUtils.getUserId(), talentIds);
        watchListService.removeTalentWatchList(talentIds);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/my-watchlist")
    @Timed
    public ResponseEntity<List<WatchListDTO>> getAllWatchList(@RequestParam(value = "fullName", required = false) String fullName,
                                                              @RequestParam(value = "company", required = false) String company,
                                                              @RequestParam(value = "title", required = false) String title, Pageable pageable) {
        log.info("[APN: TalentWatchList @{}] REST request to get all watch list by fullName: {}, company:{}, title:{}",
            SecurityUtils.getUserId(), fullName, company, title);
        Page<WatchListDTO> page = watchListService.findAll(WatchListSearch.allWatchlist(fullName, company, title), pageable);
        return new ResponseEntity<>(page.getContent(), PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/my-watchlist"), HttpStatus.OK);
    }





    /****************************以下为老的API，为PRO保留*****************************************/
    /**
     * GET  /watchlist : get all the talents in current user's watch list.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talents in body
     */
    @ApiOperation(value = "Get/Filter my watch list", notes = "The watch list contains my talents with or without job. For talents with job in watching status, " +
        "the associate application is also returned. " +
        "Use search query to search for talents. Search query is search=name:value,name2:value2. Will search name contains " +
        "value and name2 contains value2. Use sort=name,desc(asc) to sort results.", tags = {"ATS-Dashboard", "ATS-Candidates"})
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/watchlist")
    @Timed
    @AttachSimpleUser
    public ResponseEntity<List<TalentDTOV3>> getMyWatchList(@ApiParam(value = "search params. e.g. search=company:google,title:Lead%20Backend%20Engineer")
                                                          @RequestParam(value = "search", required = false) String search, Pageable pageable) {
        log.info("[APN: TalentWatchList @{}] REST request to get my watch list by search: {}", SecurityUtils.getUserId(), search);
        Page<TalentDTOV3> res = watchListService.getMyWatchListTalent(search, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(res, "/api/v3/watchlist");
        return new ResponseEntity<>(res.getContent(), headers, HttpStatus.OK);
    }

    /**
     * GET  /watchlist/job/{jobId} : get talents by jobId in current user's watch list.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talents in body
     */
    @ApiOperation(value = "Get/Filter my watching candidates for a job", notes = "This looks into Application Entity and find qualified talents who is watched by me on the job. " +
        "Talents without any jobs are ignored. " +
        "Use search query to search for talents. Search query is search=name:value,name2:value2. Will search name contains " +
        "value and name2 contains value2. Use sort=name,desc(asc) to sort results.", tags = {"APN-Pro"})
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/watchlist/job/{jobId}")
    @Timed
    @AttachSimpleUser
    public ResponseEntity<List<TalentDTOV3>> getMyWatchingTalentsByJobId(
        @ApiParam(value = "talent id", required = true) @PathVariable Long jobId,
        @ApiParam(value = "search params. e.g. search=company:google,title:Lead%20Backend%20Engineer") @RequestParam(value = "search", required = false) String search, Pageable pageable) {
        log.info("[APN: TalentWatchList @{}] REST request to get my watch list by job id: {}", SecurityUtils.getUserId(), jobId);
        Page<TalentDTOV3> res = watchListService.getMyWatchListTalentsByJobId(search, jobId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(res, "/api/v3/watchlist/job/" + jobId);
        return new ResponseEntity<>(res.getContent(), headers, HttpStatus.OK);
    }
}
