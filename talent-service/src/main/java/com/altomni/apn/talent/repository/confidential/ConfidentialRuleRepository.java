package com.altomni.apn.talent.repository.confidential;

import com.altomni.apn.talent.domain.confidential.ConfidentialRule;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ConfidentialRuleRepository extends JpaRepository<ConfidentialRule, Long> {

    List<ConfidentialRule> findAllByTenantId(Long tenantId);

    Optional<ConfidentialRule> findByIdAndTenantId(Long id, Long tenantId);

    List<ConfidentialRule> findAllByTenantIdAndEnabled(Long tenantId, boolean enabled);


    default List<ConfidentialRule> findAllEnableRules(Long tenantId) {
        return findAllByTenantIdAndEnabled(tenantId, true);
    }
}