package com.altomni.apn.talent.converter;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentInfoInput;
import com.altomni.apn.common.errors.CustomParameterizedException;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public interface TalentInfoMapper {
    @Mapping(source = "folderId", target = "hotListId")
    @Mapping(source = "extendedInfo", target = "extendedInfo")
    @Mapping(source = "talentId", target = "id")
    @Mapping(target = "esId", ignore = true)
    @Mapping(target = "applicationCount", ignore = true)
    @Mapping(target = "purchased", ignore = true)
    @Mapping(target = "addressLine", ignore = true)
    @Mapping(target = "isAM", ignore = true)
    @Mapping(target = "creditTransactionId", ignore = true)
    @Mapping(target = "additionalInfoId", ignore = true)
    @Mapping(target = "clientContactCompanyId", ignore = true)
    @Mapping(target = "companyAffiliations", ignore = true)
    @Mapping(target = "recommendFeedback", ignore = true)
    @Mapping(target = "reviewNotes", ignore = true)
    @Mapping(target = "industries", qualifiedByName = "toEnumRelationDTOList")
    @Mapping(target = "jobFunctions", qualifiedByName = "toEnumRelationDTOList")
    @Mapping(target = "languages", qualifiedByName = "toEnumRelationDTOList")
    @Mapping(target = "workAuthorization", qualifiedByName = "toEnumRelationDTOList")
//    @Mapping(target = "preferredCurrency", qualifiedByName = "toPreferredCurrency")
//    @Mapping(target = "preferredPayTimes", qualifiedByName = "toPreferredPayTimes")
//    @Mapping(target = "preferredPayType", qualifiedByName = "toPreferredPayType")
//    @Mapping(target = "preferredSalaryRange", qualifiedByName = "toPreferredSalaryRange")
    TalentDTOV3 toTalentDTO(TalentInfoInput input);

    @Named("toEnumRelationDTOList")
    default List<EnumRelationDTO> toEnumRelationDTOList(List<Integer> source) {
        if (source == null) {
            return null;
        }
        return source.stream()
                .map(item -> {
                    if (item == null) {
                        throw new CustomParameterizedException("Enum relationship cannot pass null values.");
                    }
                    EnumRelationDTO dto = new EnumRelationDTO();
                    dto.setEnumId(String.valueOf(item));
                    return dto;
                })
                .collect(Collectors.toList());
    }

//    @Named("toPreferredCurrency")
//    default String toPreferredCurrency(Object source) {
//        if (source == null) {
//            return null;
//        }
//        if (source instanceof List<?>) {
//            return ((List<String>) source).get(0);
//        }
//        return (String) source;
//    }

//    @Named("toPreferredPayTimes")
//    default Double toPreferredPayTimes(Object source) {
//        if (source == null) {
//            return null;
//        }
//        if (source instanceof List<?>) {
//            Object o = ((List<?>) source).get(0);
//            return Double.valueOf(o.toString());
//        }
//        return Double.valueOf(source.toString());
//    }
//
//    @Named("toPreferredPayType")
//    default RateUnitType toPreferredPayType(Object source) {
//        if (source == null) {
//            return null;
//        }
//        if (source instanceof List<?>) {
//            return RateUnitType.valueOf(((List<String>) source).get(0));
//        }
//        return RateUnitType.valueOf((String) source);
//    }
//
//    @Named("toPreferredSalaryRange")
//    default RangeDTO toPreferredSalaryRange(Object source) {
//        if (source == null) {
//            return null;
//        }
//        if (source instanceof List<?>) {
//            Object o = ((List<?>) source).get(0);
//            return convertToRangeDTO(o);
//        }
//        return convertToRangeDTO(source);
//    }

    default RangeDTO convertToRangeDTO(Object object) {
        JSONObject jsonObject = JSONUtil.parseObj(object);
        RangeDTO rangeDTO = new RangeDTO();
        String lte = jsonObject.getStr("lte");
        String gte = jsonObject.getStr("gte");
        if (StringUtils.isNotEmpty(lte)) {
            rangeDTO.setLte(new BigDecimal(lte));
        }
        if (StringUtils.isNotEmpty(gte)) {
            rangeDTO.setGte(new BigDecimal(gte));
        }
        return rangeDTO;
    }
}
