package com.altomni.apn.talent.domain.enumeration.linkedinproject;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CandidateStatusConverter extends AbstractAttributeConverter<CandidateStatus, Integer> {
    public CandidateStatusConverter() {
        super(CandidateStatus::toDbValue, CandidateStatus::fromDbValue);
    }
}
