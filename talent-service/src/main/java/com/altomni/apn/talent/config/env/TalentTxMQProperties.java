package com.altomni.apn.talent.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
public class TalentTxMQProperties {

    @Value("${spring.rabbitmq.addresses}")
    private String host;

    @Value("${spring.rabbitmq.port}")
    private int port;

    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;

    @Value("${spring.rabbitmq.username}")
    private String userName;

    @Value("${spring.rabbitmq.password}")
    private String password;

    @Value("${application.talent-tx-mq.exchange}")
    private String talentTxMQExchange;

    @Value("${application.talent-tx-mq.queue}")
    private String talentTxMQQueue;

    @Value("${application.talent-tx-mq.routing-key}")
    private String talentTxMQRoutingKey;

    /** finance /api/v3/updateTalent 方法使用 **/
    @Value("${application.update-talent-tx.exchange}")
    private String updateTalentTxExchange;

    @Value("${application.update-talent-tx.queue}")
    private String updateTalentTxQueue;

    @Value("${application.update-talent-tx.routing-key}")
    private String updateTalentTxQRoutingKey;

    @Value("${application.update-talent-tx.company-queue}")
    private String updateTalentTxCompanyQueue;

    @Value("${application.update-talent-tx.routing-company-key}")
    private String updateTalentTxRoutingCompanyKey;

}