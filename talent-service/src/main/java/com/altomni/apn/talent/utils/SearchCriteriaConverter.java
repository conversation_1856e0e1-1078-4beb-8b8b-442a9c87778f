package com.altomni.apn.talent.utils;

import com.altomni.apn.common.dto.search.TalentSearchFolderConditionDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.databind.ObjectMapper;

public class SearchCriteriaConverter {
    private static final ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 注册日期时间模块（如果需要）
        objectMapper.registerModule(new JavaTimeModule());
    }

    public static TalentSearchFolderConditionDTO from<PERSON>son(String json) {
        try {
            return objectMapper.readValue(json, TalentSearchFolderConditionDTO.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to parse search criteria: " + e.getMessage(), e);
        }
    }

    public static String toJson(TalentSearchFolderConditionDTO dto) {
        try {
            return objectMapper.writeValueAsString(dto);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to serialize search criteria: " + e.getMessage(), e);
        }
    }
}
