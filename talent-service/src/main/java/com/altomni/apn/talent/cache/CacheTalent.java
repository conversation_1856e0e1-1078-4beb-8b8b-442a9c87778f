package com.altomni.apn.talent.cache;
//
////import com.altomni.apn.talent.service.TalentService;
//import com.altomni.apn.talent.service.dto.TalentDTO;
//import org.springframework.cache.annotation.CacheEvict;
//import org.springframework.cache.annotation.CachePut;
//import org.springframework.cache.annotation.Cacheable;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// */
//@Service
//public class CacheTalent {
//    @Resource
//    private TalentService talentService;
//
//    @CachePut(cacheNames = {"talent"}, key = "'talent:' + #result.id", unless = "#result == null ")
//    public TalentDTO save(TalentDTO talentDTO){
//        return talentService.save(talentDTO);
//    }
//
//    @CachePut(cacheNames = {"talent"}, key = "'talent:' + #talentDTO.id", unless = "#result == null ")
//    public TalentDTO update(TalentDTO talentDTO){
//        return talentService.save(talentDTO);
//    }
//
//    @Cacheable(cacheNames = {"talent"}, key = "'talent:' + #id", unless = "#result == null")
//    public TalentDTO getById(Long id){
//        return talentService.findOne(id).orElseThrow();
//    }
//
//    @CacheEvict(cacheNames = {"talent"}, key = "'talent:' + #id", allEntries = true, beforeInvocation = true)
//    public void delete(Long id){
//        talentService.delete(id);
//    }
//}
