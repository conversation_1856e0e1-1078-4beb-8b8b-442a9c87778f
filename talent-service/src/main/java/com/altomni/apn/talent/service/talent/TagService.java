package com.altomni.apn.talent.service.talent;

import com.altomni.apn.talent.service.dto.talent.SpecialTagDTO;
import com.altomni.apn.common.dto.talent.TagDTO;

import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing Tag.
 */
public interface TagService {
    /**
     * Get all the tags.
     *
     * @return the list of entities
     */
    List<TagDTO> findAllByIds(List<Long> tagIds);


    /**
     * Get the "id" tag.
     *
     * @param id the id of the entity
     * @return the entity
     */
    Optional<TagDTO> findOne(Long id);

    /**
     * Delete the "id" tag.
     *
     * @param id the id of the entity
     */
    void delete(Long id);

    Optional<TagDTO> checkTagExists (Long tagId);

    List<TagDTO> findAll ();

    /**
     * Save a special tag.
     *
     * @param tag the entity to save
     * @return the persisted entity
     */
    SpecialTagDTO createSpecialTag(SpecialTagDTO tag);

    List<SpecialTagDTO> findAllSpecialTags();
}
