package com.altomni.apn.talent.service.query;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.talent.QTalentContact;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;


public class ContactSearch {

    public static BooleanExpression getContacts(ContactType type, String contact) {
        QTalentContact c = QTalentContact.talentContact;
        BooleanExpression typeExp;
        if (type.equals(ContactType.EMAIL)) {
            typeExp = c.type.in(ContactType.EMAIL);
        }
        else if (type.equals(ContactType.PHONE)) {
            typeExp = c.type.in(ContactType.PHONE);
        }
        else {
            typeExp = c.type.eq(type);
        }
        return c.talentId.in(JPAExpressions.selectFrom(c).where(typeExp.and(c.contact.eq(contact))).select(c.talentId).distinct());
    }

}
