package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.talent.domain.talent.Tag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the Tag entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TagRepository extends JpaRepository<Tag, Long> {

    List<Tag> findAllByTagName(String tagName);

    List<Tag> findAllByTypeAndTagName(String type, String tagName);

    List<Tag> findAllByType(String type);
}
