package com.altomni.apn.talent.repository.folder;


import com.altomni.apn.talent.domain.folder.TalentSearchFolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TalentSearchFolderRepository extends JpaRepository<TalentSearchFolder, Long> {

    Page<TalentSearchFolder> findAllByPermissionUserId(Long userId, Pageable pageable);

    Optional<TalentSearchFolder> findByNameAndPermissionUserId(String name, Long userId);

    Optional<TalentSearchFolder> findByNameAndPermissionUserIdAndIdNot(String name, Long userId, Long id);

    List<TalentSearchFolder> findAllByTalentFolderId(Long talentFolderId);

    List<TalentSearchFolder> findAllByTalentFolderIdAndPermissionUserIdInAndIsActive(Long talentFolderId, List<Long> userIds, Boolean isActive);

    @Query(value = """
    SELECT distinct f.id
    FROM talent_search_folder f
    LEFT JOIN talent_search_folder_sharing_user tu ON f.id = tu.talent_search_folder_id
    LEFT JOIN talent_search_folder_sharing_team tt ON f.id = tt.talent_search_folder_id
    LEFT JOIN talent_search_folder_owner_user ou ON f.id = ou.talent_search_folder_id
    LEFT JOIN talent_search_folder_owner_team ot ON f.id = ot.talent_search_folder_id
    WHERE (tu.user_id = :userId or (tt.team_id = :teamId and NOT JSON_CONTAINS(tt.excluded_user_ids, CAST(:userId AS JSON)))
    or ou.user_id = :userId or (ot.team_id = :teamId and NOT JSON_CONTAINS(ot.excluded_user_ids, CAST(:userId AS JSON))))
    and f.search_category != 4
    order by f.created_date desc
    limit 10
    """, nativeQuery = true)
    List<Long> findUserTop10Folder(@Param("userId")Long userId, @Param("teamId")Long teamId);

    @Query(value = """
            select DISTINCT tsf from TalentSearchFolder tsf 
            left join TalentSearchFolderOwnerTeam ot on tsf.id = ot.talentSearchFolderId
            left join TalentSearchFolderSharingTeam fst on tsf.id = fst.talentSearchFolderId
            where tsf.isActive = :isActive 
            and (ot.teamId = :teamId
            or fst.teamId = :teamId)
            order by tsf.createdDate desc
    """)
    List<TalentSearchFolder> findTeamFolder(@Param("teamId")Long teamId, @Param("isActive")Boolean isActive);

    @Query(value = """
            select DISTINCT tsf from TalentSearchFolder tsf 
            left join TalentSearchFolderOwnerUser fo on tsf.id = fo.talentSearchFolderId 
            left join TalentSearchFolderOwnerTeam ot on tsf.id = ot.talentSearchFolderId
            left join TalentSearchFolderSharingUser fsu on tsf.id = fsu.talentSearchFolderId
            left join TalentSearchFolderSharingTeam fst on tsf.id = fst.talentSearchFolderId
            where tsf.isActive = :isActive 
            and (fo.userId = :userId 
            or ot.teamId = :teamId
            or fsu.userId = :userId
            or fst.teamId = :teamId)
            order by tsf.createdDate desc
            """)
    List<TalentSearchFolder> findUserTalentSearchFolder(@Param("userId")Long userId, @Param("teamId")Long teamId, @Param("isActive")Boolean isActive);

    @Query("SELECT jsf FROM TalentSearchFolder jsf " +
            "JOIN PermissionUserTeam put ON jsf.permissionUserId = put.userId " +
            "WHERE put.teamId IN :teamIds AND jsf.talentFolderId = :talentFolderId AND jsf.isActive = :isActive")
    List<TalentSearchFolder> findAllByTalentCustomFolderIdAndTeamIds(@Param("talentFolderId") Long talentFolderId, @Param("teamIds") List<Long> teamIds, @Param("isActive") Boolean isActive);
}
