package com.altomni.apn.talent.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
public class TalentProfileMQProperties {

    @Value("${spring.rabbitmq.addresses}")
    private String host;

    @Value("${spring.rabbitmq.port}")
    private int port;

    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;

    @Value("${spring.rabbitmq.username}")
    private String username;

    @Value("${spring.rabbitmq.password}")
    private String password;

    @Value("${application.socialProfileMq.socialProfileExchange}")
    private String exchange;

    @Value("${application.socialProfileMq.linkedinRoutingKey}")
    private String linkedinRoutingKey;

    @Value("${application.socialProfileMq.linkedinRequestQueue}")
    private String linkedinRequestQueue;

    @Value("${application.socialProfileMq.linkedinResponseQueue}")
    private String linkedinResponseQueue;

    @Value("${application.socialProfileMq.linkedinDelayRoutingKey}")
    private String linkedinDelayRoutingKey;

    @Value("${application.socialProfileMq.linkedinDelayQueue}")
    private String linkedinDelayQueue;

    @Value("${application.socialProfileMq.talentIdRoutingKey}")
    private String talentIdRoutingKey;

    @Value("${application.socialProfileMq.talentIdQueue}")
    private String talentIdQueue;

    @Value("${application.socialProfileMq.socialProfileMaximumMsgCount}")
    private Integer socialProfileMaximumMsgCount;

    @Value("${application.socialProfileMq.msgExpire}")
    private Integer msgExpire;
}
