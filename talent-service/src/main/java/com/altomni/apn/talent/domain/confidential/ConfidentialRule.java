package com.altomni.apn.talent.domain.confidential;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.service.dto.confidential.ConfidentialRuleDto;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Entity
@Getter
@Setter
@Table(name = "confidential_rule",
        indexes = {
                @Index(name = "idx_tenant_id", columnList = "tenant_id", unique = true),
        }
)
public class ConfidentialRule extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @Column(name = "enabled", nullable = false)
    private boolean enabled;

    @Column(name = "confidentiality_rules", columnDefinition = "json")
    @Convert(converter = ConfidentialityRules.ConfidentialityRulesConverter.class)
    private ConfidentialityRules confidentialityRules;

    @Column(name = "declassification_rules", columnDefinition = "json")
    @Convert(converter = DeclassificationRules.DeclassificationRulesConverter.class)
    private DeclassificationRules declassificationRules;


    public boolean sameRule(ConfidentialRule other) {
        boolean tenantSame = this.tenantId.equals(other.tenantId);
        boolean confidentialityRulesSame = this.confidentialityRules == null ? other.getConfidentialityRules() == null :
                this.confidentialityRules.sameRule(other.confidentialityRules);
        boolean declassificationRulesSame = this.declassificationRules == null ? other.getDeclassificationRules() == null :
                this.declassificationRules.sameRule(other.declassificationRules);
        return tenantSame && confidentialityRulesSame && declassificationRulesSame;
    }

    public void fillWithDto(ConfidentialRuleDto dto) {
        ConfidentialityRules confidentialityRules = new ConfidentialityRules();
        if (dto.getSalary() != null && dto.getCurrency() != null) {
            confidentialityRules.setSalary(new ConfidentialityRules.Salary(dto.getCurrency(), new BigDecimal(dto.getSalary().toString())));
        }
        confidentialityRules.setIndustries(dto.getIndustries());
        confidentialityRules.setJobFunctions(dto.getJobFunctions());
        confidentialityRules.setDegreeLevel(dto.getDegreeLevel());
        this.confidentialityRules = confidentialityRules;
        DeclassificationRules declassificationRules = new DeclassificationRules();
        declassificationRules.setDeclassifyDays(dto.getDeclassifyDays());
        declassificationRules.setDeclassifyProcessIds(dto.getDeclassifyProcessIds());
        declassificationRules.setDeclassifyProcessTypes(dto.getDeclassifyProcessTypes());
        declassificationRules.setDeclassifyProcessDelayDays(dto.getDeclassifyProcessDelayDays());
        this.declassificationRules = declassificationRules;
        this.tenantId = SecurityUtils.getTenantId();
    }

    /**
     * 薪资规则，统一转换为USD年薪来计算
     */
    public Optional<BigDecimal> getUSDYearlySalary(List<EnumCurrency> allCurrencies) {
        if (!hasSalaryRule()) {
           return Optional.empty();
        }
        return Optional.ofNullable(confidentialityRules).flatMap(cr -> allCurrencies.stream().filter(c -> c.getName().equals(cr.getSalary().getCurrency()))
                .findFirst()
                .map(enumCurrency -> new BigDecimal(cr.getSalary().getAmount().toString()).multiply(new BigDecimal(enumCurrency.getFromUsdRate().toString()))));
    }

    public Set<Long> getIndustries() {
        if (confidentialityRules == null) {
            return Collections.emptySet();
        }
        return Set.copyOf(confidentialityRules.getIndustries()).stream().map(Long::valueOf).collect(Collectors.toSet());
    }

    public Set<Long> getJobFunctions() {
        if (confidentialityRules == null) {
            return Collections.emptySet();
        }
        return Set.copyOf(confidentialityRules.getJobFunctions()).stream().map(Long::valueOf).collect(Collectors.toSet());
    }

    public String getDegreeLevel() {
        if (confidentialityRules == null) {
            return null;
        }
        return confidentialityRules.getDegreeLevel();
    }

    public boolean hasSalaryRule() {
        if (confidentialityRules == null) {
            return false;
        }
        return confidentialityRules.getSalary()!= null && confidentialityRules.getSalary().getAmount()!= null
                && confidentialityRules.getSalary().getCurrency() != null;
    }

    public boolean hasIndustryRule() {
        if (confidentialityRules == null) {
            return false;
        }
        return !confidentialityRules.getIndustries().isEmpty();
    }

    public boolean hasJobFunctionRule() {
        if (confidentialityRules == null) {
            return false;
        }
        return !confidentialityRules.getJobFunctions().isEmpty();
    }

    public boolean hasDegreeLevelRule() {
        if (confidentialityRules == null) {
            return false;
        }
        return confidentialityRules.getDegreeLevel() != null;
    }

    /**
     * 是否有流程自动解除保密规则
     */
    public boolean hasDeclassifyProcessRule() {
        if (declassificationRules == null) {
            return false;
        }
        return !declassificationRules.getDeclassifyProcessIds().isEmpty()
                && !declassificationRules.getDeclassifyProcessTypes().isEmpty()
                && declassificationRules.getDeclassifyProcessDelayDays() != null;
    }

}
