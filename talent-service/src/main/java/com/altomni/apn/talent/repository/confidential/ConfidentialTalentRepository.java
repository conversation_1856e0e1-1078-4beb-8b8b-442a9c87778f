package com.altomni.apn.talent.repository.confidential;

import com.altomni.apn.talent.domain.confidential.ConfidentialTalent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface ConfidentialTalentRepository extends JpaRepository<ConfidentialTalent, Long> {

    List<ConfidentialTalent> findAllByRuleIdAndActive(Long ruleId, boolean active);

    Optional<ConfidentialTalent> findByTalentId(Long talentId);

    Optional<ConfidentialTalent> findByTalentIdAndActive(Long talentId, boolean active);

    List<ConfidentialTalent> findAllByTalentIdInAndActive(Collection<Long> talentIds, boolean active);

    List<ConfidentialTalent> findAllByConfidentialOwnerAndActive(Long confidentialOwner, boolean active);
}