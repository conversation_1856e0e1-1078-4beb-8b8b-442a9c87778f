package com.altomni.apn.talent.domain.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentCurrentLocation.
 */
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "talent_current_location")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentCurrentLocation extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "official_county")
    private String officialCounty;

    @Column(name = "official_country")
    private String officialCountry;

    @Column(name = "official_province")
    private String officialProvince;

    @Column(name = "official_city")
    private String officialCity;

    @Column(name = "zip_code")
    private String zipCode;

    @Column(name = "original_loc")
    private String originalLoc;

}
