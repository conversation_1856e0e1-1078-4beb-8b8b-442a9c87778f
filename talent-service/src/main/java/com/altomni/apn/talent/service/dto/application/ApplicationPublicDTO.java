package com.altomni.apn.talent.service.dto.application;

import com.altomni.apn.common.dto.talent.TalentPublicDTO;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * A Application.
 */
public class ApplicationPublicDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "The talent's email address.")
    private String email;

    @ApiModelProperty(value = "The talent's phone number.")
    private String phone;

    @ApiModelProperty(value = "The talent's submit user id.")
    private Long userId;

    @ApiModelProperty(value = "The talent submit job id.")
    private Long jobId;

    @ApiModelProperty(value = "The talent submit talent id.")
    private Long talentId;

    @ApiModelProperty(value = "The talent id of apn.")
    private Long apnTalentId;

    @ApiModelProperty(value = "The tenant id of apn.")
    private Long apnTenantId;

    @ApiModelProperty(value = "The talent object of this application")
    private TalentPublicDTO talent;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getApnTalentId() {
        return apnTalentId;
    }

    public ApplicationPublicDTO apnTalentId(Long apnTalentId) {
        this.apnTalentId = apnTalentId;
        return this;
    }

    public void setApnTalentId(Long apnTalentId) {
        this.apnTalentId = apnTalentId;
    }

    public Long getApnTenantId() {
        return apnTenantId;
    }

    public void setApnTenantId(Long apnTenantId) {
        this.apnTenantId = apnTenantId;
    }

    public TalentPublicDTO getTalent() {
        return talent;
    }

    public void setTalent(TalentPublicDTO talent) {
        this.talent = talent;
    }

    @Override
    public String toString() {
        return "ApplicationPublicDTO{" +
            "id=" + id +
            ", email='" + email + '\'' +
            ", phone='" + phone + '\'' +
            ", userId=" + userId +
            ", jobId=" + jobId +
            ", talentId=" + talentId +
            ", apnTalentId=" + apnTalentId +
            ", apnTenantId=" + apnTenantId +
            ", talent=" + talent +
            '}';
    }
}
