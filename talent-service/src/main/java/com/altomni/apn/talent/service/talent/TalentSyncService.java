package com.altomni.apn.talent.service.talent;

import com.altomni.apn.talent.web.rest.vm.MqMessageCountVM;

import java.util.Collection;

public interface TalentSyncService {

    String syncFailedTalentsToEs();

    void syncTalentsToEs();

    MqMessageCountVM checkTalentMqMessageCount();

    void syncTalentsToMQ(Collection<Long> talentIds, int priority);

    void bulkSyncTalentsToMQ(Collection<Long> talentIds, int priority);

    void syncTalentsToHrMQ(Collection<Long> talentIds, int priority);

    void scanTalentsWithManagerTitle();

    void syncTalentsToMQByOwner(Long userId);

    MqMessageCountVM checkTalentHrMqMessageCount();
}
