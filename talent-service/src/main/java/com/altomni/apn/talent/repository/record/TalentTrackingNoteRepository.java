package com.altomni.apn.talent.repository.record;

import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatform;
import com.altomni.apn.talent.domain.record.TalentTrackingNote;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data  repository for the TalentTrackingNote entity.
 */
@Repository
public interface TalentTrackingNoteRepository extends JpaRepository<TalentTrackingNote, Long> {

    List<TalentTrackingNote> findAllByTenantIdAndPlatformIdAndTrackingPlatform(Long tenantId, String platformId, TrackingPlatform trackingPlatform);

    List<TalentTrackingNote> findAllByTenantIdAndPlatformId(Long tenantId, String platformId);

    List<TalentTrackingNote> findAllBySyncedTalentIdIsOrderByCreatedDateDesc(Long syncedTalentId);

    @Modifying
    @Transactional
    @Query(value = "update talent_tracking_note set synced_talent_id = ?1 WHERE id = ?2", nativeQuery = true)
    int updateSyncedTalentId(Long syncedTalentId, Long id);

}
