package com.altomni.apn.talent.service.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentESHitsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Object total;

    private BigDecimal max_score;

    private List<TalentESSourceDTO> hits;
}
