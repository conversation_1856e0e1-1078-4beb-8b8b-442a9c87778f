package com.altomni.apn.talent.service.application;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessForTalentVOSimple implements Serializable {

    private static final long serialVersionUID = 3859144837158086029L;

    private Long id;

    private Long talentId;

    private String talentName;

    private Long jobId;

    private String jobCode;

    private String jobTitle;

    private Long companyId;

    private String companyName;

    private Boolean privateJob;

}
