package com.altomni.apn.talent.config;

import feign.RequestTemplate;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Type;

public class CustomFeignEncoder implements Encoder {
    private final SpringFormEncoder springFormEncoder;
    private final SpringEncoder springEncoder;

    public CustomFeignEncoder(ObjectFactory<HttpMessageConverters> messageConverters) {
        this.springFormEncoder = new SpringFormEncoder();
        this.springEncoder = new SpringEncoder(messageConverters);
    }

    @Override
    public void encode(Object object, Type bodyType, RequestTemplate template) {
        if (bodyType.equals(MultipartFile.class) ||
                (object != null && object.getClass().isArray() && MultipartFile.class.isAssignableFrom(((Object[])object)[0].getClass()))) {
            springFormEncoder.encode(object, bodyType, template);
        } else {
            springEncoder.encode(object, bodyType, template);
        }
    }
}
