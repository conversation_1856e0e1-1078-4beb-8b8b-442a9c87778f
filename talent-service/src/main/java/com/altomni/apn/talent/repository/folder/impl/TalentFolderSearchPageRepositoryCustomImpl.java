package com.altomni.apn.talent.repository.folder.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.talent.repository.folder.TalentFolderSearchPageRepositoryCustom;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class TalentFolderSearchPageRepositoryCustomImpl implements TalentFolderSearchPageRepositoryCustom {
    final static String SET_PARAMETER = "setParameter";

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        entityManager.clear();
        Integer key = SqlUtil.checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, SET_PARAMETER, Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        return query.getResultList();
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchData(queryStr, clazz, paramMap);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }


    @Override
    public Long searchCount(String queryStr, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr);
        Method method = ReflectUtil.getMethod(Query.class, SET_PARAMETER, Integer.class, Object.class);
        map.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v));
        return Long.parseLong(String.valueOf(query.getSingleResult()));
    }
}
