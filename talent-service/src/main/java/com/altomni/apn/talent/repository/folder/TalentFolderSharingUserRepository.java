package com.altomni.apn.talent.repository.folder;


import com.altomni.apn.common.dto.folder.FolderNamePermissionDTOWithCreatedDate;
import com.altomni.apn.common.dto.folder.FolderSharedUserDTO;
import com.altomni.apn.talent.domain.folder.TalentFolderSharingUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TalentFolderSharingUserRepository extends JpaRepository<TalentFolderSharingUser, Long> {

    List<TalentFolderSharingUser> findAllByTalentFolderIdInAndUserId(List<Long> sharedFolderIds, Long userId);

    List<TalentFolderSharingUser> findAllByTalentFolderId(Long talentFolderId);

    Optional<TalentFolderSharingUser> findByTalentFolderIdAndUserId(Long folderId, Long userId);

    @Query("SELECT DISTINCT new com.altomni.apn.common.dto.folder.FolderSharedUserDTO(u.id, u.firstName, u.lastName) " +
            "FROM TalentFolder tf " +
            "JOIN TalentFolderSharingUser tfsu ON tf.id = tfsu.talentFolderId " +
            "JOIN User u ON tfsu.userId = u.id " +
            "WHERE tf.permissionUserId = :userId")
    List<FolderSharedUserDTO> findDistinctTalentFolderSharedUsersByUserId(@Param("userId") Long userId);


    @Query("SELECT new com.altomni.apn.common.dto.folder.FolderSharedUserDTO(tfsu.talentFolderId, tfsu.userId, u.firstName, u.lastName ,tfsu.permissionUserId, tfsu.permission) " +
            "FROM TalentFolderSharingUser tfsu " +
            "JOIN User u ON tfsu.userId = u.id " +
            "WHERE tfsu.talentFolderId IN (:folderIds)")
    List<FolderSharedUserDTO> findTalentFolderSharingUserByTalentFolderIdIn(@Param("folderIds") List<Long> folderIds);

    @Query("SELECT DISTINCT new com.altomni.apn.common.dto.folder.FolderNamePermissionDTOWithCreatedDate(jfsu.talentFolderId, f.name, jfsu.permission, f.createdDate) " +
            "FROM TalentFolderSharingUser jfsu " +
            "JOIN TalentFolder f on jfsu.talentFolderId = f.id " +
            "WHERE jfsu.userId = :userId  ")
    List<FolderNamePermissionDTOWithCreatedDate> findUserSharedFolderByUserId(@Param("userId") Long userId);
}
