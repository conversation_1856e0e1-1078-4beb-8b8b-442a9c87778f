package com.altomni.apn.talent.domain.enumeration.record;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum TrackingType implements ConvertedEnum<Integer> {
    EMAIL(0),
    PHONE(1);

    private final Integer dbValue;

    TrackingType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TrackingType, Integer> resolver = new ReverseEnumResolver<>(TrackingType.class, TrackingType::toDbValue);

    public static TrackingType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
