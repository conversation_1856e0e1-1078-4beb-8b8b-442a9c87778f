package com.altomni.apn.talent.service.folder.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.folder.FolderNamePermissionDTO;
import com.altomni.apn.common.dto.folder.FolderNamePermissionDTOWithCreatedDate;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.folder.TalentFolderSharingTeam;
import com.altomni.apn.talent.domain.folder.TalentFolderSharingUser;
import com.altomni.apn.talent.repository.folder.TalentFolderSharingTeamRepository;
import com.altomni.apn.talent.repository.folder.TalentFolderSharingUserRepository;
import com.altomni.apn.talent.service.folder.TalentSharedCustomFolderService;
import com.altomni.apn.talent.service.mapper.folder.TalentFolderSharingTeamMapper;
import com.altomni.apn.talent.service.mapper.folder.TalentFolderSharingUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
public class TalentSharedCustomFolderServiceImpl implements TalentSharedCustomFolderService {

    @Resource
    private TalentFolderSharingTeamRepository talentFolderSharingTeamRepository;

    @Resource
    private TalentFolderSharingUserRepository talentFolderSharingUserRepository;


    @Resource
    private TalentFolderSharingUserMapper talentFolderSharingUserMapper;

    @Resource
    private TalentFolderSharingTeamMapper talentFolderSharingTeamMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Override
    @Transactional
    public void removeSharingForSharedFolder(Long folderId) {
        if (folderId == null || SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        boolean isShareToUser = removeFolderSharingToUser(folderId);
        boolean isShareToTeam = removeUserFromFolderSharingToTeam(folderId);
        //talentSearchFolderService.disableSearchFolderWithSharedFolderParams(folderId, SecurityUtils.);

        if (!isShareToTeam && !isShareToUser) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_REMOVESHARINGFORSHAREDFOLDER_NOSHARE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    private boolean removeFolderSharingToUser(Long folderId) {

        Optional<TalentFolderSharingUser> talentFolderSharing = talentFolderSharingUserRepository.findByTalentFolderIdAndUserId(folderId, SecurityUtils.getUserId());
        if (talentFolderSharing.isEmpty()) {
            return false;
        }

        talentFolderSharingUserRepository.delete(talentFolderSharing.get());

        //disable the use of folderId in searchFolder
        //talentSearchFolderService.disableSearchFolderWithFolderParamFromUserShared(folderId, Collections.singletonList(SecurityUtils.getUserId()));

        return true;
    }

    private boolean removeUserFromFolderSharingToTeam(Long folderId) {
        Optional<TalentFolderSharingTeam> talentFolderSharing = talentFolderSharingTeamRepository.findByTalentFolderIdAndTeamId(folderId, SecurityUtils.getTeamId());
        if (talentFolderSharing.isEmpty()) {
            return false;
        }

        TalentFolderSharingTeam talentFolderSharingTeam = talentFolderSharing.get();
        Set<Long> ids = talentFolderSharingTeamMapper.jsonStringToSet(talentFolderSharingTeam.getExcludedUserIds());
        if (ids.contains(SecurityUtils.getUserId())) {//handle already removed sharing user
            return false;
        }
        ids.add(SecurityUtils.getUserId());
        talentFolderSharingTeam.setExcludedUserIds(talentFolderSharingTeamMapper.setToJsonString(ids));
        talentFolderSharingTeamRepository.save(talentFolderSharingTeam);

        //disable the use of folderId in searchFolder
        //talentSearchFolderService.disableSearchFolderWithFolderParamFromUserShared(folderId, Collections.singletonList(SecurityUtils.getUserId()));

        return true;
    }





    @Override
    public List<FolderNamePermissionDTO> getAllSharedJobFolderNamePermissionList(Long userId) {
        //List<FolderNamePermissionDTO> teamSharedFolderList = talentFolderSharingTeamRepository.findTeamSharedFolderByUserId(userId);
        List<FolderNamePermissionDTOWithCreatedDate> teamSharedFolderList = talentFolderSharingTeamRepository.findTeamSharedFolderByUserId(userId)
                .stream().map( f->new FolderNamePermissionDTOWithCreatedDate(f.getId(), f.getName(), FolderPermission.fromDbValue(f.getFolderPermission()), f.getFolderCreatedDate()))
                .collect(Collectors.toList());

        List<FolderNamePermissionDTOWithCreatedDate> userSharedFolderList = talentFolderSharingUserRepository.findUserSharedFolderByUserId(userId);
        List<FolderNamePermissionDTOWithCreatedDate> combinedList = Stream.concat(teamSharedFolderList.stream(), userSharedFolderList.stream())
                .collect(Collectors.toList());
        Map<Long, FolderNamePermissionDTOWithCreatedDate> folderMap = combinedList.stream()
                .collect(Collectors.toMap(
                        FolderNamePermissionDTOWithCreatedDate::getId,
                        Function.identity(),
                        (f1, f2) -> new FolderNamePermissionDTOWithCreatedDate(f1.getId(), f1.getName(), FolderPermission.combine(f1.getFolderPermission(), f2.getFolderPermission()), f1.getFolderCreatedDate())
                ));

        List<FolderNamePermissionDTOWithCreatedDate> finalCombinedList = new ArrayList<>(folderMap.values());
        Collections.sort(finalCombinedList, Comparator.comparing(FolderNamePermissionDTOWithCreatedDate::getFolderCreatedDate).reversed());
        return new ArrayList<>(finalCombinedList);
    }

}
