package com.altomni.apn.talent.service.dto.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ESQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private ESBoolDTO query;

    private Integer size;

    private Integer from;

    private List<String> _source;

    private Object sort;
}
