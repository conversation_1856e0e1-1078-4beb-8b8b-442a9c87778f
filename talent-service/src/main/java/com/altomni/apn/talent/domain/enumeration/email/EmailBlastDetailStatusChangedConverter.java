package com.altomni.apn.talent.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class EmailBlastDetailStatusChangedConverter extends AbstractAttributeConverter<EmailBlastDetailStatusChanged, Integer> {
    public EmailBlastDetailStatusChangedConverter() {
        super(EmailBlastDetailStatusChanged::toDbValue, EmailBlastDetailStatusChanged::fromDbValue);
    }
}
