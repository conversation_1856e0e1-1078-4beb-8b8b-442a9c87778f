package com.altomni.apn.talent.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The EmailBlastStatus enumeration.
 */
public enum EmailBlastDetailUpdatedToEsStatus implements ConvertedEnum<Integer> {

    HAS_NOT_UPDATED_TO_ES(0),
    HAS_UPDATED_TO_ES(1),
    NON_EXIST_IN_ES(2);

    private final Integer dbValue;

    EmailBlastDetailUpdatedToEsStatus(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<EmailBlastDetailUpdatedToEsStatus, Integer> resolver =
        new ReverseEnumResolver<>(EmailBlastDetailUpdatedToEsStatus.class, EmailBlastDetailUpdatedToEsStatus::toDbValue);

    public static EmailBlastDetailUpdatedToEsStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
