package com.altomni.apn.talent.service.dto.tracking;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;


@Data
@ApiModel(value = "talent tracking group")
@NoArgsConstructor
@AllArgsConstructor
public class TalentTrackingGroupMemberDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "firstName")
    @NotEmpty
    private String firstName;

    @ApiModelProperty(value = "lastName")
    @NotEmpty
    private String lastName;

    @ApiModelProperty(value = "photoUrl")
    private String photoUrl;

    @ApiModelProperty(value = "title")
    private String title;

    @ApiModelProperty(value = "talentLinkedinId")
    @NotEmpty
    private String talentLinkedinId;

    @ApiModelProperty(value = "linkUrl")
    @NotEmpty
    private String linkUrl;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TalentTrackingGroupMemberDTO that = (TalentTrackingGroupMemberDTO) o;
        return talentLinkedinId.equals(that.talentLinkedinId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(talentLinkedinId);
    }
}
