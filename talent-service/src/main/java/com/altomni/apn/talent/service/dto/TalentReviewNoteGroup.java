package com.altomni.apn.talent.service.dto;

import com.altomni.apn.common.enumeration.ReviewedByType;
import com.altomni.apn.talent.domain.talent.TalentReviewNote;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class TalentReviewNoteGroup {
    private Long reviewedBy;
    private ReviewedByType reviewedByType;
    private List<TalentReviewNote> groupReviewNote = new ArrayList<>();

    public void addReviewNote(TalentReviewNote reviewNote) {
        groupReviewNote.add(reviewNote);
    }
}
