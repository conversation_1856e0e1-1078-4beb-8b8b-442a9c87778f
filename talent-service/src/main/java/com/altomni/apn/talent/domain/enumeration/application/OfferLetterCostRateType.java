package com.altomni.apn.talent.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The OfferLetterCostRateType enumeration.
 */
public enum OfferLetterCostRateType implements ConvertedEnum<Integer>{

    TAX_BURDEN_RATE(0),

    MSP_RATE(1),

    IMMIGRATION_COST(2);

    private final Integer dbValue;

    OfferLetterCostRateType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<OfferLetterCostRateType, Integer> resolver =
        new ReverseEnumResolver<>(OfferLetterCostRateType.class, OfferLetterCostRateType::toDbValue);

    public static OfferLetterCostRateType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
