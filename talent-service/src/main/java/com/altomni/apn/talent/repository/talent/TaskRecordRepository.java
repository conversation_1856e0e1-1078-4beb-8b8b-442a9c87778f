package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.talent.domain.talent.TaskRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the TaskRecord entity.
 */
@Repository
public interface TaskRecordRepository extends JpaRepository<TaskRecord, Long> {

    Page<TaskRecord> findAllByCreatedByOrderByIdDesc(String createdBy, Pageable pageable);

}
