package com.altomni.apn.talent.service.dto.talent;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class CategoryCountDTO {

    @ApiModelProperty(value = "enum id of category, for sort")
    private int categoryId;
    private Long count;

    @ApiModelProperty(value = "enum name of category")
    private String category;


    public CategoryCountDTO(int category, Long count, String pageCategory) {
        this.categoryId = category;
        this.count = count;
        this.category = pageCategory;
    }


}
