package com.altomni.apn.talent.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ProspectClientGroupMemberRole enumeration.
 */
public enum ProspectClientGroupMemberRole implements ConvertedEnum<Integer> {
    OWNER(0),
    MEMBER(1);


    private final Integer dbValue;

    ProspectClientGroupMemberRole(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<com.altomni.apn.talent.domain.enumeration.company.ProspectClientGroupMemberRole, Integer> resolver =
        new ReverseEnumResolver<>(com.altomni.apn.talent.domain.enumeration.company.ProspectClientGroupMemberRole.class, com.altomni.apn.talent.domain.enumeration.company.ProspectClientGroupMemberRole::toDbValue);

    public static com.altomni.apn.talent.domain.enumeration.company.ProspectClientGroupMemberRole fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
