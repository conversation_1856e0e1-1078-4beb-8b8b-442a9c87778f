package com.altomni.apn.talent.service.dto.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.job.service.dto.job.JobPublicDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Objects;

/**
 * A HotListTalentDTO.
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HotListTalentDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "hot list id", required = true)
    private Long hotListId;

    @ApiModelProperty(value = "talent id", required = true)
    private Long talentId;

    @ApiModelProperty(value = "job id", required = true)
    private Long jobId;

    // ************************************relate entity*********************************************
    @ApiModelProperty(value = "Hotlist talent.")
    private TalentDTOV3 talent = new TalentDTOV3();

    @ApiModelProperty(value = "potential job for hotlist talent.")
    private JobPublicDTO job = new JobPublicDTO();


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getHotListId() {
        return hotListId;
    }

    public HotListTalentDTO hotListId(Long hotListId) {
        this.hotListId = hotListId;
        return this;
    }

    public void setHotListId(Long hotListId) {
        this.hotListId = hotListId;
    }


    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public TalentDTOV3 getTalent() {
        return talent;
    }

    public void setTalent(TalentDTOV3 talent) {
        this.talent = talent;
    }

    public JobPublicDTO getJob() {
        return job;
    }

    public void setJob(JobPublicDTO job) {
        this.job = job;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HotListTalentDTO that = (HotListTalentDTO) o;
        return id.equals(that.id) &&
            hotListId.equals(that.hotListId) &&
            talentId.equals(that.talentId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, hotListId, talentId);
    }

    @Override
    public String toString() {
        return "HotListTalentDTO{" +
            "id=" + id +
            ", hotListId=" + hotListId +
            ", talentId=" + talentId +
            ", jobId=" + jobId +
            ", talent=" + talent +
            ", job=" + job +
            '}';
    }
}
