package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class TrackingCategoryConverter extends AbstractAttributeConverter<TrackingCategory, Integer> {
    public TrackingCategoryConverter() {
        super(TrackingCategory::toDbValue, TrackingCategory::fromDbValue);
    }
}
