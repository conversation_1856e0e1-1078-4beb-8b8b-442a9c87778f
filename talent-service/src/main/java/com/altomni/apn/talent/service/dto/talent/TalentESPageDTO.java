package com.altomni.apn.talent.service.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentESPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer size;

    private Integer from;

    @SerializedName("_source")
    private List<String> source;

}
