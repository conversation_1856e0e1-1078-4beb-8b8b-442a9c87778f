package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ParseType implements ConvertedEnum<Integer> {
    RESUME(0),
    JD(1),
    JDTEXT(2)
    ;

    private final Integer dbValue;

    ParseType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ParseType, Integer> resolver =
        new ReverseEnumResolver<>(ParseType.class, ParseType::toDbValue);

    public static ParseType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
