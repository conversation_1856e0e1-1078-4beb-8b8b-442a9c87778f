package com.altomni.apn.talent.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class MailingListStatusConverter extends AbstractAttributeConverter<MailingListStatus, Integer> {
    public MailingListStatusConverter() {
        super(MailingListStatus::toDbValue, MailingListStatus::fromDbValue);
    }
}
