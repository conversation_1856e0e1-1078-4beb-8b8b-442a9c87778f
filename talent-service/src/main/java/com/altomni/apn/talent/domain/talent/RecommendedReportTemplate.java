package com.altomni.apn.talent.domain.talent;

import com.altomni.apn.talent.domain.enumeration.TemplateTypeEnum;
import com.altomni.apn.talent.domain.enumeration.TemplateTypeEnumConverter;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.time.Instant;

@Data
@Entity
@Table(name = "recommended_report_template")
public class RecommendedReportTemplate {
    @Id
    @Column(name = "id", nullable = false)
    private Long id;

    @Size(max = 64)
    @Column(name = "name", length = 64)
    private String name;

    @Column(name = "type")
    @Convert(converter = TemplateTypeEnumConverter.class)
    private TemplateTypeEnum type;

    @Column(name = "report_language")
    private Integer reportLanguage;

    @Column(name = "report_type")
    private Integer reportType;

    @Column(name = "config", columnDefinition = "json")
    @JsonRawValue
    private String config;

    @Size(max = 50)
    @Column(name = "created_by", length = 50)
    private String createdBy;

    @Column(name = "created_date")
    private Instant createdDate;

    @Size(max = 50)
    @Column(name = "last_modified_by", length = 50)
    private String lastModifiedBy;

    @Column(name = "last_modified_date")
    private Instant lastModifiedDate;

}