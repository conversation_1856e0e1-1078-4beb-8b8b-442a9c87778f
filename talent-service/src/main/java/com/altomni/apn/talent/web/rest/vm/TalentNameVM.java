package com.altomni.apn.talent.web.rest.vm;
//Get multiple UserJobRelation from frontend.

import lombok.Data;

import java.util.List;

@Data
public class TalentNameVM {

    private Long id;

    private String firstName;

    private String lastName;

    private String fullName;

    private Long tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
}

