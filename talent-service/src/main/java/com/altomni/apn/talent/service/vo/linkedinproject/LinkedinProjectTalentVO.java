package com.altomni.apn.talent.service.vo.linkedinproject;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinTalent;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectTalentAppliedJobVM;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectTalentContactVM;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectTalentNoteVM;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectTalentProjectVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.*;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LinkedinProjectTalentVO extends AbstractAuditingEntity implements Serializable {

    private Long id;

    private Long tenantId;

    private Long linkedinProjectId;

    private String linkedinTalentId;

    private LinkedinTalent linkedinTalent;

    private CandidateStatus candidateStatus;

    private ContactStatus contactStatus;

    private Boolean hasContactInfo;

    private Boolean isInApplication;

    private Integer projectsCount;

    private List<LinkedinProjectTalentProjectVM> projects;

    private Integer contactHistoriesCount;

    private List<LinkedinProjectTalentContactVM> contactHistories;

    private Integer appliedJobsCount;

    private List<LinkedinProjectTalentAppliedJobVM> appliedJobs;

    private Integer notesCount;

    private List<LinkedinProjectTalentNoteVM> notes;


}
