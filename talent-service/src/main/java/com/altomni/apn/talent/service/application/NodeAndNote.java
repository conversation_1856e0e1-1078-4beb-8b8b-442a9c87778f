package com.altomni.apn.talent.service.application;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NodeAndNote {

    private NodeType nodeType;

//    private NodeStatus nodeStatus;

    private String note;

    private Long id;

    private String lastModifiedBy;

    private Long noteLastModifiedByUserId;

    private Instant lastModifiedDate;

    private String createdBy;

    private Instant createdDate;
}
