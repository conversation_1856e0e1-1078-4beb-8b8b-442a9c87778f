package com.altomni.apn.talent.service.common.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.talent.service.common.HttpService;
import okhttp3.*;
import org.apache.http.HttpHeaders;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class HttpServiceImpl implements HttpService {

    private final Logger log = LoggerFactory.getLogger(HttpServiceImpl.class);

    private final OkHttpClient client;

    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    private static final MediaType PLAIN_TEXT_TYPE = MediaType.parse("text/plain; charset=utf-8");

    public HttpServiceImpl() {
        this.client = createOkHttpClient();
    }

    private OkHttpClient createOkHttpClient() {

//        return new OkHttpClient().newBuilder()
////            .connectTimeout(connectionTimeout, TimeUnit.SECONDS)
////            .readTimeout(readTimeout, TimeUnit.SECONDS)
////            .connectionPool(new ConnectionPool(connectionPoolSize, keepAliveDuration, TimeUnit.MILLISECONDS))
////        .build();
        return new OkHttpClient().newBuilder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(5, 100, TimeUnit.MILLISECONDS))
                .build();
    }

    private HttpResponse execute(Request request) throws IOException {
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            Headers responseHeaders = response.headers() != null ? response.headers() : null;
            return new HttpResponse(response.code(), responseBody, responseHeaders);
        }
    }

    /**
     * OkHttpClient get method
     *
     * @param url url
     * @return HttpResponse
     */
    @Override
    public HttpResponse get(String url) throws IOException {
        return execute(new Request.Builder().url(url).get().build());
    }

    /**
     * OkHttpClient get method
     *
     * @param url url
     * @return HttpResponse
     */
    @Override
    public HttpResponse get(String url, Headers headers) throws IOException {
        return execute(new Request.Builder().url(url).headers(headers).get().build());
    }

    /**
     * OkHttpClient post method
     *
     * @param url         url
     * @param requestBody request body
     * @return HttpResponse
     */
    @Override
    public HttpResponse post(String url, String requestBody) throws IOException {
        return execute(new Request.Builder().url(url).post(RequestBody.create(JSON_TYPE, requestBody)).build());
    }

    @Override
    public HttpResponse patch(String url, String content) throws IOException {
        return execute(new Request.Builder().url(url).patch(RequestBody.create(JSON_TYPE, content)).build());
    }

    /**
     * OkHttpClient post method
     *
     * @param url         url
     * @param requestBody request body
     * @return HttpResponse
     */
    @Override
    public HttpResponse post(String url, Headers headers, String requestBody) throws IOException {
        return execute(new Request.Builder().url(url).headers(headers).post(RequestBody.create(JSON_TYPE, requestBody)).build());
    }

    /**
     * OkHttpClient post method
     *
     * @param url         url
     * @param requestBody request body
     * @return HttpResponse
     */
    @Async
    @Override
    public HttpResponse asyncPost(String url, String requestBody) {
        try {
            return execute(new Request.Builder().url(url).post(RequestBody.create(JSON_TYPE, requestBody)).build());
        } catch (Exception e) {
            log.error("Async post error: {}", e.getMessage());
        }
        return null;
    }

    /**
     * OkHttpClient post method
     *
     * @param url         url
     * @param requestBody request body
     * @return HttpResponse
     */
    @Override
    public HttpResponse postPlainText(String url, String requestBody) throws IOException {
        return execute(new Request.Builder().url(url).post(RequestBody.create(PLAIN_TEXT_TYPE, requestBody)).build());
    }

    /**
     * OkHttpClient post method
     *
     * @param url url
     * @return HttpResponse
     */
    @Override
    public HttpResponse post(String url) throws IOException {
        return execute(new Request.Builder().url(url).build());
    }

    /**
     * OkHttpClient put method
     *
     * @param url url
     * @return HttpResponse
     */
    @Override
    public HttpResponse put(String url) throws IOException {
        return execute(new Request.Builder().url(url).put(RequestBody.create(JSON_TYPE, "{}")).build());
    }

    /**
     * OkHttpClient put method
     *
     * @param url url
     * @return HttpResponse
     */
    @Override
    public HttpResponse put(String url, String content) throws IOException {
        return execute(new Request.Builder().url(url).put(RequestBody.create(JSON_TYPE, content)).build());
    }

    /**
     * CloseableHttpClient post method
     *
     * @param url         url
     * @param param       params
     * @param serviceName eg: parser, rater
     * @return response body
     */
    @Override
    public String post(String url, UrlEncodedFormEntity param, String serviceName) throws IOException {
        HttpPost post = initHttpPost(url, serviceName);
        post.setEntity(param);
        return executePost(post);
    }

    private HttpPost initHttpPost(String url, String serviceName) {
        HttpPost post = new HttpPost(url);
        post.setHeader(HttpHeaders.USER_AGENT, serviceName);
        post.setHeader(HttpHeaders.ACCEPT, ContentType.APPLICATION_JSON.getMimeType());
        post.setHeader(HttpHeaders.CONTENT_TYPE, ContentType.APPLICATION_FORM_URLENCODED.getMimeType());
        post.setHeader("Connection", "keep-alive");
        return post;
    }

    private String executePost(HttpPost post) throws IOException {
        try (CloseableHttpClient client = HttpClientBuilder.create().build()) {
            org.apache.http.HttpResponse response = client.execute(post);
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(response.getEntity().getContent(), StandardCharsets.UTF_8));
            StringBuilder jsonString = new StringBuilder();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                jsonString.append(line);
            }
            return jsonString.toString();
        }
    }

    /**
     * OkHttpClient delete method
     *
     * @param url url
     * @return HttpResponse
     */
    @Override
    public HttpResponse delete(String url) throws IOException {
        return execute(new Request.Builder().url(url).delete().build());
    }

    /**
     * only return headers - contentType
     * @param url
     * @return
     * @throws IOException
     */
    @Override
    public HttpResponse head(String url) {
        try (Response response = client.newCall(new Request.Builder().head().url(url).build()).execute()) {
            JSONArray jsonArray = (JSONArray) JSON.toJSON(response.headers());
            for (Object o : jsonArray) {
                JSONObject jsonObject = (JSONObject) o;
                String first = jsonObject.getString("first");
                if ("Content-Type".equals(first)) {
                    String second = jsonObject.getString("second");
                    return new HttpResponse(response.code(), second);
                }
            }
            return new HttpResponse(response.code(), null);
        } catch (Exception e) {
            return null;
        }
    }
}
