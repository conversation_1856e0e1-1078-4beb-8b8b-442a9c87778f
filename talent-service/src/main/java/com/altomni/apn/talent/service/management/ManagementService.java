package com.altomni.apn.talent.service.management;

import com.altomni.apn.common.domain.user.Tenant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Component
@FeignClient(value = "management-service")
public interface ManagementService {

    @GetMapping("/management/api/v3/public/tenants/{id}")
    ResponseEntity<Tenant> queryTenant(@PathVariable("id") Long id);

}
