package com.altomni.apn.talent.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TalentContactStatus enumeration.
 */
public enum HotlistAccessOption implements ConvertedEnum<Integer>  {
    PUBLIC(0),
    PRIVATE(1);

    private final int dbValue;

    HotlistAccessOption(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<HotlistAccessOption, Integer> resolver =
        new ReverseEnumResolver<>(HotlistAccessOption.class, HotlistAccessOption::toDbValue);

    public static HotlistAccessOption fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
