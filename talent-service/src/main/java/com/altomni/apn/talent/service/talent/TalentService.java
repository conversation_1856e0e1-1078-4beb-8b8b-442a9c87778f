package com.altomni.apn.talent.service.talent;

import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.domain.talent.TelephoneChatScript;
import com.altomni.apn.common.dto.company.ClientContactCompany;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import com.altomni.apn.common.vo.talent.TalentEmailContactVO;
import com.altomni.apn.talent.domain.talent.ExcelTaskDto;
import com.altomni.apn.talent.domain.talent.UpdateExcelTaskDto;
import com.altomni.apn.talent.service.dto.start.TalentExcelUploadUrlDto;
import com.altomni.apn.talent.service.dto.talent.ResignUserReportTalentDTO;
import com.altomni.apn.talent.service.dto.talent.TalentSimilarityDto;
import com.altomni.apn.talent.service.vo.talent.ExcelTalentProcessVo;
import com.altomni.apn.talent.web.rest.talent.dto.BrowseQuotaDTO;
import com.altomni.apn.talent.web.rest.talent.dto.GetTalentDTO;
import com.altomni.apn.talent.web.rest.talent.dto.RecommendedTemplateDTO;
import com.altomni.apn.talent.web.rest.talent.dto.TalentDetailRetrievingRecordSearchDTO;
import com.altomni.apn.talent.web.rest.vm.TalentContactSearchVM;
import com.altomni.apn.talent.web.rest.vm.TalentContactVM;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Set;

public interface TalentService {
    List<TalentDTOV3> findAllWithEntity(List<Long> ids);

    Long create(TalentInfoInput talentDTO);

    TalentDTOV3 createAndResult(String requestBody);

    Long createByExcel(TalentDTOV3 talentDTOV3, String json, String notes, String uuid, String noteType);

    TalentDTOV3 update(Long id, String requestBody);

    TalentDTOV3 updateTalentInfo(TalentDTOV3 dto);

    TalentDTOV3 updateForPro(Long id, TalentInfoInput update);

    GetTalentDTO getTalentInfo(Long id);

    String searchTalentDetailRetrievingRecord(TalentDetailRetrievingRecordSearchDTO talentDetailRetrievingRecordSearchDTO, Pageable pageable) throws IOException;

    TalentDTOV3 findTalentById(Long id, boolean checkLimit);

    TalentOwnershipDTO findTalentOwnerShip(Long id);

    List<TelephoneChatScript> getTelephoneChatScripts();

    TalentDTOV3 findTalentByIdWithoutEntity(Long id);

    List<TalentDTOV3> getTalentsByContacts(TalentContactSearchVM talentContactSearchVM);

    String getTalentName(Long talentId);

    List<ResignUserReportTalentDTO> findTalentsByIds(List<Long> talentIds);

    List<TalentBriefDTO> getTalentsByIdsWithoutEntity(Set<Long> talentIds);

    List<TalentDTOV3> checkExistTalents(TalentDTOV3 talentDTO);

    TalentDTOV3 createAndUpdateCreditTransaction(TalentDTOV3 talentDTO);

    List<TalentEmailContactVO> searchTalentEmailContacts(List<Long> talentIdList);

    SuspectedDuplications getResumeDuplicate(Long id);

    List<TalentDTOV3> searchTalentsByContactAndSimilarityAPNPro(TalentSimilarityDto talentSimilarityDto);

    Set<Long> searchTalentIdsByContactAndSimilarity(TalentSimilarityDto talentSimilarityDto);

    Set<Long> searchTalentIdsByContactAndSimilarity(TalentSimilarityDto talentSimilarityDto, List<TalentContactDTO> contacts, List<TalentContactVM> contactVmList);

    List<SuspectedDuplications> getSuspectededDuplicationsByContactAndSmilarity(TalentV3 talentV3, List<TalentContactDTO> contacts, TalentSimilarityDto talentSimilarityDt, Long ignoreTalentId);

    List<Long> searchTalentsByContactAndSimilarity(TalentDTOV3 talentDTOV3);

    List<TalentDTOV3> searchTalentsByContact(TalentDTOV3 talentDTOV3);

    List<TalentDTOV3> findTalentsBasicByIds(List<Long> ids);

    List<Long> findIdsByTenant(Long tenantId);

    Object fillResumeInfos();

    TalentDTOV3 sortTalentDTO(TalentDTOV3 talentDTOV3);

    List<ClientContactCompany> checkAddedToCompanyContacts(Long id);

    List<RecommendedTemplateDTO> getRecommendedReportTemplate(Long talentId);

    List<ExcelTalentProcessVo> findTalentProgressByTaskIds(List<String> taskIdList);

    void updateTaskStatus(UpdateExcelTaskDto excelTaskDto);

    StoreGetUploadUrlVO getUploadUrlForCreateTalentByExcel(TalentExcelUploadUrlDto uploadUrlDto);

    void downloadCreateTalentResultByExcel(ExcelTaskDto excelTaskDto, HttpServletResponse httpServletResponse);

    boolean deleteTalentResumeRelation(Long talentId);

    void setReviewNoteAttachUserInfo(List<TalentReviewNoteDTO> noteList);

    BrowseQuotaDTO getBrowseQuota();

    List<TalentContactDTO> getTalentContacts(String id, Long jobId);

    List<TalentContactDTO> getTalentAllVerificationStatusContacts(Long id);

    boolean hasRemain(Long id);

    List<SuspectedDuplications> checkContactExist(List<TalentContactDTO> contacts);

    TalentSimilarityDto convertTalentInfoInputToSimilarityDTO(TalentInfoInput input);

    boolean hasTalentViewAuthority(Long id);

    void upsertExperience(Long talentId, TalentExperienceDTO talentExperienceDTO);

    void deleteExperienceByTalentRecruitmentProcessId(Long talentId, Long talentRecruitmentProcessId);

    List<SuspectedDuplications> suspectedDuplicatePhonesCheck(String requestBody);

    void duplicateContactsCheck(TalentInfoInput input);

    List<SuspectedDuplications> apnProDuplicateCheck(TalentInfoInput input);

    void cleanUpContacts(List<TalentContactDTO> contacts);

    Set<TalentBriefVO> getAllBriefTalentsByTalentIds(Set<Long> talentIds);

    void syncClientContactToCrm(Long talentId);

    TalentDTOV3 singleSaveTalent(Long id, String requestBody);
}
