package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.dict.TalentLanguageRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface TalentLanguageRelationRepository extends JpaRepository<TalentLanguageRelation, Long> {


    @Query(value = "SELECT tlr.* FROM talent_language_relation tlr LEFT JOIN hot_list_talent hlt ON tlr.talent_id = hlt.talent_id WHERE hlt.hot_list_id = ?1" , nativeQuery = true)
    List<TalentLanguageRelation> findAllByHotlistId(Long hotlistId);
}
