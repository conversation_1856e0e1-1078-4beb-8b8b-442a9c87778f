package com.altomni.apn.talent.service.report;

import com.altomni.apn.talent.domain.report.ESStats;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "report-service")
public interface EsStatsService {

   @PostMapping("/es-stats/save")
   ResponseEntity<ESStats> save(@RequestBody ESStats eSStats);

}
