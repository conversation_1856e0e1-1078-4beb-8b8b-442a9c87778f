package com.altomni.apn.talent.service.voip;

import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.common.vo.voip.VoipContactDetailVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

@Component
@FeignClient(value = "voip-service")
public interface VoipService {

    @GetMapping("/voip/api/v3/connect/voip-contact/{id}")
    ResponseEntity<VoipContactDTO> getVoipContact(@PathVariable(value = "id") String id);

    @PostMapping("/voip/api/v3/connect/record-contact-detail/by-phone-call-id-in")
    ResponseEntity<List<VoipContactDetailVO>> getVoipContactDetailByPhoneCallIdIn(@RequestBody Set<String> phoneCallIds);

}
