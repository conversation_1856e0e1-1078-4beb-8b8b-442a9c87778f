package com.altomni.apn.talent.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class EmailBlastStatusConverter extends AbstractAttributeConverter<EmailBlastStatus, Integer> {
    public EmailBlastStatusConverter() {
        super(EmailBlastStatus::toDbValue, EmailBlastStatus::fromDbValue);
    }
}
