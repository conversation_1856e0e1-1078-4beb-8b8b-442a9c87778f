package com.altomni.apn.talent.service.dto.linkedinproject;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.utils.JsonUtil;

import java.io.Serializable;
import java.time.Instant;

public class Filter implements Serializable {

    private static final long serialVersionUID = -1968186687399597950L;

    private String id;

    private String name;

    private String content;

    private Long userId;

    private Instant createDate;

    public String toJSON() {
        JSONObject result = new JSONObject();
        JsonUtil.fluentPut(result, "id", id);
        JsonUtil.fluentPut(result, "userId", userId);
        JsonUtil.fluentPut(result, "name", name);
        JsonUtil.fluentPut(result, "content", content);
        return result.toJSONString(0);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Instant getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Instant createDate) {
        this.createDate = createDate;
    }

    @Override
    public String toString() {
        return "Filter{" +
            "id='" + id + '\'' +
            ", name='" + name + '\'' +
            ", content='" + content + '\'' +
            ", userId=" + userId +
            ", createDate=" + createDate +
            '}';
    }
}
