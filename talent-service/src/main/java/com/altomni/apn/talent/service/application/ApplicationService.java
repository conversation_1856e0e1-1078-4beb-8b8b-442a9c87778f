package com.altomni.apn.talent.service.application;

import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.ReplaceSubmitToJobResumeDTO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessForTalentVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * Service Interface for managing.
 */
@Component
@FeignClient(value = "application-service")
public interface ApplicationService {

//TODO    @GetMapping("/application/count-by-talent-id/{talentId}")
//    ResponseEntity<Integer> countByTalentId(@PathVariable("talentId") Long talentId);
//
    @GetMapping("/application/api/v3/talent-recruitment-processes/all/talentId/{talentId}")
    ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessAllByTalentId(@PathVariable("talentId") Long talentId);

    @GetMapping("/application/api/v3/talent-recruitment-processes-brief/talentId/{talentId}")
    ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessBriefByTalentId(@PathVariable("talentId") Long talentId);

    @GetMapping("/application/api/v3/talent-recruitment-processes-brief/jobId/{jobId}")
    ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessBriefByJobId(@PathVariable("jobId") Long jobId);

    @GetMapping("/application/api/v3/talent-recruitment-processes/list/talentId/{talentId}")
    ResponseEntity<List<TalentRecruitmentProcessForTalentVO>> getTalentRecruitmentProcessListByTalentId(@PathVariable("talentId") Long talentId);

    @GetMapping("/application/api/v3/talent-recruitment-processes/talentId/{talentId}/nodeType/{nodeType}")
    ResponseEntity<Integer> countApplicationByTalentId(@PathVariable("talentId") Long talentId, @PathVariable("nodeType") Integer nodeType);

    @GetMapping("/application/api/v3/recruitment-processes/{id}")
    ResponseEntity<RecruitmentProcessVO> getRecruitmentProcessById(@PathVariable("id")Long recruitmentProcessId);

    @GetMapping("/application/api/v3/talent-recruitment-processes/replace/submit-to-job/resume")
    ResponseEntity<Void> replaceSubmitToJobResume(@RequestBody ReplaceSubmitToJobResumeDTO replaceSubmitToJobResumeDTO);

    @GetMapping("/application/api/v3/talent-recruitment-processes/no-object/talentId/{talentId}")
    ResponseEntity<List<TalentRecruitmentProcessForTalentVOSimple>> getTalentRecruitmentProcessListVOByTalentId(@PathVariable("talentId") Long talentId);

    @GetMapping("/application/api/v3/talent-recruitment-processes/note")
    ResponseEntity<AllNotesVO> getAllNotesByTalentRecruitmentProcessId(@RequestParam(value = "talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @PostMapping("/application/api/v3/talent-recruitment-processes/user/{userId}/filter-talent-ids")
    ResponseEntity<Set<Long>> filterTalentIdsByUserIdAndTalentIds(@PathVariable("userId") Long userId, @RequestBody Set<Long> talentIds);

    @GetMapping("/application/api/v3/talent-recruitment-processes/talent/{talentId}/resignations")
    ResponseEntity<List<TalentRecruitmentProcessResignationVO>> getResignationsByTalentId(@PathVariable("talentId") Long talentId);

    @PutMapping("/application/api/v3/talent-recruitment-processes/resigned-user/{userId}/recalculate-commission")
    ResponseEntity<Void> recalculateOwnerCommission(@PathVariable("userId") Long userId);
}
