package com.altomni.apn.talent.domain.enumeration.linkedinproject;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CandidateStatus implements ConvertedEnum<Integer>{

    ACTIVE(0),

    ARCHIVED(1);


    private final Integer dbValue;

    CandidateStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<CandidateStatus, Integer> resolver =
        new ReverseEnumResolver<>(CandidateStatus.class, CandidateStatus::toDbValue);

    public static CandidateStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
