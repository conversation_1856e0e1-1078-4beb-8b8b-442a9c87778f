package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Set;


/**
 * Spring Data  repository for the TalentOwner entity.
 */
@Repository
public interface TalentOwnershipRepository extends JpaRepository<TalentOwnership, Long> {

    List<TalentOwnership> findAllByTalentIdAndExpireTimeGreaterThanEqual(Long talentId, Instant now);

    List<TalentOwnership> findAllByTalentIdAndOwnershipTypeAndExpireTimeGreaterThanEqual(Long talentId, TalentOwnershipType ownershipType, Instant now);

    List<TalentOwnership> findAllByTalentIdAndOwnershipTypeInAndExpireTimeGreaterThanEqual(Long talentId, List<TalentOwnershipType> ownershipType, Instant now);
    List<TalentOwnership> findAllByTalentIdAndOwnershipTypeIn(Long talentId, List<TalentOwnershipType> ownershipType);

    List<TalentOwnership> findAllByTalentIdAndOwnershipType(Long talentId, TalentOwnershipType ownershipType);

    List<TalentOwnership> findAllByTalentId(Long talentId);

    void deleteAllByTalentIdAndOwnershipType(Long talentId, TalentOwnershipType talentOwnershipType);

    void deleteAllByTalentIdAndUserIdInAndOwnershipTypeIn(Long talentId, List<Long> userId, List<TalentOwnershipType> talentOwnershipType);

    List<TalentOwnership> findAllByTalentIdInAndExpireTimeGreaterThanEqual(List<Long> talentIds, Instant now);

    List<TalentOwnership> findAllByTalentIdInAndOwnershipTypeIn(List<Long> talentIds, List<TalentOwnershipType> ownershipType);

    @Modifying
    @Query(value = "UPDATE TalentOwnership t SET t.userId =:newOwnerId WHERE t.userId=:userId and t.ownershipType <> :ownershipType")
    void transferOwnershipExceptRole(@Param("userId") Long userId, @Param("newOwnerId") Long newOwnerId, @Param("ownershipType") TalentOwnershipType ownershipType);

    @Modifying
    @Query(value = "UPDATE TalentOwnership t SET t.expireTime =:expireTime WHERE t.userId=:userId and t.ownershipType = :ownershipType and t.expireTime > :expireTime")
    void expireOwnershipByRole(@Param("userId") Long userId, @Param("ownershipType") TalentOwnershipType ownershipType, @Param("expireTime") Instant expireTime);

    @Modifying
    @Query(value = "delete o1 from talent_ownership o1 " +
            "inner join talent_ownership o2 on o1.user_id=o2.user_id " +
            "where o1.user_id=:userId and o1.ownership_type=0 and o2.user_id=:userId and o2.ownership_type=2", nativeQuery = true)
    void removeDuplicateOwnerAndShare(@Param("userId") Long userId);

    List<TalentOwnership> findAllByTalentIdIn(List<Long> talentIds);

    @Query(value = "select t.code from permission_team t " +
            "left join permission_user_team ut on ut.team_id=t.id " +
            "left join talent_ownership o on ut.user_id = o.user_id " +
            "where o.talent_id=:talentId and o.ownership_type=2 and t.id in :teamIds", nativeQuery = true)
    Set<String> getTeamCodesByTalentIdAndTeamIdsIn(@Param("talentId") Long talentId, @Param("teamIds") Collection<Long> teamIds);

    @Query(value = "select o.talent_id from permission_team t " +
            "inner join permission_user_team ut on ut.team_id=t.id " +
            "inner join talent_ownership o on ut.user_id = o.user_id " +
            "where o.talent_id in :talentIds and o.ownership_type=2 and t.id in :teamIds", nativeQuery = true)
    Set<Long> getTalentsByTalentIdInAndTeamIdIn(@Param("talentIds") Set<Long> talentIds, @Param("teamIds") Collection<Long> teamIds);

    @Query(value = "select t.code from permission_team t " +
            "left join permission_user_team ut on ut.team_id=t.id " +
            "where ut.user_id=:userId", nativeQuery = true)
    Set<String> getTeamCodesByUserId(@Param("userId") Long userId);

    @Query(value = "select ut.team_id from permission_user_team ut " +
            "left join talent_ownership o on ut.user_id = o.user_id " +
            "where o.talent_id=:talentId and ut.is_primary=1 and o.ownership_type=2 " +
            "union " +
            "select ut.team_id from permission_user_team ut " +
            "left join talent t on t.puser_id = ut.user_id " +
            "where t.id=:talentId and ut.is_primary=1", nativeQuery = true)
    Set<Long> getTeamIdsByTalentId(@Param("talentId") Long talentId);
}
