package com.altomni.apn.talent.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TalentContactStatus enumeration.
 */
public enum HotlistSourceType implements ConvertedEnum<Integer>  {
    APN_PRO(0),
    CONTRACTING(1),
    FTE(2),
    CAMPUS_RECRUITING(3),
    FLAG_TRAINING(4);


    private final int dbValue;

    HotlistSourceType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<HotlistSourceType, Integer> resolver =
        new ReverseEnumResolver<>(HotlistSourceType.class, HotlistSourceType::toDbValue);

    public static HotlistSourceType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
