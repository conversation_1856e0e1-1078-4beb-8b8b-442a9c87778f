package com.altomni.apn.talent.service.tracking.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.GroupMemberStatus;
import com.altomni.apn.talent.domain.enumeration.tracking.*;
import com.altomni.apn.talent.domain.linkedin.TalentTrackingLinkedinPendingGroup;
import com.altomni.apn.talent.domain.linkedin.TalentTrackingLinkedinPendingGroupMember;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroup;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroupMember;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinMessageTemplate;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinPending;
import com.altomni.apn.talent.repository.linkedin.TalentTrackingLinkedinPendingGroupMemberRepository;
import com.altomni.apn.talent.repository.linkedin.TalentTrackingLinkedinPendingGroupRepository;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinGroupMemberRepository;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinGroupRepository;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinMessageTemplateRepository;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinPendingRepository;
import com.altomni.apn.talent.service.dto.tracking.*;
import com.altomni.apn.talent.service.talent.LinkedinAccountInfoService;
import com.altomni.apn.talent.service.tracking.TalentTrackingService;
import com.altomni.apn.talent.service.vo.tracking.*;
import com.altomni.apn.talent.web.rest.talent.dto.SavePendingGroupMemberRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.ws.rs.ForbiddenException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TalentTrackingServiceImpl implements TalentTrackingService {

    @Resource
    private LinkedinAccountInfoService linkedinAccountInfoService;

    @Resource
    private TalentTrackingLinkedinPendingRepository talentTrackingLinkedinPendingRepository;

    @Resource
    private TalentTrackingLinkedinMessageTemplateRepository talentTrackingLinkedinMessageTemplateRepository;

    @Resource
    private TalentTrackingLinkedinGroupRepository talentTrackingLinkedinGroupRepository;

    @Resource
    private TalentTrackingLinkedinGroupMemberRepository talentTrackingLinkedinGroupMemberRepository;

    @Resource
    private TalentTrackingLinkedinPendingGroupMemberRepository talentTrackingLinkedinPendingGroupMemberRepository;

    @Resource
    private TalentTrackingLinkedinPendingGroupRepository talentTrackingLinkedinPendingGroupRepository;

    //没有软删的人脉status
    private static final List<TrackingStatus> ACTIVE_TRACKING_STATUS = Arrays.asList(TrackingStatus.NOT_SENT, TrackingStatus.SENT);


    @Override
    @Transactional
    public TalentTrackingVO saveTalentTracking(TalentTrackingDTO talentTrackingDTO) {
        TalentTrackingLinkedinPending talentTrackingLinkedinPending = talentTrackingLinkedinPendingRepository.findFirstByTenantIdAndCategoryAndStatusInAndOperatorLinkedinIdAndTalentLinkedinId(SecurityUtils.getTenantId(), talentTrackingDTO.getCategory(), ACTIVE_TRACKING_STATUS, talentTrackingDTO.getOperatorLinkedinId(), talentTrackingDTO.getTalentLinkedinId());
        if (ObjectUtil.isNotEmpty(talentTrackingLinkedinPending)) {
            throw new CustomParameterizedException("talent already exist.");
        }
        // 查询有没有在三周内撤回过此用户的记录, 如果有的话需要回填一下撤回的时间，便于前端控制不允许再次添加好友
        Optional<TalentTrackingLinkedinPending> lastInactiveTrackingOpt = talentTrackingLinkedinPendingRepository.findAllByTenantIdAndStatusInAndOperatorLinkedinIdAndTalentLinkedinIdIn(SecurityUtils.getTenantId(),
                        List.of(TrackingStatus.IN_ACTIVE), talentTrackingDTO.getOperatorLinkedinId(), List.of(talentTrackingDTO.getTalentLinkedinId()))
                .stream().filter(track -> track.getLastInteractionTime() != null)
                .max(Comparator.comparing(TalentTrackingLinkedinPending::getLastInteractionTime))
                .filter(track -> track.getLastInteractionTime().plus(21, ChronoUnit.DAYS).isAfter(Instant.now()));

        talentTrackingLinkedinPending = TalentTrackingLinkedinPending.fromTalentTrackingDTO(talentTrackingDTO);
        talentTrackingLinkedinPending.setStatus(TrackingStatus.NOT_SENT);
        talentTrackingLinkedinPending.setTenantId(SecurityUtils.getTenantId());
        if (lastInactiveTrackingOpt.isPresent()) {
            talentTrackingLinkedinPending.setLastInteractionTime(lastInactiveTrackingOpt.get().getLastInteractionTime());
        }
        talentTrackingLinkedinPending = talentTrackingLinkedinPendingRepository.save(talentTrackingLinkedinPending);
        if(talentTrackingDTO.getGroupIdList() != null && !talentTrackingDTO.getGroupIdList().isEmpty()) {
            SavePendingGroupMemberRequestDTO requestDTO = new SavePendingGroupMemberRequestDTO();
            requestDTO.setGroupIds(talentTrackingDTO.getGroupIdList());
            requestDTO.setMembers(List.of(talentTrackingLinkedinPending.getId()));
            linkedinAccountInfoService.saveGroupMember(requestDTO);
        }
        return TalentTrackingLinkedinPending.toTalentTrackingVO(talentTrackingLinkedinPending);
    }

    @Override
    public Page<TalentTrackingLinkedinPending> searchTalentTracking(TalentTrackingSearchDTO talentTrackingSearchDTO, Pageable pageable) {
        return talentTrackingLinkedinPendingRepository.findAll(queryAllTalentTrackingLinkedinPending(SecurityUtils.getTenantId(), talentTrackingSearchDTO.getCategory(), talentTrackingSearchDTO.getStatus(), talentTrackingSearchDTO.getOperatorLinkedinId()), pageable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inactiveTalentTracking(TalentTrackingManageDTO talentTrackingManageDTO) {
        List<TalentTrackingLinkedinPending> talentTrackingLinkedinPendingList = queryTalentTrackingLinkedinPending(talentTrackingManageDTO.getIds(), talentTrackingManageDTO.getOperatorLinkedinId());
        talentTrackingLinkedinPendingList.forEach(item -> item.setStatus(TrackingStatus.IN_ACTIVE));
        talentTrackingLinkedinPendingRepository.saveAll(talentTrackingLinkedinPendingList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TalentTrackingVO> sentTalentTracking(TalentTrackingManageDTO talentTrackingManageDTO) {
        List<TalentTrackingLinkedinPending> talentTrackingLinkedinPendingList = queryTalentTrackingLinkedinPending(talentTrackingManageDTO.getIds(), talentTrackingManageDTO.getOperatorLinkedinId());
        List<String> talentLinkedinIds = talentTrackingLinkedinPendingList.stream().map(TalentTrackingLinkedinPending::getTalentLinkedinId).toList();
        // 根据候选人领英 id 再次查询，因为同一个候选人可能同时在链接加人列表和待加列表中，同一个候选人都需要设置成已发送状态
        List<TalentTrackingLinkedinPending> realPendingList = talentTrackingLinkedinPendingRepository.findAllByTenantIdAndStatusInAndOperatorLinkedinIdAndTalentLinkedinIdIn(SecurityUtils.getTenantId(),
                ACTIVE_TRACKING_STATUS, talentTrackingManageDTO.getOperatorLinkedinId(), talentLinkedinIds);
        realPendingList.forEach(item -> {
            item.setStatus(TrackingStatus.SENT);
            item.setLastInteractionTime(Instant.now());
        });
        talentTrackingLinkedinPendingRepository.saveAll(realPendingList);
        addWaitingToMemberGroup(talentTrackingManageDTO.getGroupIdList(), realPendingList);
        return realPendingList.stream().map(TalentTrackingLinkedinPending::toTalentTrackingVO).toList();
    }

    private void addWaitingToMemberGroup(List<Long> groupIds, List<TalentTrackingLinkedinPending> realPendingList) {
        if(groupIds == null || groupIds.isEmpty()) {
            return;
        }
        if(realPendingList == null || realPendingList.isEmpty()) {
            return;
        }
        List<TalentTrackingGroupMemberDTO> members = convert2TalentTrackingGroupMemberDTO(realPendingList);
        Long tenantId = SecurityUtils.getTenantId();

        List<TalentTrackingLinkedinGroupMember> existedMemberList = talentTrackingLinkedinGroupMemberRepository.findAllByGroupIdInAndTalentLinkedinIdIn(groupIds, members.stream().map(TalentTrackingGroupMemberDTO::getTalentLinkedinId).toList());
        Map<Long, Set<String>> existedMemberMap = existedMemberList.stream()
                .collect(Collectors.groupingBy(
                        TalentTrackingLinkedinGroupMember::getGroupId,
                        Collectors.mapping(TalentTrackingLinkedinGroupMember::getTalentLinkedinId, Collectors.toSet())
                ));

        List<TalentTrackingLinkedinGroupMember> addMemberList = new ArrayList<>();
        groupIds.forEach(groupId -> {
            Set<String> itemExistedMembers = existedMemberMap.getOrDefault(groupId, new HashSet<>());
            addMemberList.addAll(members.stream().filter(item -> !itemExistedMembers.contains(item.getTalentLinkedinId())).map(o -> TalentTrackingLinkedinGroupMember.fromTalentTrackingGroupMemberDTO(o, tenantId, groupId, GroupMemberStatus.WAITING)).toList());
        });
        talentTrackingLinkedinGroupMemberRepository.saveAll(addMemberList);
    }

    private List<TalentTrackingGroupMemberDTO> convert2TalentTrackingGroupMemberDTO(List<TalentTrackingLinkedinPending> realPendingList) {
        if (CollUtil.isEmpty(realPendingList)) {
            return new ArrayList<>();
        }
        return realPendingList.stream().map(p -> {
            TalentTrackingGroupMemberDTO talentTrackingGroupMemberDTO = new TalentTrackingGroupMemberDTO();
            talentTrackingGroupMemberDTO.setFirstName(p.getFirstName());
            talentTrackingGroupMemberDTO.setLastName(p.getLastName());
            talentTrackingGroupMemberDTO.setLinkUrl(p.getLinkUrl());
            talentTrackingGroupMemberDTO.setTitle(p.getTitle());
            talentTrackingGroupMemberDTO.setPhotoUrl(p.getPhotoUrl());
            talentTrackingGroupMemberDTO.setTalentLinkedinId(p.getTalentLinkedinId());
            return talentTrackingGroupMemberDTO;
        }).collect(Collectors.toMap(
                        TalentTrackingGroupMemberDTO::getTalentLinkedinId,
                        dto -> dto,
                        (existing, replacement) -> existing // 保留第一个，忽略重复的
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TalentTrackingVO> retractSentTalentTracking(TalentTrackingManageDTO talentTrackingManageDTO) {
        List<TalentTrackingLinkedinPending> talentTrackingLinkedinPendingList = queryTalentTrackingLinkedinPendingByTalentLinkedinIds(talentTrackingManageDTO.getTalentLinkedinIds(), talentTrackingManageDTO.getOperatorLinkedinId());
        talentTrackingLinkedinPendingList.forEach(item -> {
            item.setStatus(TrackingStatus.NOT_SENT);
            item.setLastInteractionTime(Instant.now());
        });
        talentTrackingLinkedinPendingRepository.saveAll(talentTrackingLinkedinPendingList);
        return talentTrackingLinkedinPendingList.stream().map(TalentTrackingLinkedinPending::toTalentTrackingVO).toList();
    }

    @Override
    public Page<TalentTrackingLinkedinMessageTemplate> searchTalentTrackingTemplate(TalentTrackingTemplateSearchDTO talentTrackingTemplateSearchD, Pageable pageable) {
        return talentTrackingLinkedinMessageTemplateRepository.findAll(queryAllTalentTrackingLinkedinMessageTemplate(SecurityUtils.getTenantId(), talentTrackingTemplateSearchD.getCategory(), talentTrackingTemplateSearchD.getStatus(), SecurityUtils.getUserId()), pageable);
    }

    @Override
    public TalentTrackingTemplateVO saveTalentTrackingTemplate(TalentTrackingTemplateDTO talentTrackingTemplateDTO) {
        TalentTrackingLinkedinMessageTemplate talentTrackingLinkedinMessageTemplate = TalentTrackingLinkedinMessageTemplate.fromTalentTrackingTemplateDTO(talentTrackingTemplateDTO);
        talentTrackingLinkedinMessageTemplate.setStatus(TrackingTemplateStatus.ACTIVE);
        talentTrackingLinkedinMessageTemplate.setTenantId(SecurityUtils.getTenantId());
        talentTrackingLinkedinMessageTemplate = talentTrackingLinkedinMessageTemplateRepository.save(talentTrackingLinkedinMessageTemplate);
        return TalentTrackingLinkedinMessageTemplate.toTalentTrackingTemplateVO(talentTrackingLinkedinMessageTemplate);
    }

    @Override
    public TalentTrackingTemplateVO updateTalentTrackingTemplate(Long id, TalentTrackingTemplateDTO talentTrackingTemplateDTO) {
        TalentTrackingLinkedinMessageTemplate talentTrackingLinkedinMessageTemplate = talentTrackingLinkedinMessageTemplateRepository.findById(id).orElseThrow(() -> new NotFoundException("template does not exist."));
        if (!talentTrackingLinkedinMessageTemplate.getTenantId().equals(SecurityUtils.getTenantId()) || !talentTrackingLinkedinMessageTemplate.getPermissionUserId().equals(SecurityUtils.getUserId())) {
            throw new ForbiddenException("template does not exist.");
        }
        ServiceUtils.myCopyProperties(talentTrackingTemplateDTO, talentTrackingLinkedinMessageTemplate);
        talentTrackingLinkedinMessageTemplate = talentTrackingLinkedinMessageTemplateRepository.save(talentTrackingLinkedinMessageTemplate);
        return TalentTrackingLinkedinMessageTemplate.toTalentTrackingTemplateVO(talentTrackingLinkedinMessageTemplate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inactiveTalentTrackingTemplate(TalentTrackingManageDTO talentTrackingManageDTO) {
        if (CollUtil.isEmpty(talentTrackingManageDTO.getIds())) {
            return;
        }
        List<TalentTrackingLinkedinMessageTemplate> talentTrackingLinkedinMessageTemplateList = talentTrackingLinkedinMessageTemplateRepository.findAllByIdInAndTenantIdAndStatusAndPermissionUserId(talentTrackingManageDTO.getIds(), SecurityUtils.getTenantId(), TrackingTemplateStatus.ACTIVE, SecurityUtils.getUserId());
        talentTrackingLinkedinMessageTemplateList.forEach(item -> item.setStatus(TrackingTemplateStatus.IN_ACTIVE));
        talentTrackingLinkedinMessageTemplateRepository.saveAll(talentTrackingLinkedinMessageTemplateList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentTrackingGroupVO saveGroup(TalentTrackingGroupDTO talentTrackingGroupDTO) {
        checkDuplicateGroup(talentTrackingGroupDTO.getName(), talentTrackingGroupDTO.getOperatorLinkedinId(), null);
        TalentTrackingLinkedinGroup talentTrackingLinkedinGroup = TalentTrackingLinkedinGroup.fromTalentTrackingGroupDTO(talentTrackingGroupDTO);
        talentTrackingLinkedinGroup.setStatus(TrackingGroupStatus.ACTIVE);
        talentTrackingLinkedinGroup.setTenantId(SecurityUtils.getTenantId());
        talentTrackingLinkedinGroup = talentTrackingLinkedinGroupRepository.save(talentTrackingLinkedinGroup);
        int memberCount = saveGroupMember(talentTrackingGroupDTO.getMembers(), SecurityUtils.getTenantId(), talentTrackingLinkedinGroup.getId());

        TalentTrackingGroupVO talentTrackingGroupVO = TalentTrackingLinkedinGroup.toTalentTrackingGroupVO(talentTrackingLinkedinGroup);
        talentTrackingGroupVO.setMemberCount(memberCount);
        return talentTrackingGroupVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentTrackingGroupVO updateGroup(Long id, TalentTrackingGroupDTO talentTrackingGroupDTO) {
        TalentTrackingLinkedinGroup talentTrackingLinkedinGroup = talentTrackingLinkedinGroupRepository.findById(id).orElseThrow(() -> new NotFoundException("This member group does not exist."));
        if (!talentTrackingLinkedinGroup.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("This member group does not exist.");
        }
        checkDuplicateGroup(talentTrackingGroupDTO.getName(), talentTrackingLinkedinGroup.getOperatorLinkedinId(), id);
        talentTrackingLinkedinGroup.setName(talentTrackingGroupDTO.getName());
        talentTrackingLinkedinGroupRepository.save(talentTrackingLinkedinGroup);
        return toTalentTrackingGroupVO(talentTrackingLinkedinGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void inactiveGroup(Long id) {
        TalentTrackingLinkedinGroup talentTrackingLinkedinGroup = talentTrackingLinkedinGroupRepository.findById(id).orElseThrow(() -> new NotFoundException("This member group does not exist."));
        if (!talentTrackingLinkedinGroup.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("This member group does not exist.");
        }

        talentTrackingLinkedinGroup.setStatus(TrackingGroupStatus.IN_ACTIVE);
        talentTrackingLinkedinGroupRepository.save(talentTrackingLinkedinGroup);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveGroupMember(TalentTrackingGroupManageDTO talentTrackingGroupManageDTO) {
        List<Long> groupIds = talentTrackingGroupManageDTO.getGroupIds();
        List<TalentTrackingGroupMemberDTO> members = talentTrackingGroupManageDTO.getMembers();
        Long tenantId = SecurityUtils.getTenantId();

        List<TalentTrackingLinkedinGroupMember> existedAllMemberList = talentTrackingLinkedinGroupMemberRepository.findAllByGroupIdInAndTalentLinkedinIdIn(groupIds, members.stream().map(TalentTrackingGroupMemberDTO::getTalentLinkedinId).toList());
        //如果是waiting状态直接删除，后面在添加normal状态的
        List<TalentTrackingLinkedinGroupMember> waiting = existedAllMemberList.stream().filter(p -> GroupMemberStatus.WAITING.equals(p.getStatus())).toList();

        List<TalentTrackingLinkedinGroupMember> existedMemberList = existedAllMemberList.stream().filter(p -> GroupMemberStatus.NORMAL.equals(p.getStatus())).toList();
        Map<Long, Set<String>> existedMemberMap = existedMemberList.stream()
                .collect(Collectors.groupingBy(
                        TalentTrackingLinkedinGroupMember::getGroupId,
                        Collectors.mapping(TalentTrackingLinkedinGroupMember::getTalentLinkedinId, Collectors.toSet())
                ));

        List<TalentTrackingLinkedinGroupMember> addMemberList = new ArrayList<>();
        groupIds.forEach(groupId -> {
            Set<String> itemExistedMembers = existedMemberMap.getOrDefault(groupId, new HashSet<>());
            addMemberList.addAll(members.stream().filter(item -> !itemExistedMembers.contains(item.getTalentLinkedinId())).map(o -> TalentTrackingLinkedinGroupMember.fromTalentTrackingGroupMemberDTO(o, tenantId, groupId)).toList());
        });
        talentTrackingLinkedinGroupMemberRepository.deleteAll(waiting);
        talentTrackingLinkedinGroupMemberRepository.saveAll(addMemberList);
    }

    @Override
    public Page<TalentTrackingLinkedinGroup> searchGroup(String operatorLinkedinId, Pageable pageable) {
        return talentTrackingLinkedinGroupRepository.findAllByTenantIdAndOperatorLinkedinIdAndStatus(SecurityUtils.getTenantId(), operatorLinkedinId, TrackingGroupStatus.ACTIVE, pageable);
    }

    @Override
    public List<TalentTrackingGroupVO> toTalentTrackingGroupVO(List<TalentTrackingLinkedinGroup> talentTrackingLinkedinGroupList) {
        List<TalentTrackingGroupVO> talentTrackingGroupVOList = talentTrackingLinkedinGroupList.stream().map(TalentTrackingLinkedinGroup::toTalentTrackingGroupVO).toList();
        List<TalentTrackingLinkedinGroupMember> talentTrackingLinkedinGroupMemberList = talentTrackingLinkedinGroupMemberRepository.findAllByGroupIdInAndStatus(talentTrackingLinkedinGroupList.stream().map(TalentTrackingLinkedinGroup::getId).distinct().toList(), GroupMemberStatus.NORMAL);
        List<TalentTrackingVO> talentTrackingVOList = talentTrackingLinkedinGroupMemberList.stream().map(TalentTrackingLinkedinGroupMember::toTalentTrackingVO).toList();
        Map<Long, List<TalentTrackingVO>> talentTrackingVOMap = talentTrackingVOList.stream().collect(Collectors.groupingBy(TalentTrackingVO::getGroupId));
        talentTrackingGroupVOList.forEach(item -> {
            List<TalentTrackingVO> itemList = talentTrackingVOMap.getOrDefault(item.getId(), new ArrayList<>());
            item.setMembers(itemList);
            item.setMemberCount(itemList.size());
        });
        return talentTrackingGroupVOList;
    }

    @Override
    public List<TalentTrackingVO> queryGroupMember(Long id) {
        TalentTrackingLinkedinGroup talentTrackingLinkedinGroup = talentTrackingLinkedinGroupRepository.findById(id).orElseThrow(() -> new NotFoundException("This member group does not exist."));
        if (!talentTrackingLinkedinGroup.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("This member group does not exist.");
        }
        List<TalentTrackingLinkedinGroupMember> trackingLinkedinGroupMemberList = talentTrackingLinkedinGroupMemberRepository.findAllByGroupIdAndStatus(id, GroupMemberStatus.NORMAL);
        return trackingLinkedinGroupMemberList.stream().map(TalentTrackingLinkedinGroupMember::toTalentTrackingVO).toList();
    }

    @Override
    public List<TalentTrackingGroupVO> queryGroupByOperatorLinkedinId(String operatorLinkedinId) {
        List<TalentTrackingLinkedinGroup> talentTrackingLinkedinGroupList = talentTrackingLinkedinGroupRepository.findAllByTenantIdAndOperatorLinkedinIdAndStatusOrderByCreatedDateDesc(SecurityUtils.getTenantId(), operatorLinkedinId, TrackingGroupStatus.ACTIVE);
        return talentTrackingLinkedinGroupList.stream().map(TalentTrackingLinkedinGroup::toTalentTrackingGroupVO).toList();
    }

    @Override
    public void setGroupList(List<TalentTrackingVO> talentTrackingVOList) {
        if (CollUtil.isEmpty(talentTrackingVOList)) {
            return;
        }
        
        // 提取所有待加好友的ID
        List<Long> pendingIds = talentTrackingVOList.stream()
                .map(TalentTrackingVO::getId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        
        if (CollUtil.isEmpty(pendingIds)) {
            return;
        }
        
        // 查询所有相关的分组成员关系
        List<TalentTrackingLinkedinPendingGroupMember> groupMembers =
                talentTrackingLinkedinPendingGroupMemberRepository.findByPendingIdIn(pendingIds);
        
        if (CollUtil.isEmpty(groupMembers)) {
            // 如果没有分组关系，设置空列表
            talentTrackingVOList.forEach(vo -> vo.setGroupList(new ArrayList<>()));
            return;
        }
        
        // 提取所有分组ID
        List<Long> groupIds = groupMembers.stream()
                .map(TalentTrackingLinkedinPendingGroupMember::getGroupId)
                .distinct()
                .collect(Collectors.toList());
        
        // 查询所有相关的分组信息
        List<TalentTrackingLinkedinPendingGroup> groups =
                talentTrackingLinkedinPendingGroupRepository.findAllById(groupIds);
        
        // 构建分组ID到分组信息的映射
        Map<Long, TalentTrackingLinkedinPendingGroup> groupMap = groups.stream()
                .collect(Collectors.toMap(TalentTrackingLinkedinPendingGroup::getId, group -> group));
        
        // 构建待加好友ID到分组列表的映射
        Map<Long, List<GroupInfo>> pendingGroupMap = groupMembers.stream()
                .filter(member -> groupMap.containsKey(member.getGroupId()))
                .collect(Collectors.groupingBy(
                        TalentTrackingLinkedinPendingGroupMember::getPendingId,
                        Collectors.mapping(
                                member -> {
                                    TalentTrackingLinkedinPendingGroup group = groupMap.get(member.getGroupId());
                                    return new GroupInfo(group.getId(), group.getName());
                                },
                                Collectors.toList()
                        )
                ));
        
        // 为每个TalentTrackingVO设置分组列表
        talentTrackingVOList.forEach(vo -> {
            List<GroupInfo> groupList = pendingGroupMap.getOrDefault(vo.getId(), new ArrayList<>());
            vo.setGroupList(groupList);
        });
    }

    private TalentTrackingGroupVO toTalentTrackingGroupVO(TalentTrackingLinkedinGroup talentTrackingLinkedinGroup) {
        TalentTrackingGroupVO talentTrackingGroupVO = TalentTrackingLinkedinGroup.toTalentTrackingGroupVO(talentTrackingLinkedinGroup);
        int memberCount = talentTrackingLinkedinGroupMemberRepository.countDistinctByGroupIdAndStatus(talentTrackingGroupVO.getId(), GroupMemberStatus.NORMAL);
        talentTrackingGroupVO.setMemberCount(memberCount);
        return talentTrackingGroupVO;
    }

    private int saveGroupMember(List<TalentTrackingGroupMemberDTO> talentTrackingGroupMemberDTOList, Long tenantId, Long groupId) {
        if (CollUtil.isEmpty(talentTrackingGroupMemberDTOList)) {
            return 0;
        }

        List<TalentTrackingLinkedinGroupMember> talentTrackingLinkedinGroupMemberList = talentTrackingGroupMemberDTOList.stream().map(o -> TalentTrackingLinkedinGroupMember.fromTalentTrackingGroupMemberDTO(o, tenantId, groupId)).toList();
        talentTrackingLinkedinGroupMemberRepository.saveAll(talentTrackingLinkedinGroupMemberList);
        return talentTrackingLinkedinGroupMemberList.size();
    }

    //领英人员分组查重
    private void checkDuplicateGroup(String name, String operatorId, Long id) {
        // 查询状态为 active 的成员分组，检查该用户是否有重名的成员分组
        List<TalentTrackingLinkedinGroup> talentTrackingLinkedinGroupList = talentTrackingLinkedinGroupRepository
                .findAllByTenantIdAndOperatorLinkedinIdAndStatusAndName(SecurityUtils.getTenantId(), operatorId, TrackingGroupStatus.ACTIVE, name);

        if (CollUtil.isNotEmpty(talentTrackingLinkedinGroupList) && talentTrackingLinkedinGroupList.stream()
                .noneMatch(announcement -> Objects.equals(announcement.getId(), id))) {
            throw new CustomParameterizedException("This member group already exists.");
        }
    }

    private List<TalentTrackingLinkedinPending> queryTalentTrackingLinkedinPending(List<Long> ids, String operatorLinkedinId) {
        if (CollUtil.isEmpty(ids) || ObjectUtil.isEmpty(operatorLinkedinId)) {
            return new ArrayList<>();
        }
        return talentTrackingLinkedinPendingRepository.findAllByIdInAndTenantIdAndStatusInAndOperatorLinkedinId(ids, SecurityUtils.getTenantId(), ACTIVE_TRACKING_STATUS, operatorLinkedinId);
    }

    private List<TalentTrackingLinkedinPending> queryTalentTrackingLinkedinPendingByTalentLinkedinIds(List<String> talentLinkedinIds, String operatorLinkedinId) {
        if (CollUtil.isEmpty(talentLinkedinIds) || ObjectUtil.isEmpty(operatorLinkedinId)) {
            return new ArrayList<>();
        }
        return talentTrackingLinkedinPendingRepository.findAllByTenantIdAndStatusInAndOperatorLinkedinIdAndTalentLinkedinIdIn(SecurityUtils.getTenantId(), ACTIVE_TRACKING_STATUS, operatorLinkedinId, talentLinkedinIds);
    }

    private Specification<TalentTrackingLinkedinPending> queryAllTalentTrackingLinkedinPending(Long tenantId, TrackingCategory category, TrackingStatus status, String operatorLinkedinId) {
        Specification<TalentTrackingLinkedinPending> specification = new Specification<TalentTrackingLinkedinPending>() {
            @Override
            public Predicate toPredicate(Root<TalentTrackingLinkedinPending> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                Predicate predicate = criteriaBuilder.conjunction();
                predicate.getExpressions().add(criteriaBuilder.equal(root.get("tenantId"), tenantId));
                if (ObjectUtil.isNotEmpty(category)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("category"), category.toDbValue()));
                }
                if (ObjectUtil.isNotEmpty(status)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("status"), status.toDbValue()));
                } else {
                    //默认查未发送的待加人脉数据
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("status"), TrackingStatus.NOT_SENT.toDbValue()));
                }
                if (ObjectUtil.isNotEmpty(operatorLinkedinId)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("operatorLinkedinId"), operatorLinkedinId));
                }
                return predicate;
            }
        };
        return specification;
    }

    private Specification<TalentTrackingLinkedinMessageTemplate> queryAllTalentTrackingLinkedinMessageTemplate(Long tenantId, TrackingTemplateCategory category, TrackingTemplateStatus status, Long operatorId) {
        Specification<TalentTrackingLinkedinMessageTemplate> specification = new Specification<TalentTrackingLinkedinMessageTemplate>() {
            @Override
            public Predicate toPredicate(Root<TalentTrackingLinkedinMessageTemplate> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                Predicate predicate = criteriaBuilder.conjunction();
                predicate.getExpressions().add(criteriaBuilder.equal(root.get("tenantId"), tenantId));
                if (ObjectUtil.isNotEmpty(category)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("category"), category.toDbValue()));
                }
                if (ObjectUtil.isNotEmpty(status)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("status"), status.toDbValue()));
                } else {
                    //默认查活跃的
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("status"), TrackingTemplateStatus.ACTIVE.toDbValue()));
                }
                if (ObjectUtil.isNotEmpty(operatorId)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("permissionUserId"), operatorId));
                }
                return predicate;
            }
        };
        return specification;
    }

    @Override
    public List<TalentTrackingGroupInfoVO> queryGroupInfo(TalentTrackingGroupInfoSearchDTO searchDTO) {
        Long tenantId = SecurityUtils.getTenantId();
        String operatorLinkedinId = searchDTO.getOperatorLinkedinId();
        List<String> linkedinIdList = searchDTO.getLinkedinIdList();

        // 一次性查询该操作者的所有活跃分组及其成员信息
        List<TalentTrackingLinkedinGroup> allGroups = talentTrackingLinkedinGroupRepository
                .findAllByTenantIdAndOperatorLinkedinIdAndStatusOrderByCreatedDateDesc(tenantId, operatorLinkedinId, TrackingGroupStatus.ACTIVE);

        if (CollUtil.isEmpty(allGroups)) {
            return new ArrayList<>();
        }

        List<Long> allGroupIds = allGroups.stream().map(TalentTrackingLinkedinGroup::getId).collect(Collectors.toList());
        
        // 一次性查询所有分组的成员
        List<TalentTrackingLinkedinGroupMember> allMembers = talentTrackingLinkedinGroupMemberRepository
                .findAllByGroupIdInAndStatus(allGroupIds, GroupMemberStatus.NORMAL);

        if (CollUtil.isEmpty(allMembers)) {
            return new ArrayList<>();
        }

        // 如果没有传入linkedinIdList，使用所有成员的领英ID
        if (CollUtil.isEmpty(linkedinIdList)) {
            linkedinIdList = allMembers.stream()
                    .map(TalentTrackingLinkedinGroupMember::getTalentLinkedinId)
                    .distinct()
                    .collect(Collectors.toList());
        }

        if (CollUtil.isEmpty(linkedinIdList)) {
            return new ArrayList<>();
        }

        // 构建分组ID到分组信息的映射
        Map<Long, TalentTrackingLinkedinGroup> groupMap = allGroups.stream()
                .collect(Collectors.toMap(TalentTrackingLinkedinGroup::getId, group -> group));

        List<String> needReturnLinkedinIdList = linkedinIdList;
        // 过滤出指定领英ID的成员，并按领英ID分组
        Map<String, List<TalentTrackingLinkedinGroupMember>> membersByLinkedinId = allMembers.stream()
                .filter(member -> needReturnLinkedinIdList.contains(member.getTalentLinkedinId()))
                .filter(member -> groupMap.containsKey(member.getGroupId()))
                .collect(Collectors.groupingBy(TalentTrackingLinkedinGroupMember::getTalentLinkedinId));

        // 构建返回结果
        return linkedinIdList.stream()
                .map(linkedinId -> {
                    TalentTrackingGroupInfoVO vo = new TalentTrackingGroupInfoVO();
                    vo.setLinkedinId(linkedinId);

                    List<GroupInfo> groupList = membersByLinkedinId.getOrDefault(linkedinId, new ArrayList<>())
                            .stream()
                            .map(member -> {
                                TalentTrackingLinkedinGroup group = groupMap.get(member.getGroupId());
                                return new GroupInfo(group.getId(), group.getName());
                            })
                            .collect(Collectors.toList());

                    vo.setGroupList(groupList);
                    return vo;
                })
                .collect(Collectors.toList());
    }
}
