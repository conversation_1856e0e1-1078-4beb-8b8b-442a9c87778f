package com.altomni.apn.talent.service.folder.impl;


import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.folder.*;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.common.enumeration.folder.FolderType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.sql.QueryProcessService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.folder.TalentFolderSharingTeam;
import com.altomni.apn.talent.domain.vm.folder.SearchTalentFolderVM;
import com.altomni.apn.talent.repository.folder.TalentFolderSearchPageRepositoryCustom;
import com.altomni.apn.talent.repository.folder.TalentFolderSharingTeamRepository;
import com.altomni.apn.talent.repository.folder.TalentFolderSharingUserRepository;
import com.altomni.apn.talent.service.folder.TalentCustomFolderListPageService;
import com.altomni.apn.talent.service.mapper.folder.SearchTalentFolderMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TalentCustomFolderListPageServiceImpl implements TalentCustomFolderListPageService {

    private final static String NAME_COLUMN = "f.name";

    private final static String NOTE_COLUMN = "f.folder_note";
    private final static String CREATED_DATE_COLUMN = "f.created_date";
    private static final String LAST_MODIFIED_DATE_COLUMN = "f.last_modified_date";

    private static final String CREATOR = "f.puser_id";

    @Resource
    private SearchTalentFolderMapper searchTalentFolderMapper;

    @Resource
    private TalentFolderSharingUserRepository talentFolderSharingUserRepository;

    @Resource
    private TalentFolderSharingTeamRepository talentFolderSharingTeamRepository;

    @Resource
    QueryProcessService queryProcessService;

    @Resource
    private TalentFolderSearchPageRepositoryCustom talentFolderSearchPageRepositoryCustom;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Override
    public Page<ListPageFolderDTO> searchTalentFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable) {

        Page<SearchTalentFolderVM> folderVMPage = getTalentFolders(searchRequestDTO, pageable);
        List<ListPageFolderDTO> folderDTOList = toFolderDTO(folderVMPage.getContent(), searchRequestDTO.getFolderType());

        return new PageImpl<>(folderDTOList, pageable, folderVMPage.getTotalElements());
    }

    private List<ListPageFolderDTO> toFolderDTO(List<SearchTalentFolderVM> searchTalentFolderVMList, FolderType folderType) {
        List<ListPageFolderDTO> folderDTOList = searchTalentFolderMapper.toDto(searchTalentFolderVMList);
        List<Long> folderIds = folderDTOList.stream().map(ListPageFolderDTO::getId).collect(Collectors.toList());
        if (folderIds.size() == 0) {
            return folderDTOList;
        }

        List<FolderSharedUserDTO> userDTOList = talentFolderSharingUserRepository.findTalentFolderSharingUserByTalentFolderIdIn(folderIds);
        List<FolderSharedTeamDTO> teamDTOList = talentFolderSharingTeamRepository.findTalentFolderSharedTeamByTalentFolderIdIn(folderIds);
        Map<Long, List<FolderSharedUserDTO>> userDTOMap = userDTOList.stream()
                .map(userDTO -> {
                    userDTO.setFullName(CommonUtils.formatFullName(userDTO.getFirstName(), userDTO.getLastName()));
                    return userDTO;
                })
                .collect(Collectors.groupingBy(FolderSharedUserDTO::getFolderId));

        Map<Long, List<FolderSharedTeamDTO>> teamDTOMap = teamDTOList.stream()
                .collect(Collectors.groupingBy(FolderSharedTeamDTO::getFolderId));

        // set the shareTo info
        folderDTOList.forEach(folderDTO -> {
            StringJoiner sharedTo = new StringJoiner(",");
            if (userDTOMap.containsKey(folderDTO.getId())) {
                folderDTO.setSharedUsers(userDTOMap.get(folderDTO.getId()).stream().map(o -> new FolderSharedTargetBriefDTO(o.getUserId(), (folderType.equals(FolderType.CUSTOMIZED) ? o.getFolderPermission() : null), o.getFullName())).collect(Collectors.toList()));
                userDTOMap.get(folderDTO.getId()).forEach(item -> sharedTo.add(item.getFullName()));
            }
            if (teamDTOMap.containsKey(folderDTO.getId())) {
                folderDTO.setSharedTeams(teamDTOMap.get(folderDTO.getId()).stream().map(o -> new FolderSharedTargetBriefDTO(o.getTeamId(), (folderType.equals(FolderType.CUSTOMIZED) ? o.getFolderPermission() : null), o.getTeamName())).collect(Collectors.toList()));
                teamDTOMap.get(folderDTO.getId()).forEach(item -> sharedTo.add(item.getTeamName()));
            }
        });

        //set the permission for each shared folder when request is for shared
        if (FolderType.SHARED.equals(folderType)) {
            Map<Long, FolderPermission> folderUserPermissionMap = userDTOList.stream().filter(o -> o.getUserId().equals(SecurityUtils.getUserId())).collect(Collectors.toMap(FolderSharedUserDTO::getFolderId, FolderSharedUserDTO::getFolderPermission));

            for (ListPageFolderDTO folder : folderDTOList) {
                if (folderUserPermissionMap.containsKey(folder.getId())) {
                    folder.setFolderPermission(folderUserPermissionMap.get(folder.getId()));
                }
            }

            //set permission from the team permission
            List<TalentFolderSharingTeam> talentFolderSharingTeamList = talentFolderSharingTeamRepository.findTalentFolderSharedTeamByTalentFolderIdInAndUserId(
                    teamDTOList.stream().map(FolderSharedTeamDTO::getFolderId).collect(Collectors.toList()),
                    SecurityUtils.getUserId()
            );

            Map<Long, FolderPermission> folderTeamPermissionMap = talentFolderSharingTeamList.stream()
                    .collect(Collectors.toMap(
                            TalentFolderSharingTeam::getTalentFolderId,
                            TalentFolderSharingTeam::getPermission,
                            FolderPermission::combine
                    ));

            //overwrite the permission if the team permission on user is over user permission
            for (ListPageFolderDTO folder : folderDTOList) {
                if (folderTeamPermissionMap.containsKey(folder.getId())) {
                    folder.setFolderPermission(FolderPermission.combine(
                            folder.getFolderPermission(),
                            folderTeamPermissionMap.get(folder.getId())
                    ));
                }
            }
        }
        return folderDTOList;
    }

    private Page<SearchTalentFolderVM> getTalentFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable) {
        log.info("[APN get folder list by search request userId:{}", SecurityUtils.getUserId());
        Map<Integer, Object> paramsMap = new HashMap<>(16);
        StringBuffer countSql = new StringBuffer();
        StringBuffer dataSql = new StringBuffer();
        setSearchFolderSql(searchRequestDTO, pageable, paramsMap, countSql, dataSql);
        Long total = talentFolderSearchPageRepositoryCustom.searchCount(countSql.toString(), paramsMap);
        List<SearchTalentFolderVM> customFolderList = talentFolderSearchPageRepositoryCustom.searchData(dataSql.toString(), SearchTalentFolderVM.class, paramsMap);
        return new PageImpl<>(customFolderList, pageable, total);
    }


    private void setSearchFolderSql(FolderSearchRequestDTO searchRequestDTO, Pageable pageable, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (searchRequestDTO.getFolderType().equals(FolderType.CUSTOMIZED)) {
            setSearchCustomFolderSQL(countSql, dataSql);
        } else if (searchRequestDTO.getFolderType().equals(FolderType.SHARED)) {
            setSearchSharedFolderSQL(countSql, dataSql);
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_SETSEARCHFOLDERSQL_TYPEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }

        setFolderSearchParam(searchRequestDTO, paramsMap, countSql, dataSql);
        setFolderPageable(pageable, dataSql);
    }

    //TODO: move to repository
    private void setSearchCustomFolderSQL(StringBuffer countSql, StringBuffer dataSql){
        countSql.append("SELECT COUNT(f.id) FROM talent_folder f WHERE f.puser_id = ").append(SecurityUtils.getUserId());
        dataSql.append("SELECT f.id, f.name, f.folder_note, f.tenant_id, f.created_by, f.created_date, f.last_modified_by, f.pteam_id, f.puser_id, CASE WHEN CONCAT(u.first_name, ' ', u.last_name) REGEXP '[A-Za-z]' THEN CONCAT(u.first_name, ' ', u.last_name)\n" +
                "       ELSE CONCAT(u.last_name, u.first_name)\n" +
                "  END AS creator ,\n" +
                "       GREATEST(f.last_modified_date,\n" +
                "           COALESCE((SELECT MAX(tfsu.last_modified_date) FROM talent_folder_sharing_user tfsu WHERE tfsu.talent_folder_id = f.id), '1970-01-01'),\n" +
                "           COALESCE((SELECT MAX(tfst.last_modified_date) FROM talent_folder_sharing_team tfst WHERE tfst.talent_folder_id = f.id), '1970-01-01')\n" +
                "                ) AS last_modified_date FROM talent_folder f INNER JOIN `user` u ON u.id = f.puser_id WHERE f.puser_id = ").append(SecurityUtils.getUserId());

    }

    private void setSearchSharedFolderSQL(StringBuffer countSql, StringBuffer dataSql){
        countSql.append("SELECT COUNT(f.id) \n" +
                "	FROM talent_folder f \n" +
                "	WHERE f.puser_id <> ").append(SecurityUtils.getUserId()).append("\n" +
                "   AND (EXISTS ( \n" +
                "			SELECT 1 \n" +
                "			FROM talent_folder_sharing_user su \n" +
                "			WHERE f.id = su.talent_folder_id AND su.user_id = ").append(SecurityUtils.getUserId()).append("\n" +
                "	) OR EXISTS ( \n" +
                "			SELECT 1 \n" +
                "			FROM talent_folder_sharing_team st \n" +
                "			INNER JOIN permission_user_team ptu ON st.team_id = ptu.team_id \n" +
                "			WHERE f.id = st.talent_folder_id AND ptu.user_id = ").append(SecurityUtils.getUserId()).append("\n" +
                "               AND (st.excluded_user_ids IS NULL OR JSON_CONTAINS(st.excluded_user_ids, CAST(").append(SecurityUtils.getUserId()).append(" AS json)) = 0 ) \n" +
                "	))");

        dataSql.append("SELECT f.id, f.name, f.folder_note, f.tenant_id, f.created_by, f.created_date, f.last_modified_by, f.pteam_id, f.puser_id \n" +
                "	, CONCAT(u.first_name,' ',last_name) creator,\n" +
                "       GREATEST(f.last_modified_date,\n" +
                "          COALESCE((SELECT MAX(tfsu.last_modified_date) FROM talent_folder_sharing_user tfsu WHERE tfsu.talent_folder_id = f.id), '1970-01-01'),\n" +
                "          COALESCE((SELECT MAX(tfst.last_modified_date) FROM talent_folder_sharing_team tfst WHERE tfst.talent_folder_id = f.id), '1970-01-01')\n" +
                "                ) AS last_modified_date  FROM talent_folder f INNER JOIN `user` u ON u.id = f.puser_id \n" +
                "	WHERE f.puser_id <> ").append(SecurityUtils.getUserId()).append("\n" +
                " AND (EXISTS ( \n" +
                "			SELECT 1 \n" +
                "			FROM talent_folder_sharing_user su \n" +
                "			WHERE f.id = su.talent_folder_id AND su.user_id = ").append(SecurityUtils.getUserId()).append("\n" +
                "	) OR EXISTS ( \n" +
                "			SELECT 1 \n" +
                "			FROM talent_folder_sharing_team st \n" +
                "			INNER JOIN permission_user_team ptu ON st.team_id = ptu.team_id \n" +
                "			WHERE f.id = st.talent_folder_id AND ptu.user_id = ").append(SecurityUtils.getUserId()).append(" AND (st.excluded_user_ids IS NULL OR JSON_CONTAINS(st.excluded_user_ids, CAST(").append(SecurityUtils.getUserId()).append(" AS json)) = 0 ) \n" +
                "	))");
    }


    //TODO: use general method;
    private void setFolderSearchParam(FolderSearchRequestDTO searchRequestDTO, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {

        if (searchRequestDTO.getCreatedDate() != null) {
            queryProcessService.setDateBetweenParamFilter(paramsMap, countSql, dataSql, CREATED_DATE_COLUMN, searchRequestDTO.getCreatedDate().getDateFrom(), searchRequestDTO.getCreatedDate().getDateTo());
        }

        if (searchRequestDTO.getLastModifiedDate() != null) {
            queryProcessService.setDateBetweenParamFilter(paramsMap, countSql, dataSql, LAST_MODIFIED_DATE_COLUMN, searchRequestDTO.getLastModifiedDate().getDateFrom(), searchRequestDTO.getLastModifiedDate().getDateTo());
        }

//        Instant createdDateFrom = searchRequestDTO.getCreatedDateFrom();
//        Instant createdDateTo = searchRequestDTO.getCreatedDateTo();
//        if (createdDateFrom != null) {
//            countSql.append(" AND f.created_date > ?").append(paramsMap.size() + 1);
//            dataSql.append(" AND f.created_date > ?").append(paramsMap.size() + 1);
//            paramsMap.put(paramsMap.size() + 1, createdDateFrom);
//        }
//        if (createdDateTo != null) {
//            countSql.append(" AND f.created_date < ?").append(paramsMap.size() + 1);
//            dataSql.append(" AND f.created_date < ?").append(paramsMap.size() + 1);
//            paramsMap.put(paramsMap.size() + 1, createdDateTo);
//        }
//
//        Instant lastModifiedDateFrom = searchRequestDTO.getLastModifiedDateFrom();
//        Instant lastModifiedDateTo = searchRequestDTO.getLastModifiedDateTo();
//        if (lastModifiedDateFrom != null) {
//            countSql.append(" AND f.last_modified_date > ?").append(paramsMap.size() + 1);
//            dataSql.append(" AND f.last_modified_date > ?").append(paramsMap.size() + 1);
//            paramsMap.put(paramsMap.size() + 1, lastModifiedDateFrom);
//        }
//        if (lastModifiedDateTo != null) {
//            countSql.append(" AND f.last_modified_date < ?").append(paramsMap.size() + 1);
//            dataSql.append(" AND f.last_modified_date < ?").append(paramsMap.size() + 1);
//            paramsMap.put(paramsMap.size() + 1, lastModifiedDateTo);
//        }

        //folder name
        if (StringUtils.isNotBlank(searchRequestDTO.getName())) {
            queryProcessService.setColumnLikeParamFilter(paramsMap, countSql, dataSql, NAME_COLUMN, searchRequestDTO.getName());
        }

        //folder note
        if (StringUtils.isNotBlank(searchRequestDTO.getNote())) {
            queryProcessService.setColumnLikeParamFilter(paramsMap, countSql, dataSql, NOTE_COLUMN, searchRequestDTO.getNote());
        }
//        String note = searchRequestDTO.getNote();
//        if (note != null && StringUtils.isNotEmpty(note)) {
//            countSql.append(" AND f.folder_note LIKE ?").append(paramsMap.size() + 1);
//            dataSql.append(" AND f.folder_note LIKE ?").append(paramsMap.size() + 1);
//            paramsMap.put(paramsMap.size() + 1, String.format("%%%s%%", note));
//        }

        //Share User/Team query
        List<Long> sharedUsers = searchRequestDTO.getSharedUsers();
        if (sharedUsers != null && !sharedUsers.isEmpty()) {
            countSql.append(" AND EXISTS (SELECT 1 FROM talent_folder_sharing_user fu WHERE fu.talent_folder_id = f.id AND fu.user_id IN ?").append(paramsMap.size() + 1).append(")");
            dataSql.append(" AND EXISTS (SELECT 1 FROM talent_folder_sharing_user fu WHERE fu.talent_folder_id = f.id AND fu.user_id IN ?").append(paramsMap.size() + 1).append(")");
            paramsMap.put(paramsMap.size() + 1, sharedUsers);
        }

        List<Long> sharedTeams = searchRequestDTO.getSharedTeams();
        if (sharedTeams != null && !sharedTeams.isEmpty()) {
            countSql.append(" AND EXISTS (SELECT 1 FROM talent_folder_sharing_team ft WHERE ft.talent_folder_id = f.id AND ft.team_id IN ?").append(paramsMap.size() + 1).append(")");
            dataSql.append(" AND EXISTS (SELECT 1 FROM talent_folder_sharing_team ft WHERE ft.talent_folder_id = f.id AND ft.team_id IN ?").append(paramsMap.size() + 1).append(")");
            paramsMap.put(paramsMap.size() + 1, sharedTeams);
        }

        List<Long> creators = searchRequestDTO.getCreator();
        if (creators != null && !creators.isEmpty()) {
            queryProcessService.setColumnIdsInParaFilter(paramsMap, countSql, dataSql, CREATOR, creators);
        }

        List<String> generalTexts = searchRequestDTO.getGeneralText();
        if (generalTexts != null && !generalTexts.isEmpty()) {
            queryProcessService.setSingleGeneralSearchParamFilter(paramsMap, countSql, dataSql, NAME_COLUMN, generalTexts.get(0));
        }

    }

    /***
     * Search sub folder: sql search utilize
     */


    private void setFolderPageable(Pageable pageable, StringBuffer dataSql) {
        int size = pageable.getPageSize();
        int page = pageable.getPageNumber();
        int start = size * page;
        if (pageable.getSort().isSorted()) {
            dataSql.append(" ORDER BY ");
            StringJoiner joiner = new StringJoiner(", ");
            for (Sort.Order order : pageable.getSort()) {
                String column = null;
                switch (order.getProperty()) {
                    case "createdDate":
                        column = "created_date";
                        break;
                    case "lastModifiedDate":
                        column = "last_modified_date";
                        break;
                    case "creator":
                        column = "CASE WHEN creator IS NULL OR creator = '' THEN 0 ELSE 1 END DESC, CONVERT(creator USING gbk)";
                        break;
                    case "name":
                        column = "CONVERT(name USING gbk)";
                        break;
                    default:
                        column = order.getProperty();
                }
                joiner.add(column + " " + order.getDirection());
            }
            dataSql.append(joiner);
        }
        dataSql.append(" LIMIT ").append(start).append(", ").append(size);
    }
}
