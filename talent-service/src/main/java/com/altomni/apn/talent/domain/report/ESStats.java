package com.altomni.apn.talent.domain.report;



import com.altomni.apn.common.domain.AbstractAuditingEntity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A ESStats.
 */
@Entity
@Table(name = "esstats")
public class ESStats extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "search_str")
    private String searchStr;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public ESStats userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public ESStats tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getSearchStr() {
        return searchStr;
    }

    public ESStats searchStr(String searchStr) {
        this.searchStr = searchStr;
        return this;
    }

    public void setSearchStr(String searchStr) {
        this.searchStr = searchStr;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ESStats eSStats = (ESStats) o;
        if (eSStats.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), eSStats.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "ESStats{" +
            "id=" + getId() +
            ", userId='" + getUserId() + "'" +
            ", tenantId='" + getTenantId() + "'" +
            ", searchStr='" + getSearchStr() + "'" +
            "}";
    }
}
