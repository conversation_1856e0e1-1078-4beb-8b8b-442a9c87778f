package com.altomni.apn.talent.domain.folder;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.google.common.base.Objects;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.time.Instant;

@Getter
@Setter
@Entity
@Table(name = "user_daily_candidate_roll_list_config")
public class UserDailyCandidateRollListConfig extends AbstractAuditingEntity {
    @Id
    @Column(name = "id", nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_search_folder_id")
    private Long talentSearchFolderId;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "search_date")
    private Instant searchDate;

    @Column(name = "last_search_date")
    private Instant lastSearchDate;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserDailyCandidateRollListConfig that = (UserDailyCandidateRollListConfig) o;
        return Objects.equal(id, that.id) && Objects.equal(talentSearchFolderId, that.talentSearchFolderId) && Objects.equal(userId, that.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id, talentSearchFolderId, userId);
    }
}