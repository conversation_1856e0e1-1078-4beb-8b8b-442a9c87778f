package com.altomni.apn.talent.service.dto.start;


import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.user.domain.enumeration.FeeType;
import com.altomni.apn.user.domain.enumeration.FeeTypeConverter;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Convert;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * A StartFteRate.
 */
public class StartFteRateDTO implements Serializable {

    private static final long serialVersionUID = -1287979898313457637L;

    private Long id;

    private Long startId;

    private Integer currency = CurrencyConstants.USD;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType rateUnitType;

    private BigDecimal salary = BigDecimal.ZERO;

    private BigDecimal signOnBonus = BigDecimal.ZERO;

    private BigDecimal retentionBonus = BigDecimal.ZERO;

    private BigDecimal annualBonus = BigDecimal.ZERO;

    private BigDecimal relocationPackage = BigDecimal.ZERO;

    private BigDecimal extraFee = BigDecimal.ZERO;

    private BigDecimal totalBillableAmount = BigDecimal.ZERO;

    @Convert(converter = FeeTypeConverter.class)
    private FeeType feeType;

    private BigDecimal feePercentage;

    @ApiModelProperty(value = "GP")
    private BigDecimal totalBillAmount = BigDecimal.ZERO;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStartId() {
        return startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public RateUnitType getRateUnitType() {
        return rateUnitType;
    }

    public void setRateUnitType(RateUnitType rateUnitType) {
        this.rateUnitType = rateUnitType;
    }

    public BigDecimal getSalary() {
        return salary;
    }

    public void setSalary(BigDecimal salary) {
        this.salary = salary;
    }

    public BigDecimal getSignOnBonus() {
        return signOnBonus;
    }

    public void setSignOnBonus(BigDecimal signOnBonus) {
        this.signOnBonus = signOnBonus;
    }

    public BigDecimal getRetentionBonus() {
        return retentionBonus;
    }

    public void setRetentionBonus(BigDecimal retentionBonus) {
        this.retentionBonus = retentionBonus;
    }

    public BigDecimal getAnnualBonus() {
        return annualBonus;
    }

    public void setAnnualBonus(BigDecimal annualBonus) {
        this.annualBonus = annualBonus;
    }

    public BigDecimal getRelocationPackage() {
        return relocationPackage;
    }

    public void setRelocationPackage(BigDecimal relocationPackage) {
        this.relocationPackage = relocationPackage;
    }

    public BigDecimal getExtraFee() {
        return extraFee;
    }

    public void setExtraFee(BigDecimal extraFee) {
        this.extraFee = extraFee;
    }

    public BigDecimal getTotalBillableAmount() {
        return totalBillableAmount;
    }

    public void setTotalBillableAmount(BigDecimal totalBillableAmount) {
        this.totalBillableAmount = totalBillableAmount;
    }

    public FeeType getFeeType() {
        return feeType;
    }

    public void setFeeType(FeeType feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getFeePercentage() {
        return feePercentage;
    }

    public void setFeePercentage(BigDecimal feePercentage) {
        this.feePercentage = feePercentage;
    }

    public BigDecimal getTotalBillAmount() {
        return totalBillAmount;
    }

    public void setTotalBillAmount(BigDecimal totalBillAmount) {
        this.totalBillAmount = totalBillAmount;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StartFteRateDTO startFteRate = (StartFteRateDTO) o;
        if (startFteRate.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), startFteRate.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "StartFteRate{" +
            "id=" + id +
            ", startId=" + startId +
            ", currency=" + currency +
            ", rateUnitType=" + rateUnitType +
            ", salary=" + salary +
            ", signOnBonus=" + signOnBonus +
            ", retentionBonus=" + retentionBonus +
            ", annualBonus=" + annualBonus +
            ", relocationPackage=" + relocationPackage +
            ", extraFee=" + extraFee +
            ", totalBillableAmount=" + totalBillableAmount +
            ", feeType=" + feeType +
            ", feePercentage=" + feePercentage +
            ", totalBillAmount=" + totalBillAmount +
            '}';
    }
}
