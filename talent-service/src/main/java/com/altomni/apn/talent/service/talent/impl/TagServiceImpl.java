package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.convert.Convert;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.talent.Tag;
import com.altomni.apn.talent.repository.talent.TagRepository;
import com.altomni.apn.talent.service.dto.talent.SpecialTagDTO;
import com.altomni.apn.common.dto.talent.TagDTO;
import com.altomni.apn.talent.service.talent.TagService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Service Implementation for managing Tag.
 */
@Service
@Transactional
public class TagServiceImpl implements TagService {

    private final Logger log = LoggerFactory.getLogger(TagServiceImpl.class);

    private final TagRepository tagRepository;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final TalentApiPromptProperties talentApiPromptProperties;

    private final String SPECIAL_TAG = "SPECIAL_TAG";

    public TagServiceImpl(TagRepository tagRepository,CommonApiMultilingualConfig commonApiMultilingualConfig,TalentApiPromptProperties talentApiPromptProperties) {
        this.tagRepository = tagRepository;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.talentApiPromptProperties = talentApiPromptProperties;
    }

    /**
     * Get all the tags.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public List<TagDTO> findAllByIds(List<Long> tagIds) {
        log.debug("Request to get all Tags");
        return Convert.toList(TagDTO.class, tagRepository.findAllById(tagIds));
    }


    @Override
    @Transactional(readOnly = true)
    public List<TagDTO> findAll() {
        log.debug("Request to get all Tags");
        return Convert.toList(TagDTO.class, tagRepository.findAll());
    }

    /**
     * Get one tag by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<TagDTO> findOne(Long id) {
        log.debug("Request to get Tag : {}", id);
        return Optional.of(Convert.convert(TagDTO.class, tagRepository.getById(id)));
    }

    /**
     * Delete the tag by id.
     *
     * @param id the id of the entity
     */
    @Override
    public void delete(Long id) {
        log.debug("Request to delete Tag : {}", id);        tagRepository.deleteById(id);
    }

    @Override
    public Optional<TagDTO> checkTagExists (Long tagId) {
        Optional<TagDTO> tagDTO = findOne(tagId);
        if (!tagDTO.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CHECKTAGEXISTS_TAGIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(tagId),talentApiPromptProperties.getTalentService()));
        }
        return tagDTO;
    }


    // validation
    private void validate (TagDTO tagDTO) {
        if (CollectionUtils.isNotEmpty(tagRepository.findAllByTagName(tagDTO.getTagName()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_VALIDATE_TAGNAMENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(tagDTO.getTagName()),talentApiPromptProperties.getTalentService()));
        }
    }

    /**
     * Save a special tag.
     *
     * @param specialTagDTO the entity to save
     * @return the persisted entity
     */
    @Override
    public SpecialTagDTO createSpecialTag(SpecialTagDTO specialTagDTO) {
        if (specialTagDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CREATESPECIALTAG_IDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        if (CollectionUtils.isNotEmpty(tagRepository.findAllByTypeAndTagName(SPECIAL_TAG, specialTagDTO.getTagName()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CREATESPECIALTAG_SPECIALTAGNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(specialTagDTO.getTagName()),talentApiPromptProperties.getTalentService()));
        }
        if (SecurityUtils.isSuperUser() || SecurityUtils.isTeamLeader()) {
            specialTagDTO.setType(SPECIAL_TAG);
            return SpecialTagDTO.fromTag(tagRepository.save(Tag.fromSpecialTagDTO(specialTagDTO)));
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_CREATESPECIALTAG_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    @Override
    public List<SpecialTagDTO> findAllSpecialTags() {
        List<Tag> tags = tagRepository.findAllByType(SPECIAL_TAG);
        List<SpecialTagDTO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tags)) {
            for (Tag tag : tags) {
                result.add(SpecialTagDTO.fromTag(tag));
            }
        }
        return result;
    }
}
