package com.altomni.apn.talent.domain.linkedinproject;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A UserFavoriteLinkedinProject.
 */
@Entity
@Table(name = "user_favorite_linkedin_project")
public class UserFavoriteLinkedinProject implements Serializable {

    private static final long serialVersionUID = -3100624315152395251L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "linkedin_project_id")
    private Long linkedinProjectId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public UserFavoriteLinkedinProject userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getLinkedinProjectId() {
        return linkedinProjectId;
    }

    public UserFavoriteLinkedinProject linkedinProjectId(Long linkedinProjectId) {
        this.linkedinProjectId = linkedinProjectId;
        return this;
    }

    public void setLinkedinProjectId(Long linkedinProjectId) {
        this.linkedinProjectId = linkedinProjectId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UserFavoriteLinkedinProject userFavoriteLinkedinProject = (UserFavoriteLinkedinProject) o;
        if (userFavoriteLinkedinProject.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), userFavoriteLinkedinProject.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "UserFavoriteLinkedinProject{" +
            "id=" + id +
            ", userId=" + userId +
            ", linkedinProjectId=" + linkedinProjectId +
            '}';
    }
}
