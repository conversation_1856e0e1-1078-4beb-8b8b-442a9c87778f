package com.altomni.apn.talent.web.rest.talent;

import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.linkedin.TalentTrackingLinkedinPendingGroup;
import com.altomni.apn.talent.service.talent.LinkedinAccountInfoService;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingGroupVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingVO;
import com.altomni.apn.talent.web.rest.talent.dto.*;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * REST controller for managing LinkedIn account information.
 */
@Api(tags = {"Talent LinkedIn"})
@RestController
@RequestMapping("/api/v3")
public class TalentLinkedinResource {

    private final Logger log = LoggerFactory.getLogger(TalentLinkedinResource.class);

    @Resource
    private LinkedinAccountInfoService linkedinAssistantService;

    /**
     * 获取保存的领英账号信息
     *
     * @return 领英账号信息
     */
    @ApiOperation(value = "获取保存的领英账号信息", notes = "根据领英ID获取已保存的账号信息")
    @PostMapping("/linkedin/info")
    @Timed
    public ResponseEntity<LinkedinAccountInfoResponse> getLinkedinInfo(@RequestBody LinkedinAccountInfoRequest request) {
        log.info("[APN: TalentLinkedin @{}] REST request to get LinkedIn info for linkedinId: {}", 
                SecurityUtils.getUserId(), request.getLinkedinId());
        
        LinkedinAccountInfoResponse response = linkedinAssistantService.getLinkedinAccountInfo(request.getLinkedinId());
        return ResponseEntity.ok(response);
    }

    /**
     * 保存当前的领英账号信息
     *
     * @param request 领英账号信息请求
     * @return 无返回内容
     */
    @ApiOperation(value = "保存当前的领英账号信息", notes = "保存或更新领英账号的好友数量和匹配时间")
    @PutMapping("/linkedin/info")
    @Timed
    public ResponseEntity<Void> saveLinkedinInfo(
            @ApiParam(value = "领英账号信息", required = true)@RequestBody LinkedinAccountInfoRequest request) {
        log.info("[APN: TalentLinkedin @{}] REST request to save LinkedIn info: {}", 
                SecurityUtils.getUserId(), request);
        
        linkedinAssistantService.saveLinkedinAccountInfo(request);
        return ResponseEntity.ok().build();
    }

    /**
     * 新增埋点事件
     *
     * @param request 事件追踪请求
     * @return 无返回内容
     */
    @ApiOperation(value = "新增埋点事件", notes = "批量添加领英助手事件追踪记录")
    @PutMapping("/linkedin/event-tracking")
    @Timed
    public ResponseEntity<Void> addFriendNotify(
            @ApiParam(value = "事件追踪信息", required = true) @Valid @RequestBody LinkedinEventTrackingRequest request) {
        log.info("[APN: TalentLinkedin @{}] REST request to add friend notify events for operator, events count: {}",
                SecurityUtils.getUserId(),
                request.getEventTrackingList() != null ? request.getEventTrackingList().size() : 0);

        linkedinAssistantService.addEventTracking(request);
        return ResponseEntity.ok().build();
    }

    /**
     * 新建待加好友分组
     *
     * @param request 分组创建请求
     * @return 分组信息
     */
    @ApiOperation(value = "新建待加好友分组", notes = "创建一个新的待加好友分组，可以包含指定的成员")
    @PostMapping("/tracking/pending/group")
    @Timed
    public ResponseEntity<TalentTrackingGroupVO> createPendingGroup(
            @ApiParam(value = "分组创建信息", required = true) @Valid @RequestBody LinkedinPendingGroupRequest request) {
        log.info("[APN: TalentLinkedin @{}] REST request to create pending group: {}",
                SecurityUtils.getUserId(), request);

        return ResponseEntity.ok(linkedinAssistantService.linkedinAssistantService(request));
    }

    @PutMapping("/tracking/pending/group/{id}")
    public ResponseEntity<TalentTrackingGroupVO> updateGroup(@PathVariable("id") Long id, @RequestBody LinkedinPendingGroupRequest request) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to update talent pending tracking group. id: {}, dto: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), id, request);
        return new ResponseEntity<>(linkedinAssistantService.updateGroup(id, request.getName()), HttpStatus.CREATED);
    }

    @DeleteMapping("/tracking/pending/group/{id}")
    public ResponseEntity<HttpStatus> inactiveGroup(@PathVariable("id") Long id) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to inactive talent pending tracking group. id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), id);
        linkedinAssistantService.inactiveGroup(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/tracking/pending/group/member")
    public ResponseEntity<HttpStatus> savePendingGroupMember(@Valid @RequestBody SavePendingGroupMemberRequestDTO requestDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to save talent pending tracking group member: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), requestDTO);
        linkedinAssistantService.saveGroupMember(requestDTO);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @GetMapping("/tracking/pending/group/search")
    public ResponseEntity<List<TalentTrackingGroupVO>> searchGroup(@RequestParam("operatorLinkedinId") String operatorLinkedinId, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to search talent pending tracking group: {}, operatorLinkedinId: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), operatorLinkedinId);
        Page<TalentTrackingLinkedinPendingGroup> page = linkedinAssistantService.searchGroup(operatorLinkedinId, pageable);
        List<TalentTrackingGroupVO> talentTrackingGroupVOList = linkedinAssistantService.toTalentTrackingGroupVOList(page.getContent());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/tracking/pending/group/search");
        headers.add("Access-Control-Expose-Headers","Pagination-Count");
        return new ResponseEntity<>(talentTrackingGroupVOList, headers, HttpStatus.OK);
    }

    @GetMapping("/tracking/pending/group/{id}/member")
    public ResponseEntity<List<TalentTrackingVO>> queryGroupMember(@PathVariable("id") Long id) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to query talent pending tracking group member: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), id);
        List<TalentTrackingVO> talentTrackingVOList = linkedinAssistantService.queryGroupMember(id);
        return new ResponseEntity<>(talentTrackingVOList, HttpStatus.OK);
    }

    @GetMapping("/tracking/pending/group/list")
    public ResponseEntity<List<TalentTrackingGroupVO>> queryGroupByOperatorLinkedinId(@RequestParam("operatorLinkedinId") String operatorLinkedinId) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to query talent pending tracking group by operatorLinkedinId: {}, operatorLinkedinId: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), operatorLinkedinId);
        return new ResponseEntity<>(linkedinAssistantService.queryGroupByOperatorLinkedinId(operatorLinkedinId), HttpStatus.OK);
    }

    @PutMapping("/linkedin/add-friend-notify")
    public ResponseEntity<Void> addFriendNotify(@RequestBody AddFriendNotifyRequestDTO requestDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to notify add friend, request dto: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), requestDTO);
        linkedinAssistantService.addFriendNotify(requestDTO);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/linkedin/notify-add-to-friend-group")
    public ResponseEntity<Void> notifyAddToFriendGroup(@RequestBody NotifyAddToFriendGroupRequestDTO requestDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to notify add to friend group, request dto: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), requestDTO);
        linkedinAssistantService.notifyAddToFriendGroup(requestDTO);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/linkedin/wait-add-to-group")
    public ResponseEntity<GetWaitAddToGroupResponseDTO> getWaitAddToGroup(@RequestBody GetWaitAddToGroupRequestDTO requestDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to get waiting add to group, request dto: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), requestDTO);
        return new ResponseEntity<>(linkedinAssistantService.getWaitAddToGroup(requestDTO), HttpStatus.OK);
    }
}