package com.altomni.apn.talent.domain.vm.folder;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Entity
@NoArgsConstructor
@AllArgsConstructor
public class SearchTalentFolderVM extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    private Long id;

    private String name;

    @Column(name = "folder_note")
    private String folderNote;

    private String creator;

    @Column(name = "tenant_id")
    private Long tenantId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFolderNote() {
        return folderNote;
    }

    public void setFolderNote(String folderNote) {
        this.folderNote = folderNote;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
}
