package com.altomni.apn.talent.service.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Set;

@Data
public class TalentFolderSharingTeamDTO {
    private Long id;
    private Long jobFolderId;
    private Long teamId;
    @ApiModelProperty(value = "The permission for customFolder.", allowableValues = "READ, EDIT")
    @NotNull(message = "permission is required")
    private FolderPermission permission;
    @JsonProperty
    private Set<Long> excludedUserIds;

    public boolean DoesUserRemoveTeamSharing(Long userId){
        return this.getExcludedUserIds().stream().anyMatch(userId::equals);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobFolderId() {
        return jobFolderId;
    }

    public void setJobFolderId(Long jobFolderId) {
        this.jobFolderId = jobFolderId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public FolderPermission getPermission() {
        return permission;
    }

    public void setPermission(FolderPermission permission) {
        this.permission = permission;
    }

    public Set<Long> getExcludedUserIds() {
        return excludedUserIds;
    }

    public void setExcludedUserIds(Set<Long> excludedUserIds) {
        this.excludedUserIds = excludedUserIds;
    }
}
