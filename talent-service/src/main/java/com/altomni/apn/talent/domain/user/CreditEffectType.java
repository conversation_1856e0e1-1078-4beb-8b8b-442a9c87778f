package com.altomni.apn.talent.domain.user;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The credit type enumeration.
 * <AUTHOR>
 */
public enum CreditEffectType implements ConvertedEnum<Integer> {
    INSTANT(0),
    NEXT_MONTH(1);

    private final int dbValue;

    CreditEffectType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CreditEffectType, Integer> resolver = new ReverseEnumResolver<>(CreditEffectType.class, CreditEffectType::toDbValue);

    public static CreditEffectType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
