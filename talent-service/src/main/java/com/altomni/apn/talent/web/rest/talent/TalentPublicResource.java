package com.altomni.apn.talent.web.rest.talent;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.aop.user.AttachSimpleUser;
import com.altomni.apn.common.dto.folder.talentrelatejob.SharedLinkDTO;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentFromSharingLinkDTO;
import com.altomni.apn.common.vo.job.jobsharing.JobSharingPublicResponseVO;
import com.altomni.apn.talent.service.folder.TalentRelateJobFolderJobSharingPlatformProcessService;
import com.altomni.apn.talent.service.talent.TalentPublicService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URISyntaxException;

/**
 * REST controller for managing Sharing Link related API
 * Warn: All method inside should be public for external use only.
 */
@Api(tags = {"Talent"})
@RestController
@RequestMapping("/api/v3/public")
public class TalentPublicResource {

    private final Logger log = LoggerFactory.getLogger(TalentPublicResource.class);

    private static final String ENTITY_NAME = "talent public";

    @Resource
    private TalentPublicService talentPublicService;


    @Resource
    private TalentRelateJobFolderJobSharingPlatformProcessService talentRelateJobFolderJobSharingPlatformProcessService;


    @PostMapping("/sharing-platform/talents")
    @AttachSimpleUser
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> createTalent(@Valid @RequestBody TalentFromSharingLinkDTO talentDTO) throws URISyntaxException {
        log.info("[APN: Talent Public] REST request to save Talent: {}", talentDTO);

        Long talentId = talentPublicService.createTalentFromSharedURL(talentDTO);

        log.info("[APN: Talent Public] REST request to save Talent finished with id{}", talentId);
        return ResponseEntity.ok().build();
    }

    /**
     * Public API
     * @param sharedLinkDTO
     * @return
     * @throws URISyntaxException
     */
    @PostMapping("/sharing-platform/shared-link")
    @AttachSimpleUser
    @Timed
    public ResponseEntity<JobSharingPublicResponseVO> getSharedLinkExpirationTime(@Valid @RequestBody SharedLinkDTO sharedLinkDTO) throws URISyntaxException {
        log.info("[APN: Talent Public] REST request to get Shared Link Expiration Time: {}", sharedLinkDTO);

        JobSharingPublicResponseVO result = talentRelateJobFolderJobSharingPlatformProcessService.getSharingPlatformURLExpirationTime(sharedLinkDTO);
        return ResponseEntity.ok(result);
    }


}
