package com.altomni.apn.talent.domain.enumeration.linkedinproject;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ContactStatus implements ConvertedEnum<Integer>{

    UN_CONTACTED(0),

    CONTACTED(1),

    REPLIED(2);


    private final Integer dbValue;

    ContactStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ContactStatus, Integer> resolver =
        new ReverseEnumResolver<>(ContactStatus.class, ContactStatus::toDbValue);

    public static ContactStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
