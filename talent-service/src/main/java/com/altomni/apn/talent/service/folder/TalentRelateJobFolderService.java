package com.altomni.apn.talent.service.folder;


import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderBasicInfoDTO;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderSearchDTO;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderWithCreatedFlag;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFoldersDTO;
import com.altomni.apn.common.vo.talent.AddTalentsToFoldersOutput;
import com.altomni.apn.talent.web.rest.talent.dto.TalentsToFoldersDTO;

public interface TalentRelateJobFolderService {
    TalentRelateJobFolderWithCreatedFlag addRelateJobFolders(TalentRelateJobFoldersDTO talentRelateJobFoldersDTO) throws Exception;

    TalentRelateJobFoldersDTO editRelateJobFolders(String folderId, TalentRelateJobFoldersDTO talentRelateJobFoldersDTO);

    TalentRelateJobFoldersDTO mergeRelateJobFoldersRequest(String folderId);

    void rejectRelateJobFoldersRequest(String folderId);

    void deleteRelateJobFolders(String folderId);

    AddTalentsToFoldersOutput addTalentsToFolders(String folderId, TalentsToFoldersDTO talentsToFoldersDTO);

    void removeTalentsToFolders(String folderId, TalentsToFoldersDTO talentsToFoldersDTO);

    TalentRelateJobFoldersDTO getRelateJobFolders(String folderId);

    Boolean isFolderIdValid(String folderId);

    TalentRelateJobFolderBasicInfoDTO getTalentRelateJobFolderBasicInfo(TalentRelateJobFolderSearchDTO talentRelateJobFolderSearchDTO);
}
