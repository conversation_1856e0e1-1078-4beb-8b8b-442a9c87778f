package com.altomni.apn.talent.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum StartType implements ConvertedEnum<Integer> {

    /**
     * For contract job: first created start
     */
    CONTRACT_NEW_HIRE(0),

    /**
     * For contract job: extended start
     */
    CONTRACT_EXTENSION(1),

    /**
     * For FTE job: need to replacement when start failed warranty.
     */
    FTE_FAIL_WARRANTY_REPLACEMENT(2),

    /**
     * For FTE job: no need to replacement when start failed warranty.
     */
    FTE_FAIL_WARRANTY_NO_REPLACEMENT(3),

    /**
     * For FTE job: first created start
     */
    FTE_NEW_HIRE(4),

    /**
     * Convert start from contract job to fte
     */
    CONVERT_TO_FTE(5);

    private final int dbValue;
    StartType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<StartType, Integer> resolver =
        new ReverseEnumResolver<>(StartType.class, StartType::toDbValue);

    public static StartType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
