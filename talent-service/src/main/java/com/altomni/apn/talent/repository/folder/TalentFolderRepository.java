package com.altomni.apn.talent.repository.folder;

import com.altomni.apn.common.dto.folder.FolderNameDTO;
import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.talent.domain.folder.TalentFolder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TalentFolderRepository extends JpaRepository<TalentFolder, Long> {
    TalentFolder findOneByPermissionUserIdAndName(Long pUserId, String name);

    /**
     *
        folder list function
     */
    @Query(value = "SELECT new com.altomni.apn.common.dto.folder.FolderNameDTO(f.id, f.name) FROM TalentFolder f WHERE f.permissionUserId = ?1 ORDER BY f.createdDate desc ")
    List<FolderNameDTO> findAllFolderNameByPermissionUserId(Long userId);

    @Query(value = "SELECT new com.altomni.apn.common.dto.folder.FolderNameDTO(f.id, f.name) " +
            "FROM TalentFolder f " +
            "WHERE EXISTS ( " +
            "    SELECT 1 " +
            "    FROM TalentFolderSharingUser su " +
            "    WHERE f.id = su.talentFolderId AND su.userId = ?1 " +
            "       AND su.permission = ?2 " +
            ") OR EXISTS ( " +
            "    SELECT 1 " +
            "    FROM TalentFolderSharingTeam st " +
            "    INNER JOIN PermissionUserTeam ptu ON st.teamId = ptu.teamId " +
            "    WHERE f.id = st.talentFolderId AND ptu.userId = ?1 " +
            "       AND st.permission = ?2 " +
            "       AND (st.excludedUserIds IS NULL OR st.excludedUserIds NOT LIKE CONCAT('%', ?1, '%')) " +
            ") ")
    List<FolderNameDTO> findAllSharedFolderNameByUserIdAndPermission(Long userId, FolderPermission permission);

    @Query(value = "SELECT new com.altomni.apn.common.dto.folder.FolderNameDTO(f.id, f.name) " +
            "FROM TalentFolder f " +
            "WHERE EXISTS ( " +
            "    SELECT 1 " +
            "    FROM TalentFolderSharingUser su " +
            "    WHERE f.id = su.talentFolderId AND su.userId = ?1 " +
            ") OR EXISTS ( " +
            "    SELECT 1 " +
            "    FROM TalentFolderSharingTeam st " +
            "    INNER JOIN PermissionUserTeam ptu ON st.teamId = ptu.teamId " +
            "    WHERE f.id = st.talentFolderId AND ptu.userId = ?1 " +
            "       AND (st.excludedUserIds IS NULL OR st.excludedUserIds NOT LIKE CONCAT('%', ?1, '%')) " +
            ") ")
    List<FolderNameDTO> findAllSharedFolderNameByUserId(Long userId);


    List<TalentFolder> findAllByIdIn(List<Long> talentFolderIds);

}
