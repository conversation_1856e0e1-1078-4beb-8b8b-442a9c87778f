package com.altomni.apn.talent.service;

import com.altomni.apn.common.vo.dict.EnumCommonPoolVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EnumCommonPoolService {

    private Map<String, EnumCommonPoolVO> cachedIndustryMap = new HashMap<>();

    private Map<String, EnumCommonPoolVO> cachedJobFunctionMap = new HashMap<>();

    @PostConstruct
    public void init() {
        loadEnumIndustryIntoCache();
        loadEnumJobFunctionIntoCache();
    }

    private void loadEnumIndustryIntoCache() {
        cachedIndustryMap = loadCsvFromFile("enum_industry.csv");
        log.info("[EnumCommonPoolService] Loaded {} industries", cachedIndustryMap.size());
    }

    private void loadEnumJobFunctionIntoCache() {
        cachedJobFunctionMap = loadCsvFromFile("enum_job_function.csv");
        log.info("[EnumCommonPoolService] Loaded {} job functions", cachedJobFunctionMap.size());
    }

    private Map<String, EnumCommonPoolVO> loadCsvFromFile(String filePath) {
        Map<String, EnumCommonPoolVO> dataList = new HashMap<>();

        try {
//            File file = ResourceUtils.getFile(filePath);
//            List<String> lines = Files.readAllLines(Paths.get(file.toURI()));
            // 使用 ClassPathResource 获取 InputStream，而不是尝试解析为 File
            InputStream inputStream = new ClassPathResource(filePath).getInputStream();
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));

            List<String> lines = reader.lines().collect(Collectors.toList());
            reader.close();

            // 移除表头
            if (!lines.isEmpty()) {
                lines.remove(0);
            }

            dataList = lines.stream().map(line -> {
                String[] parts = line.split(",");
                if (parts.length < 6) {
                    return null; // 忽略格式不正确的行
                }
                EnumCommonPoolVO data = new EnumCommonPoolVO();
                data.setName(parts[0].trim());
                data.setCnSortOrder(Integer.parseInt(parts[1].trim()));
                data.setEnSortOrder(Integer.parseInt(parts[2].trim()));
                data.setCnDisplay(parts[3].trim());
                data.setEnDisplay(parts[4].trim());
                data.setId(Long.parseLong(parts[5].trim()));
                data.setEnumId(Long.parseLong(parts[6].trim()));
                return data;
            }).filter(Objects::nonNull).collect(Collectors.toMap(EnumCommonPoolVO::getName, Function.identity()));

        } catch (IOException | NumberFormatException e) {
            log.error("[EnumCommonPoolService: loadCsvFromFile] error when loading: {}", filePath, e);
        }

        return dataList;
    }

    public List<EnumCommonPoolVO> getIndustries() {
        return cachedIndustryMap.values().stream().collect(Collectors.toList());
    }

    public List<Long> getIndustryEnumIdsByNames(List<String> names) {
        return names.stream().map(name -> cachedIndustryMap.get(name)).filter(Objects::nonNull).map(EnumCommonPoolVO::getEnumId).collect(Collectors.toList());
    }

    public List<EnumCommonPoolVO> getJobFunctions() {
        return cachedJobFunctionMap.values().stream().collect(Collectors.toList());
    }

    public List<Long> getJobFunctionEnumIdsByNames(List<String> names) {
        return names.stream().map(name -> cachedJobFunctionMap.get(name)).filter(Objects::nonNull).map(EnumCommonPoolVO::getEnumId).collect(Collectors.toList());
    }

}
