package com.altomni.apn.talent.service.vo.tracking;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "用户好友分组信息")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentTrackingGroupInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "领英ID")
    private String linkedinId;

    @ApiModelProperty(value = "分组列表")
    private List<GroupInfo> groupList;
}