package com.altomni.apn.talent.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The EmailBlastStatus enumeration.
 */
public enum EmailBlastStatus implements ConvertedEnum<Integer> {
    DRAFT(0),
    SCHEDULED(1),
    SENT(2),
    CANCELED(3),
    DELETED(4);

    private final Integer dbValue;

    EmailBlastStatus(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<EmailBlastStatus, Integer> resolver =
        new ReverseEnumResolver<>(EmailBlastStatus.class, EmailBlastStatus::toDbValue);

    public static EmailBlastStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
