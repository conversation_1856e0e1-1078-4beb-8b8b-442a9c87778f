package com.altomni.apn.talent.service.dto.linkedinproject;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.utils.JsonUtil;

import java.io.Serializable;
import java.time.Instant;

public class SearchHistory implements Serializable {

    private static final long serialVersionUID = -1968186687399597950L;

    private String id;

    private Long jobId;

    private String name;

    private String content;

    private String searchNote;

    private String jobDescription;

    private Long totalReturnedCount;

    private Long viewedCount;

    private Long clickedCount;

    private Long userId;

    private Instant createDate;

    public String toJSON() {
        JSONObject result = new JSONObject();
        JsonUtil.fluentPut(result, "id", id);
        JsonUtil.fluentPut(result, "userId", userId);
        JsonUtil.fluentPut(result, "jobId", jobId);
        JsonUtil.fluentPut(result, "name", name);
        JsonUtil.fluentPut(result, "content", content);
        JsonUtil.fluentPut(result, "searchNote", searchNote);
        JsonUtil.fluentPut(result, "jobDescription", jobDescription);
        JsonUtil.fluentPut(result, "totalReturnedCount", totalReturnedCount);
        JsonUtil.fluentPut(result, "viewedCount", viewedCount);
        JsonUtil.fluentPut(result, "clickedCount", clickedCount);
        return result.toJSONString(0);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getJobId() { return jobId; }

    public void setJobId(Long jobId) { this.jobId = jobId; }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSearchNote() { return searchNote; }

    public void setSearchNote(String searchNote) { this.searchNote = searchNote; }

    public String getJobDescription() { return jobDescription; }

    public void setJobDescription(String jobDescription) { this.jobDescription = jobDescription; }

    public Long getTotalReturnedCount() { return totalReturnedCount; }

    public void setTotalReturnedCount(Long totalReturnedCount) { this.totalReturnedCount = totalReturnedCount; }

    public Long getViewedCount() { return viewedCount; }

    public void setViewedCount(Long viewedCount) { this.viewedCount = viewedCount; }

    public Long getClickedCount() { return clickedCount; }

    public void setClickedCount(Long clickedCount) { this.clickedCount = clickedCount; }

    public Instant getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Instant createDate) {
        this.createDate = createDate;
    }

    @Override
    public String toString() {
        return "SearchHistory{" +
            "id='" + id + '\'' +
            ", name='" + name + '\'' +
            ", content='" + content + '\'' +
            ", userId=" + userId +
            ", jobId=" + jobId +
            ", searchNote='" + searchNote + '\'' +
            ", jobDescription='" + jobDescription + '\'' +
            ", totalReturnedCount=" + totalReturnedCount +
            ", viewedCount=" + viewedCount +
            ", clickedCount=" + clickedCount +
            ", createDate=" + createDate +
            '}';
    }
}
