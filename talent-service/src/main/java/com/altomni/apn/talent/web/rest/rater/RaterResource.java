package com.altomni.apn.talent.web.rest.rater;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.talent.TalentESConditionDTO;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.service.dto.redis.RecommendedTenantJobResponse;
import com.altomni.apn.talent.service.rater.RaterService;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

@RestController
@RequestMapping("/api/v3")
public class RaterResource {

    private final Logger log = LoggerFactory.getLogger(RaterResource.class);

    @Resource
    private RaterService raterService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @GetMapping("/recommend-jobs-for-tenant-talent/talentId/{talentId}")
    @Timed
    public ResponseEntity<RecommendedTenantJobResponse> recommendJobsForTenantTalent(@PathVariable Long talentId, Pageable pageable, @RequestParam(name = "refresh", defaultValue = "false") boolean refresh) throws IOException {
        log.info("[APN: Rater@{}] REST request to recommend jobs for tenant talent: {}", SecurityUtils.getUserId(), talentId);
        return ResponseEntity.ok(raterService.recommendJobsForTenantTalent(talentId, pageable, refresh));
    }

    @PostMapping("/recommend-jobs-for-common-talent/esId")
    @Timed
    public ResponseEntity<RecommendedTenantJobResponse> recommendJobsForCommonTalent(@RequestBody TalentESConditionDTO condition, Pageable pageable, @RequestParam(name = "refresh", defaultValue = "false") boolean refresh) throws IOException {
        log.info("[APN: Rater@{}] REST request to recommend jobs for common talent: {}", SecurityUtils.getUserId(), condition);
        if (ObjectUtil.isNull(condition.getEsId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.RATER_RECOMMENDJOBSFORCOMMONTALENT_ESIDEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        return new ResponseEntity<>(raterService.recommendJobsForCommonTalent(condition.getEsId(), pageable, refresh), HttpStatus.OK);
    }

}
