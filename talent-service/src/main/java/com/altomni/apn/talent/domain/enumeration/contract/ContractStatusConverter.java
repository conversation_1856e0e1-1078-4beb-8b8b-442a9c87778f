package com.altomni.apn.talent.domain.enumeration.contract;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class ContractStatusConverter extends AbstractAttributeConverter<ContractStatus, Integer> {
    public ContractStatusConverter() {
        super(ContractStatus::toDbValue, ContractStatus::fromDbValue);
    }
}
