package com.altomni.apn.talent.service.linkedinproject;


import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinTalentDTO;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinTalentVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing LinkedinTalent.
 */
public interface LinkedinTalentService {

    /**
     * Save a linkedinTalent.
     *
     * @param linkedinTalent the entity to save
     * @return the persisted entity
     */
    LinkedinTalentVO create(LinkedinTalentDTO linkedinTalent);

    /**
     * Save a linkedinTalent.
     *
     * @param linkedinTalent the entity to save
     * @return the persisted entity
     */
    LinkedinTalentVO update(LinkedinTalentDTO linkedinTalent);

    /**
     * Get all the linkedinTalents.
     *
     * @return the list of entities
     */
    Page<LinkedinTalentVO> findAll(Pageable pageable);


    /**
     * Get the "id" linkedinTalent.
     *
     * @param id the id of the entity
     * @return the entity
     */
    LinkedinTalentVO findOne(String id);

    /**
     * Delete the "id" linkedinTalent.
     *
     * @param id the id of the entity
     */
    void delete(String id);

    Page<LinkedinTalentVO> findAllByTalentIds(List<String> ids, Pageable pageable);
}
