package com.altomni.apn.talent.web.rest.talent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

@ApiModel(description = "事件追踪项")
public class EventTrackingItem {

    @ApiModelProperty(value = "事件名称", required = true, example = "friend_request")
    @NotBlank(message = "事件名称不能为空")
    @JsonProperty("event")
    private String event;

    @ApiModelProperty(value = "事件发生时间", required = true, example = "2025-07-08T05:17:09.142Z")
    @NotBlank(message = "事件时间不能为空")
    @JsonProperty("eventDateTime")
    private String eventDateTime;

    @ApiModelProperty(value = "操作者领英ID", required = true, example = "operator-linkedin-123")
    @NotBlank(message = "操作者领英ID不能为空")
    @JsonProperty("operatorLinkedinId")
    private String operatorLinkedinId;

    @ApiModelProperty(value = "操作对象的领英ID", required = true, example = "target-linkedin-456")
    @NotBlank(message = "目标领英ID不能为空")
    @JsonProperty("linkedinId")
    private String linkedinId;

    @ApiModelProperty(value = "事件详情", example = "好友请求详细信息")
    @JsonProperty("detail")
    private String detail;

    // Getters and Setters
    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getEventDateTime() {
        return eventDateTime;
    }

    public void setEventDateTime(String eventDateTime) {
        this.eventDateTime = eventDateTime;
    }

    public String getOperatorLinkedinId() {
        return operatorLinkedinId;
    }

    public void setOperatorLinkedinId(String operatorLinkedinId) {
        this.operatorLinkedinId = operatorLinkedinId;
    }

    public String getLinkedinId() {
        return linkedinId;
    }

    public void setLinkedinId(String linkedinId) {
        this.linkedinId = linkedinId;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }
}
