package com.altomni.apn.talent.domain.record;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.record.TrackingType;
import com.altomni.apn.talent.domain.enumeration.record.TrackingTypeConverter;
import com.altomni.apn.talent.service.dto.record.TalentTrackingRecordDTO;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A TalentTrackingRecord.
 * <AUTHOR>
 */
@Entity
@Table(name = "talent_tracking_record")
public class TalentTrackingRecord extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -956520387234760691L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "user_id")
    private Long userId;

    @NotNull
    @Convert(converter = TrackingTypeConverter.class)
    @Column(name = "tracking_type")
    private TrackingType trackingType;

    @NotNull
    @Column(name = "contact")
    private String contact;

    @Column(name = "touch_time")
    private Instant touchTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public TrackingType getTrackingType() {
        return trackingType;
    }

    public void setTrackingType(TrackingType trackingType) {
        this.trackingType = trackingType;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public Instant getTouchTime() {
        return touchTime;
    }

    public void setTouchTime(Instant touchTime) {
        this.touchTime = touchTime;
    }

    public TalentTrackingRecord tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public TalentTrackingRecord userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public TalentTrackingRecord trackingType(TrackingType trackingType) {
        this.trackingType = trackingType;
        return this;
    }

    public TalentTrackingRecord contact(String contact) {
        this.contact = contact;
        return this;
    }

    public TalentTrackingRecord touchTime(Instant touchTime) {
        this.touchTime = touchTime;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TalentTrackingRecord talentTrackingRecord = (TalentTrackingRecord) o;
        if (talentTrackingRecord.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), talentTrackingRecord.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "TalentTrackingRecord{" +
                "id=" + id +
                ", tenantId=" + tenantId +
                ", userId=" + userId +
                ", trackingType=" + trackingType +
                ", contact='" + contact + '\'' +
                ", touchTime=" + touchTime +
                '}';
    }

    public static TalentTrackingRecord fromTalentTrackingRecordDTO(TalentTrackingRecordDTO talentTrackingRecordDTO) {
        TalentTrackingRecord talentTrackingRecord = new TalentTrackingRecord();
        ServiceUtils.myCopyProperties(talentTrackingRecordDTO, talentTrackingRecord);
        return talentTrackingRecord;
    }
}
