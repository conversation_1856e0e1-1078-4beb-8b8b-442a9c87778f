package com.altomni.apn.talent.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum StartFailedWarrantyReason implements ConvertedEnum<Integer> {


    CANDIDATE_RESIGNED(0),
    TERMINATED_PERFORMANCE_REASON(1),
    TERMINATED_OTHER_REASONS_FROM_CANDIDATE(2);

    private final int dbValue;
    StartFailedWarrantyReason(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<StartFailedWarrantyReason, Integer> resolver =
        new ReverseEnumResolver<>(StartFailedWarrantyReason.class, StartFailedWarrantyReason::toDbValue);

    public static StartFailedWarrantyReason fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
