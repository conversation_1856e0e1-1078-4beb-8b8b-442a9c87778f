package com.altomni.apn.talent.service.talent;

import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.talent.TalentOwnershipDTO;

import java.time.Instant;
import java.util.List;
import java.util.Set;

/**
 * Service Interface for managing TalentOwnership.
 */
public interface TalentOwnershipService {

    List<TalentOwnershipDTO> create(Long talentId, List<TalentOwnershipDTO> talentOwnerships, Instant createDate);

    /**
     * upsert TalentOwnerships.
     * @return the persisted entity
     */
    List<TalentOwnershipDTO> replace(Long talentId, List<Long> userIds);

    /**
     * Get all the TalentOwnerships.
     *
     * @return the list of entities
     */
    List<TalentOwnershipDTO> findAll(Long talentId);

    /**
     * find all talent ownership by talent id, including expired.
     * @param talentId talent id
     * @return list of talent ownership
     */
    List<TalentOwnership> findAllByTalentIdAndOwnershipTypeIncludeExpired(Long talentId, List<TalentOwnershipType> ownershipType);

    List<TalentOwnership> findAllByTalentIdAndOwnershipType(Long talentId, TalentOwnershipType ownershipType);

//TODO    void save(Long talentId, Long applicationId, List<ApplicationCommission> applicationCommissions);

    void delete(Long talentId);

    void deleteAllByTalentId(Long talentId);

    /**
     * @param talent object
     * @return true: talent is in protection period; false: talent out of protection period;
     */
    boolean validateTalentIsInProtectionPeriodOrNot(TalentV3 talent);

    List<TalentOwnership> saveAll(List<TalentOwnership> talentOwnerships);

    void transferOwnership(Long userId, Long newOwnerId);

    void removeDuplicateOwnerAndShare(Long userId);

    void inactivateCrmUser(Long userId);

    List<Long> getViewableTalentIdsOnUser(List<Long> talentIds, Long userId);

    Set<Long> filterValidTalents(List<Long> talentIds);
}
