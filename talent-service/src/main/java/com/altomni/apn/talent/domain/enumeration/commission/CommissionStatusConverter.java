package com.altomni.apn.talent.domain.enumeration.commission;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CommissionStatusConverter extends AbstractAttributeConverter<CommissionStatus, Integer> {
    public CommissionStatusConverter() {
        super(CommissionStatus::toDbValue, CommissionStatus::fromDbValue);
    }
}
