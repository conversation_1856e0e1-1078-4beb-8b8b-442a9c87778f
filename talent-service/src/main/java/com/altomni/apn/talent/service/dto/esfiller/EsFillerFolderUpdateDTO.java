package com.altomni.apn.talent.service.dto.esfiller;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class EsFillerFolderUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> ids;

    private List<String> from;

    private List<String> to;

    public EsFillerFolderUpdateDTO(List<String> ids, List<String> from, List<String> to) {
        this.ids = ids;
        this.from = from;
        this.to = to;
    }
}
