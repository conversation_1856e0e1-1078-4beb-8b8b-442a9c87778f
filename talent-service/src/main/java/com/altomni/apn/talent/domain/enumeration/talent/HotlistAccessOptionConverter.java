package com.altomni.apn.talent.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class HotlistAccessOptionConverter extends AbstractAttributeConverter<HotlistAccessOption, Integer> {
    public HotlistAccessOptionConverter() {
        super(HotlistAccessOption::toDbValue, HotlistAccessOption::fromDbValue);
    }
}
