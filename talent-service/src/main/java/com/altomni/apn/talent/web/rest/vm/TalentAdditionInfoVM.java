package com.altomni.apn.talent.web.rest.vm;

import lombok.Data;

@Data
public class TalentAdditionInfoVM {

    private Long talentId;

    private Long additionInfoId;

    private String additionalInfo;

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getAdditionInfoId() {
        return additionInfoId;
    }

    public void setAdditionInfoId(Long additionInfoId) {
        this.additionInfoId = additionInfoId;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }
}
