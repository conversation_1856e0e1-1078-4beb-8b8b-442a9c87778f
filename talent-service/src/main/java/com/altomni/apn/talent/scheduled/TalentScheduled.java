package com.altomni.apn.talent.scheduled;

import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.job.JobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * talent scheduled
 * <AUTHOR>
 */

@Slf4j
@Service
public class TalentScheduled {

    @Resource
    private EsFillerTalentService esFillerTalentService;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private JobService jobService;

    /**
     * max 40
     */
    /*@Value("${application.sync.threadNum:12}")
    private Integer threadNum;*/

    /*private volatile ExecutorService executorService;*/

    //@Scheduled(fixedRateString = "${application.esfiller.scheduledSyncRate}" + "000")
    /*public void syncTalentsToEs() {
        log.info("[TalentScheduled: scheduledSyncTalentsToES @-1] Scheduled sync Talents start at: {}", LocalDateTime.now());
        Long count = talentRepository.countOutOfSyncTalentIds();
        if (count <= 0) {
            return;
        }
        int startNum = 0;
        int pageSize = 2000;
        //todo redis/mysql lock to solve distributed problems
        while(true) {
            List<Long> talentIds = talentRepository.findOutOfSyncTalentIds(startNum, pageSize);
            log.info("[TalentScheduled: scheduledSyncTalentsToES @-1] total talents size: {}", talentIds.size());
            if (CollectionUtils.isEmpty(talentIds)) {
                break;
            }
            startNum += pageSize;
            List<List<Long>> listList = CollUtil.split(talentIds, 50);
            listList = listList.stream().filter(CollUtil::isNotEmpty).collect(Collectors.toList());
            CountDownLatch countDownLatch = new CountDownLatch(listList.size());
            listList.forEach(ids -> getExecutorService().execute(() -> {
                try {
                    List<Long> successIds = ids.stream().filter(id -> esFillerTalentService.syncTalentToEs2(id)).collect(Collectors.toList());
                    if (!successIds.isEmpty()) {
                        jobService.clearAllSyncRecordError(AsyncEnum.DATA_TYPE_TALENT, successIds);
                    }
                } catch (Exception e) {
                    log.error(ExceptionUtils.getStackTrace(e));
                } finally {
                    countDownLatch.countDown();
                }
            }));
            try {
                countDownLatch.await(30,TimeUnit.MINUTES);
            } catch (Exception e) {
                log.error("[apn]  talentScheduled await time out, message = [{}]", ExceptionUtils.getStackTrace(e));
            }
        }
        log.info("[TalentScheduled: scheduledSyncTalentsToES @-1] Scheduled sync Talents to ES Done!");
    }*/

    /*public void resetPausedSyncTalents() {
        log.info("[TalentScheduled: resetPausedSyncTalents @{}] REST request to reset paused sync talents to sync", SecurityUtils.getUserId());
        talentRepository.resetPausedSyncTalents();
    }*/

    /*private ExecutorService getExecutorService() {
        if (executorService == null) {
            synchronized (TalentScheduled.class) {
                if (executorService == null) {
                    executorService = new ThreadPoolExecutor(
                            threadNum,
                            threadNum * 2,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-scheduled-sync-talent-to-es", false));
                }
            }
        }
        return executorService;
    }*/
}
