package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The Currency enumeration.
 */
public enum SearchFilterType implements ConvertedEnum<String>{
    AND("and"),
    OR("or");

    // static resolving:
    public static final ReverseEnumResolver<SearchFilterType, String> resolver =
        new ReverseEnumResolver<>(SearchFilterType.class, SearchFilterType::toDbValue);
    private final String dbValue;

    SearchFilterType(String dbValue) {
        this.dbValue = dbValue;
    }

    public static SearchFilterType fromDbValue(String dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public String toDbValue() {
        return dbValue;
    }
}
