package com.altomni.apn.talent.domain.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.enumeration.ReviewedByType;
import com.altomni.apn.common.enumeration.ReviewedByTypeConverter;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Table(name = "talent_review_note")
public class TalentReviewNote extends AbstractAuditingEntity  {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "reviewed_by_type")
    @Convert(converter = ReviewedByTypeConverter.class)
    private ReviewedByType reviewedByType;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "reviewed_by")
    private Long reviewedBy;

    @Column(name = "reviewed_date")
    private LocalDateTime reviewedDate;

    @Column(name = "timezone")
    private String timezone;

    @Column(name = "note")
    private String note;
}