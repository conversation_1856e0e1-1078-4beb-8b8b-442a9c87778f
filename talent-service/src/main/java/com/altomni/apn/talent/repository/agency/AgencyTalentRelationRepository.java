package com.altomni.apn.talent.repository.agency;

import com.altomni.apn.talent.domain.agency.AgencyTalentRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * Spring Data  repository for the AgencySubmitApplication entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AgencyTalentRelationRepository extends JpaRepository<AgencyTalentRelation, Long> {

    Optional<AgencyTalentRelation> findByTalentId(Long talentId);

}
