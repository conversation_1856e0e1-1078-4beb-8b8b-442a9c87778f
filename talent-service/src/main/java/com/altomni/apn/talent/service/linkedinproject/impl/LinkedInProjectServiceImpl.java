package com.altomni.apn.talent.service.linkedinproject.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.company.CompanyBriefDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.constants.StatisticConstants;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Status;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Visibility;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinProject;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinProjectMember;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinProjectTalent;
import com.altomni.apn.talent.domain.linkedinproject.UserFavoriteLinkedinProject;
import com.altomni.apn.talent.domain.vm.linkedinproject.JobCompanyVM;
import com.altomni.apn.talent.repository.linkedinproject.LinkedinProjectMemberRepository;
import com.altomni.apn.talent.repository.linkedinproject.LinkedinProjectRepository;
import com.altomni.apn.talent.repository.linkedinproject.LinkedinProjectTalentRepository;
import com.altomni.apn.talent.repository.linkedinproject.UserFavoriteLinkedinProjectRepository;
import com.altomni.apn.talent.service.CompanyService;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.common.HttpService;
import com.altomni.apn.talent.service.dto.company.CompanyDTO;
import com.altomni.apn.talent.service.dto.linkedinproject.Filter;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectDTO;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectStatsDTO;
import com.altomni.apn.talent.service.dto.linkedinproject.SearchHistory;
import com.altomni.apn.talent.service.job.JobService;
import com.altomni.apn.talent.service.linkedinproject.LinkedInProjectService;
import com.altomni.apn.talent.service.record.impl.TalentTrackingBaseServiceImpl;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinProjectVO;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.altomni.apn.common.config.constants.RedisConstants.*;

@Slf4j
@Service
public class LinkedInProjectServiceImpl extends TalentTrackingBaseServiceImpl implements LinkedInProjectService {

    @Resource
    private HttpService httpService;

    @Resource
    private LinkedinProjectRepository linkedinProjectRepository;

    @Resource
    private LinkedinProjectMemberRepository linkedinProjectMemberRepository;

    @Resource
    private UserFavoriteLinkedinProjectRepository userFavoriteLinkedinProjectRepository;

    @Resource
    private LinkedinProjectTalentRepository linkedinProjectTalentRepository;

    @Resource
    private JobService jobService;

    @Resource
    private UserService userService;

    @Resource
    private CompanyService companyService;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    private static final int REDIS_NEW_LINKEDIN_PROJECT_REMINDER_EXP_TIME = 30 * 24 * 60 * 60; //30 days


    /*public LinkedInProjectServiceImpl(HttpService httpService, LinkedinProjectRepository linkedinProjectRepository, LinkedinProjectMemberRepository linkedinProjectMemberRepository,
                                      UserFavoriteLinkedinProjectRepository userFavoriteLinkedinProjectRepository, JobService jobService, UserService userService, CompanyService companyService) {
        this.httpService = httpService;
        this.linkedinProjectRepository = linkedinProjectRepository;
        this.linkedinProjectMemberRepository = linkedinProjectMemberRepository;
        this.userFavoriteLinkedinProjectRepository = userFavoriteLinkedinProjectRepository;
        this.jobService = jobService;
        this.userService = userService;
        this.companyService = companyService;
    }*/

    private void verifyTenant(Long tenantId) {
        if (!SecurityUtils.isCurrentTenant(tenantId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_VERIFYTENANT_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    @Override
    public HttpResponse saveFilters(Filter filter) throws IOException {
        filter.setUserId(SecurityUtils.getUserId());
        return httpService.post(applicationProperties.getStatisticUrl() + StatisticConstants.LINKEDIN_PROJECT_FILTERS_URL, filter.toJSON());
    }

    @Override
    public HttpResponse getFilters() throws IOException {
        return httpService.get(applicationProperties.getStatisticUrl() + StatisticConstants.LINKEDIN_PROJECT_FILTERS_GET_URL + SecurityUtils.getUserId());
    }

    @Override
    public HttpResponse deleteFilterById(String id) throws IOException {
        return httpService.delete(applicationProperties.getStatisticUrl() + StatisticConstants.LINKEDIN_PROJECT_FILTERS_BY_ID_URL + id);
    }

    @Override
    public HttpResponse deleteSearchHistoryById(String id) throws IOException {
        return httpService.delete(applicationProperties.getStatisticUrl() + StatisticConstants.LINKEDIN_PROJECT_SEARCH_HISTORY_BY_ID_URL + id);
    }

    @Override
    public HttpResponse saveSearchHistory(SearchHistory searchHistory) throws IOException {
        searchHistory.setUserId(SecurityUtils.getUserId());
        return httpService.post(applicationProperties.getStatisticUrl() + StatisticConstants.LINKEDIN_PROJECT_SEARCH_HISTORY_URL, searchHistory.toJSON());
    }

    @Override
    public HttpResponse getSearchHistories() throws IOException {
        return httpService.get(applicationProperties.getStatisticUrl() + StatisticConstants.LINKEDIN_PROJECT_SEARCH_HISTORY_GET_BY_USER_ID_URL + SecurityUtils.getUserId());
    }

    @Override
    public HttpResponse getSearchHistoryById(String id) throws IOException {
        return httpService.get(applicationProperties.getStatisticUrl() + StatisticConstants.LINKEDIN_PROJECT_SEARCH_HISTORY_BY_ID_URL + id);
    }

    @Override
    public LinkedinProjectVO saveLinkedinProject(LinkedinProjectDTO linkedinProjectDto) {
        // 手动控制事务
        TransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        LinkedinProject project;
        try {
            LinkedinProject linkedinProject = new LinkedinProject();
            ServiceUtils.myCopyProperties(linkedinProjectDto, linkedinProject);
            linkedinProject.setTenantId(SecurityUtils.getTenantId());
            linkedinProject.setCreatedUserId(SecurityUtils.getUserId());
            linkedinProject.setStatus(Status.ACTIVE);
            linkedinProject.setVisibility(linkedinProjectDto.getVisibility() != null ? linkedinProjectDto.getVisibility() : Visibility.VISIBLE_TO_ALL);
            project = linkedinProjectRepository.save(linkedinProject);
            saveLinkedinProjectMember(project.getId(), linkedinProjectDto.getMembers());
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
        cacheNewLinkedinProjectReminderToRedis(project);
        return toDto(project);
    }

    private void cacheNewLinkedinProjectReminderToRedis(LinkedinProject linkedinProject) {
        try {
            Set<Long> userIds = new HashSet<>();
            if (Visibility.VISIBLE_TO_PROJECT_MEMBERS.equals(linkedinProject.getVisibility())) {
                userIds = linkedinProjectMemberRepository.findByLinkedinProjectId(linkedinProject.getId()).stream().map(LinkedinProjectMember::getUserId).collect(Collectors.toSet());
            } else {
                userIds = userService.getAllBriefUsers().getBody().stream().map(UserBriefDTO::getId).collect(Collectors.toSet());
            }

            String key = String.format(LINKEDIN_PROJECT_NEW_PROJECT_KEY_PATTERN, linkedinProject.getId());

            commonRedisService.sadd(key, userIds, REDIS_NEW_LINKEDIN_PROJECT_REMINDER_EXP_TIME);
        } catch (Exception e) {
            log.error("[LinkedinProjectService: cacheNewLinkedinProjectReminderToRedis] error msg: {}", e.getMessage(), e);
        }
    }

    private LinkedinProjectVO toDto(Long id) {
        return toDto(findOne(id));
    }

    private LinkedinProjectVO toDto(LinkedinProject linkedinProject) {
        LinkedinProjectVO result = LinkedinProjectVO.fromLinkedinProject(linkedinProject);

        Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<JobDTOV3> jobFuture = linkedinProject.getJobId() == null ? CompletableFuture.supplyAsync(() -> null)
            : CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return jobService.findByIdNoToken(linkedinProject.getJobId()).getBody();
        }, executor).thenApplyAsync(job -> {
            SecurityUtils.setAuthentication(authentication);
            if (job == null) {
                return null;
            }
            CompanyDTO company = companyService.findById(job.getCompanyId()).getBody();
            if (company != null) {
                CompanyBriefDTO companyBriefDTO = new CompanyBriefDTO();
                ServiceUtils.myCopyProperties(company, companyBriefDTO);
                job.setCompany(companyBriefDTO);
            }
            return job;
        }, executor);

        CompletableFuture<List<LinkedinProjectMember>> projectMembersFuture = CompletableFuture.supplyAsync(() -> linkedinProjectMemberRepository.findByLinkedinProjectId(linkedinProject.getId()), executor)
            .thenApplyAsync(projectMembers -> {
                SecurityUtils.setAuthentication(authentication);
                if (CollectionUtils.isEmpty(projectMembers)) {
                    return Collections.emptyList();
                }
                List<Long> userIdList = projectMembers.stream().map(LinkedinProjectMember::getUserId).collect(Collectors.toList());
                List<User> userList = userService.findByIds(userIdList).getBody();
                if (CollectionUtils.isEmpty(userList)) {
                    return Collections.emptyList();
                }
                Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, User -> User));
                projectMembers.stream().filter(member -> userMap.containsKey(member.getUserId()))
                    .forEach(linkedinProjectMember -> linkedinProjectMember.setUser(userMap.get(linkedinProjectMember.getUserId())));
                return projectMembers;
            }, executor);

        var favoriteFuture = CompletableFuture.supplyAsync(() ->
            userFavoriteLinkedinProjectRepository.findByLinkedinProjectIdAndUserId(linkedinProject.getId(), SecurityUtils.getUserId()) != null, executor);

        CompletableFuture<LinkedinProjectStatsDTO> statsFuture = CompletableFuture.supplyAsync(() -> {
            LinkedinProjectStatsDTO dto = linkedinProjectTalentRepository.countStatsByTenantAndProject(
                    SecurityUtils.getTenantId(), linkedinProject.getId()
            );
            if (Objects.isNull(dto)) {
                dto = new LinkedinProjectStatsDTO();
            }
            if (Objects.isNull(dto.getTotalCandidatesCount())) {
                dto.setTotalCandidatesCount(0L);
            }
            if (Objects.isNull(dto.getCandidatesWithContactInfoCount())) {
                dto.setCandidatesWithContactInfoCount(0L);
            }
            if (Objects.isNull(dto.getCandidatesInApplicationCount())) {
                dto.setCandidatesInApplicationCount(0L);
            }
            return dto;
        }, executor);

        CompletableFuture.allOf(jobFuture, projectMembersFuture, favoriteFuture, statsFuture)
            .exceptionally(t -> {
                log.error("Error occurred when fetching linkinProject data: ", t);
                throw new RuntimeException("Error occurred when fetching linkinProject  data ");
            }).join();

        result.setJob(jobFuture.join());
        result.setMembers(projectMembersFuture.join());
        result.setFavorite(favoriteFuture.join());

        LinkedinProjectStatsDTO statsDTO = statsFuture.join();
        result.setTotalCandidatesCount(statsDTO.getTotalCandidatesCount());
        result.setCandidatesWithContactInfoCount(statsDTO.getCandidatesWithContactInfoCount());
        result.setCandidatesInApplicationCount(statsDTO.getCandidatesInApplicationCount());

        String newProjectRedisKey = String.format(LINKEDIN_PROJECT_NEW_PROJECT_KEY_PATTERN, linkedinProject.getId());
        boolean isNewLinkedinProject = commonRedisService.sismember(newProjectRedisKey, SecurityUtils.getUserId());
        result.setNewLinkedinProject(isNewLinkedinProject);

        return result;
    }

    private void toDto(List<LinkedinProjectVO> linkedinProjects) {
        Map<Long, LinkedinProjectVO> linkedinProjectMap = linkedinProjects.stream().collect(Collectors.toMap(LinkedinProjectVO::getId, LinkedinProjectVO -> LinkedinProjectVO));

        List<Long> jobIdList = linkedinProjects.stream().map(LinkedinProjectVO::getJobId).collect(Collectors.toList());
        List<Long> linkedInProjectIdList = linkedinProjects.stream().map(LinkedinProjectVO::getId).collect(Collectors.toList());

        List<JobCompanyVM> jobCompanyList = searchJobCompanyByIds(jobIdList);
        Map<Long, JobCompanyVM> jobCompanyMap = jobCompanyList.stream().collect(Collectors.toMap(JobCompanyVM::getId, JobCompanyVM -> JobCompanyVM));

        List<LinkedinProjectMember> linkedinProjectMembers = linkedinProjectMemberRepository.findByLinkedinProjectIdIn(linkedInProjectIdList);
        if (CollectionUtils.isNotEmpty(linkedinProjectMembers)) {
            List<Long> userIdList = linkedinProjectMembers.stream().map(LinkedinProjectMember::getUserId).collect(Collectors.toList());
            List<User> userList = userService.findByIds(userIdList).getBody();
            if (CollectionUtils.isNotEmpty(userList)) {
                Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, User -> User));
                linkedinProjectMembers.forEach(linkedinProjectMember -> {
                    if (userMap.containsKey(linkedinProjectMember.getUserId())) {
                        linkedinProjectMember.setUser(userMap.get(linkedinProjectMember.getUserId()));
                    }
                });
            }
        }

        Map<Long, List<LinkedinProjectMember>> linkedinProjectMembersMap = linkedinProjectMembers.stream().collect(Collectors.groupingBy(LinkedinProjectMember::getLinkedinProjectId));

        List<UserFavoriteLinkedinProject> userFavoriteLinkedinProjectList = userFavoriteLinkedinProjectRepository.findByLinkedinProjectIdInAndUserId(linkedInProjectIdList, SecurityUtils.getUserId());
        Map<Long, UserFavoriteLinkedinProject> userFavoriteLinkedinProjectMap = userFavoriteLinkedinProjectList.stream().collect(Collectors.toMap(UserFavoriteLinkedinProject::getLinkedinProjectId, UserFavoriteLinkedinProject -> UserFavoriteLinkedinProject));

        List<LinkedinProjectStatsDTO> linkedinProjectStatsList = linkedinProjectTalentRepository.countStatsByTenantAndProjectIds(SecurityUtils.getTenantId(), linkedInProjectIdList);
        Map<Long, LinkedinProjectStatsDTO> linkedinProjectStatsMap = linkedinProjectStatsList.stream().collect(Collectors.toMap(LinkedinProjectStatsDTO::getLinkedinProjectId, Function.identity()));


        Set<Long> privateJobIds = linkedinProjectRepository.filterPrivateJob(SecurityUtils.getTenantId(), jobIdList);
        linkedinProjects.forEach(o -> {
            if (linkedinProjectMap.containsKey(o.getId())) {
                Long jobId = linkedinProjectMap.get(o.getId()).getJobId();
                if (jobCompanyMap.containsKey(jobId)) {
                    JobCompanyVM jobCompanyVM = jobCompanyMap.get(jobId);
                    JobDTOV3 job = new JobDTOV3();
                    ServiceUtils.myCopyProperties(jobCompanyVM, job);
                    CompanyBriefDTO companyBriefDTO = new CompanyBriefDTO();
                    companyBriefDTO.setId(jobCompanyVM.getCompanyId());
                    companyBriefDTO.setName(jobCompanyVM.getCompanyName());
                    job.setCompany(companyBriefDTO);
                    job.setIsPrivateJob(privateJobIds.contains(jobId));
                    o.setJob(job);
                }
            }

            if (linkedinProjectMembersMap.containsKey(o.getId())) {
                o.setMembers(linkedinProjectMembersMap.get(o.getId()));
            }

            if (userFavoriteLinkedinProjectMap.containsKey(o.getId())) {
                o.setFavorite(Boolean.TRUE);
            }

            if (linkedinProjectStatsMap.containsKey(o.getId())) {
                LinkedinProjectStatsDTO linkedinProjectStatsDTO = linkedinProjectStatsMap.get(o.getId());
                o.setTotalCandidatesCount(linkedinProjectStatsDTO.getTotalCandidatesCount());
                o.setCandidatesWithContactInfoCount(linkedinProjectStatsDTO.getCandidatesWithContactInfoCount());
                o.setCandidatesInApplicationCount(linkedinProjectStatsDTO.getCandidatesInApplicationCount());
            }

            String redisKey = String.format(LINKEDIN_PROJECT_NEW_TALENT_KEY_PATTERN, o.getId());
            boolean hasNewTalent = commonRedisService.sismember(redisKey, SecurityUtils.getUserId());
            o.setHasNewLinkedinTalent(hasNewTalent);

            String newProjectRedisKey = String.format(LINKEDIN_PROJECT_NEW_PROJECT_KEY_PATTERN, o.getId());
            boolean isNewLinkedinProject = commonRedisService.sismember(newProjectRedisKey, SecurityUtils.getUserId());
            o.setNewLinkedinProject(isNewLinkedinProject);
        });
    }


    @Override
    public LinkedinProjectVO updateLinkedinProject(LinkedinProjectDTO linkedinProjectDto) {
        LinkedinProject linkedinProject = findOne(linkedinProjectDto.getId());
        Status oldStatus = linkedinProject.getStatus();
        Visibility oldVisibility = linkedinProject.getVisibility();
        verifyTenant(linkedinProject.getTenantId());
        ServiceUtils.myCopyProperties(linkedinProjectDto, linkedinProject);
        linkedinProject.setSystemGenerated(linkedinProject.getSystemGenerated());
        linkedinProject.setDescription(linkedinProjectDto.getDescription());
        linkedinProject.setJobId(linkedinProjectDto.getJobId());
        linkedinProject.setStatus(linkedinProject.getStatus() == null ? oldStatus : linkedinProject.getStatus());
        linkedinProject.setVisibility(linkedinProjectDto.getVisibility() == null ? oldVisibility : linkedinProject.getVisibility());
        linkedinProjectRepository.saveAndFlush(linkedinProject);
        if (CollectionUtils.isNotEmpty(linkedinProjectDto.getMembers())) {
            linkedinProjectMemberRepository.deleteByLinkedinProjectId(linkedinProject.getId());
            saveLinkedinProjectMember(linkedinProject.getId(), linkedinProjectDto.getMembers());
        } else {
            linkedinProjectMemberRepository.deleteByLinkedinProjectId(linkedinProject.getId());
        }
        cacheNewLinkedinProjectReminderToRedis(linkedinProject);
        return toDto(linkedinProject);
    }

    private LinkedinProject findOne(Long id) {
        LinkedinProject linkedinProject = linkedinProjectRepository.findById(id).orElse(null);
        if (linkedinProject == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_FINDONE_NOTFOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(id),talentApiPromptProperties.getTalentService()));
        }
        return linkedinProject;
    }

    private void saveLinkedinProjectMember(Long linkedinProjectId, List<LinkedinProjectMember> linkedinProjectMembers) {
        if (CollectionUtils.isNotEmpty(linkedinProjectMembers)) {
            List<LinkedinProjectMember> result = new ArrayList<>();
            linkedinProjectMembers.forEach(linkedinProjectMember -> {
                linkedinProjectMember.setLinkedinProjectId(linkedinProjectId);
                result.add(linkedinProjectMember);
            });
            linkedinProjectMemberRepository.saveAll(result);
        }
    }

    /**
     * All public projects and
     *
     * @return project list
     */
    @Override
    public Page<LinkedinProjectVO> findAll(BooleanExpression allLinkedinProject, Pageable pageable) {
        PageRequest pageRequest = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());
        Page<LinkedinProjectVO> page = linkedinProjectRepository.findAll(allLinkedinProject, pageRequest).map(LinkedinProjectVO::fromLinkedinProject);
        toDto(page.getContent());
        return page;
    }

    @Override
    public LinkedinProjectVO favoriteLinkedinProject(Long id) {
        // 手动控制事务
        TransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        try {
            findOne(id);
            UserFavoriteLinkedinProject exist = userFavoriteLinkedinProjectRepository.findByLinkedinProjectIdAndUserId(id, SecurityUtils.getUserId());
            if (exist == null) {
                userFavoriteLinkedinProjectRepository.save(new UserFavoriteLinkedinProject().userId(SecurityUtils.getUserId()).linkedinProjectId(id));
            }
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
        return toDto(id);
    }

    @Override
    public LinkedinProjectVO getLinkedinProjectById(Long id) {
        LinkedinProject linkedinProject = findOne(id);
        verifyTenant(linkedinProject.getTenantId());
        String redisKey = String.format(LINKEDIN_PROJECT_NEW_TALENT_KEY_PATTERN, linkedinProject.getId());
        commonRedisService.srem(redisKey, SecurityUtils.getUserId());
        String newProjectRedisKey = String.format(LINKEDIN_PROJECT_NEW_PROJECT_KEY_PATTERN, linkedinProject.getId());
        commonRedisService.srem(newProjectRedisKey, SecurityUtils.getUserId());
        return toDto(linkedinProject);
    }

    @Override
    public LinkedinProjectVO unFavoriteLinkedinProject(Long id) {
        // 手动控制事务
        TransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        try {
            findOne(id);
            UserFavoriteLinkedinProject exist = userFavoriteLinkedinProjectRepository.findByLinkedinProjectIdAndUserId(id, SecurityUtils.getUserId());
            if (exist != null) {
                userFavoriteLinkedinProjectRepository.delete(exist);
            }
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
        return toDto(id);
    }

    private List<JobCompanyVM> searchJobCompanyByIds(List<Long> jobIdList) {
        if (CollectionUtils.isEmpty(jobIdList)) {
            return new ArrayList<>();
        }

        String selectSql ="SELECT j.id, j.company_id, j.title, rp.job_type, j.`status`, c.`full_business_name` company_name, j.created_by, j.created_date, j.last_modified_by, j.last_modified_date FROM job j LEFT JOIN recruitment_process rp on rp.id = j.recruitment_process_id LEFT JOIN company c ON j.company_id = c.id WHERE j.id IN ?1";
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, jobIdList);
        List<JobCompanyVM> jobCompanyList = searchData(selectSql, JobCompanyVM.class, paramMap);
        if (CollectionUtils.isEmpty(jobCompanyList)) {
            return new ArrayList<>();
        }
        return jobCompanyList;
    }
}
