package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.GroupMemberStatus;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingGroupStatus;
import com.altomni.apn.talent.domain.linkedin.LinkedinAccountInfo;
import com.altomni.apn.talent.domain.linkedin.LinkedinAssistantEventTracking;
import com.altomni.apn.talent.domain.linkedin.TalentTrackingLinkedinPendingGroup;
import com.altomni.apn.talent.domain.linkedin.TalentTrackingLinkedinPendingGroupMember;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroupMember;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinPending;
import com.altomni.apn.talent.repository.linkedin.LinkedinAccountInfoRepository;
import com.altomni.apn.talent.repository.linkedin.LinkedinAssistantEventTrackingRepository;
import com.altomni.apn.talent.repository.linkedin.TalentTrackingLinkedinPendingGroupMemberRepository;
import com.altomni.apn.talent.repository.linkedin.TalentTrackingLinkedinPendingGroupRepository;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinGroupMemberRepository;
import com.altomni.apn.talent.repository.tracking.TalentTrackingLinkedinPendingRepository;
import com.altomni.apn.talent.service.talent.LinkedinAccountInfoService;
import com.altomni.apn.talent.service.vo.tracking.LinkedinTalentBasicInfo;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingGroupVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingVO;
import com.altomni.apn.talent.web.rest.talent.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.ws.rs.ForbiddenException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing LinkedIn account information.
 */
@Service
@Slf4j
public class LinkedinAccountInfoServiceImpl implements LinkedinAccountInfoService {

    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private TalentTrackingLinkedinGroupMemberRepository talentTrackingLinkedinGroupMemberRepository;

    @Resource
    private LinkedinAccountInfoRepository linkedinAccountInfoRepository;

    @Resource
    private LinkedinAssistantEventTrackingRepository linkedinAssistantEventTrackingRepository;

    @Resource
    private TalentTrackingLinkedinPendingGroupRepository pendingGroupRepository;

    @Resource
    private TalentTrackingLinkedinPendingGroupMemberRepository pendingGroupMemberRepository;

    @Resource
    private TalentTrackingLinkedinPendingRepository pendingRepository;

    @Resource
    private TalentTrackingLinkedinGroupMemberRepository friendGroupMemberRepository;

    @Override
    @Transactional(readOnly = true)
    public LinkedinAccountInfoResponse getLinkedinAccountInfo(String linkedinId) {
        log.debug("Request to get LinkedIn account info for linkedinId: {}", linkedinId);
        
        Optional<LinkedinAccountInfo> accountInfoOpt = linkedinAccountInfoRepository.findByLinkedinId(linkedinId);
        
        LinkedinAccountInfoResponse response = new LinkedinAccountInfoResponse();
        if (accountInfoOpt.isPresent()) {
            LinkedinAccountInfo accountInfo = accountInfoOpt.get();
            response.setFriendCount(accountInfo.getFriendCount() != null ? accountInfo.getFriendCount().intValue() : null);
            
            if (accountInfo.getMatchFriendDatetime() != null) {
                Instant matchFriendDatetime = accountInfo.getMatchFriendDatetime();
                String formattedDate = DateTimeFormatter
                        .ofPattern("yyyy-MM-dd HH:mm:ss")
                        .format(LocalDateTime.ofInstant(matchFriendDatetime, ZoneOffset.UTC));
                response.setMatchFriendDatetime(formattedDate);
            }
        }
        
        return response;
    }

    @Override
    public void saveLinkedinAccountInfo(LinkedinAccountInfoRequest request) {
        log.debug("Request to save LinkedIn account info: {}", request);
        
        Optional<LinkedinAccountInfo> existingOpt = linkedinAccountInfoRepository.findByLinkedinId(request.getLinkedinId());
        
        LinkedinAccountInfo accountInfo;
        if (existingOpt.isPresent()) {
            accountInfo = existingOpt.get();
        } else {
            accountInfo = new LinkedinAccountInfo();
            accountInfo.setLinkedinId(request.getLinkedinId());
        }
        
        accountInfo.setFriendCount(request.getFriendCount() != null ? request.getFriendCount().longValue() : null);
        
        // 解析时间字符串
        if (request.getMatchFriendDatetime() != null) {
            try {
                LocalDateTime dateTime = LocalDateTime.parse(request.getMatchFriendDatetime(), DATETIME_FORMATTER);
                accountInfo.setMatchFriendDatetime(dateTime.atZone(ZoneOffset.UTC).toInstant());
            } catch (Exception e) {
                log.warn("Failed to parse matchFriendDatetime: {}, using current time", request.getMatchFriendDatetime());
                accountInfo.setMatchFriendDatetime(Instant.now());
            }
        }
        linkedinAccountInfoRepository.save(accountInfo);
        log.debug("Saved LinkedIn account info for linkedinId: {}", request.getLinkedinId());
    }

    @Override
    public void addEventTracking(LinkedinEventTrackingRequest request) {
        if (request.getEventTrackingList() == null || request.getEventTrackingList().isEmpty()) {
            log.warn("Event tracking list is empty, skipping save operation");
            return;
        }

        List<LinkedinAssistantEventTracking> trackingEntities = new ArrayList<>();

        for (LinkedinEventTrackingRequest.EventTrackingItem item : request.getEventTrackingList()) {
            LinkedinAssistantEventTracking tracking = new LinkedinAssistantEventTracking();
            tracking.setEventName(item.getEvent());
            tracking.setOperatorLinkedinId(item.getOperatorLinkedinId());
            tracking.setLinkedinId(item.getLinkedinId());
            tracking.setDetail(item.getDetail());

            // 解析事件时间
            try {
                // 支持 ISO 8601 格式: 2025-07-08T05:17:09.142Z
                Instant eventTime = Instant.parse(item.getEventDateTime());
                tracking.setEventDatetime(eventTime);
            } catch (Exception e) {
                log.warn("Failed to parse event datetime: {}, using current time", item.getEventDateTime());
                tracking.setEventDatetime(Instant.now());
            }

            trackingEntities.add(tracking);
        }

        // 批量保存
        linkedinAssistantEventTrackingRepository.saveAll(trackingEntities);
        log.debug("Successfully saved {} event tracking records", trackingEntities.size());
    }

    //领英人员待加列表分组查重
    private void checkDuplicateGroup(String name, String operatorId, Long id) {
        // 查询状态为 active 的成员分组，检查该用户是否有重名的成员分组
        List<TalentTrackingLinkedinPendingGroup> talentTrackingLinkedinGroupList = pendingGroupRepository
                .findAllByTenantIdAndOperatorLinkedinIdAndStatusAndName(SecurityUtils.getTenantId(), operatorId, TrackingGroupStatus.ACTIVE, name);

        if (CollUtil.isNotEmpty(talentTrackingLinkedinGroupList) && talentTrackingLinkedinGroupList.stream()
                .noneMatch(announcement -> Objects.equals(announcement.getId(), id))) {
            throw new CustomParameterizedException("This member group already exists.");
        }
    }

    @Override
    public TalentTrackingGroupVO linkedinAssistantService(LinkedinPendingGroupRequest talentTrackingGroupDTO) {
        checkDuplicateGroup(talentTrackingGroupDTO.getName(), talentTrackingGroupDTO.getOperatorLinkedinId(), null);
        TalentTrackingLinkedinPendingGroup talentTrackingLinkedinGroup = TalentTrackingLinkedinPendingGroup.fromTalentTrackingGroupDTO(talentTrackingGroupDTO);
        talentTrackingLinkedinGroup.setStatus(TrackingGroupStatus.ACTIVE);
        talentTrackingLinkedinGroup.setTenantId(SecurityUtils.getTenantId());
        talentTrackingLinkedinGroup = pendingGroupRepository.save(talentTrackingLinkedinGroup);
        int memberCount = saveGroupMember(talentTrackingGroupDTO.getMembers(), talentTrackingLinkedinGroup.getId());

        TalentTrackingGroupVO talentTrackingGroupVO = TalentTrackingLinkedinPendingGroup.toTalentTrackingGroupVO(talentTrackingLinkedinGroup);
        talentTrackingGroupVO.setMemberCount(memberCount);
        return talentTrackingGroupVO;
    }

    @Override
    public TalentTrackingGroupVO updateGroup(Long id, String name) {
        TalentTrackingLinkedinPendingGroup talentTrackingLinkedinGroup = pendingGroupRepository.findById(id).orElseThrow(() -> new NotFoundException("This member group does not exist."));
        if (!talentTrackingLinkedinGroup.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("This member group does not exist.");
        }
        checkDuplicateGroup(name, talentTrackingLinkedinGroup.getOperatorLinkedinId(), id);
        talentTrackingLinkedinGroup.setName(name);
        pendingGroupRepository.save(talentTrackingLinkedinGroup);
        return toTalentTrackingGroupVO(talentTrackingLinkedinGroup);
    }

    @Override
    public void inactiveGroup(Long id) {
        TalentTrackingLinkedinPendingGroup talentTrackingLinkedinGroup = pendingGroupRepository.findById(id).orElseThrow(() -> new NotFoundException("This member group does not exist."));
        if (!talentTrackingLinkedinGroup.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("This member group does not exist.");
        }

        talentTrackingLinkedinGroup.setStatus(TrackingGroupStatus.IN_ACTIVE);
        pendingGroupRepository.save(talentTrackingLinkedinGroup);
    }

    @Override
    public void saveGroupMember(SavePendingGroupMemberRequestDTO requestDTO) {
        List<Long> groupIds = requestDTO.getGroupIds();
        List<Long> membersIds = requestDTO.getMembers();

        List<TalentTrackingLinkedinPendingGroupMember> existedMemberList = pendingGroupMemberRepository.findAllByGroupIdInAndPendingIdIn(groupIds, membersIds);
        Map<Long, Set<Long>> existedMemberMap = existedMemberList.stream()
                .collect(Collectors.groupingBy(
                        TalentTrackingLinkedinPendingGroupMember::getGroupId,
                        Collectors.mapping(TalentTrackingLinkedinPendingGroupMember::getPendingId, Collectors.toSet())
                ));

        List<TalentTrackingLinkedinPendingGroupMember> addMemberList = new ArrayList<>();
        groupIds.forEach(groupId -> {
            Set<Long> itemExistedMembers = existedMemberMap.getOrDefault(groupId, new HashSet<>());
            addMemberList.addAll(membersIds.stream().filter(item -> !itemExistedMembers.contains(item)).map(o -> TalentTrackingLinkedinPendingGroupMember.fromTalentTrackingGroupMemberDTO(o, groupId)).toList());
        });
        pendingGroupMemberRepository.saveAll(addMemberList);
    }

    @Override
    public Page<TalentTrackingLinkedinPendingGroup> searchGroup(String operatorLinkedinId, Pageable pageable) {
        return pendingGroupRepository.findAllByTenantIdAndOperatorLinkedinIdAndStatus(SecurityUtils.getTenantId(), operatorLinkedinId, TrackingGroupStatus.ACTIVE, pageable);
    }

    @Override
    public List<TalentTrackingGroupVO> toTalentTrackingGroupVOList(List<TalentTrackingLinkedinPendingGroup> talentTrackingLinkedinGroupList) {
        List<TalentTrackingGroupVO> talentTrackingGroupVOList = talentTrackingLinkedinGroupList.stream().map(TalentTrackingLinkedinPendingGroup::toTalentTrackingGroupVO).toList();

        List<TalentTrackingVO> talentTrackingVOList = getGroupMemberInfo(talentTrackingLinkedinGroupList.stream().map(TalentTrackingLinkedinPendingGroup::getId).distinct().toList());
        Map<Long, List<TalentTrackingVO>> talentTrackingVOMap = talentTrackingVOList.stream().collect(Collectors.groupingBy(TalentTrackingVO::getGroupId));
        talentTrackingGroupVOList.forEach(item -> {
            List<TalentTrackingVO> itemList = talentTrackingVOMap.getOrDefault(item.getId(), new ArrayList<>());
            item.setMembers(itemList);
            item.setMemberCount(itemList.size());
        });
        return talentTrackingGroupVOList;
    }

    @Override
    public List<TalentTrackingVO> queryGroupMember(Long id) {
        TalentTrackingLinkedinPendingGroup talentTrackingLinkedinGroup = pendingGroupRepository.findById(id).orElseThrow(() -> new NotFoundException("This member group does not exist."));
        if (!talentTrackingLinkedinGroup.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("This member group does not exist.");
        }
        return getGroupMemberInfo(List.of(id));
    }

    @Override
    public List<TalentTrackingGroupVO> queryGroupByOperatorLinkedinId(String operatorLinkedinId) {
        List<TalentTrackingLinkedinPendingGroup> talentTrackingLinkedinGroupList = pendingGroupRepository.findAllByTenantIdAndOperatorLinkedinIdAndStatusOrderByCreatedDateDesc(SecurityUtils.getTenantId(), operatorLinkedinId, TrackingGroupStatus.ACTIVE);
        return toTalentTrackingGroupVOList(talentTrackingLinkedinGroupList);
    }

    @Override
    public void addFriendNotify(AddFriendNotifyRequestDTO requestDTO) {
        String operatorLinkedinId = requestDTO.getOperatorLinkedinId();
        List<String> linkedinIdList = requestDTO.getLinkedinIdList();
        if(StringUtils.isEmpty(operatorLinkedinId)) {
            return;
        }
        if(linkedinIdList == null || linkedinIdList.isEmpty()) {
            return;
        }
        List<TalentTrackingLinkedinGroupMember> waitingGroupMember = friendGroupMemberRepository.findWaitingGroupMember(operatorLinkedinId, linkedinIdList);
        waitingGroupMember.forEach(c -> c.setStatus(GroupMemberStatus.NORMAL));
        friendGroupMemberRepository.saveAll(waitingGroupMember);
    }

    @Override
    public GetWaitAddToGroupResponseDTO getWaitAddToGroup(GetWaitAddToGroupRequestDTO requestDTO) {
        String operatorLinkedinId = requestDTO.getOperatorLinkedinId();
        if(StringUtils.isEmpty(operatorLinkedinId)) {
            return new GetWaitAddToGroupResponseDTO();
        }
        List<String> waitingGroupMemberByOperator = friendGroupMemberRepository.findWaitingGroupMemberByOperator(operatorLinkedinId);
        GetWaitAddToGroupResponseDTO ret = new GetWaitAddToGroupResponseDTO();
        ret.setLinkedinIdList(waitingGroupMemberByOperator);
        return ret;
    }

    @Override
    public void notifyAddToFriendGroup(NotifyAddToFriendGroupRequestDTO requestDTO) {
        List<Long> groupIdList = requestDTO.getGroupIdList();
        if(CollUtil.isEmpty(groupIdList)) {
            return;
        }
        List<LinkedinTalentBasicInfo> talentLinkedinInfoList = requestDTO.getTalentLinkedinInfoList();
        if(CollUtil.isEmpty(talentLinkedinInfoList)) {
            return;
        }
        List<TalentTrackingLinkedinGroupMember> existedMemberList = talentTrackingLinkedinGroupMemberRepository.findAllByGroupIdInAndTalentLinkedinIdIn(groupIdList, talentLinkedinInfoList.stream().map(LinkedinTalentBasicInfo::getTalentLinkedinId).toList());
        Map<Long, Set<String>> existedMemberMap = existedMemberList.stream()
                .collect(Collectors.groupingBy(
                        TalentTrackingLinkedinGroupMember::getGroupId,
                        Collectors.mapping(TalentTrackingLinkedinGroupMember::getTalentLinkedinId, Collectors.toSet())
                ));
        Long tenantId = SecurityUtils.getTenantId();
        List<TalentTrackingLinkedinGroupMember> addMemberList = new ArrayList<>();
        groupIdList.forEach(groupId -> {
            Set<String> itemExistedMembers = existedMemberMap.getOrDefault(groupId, new HashSet<>());
            addMemberList.addAll(talentLinkedinInfoList.stream().filter(item -> !itemExistedMembers.contains(item.getTalentLinkedinId())).map(o -> TalentTrackingLinkedinGroupMember.fromTalentTrackingGroupMemberDTO(o, tenantId, groupId, GroupMemberStatus.WAITING)).toList());
        });
        talentTrackingLinkedinGroupMemberRepository.saveAll(addMemberList);
    }

    private List<TalentTrackingVO> getGroupMemberInfo(List<Long> groupIds) {
        List<TalentTrackingLinkedinPendingGroupMember> talentTrackingLinkedinGroupMemberList = pendingGroupMemberRepository.findAllByGroupIdIn(groupIds);
        List<Long> pendingIds = talentTrackingLinkedinGroupMemberList.stream().map(TalentTrackingLinkedinPendingGroupMember::getPendingId).distinct().toList();
        Map<Long, TalentTrackingLinkedinPending> pendingTalentsMap = pendingRepository.findAllById(pendingIds).stream()
                .collect(Collectors.toMap(
                        TalentTrackingLinkedinPending::getId,  // 键映射函数
                        talent -> talent,                       // 值映射函数
                        (existing, replacement) -> replacement  // 合并函数，这里选择保留后面的元素
                ));

        return talentTrackingLinkedinGroupMemberList.stream().map(gm -> {
            TalentTrackingVO talentTrackingVO = new TalentTrackingVO();
            TalentTrackingLinkedinPending talentTrackingLinkedinPending = pendingTalentsMap.get(gm.getPendingId());
            talentTrackingVO.setFirstName(talentTrackingLinkedinPending.getFirstName());
            talentTrackingVO.setLastName(talentTrackingLinkedinPending.getLastName());
            talentTrackingVO.setPhotoUrl(talentTrackingLinkedinPending.getPhotoUrl());
            talentTrackingVO.setTitle(talentTrackingLinkedinPending.getTitle());
            talentTrackingVO.setTalentLinkedinId(talentTrackingLinkedinPending.getTalentLinkedinId());
            talentTrackingVO.setOperatorLinkedinId(talentTrackingLinkedinPending.getOperatorLinkedinId());
            talentTrackingVO.setLinkUrl(talentTrackingLinkedinPending.getLinkUrl());
            talentTrackingVO.setLastInteractionTime(talentTrackingLinkedinPending.getLastInteractionTime());
            talentTrackingVO.setCategory(talentTrackingLinkedinPending.getCategory());
            talentTrackingVO.setStatus(talentTrackingLinkedinPending.getStatus());

            talentTrackingVO.setGroupId(gm.getGroupId());
            return talentTrackingVO;
        }).toList();
    }

    private TalentTrackingGroupVO toTalentTrackingGroupVO(TalentTrackingLinkedinPendingGroup talentTrackingLinkedinGroup) {
        TalentTrackingGroupVO talentTrackingGroupVO = TalentTrackingLinkedinPendingGroup.toTalentTrackingGroupVO(talentTrackingLinkedinGroup);
        int memberCount = pendingGroupMemberRepository.countDistinctByGroupId(talentTrackingGroupVO.getId());
        talentTrackingGroupVO.setMemberCount(memberCount);
        return talentTrackingGroupVO;
    }

    private int saveGroupMember(List<Long> pendingIds, Long groupId) {
        if (CollUtil.isEmpty(pendingIds)) {
            return 0;
        }
        List<TalentTrackingLinkedinPendingGroupMember> list = pendingIds.stream().map(i -> {
            TalentTrackingLinkedinPendingGroupMember groupMember = new TalentTrackingLinkedinPendingGroupMember();
            groupMember.setGroupId(groupId);
            groupMember.setPendingId(i);
            return groupMember;
        }).toList();
        pendingGroupMemberRepository.saveAll(list);

        return list.size();
    }
}