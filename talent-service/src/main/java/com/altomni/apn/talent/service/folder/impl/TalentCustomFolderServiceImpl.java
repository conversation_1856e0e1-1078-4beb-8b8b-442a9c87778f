package com.altomni.apn.talent.service.folder.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.folder.*;
import com.altomni.apn.common.dto.search.SearchConditionDTO;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.folder.CustomFolderSharingTargetCategory;
import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.folder.*;
import com.altomni.apn.talent.repository.folder.*;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.dto.folder.FolderEmailRequestDTO;
import com.altomni.apn.talent.service.dto.folder.TalentFolderDTO;
import com.altomni.apn.talent.service.dto.folder.TalentFolderRelationListDTO;
import com.altomni.apn.talent.service.dto.folder.TalentFolderSharingTeamDTO;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.folder.TalentCustomFolderService;
import com.altomni.apn.talent.service.folder.TalentSearchFolderSearchParamService;
import com.altomni.apn.talent.service.folder.TalentSharedCustomFolderService;
import com.altomni.apn.talent.service.mapper.folder.TalentFolderMapper;
import com.altomni.apn.talent.service.mapper.folder.TalentFolderSharingTeamMapper;
import com.altomni.apn.talent.service.mapper.folder.TalentFolderSharingUserMapper;
import com.altomni.apn.talent.service.talent.TalentSyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.remoting.RemoteAccessException;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class TalentCustomFolderServiceImpl implements TalentCustomFolderService {

    @Resource
    private UserService userService;

    @Resource
    private TalentFolderRepository talentFolderRepository;

    @Resource
    private TalentFolderDetailRepository talentFolderDetailRepository;

    @Resource
    private TalentFolderRelationRepository talentFolderRelationRepository;


    @Resource
    private TalentFolderSharingTeamRepository talentFolderSharingTeamRepository;

    @Resource
    private TalentFolderSharingUserRepository talentFolderSharingUserRepository;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private TalentFolderMapper talentFolderMapper;

    @Resource
    private TalentFolderSharingUserMapper talentFolderSharingUserMapper;

    @Resource
    private TalentFolderSharingTeamMapper talentFolderSharingTeamMapper;

    @Resource
    private TalentSearchFolderSearchParamService talentSearchFolderSearchParamService;

    @Resource
    private TalentSharedCustomFolderService talentSharedCustomFolderService;

    @Resource
    private EsFillerTalentService esFillerTalentService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;


    @Override
    public Page<TalentFolderDetail> getTalentFolderDetailsByUserId(Long userId, Pageable pageable) {
        SearchConditionDTO.checkPageable(pageable);
        return talentFolderDetailRepository.findByPermissionUserId(userId, pageable);
    }

    @Override
    public List<TalentFolder> getTalentFolders(List<Long> folderIds) {
        return talentFolderRepository.findAllByIdIn(folderIds);
    }

    @Override
    public Map<Long, Long> getTalentFolderUserMap(List<Long> folderIds) {
        return getTalentFolders(folderIds).stream()
                .collect(Collectors.toMap(TalentFolder::getId, TalentFolder::getPermissionUserId));
    }

    @Override
    @Transactional
    public TalentFolderDTO createTalentFolder(TalentFolderDTO talentFolderDTO) {
        if (talentFolderDTO == null || StringUtils.isBlank(talentFolderDTO.getName())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        Optional<TalentFolder> talentFolderfromDB = Optional.ofNullable(talentFolderRepository.findOneByPermissionUserIdAndName(SecurityUtils.getUserId(), talentFolderDTO.getName()));
        if (talentFolderfromDB.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_CREATETALENTFOLDER_INVALIDFOLDERNAME.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        TalentFolder talentFolder = talentFolderMapper.toEntity(talentFolderDTO);
        talentFolder.setTenantId(SecurityUtils.getTenantId());
        talentFolder = talentFolderRepository.saveAndFlush(talentFolder);
        if (talentFolderDTO.getTalentIds() != null) {
            TalentFolderRelationListDTO talentFolderRelationListDTO = new TalentFolderRelationListDTO();
            talentFolderRelationListDTO.setFolderIds(List.of(talentFolder.getId()));
            talentFolderRelationListDTO.setTalentIds(talentFolderDTO.getTalentIds());
            addTalentsToFolders(talentFolderRelationListDTO);
        }

        Long talentFolderId = talentFolder.getId();
        if (talentFolderDTO.getShareTo() != null) {
            talentFolderDTO.setShareTo(addTalentFolderSharing(talentFolderDTO.getShareTo(), talentFolderId));
        }
        talentFolderDTO.setId(talentFolder.getId());
        return talentFolderDTO;

    }


    @Override
    @Transactional
    public TalentFolderDTO updateTalentFolder(TalentFolderDTO talentFolderDTO, Long talentFolderId) {
        TalentFolder talentFolder = getValidateTalentFolderBeforeUpdate(talentFolderDTO, talentFolderId);

        talentFolder.setName(talentFolderDTO.getName());
        talentFolder.setFolderNote(talentFolderDTO.getFolderNote());
        talentFolder = talentFolderRepository.save(talentFolder);

        List<CustomFolderSharingTargetDTO> sharingDTO = new ArrayList<>();
        if (talentFolderDTO.getShareTo() != null) {
            sharingDTO = updateFolderSharing(talentFolderDTO.getShareTo(), talentFolder.getId());
            talentFolderDTO.setShareTo(sharingDTO);
        } else {
            //update search folder
            talentSearchFolderSearchParamService.disableSearchFolder(talentFolderId);
        }

        talentFolderDTO = talentFolderMapper.toDto(talentFolder);
        talentFolderDTO.setShareTo(sharingDTO);
        talentFolderDTO.setId(talentFolder.getId());
        return talentFolderDTO;

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFolder(Long folderId) {
        Optional<TalentFolderDetail> talentFolder = talentFolderDetailRepository.findById(folderId);
        if (talentFolder.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_DELETEFOLDER_INVALIDFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        TalentFolderDetail talentFolderDetail = talentFolder.get();
        if (!talentFolderDetail.getPermissionUserId().equals(SecurityUtils.getUserId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_DELETEFOLDER_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        talentFolderRepository.deleteById(folderId);
        List<Long> sharingUserIds = talentFolderDetail.getTalentFolderSharingUsers().stream().map(TalentFolderSharingUser::getId).collect(Collectors.toList());
        talentFolderSharingUserRepository.deleteAllByIdInBatch(sharingUserIds);
        List<Long> sharingTeamIds = talentFolderDetail.getTalentFolderSharingTeams().stream().map(TalentFolderSharingTeam::getId).collect(Collectors.toList());
        talentFolderSharingUserRepository.deleteAllByIdInBatch(sharingTeamIds);
        List<Long> relationIds = talentFolderDetail.getTalentFolderRelations().stream().map(TalentFolderRelation::getId).collect(Collectors.toList());
        talentFolderRelationRepository.deleteAllByIdInBatch(relationIds);

        log.info("[APN V3 Folder] Delete Talent Custom Folder {}, team Sharing {}, user Sharing {}, talent relation {}", folderId, sharingTeamIds, sharingUserIds, relationIds);

        //sync up the active status for search folder
        talentSearchFolderSearchParamService.disableSearchFolder(folderId);


    }

    @Autowired
    private TalentSyncService talentSyncService;

    /*
     * Folder Talent Relation functions
     * */

    @Override
    @Transactional
    public void addTalentsToFolders(TalentFolderRelationListDTO talentFolderRelationListDTO) {
        if (!(talentFolderRelationListDTO.isTalentsValid() && talentFolderRelationListDTO.isFoldersValid())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        checkRWPermissionOnFolders(fetchAllFoldersIfExist(talentFolderRelationListDTO.getFolderIds()));
        validateTalents(talentFolderRelationListDTO.getTalentIds());

        List<Pair<Long, Long>> talentAndFolderPair = getAllValidTalentFolderRelation(talentFolderRelationListDTO.getTalentIds(), talentFolderRelationListDTO.getFolderIds());

        List<TalentFolderRelation> talentFolderRelations = talentAndFolderPair.stream()
                .map(pair -> {
                    TalentFolderRelation talentFolderRelation = new TalentFolderRelation();
                    talentFolderRelation.setTalentId(pair.getLeft());
                    talentFolderRelation.setTalentFolderId(pair.getRight());
                    return talentFolderRelation;
                })
                .collect(Collectors.toList());
        talentFolderRelationRepository.saveAllAndFlush(talentFolderRelations);

        //sync in es
        updateTalentFolderRelationInES(talentFolderRelationListDTO.getTalentIds(), talentFolderRelationListDTO.getFolderIds(), null);
        //防止候选人没同步到es就调用了sync in es
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            talentSyncService.syncTalentsToMQ(talentFolderRelationListDTO.getTalentIds(), 1);
        });
    }


    @Override
    @Transactional
    public void deleteTalentsFromFolder(TalentFolderRelationListDTO talentFolderRelationListDTO, Long folderId) {
        if (!talentFolderRelationListDTO.isTalentsValid()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        checkRWPermissionOnFolders(Collections.singletonList(fetchFolderIfExist(folderId)));
        validateTalents(talentFolderRelationListDTO.getTalentIds());
        checkAllTalentsUnderCurrentFolder(talentFolderRelationListDTO.getTalentIds(), folderId);

        talentFolderRelationRepository.deleteAllByTalentIdInAndTalentFolderId(talentFolderRelationListDTO.getTalentIds(), folderId);

        //sync in es
        updateTalentFolderRelationInES(talentFolderRelationListDTO.getTalentIds(), null, Collections.singletonList(folderId));

    }


    /****
     * Folder Sharing functions
     * */
    private List<CustomFolderSharingTargetDTO> updateFolderSharing(List<CustomFolderSharingTargetDTO> talentFolderSharingDTOList, Long talentFolderId) {
        log.info("TalentV3 update talent folder {} sharing ", talentFolderId);
        List<CustomFolderSharingTargetDTO> updatedList = talentFolderSharingUserMapper.toFolderSharingDto(updateFolderSharingUser(talentFolderSharingDTOList, talentFolderId));
        updatedList.addAll(talentFolderSharingTeamMapper.toFolderSharingDto(updateFolderSharingTeam(talentFolderSharingDTOList, talentFolderId)));
        return updatedList;

    }


    private List<TalentFolderSharingTeam> updateFolderSharingTeam(List<CustomFolderSharingTargetDTO> talentFolderSharingDTOList, Long talentFolderId) {
        log.info("[Talent V3 {} ]: update current folder with team sharing", talentFolderId);
        List<TalentFolderSharingTeam> oldFolderSharingList = talentFolderSharingTeamRepository.findAllByTalentFolderId(talentFolderId);
        List<TalentFolderSharingTeam> newFolderSharingList = talentFolderSharingDTOList.stream()
                .filter(sharing -> sharing.getTargetCategory() == CustomFolderSharingTargetCategory.TEAM)
                .map(dto -> {
                    TalentFolderSharingTeam talentFolderSharingTeam = talentFolderSharingTeamMapper.toEntity(dto);
                    talentFolderSharingTeam.setTalentFolderId(talentFolderId);
                    return talentFolderSharingTeam;
                })
                .collect(Collectors.toList());
        List<TalentFolderSharingTeam> toAddFolderSharingList = new ArrayList<>();
        List<TalentFolderSharingTeam> finalSharingList = new ArrayList<>();
        List<TalentFolderSharingTeam> toDeleteSharingList = new ArrayList<>();
        if (oldFolderSharingList.size() != 0) {//delete existing and add new
            List<TalentFolderSharingTeam> intersection = oldFolderSharingList.stream()
                    .filter(oldItem -> newFolderSharingList.stream()
                            .anyMatch(oldItem::isSameFolderSharing))
                    .collect(Collectors.toList());
            toAddFolderSharingList = newFolderSharingList.stream()
                    .filter(newItem -> oldFolderSharingList.stream()
                            .noneMatch(oldItem -> oldItem.isSameFolderSharing(newItem)))
                    .peek(item -> item.setTalentFolderId(talentFolderId))
                    .collect(Collectors.toList());
            toDeleteSharingList = oldFolderSharingList.stream()
                    .filter(oldItem -> newFolderSharingList.stream()
                            .noneMatch(oldItem::isSameFolderSharing))
                    .collect(Collectors.toList());
            talentFolderSharingTeamRepository.deleteAllInBatch(toDeleteSharingList);
            finalSharingList.addAll(intersection);

            //recover sharing when user share to same team again;
            recoverExistSharingTeam(talentFolderId, intersection.stream().map(TalentFolderSharingTeam::getTeamId).collect(Collectors.toList()));


        } else if (newFolderSharingList.size() != 0) { //add if no old
            toAddFolderSharingList = newFolderSharingList;
        }

        finalSharingList.addAll(talentFolderSharingTeamRepository.saveAll(toAddFolderSharingList));

        //handle search folder
        updateSearchFolderSharingTeam(talentFolderId,
                toAddFolderSharingList.stream().map(TalentFolderSharingTeam::getTeamId).collect(Collectors.toList()),
                toDeleteSharingList.stream().map(TalentFolderSharingTeam::getTeamId).collect(Collectors.toList())

        );

        return finalSharingList;

    }

    private void recoverExistSharingTeam(Long folderId, List<Long> teamIds) {
        talentFolderSharingTeamRepository.updateExcludedUserIdsToNullByTalentFolderIdAndTeamIdIn(folderId, teamIds);
    }

    private void updateSearchFolderSharingTeam(Long talentFolderId, List<Long> toEnableTeamIds, List<Long> toDisableTeamIds) {
        if (!toDisableTeamIds.isEmpty()) {
            talentSearchFolderSearchParamService.disableSearchFolderWithFolderParamFromTeamShared(talentFolderId, toDisableTeamIds);
        }
        if (!toEnableTeamIds.isEmpty()) {
            talentSearchFolderSearchParamService.enableSearchFolderWithFolderParamFromTeamShared(talentFolderId, toEnableTeamIds);
        }
    }


    private List<TalentFolderSharingUser> updateFolderSharingUser(List<CustomFolderSharingTargetDTO> talentFolderSharingDTOList, Long talentFolderId) {
        log.info("[Talent V3 {} ]: update current folder with user sharing", talentFolderId);
        Long userId = SecurityUtils.getUserId();
        List<TalentFolderSharingUser> oldFolderSharingList = talentFolderSharingUserRepository.findAllByTalentFolderId(talentFolderId);
        List<TalentFolderSharingUser> newFolderSharingList = talentFolderSharingDTOList.stream()
                .filter(sharing -> sharing.getTargetCategory() == CustomFolderSharingTargetCategory.USER && !sharing.getTargetId().equals(userId))
                .map(dto -> {
                    TalentFolderSharingUser talentFolderSharing = talentFolderSharingUserMapper.toEntity(dto);
                    if (talentFolderSharing.DoesShareWithOwner(SecurityUtils.getUserId())) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_UPDATEFOLDERSHARINGUSER_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
                    }
                    talentFolderSharing.setTalentFolderId(talentFolderId);
                    return talentFolderSharing;
                })
                .collect(Collectors.toList());
        List<TalentFolderSharingUser> toAddFolderSharingList = new ArrayList<>();
        List<TalentFolderSharingUser> finalSharingList = new ArrayList<>();
        List<TalentFolderSharingUser> toDeleteSharingList = new ArrayList<>();
        if (oldFolderSharingList.size() != 0) {
            List<TalentFolderSharingUser> intersection = oldFolderSharingList.stream()
                    .filter(oldItem -> newFolderSharingList.stream()
                            .anyMatch(oldItem::isSameFolderSharing))
                    .collect(Collectors.toList());
            toAddFolderSharingList = newFolderSharingList.stream()
                    .filter(newItem -> oldFolderSharingList.stream()
                            .noneMatch(oldItem -> oldItem.isSameFolderSharing(newItem)))
                    .peek(item -> item.setTalentFolderId(talentFolderId))
                    .collect(Collectors.toList());
            toDeleteSharingList = oldFolderSharingList.stream()
                    .filter(oldItem -> newFolderSharingList.stream()
                            .noneMatch(oldItem::isSameFolderSharing))
                    .collect(Collectors.toList());
            talentFolderSharingUserRepository.deleteAllInBatch(toDeleteSharingList);
            finalSharingList.addAll(intersection);

        } else if (newFolderSharingList.size() != 0) {
            toAddFolderSharingList = newFolderSharingList;
        }

        finalSharingList.addAll(talentFolderSharingUserRepository.saveAll(toAddFolderSharingList));

        //update search folder
        updateSearchFolderSharingUser(talentFolderId,
                toAddFolderSharingList.stream().map(TalentFolderSharingUser::getUserId).collect(Collectors.toList()),
                toDeleteSharingList.stream().map(TalentFolderSharingUser::getUserId).collect(Collectors.toList())
        );

        return finalSharingList;

    }

    private void updateSearchFolderSharingUser(Long talentFolderId, List<Long> toEnableUserIds, List<Long> toDisableUserIds) {
        if (toDisableUserIds.size() != 0) {
            talentSearchFolderSearchParamService.disableSearchFolderWithFolderParamFromUserShared(talentFolderId, toDisableUserIds);
        }
        if (toEnableUserIds.size() != 0) {
            talentSearchFolderSearchParamService.enableSearchFolderWithFolderParamFromUserShared(talentFolderId, toEnableUserIds);
        }
    }


    private List<CustomFolderSharingTargetDTO> addTalentFolderSharing(List<CustomFolderSharingTargetDTO> talentFolderSharingDTOList, Long talentFolderId) {
        return Stream.concat(
                addTalentFolderSharingForTeams(talentFolderSharingDTOList, talentFolderId).stream(),
                addTalentFolderSharingForUsers(talentFolderSharingDTOList, talentFolderId).stream())
                .collect(Collectors.toList());

    }

    private List<CustomFolderSharingTargetDTO> addTalentFolderSharingForTeams(List<CustomFolderSharingTargetDTO> talentFolderSharingDTOList, Long talentFolderId) {
        log.info("[Talent V3 {}]: add current folder with team sharing", talentFolderId);
        HashMap<Long, FolderPermission> existingTeamPermissions = new HashMap<>();
        List<TalentFolderSharingTeam> talentFolderSharingTeamList = talentFolderSharingDTOList.stream()
                .filter(sharing -> sharing.getTargetCategory() == CustomFolderSharingTargetCategory.TEAM)
                .map(dto -> {
                    TalentFolderSharingTeam talentFolderSharingTeam = talentFolderSharingTeamMapper.toEntity(dto);
                    talentFolderSharingTeam.setTalentFolderId(talentFolderId);
                    if (existingTeamPermissions.containsKey(talentFolderSharingTeam.getTeamId())) {
                        FolderPermission existingPermission = existingTeamPermissions.get(talentFolderSharingTeam.getTeamId());
                        if (!existingPermission.equals(talentFolderSharingTeam.getPermission())) {
                            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_ADDTALENTFOLDERSHARINGFORTEAMS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
                        }
                        return null;
                    } else {
                        existingTeamPermissions.put(talentFolderSharingTeam.getTeamId(), talentFolderSharingTeam.getPermission());
                    }
                    return talentFolderSharingTeam;
                })
                .collect(Collectors.toList());
        talentFolderSharingTeamList = talentFolderSharingTeamRepository.saveAll(talentFolderSharingTeamList);

        return talentFolderSharingTeamList.stream().map(talentFolderSharingTeamMapper::toFolderSharingDto).collect(Collectors.toList());
    }

    private List<CustomFolderSharingTargetDTO> addTalentFolderSharingForUsers(List<CustomFolderSharingTargetDTO> talentFolderSharingDTOList, Long talentFolderId) {
        log.info("[Talent V3 {}]: add current folder with user sharing", talentFolderId);
        Long userId = SecurityUtils.getUserId();
        HashMap<Long, FolderPermission> existingUserPermissions = new HashMap<>();
        List<TalentFolderSharingUser> talentFolderSharingUserList = talentFolderSharingDTOList.stream()
                .filter(sharing -> sharing.getTargetCategory() == CustomFolderSharingTargetCategory.USER && !sharing.getTargetId().equals(userId))
                .map(dto -> {
                    TalentFolderSharingUser talentFolderSharingUser = talentFolderSharingUserMapper.toEntity(dto);
                    talentFolderSharingUser.setTalentFolderId(talentFolderId);
                    if (existingUserPermissions.containsKey(talentFolderSharingUser.getUserId())) {
                        FolderPermission existingPermission = existingUserPermissions.get(talentFolderSharingUser.getUserId());
                        if (!existingPermission.equals(talentFolderSharingUser.getPermission())) {
                            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_ADDTALENTFOLDERSHARINGFORTEAMS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
                        }
                        return null;
                    } else {
                        existingUserPermissions.put(talentFolderSharingUser.getUserId(), talentFolderSharingUser.getPermission());
                    }

                    if (talentFolderSharingUser.DoesShareWithOwner(SecurityUtils.getUserId())) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_UPDATEFOLDERSHARINGUSER_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
                    }
                    return talentFolderSharingUser;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        talentFolderSharingUserList = talentFolderSharingUserRepository.saveAll(talentFolderSharingUserList);

        return talentFolderSharingUserList.stream().map(talentFolderSharingUserMapper::toFolderSharingDto).collect(Collectors.toList());
    }


    @Override
    @Transactional
    public void removeSharingForSharedFolder(Long folderId) {
        if (folderId == null || SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        boolean isShareToUser = removeFolderSharingToUser(folderId);
        boolean isShareToTeam = removeUserFromFolderSharingToTeam(folderId);

        if (!isShareToTeam && !isShareToUser) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_REMOVESHARINGFORSHAREDFOLDER_NOSHARE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
    }

    @Override
    public List<FolderSharedTeamDTO> getDistinctSharedTeamsByUserId() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        return talentFolderSharingTeamRepository.findDistinctTalentFolderSharedTeamsByUserId(SecurityUtils.getUserId());
    }

    @Override
    public List<FolderSharedUserDTO> getDistinctSharedUsersByUserId() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        //return jobFolderSharingUserRepository.findJobFolderSharedUsersByUserId(SecurityUtils.getUserId());
        List<FolderSharedUserDTO> sharedUserDTOList = talentFolderSharingUserRepository.findDistinctTalentFolderSharedUsersByUserId(SecurityUtils.getUserId());
        return sharedUserDTOList;

    }

    /**
     * Custom and Shared folder
     */

    @Override
    public FolderListDTO getCollaborativeTalentFolderList() { // READWRITE permission folder list
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        log.info("[APN] fetch collaborative job folder list, user {}", SecurityUtils.getUserId());
        Long userId = SecurityUtils.getUserId();
        FolderListDTO folderListDTO = new FolderListDTO();
        folderListDTO.setUserId(userId);

        CompletableFuture<List<FolderNameDTO>> myFolderNamesFuture = CompletableFuture.supplyAsync(() -> getMyJobFolderNameList(userId));
        CompletableFuture<List<FolderNameDTO>> sharedFolderNamesFuture = CompletableFuture.supplyAsync(() -> getCollaborativeSharedJobFolderNameList(userId));
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(myFolderNamesFuture, sharedFolderNamesFuture);

        combinedFuture.exceptionally(ex -> {
            log.error("[APN] Error in asynchronous operation: " + ex.getMessage());
            return null;
        });

        combinedFuture.join();
        try {
            folderListDTO.setMyFolderList(myFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching my folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETCOLLABORATIVETALENTFOLDERLIST_FAILFETCH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        try {
            folderListDTO.setSharedFolderList(sharedFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching shared folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETCOLLABORATIVETALENTFOLDERLIST_FAILSHAREFETCH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        return folderListDTO;

    }


    @Override
    public FolderPermissionListDTO getCustomAndSharedJobFolderWithPermissionList() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETCUSTOMANDSHAREDJOBFOLDERWITHPERMISSIONLIST_NOPERMISSIO.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        log.info("[APN] fetch collaborative job folder list, user {}", SecurityUtils.getUserId());
        Long userId = SecurityUtils.getUserId();
        FolderPermissionListDTO folderListDTO = new FolderPermissionListDTO();
        folderListDTO.setUserId(userId);

        CompletableFuture<List<FolderNamePermissionDTO>> myFolderNamesFuture = CompletableFuture.supplyAsync(() ->
                getMyJobFolderNameList(userId).stream()
                        .map(folderName -> new FolderNamePermissionDTO(folderName.getId(), folderName.getName(), FolderPermission.READWRITE))
                        .collect(Collectors.toList())
        );
        CompletableFuture<List<FolderNamePermissionDTO>> sharedFolderNamesFuture = CompletableFuture.supplyAsync(() -> talentSharedCustomFolderService.getAllSharedJobFolderNamePermissionList(userId));
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(myFolderNamesFuture, sharedFolderNamesFuture);

        combinedFuture.exceptionally(ex -> {
            log.error("[APN] Error in asynchronous operation: " + ex.getMessage());
            return null;
        });

        combinedFuture.join();
        try {
            folderListDTO.setMyFolderList(myFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching my folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETCOLLABORATIVETALENTFOLDERLIST_FAILFETCH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        try {
            folderListDTO.setSharedFolderList(sharedFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching shared folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETCOLLABORATIVETALENTFOLDERLIST_FAILSHAREFETCH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        return folderListDTO;
    }

    @Override
    public FolderListDTO getCustomAndSharedTalentFolderList() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETCUSTOMANDSHAREDJOBFOLDERWITHPERMISSIONLIST_NOPERMISSIO.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        log.info("[APN] fetch custom and shared Talent folder list, user {}", SecurityUtils.getUserId());
        Long userId = SecurityUtils.getUserId();
        FolderListDTO folderListDTO = new FolderListDTO();
        folderListDTO.setUserId(userId);

        CompletableFuture<List<FolderNameDTO>> myFolderNamesFuture = CompletableFuture.supplyAsync(() -> getMyJobFolderNameList(userId));
        CompletableFuture<List<FolderNameDTO>> sharedFolderNamesFuture = CompletableFuture.supplyAsync(() -> getAllSharedJobFolderNameList(userId));
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(myFolderNamesFuture, sharedFolderNamesFuture);

        combinedFuture.exceptionally(ex -> {
            log.error("[APN] Error in asynchronous operation: " + ex.getMessage());
            return null;
        });

        combinedFuture.join();
        try {
            folderListDTO.setMyFolderList(myFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching my folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETCOLLABORATIVETALENTFOLDERLIST_FAILFETCH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        try {
            folderListDTO.setSharedFolderList(sharedFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching shared folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETCOLLABORATIVETALENTFOLDERLIST_FAILSHAREFETCH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        return folderListDTO;
    }

    public List<FolderNameDTO> getMyJobFolderNameList(Long userId) {
        return talentFolderRepository.findAllFolderNameByPermissionUserId(userId);
    }

    public List<FolderNameDTO> getCollaborativeSharedJobFolderNameList(Long userId) {
        return talentFolderRepository.findAllSharedFolderNameByUserIdAndPermission(userId, FolderPermission.READWRITE);
    }

    public List<FolderNameDTO> getAllSharedJobFolderNameList(Long userId) {
        return talentFolderRepository.findAllSharedFolderNameByUserId(userId);
    }


    /*
     * Folder related info
     * */

    final static Long MAX_TALENT_EMAIL = 100000L;

    @Override
    public List<String> getAllTalentsEmailInFolders(FolderEmailRequestDTO folderEmailRequestDTO) {
        if (folderEmailRequestDTO == null || folderEmailRequestDTO.getFolderIds() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETALLTALENTSEMAILINFOLDERS_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        //check for shared folders list
        checkRWPermissionOnFolders(talentFolderRepository.findAllByIdIn(folderEmailRequestDTO.getFolderIds()));

        Long talentCounts = talentFolderRelationRepository.countAllByTalentFolderIdIn(folderEmailRequestDTO.getFolderIds());
        if (talentCounts > MAX_TALENT_EMAIL) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETALLTALENTSEMAILINFOLDERS_MAXCOUNTLIMIT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        List<String> emailList = talentFolderRelationRepository.findTalentsEmailByFolderIds(folderEmailRequestDTO.getFolderIds());
        return emailList;
    }

    @Override
    public ListPageFolderDTO getTalentFolderById(Long folderId) {
        TalentFolder talentFolder = talentFolderRepository.findById(folderId).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETTALENTFOLDERBYID_FOLDERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService())));
        List<FolderSharedUserDTO> userDTOList = talentFolderSharingUserRepository.findTalentFolderSharingUserByTalentFolderIdIn(List.of(talentFolder.getId()));
        List<FolderSharedTeamDTO> teamDTOList = talentFolderSharingTeamRepository.findTalentFolderSharedTeamByTalentFolderIdIn(List.of(talentFolder.getId()));
        if (!checkUserAccessFolderPermission(talentFolder, teamDTOList, userDTOList)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETTALENTFOLDERBYID_FOLDERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        ListPageFolderDTO folderDTO = new ListPageFolderDTO();
        if (!userDTOList.isEmpty()) {
            folderDTO.setSharedUsers(userDTOList.stream().map(o -> new FolderSharedTargetBriefDTO(o.getUserId(), o.getFolderPermission(), o.getFullName())).collect(Collectors.toList()));
        }
        if (!teamDTOList.isEmpty()) {
            folderDTO.setSharedTeams(teamDTOList.stream().map(o -> new FolderSharedTargetBriefDTO(o.getTeamId(), o.getFolderPermission(), o.getTeamName())).collect(Collectors.toList()));
        }
        folderDTO.setId(talentFolder.getId());
        folderDTO.setName(talentFolder.getName());
        folderDTO.setFolderNote(talentFolder.getFolderNote());
        folderDTO.setCreator(getCreatorName(SecurityUtils.getUserIdFromCreatedBy(talentFolder.getCreatedBy())));
        folderDTO.setCreatedDate(talentFolder.getCreatedDate());
        folderDTO.setLastModifiedDate(talentFolder.getLastModifiedDate());
        folderDTO.setFolderPermission(getCurrentUserFolderPermission(talentFolder, userDTOList, teamDTOList));

        return folderDTO;
    }

    private FolderPermission getCurrentUserFolderPermission(TalentFolder talentFolder, List<FolderSharedUserDTO> userDTOList, List<FolderSharedTeamDTO> teamDTOList) {
        Long userId = SecurityUtils.getUserId();
        Long teamId = SecurityUtils.getTeamId();
        if (talentFolder.getPermissionUserId().equals(userId)) {
            return FolderPermission.READWRITE;
        }
        Optional<FolderSharedUserDTO> shareUserOpt = userDTOList.stream().filter(p -> p.getUserId().equals(userId)).findFirst();
        Optional<FolderSharedTeamDTO> shareTeamOpt = teamDTOList.stream().filter(p -> p.getTeamId().equals(teamId)).findFirst();
        if (shareUserOpt.isPresent() && shareTeamOpt.isPresent()) {
            FolderPermission userPermission = shareUserOpt.get().getFolderPermission();
            FolderPermission teamPermission = shareTeamOpt.get().getFolderPermission();
            return FolderPermission.max(userPermission, teamPermission);
        }
        if (shareUserOpt.isPresent()) {
            return shareUserOpt.get().getFolderPermission();
        }
        if (shareTeamOpt.isPresent()) {
            return shareTeamOpt.get().getFolderPermission();
        }
        return FolderPermission.NOPERMISSION;
    }

    private String getCreatorName(Long userId) {
        ResponseEntity<User> res = userService.getUserById(userId);
        if (res.getStatusCode() != HttpStatus.OK) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETCREATORNAME_RESPONSENOOK.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        User user = res.getBody();
        return CommonUtils.formatFullName(user.getFirstName(), user.getLastName());
    }

    private boolean checkUserAccessFolderPermission(TalentFolder talentFolder, List<FolderSharedTeamDTO> folderSharingTeams, List<FolderSharedUserDTO> folderSharingUsers) {
        Long userId = SecurityUtils.getUserId();
        Long teamId = SecurityUtils.getTeamId();
        if (talentFolder.getPermissionUserId().equals(userId)) {
            return true;
        }
        return folderSharingTeams.stream().anyMatch(f -> f.getTeamId().equals(teamId)) || folderSharingUsers.stream().anyMatch(f -> f.getUserId().equals(userId));
    }


    /*********************
     * folder utilize function
     * */

    private TalentFolder getValidateTalentFolderBeforeUpdate(TalentFolderDTO talentFolderDTO, Long talentFolderId) {
        log.info("TalentV3: get valid talent folder list before updating current talent folder ");
        if (talentFolderDTO == null || talentFolderId == null)
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));

        TalentFolder talentFolderById = fetchFolderIfExist(talentFolderId);

        checkRWPermissionOnFolders(Collections.singletonList(talentFolderById));

        Optional<TalentFolder> talentFolderByName = Optional.ofNullable(talentFolderRepository.findOneByPermissionUserIdAndName(SecurityUtils.getUserId(), talentFolderDTO.getName()));
        if (talentFolderByName.isPresent() && !talentFolderByName.get().getId().equals(talentFolderId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_GETVALIDATETALENTFOLDERBEFOREUPDATE_NAMEEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        return talentFolderById;


    }

    private List<Pair<Long, Long>> getAllValidTalentFolderRelation(List<Long> talentIds, List<Long> talentFolderIds) {

        Set<Pair<Long, Long>> existRelations = talentFolderRelationRepository.getAllByTalentFolderIdIn(talentFolderIds)
                .stream().map(relation -> Pair.of(relation.getTalentId(), relation.getTalentFolderId())).collect(Collectors.toSet());
        Set<Pair<Long, Long>> receivedFolderTalentPairList = talentIds.stream()
                .flatMap(talentId -> talentFolderIds.stream()
                        .map(folderId -> Pair.of(talentId, folderId)))
                .collect(Collectors.toSet());

        List<Pair<Long, Long>> newFolderTalentPair = receivedFolderTalentPairList.stream().filter(received -> !existRelations.contains(received)).collect(Collectors.toList());
        log.info("JOBS: Add Talents to Folder, get all <talent, folder>: {}, new added {}", receivedFolderTalentPairList.size(), newFolderTalentPair.size());
        return newFolderTalentPair;
    }

    private List<TalentFolder> fetchAllFoldersIfExist(List<Long> folderIds) {
        List<TalentFolder> talentFolders = talentFolderRepository.findAllById(folderIds);
        if (talentFolders.size() != folderIds.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_DELETEFOLDER_INVALIDFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        return talentFolders;
    }

    private TalentFolder fetchFolderIfExist(Long folderId) {
        Optional<TalentFolder> talentFolder = talentFolderRepository.findById(folderId);
        return talentFolder.orElseThrow(() -> new CustomParameterizedException("Invalid Folder."));
    }


    private boolean removeFolderSharingToUser(Long folderId) {

        Optional<TalentFolderSharingUser> talentFolderSharing = talentFolderSharingUserRepository.findByTalentFolderIdAndUserId(folderId, SecurityUtils.getUserId());
        if (talentFolderSharing.isEmpty()) {
            return false;
        }

        talentFolderSharingUserRepository.delete(talentFolderSharing.get());

        //disable the use of folderId in searchFolder
        talentSearchFolderSearchParamService.disableSearchFolderWithFolderParamFromUserShared(folderId, Collections.singletonList(SecurityUtils.getUserId()));

        return true;
    }

    private boolean removeUserFromFolderSharingToTeam(Long folderId) {
        Optional<TalentFolderSharingTeam> talentFolderSharing = talentFolderSharingTeamRepository.findByTalentFolderIdAndTeamId(folderId, SecurityUtils.getTeamId());
        if (talentFolderSharing.isEmpty()) {
            return false;
        }

        TalentFolderSharingTeam talentFolderSharingTeam = talentFolderSharing.get();
        Set<Long> ids = talentFolderSharingTeamMapper.jsonStringToSet(talentFolderSharingTeam.getExcludedUserIds());
        if (ids.contains(SecurityUtils.getUserId())) {//handle already removed sharing user
            return false;
        }
        ids.add(SecurityUtils.getUserId());
        talentFolderSharingTeam.setExcludedUserIds(talentFolderSharingTeamMapper.setToJsonString(ids));
        talentFolderSharingTeamRepository.save(talentFolderSharingTeam);

        //disable the use of folderId in searchFolder
        talentSearchFolderSearchParamService.disableSearchFolderWithFolderParamFromUserShared(folderId, Collections.singletonList(SecurityUtils.getUserId()));

        return true;
    }

    private boolean checkRWPermissionOnFolders(List<TalentFolder> talentFolders) {
        log.info("Check user read and write permission on folders: userId:{}, teamId: {}", SecurityUtils.getUserId(), SecurityUtils.getTeamId());
        List<Long> sharedFolderIds = talentFolders.stream()
                .filter(folder -> !folder.getPermissionUserId().equals(SecurityUtils.getUserId()))
                .map(TalentFolder::getId)
                .collect(Collectors.toList());

        if (sharedFolderIds.size() > 0) {
            List<TalentFolderSharingUser> shareToUserList = talentFolderSharingUserRepository.findAllByTalentFolderIdInAndUserId(sharedFolderIds, SecurityUtils.getUserId());
            shareToUserList.stream().filter(t -> t.hasWritePermission(SecurityUtils.getUserId())).forEach(t -> {
                sharedFolderIds.remove(t.getTalentFolderId());
            });
            if (CollUtil.isNotEmpty(sharedFolderIds)) {
                List<TalentFolderSharingTeam> shareToTeamList = talentFolderSharingTeamRepository.findAllByTalentFolderIdInAndTeamId(sharedFolderIds, SecurityUtils.getTeamId());
                shareToTeamList.stream().filter(t -> t.hasWritePermission(SecurityUtils.getTeamId())).forEach(t -> {
                    TalentFolderSharingTeamDTO talentFolderSharingTeamDTO = talentFolderSharingTeamMapper.toDto(t);
                    if (!talentFolderSharingTeamDTO.DoesUserRemoveTeamSharing(SecurityUtils.getUserId())) {
                        sharedFolderIds.remove(t.getTalentFolderId());
                    }
                });
            }
        }
        return sharedFolderIds.size() <= 0;

    }



    private boolean validateTalents(List<Long> talents) {
        List<TalentV3> talentV3List = talentRepository.findAllByIdInAndTenantId(talents, SecurityUtils.getTenantId());
        if (talentV3List.size() != talents.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_VALIDATETALENTS_INVALIDTALENTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        return true;
    }

    private boolean checkAllTalentsUnderCurrentFolders(List<Long> talents, List<Long> talentFolderId) {
        List<TalentFolderRelation> relations = talentFolderRelationRepository.getAllByTalentFolderIdIn(talentFolderId);
        boolean doesTalentsAllinFolder = new HashSet<>(relations.stream().map(TalentFolderRelation::getTalentId).collect(Collectors.toList())).containsAll(talents);
        if (!doesTalentsAllinFolder) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_CHECKALLTALENTSUNDERCURRENTFOLDER_NOTINFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        return true;
    }

    private boolean checkAllTalentsUnderCurrentFolder(List<Long> talents, Long talentFolderId) {
        List<TalentFolderRelation> relations = talentFolderRelationRepository.getAllByTalentFolderId(talentFolderId);
        boolean doesTalentsAllinFolder = relations.stream().map(TalentFolderRelation::getTalentId).collect(Collectors.toSet()).containsAll(talents);
        if (!doesTalentsAllinFolder) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_CHECKALLTALENTSUNDERCURRENTFOLDER_NOTINFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }

        return true;
    }


    /***
     * Update es filler
     */
    private void updateTalentFolderRelationInES(List<Long> talentIds, List<Long> toFolderIds, List<Long> fromFolderIds) {
        if (CollUtil.isEmpty(talentIds) || (toFolderIds == null && fromFolderIds == null)) {
            return;
        }

        Long tenantId = SecurityUtils.getTenantId();
        CompletableFuture.supplyAsync(() -> {
            esFillerTalentService.updateTalentFolder(
                    talentIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()),
                    toFolderIds == null ? null : toFolderIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()),
                    fromFolderIds == null ? null : fromFolderIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()),
                    tenantId);
            return 0;
        });
    }

}
