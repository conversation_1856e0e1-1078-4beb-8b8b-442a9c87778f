package com.altomni.apn.talent.web.rest.talent.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 新建待加好友分组请求DTO
 */
@ApiModel(description = "新建待加好友分组请求")
@Data
public class LinkedinPendingGroupRequest {

    @ApiModelProperty(value = "名称", required = true, example = "高级工程师分组")
    @NotBlank(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "创建人的领英ID", required = true, example = "john-doe-123456")
    @NotBlank(message = "创建人领英ID不能为空")
    private String operatorLinkedinId;

    @ApiModelProperty(value = "待添加列表中的ID", example = "[1, 2, 3]")
    private List<Long> members;
}