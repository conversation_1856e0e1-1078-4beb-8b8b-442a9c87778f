package com.altomni.apn.talent.domain.confidential;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.math.BigDecimal;
import java.util.*;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ConfidentialityRules {

    /**
     * 薪资
     */
    private Salary salary;
    /**
     * 行业
     */
    private Set<String> industries = Collections.emptySet();
    /**
     * 职位
     */
    private Set<String> jobFunctions = Collections.emptySet();
    /**
     * 学历
     */
    private String degreeLevel;


    public boolean sameRule(ConfidentialityRules other) {
        if (this == other) {
            return true;
        }
        if (other == null) {
            return false;
        }
        boolean industrySame = Set.copyOf(this.industries).equals(Set.copyOf(other.industries));
        boolean jobFunctionSame = Set.copyOf(this.jobFunctions).equals(Set.copyOf(other.jobFunctions));
        boolean salarySame = this.salary == null ? other.salary == null : this.salary.sameRule(other.salary);
        boolean degreeLevelSame = Objects.equals(this.degreeLevel, other.degreeLevel);

        return industrySame && jobFunctionSame && salarySame && degreeLevelSame;
    }


    @Getter
    @Setter
    @NoArgsConstructor
    public static class Salary {
        /**
         * 薪资币种
         */
        private String currency;
        /**
         * 最低薪资要求
         */
        private BigDecimal amount;

        public Salary(String currency, BigDecimal amount) {
            this.currency = currency;
            this.amount = amount;
        }

        public boolean sameRule(Salary other) {
            if (this == other) {
                return true;
            }
            if (other == null) {
                return false;
            }
            return this.currency.equals(other.currency) && this.amount.equals(other.amount);
        }
    }


    @Converter
    public static class ConfidentialityRulesConverter implements AttributeConverter<ConfidentialityRules, String> {

        private final static ObjectMapper objectMapper = new ObjectMapper();

        @Override
        public String convertToDatabaseColumn(ConfidentialityRules attribute) {
            if (attribute == null) {
                return "{}";
            }
            try {
                return objectMapper.writeValueAsString(attribute);
            } catch (Exception e) {
                throw new RuntimeException("Conversion error", e);
            }
        }

        @Override
        public ConfidentialityRules convertToEntityAttribute(String dbData) {
            if (dbData == null || dbData.isBlank() || dbData.equals("{}")) {
                return null;
            }
            try {
                return objectMapper.readValue(dbData, new TypeReference<>() {
                });
            } catch (Exception e) {
                throw new RuntimeException("Conversion error", e);
            }
        }
    }
}
