package com.altomni.apn.talent.config;

import com.altomni.apn.common.auth.agency_auth.AgencyTokenFilter;
import com.altomni.apn.common.auth.agency_auth.AgencyUserTokenStore;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetTokenFilter;
import com.altomni.apn.common.auth.SkipOAuthTokenResolver;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetUserTokenStore;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationEntryPoint;
import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import java.util.List;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class WebSecurityConfig {

    private static final String[] PUBLIC_ENDPOINTS = {
            "/api/v3/liveness",
            "/actuator/**",
            "/api/v3/talents/fullName/{talentId}"
    };

    private static final String[] AGENCY_ENDPOINTS = {
            "/**"
//            "/api/v3/talent-notes/{id}",
//            "/api/v3/talent-notes/talent/{talentId}/notes",
//            "/api/v3/talent-notes",
//            "/api/v3/talents",
//            "/api/v3/es/search-shcool/{schoolName}",
//            "/api/v3/talent-resumes/relation/{relationId}",
//            "/api/v3/talents/fullName/{talentId}",
//            "/api/v3/talents/{id}",
//            "/api/v3/talent-resume/find-by-uuid-and-tenantId"
    };

    private final TimesheetUserTokenStore timesheetUserTokenStore;

    private final AgencyUserTokenStore agencyUserTokenStore;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 资源服务器配置
        http.oauth2ResourceServer(auth -> auth.opaqueToken(Customizer.withDefaults()));
        // 没有 token 或者 token 过期，返回 401
        http.exceptionHandling(exceptionHandling -> exceptionHandling.authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint()));
        // 无状态 session
        http.sessionManagement((session) -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        // 资源权限配置，所有请求都需要认证
        http.authorizeHttpRequests(
                authorizationManagerRequestMatcherRegistry -> authorizationManagerRequestMatcherRegistry
                        .requestMatchers(PUBLIC_ENDPOINTS).permitAll()
                        .anyRequest().authenticated());

        http.addFilterBefore(agencyTokenFilter(agencyUserTokenStore), BearerTokenAuthenticationFilter.class);
        http.addFilterBefore(timesheetTokenFilter(timesheetUserTokenStore), BearerTokenAuthenticationFilter.class);
        return http.build();
    }


    @Bean
    public BearerTokenResolver bearerTokenResolver() {
        return new SkipOAuthTokenResolver();
    }

    public TimesheetTokenFilter timesheetTokenFilter(TimesheetUserTokenStore timesheetUserTokenStore) {
        List<AntPathRequestMatcher> antPathRequestMatchers = List.of(
                new AntPathRequestMatcher("/api/v3/talent-resume/find-by-uuid-and-tenantId", HttpMethod.GET.name()),
                new AntPathRequestMatcher("/api/v3/talent-resumes", HttpMethod.POST.name()),
                new AntPathRequestMatcher("/api/v3/talents/info/{id}", HttpMethod.PUT.name()),
                new AntPathRequestMatcher("/api/v3/talent-resumes/{id}", HttpMethod.DELETE.name(), true));
        return new TimesheetTokenFilter(timesheetUserTokenStore, antPathRequestMatchers);
    }

    public AgencyTokenFilter agencyTokenFilter(AgencyUserTokenStore agencyUserTokenStore) {
        return new AgencyTokenFilter(agencyUserTokenStore, AGENCY_ENDPOINTS);
    }
}

