package com.altomni.apn.talent.web.rest.record;

import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatform;
import com.altomni.apn.talent.service.dto.record.TalentTrackingNoteDTO;
import com.altomni.apn.talent.service.record.TalentTrackingNoteService;
import com.altomni.apn.talent.service.vo.record.TalentNoteVO;
import com.altomni.apn.talent.service.vo.record.TalentTrackingNoteVO;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing TalentTrackingNote.
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3")
public class TalentTrackingNoteResource {

    private final Logger log = LoggerFactory.getLogger(TalentTrackingNoteResource.class);

    private static final String ENTITY_NAME = "TalentTrackingNote";

    private final TalentTrackingNoteService talentTrackingNoteService;

    public TalentTrackingNoteResource(TalentTrackingNoteService talentTrackingNoteService) {
        this.talentTrackingNoteService = talentTrackingNoteService;
    }

    /**
     * POST  /talent-tracking-notes : Create a new TalentTrackingNote.
     *
     * @param talentTrackingNoteDTO the TalentTrackingNote to create
     * @return the ResponseEntity with status 201 (Created) and with body the new TalentTrackingNote, or with status 400 (Bad Request) if the TalentTrackingNote has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/talent-tracking-notes")
    @Timed
    public ResponseEntity<TalentTrackingNoteVO> createTalentTrackingNote(@Valid @RequestBody TalentTrackingNoteDTO talentTrackingNoteDTO) throws URISyntaxException {
        log.info("[APN V3: TalentTrackingNote @{}] REST request to save TalentTrackingNotes: {}", SecurityUtils.getUserId(), talentTrackingNoteDTO);

        TalentTrackingNoteVO result = talentTrackingNoteService.save(talentTrackingNoteDTO);
        return ResponseEntity.created(new URI("/api/v1/talent-tracking-notes/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * GET  /talent-tracking-notes : get all the TalentTrackingNote.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of TalentTrackingNote in body
     */
    @GetMapping("/talent-tracking-notes")
    @Timed
    public List<TalentTrackingNoteVO> getAllTalentTrackingNote(@RequestParam(value = "platformId") String platformId,
                                                                   @RequestParam(value = "trackingPlatform") TrackingPlatform trackingPlatform) {
        log.info("[APN V3: TalentTrackingNote @{}] REST request to get all TalentTrackingNotes. platformId: {}, trackingPlatform: {}",
                SecurityUtils.getUserId(), platformId, trackingPlatform);
        return talentTrackingNoteService.findAllTalentTrackingNotes(platformId, trackingPlatform);
    }

    /**
     * GET  /talent-tracking-notes : get all the TalentTrackingNote.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of TalentTrackingNote in body
     */
    @GetMapping("/talent-tracking-notes/sync-to-apn-talent-note")
    @Timed
    public List<TalentNoteVO> syncProTalentTrackingNoteToApnTalentNote(@RequestParam(value = "talentId") Long talentId, @RequestParam(value = "platformId") String platformId) {
        log.info("[APN V3: TalentTrackingNote @{}] REST request to sync Pro TalentTrackingNotes to APN talent note. talentId: {}, platformId: {}", SecurityUtils.getUserId(), talentId, platformId);
        return talentTrackingNoteService.syncProTalentTrackingNoteToApnTalentNote(talentId, platformId);
    }
}
