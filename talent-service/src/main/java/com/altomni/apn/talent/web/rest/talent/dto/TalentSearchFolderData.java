package com.altomni.apn.talent.web.rest.talent.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentSearchFolderData {
    private Long id;
    private String name;
    private String total;
    private String newlyCount;
    private String pendingReviewCount;
    private boolean display;
    private Instant createdDate;
}
