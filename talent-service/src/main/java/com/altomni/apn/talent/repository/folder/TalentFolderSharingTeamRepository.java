package com.altomni.apn.talent.repository.folder;


import com.altomni.apn.common.dto.folder.FolderSharedTeamDTO;
import com.altomni.apn.common.dto.folder.IFolderNamePermissionDTOWithCreatedDate;
import com.altomni.apn.talent.domain.folder.TalentFolderSharingTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TalentFolderSharingTeamRepository extends JpaRepository<TalentFolderSharingTeam, Long> {

    List<TalentFolderSharingTeam> findAllByTalentFolderIdInAndTeamId(List<Long> sharedFolderIds, Long teamId);

    List<TalentFolderSharingTeam> findAllByTalentFolderId(Long talentFolderId);

    @Modifying
    @Query("update TalentFolderSharingTeam tfst set tfst.excludedUserIds = null where tfst.talentFolderId = :folderId and tfst.teamId in :teamIds")
    void updateExcludedUserIdsToNullByTalentFolderIdAndTeamIdIn(@Param("folderId") Long folderId, @Param("teamIds") List<Long> teamIds);

    Optional<TalentFolderSharingTeam> findByTalentFolderIdAndTeamId(Long folderId, Long teamId);

    @Query("SELECT DISTINCT new com.altomni.apn.common.dto.folder.FolderSharedTeamDTO(t.id, t.name) " +
            "FROM TalentFolder tf " +
            "JOIN TalentFolderSharingTeam tfst ON tf.id = tfst.talentFolderId " +
            "JOIN PermissionTeam t ON tfst.teamId = t.id " +
            "WHERE tf.permissionUserId = :userId")
    List<FolderSharedTeamDTO> findDistinctTalentFolderSharedTeamsByUserId(@Param("userId") Long userId);


    @Query("SELECT new com.altomni.apn.common.dto.folder.FolderSharedTeamDTO(tfst.talentFolderId, tfst.teamId, t.name, tfst.permissionUserId, tfst.permission) " +
            "FROM TalentFolderSharingTeam tfst " +
            "JOIN PermissionTeam t ON t.id = tfst.teamId " +
            "WHERE tfst.talentFolderId IN :folderIds")
    List<FolderSharedTeamDTO> findTalentFolderSharedTeamByTalentFolderIdIn(@Param("folderIds") List<Long> folderIds);

    @Query("SELECT st FROM TalentFolderSharingTeam st " +
            "INNER JOIN PermissionUserTeam put on st.teamId  = put.teamId " +
            "WHERE st.talentFolderId IN (:folderIds) AND put.userId = :userId")
    List<TalentFolderSharingTeam> findTalentFolderSharedTeamByTalentFolderIdInAndUserId(@Param("folderIds") List<Long> folderIds, @Param("userId") Long userId);


    @Query(value = "SELECT tfst.talent_folder_id as id, f.name as name, tfst.permission as folderPermission, f.created_date as folderCreatedDate " +
            "FROM talent_folder_sharing_team tfst " +
            "JOIN talent_folder f on tfst.talent_folder_id = f.id " +
            "WHERE f.puser_id != :userId " +
            "AND EXISTS (SELECT 1 FROM permission_user_team put WHERE put.user_id = :userId AND put.team_id = tfst.team_id) " +
            "AND (tfst.excluded_user_ids is null OR json_contains(tfst.excluded_user_ids, cast(:userId as json)) = 0) ", nativeQuery = true)
    List<IFolderNamePermissionDTOWithCreatedDate> findTeamSharedFolderByUserId(@Param("userId") Long userId);

}
