package com.altomni.apn.talent.domain.enumeration.linkedinproject;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum GroupMemberStatus implements ConvertedEnum<Integer> {
    WAITING(0),

    NORMAL(1);


    private final Integer dbValue;

    GroupMemberStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<GroupMemberStatus, Integer> resolver =
            new ReverseEnumResolver<>(GroupMemberStatus.class, GroupMemberStatus::toDbValue);

    public static GroupMemberStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
