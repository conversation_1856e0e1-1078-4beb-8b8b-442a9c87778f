package com.altomni.apn.talent.domain.email;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.talent.domain.enumeration.MailingListSource;
import com.altomni.apn.talent.domain.enumeration.MailingListSourceConverter;
import com.altomni.apn.talent.service.dto.email.Recipient;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A EmailListDetails.
 */
@Entity
@Table(name = "mailing_list_content")
public class MailingListContent extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "email")
    private String email;

    @Column(name = "name")
    private String name;

    @Column(name = "mailing_list_id")
    private Long mailingListId;

    @Column(name = "source")
    @Convert(converter = MailingListSourceConverter.class)
    private MailingListSource source;

    public MailingListContent() {}

    public MailingListContent(Recipient recipient, Long mailingListId) {
        this.email = recipient.getEmail().toLowerCase();
        this.name = recipient.getName();
        this.mailingListId = mailingListId;
        this.source = MailingListSource.CANDIDATE_PAGE;
    }


    public MailingListContent(String email, String name, Long mailingListId, MailingListSource source) {
        this.email = email;
        this.name = name;
        this.mailingListId = mailingListId;
        this.source = source;
    }

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public MailingListContent email(String email) {
        this.email = email;
        return this;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public MailingListContent name(String name) {
        this.name = name;
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getMailingListId() {
        return mailingListId;
    }

    public MailingListContent mailingListId(Long mailingListId) {
        this.mailingListId = mailingListId;
        return this;
    }

    public void setMailingListId(Long emailListId) {
        this.mailingListId = emailListId;
    }

    public MailingListSource getSource() {
        return source;
    }

    public MailingListContent source(MailingListSource source) {
        this.source = source;
        return this;
    }

    public void setSource(MailingListSource source) {
        this.source = source;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MailingListContent)) {
            return false;
        }
        return id != null && id.equals(((MailingListContent) o).id);
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "MailingListContent{" +
            "id=" + getId() +
            ", email='" + getEmail() + "'" +
            ", name='" + getName() + "'" +
            ", emailListId=" + getMailingListId() +
            ", source='" + getSource() + "'" +
            "}";
    }
}
