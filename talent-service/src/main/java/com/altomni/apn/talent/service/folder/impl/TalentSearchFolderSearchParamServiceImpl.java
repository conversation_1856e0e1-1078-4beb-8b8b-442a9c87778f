package com.altomni.apn.talent.service.folder.impl;


import com.altomni.apn.talent.domain.folder.TalentSearchFolder;
import com.altomni.apn.talent.repository.folder.TalentSearchFolderRepository;
import com.altomni.apn.talent.service.folder.TalentSearchFolderSearchParamService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TalentSearchFolderSearchParamServiceImpl implements TalentSearchFolderSearchParamService {
    

    @Resource
    TalentSearchFolderRepository talentSearchFolderRepository;


    @Override
    public void disableSearchFolder(Long talentFolderId) {
        List<TalentSearchFolder> talentSearchFolderList = talentSearchFolderRepository.findAllByTalentFolderId(talentFolderId);
        saveSearchFolderStatusUpdate(talentSearchFolderList, false);
    }

    @Override
    public void disableSearchFolderWithFolderParamFromTeamShared(Long talentFolderId, List<Long> teamIds) {
        List<TalentSearchFolder> talentSearchFolderList = talentSearchFolderRepository.findAllByTalentCustomFolderIdAndTeamIds(talentFolderId, teamIds, true);
        saveSearchFolderStatusUpdate(talentSearchFolderList, false);
    }

    @Override
    public void enableSearchFolderWithFolderParamFromTeamShared(Long talentFolderId, List<Long> teamIds) {
        List<TalentSearchFolder> talentSearchFolderList = talentSearchFolderRepository.findAllByTalentCustomFolderIdAndTeamIds(talentFolderId, teamIds, false);
        saveSearchFolderStatusUpdate(talentSearchFolderList, true);
    }


    @Override
    public void disableSearchFolderWithFolderParamFromUserShared(Long talentFolderId, List<Long> userIds) {
        List<TalentSearchFolder> talentSearchFolderList = talentSearchFolderRepository.findAllByTalentFolderIdAndPermissionUserIdInAndIsActive(talentFolderId, userIds, true);
        saveSearchFolderStatusUpdate(talentSearchFolderList, false);
    }

    @Override
    public void enableSearchFolderWithFolderParamFromUserShared(Long talentFolderId, List<Long> userIds) {
        List<TalentSearchFolder> talentSearchFolderList = talentSearchFolderRepository.findAllByTalentFolderIdAndPermissionUserIdInAndIsActive(talentFolderId, userIds, false);
        saveSearchFolderStatusUpdate(talentSearchFolderList, true);
    }

    private void saveSearchFolderStatusUpdate(List<TalentSearchFolder> talentSearchFolderList, boolean searchFolderActiveStatus){
        if (talentSearchFolderList.size() != 0) {
            talentSearchFolderRepository.saveAll(talentSearchFolderList.stream().
                    map(folder -> {
                        folder.setActive(searchFolderActiveStatus);
                        return folder;
                    }).collect(Collectors.toList())
            );
        }
    }
}
