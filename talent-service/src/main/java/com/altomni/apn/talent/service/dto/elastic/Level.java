package com.altomni.apn.talent.service.dto.elastic;

public enum Level {
    C_LEVEL(70, null),
    VP_LEVEL(55, 60),
    DIRECTOR(50, 55),
    MANAGER(35, 50),
    NON_MANAGER(0, 30);

    private final Integer minScore;

    private final Integer maxScore;

    Level(Integer minScore, Integer maxScore) {
        this.minScore = minScore;
        this.maxScore = maxScore;
    }

//    @JsonCreator
//    public static Level fromValue(String value) {
//        if (StringUtils.equalsIgnoreCase("null", value)) return null;
//        for (Level level : Level.values()) {
//            if (StringUtils.equals(level.name(), value)) {
//                return level;
//            }
//        }
//        throw new IllegalArgumentException("Unknown value " + value);
//    }

    public Integer getMinScore() { return this.minScore; }

    public Integer getMaxScore() { return this.maxScore; }
}
