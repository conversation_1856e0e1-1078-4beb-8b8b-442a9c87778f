package com.altomni.apn.talent.service.job;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.domain.dict.EnumWorkAuthorization;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.talent.CreationTalentType;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.domain.async.AsyncRecord;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;
import com.altomni.apn.job.service.dto.dict.EnumDictDTO;
import io.micrometer.core.annotation.Timed;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
@FeignClient(value = "job-service")
public interface JobService {

    @GetMapping("/job/api/v3/jobs/{id}")
    ResponseEntity<JobV3> findById(@PathVariable("id") Long jobId);

    @GetMapping("/job/api/v3/jobs/no-token/{id}")
    ResponseEntity<JobDTOV3> findByIdNoToken(@PathVariable("id") Long jobId);

    @DeleteMapping("/job/api/v3/jobs/async-record/{asyncEnum}")
    ResponseEntity<Void> clearAllSyncRecordError(@PathVariable("asyncEnum") AsyncEnum asyncEnum, @RequestBody List<Long> successIds);

    @PostMapping("/job/api/v3/jobs/async-record")
    ResponseEntity<AsyncRecord> saveAsyncRecord(@RequestBody AsyncRecord asyncRecord);

    @GetMapping("/job/api/v3/jobs/async-record/async-type/{asyncEnum}/talent/{talentId}/count-sync-error")
    ResponseEntity<Integer> countSyncError(@PathVariable("asyncEnum") AsyncEnum asyncEnum, @PathVariable("talentId") Long talentId);

    @PutMapping("/job/api/v3/jobs/async-record/update-failure-talent-to-success")
    void updateSyncFailureTalentsToSuccess(@RequestBody List<Long> succeedIds);

    @PutMapping("/job/api/v3/jobs/column-preference/user/{userId}/creation-talent-type/{type}/module-type/{moduleType}")
    void updateColumnPreferenceByUserId(@PathVariable("userId") Long userId, @PathVariable("type") CreationTalentType type, @PathVariable("moduleType") ModuleType moduleType);

//    @GetMapping("/job/api/v3/dict/jobFunctions")
//    ResponseEntity<List<EnumDictDTO>> getJobFunctions(@RequestParam(value = "type") SortType type);
//
//    @PostMapping("/job/api/v3/dict/jobFunctions/transfer-by-ids")
//    ResponseEntity<List<String>> transferJobFunctionsByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/jobFunctions/get-by-ids")
//    ResponseEntity<List<String>> getJobFunctionsByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/jobFunctions/transfer-by-names-to-ids-without-ignore-parent-class")
//    ResponseEntity<List<Long>> transferJobFunctionsByNamesToIdWithoutIgnoreParentClass(@RequestBody JSONArray names);

//    @PostMapping(value = "/job/api/v3/dict/jobFunctions/transfer-by-names-to-ids")
//    ResponseEntity<List<Long>> transferJobFunctionsByNamesToId(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/jobFunctions/ui-name")
//    ResponseEntity<List<String>> getJobFunctionsUINameByIds(@RequestBody String ids);

//    @PostMapping("/job/api/v3/dict/jobFunctions/transfer-by-names")
//    public ResponseEntity<List<String>> transferJobFunctionsByNames(@RequestBody List<String> names);

//    @GetMapping("/job/api/v3/dict/industries")
//    public ResponseEntity<List<EnumDictDTO>> getIndustries(@RequestParam(value = "type") SortType type);

//    @PostMapping("/job/api/v3/dict/industries/transfer-by-ids")
//    ResponseEntity<List<String>> transferIndustriesByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/industries/get-by-ids")
//    ResponseEntity<List<String>> getIndustriesByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/industries/transfer-by-names-to-ids-without-ignore-parent-class")
//    ResponseEntity<List<Long>> transferIndustriesByNamesToIdWithoutIgnoreParentClass(@RequestBody JSONArray names);

//    @PostMapping(value = "/job/api/v3/dict/industries/transfer-by-names-to-ids")
//    ResponseEntity<List<Long>> transferIndustriesByNamesToId(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/industries/ui-name")
//    ResponseEntity<List<String>> getIndustriesUINameByIds(@RequestBody String ids);

//    @PostMapping("/job/api/v3/dict/industries/transfer-by-names")
//    ResponseEntity<List<String>> transferIndustriesByNames(@RequestBody List<String> names);

//    @GetMapping("/job/api/v3/dict/workAuthorization")
//    ResponseEntity<List<EnumDictDTO>> getWorkAuthorization(@RequestParam(value = "type") SortType type);

//    @PostMapping("/job/api/v3/dict/workAuthorization/transfer-by-ids")
//    public ResponseEntity<List<String>> transferWorkAuthorizationByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/workAuthorization/get-by-ids")
//    public ResponseEntity<List<String>> getWorkAuthorizationByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/workAuthorization/transfer-by-names-to-ids-without-ignore-parent-class")
//    ResponseEntity<List<Long>> transferWorkAuthorizationByNamesToIdWithoutIgnoreParentClass(@RequestBody JSONArray names);

//    @PostMapping(value = "/job/api/v3/dict/workAuthorization/transfer-by-names-to-ids")
//    ResponseEntity<List<Long>> transferWorkAuthorizationByNamesToId(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/workAuthorization/transfer-by-names")
//    public ResponseEntity<List<String>> transferWorkAuthorizationByNames(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/workAuthorization/name")
//    public ResponseEntity<String> getUINameByName(@RequestBody String name);

//    @PostMapping("/job/api/v3/dict/workAuthorization/names")
//    ResponseEntity<Map<String, String>> getUINameByNames(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/workAuthorization/id")
//    public ResponseEntity<JSONObject> getUINameByName(@RequestBody List<EnumWorkAuthorization> list);

//    @GetMapping("/job/api/v3/dict/degrees")
//    ResponseEntity<List<EnumDictDTO>> getDegrees(@RequestParam(value = "type") SortType type);

//    @PostMapping("/job/api/v3/dict/degrees/transfer-by-ids")
//    ResponseEntity<List<String>> transferDegreesByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/degrees/get-by-ids")
//    ResponseEntity<List<String>> getDegreesByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/degrees/transfer-by-names-to-ids-without-ignore-parent-class")
//    ResponseEntity<List<Long>> transferDegreesByNamesToIdWithoutIgnoreParentClass(@RequestBody JSONArray names);

//    @PostMapping("/job/api/v3/dict/degrees/transfer-by-names-to-ids")
//    ResponseEntity<List<Long>> transferDegreesByNamesToId(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/degrees/transfer-by-names-to-list")
//    ResponseEntity<List<EnumDegree>> transferDegreesByNamesToList(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/degrees/transfer-by-names")
//    ResponseEntity<List<String>> transferDegreesByNames(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/degrees/transfer-by-names-to-map")
//    ResponseEntity<Map<String, List<String>>> transferDegreesByNamesToMap(@RequestBody List<String> names);


//    @GetMapping("/job/api/v3/dict/languages")
//    ResponseEntity<List<EnumDictDTO>> getLanguages(@RequestParam(value = "type") SortType type);

//    @PostMapping("/job/api/v3/dict/languages/transfer-by-ids")
//    ResponseEntity<List<String>> transferLanguagesByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/languages/get-by-ids")
//    ResponseEntity<List<String>> getLanguagesByIds(@RequestBody List<String> ids);

//    @PostMapping("/job/api/v3/dict/languages/transfer-by-names-to-ids-without-ignore-parent-class")
//    ResponseEntity<List<Long>> transferLanguagesByNamesToIdWithoutIgnoreParentClass(@RequestBody JSONArray names);

//    @PostMapping(value = "/job/api/v3/dict/languages/transfer-by-names-to-ids")
//    ResponseEntity<List<Long>> transferLanguagesByNamesToId(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/languages/transfer-by-names")
//    ResponseEntity<List<String>> transferLanguagesByNames(@RequestBody List<String> names);

//    @PostMapping("/job/api/v3/dict/languages/ui-name")
//    ResponseEntity<List<String>> getLanguagesUINameByIds(@RequestBody String ids);

    @GetMapping("/job/api/v3/jobs/hotlist/{hotListId}")
    ResponseEntity<List<JobV3>> findAllByHotListId(@PathVariable("hotListId") Long hotListId);

//    @PostMapping("/job/api/v3/dict/degrees/findAllEnumDegree")
//    ResponseEntity<List<EnumDegree>> findAllEnumDegree();

//    @GetMapping("/job/api/v3/dict/user-responsibility/all")
//    ResponseEntity<List<EnumUserResponsibility>> findAllUserResponsibility();

//    @GetMapping("/job/api/v3/dict/user-responsibility/info/id/{id}")
//    ResponseEntity<EnumUserResponsibility> findUserResponsibilityById(@PathVariable("id") Long id);

//    @GetMapping("/job/api/v3/dict/currency/all")
//    ResponseEntity<List<EnumCurrency>> findAllEnumCurrency();

//    @GetMapping("/job/api/v3/dict/currency/info/id/{id}")
//    ResponseEntity<EnumCurrency> findEnumCurrencyById(@PathVariable("id") Integer id);
//
//    @GetMapping("/job/api/v3/dict/currency/info/name/{name}")
//    ResponseEntity<EnumCurrency> findEnumCurrencyByName(@PathVariable("name") String name);

    @PostMapping(value = "/job/api/v3/brief-jobs/ids")
    ResponseEntity<List<JobBriefDTO>> getBriefJobListByIds(@RequestBody List<Long> ids);


    @PostMapping("/job/api/v3/get-private-job-ids")
    Set<Long> findPrivateJobIds(List<Long> jobIds);
}
