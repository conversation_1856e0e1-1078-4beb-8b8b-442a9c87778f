package com.altomni.apn.talent.service.vo.tracking;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(value = "talent tracking group")
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentTrackingGroupVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "name")
    private String name;

    @ApiModelProperty(value = "operatorLinkedinId")
    private String operatorLinkedinId;

    @ApiModelProperty(value = "memberCount")
    private Integer memberCount;

    @ApiModelProperty(value = "members")
    private List<TalentTrackingVO> members;

}
