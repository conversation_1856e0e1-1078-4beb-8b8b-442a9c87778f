package com.altomni.apn.talent.service.vo.event;

import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.event.EventUser;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * A EventUser.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EventUserVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "event id.", required = true)
    private Long eventId;

    @ApiModelProperty(value = "user first name.", required = true)
    private String firstName;

    @ApiModelProperty(value = "user last name.", required = true)
    private String lastName;

    @ApiModelProperty(value = "user full name")
    private String fullName;

    @ApiModelProperty(value = "email", required = true)
    private String email;

    @ApiModelProperty(value = "phone number")
    private String phone;

    @ApiModelProperty(value = "university")
    private String collegeName;

    @ApiModelProperty(value = "company")
    private String company;

    @ApiModelProperty(value = "user linkedIn url")
    private String linkedIn;

    @ApiModelProperty(value = "user facebook url")
    private String facebook;

    @ApiModelProperty(value = "user twitter url")
    private String twitter;

    private Boolean subscribe;

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getLinkedIn() {
        return linkedIn;
    }

    public void setLinkedIn(String linkedIn) {
        this.linkedIn = linkedIn;
    }

    public String getFacebook() {
        return facebook;
    }

    public void setFacebook(String facebook) {
        this.facebook = facebook;
    }

    public String getTwitter() {
        return twitter;
    }

    public void setTwitter(String twitter) {
        this.twitter = twitter;
    }

    public Boolean getSubscribe() {
        return subscribe;
    }

    public void setSubscribe(Boolean subscribe) {
        this.subscribe = subscribe;
    }

// jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EventUserVO event_user = (EventUserVO) o;
        if (event_user.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), event_user.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "EventUser{" +
            "id=" + id +
            ", eventId=" + eventId +
            ", firstName='" + firstName + '\'' +
            ", lastName='" + lastName + '\'' +
            ", fullName='" + fullName + '\'' +
            ", email='" + email + '\'' +
            ", phone='" + phone + '\'' +
            ", collegeName='" + collegeName + '\'' +
            ", company='" + company + '\'' +
            ", linkedIn='" + linkedIn + '\'' +
            ", facebook='" + facebook + '\'' +
            ", twitter='" + twitter + '\'' +
            ", subscribe=" + subscribe +
            '}';
    }

    public static EventUserVO fromEventUser(EventUser eventUser) {
        EventUserVO eventUserVO = new EventUserVO();
        ServiceUtils.myCopyProperties(eventUser, eventUserVO);
        return eventUserVO;
    }
}
