package com.altomni.apn.talent.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A HotListUser.
 */
@ApiModel(description = "Mapping of user and hotList relation.")
@Entity
@Table(name = "hot_list_user")
public class HotListUser extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "hot list id", required = true)
    @Column(name = "hot_list_id")
    private Long hotListId;

    @ApiModelProperty(value = "user id", required = true)
    @Column(name = "user_id")
    private Long userId;

    public Long getHotListId() {
        return hotListId;
    }

    public HotListUser hotListId(Long hotListId) {
        this.hotListId = hotListId;
        return this;
    }

    public void setHotListId(Long hotListId) {
        this.hotListId = hotListId;
    }

    public Long getUserId() {
        return userId;
    }

    public HotListUser userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HotListUser hotListUser = (HotListUser) o;
        return Objects.equals(userId, hotListUser.userId) &&
            Objects.equals(hotListId, hotListUser.hotListId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, hotListId);
    }

    @Override
    public String toString() {
        return "HotListUser{" +
            "id=" + id +
            ", hotListId=" + hotListId +
            ", userId=" + userId +
            '}';
    }
}
