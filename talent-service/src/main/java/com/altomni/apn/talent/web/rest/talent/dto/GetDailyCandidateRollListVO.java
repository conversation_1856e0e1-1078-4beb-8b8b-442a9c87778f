package com.altomni.apn.talent.web.rest.talent.dto;

import lombok.Data;

import java.util.List;

@Data
public class GetDailyCandidateRollListVO {
    private List<TalentSearchFolderData> dailyCandidateRollList;
    private boolean searchFolderUpdate;
    private String searchDate;

    public GetDailyCandidateRollListVO() {
    }

    public GetDailyCandidateRollListVO(List<TalentSearchFolderData> dailyCandidateRollList, boolean searchFolderUpdate, String searchDate) {
        this.dailyCandidateRollList = dailyCandidateRollList;
        this.searchFolderUpdate = searchFolderUpdate;
        this.searchDate = searchDate;
    }
}
