package com.altomni.apn.talent.web.rest.event;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.event.Event;
import com.altomni.apn.talent.service.dto.event.EventDTO;
import com.altomni.apn.talent.service.event.EventService;
import com.altomni.apn.talent.service.vo.event.EventVO;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Event.
 */
@RestController
@RequestMapping("/api/v3")
public class EventResource {

    private final Logger log = LoggerFactory.getLogger(EventResource.class);

    private static final String ENTITY_NAME = "event";

    private final EventService eventService;

    public EventResource(EventService eventService) {
        this.eventService = eventService;
    }

    /**
     * POST  /events : Create a new event.
     * 
     * @param event the event to create
     * @return the ResponseEntity with status 201 (Created) and with body the new event, or with status 400 (Bad Request) if the event has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/events")
    @NoRepeatSubmit
    @Timed
    public ResponseEntity<EventVO> createEvent(@Valid @RequestBody EventDTO event) throws URISyntaxException {
        log.info("[APN: Event @{}] REST request to save Event : {}", SecurityUtils.getUserId(), event);
        EventVO result = eventService.create(event);
        return ResponseEntity.created(new URI("/api/events/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * PUT  /events : Updates an existing event.
     *
     * @param event the event to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated event,
     * or with status 400 (Bad Request) if the event is not valid,
     * or with status 500 (Internal Server Error) if the event couldn't be updated
     */
    @PutMapping("/events/{id}")
    @NoRepeatSubmit
    @Timed
    public ResponseEntity<EventVO> updateEvent(@PathVariable Long id, @Valid @RequestBody EventDTO event) {
        log.info("[APN: Event @{}] REST request to update Event : {}", SecurityUtils.getUserId(), event);
        event.setId(id);
        EventVO result = eventService.update(event);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * GET  /events : get all the events.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of events in body
     */
    @GetMapping("/events")
    @NoRepeatSubmit
    @Timed
    public List<EventVO> getAllEvents() {
        log.info("[APN: Event @{}] REST request to get all Events", SecurityUtils.getUserId());
        return eventService.findAll();
    }

    /**
     * GET  /events/:id : get the "id" event.
     *
     * @param id the id of the event to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the event, or with status 404 (Not Found)
     */
    @GetMapping("/events/{id}")
    @NoRepeatSubmit
    @Timed
    public ResponseEntity<EventVO> getEvent(@PathVariable Long id) {
        log.info("[APN: Event @{}] REST request to get Event : {}", SecurityUtils.getUserId(), id);
        EventVO event = eventService.findOne(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(event));
    }
}
