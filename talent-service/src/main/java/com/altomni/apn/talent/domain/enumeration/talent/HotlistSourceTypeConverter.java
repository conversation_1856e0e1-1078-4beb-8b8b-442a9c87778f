package com.altomni.apn.talent.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class HotlistSourceTypeConverter extends AbstractAttributeConverter<HotlistSourceType, Integer> {
    public HotlistSourceTypeConverter() {
        super(HotlistSourceType::toDbValue, HotlistSourceType::fromDbValue);
    }
}
