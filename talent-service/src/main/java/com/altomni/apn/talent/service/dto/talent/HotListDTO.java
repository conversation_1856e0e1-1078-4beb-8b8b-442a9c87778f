package com.altomni.apn.talent.service.dto.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.talent.HotlistAccessOption;
import com.altomni.apn.talent.domain.enumeration.talent.HotlistSourceType;
import com.altomni.apn.talent.domain.user.HotList;
import com.altomni.apn.talent.service.dto.user.HotListUserDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * A HotListDTO.
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HotListDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "hot list id", required = true)
    private Long id;

    @ApiModelProperty(value = "hot list title", required = true)
    private String title;

    @ApiModelProperty(value = "hot list notes", required = true)
    private String notes;

    @ApiModelProperty(value = "hot list source type", required = true)
    private HotlistSourceType sourceType;

    @ApiModelProperty(value = "hot list access option : public/private", required = true)
    private HotlistAccessOption accessOption;

    @ApiModelProperty(value = "The tenant id talent belongs to. This is saved from the user's tenant when creating talent.")
    private Long tenantId;

    // ************************************relate entity*********************************************
    @ApiModelProperty(value = "Hotlist user group.")
    private List<HotListUserDTO> hotListUsers = new ArrayList<>();

    public static HotListUserDTO fromHotList (HotList hotList) {
        HotListUserDTO dto = new HotListUserDTO();
        ServiceUtils.myCopyProperties(hotList, dto);
        return dto;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public HotListDTO title(String title) {
        this.title = title;
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getNotes() {
        return notes;
    }

    public HotListDTO notes(String notes) {
        this.notes = notes;
        return this;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public HotListDTO tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public HotlistSourceType getSourceType() {
        return sourceType;
    }

    public void setSourceType(HotlistSourceType sourceType) {
        this.sourceType = sourceType;
    }

    public HotlistAccessOption getAccessOption() {
        return accessOption;
    }

    public void setAccessOption(HotlistAccessOption accessOption) {
        this.accessOption = accessOption;
    }

    public List<HotListUserDTO> getHotListUsers() {
        return hotListUsers;
    }

    public void setHotListUsers(List<HotListUserDTO> hotListUsers) {
        this.hotListUsers = hotListUsers;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HotListDTO hotList = (HotListDTO) o;
        if (hotList.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), hotList.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "HotListDTO{" +
            "id=" + id +
            ", title='" + title + '\'' +
            ", notes='" + notes + '\'' +
            ", sourceType=" + sourceType +
            ", accessOption=" + accessOption +
            ", tenantId=" + tenantId +
            ", hotListUsers=" + hotListUsers +
            '}';
    }
}
