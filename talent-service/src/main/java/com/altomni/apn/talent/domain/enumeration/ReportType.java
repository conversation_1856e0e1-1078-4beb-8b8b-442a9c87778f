package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ReportType enumeration.
 * <AUTHOR>
 */
public enum ReportType implements ConvertedEnum<Integer> {
    /**
     * Old Report
     */
    COMPANY(10),
    RECRUITER(11),
    COMPANY_RECRUITER(12),
    HIRING_MANAGER(13),
    ALL_OPEN_JOBS_BY_COMPANY(14),
    TREND_MONTH(20),
    TREND_COMPANY(21),
    PIPELINE_RECRUITER(30),
    PIPELINE_COMPANY(31),
    PIPELINE_SOURCER(32),
    PIPELINE_CONTRIBUTOR(33),

    /**
     * G Report
     */
    G2_CURRENT_STATUS_APPLICATION(100),
    G2_ALL_STATUS_APPLICATION(110),
    G4_CURRENT_STATUS_APPLICATION(120),
    G4_ALL_STATUS_APPLICATION(130),

    /**
     * V2 Report
     */
    V2_JOB_BY_COMPANY(210),
    V2_JOB_BY_USER(211),
    V2_JOB_DETAILS(212),
    V2_USER_ALL_SOURCED_TALENTS_STATUS_CHANGED(310),
    V2_USER_ALL_SOURCED_TALENTS_STATUS_CHANGED_WEEKS(311),

    /**
     * P Report
     */
    P1_PIPELINE_ANALYTICS_BY_USERS(320),
    P4_PIPELINE_DETAILS(321),


    /**
     * S3 Report
     */
    USER_JOB_TALENT(410);



    private final int dbValue;

    ReportType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ReportType, Integer> resolver =
        new ReverseEnumResolver<>(ReportType.class, ReportType::toDbValue);

    public static ReportType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
