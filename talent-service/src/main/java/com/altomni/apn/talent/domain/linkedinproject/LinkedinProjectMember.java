package com.altomni.apn.talent.domain.linkedinproject;

import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.domain.user.User;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A LinkedinProjectMember.
 */
@Entity
@Table(name = "linkedin_project_member")
public class LinkedinProjectMember implements Serializable {

    private static final long serialVersionUID = 219568413392529591L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "linkedin_project_id")
    private Long linkedinProjectId;

    @Column(name = "user_id")
    private Long userId;

    @Transient
    @JsonProperty
    public User user;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLinkedinProjectId() {
        return linkedinProjectId;
    }

    public void setLinkedinProjectId(Long linkedinProjectId) {
        this.linkedinProjectId = linkedinProjectId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
        this.user.setMonthlyCredit(null);
        this.user.setBulkCredit(null);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinProjectMember linkedinProjectMember = (LinkedinProjectMember) o;
        if (linkedinProjectMember.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinProjectMember.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinProjectMember{" +
            "id=" + id +
            ", linkedinProjectId=" + linkedinProjectId +
            ", userId=" + userId +
            ", user=" + user +
            '}';
    }
}
