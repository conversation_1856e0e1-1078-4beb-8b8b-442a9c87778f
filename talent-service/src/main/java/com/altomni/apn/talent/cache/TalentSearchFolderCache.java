package com.altomni.apn.talent.cache;

import com.altomni.apn.talent.service.folder.TalentSearchFolderService;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.CacheStats;
import com.google.common.cache.LoadingCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class TalentSearchFolderCache {
    @Resource
    private TalentSearchFolderService talentSearchFolderService;

    private LoadingCache<Long, Long> candidateCountCache;

    private static final int MAX_CACHE_SIZE = 2000;  // 最大缓存条目数
    private static final int EXPIRE_DAYS = 1;         // 过期时间(天)

    @PostConstruct
    public void init() {
        candidateCountCache = CacheBuilder.newBuilder()
                .maximumSize(MAX_CACHE_SIZE)              // 设置最大缓存条目
                .expireAfterWrite(EXPIRE_DAYS, TimeUnit.DAYS)  // 一天后过期
                .recordStats()                            // 记录统计信息
                .removalListener(notification -> {
                    log.info("Cache entry removed. Key: {}, Cause: {}",
                            notification.getKey(), notification.getCause());
                })
                .build(new CacheLoader<Long, Long>() {
                    @Override
                    public Long load(Long folderId) {
                        return loadCandidateCount(folderId);
                    }
                });
    }

    // 获取文件夹候选人数量
    public Long getCandidateCount(Long folderId) {
        try {
            return candidateCountCache.get(folderId);
        } catch (ExecutionException e) {
            log.error("Error getting candidate count for folder: " + folderId, e);
            // 发生错误时直接从数据库加载
            return loadCandidateCount(folderId);
        }
    }

    // 手动更新缓存
    public void updateCandidateCount(Long folderId) {
        candidateCountCache.invalidate(folderId);
        // 可选：预加载
        try {
            candidateCountCache.get(folderId);
        } catch (ExecutionException e) {
            log.error("Error preloading candidate count for folder: " + folderId, e);
        }
    }

    // 获取缓存统计信息
    public CacheStats getCacheStats() {
        return candidateCountCache.stats();
    }

    // 清除所有缓存
    public void clearCache() {
        candidateCountCache.invalidateAll();
    }

    // 从服务加载数据的方法
    private Long loadCandidateCount(Long folderId) {
        log.info("Loading candidate count for folder: {}", folderId);
        return talentSearchFolderService.countCandidatesInFolder(folderId);
    }
}
