package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.talent.service.dto.email.Recipient;
import com.altomni.apn.talent.web.rest.vm.HotlistTalentVM;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.TypedQuery;
import java.util.List;

@Repository
public class TalentHotlistVMRepositoryImpl implements TalentHotlistVMRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<HotlistTalentVM> findTalentsByHotListId(Long hotListId) {
        String query = "SELECT NEW com.altomni.apn.talent.web.rest.vm.HotlistTalentVM(t.id, t.tenantId, t.firstName, t.lastName, t.fullName, t.createdBy, t.createdDate, t.lastModifiedBy, t.lastModifiedDate) " +
                "FROM TalentV3 t " +
                "LEFT JOIN HotListTalent hlt ON hlt.talentId = t.id WHERE hlt.hotListId = ?1";

        TypedQuery<HotlistTalentVM> typedQuery = entityManager.createQuery(query, HotlistTalentVM.class)
                .setParameter(1, hotListId);

        return typedQuery.getResultList();
    }

}
