package com.altomni.apn.talent.service.vo.tracking;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class LinkedinTalentBasicInfo {
    @ApiModelProperty(value = "firstName")
    private String firstName;

    @ApiModelProperty(value = "lastName")
    private String lastName;

    @ApiModelProperty(value = "photoUrl")
    private String photoUrl;

    @ApiModelProperty(value = "title")
    private String title;

    @ApiModelProperty(value = "talentLinkedinId")
    private String talentLinkedinId;

    @ApiModelProperty(value = "linkUrl")
    private String linkUrl;
}
