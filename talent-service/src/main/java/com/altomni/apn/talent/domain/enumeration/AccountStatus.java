package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobStatus enumeration.
 */
public enum AccountStatus implements ConvertedEnum<Integer> {

    AVAILABLE(0),

    FROZEN(1),

    INVALID(-1);

    private final int dbValue;

    AccountStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<AccountStatus, Integer> resolver =
        new ReverseEnumResolver<>(AccountStatus.class, AccountStatus::toDbValue);

    public static AccountStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
