package com.altomni.apn.talent.service.dto.tracking;


import com.altomni.apn.talent.domain.enumeration.tracking.TrackingCategory;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingCategoryConverter;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingStatus;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingStatusConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;


@Data
@ApiModel(value = "talent tracking search")
@NoArgsConstructor
@AllArgsConstructor
public class TalentTrackingSearchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "operatorLinkedinId")
    @NotEmpty
    private String operatorLinkedinId;

    @ApiModelProperty(value = "category")
    @Convert(converter = TrackingCategoryConverter.class)
    private TrackingCategory category;

    @ApiModelProperty(value = "category")
    @Convert(converter = TrackingStatusConverter.class)
    private TrackingStatus status;

}
