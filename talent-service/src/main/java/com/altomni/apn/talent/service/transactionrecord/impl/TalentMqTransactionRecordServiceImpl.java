package com.altomni.apn.talent.service.transactionrecord.impl;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.domain.transactionrecord.CommonMqTransactionRecord;
import com.altomni.apn.common.enumeration.enums.MqTranRecordBusTypeEnums;
import com.altomni.apn.common.enumeration.enums.MqTranRecordStatusEnums;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentTxMQProperties;
import com.altomni.apn.talent.repository.transactionrecord.TalentCommonMqTransactionRecordRepository;
import com.altomni.apn.talent.service.transactionrecord.TalentMqTransactionRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * mq 事务记录
 */
@Service
@Slf4j
public class TalentMqTransactionRecordServiceImpl implements TalentMqTransactionRecordService {

    @Resource
    TalentCommonMqTransactionRecordRepository talentCommonMqTransactionRecordRepository;

    @Resource(name = "talentTxRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource
    private TalentTxMQProperties talentTxMQProperties;

    @Value("${application.notification.lark.mq.webhookKey}")
    private String LARK_WEBHOOK_KEY;

    @Value("${application.notification.lark.mq.webhookUrl}")
    private String LARK_WEBHOOK_URL;

    @Value("${application.notification.lark.mq.enabled}")
    private boolean enabled;

    @Value("${application.notification.lark.mq.threshold:3}")
    private Integer threshold;

    /**
     * mq 事务重试
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String searchMqTxSendFail() {
        List<CommonMqTransactionRecord> commonMqTransactionRecordList = talentCommonMqTransactionRecordRepository.
                findBySendStatusInAndSendCountLessThan(Arrays.asList(MqTranRecordStatusEnums.PENDING.toDbValue(),MqTranRecordStatusEnums.NOT_ARRIVE_QUEUE.toDbValue()),threshold);
        if (null != commonMqTransactionRecordList && !commonMqTransactionRecordList.isEmpty()) {
            for (CommonMqTransactionRecord record : commonMqTransactionRecordList) {

                Integer i = record.getSendCount() + 1;
                talentCommonMqTransactionRecordRepository.updateStatusAndSendCountById(record.getId().longValue(), i);
                log.info("[APN: TalentService @{}] retry send to talent tx rabbit, update send count, send count :{},send id:{}", i, record.getId());

                rabbitTemplate.convertAndSend(talentTxMQProperties.getTalentTxMQExchange(), talentTxMQProperties.getTalentTxMQRoutingKey(), record.getSendContent(), new CorrelationData(String.valueOf(record.getId())));
                log.info("[APN: TalentService @{}] retry send to talent tx rabbit -> param: {}", SecurityUtils.getUserId(), JSON.toJSONString(record.getSendContent()));

                if (i.equals(threshold)) {
                    String msg = "talent-service-> 业务类型：" + MqTranRecordBusTypeEnums.fromDbValue(record.getBusType()).getDesc() + ", 发送到MQ数据已经超过3次，需要人工处理。";
                    NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, msg);
                }
            }
        }
        return "success";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusById(Long id, Integer sendStatus) {
        talentCommonMqTransactionRecordRepository.updateStatusById(id, sendStatus);
        log.info("update transaction record status is {}, id:{}", sendStatus, id);
    }

    /**
     * 发送lark
     *
     * @param message
     */
    @Override
    public void sendExceptionByLark(String message) {
        if (StringUtils.isNotBlank(message)) {
            NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, message);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusByBusIdAndBusType(Long busId, Integer busType, Integer sendStatus) {
        talentCommonMqTransactionRecordRepository.updateStatusByBusIdAndBusType(busId, busType, sendStatus);
        log.info("update transaction record status is {}, busId:{}, busType:{}", sendStatus, busId, busType);
    }

    @Override
    public CommonMqTransactionRecord findById(BigInteger id) {
        Optional<CommonMqTransactionRecord> optional = talentCommonMqTransactionRecordRepository.findById(id);
        if(optional.isPresent()){
            return optional.get();
        }else{
            try {
                Thread.sleep(10000);
                optional = talentCommonMqTransactionRecordRepository.findById(id);
                if(optional.isPresent()) {
                    return optional.get();
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }
}