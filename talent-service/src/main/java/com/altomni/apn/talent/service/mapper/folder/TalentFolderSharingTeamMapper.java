package com.altomni.apn.talent.service.mapper.folder;

import com.altomni.apn.common.dto.folder.CustomFolderSharingTargetDTO;
import com.altomni.apn.talent.domain.folder.TalentFolderSharingTeam;
import com.altomni.apn.talent.service.dto.folder.TalentFolderSharingTeamDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public interface TalentFolderSharingTeamMapper {

    @Mapping(target = "excludedUserIds", source = "excludedUserIds", qualifiedByName = "jsonStringToSet")
    TalentFolderSharingTeamDTO toDto(TalentFolderSharingTeam entity);

    @Mapping(target = "targetId", source = "teamId")
    @Mapping(target = "targetCategory", constant = "TEAM")
    CustomFolderSharingTargetDTO toFolderSharingDto(TalentFolderSharingTeam entity);

    @Mapping(target = "targetId", source = "teamId")
    @Mapping(target = "targetCategory", constant = "TEAM")
    List<CustomFolderSharingTargetDTO> toFolderSharingDto(List<TalentFolderSharingTeam> entities);

    @Mapping(target = "excludedUserIds", source = "excludedUserIds", qualifiedByName = "setToJsonString")
    TalentFolderSharingTeam toEntity(TalentFolderSharingTeamDTO dto);

    @Mapping(target = "teamId", source = "targetId")
    TalentFolderSharingTeam toEntity(CustomFolderSharingTargetDTO dto);

    @Named("jsonStringToSet")
    default Set<Long> jsonStringToSet(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return new HashSet<>();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(jsonString, new TypeReference<Set<Long>>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error parsing JSON string", e);
        }
    }

    @Named("setToJsonString")
    default String setToJsonString(Set<Long> set) {
        if (set == null) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(set);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting set to JSON string", e);
        }
    }
}
