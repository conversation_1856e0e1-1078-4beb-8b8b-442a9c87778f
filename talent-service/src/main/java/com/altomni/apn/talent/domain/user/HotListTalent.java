package com.altomni.apn.talent.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A HotListTalent.
 */
@Entity
@Table(name = "hot_list_talent")
public class HotListTalent extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "hot list id", required = true)
    @Column(name = "hot_list_id")
    private Long hotListId;

    @ApiModelProperty(value = "talent id", required = true)
    @Column(name = "talent_id")
    private Long talentId;

    @ApiModelProperty(value = "job id", required = true)
    @Column(name = "job_id")
    private Long jobId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getHotListId() {
        return hotListId;
    }

    public HotListTalent hotListId(Long hotListId) {
        this.hotListId = hotListId;
        return this;
    }

    public void setHotListId(Long hotListId) {
        this.hotListId = hotListId;
    }

    public Long getTalentId() {
        return talentId;
    }

    public HotListTalent talentId(Long talentId) {
        this.talentId = talentId;
        return this;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HotListTalent hotListTalent = (HotListTalent) o;
        if (hotListTalent.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), hotListTalent.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "HotListTalent{" +
            "id=" + id +
            ", hotListId=" + hotListId +
            ", talentId=" + talentId +
            ", jobId=" + jobId +
            '}';
    }

    public HotListTalent() {
    }

    public HotListTalent(Long hotListId, Long talentId) {
        this.hotListId = hotListId;
        this.talentId = talentId;
    }
}
