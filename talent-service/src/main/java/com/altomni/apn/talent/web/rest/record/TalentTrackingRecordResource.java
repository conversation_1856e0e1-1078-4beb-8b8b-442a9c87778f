package com.altomni.apn.talent.web.rest.record;


import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.service.dto.record.TalentTrackingEmailDTO;
import com.altomni.apn.talent.service.dto.record.TalentTrackingRecordDTO;
import com.altomni.apn.talent.service.dto.record.TalentTrackingRecordSearchDTO;
import com.altomni.apn.talent.service.record.TalentTrackingRecordService;
import com.altomni.apn.talent.service.vo.record.TalentTrackingRecordListVO;
import com.altomni.apn.talent.service.vo.record.TalentTrackingRecordVO;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing TalentTrackingRecord.
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3")
public class TalentTrackingRecordResource {

    private final Logger log = LoggerFactory.getLogger(TalentTrackingRecordResource.class);

    private static final String ENTITY_NAME = "TalentTrackingRecord";

    private final TalentTrackingRecordService talentTrackingRecordService;

    public TalentTrackingRecordResource(TalentTrackingRecordService talentTrackingRecordService) {
        this.talentTrackingRecordService = talentTrackingRecordService;
    }

    /**
     * POST  /talent-tracking-records : Create a new talentTrackingRecord.
     *
     * @param talentTrackingRecordDTO the talentTrackingRecord to create
     * @return the ResponseEntity with status 201 (Created) and with body the new talentTrackingRecord, or with status 400 (Bad Request) if the talentTrackingRecord has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/talent-tracking-records")
    @Timed
    public ResponseEntity<TalentTrackingRecordVO> createTalentTrackingRecord(@Valid @RequestBody TalentTrackingRecordDTO talentTrackingRecordDTO) throws URISyntaxException {
        log.info("[APN V3: TalentTrackingRecord @{}] REST request to save TalentTrackingRecords: {}", SecurityUtils.getUserId(), talentTrackingRecordDTO);

        TalentTrackingRecordVO result = talentTrackingRecordService.save(talentTrackingRecordDTO);
        return ResponseEntity.created(new URI("/api/v1/talent-tracking-records/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * GET  /talent-tracking-records : get all the talentTrackingRecords.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talentTrackingRecords in body
     */
    @PostMapping("/talent-tracking-records/search")
    @Timed
    public List<TalentTrackingRecordListVO> getAllTalentTrackingRecords(@RequestBody TalentTrackingRecordSearchDTO talentTrackingRecordSearchDTO) {
        log.info("[APN: TalentTrackingRecord @{}] REST request to get all TalentTrackingRecords: {}", SecurityUtils.getUserId(), talentTrackingRecordSearchDTO);
        return talentTrackingRecordService.findAllTalentTrackingRecords(talentTrackingRecordSearchDTO);
    }

    @PostMapping("/talent-tracking-records/send_rich_mail")
    @Timed
    public ResponseEntity<Void> sendRichEmail(@Valid @RequestBody TalentTrackingEmailDTO talentTrackingEmailDTO){
        log.info("[APN: TalentTrackingRecord @{}] REST request to send rich email: {}", SecurityUtils.getUserId(), talentTrackingEmailDTO);
        talentTrackingRecordService.sendRichEmail(talentTrackingEmailDTO);
        return ResponseEntity.ok().build();
    }
}
