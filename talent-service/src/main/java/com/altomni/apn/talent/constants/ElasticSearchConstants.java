package com.altomni.apn.talent.constants;

import java.util.Arrays;
import java.util.List;

/**
 * Application constants.
 *
 * <AUTHOR>
 */
public final class ElasticSearchConstants {

    public static final String HITS = "hits";

    public static final String SOURCE = "_source";

    public static final String ID = "_id";

    public static final String TOTAL = "total";

    public static final String MUST = "must";

    public static final String MUST_NOT = "must_not";

    public static final String QUERY = "query";

    public static final String BOOL = "bool";

    public static final String VIEW = "view";

    public static final String SCORES = "scores";

    public static final String CONTACTS = "contacts";

    public static final List<String> JOB_KEY = Arrays.asList("id", "tenantId", "company", "department", "title", "jobType", "code",
        "startDate", "endDate", "postingTime", "status", "text", "clientContactCategory", "clientContactName",
        "jdText", "publicDesc", "jdUrl", "locations", "jobFunctions",
        "assignedUsers", "requiredSkills", "preferredSkills", "requiredLanguages", "preferredLanguages",
        "visible", "favorite", "currency", "billRange", "salaryRange", "payType", "openings",
        "maxSubmissions", "minimumDegreeLevel", "experienceYearRange");

    public static final List<String> JOB_SEARCH_SOURCE = Arrays.asList("title", "companyName", "companyId", "code", "type", "status", "postingTime",
        "assignedUsers", "locations.originDisplay", "jobFunctions", "jobFunctionDisplays", "minimumDegreeLevel", "requiredSkills.skillName",
        "preferredSkills.skillName", "requiredLanguages", "preferredLanguages", "currency", "experienceYearRange");


    public static final String INDEX_JOBS = "jobs";

    public static final String INDEX_TALENTS = "talents";

    public static final String INDEX_STARTS = "starts";

    public static final Integer ES_FILLER_PARTITION_MAX_NUMBER = 100;

    public ElasticSearchConstants() {

    }
}
