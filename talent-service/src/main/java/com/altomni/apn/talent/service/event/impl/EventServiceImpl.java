package com.altomni.apn.talent.service.event.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.enumeration.event.Status;
import com.altomni.apn.talent.domain.event.Event;
import com.altomni.apn.talent.repository.event.EventRepository;
import com.altomni.apn.talent.service.dto.event.EventDTO;
import com.altomni.apn.talent.service.event.EventService;
import com.altomni.apn.talent.service.vo.event.EventVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EventServiceImpl implements EventService {

    private final EventRepository eventRepository;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final TalentApiPromptProperties talentApiPromptProperties;

    public EventServiceImpl(EventRepository eventRepository,
                            CommonApiMultilingualConfig commonApiMultilingualConfig,
                            TalentApiPromptProperties talentApiPromptProperties) {
        this.eventRepository = eventRepository;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.talentApiPromptProperties = talentApiPromptProperties;
    }

    @Override
    public EventVO create(EventDTO event) {
        if (event.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.EVENT_CREATE_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        return EventVO.fromEvent(eventRepository.save(Event.fromEventDTO(event)));
    }

    @Override
    public EventVO update(EventDTO update) {
        if (update.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.EVENT_UPDATE_IDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        Event existEvent = eventRepository.findById(update.getId()).orElse(null);
        if (existEvent == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.EVENT_UPDATE_EVENTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        ServiceUtils.myCopyProperties(update, existEvent, Event.UpdateSkipProperties);
        return EventVO.fromEvent(eventRepository.save(existEvent));
    }

    @Override
    public EventVO findOne(Long id) {
        Event existEvent = eventRepository.findById(id).orElse(new Event());
        return EventVO.fromEvent(existEvent);
    }

    @Override
    public List<EventVO> findAll() {
        return eventRepository.findAllByStatus(Status.Available).stream().map(EventVO::fromEvent).collect(Collectors.toList());
    }
}
