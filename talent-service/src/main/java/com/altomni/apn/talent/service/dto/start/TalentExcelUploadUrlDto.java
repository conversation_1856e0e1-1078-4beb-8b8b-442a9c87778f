package com.altomni.apn.talent.service.dto.start;

import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.store.UploadUrlDto;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.Data;

@Data
public class TalentExcelUploadUrlDto extends UploadUrlDto {

    private String hotListId;

    private String taskId;

    public String getTaskId() {
        return getUuid() + "-" + SecurityUtils.getTenantId() + (StrUtil.isNotBlank(getHotListId())? "-" + getHotListId(): "");
    }

}
