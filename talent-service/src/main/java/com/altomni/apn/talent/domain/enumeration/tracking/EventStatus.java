package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EventStatus implements ConvertedEnum<Integer> {
    NORMAL(0), COMPLETE(1);
    private int val;

    EventStatus(int val) {
        this.val = val;
    }

    @Override
    public Integer toDbValue() {
        return 0;
    }

    // static resolving:
    public static final ReverseEnumResolver<EventStatus, Integer> resolver =
            new ReverseEnumResolver<>(EventStatus.class, EventStatus::toDbValue);

    public static EventStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
