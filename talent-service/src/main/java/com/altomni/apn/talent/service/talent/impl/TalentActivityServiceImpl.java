package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.util.NumberUtil;
import com.altomni.apn.common.aop.user.SimpleUserAspect;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.activity.*;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.enumeration.enums.DisplayType;
import com.altomni.apn.common.service.activity.ActivityConfigService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.domain.user.SimpleUserInfoDTO;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.dto.activitylog.TalentActivityDTO;
import com.altomni.apn.talent.service.talent.TalentActivityService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TalentActivityServiceImpl implements TalentActivityService {

    final static String TALENT_ACTIVITY_KEY = "talentId";

    final static String esIndex = "activity_talent_";

    final static String UNKNOWNUSER = "Unknown user";
    final static String SYSTEM = "System";

    final static String DEFAULT_SORT_KEY = "createdDate";

    final static String DEFAULT_SORT_ORDER = "desc";

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private ActivityConfigService activityConfigService;

    @Resource
    private HttpService httpService;

    @Resource
    private UserService userService;


    private String getCommonServiceUrl() {
        return applicationProperties.getTalentActivityESUrl() + esIndex + SecurityUtils.getTenantId() + "/_search";
    }

    @Override
    public Page<TalentActivityDTO> getTalentActivities(Long talentId, Pageable pageable) throws Throwable {
        ActivityESRequestDTO activityESRequestDTO = activityConfigService.generateESRequest(TALENT_ACTIVITY_KEY, talentId, pageable, DEFAULT_SORT_KEY, DEFAULT_SORT_ORDER);
        Gson gson = new Gson();
        String condition = gson.toJson(activityESRequestDTO);

        String url = getCommonServiceUrl();
        DisplayType displayType = DisplayType.BILINGUAL;
        List<TalentActivityDTO> talentActivityDTOList = new ArrayList<>();
        Long total = 0L;
        try {
            log.info("[APN: Es TalentActivity @{}] search talentActivity from activity service request, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
            HttpResponse response = httpService.post(url, condition);
            if (response != null) {
                if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.info("[APN: Es TalentActivity @{}] search talentActivity from activity service success, response body: {}", SecurityUtils.getUserId(), response.getBody());
                    ESResponse esResponse = parseESResponse(response.getBody());
                    total = esResponse.getHits().getTotal().getValue();

                    talentActivityDTOList.addAll(formatTalentActivity(getSourceChangeFromResponse(esResponse), displayType, talentId));
                } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                    log.error("[APN: Es TalentActivity @{}] search talentActivity from activity service failed, searchRequest: {}, pageable:{}, responseCode:{}", SecurityUtils.getUserId(), condition, pageable, response.getCode());
                }
            }

        } catch (Exception ex) {
            log.error(ex.toString());
            throw ex.getCause();
        }

        return new PageImpl<>(talentActivityDTOList, pageable, total);
    }

    /***
     * format talent activityDTO for displaying
     * @param esActivitySourceDTOList
     * @param talentId
     * @return
     */
    private List<TalentActivityDTO> formatTalentActivity(List<ESActivitySourceDTO> esActivitySourceDTOList, DisplayType displayType, Long talentId) {
        log.info("[APN User {} get talent {} Activity: format talent Activity ", SecurityUtils.getUserId(), talentId);
        if (esActivitySourceDTOList == null) {
            return null;
        }
        //current dont need to set the value for the enum
        //Map<String, ActivityConfig> activityConfigMap = activityConfigService.findAllTalentActivityConfig(SourceType.JOB);
        // 先判断集合是否为null，再过滤掉集合中的null元素，最后处理字段
        List<String> uids = Optional.ofNullable(esActivitySourceDTOList)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .map(ESActivitySourceDTO::getCreatedBy)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<String> userIds = uids.stream().filter(NumberUtil::isNumber).collect(Collectors.toList());
        List<User> responseFindByIds = userService.findByIds(userIds.stream()
                .map(NumberUtils::toLong)
                .collect(Collectors.toList())).getBody();
        Map<String, User> userMap = responseFindByIds.stream()
                .collect(Collectors.toMap(User::getStringId, user -> user));

        List<TalentActivityDTO> talentActivityDTOList = Optional.ofNullable(esActivitySourceDTOList)
                .orElse(Collections.emptyList())
                .stream()
                // 过滤掉集合中的null元素，避免后续操作NPE
                .filter(Objects::nonNull)
                // 转换为TalentActivityDTO，同时处理转换过程中的可能null值
                .map(activity -> {
                    TalentActivityDTO talentActivityDTO = new TalentActivityDTO();
                    //convert changeField from db value to view value
                    if (activity.getChangeFields() != null) {
                        List<ChangeFieldDTO> changeDTOList = new ArrayList<>();
                        Map<String, ChangeFieldDTO> keyChangeFieldDTO = new HashMap<>();
                        for (ActivityChangeDTO changeDTO : activity.getChangeFields()) {
                            if (changeDTO.getKey() == null || StringUtils.isBlank(changeDTO.getKey())) continue;
                            ChangeFieldDTO changeFieldDTO = new ChangeFieldDTO();
                            changeFieldDTO.setChangeFieldDTOFromActivityDTO(changeDTO);
                            Long fieldId = changeDTO.getFieldId();
                            if (fieldId != null) {
                                changeFieldDTO.setFieldId(fieldId);
                            }
                            if (keyChangeFieldDTO.containsKey(changeFieldDTO.getKey())) {
                                if (StringUtils.isNotBlank(changeFieldDTO.getChangedFrom()) || StringUtils.isNotBlank(changeFieldDTO.getChangedTo())) {
                                    changeDTOList.add(changeFieldDTO);
                                }
                            } else {
                                keyChangeFieldDTO.put(changeFieldDTO.getKey(), changeFieldDTO);
                                changeDTOList.add(changeFieldDTO);
                            }
                        }
                        talentActivityDTO.setChangeFields(changeDTOList);
                    }
                    talentActivityDTO.setTalentId(talentId);
                    talentActivityDTO.setCreatedDate(activity.getCreatedDate());
                    //set operator info
                    SimpleUserInfoDTO userDTO = new SimpleUserInfoDTO();
                    if (userMap.containsKey(activity.getCreatedBy())) {
                        userDTO.setId(activity.getCreatedBy());
                        userDTO.setFullName(userMap.get(activity.getCreatedBy()).getFirstName() + " " + userMap.get(activity.getCreatedBy()).getLastName());
                    } else {
                        String createdBy = activity.getCreatedBy();
                        if (SimpleUserAspect.isTimesheetUser(createdBy)) {
                            userDTO.setId(createdBy);
                            String[] uid = createdBy.split("_");
                            if (uid.length == 3) {
                                TimeSheetUserType timeSheetUserType = TimeSheetUserType.fromDbValue(Integer.valueOf(uid[2]));
                                if (timeSheetUserType == null) {
                                    userDTO.setFullName(UNKNOWNUSER);
                                }
                                if (TimeSheetUserType.TALENT == timeSheetUserType) {
                                    userDTO.setFullName(SimpleUserAspect.CANDIDATE_MODIFIED);
                                } else if (TimeSheetUserType.CLIENT == timeSheetUserType) {
                                    userDTO.setFullName(SimpleUserAspect.CLIENT_MODIFIED);
                                }
                            } else {
                                userDTO.setFullName(UNKNOWNUSER);
                            }
                        } else {
                            //timesheet现在候选人自己修改 createdBy为system
                            if ("system".equalsIgnoreCase(createdBy)) {
                                userDTO.setFullName(SimpleUserAspect.CANDIDATE_MODIFIED);
                            } else {
                                //默认情况显示System
                                userDTO.setFullName(SYSTEM);
                            }
                        }
                    }
                    talentActivityDTO.setCreatedBy(userDTO);
                    return talentActivityDTO;
                }).collect(Collectors.toList());

        return talentActivityDTOList;
    }

    private ESResponse parseESResponse(String response) throws JsonProcessingException {
        log.info("[APN User {} get talent Activity: process parse from response", SecurityUtils.getUserId());

        if (StringUtils.isEmpty(response)) {
            return null;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ESResponse searchResponse = objectMapper.readValue(response, ESResponse.class);
        return searchResponse;
    }

    private List<ESActivitySourceDTO> getSourceChangeFromResponse(ESResponse esResponse) {
        log.info("[APN User {} get talent Activity: get changeFields Source data from response", SecurityUtils.getUserId());
        if (esResponse.getHits() == null && esResponse.getHits().getHits() == null) {
            return null;
        }

        List<ESHit> activityHits = esResponse.getHits().getHits();
        List<ESActivitySourceDTO> activityDTOList = new ArrayList<>();
        for (ESHit hit : activityHits) {
            Map<String, Object> source = hit.getSource();
            activityDTOList.add(JsonUtil.fromJson(JsonUtil.toJson(source), ESActivitySourceDTO.class));
        }

        return activityDTOList;

    }


}
