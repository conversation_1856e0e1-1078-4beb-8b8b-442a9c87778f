package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The WorkAuth enumeration.
 */
public enum WorkAuth implements ConvertedEnum<Integer>  {
    CPT(0), OPT(1), H1(2), H2(3), H4(4), J1(5), J2(6), L1(7), L2(8), O1(9), O2(10), <PERSON><PERSON><PERSON>(11), <PERSON>(12);

    private final int dbValue;

    WorkAuth(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<WorkAuth, Integer> resolver =
        new ReverseEnumResolver<>(WorkAuth.class, WorkAuth::toDbValue);

    public static WorkAuth fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
