package com.altomni.apn.talent.domain.enumeration.application;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class OfferLetterCostRateTypeConverter extends AbstractAttributeConverter<OfferLetterCostRateType, Integer> {
    public OfferLetterCostRateTypeConverter() {
        super(OfferLetterCostRateType::toDbValue, OfferLetterCostRateType::fromDbValue);
    }
}
