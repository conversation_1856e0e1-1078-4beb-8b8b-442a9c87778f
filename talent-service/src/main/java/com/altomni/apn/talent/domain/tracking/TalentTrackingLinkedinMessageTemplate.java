package com.altomni.apn.talent.domain.tracking;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.tracking.*;
import com.altomni.apn.talent.service.dto.tracking.TalentTrackingDTO;
import com.altomni.apn.talent.service.dto.tracking.TalentTrackingTemplateDTO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingTemplateVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * A TalentTrackingLinkedinMessageTemplate.
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "talent_tracking_linkedin_message_template")
public class TalentTrackingLinkedinMessageTemplate extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "title")
    private String title;

    @Column(name = "template")
    private String template;

    @Convert(converter = TrackingTemplateStatusConverter.class)
    @Column(name = "status")
    private TrackingTemplateStatus status;

    @Convert(converter = TrackingTemplateCategoryConverter.class)
    @Column(name = "category")
    private TrackingTemplateCategory category;

    @Column(name = "operatorLinkedinId")
    private String operatorLinkedinId;

    @Column(name = "tenant_id")
    private Long tenantId;

    public static TalentTrackingLinkedinMessageTemplate fromTalentTrackingTemplateDTO(TalentTrackingTemplateDTO talentTrackingTemplateDTO) {
        TalentTrackingLinkedinMessageTemplate talentTrackingLinkedinMessageTemplate = new TalentTrackingLinkedinMessageTemplate();
        ServiceUtils.myCopyProperties(talentTrackingTemplateDTO, talentTrackingLinkedinMessageTemplate);
        return talentTrackingLinkedinMessageTemplate;
    }

    public static TalentTrackingTemplateVO toTalentTrackingTemplateVO(TalentTrackingLinkedinMessageTemplate talentTrackingLinkedinPending) {
        TalentTrackingTemplateVO talentTrackingTemplateVO = new TalentTrackingTemplateVO();
        ServiceUtils.myCopyProperties(talentTrackingLinkedinPending, talentTrackingTemplateVO);
        return talentTrackingTemplateVO;
    }
}
