package com.altomni.apn.talent.service.dto.talent;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Transient;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TalentDetailRecentTenDTO implements Serializable, AttachConfidentialTalent {

    private Long id;

    private String name;

    private Boolean confidentialTalentViewAble;

    private ConfidentialInfoDto confidentialInfo;

    @Override
    public void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble) {
        this.confidentialTalentViewAble = confidentialTalentViewAble;
    }

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        this.confidentialInfo = confidentialInfo;
    }

    @Override
    public Long getTalentId() {
        return id;
    }

    @Override
    public void encrypt() {

    }
}
