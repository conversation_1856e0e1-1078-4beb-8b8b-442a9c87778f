package com.altomni.apn.talent.web.rest.talent;

import com.altomni.apn.common.config.audit.AuditUserHolder;
import com.altomni.apn.common.dto.talent.TalentAutoDeclassifyDto;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;
import com.altomni.apn.talent.service.confidential.TalentConfidentialService;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.talent.service.dto.confidential.ConfidentialRuleDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("/api/v3/talents")
@RequiredArgsConstructor
public class TalentConfidentialResource {

    private final TalentConfidentialService talentConfidentialService;

    /**
     * Create a new confidentiality rule
     * @param confidentialRuleDto the confidentiality rule to create
     * @return the created confidentiality rule
     */
    @PostMapping("/confidentiality-rule")
    public ResponseEntity<ConfidentialRuleDto> createConfidentialRule(@RequestBody ConfidentialRuleDto confidentialRuleDto) {
        log.info("[APN: Talent Confidential @{}] Creating confidentiality rule", SecurityUtils.getUserId());
        return ResponseEntity.ok(talentConfidentialService.createRule(confidentialRuleDto));
    }

    /**
     * List all confidentiality rules
     * @return the list of confidentiality rules
     */
    @GetMapping("/confidentiality-rule/list")
    public ResponseEntity<List<ConfidentialRuleDto>> listConfidentialRules() {
        log.info("[APN: Talent Confidential @{}] Listing confidentiality rules", SecurityUtils.getUserId());
        return ResponseEntity.ok(talentConfidentialService.listRules());
    }


    /**
     * Update an existing confidentiality rule
     * @param id the id of the confidentiality rule to update
     * @param confidentialRuleDto the updated confidentiality rule
     * @return the updated confidentiality rule
     */
    @PutMapping("/confidentiality-rule/{id}")
    public ResponseEntity<ConfidentialRuleDto> updateConfidentialRule(@PathVariable("id") Long id, @RequestBody ConfidentialRuleDto confidentialRuleDto) {
        log.info("[APN: Talent Confidential @{}] Updating confidentiality rule with id {}", SecurityUtils.getUserId(), id);
        ConfidentialRuleDto result;
        try {
            result = talentConfidentialService.updateRule(id, confidentialRuleDto);
        } finally {
            AuditUserHolder.clear();
        }
        return ResponseEntity.ok(result);
    }

    /**
     * Delete an existing confidentiality rule
     * @param id the id of the confidentiality rule to delete
     */
    @DeleteMapping("/confidentiality-rule/{id}")
    public ResponseEntity<Void> deleteConfidentialRule(@PathVariable("id") Long id) {
        log.info("[APN: Talent Confidential @{}] Deleting confidentiality rule with id {}", SecurityUtils.getUserId(), id);
        try {
            talentConfidentialService.deleteRule(id);
        } finally {
            AuditUserHolder.clear();
        }
        return ResponseEntity.ok().build();
    }

    /**
     * Get the effect of a confidentiality rule on update
     * @param id rule id
     * @param confidentialRuleDto the confidentiality rule to check
     * @return the set of talent ids that will be affected by the rule on update
     */
    @PostMapping("/confidentiality-rule/{id}/update/effect")
    public ResponseEntity<Set<Long>> ruleEffectOnUpdate(@PathVariable("id") Long id, @RequestBody ConfidentialRuleDto confidentialRuleDto) {
        log.info("[APN: Talent Confidential @{}] Updating confidentiality rule effect with id {}", SecurityUtils.getUserId(), id);
        confidentialRuleDto.setId(id);
        return ResponseEntity.ok(talentConfidentialService.effectOnUpdate(id, confidentialRuleDto));
    }

    /**
     * Get the effect of a confidentiality rule on delete
     * @param id rule id
     * @return the set of talent ids that will be affected by the rule on delete
     */
    @GetMapping("/confidentiality-rule/{id}/delete/effect")
    public ResponseEntity<Set<Long>> ruleEffectOnDelete(@PathVariable("id") Long id) {
        log.info("[APN: Talent Confidential @{}] Deleting confidentiality rule effect with id {}", SecurityUtils.getUserId(), id);
        return ResponseEntity.ok(talentConfidentialService.effectOnDelete(id));
    }

    /**
     * Activate all confidentiality rules
     */
    @PutMapping("/confidentiality-rule/active")
    public ResponseEntity<Void> activateConfidentialRules() {
        log.info("[APN: Talent Confidential @{}] Activating confidentiality rules", SecurityUtils.getUserId());
        talentConfidentialService.activeAllRules();
        return ResponseEntity.ok().build();
    }

    /**
     * confidential a talent
     * @param talentId the id of the talent to confidential
     * @return the confidential information of the talent
     */
    @PostMapping("/{talentId}/confidential")
    public ResponseEntity<ConfidentialInfoDto> confidentialTalent(@PathVariable("talentId") Long talentId) {
        log.info("[APN: Talent Confidential @{}] Getting confidential information for talent with id {}", SecurityUtils.getUserId(), talentId);
        return ResponseEntity.ok(talentConfidentialService.confidentialTalent(talentId));
    }

    /**
     * declassify a talent
     * @param talentId the id of the talent to declassify
     */
    @DeleteMapping("/{talentId}/confidential")
    public ResponseEntity<Void> declassifyTalent(@PathVariable("talentId") Long talentId) {
       log.info("[APN: Talent Confidential @{}] Declassifying talent with id {}", SecurityUtils.getUserId(), talentId);
       talentConfidentialService.declassifyTalent(talentId, TalentDeclassifyType.PROACTIVELY);
       return ResponseEntity.ok().build();
    }


    /**
     * check user has view able confidential talent
     * @param talentId talent id
     * @return true if user has view able confidential talent, false otherwise
     */
    @GetMapping("/{talentId}/confidential/view-able")
    public ResponseEntity<Boolean> confidentialTalentViewAble(@PathVariable("talentId") Long talentId) {
       log.info("[APN: Talent Confidential @{}] Checking if talent with id {} is view able", SecurityUtils.getUserId(), talentId);
       return ResponseEntity.ok(talentConfidentialService.confidentialTalentViewAble(talentId));
    }

    /**
     * filter view able confidential talent ids
     * @param talentIds talent ids
     * @return the set of view able confidential talent ids
     */
    @PostMapping("/confidential/view-able")
    public ResponseEntity<Set<Long>> filterConfidentialTalentViewAble(@RequestBody Set<Long> talentIds) {
        log.info("[APN: Talent Confidential @{}] Filtering confidential talent view able with ids {}", SecurityUtils.getUserId(), talentIds);
        return ResponseEntity.ok(talentConfidentialService.filterViewableTalentIds(talentIds));
    }

    /**
     * get confidential information for talents
     * @param talentIds talent ids
     * @return the map of talent id and confidential information
     */
    @PostMapping("/confidential/info")
    public ResponseEntity<Map<Long, ConfidentialInfoDto>> getTalentConfidentialInfo(@RequestBody Set<Long> talentIds) {
        log.info("[APN: Talent Confidential @{}] Getting confidential information for talents with ids {}", SecurityUtils.getUserId(), talentIds);
        return ResponseEntity.ok(talentConfidentialService.getConfidentialInfoBatch(talentIds));
    }

    /**
     * auto declassify talent by process
     * @param talentAutoDeclassifyDto the talent auto declassify dto
     */
    @PutMapping("/declassify/process")
    public ResponseEntity<Void> declassifyProcess(@RequestBody TalentAutoDeclassifyDto talentAutoDeclassifyDto) {
        log.info("[APN: Talent Confidential @{}] Declassifying talent by process with id {} ", SecurityUtils.getUserId(), talentAutoDeclassifyDto.getTalentId());
        try {
            talentConfidentialService.tryAutoDeclassifyTalentByProcess(talentAutoDeclassifyDto);
        } finally {
            AuditUserHolder.clear();
        }
        return ResponseEntity.ok().build();
    }

    /**
     * auto declassify talent by xxl job
     * @param talentAutoDeclassifyDto the talent auto declassify dto
     */
    @DeleteMapping("/confidential/auto")
    public ResponseEntity<Void> autoDeclassifyTalent(@RequestBody TalentAutoDeclassifyDto talentAutoDeclassifyDto) {
        log.info("[APN: Talent Confidential @{}] xxl job auto declassifying talent with id {}", SecurityUtils.getUserId(), talentAutoDeclassifyDto.getTalentId());
        try {
            talentConfidentialService.autoDeclassifyTalent(talentAutoDeclassifyDto);
        } finally {
            AuditUserHolder.clear();
        }
        return ResponseEntity.ok().build();
    }

    /**
     * handover confidential talent
     * @param fromUser the user who confidential the talent
     * @param toUser the user who will receive the talent
     */
    @PutMapping("/confidential/handover/{fromUser}/{toUser}")
    public ResponseEntity<Void> handoverConfidentialTalent(@PathVariable("fromUser") Long fromUser, @PathVariable("toUser") Long toUser) {
        log.info("[APN: Talent Confidential @{}] Handover confidential talent from {} to {}", SecurityUtils.getUserId(), fromUser, toUser);
        try {
            talentConfidentialService.handoverConfidentialTalent(fromUser, toUser);
        } finally {
            AuditUserHolder.clear();
        }
        return ResponseEntity.ok().build();
    }

}
