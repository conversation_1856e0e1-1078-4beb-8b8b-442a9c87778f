package com.altomni.apn.talent.repository.job;

import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Repository;
import java.util.List;


/**
 * Spring Data JPA repository for the Job entity.
 * ！！！only for talent call candidate note to get job title, otherwise using job-service to query job.
 * Any other job CRUDs in talent-service are forbidden
 */
@EnableCaching
@EnableAsync
@Repository
public interface JobBriefRepository extends JpaRepository<JobV3, Long>, QuerydslPredicateExecutor<JobV3> {

    @Query(value = "select new com.altomni.apn.common.dto.job.JobBriefDTO(j.id, j.title, j.status) from JobV3 j where j.id in :jobIds")
    List<JobBriefDTO> findBriefJobsByIdIn(@Param("jobIds") List<Long> jobIds);
}
