package com.altomni.apn.talent.service.dto.user;

public class CredentialDTO {

    public String access_token;

    public String refresh_token;

    public String scope;

    public int expires_in;

    @Override
    public String toString() {
        return "CredentialDTO{" +
            "access_token='" + access_token + '\'' +
            ", refresh_token='" + refresh_token + '\'' +
            ", scope='" + scope + '\'' +
            ", expires_in=" + expires_in +
            '}';
    }
}
