package com.altomni.apn.talent.domain.folder;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.talent.domain.enumeration.folder.TalentSearchCategory;
import com.altomni.apn.talent.domain.enumeration.folder.TalentSearchCategoryConverter;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;


@Entity
@Table(name = "talent_search_folder")
@Data
public class TalentSearchFolder extends AbstractPermissionAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "folder_note", length = 255)
    private String folderNote;

    @Column(name = "search_criteria", nullable = false, columnDefinition = "json")
    @JsonRawValue
    private String searchCriteria;

    @Column(name = "search_category", nullable = false)
    @Convert(converter = TalentSearchCategoryConverter.class)
    private TalentSearchCategory searchCategory = TalentSearchCategory.NOCATEGORY;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "talent_folder_id")
    private Long talentFolderId;

    @ApiModelProperty(value = "one or more permission")
    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "talent_search_folder_id")
    private Set<TalentSearchFolderSharingTeam> sharingTeam;

    @ApiModelProperty(value = "one or more permission")
    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "talent_search_folder_id")
    private Set<TalentSearchFolderSharingUser> sharingUser;

    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "talent_search_folder_id")
    private Set<TalentSearchFolderOwnerUser> ownerUser;

    @ApiModelProperty(value = "one or more permission")
    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "talent_search_folder_id")
    private Set<TalentSearchFolderOwnerTeam> ownerTeam;

    @Column(name = "is_active")
    private boolean isActive;

    public Long getId() {
        return id;
    }

    public String getFolderNote() {
        return folderNote;
    }

    public void setFolderNote(String folderNote) {
        this.folderNote = folderNote;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSearchCriteria() {
        return searchCriteria;
    }

    public void setSearchCriteria(String searchCriteria) {
        this.searchCriteria = searchCriteria;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getTalentFolderId() {
        return talentFolderId;
    }

    public void setTalentFolderId(Long talentFolderId) {
        this.talentFolderId = talentFolderId;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public TalentSearchCategory getSearchCategory() {
        return searchCategory;
    }

    public void setSearchCategory(TalentSearchCategory searchCategory) {
        this.searchCategory = searchCategory;
    }


}
