package com.altomni.apn.talent.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The EmailBlastStatus enumeration.
 */
public enum EmailBlastDetailStatusChanged implements ConvertedEnum<Integer> {

    UNCHANGED(0),
    CHANGED(1);

    private final Integer dbValue;

    EmailBlastDetailStatusChanged(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<EmailBlastDetailStatusChanged, Integer> resolver =
        new ReverseEnumResolver<>(EmailBlastDetailStatusChanged.class, EmailBlastDetailStatusChanged::toDbValue);

    public static EmailBlastDetailStatusChanged fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
