package com.altomni.apn.talent.repository.linkedinproject;

import com.altomni.apn.talent.domain.linkedinproject.UserFavoriteLinkedinProject;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data  repository for the UserFavoriteLinkedinProject entity.
 */
@Repository
public interface UserFavoriteLinkedinProjectRepository extends JpaRepository<UserFavoriteLinkedinProject, Long> {

    UserFavoriteLinkedinProject findByLinkedinProjectIdAndUserId(Long linkedinProjectId, Long userId);

    List<UserFavoriteLinkedinProject> findByLinkedinProjectIdInAndUserId(List<Long> linkedinProjectIds, Long userId);
}
