package com.altomni.apn.talent.domain.enumeration.contract;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractType enumeration.
 */
public enum ContractType implements ConvertedEnum<Integer> {
    GENERAL_RECRUITING(0), // EXECUTIVE_RECRUITING
    GENERAL_STAFFING(1), //STAFFING
    PAYROLL(2),
    CAMPUS_RECRUITING(3),
    RECRUITMENT_PROCESS_OUTSOURCING(4), //RPO
    INTERNSHIP(5),
    OTHERS(99);

    private final Integer dbValue;

    ContractType(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<ContractType, Integer> resolver =
        new ReverseEnumResolver<>(ContractType.class, ContractType::toDbValue);

    public static ContractType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
