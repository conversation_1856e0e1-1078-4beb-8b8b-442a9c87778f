package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.domain.talent.TalentVoiceMessageNote;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;


/**
 * Spring Data JPA repository for the TalentNote entity.
 */
@Repository
public interface TalentVoiceMessageNoteRepository extends JpaRepository<TalentVoiceMessageNote, Long> {

}
