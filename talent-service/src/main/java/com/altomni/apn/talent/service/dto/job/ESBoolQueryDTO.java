package com.altomni.apn.talent.service.dto.job;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ESBoolQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<JSONObject> should;

    private List<JSONObject> must;

    private List<JSONObject> must_not;

    private List<JSONObject> filter;

    private Integer minimum_should_match;

}
