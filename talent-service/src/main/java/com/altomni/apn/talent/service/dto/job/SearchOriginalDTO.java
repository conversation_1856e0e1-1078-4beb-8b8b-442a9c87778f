package com.altomni.apn.talent.service.dto.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.config.constants.EsFillerConstants;
import com.altomni.apn.common.utils.SecurityUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SearchOriginalDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<JSONObject> and;

    private List<JSONObject> or;

    public static SearchOriginalDTO getSearchConditionDTO(List<Long> teamIdList, Long userId) {
        SearchOriginalDTO conditionDTO = new SearchOriginalDTO();
        List<JSONObject> ORCondition = new ArrayList<>();
        JSONObject affiliations = new JSONObject();
        JSONObject userResponsibility = new JSONObject();
        List<String> pteamList = new ArrayList<>();
        pteamList.add("all");
        if (CollUtil.isNotEmpty(teamIdList)) {
            teamIdList.forEach(t -> pteamList.add(EsFillerConstants.ES_PTEAM + t));
        }
        affiliations.put("affiliations", pteamList);
        if (userId != null) {
            userResponsibility.put(EsFillerConstants.ES_CREATED_BY + ".id", Collections.singletonList(userId));
            ORCondition.add(userResponsibility);
        }
        ORCondition.add(affiliations);
        conditionDTO.setOr(ORCondition);
        return conditionDTO;
    }

}
