package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class TrackingGroupStatusConverter extends AbstractAttributeConverter<TrackingGroupStatus, Integer> {
    public TrackingGroupStatusConverter() {
        super(TrackingGroupStatus::toDbValue, TrackingGroupStatus::fromDbValue);
    }
}
