package com.altomni.apn.talent.domain.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.Optional;

public enum TalentSearchCategory implements ConvertedEnum<Integer> {

    MYCANDIDATES(1, "MyCandidates", "myTalent"),
    ALLCANDIDATES(2, "AllCandidates", "allTalent"),
    DATABASECANDIDATES(4, "DatabaseCandidates", "commonPoolTalent"),

    CUSTOMIZED(2001, "Customized", "customized"),

    SHARED(2002, "Shared", "shared"),
    NOCATEGORY(10000, "NOCATEGORY", "NOCATEGORY");


    private final int dbValue;

    private final String name;

    private final String ESKey;

    TalentSearchCategory(int dbValue, String name, String ESKey) {
        this.dbValue = dbValue;
        this.name = name;
        this.ESKey = ESKey;

    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }


    public String getESKey() {
        return ESKey;
    }

    public static final ReverseEnumResolver<TalentSearchCategory, Integer> resolver =
            new ReverseEnumResolver<>(TalentSearchCategory.class, TalentSearchCategory::toDbValue);

    public static TalentSearchCategory fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }


    public static Optional<TalentSearchCategory> fromDbValue(int dbValue) {
        for (TalentSearchCategory status : values()) {
            if (status.toDbValue() == dbValue) {
                return Optional.of(status);
            }
        }
        return Optional.empty();
    }

    public static boolean isValid(String str) {
        for (TalentSearchCategory category : TalentSearchCategory.values()) {
            //build in name() method
            if (category.name().equals(str)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Use Folder page for statistics
     * @param ESKey
     * @return
     */
    public static TalentSearchCategory fromESKey(String ESKey){
        int minValue = TalentSearchCategory.MYCANDIDATES.getDbValue();
        int maxValue = (TalentSearchCategory.DATABASECANDIDATES.getDbValue() + 1);

        for (TalentSearchCategory category : TalentSearchCategory.values()) {
            if(category.dbValue < minValue || category.dbValue >= maxValue ){
                continue;
            }

            if (category.getESKey().equalsIgnoreCase(ESKey)) {
                return category;
            }
        }
        return TalentSearchCategory.NOCATEGORY;
    }


}
