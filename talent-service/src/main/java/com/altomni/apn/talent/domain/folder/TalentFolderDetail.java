package com.altomni.apn.talent.domain.folder;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;

@Entity
@Table(name = "talent_folder")
@Data
@NamedEntityGraph(
        name = "talent_folder-graph",
        attributeNodes = {
                @NamedAttributeNode(value = "talentFolderRelations"),
                @NamedAttributeNode(value = "talentFolderSharingUsers"),
                @NamedAttributeNode(value = "talentFolderSharingTeams"),
        },
        includeAllAttributes = true
)
public class TalentFolderDetail extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "tenant_id")
    private Long tenantId;

    @OneToMany
    @JoinColumn(name = "talent_folder_id")
    private Set<TalentFolderRelation> talentFolderRelations;

    @OneToMany
    @JoinColumn(name = "talent_folder_id")
    private Set<TalentFolderSharingUser> talentFolderSharingUsers;

    @OneToMany
    @JoinColumn(name = "talent_folder_id")
    private Set<TalentFolderSharingTeam> talentFolderSharingTeams;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Set<TalentFolderRelation> getTalentFolderRelations() {
        return talentFolderRelations;
    }

    public void setTalentFolderRelations(Set<TalentFolderRelation> talentFolderRelations) {
        this.talentFolderRelations = talentFolderRelations;
    }

    public Set<TalentFolderSharingUser> getTalentFolderSharingUsers() {
        return talentFolderSharingUsers;
    }

    public void setTalentFolderSharingUsers(Set<TalentFolderSharingUser> talentFolderSharingUsers) {
        this.talentFolderSharingUsers = talentFolderSharingUsers;
    }

    public Set<TalentFolderSharingTeam> getTalentFolderSharingTeams() {
        return talentFolderSharingTeams;
    }

    public void setTalentFolderSharingTeams(Set<TalentFolderSharingTeam> talentFolderSharingTeams) {
        this.talentFolderSharingTeams = talentFolderSharingTeams;
    }
}
