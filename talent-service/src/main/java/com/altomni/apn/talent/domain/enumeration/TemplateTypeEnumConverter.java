package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class TemplateTypeEnumConverter extends AbstractAttributeConverter<TemplateTypeEnum, Integer> {
    public TemplateTypeEnumConverter() {
        super(TemplateTypeEnum::toDbValue, TemplateTypeEnum::fromDbValue);
    }
}
