package com.altomni.apn.talent.web.rest.talent;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.aop.user.AttachSimpleUser;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.domain.talent.TalentVoiceMessageNote;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.dto.talent.CreateTalentNoteDTO;
import com.altomni.apn.common.dto.talent.CreateTalentVoiceMessageNoteDTO;
import com.altomni.apn.common.dto.talent.TalentNoteDTO;
import com.altomni.apn.common.dto.talent.TalentReviewNoteDTO;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.constants.EnrichStatusEnum;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.constants.EnrichStatusEnum;
import com.altomni.apn.talent.repository.talent.TalentNoteRepository;
import com.altomni.apn.talent.service.talent.TalentNoteService;
import com.altomni.apn.talent.web.rest.talent.dto.TopTalentNoteDTO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.BufferedReader;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

/**
 * REST controller for managing TalentNote.
 */
@Api(tags = {"Talent", "ATS-Candidates"})
@RestController
@RequestMapping("/api/v3")
public class TalentNoteResource {

    private final Logger log = LoggerFactory.getLogger(TalentNoteResource.class);

    private static final String ENTITY_NAME = "talentNote";

    @Resource
    private TalentNoteRepository talentNoteRepository;

    @Resource
    private TalentNoteService talentNoteService;


    @ApiOperation(value = "Get all notes for a talent by type")
    @PostMapping("/talent/note/top")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> topTalentNote(@RequestBody TopTalentNoteDTO dto) {
        log.info("[APN: TalentNote @{}] REST request to top note for talent {}", SecurityUtils.getUserId(), dto);
        talentNoteService.topTalentNote(dto);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "Get all notes for a talent by type")
    @GetMapping("/talent/{id}/note")
    @Timed
    public ResponseEntity<List<Map<String, Object>>> getTalentNoteByType(@PathVariable("id") Long talentId, @RequestParam List<String> type, @RequestParam(required = false) String noteType) {
        log.info("[APN: TalentNote @{}] REST request to get note by type for talent {}, search type : {}", SecurityUtils.getUserId(), talentId, JSONUtil.toJsonStr(type));
        List<Map<String, Object>> list = talentNoteService.getTalentNoteByType(talentId, type, noteType);
        return ResponseEntity.ok(list);
    }

    @GetMapping("/talent/{id}/note/enrich")
    public ResponseEntity<String> getTalentNoteEnrich(@PathVariable("id") Long talentId) {
        log.info("[APN: TalentNote @{}] REST request to get note by type for talent {}", SecurityUtils.getUserId(), talentId);
        return ResponseEntity.ok(talentNoteService.getTalentNoteEnrich(talentId));
    }


    /**
     * GET  /talent-notes/talent/:talentId : get all the talentNotes on a talent.
     *
     * @param talentId the id of the talent to get all notes
     * @return the ResponseEntity with status 200 (OK) and the list of talentNotes in body
     */
    @ApiOperation(value = "Get all notes for a talent")
    @GetMapping("/talent-notes/talent/{talentId}")
    @Timed
    public List<TalentNote> getAllTalentNotesForTalent(@PathVariable("talentId") Long talentId) {
        log.info("[APN: TalentNote @{}] REST request to get all TalentNotes for talent {}", SecurityUtils.getUserId(), talentId);
        return talentNoteRepository.findAllByTalentId(talentId);
    }

    @ApiOperation(value = "Get all notes for a talent")
    @GetMapping("/talent-notes/talent/{talentId}/notes")
    @Timed
    public ResponseEntity<List<TalentNoteDTO>> getTalentNotesForTalent(@PathVariable("talentId") Long talentId) {
        log.info("[APN: TalentNote @{}] REST request to get all TalentNotes for talent {}", SecurityUtils.getUserId(), talentId);
        return ResponseEntity.ok(talentNoteService.findAllByTalentId(talentId));
    }

    @ApiOperation(value = "Get all notes for a talent")
    @GetMapping("/talent-notes/talent/{talentId}/notes/agency/{agencyId}")
    @Timed
    public ResponseEntity<List<TalentNoteDTO>> getTalentNotesForTalent(@PathVariable("talentId") Long talentId, @PathVariable("agencyId") Long agencyId) {
        log.info("[APN: TalentNote @{}] REST request to get all TalentNotes for talent {} within agency: {}", SecurityUtils.getUserId(), talentId, agencyId);
        return ResponseEntity.ok(talentNoteService.findAllByTalentIdAndAgencyId(talentId, agencyId));
    }


    /**
     * POST  /talent-notes : Create a new talentNote.
     *
     * @param talentNote the talentNote to create
     * @return the ResponseEntity with status 201 (Created) and with body the new talentNote, or with status 400 (Bad Request) if the talentNote has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "user create note on talent", notes = "note owner will be current user.")
    @PostMapping("/talent-notes")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentNote> createTalentNote(@Valid @RequestBody CreateTalentNoteDTO talentNote) throws URISyntaxException, IOException {
        //if the talentNote is created by system, then system will assign the user
        if(!talentNote.getIsSystem()) talentNote.setUserId(SecurityUtils.getUserId());
        log.info("[APN: TalentNote @{}] REST request to save TalentNote : {}", SecurityUtils.getUserId(), JSON.toJSONString(talentNote));
        TalentNote result = talentNoteService.createTalentNote(talentNote);
        return ResponseEntity.created(new URI("/api/v3/talent-notes/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * POST  /talent-voice-message-notes : Create a new talentVoiceMessageNote.
     *
     * @param talentNote the talentNote to create
     * @return the ResponseEntity with status 201 (Created) and with body the new talentVoiceMessageNote, or with status 400 (Bad Request) if the talentNote has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "user create voice message note on talent", notes = "note owner will be current user.")
    @PostMapping("/talent-voice-message-notes")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentVoiceMessageNote> createTalentVoiceMessageNote(@Valid @RequestBody CreateTalentVoiceMessageNoteDTO talentNote) throws URISyntaxException, IOException {
        //if the talentNote is created by system, then system will assign the user
        if(!talentNote.getIsSystem()) talentNote.setUserId(SecurityUtils.getUserId());
        log.info("[APN: TalentNote @{}] REST request to save TalentNote : {}", SecurityUtils.getUserId(), JSON.toJSONString(talentNote));
        TalentVoiceMessageNote result = talentNoteService.createTalentVoiceMessageNote(talentNote);
        return ResponseEntity.created(new URI("/api/v3/talent-voice-message-notes/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    /**
     * PUT  /talent-notes/:id : Updates an existing talentNote.
     *
     * @param id the id of the talentNote to update
     * @param talentNote the talentNote to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated talentNote,
     * or with status 400 (Bad Request) if the talentNote is not valid,
     * or with status 500 (Internal Server Error) if the talentNote couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "User update note on talent", notes = "Only owner can update")
    @PutMapping("/talent-notes/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentNote> updateTalentNote(@PathVariable Long id, @Valid @RequestBody CreateTalentNoteDTO talentNote) throws URISyntaxException, IOException {
        talentNote.setId(id);
        log.info("[APN: TalentNote @{}] REST request to update TalentNote : {}", SecurityUtils.getUserId(), JSON.toJSONString(talentNote));
        TalentNote result = talentNoteService.updateTalentNote(talentNote);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, talentNote.getId().toString()))
            .body(result);
    }

    /**
     * GET  /talent-notes : get all the talentNotes.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talentNotes in body
     */
    @ApiOperation(value = "[Not Needed] Get all notes on all talents")
    @GetMapping("/talent-notes")
    @Timed
    public List<TalentNote> getAllTalentNotes() {
        log.info("[APN: TalentNote @{}] REST request to get all TalentNotes", SecurityUtils.getUserId());
        return talentNoteRepository.findAll();
    }

    /**
     * GET  /talent-notes/:id : get the "id" talentNote.
     *
     * @param id the id of the talentNote to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the talentNote, or with status 404 (Not Found)
     */
    @ApiOperation(value = "Get a note")
    @GetMapping("/talent-notes/{id}")
    @Timed
    @AttachSimpleUser
    public ResponseEntity<TalentNoteDTO> getTalentNoteById(@PathVariable Long id) {
        log.info("[APN: TalentNote @{}] REST request to get TalentNote : {}", SecurityUtils.getUserId(), id);
        TalentNote talentNote = talentNoteRepository.findById(id).orElseThrow(() -> new NotFoundException("Note is not exist!"));
        TalentNoteDTO talentNoteDTO = new TalentNoteDTO();
        BeanUtil.copyProperties(talentNote, talentNoteDTO);
        String enrichResult = talentNoteDTO.getEnrichResult();
        String parsedResult = talentNoteDTO.getParsedResult();
        if(parsedResult != null) {
            if(enrichResult == null) {
                talentNoteDTO.setEnrichStatus(EnrichStatusEnum.WAITING_ENRICH.name());
            } else {
                JSONObject enrichResultJson = JSONUtil.parseObj(enrichResult);
                talentNoteDTO.setEnrichStatus(enrichResultJson.isEmpty() ? EnrichStatusEnum.DO_NOT_ENRICH.name() : EnrichStatusEnum.ENRICHED.name());
            }
        }
        return ResponseEntity.ok(talentNoteDTO);
    }

    /**
     * DELETE  /talent-notes/:id : delete the "id" talentNote.
     *
     * @param id the id of the talentNote to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "Delete a note", notes = "Only owner can delete")
    @DeleteMapping("/talent-notes/{id}")
    @Timed
    @NoRepeatSubmit
    @Transactional
    public ResponseEntity<Void> deleteTalentNoteById(@PathVariable Long id) throws IOException {
        log.info("[APN: TalentNote @{}] REST request to delete TalentNote : {}", SecurityUtils.getUserId(), id);
        talentNoteService.deleteTalentNote(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }


    @ApiOperation(value = "user create reivew note on talent", notes = "note owner will be current user.")
    @PostMapping("/talent-review-notes")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentReviewNoteDTO> createTalentReviewNote(@Valid @RequestBody TalentReviewNoteDTO talentReviewNoteDTO)  {
        log.info("[APN: TalentNote @{}] REST request to create TalentReviewNote", SecurityUtils.getUserId());
        TalentReviewNoteDTO talentReviewNote = talentNoteService.createTalentReviewNote(talentReviewNoteDTO);
        return ResponseEntity.ok(talentReviewNote);
    }

    @ApiOperation(value = "user update review note on talent", notes = "note owner will be current user.")
    @PostMapping("/talent-review-notes/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentReviewNoteDTO> updateTalentReviewNote(@PathVariable Long id, @Valid @RequestBody TalentReviewNoteDTO talentReviewNoteDTO)  {
        log.info("[APN: TalentNote @{}] REST request to update TalentReviewNote", SecurityUtils.getUserId());
        TalentReviewNoteDTO body = talentNoteService.updateTalentReviewNote(id, talentReviewNoteDTO);
        return ResponseEntity.ok(body);
    }

    @ApiOperation(value = "get all talent note by voip phone call id", notes = "if voip phone call id exist")
    @PostMapping("/talent-call-candidate-notes/voip-phone-call-ids")
    @Timed
    public ResponseEntity<List<TalentNoteDTO>> getAllCallCandidateNotesByVoipPhoneCallIdsIds(@RequestBody List<String> phoneCallIds)  {
        log.info("[APN: TalentNote @{}] REST request to get all call candidate notes by voip phone call id: {}", SecurityUtils.getUserId(), phoneCallIds);
        List<TalentNoteDTO> notes = talentNoteService.findAllByVoipPhoneCallIds(phoneCallIds);
        return ResponseEntity.ok(notes);
    }

    /**
     * Get talent note draft
     * @param talentId
     * @return
     */
    @ApiOperation(value = "Get talent note draft")
    @GetMapping("/talent-notes/{talentId}/draft")
    @Timed
    public ResponseEntity<TalentNoteDraftDTO> getTalentNoteDraft(@PathVariable("talentId") Long talentId) {
        log.info("[APN: TalentNote @{}] REST request to get TalentNoteDraft for talent {}", SecurityUtils.getUserId(), talentId);
        return new ResponseEntity<>(talentNoteService.getTalentNoteDraft(talentId), HttpStatus.OK);
    }

    /**
     * Update talent note draft
     * @param draft
     * @return
     */
    @ApiOperation(value = "Update talent note draft")
    @PutMapping("/talent-notes/draft")
    @Timed
    public ResponseEntity<Void> updateTalentNoteDraft(@RequestBody TalentNoteDraftDTO draft) {
        log.info("[APN: TalentNote @{}] REST request to get TalentNoteDraft for talent", SecurityUtils.getUserId());
        talentNoteService.updateTalentNoteDraft(draft);
        return ResponseEntity.ok().build();
    }



}
