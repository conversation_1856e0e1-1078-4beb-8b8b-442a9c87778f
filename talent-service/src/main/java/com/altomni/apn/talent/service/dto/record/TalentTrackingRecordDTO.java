package com.altomni.apn.talent.service.dto.record;

import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.record.TrackingType;
import com.altomni.apn.talent.domain.record.TalentTrackingRecord;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

@Data
public class TalentTrackingRecordDTO implements Serializable {

    private static final long serialVersionUID = -956520387234760691L;

    private TrackingType trackingType;

    @NotNull
    private String contact;

    private Instant touchTime;

    public TrackingType getTrackingType() {
        return trackingType;
    }

    public void setTrackingType(TrackingType trackingType) {
        this.trackingType = trackingType;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public Instant getTouchTime() {
        return touchTime;
    }

    public void setTouchTime(Instant touchTime) {
        this.touchTime = touchTime;
    }

    public static TalentTrackingRecordDTO fromTalentTrackingRecord(TalentTrackingRecord talentTrackingRecord) {
        TalentTrackingRecordDTO talentTrackingRecordDTO = new TalentTrackingRecordDTO();
        ServiceUtils.myCopyProperties(talentTrackingRecord, talentTrackingRecordDTO);
        return talentTrackingRecordDTO;
    }
}
