package com.altomni.apn.talent.service.dto.linkedinproject;

import com.altomni.apn.talent.domain.linkedinproject.LinkedinTalentContact;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
public class LinkedinTalentDTO implements Serializable {

    private static final long serialVersionUID = -3045905677401096041L;

    private String id;

    private String trackingNotePlatformId;

    private Long apnTalentId;

    private String additionalContent;

    @Transient
    @JsonProperty
    public List<LinkedinTalentContact> contacts;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTrackingNotePlatformId() {
        return trackingNotePlatformId;
    }

    public void setTrackingNotePlatformId(String trackingNotePlatformId) {
        this.trackingNotePlatformId = trackingNotePlatformId;
    }

    public Long getApnTalentId() {
        return apnTalentId;
    }

    public void setApnTalentId(Long apnTalentId) {
        this.apnTalentId = apnTalentId;
    }

    public String getAdditionalContent() {
        return additionalContent;
    }

    public void setAdditionalContent(String additionalContent) {
        this.additionalContent = additionalContent;
    }

    public List<LinkedinTalentContact> getContacts() {
        return contacts;
    }

    public void setContacts(List<LinkedinTalentContact> contacts) {
        this.contacts = contacts;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinTalentDTO linkedinTalent = (LinkedinTalentDTO) o;
        if (linkedinTalent.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinTalent.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinTalent{" +
            "id='" + id + '\'' +
            ", trackingNotePlatformId='" + trackingNotePlatformId + '\'' +
            ", apnTalentId=" + apnTalentId +
            ", additionalContent='" + additionalContent + '\'' +
            ", contacts=" + contacts +
            '}';
    }
}
