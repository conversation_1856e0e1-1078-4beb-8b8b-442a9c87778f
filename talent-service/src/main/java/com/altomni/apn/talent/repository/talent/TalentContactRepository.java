package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus;
import com.altomni.apn.common.domain.talent.QTalentContact;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;


/**
 * Spring Data JPA repository for the TalentContact entity.
 */
@Repository
public interface TalentContactRepository extends JpaRepository<TalentContact, Long>, QuerydslPredicateExecutor<TalentContact>, QuerydslBinderCustomizer<QTalentContact>, JpaSpecificationExecutor<TalentContact> {

    @Override
    default public void customize(QuerydslBindings bindings, QTalentContact root) {
        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    }

    List<TalentContact> findAllByTalentIdInAndTypeAndStatus(List<Long> talentIds, ContactType type, TalentContactStatus status);

    List<TalentContact> findAllByTalentIdInAndTypeAndStatusAndSort(List<Long> talentIds, ContactType type, TalentContactStatus status, Integer sort);

    List<TalentContact> findAllByTalentIdAndStatus(Long talentId, TalentContactStatus status);

    @Query(value = "SELECT tc FROM TalentContact tc WHERE tc.talentId = ?1 AND tc.status = ?2 AND (tc.verificationStatus IS NULL OR tc.verificationStatus != com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus.WRONG_CONTACT)")
    List<TalentContact> findAllByTalentIdAndStatusAndIsNotWrongContact(Long talentId, TalentContactStatus status);

    List<TalentContact> findAllByTalentId(Long talentId);

    List<TalentContact> findAllByTalentIdAndStatusAndTypeIn(Long talentId, TalentContactStatus status, List<ContactType> type);

    @Query(value = "SELECT tc FROM TalentContact tc WHERE tc.talentId = ?1 AND tc.status = ?2 AND (tc.verificationStatus IS NULL OR tc.verificationStatus != ?3) AND tc.type IN (?4)")
    List<TalentContact> findAllByTalentIdAndStatusAndVerificationStatusIsNotAndTypeIn(Long talentId, TalentContactStatus status, TalentContactVerificationStatus talentContactVerificationStatus, List<ContactType> type);

    List<TalentContact> findAllByTalentIdAndTenantIdAndStatusOrderBySort(Long talentId, Long tenantId, TalentContactStatus status);

    TalentContact findByIdAndStatus(Long Id, TalentContactStatus status);

    @Query(value = "SELECT tc FROM TalentContact tc WHERE tc.type = ?1 AND tc.contact = ?2 AND tc.tenantId = ?3 AND tc.status = ?4 AND (tc.verificationStatus IS NULL OR tc.verificationStatus != com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus.WRONG_CONTACT)")
    List<TalentContact> findAllByTypeAndContactAndTenantIdAndStatus(ContactType type, String contact, Long tenantId, TalentContactStatus status);

    List<TalentContact> findAllByTalentIdInAndStatusAndTypeInOrderByLastModifiedDateDesc(List<Long> talentId, TalentContactStatus status, List<ContactType> type);

    List<TalentContact> findAllByTalentIdInAndStatus(List<Long> talentId, TalentContactStatus status);

    @Modifying
    @Transactional
    @Query(value = " update talent_contact tc set tc.info =:talentContactInfo where tc.id =:talentContactId ",nativeQuery = true)
    void updateLinkedinIdentifier(@Param("talentContactId") Long talentContactId, @Param("talentContactInfo") String talentContactInfo);

    List<TalentContact> findAllByContactAndTypeIn(String contact, List<ContactType> types);

    Optional<TalentContact> findByTenantIdAndTalentIdAndTypeAndContactAndStatus(Long tenantId, Long talentId, ContactType contactType, String contactInfo, TalentContactStatus status);
}


