package com.altomni.apn.talent.service.record.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatform;
import com.altomni.apn.talent.domain.record.TalentTrackingNote;
import com.altomni.apn.talent.domain.vm.record.UserNameVM;
import com.altomni.apn.talent.repository.record.TalentTrackingNoteRepository;
import com.altomni.apn.talent.repository.talent.TalentNoteRepository;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.dto.record.TalentTrackingNoteDTO;
import com.altomni.apn.talent.service.record.TalentTrackingNoteService;
import com.altomni.apn.talent.service.talent.TalentNoteService;
import com.altomni.apn.talent.service.vo.record.TalentNoteVO;
import com.altomni.apn.talent.service.vo.record.TalentTrackingNoteVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class TalentTrackingNoteServiceImpl extends TalentTrackingBaseServiceImpl implements TalentTrackingNoteService {

    private final Logger log = LoggerFactory.getLogger(TalentTrackingNoteServiceImpl.class);

    private final TalentTrackingNoteRepository talentTrackingNoteRepository;

    private final TalentRepository talentRepository;

    private final TalentNoteRepository talentNoteRepository;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final TalentApiPromptProperties talentApiPromptProperties;

    private final TalentNoteService talentNoteService;

    public TalentTrackingNoteServiceImpl(TalentTrackingNoteRepository talentTrackingNoteRepository, TalentRepository talentRepository, TalentNoteRepository talentNoteRepository,CommonApiMultilingualConfig commonApiMultilingualConfig,TalentApiPromptProperties talentApiPromptProperties, TalentNoteService talentNoteService) {
        this.talentTrackingNoteRepository = talentTrackingNoteRepository;
        this.talentRepository = talentRepository;
        this.talentNoteRepository = talentNoteRepository;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.talentApiPromptProperties = talentApiPromptProperties;
        this.talentNoteService = talentNoteService;
    }

    @Override
    public TalentTrackingNoteVO save(TalentTrackingNoteDTO talentTrackingNoteDTO) {
        TalentTrackingNote talentTrackingNote = TalentTrackingNote.fromTalentTrackingNoteDTO(talentTrackingNoteDTO);
        talentTrackingNote.setTenantId(SecurityUtils.getTenantId());
        talentTrackingNote.setUserId(SecurityUtils.getUserId());
        return TalentTrackingNoteVO.fromTalentTrackingNote(talentTrackingNoteRepository.save(talentTrackingNote));
    }

    @Override
    public List<TalentTrackingNoteVO> findAllTalentTrackingNotes(String platformId, TrackingPlatform trackingPlatform) {
        List<TalentTrackingNote> talentTrackingNotes = talentTrackingNoteRepository.findAllByTenantIdAndPlatformIdAndTrackingPlatform(SecurityUtils.getTenantId(), platformId, trackingPlatform);
        List<Long> userIds = talentTrackingNotes.stream().map(TalentTrackingNote::getUserId).distinct().collect(Collectors.toList());
        List<UserNameVM> userNameList = searchUserNameByIds(userIds);
        Map<Long, UserNameVM> userNameMap = userNameList.stream().collect(Collectors.toMap(UserNameVM::getId, UserNameVM -> UserNameVM));

        List<TalentTrackingNoteVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(talentTrackingNotes)) {
            for (TalentTrackingNote talentTrackingNote : talentTrackingNotes) {
                TalentTrackingNoteVO talentTrackingNoteVO = TalentTrackingNoteVO.fromTalentTrackingNote(talentTrackingNote);
                if (userNameMap.containsKey(talentTrackingNote.getUserId())) {
                    talentTrackingNoteVO.setUserFullName(CommonUtils.formatFullName(userNameMap.get(talentTrackingNote.getUserId()).getFirstName(), userNameMap.get(talentTrackingNote.getUserId()).getLastName()));
                }
                result.add(talentTrackingNoteVO);
            }
        }
        return result;
    }

    @Override
    public List<TalentNoteVO> syncProTalentTrackingNoteToApnTalentNote(Long talentId, String platformId) {
        TalentV3 talent = talentRepository.findById(talentId).orElse(null);
        if (talent == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.RECORD_SYNCPROTALENTTRACKINGNOTETOAPNTALENTNOTE_TALENTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(talentId),talentApiPromptProperties.getTalentService()));
        }
        List<TalentTrackingNote> talentTrackingNotes = talentTrackingNoteRepository.findAllByTenantIdAndPlatformId(talent.getTenantId(), platformId);
        if (CollectionUtils.isNotEmpty(talentTrackingNotes)) {
            for (TalentTrackingNote talentTrackingNote : talentTrackingNotes) {
                if (talentTrackingNote.getSyncedTalentId() == null) {
                    TalentNote talentNote = new TalentNote();
                    talentNote.setTalentId(talentId);
                    talentNote.setUserId(talentTrackingNote.getUserId());
                    talentNote.setNoteType(TalentNoteType.OTHERS);
                    talentNote.setNote(talentTrackingNote.getNote());
                    talentNote.setTitle("Synced From APN-Pro");
                    TalentNote saved = talentNoteRepository.save(talentNote);
                    talentNoteRepository.updateCreatedBy(talentTrackingNote.getCreatedBy(), talentTrackingNote.getCreatedDate(), talentTrackingNote.getLastModifiedBy(), talentTrackingNote.getLastModifiedDate(), saved.getId());
                    talentTrackingNoteRepository.updateSyncedTalentId(talentId, talentTrackingNote.getId());
                    talentNoteService.notifyNoteEnrich(saved);
                }
            }
            return talentNoteRepository.findAllByTalentId(talentId).stream().map(TalentNoteVO::fromTalentNote).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

    private List<UserNameVM> searchUserNameByIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        String selectSql ="SELECT id, first_name, last_name FROM `user` WHERE id IN (?1)";
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, userIdList);
        List<UserNameVM> userNameList =  searchData(selectSql, UserNameVM.class, paramMap);
        if (CollectionUtils.isEmpty(userNameList)) {
            return new ArrayList<>();
        }
        return userNameList;
    }

}
