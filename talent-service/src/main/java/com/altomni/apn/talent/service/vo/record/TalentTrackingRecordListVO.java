package com.altomni.apn.talent.service.vo.record;


import com.altomni.apn.talent.service.dto.record.TalentTrackingRecordDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentTrackingRecordListVO implements Serializable {

    private static final long serialVersionUID = 5514287855806498133L;

    private Long userId;

    private String fullName;

    private List<TalentTrackingRecordDTO> records;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public List<TalentTrackingRecordDTO> getRecords() {
        return records;
    }

    public void setRecords(List<TalentTrackingRecordDTO> records) {
        this.records = records;
    }

    @Override
    public String toString() {
        return "TalentTrackingRecordListDTO{" +
                "userId=" + userId +
                ", fullName='" + fullName + '\'' +
                ", records=" + records +
                '}';
    }
}
