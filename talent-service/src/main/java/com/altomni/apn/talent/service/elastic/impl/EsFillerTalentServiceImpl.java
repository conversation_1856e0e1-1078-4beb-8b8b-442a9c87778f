package com.altomni.apn.talent.service.elastic.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.ElasticSearchConstants;
import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import com.altomni.apn.common.constants.ResponsibilityConstants;
import com.altomni.apn.common.domain.dict.*;
import com.altomni.apn.common.domain.enumeration.CommonDataStatus;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.talent.*;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessBriefDTO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.repository.talent.ResumeRepository;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderRepository;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderTalentRepository;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.service.enums.*;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.talent.TalentToHrVO;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.job.service.dto.address.LocationESDTO;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.EsfillerMQProperties;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.constants.Constants;
import com.altomni.apn.talent.domain.agency.AgencyTalentRelation;
import com.altomni.apn.talent.domain.enumeration.start.StartStatus;
import com.altomni.apn.talent.domain.talent.TalentCurrentLocation;
import com.altomni.apn.talent.domain.talent.TalentReviewNote;
import com.altomni.apn.talent.repository.agency.AgencyTalentRelationRepository;
import com.altomni.apn.talent.repository.folder.TalentFolderRelationRepository;
import com.altomni.apn.talent.repository.talent.*;
import com.altomni.apn.talent.service.CompanyService;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.application.ApplicationService;
import com.altomni.apn.talent.service.common.HttpService;
import com.altomni.apn.talent.service.confidential.TalentConfidentialService;
import com.altomni.apn.talent.service.dto.esfiller.EsFillerFolderUpdateDTO;
import com.altomni.apn.talent.service.dto.start.StartDTO;
import com.altomni.apn.talent.service.dto.talent.DuplicationCheckResponseSuspectedDuplicationsDto;
import com.altomni.apn.talent.service.dto.talent.SimilarityResultDto;
import com.altomni.apn.talent.service.dto.talent.TalentESResponseDTO;
import com.altomni.apn.talent.service.dto.talent.TalentSimilarityDto;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.finance.FinanceService;
import com.altomni.apn.talent.service.job.JobService;
import com.altomni.apn.talent.service.rabbitmq.RabbitMqService;
import com.altomni.apn.talent.service.rater.RaterService;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import com.google.common.collect.Lists;
import okhttp3.Headers;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.cache.CachesEndpoint;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.altomni.apn.common.config.constants.ElasticSearchConstants.ESFILLER_KEY_NICK_NAME;
import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;

/**
 * Only use this class to sync Mysql data(talent) to elastic search
 *
 * <AUTHOR>
 */
@Service
@RefreshScope
public class EsFillerTalentServiceImpl implements EsFillerTalentService {

    private Logger log = LoggerFactory.getLogger(EsFillerTalentServiceImpl.class);

    @Resource
    private TalentNoteRepository talentNoteRepository;

    @Resource
    private TalentAdditionalInfoRepository talentAdditionalInfoRepository;

    @Resource
    private TalentFolderRelationRepository talentFolderRelationRepository;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource
    private HttpService httpService;

    @Resource
    private TalentContactRepository talentContactRepository;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private TalentOwnershipRepository talentOwnershipRepository;

    @Resource
    private TalentLocationRepository talentLocationRepository;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private CompanyService companyService;

    @Resource
    private JobService jobService;

    @Resource
    private CanalService canalService;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private UserService userService;

    @Resource
    private ResumeRepository resumeRepository;

    @Resource
    private FinanceService financeService;

    @Resource
    private EnumLanguageService enumLanguageService;

    @Resource
    private EnumIndustryService enumIndustryService;

    @Resource
    private EnumJobFunctionService enumJobFunctionService;

    @Resource
    private EnumWorkAuthorizationService enumWorkAuthorizationService;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Resource
    private EnumMotivationService enumMotivationService;

    @Resource(name = "jobdivaRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource
    private JobdivaRabbitProperties jobdivaRabbitProperties;

    @Resource
    private AgencyTalentRelationRepository agencyTalentRelationRepository;

    @Resource
    private TalentConfidentialService confidentialService;

    @Resource
    private RaterService raterService;

    private final int SUCCESS_CODE = 200;

    private final int FAILED_CODE = 500;

    private final int ERROR_CODE = 504;

    public static final Integer TALENT_FILL_FLAG_EXPIRATION_TIME_SECONDS = 60 * 60 * 24 * 3;
    @Autowired
    private CachesEndpoint cachesEndpoint;

    private String syncUrl(Long tenantId, Long dataId) {
        return applicationProperties.getEsFillerSyncUrl() + tenantId + "/talent/" + dataId + "/fill_es/";
    }

//    private String updateUserNameUrl(Long tenantId, Long userId) {
//        return applicationProperties.getEsFillerSyncUrl() + tenantId + "/user/" + userId + "/update_es";
//    }

    private String getUnlockUrl() {
        return applicationProperties.getEsFillerBaseUrl() + "/v3/common/unlock";
    }

    private String updateClientContactNameUrl(Long tenantId, Long clientId) {
        return applicationProperties.getEsFillerSyncUrl() + tenantId + "/client_contact/" + clientId + "/update_es";
    }

    private String checkTalentDuplicationUrl(Long tenantId) {
        return applicationProperties.getEsFillerSyncUrl() + tenantId + "/check_talent_duplication";
    }

    private String getContactsUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v2/es-talents/get-contacts-by-id";
    }

    private String commonDbUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/get-talent-by-id";
    }

    private String commonServiceUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/hitalent";
    }

    private String updateTalentFolderUrl(Long tenantId) {
        return applicationProperties.getEsFillerSyncUrl() + tenantId + "/talents_folder_update";
    }

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * map =》 {
     * "_id1": [
     * "tenant_id1",
     * "tenant_id2"
     * ],
     * "_id2": [
     * "tenant_id1",
     * "tenant_id2"
     * ]
     * }
     *
     * @param map
     * @return
     */
    @Override
    public void unlockCommonTalent(Map<String, List<String>> map, Boolean isSyncHistoryData) {
        log.info("unlock common talent start ..., isSyncHistoryData = {}", isSyncHistoryData);
//        HttpResponse httpResponse;
//        try {
//            String url = getUnlockUrl();
//            if (isSyncHistoryData) {
//                httpResponse = httpService.post(url, JSONUtil.toJsonStr(map));
//            } else {
//                httpResponse = httpService.put(url, JSONUtil.toJsonStr(map));
//            }
//            log.info("unlock common talent request url = {}, result status = {}", url, httpResponse.getCode());
//            if (!Objects.equals(httpResponse.getCode(), HttpStatus.OK.value())) {
//                return httpResponse;
//            }
//        } catch (Exception e) {
//            log.error("unlock common talent is error , msg = {}", ExceptionUtils.getStackTrace(e));
//            throw new CustomParameterizedException("Internal Server Error");
//        }
//        return httpResponse;
        JSONObject request = new JSONObject();
        request.put("_type", isSyncHistoryData ? "COMMONDB_UNLOCK_BATCH_UPDATE" : "COMMONDB_UNLOCK_SINGLE_APPEND");
        request.put("_params", new HashMap<>(){{put("commondb_unlock_id_map", map);}});
        rabbitMqService.sendTalentUnlock(JSONUtil.toJsonStr(request));
    }

    @Async
    @Override
    public HttpResponse syncTalentToEs(Long talentId) throws IOException {
        /*entityManager.clear();
        TalentV3 talent = talentRepository.findById(talentId).orElse(null);
        if (talent == null) {
            return null;
        }
        JSONObject es = translateEs(talent);
        HttpResponse response = null;
        try {
            response = httpService.post(syncUrl(talent.getTenantId(), talentId), JSON.toJSONString(es));
            if (!Objects.isNull(response) && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode()) && org.apache.commons.lang3.StringUtils.isNotBlank(response.getBody())) {
//                log.info("[APN: TalentService] create talent to EsFiller success, id: {}, response code: {}, response message: {}", talentId, response.getCode(), response.getBody());
                if (JSONUtil.isJsonObj(response.getBody())) {
                    saveTalentInfos(response.getBody(), talent);
                }
                talentRepository.updateTalentLastSyncTime(talentId);
                jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, talentId, AsyncEnum.DATA_TYPE_TALENT, Status.Available, response.getCode(), response.getBody()));
            } else {
                log.error("[APN: TalentService] create talent to EsFiller error, id: {}, response code: {}, response message: {}", talentId, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, talentId, AsyncEnum.DATA_TYPE_TALENT, Status.Failed, response != null ? response.getCode() : null, response != null ? response.getBody() : null));
            }
        } catch (IOException e) {
            log.error("[APN: TalentService] create talent to EsFiller IOException, id: {}", talentId);
            jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, talentId, AsyncEnum.DATA_TYPE_TALENT, Status.Failed, ERROR_CODE, e.getMessage()));
        }
        return response;*/
        return null;
    }

    @Override
    public boolean syncTalentToEs2(Long talentId) {
        /*log.error("[APN: EsFillerTalentService] sync talent to esFillerTalentId [{}]", talentId);
        TalentV3 talent = talentRepository.findById(talentId).orElse(null);
        if (talent == null) {
            return true;
        }
        JSONObject es = null;
        int code = SUCCESS_CODE;
        String responseBody = "";
        String requestBody = "";
        try {
            es = translateEs(talent);
            requestBody = JSON.toJSONString(es);
            HttpResponse response = httpService.post(syncUrl(talent.getTenantId(), talentId), requestBody);
            if (!Objects.isNull(response) && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode()) && org.apache.commons.lang3.StringUtils.isNotBlank(response.getBody())) {
//                log.info("[EsFillerTalentService: syncTalentToES2 @-1] sync talent to EsFiller success, id: {}, response: {}, response code: {}, response message: {}", talentId, response, response.getCode(), response.getBody());
                if (JSONUtil.isJsonObj(response.getBody())) {
//                raterService.refreshTalentRater(talentId);
//                asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, talentId, AsyncEnum.DATA_TYPE_TALENT, Status.Available, response.getCode(), response.getBody()));
                    saveTalentInfos(response.getBody(), talent);
                }
                talentRepository.updateTalentLastSyncTime(talentId);
                return true;
            } else {
                log.info("[EsFillerTalentService: syncTalentToES2 @-1] sync talent to EsFiller error, id: {}, request: {}, response: {}, response code: {}, response message: {}", talentId, requestBody, response, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_ESFILLER, talentId, AsyncEnum.DATA_TYPE_TALENT, Status.Failed, response != null ? response.getCode() : null, response != null ? response.getBody() : null));
                code = response != null ? response.getCode() : FAILED_CODE;
                responseBody = response != null ? response.getBody() : "";
                return false;
            }
        } catch (Exception e) {
            log.error("[EsFillerTalentService: syncTalentToES2 @-1] sync talent to EsFiller IOException, talent id: [{}], request: {}, message = [{}]", talentId, requestBody, ExceptionUtils.getStackTrace(e));
            jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_ESFILLER, talentId, AsyncEnum.DATA_TYPE_TALENT, Status.Failed, ERROR_CODE, e.getMessage()));
            code = ERROR_CODE;
            responseBody = "EsFiller Talent Timeout";
            return false;
        } finally {
            if (code != SUCCESS_CODE) {
                if (code == ERROR_CODE) {
                    int errorCount = jobService.countSyncError(AsyncEnum.DATA_TYPE_TALENT, talentId).getBody();
                    if (errorCount >= applicationProperties.getPauseSyncThreshold()) {
                        talentRepository.setTalentSyncToPaused(talentId);
                        String msg =
                                "=========== Sync talent to ES Error =========== \n" +
                                        "TalentId: " + talentId + " (timeout more than " + applicationProperties.getPauseSyncThreshold() + " times)\n" +
                                        "ResponseBody: " + responseBody + "\n" +
                                        "RequestBody: " + requestBody;
                        NotificationUtils.sendAlertToLark(applicationProperties.getLarkWebhookKey(), applicationProperties.getLarkWebhookUrl(), msg);
                    }
                } else {
                    talentRepository.setTalentSyncToPaused(talentId);
                    String msg =
                            "=========== Sync talent to ES Error =========== \n" +
                                    "TalentId: " + talentId + " \n" +
                                    "ErrorCode: " + code + " \n" +
                                    "ResponseBody: " + responseBody + "\n" +
                                    "RequestBody: " + requestBody;
                    NotificationUtils.sendAlertToLark(applicationProperties.getLarkWebhookKey(), applicationProperties.getLarkWebhookUrl(), msg);
                }
            }
        }*/
        return true;
    }

    @Async
    @Override
    public void asyncTalentToEs(Long talentId, Long tenantId) {
        /*entityManager.clear();
        TalentV3 talent = talentRepository.findById(talentId).orElse(null);
        if (talent == null) {
            log.error("[apn talent sync to es , talent is null by id ={}]", talentId);
            return;
        }
        try {
            JSONObject es = translateEs(talent);
            if (ObjectUtil.isEmpty(es)) {
                return;
            }
            HttpResponse response = httpService.post(syncUrl(tenantId, talentId), JSON.toJSONString(es));
            if (!Objects.isNull(response) && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode()) && org.apache.commons.lang3.StringUtils.isNotBlank(response.getBody())) {
//                log.info("[APN: TalentService] create/update talent to EsFiller success, id: {}, response code: {}, response message: {}", talentId, response.getCode(), response.getBody());
                if (JSONUtil.isJsonObj(response.getBody())) {
                    saveTalentInfos(response.getBody(), talent);
                }
                talentRepository.updateTalentLastSyncTime(talentId);
                jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, talentId, AsyncEnum.DATA_TYPE_TALENT, Status.Available, response.getCode(), response.getBody()));
            } else {
                log.error("[APN: TalentService] create/update talent to EsFiller error, id: {}, response code: {}, response message: {}", talentId, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, talentId, AsyncEnum.DATA_TYPE_TALENT, Status.Failed, response != null ? response.getCode() : null, response != null ? response.getBody() : null));
            }
        } catch (IOException e) {
            log.error("[APN: TalentService] create/update talent to EsFiller IOException, id: {}", talentId);
            jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_SINGLE, talentId, AsyncEnum.DATA_TYPE_TALENT, Status.Failed, ERROR_CODE, e.getMessage()));
        }*/
    }

    @Override
    public void syncTranslateTalentToEs(Long talentId) {
        /*TalentV3 talent = talentRepository.findById(talentId).orElse(null);
        if (talent == null) {
            return;
        }
        JSONObject es = translateEs(talent);
        try {
            HttpResponse response = httpService.post(syncUrl(talent.getTenantId(), talent.getId()), es.toString());
            if (!Objects.isNull(response) && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode()) && org.apache.commons.lang3.StringUtils.isNotBlank(response.getBody())) {
//                log.info("[APN: EsFillerTalentService @-1] batch sync talent to EsFiller success, id: {}", talent.getId());
                if (JSONUtil.isJsonObj(response.getBody())) {
                    saveTalentInfos(response.getBody(), talent);
                }
                talentRepository.updateTalentLastSyncTime(talentId);
                jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_ESFILLER_BATCH, talent.getId(), AsyncEnum.DATA_TYPE_TALENT, Status.Available, response.getCode(), response.getBody()));
            } else {
                log.error("[APN: EsFillerTalentService @-1] batch sync talent to EsFiller error, id: {}, response code: {}, response message: {}", talentId, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_ESFILLER_BATCH, talent.getId(), AsyncEnum.DATA_TYPE_TALENT, Status.Failed, response != null ? response.getCode() : null, response != null ? response.getBody() : null));
            }
        } catch (IOException e) {
            jobService.saveAsyncRecord(new AsyncRecord(AsyncEnum.ASYNC_TYPE_ESFILLER_BATCH, talent.getId(), AsyncEnum.DATA_TYPE_TALENT, Status.Failed, ERROR_CODE, "ESFiller Talent timeout"));
            log.error("[APN: EsFillerTalentService @-1] batch sync talent to EsFiller IOException", e);
        }*/
    }

    @Override
    public String searchFromCommonPool(String searchRequest) throws IOException {
        if (ObjectUtil.isNull(searchRequest)) {
            return null;
        }
        //common pool url
        StopWatch stopWatch = new StopWatch("talentDataServiceTask");
        stopWatch.start();
        HttpResponse response = httpService.post(commonDbUrl(), getHeaders(), searchRequest);
        stopWatch.stop();
        log.info("[apn] talentDataServiceTask time [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsFillerTalentService @{}] search talent from common service success, searchRequest: {}", SecurityUtils.getUserId(), searchRequest);
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode())) {
                return ObjectUtil.toString(new TalentESResponseDTO());
            } else {
                log.error("[APN: EsFillerTalentService @{}] search talent from common service error, searchRequest: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), searchRequest, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsFillerTalentService @{}] search talent from common service error and response is null, searchRequest: {}", SecurityUtils.getUserId(), searchRequest);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.ELASTIC_SEARCHFROMCOMMONPOOL_RESPONSENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        return response.getBody();
    }

    @Override
    public String searchContactsFromCommonPool(TalentESConditionDTO condition) throws IOException {
        if (ObjectUtil.isNull(condition.getEsId())) {
            return null;
        }
        //common pool url
        HttpResponse response = httpService.post(getContactsUrl(), getHeaders(), JSONUtil.toJsonStr(condition));
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsFillerTalentService @{}] search contacts from common service success, condition: {}", SecurityUtils.getUserId(), condition);
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode())) {
                return ObjectUtil.toString(new TalentESResponseDTO());
            } else {
                log.error("[APN: EsFillerTalentService @{}] search contacts from common service error, condition: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), condition, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsFillerTalentService @{}] search contacts from common service error and response is null, condition: {}", SecurityUtils.getUserId(), condition);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.ELASTIC_SEARCHFROMCOMMONPOOL_RESPONSENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        return response.getBody();
    }

    @Async
    @Override
    public void updateClientContactName(Long clientId) {
        SalesLeadClientContact salesLeadClientContact = companyService.getSalesLeadClientContactById(clientId).getBody();
        if (salesLeadClientContact == null) {
            log.error("[APN: EsFillerTalentService] update clientContactName by es is error, clientId = [{}]", clientId);
            return;
        }
        TalentV3 talentV3 = talentRepository.findById(salesLeadClientContact.getTalentId()).orElse(null);
        if (talentV3 == null) {
            log.error("[APN: EsFillerTalentService] update clientContactName by es is error, clientId = [{}], talentId: {}", clientId, salesLeadClientContact.getTalentId());
            return;
        }
        Map<String, String> param = new HashMap<>(16);
        param.put("fullName", CommonUtils.formatFullName(talentV3.getFirstName(), talentV3.getLastName(), talentV3.getFullName()));
        try {
            HttpResponse response = httpService.post(updateClientContactNameUrl(SecurityUtils.getTenantId(), clientId), JSON.toJSONString(param));
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
//                log.info("[APN: EsFillerTalentService] Update clientContact name to EsFiller success, id: {}, response code: {}, response message: {}", clientId, response.getCode(), response.getBody());
            } else {
                log.error("[APN: EsFillerTalentService] Update clientContact name to EsFiller error, id: {}, response code: {}, response message: {}", clientId, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } catch (IOException e) {
            log.error("[APN: EsFillerTalentService] Update clientContact name IOException, clientId id {}", clientId);
        }
    }

    @Override
    public void updateTalentsFolder(Long hotlistId, List<Long> talentIds, boolean addToFolder, Integer count, Long tenantId) {
        EsFillerFolderUpdateDTO folderUpdateDTO = new EsFillerFolderUpdateDTO();
        folderUpdateDTO.setIds(talentIds.stream().map(String::valueOf).collect(Collectors.toList()));
        if (addToFolder) {
            folderUpdateDTO.setTo(CollUtil.newArrayList(hotlistId + ""));
        } else {
            folderUpdateDTO.setFrom(CollUtil.newArrayList(hotlistId + ""));
        }
        String url = applicationProperties.getEsFillerSyncUrl() + tenantId + "/talents_folder_update";
        try {
            log.info(" update talents folder request url = {} hotListId = {}, addToFolder = {}, count = {}", url, hotlistId, addToFolder, count);
            HttpResponse response = httpService.post(url, JSON.toJSONString(folderUpdateDTO));
            int code = response.getCode();
            if (!Objects.equals(code, HttpStatus.OK.value())) {
                log.info(" update talents folder response result = {}", response.getBody());
                if (count >= 10) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.ELASTIC_UPDATETALENTSFOLDER_INTERNALERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
                }
                TimeUnit.SECONDS.sleep(1);
                updateTalentsFolder(hotlistId, talentIds, addToFolder, count + 1, tenantId);
            }
            log.info(" update talents folder success ");
        } catch (IOException e) {
            log.error("update talents folder error: {}", e.getMessage());
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @NotNull
    private Headers getHeaders() {
        Map<String, String> headersbuilder = new HashMap<>(16);
        headersbuilder.put("pin", applicationProperties.getApnCommonServicePin());
        return Headers.of(headersbuilder);
    }

    public static boolean isValidDate(String birthday) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            LocalDate.parse(birthday, formatter);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    public JSONObject translateEs(TalentV3 talent) {
        //add unusedKey from jdParser
        JSONObject esDocumentJson = new JSONObject();
        if (Objects.isNull(talent)) {
            return esDocumentJson;
        }

        //sourceAgency
        Optional<AgencyTalentRelation> agency = agencyTalentRelationRepository.findByTalentId(talent.getId());
        agency.ifPresent(agencyTalentRelation -> esDocumentJson.put("sourceAgency", String.valueOf(agencyTalentRelation.getAgencyId())));

        //additionalInfo
        addAdditionalInfo(esDocumentJson, talent);
        //applications
        addApplications(esDocumentJson, talent);
        //add start(availableDate)
        addStart(esDocumentJson, talent);
        if (StringUtils.isNotBlank(talent.getPhotoUrl())) {
            esDocumentJson.put("s3PhotoUrl", talent.getPhotoUrl());
        }
        Instant createdDate = talent.getCreatedDate();
        if (createdDate != null) {
            //createdDate
            esDocumentJson.put("createdDate", createdDate);
        }
        //contacts
        addContacts(esDocumentJson, talent);
        //currency
        if (ObjectUtil.isNotEmpty(esDocumentJson.get("currency"))) {
            EnumCurrency enumCurrency;
            if (esDocumentJson.get("currency") instanceof String && !NumberUtil.isNumber(String.valueOf(esDocumentJson.get("currency")))) {
                enumCurrency = enumCurrencyService.findEnumCurrencyByName((String) esDocumentJson.get("currency"));
            } else {
                Integer currency = (Integer) esDocumentJson.get("currency");
                enumCurrency = enumCurrencyService.findEnumCurrencyById(currency);
            }
            if (ObjectUtil.isNotEmpty(enumCurrency)) {
                esDocumentJson.put("currency", enumCurrency.getName());
                esDocumentJson.put("currencyUSDExchangeRate", enumCurrency.getFromUsdRate());
            }
        } else {
            //TODO 临时处理下 currency没有的错误数据其他也不传
            esDocumentJson.remove("currencyUSDExchangeRate");
            esDocumentJson.remove("salaryRange");
            esDocumentJson.remove("payType");
        }
//        if (ObjectUtil.isNotEmpty(esDocumentJson.get("preferredCurrency"))) {
//            EnumCurrency enumCurrency;
//            if (esDocumentJson.get("preferredCurrency") instanceof String && !NumberUtil.isNumber(String.valueOf(esDocumentJson.get("preferredCurrency")))) {
//                enumCurrency = enumCurrencyService.findEnumCurrencyByName((String) esDocumentJson.get("preferredCurrency"));
//            } else {
//                Integer currency = (Integer) esDocumentJson.get("preferredCurrency");
//                enumCurrency = enumCurrencyService.findEnumCurrencyById(currency);
//            }
//            if (ObjectUtil.isNotEmpty(enumCurrency)) {
//                esDocumentJson.put("preferredCurrency", enumCurrency.getName());
//                esDocumentJson.put("preferredCurrencyUSDExchangeRate", enumCurrency.getFromUsdRate());
//            }
//        } else {
//            //TODO 临时处理下 currency没有的错误数据其他也不传
//            esDocumentJson.remove("preferredCurrencyUSDExchangeRate");
//            esDocumentJson.remove("preferredSalaryRange");
//            esDocumentJson.remove("preferredPayType");
//        }
        //currentLocation
        TalentCurrentLocation currentLocation = talentLocationRepository.findByTalentId(talent.getId());
        if (ObjectUtil.isNotEmpty(currentLocation)) {
            esDocumentJson.put("currentLocation", JSONUtil.toBean(currentLocation.getOriginalLoc(), LocationDTO.class));
        }
        //firstName/lastName/fullName
        if (ObjectUtil.isNotEmpty(talent.getFirstName())) {
            esDocumentJson.put("firstName", talent.getFirstName());
        }
        if (ObjectUtil.isNotEmpty(talent.getLastName())) {
            esDocumentJson.put("lastName", talent.getLastName());
        }
        if (ObjectUtil.isNotEmpty(talent.getFullName())) {
            esDocumentJson.put("fullName", talent.getFullName());
        }
        if (ObjectUtil.isNotEmpty(talent.getOwnedByTenants())) {
            esDocumentJson.put("ownedByTenants", String.valueOf(talent.getOwnedByTenants()));
        }
        //industries
        if (ObjectUtil.isNotEmpty(talent.getIndustries())) {
            esDocumentJson.put("industries", talent.getIndustries().stream().map(TalentIndustryRelation::getEnumId).collect(Collectors.toList()));
        }
        //jobFunctions
        if (ObjectUtil.isNotEmpty(talent.getJobFunctions())) {
            esDocumentJson.put("jobFunctions", talent.getJobFunctions().stream().map(TalentJobFunctionRelation::getEnumId).collect(Collectors.toList()));
        }
        //languages
        if (ObjectUtil.isNotEmpty(talent.getLanguages())) {
            esDocumentJson.put("languages", enumLanguageService.transferLanguagesByIds(EnumRelationDTO.convert(talent.getLanguages())));
        }
        //folders
        addFolders(esDocumentJson, talent);
        //gender
//        Integer genderId = talent.getGender();
//        if(ObjectUtil.isNotEmpty(genderId)) {
//            List<EnumGender> allEnumGender = enumCommonService.findAllEnumGender();
//            allEnumGender.stream().filter(p -> p.getEnumId() == genderId).findFirst().ifPresent(enumGender -> esDocumentJson.put("gender", enumGender.getName()));
//        }
        //motivation
        Integer motivationId = talent.getMotivationId();
        if (ObjectUtil.isNotEmpty(motivationId)) {
            List<EnumMotivation> allEnumMotivation = enumCommonService.findAllEnumMotivation();
            allEnumMotivation.stream().filter(p -> p.getEnumId().equals(motivationId)).findFirst().ifPresent(motivation -> esDocumentJson.put("motivation", StringUtils.join(motivation.getDisplayOrder(), ":", motivation.getEnumName())));
        }
        Instant lastModifiedDate = getTalentLastModifiedDate(talent);
        if (lastModifiedDate != null) {
            //lastModifiedDate
            esDocumentJson.put("lastModifiedDate", DateUtil.fromInstantToUtcDateTimeWithMillisecond(lastModifiedDate));
        }
        if (talent.getId() != null) {
            //text/skillsText
            Resume resume = resumeRepository.findOneByTalentId(talent.getId(), CommonDataStatus.AVAILABLE.toDbValue());
            if (ObjectUtil.isNotEmpty(resume)) {
                if (ObjectUtil.isNotEmpty(resume.getText())) {
                    esDocumentJson.put("text", resume.getText());
                }
                if (ObjectUtil.isNotEmpty(resume.getSkillsText())) {
                    esDocumentJson.put("skillsText", resume.getSkillsText());
                }
            }
        }
        //workAuthorization
        if (ObjectUtil.isNotEmpty(talent.getWorkAuthorization())) {
            List<String> ids = EnumRelationDTO.convert(talent.getWorkAuthorization());
            List<EnumWorkAuthorization> enumWorkAuthorizationList = new ArrayList<>();
            ids.forEach(o -> {
                EnumWorkAuthorization enumWorkAuthorization = new EnumWorkAuthorization();
                enumWorkAuthorization.setId(Long.parseLong(o));
                enumWorkAuthorizationList.add(enumWorkAuthorization);
            });
            JSONObject jsonObject = enumWorkAuthorizationService.getUINameByName(enumWorkAuthorizationList);
            if (jsonObject != null && jsonObject.containsKey("data")) {
                esDocumentJson.put("workAuthorization", jsonObject.get("data"));
            }
        }
        if(talent.getId() != null) {
            TalentV3 talentV3 = talentRepository.findById(talent.getId()).orElse(null);
            if(talentV3 != null) {
                CreditTransaction creditTransaction = userService.findCreditTransactionByTenantIdAndStatusAndTalentIdIs(talentV3.getTenantId(), Status.Available, talent.getId()).getBody();
                if(creditTransaction != null) {
                    String commonDBSearchESId = creditTransaction.getCommonDBSearchESId();
                    if(commonDBSearchESId != null) {
                        esDocumentJson.put("commondbSearchId", commonDBSearchESId);
                    }
                }
            }
        }

        //Notes
        addNotes(esDocumentJson, talent);
        //Review Notes
        addReviewNotes(esDocumentJson, talent);

        List<TalentOwnership> allByTalentId = talentOwnershipRepository.findAllByTalentId(talent.getId());
        //TalentResponsibility
        addTalentResponsibility(esDocumentJson, talent, allByTalentId);
        //foldersOfPreSubmitTalents
        addFoldersOfPreSubmitTalents(esDocumentJson, talent);

        //affiliation
        setAffiliation(esDocumentJson, talent, allByTalentId);

        log.info("[APN: EsFillerTalentService] get talent id:{} es document -> es: {}", talent.getId(), esDocumentJson);
        return esDocumentJson;
    }


    @Resource
    private TalentReviewNoteRepository reviewNoteRepository;

    private void addReviewNotes(JSONObject esDocumentJson, TalentV3 talent) {
        if (talent.getId() == null) {
            return;
        }
        List<TalentReviewNote> talentReviewNotes = reviewNoteRepository.findAllByTalentIdIsOrderByLastModifiedDateAsc(talent.getId());
        if (talentReviewNotes.isEmpty()) {
            return;
        }
//        talentReviewNotes = filterReviewNotes(talentReviewNotes);
        List<JSONObject> notes = talentReviewNotes.stream().map(n -> {
            JSONObject note = new JSONObject();
            note.put("reviewedBy", getConcatReviewedBy(n));
            String timezone = n.getTimezone();
            note.put("reviewTime", convertToUTC(n.getReviewedDate(), timezone));
            note.put("lastModifiedDate", DateUtil.fromInstantToUtcDateTimeWithMillisecond(n.getLastModifiedDate()));
            return note;
        }).collect(Collectors.toList());
        esDocumentJson.put("reviewInfos", notes);
    }

    /*同样的reviewBy取最后编辑时间最新的审查状态*/
    private List<TalentReviewNote> filterReviewNotes(List<TalentReviewNote> talentReviewNotes) {
        return talentReviewNotes.stream()
                .collect(Collectors.groupingBy(
                        note -> Arrays.asList(note.getReviewedBy(), note.getReviewedByType()),
                        Collectors.maxBy(Comparator.comparing(TalentReviewNote::getLastModifiedDate))
                ))
                .values().stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    private String getConcatReviewedBy(TalentReviewNote n) {
        return StrUtil.concat(false, n.getReviewedByType().name(), "_", String.valueOf(n.getReviewedBy()));
    }

    private String convertToUTC(LocalDateTime reviewedDate, String timezone) {
        ZoneId zone = ZoneId.of(timezone);
        ZonedDateTime zonedDateTime = reviewedDate.atZone(zone);
        ZonedDateTime utc = zonedDateTime.withZoneSameInstant(ZoneId.of("Z"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
        return utc.format(formatter);
    }

    private EnumGenderIdentity getGenderEnumById(String genderName, List<EnumGenderIdentity> allEnumGender) {
        for (EnumGenderIdentity gender : allEnumGender) {
            if (gender.getName().equals(genderName)) {
                return gender;
            }
        }
        return null;
    }

    private Instant getTalentLastModifiedDate(TalentV3 talent) {
        Long id = talent.getId();
        if (id == null) {
            return talent.getLastModifiedDate();
        }
        return talentRepository.getTalentEsLastModifiedTime(id);
    }

    @Resource
    private TalentRelateJobFolderTalentRepository talentRelateJobFolderTalentRepository;

    @Resource
    private TalentRelateJobFolderRepository talentRelateJobFolderRepository;

    private void addFoldersOfPreSubmitTalents(JSONObject esDocumentJson, TalentV3 talent) {
        List<TalentAssociationJobFolderTalent> folderTalents = talentRelateJobFolderTalentRepository.findByTalentIdIs(talent.getId());
        Set<String> folderIdSet = folderTalents.stream().map(TalentAssociationJobFolderTalent::getTalentRelateJobFolderFolderId).collect(Collectors.toSet());
        List<TalentAssociationJobFolder> allByFolderIdIn = talentRelateJobFolderRepository.findAllByFolderIdIn(new ArrayList<>(folderIdSet));

        if (!folderTalents.isEmpty()) {
            Map<String, List<TalentAssociationJobFolderTalent>> folderMap = folderTalents.stream()
                    .collect(Collectors.groupingBy(TalentAssociationJobFolderTalent::getTalentRelateJobFolderFolderId));
            List<JSONObject> folders = new ArrayList<>();
            for (Map.Entry<String, List<TalentAssociationJobFolderTalent>> entry : folderMap.entrySet()) {
                String folderId = entry.getKey();
                List<TalentAssociationJobFolderTalent> value = entry.getValue();
                JSONObject object = new JSONObject();
                List<Long> addedUserList = value.stream().map(TalentAssociationJobFolderTalent::getAddedBy).collect(Collectors.toList());
                object.put("folderId", folderId);
                setTalentFolderResponsibility(addedUserList, object);
                String jobId = getJobIdByFolderId(folderId, allByFolderIdIn);
                if(jobId != null) {
                    object.put("jobId", jobId);
                }
                folders.add(object);
            }
            esDocumentJson.put("foldersOfPreSubmitTalents", folders);
        }
    }

    private String getJobIdByFolderId(String folderId, List<TalentAssociationJobFolder> allByFolderIdIn) {
        for(TalentAssociationJobFolder folder : allByFolderIdIn) {
            if(folder.getFolderId().equals(folderId)) {
                return folder.getJobId().toString();
            }
        }
        return null;
    }

    private void setTalentFolderResponsibility(List<Long> addedUserId, JSONObject object) {
        List<EnumUserResponsibility> userResponsibilities = enumCommonService.findFoldersOfPreSubmitTalentsEnumUserResponsibility();
        for (EnumUserResponsibility userResponsibility : userResponsibilities) {
            String esKey = getResponsibilityEsKey(userResponsibility.getFoldersOfPreSubmitTalentsEsKey());
            switch (userResponsibility.getLabel()) {
                case ResponsibilityConstants.ADDED_BY:
                    if (!addedUserId.isEmpty()) {
                        object.put(esKey, packageIdListToIdAndNameJSONArray(addedUserId));
                    }
                    break;
            }
        }
    }

    private void setAffiliation(JSONObject esDocumentJson, TalentV3 talent, List<TalentOwnership> allByTalentId) {
        List<String> affiliations = new ArrayList<>();
        //talent share to all
        Boolean viewableToAll = isCurrentTalentShareWithAll(allByTalentId, talent);
        //check current talent is as contact;
        Boolean activeContact = isTalentActiveContact(talent);

        //暂时不需要talent查看权限,所以同步时作为可以被所有人看到的talent传all, client contact且非共享给所有人则不传
        if (viewableToAll || !activeContact) {
            affiliations.add("all");
        }
        List<Long> puserId = new ArrayList<>();
        puserId.add(talent.getPermissionUserId());
        puserId.addAll(allByTalentId.stream()
                .filter(talentOwnership -> TalentOwnershipType.SHARE.equals(talentOwnership.getOwnershipType()) || TalentOwnershipType.TALENT_OWNER.equals(talentOwnership.getOwnershipType()))
                .map(TalentOwnership::getUserId).collect(Collectors.toList()));
        puserId = puserId.stream().filter(Objects::nonNull).collect(Collectors.toList());
        //get ownership userId
        List<String> userStr = puserId.stream().distinct()
                .map(u -> "puser_" + u).toList();
        if(CollUtil.isNotEmpty(userStr)){
            affiliations.addAll(userStr);
        }
        //pteam 只返回talent_owner的团队 兼容老数据中没有设置talent_owner的情况
        List<Long> ownerUserId = allByTalentId.stream().filter(o -> TalentOwnershipType.TALENT_OWNER.equals(o.getOwnershipType())).map(TalentOwnership::getUserId).collect(Collectors.toList());
        if (Objects.nonNull(talent.getCreatedBy())){
            ownerUserId.add(SecurityUtils.getUserIdFromCreatedBy(talent.getCreatedBy()));
        }else {
            ownerUserId.add(talent.getPermissionUserId());
        }

        List<Long> pteamId = new ArrayList<>();
        ownerUserId.stream().distinct().forEach(c -> {
            if(c == null){
                return;
            }
            if (Objects.isNull(talent.getTenantId())){
                return;
            }
            PermissionUserTeamPermissionVM.PermissionDetail permissionVM = userService.getAllDataPermissionsByTenantIdAndUserId(talent.getTenantId(), c).getBody();
            Long primaryTeamId = permissionVM.getPrimaryTeamId();
            if(primaryTeamId != null) {
                pteamId.add(primaryTeamId);
            }
        });
        List<String> teamStr = pteamId.stream().distinct()
                .map(u -> "pteam_" + u).toList();
        if(CollUtil.isNotEmpty(teamStr)){
            affiliations.addAll(teamStr);
        }

        esDocumentJson.put("affiliations", affiliations);
    }

    private void addTalentResponsibility(JSONObject object, TalentV3 talent, List<TalentOwnership> allByTalentId) {
        if (talent.getId() == null) {
            return;
        }
        List<EnumUserResponsibility> talentEnumUserResponsibility = enumCommonService.findTalentEnumUserResponsibility();
        //List<TalentOwnership> allByTalentId = talentOwnershipRepository.findAllByTalentId(talent.getId());
        talentEnumUserResponsibility.forEach(r -> {
            String talentEsKey = r.getTalentEsKey();
            String esKey = getResponsibilityEsKey(talentEsKey);
            switch (r.getLabel()) {
                case ResponsibilityConstants.OWNER:
                    //兼容老数据中没有设置talent_owner的情况
                    List<Long> ownerUserId = allByTalentId.stream().filter(o -> TalentOwnershipType.TALENT_OWNER.equals(o.getOwnershipType())).map(TalentOwnership::getUserId).collect(Collectors.toList());
                    if (ownerUserId.isEmpty() && StringUtils.isNotEmpty(talent.getCreatedBy())) {
                        ownerUserId.add(SecurityUtils.getUserIdFromCreatedBy(talent.getCreatedBy()));
                    }
                    if (!ownerUserId.isEmpty()) {
                        object.put(esKey, packageIdListToIdAndNameJSONArray(ownerUserId));
                    }
                    break;
                case ResponsibilityConstants.SHARED_BY:
                    List<Long> shareUserId = allByTalentId.stream().filter(o -> TalentOwnershipType.SHARE.equals(o.getOwnershipType())).map(TalentOwnership::getUserId).collect(Collectors.toList());
                    // 全员开放时，userId 存一个负一
                    allByTalentId.stream().filter(o -> TalentOwnershipType.TENANT_SHARE.equals(o.getOwnershipType())).findAny().ifPresent(o -> {
                        JSONArray array = new JSONArray();
                        JSONObject tenantShareUser = new JSONObject();
                        tenantShareUser.put("id", -1L);
                        tenantShareUser.put("name", TalentOwnershipType.TENANT_SHARE.name());
                        array.add(tenantShareUser);
                        object.put(esKey, array);
                    });
                    if (!shareUserId.isEmpty()) {
                        object.put(esKey, packageIdListToIdAndNameJSONArray(shareUserId));
                    }
                    break;
                case ResponsibilityConstants.CREATED_BY:
                    String createdByStr = talent.getCreatedBy();
                    if (StringUtils.isNotEmpty(createdByStr)) {
                        Long createdBy = SecurityUtils.getUserIdFromCreatedBy(createdByStr);
                        object.put(esKey, packageIdListToIdAndNameJSONArray(Lists.newArrayList(createdBy)));
                    }
                    break;
                case ResponsibilityConstants.CONFIDENTIAL_OWNER:
                    Optional<ConfidentialInfoDto> confidentialInfo = confidentialService.getConfidentialInfo(talent.getId());
                    confidentialInfo.ifPresent(confidentialInfoDto -> {
                        object.put(esKey, packageIdListToIdAndNameJSONArray(Lists.newArrayList(confidentialInfoDto.getConfidentialOwner())));
                        confidentialInfoDto.setConfidentialOwner(null);
                        object.put("confidentialInfo", JSONUtil.parseObj(JSONUtil.toJsonStr(confidentialInfoDto)));
                    });
                    break;
            }
        });
    }

    private void addNotes(JSONObject esDocumentJson, TalentV3 talent) {
        if (talent.getId() == null) {
            return;
        }
        List<TalentNote> allByTalentId = talentNoteRepository.findAllByTalentId(talent.getId());
        if (allByTalentId.isEmpty()) {
            return;
        }
        List<EnumMotivation> allEnumMotivation = enumCommonService.findAllEnumMotivation();
        List<EnumUserResponsibility> talentNoteEnumUserResponsibility = enumCommonService.findTalentNoteEnumUserResponsibility();
        List<JSONObject> notes = allByTalentId.stream().map(n -> getTalentJsonObject(n, allEnumMotivation, talentNoteEnumUserResponsibility)).collect(Collectors.toList());
        esDocumentJson.put("notes", notes);
    }


    private Boolean isCurrentTalentShareWithAll(List<TalentOwnership> allByTalentId, TalentV3 talentV3) {
        var tanentShareList = allByTalentId.stream().filter(talentOwnership -> TalentOwnershipType.TENANT_SHARE.equals(talentOwnership.getOwnershipType()) && Constants.TALENT_OWNERSHIP_SHARE_WITH_ALL_USER_ID.equals(talentOwnership.getUserId())).toList();
        if (!tanentShareList.isEmpty()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }

    private JSONObject getTalentJsonObject(TalentNote n, List<EnumMotivation> allEnumMotivation, List<EnumUserResponsibility> talentNoteEnumUserResponsibility) {
        JSONObject object = new JSONObject();
        object.put("id", n.getId());
        object.put("createdDate", n.getCreatedDate());
        object.put("lastModifiedDate", n.getLastModifiedDate());
        object.put("title", n.getTitle());
        object.put("text", HtmlUtil.cleanHtmlTag(n.getNote()));
        TalentNoteType noteType = n.getNoteType();
        if (noteType != null) {
            object.put("contactType", noteType.name());
        }
        Integer noteStatus = n.getNoteStatus();
        if (noteStatus != null) {
            Optional<EnumMotivation> motivation = allEnumMotivation.stream().filter(p -> p.getEnumId().equals(noteStatus)).findFirst();
            motivation.ifPresent(c -> object.put("motivation", c.getDisplayOrder() + ":" + c.getName()));
        }
        resolveTalentNoteResponsibility(object, talentNoteEnumUserResponsibility, n);
        return object;
    }

    private JSONArray packageIdListToIdAndNameJSONArray(List<Long> idList) {
        List<User> userList = userService.findByIds(idList).getBody();
        JSONArray array = new JSONArray();
        idList.forEach(id -> {
            JSONObject object = new JSONObject();
            object.put("id", id);
            String name = getNameByUserListAndUserId(userList, id);
            object.put("name", name);

            array.add(object);
        });
        return array;
    }

    private void resolveTalentNoteResponsibility(JSONObject object, List<EnumUserResponsibility> talentNoteEnumUserResponsibility, TalentNote note) {
        talentNoteEnumUserResponsibility.forEach(r -> {
            String talentNoteEsKey = r.getTalentNoteEsKey();
            String esKey = getResponsibilityEsKey(talentNoteEsKey);
            switch (r.getLabel()) {
                case ResponsibilityConstants.CREATED_BY:
                    if(StringUtils.isNotEmpty(note.getCreatedBy())) {
                        Long createdBy = SecurityUtils.getUserIdFromCreatedBy(note.getCreatedBy());
                        if (createdBy != null) {
                            object.put(esKey, packageIdListToIdAndNameJSONArray(Lists.newArrayList(createdBy)));
                        }
                    }
                    break;
                case ResponsibilityConstants.LAST_MODIFIED_BY:
                    if(StringUtils.isNotEmpty(note.getLastModifiedBy())) {
                        Long lastModifiedBy = SecurityUtils.getUserIdFromCreatedBy(note.getLastModifiedBy());
                        if (lastModifiedBy != null) {
                            object.put(esKey, packageIdListToIdAndNameJSONArray(Lists.newArrayList(lastModifiedBy)));
                        }
                    }
                    break;
            }
        });
    }

    private void addFolders(JSONObject esDocumentJson, TalentV3 talent) {
        if (talent.getId() == null) {
            return;
        }
        Set<Long> folders = talentFolderRelationRepository.findFolderIdsByTalentId(talent.getId());
        List<String> folderIds = folders.stream().map(Object::toString)
                .collect(Collectors.toList());
        if (folderIds.isEmpty()) {
            return;
        }
        esDocumentJson.put("folders", folderIds);
    }

    private void addApplications(JSONObject esDocumentJson, TalentV3 talent) {
        if (talent.getId() == null) {
            return;
        }
        ResponseEntity<List<TalentRecruitmentProcessVO>> talentRecruitmentProcess = applicationService.getTalentRecruitmentProcessAllByTalentId(talent.getId());
        List<EnumUserResponsibility> enumUserResponsibility = enumCommonService.findApplicationsEnumUserResponsibility();
        if (talentRecruitmentProcess != null && HttpStatus.OK.equals(talentRecruitmentProcess.getStatusCode())) {
            List<TalentRecruitmentProcessVO> recruitmentProcessList = talentRecruitmentProcess.getBody();
            if (!recruitmentProcessList.isEmpty()) {
                List<JSONObject> applications = recruitmentProcessList.stream().map(r -> {
                    JSONObject object = new JSONObject();
                    NodeType lastNodeType = r.getLastNodeType();
                    if (lastNodeType != null) {
                        NodeStatus lastNodeStatus = r.getLastNodeStatus();
                        //talent同步es时，因为nodeStatus保存着终止状态，在终止时会将lastNoteTypeId传ELIMINATED(4) 实现一个字段可以搜索终止状态的流程
                        if (NodeStatus.ELIMINATED.equals(lastNodeStatus)) {
                            object.put("lastNodeTypeId", String.valueOf(NodeStatus.ELIMINATED.toDbValue()));
                        } else {
                            object.put("lastNodeTypeId", String.valueOf(lastNodeType.toDbValue()));
                        }
                    }
                    Long recruitmentProcessId = r.getRecruitmentProcessId();
                    if (recruitmentProcessId != null) {
                        object.put("recruitmentProcessId", String.valueOf(recruitmentProcessId));
                    }
                    object.put("jobId", String.valueOf(r.getJobId()));
                    object.put("createdDate", r.getCreatedDate());
                    object.put("lastModifiedDate", r.getLastModifiedDate());


                    //application user responsibility
                    resolveApplicationResponsibility(object, enumUserResponsibility, r);


                    return object;
                }).collect(Collectors.toList());


                //组装coAMCountries
                List<TalentRecruitmentProcessKpiUserVO> coAmKpiUserList = new ArrayList<>();
                recruitmentProcessList.forEach(w -> {
                    List<TalentRecruitmentProcessKpiUserVO> kpiUsers = w.getKpiUsers();
                    if (CollUtil.isNotEmpty(kpiUsers)) {
                        coAmKpiUserList.addAll(kpiUsers.stream().filter(v -> UserRole.CO_AM.equals(v.getUserRole())).collect(Collectors.toList()));
                    }
                });

                if(!coAmKpiUserList.isEmpty()){
                    Map<Long,List<TalentRecruitmentProcessKpiUserVO>> coAmMap = coAmKpiUserList.stream().collect(Collectors.groupingBy(v -> v.getUserId()));
                    List<JSONObject> countryList = new ArrayList<>();
                    if (null != coAmMap && !coAmMap.isEmpty()) {
                        coAmMap.forEach((k, v) -> {
                            JSONObject administratorJson = new JSONObject();
                            administratorJson.put("userId", k);
                            administratorJson.put("countryIds", v.stream().map(x -> x.getCountry()).distinct().collect(Collectors.toList()));
                            countryList.add(administratorJson);
                        });
                    }
                    esDocumentJson.put("coAMCountries", countryList);
                }
                esDocumentJson.put("applications", applications);
            }
        }

    }

    private void resolveApplicationResponsibility(JSONObject object, List<EnumUserResponsibility> enumUserResponsibility, TalentRecruitmentProcessVO recruitmentProcess) {
        List<Map<String, Object>> amMapList = new ArrayList<>();
        List<Map<String, Object>> coAmMapList = new ArrayList<>();
        List<Map<String, Object>> dmMapList = new ArrayList<>();
        List<Map<String, Object>> recruiterMapList = new ArrayList<>();
        List<Map<String, Object>> sourcerMapList = new ArrayList<>();
        List<Map<String, Object>> salesLeadOwnerMapList = new ArrayList<>();
        List<Map<String, Object>> bdOwnerMapList = new ArrayList<>();
        List<Map<String, Object>> participantMapList = new ArrayList<>();


        //assignedUsers
        List<TalentRecruitmentProcessKpiUserVO> kpiUsers = recruitmentProcess.getKpiUsers();
        if (CollUtil.isEmpty(kpiUsers)) {
            return;
        }
        List<Long> userIdList = kpiUsers.stream().map(TalentRecruitmentProcessKpiUserVO::getUserId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(userIdList)) {
            return;
        }

        List<User> userList = userService.findByIds(userIdList).getBody();
        if (CollUtil.isNotEmpty(userList)) {
            kpiUsers.forEach(s -> {
                Map<String, Object> map = new HashMap<>(16);
                map.put("id", s.getUserId());
                map.put("name", getNameByUserListAndUserId(userList, s.getUserId()));
                participantMapList.add(map);
                if(s.getUserRole() != null) {
                    switch (s.getUserRole()) {
                        case AM:
                            amMapList.add(map);
                            break;
                        case CO_AM:
                            coAmMapList.add(map);
                            break;
                        case DM:
                            dmMapList.add(map);
                            break;
                        case RECRUITER:
                            recruiterMapList.add(map);
                            break;
                        case SOURCER:
                            sourcerMapList.add(map);
                            break;
                        case SALES_LEAD_OWNER:
                            salesLeadOwnerMapList.add(map);
                            break;
                        case BD_OWNER:
                            bdOwnerMapList.add(map);
                            break;
                        default:
                            break;
                    }
                }
            });
        }

        enumUserResponsibility.forEach(r -> {
            String esKey = getResponsibilityEsKey(r.getApplicationEsKey());
            switch (r.getLabel()) {
                case ResponsibilityConstants.AM:
                    putRoleMapToJson(object, amMapList, esKey);
                    break;
                case ResponsibilityConstants.COOPERATE_ACCOUNT_MANAGER:
                    putRoleMapToJson(object, coAmMapList, esKey);
                    break;
                case ResponsibilityConstants.DM:
                    putRoleMapToJson(object, dmMapList, esKey);
                    break;
                case ResponsibilityConstants.RECRUITER:
                    putRoleMapToJson(object, recruiterMapList, esKey);
                    break;
                case ResponsibilityConstants.SOURCER:
                    putRoleMapToJson(object, sourcerMapList, esKey);
                    break;
                case ResponsibilityConstants.BD_OWNER:
                    putRoleMapToJson(object, bdOwnerMapList, esKey);
                    break;
                case ResponsibilityConstants.SALES_LEAD_OWNER:
                    putRoleMapToJson(object, salesLeadOwnerMapList, esKey);
                    break;
                case ResponsibilityConstants.PARTICIPANT:
                    putRoleMapToJson(object, participantMapList, esKey);
                    break;
                case ResponsibilityConstants.CREATED_BY:
                    UserBriefDTO createdUser = recruitmentProcess.getCreatedUser();
                    if (createdUser != null) {
                        object.put(esKey, packageIdListToIdAndNameJSONArray(Lists.newArrayList(createdUser.getId())));
                    }
                    break;
                case ResponsibilityConstants.LAST_MODIFIED_BY:
                    UserBriefDTO lastModifiedUser = recruitmentProcess.getLastModifiedUser();
                    if (lastModifiedUser != null) {
                        object.put(esKey, packageIdListToIdAndNameJSONArray(Lists.newArrayList(lastModifiedUser.getId())));
                    }
                    break;
            }
        });
    }

    private String getResponsibilityEsKey(String esKey) {
        if (esKey.contains(".")) {
            return esKey.substring(esKey.lastIndexOf(".") + 1, esKey.length());
        }
        return esKey;
    }

    private void putRoleMapToJson(JSONObject object, List<Map<String, Object>> roleMapList, String esKey) {
        if (!roleMapList.isEmpty()) {
            object.put(esKey, roleMapList.stream().distinct().collect(Collectors.toList()));
        }
    }

    private void addContacts(JSONObject esDocumentJson, TalentV3 talent) {
        if (talent.getId() == null) {
            return;
        }
        List<TalentContact> talentContactList = talentContactRepository.findAllByTalentIdAndTenantIdAndStatusOrderBySort(talent.getId(), talent.getTenantId(), TalentContactStatus.AVAILABLE);
        if (ObjectUtil.isNotEmpty(talentContactList)) {
            List<JSONObject> contactList = talentContactList.stream().map(s -> {
                JSONObject object = new JSONObject();
                object.put("contact", s.getContact());
                if (ObjectUtil.isNotEmpty(s.getDetails())) {
                    object.put("details", s.getDetails());
                }
                object.put("type", s.getType());
                if (TalentContactVerificationStatus.WRONG_CONTACT.equals(s.getVerificationStatus())) {
                    object.put("incorrect", true);
                }
                return object;
            }).collect(Collectors.toList());
            esDocumentJson.put("contacts", contactList);
        }
    }

    private void addStart(JSONObject esDocumentJson, TalentV3 talent) {
        if (talent.getId() == null) {
            return;
        }
        StartDTO start = financeService.findStartByTalentIdAndStatus(talent.getId(), StartStatus.ACTIVE).getBody();
        if (start != null) {
            JobDTOV3 job = jobService.findByIdNoToken(start.getJobId()).getBody();
            if (job == null) {
                return;
            }
            RecruitmentProcessBriefDTO recruitmentProcess = job.getRecruitmentProcess();
            if (recruitmentProcess == null) {
                return;
            }
            ResponseEntity<RecruitmentProcessVO> getRecruitmentProcessByIdRes = applicationService.getRecruitmentProcessById(recruitmentProcess.getId());
            if (getRecruitmentProcessByIdRes == null) {
                return;
            }
            RecruitmentProcessVO recruitmentProcessVO = getRecruitmentProcessByIdRes.getBody();
            if (recruitmentProcessVO == null) {
                return;
            }
            if (ObjectUtil.isNotEmpty(job)) {
                if (JobType.FULL_TIME.equals(recruitmentProcessVO.getJobType())) {
                    if (ObjectUtil.isNotEmpty(start.getStartDate())) {
                        esDocumentJson.put("availableDate", (start.getStartDate().plus(3, ChronoUnit.MONTHS)));
                    }
                } else {
                    StartDTO startExtension = financeService.findByTalentIdAndStatusOrderByEndDate(talent.getId(), StartStatus.CONTRACT_EXTENDED).getBody();
                    if (startExtension != null) {
                        esDocumentJson.put("availableDate", startExtension.getEndDate());
                    } else {
                        if (ObjectUtil.isNotEmpty(start.getEndDate())) {
                            esDocumentJson.put("availableDate", start.getEndDate());
                        }
                    }
                }
            }
        }
    }

    private Boolean isTalentActiveContact(TalentV3 talent) {
        com.alibaba.fastjson.JSONObject extendedJson = JSON.parseObject(talent.getTalentExtendedInfo());
        AtomicBoolean isActiveContact = new AtomicBoolean(false);
        com.alibaba.fastjson.JSONArray experiencesJsonArray = extendedJson.getJSONArray("experiences");
        if (ObjectUtil.isNotEmpty(experiencesJsonArray)) {
            experiencesJsonArray.toJavaList(com.alibaba.fastjson.JSONObject.class).forEach(jsonObject -> {
                if (checkKeyInJsonObject(jsonObject, "activeCompanyId") || checkKeyInJsonObject(jsonObject, "activeCRMAccountId")) {
                    isActiveContact.set(true);
                }
            });
        }
        return isActiveContact.get();
    }


    private Boolean checkKeyInJsonObject(com.alibaba.fastjson.JSONObject jsonObject, String field) {
        String strId = jsonObject.getString(field);
        if (StringUtils.isNotEmpty(strId)) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private void addAdditionalInfo(JSONObject esDocumentJson, TalentV3 talent) {
        com.alibaba.fastjson.JSONObject extendedJson = JSON.parseObject(talent.getTalentExtendedInfo());
        if (CollUtil.isNotEmpty(extendedJson)) {
            esDocumentJson.putAll(JSON.parseObject(JSON.toJSONString(extendedJson, (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1))));
        }
        //TODO 考虑下怎么解决   dodaX作为channel时额外的两个字段
        esDocumentJson.remove("dodaXCandidateID");
        esDocumentJson.remove("dodaXOwnershipPeriod");
        esDocumentJson.remove("periodOfStay");
        //过滤候选人优化字段
        esDocumentJson.remove("currentCompany");
        esDocumentJson.remove("currentTitle");
        //临时处理 将collegeInfo中值为空对象的key移除，比如"collegeInfo":{"QS2021Rank":{},"englishCollegeName":{},"categories":{},"chineseCollegeName":"青岛大学","QSRank":{}}
        removeEmptyObjects(extendedJson);
        //experience中的id转为string传入es
        com.alibaba.fastjson.JSONArray experiencesJsonArray = extendedJson.getJSONArray("experiences");
        if (ObjectUtil.isNotEmpty(experiencesJsonArray)) {
            List<com.alibaba.fastjson.JSONObject> experiencesList = experiencesJsonArray.toJavaList(com.alibaba.fastjson.JSONObject.class).stream().map(jsonObject -> {
                translateObjectIdToStr(jsonObject, "activeCompanyId");
                translateObjectIdToStr(jsonObject, "companyId");
                translateObjectIdToStr(jsonObject, "activeCRMAccountId");
                translateObjectIdToStr(jsonObject, "crmAccountId");
                //禁猎客户相关公司id
                translateObjectIdToStr(jsonObject, "businessInfoCompanyId");
                translateObjectIdToStr(jsonObject, "bdCompanyId");
                translateObjectIdToStr(jsonObject, "recogLeadsCompanyId");
                translateObjectIdToStr(jsonObject, "recogCRMAccountId");
                translateObjectIdToStr(jsonObject, "recogCompanyId");
                return jsonObject;
            }).collect(Collectors.toList());
            esDocumentJson.put("experiences", experiencesList);
        }
    }


    private void translateObjectIdToStr(com.alibaba.fastjson.JSONObject jsonObject, String field) {
        String strId = jsonObject.getString(field);
        if (StringUtils.isNotEmpty(strId)) {
            jsonObject.put(field, strId);
        }
    }

    // 递归删除值为空对象的键
    private static void removeEmptyObjects(Object obj) {
        if (obj instanceof JSONObject) {
            JSONObject json_obj = (JSONObject) obj;
            Iterator<String> keys = json_obj.keySet().iterator();

            while (keys.hasNext()) {
                String key = keys.next();
                Object value = json_obj.get(key);

                if (value instanceof JSONObject) {
                    JSONObject value_obj = (JSONObject) value;

                    if (value_obj.isEmpty()) {
                        keys.remove();
                    } else {
                        removeEmptyObjects(value_obj);
                    }
                } else if (value instanceof JSONArray) {
                    JSONArray value_arr = (JSONArray) value;
                    for (int i = 0; i < value_arr.size(); i++) {
                        removeEmptyObjects(value_arr.get(i));
                    }
                }
            }
        } else if (obj instanceof JSONArray) {
            JSONArray json_arr = (JSONArray) obj;

            for (int i = 0; i < json_arr.size(); i++) {
                removeEmptyObjects(json_arr.get(i));
            }
        }
    }


    private String getNameByUserListAndUserId(List<User> userList, Long userId) {
        String userName = "";
        if (CollUtil.isEmpty(userList)) {
            return userName;
        }
        if (userList.stream().anyMatch(u -> u.getId().equals(userId))) {
            Optional<User> userOptional = userList.stream().filter(u -> u.getId().equals(userId)).findFirst();
            User user = new User();
            if (userOptional.isPresent()) {
                user = userOptional.get();
            }
            userName = CommonUtils.formatFullName(user.getFirstName(), user.getLastName());
        }
        return userName;
    }

//    @Override
//    public void syncOneByRedis(Integer partitionId, Long talentId) {
//        String syncedKey = RedisConstants.esfillerTalentSyncedKey(partitionId);
//        String errorCodeKey = RedisConstants.ESFILLER + RedisConstants.SYNC_TALENTS_TO_ES + RedisConstants.ERROR + talentId + RedisConstants.CODE;
//        String errorBodyKey = RedisConstants.ESFILLER + RedisConstants.SYNC_TALENTS_TO_ES + RedisConstants.ERROR + talentId + RedisConstants.BODY;
//        TalentV3 talentV3 = talentRepository.findById(talentId).orElse(null);
//        if (talentV3 == null) {
//            redisService.hset(syncedKey, String.valueOf(talentId), HttpStatus.NOT_FOUND.toString());
//        } else {
//            String redisStatus = redisService.hget(syncedKey, String.valueOf(talentId));
//            if (StringUtils.isNotEmpty(redisStatus) && HttpStatus.OK.toString().equals(redisStatus)) {
//                log.info("[APN: EsFillerTalentService @{}] this talent synced already, id: {}", SecurityUtils.getUserId(), talentId);
//            } else {
//                try {
//                    log.info("[APN: EsFillerTalentService @{}] start sync talent, id: {}", SecurityUtils.getUserId(), talentId);
//                    JSONObject es = translateEs(talentV3);
//                    HttpResponse response = httpService.post(syncUrl(talentV3.getTenantId(), talentV3.getId()), JSON.toJSONString(es));
//                    if (response != null) {
//                        if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
//                            log.info("[APN: EsFillerTalentService @{}] sync talent success, id: {}", SecurityUtils.getUserId(), talentId);
//                            redisService.hset(syncedKey, String.valueOf(talentId), HttpStatus.OK.toString());
//                        } else {
//                            redisService.set(errorCodeKey, String.valueOf(response.getCode()));
//                            redisService.set(errorBodyKey, response.getBody());
//                        }
//                    }
//                } catch (IOException e) {
//                    redisService.set(errorCodeKey, HttpStatus.REQUEST_TIMEOUT.toString());
//                    redisService.set(errorBodyKey, e.getMessage());
//                }
//            }
//        }
//        log.info("[APN: EsFillerTalentService @{}] sync talent finished, id: {}", SecurityUtils.getUserId(), talentId);
//    }

//    @Override
//    public void syncOneFromErrorByRedis(Integer partitionId, Long talentId) {
//        String syncedKey = RedisConstants.esfillerTalentSyncedKey(partitionId);
//        String errorCodeKey = RedisConstants.ESFILLER + RedisConstants.SYNC_TALENTS_TO_ES + RedisConstants.ERROR + talentId + RedisConstants.CODE;
//        String errorBodyKey = RedisConstants.ESFILLER + RedisConstants.SYNC_TALENTS_TO_ES + RedisConstants.ERROR + talentId + RedisConstants.BODY;
//        boolean exists = redisService.exists(errorCodeKey);
//        if (exists) {
//            log.info("[APN: EsFillerTalentService @{}] this is an error talent, need to sync, id: {}", SecurityUtils.getUserId(), talentId);
//            TalentV3 talentV3 = talentRepository.findById(talentId).orElse(null);
//            if (talentV3 == null) {
//                redisService.hset(syncedKey, String.valueOf(talentId), HttpStatus.NOT_FOUND.toString());
//            } else {
//                String redisStatus = redisService.hget(syncedKey, String.valueOf(talentId));
//                boolean syncSuccess = StringUtils.isNotEmpty(redisStatus) && HttpStatus.OK.toString().equals(redisStatus);
//                if (syncSuccess) {
//                    redisService.delete(errorCodeKey);
//                    redisService.delete(errorBodyKey);
//                    log.info("[APN: EsFillerTalentService @{}] this talent synced already, id: {}", SecurityUtils.getUserId(), talentId);
//                } else {
//                    try {
//                        log.info("[APN: EsFillerTalentService @{}] start sync talent, id: {}", SecurityUtils.getUserId(), talentId);
//                        JSONObject es = translateEs(talentV3);
//                        HttpResponse response = httpService.post(syncUrl(talentV3.getTenantId(), talentV3.getId()), JSON.toJSONString(es));
//                        if (response != null) {
//                            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
//                                log.info("[APN: EsFillerTalentService @{}] sync talent success, id: {}", SecurityUtils.getUserId(), talentId);
//                                redisService.hset(syncedKey, String.valueOf(talentId), HttpStatus.OK.toString());
//                                redisService.delete(errorCodeKey);
//                                redisService.delete(errorBodyKey);
//                            } else {
//                                redisService.set(errorCodeKey, String.valueOf(response.getCode()));
//                                redisService.set(errorBodyKey, response.getBody());
//                            }
//                        }
//                    } catch (IOException e) {
//                        redisService.set(errorCodeKey, HttpStatus.REQUEST_TIMEOUT.toString());
//                        redisService.set(errorBodyKey, e.getMessage());
//                    }
//                    log.info("[APN: EsFillerTalentService @{}] sync talent finished, id: {}", SecurityUtils.getUserId(), talentId);
//                }
//            }
//        }
//    }

//    private void saveTalentInfos(String responseBody, TalentV3 talent) {
//        JSONObject talentJson = JSONUtil.parseObj(responseBody);
//        //name
//        if (ObjectUtil.isNotNull(talentJson.getStr(ElasticSearchConstants.ESFILLER_KEY_FULL_NAME))) {
//            talent.setFullName(talentJson.getStr(ElasticSearchConstants.ESFILLER_KEY_FULL_NAME));
//        }
//        if (ObjectUtil.isNotNull(talentJson.getStr(ElasticSearchConstants.ESFILLER_KEY_FIRST_NAME))) {
//            talent.setFirstName(talentJson.getStr(ElasticSearchConstants.ESFILLER_KEY_FIRST_NAME));
//        }
//        if (ObjectUtil.isNotNull(talentJson.getStr(ElasticSearchConstants.ESFILLER_KEY_LAST_NAME))) {
//            talent.setLastName(talentJson.getStr(ElasticSearchConstants.ESFILLER_KEY_LAST_NAME));
//        }
//        //current location
//        if (ObjectUtil.isNotEmpty(talentJson.getJSONObject(ElasticSearchConstants.ESFILLER_KEY_CURRENT_LOCATIONS))) {
//            LocationESDTO dto = talentJson.getJSONObject(ElasticSearchConstants.ESFILLER_KEY_CURRENT_LOCATIONS).toBean(LocationESDTO.class);
//            if (ObjectUtil.isNotEmpty(dto.getOfficialCity()) || ObjectUtil.isNotEmpty(dto.getOfficialCountry())
//                    || ObjectUtil.isNotEmpty(dto.getOfficialProvince()) || ObjectUtil.isNotEmpty(dto.getOfficialCounty())) {
//                talentLocationRepository.updateOfficialInfoById(talent.getId(), dto.getOfficialCity(), dto.getOfficialCountry(), dto.getOfficialProvince(), dto.getOfficialCounty());
//            }
//        }
//        //languages
//        if (talent.getLanguages() == null || talent.getLanguages().isEmpty()) {
//            JSONArray languageArray = talentJson.getJSONArray(ElasticSearchConstants.DROP_DOWN_LANGUAGE);
//            if (CollectionUtil.isEmpty(languageArray)) {
//                languageArray = new JSONArray();
//            }
//            List<Long> languages = enumLanguageService.transferLanguagesByNamesToIdWithoutIgnoreParentClass(languageArray);
//            if (languages != null && !languages.isEmpty()) {
//                talent.setLanguages(new HashSet<>(Convert.toList(TalentLanguageRelation.class, EnumRelationDTO.transfer(languages))));
//            }
//        }
//        //jobFunctions
//        if (talent.getJobFunctions() == null || talent.getJobFunctions().isEmpty()) {
//            JSONArray jobFunctionArray = talentJson.getJSONArray(ElasticSearchConstants.DROP_DOWN_JOBFUNCTION);
//            if (CollectionUtil.isEmpty(jobFunctionArray)) {
//                jobFunctionArray = new JSONArray();
//            }
//            List<Long> jobFunctions = enumJobFunctionService.transferJobFunctionsByNamesToIdWithoutIgnoreParentClass(jobFunctionArray);
//            if (jobFunctions != null && !jobFunctions.isEmpty()) {
//                talent.setJobFunctions(new HashSet<>(Convert.toList(TalentJobFunctionRelation.class, EnumJobFunction.transfer(jobFunctions))));
//            }
//        }
//        //industries
//        if (talent.getIndustries() == null || talent.getIndustries().isEmpty()) {
//            JSONArray industryArray = talentJson.getJSONArray(ElasticSearchConstants.DROP_DOWN_INDUSTRY);
//            if (CollectionUtil.isEmpty(industryArray)) {
//                industryArray = new JSONArray();
//            }
//            List<Long> industries = enumIndustryService.transferIndustriesByNamesToIdWithoutIgnoreParentClass(industryArray);
//            if (industries != null && !industries.isEmpty()) {
//                talent.setIndustries(new HashSet<>(Convert.toList(TalentIndustryRelation.class, EnumIndustry.transfer(industries))));
//            }
//        }
//        //workAuthorization
//        if (talent.getWorkAuthorization() == null || talent.getWorkAuthorization().isEmpty()) {
//            String workAuthorizationGet = talentJson.getStr(ElasticSearchConstants.DROP_DOWN_WORKAUTHORIZATION);
//            JSONArray workAuthorizationArray = new JSONArray();
//            workAuthorizationArray.add(workAuthorizationGet);
//            List<Long> workAuthorization = enumWorkAuthorizationService.transferWorkAuthorizationByNamesToIdWithoutIgnoreParentClass(workAuthorizationArray);
//            if (workAuthorization != null && !workAuthorization.isEmpty()) {
//                talent.setWorkAuthorization(new HashSet<>(Convert.toList(TalentWorkAuthorizationRelation.class, EnumWorkAuthorization.transfer(workAuthorization))));
//            }
//        }
//        talentRepository.save(talent);
//    }

    @Override
    public BigDecimal checkTalentDuplication(TalentV3 talent) {
        BigDecimal similarity = BigDecimal.ZERO;
        TalentV3 talentEntity = talentRepository.findById(talent.getId()).orElse(null);
        if (talentEntity == null) {
            log.error("[apn talent sync to es , talent is null by id ={}]", talent.getId());
            return null;
        }
        JSONObject es = translateEs(talent);
        if (ObjectUtil.isEmpty(es)) {
            return similarity;
        }
        try {
            HttpResponse response = httpService.post(checkTalentDuplicationUrl(SecurityUtils.getTenantId()), JSONUtil.toJsonStr(es));
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                SimilarityResultDto similarityResultDto = JSONUtil.toBean(response.getBody(), SimilarityResultDto.class);
                if (similarityResultDto != null && CollUtil.isNotEmpty(similarityResultDto.getSuspectedDuplications())) {
                    DuplicationCheckResponseSuspectedDuplicationsDto duplicationCheckResponseSuspectedDuplicationsDto = similarityResultDto.getSuspectedDuplications().stream()
                            .filter(ObjectUtil::isNotEmpty).max(Comparator.comparing(DuplicationCheckResponseSuspectedDuplicationsDto::get_similarity)).orElse(null);
                    similarity = duplicationCheckResponseSuspectedDuplicationsDto == null ? BigDecimal.ZERO : duplicationCheckResponseSuspectedDuplicationsDto.get_similarity();
                }
            } else {
                log.error("[APN: EsFillerTalentService] checkTalentDuplication to EsFiller error,request body:{} , response code: {}, response message: {}", es, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } catch (Exception e) {
            log.error("[APN: EsFillerTalentService] checkTalentDuplication Exception, talent = {}, message = {}", JSONUtil.toJsonStr(es), ExceptionUtils.getMessage(e));
        }
        return similarity;
    }

    @Override
    public List<SuspectedDuplications> checkTalentDuplicationWithResult(TalentV3 talent, List<TalentContactDTO> contacts, TalentSimilarityDto talentSimilarityDto, String similarity, Long ignoreTalentId) {
        List<SuspectedDuplications> resultList = new ArrayList<>();
        StopWatch stopWatch = new StopWatch("checkTalentDuplicationWithResult");
        stopWatch.start("[1] checkTalentDuplicationTranslateEs");
        JSONObject es = checkTalentDuplicationTranslateEs(talent, contacts, talentSimilarityDto);
        if (CollUtil.isEmpty(es)) {
            return resultList;
        }
        String url = checkTalentDuplicationUrl(SecurityUtils.getTenantId());
        Map<String, String> params = new HashMap<>();
        if(similarity != null) {
            params.put("minSimilarity", similarity);
        }
        if(ignoreTalentId != null) {
            params.put("ignoreTalentIds", ignoreTalentId.toString());
        }
        try {
            stopWatch.stop();
            stopWatch.start("[2] postEs");
            HttpResponse response = httpService.post(addParamsToUrl(url, params), JSONUtil.toJsonStr(es));
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                CheckTalentDuplicationDTO similarityResultDto = JSONUtil.toBean(response.getBody(), CheckTalentDuplicationDTO.class);
                if (similarityResultDto != null && CollUtil.isNotEmpty(similarityResultDto.getSuspectedDuplications())) {
                    resultList = similarityResultDto.getSuspectedDuplications();
                }
            } else {
                log.error("[APN: EsFillerTalentService] checkTalentDuplication to EsFiller error,request body:{} , response code: {}, response message: {}", es, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } catch (Exception e) {
            log.error("[APN: EsFillerTalentService] checkTalentDuplication Exception, talent = {}, message = {}", JSONUtil.toJsonStr(es), ExceptionUtils.getMessage(e));
        }
        log.info("checkTalentDuplicationWithResult result : {}, param = {}", JSONUtil.toJsonStr(resultList), JSONUtil.toJsonStr(es));
        stopWatch.stop();
        log.info("[apn @{}] checkTalentDuplicationWithResult time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return resultList;
    }

    public static String addParamsToUrl(String url, Map<String, String> params) throws URISyntaxException {
        URI uri = new URI(url);
        StringBuilder newQuery = new StringBuilder(uri.getQuery() == null ? "" : uri.getQuery());

        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (newQuery.length() > 0) {
                newQuery.append("&");
            }
            newQuery.append(entry.getKey()).append("=").append(entry.getValue());
        }

        URI newUri = new URI(uri.getScheme(), uri.getAuthority(), uri.getPath(), newQuery.toString(), uri.getFragment());
        return newUri.toString();
    }

    @Override
    public List<DuplicationCheckResponseSuspectedDuplicationsDto> checkTalentDuplicationWithResult(Long tenantId, List<TalentContactDTO> contacts) {
        List<DuplicationCheckResponseSuspectedDuplicationsDto> resultList = new ArrayList<>();
        JSONObject es = checkTalentDuplicationTranslateEs(contacts);
        if (CollUtil.isEmpty(es)) {
            return resultList;
        }
        try {
            HttpResponse response = httpService.post(checkTalentDuplicationUrl(tenantId), JSONUtil.toJsonStr(es));
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                SimilarityResultDto similarityResultDto = JSONUtil.toBean(response.getBody(), SimilarityResultDto.class);
                if (similarityResultDto != null && CollUtil.isNotEmpty(similarityResultDto.getSuspectedDuplications())) {
                    resultList = similarityResultDto.getSuspectedDuplications();
                }
            } else {
                log.error("[APN: EsFillerTalentService] checkTalentDuplication to EsFiller error,request body:{} , response code: {}, response message: {}", es, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } catch (Exception e) {
            log.error("[APN: EsFillerTalentService] checkTalentDuplication Exception, talent = {}, message = {}", JSONUtil.toJsonStr(es), ExceptionUtils.getMessage(e));
        }
        log.info("checkTalentDuplicationByEsFillerWithResult param = {}, result : {}", JSONUtil.toJsonStr(es), JSONUtil.toJsonStr(resultList));
        return resultList;
    }

    private JSONObject checkTalentDuplicationTranslateEs(TalentV3 talent, List<TalentContactDTO> contacts, TalentSimilarityDto talentSimilarityDto) {
        JSONObject es = translateEs(talent);
//        if (CollUtil.isEmpty(es)) {
//            return new JSONObject();
//        }
        addContactsWithNoMysql(contacts, es);
        if (Objects.nonNull(talentSimilarityDto) && ObjectUtil.isNotEmpty(talentSimilarityDto.getCurrentLocation())) {
            es.put("currentLocation", talentSimilarityDto.getCurrentLocation());
        }
        if (Objects.nonNull(talent) && talent.getId() != null) {
            es.put("ignoreTalentIds", List.of(String.valueOf(talent.getId())));
        }
        return es;
    }

    private JSONObject checkTalentDuplicationTranslateEs(List<TalentContactDTO> contacts) {
        JSONObject es = new JSONObject();
        addContactsWithNoMysql(contacts, es);
        return es;
    }


    private void addContactsWithNoMysql(List<TalentContactDTO> contacts, JSONObject es) {
        if (CollUtil.isNotEmpty(contacts)) {
            List<JSONObject> contactList = contacts.stream().map(s -> {
                JSONObject object = new JSONObject();
                object.put("contact", s.getContact());
                if (ObjectUtil.isNotEmpty(s.getDetails())) {
                    object.put("details", s.getDetails());
                }
                object.put("type", s.getType());
                return object;
            }).collect(Collectors.toList());
            es.put("contacts", contactList);
        }
    }

    @Override
    public void extractBulkTalentToMq(Collection<Long> talentIds, int priority) {
        List<JSONObject> talentProfiles = talentRepository.findAllById(talentIds).stream().map(talent -> buildTalentProfile(talent, 0)).collect(Collectors.toList());
        log.info("[EsFillerTalentService: syncTalentToMQ @{}] talentProfiles length: {}, ids: {}", SecurityUtils.getUserId(), talentProfiles.size(), talentIds);
        for (JSONObject talentProfile : talentProfiles) {
            log.info("[EsFillerTalentService: syncTalentToMQ @{}] sync talentProfile profile: {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(talentProfile));
            if (Objects.nonNull(talentProfile)) {
                Long id = talentProfile.getLong("_id");
                try {
                    rabbitMqService.sendTalentProfile(JSONUtil.toJsonStr(talentProfile), priority);
                    canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.TALENT);
                    log.info("[EsFillerTalentService: syncTalentToMQ @{}] save talent to MQ success, id: {}", SecurityUtils.getUserId(), id);
                } catch (Exception e) {
                    log.error("[EsFillerTalentService: syncTalentToMQ @{}] save talent to MQ error, id: {}", SecurityUtils.getUserId(), id);
                    canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "Send Talent to MQ Error" +
                            "\n\tTalent ID: " + id +
                            "\n\tError: " +
                            "\n\t" + ExceptionUtils.getStackTrace(e);
                    NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
                }
            }
        }
    }

    @Override
    public void extractTalentToMq(Collection<Long> talentIds, int priority) {
        List<JSONObject> talentProfiles = talentRepository.findAllById(talentIds).stream().map(talent -> buildTalentProfile(talent, 0)).collect(Collectors.toList());
        log.info("[EsFillerTalentService: syncTalentToMQ @{}] talentProfiles length: {}, ids: {}", SecurityUtils.getUserId(), talentProfiles.size(), talentIds);
        for (JSONObject talentProfile : talentProfiles) {
            log.info("[EsFillerTalentService: syncTalentToMQ @{}] sync talentProfile profile: {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(talentProfile));
            if (Objects.nonNull(talentProfile)) {
                Long id = talentProfile.getLong("_id");
                try {
                    rabbitMqService.sendTalentProfile(JSONUtil.toJsonStr(talentProfile), priority);
                    canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.TALENT);
                    invokeRaterByTalent(talentProfile);
                    log.info("[EsFillerTalentService: syncTalentToMQ @{}] save talent to MQ success, id: {}", SecurityUtils.getUserId(), id);
                } catch (Exception e) {
                    log.error("[EsFillerTalentService: syncTalentToMQ @{}] save talent to MQ error, id: {}", SecurityUtils.getUserId(), id);
                    canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "Send Talent to MQ Error" +
                            "\n\tTalent ID: " + id +
                            "\n\tError: " +
                            "\n\t" + ExceptionUtils.getStackTrace(e);
                    NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
                }
            }
        }
    }

    private void invokeRaterByTalent(JSONObject talentProfile){
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            try {
                SecurityContextHolder.setContext(context);
                if (talentProfile.containsKey("_source")) {
                    JSONObject source = talentProfile.getJSONObject("_source");
                    if (source.containsKey("lastModifiedDate")) {
                        Long talentId = talentProfile.getLong("_id");
                        Long tenantId = talentProfile.getLong("_tenant_id");
                        raterService.invokeRecommendJobsForTenantTalent(talentId, tenantId);
                        log.info("[EsFillerTalentService: syncTalentToMQ @{}] : invoke rater {}", SecurityUtils.getUserId(), talentId);
                    }
                }
            } catch (ExternalServiceInterfaceException e) {
                if (Objects.equals(e.getCode(), HttpStatus.NOT_ACCEPTABLE.value()) || Objects.equals(e.getCode(), HttpStatus.NOT_FOUND.value())) {
                    log.error("[EsFillerTalentService: invokeRaterByTalent @{}] External service error for talentProfile: {}, code: {}",
                            SecurityUtils.getUserId(), talentProfile, e.getCode());
                } else {
                    log.info("[EsFillerTalentService: invokeRaterByTalent @{}] Unexpected external service error for talentProfile: {}, error stack: {}",
                            SecurityUtils.getUserId(), talentProfile, ExceptionUtils.getStackTrace(e));
                    NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(),
                            "invoke rater error , profile: " + talentProfile + "\nError stack: " + ExceptionUtils.getStackTrace(e));
                }
            } catch (Exception e) {
                log.info("[EsFillerTalentService: invokeRaterByTalent @{}] General error during rater invocation for talentProfile: {}, error stack: {}",
                        SecurityUtils.getUserId(), talentProfile, ExceptionUtils.getStackTrace(e));
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(),
                        "invoke rater error , profile: " + talentProfile + "\nError stack: " + ExceptionUtils.getStackTrace(e));
            }
        });
    }

    @Override
    public void buildJsonToJobdivaMq(Collection<Long> talentIds, int priority) {
        talentRepository.getTalentDataSyncToHr(talentIds.stream().toList()).forEach(vo -> {
            log.info("[buildJsonToJobdivaMq: syncTalentToJobdivaMQ @{}] save talent {} to rabbitMQ", SecurityUtils.getUserId(), vo.getId());
            try {
                JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
                JSONObject jsonObject = new JSONObject(jsonConfig);
                jsonObject.put("type", JobdivaDataSyncTypeEnum.TALENT);
                jsonObject.put("entity", buildTalentVO(vo));
                rabbitTemplate.convertAndSend(jobdivaRabbitProperties.getApnToJobdivaExchange(), jobdivaRabbitProperties.getApnToJobdivaRoutingKey(), JSONUtil.toJsonStr(jsonObject), message -> {
                    message.getMessageProperties().setPriority(priority);
                    return message;
                });
                log.info("[buildJsonToJobdivaMq: syncTalentToJobdivaMQ @{}] save talent to jobdiva MQ success, id: {}", SecurityUtils.getUserId(), vo.getId());
            }catch (Exception e) {
                log.error("[buildJsonToJobdivaMq: syncTalentToJobdivaMQ @{}] send talent to rabbitMQ error: {}", SecurityUtils.getUserId(), e.getMessage());
            }
        });
    }

    private JSONObject buildTalentVO(TalentToHrVO vo) {
        JSONObject talentVO = new JSONObject();
        talentVO.put("id", vo.getId());
        talentVO.put("fullName", vo.getFullName());
        talentVO.put("tenantId", vo.getTenantId());
        talentVO.put("photoUrl", vo.getPhotoUrl());
        talentVO.put("email", vo.getEmail());
        return talentVO;
    }

    private JSONObject buildTalentProfile(TalentV3 talent, int deep) {
        JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        cn.hutool.json.JSONObject request = new cn.hutool.json.JSONObject(jsonConfig);
        try {
            if (deep > 0) {
                Thread.sleep(2000);
            }
            cn.hutool.json.JSONObject es = translateEs(talent);
            request.put("_id", talent.getId());
            //request.put("_index", "talents_" + talent.getTenantId());
            request.put("_tenant_id", String.valueOf(talent.getTenantId()));
            request.put("_type", "talent");
            request.put("_return_routing_key", esfillerMQProperties.getApnNormalizedTalentRoutingKey());
            request.put("_source", es);
        } catch (Exception e) {
            log.error("[EsFillerTalentService: syncTalentToMQ @{}] buildTalentProfile error, id: {}, error: {}", SecurityUtils.getUserId(), talent.getId(), ExceptionUtils.getStackTrace(e));
            if (deep < esfillerMQProperties.getRetryThreshold()) {
                request = buildTalentProfile(talent, deep + 1);
            } else {
                commonRedisService.saveFailedTalentIds(Arrays.asList(talent.getId()));
                request = null;
                String message = "Build Talent Profile Error" +
                        "\n\tTalent ID: " + talent.getId() +
                        "\n\tTalent Name: " + talent.getFullName() +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
        return request;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveNormalizedTalentInfos(String responseBody) {
        cn.hutool.json.JSONObject responseJson = JSONUtil.parseObj(responseBody);
        Long talentId = responseJson.getLong("_id");
        TalentV3 talent = talentRepository.findById(talentId).orElse(new TalentV3());
        if (ObjectUtil.isEmpty(talent.getId())) {
            return;
        }
        cn.hutool.json.JSONObject talentJson = responseJson.getJSONObject("_source");
        //name
        String firstName = talentJson.getStr("firstName") != null ? talentJson.getStr("firstName") : talent.getFirstName();
        String lastName = talentJson.getStr("lastName") != null ? talentJson.getStr("lastName") : talent.getLastName();
        String fullName = talentJson.getStr("fullName") != null ? talentJson.getStr("fullName") : talent.getFullName();
        talentRepository.updateTalentNameInfo(talentId, firstName, lastName, fullName);
        //current location
        if (ObjectUtil.isNotEmpty(talentJson.getJSONObject(ElasticSearchConstants.ESFILLER_KEY_CURRENT_LOCATIONS))) {
            LocationESDTO dto = talentJson.getJSONObject(ElasticSearchConstants.ESFILLER_KEY_CURRENT_LOCATIONS).toBean(LocationESDTO.class);
            TalentCurrentLocation currentLocation = talentLocationRepository.findByTalentId(talentId);
            if (currentLocation == null) {
                return;
            }
            if (ObjectUtil.isNotEmpty(dto.getOfficialCity()) || ObjectUtil.isNotEmpty(dto.getOfficialCountry())
                    || ObjectUtil.isNotEmpty(dto.getOfficialProvince()) || ObjectUtil.isNotEmpty(dto.getOfficialCounty())) {
                String officialCity = dto.getCity();
                String officialCountry = dto.getOfficialCountry();
                String officialProvince = dto.getOfficialProvince();
                String officialCounty = dto.getOfficialCounty();
                String zipCode = StringUtils.isEmpty(currentLocation.getZipCode()) && ObjectUtil.isNotEmpty(dto.getZipcode()) ? dto.getZipcode() : currentLocation.getZipCode();
                talentLocationRepository.updateOfficialInfoById(talentId, officialCity, officialCountry, officialProvince, officialCounty, zipCode);
            }
        }
        //additional info
        JSONArray educations = getEducationDTO(talentJson);
        JSONArray experiences = getExperienceDTO(talentJson);
        TalentAdditionalInfo talentAdditionalInfo = talent.getTalentAdditionalInfo();
        if (talentAdditionalInfo != null) {
            String extendedInfo = talentAdditionalInfo.getExtendedInfo();
            JSONObject extendedInfoObject = JSONUtil.parseObj(extendedInfo);
            if (StringUtils.isNotEmpty(talentJson.getStr(ESFILLER_KEY_NICK_NAME))) {
                extendedInfoObject.put(ESFILLER_KEY_NICK_NAME, talentJson.getStr(ESFILLER_KEY_NICK_NAME));
            }
            setEducationAndExperienceInfo(extendedInfoObject, educations, experiences);
            talentAdditionalInfoRepository.updateExtendInfoById(talentAdditionalInfo.getId(), extendedInfoObject.toString());
        }
        talentRepository.updateTalentLastSyncTime(talent.getId(), Instant.now());
    }

    private JSONArray getEducationDTO(JSONObject talentJson) {
        JSONArray ret = new JSONArray();

        // 处理顶级教育信息
        JSONObject topEducation = talentJson.getJSONObject("topEducation");
        if (topEducation != null) {
            ret.add(topEducation);
        }

        // 处理非顶级教育信息
        JSONArray nonTopEducations = talentJson.getJSONArray("nonTopEducations");
        if (nonTopEducations != null && !nonTopEducations.isEmpty()) {
            for (int i = 0; i < nonTopEducations.size(); i++) {
                ret.add(nonTopEducations.getJSONObject(i));
            }
        }

        return ret;
    }

    private JSONArray getExperienceDTO(JSONObject talentJson) {
        JSONArray ret = new JSONArray();
        JSONArray currentExperiences = talentJson.getJSONArray("currentExperiences");
        if (CollUtil.isNotEmpty(currentExperiences)) {
            ret.addAll(currentExperiences);
        }
        JSONArray pastExperiences = talentJson.getJSONArray("pastExperiences");
        if (CollUtil.isNotEmpty(pastExperiences)) {
            ret.addAll(pastExperiences);
        }
        return ret;
    }

    private void setEducationAndExperienceInfo(JSONObject extendedInfoObject, JSONArray educationDTOList, JSONArray experiencesDTOList) {
        JSONArray educations = extendedInfoObject.getJSONArray("educations");
        if (educations != null) {
            for (int i = 0; i < educations.size(); i++) {
                JSONObject education = educations.getJSONObject(i);
                JSONObject educationDTO = getEducationInfo(education, educationDTOList);
                if (educationDTO != null) {
                    JSONObject collegeInfo = educationDTO.getJSONObject("collegeInfo");
                    if (collegeInfo != null) {
                        education.put("collegeInfo", collegeInfo);
                    } else {
                        education.remove("collegeInfo");
                    }
                } else {
                    education.remove("collegeInfo");
                }
            }
        }


        //禁猎客户标签：这些id更新时必须当做一个整体一起更新。每次移除这些key并设置
        JSONArray experiences = extendedInfoObject.getJSONArray("experiences");
        if (experiences != null) {
            for (int i = 0; i < experiences.size(); i++) {
                JSONObject experience = experiences.getJSONObject(i);
                JSONObject experiencesDTO = getExperienceInfo(experience, experiencesDTOList);
                if (experiencesDTO != null) {
                    experience.remove("businessInfoCompanyId");
                    experience.remove("bdCompanyId");
                    experience.remove("recogLeadsCompanyId");
                    experience.remove("recogCRMAccountId");
                    experience.remove("recogCompanyId");

                    if (ObjectUtil.isNotNull(experiencesDTO.getStr("businessInfoCompanyId"))) {
                        experience.put("businessInfoCompanyId", experiencesDTO.getStr("businessInfoCompanyId"));
                    }
                    if (ObjectUtil.isNotNull(experiencesDTO.getStr("bdCompanyId"))) {
                        experience.put("bdCompanyId", experiencesDTO.getStr("bdCompanyId"));
                    }
                    if (ObjectUtil.isNotNull(experiencesDTO.getStr("recogLeadsCompanyId"))) {
                        experience.put("recogLeadsCompanyId", experiencesDTO.getStr("recogLeadsCompanyId"));
                    }
                    if (ObjectUtil.isNotNull(experiencesDTO.getStr("recogCRMAccountId"))) {
                        experience.put("recogCRMAccountId", experiencesDTO.getStr("recogCRMAccountId"));
                    }
                    if (ObjectUtil.isNotNull(experiencesDTO.getStr("recogCompanyId"))) {
                        experience.put("recogCompanyId", experiencesDTO.getStr("recogCompanyId"));
                    }
                }
            }
        }
    }

    private JSONObject getEducationInfo(JSONObject education, JSONArray educationDTOList) {
        String collegeName = education.getStr("collegeName");
        if (educationDTOList == null || educationDTOList.isEmpty()) {
            return null;
        }
        for (int i = 0; i < educationDTOList.size(); i++) {
            JSONObject educationLevel = educationDTOList.getJSONObject(i);
            if (ObjectUtil.equal(collegeName, educationLevel.getStr("collegeName"))) {
                return educationLevel;
            }
        }
        return null;
    }

    private JSONObject getExperienceInfo(JSONObject experience, JSONArray experiencesDTOList) {
        //判断条件暂且依照id匹配
        Long experienceId = experience.getLong("id");
        if (experiencesDTOList.isEmpty()) {
            return null;
        }

        if (experiencesDTOList != null && !experiencesDTOList.isEmpty()) {
            for (int i = 0; i < experiencesDTOList.size(); i++) {
                if (ObjectUtil.equal(experienceId, experiencesDTOList.getJSONObject(i).getLong("id"))) {
                    return experiencesDTOList.getJSONObject(i);
                }
            }
        }
        return null;
    }


    @Override
    public void updateTalentFolder(List<String> talentIds, List<String> toFolderIds, List<String> fromFolderIds, Long tenantId) {
        if (CollUtil.isEmpty(talentIds) || (toFolderIds == null && fromFolderIds == null)) {
            return;
        }

        JSONObject es = new JSONObject();
        es.put("ids", talentIds.stream().distinct().collect(Collectors.toList()));
        if (toFolderIds != null && CollUtil.isNotEmpty(toFolderIds)) {
            es.put("to", toFolderIds.stream().distinct().collect(Collectors.toList()));
        }
        if (fromFolderIds != null && CollUtil.isNotEmpty(fromFolderIds)) {
            es.put("from", fromFolderIds.stream().distinct().collect(Collectors.toList()));
        }
        if (ObjectUtil.isEmpty(es)) {
            return;
        }
        try {
            HttpResponse response = httpService.post(updateTalentFolderUrl(tenantId), JSONUtil.toJsonStr(es));
            if (response == null || !ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[APN: EsFillerTalentService] updateTalentFolder to EsFiller error,request body:{} , response code: {}, response message: {}", es, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                String message = "Update talent folder to MQ Error" +
                        "\n\tTalent IDs: " + talentIds +
                        "\n\tError: " +
                        "\n\t" + response;
                //NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        } catch (Exception e) {
            //redisService.saveFailedTalentIds(talentIds);
            log.error("[APN: EsFillerTalentService] updateTalentFolder Exception, talent = {}, message = {}", JSONUtil.toJsonStr(es), ExceptionUtils.getMessage(e));
            String message = "Update talent folder to MQ Error" +
                    "\n\tTalent IDs: " + talentIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            //NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
    }

    private String talentToFoldersOfPreSubmitTalentsUrl(Long tenantId) {
        return applicationProperties.getEsFillerSyncUrl() + tenantId + "/talents_pre_submit_folder_update";
    }

    @Override
    public void updateTalentToFoldersOfPreSubmitTalents(Long tenantId, String requestBody) throws Exception {
        String url = talentToFoldersOfPreSubmitTalentsUrl(tenantId);
        log.info("[APN: EsFillerTalentService] updateTalentToFoldersOfPreSubmitTalents url: {}, request body:{}", url, requestBody);
        HttpResponse response = httpService.put(url, requestBody);
        if (response == null || !ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
            log.error("[APN: EsFillerTalentService] updateTalentToFoldersOfPreSubmitTalents to EsFiller error,request body:{} , response code: {}, response message: {}", requestBody, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            throw new CustomParameterizedException("Invoke esFiller error!");
        }
    }

    @Override
    public void deleteTalentToFoldersOfPreSubmitTalents(Long tenantId, String requestBody) throws Exception {
        String url = talentToFoldersOfPreSubmitTalentsUrl(tenantId);
        log.info("[APN: EsFillerTalentService] deleteTalentToFoldersOfPreSubmitTalents url: {}, request body:{}", url, requestBody);
        HttpResponse response = httpService.patch(url, requestBody);
        if (response == null || !ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
            log.error("[APN: EsFillerTalentService] deleteTalentToFoldersOfPreSubmitTalents to EsFiller error,request body:{} , response code: {}, response message: {}", requestBody, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            throw new CustomParameterizedException("Invoke esFiller error!");
        }
    }

    @Override
    public void updateTalentRelateJobFolder(Long tenantId, Long jobId, String folderId, String requestBody) throws IOException {
        String url = foldersOfPreSubmitTalentsUrl(tenantId, jobId, folderId);
        log.info("[APN: EsFillerTalentService] updateTalentRelateJobFolder url: {}, request body:{}", url, requestBody);
        HttpResponse response = httpService.put(url, requestBody);
        if (response == null || !ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
            log.error("[APN: EsFillerTalentService] updateTalentRelateJobFolder to EsFiller error,request body:{} , response code: {}, response message: {}", requestBody, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            throw new CustomParameterizedException("Invoke esFiller error!");
        }
    }

    @Override
    public void mergeTalentRelateJobFolder(Long tenantId, Long jobId, String folderId, String requestBody) throws Exception {
        String url = mergeFoldersOfPreSubmitTalentsUrl(tenantId, jobId, folderId);
        log.info("[APN: EsFillerTalentService] mergeTalentRelateJobFolder url: {}, request body:{}", url, requestBody);
        HttpResponse response = httpService.patch(url, requestBody);
        if (response == null || !ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
            log.error("[APN: EsFillerTalentService] mergeTalentRelateJobFolder to EsFiller error,request body:{} , response code: {}, response message: {}", requestBody, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            throw new CustomParameterizedException("Invoke esFiller error!");
        }
    }

    private String foldersOfPreSubmitTalentsUrl(Long tenantId, Long jobId, String folderId) {
        return applicationProperties.getEsFillerSyncUrl() + tenantId + "/job/" + jobId + "/folder_of_pre_submit_talents/" + folderId + "/";
    }


    private String mergeFoldersOfPreSubmitTalentsUrl(Long tenantId, Long jobId, String folderId) {
        return applicationProperties.getEsFillerSyncUrl() + tenantId + "/job/" + jobId + "/folder_of_pre_submit_talents/" + folderId + "/annexed_by";
    }

    @Override
    public void deleteTalentRelateJobFolder(Long tenantId, Long jobId, String folderId) throws Exception {
        String url = foldersOfPreSubmitTalentsUrl(tenantId, jobId, folderId);
        log.info("[APN: EsFillerTalentService] deleteTalentRelateJobFolder url: {}", url);
        HttpResponse response = httpService.delete(url);
        if (response == null || !ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
            log.error("[APN: EsFillerTalentService] deleteTalentRelateJobFolder to EsFiller error , response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            throw new CustomParameterizedException("Invoke esFiller error!");
        }
    }
}
