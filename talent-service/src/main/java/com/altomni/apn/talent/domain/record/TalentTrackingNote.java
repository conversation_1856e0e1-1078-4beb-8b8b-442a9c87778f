package com.altomni.apn.talent.domain.record;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatform;
import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatformConverter;
import com.altomni.apn.talent.service.dto.record.TalentTrackingNoteDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A TalentTrackingNote.
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "talent_tracking_note")
public class TalentTrackingNote extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "user_id")
    private Long userId;

    @NotNull
    @Convert(converter = TrackingPlatformConverter.class)
    @Column(name = "tracking_platform", nullable = false)
    private TrackingPlatform trackingPlatform;

    @NotNull
    @Column(name = "platform_id", nullable = false)
    private String platformId;

    @Column(name = "note")
    private String note;

    @Column(name = "synced_talent_id")
    private Long syncedTalentId;

    public static TalentTrackingNote fromTalentTrackingNoteDTO(TalentTrackingNoteDTO talentTrackingNoteDTO) {
        TalentTrackingNote talentTrackingNote = new TalentTrackingNote();
        ServiceUtils.myCopyProperties(talentTrackingNoteDTO, talentTrackingNote);
        return talentTrackingNote;
    }
}
