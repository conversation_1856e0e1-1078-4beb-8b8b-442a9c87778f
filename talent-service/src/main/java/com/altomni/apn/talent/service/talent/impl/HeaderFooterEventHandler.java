package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.talent.constants.ReportPDFFormat;
import com.altomni.apn.talent.constants.ReportTemplateConstants;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.Optional;

@Slf4j
public class HeaderFooterEventHandler extends PdfPageEventHelper {
    private ReportPDFFormat reportPDFFormat;
    private User user;

    HeaderFooterEventHandler(ReportPDFFormat reportPDFFormat, User user) {
        this.reportPDFFormat = reportPDFFormat;
        this.user = user;
    }

    @Override
    public void onStartPage(PdfWriter writer, Document document) {
        try {
            setPageHeader(writer, document);
            setPageFooter(writer, document);
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void setPageFooter(PdfWriter writer, Document document) {
        Font greyFont = FontFactory.getFont(ReportTemplateConstants.PINGFANG_FONT, BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 5, -1, new BaseColor(148,148,148));
        Font pageNumFont = FontFactory.getFont(ReportTemplateConstants.PINGFANG_FONT, BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 5, -1, BaseColor.BLACK);

        try {
            // Left side: three paragraphs
            Paragraph paragraph1 = new Paragraph(reportPDFFormat.footer(), greyFont);
            Paragraph pageNumber = new Paragraph(String.valueOf(document.getPageNumber()), pageNumFont);

            // Add the paragraph to the page
            PdfContentByte canvas = writer.getDirectContent();
            ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, new Phrase(paragraph1), document.leftMargin(), document.bottom() - 20, 0);
            ColumnText.showTextAligned(canvas, Element.ALIGN_RIGHT, new Phrase(pageNumber), document.right(), document.bottom() - 20, 0);
        } catch (Exception e) {
            log.error("Export recommendation report PDF drawing exception.");
            throw e;
        }
    }

    private void setPageHeader(PdfWriter writer, Document document) throws DocumentException, IOException {
        Font normalFont = FontFactory.getFont(ReportTemplateConstants.PINGFANG_FONT, BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);
        Font boldFont = new Font(normalFont.getBaseFont(), 24, Font.BOLD, BaseColor.GRAY);

        try {
            PdfContentByte canvas = writer.getDirectContent();
            // Calculate header text position
            float headerX = document.leftMargin();
            float headerY = document.top() + 20;

            Phrase headerText = new Phrase(reportPDFFormat.headerTitle(), boldFont);
            ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, headerText, headerX, headerY + 60, 0);

            String userName = StrUtil.format(reportPDFFormat.headerUserName(), CommonUtils.formatFullName(user.getFirstName(), user.getLastName()));
            String userPhone = StrUtil.format(reportPDFFormat.headerUserPhone(), Optional.ofNullable(user.getPhone()).orElse(""));
            ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, new Phrase(userName, normalFont), headerX, headerY + 30, 0);
            ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, new Phrase(userPhone, normalFont), headerX+200, headerY + 30, 0);

            String userEmail = StrUtil.format(reportPDFFormat.headerUserEmail(), Optional.ofNullable(user.getEmail()).orElse(""));
            String createDate = StrUtil.format(reportPDFFormat.headerCreateDate(), DateUtil.today());
            ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, new Phrase(userEmail, normalFont), headerX, headerY + 20, 0);
            ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, new Phrase(createDate, normalFont), headerX+200, headerY + 20, 0);


            // Show the image in the header
            ClassPathResource resource = new ClassPathResource(ReportTemplateConstants.LOGO);
            Image image = Image.getInstance(resource.getInputStream().readAllBytes());
            image.scaleToFit(100, 100);
            float imageX = document.right() - image.getScaledWidth();
            float imageY = headerY + 40; // Adjust the y-coordinate for image position
            image.setAbsolutePosition(imageX, imageY);
            canvas.addImage(image);

            // Draw a horizontal line below the header
            canvas.setLineWidth(3f);
            canvas.moveTo(document.leftMargin(), headerY+10);
            canvas.lineTo(document.right(), headerY+10);
            canvas.stroke();
        } catch (Exception e) {
            log.error("Export recommendation report PDF drawing exception.");
        }
    }
}
