package com.altomni.apn.talent.domain.enumeration;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class UserAccountTypeConverter extends AbstractAttributeConverter<UserAccountType, Integer> {
    public UserAccountTypeConverter() {
        super(UserAccountType::toDbValue, UserAccountType::fromDbValue);
    }
}
