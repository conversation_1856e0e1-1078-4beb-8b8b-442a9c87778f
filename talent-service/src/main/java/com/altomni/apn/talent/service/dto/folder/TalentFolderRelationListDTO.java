package com.altomni.apn.talent.service.dto.folder;


import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@ApiModel(value = "Jobs and Folders: many to many ")
public class TalentFolderRelationListDTO {

    List<Long> talentIds;
    List<Long> folderIds;

    public boolean isTalentsValid() {
        return !isListDuplicate(this.talentIds);
    }

    public boolean isFoldersValid(){
        return !isListDuplicate(this.folderIds);
    }


    private boolean isListDuplicate(List<Long> list) {
        if (list == null || list.isEmpty()) {
            return false;
        }

        Set<Long> uniqueIds = new HashSet<>(list);
        return uniqueIds.size() != list.size();
    }
}
