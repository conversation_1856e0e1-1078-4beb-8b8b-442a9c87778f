package com.altomni.apn.talent.service.dto.linkedinproject;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LinkedinProjectStatsDTO {
    private Long linkedinProjectId;
    private Long totalCandidatesCount;
    private Long candidatesWithContactInfoCount;
    private Long candidatesInApplicationCount;

    public LinkedinProjectStatsDTO(Long linkedinProjectId, Long totalCandidatesCount, Integer candidatesWithContactInfoCount, Integer candidatesInApplicationCount) {
        this.linkedinProjectId = linkedinProjectId;
        this.totalCandidatesCount = totalCandidatesCount;
        this.candidatesWithContactInfoCount = Long.valueOf(candidatesWithContactInfoCount);
        this.candidatesInApplicationCount = Long.valueOf(candidatesInApplicationCount);
    }
}
