package com.altomni.apn.talent.service.talent;


import com.altomni.apn.talent.domain.linkedin.TalentTrackingLinkedinPendingGroup;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingGroupVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingVO;
import com.altomni.apn.talent.web.rest.talent.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * Service Interface for managing LinkedIn account information.
 */
public interface LinkedinAccountInfoService {

    /**
     * 获取领英账号信息
     *
     * @param linkedinId 领英ID
     * @return 领英账号信息响应
     */
    LinkedinAccountInfoResponse getLinkedinAccountInfo(String linkedinId);

    /**
     * 保存领英账号信息
     *
     * @param request 领英账号信息请求
     */
    void saveLinkedinAccountInfo(LinkedinAccountInfoRequest request);

    /**
     * 新增事件追踪记录
     *
     * @param request 事件追踪请求
     */
    void addEventTracking(LinkedinEventTrackingRequest request);

    TalentTrackingGroupVO linkedinAssistantService(@Valid LinkedinPendingGroupRequest request);

    TalentTrackingGroupVO updateGroup(Long id, @NotBlank(message = "名称不能为空") String name);

    void inactiveGroup(Long id);

    void saveGroupMember(@Valid SavePendingGroupMemberRequestDTO requestDTO);

    Page<TalentTrackingLinkedinPendingGroup> searchGroup(String operatorLinkedinId, Pageable pageable);

    List<TalentTrackingGroupVO> toTalentTrackingGroupVOList(List<TalentTrackingLinkedinPendingGroup> content);

    List<TalentTrackingVO> queryGroupMember(Long id);

    List<TalentTrackingGroupVO> queryGroupByOperatorLinkedinId(String operatorLinkedinId);

    void addFriendNotify(AddFriendNotifyRequestDTO requestDTO);

    GetWaitAddToGroupResponseDTO getWaitAddToGroup(GetWaitAddToGroupRequestDTO requestDTO);

    void notifyAddToFriendGroup(NotifyAddToFriendGroupRequestDTO requestDTO);
}