package com.altomni.apn.talent.repository.linkedin;

import com.altomni.apn.talent.domain.linkedin.TalentTrackingLinkedinPendingGroupMember;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the TalentTrackingLinkedinPendingGroupMember entity.
 */
@Repository
public interface TalentTrackingLinkedinPendingGroupMemberRepository extends JpaRepository<TalentTrackingLinkedinPendingGroupMember, Long> {


    List<TalentTrackingLinkedinPendingGroupMember> findByPendingIdIn(List<Long> pendingIds);

    int countDistinctByGroupId(Long id);

    List<TalentTrackingLinkedinPendingGroupMember> findAllByGroupIdInAndPendingIdIn(List<Long> groupIds, List<Long> membersIds);

    List<TalentTrackingLinkedinPendingGroupMember> findAllByGroupIdIn(List<Long> groupIds);

    List<TalentTrackingLinkedinPendingGroupMember> findAllByGroupId(Long id);
}