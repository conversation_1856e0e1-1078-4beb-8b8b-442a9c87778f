package com.altomni.apn.talent.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.EsFillerConstants;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.dto.search.ConditionParam;
import com.altomni.apn.common.dto.search.Relation;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.common.dto.search.SearchParam;
import lombok.experimental.UtilityClass;

import java.util.*;
import java.util.stream.Collectors;

@UtilityClass
public class DataServiceConvertUtil {

    public void convertCurrency(SearchGroup searchGroup, List<EnumCurrency> enumCurrencyList) {
        if (CollUtil.isEmpty(enumCurrencyList) || ObjectUtil.isEmpty(searchGroup)) {
            return;
        }
        List<SearchParam> searchParamList = searchGroup.getSearch();
        List<SearchParam> filterParamList = searchGroup.getFilter().getQueryFilter();
        Map<Integer, String> map = enumCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, EnumCurrency::getName));
        doCheckCurrencyAndConvert(searchParamList, map);
        doCheckCurrencyAndConvert(filterParamList, map);
    }

    /**
     * condition eg:
     * {
     *     "key": "preferredSalary",
     *     "value": {
     *             "data": {
     *                     "gte": 250000,
     *                     "lte": 300000
     *             },
     *             "currency": 1,
     *             "timeUnit": "YEAR"
     *     }
     * }
     * 1=>USD
     */
    private void doCheckCurrencyAndConvert(List<SearchParam> searchParamList, Map<Integer, String> map) {
        final String attributeName = "currency";
        if (CollUtil.isNotEmpty(searchParamList)) {
            searchParamList.forEach(param -> {
                List<ConditionParam> conditionList = param.getCondition().stream().filter(ObjectUtil::isNotNull).filter(condition -> JSONUtil.toJsonStr(condition).contains("\"" + attributeName + "\"")).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(conditionList)) {
                    conditionList.forEach(condition -> {
                        Object value = condition.getValue();
                        JSONObject jsonObject = JSONUtil.parseObj(value);
                        Object currency = jsonObject.get(attributeName);
                        if (currency instanceof Integer && ObjectUtil.isNotNull(map.get(currency))) {
                            jsonObject.put(attributeName, map.get(currency));
                        }
                        condition.setValue(jsonObject);
                    });
                }
            });
        }
    }


    public void convertEsAndFilter(List<SearchParam> filter, SearchGroup searchGroup, List<EnumUserResponsibility> userResponsibilityList) {
        if (CollUtil.isEmpty(filter)) {
            return;
        }
        Optional.of(searchGroup.getFilter()).ifPresent(filterList -> {
            List<ConditionParam> list = new ArrayList<>();
            filter.forEach(param -> {
                ConditionParam conditionParam = param.getCondition().stream().filter(e -> Objects.equals("ANY", e.getKey())).findAny().orElse(null);
                if (ObjectUtil.isNotNull(conditionParam)) {
                    Object value = conditionParam.getValue();
                    param.getCondition().remove(conditionParam);
                    JSONObject jsonObject = JSONUtil.parseObj(value);
                    Object data = jsonObject.get("data");
                    if (data instanceof String) {
                        String dataStr = (String) data;
                        jsonObject.put("data", CollUtil.newArrayList(Long.parseLong(dataStr)));
                    } else if (data instanceof Integer) {
                        Integer dataStr = (Integer) data;
                        jsonObject.put("data", CollUtil.newArrayList(dataStr.longValue()));
                    }
                    if (CollUtil.isNotEmpty(userResponsibilityList)) {
                        userResponsibilityList.forEach(en -> list.add(new ConditionParam(EsFillerConstants.RESPONSIBILITY + en.getId() + ".userId", jsonObject, null)));
                    }
                }
            });
            if (CollUtil.isNotEmpty(list)) {
                SearchParam searchParam = new SearchParam();
                searchParam.setRelation(Relation.OR);
                searchParam.setCondition(list);
                filter.add(searchParam);
            }
            searchGroup.getFilter().setQueryFilter(filter.stream().filter(s -> CollUtil.isNotEmpty(s.getCondition())).collect(Collectors.toList()));
        });
    }

    public void convertEsFilterEnum(SearchGroup searchGroup, List<EnumUserResponsibility> userResponsibilityList) {
        List<SearchParam> searchParamList = searchGroup.getSearch();
        if (CollUtil.isEmpty(searchParamList)) {
            return;
        }
        if (CollUtil.isNotEmpty(userResponsibilityList)) {
            searchParamList.forEach(param -> {
                List<ConditionParam> conditionParamList = param.getCondition();
                if (CollUtil.isNotEmpty(conditionParamList)) {
                    conditionParamList.forEach(condition ->
                            userResponsibilityList.stream().filter(e -> Objects.equals(e.getLabel(), condition.getKey())).findFirst()
                                    .ifPresent(e -> condition.setKey(EsFillerConstants.RESPONSIBILITY + e.getId() + ".userId")));
                }
            });
        }
    }

    public String convertUserResponsibility(String property, List<EnumUserResponsibility> enumUserResponsibilityList) {
        if (CollUtil.isNotEmpty(enumUserResponsibilityList)) {
            Optional<EnumUserResponsibility> optional = enumUserResponsibilityList.stream()
                    .filter(enumUserResponsibility -> Objects.equals(property, enumUserResponsibility.getLabel())).findFirst();
            if (optional.isPresent()) {
                return EsFillerConstants.RESPONSIBILITY + optional.get().getId();
            }
        }
        return property;
    }

}
