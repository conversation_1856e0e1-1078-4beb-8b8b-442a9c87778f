package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.enumeration.CommonDataStatus;
import com.altomni.apn.common.domain.talent.TalentResumeRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TalentResumeRelationRepository extends JpaRepository<TalentResumeRelation, Long> {
    @Query(value = "select * from talent_resume_relation t where t.resume_id = ?1 and t.tenant_id = ?2 and t.talent_id = ?3 order by created_date desc limit 1", nativeQuery = true)
    TalentResumeRelation findByResumeIdAndTenantIdAndTalentId(Long resumeId, Long tenantId, Long talentId);

    @Query(value = "select * from talent_resume_relation t where t.resume_id = ?1 and t.tenant_id = ?2 and t.status = 0 order by created_date desc limit 1", nativeQuery = true)
    TalentResumeRelation findByResumeIdAndTenantId(Long resumeId, Long tenantId);

    List<TalentResumeRelation> findAllByResumeIdIsAndTenantIdIsAndStatusIs(Long resumeId, Long tenantId, CommonDataStatus status);

    @Query(value = "select t from TalentResumeRelation t where t.talentId = ?1 and t.status = com.altomni.apn.common.domain.enumeration.CommonDataStatus.AVAILABLE order by t.createdDate desc")
    List<TalentResumeRelation> findAllByTalentId(Long talentId);

    List<TalentResumeRelation> findAllByTalentIdInAndStatusOrderByCreatedDateDesc(List<Long> talentIds, CommonDataStatus status);

    List<TalentResumeRelation> findAllByTalentIdAndResumeIdIn(Long talentId, List<Long> resumeIds);

    List<TalentResumeRelation> findAllByTalentIdAndTenantId(Long talentId, Long tenantId);

    List<TalentResumeRelation> findAllByResumeIdInAndStatusIsAndTenantIdIs(List<Long> resumeIds, CommonDataStatus status, Long tenantId);

}
