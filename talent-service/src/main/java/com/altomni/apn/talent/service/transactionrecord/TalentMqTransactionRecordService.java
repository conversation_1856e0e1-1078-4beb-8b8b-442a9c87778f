package com.altomni.apn.talent.service.transactionrecord;

import com.altomni.apn.common.domain.transactionrecord.CommonMqTransactionRecord;

import java.math.BigInteger;

public interface TalentMqTransactionRecordService {

    String searchMqTxSendFail();

    void updateStatusById(Long id, Integer sendStatus);

    void sendExceptionByLark(String message);

    void updateStatusByBusIdAndBusType(Long busId, Integer busType, Integer sendStatus);

    CommonMqTransactionRecord findById(BigInteger id);
}