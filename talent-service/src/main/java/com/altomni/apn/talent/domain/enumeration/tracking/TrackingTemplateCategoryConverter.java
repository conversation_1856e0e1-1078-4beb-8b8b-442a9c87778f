package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class TrackingTemplateCategoryConverter extends AbstractAttributeConverter<TrackingTemplateCategory, Integer> {
    public TrackingTemplateCategoryConverter() {
        super(TrackingTemplateCategory::toDbValue, TrackingTemplateCategory::fromDbValue);
    }
}
