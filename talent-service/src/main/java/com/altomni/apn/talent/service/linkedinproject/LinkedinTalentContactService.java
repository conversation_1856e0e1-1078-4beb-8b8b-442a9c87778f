package com.altomni.apn.talent.service.linkedinproject;


import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinTalentContactDTO;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinTalentContactVO;

import java.util.List;

/**
 * Service Interface for managing LinkedinTalentContact.
 */
public interface LinkedinTalentContactService {

    List<LinkedinTalentContactVO> replace(String linkedinTalentId, List<LinkedinTalentContactDTO> linkedinTalentContacts);

    List<LinkedinTalentContactVO> findAll(String linkedinTalentId);

    void deleteAllContactsByLinkedinTalentId(String linkedinTalentId);
}
