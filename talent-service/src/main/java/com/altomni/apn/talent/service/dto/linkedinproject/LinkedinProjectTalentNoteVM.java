package com.altomni.apn.talent.service.dto.linkedinproject;

import com.altomni.apn.common.domain.user.User;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;

@Data
public class LinkedinProjectTalentNoteVM implements Serializable {

    private static final long serialVersionUID = 8651525688707729775L;

    private String note;

    private Instant createdDate;

    private Long userId;

    @Transient
    @JsonProperty
    public User user;

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        User singleUser = new User();
        singleUser.setFirstName(user.getFirstName());
        singleUser.setLastName(user.getLastName());
        singleUser.setActivated(user.isActivated());
        singleUser.setMonthlyCredit(null);
        singleUser.setBulkCredit(null);
        this.user = singleUser;
    }

    @Override
    public String toString() {
        return "LinkedinProjectTalentNoteVM{" +
            "note='" + note + '\'' +
            ", createdDate=" + createdDate +
            ", userId=" + userId +
            ", user=" + user +
            '}';
    }
}
