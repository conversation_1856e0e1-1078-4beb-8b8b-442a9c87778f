package com.altomni.apn.talent.repository.folder;

import com.altomni.apn.talent.domain.folder.TalentFolderDetail;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TalentFolderDetailRepository extends JpaRepository<TalentFolderDetail, Long> {

    @EntityGraph(type= EntityGraph.EntityGraphType.FETCH, value = "folder-graph", attributePaths = {"talentFolderRelations", "talentFolderSharingUsers", "talentFolderSharingTeams"})
    Optional<TalentFolderDetail> findById(Long id);

    @EntityGraph(type= EntityGraph.EntityGraphType.FETCH, value = "folder-graph", attributePaths = {"talentFolderRelations", "talentFolderSharingUsers", "talentFolderSharingTeams"})
    Page<TalentFolderDetail> findByPermissionUserId(Long userId, Pageable page);




}
