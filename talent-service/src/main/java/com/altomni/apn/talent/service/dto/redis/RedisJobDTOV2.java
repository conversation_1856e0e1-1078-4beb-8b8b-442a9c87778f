package com.altomni.apn.talent.service.dto.redis;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class RedisJobDTOV2 {

    private Long _id;

    private Long id;

    private Double score;

    private String title;

    private String companyName;

    private List<RedisJobSkill> requiredSkills;

    private List<RedisJobSkill> preferredSkills;

    private String postingTime;

    private boolean isPrivateJob;

    public static class RedisJobSkill {

        private String skillName;

        public String getSkillName() {
            return skillName;
        }

        public void setSkillName(String skillName) {
            this.skillName = skillName;
        }
    }
}
