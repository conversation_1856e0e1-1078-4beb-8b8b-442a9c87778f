package com.altomni.apn.talent.service.xxljob;

import com.altomni.apn.talent.domain.confidential.ConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;

import java.util.Collection;

public interface XxlJobService {

    void addTalentDeclassifyJob(ConfidentialTalent confidentialTalent, TalentDeclassifyType declassifyReason, String... additionalParams);

    void deleteTalentDeclassifyJob(Long talentId);

    void deleteTalentDeclassifyJob(Collection<Long> talentIds);
}
