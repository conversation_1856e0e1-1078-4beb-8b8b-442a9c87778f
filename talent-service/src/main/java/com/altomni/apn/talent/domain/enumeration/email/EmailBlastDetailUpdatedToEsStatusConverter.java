package com.altomni.apn.talent.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class EmailBlastDetailUpdatedToEsStatusConverter extends AbstractAttributeConverter<EmailBlastDetailUpdatedToEsStatus, Integer> {
    public EmailBlastDetailUpdatedToEsStatusConverter() {
        super(EmailBlastDetailUpdatedToEsStatus::toDbValue, EmailBlastDetailUpdatedToEsStatus::fromDbValue);
    }
}
