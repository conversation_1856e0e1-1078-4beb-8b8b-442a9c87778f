package com.altomni.apn.talent.service.elastic;

import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.TalentESConditionDTO;
import com.altomni.apn.talent.service.dto.talent.DuplicationCheckResponseSuspectedDuplicationsDto;
import com.altomni.apn.talent.service.dto.talent.TalentSimilarityDto;
import com.altomni.apn.common.dto.talent.SuspectedDuplications;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface EsFillerTalentService {

    @Deprecated
    HttpResponse syncTalentToEs(Long id) throws IOException;

    @Deprecated
    boolean syncTalentToEs2(Long id);

    @Deprecated
    void asyncTalentToEs(Long id, Long tenantId);

    @Deprecated
    void syncTranslateTalentToEs(Long talentId);

    String searchFromCommonPool(String searchRequest) throws IOException;

    String searchContactsFromCommonPool(TalentESConditionDTO condition) throws IOException;

//    void syncOneByRedis(Integer partitionId, Long talentId);

//    void syncOneFromErrorByRedis(Integer partitionId, Long talentId);

//    void updateUserName(Long userId);

    void updateClientContactName(Long clientId);

    void updateTalentsFolder(Long hotlistId, List<Long> talentIds, boolean addToFolder, Integer count, Long tenantId);

    BigDecimal checkTalentDuplication(TalentV3 talent);

    List<SuspectedDuplications> checkTalentDuplicationWithResult(TalentV3 talentV3, List<TalentContactDTO> contacts, TalentSimilarityDto talentSimilarityDto, String similarity, Long ignoreTalentId);

    List<DuplicationCheckResponseSuspectedDuplicationsDto> checkTalentDuplicationWithResult(Long tenantId, List<TalentContactDTO> contacts);

    void extractTalentToMq(Collection<Long> talentIds, int priority);

    void extractBulkTalentToMq(Collection<Long> talentIds, int priority);

    void buildJsonToJobdivaMq(Collection<Long> talentIds, int priority);

    void saveNormalizedTalentInfos(String normalizedTalent);

    void unlockCommonTalent(Map<String, List<String>> map, Boolean isSyncHistoryData);

    void updateTalentFolder(List<String> talentIds, List<String> toFolderIds, List<String> fromFolderIds, Long tenantId);

    void updateTalentToFoldersOfPreSubmitTalents(Long tenantId, String requestBody) throws Exception;

    void deleteTalentToFoldersOfPreSubmitTalents(Long tenantId, String requestBody) throws Exception;

    void updateTalentRelateJobFolder(Long tenantId, Long jobId, String folderId, String requestBody) throws IOException;

    void mergeTalentRelateJobFolder(Long tenantId, Long jobId, String folderId, String requestBody) throws Exception;

    void deleteTalentRelateJobFolder(Long tenantId, Long jobId, String folderId) throws Exception;
}
