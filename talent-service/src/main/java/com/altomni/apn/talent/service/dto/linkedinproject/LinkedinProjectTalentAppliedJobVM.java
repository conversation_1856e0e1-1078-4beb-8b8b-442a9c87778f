package com.altomni.apn.talent.service.dto.linkedinproject;

import com.altomni.apn.common.domain.enumeration.application.NodeType;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

public class LinkedinProjectTalentAppliedJobVM implements Serializable {

    private static final long serialVersionUID = -928410614854801311L;

    private Long jobId;

    private String jobTitle;

    private NodeType status;

    private Instant appliedDate;

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public NodeType getStatus() {
        return status;
    }

    public void setStatus(NodeType status) {
        this.status = status;
    }

    public Instant getAppliedDate() {
        return appliedDate;
    }

    public void setAppliedDate(Instant appliedDate) {
        this.appliedDate = appliedDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinProjectTalentAppliedJobVM linkedinProjectTalent = (LinkedinProjectTalentAppliedJobVM) o;
        if (linkedinProjectTalent.getJobId() == null || getJobId() == null) {
            return false;
        }
        return Objects.equals(getJobId(), linkedinProjectTalent.getJobId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getJobId());
    }

    @Override
    public String toString() {
        return "LinkedinProjectTalentAppliedJobVM{" +
            "jobId=" + jobId +
            ", jobTitle='" + jobTitle + '\'' +
            ", status=" + status +
            ", appliedDate=" + appliedDate +
            '}';
    }
}
