package com.altomni.apn.talent.service.confidential;

import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.talent.TalentAutoDeclassifyDto;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.talent.service.dto.confidential.ConfidentialRuleDto;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

public interface TalentConfidentialService {

    ConfidentialRuleDto createRule(ConfidentialRuleDto dto);

    List<ConfidentialRuleDto> listRules();

    ConfidentialRuleDto updateRule(Long id, ConfidentialRuleDto dto);

    void deleteRule(Long id);

    void activeAllRules();

    Set<Long> effectOnUpdate(Long id, ConfidentialRuleDto dto);

    Set<Long> effectOnDelete(Long id);

    ConfidentialInfoDto confidentialTalent(Long talentId);

    void declassifyTalent(Long talentId, TalentDeclassifyType declassifyReason);

    void confidentialTalent(TalentV3 talent);

    boolean tryDeclassifyTalentByTalentUpdate(TalentV3 talent);

    boolean confidentialTalentViewAble(Long talentId);

    Set<Long> filterViewableTalentIds(Set<Long> talentIds);

    Optional<ConfidentialInfoDto> getConfidentialInfo(Long talentId);

    Map<Long, ConfidentialInfoDto> getConfidentialInfoBatch(Set<Long> talentIds);

    void tryAutoDeclassifyTalentByProcess(TalentAutoDeclassifyDto talentAutoDeclassifyDto);

    void autoDeclassifyTalent(TalentAutoDeclassifyDto talentAutoDeclassifyDto);

    void handoverConfidentialTalent(Long fromUser, Long toUser);
}
