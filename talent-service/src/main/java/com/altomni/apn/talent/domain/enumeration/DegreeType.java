package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;

public enum DegreeType implements ConvertedEnum<Integer> {
    Associate(10), Bachelor(20), MBA(30), Master(35), MD(40), PhD(45);

    private final Integer dbValue;

    DegreeType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<DegreeType, Integer> resolver =
        new ReverseEnumResolver<>(DegreeType.class, DegreeType::toDbValue);

    public static DegreeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static DegreeType getMinDegree(Collection<DegreeType> degrees) {
        if(CollectionUtils.isEmpty(degrees)) {
            return null;
        }
        DegreeType minDegree = null;
        for(DegreeType degree : degrees) {
            if(minDegree == null || minDegree.compareTo(degree) > 0) {
                minDegree = degree;
            }
        }
        return minDegree;
    }
}
