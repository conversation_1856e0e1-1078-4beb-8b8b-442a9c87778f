package com.altomni.apn.talent.domain.linkedinproject;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinTalentDTO;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * A LinkedinTalent.
 */
@Entity
@Table(name = "linkedin_talent")
public class LinkedinTalent extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -3045905677401096041L;

    @Id
    private String id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "tracking_note_platform_id")
    private String trackingNotePlatformId;

    @Column(name = "apn_talent_id")
    private Long apnTalentId;

    @Column(name = "additional_content")
    private String additionalContent;

    @Transient
    @JsonProperty
    public List<LinkedinTalentContact> contacts;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getTrackingNotePlatformId() {
        return trackingNotePlatformId;
    }

    public void setTrackingNotePlatformId(String trackingNotePlatformId) {
        this.trackingNotePlatformId = trackingNotePlatformId;
    }

    public Long getApnTalentId() {
        return apnTalentId;
    }

    public void setApnTalentId(Long apnTalentId) {
        this.apnTalentId = apnTalentId;
    }

    public String getAdditionalContent() {
        return additionalContent;
    }

    public void setAdditionalContent(String additionalContent) {
        this.additionalContent = additionalContent;
    }

    public List<LinkedinTalentContact> getContacts() {
        return contacts;
    }

    public void setContacts(List<LinkedinTalentContact> contacts) {
        this.contacts = contacts;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinTalent linkedinTalent = (LinkedinTalent) o;
        if (linkedinTalent.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinTalent.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinTalent{" +
            "id='" + id + '\'' +
            ", tenantId=" + tenantId +
            ", trackingNotePlatformId='" + trackingNotePlatformId + '\'' +
            ", apnTalentId=" + apnTalentId +
            ", additionalContent='" + additionalContent + '\'' +
            ", contacts=" + contacts +
            '}';
    }

    public static LinkedinTalent fromLinkedinTalentDTO(LinkedinTalentDTO linkedinTalentDTO) {
        LinkedinTalent linkedinTalent = new LinkedinTalent();
        ServiceUtils.myCopyProperties(linkedinTalentDTO, linkedinTalent);
        return linkedinTalent;
    }
}
