package com.altomni.apn.talent.service.dto.redis;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class RecommendedTenantJobResponse implements Serializable {

    private static final long serialVersionUID = 1244144690728704546L;

    @ApiModelProperty(value = "[STARTED, FINISHED, ON_GOING, ERROR]")
    private String status;

    private String jobs;

    private Integer total;

    private Boolean hasMore;

    public Boolean getHasMore() {
        return hasMore;
    }

    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }


    public String getStatus() {
        return status;
    }

    public RecommendedTenantJobResponse status(String status) {
        this.status = status;
        return this;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getJobs() {
        return jobs;
    }

    public void setJobs(String jobs) {
        this.jobs = jobs;
    }

    public RecommendedTenantJobResponse jobs(String jobs) {
        this.jobs = jobs;
        return this;
    }

}
