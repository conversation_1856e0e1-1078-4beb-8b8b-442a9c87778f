package com.altomni.apn.talent.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class ProspectClientGroupMemberRoleConverter extends AbstractAttributeConverter<ProspectClientGroupMemberRole, Integer> {
    public ProspectClientGroupMemberRoleConverter() {
        super(ProspectClientGroupMemberRole::toDbValue, ProspectClientGroupMemberRole::fromDbValue);
    }
}
