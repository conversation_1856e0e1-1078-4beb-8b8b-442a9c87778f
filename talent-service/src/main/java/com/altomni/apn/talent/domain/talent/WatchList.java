package com.altomni.apn.talent.domain.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A WatchList.
 */
@ApiModel(description = "user's watch list for talents. Watch list has user's private talents, who are not candidates. When " +
    "user applies job for talent, it is removed from user's watch list")
@Entity
@Table(name = "watch_list")
public class WatchList extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -4774923430089714580L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "resume_id")
    private Long resumeId;

    @Column(name = "note")
    private String note;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public WatchList userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTalentId() {
        return talentId;
    }

    public WatchList talentId(Long talentId) {
        this.talentId = talentId;
        return this;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getResumeId() {
        return resumeId;
    }

    public void setResumeId(Long resumeId) {
        this.resumeId = resumeId;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        WatchList watchList = (WatchList) o;
        if (watchList.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), watchList.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "WatchList{" +
            "id=" + id +
            ", userId=" + userId +
            ", talentId=" + talentId +
            ", jobId=" + jobId +
            ", resumeId=" + resumeId +
            ", note='" + note + '\'' +
            '}';
    }
}
