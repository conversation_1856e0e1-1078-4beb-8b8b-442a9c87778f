package com.altomni.apn.talent.domain.confidential;

import com.altomni.apn.common.config.audit.AuditUserHolder;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.time.Instant;
import java.util.concurrent.TimeUnit;

@Entity
@Getter
@Setter
@Table(name = "confidential_talent",
        indexes = {
                @Index(name = "idx_talent_id", columnList = "talent_id", unique = true),
                @Index(name = "idx_rule_id", columnList = "rule_id")
        }
)
public class ConfidentialTalent extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    @Column(name = "active", nullable = false)
    private boolean active;

    @Column(name = "confidential_start_time", nullable = false)
    private Instant confidentialStartTime;

    @Column(name = "confidential_end_time", nullable = false)
    private Instant confidentialEndTime;

    @Column(name = "confidential_owner", nullable = false)
    private Long confidentialOwner;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "rule_id", referencedColumnName = "id")
    private ConfidentialRule rule;

    @Column(name = "declassify_reason")
    private String declassifyReason;

    public void inactive(TalentDeclassifyType declassifyType) {
        this.inactive(declassifyType, null);
    }

    public void inactive(TalentDeclassifyType declassifyType, String additionalParam) {
        this.active = false;
        this.declassifyReason = declassifyType.getValue();
        if (additionalParam != null && !additionalParam.isBlank()) {
            this.declassifyReason = this.declassifyReason + "_" + additionalParam;
        }
        if (declassifyType.isSystem()) {
            Long tenantId = SecurityUtils.getTenantId();
            if (tenantId == null) {
                tenantId = rule.getTenantId();
            }
            AuditUserHolder.set("-1," + tenantId);
        }
    }

    public void handover(Long newOwner) {
        this.confidentialOwner = newOwner;
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId == null) {
            tenantId = rule.getTenantId();
        }
        AuditUserHolder.set("-1," + tenantId);
    }

    public void activeConfidential(Long talentId, ConfidentialRule rule) {
        this.talentId = talentId;
        this.rule = rule;
        this.confidentialOwner = SecurityUtils.getUserId();
        this.active = true;
        this.declassifyReason = null;
        this.confidentialStartTime = Instant.now();
        this.confidentialEndTime = Instant.now().plus(rule.getDeclassificationRules().getDeclassifyDays(), TimeUnit.DAYS.toChronoUnit());
    }

    public boolean reComputeDeclassifyTime(ConfidentialRule rule) {
        Instant newEndTime = this.getConfidentialStartTime().plus(rule.getDeclassificationRules().getDeclassifyDays(), TimeUnit.DAYS.toChronoUnit());
        if (newEndTime.equals(this.getConfidentialEndTime())) {
            return false;
        }
        this.confidentialEndTime = newEndTime;
        return true;
    }
}
