package com.altomni.apn.talent.service.vo.linkedinproject;

import java.io.Serializable;

public class LinkedinProjectTalentStatusCountVO implements Serializable {

    private static final long serialVersionUID = 49011356243569498L;

    private Integer activeCandidatesWithContactInfo;

    private Integer activeCandidatesWithoutContactInfo;

    private Integer archivedCandidates;

    private Integer unContacted;

    private Integer unContactedActiveCandidatesWithContactInfo;

    private Integer unContactedActiveCandidatesWithoutContactInfo;

    private Integer contacted;

    private Integer replied;

    private Integer emailedCandidates;

    public Integer getActiveCandidatesWithContactInfo() {
        return activeCandidatesWithContactInfo;
    }

    public void setActiveCandidatesWithContactInfo(Integer activeCandidatesWithContactInfo) {
        this.activeCandidatesWithContactInfo = activeCandidatesWithContactInfo;
    }

    public Integer getActiveCandidatesWithoutContactInfo() {
        return activeCandidatesWithoutContactInfo;
    }

    public void setActiveCandidatesWithoutContactInfo(Integer activeCandidatesWithoutContactInfo) {
        this.activeCandidatesWithoutContactInfo = activeCandidatesWithoutContactInfo;
    }

    public Integer getArchivedCandidates() {
        return archivedCandidates;
    }

    public void setArchivedCandidates(Integer archivedCandidates) {
        this.archivedCandidates = archivedCandidates;
    }

    public Integer getUnContacted() {
        return unContacted;
    }

    public void setUnContacted(Integer unContacted) {
        this.unContacted = unContacted;
    }

    public Integer getUnContactedActiveCandidatesWithContactInfo() {
        return unContactedActiveCandidatesWithContactInfo;
    }

    public void setUnContactedActiveCandidatesWithContactInfo(Integer unContactedActiveCandidatesWithContactInfo) {
        this.unContactedActiveCandidatesWithContactInfo = unContactedActiveCandidatesWithContactInfo;
    }

    public Integer getUnContactedActiveCandidatesWithoutContactInfo() {
        return unContactedActiveCandidatesWithoutContactInfo;
    }

    public void setUnContactedActiveCandidatesWithoutContactInfo(Integer unContactedActiveCandidatesWithoutContactInfo) {
        this.unContactedActiveCandidatesWithoutContactInfo = unContactedActiveCandidatesWithoutContactInfo;
    }

    public Integer getContacted() {
        return contacted;
    }

    public void setContacted(Integer contacted) {
        this.contacted = contacted;
    }

    public Integer getReplied() {
        return replied;
    }

    public void setReplied(Integer replied) {
        this.replied = replied;
    }

    public Integer getEmailedCandidates() {
        return emailedCandidates;
    }

    public void setEmailedCandidates(Integer emailedCandidates) {
        this.emailedCandidates = emailedCandidates;
    }

    @Override
    public String toString() {
        return "LinkedinProjectTalentStatusCount{" +
            "activeCandidatesWithContactInfo=" + activeCandidatesWithContactInfo +
            ", activeCandidatesWithoutContactInfo=" + activeCandidatesWithoutContactInfo +
            ", archivedCandidates=" + archivedCandidates +
            ", unContacted=" + unContacted +
            ", unContactedActiveCandidatesWithContactInfo=" + unContactedActiveCandidatesWithContactInfo +
            ", unContactedActiveCandidatesWithoutContactInfo=" + unContactedActiveCandidatesWithoutContactInfo +
            ", contacted=" + contacted +
            ", replied=" + replied +
            ", emailedCandidates=" + emailedCandidates +
            '}';
    }
}
