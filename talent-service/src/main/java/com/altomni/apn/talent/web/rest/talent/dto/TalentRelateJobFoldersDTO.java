//package com.altomni.apn.talent.web.rest.talent.dto;
//
//import com.altomni.apn.common.domain.enumeration.job.JobStatus;
//import com.altomni.apn.talent.domain.user.RelateJobFolderUserInfo;
//import com.fasterxml.jackson.annotation.JsonInclude;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.stream.Collectors;
//
//@Data
//@JsonInclude(JsonInclude.Include.NON_EMPTY)
//@ApiModel
//public class TalentRelateJobFoldersDTO implements Serializable {
//    private static final long serialVersionUID = 1L;
//
//    private String id;
//    private Long jobId;
//
//    private String note;
//    @ApiModelProperty(value = "companyName")
//    private String companyName;
//
//    @ApiModelProperty(value = "The title for the position")
//    private String title;
//
//    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
//    private JobStatus status;
//
//    @ApiModelProperty(value = "job priority")
//    private String priority;
//
//    @ApiModelProperty(value = "current folder type")
//    private String type;
//
//    private RelateJobFolderUserInfo createBy;
//
//    private List<RelateJobFolderUserInfo> owner;
//
//    private List<RelateJobFolderUserInfo> share;
//
//    private List<Long> talentIds;
//
//    private AddTalentsToFoldersOutput talentsToFoldersInfo;
//
//    public void excludeSelf(Long userId) {
//        if(owner != null) {
//            owner = owner.stream().filter(p -> !userId.equals(p.getId())).collect(Collectors.toList());
//        } else {
//            owner = new ArrayList<>();
//        }
//        if(share != null) {
//            share = share.stream().filter(p -> !userId.equals(p.getId())).collect(Collectors.toList());
//        } else {
//            share = new ArrayList<>();
//        }
//    }
//}
