package com.altomni.apn.talent.service.dto.start;


import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.talent.domain.enumeration.start.StartStatus;
import com.altomni.apn.talent.domain.enumeration.start.StartType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class StartDTO implements Serializable {

    private static final long serialVersionUID = 4407706925970162814L;

    private Long id;

    private Long tenantId;

    private LocalDate startDate;

    private LocalDate endDate;

    private LocalDate warrantyEndDate;

    private StartStatus status;

    private StartType startType;

    private Long talentId;

    private String talentName;

    private Long jobId;

    private String jobTitle;

    private Long companyId;

    private String company;

    private Long talentRecruitmentProcessId;

    private Long clientContactId;

    private JobType positionType;

    private String timeZone;

    private String note;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "talentId", "jobId"));

}
