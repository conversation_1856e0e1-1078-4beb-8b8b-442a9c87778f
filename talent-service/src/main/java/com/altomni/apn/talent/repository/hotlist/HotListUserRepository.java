package com.altomni.apn.talent.repository.hotlist;

import com.altomni.apn.talent.domain.user.HotListUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the HotListUser entity.
 */
@Repository
public interface HotListUserRepository extends JpaRepository<HotListUser, Long> {

    List<HotListUser> findAllByHotListId(Long hotListId);

    List<HotListUser> findAllByHotListIdAndUserId(Long hotListId, Long userId);

    List<HotListUser> findAllByUserId(Long userId);

    void deleteAllByHotListId(Long hotListId);
}
