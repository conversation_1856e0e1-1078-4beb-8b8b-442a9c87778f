package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.dict.*;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.talent.TalentExcelStatusEnum;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.WithDataException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.constants.RedisConstants;
import com.altomni.apn.talent.domain.talent.ParserResultForExcelVo;
import com.altomni.apn.talent.service.talent.TalentCreateByExcelService;
import com.altomni.apn.talent.service.talent.TalentService;
import com.altomni.apn.talent.service.talent.channel.CreateTalentByExcelChannel;
import com.altomni.apn.talent.service.vo.talent.TalentFailReasonVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 *  根据excel 创建talent的实现类
 */
@Slf4j
@RefreshScope
@Service("talentCreateByExcelService")
public class TalentCreateByExcelServiceImpl implements TalentCreateByExcelService {

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private TalentService talentService;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private CreateTalentByExcelChannel createTalentByExcelChannel;

    private static final List<String> PARSER_ERROR_LIST = CollUtil.newArrayList("ERROR", "NOT_ACCEPT");

    private static final String PARSE_SUCCESS = "FINISHED";

    @Value("${excel.parser.timeout:120}")
    private int excelParserTimeOut;

    @Resource
    private ApplicationProperties applicationProperties;

    /**
     * 根据 excel 创建talent
     * @param taskId
     *
     */
    @Override
    public void createTalentByParserResultFromExcel(String taskId) {
        try (Jedis jedis = commonRedisService.getJedisParserInstance()) {
            //模拟内部接口调用规则, 没有添加token 信息, token信息有 过期的风险,目前这种方式, 如果存在feign 调用, 接口是开放的就么有问题
            checkTaskExpire(jedis, taskId);
            //检查是否终止
            if (checkTaskIsTerminate(taskId, jedis)) {
                log.info("create talent by excel is TERMINATE, taskId = {}", taskId);
                return;
            }
            // taskId =  uuid + "-" + tenantId + "-" + hotListId;
            String[] taskIdArray = taskId.split("-");
            String uuid = taskIdArray[0];
            String hotListId = "";
            if (Objects.equals(taskIdArray.length, 3)) {
                hotListId = taskIdArray[2];
            }
            // 获取 parser 解析状态, 没有则表示parser 解析未开始, 或者过期
            ParserResultForExcelVo parserResultForExcelVo = getParserResultForExcelVo(uuid, jedis);
            if (parserResultForExcelVo == null) {
                // 为空则表示 parser 解析任务过期, 需要重新解析
                if (Objects.equals(1L, jedis.setnx(applicationProperties.getTalentExcelSqlRetryFlag() + ":" + uuid, ""))) {
                    createTalentByExcelChannel.initParserRedisAndSendSqlByS3(uuid, true, false, hotListId);
                    jedis.expire(applicationProperties.getTalentExcelSqlRetryFlag() + ":" + uuid, 6000);
                    return;
                } else {
                    // parser 服务有问题
                    log.info("get result from parser is error, taskId = {}", taskId);
                    insertErrorResultToRedis(taskId, jedis);
                    return;
                }
            }
            // 有解析结果, 但是解析结果是失败的
            if (PARSER_ERROR_LIST.contains(parserResultForExcelVo.getStatus())) {
                // 删除任务信息,且任务结束
                log.info("parser is {}, taskId = {}", parserResultForExcelVo.getStatus(), taskId);
                insertErrorResultToRedis(taskId, jedis);
                return;
            }
            // 获取目前执行任务的下标, 从0开始
            Integer currentIndex = getCurrentIndex(taskId, jedis);
            if (currentIndex == null) {
                //设置 total
                jedis.hset(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_TOTAL, parserResultForExcelVo.getTotalRows());
                // 获取 excel 开始的下标
                jedis.hset(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_START_ROW_INDEX, parserResultForExcelVo.getStartRowIndex());
                // 设置初始化 扫描下标
                currentIndex = 0;
                updateCurrentIndex(taskId, jedis, 0L);
                //转态改为进行中
                if (!hsetStatusAndCheckStatus(jedis, taskId, TalentExcelStatusEnum.IN_PROGRESS.name())) {
                    log.info("create talent by excel is TERMINATE, taskId = {}", taskId);
                    return;
                }
                // 设置任务开始时间
                jedis.hset(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_TASK_START_DATE, System.currentTimeMillis() + "");
                updateExpire(taskId, jedis);
            }
            List<String> dataList = jedis.lrange(RedisConstants.DATA_KEY_EXCEL_PARSER + uuid + RedisConstants.DATA_KEY_DATA, currentIndex, -1);
            if (CollUtil.isEmpty(dataList) && !Objects.equals(parserResultForExcelVo.getStatus(), ParseStatus.FINISHED.name())) {
                log.info("excel parsing continues with no new results, taskId = {}", taskId);
                return;
            }
            for (String dataJson : dataList) {
                //检查是否终止
                if (checkTaskIsTerminate(taskId, jedis)) {
                    log.info("create talent by excel is TERMINATE, taskId = {}", taskId);
                    return;
                }
                TalentFailReasonVo talentFailReasonVo = new TalentFailReasonVo();
                try {
                    JSONObject jsonObject = JSONUtil.parseObj(dataJson);
                    Integer index = jsonObject.getInt("index");
                    if (!ObjectUtil.equal(currentIndex + 1, index)) {
                        continue;
                    }
                    currentIndex = index;
                    talentFailReasonVo.setIndex(Long.valueOf(index));
                    parserResultFromJsonCreateTalent(taskId, talentFailReasonVo, dataJson, jedis, hotListId, uuid);
                } catch (WithDataException e) {
                    talentFailReasonVo.setReason("Duplicate entry talent data");
                } catch (CustomParameterizedException e) {
                    talentFailReasonVo.setReason("The name and contact information cannot be empty");
                } catch (Exception e) {
                    talentFailReasonVo.setReason("Unknown error");
                    log.error("create talent by excel is error , taskId = {}, msg = {}", taskId, ExceptionUtils.getStackTrace(e));
                }
                updateCurrentIndex(taskId, jedis, talentFailReasonVo.getIndex());
                if (StrUtil.isNotBlank(talentFailReasonVo.getReason())) {
                    jedis.lpush(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_DATA, JSONUtil.toJsonStr(talentFailReasonVo));
                    log.info("create talent is error, index = {}, taskId = {}, reason = {}", talentFailReasonVo.getIndex(), taskId, talentFailReasonVo.getReason());
                }
            }
            // 查看是否完成, 检查下标的偏移量等于total
            if (Objects.equals(currentIndex, Integer.parseInt(parserResultForExcelVo.getTotalRows()))) {
                //finish,to delete task
                updateStatus(taskId, jedis, TalentExcelStatusEnum.FINISH.name());
                updateExpire(taskId, jedis);
                deleteTask(taskId, jedis);
                return;
            }
            updateExpire(taskId, jedis);
        } catch (Exception e) {
            //未知异常, 结束任务
            log.error("create talent by excel is error, msg = {}", e.getMessage());
            throw e;
        }
    }

    private ParserResultForExcelVo getParserResultForExcelVo(String uuid, Jedis jedis) {
        int i = 0;
        log.info("excelParserTimeOut = " + excelParserTimeOut);
        while (i < excelParserTimeOut) {
            try {
                ParserResultForExcelVo vo = new ParserResultForExcelVo();
                String status = jedis.get(RedisConstants.DATA_KEY_EXCEL_PARSER + uuid + RedisConstants.DATA_KEY_STATUS);
                String total = jedis.hget(RedisConstants.DATA_KEY_EXCEL_PARSER + uuid + RedisConstants.DATA_KEY_METADATA, RedisConstants.DATA_KEY_EXCEL_PARSER_TOTAL_ROW);
                String startRowIndex = jedis.hget(RedisConstants.DATA_KEY_EXCEL_PARSER + uuid + RedisConstants.DATA_KEY_METADATA, RedisConstants.DATA_KEY_EXCEL_PARSER_START_ROW_INDEX);
                if (StrUtil.isNotBlank(status) && StrUtil.isNotBlank(total) && StrUtil.isNotBlank(startRowIndex)) {
                    vo.setStatus(status);
                    vo.setTotalRows(total);
                    vo.setStartRowIndex(startRowIndex);
                    return vo;
                }
                TimeUnit.SECONDS.sleep(1L);
            } catch (InterruptedException ignored) {
                continue;
            } catch (Exception e) {
                log.error("get parser result for excel is error, msg = {}", ExceptionUtils.getStackTrace(e));
            } finally {
                i++;
            }
        }
        return null;
    }

    private void parserResultFromJsonCreateTalent(String taskId, TalentFailReasonVo talentFailReasonVo, String dataJson, Jedis jedis, String hotListId, String uuid) {
        JSONObject jsonObject = JSONUtil.parseObj(dataJson);
        Integer index = jsonObject.getInt("index");
        String rowStatus = jsonObject.getStr("status");
        if (Objects.equals(PARSE_SUCCESS, rowStatus)) {
            JSONObject dataObject = jsonObject.getJSONObject("data");
            String json = JSONUtil.toJsonStr(dataObject);
            if (ObjectUtil.isNotEmpty(dataObject)) {
                // "notes":{"text":"","contactType":""}
                JSONObject notesObject = dataObject.getJSONObject("notes");
                String notes = null;
                String noteType = null;
                if (ObjectUtil.isNotNull(notesObject)) {
                    notes = notesObject.getStr("text");
                    noteType = notesObject.getStr("contactType");
                }
                dataObject.remove("notes");
                formatData(dataObject);
                TalentDTOV3 talentDtoV3 = JSONUtil.toBean(JSONUtil.toJsonStr(dataObject), TalentDTOV3.class);
                filterContacts(talentDtoV3);
                if (StrUtil.isNotBlank(hotListId)) {
                    talentDtoV3.setHotListId(Long.parseLong(hotListId));
                }
                long talentId = talentService.createByExcel(talentDtoV3, json, notes, uuid, noteType);
                jedis.sadd(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_SUCCESS_DATA, index + "-" + talentId);
                log.info("create talent is success, talentId = {}, taskId = {}", talentId, taskId);
            } else {
                // parser 成功但是会出现空行的情况
                talentFailReasonVo.setReason("no data");
            }
        } else {
            // 直接返回 parser 解析的错误原因
            talentFailReasonVo.setReason("parser " + rowStatus);
        }
    }

    private boolean checkTaskIsTerminate(String taskId, Jedis jedis) {
        String taskStatus = jedis.hget(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_STATUS);
        // 获取任务状态, 查看任务是否被终止
        if (Objects.equals(taskStatus, TalentExcelStatusEnum.TERMINATE.name())) {
            deleteTaskDetail(taskId, jedis);
            deleteTask(taskId, jedis);
            return true;
        }
        return false;
    }

    private void filterContacts(TalentDTOV3 talentDtoV3) {
        if (CollUtil.isEmpty(talentDtoV3.getContacts())) {
            return;
        }
        List<TalentContactDTO> contacts = talentDtoV3.getContacts().stream().filter(talentContactDTO -> {
            if (Objects.equals(talentContactDTO.getType(), ContactType.LINKEDIN)) {
                return !StrUtil.isBlank(talentContactDTO.getContact());
            }
            return true;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(contacts)) {
            talentDtoV3.setContacts(contacts);
        }
    }

    private void formatData(JSONObject dataObject) {
        //update educational
        JSONArray jsonArray = dataObject.getJSONArray("educations");
        if (CollUtil.isNotEmpty(jsonArray)) {
            JSONArray educations = new JSONArray();
            jsonArray.stream().map(JSONUtil::parseObj).forEach(education -> {
                if (StrUtil.isBlank(education.getStr("collegeName"))) {
                    return;
                }
                if (StrUtil.isNotBlank(education.getStr("majorName"))
                        || StrUtil.isNotBlank(education.getStr("degreeLevel"))
                        || StrUtil.isNotBlank(education.getStr("startDate"))) {
                    educations.add(education);
                    return;
                }
                educations.add(education);
            });
            if (CollUtil.isNotEmpty(educations)) {
                dataObject.put("educations", educations);
            } else {
                dataObject.remove("educations");
            }
        }
        //experience
        JSONArray experienceArray = dataObject.getJSONArray("experiences");
        if (CollUtil.isNotEmpty(experienceArray)) {
            JSONArray experiences = new JSONArray();
            experienceArray.stream().map(JSONUtil::parseObj).forEach(experience -> {
                if (StrUtil.isBlank(experience.getStr("title"))) {
                    return;
                }
                if (experience.getStr("title").length() > 250) {
                    return;
                }
                if (StrUtil.isNotBlank(experience.getStr("companyName"))
                        || StrUtil.isNotBlank(experience.getStr("startDate")) ) {
                    experiences.add(experience);
                    return;
                }
                experiences.add(experience);
            });
            if (CollUtil.isNotEmpty(experiences)) {
                dataObject.put("experiences", experiences);
            } else {
                dataObject.remove("experiences");
            }
        }
        // jobFunction, industries, language
        JSONArray languagesArray = dataObject.getJSONArray("languages");
        if (CollUtil.isNotEmpty(languagesArray)) {
            Map<String, Long> enumLanguageMap = enumCommonService.findAllEnumLanguages().stream().collect(Collectors.toMap(EnumLanguage::getName, EnumLanguage::getId));
            List<EnumRelationDTO> languageList = getEnumRelationDtoListByName(enumLanguageMap, languagesArray);
            if (CollUtil.isNotEmpty(languageList)) {
                dataObject.put("languages", languageList);
            } else {
                dataObject.remove("languages");
            }
        }
        JSONArray industriesArray = dataObject.getJSONArray("industries");
        if (CollUtil.isNotEmpty(industriesArray)) {
            List<EnumRelationDTO> industryList = getEnumRelationDtoListById(industriesArray);
            if (CollUtil.isNotEmpty(industryList)) {
                dataObject.put("industries", industryList);
            } else {
                dataObject.remove("industries");
            }
        }
        JSONArray jobFunctionsArray = dataObject.getJSONArray("jobFunctions");
        if (CollUtil.isNotEmpty(jobFunctionsArray)) {
            List<EnumRelationDTO> jobFunctionList = getEnumRelationDtoListById(jobFunctionsArray);
            if (CollUtil.isNotEmpty(jobFunctionList)) {
                dataObject.put("jobFunctions", jobFunctionList);
            } else {
                dataObject.remove("jobFunctions");
            }
        }
        //currency
        String currencyCode = dataObject.getStr("currency");
        if (StrUtil.isNotBlank(currencyCode)) {
            Map<String, Integer> enumCurrencyMap = enumCommonService.findAllEnumCurrency().stream().collect(Collectors.toMap(EnumCurrency::getName, EnumCurrency::getId));
            if (enumCurrencyMap.containsKey(currencyCode)) {
                dataObject.put("currency", enumCurrencyMap.get(currencyCode));
            }
        }
    }

    private List<EnumRelationDTO> getEnumRelationDtoListByName(Map<String, Long> enumMap, JSONArray jsonArray) {
        return jsonArray.stream().map(String::valueOf).map(enumMap::get).filter(ObjectUtil::isNotEmpty).map(id -> {
            EnumRelationDTO enumRelationDTO = new EnumRelationDTO();
            enumRelationDTO.setEnumId(String.valueOf(id));
            return enumRelationDTO;
        }).collect(Collectors.toList());
    }

    private List<EnumRelationDTO> getEnumRelationDtoListById(JSONArray jsonArray) {
        return jsonArray.stream().map(String::valueOf).filter(ObjectUtil::isNotEmpty).map(id -> {
            EnumRelationDTO enumRelationDTO = new EnumRelationDTO();
            enumRelationDTO.setEnumId(String.valueOf(id));
            return enumRelationDTO;
        }).collect(Collectors.toList());
    }

    private void updateStatus(String taskId, Jedis jedis, String status) {
        jedis.hset(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_STATUS, status);
    }

    public Boolean hsetStatusAndCheckStatus(Jedis jedis, String taskId, String status) {
        String luaScript = "if redis.call('hget', KEYS[1], ARGV[1]) == ARGV[2] then return nil else return redis.call('hset', KEYS[1], ARGV[1], ARGV[3]) end";
        Object result = jedis.eval(luaScript, 1, applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA,
                RedisConstants.METADATA_KEY_STATUS, "TERMINATE", status);
        if (result == null) {
            deleteTaskDetail(taskId, jedis);
            deleteTask(taskId, jedis);
            return false;
        }
        return true;
    }

    private void deleteTask(String taskId, Jedis jedis) {
        jedis.lrem(applicationProperties.getCreateTalentByExcelQueue(), 0, taskId);
    }

    private void deleteTaskDetail(String taskId, Jedis jedis) {
        jedis.del(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA);
        jedis.del(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_DATA);
        jedis.del(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_SUCCESS_DATA);
    }

    private void updateExpire(String taskId, Jedis jedis) {
        jedis.expire(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.REDIS_EXPIRE_TIME);
        jedis.expire(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_DATA, RedisConstants.REDIS_EXPIRE_TIME);
        jedis.expire(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_SUCCESS_DATA, RedisConstants.REDIS_EXPIRE_TIME);
    }

    private boolean checkTaskExpire(Jedis jedis, String taskId) {
        String startTimeStr = jedis.hget(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_TASK_START_DATE);
        if (StrUtil.isNotBlank(startTimeStr)) {
            Long startTime = Long.parseLong(startTimeStr);
            Long lastTime = System.currentTimeMillis();
            long timeInSeconds = (lastTime - startTime) / 1000;
            // excel 只支持500条数据, 10 分钟没有结束即为任务超时
            log.info("create talent by excel ,task id = {}, time = {}", taskId, timeInSeconds);
            return timeInSeconds > 600;
        }
        return false;
    }


    private void updateCurrentIndex(String taskId, Jedis jedis, Long index) {
        jedis.hset(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_CURRENTINDEX, String.valueOf(index));
    }

    private Integer getCurrentIndex(String taskId, Jedis jedis) {
        String indexString = jedis.hget(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_CURRENTINDEX);
        if (StrUtil.isBlank(indexString)) {
            return null;
        }
        return Integer.parseInt(indexString);
    }

    private void insertErrorResultToRedis(String taskId, Jedis jedis) {
        jedis.hset(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, "status", TalentExcelStatusEnum.ERROR.name());
        jedis.expire(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.REDIS_EXPIRE_TIME);
        deleteTask(taskId, jedis);
    }


}
