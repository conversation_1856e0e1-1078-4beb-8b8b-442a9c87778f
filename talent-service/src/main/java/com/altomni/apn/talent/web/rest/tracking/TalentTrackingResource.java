package com.altomni.apn.talent.web.rest.tracking;

import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroup;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinMessageTemplate;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinPending;
import com.altomni.apn.talent.service.dto.tracking.*;
import com.altomni.apn.talent.service.tracking.TalentTrackingService;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingGroupInfoVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingGroupVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingTemplateVO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/v3/tracking")
@Slf4j
public class TalentTrackingResource {

    @Resource
    private TalentTrackingService talentTrackingService;

    @PostMapping("/pending")
    public ResponseEntity<TalentTrackingVO> saveTalentTracking(@Valid @RequestBody TalentTrackingDTO talentTrackingDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to save talent tracking: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingDTO);
        return new ResponseEntity<>(talentTrackingService.saveTalentTracking(talentTrackingDTO), HttpStatus.CREATED);
    }

    @PostMapping("/pending/search")
    public ResponseEntity<List<TalentTrackingVO>> searchTalentTracking(@Valid @RequestBody TalentTrackingSearchDTO talentTrackingSearchDTO, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to search talent tracking: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingSearchDTO);
        Page<TalentTrackingLinkedinPending> page = talentTrackingService.searchTalentTracking(talentTrackingSearchDTO, pageable);
        List<TalentTrackingVO> talentTrackingVOList = page.getContent().stream().map(TalentTrackingLinkedinPending::toTalentTrackingVO).toList();
        talentTrackingService.setGroupList(talentTrackingVOList);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v1/tracking/pending/search");
        headers.add("Access-Control-Expose-Headers","Pagination-Count");
        return ResponseEntity.ok().headers(headers).body(talentTrackingVOList);
    }

    @DeleteMapping("/pending/inactive")
    public ResponseEntity<HttpStatus> inactiveTalentTracking(@Valid @RequestBody TalentTrackingManageDTO talentTrackingManageDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to inactive talent tracking: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingManageDTO);
        talentTrackingService.inactiveTalentTracking(talentTrackingManageDTO);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/pending/sent")
    public ResponseEntity<List<TalentTrackingVO>> sentTalentTracking(@Valid @RequestBody TalentTrackingManageDTO talentTrackingManageDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to sent talent tracking: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingManageDTO);
        return new ResponseEntity<>(talentTrackingService.sentTalentTracking(talentTrackingManageDTO), HttpStatus.CREATED);
    }

    @PostMapping("/pending/retract-sent")
    public ResponseEntity<List<TalentTrackingVO>> retractSentTalentTracking(@Valid @RequestBody TalentTrackingManageDTO talentTrackingManageDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to retract sent talent tracking: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingManageDTO);
        return new ResponseEntity<>(talentTrackingService.retractSentTalentTracking(talentTrackingManageDTO), HttpStatus.CREATED);
    }

    @PostMapping("/message-template/search")
    public ResponseEntity<List<TalentTrackingTemplateVO>> searchTalentTrackingTemplate(@Valid @RequestBody TalentTrackingTemplateSearchDTO talentTrackingTemplateSearchDTO, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to search talent tracking template: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingTemplateSearchDTO);
        Page<TalentTrackingLinkedinMessageTemplate> page = talentTrackingService.searchTalentTrackingTemplate(talentTrackingTemplateSearchDTO, pageable);
        List<TalentTrackingTemplateVO> talentTrackingTemplateVOList = page.getContent().stream().map(TalentTrackingLinkedinMessageTemplate::toTalentTrackingTemplateVO).toList();
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/tracking/message-template/search");
        headers.add("Access-Control-Expose-Headers","Pagination-Count");
        return ResponseEntity.ok().headers(headers).body(talentTrackingTemplateVOList);
    }

    @PostMapping("/message-template")
    public ResponseEntity<TalentTrackingTemplateVO> saveTalentTrackingTemplate(@Valid @RequestBody TalentTrackingTemplateDTO talentTrackingTemplateDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to save talent tracking template: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingTemplateDTO);
        return new ResponseEntity<>(talentTrackingService.saveTalentTrackingTemplate(talentTrackingTemplateDTO), HttpStatus.CREATED);
    }

    @PutMapping("/message-template/{id}")
    public ResponseEntity<TalentTrackingTemplateVO> updateTalentTrackingTemplate(@PathVariable("id") Long id, @Valid @RequestBody TalentTrackingTemplateDTO talentTrackingTemplateDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to update talent tracking template: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingTemplateDTO);
        return new ResponseEntity<>(talentTrackingService.updateTalentTrackingTemplate(id, talentTrackingTemplateDTO), HttpStatus.CREATED);
    }

    @DeleteMapping("/message-template/inactive")
    public ResponseEntity<HttpStatus> inactiveTalentTrackingTemplate(@Valid @RequestBody TalentTrackingManageDTO talentTrackingManageDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to inactive talent tracking template: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingManageDTO);
        talentTrackingService.inactiveTalentTrackingTemplate(talentTrackingManageDTO);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/group")
    public ResponseEntity<TalentTrackingGroupVO> saveGroup(@Valid @RequestBody TalentTrackingGroupDTO talentTrackingGroupDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to save talent tracking group: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingGroupDTO);
        return new ResponseEntity<>(talentTrackingService.saveGroup(talentTrackingGroupDTO), HttpStatus.CREATED);
    }

    @PutMapping("/group/{id}")
    public ResponseEntity<TalentTrackingGroupVO> updateGroup(@PathVariable("id") Long id, @RequestBody TalentTrackingGroupDTO talentTrackingGroupDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to update talent tracking group. id: {}, dto: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), id, talentTrackingGroupDTO);
        return new ResponseEntity<>(talentTrackingService.updateGroup(id, talentTrackingGroupDTO), HttpStatus.CREATED);
    }

    @DeleteMapping("/group/{id}")
    public ResponseEntity<HttpStatus> inactiveGroup(@PathVariable("id") Long id) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to inactive talent tracking group. id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), id);
        talentTrackingService.inactiveGroup(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @PostMapping("/group/member")
    public ResponseEntity<HttpStatus> saveGroupMember(@Valid @RequestBody TalentTrackingGroupManageDTO talentTrackingGroupManageDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to save talent tracking group member: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentTrackingGroupManageDTO);
        talentTrackingService.saveGroupMember(talentTrackingGroupManageDTO);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @GetMapping("/group/search")
    public ResponseEntity<List<TalentTrackingGroupVO>> searchGroup(@RequestParam("operatorLinkedinId") String operatorLinkedinId, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to search talent tracking group: {}, operatorLinkedinId: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), operatorLinkedinId);
        Page<TalentTrackingLinkedinGroup> page = talentTrackingService.searchGroup(operatorLinkedinId, pageable);
        List<TalentTrackingGroupVO> talentTrackingGroupVOList = talentTrackingService.toTalentTrackingGroupVO(page.getContent());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/tracking/group/search");
        headers.add("Access-Control-Expose-Headers","Pagination-Count");
        return new ResponseEntity<>(talentTrackingGroupVOList, headers, HttpStatus.OK);
    }

    @GetMapping("/group/{id}/member")
    public ResponseEntity<List<TalentTrackingVO>> queryGroupMember(@PathVariable("id") Long id) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to query talent tracking group member: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), id);
        List<TalentTrackingVO> talentTrackingVOList = talentTrackingService.queryGroupMember(id);
        return new ResponseEntity<>(talentTrackingVOList, HttpStatus.OK);
    }

    @GetMapping("/group/list")
    public ResponseEntity<List<TalentTrackingGroupVO>> queryGroupByOperatorLinkedinId(@RequestParam("operatorLinkedinId") String operatorLinkedinId) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to query talent tracking group by operatorLinkedinId: {}, operatorLinkedinId: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), operatorLinkedinId);
        return new ResponseEntity<>(talentTrackingService.queryGroupByOperatorLinkedinId(operatorLinkedinId), HttpStatus.OK);
    }

    @PostMapping("/group/info")
    public ResponseEntity<List<TalentTrackingGroupInfoVO>> queryGroupInfo(@Valid @RequestBody TalentTrackingGroupInfoSearchDTO searchDTO) {
        log.info("[APN @{}: TalentTrackingResource @{}] REST request to query talent tracking group info: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), searchDTO);
        List<TalentTrackingGroupInfoVO> result = talentTrackingService.queryGroupInfo(searchDTO);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
