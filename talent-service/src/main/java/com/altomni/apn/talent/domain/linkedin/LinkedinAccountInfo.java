package com.altomni.apn.talent.domain.linkedin;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;

@Entity
@Table(name = "linkedin_account_info")
@Data
public class LinkedinAccountInfo extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Size(max = 250)
    @Column(name = "linkedin_id", length = 250, nullable = false)
    private String linkedinId;

    @Column(name = "friend_count")
    private Long friendCount;

    @Column(name = "match_friend_datetime")
    private Instant matchFriendDatetime;

}