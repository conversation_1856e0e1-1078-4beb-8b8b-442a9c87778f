package com.altomni.apn.talent.service.finance;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.talent.domain.enumeration.start.StartStatus;
import com.altomni.apn.talent.service.dto.start.StartDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

@Component
@FeignClient(value = "finance-service")
public interface FinanceService {

    @GetMapping("/finance/api/v3/starts/talentId/{talentId}/status/{status}")
    ResponseEntity<StartDTO> findStartByTalentIdAndStatus(@PathVariable("talentId") Long talentId, @PathVariable("status") StartStatus status);

    @GetMapping("/finance/api/v3/starts/order/{talentId}/{status}")
    ResponseEntity<StartDTO> findByTalentIdAndStatusOrderByEndDate(@PathVariable("talentId") Long talentId, @PathVariable("status") StartStatus status);


    @PostMapping("/finance/api/v3/starts/filter-talents-with-active-fte-starts")
    ResponseEntity<Set<Long>> filterTalentsWithActiveFteStarts(@RequestBody Set<Long> talentIds);

    @GetMapping("/finance/api/v3/starts/termination/talent/{talentId}/applications")
    ResponseEntity<Set<Long>> findTerminatedApplicationIdsByTalentId(@PathVariable("talentId") Long talentId);
}
