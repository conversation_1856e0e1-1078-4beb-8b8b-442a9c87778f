package com.altomni.apn.talent.service.talent;

import com.altomni.apn.talent.domain.talent.WatchList;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.talent.service.dto.talent.WatchListDTO;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface WatchListService {

    WatchList create(WatchList watchList);

    void removeTalentWatchList(List<Long> talentIds);

    void removeTalentWatchListByIds(List<Long> ids);

    Page<WatchListDTO> findAll(BooleanExpression allWatchlist, Pageable pageable);

    Page<TalentDTOV3> getMyWatchListTalent(String search, Pageable pageable);

    Page<TalentDTOV3> getMyWatchListTalentsByJobId(String search, Long jobId, Pageable pageable);
}
