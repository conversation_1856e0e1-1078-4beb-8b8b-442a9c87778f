package com.altomni.apn.talent.repository.tracking;

import com.altomni.apn.talent.domain.enumeration.tracking.TrackingGroupStatus;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinGroup;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinPending;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TalentTrackingLinkedinGroupRepository extends JpaRepository<TalentTrackingLinkedinGroup, Long>, JpaSpecificationExecutor<TalentTrackingLinkedinPending> {

    List<TalentTrackingLinkedinGroup> findAllByTenantIdAndOperatorLinkedinIdAndStatusAndName(Long tenantId, String operatorId, TrackingGroupStatus status, String name);

    Page<TalentTrackingLinkedinGroup> findAllByTenantIdAndOperatorLinkedinIdAndStatus(Long tenantId, String operatorId, TrackingGroupStatus status, Pageable pageable);

    List<TalentTrackingLinkedinGroup> findAllByTenantIdAndOperatorLinkedinIdAndStatusOrderByCreatedDateDesc(Long tenantId, String operatorId, TrackingGroupStatus status);
}
