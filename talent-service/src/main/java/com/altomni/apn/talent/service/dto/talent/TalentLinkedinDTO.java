package com.altomni.apn.talent.service.dto.talent;

import cn.hutool.json.JSONArray;
import com.fasterxml.jackson.annotation.JsonInclude;
import liquibase.pro.packaged.S;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * A TalentLinkedinDTO.
 *
 * <AUTHOR>
 */
public interface TalentLinkedinDTO {

    Long getTalentContactId();

    Long getTalentId();

    String getLinkedinId();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    class TalentLinkedinRequest{
        private Long talentContactId;

        private Long talentId;

        private String linkedinId;

        private String company;

        private String title;
    }
}
