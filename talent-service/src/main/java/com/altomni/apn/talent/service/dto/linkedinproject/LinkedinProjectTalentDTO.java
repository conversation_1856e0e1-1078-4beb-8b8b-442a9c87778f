package com.altomni.apn.talent.service.dto.linkedinproject;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatusConverter;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatusConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.Objects;

@Data
public class LinkedinProjectTalentDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 321172203542826437L;

    private Long id;

    private Long linkedinProjectId;

    private String linkedinTalentId;

    @Convert(converter = CandidateStatusConverter.class)
    private CandidateStatus candidateStatus = CandidateStatus.ACTIVE;

    @Convert(converter = ContactStatusConverter.class)
    private ContactStatus contactStatus;

    private Boolean hasContactInfo = Boolean.FALSE;

    private Boolean isInApplication = Boolean.FALSE;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getLinkedinProjectId() {
        return linkedinProjectId;
    }

    public void setLinkedinProjectId(Long linkedinProjectId) {
        this.linkedinProjectId = linkedinProjectId;
    }

    public String getLinkedinTalentId() {
        return linkedinTalentId;
    }

    public void setLinkedinTalentId(String linkedinTalentId) {
        this.linkedinTalentId = linkedinTalentId;
    }

    public CandidateStatus getCandidateStatus() {
        return candidateStatus;
    }

    public void setCandidateStatus(CandidateStatus candidateStatus) {
        this.candidateStatus = candidateStatus;
    }

    public ContactStatus getContactStatus() {
        return contactStatus;
    }

    public void setContactStatus(ContactStatus contactStatus) {
        this.contactStatus = contactStatus;
    }

    public Boolean getHasContactInfo() { return hasContactInfo; }

    public void setHasContactInfo(Boolean hasContactInfo) { this.hasContactInfo = hasContactInfo; }

    public Boolean getInApplication() { return isInApplication; }

    public void setInApplication(Boolean inApplication) { isInApplication = inApplication; }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinProjectTalentDTO linkedinProjectTalent = (LinkedinProjectTalentDTO) o;
        if (linkedinProjectTalent.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinProjectTalent.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinProjectTalent{" +
            "id=" + id +
            ", linkedinProjectId=" + linkedinProjectId +
            ", linkedinTalentId='" + linkedinTalentId + '\'' +
            ", candidateStatus=" + candidateStatus +
            ", contactStatus=" + contactStatus +
            ", hasContactInfo=" + hasContactInfo +
            ", isInApplication=" + isInApplication +
            '}';
    }
}
