package com.altomni.apn.talent.web.rest.talent;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.talent.Resume;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;
import com.altomni.apn.common.dto.talent.TalentResumeOutput;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.talent.TalentResumeService;
import com.altomni.apn.talent.web.rest.talent.dto.GetTalentResumeByTalentIdDTO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing TalentResume.
 */
@Api(tags = {"Talent", "ATS-Candidates"})
@RestController
@RequestMapping("/api/v3")
public class TalentResumeResource {

    private final Logger log = LoggerFactory.getLogger(TalentResumeResource.class);

    private static final String ENTITY_NAME = "talentResume";

    @Resource
    private TalentResumeService talentResumeService;

    @Resource
    private EsFillerTalentService esFillerTalentService;

    /**
     * POST  /talent-resumes : Create a new talentResume.
     *
     * @param talentResumeDTO the talentResume to create
     * @return the ResponseEntity with status 201 (Created) and with body the new talentResume, or with status 400 (Bad Request) if the talentResume has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Create talent resume", notes = "The pdf/word version should already be saved in cloud provider")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping("/talent-resumes")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentResumeDTO> createTalentResume(@Valid @RequestBody TalentResumeDTO talentResumeDTO) throws URISyntaxException, IOException {
        log.info("[APN: TalentResume @{}] REST request to save TalentResume : {}", SecurityUtils.getUserId(), talentResumeDTO);
        TalentResumeDTO result = talentResumeService.createTalentResume(talentResumeDTO);
        return ResponseEntity.created(new URI("/api/v3/talent-resumes/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }


    /**
     * GET  /talent-resumes/:id : get the "id" talentResume.
     *
     * @param id the id of the talentResume to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the talentResume, or with status 404 (Not Found)
     */
    @ApiOperation(value = "Get resume by id")
    @GetMapping("/talent-resumes/{id}")
    @Timed
    public ResponseEntity<TalentResumeDTO> getTalentResume(@PathVariable Long id) {
        log.info("[APN: TalentResume @{}] REST request to get TalentResume : {}", SecurityUtils.getUserId(), id);
        TalentResumeDTO talentResume = talentResumeService.findOne(id);
        return ResponseEntity.ok(talentResume);
    }

    @GetMapping("/talent-resumes/relation/{relationId}")
    @Timed
    public ResponseEntity<TalentResumeDTO> getTalentResumeByTalentResumeRelationId(@PathVariable Long relationId) {
        log.info("[APN: TalentResume @{}] REST request to get TalentResume by talentResumeRelationId : {}", SecurityUtils.getUserId(), relationId);
        TalentResumeDTO talentResume = talentResumeService.findOneByTalentResumeRelationId(relationId);
        return ResponseEntity.ok(talentResume);
    }

    /**
     * GET  /talent-resumes/:id : get the "id" talentResume.
     *
     * @param talentId the id of the talentResume to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the talentResume, or with status 404 (Not Found)
     */
    @ApiOperation(value = "Get resume by id")
    @GetMapping("/talent-resumes/talent/{talentId}")
    @Timed
    public ResponseEntity<TalentResumeOutput> getTalentResumeByTalentId(@PathVariable String talentId, @RequestParam(value = "jobId", required = false) Long jobId) {
        log.info("[APN: TalentResume @{}] REST request to get TalentResume list by talent id: {}", SecurityUtils.getUserId(), talentId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentResumeService.findAllByTalentId(talentId, jobId)));
    }

    @ApiOperation(value = "Get resume by id（common db 候选人使用，因为id里有/ 无法使用路径传参）")
    @PostMapping("/talent-resumes/talent")
    @Timed
    public ResponseEntity<TalentResumeOutput> getTalentResumeByTalentId(@RequestBody GetTalentResumeByTalentIdDTO dto) {
        log.info("[APN: TalentResume @{}] REST request to get TalentResume list by talent id: {}", SecurityUtils.getUserId(), dto);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentResumeService.findAllByTalentId(dto.getTalentId(), dto.getJobId())));
    }

    /**
     * DELETE  /talent-resumes/:id : delete the "id" talentResume.
     *
     * @param id the id of the talentResume to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "Delete a resume")
    @DeleteMapping("/talent-resumes/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteTalentResume(@PathVariable Long id) throws IOException {
        log.info("[APN: TalentResume @{}] REST request to delete TalentResumeRelation : {}", SecurityUtils.getUserId(), id);
        talentResumeService.deleteTalentResume(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }



    /** ------------------------------------------- For Parser Use ONLY ------------------------------------------- */
    @GetMapping("/talent-resume/find-by-uuid-and-tenantId")
    @Timed
    public ResponseEntity<TalentResumeDTO> findByUuidAndTenantId(@RequestParam String uuid, @RequestParam Long tenantId) {
        log.info("[APN: TalentResume @{}] Request to find talent resume and convert to ParserResponse by uuid : {} and tenantId : {}", SecurityUtils.getUserId(), uuid, tenantId);
        return ResponseEntity.ok(talentResumeService.findByUuidAndTenantId(uuid, tenantId));
    }

    @PutMapping("/talent-resumes/saveResume")
    @Timed
    public ResponseEntity<Resume> saveResume(@RequestBody Resume resume) {
        log.info("[APN: TalentResume @{}] Request to createResume by resume : {}", SecurityUtils.getUserId(), resume);
        return ResponseEntity.ok(talentResumeService.saveResume(resume));
    }


    @ApiOperation(value = "Get resume by id")
    @GetMapping("/talent-resumes/talentId/{talentId}")
    @Timed
    public ResponseEntity<List<TalentResumeDTO>> getTalentResumeListByTalentId(@PathVariable Long talentId) {
        log.info("[APN: TalentResumeList @{}] REST request to get TalentResume list by talent id: {}", SecurityUtils.getUserId(), talentId);
        List<TalentResumeDTO> res = talentResumeService.findAllWithoutPortraitByTalentId(talentId);
        return ResponseEntity.ok(res);
    }

}
