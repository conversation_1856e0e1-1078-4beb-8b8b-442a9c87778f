package com.altomni.apn.talent.service.record;

import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.talent.service.dto.record.TalentTrackingEmailDTO;
import com.altomni.apn.talent.service.dto.record.TalentTrackingRecordDTO;
import com.altomni.apn.talent.service.dto.record.TalentTrackingRecordSearchDTO;
import com.altomni.apn.talent.service.vo.record.TalentTrackingRecordListVO;
import com.altomni.apn.talent.service.vo.record.TalentTrackingRecordVO;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface TalentTrackingRecordService {

    /** save talent tracking record
     * @param talentTrackingRecordDTO talent tracking record
     * @return talent tracking record
     */
    TalentTrackingRecordVO save(TalentTrackingRecordDTO talentTrackingRecordDTO);

    /**
     * save email tracking records
     * @param mailVm
     */
    void saveEmailTrackingRecord(MailVM mailVm);

    List<TalentTrackingRecordListVO> findAllTalentTrackingRecords(TalentTrackingRecordSearchDTO talentTrackingRecordSearchDTO);

    void sendRichEmail(TalentTrackingEmailDTO talentTrackingEmailDTO);
}
