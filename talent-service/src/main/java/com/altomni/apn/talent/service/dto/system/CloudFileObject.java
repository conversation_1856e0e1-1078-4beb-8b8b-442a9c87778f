package com.altomni.apn.talent.service.dto.system;

public class CloudFileObject {

    private byte[] content;

    private String contentType;

    private String fileName;

    public CloudFileObject() {}

    public CloudFileObject(byte[] content, String contentType, String fileName) {
        this.content = content;
        this.contentType = contentType;
        this.fileName = fileName;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    @Override
    public String toString() {
        return "CloudFileObject{" +
            "fileName='" + fileName + '\'' +
            ", contentType='" + contentType + '\'' +
            '}';
    }
}
