package com.altomni.apn.talent.repository.hotlist;

import com.altomni.apn.talent.domain.user.HotListTalent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the HotListTalent entity.
 */
@Repository
public interface HotListTalentRepository extends JpaRepository<HotListTalent, Long> {

    HotListTalent findOneByHotListIdAndTalentId(Long hotListId, Long userId);

    List<HotListTalent> findAllByHotListId(Long hotListId);

    List<HotListTalent> findAllByTalentId(Long talentId);

    void deleteAllByHotListId(Long hotListId);

    List<HotListTalent> findAllByHotListIdAndTalentId(Long hotListId, Long talentId);

    void deleteAllByHotListIdAndTalentIdIn(Long hotListId, List<Long> talentIds);

    Integer countByHotListId(Long hotListId);

    List<HotListTalent> findAllByHotListIdAndTalentIdIn(Long hotListId, List<Long> talentIds);
}
