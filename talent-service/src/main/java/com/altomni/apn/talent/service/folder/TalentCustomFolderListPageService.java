package com.altomni.apn.talent.service.folder;


import com.altomni.apn.common.dto.folder.FolderSearchRequestDTO;
import com.altomni.apn.common.dto.folder.ListPageFolderDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TalentCustomFolderListPageService {
    Page<ListPageFolderDTO> searchTalentFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable);
}
