package com.altomni.apn.talent.constants;

public class CnReportPDFFormat implements ReportPDFFormat{
    private static final String HEADER_TITLE = "候选人报告";
    private static final String HEADER_USER_NAME = "顾问：{}";
    private static final String HEADER_USER_PHONE = "电话： {}";
    private static final String HEADER_USER_EMAIL = "邮箱： {}";
    private static final String HEADER_CREATE_DATE = "创建日期： {}";
    private static final String JOB_RECOMMENDED = "职位推荐";
    private static final String RECOMMENDED_REASON = "推荐理由";
    private static final String TALENT_INFO = "基本信息";
    private static final String TALENT_NAME = "姓名：";
    private static final String TALENT_BIRTHDAY = "生日：";
    private static final String TALENT_GENDER = "性别：";
    private static final String CURRENT_LOCATION = "目前所在地：";
    private static final String PREFERRED_LOCATION = "意向所在地：";
    private static final String WORK_AUTHORIZATION = "工作许可：";
    private static final String TALENT_PHONE = "电话：";
    private static final String TALENT_EMAIL = "邮箱：";
    private static final String PREFERRED_SALARY = "意向薪资：";
    private static final String WORK_EXPERIENCE = "工作经历";
    private static final String PROJECT_EXPERIENCE = "项目经历";
    private static final String EDUCATION = "教育背景";
    private static final String SKILL = "工作技能";
    private static final String LANGUAGE = "语言";
    private static final String FOOTER = "机密：本报告仅为上述客户方准备，由于该报告涉及机密信息，仅限于客户方相关人士阅读。";

    @Override
    public String headerTitle() {
        return HEADER_TITLE;
    }

    @Override
    public String headerUserName() {
        return HEADER_USER_NAME;
    }

    @Override
    public String headerUserPhone() {
        return HEADER_USER_PHONE;
    }

    @Override
    public String headerUserEmail() {
        return HEADER_USER_EMAIL;
    }

    @Override
    public String headerCreateDate() {
        return HEADER_CREATE_DATE;
    }

    @Override
    public String jobRecommended() {
        return JOB_RECOMMENDED;
    }

    @Override
    public String recommendedReason() {
        return RECOMMENDED_REASON;
    }

    @Override
    public String talentInfo() {
        return TALENT_INFO;
    }

    @Override
    public String talentName() {
        return TALENT_NAME;
    }

    @Override
    public String talentBirthday() {
        return TALENT_BIRTHDAY;
    }

    @Override
    public String talentGender() {
        return TALENT_GENDER;
    }

    @Override
    public String currentLocation() {
        return CURRENT_LOCATION;
    }

    @Override
    public String preferredLocation() {
        return PREFERRED_LOCATION;
    }

    @Override
    public String workAuthorization() {
        return WORK_AUTHORIZATION;
    }

    @Override
    public String talentPhone() {
        return TALENT_PHONE;
    }

    @Override
    public String talentEmail() {
        return TALENT_EMAIL;
    }

    @Override
    public String preferredSalary() {
        return PREFERRED_SALARY;
    }

    @Override
    public String workExperience() {
        return WORK_EXPERIENCE;
    }

    @Override
    public String projectExperience() {
        return PROJECT_EXPERIENCE;
    }

    @Override
    public String education() {
        return EDUCATION;
    }

    @Override
    public String skill() {
        return SKILL;
    }

    @Override
    public String language() {
        return LANGUAGE;
    }

    @Override
    public String footer() {
        return FOOTER;
    }
}
