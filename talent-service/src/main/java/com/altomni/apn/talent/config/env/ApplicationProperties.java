package com.altomni.apn.talent.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.okHttpClient.connectionTimeout}")
    private Integer connectionTimeout;

    @Value("${application.crmUrl}")
    private String crmUrl;

    @Value("${application.okHttpClient.readTimeout}")
    private Integer readTimeout;

    @Value("${application.okHttpClient.connectionPoolSize}")
    private Integer connectionPoolSize;

    @Value("${application.okHttpClient.keepAliveDuration}")
    private Integer keepAliveDuration;

    @Value("${application.esfiller.maxNumberOfThreads}")
    private Integer maxNumberOfThreads;

    @Value("${application.commonService}")
    private String apnCommonServiceUrl;

    @Value("${application.esfiller.syncUrl}")
    private String esFillerSyncUrl;

    @Value("${application.esfiller.baseUrl}")
    private String esFillerBaseUrl;

    @Value("${application.esfiller.pauseSyncThreshold}")
    private Integer pauseSyncThreshold;

    @Value("${application.esfiller.larkWebhookKey}")
    private String larkWebhookKey;

    @Value("${application.esfiller.larkWebhookUrl}")
    private String larkWebhookUrl;

    @Value("${application.commonServicePin}")
    private String apnCommonServicePin;

    @Value("${application.statistic.url}")
    private String statisticUrl;

    @Value("${application.statistic.talentDetailRetrievingAuditApi:}")
    private String talentDetailRetrievingAuditApi;

    @Value("${application.statistic.searchTalentDetailRetrievingAuditApi:}")
    private String searchTalentDetailRetrievingAuditApi;

    @Value("${application.rater.url}")
    private String raterUrl;

    @Value("${application.rater.commonUrl}")
    private String commonUrl;

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.host-rater}")
    private String redisHostRater;

    @Value("${spring.redis.host-parser}")
    private String redisHostParser;

    @Value("${spring.redis.port}")
    private Integer redisPort;

    @Value("${spring.redis.port-rater}")
    private Integer redisPortRater;

    @Value("${spring.redis.port-parser}")
    private Integer redisPortParser;

    @Value("${spring.redis.database}")
    private Integer redisDb;

    @Value("${application.talent.similarity}")
    private BigDecimal similarity;

    @Value("${application.emailService.supportSender}")
    private String supportSender;

    @Value("${chat-service.recommend-reason-url}")
    private String recommendReasonUrl;

    @Value("${chat-service.auth-key}")
    private String authKey;

    @Value("${application.talent.searchKeywordHistory.maxSize:10}")
    private Integer searchHistoryMaxSize;

    @Value("${application.talent.searchKeywordHistory.expireTime:100}")
    private Integer searchHistoryExpireTime;

    @Value("${application.elasticrecord.url}")
    private String talentActivityESUrl;

    @Value("${application.aws.accessKey}")
    private String accessKey;

    @Value("${application.aws.secretKey}")
    private String secretKey;

    @Value("${application.address-list-sqs.region}")
    private String addressListSqsRegion;

    @Value("${application.address-list-sqs.queue}")
    private String addressListSqsQueue;

    @Value("${application.mainPath.baseUrl}")
    private String baseUrl;

    // 区分不同环境的redis可以,prod无需配置
    @Value("${excel.create_talent_by_excel_progress:create_talent_by_excel_progress:}")
    private String createTalentByExcelProgress;

    @Value("${excel.talent_excel_sql_retry_flag:talent_excel_sql_retry_flag}")
    private String talentExcelSqlRetryFlag;

    @Value("${excel.create_talent_by_excel_queue:create_talent_by_excel_queue}")
    private String createTalentByExcelQueue;

    @Value("${excel.create_talent_by_excel_node_queue:create_talent_by_excel_node_queue}")
    private String createTalentByExcelNodeQueue;

    @Value("${talent.profile.title-keywords}")
    private String titleKeywords;

    @Value("${publicSpringCloud.submitToJobPermissionKey}")
    private String submitToJobPermissionKey;

    @Value("${application.searchPermission.talent}")
    private String talentSearchAgencyPermissionUri;

    @Value("${public-statistic.monitor.contact.tenants:}")
    private String monitorContactForTenants;

    @Value("${public-statistic.monitor.contact.threshold:10}")
    private int monitorContactThreshold;

    @Value("${public-statistic.monitor.contact.url:}")
    private String monitorContactUrl;

    @Value("${public-statistic.monitor.contact.lark.enabled:false}")
    private boolean monitorEnabled;

    @Value("${public-statistic.monitor.contact.lark.webhookKey:}")
    private String monitorWebhookKey;

    @Value("${public-statistic.monitor.contact.lark.webhookUrl:}")
    private String monitorWebhookUrl;
}
