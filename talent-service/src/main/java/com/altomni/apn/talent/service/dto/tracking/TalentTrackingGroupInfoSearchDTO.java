package com.altomni.apn.talent.service.dto.tracking;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@ApiModel(description = "查询用户好友分组信息请求")
@Data
public class TalentTrackingGroupInfoSearchDTO {

    @ApiModelProperty(value = "领英ID列表，不传查当前操作领英用户的所有在分组中的领英好友的分组信息")
    private List<String> linkedinIdList;

    @ApiModelProperty(value = "当前操作者领英ID", required = true)
    @NotBlank(message = "当前操作者领英ID不能为空")
    private String operatorLinkedinId;
}