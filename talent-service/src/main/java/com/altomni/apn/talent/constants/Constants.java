package com.altomni.apn.talent.constants;

import com.google.common.net.MediaType;
import org.apache.commons.beanutils.converters.LongArrayConverter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Application constants.
 */
public final class Constants {

    /**
     * for third party jobs tenant
     */
    public static final int NOTE_MAX_LENGTH = 40000;

    /**
     * for third party jobs tenant
     */
    public static final Long INDIVIDUAL_TENANT_ID = 1L;

    /**
     * Add for Redis
     */
    public static final Integer ACTIVE = 1;

    public static final String EMAILY_XXL_JOB_TOKEN = "EMAILY-XXL-JOB-TOKEN";

    /**
     * Add for parser
     */
    public static final int REDIS_EXPIRE_TIME = 3600 * 360; //360 hours
    public static final List<String> CONTENT_TYPE_APPLICATION_JSON = new ArrayList<>(Arrays.asList("application/json;charset=UTF-8","application/json; charset=utf-8"));
    public static final String CONTENT_TYPE_WPS_WRITER = "application/wps-writer";
    public static final List<String> CONTENT_TYPE_OCTET_STREAM= new ArrayList<>(Arrays.asList("application/octet-stream","binary/octet-stream"));
    public static final String USER_METADATA_HAS_DISPLAY = "has_display";
    public static final String USER_METADATA_HAS_PORTRAIT = "has_portrait";
    public static final String USER_METADATA_N_PAGES = "n_pages";

    public static String fileSuffixToContentType(String suffix){
        switch (suffix){
            case "txt" :
                return MediaType.PLAIN_TEXT_UTF_8.toString();
            case "diff" :
            case "patch" :
                return "text/x-diff; charset=utf-8";
            case "csv" :
                return "application/csv";
            case "pdf" :
                return "application/pdf";
            case "docx" :
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "zip" :
                return "application/zip";
            case "doc" :
            case "dot" :
                return "application/msword";
            case "rtf" :
                return "text/rtf; charset=utf-8";
            case "dotx" :
                return "application/vnd.openxmlformats-officedocument.wordprocessingml.template";
            case "htm" :
            case "html" :
                return "text/html; charset=utf-8";
            case "odt" :
                return "application/vnd.oasis.opendocument.text";
            case "eml" :
            case "mht" :
                return "message/rfc822";
            case "ppt" :
                return "application/vnd.ms-powerpoint";
            case "pptx" :
                return "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "jpg" :
            case "jpeg" :
                return "image/jpeg";
            case "png" :
                return "image/png";
            case "bmp" :
                return "image/bmp";
            default:
                return null;
        }
    }


    /**
     * intellipro group domain name
     */
    public static final String INTELLIPRO_GROUP_COM = "@intelliprogroup.com";

    public final static String DEFAULT_ZERO_CODE = "ZERO";

    public final static String DEFAULT_ZERO_DESC = "0";

    /*
    -1L means that current talent is sharing with all users. special case;
     */
    public final static Long TALENT_OWNERSHIP_SHARE_WITH_ALL_USER_ID = -1L;

    private Constants() { }
}
