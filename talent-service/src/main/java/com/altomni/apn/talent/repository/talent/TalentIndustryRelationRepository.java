package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.dict.TalentIndustryRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface TalentIndustryRelationRepository extends JpaRepository<TalentIndustryRelation, Long> {

    @Modifying
    @Transactional
    @Query(value = "delete from talent_industry_relation where talent_id = ?1", nativeQuery = true)
    void deleteAllByTalentId(Long talentId);

}