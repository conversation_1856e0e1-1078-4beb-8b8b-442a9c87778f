package com.altomni.apn.talent.service.dto.linkedinproject;

import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus;

import java.io.Serializable;
import java.util.Objects;

public class LinkedinProjectTalentProjectVM implements Serializable {

    private static final long serialVersionUID = 7106828367122085120L;

    private Long id;

    private String name;

    private ContactStatus contactStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ContactStatus getContactStatus() {
        return contactStatus;
    }

    public void setContactStatus(ContactStatus contactStatus) {
        this.contactStatus = contactStatus;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinProjectTalentProjectVM linkedinProjectTalent = (LinkedinProjectTalentProjectVM) o;
        if (linkedinProjectTalent.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinProjectTalent.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinProjectTalentProjectVM{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", contactStatus=" + contactStatus +
            '}';
    }
}
