package com.altomni.apn.talent.service.jobdiva;

import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

@Component
@FeignClient(value = "jobdiva-service")
public interface JobdivaService {

    @GetMapping("/jobdiva/api/v3/onboarding/timesheet-user/talent/{talentId}")
    ResponseEntity<TimeSheetUserDTO> getTimeSheetUser(@PathVariable("talentId") Long talentId);

    @PostMapping("/jobdiva/api/v3/onboarding/timesheet-user/save")
    ResponseEntity<Boolean> saveTimeSheetUser(@RequestBody TimeSheetUserDTO timeSheetUser);

    @PostMapping("/jobdiva/api/v3/timesheet/user/findByUsernameOrEmailList")
    ResponseEntity<List<TimeSheetUserDTO>> findByUsernameOrEmailList(@RequestBody Set<String> emailList);

}
