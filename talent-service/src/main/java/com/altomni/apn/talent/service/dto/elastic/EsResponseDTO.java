package com.altomni.apn.talent.service.dto.elastic;

import com.altomni.apn.common.dto.talent.TalentDTOV3;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.data.domain.Page;

import java.io.Serializable;

public class EsResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ES status")
    private String status;

    @ApiModelProperty(value = "Redis uuid")
    private String uuid;

    @ApiModelProperty(value = "ES data")
    private Page<TalentDTOV3> page;


    public String getStatus() {
        return status;
    }

    public EsResponseDTO status(String status) {
        this.status = status;
        return this;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUuid() { return uuid; }

    public EsResponseDTO uuid(String uuid) {
        this.uuid = uuid;
        return this;
    }

    public void setUuid(String uuid) { this.uuid = uuid; }

    public Page<TalentDTOV3> getPage() {
        return page;
    }

    public void setPage(Page<TalentDTOV3> page) {
        this.page = page;
    }

    public EsResponseDTO page(Page<TalentDTOV3> page) {
        this.page = page;
        return this;
    }

    @Override
    public String toString() {
        return "EsResponseDTO{" +
            "status='" + status + '\'' +
            ", uuid='" + uuid + '\'' +
            ", page=" + page +
            '}';
    }
}
