package com.altomni.apn.talent.domain.folder;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.common.enumeration.folder.FolderPermissionConverter;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@Table(name = "talent_folder_sharing_user")
public class TalentFolderSharingUser extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_folder_id", nullable = false)
    private Long talentFolderId;


    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Convert(converter = FolderPermissionConverter.class)
    @Column(name = "permission", nullable = false)
    private FolderPermission permission;

    public boolean hasWritePermission(Long userId){
        return  this.userId.equals(userId) && this.permission.equals(FolderPermission.READWRITE);

    }
    public boolean hasReadPermission(){
        return (this.permission.toDbValue() & 0x6) != 0;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentFolderId() {
        return talentFolderId;
    }

    public void setTalentFolderId(Long talentFolderId) {
        this.talentFolderId = talentFolderId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public FolderPermission getPermission() {
        return permission;
    }

    public void setPermission(FolderPermission permission) {
        this.permission = permission;
    }

    public boolean DoesShareWithOwner(Long userId){
        return this.userId.equals(userId);
    }

    public boolean isSameFolderSharing(TalentFolderSharingUser talentFolderSharingUser) {
        return this.userId.equals(talentFolderSharingUser.getUserId())
                && this.talentFolderId.equals(talentFolderSharingUser.getTalentFolderId())
                && this.permission.equals(talentFolderSharingUser.getPermission());
    }
}
