package com.altomni.apn.talent.service.feedback.impl;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.statistics.JobTalentRecommendFeedback;
import com.altomni.apn.talent.repository.statistics.JobTalentRecommendFeedbackRepository;
import com.altomni.apn.talent.service.feedback.RecommendFeedbackService;
import com.altomni.apn.talent.web.rest.talent.dto.RecommendFeedbackDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class RecommendFeedbackServiceImpl implements RecommendFeedbackService {

    private Logger log = LoggerFactory.getLogger(RecommendFeedbackServiceImpl.class);

    @Resource
    private JobTalentRecommendFeedbackRepository feedbackRepository;

    @Override
    public void recommendFeedback(RecommendFeedbackDTO recommendFeedbackDTO) {
        JobTalentRecommendFeedback jobTalentRecommendFeedback = new JobTalentRecommendFeedback();
        jobTalentRecommendFeedback.setJobId(recommendFeedbackDTO.getJobId());
        jobTalentRecommendFeedback.setTalentId(recommendFeedbackDTO.getTalentId());
        jobTalentRecommendFeedback.setTenantId(SecurityUtils.getTenantId());
        jobTalentRecommendFeedback.setNote(recommendFeedbackDTO.getNote());
        List<String> reason = recommendFeedbackDTO.getReason();
        String result = Optional.ofNullable(reason)
                .map(l -> l.stream()
                        .sorted()
                        .collect(Collectors.joining(",")))
                .orElse(null);  // 如果list为null返回空字符串
        jobTalentRecommendFeedback.setReason(result);
        feedbackRepository.save(jobTalentRecommendFeedback);
    }
}
