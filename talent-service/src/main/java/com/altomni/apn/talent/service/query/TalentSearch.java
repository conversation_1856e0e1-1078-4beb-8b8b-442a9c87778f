package com.altomni.apn.talent.service.query;

import com.altomni.apn.common.domain.talent.QTalentV3;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.talent.QWatchList;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.JPAExpressions;


public class TalentSearch {

    public static BooleanExpression talents(String search) {
        QTalentV3 qTalent = QTalentV3.talentV3;
        PathBuilder<TalentV3> entityPath = new PathBuilder<>(TalentV3.class, "talentV3");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);

        BooleanExpression byTenant = qTalent.tenantId.eq(SecurityUtils.getTenantId());
        return byTenant.and(builder.build(entityPath));
    }

    public static BooleanExpression candidates(String search) {
        QTalentV3 qTalent = QTalentV3.talentV3;
        PathBuilder<TalentV3> entityPath = new PathBuilder<>(TalentV3.class, "talentV3");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);

        BooleanExpression byTenant = qTalent.tenantId.eq(SecurityUtils.getTenantId());
        return byTenant.and(builder.build(entityPath));
    }

//TODO    public static BooleanExpression myCandidates(String search) {
//        QTalentV3 qTalent = QTalentV3.talentV3;
//        PathBuilder<TalentV3> entityPath = new PathBuilder<>(TalentV3.class, "talentV3");
//        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);
//        BooleanExpression baseCondition = qTalent.tenantId.eq(SecurityUtils.getTenantId());
//        QActivityUnique qActivityUnique = QActivityUnique.activityUnique;
//        BooleanExpression isMyCandidate = qTalent.id.in(JPAExpressions.selectFrom(qActivityUnique)
//            .where(qActivityUnique.userId.eq(SecurityUtils.getUserId())).select(qActivityUnique.talentId))
//            .or(qTalent.id.in(JPAExpressions.selectFrom(qTalent)
//                .where(qTalent.createdBy.eq(SecurityUtils.getCurrentUserLogin().get().getUsername())).select(qTalent.id))
//        );
//        return baseCondition.and(isMyCandidate).and(builder.build(entityPath));
//    }

    public static BooleanExpression watchlist(String search) {
        QTalentV3 qTalent = QTalentV3.talentV3;
        PathBuilder<TalentV3> entityPath = new PathBuilder<>(TalentV3.class, "talentV3");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);

        QWatchList qWatchList = QWatchList.watchList;
        BooleanExpression myWatchList = qTalent.id.in(JPAExpressions.selectFrom(qWatchList)
            .where(qWatchList.userId.eq(SecurityUtils.getUserId())).select(qWatchList.talentId));
        return myWatchList.and(builder.build(entityPath));
    }

    public static BooleanExpression allWatchlist(String search) {
        QTalentV3 qTalent = QTalentV3.talentV3;
        PathBuilder<TalentV3> entityPath = new PathBuilder<>(TalentV3.class, "talentV3");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);
        QWatchList qWatchList = QWatchList.watchList;
        BooleanExpression myWatchList = qTalent.id.in(JPAExpressions.selectFrom(qWatchList)
            .where(qWatchList.userId.eq(SecurityUtils.getUserId())).select(qWatchList.talentId).orderBy(qWatchList.createdDate.desc()));
        return myWatchList.and(builder.build(entityPath));
    }

    /**
     * Return my watching talents just for the job
     * @param search
     * @param jobId
     * @return
     */
    public static BooleanExpression myWatchingTalentsForJob(String search, Long jobId) {
        QTalentV3 qTalent = QTalentV3.talentV3;
        PathBuilder<TalentV3> entityPath = new PathBuilder<>(TalentV3.class, "talentV3");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);
        QWatchList qWatchList = QWatchList.watchList;
        BooleanExpression myWatchListByJob = qTalent.id.in(JPAExpressions.selectFrom(qWatchList)
            .where(qWatchList.userId.eq(SecurityUtils.getUserId()).and(qWatchList.jobId.eq(jobId))).select(qWatchList.talentId));
        return myWatchListByJob.and(builder.build(entityPath));
    }

//TODO    public static BooleanExpression hireList(String search) {
//        QTalentV3 qTalent = QTalentV3.talentV3;
//        PathBuilder<TalentV3> entityPath = new PathBuilder<>(TalentV3.class, "talentV3");
//        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);
//
//        QApplication qApplication = QApplication.application;
//        BooleanExpression myHireList = qTalent.id.in(JPAExpressions.selectFrom(qApplication)
//            .where(qApplication.userId.eq(SecurityUtils.getUserId()).and(qApplication.status.eq(ActivityStatus.Started)))
//            .select(qApplication.talentId));
//        return myHireList.and(builder.build(entityPath));
//    }

    public static BooleanExpression publicList(String search) {
        QTalentV3 qTalent = QTalentV3.talentV3;
        PathBuilder<TalentV3> entityPath = new PathBuilder<>(TalentV3.class, "talentV3");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);

        BooleanExpression byTenant = qTalent.tenantId.eq(SecurityUtils.getTenantId());
        return byTenant.and(builder.build(entityPath));
    }
}
