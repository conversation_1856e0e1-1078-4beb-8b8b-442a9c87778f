package com.altomni.apn.talent.service.linkedinproject;

import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.talent.service.dto.linkedinproject.Filter;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectDTO;
import com.altomni.apn.talent.service.dto.linkedinproject.SearchHistory;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinProjectVO;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;

public interface LinkedInProjectService {

    HttpResponse saveFilters(Filter filter) throws IOException;

    HttpResponse saveSearchHistory(SearchHistory searchHistory) throws IOException;

    HttpResponse getFilters() throws IOException;

    HttpResponse deleteFilterById(String id) throws IOException;

    HttpResponse getSearchHistories() throws IOException;

    HttpResponse getSearchHistoryById(String id) throws IOException ;

    HttpResponse deleteSearchHistoryById(String id) throws IOException;

    LinkedinProjectVO saveLinkedinProject(LinkedinProjectDTO linkedinProjectDto);

    Page<LinkedinProjectVO> findAll(BooleanExpression allLinkedinProject, Pageable pageable);

    LinkedinProjectVO updateLinkedinProject(LinkedinProjectDTO linkedinProjectDto);

    LinkedinProjectVO getLinkedinProjectById(Long id);

    LinkedinProjectVO favoriteLinkedinProject(Long id);

    LinkedinProjectVO unFavoriteLinkedinProject(Long id);
}
