package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TrackingCategory enumeration.
 */
public enum TrackingCategory implements ConvertedEnum<Integer> {
    PAGE_ADDED(10),
    FROM_LINK(20);


    private final Integer dbValue;

    TrackingCategory(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TrackingCategory, Integer> resolver =
        new ReverseEnumResolver<>(TrackingCategory.class, TrackingCategory::toDbValue);

    public static TrackingCategory fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
