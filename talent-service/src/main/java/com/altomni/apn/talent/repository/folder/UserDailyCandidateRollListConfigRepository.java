package com.altomni.apn.talent.repository.folder;


import com.altomni.apn.talent.domain.folder.UserDailyCandidateRollListConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserDailyCandidateRollListConfigRepository extends JpaRepository<UserDailyCandidateRollListConfig, Long> {

    List<UserDailyCandidateRollListConfig> findAllByUserIdIn(List<Long> userId);
    List<UserDailyCandidateRollListConfig> findAllByUserIdIs(Long userId);

    List<UserDailyCandidateRollListConfig> findAllByTalentSearchFolderIdInAndUserIdIs(List<Long> folderIds, Long userId);
    UserDailyCandidateRollListConfig findAllByTalentSearchFolderIdIsAndUserIdIs(Long folderId, Long userId);

    void deleteByUserIdIsAndTalentSearchFolderIdIs(Long userId, Long talentSearchFolderId);

    @Modifying
    @Query(nativeQuery = true,
            value = "delete c from user_daily_candidate_roll_list_config c " +
                    "left join user u on c.user_id = u.id " +
                    "left join permission_user_team put on put.user_id = c.user_id " +
                    "where put.is_primary = 1 " +
                    "and c.talent_search_folder_id = :folderId " +
                    "and u.id in (:userIds) ")
    void deleteByFolderIdAndUserIds(
            @Param("folderId") Long folderId,
            @Param("userIds") List<Long> userIds);

    @Modifying
    @Query(nativeQuery = true,
            value = "delete c from user_daily_candidate_roll_list_config c " +
                    "left join user u on c.user_id = u.id " +
                    "left join permission_user_team put on put.user_id = c.user_id " +
                    "where put.is_primary = 1 " +
                    "and c.talent_search_folder_id = :folderId " +
                    "and put.team_id in (:teamIds)")
    void deleteByFolderIdAndTeamIds(
            @Param("folderId") Long folderId,
            @Param("teamIds") List<Long> teamIds);


    @Modifying
    @Query(nativeQuery = true,
            value = "delete c from user_daily_candidate_roll_list_config c " +
                    "left join user u on c.user_id = u.id " +
                    "left join permission_user_team put on put.user_id = c.user_id " +
                    "where put.is_primary = 1 " +
                    "and c.talent_search_folder_id = :folderId " +
                    "and put.team_id in (:teamIds)" +
                    "and u.id not in (:userIds)")
    void deleteByFolderIdAndTeamIds(
            @Param("folderId") Long folderId,
            @Param("teamIds") List<Long> teamIds,
            @Param("userIds") List<Long> userIds);
}
