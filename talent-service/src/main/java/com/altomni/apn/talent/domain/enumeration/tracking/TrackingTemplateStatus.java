package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TrackingCategory enumeration.
 */
public enum TrackingTemplateStatus implements ConvertedEnum<Integer> {
    IN_ACTIVE(10),
    ACTIVE(20);


    private final Integer dbValue;

    TrackingTemplateStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TrackingTemplateStatus, Integer> resolver =
        new ReverseEnumResolver<>(TrackingTemplateStatus.class, TrackingTemplateStatus::toDbValue);

    public static TrackingTemplateStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
