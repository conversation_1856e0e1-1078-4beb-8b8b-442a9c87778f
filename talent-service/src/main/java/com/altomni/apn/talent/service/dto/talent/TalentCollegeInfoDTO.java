package com.altomni.apn.talent.service.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentCollegeInfoDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    private String englishCollegeName;

    private String chineseCollegeName;

}


