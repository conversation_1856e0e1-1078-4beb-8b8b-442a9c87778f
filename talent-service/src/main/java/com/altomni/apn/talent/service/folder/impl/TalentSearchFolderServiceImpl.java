package com.altomni.apn.talent.service.folder.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.folder.FolderListDTO;
import com.altomni.apn.common.dto.folder.TeamUserSetRollList;
import com.altomni.apn.common.dto.search.*;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.WithDataException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.enumeration.folder.TalentSearchCategory;
import com.altomni.apn.talent.domain.folder.*;
import com.altomni.apn.talent.repository.folder.*;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.dto.folder.LastFollowUpByChange;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderDTO;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderSharingTeamDTO;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderSharingUserDTO;
import com.altomni.apn.talent.service.folder.TalentCustomFolderService;
import com.altomni.apn.talent.service.folder.TalentSearchFolderService;
import com.altomni.apn.talent.service.mapper.folder.TalentSearchFolderMapper;
import com.altomni.apn.talent.service.talent.TalentServiceV3;
import com.altomni.apn.talent.utils.SearchCriteriaConverter;
import com.altomni.apn.talent.web.rest.talent.dto.GetDailyCandidateRollListDTO;
import com.altomni.apn.talent.web.rest.talent.dto.GetDailyCandidateRollListVO;
import com.altomni.apn.talent.web.rest.talent.dto.TalentSearchFolderData;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TalentSearchFolderServiceImpl implements TalentSearchFolderService {
    final static String NAME_COLUMN = "f.name";
    final static String CREATED_DATE_COLUMN = "f.created_date";

    @Resource
    TalentSearchFolderRepository talentSearchFolderRepository;


    @Resource
    TalentCustomFolderService talentCustomFolderService;

    @Resource
    TalentSearchFolderMapper talentSearchFolderMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Resource
    CachePermission cachePermission;

    @Resource
    ApplicationProperties applicationProperties;

    @Override
    @Transactional
    public TalentSearchFolderDTO createTalentSearchFolder(TalentSearchFolderDTO talentSearchFolderDTO, String rollListJump) {
        if (talentSearchFolderDTO == null || StringUtils.isEmpty(talentSearchFolderDTO.getName())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTCUSTOM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        if(CollUtil.isEmpty(talentSearchFolderDTO.getOwnerUserList()) && CollUtil.isEmpty(talentSearchFolderDTO.getOwnerTeamList())) {
            throw new CustomParameterizedException("Owner is not empty!");
        }
        if(!checkOwnerShareRepeat(talentSearchFolderDTO)) {
            throw new CustomParameterizedException("The same user/team cannot be both share and owner at the same time.");
        }

        validateCustomFolderAsSearchFolderParam(talentSearchFolderDTO);

        TalentSearchFolder talentSearchFolder = talentSearchFolderMapper.toEntity(talentSearchFolderDTO);

        talentSearchFolder.setTenantId(SecurityUtils.getTenantId());
        talentSearchFolder.setActive(true);
        setSharingTeam(talentSearchFolder, talentSearchFolderDTO);
        setSharingUser(talentSearchFolder, talentSearchFolderDTO);
        setOwnerUser(talentSearchFolder, talentSearchFolderDTO);
        setOwnerTeam(talentSearchFolder, talentSearchFolderDTO);
        replaceLastFollowUpBy(talentSearchFolder, talentSearchFolderDTO.getUserFilterUpdate());
        removeJumpAdditionalParam(talentSearchFolder, rollListJump);

        //check sourceAgency search permission
        checkSourceAgencySearchPermission(talentSearchFolder);

        talentSearchFolder = talentSearchFolderRepository.save(talentSearchFolder);
        addRollListConfig(talentSearchFolder);
        return getTalentSearchFolderDTO(talentSearchFolder);
    }

    private void checkSourceAgencySearchPermission(TalentSearchFolder talentSearchFolder) {
        String searchStr = talentSearchFolder.getSearchCriteria();
        Pattern pattern = Pattern.compile("\"key\"\\s*:\\s*\"sourceAgency\"");
        Matcher matcher = pattern.matcher(searchStr);
        if (matcher.find()) {
            List<Long> userIds = getUserIdsByOwnerAndShare(talentSearchFolder.getOwnerUser(), talentSearchFolder.getSharingUser(), talentSearchFolder.getOwnerTeam(), talentSearchFolder.getSharingTeam());
            List<Long> noPermissionUserIds = new ArrayList<>();
            for (Long userId : userIds) {
                if (!cachePermission.hasUserPrivilegePermission(userId, applicationProperties.getTalentSearchAgencyPermissionUri())) {
                    noPermissionUserIds.add(userId);
                }
            }

            if (!noPermissionUserIds.isEmpty()) {
                throw new WithDataException("The users in the list don't have the permission to search source agency!", cn.hutool.http.Status.HTTP_PRECON_FAILED, noPermissionUserIds);
            }
        }
    }

    private void setOwnerTeam(TalentSearchFolder talentSearchFolder, TalentSearchFolderDTO talentSearchFolderDTO) {
        List<TalentSearchFolderSharingTeamDTO> ownerTeamList = talentSearchFolderDTO.getOwnerTeamList();
        if(ownerTeamList != null) {
            Set<TalentSearchFolderOwnerTeam> teamSet = ownerTeamList.stream().map(f -> {
                TalentSearchFolderOwnerTeam sharingTeam = new TalentSearchFolderOwnerTeam();
                sharingTeam.setTeamId(f.getTeamId());
                Set<Long> excludedUserIds = f.getExcludedUserIds();
                if(excludedUserIds == null) {
                    excludedUserIds = new HashSet<>();
                }
                sharingTeam.setExcludedUserIds(JSONUtil.toJsonStr(excludedUserIds));
                return sharingTeam;
            }).collect(Collectors.toSet());
            talentSearchFolder.setOwnerTeam(teamSet);
        }
    }

    private boolean checkOwnerShareRepeat(TalentSearchFolderDTO talentSearchFolderDTO) {
        List<TalentSearchFolderSharingUserDTO> ownerUserList = talentSearchFolderDTO.getOwnerUserList();
        List<TalentSearchFolderSharingTeamDTO> ownerTeamList = talentSearchFolderDTO.getOwnerTeamList();
        List<TalentSearchFolderSharingUserDTO> sharingUserList = talentSearchFolderDTO.getSharingUserList();
        List<TalentSearchFolderSharingTeamDTO> sharingTeamList = talentSearchFolderDTO.getSharingTeamList();
        // 检查用户是否重复
        if (CollUtil.isNotEmpty(ownerUserList) && CollUtil.isNotEmpty(sharingUserList)) {
            Set<Long> ownerUserIds = ownerUserList.stream()
                    .map(TalentSearchFolderSharingUserDTO::getUserId)
                    .collect(Collectors.toSet());

            boolean hasUserRepeat = sharingUserList.stream()
                    .map(TalentSearchFolderSharingUserDTO::getUserId)
                    .anyMatch(ownerUserIds::contains);

            if (hasUserRepeat) {
                return true;
            }
        }

        // 检查团队是否重复
        if (CollUtil.isNotEmpty(ownerTeamList) && CollUtil.isNotEmpty(sharingTeamList)) {
            Set<Long> ownerTeamIds = ownerTeamList.stream()
                    .map(TalentSearchFolderSharingTeamDTO::getTeamId)
                    .collect(Collectors.toSet());

            boolean hasTeamRepeat = sharingTeamList.stream()
                    .map(TalentSearchFolderSharingTeamDTO::getTeamId)
                    .anyMatch(ownerTeamIds::contains);

            return hasTeamRepeat;
        }

        return true;
    }

    public static final int rollListConfigMax = 10;

    private void addRollListConfig(TalentSearchFolder talentSearchFolder) {
        if(TalentSearchCategory.DATABASECANDIDATES.equals(talentSearchFolder.getSearchCategory())) {
            return;
        }
        List<Long> userIds = getUserIdsByOwnerAndShare(talentSearchFolder.getOwnerUser(), talentSearchFolder.getSharingUser(), talentSearchFolder.getOwnerTeam(), talentSearchFolder.getSharingTeam());
        addRollListConfigByUserIds(userIds, talentSearchFolder.getId());
    }

    private List<Long> getUserIdsByOwnerAndShare(Set<TalentSearchFolderOwnerUser> ownerUserList, Set<TalentSearchFolderSharingUser> shareUserList, Set<TalentSearchFolderOwnerTeam> ownerTeamList, Set<TalentSearchFolderSharingTeam> shareTeamList) {
        Set<Long> userIds = new HashSet<>();
        if(ownerUserList != null) {
            userIds.addAll(ownerUserList.stream().map(TalentSearchFolderOwnerUser::getUserId).collect(Collectors.toSet()));
        }
        if(shareUserList != null) {
            userIds.addAll(shareUserList.stream().map(TalentSearchFolderSharingUser::getUserId).collect(Collectors.toSet()));
        }

        Set<Long> teamIds = new HashSet<>();
        if(ownerTeamList != null) {
            teamIds.addAll(ownerTeamList.stream().map(TalentSearchFolderOwnerTeam::getTeamId).collect(Collectors.toSet()));
        }
        if(shareTeamList != null) {
            teamIds.addAll(shareTeamList.stream().map(TalentSearchFolderSharingTeam::getTeamId).collect(Collectors.toSet()));
        }
        if(!teamIds.isEmpty()) {
            PermissionTeamMemberSearchVM searchVM = new PermissionTeamMemberSearchVM();
            searchVM.setTeamIds(teamIds);
            List<PermissionTeamUserDTO> teamUserDTOList = userService.getTeamUsersByPermissionTeamIdIn(searchVM).getBody();
            if(teamUserDTOList != null) {
                userIds.addAll(teamUserDTOList.stream().map(PermissionTeamUserDTO::getId).collect(Collectors.toSet()));
            }
        }
        return new ArrayList<>(userIds);
    }

    private void addRollListConfigByUserIds(List<Long> ids, Long folderId) {
        if(ids.isEmpty() || folderId == null) {
            return;
        }
        Map<Long, List<UserDailyCandidateRollListConfig>> userRollListConfig = userDailyCandidateRollListConfigRepository.findAllByUserIdIn(ids).stream()
                .collect(Collectors.groupingBy(UserDailyCandidateRollListConfig::getUserId));
        for(Long userId : ids) {
            List<UserDailyCandidateRollListConfig> configList = userRollListConfig.get(userId);
            if(configList == null || configList.size() < rollListConfigMax) {
                if(existTalentSearchFolder(folderId, configList)) {
                    continue;
                }
                UserDailyCandidateRollListConfig newConfig = new UserDailyCandidateRollListConfig();
                newConfig.setUserId(userId);
                newConfig.setTalentSearchFolderId(folderId);
                userDailyCandidateRollListConfigRepository.save(newConfig);
                commonRedisService.delete(getDailyCandidateRollListRedisKey(userId));
            } else {
                setSearchFolderUpdate(userId);
            }
        }
    }

    private boolean existTalentSearchFolder(Long folderId, List<UserDailyCandidateRollListConfig> configList) {
        if(configList == null) {
            return false;
        }
        for(UserDailyCandidateRollListConfig config : configList) {
            if(folderId.equals(config.getTalentSearchFolderId())) {
                return true;
            }
        }
        return false;
    }

    private void removeJumpAdditionalParam(TalentSearchFolder talentSearchFolder, String rollListJump) {
        TalentSearchFolderConditionDTO searchCriteria = SearchCriteriaConverter.fromJson(talentSearchFolder.getSearchCriteria());
        List<SearchParam> filter = searchCriteria.getFilter();
        if(filter == null) {
            return;
        }
        for(SearchParam searchParam : filter) {
            List<ConditionParam> condition = searchParam.getCondition();
            if(CollUtil.isNotEmpty(condition) && rollListJump != null) {
                condition.removeIf(p -> {
                    if("NEWLY".equals(rollListJump)) {
                        return "createdDate".equals(p.getKey());
                    } else if ("PENDING_REVIEW".equals(rollListJump)) {
                        return "pendingReview".equals(p.getKey()) || "reviewInfo".equals(p.getKey());
                    }
                    return false;
                });
            }
        }
        talentSearchFolder.setSearchCriteria(JSONUtil.toJsonStr(searchCriteria));
    }

    private void replaceLastFollowUpBy(TalentSearchFolder talentSearchFolder, LastFollowUpByChange userFilterUpdate) {
        if(userFilterUpdate == null) {
            return;
        }
        //responsibility5  创建人 CreatedBy
        //applications.responsibility0  流程提交人 ProcessSubmittedBy
        //notes.responsibility5  备注创建人 NotesCreatedBy
        //notes.responsibility0  备注编辑人 NotesEditedBy
        List<String> key = List.of("responsibility5", "applications.responsibility0", "notes.responsibility5", "notes.responsibility0");
        TalentSearchFolderConditionDTO conditionDTO = SearchCriteriaConverter.fromJson(talentSearchFolder.getSearchCriteria());
        List<ComplexSearchParam> search = conditionDTO.getSearch();
        if(search != null) {
            search.forEach(searchParam -> {
                if (searchParam.getCondition() != null) {
                    processConditions(searchParam.getCondition(), key, userFilterUpdate);
                }
            });
        }
        talentSearchFolder.setSearchCriteria(JSONUtil.toJsonStr(conditionDTO));
    }

    private void processConditions(List<SearchCondition> conditions, List<String> key, LastFollowUpByChange userFilterUpdate) {
        for (SearchCondition condition : conditions) {
            if (condition instanceof ComplexSearchParam) {
                ComplexSearchParam complexParam = (ComplexSearchParam) condition;
                if (complexParam.getCondition() != null) {
                    processConditions(complexParam.getCondition(), key, userFilterUpdate);
                }
            } else if (condition instanceof ConditionParam) {
                ConditionParam conditionParam = (ConditionParam) condition;
                if (key.contains(conditionParam.getKey())) {
                    replaceConditionParam(conditionParam, userFilterUpdate);
                }
            }
        }
    }

    private void replaceConditionParam(ConditionParam conditionParam, LastFollowUpByChange userFilterUpdate) {
        JSONObject value = JSONUtil.parseObj(conditionParam.getValue());
        JSONObject data = value.getJSONObject("data");
        if(CollUtil.isNotEmpty(userFilterUpdate.getUserId())) {
            data.put("userId", userFilterUpdate.getUserId().stream().map(String::valueOf).toList());
        } else {
            data.put("userId", new ArrayList<>());
        }
        if (CollUtil.isNotEmpty(userFilterUpdate.getTeamId())) {
            data.put("teamId", userFilterUpdate.getTeamId().stream().map(String::valueOf).toList());
        } else {
            data.put("teamId", new ArrayList<>());
        }
        conditionParam.setValue(value);
    }

    private void setOwnerUser(TalentSearchFolder talentSearchFolder, TalentSearchFolderDTO talentSearchFolderDTO) {
        List<TalentSearchFolderSharingUserDTO> ownerUserList = talentSearchFolderDTO.getOwnerUserList();
        if(ownerUserList != null) {
            Set<TalentSearchFolderOwnerUser> userSet = ownerUserList.stream().map(f -> {
                TalentSearchFolderOwnerUser sharingUser = new TalentSearchFolderOwnerUser();
                sharingUser.setUserId(f.getUserId());
                return sharingUser;
            }).collect(Collectors.toSet());
            talentSearchFolder.setOwnerUser(userSet);
        }
    }

    private void setSharingUser(TalentSearchFolder talentSearchFolder, TalentSearchFolderDTO talentSearchFolderDTO) {
        List<TalentSearchFolderSharingUserDTO> sharingUserList = talentSearchFolderDTO.getSharingUserList();
        if(sharingUserList != null) {
            Set<TalentSearchFolderSharingUser> userSet = sharingUserList.stream().map(f -> {
                TalentSearchFolderSharingUser sharingUser = new TalentSearchFolderSharingUser();
                sharingUser.setUserId(f.getUserId());
                return sharingUser;
            }).collect(Collectors.toSet());
            talentSearchFolder.setSharingUser(userSet);
        }
    }

    private void setSharingTeam(TalentSearchFolder talentSearchFolder, TalentSearchFolderDTO talentSearchFolderDTO) {
        List<TalentSearchFolderSharingTeamDTO> sharingTeamList = talentSearchFolderDTO.getSharingTeamList();
        if(sharingTeamList != null) {
            Set<TalentSearchFolderSharingTeam> teamSet = sharingTeamList.stream().map(f -> {
                TalentSearchFolderSharingTeam sharingTeam = new TalentSearchFolderSharingTeam();
                sharingTeam.setTeamId(f.getTeamId());
                Set<Long> excludedUserIds = f.getExcludedUserIds();
                if(excludedUserIds == null) {
                    excludedUserIds = new HashSet<>();
                }
                sharingTeam.setExcludedUserIds(JSONUtil.toJsonStr(excludedUserIds));
                return sharingTeam;
            }).collect(Collectors.toSet());
            talentSearchFolder.setSharingTeam(teamSet);
        }
    }

    private TalentSearchFolderDTO getTalentSearchFolderDTO(TalentSearchFolder talentSearchFolder) {
        TalentSearchFolderDTO dto = talentSearchFolderMapper.toDto(talentSearchFolder);
        Set<TalentSearchFolderSharingTeam> sharingTeam = talentSearchFolder.getSharingTeam();
        if(sharingTeam != null) {
            List<TalentSearchFolderSharingTeamDTO> teamList = sharingTeam.stream().map(f -> {
                TalentSearchFolderSharingTeamDTO team = new TalentSearchFolderSharingTeamDTO();
                team.setTeamId(f.getTeamId());
                return team;
            }).collect(Collectors.toList());
            dto.setSharingTeamList(teamList);
        }
        Set<TalentSearchFolderSharingUser> sharingUser = talentSearchFolder.getSharingUser();
        if(sharingUser != null) {
            List<TalentSearchFolderSharingUserDTO> userlist = sharingUser.stream().map(f -> {
                TalentSearchFolderSharingUserDTO user = new TalentSearchFolderSharingUserDTO();
                user.setUserId(f.getUserId());
                return user;
            }).collect(Collectors.toList());
            dto.setSharingUserList(userlist);
        }
        Set<TalentSearchFolderOwnerUser> ownerUser = talentSearchFolder.getOwnerUser();
        if(ownerUser != null) {
            List<TalentSearchFolderSharingUserDTO> userlist = ownerUser.stream().map(f -> {
                TalentSearchFolderSharingUserDTO user = new TalentSearchFolderSharingUserDTO();
                user.setUserId(f.getUserId());
                return user;
            }).collect(Collectors.toList());
            dto.setOwnerUserList(userlist);
        }
        Set<TalentSearchFolderOwnerTeam> ownerTeam = talentSearchFolder.getOwnerTeam();
        if(ownerTeam != null) {
            List<TalentSearchFolderSharingTeamDTO> teamList = ownerTeam.stream().map(f -> {
                TalentSearchFolderSharingTeamDTO team = new TalentSearchFolderSharingTeamDTO();
                team.setTeamId(f.getTeamId());
                return team;
            }).collect(Collectors.toList());
            dto.setOwnerTeamList(teamList);
        }
        return dto;
    }

    private void validateCustomFolderAsSearchFolderParam(TalentSearchFolderDTO talentSearchFolderDTO){
        if (talentSearchFolderDTO.getTalentFolderId() != null) {
            FolderListDTO folderListDTO = talentCustomFolderService.getCustomAndSharedTalentFolderList();

            boolean folderExistsInMyFolders = folderListDTO.getMyFolderList()
                    .stream()
                    .anyMatch(folder -> folder.getId().equals(talentSearchFolderDTO.getTalentFolderId()));
            boolean folderExistsInSharedFolders = folderListDTO.getSharedFolderList()
                    .stream()
                    .anyMatch(folder -> folder.getId().equals(talentSearchFolderDTO.getTalentFolderId()));
            if (!(folderExistsInMyFolders || folderExistsInSharedFolders)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_VALIDATECUSTOMFOLDERASSEARCHFOLDERPARAM_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
            }
        }
    }



    @Override
    @Transactional
    public TalentSearchFolderDTO updateTalentSearchFolder(Long id, TalentSearchFolderDTO talentSearchFolderDTO) {
        TalentSearchFolder talentSearchFolder = talentSearchFolderRepository.findById(id).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_GETSEARCHFOLDERBYID_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService())));
        if(CollUtil.isEmpty(talentSearchFolderDTO.getOwnerUserList()) && CollUtil.isEmpty(talentSearchFolderDTO.getOwnerTeamList())) {
            throw new CustomParameterizedException("Owner is not empty!");
        }
        if(!checkEditPermission(talentSearchFolder)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_VALIDATECUSTOMFOLDERASSEARCHFOLDERPARAM_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        if(!checkOwnerShareRepeat(talentSearchFolderDTO)) {
            throw new CustomParameterizedException("The same user/team cannot be both share and owner at the same time.");
        }
        talentSearchFolder.setName(talentSearchFolderDTO.getName());
        talentSearchFolder.setFolderNote(talentSearchFolderDTO.getFolderNote());
        updateFolderPermissions(id, talentSearchFolder, talentSearchFolderDTO);
        replaceLastFollowUpBy(talentSearchFolder, talentSearchFolderDTO.getUserFilterUpdate());

        //check sourceAgency search permission
        checkSourceAgencySearchPermission(talentSearchFolder);

        talentSearchFolder = talentSearchFolderRepository.saveAndFlush(talentSearchFolder);
        commonRedisService.delete(getDailyCandidateRollListRedisKey(SecurityUtils.getUserId()));
        return getTalentSearchFolderDTO(talentSearchFolder);
    }

    private void updateFolderPermissions(Long id, TalentSearchFolder talentSearchFolder, TalentSearchFolderDTO talentSearchFolderDTO) {
        List<Long> removeTeamIds = new ArrayList<>();
        List<Long> removeUserIds = new ArrayList<>();
        List<Long> addTeamIds = new ArrayList<>();
        List<Long> addUserIds = new ArrayList<>();
        updateSharingTeam(talentSearchFolder, talentSearchFolderDTO, removeTeamIds, addTeamIds);
        updateSharingUser(talentSearchFolder, talentSearchFolderDTO, removeUserIds, addUserIds);
        updateOwnerTeam(talentSearchFolder, talentSearchFolderDTO, removeTeamIds, addTeamIds);
        updateOwnerUser(talentSearchFolder, talentSearchFolderDTO, removeUserIds, addUserIds);
        if(TalentSearchCategory.DATABASECANDIDATES.equals(talentSearchFolderDTO.getSearchCategory())) {
            return;
        }
        List<Long> existTeamIds = new ArrayList<>();
        if(CollUtil.isNotEmpty(talentSearchFolderDTO.getOwnerTeamList())) {
            existTeamIds.addAll(talentSearchFolderDTO.getOwnerTeamList().stream().map(TalentSearchFolderSharingTeamDTO::getTeamId).collect(Collectors.toList()));
        }
        if(CollUtil.isNotEmpty(talentSearchFolderDTO.getSharingTeamList())) {
            existTeamIds.addAll(talentSearchFolderDTO.getSharingTeamList().stream().map(TalentSearchFolderSharingTeamDTO::getTeamId).collect(Collectors.toList()));
        }
        List<Long> existUserIds = new ArrayList<>();
        if(CollUtil.isNotEmpty(talentSearchFolderDTO.getOwnerUserList())) {
            existUserIds.addAll(talentSearchFolderDTO.getOwnerUserList().stream().map(TalentSearchFolderSharingUserDTO::getUserId).collect(Collectors.toList()));
        }
        if(CollUtil.isNotEmpty(talentSearchFolderDTO.getSharingUserList())) {
            existUserIds.addAll(talentSearchFolderDTO.getSharingUserList().stream().map(TalentSearchFolderSharingUserDTO::getUserId).collect(Collectors.toList()));
        }
        if(!removeUserIds.isEmpty()) {
            filterRemoveUserIdsByExistTeamId(removeUserIds, existTeamIds);
            userDailyCandidateRollListConfigRepository.deleteByFolderIdAndUserIds(id, removeUserIds);
            for(Long userId : removeUserIds) {
                commonRedisService.delete(getDailyCandidateRollListRedisKey(userId));
            }
        }
        if(!removeTeamIds.isEmpty()) {
            if(existUserIds.isEmpty()) {
                userDailyCandidateRollListConfigRepository.deleteByFolderIdAndTeamIds(id, removeTeamIds);
            } else {
                userDailyCandidateRollListConfigRepository.deleteByFolderIdAndTeamIds(id, removeTeamIds, existUserIds);
            }
            PermissionTeamMemberSearchVM permissionTeamMemberSearchVM = new PermissionTeamMemberSearchVM();
            permissionTeamMemberSearchVM.setTeamIds(new HashSet<>(removeTeamIds));
            List<PermissionTeamUserDTO> userDTOList = userService.getTeamUsersByPermissionTeamIdIn(permissionTeamMemberSearchVM).getBody();
            if(CollUtil.isNotEmpty(userDTOList)) {
                for(PermissionTeamUserDTO permissionTeamUserDTO : userDTOList) {
                    if(!existUserIds.contains(permissionTeamUserDTO.getId())) {
                        commonRedisService.delete(getDailyCandidateRollListRedisKey(permissionTeamUserDTO.getId()));
                    }
                }
            }
        }
        addRollListByUserIdAndTeamId(addUserIds, addTeamIds, id);
    }

    private void filterRemoveUserIdsByExistTeamId(List<Long> removeUserIds, List<Long> existTeamIds) {
        PermissionTeamMemberSearchVM input = new PermissionTeamMemberSearchVM();
        input.setTeamIds(new HashSet<>(existTeamIds));
        List<PermissionTeamUserDTO> body = userService.getTeamUsersByPermissionTeamIdIn(input).getBody();
        if(CollUtil.isNotEmpty(body)) {
            body.stream().map(PermissionTeamUserDTO::getId).forEach(removeUserIds::remove);
        }
    }

    private void addRollListByUserIdAndTeamId(List<Long> addUserIds, List<Long> addTeamIds, Long id) {
        Set<Long> userIds = new HashSet<>(addUserIds);
        if(!addTeamIds.isEmpty()) {
            PermissionTeamMemberSearchVM searchVM = new PermissionTeamMemberSearchVM();
            searchVM.setTeamIds(new HashSet<>(addTeamIds));
            List<PermissionTeamUserDTO> teamUserDTOList = userService.getTeamUsersByPermissionTeamIdIn(searchVM).getBody();
            if(teamUserDTOList != null) {
                userIds.addAll(teamUserDTOList.stream().map(PermissionTeamUserDTO::getId).collect(Collectors.toSet()));
            }
        }
        addRollListConfigByUserIds(new ArrayList<>(userIds), id);
    }

    private void updateOwnerTeam(TalentSearchFolder talentSearchFolder, TalentSearchFolderDTO talentSearchFolderDTO, List<Long> removeTeamIds, List<Long> addTeamIds) {
        List<TalentSearchFolderSharingTeamDTO> newPermissions = talentSearchFolderDTO.getOwnerTeamList();
        Set<TalentSearchFolderOwnerTeam> permissions = talentSearchFolder.getOwnerTeam();
        Iterator<TalentSearchFolderOwnerTeam> iter = permissions.iterator();
        while(iter.hasNext()) {
            TalentSearchFolderOwnerTeam next = iter.next();
            if(!containPermission(newPermissions, next)) {
                removeTeamIds.add(next.getTeamId());
                iter.remove();
            }
        }
        for(TalentSearchFolderSharingTeamDTO info : newPermissions) {
            if(!ownerTeamContainPermission(permissions, info)) {
                TalentSearchFolderOwnerTeam p = new TalentSearchFolderOwnerTeam();
                p.setTeamId(info.getTeamId());
                p.setExcludedUserIds(JSONUtil.toJsonStr(new HashSet<>()));
                permissions.add(p);
                addTeamIds.add(info.getTeamId());
            }
        }
        talentSearchFolder.setOwnerTeam(permissions);
    }

    private void updateOwnerUser(TalentSearchFolder talentSearchFolder, TalentSearchFolderDTO talentSearchFolderDTO, List<Long> removeUserIds, List<Long> addUserIds) {
        List<TalentSearchFolderSharingUserDTO> newOwnerUserPermissions = talentSearchFolderDTO.getOwnerUserList();
        Set<TalentSearchFolderOwnerUser> ownerUserPermissions = talentSearchFolder.getOwnerUser();
        Iterator<TalentSearchFolderOwnerUser> ownerUserIter = ownerUserPermissions.iterator();
        while(ownerUserIter.hasNext()) {
            TalentSearchFolderOwnerUser next = ownerUserIter.next();
            if(!containOwnerUserPermission(newOwnerUserPermissions, next)) {
                removeUserIds.add(next.getUserId());
                ownerUserIter.remove();
            }
        }
        for(TalentSearchFolderSharingUserDTO info : newOwnerUserPermissions) {
            if(!containOwnerPermission(ownerUserPermissions, info)) {
                TalentSearchFolderOwnerUser p = new TalentSearchFolderOwnerUser();
                p.setUserId(info.getUserId());
                ownerUserPermissions.add(p);
                addUserIds.add(info.getUserId());
            }
        }
        talentSearchFolder.setOwnerUser(ownerUserPermissions);
    }

    private void updateSharingUser(TalentSearchFolder talentSearchFolder, TalentSearchFolderDTO talentSearchFolderDTO, List<Long> removeUserIds, List<Long> addUserIds) {
        List<TalentSearchFolderSharingUserDTO> newUserPermissions = talentSearchFolderDTO.getSharingUserList();
        Set<TalentSearchFolderSharingUser> userPermissions = talentSearchFolder.getSharingUser();
        Iterator<TalentSearchFolderSharingUser> userIter = userPermissions.iterator();
        while(userIter.hasNext()) {
            TalentSearchFolderSharingUser next = userIter.next();
            if(!containUserPermission(newUserPermissions, next)) {
                removeUserIds.add(next.getUserId());
                userIter.remove();
            }
        }
        for(TalentSearchFolderSharingUserDTO info : newUserPermissions) {
            if(!containPermission(userPermissions, info)) {
                TalentSearchFolderSharingUser p = new TalentSearchFolderSharingUser();
                p.setUserId(info.getUserId());
                userPermissions.add(p);
                addUserIds.add(info.getUserId());
            }
        }
        talentSearchFolder.setSharingUser(userPermissions);
    }

    private void updateSharingTeam(TalentSearchFolder talentSearchFolder, TalentSearchFolderDTO talentSearchFolderDTO, List<Long> removeTeamIds, List<Long> addTeamIds) {
        List<TalentSearchFolderSharingTeamDTO> newPermissions = talentSearchFolderDTO.getSharingTeamList();
        Set<TalentSearchFolderSharingTeam> permissions = talentSearchFolder.getSharingTeam();
        Iterator<TalentSearchFolderSharingTeam> iter = permissions.iterator();
        while(iter.hasNext()) {
            TalentSearchFolderSharingTeam next = iter.next();
            if(!containPermission(newPermissions, next)) {
                removeTeamIds.add(next.getTeamId());
                iter.remove();
            }
        }
        for(TalentSearchFolderSharingTeamDTO info : newPermissions) {
            if(!containPermission(permissions, info)) {
                TalentSearchFolderSharingTeam p = new TalentSearchFolderSharingTeam();
                p.setTeamId(info.getTeamId());
                p.setExcludedUserIds(JSONUtil.toJsonStr(new HashSet<>()));
                permissions.add(p);
                addTeamIds.add(info.getTeamId());
            }
        }
        talentSearchFolder.setSharingTeam(permissions);
    }

    private boolean containPermission(Set<TalentSearchFolderSharingUser> newPermissions, TalentSearchFolderSharingUserDTO info) {
        for(TalentSearchFolderSharingUser permission : newPermissions) {
            if(permission.getUserId().equals(info.getUserId())) {
                return true;
            }
        }
        return false;
    }

    private boolean containOwnerPermission(Set<TalentSearchFolderOwnerUser> newPermissions, TalentSearchFolderSharingUserDTO info) {
        for(TalentSearchFolderOwnerUser permission : newPermissions) {
            if(permission.getUserId().equals(info.getUserId())) {
                return true;
            }
        }
        return false;
    }

    private boolean containOwnerUserPermission(List<TalentSearchFolderSharingUserDTO> newUserPermissions, TalentSearchFolderOwnerUser info) {
        for(TalentSearchFolderSharingUserDTO permission : newUserPermissions) {
            if(permission.getUserId().equals(info.getUserId())) {
                return true;
            }
        }
        return false;
    }

    private boolean containUserPermission(List<TalentSearchFolderSharingUserDTO> newUserPermissions, TalentSearchFolderSharingUser info) {
        for(TalentSearchFolderSharingUserDTO permission : newUserPermissions) {
            if(permission.getUserId().equals(info.getUserId())) {
                return true;
            }
        }
        return false;
    }


    private boolean containPermission(List<TalentSearchFolderSharingTeamDTO> permissions, TalentSearchFolderOwnerTeam info) {
        for(TalentSearchFolderSharingTeamDTO permission : permissions) {
            if(permission.getTeamId().equals(info.getTeamId())) {
                return true;
            }
        }
        return false;
    }

    private boolean containPermission(List<TalentSearchFolderSharingTeamDTO> permissions, TalentSearchFolderSharingTeam info) {
        for(TalentSearchFolderSharingTeamDTO permission : permissions) {
            if(permission.getTeamId().equals(info.getTeamId())) {
                return true;
            }
        }
        return false;
    }

    private boolean ownerTeamContainPermission(Set<TalentSearchFolderOwnerTeam> newPermissions, TalentSearchFolderSharingTeamDTO next) {
        for(TalentSearchFolderOwnerTeam permission : newPermissions) {
            if(permission.getTeamId().equals(next.getTeamId())) {
                return true;
            }
        }
        return false;
    }

    private boolean containPermission(Set<TalentSearchFolderSharingTeam> newPermissions, TalentSearchFolderSharingTeamDTO next) {
        for(TalentSearchFolderSharingTeam permission : newPermissions) {
            if(permission.getTeamId().equals(next.getTeamId())) {
                return true;
            }
        }
        return false;
    }

    private boolean checkEditPermission(TalentSearchFolder talentSearchFolder) {
        boolean check = false;
        Set<TalentSearchFolderOwnerUser> ownerUser = talentSearchFolder.getOwnerUser();
        check |= checkOwnerUser(ownerUser);
        Set<TalentSearchFolderOwnerTeam> ownerTeam = talentSearchFolder.getOwnerTeam();
        check |= checkOwnerTeam(ownerTeam);
        return check;
    }

    private boolean checkOwnerUser(Set<TalentSearchFolderOwnerUser> ownerUser) {
        if(ownerUser != null) {
            List<Long> userIds = ownerUser.stream().map(TalentSearchFolderOwnerUser::getUserId).toList();
            return userIds.contains(SecurityUtils.getUserId());
        }
        return false;
    }

    private boolean checkOwnerTeam(Set<TalentSearchFolderOwnerTeam> ownerTeam) {
        if(ownerTeam != null) {
            List<Long> teamIds = ownerTeam.stream().map(TalentSearchFolderOwnerTeam::getTeamId).toList();
            return teamIds.contains(SecurityUtils.getTeamId());
        }
        return false;
    }

    @Override
    @Transactional
    public void deleteTalentSearchFolder(Long folderId) {
        if (folderId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_FOLDERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        TalentSearchFolder talentSearchFolder = talentSearchFolderRepository.findById(folderId).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_GETSEARCHFOLDERBYID_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService())));;

        if(checkEditPermission(talentSearchFolder)) {
            talentSearchFolderRepository.delete(talentSearchFolder);
        } else {
            Set<TalentSearchFolderSharingUser> sharingUser = talentSearchFolder.getSharingUser();
            sharingUser.removeIf(next -> next.getUserId().equals(SecurityUtils.getUserId()));
            Set<TalentSearchFolderSharingTeam> sharingTeam = talentSearchFolder.getSharingTeam();
            sharingTeam.forEach(c -> {
                if(c.getTeamId().equals(SecurityUtils.getTeamId())) {
                    JSONArray jsonArray = JSONUtil.parseArray(c.getExcludedUserIds());
                    List<Long> list = jsonArray.toList(Long.class);
                    if(!list.contains(SecurityUtils.getUserId())) {
                        list.add(SecurityUtils.getUserId());
                    }
                    c.setExcludedUserIds(JSONUtil.toJsonStr(new HashSet<>(list)));
                }
            });

            talentSearchFolderRepository.save(talentSearchFolder);
        }
        userDailyCandidateRollListConfigRepository.deleteByUserIdIsAndTalentSearchFolderIdIs(SecurityUtils.getUserId(), talentSearchFolder.getId());
        commonRedisService.delete(getDailyCandidateRollListRedisKey(SecurityUtils.getUserId()));
    }

    @Override
    public TalentSearchFolderDTO getTalentSearchFolder(Long folderId, boolean checkUserFilter, String rollListJump) {
        if (folderId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_FOLDERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        TalentSearchFolder talentSearchFolder = talentSearchFolderRepository.findById(folderId).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_GETSEARCHFOLDERBYID_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService())));;
        if(!checkFolderPermission(talentSearchFolder)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_GETSEARCHFOLDERBYID_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        TalentSearchFolderDTO talentSearchFolderDTO = getTalentSearchFolderDTO(talentSearchFolder);
        if(checkUserFilter) {
            talentSearchFolderDTO.setExistUserFilter(getExistUserFiler(talentSearchFolder.getSearchCriteria()));
        }
        rollListJumpAddFilter(folderId, talentSearchFolderDTO, rollListJump);
        return talentSearchFolderDTO;
    }

    private void rollListJumpAddFilter(Long folderId, TalentSearchFolderDTO talentSearchFolderDTO, String rollListJump) {
        TalentSearchFolderConditionDTO searchCriteria = talentSearchFolderDTO.getSearchCriteria();
        List<SearchParam> filter = searchCriteria.getFilter();
        if(filter == null) {
            filter = new ArrayList<>();
        }
        if("NEWLY".equals(rollListJump)) {
            //从新增候选人跳转
            UserDailyCandidateRollListConfig config = userDailyCandidateRollListConfigRepository.findAllByTalentSearchFolderIdIsAndUserIdIs(folderId, SecurityUtils.getUserId());
            List<SearchParam> lastModifiedParamCondition = getCreatedDateParamCondition(config);
            if(lastModifiedParamCondition != null) {
                filter.addAll(lastModifiedParamCondition);
            }
        } else if ("PENDING_REVIEW".equals(rollListJump)) {
            //从待审查跳转
            filter.addAll(getPendingReviewParamCondition());
        }
        searchCriteria.setFilter(filter);
    }

    private List<String> getExistUserFiler(String searchCriteria) {
        //responsibility5  创建人 CreatedBy
        //applications.responsibility0  流程提交人 ProcessSubmittedBy
        //notes.responsibility5  备注创建人 NotesCreatedBy
        //notes.responsibility0  备注编辑人 NotesEditedBy
        List<String> ret = new ArrayList<>();
        TalentSearchConditionDTO conditionDTO = SearchCriteriaConverter.fromJson(searchCriteria);
        List<ComplexSearchParam> search = conditionDTO.getSearch();

        if (search != null) {
            search.forEach(searchParam -> {
                if (searchParam.getCondition() != null) {
                    getExistUserFilerProcessConditions(searchParam.getCondition(), ret);
                }
            });
        }
        return ret;
    }

    private void getExistUserFilerProcessConditions(List<SearchCondition> conditions, List<String> ret) {
        for (SearchCondition condition : conditions) {
            if (condition instanceof ComplexSearchParam) {
                ComplexSearchParam complexParam = (ComplexSearchParam) condition;
                if (complexParam.getCondition() != null) {
                    getExistUserFilerProcessConditions(complexParam.getCondition(), ret);
                }
            } else if (condition instanceof ConditionParam) {
                ConditionParam conditionParam = (ConditionParam) condition;
                switch (conditionParam.getKey()) {
                    case "responsibility5":
                        ret.add("CreatedBy");
                        break;
                    case "applications.responsibility0":
                        ret.add("ProcessSubmittedBy");
                        break;
                    case "notes.responsibility5":
                        ret.add("NotesCreatedBy");
                        break;
                    case "notes.responsibility0":
                        ret.add("NotesEditedBy");
                        break;
                }
            }
        }
    }

    @Override
    public TalentSearchFolderDTO updateTalentSearchFolderCondition(Long folderId, TalentSearchFolderDTO talentSearchFolderDTO, String rollListJump) {
        TalentSearchFolder talentSearchFolder = talentSearchFolderRepository.findById(folderId).orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_GETSEARCHFOLDERBYID_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService())));
        if(!checkEditPermission(talentSearchFolder)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.FOLDER_TALENTSEARCH_VALIDATECUSTOMFOLDERASSEARCHFOLDERPARAM_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        talentSearchFolder.setSearchCategory(talentSearchFolderDTO.getSearchCategory());
        talentSearchFolder.setSearchCriteria(TalentSearchFolderMapper.searchCriteriaToJsonString(talentSearchFolderDTO.getSearchCriteria()));
        talentSearchFolder.setTalentFolderId(talentSearchFolderDTO.getTalentFolderId());
        replaceLastFollowUpBy(talentSearchFolder, talentSearchFolderDTO.getUserFilterUpdate());
        removeJumpAdditionalParam(talentSearchFolder, rollListJump);

        //check sourceAgency search permission
        checkSourceAgencySearchPermission(talentSearchFolder);

        talentSearchFolder = talentSearchFolderRepository.saveAndFlush(talentSearchFolder);
        commonRedisService.delete(getDailyCandidateRollListRedisKey(SecurityUtils.getUserId()));
        return getTalentSearchFolderDTO(talentSearchFolder);
    }

    @Resource
    private UserDailyCandidateRollListConfigRepository userDailyCandidateRollListConfigRepository;

//    @Resource
//    private TalentSearchFolderCache talentSearchFolderCache;

    @Override
    public List<TalentSearchFolderData> getDailyCandidateRollListConfig() {
        List<Long> displayFolderId = userDailyCandidateRollListConfigRepository.findAllByUserIdIs(SecurityUtils.getUserId()).stream().map(UserDailyCandidateRollListConfig::getTalentSearchFolderId).distinct().toList();

        List<TalentSearchFolder> userTalentSearchFolder = talentSearchFolderRepository.findUserTalentSearchFolder(SecurityUtils.getUserId(), SecurityUtils.getTeamId(), true);
        return userTalentSearchFolder.stream().filter(p -> {
            //排除数据库搜索条件及share文件夹中被删除的
            Set<TalentSearchFolderSharingTeam> sharingTeam = p.getSharingTeam();
            if(sharingTeam == null) {
                sharingTeam = new HashSet<>();
            }
            List<Long> excludedUserIds = sharingTeam.stream()
                    .map(s -> JSONUtil.parseArray(s.getExcludedUserIds()).toList(Long.class))
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            return !TalentSearchCategory.DATABASECANDIDATES.equals(p.getSearchCategory()) && !excludedUserIds.contains(SecurityUtils.getUserId());
        }).map(u -> {
            TalentSearchFolderData data = new TalentSearchFolderData();
            data.setId(u.getId());
            data.setName(u.getName());
            data.setDisplay(displayFolderId.contains(u.getId()));

            return data;
        }).toList();
    }

    @Override
    public void updateDailyCandidateRollList(GetDailyCandidateRollListDTO input) {
        if(input.getSearchFolderIds().size() > rollListConfigMax) {
            throw new CustomParameterizedException("Exceeding the maximum value on the roll list.");
        }
        Long currentUserId = SecurityUtils.getUserId();
        List<UserDailyCandidateRollListConfig> existingConfigs =
                userDailyCandidateRollListConfigRepository.findAllByUserIdIs(currentUserId);

        // 将现有配置转换为Set便于比较
        Set<Long> existingFolderIds = existingConfigs.stream()
                .map(UserDailyCandidateRollListConfig::getTalentSearchFolderId)
                .collect(Collectors.toSet());

        // 将入参转换为Set便于比较
        Set<Long> newFolderIds = new HashSet<>(input.getSearchFolderIds());
//        Set<Long> newFolderIds = new HashSet<>(input.getSearchFolderIds()).stream().filter(f -> {
//            return talentSearchFolderCache.getCandidateCount(f) < 10000;
//        }).collect(Collectors.toSet());

        // 需要删除的配置（在现有配置中但不在新列表中）
        List<UserDailyCandidateRollListConfig> configsToDelete = existingConfigs.stream()
                .filter(config -> !newFolderIds.contains(config.getTalentSearchFolderId()))
                .collect(Collectors.toList());

        // 需要新增的folderId（在新列表中但不在现有配置中）
        List<UserDailyCandidateRollListConfig> configsToAdd = newFolderIds.stream()
                .filter(folderId -> !existingFolderIds.contains(folderId))
                .map(folderId -> {
                    UserDailyCandidateRollListConfig config = new UserDailyCandidateRollListConfig();
                    config.setUserId(currentUserId);
                    config.setTalentSearchFolderId(folderId);
                    return config;
                })
                .collect(Collectors.toList());

        // 执行删除操作
        if (!configsToDelete.isEmpty()) {
            userDailyCandidateRollListConfigRepository.deleteAll(configsToDelete);
        }

        // 执行新增操作
        if (!configsToAdd.isEmpty()) {
            userDailyCandidateRollListConfigRepository.saveAll(configsToAdd);

        }
        if(!configsToDelete.isEmpty() || !configsToAdd.isEmpty()) {
            getDailyCandidateRollList(true);
        }
    }

    private void setSearchFolderUpdate(Long userId) {
        String data = commonRedisService.get(getDailyCandidateRollListRedisKey(userId));
        if(data != null) {
            GetDailyCandidateRollListVO bean = JSONUtil.toBean(data, GetDailyCandidateRollListVO.class);
            bean.setSearchFolderUpdate(true);
            saveDailyCandiateRolllistToRedis(bean, userId);
        }
    }

    @Resource
    private TalentServiceV3 talentServiceV3;

    @Override
    public Long countCandidatesInFolder(Long folderId) {
        TalentSearchFolder talentSearchFolder = talentSearchFolderRepository.findById(folderId).orElse(null);
        if(talentSearchFolder != null) {
            HttpHeaders headers = new HttpHeaders();
            headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count", "Pagination-Owned-Data-Count", "Pagination-Not-Owned-Data-Count"));
            TalentSearchConditionDTO conditionDTO = SearchCriteriaConverter.fromJson(talentSearchFolder.getSearchCriteria());

            try {
                talentServiceV3.searchTalentFromES(conditionDTO, PageRequest.of(1,1), headers, false);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            String count = headers.getFirst("Pagination-Count");
            if(count != null) {
                return Long.valueOf(count);
            }
        }
        return 0L;
    }



    @Override
    public GetDailyCandidateRollListVO getDailyCandidateRollList(boolean refresh) {
        List<UserDailyCandidateRollListConfig> userRollListConfig = userDailyCandidateRollListConfigRepository.findAllByUserIdIs(SecurityUtils.getUserId());
        List<Long> folderIds = userRollListConfig.stream().map(UserDailyCandidateRollListConfig::getTalentSearchFolderId).distinct().toList();
        GetDailyCandidateRollListVO ret;
        if(refresh) {
            ret = getDailyCandidateRollListFromEs(folderIds, SecurityUtils.getUserId());
        } else {
            ret = getDailyCandidateRollListFromRedis(SecurityUtils.getUserId());
            if(ret == null) {
                ret = getDailyCandidateRollListFromEs(folderIds, SecurityUtils.getUserId());
            }
        }
        List<TalentSearchFolderData> list = ret.getDailyCandidateRollList();
        //根据新增人数倒序排序
        list.sort((a, b) -> {
            // 首先按照新增人数倒序
            int countA = Integer.parseInt(a.getNewlyCount());
            int countB = Integer.parseInt(b.getNewlyCount());
            if (countB != countA) {
                return countB - countA;
            }
            // 如果新增人数相同，则按照创建时间倒序
            return b.getCreatedDate().compareTo(a.getCreatedDate());
        });
        return ret;
    }

    @Resource
    private UserService userService;

    @Resource
    private TalentSearchFolderOwnerUserRepository ownerUserRepository;
    @Resource
    private TalentSearchFolderOwnerTeamRepository ownerTeamRepository;

    @Resource
    private TalentSearchFolderSharingUserRepository sharingUserRepository;

    @Resource
    private TalentSearchFolderSharingTeamRepository sharingTeamRepository;

    @Override
    @Transactional
    public void talentSearchFolderSetDefaultRollList() {
        log.info("begin set default roll list.");
        if(!SecurityUtils.isAdmin()) {
            log.info("begin set default roll list. account not admin.");
        }
        Set<Long> userIds = new HashSet<>();
        userIds.addAll(ownerUserRepository.findAll().stream().map(TalentSearchFolderOwnerUser::getUserId).collect(Collectors.toSet()));
        userIds.addAll(sharingUserRepository.findAll().stream().map(TalentSearchFolderSharingUser::getUserId).collect(Collectors.toSet()));

        Map<Long, Long> userTeam = new HashMap<>();
        for(Long userId : userIds) {
            userTeam.put(userId, -1L);
        }
        Set<Long> teamIds = new HashSet<>();
        teamIds.addAll(ownerTeamRepository.findAll().stream().map(TalentSearchFolderOwnerTeam::getTeamId).collect(Collectors.toSet()));
        teamIds.addAll(sharingTeamRepository.findAll().stream().map(TalentSearchFolderSharingTeam::getTeamId).collect(Collectors.toSet()));
        PermissionTeamMemberSearchVM searchVM = new PermissionTeamMemberSearchVM();
        searchVM.setTeamIds(teamIds);
        List<PermissionTeamUserDTO> teamUserDTOList = userService.getTeamUsersByPermissionTeamIdIn(searchVM).getBody();
        if(CollUtil.isNotEmpty(teamUserDTOList)) {
            for(PermissionTeamUserDTO teamUserDTO : teamUserDTOList) {
                userTeam.put(teamUserDTO.getId(), teamUserDTO.getTeamId());
            }
        }
        for(Long userId : userTeam.keySet()) {
            setUserDefaultRollList(userId, userTeam.get(userId));
        }

        log.info("end set default roll list.");
    }

    @Override
    @Transactional
    public void teamAddUserSetRollList(TeamUserSetRollList input) {
        List<Long> userIds = input.getUserIds();
        Long teamId = input.getTeamId();
        if(CollUtil.isEmpty(userIds) || teamId == null) {
            return;
        }
        List<TalentSearchFolder> teamFolder = talentSearchFolderRepository.findTeamFolder(teamId, true);
        Map<Long, List<UserDailyCandidateRollListConfig>> userRollListConfig = userDailyCandidateRollListConfigRepository.findAllByUserIdIn(userIds).stream()
                .collect(Collectors.groupingBy(UserDailyCandidateRollListConfig::getUserId));
        List<UserDailyCandidateRollListConfig> rollListConfigs = new ArrayList<>();
        for(Long userId : userIds) {
            List<UserDailyCandidateRollListConfig> existList = userRollListConfig.get(userId);
            int userIdAddCount = 0;
            List<Long> existFolderIds = existList == null ? new ArrayList<>() : existList.stream().map(UserDailyCandidateRollListConfig::getTalentSearchFolderId).toList();
            for(TalentSearchFolder folder : teamFolder) {
                if(!notExcludeUserId(folder, userId)) {
                    continue;
                }
                if(existFolderIds.contains(folder.getId())) {
                    continue;
                }
                if(existFolderIds.size() + userIdAddCount >= rollListConfigMax) {
                    continue;
                }
                UserDailyCandidateRollListConfig config = new UserDailyCandidateRollListConfig();
                config.setUserId(userId);
                config.setTalentSearchFolderId(folder.getId());
                rollListConfigs.add(config);
                userIdAddCount++;
            }
        }
        if(!rollListConfigs.isEmpty()) {
            userDailyCandidateRollListConfigRepository.saveAll(rollListConfigs);
            List<Long> list = rollListConfigs.stream().map(UserDailyCandidateRollListConfig::getUserId).distinct().toList();
            for(Long userId : list) {
                commonRedisService.delete(getDailyCandidateRollListRedisKey(userId));
            }
        }
    }

    @Override
    public void teamRemoveUserSetRollList(TeamUserSetRollList input) {
        List<Long> userIds = input.getUserIds();
        Long teamId = input.getTeamId();
        if(CollUtil.isEmpty(userIds) || teamId == null) {
            return;
        }
        List<TalentSearchFolder> teamFolder = talentSearchFolderRepository.findTeamFolder(teamId, true);
        List<UserDailyCandidateRollListConfig> userRollListConfig = userDailyCandidateRollListConfigRepository.findAllByUserIdIn(userIds);
        List<UserDailyCandidateRollListConfig> rollListConfigs = new ArrayList<>();
        for(UserDailyCandidateRollListConfig existConfig : userRollListConfig) {
            TalentSearchFolder existTalentSearchFolder = getExistTalentSearchFolder(existConfig.getTalentSearchFolderId(), teamFolder);
            if(existTalentSearchFolder != null) {
                Set<TalentSearchFolderOwnerUser> ownerUser = existTalentSearchFolder.getOwnerUser();
                List<Long> ownerUserIds = ownerUser == null ? new ArrayList<>() : ownerUser.stream().map(TalentSearchFolderOwnerUser::getUserId).collect(Collectors.toList());
                Set<TalentSearchFolderSharingUser> sharingUser = existTalentSearchFolder.getSharingUser();
                List<Long> sharingUserIds = sharingUser == null ? new ArrayList<>() : sharingUser.stream().map(TalentSearchFolderSharingUser::getUserId).collect(Collectors.toList());
                Set<TalentSearchFolderOwnerTeam> ownerTeam = existTalentSearchFolder.getOwnerTeam();
                Set<TalentSearchFolderSharingTeam> sharingTeam = existTalentSearchFolder.getSharingTeam();
                Set<Long> teamIds = new HashSet<>();
                if(ownerTeam != null) {
                    teamIds.addAll(ownerTeam.stream().map(TalentSearchFolderOwnerTeam::getTeamId).collect(Collectors.toSet()));
                }
                if(sharingTeam != null) {
                    teamIds.addAll(sharingTeam.stream().map(TalentSearchFolderSharingTeam::getTeamId).collect(Collectors.toSet()));
                }
                if(!teamIds.contains(input.getCurrentTeamId()) && !ownerUserIds.contains(existConfig.getUserId()) && !sharingUserIds.contains(sharingUser)) {
                    rollListConfigs.add(existConfig);
                }
            }
        }

        if(!rollListConfigs.isEmpty()) {
            userDailyCandidateRollListConfigRepository.deleteAll(rollListConfigs);
            List<Long> list = rollListConfigs.stream().map(UserDailyCandidateRollListConfig::getUserId).distinct().toList();
            for(Long userId : list) {
                commonRedisService.delete(getDailyCandidateRollListRedisKey(userId));
            }
        }
    }

    @Resource
    private TalentSearchFolderTempRepository talentSearchFolderTempRepository;

    @Override
    public void updateSearchCondition(Long folderId) {
        List<TalentSearchFolderTemp> talentSearchFolderList = folderId == null ?
                talentSearchFolderTempRepository.findAll() :
                talentSearchFolderTempRepository.findAllById(List.of(folderId));

        for(TalentSearchFolderTemp talentSearchFolder : talentSearchFolderList) {
            TalentSearchFolderConditionDTO searchCriteria = SearchCriteriaConverter.fromJson(talentSearchFolder.getSearchCriteria());
            if (searchCriteria == null || searchCriteria.getSearch() == null) {
                continue;
            }

            log.info("update talent folder condition, folder id :{}", talentSearchFolder.getId());

            // 处理搜索条件并检查是否有变更
            SearchUpdateResult result = processSearchConditions(searchCriteria.getSearch());
            if (result.isUpdated()) {
                searchCriteria.setSearch(result.getUpdatedSearch());
                talentSearchFolder.setSearchCriteria(SearchCriteriaConverter.toJson(searchCriteria));
                talentSearchFolderTempRepository.save(talentSearchFolder);
            }
        }
        log.info("update talent folder condition complete.");

    }
    private Set<String> responsibilityKeys = Set.of(
            "responsibility5",
            "notes.responsibility5",
            "applications.responsibility0",
            "notes.responsibility0"
    );

    private Set<String> applicationKeys = Set.of(
            "applications.responsibility3.id",
            "responsibility6.id",
            "applications.responsibility2.id",
            "applications.responsibility1.id",
            "applications.responsibility6.id"
    );

    private Set<String> dateKeys = Set.of(
            "notes.createdDate",
            "createdDate",
            "applications.lastModifiedDate",
            "notes.lastModifiedDate"
    );

    private SearchUpdateResult processSearchConditions(List<ComplexSearchParam> searchParams) {
        if (searchParams == null) {
            return new SearchUpdateResult(null, false);
        }

        List<ComplexSearchParam> result = new ArrayList<>();
        boolean hasUpdates = false;

        for (ComplexSearchParam param : searchParams) {
            ComplexSearchParam processedParam = new ComplexSearchParam();
            processedParam.setRelation(param.getRelation());

            // 收集三组不同的条件
            List<ConditionParam> responsibilityParams = new ArrayList<>();
            List<ConditionParam> applicationParams = new ArrayList<>();
            List<ConditionParam> dateParams = new ArrayList<>();
            List<SearchCondition> otherConditions = new ArrayList<>();

            collectConditions(param.getCondition(), responsibilityParams, applicationParams, dateParams, otherConditions);

            // 检查和处理每组条件
            boolean currentParamUpdated = false;

            if (!responsibilityParams.isEmpty()) {
                if(!isAllRequiredKeysPresent(responsibilityParams, responsibilityKeys)) {
                    otherConditions.addAll(responsibilityParams);
                } else {
                    ComplexSearchParam responsibilityComplex = new ComplexSearchParam();
                    responsibilityComplex.setRelation(param.getRelation());
                    responsibilityComplex.setCondition(new ArrayList<>(responsibilityParams));
                    otherConditions.add(responsibilityComplex);
                    currentParamUpdated = true;
                }
            }

            if (!applicationParams.isEmpty()) {
                if(!isAllRequiredKeysPresent(applicationParams, applicationKeys)) {
                    otherConditions.addAll(applicationParams);
                } else {
                    ComplexSearchParam applicationComplex = new ComplexSearchParam();
                    applicationComplex.setRelation(param.getRelation());
                    applicationComplex.setCondition(new ArrayList<>(applicationParams));
                    otherConditions.add(applicationComplex);
                    currentParamUpdated = true;
                }
            }

            if (!dateParams.isEmpty()) {
                if(!isAllRequiredKeysPresent(dateParams, dateKeys)) {
                    otherConditions.addAll(dateParams);
                } else {
                    ComplexSearchParam dateComplex = new ComplexSearchParam();
                    dateComplex.setRelation(param.getRelation());
                    dateComplex.setCondition(new ArrayList<>(dateParams));
                    otherConditions.add(dateComplex);
                    currentParamUpdated = true;
                }
            }

            processedParam.setCondition(otherConditions);
            result.add(processedParam);

            if (currentParamUpdated) {
                hasUpdates = true;
            }
        }

        return new SearchUpdateResult(result, hasUpdates);
    }

    private boolean isAllRequiredKeysPresent(List<ConditionParam> params, Set<String> requiredKeys) {
        if (params.size() != requiredKeys.size()) {
            return false;
        }

        Set<String> foundKeys = params.stream()
                .map(ConditionParam::getKey)
                .collect(Collectors.toSet());

        return foundKeys.containsAll(requiredKeys);
    }

    private void collectConditions(List<SearchCondition> conditions,
                                   List<ConditionParam> responsibilityParams,
                                   List<ConditionParam> applicationParams,
                                   List<ConditionParam> dateParams,
                                   List<SearchCondition> otherConditions) {
        if (conditions == null) {
            return;
        }

        for (SearchCondition condition : conditions) {
            if (condition instanceof ConditionParam) {
                ConditionParam condParam = (ConditionParam) condition;
                if (responsibilityKeys.contains(condParam.getKey())) {
                    responsibilityParams.add(condParam);
                } else if (applicationKeys.contains(condParam.getKey())) {
                    applicationParams.add(condParam);
                } else if (dateKeys.contains(condParam.getKey())) {
                    dateParams.add(condParam);
                } else {
                    otherConditions.add(condParam);
                }
            } else {
                otherConditions.add(condition);
            }
        }
    }

    @Data
    @AllArgsConstructor
    private static class SearchUpdateResult {
        private List<ComplexSearchParam> updatedSearch;
        private boolean updated;
    }

    private TalentSearchFolder getExistTalentSearchFolder(Long talentSearchFolderId, List<TalentSearchFolder> teamFolder) {
        for(TalentSearchFolder existFolder : teamFolder) {
            if(existFolder.getId().equals(talentSearchFolderId)) {
                return existFolder;
            }
        }
        return null;
    }

    private boolean notExcludeUserId(TalentSearchFolder folder, Long userId) {
        //排除数据库搜索条件及share文件夹中被删除的
        Set<TalentSearchFolderSharingTeam> sharingTeam = folder.getSharingTeam();
        if(sharingTeam == null) {
            sharingTeam = new HashSet<>();
        }
        List<Long> excludedUserIds = sharingTeam.stream()
                .map(s -> JSONUtil.parseArray(s.getExcludedUserIds()).toList(Long.class))
                .flatMap(List::stream)
                .collect(Collectors.toList());

        return !TalentSearchCategory.DATABASECANDIDATES.equals(folder.getSearchCategory()) && !excludedUserIds.contains(userId);
    }

    private void setUserDefaultRollList(Long userId, Long teamId) {
        List<Long> userTop10Folder = talentSearchFolderRepository.findUserTop10Folder(userId, teamId);
        if(CollUtil.isNotEmpty(userTop10Folder)) {
            List<UserDailyCandidateRollListConfig> configList = new ArrayList<>();
            for(Long folderId : userTop10Folder) {
                UserDailyCandidateRollListConfig config = new UserDailyCandidateRollListConfig();
                config.setUserId(userId);
                config.setTalentSearchFolderId(folderId);
                configList.add(config);
            }
            userDailyCandidateRollListConfigRepository.saveAll(configList);
        }
    }

    @Resource
    private CommonRedisService commonRedisService;

    private static final String dailyCandidateRollList = "DAILY_CANDIDATE_ROLL_LIST:";

    private String getDailyCandidateRollListRedisKey(Long userId) {
        return dailyCandidateRollList + userId;
    }

    private GetDailyCandidateRollListVO getDailyCandidateRollListFromRedis(Long userId) {
        String data = commonRedisService.get(getDailyCandidateRollListRedisKey(userId));
        if(data == null) {
            return null;
        }
        return JSONUtil.toBean(data, GetDailyCandidateRollListVO.class);
    }

    private GetDailyCandidateRollListVO getDailyCandidateRollListFromEs(List<Long> folderIds, Long userId) {

        List<TalentSearchFolder> talentSearchFolderList = talentSearchFolderRepository.findAllById(folderIds);
        List<UserDailyCandidateRollListConfig> oldRollListConfig = userDailyCandidateRollListConfigRepository.findAllByTalentSearchFolderIdInAndUserIdIs(folderIds, SecurityUtils.getUserId());
        Map<Long, UserDailyCandidateRollListConfig> map = oldRollListConfig.stream()
                .collect(Collectors.toMap(
                        UserDailyCandidateRollListConfig::getTalentSearchFolderId,
                        config -> config
                ));

        Instant now = Instant.now();
        SecurityContext context = SecurityContextHolder.getContext();
        List<TalentSearchFolderData> folderDataList = Collections.synchronizedList(new ArrayList<>());
        List<UserDailyCandidateRollListConfig> newRollListConfigs = Collections.synchronizedList(new ArrayList<>());
        List<CompletableFuture<Void>> futures = talentSearchFolderList.stream()
                .map(folder -> CompletableFuture.runAsync(() -> {
                    SecurityContextHolder.setContext(context);
                    TalentSearchFolderData folderData = new TalentSearchFolderData();
                    String count = searchCount(folder.getSearchCriteria());
                    folderData.setCreatedDate(folder.getCreatedDate());
                    folderData.setId(folder.getId());
                    folderData.setDisplay(true);
                    folderData.setName(folder.getName());
                    folderData.setTotal(count);

                    UserDailyCandidateRollListConfig config = map.get(folder.getId());
                    if(config == null) {
                        config = new UserDailyCandidateRollListConfig();
                        config.setTalentSearchFolderId(folder.getId());
                        config.setUserId(SecurityUtils.getUserId());
                    }
                    //保存上次搜素时间
                    config.setLastSearchDate(config.getSearchDate());
                    config.setSearchDate(now);

                    folderData.setNewlyCount(searchCountAddCondition(
                            folder.getSearchCriteria(),
                            getCreatedDateParamCondition(config)
                    ));

                    folderData.setPendingReviewCount(searchCountAddCondition(
                            folder.getSearchCriteria(),
                            getPendingReviewParamCondition()
                    ));


                    // 同步添加结果
                    newRollListConfigs.add(config);
                    folderDataList.add(folderData);
                }))
                .collect(Collectors.toList());

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        if(!newRollListConfigs.isEmpty()) {
            userDailyCandidateRollListConfigRepository.saveAll(newRollListConfigs);
        }
        List<UserDailyCandidateRollListConfig> delConfigs = oldRollListConfig.stream()
                .filter(old -> newRollListConfigs.stream()
                        .noneMatch(newConfig ->
                                Objects.equals(old.getTalentSearchFolderId(), newConfig.getTalentSearchFolderId())))
                .collect(Collectors.toList());

        if(!delConfigs.isEmpty()) {
            userDailyCandidateRollListConfigRepository.deleteAll(delConfigs);
        }

        GetDailyCandidateRollListVO ret =  new GetDailyCandidateRollListVO();
        ret.setSearchFolderUpdate(false);
        ret.setDailyCandidateRollList(folderDataList);
        ret.setSearchDate(DateUtil.fromInstantToUtcDateTimeWithMillisecond(now));
        saveDailyCandiateRolllistToRedis(ret, userId);
        return ret;
    }

    private List<SearchParam> getPendingReviewParamCondition() {
        List<SearchParam> ret = new ArrayList<>();
        ret.add(getReviewInfoUnAudit());
        return ret;
    }

    private SearchParam getReviewInfoUnAudit() {
        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.OR);
        List<ConditionParam> conditionParams = new ArrayList<>();
        conditionParams.add(getReviewNotComplete());
        conditionParams.add(getReviewNotExist());


        searchParam.setCondition(conditionParams);
        return searchParam;
    }

    private ConditionParam getReviewNotExist() {
        // 创建 reviewInfo 条件
        ConditionParam conditionParam = new ConditionParam();
        conditionParam.setKey("reviewInfo");

        // 构建嵌套的 value 结构
        JSONObject dataObj = new JSONObject();
        dataObj.put("hasReview", false);

        List<String> reviewBy = new ArrayList<>();
        reviewBy.add("USER_" + SecurityUtils.getUserId());
        if(SecurityUtils.getTeamId() != null) {
            reviewBy.add("TEAM_" + SecurityUtils.getTeamId());
        }
        dataObj.put("reviewedBy", reviewBy);


        JSONObject valueObj = new JSONObject();
        valueObj.put("data", dataObj);

        conditionParam.setValue(valueObj);

        return conditionParam;
    }

    private ConditionParam getReviewNotComplete() {
        // 创建 reviewInfo 条件
        ConditionParam conditionParam = new ConditionParam();
        conditionParam.setKey("reviewInfo");

        // 构建嵌套的 value 结构
        JSONObject reviewTimeObj = new JSONObject();
        reviewTimeObj.put("lte", "9998-01-01");

        JSONObject dataObj = new JSONObject();
        dataObj.put("reviewTime", reviewTimeObj);

        List<String> reviewBy = new ArrayList<>();
        reviewBy.add("USER_" + SecurityUtils.getUserId());
        if(SecurityUtils.getTeamId() != null) {
            reviewBy.add("TEAM_" + SecurityUtils.getTeamId());
        }
        dataObj.put("reviewedBy", reviewBy);


        JSONObject valueObj = new JSONObject();
        valueObj.put("data", dataObj);

        conditionParam.setValue(valueObj);

        return conditionParam;
    }

    private List<SearchParam> getCreatedDateParamCondition(UserDailyCandidateRollListConfig config) {
        if(config.getLastSearchDate() == null) {
            return null;
        }
        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.AND);
        List<ConditionParam> conditionParams = new ArrayList<>();
        ConditionParam conditionParam = new ConditionParam();
        conditionParam.setKey("createdDate");
        JSONObject data = new JSONObject();
        data.put("gte", DateUtil.fromInstantToUtcDateTimeWithMillisecond(config.getLastSearchDate()));
        data.put("lte", DateUtil.fromInstantToUtcDateTimeWithMillisecond(config.getSearchDate()));

        JSONObject value = new JSONObject();
        value.put("data", data);
        conditionParam.setValue(value);
        conditionParams.add(conditionParam);
        searchParam.setCondition(conditionParams);
        return List.of(searchParam);
    }

    private String searchCountAddCondition(String searchCriteria, List<SearchParam> searchParam) {
        //如果config中没有searchData，则新增为0
        if(searchParam == null) {
            return "0";
        }
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList(
                "Pagination-Count",
                "Pagination-Owned-Data-Count",
                "Pagination-Not-Owned-Data-Count"
        ));
        TalentSearchConditionDTO conditionDTO = SearchCriteriaConverter.fromJson(searchCriteria);


        List<SearchParam> filter = conditionDTO.getFilter();
        if(filter == null) {
            filter = new ArrayList<>();
        }
        filter.addAll(searchParam);
        conditionDTO.setFilter(filter);
        String count = "0";
        try {
            talentServiceV3.searchTalentFromES(
                    conditionDTO,
                    PageRequest.of(1,1),
                    headers,
                    false,
                    false
            );

            // 获取总数
            count = headers.getFirst("Pagination-Count");
        } catch (IOException e) {
            throw new RuntimeException("获取人才数据失败", e);
        }

        return count;
    }

//    private Long getNewlyCount(List<Long> ids, UserDailyCandidateRollListConfig userDailyCandidateRollListConfig) {
//        if(userDailyCandidateRollListConfig.getSearchDate() == null) {
//            return 0L;
//        }
//        RoaringBitmap oldRoaringBitMap = userDailyCandidateRollListConfig.getTalentIdBitmapFromByteArray();
//        int[] intArray = ids.stream()
//                .mapToInt(Long::intValue)
//                .toArray();
//        RoaringBitmap newRoaringBitMap = RoaringBitmap.bitmapOf(intArray);
//
//    // 同样是计算bitmap2中有而bitmap1中没有的元素数量
//        RoaringBitmap diff = RoaringBitmap.andNot(newRoaringBitMap, oldRoaringBitMap);
//
//    // 获取具体的差异元素（如果需要）
//        int[] diffElements = diff.toArray();
//        userDailyCandidateRollListConfig.setTalentIdBitmapByRoaringBitmap(newRoaringBitMap);
//        userDailyCandidateRollListConfig.setIncrementalTalentId(JSONUtil.toJsonStr(diffElements));
//
//        return diff.getLongCardinality();
//    }

    private String searchCount(String searchCriteria) {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList(
                "Pagination-Count",
                "Pagination-Owned-Data-Count",
                "Pagination-Not-Owned-Data-Count"
        ));

        TalentSearchConditionDTO conditionDTO = SearchCriteriaConverter.fromJson(searchCriteria);

        String count = "0";
        try {
                talentServiceV3.searchTalentFromES(
                        conditionDTO,
                        PageRequest.of(1,1),
                        headers,
                        false,
                        false
                );

                // 获取总数
                count = headers.getFirst("Pagination-Count");
        } catch (IOException e) {
            throw new RuntimeException("获取人才数据失败", e);
        }

        return count;
    }

    private void saveDailyCandiateRolllistToRedis(GetDailyCandidateRollListVO ret, Long userId) {
        commonRedisService.set(getDailyCandidateRollListRedisKey(userId), JSONUtil.toJsonStr(ret), 24*60*60);
    }

    private boolean checkFolderPermission(TalentSearchFolder talentSearchFolder) {
        Set<TalentSearchFolderOwnerUser> ownerUser = talentSearchFolder.getOwnerUser();

        boolean check = ownerUser == null ? talentSearchFolder.getPermissionUserId().equals(SecurityUtils.getUserId()) :
                ownerUser.stream().anyMatch(p -> p.getUserId().equals(SecurityUtils.getUserId()));
        Set<TalentSearchFolderSharingUser> sharingUser = talentSearchFolder.getSharingUser();
        check = check || sharingUser.stream().anyMatch(p -> p.getUserId().equals(SecurityUtils.getUserId()));
        Set<TalentSearchFolderSharingTeam> sharingTeam = talentSearchFolder.getSharingTeam();
        check = check || sharingTeam.stream().anyMatch(p -> {
            JSONArray jsonArray = JSONUtil.parseArray(p.getExcludedUserIds());
            List<Long> list = jsonArray.toList(Long.class);
            return p.getTeamId().equals(SecurityUtils.getTeamId()) && !list.contains(SecurityUtils.getUserId());
        });
        Set<TalentSearchFolderOwnerTeam> ownerTeam = talentSearchFolder.getOwnerTeam();
        return check || ownerTeam.stream().anyMatch(p -> {
            JSONArray jsonArray = JSONUtil.parseArray(p.getExcludedUserIds());
            List<Long> list = jsonArray.toList(Long.class);
            return p.getTeamId().equals(SecurityUtils.getTeamId()) && !list.contains(SecurityUtils.getUserId());
        });
    }
}
