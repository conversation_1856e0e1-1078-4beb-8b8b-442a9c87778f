package com.altomni.apn.talent.service.dto.folder;

import com.altomni.apn.common.dto.search.TalentSearchFolderConditionDTO;
import com.altomni.apn.talent.domain.enumeration.folder.TalentSearchCategory;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TalentSearchFolderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    public String getFolderNote() {
        return folderNote;
    }

    public void setFolderNote(String folderNote) {
        this.folderNote = folderNote;
    }

    private String folderNote;

    private TalentSearchCategory searchCategory;

    private TalentSearchFolderConditionDTO searchCriteria;

    private Long tenantId;

    private List<TalentSearchFolderSharingTeamDTO> ownerTeamList;
    private List<TalentSearchFolderSharingUserDTO> ownerUserList;
    private List<TalentSearchFolderSharingTeamDTO> sharingTeamList;
    private List<TalentSearchFolderSharingUserDTO> sharingUserList;

    @ApiModelProperty(value = "talent custom folder id")
    private Long talentFolderId;

    @ApiModelProperty(value = "indicate whether current talent folder is active or not")
    private boolean isActive;

    private String createdBy;
    private Instant createdDate;

    //克隆时获取搜索文件夹使用
    private List<String> existUserFilter;
    //克隆并保存时替换相关属性
    private LastFollowUpByChange userFilterUpdate;

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @JsonIgnore
    public Boolean isValidSearchFolder(){
        return this.isActive();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public TalentSearchFolderConditionDTO getSearchCriteria() {
        return searchCriteria;
    }

    public void setSearchCriteria(TalentSearchFolderConditionDTO searchCriteria) {
        this.searchCriteria = searchCriteria;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getTalentFolderId() {
        return talentFolderId;
    }

    public void setTalentFolderId(Long talentFolderId) {
        this.talentFolderId = talentFolderId;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public TalentSearchCategory getSearchCategory() {
        return searchCategory;
    }

    public void setSearchCategory(TalentSearchCategory searchCategory) {
        this.searchCategory = searchCategory;
    }

}
