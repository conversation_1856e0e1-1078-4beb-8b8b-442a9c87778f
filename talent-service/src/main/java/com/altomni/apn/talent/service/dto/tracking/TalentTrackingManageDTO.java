package com.altomni.apn.talent.service.dto.tracking;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(value = "talent tracking manage")
@NoArgsConstructor
@AllArgsConstructor
public class TalentTrackingManageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ids")
    private List<Long> ids;

    @ApiModelProperty(value = "talentLinkedinIds")
    private List<String> talentLinkedinIds;

    @ApiModelProperty(value = "operatorLinkedinId")
    private String operatorLinkedinId;

    private List<Long> groupIdList;
}
