package com.altomni.apn.talent.repository.linkedinproject;

import com.altomni.apn.talent.domain.linkedinproject.LinkedinTalentContact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the LinkedinTalentContact entity.
 */
@Repository
public interface LinkedinTalentContactRepository extends JpaRepository<LinkedinTalentContact, Long> {

    List<LinkedinTalentContact> findAllByLinkedinTalentId(String linkedinTalentId);

    void deleteAllByLinkedinTalentId(String linkedinTalentId);

    List<LinkedinTalentContact> findAllByLinkedinTalentIdAndTenantId(String linkedinTalentId, Long tenantId);

    void deleteAllByLinkedinTalentIdAndTenantId(String linkedinTalentId, Long tenantId);
}
