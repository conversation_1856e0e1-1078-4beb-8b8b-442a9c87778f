package com.altomni.apn.talent.service.talent;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.UpdateTalentContactVerificationStatusDTO;
import com.altomni.apn.talent.web.rest.vm.TalentContactVM;
import com.altomni.apn.common.domain.enumeration.user.Status;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;
import java.util.Set;

public interface TalentContactService {

    void delete(Long id) throws IOException;

    TalentContact findOne(Long id);

    TalentContact findByIdAndStatus(Long id, Status status);

    List<TalentContact> getAllTalentContacts(ContactType type, String contact, Pageable pageable);

    /** For TalentService internal use */
    Set<Long> getTalentIdsByContactsIgnoreWrongContact(Long ignoreTalentId, List<TalentContactVM> contacts);

    Set<TalentContact> getTalentDuplicationsByContacts(Long ignoreTalentId, List<TalentContactVM> contacts);

    List<TalentContact> findAllByTalentIdInAndTypeAndStatus(List<Long> talentIds, ContactType email, TalentContactStatus available);

    List<TalentContact> findAllByTalentIdInAndTypeAndStatusWithPermission(List<Long> talentIds, ContactType email, TalentContactStatus available);

    List<TalentContact> findPrimaryEmailAllByTalentIdInAndTypeAndStatus(List<Long> talentIds, ContactType email, TalentContactStatus available);

    List<TalentContact> findAllByTalentId(Long id);

    List<Long> getAllViewableTalentIds(List<Long> talentIds);

    List<TalentContactDTO> findAllContactByContactAndTypes(String contact, List<ContactType> types);

    TalentContact updateTalentContactVerificationStatus(UpdateTalentContactVerificationStatusDTO updateContactVerificationStatusDTO);

}
