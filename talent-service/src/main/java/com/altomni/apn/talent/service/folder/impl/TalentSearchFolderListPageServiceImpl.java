package com.altomni.apn.talent.service.folder.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.user.SimpleUser;
import com.altomni.apn.common.dto.folder.FolderSearchRequestDTO;
import com.altomni.apn.common.dto.folder.FolderSharedTeamDTO;
import com.altomni.apn.common.dto.user.NameEmailUserDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.folder.SearchFolderStatus;
import com.altomni.apn.common.repository.user.SimpleUserRepository;
import com.altomni.apn.common.service.sql.QueryProcessService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.folder.*;
import com.altomni.apn.talent.repository.folder.TalentFolderSearchPageRepositoryCustom;
import com.altomni.apn.talent.repository.folder.TalentSearchFolderRepository;
import com.altomni.apn.talent.repository.talent.TalentReviewNoteRepository;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderSharingTeamDTO;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderSharingUserDTO;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderStatusDTO;
import com.altomni.apn.talent.service.folder.TalentCustomFolderService;
import com.altomni.apn.talent.service.folder.TalentSearchFolderListPageService;
import com.altomni.apn.talent.service.folder.TalentSearchFolderService;
import com.altomni.apn.talent.service.mapper.folder.TalentSearchFolderStatusDTOMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class TalentSearchFolderListPageServiceImpl implements TalentSearchFolderListPageService {

    final static String NAME_COLUMN = "f.name";
    final static String CREATED_DATE_COLUMN = "f.created_date";
    final static String SHARING_USER_COLUMN = "tu.user_id";
    final static String SHARING_TEAM_COLUMN = "tt.team_id";
    final static String OWNER_USER_COLUMN = "ou.user_id";
    final static String OWNER_TEAM_COLUMN = "ot.team_id";

    @Resource
    TalentSearchFolderStatusDTOMapper talentSearchFolderStatusDTOMapper;

    @Resource
    TalentSearchFolderService talentSearchFolderService;

    @Resource
    TalentCustomFolderService talentCustomFolderService;

    @Resource
    QueryProcessService queryProcessService;

    @Resource
    UserService userService;

    @Resource
    TalentFolderSearchPageRepositoryCustom talentFolderSearchPageRepositoryCustom;

    @Resource
    private TalentSearchFolderRepository talentSearchFolderRepository;

    /**
     * Search sub folder: search folder
     */
    @Override
    public Page<TalentSearchFolderStatusDTO> findSearchFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable) {
        Map<Integer, Object> paramsMap = new HashMap<>(16);
        StringBuffer countSql = new StringBuffer();
        StringBuffer dataSql = new StringBuffer();
        setSearchFolderSql(searchRequestDTO, pageable, paramsMap, countSql, dataSql);
        String countSqlGroup = "select count(*) from ( %s ) AS subquery";
        Long total = talentFolderSearchPageRepositoryCustom.searchCount(String.format(countSqlGroup, countSql.toString()), paramsMap);
        List<TalentSearchFolder> talentSearchFolderList = talentFolderSearchPageRepositoryCustom.searchData(dataSql.toString(), TalentSearchFolder.class, paramsMap);

        List<TalentSearchFolderStatusDTO> talentSearchFolderStatusDTOList = talentSearchFolderStatusDTOMapper.toSimpleDto(talentSearchFolderList);
        List<Long> folderIds = talentSearchFolderList.stream().map(TalentSearchFolder::getId).collect(Collectors.toList());
        setStatusForInactiveSearchFolder(talentSearchFolderStatusDTOList, talentSearchFolderRepository.findAllById(folderIds));
        setSharingInfo(talentSearchFolderStatusDTOList, talentSearchFolderRepository.findAllById(folderIds));

        return new PageImpl<>(talentSearchFolderStatusDTOList, pageable, total);
    }
    
    @Resource
    private SimpleUserRepository simpleUserRepository;
    @Resource
    private TalentReviewNoteRepository reviewNoteRepository;
    
    

    private void setSharingInfo(List<TalentSearchFolderStatusDTO> talentSearchFolderStatusDTOList, List<TalentSearchFolder> talentSearchFolderList) {
        Set<Long> userIds = talentSearchFolderList.stream()
                .flatMap(talentSearchFolder -> talentSearchFolder.getSharingUser().stream()).map(TalentSearchFolderSharingUser::getUserId).collect(Collectors.toSet());
        userIds.addAll(talentSearchFolderList.stream()
                .flatMap(talentSearchFolder -> Optional.ofNullable(talentSearchFolder.getOwnerUser())
                        .orElse(Collections.emptySet())
                        .stream())
                .map(TalentSearchFolderOwnerUser::getUserId)
                .collect(Collectors.toSet()));
        Set<Long> teamIds = talentSearchFolderList.stream()
                .flatMap(talentSearchFolder -> talentSearchFolder.getSharingTeam().stream()).map(TalentSearchFolderSharingTeam::getTeamId).collect(Collectors.toSet());
        teamIds.addAll(talentSearchFolderList.stream()
                .flatMap(talentSearchFolder -> Optional.ofNullable(talentSearchFolder.getOwnerTeam())
                        .orElse(Collections.emptySet())
                        .stream())
                .map(TalentSearchFolderOwnerTeam::getTeamId)
                .collect(Collectors.toSet()));

        Map<Long, SimpleUser> userMap = simpleUserRepository.findAllById(userIds).stream().collect(Collectors.toMap(SimpleUser::getId, Function.identity()));
        Map<Long, FolderSharedTeamDTO> teamMap = reviewNoteRepository.findFolderSharedTeamDTO(teamIds).stream().collect(Collectors.toMap(FolderSharedTeamDTO::getTeamId, Function.identity()));

        talentSearchFolderStatusDTOList.forEach(dto -> {
            TalentSearchFolder talentSearchFolderById = getTalentSearchFolderById(dto.getId(), talentSearchFolderList);
            Set<TalentSearchFolderSharingUser> sharingUser = talentSearchFolderById.getSharingUser();
            dto.setSharingUserList(sharingUser.stream().map(f -> {
                TalentSearchFolderSharingUserDTO userDto = new TalentSearchFolderSharingUserDTO();
                userDto.setUserId(f.getUserId());
                userDto.setId(f.getId());
                SimpleUser simpleUser = userMap.get(f.getUserId());
                if(simpleUser != null) {
                    userDto.setName(CommonUtils.formatFullName(simpleUser.getFirstName(), simpleUser.getLastName()));
                }
                return userDto;
            }).collect(Collectors.toList()));
            Set<TalentSearchFolderSharingTeam> sharingTeam = talentSearchFolderById.getSharingTeam();
            dto.setSharingTeamList(sharingTeam.stream().map(f -> {
                TalentSearchFolderSharingTeamDTO teamDto = new TalentSearchFolderSharingTeamDTO();
                teamDto.setTeamId(f.getTeamId());
                teamDto.setId(f.getId());
                JSONArray jsonArray = JSONUtil.parseArray(f.getExcludedUserIds());
                teamDto.setExcludedUserIds(new HashSet<>(jsonArray.toList(Long.class)));
                FolderSharedTeamDTO teamDTO = teamMap.get(f.getTeamId());
                if(teamDTO != null) {
                    teamDto.setName(teamDTO.getTeamName());
                }
                return teamDto;
            }).collect(Collectors.toList()));
            Set<TalentSearchFolderOwnerUser> ownerUser = talentSearchFolderById.getOwnerUser();
            dto.setOwnerUserList(ownerUser.stream().map(f -> {
                TalentSearchFolderSharingUserDTO userDto = new TalentSearchFolderSharingUserDTO();
                userDto.setUserId(f.getUserId());
                userDto.setId(f.getId());
                SimpleUser simpleUser = userMap.get(f.getUserId());
                if(simpleUser != null) {
                    userDto.setName(CommonUtils.formatFullName(simpleUser.getFirstName(), simpleUser.getLastName()));
                }
                return userDto;
            }).collect(Collectors.toList()));
            Set<TalentSearchFolderOwnerTeam> ownerTeam = talentSearchFolderById.getOwnerTeam();
            dto.setOwnerTeamList(ownerTeam.stream().map(f -> {
                TalentSearchFolderSharingTeamDTO teamDto = new TalentSearchFolderSharingTeamDTO();
                teamDto.setTeamId(f.getTeamId());
                teamDto.setId(f.getId());
                JSONArray jsonArray = JSONUtil.parseArray(f.getExcludedUserIds());
                teamDto.setExcludedUserIds(new HashSet<>(jsonArray.toList(Long.class)));
                FolderSharedTeamDTO teamDTO = teamMap.get(f.getTeamId());
                if(teamDTO != null) {
                    teamDto.setName(teamDTO.getTeamName());
                }
                return teamDto;
            }).collect(Collectors.toList()));
        });
    }

    private void setSearchFolderSql(FolderSearchRequestDTO searchRequestDTO, Pageable pageable, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        String sCountSql = """
                SELECT COUNT(f.id) FROM talent_search_folder f
                LEFT JOIN talent_search_folder_sharing_user tu ON f.id = tu.talent_search_folder_id
                LEFT JOIN talent_search_folder_sharing_team tt ON f.id = tt.talent_search_folder_id
                LEFT JOIN talent_search_folder_owner_user ou ON f.id = ou.talent_search_folder_id
                LEFT JOIN talent_search_folder_owner_team ot ON f.id = ot.talent_search_folder_id
                WHERE (tu.user_id = %s or (tt.team_id = %s and NOT JSON_CONTAINS(tt.excluded_user_ids, '%s'))
                       or ou.user_id = %s or (ot.team_id = %s and NOT JSON_CONTAINS(ot.excluded_user_ids, '%s')))
                """;


        String sDataSql = """
                SELECT f.id, f.name, f.folder_note, f.search_category, f.search_criteria, f.created_by, f.created_date, f.talent_folder_id, f.is_active, f.last_modified_by, f.last_modified_date, f.tenant_id, f.puser_id, f.pteam_id
                FROM talent_search_folder f
                LEFT JOIN talent_search_folder_sharing_user tu ON f.id = tu.talent_search_folder_id
                LEFT JOIN talent_search_folder_sharing_team tt ON f.id = tt.talent_search_folder_id
                LEFT JOIN talent_search_folder_owner_user ou ON f.id = ou.talent_search_folder_id
                LEFT JOIN talent_search_folder_owner_team ot ON f.id = ot.talent_search_folder_id
                WHERE (tu.user_id = %s or (tt.team_id = %s and NOT JSON_CONTAINS(tt.excluded_user_ids, '%s'))
                 or ou.user_id = %s or (ot.team_id = %s and NOT JSON_CONTAINS(ot.excluded_user_ids, '%s')))
                """;
        countSql.append(String.format(sCountSql, SecurityUtils.getUserId(), SecurityUtils.getTeamId(), SecurityUtils.getUserId(), SecurityUtils.getUserId(), SecurityUtils.getTeamId(), SecurityUtils.getUserId()));
        dataSql.append(String.format(sDataSql, SecurityUtils.getUserId(), SecurityUtils.getTeamId(), SecurityUtils.getUserId(), SecurityUtils.getUserId(), SecurityUtils.getTeamId(), SecurityUtils.getUserId()));

        setSearchFolderSearchParam(searchRequestDTO, paramsMap, countSql, dataSql);
        queryProcessService.setQueryPaginationAndSort(pageable, dataSql);
    }

    private void setSearchFolderSearchParam(FolderSearchRequestDTO searchRequestDTO, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (StringUtils.isNotBlank(searchRequestDTO.getName())) {
            queryProcessService.setColumnLikeParamFilter(paramsMap, countSql, dataSql, NAME_COLUMN, searchRequestDTO.getName());
        }

        if (searchRequestDTO.getCreatedDate() != null) {
            queryProcessService.setDateBetweenParamFilter(paramsMap, countSql, dataSql, CREATED_DATE_COLUMN, searchRequestDTO.getCreatedDate().getDateFrom(), searchRequestDTO.getCreatedDate().getDateTo());
        }
        // depreacted old way
//        if (searchRequestDTO.getCreatedDateFrom() != null || searchRequestDTO.getCreatedDateTo() != null) {
//            queryProcessService.setDateBetweenParamFilter(paramsMap, countSql, dataSql, CREATED_DATE_COLUMN, searchRequestDTO.getCreatedDateFrom(), searchRequestDTO.getCreatedDateTo());
//        }

        if (searchRequestDTO.getGeneralText() != null) {
            queryProcessService.setSingleGeneralSearchParamFilter(paramsMap, countSql, dataSql, NAME_COLUMN, searchRequestDTO.getGeneralText().get(0));
        }
        if(searchRequestDTO.getSharedTeams() != null) {
            queryProcessService.setColumnIdsInParaFilter(paramsMap, countSql, dataSql, SHARING_TEAM_COLUMN, searchRequestDTO.getSharedTeams());
        }
        if(searchRequestDTO.getSharedUsers() != null) {
            queryProcessService.setColumnIdsInParaFilter(paramsMap, countSql, dataSql, SHARING_USER_COLUMN, searchRequestDTO.getSharedUsers());
        }
        if(searchRequestDTO.getOwnerTeams() != null) {
            queryProcessService.setColumnIdsInParaFilter(paramsMap, countSql, dataSql, OWNER_TEAM_COLUMN, searchRequestDTO.getOwnerTeams());
        }
        if(searchRequestDTO.getOwnerUsers() != null) {
            queryProcessService.setColumnIdsInParaFilter(paramsMap, countSql, dataSql, OWNER_USER_COLUMN, searchRequestDTO.getOwnerUsers());
        }
        dataSql.append(" group by f.id");
        countSql.append(" group by f.id");
    }

    private void setStatusForInactiveSearchFolder(List<TalentSearchFolderStatusDTO> talentSearchFolderStatusDTOList, List<TalentSearchFolder> talentSearchFolderList) {
        List<Long> folderIds = talentSearchFolderStatusDTOList.stream()
                .filter(dto -> !dto.isActive())
                .map(TalentSearchFolderStatusDTO::getTalentFolderId)
                .collect(Collectors.toList());

        if (folderIds.isEmpty()) {
            talentSearchFolderStatusDTOList.forEach(dto -> dto.setTalentFolderStatus(SearchFolderStatus.FOLDER_ACTIVE));
            return;
        }

        Map<Long, Long> folderUserMap = talentCustomFolderService.getTalentFolderUserMap(folderIds);

        List<UserBriefDTO> userBriefs = userService.getAllBriefUsersByIds(new ArrayList<>(folderUserMap.values())).getBody();
        Map<Long, UserBriefDTO> idUserMap = userBriefs.stream()
                .collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));

        talentSearchFolderStatusDTOList.forEach(dto -> {
            if (dto.isActive()) {
                dto.setTalentFolderStatus(SearchFolderStatus.FOLDER_ACTIVE);
            } else if (dto.getTalentFolderId() != null) {
                if (!folderUserMap.containsKey(dto.getTalentFolderId())) {
                    dto.setTalentFolderStatus(SearchFolderStatus.FOLDER_DELETED);
                } else {
                    dto.setTalentFolderStatus(SearchFolderStatus.FOLDER_SHARE_REVOKE);
                    UserBriefDTO userBriefDTO = idUserMap.get(folderUserMap.get(dto.getTalentFolderId()));
                    dto.setTalentFolderCreator(new NameEmailUserDTO(userBriefDTO.getId(),
                            CommonUtils.formatFullName(userBriefDTO.getFirstName(), userBriefDTO.getLastName()),
                            userBriefDTO.getEmail()));
                }
            } else {
                //special case, the search folder is set to inactive, but it is not assicated with any talent_folder.
                dto.setTalentFolderStatus(SearchFolderStatus.UNKNOWN_INACTIVE);
            }
        });
    }

    private TalentSearchFolder getTalentSearchFolderById(Long id, List<TalentSearchFolder> talentSearchFolderList) {
        for(TalentSearchFolder folder : talentSearchFolderList) {
            if(folder.getId().equals(id)) {
                return folder;
            }
        }
        return null;
    }

}
