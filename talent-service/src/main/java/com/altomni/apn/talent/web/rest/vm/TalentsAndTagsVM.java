package com.altomni.apn.talent.web.rest.vm;
//Get multiple UserJobRelation from frontend.

import java.util.List;

public class TalentsAndTagsVM {
    List<Long> talentIds;

    List<Long> tagIds;

    public List<Long> getTalentIds() {
        return talentIds;
    }

    public void setTalentIds(List<Long> talentIds) {
        this.talentIds = talentIds;
    }

    public List<Long> getTagIds() {
        return tagIds;
    }

    public void setTagIds(List<Long> tagIds) {
        this.tagIds = tagIds;
    }

    @Override
    public String toString() {
        return "TalentsAndTagsVM{" +
            "talentIds=" + talentIds +
            ", tagIds=" + tagIds +
            '}';
    }
}

