package com.altomni.apn.talent.service.xxljob.impl;

import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnParamDTO;
import com.altomni.apn.common.repository.xxljob.XxlJobRelationRepository;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.confidential.ConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;
import com.altomni.apn.talent.service.xxljob.XxlJobClientService;
import com.altomni.apn.talent.service.xxljob.XxlJobService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.ZoneId;
import java.util.Collection;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class XxlJobServiceImpl implements XxlJobService {

    private final XxlJobRelationRepository xxlJobRelationRepository;
    private final XxlJobClientService xxlJobClientService;


    @Override
    public void addTalentDeclassifyJob(ConfidentialTalent confidentialTalent, TalentDeclassifyType declassifyReason, String... additionalParams) {
        List<XxlJobRelation> existingRelations = xxlJobRelationRepository.findAllByTypeAndReferenceId(XxlJobRelationTypeEnum.TALENT_DECLASSIFY, confidentialTalent.getTalentId());
        if (!existingRelations.isEmpty()) {
            xxlJobClientService.deleteXxlJobIdList(existingRelations.stream().map(XxlJobRelation::getXxlJobId).toList());
        }
        XxlJobApnDTO xxlJobApnDTO = new XxlJobApnDTO();
        xxlJobApnDTO.setJobDesc("Auto declassify talent by rule");
        XxlJobApnParamDTO xxlJobApnParamDTO = new XxlJobApnParamDTO();
        xxlJobApnParamDTO.setXxlJobType(XxlJobRelationTypeEnum.TALENT_DECLASSIFY);
        xxlJobApnParamDTO.setReferenceId(confidentialTalent.getTalentId());
        xxlJobApnParamDTO.setUserId(SecurityUtils.getUserId());
        xxlJobApnParamDTO.setTenantId(SecurityUtils.getTenantId());
        xxlJobApnParamDTO.setTimezone(ZoneId.systemDefault().toString());
        xxlJobApnParamDTO.setSendTime(confidentialTalent.getConfidentialEndTime());
        xxlJobApnParamDTO.setXxlJobParam(Map.of("declassifyReason", declassifyReason,
                "additionalParams", additionalParams,
                "xxlJobType", XxlJobRelationTypeEnum.TALENT_DECLASSIFY));
        xxlJobApnDTO.setXxlJobApnParamDTO(xxlJobApnParamDTO);
        xxlJobClientService.createXxlJob(xxlJobApnDTO);
    }

    @Override
    public void deleteTalentDeclassifyJob(Long talentId) {
        List<XxlJobRelation> existingRelations = xxlJobRelationRepository.findAllByTypeAndReferenceId(XxlJobRelationTypeEnum.TALENT_DECLASSIFY, talentId);
        if (!existingRelations.isEmpty()) {
            xxlJobClientService.deleteXxlJobIdList(existingRelations.stream().map(XxlJobRelation::getXxlJobId).toList());
        }
    }

    @Override
    public void deleteTalentDeclassifyJob(Collection<Long> talentIds) {
        List<XxlJobRelation> existingRelations = xxlJobRelationRepository.findAllByTypeAndReferenceIdIn(XxlJobRelationTypeEnum.TALENT_DECLASSIFY, talentIds.stream().toList());
        if (!existingRelations.isEmpty()) {
            xxlJobClientService.deleteXxlJobIdList(existingRelations.stream().map(XxlJobRelation::getXxlJobId).toList());
        }
    }
}
