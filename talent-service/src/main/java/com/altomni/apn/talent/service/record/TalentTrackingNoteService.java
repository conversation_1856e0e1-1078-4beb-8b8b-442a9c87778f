package com.altomni.apn.talent.service.record;


import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatform;
import com.altomni.apn.talent.service.dto.record.TalentTrackingNoteDTO;
import com.altomni.apn.talent.service.vo.record.TalentNoteVO;
import com.altomni.apn.talent.service.vo.record.TalentTrackingNoteVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TalentTrackingNoteService {

    /** save talent tracking note
     * @param talentTrackingNoteDTO talent tracking note
     * @return talent tracking note
     */
    TalentTrackingNoteVO save(TalentTrackingNoteDTO talentTrackingNoteDTO);

    /** get all notes
     * @param platformId platform id
     * @param trackingPlatform platform type
     * @return list of TalentTrackingNote
     */
    List<TalentTrackingNoteVO> findAllTalentTrackingNotes(String platformId, TrackingPlatform trackingPlatform);

    List<TalentNoteVO> syncProTalentTrackingNoteToApnTalentNote(Long talentId, String platformId);
}
