package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.talent.domain.talent.WatchList;
import com.altomni.apn.talent.repository.talent.TalentContactRepository;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.repository.talent.WatchListRepository;
import com.altomni.apn.talent.service.CompanyService;
import com.altomni.apn.talent.service.application.ApplicationService;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.talent.service.dto.company.CompanyDTO;
import com.altomni.apn.talent.service.dto.talent.WatchListDTO;
import com.altomni.apn.talent.service.job.JobService;
import com.altomni.apn.talent.service.query.TalentSearch;
import com.altomni.apn.talent.service.talent.WatchListService;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class WatchListServiceImpl implements WatchListService {

    private final Logger log = LoggerFactory.getLogger(WatchListServiceImpl.class);

    @Resource
    private WatchListRepository watchListRepository;

    @Resource
    private JobService jobService;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private CompanyService companyService;


    @Resource
    private TalentContactRepository talentContactRepository;

    @Resource
    private ApplicationService applicationService;

    @Override
    public WatchList create(WatchList watchList) {
        Optional<WatchList> exist = watchListRepository.findOneByUserIdAndTalentIdAndJobId(SecurityUtils.getUserId(), watchList.getTalentId(), watchList.getJobId());
        if (exist.isPresent()) {
            return exist.get();
        }
        watchList.setUserId(SecurityUtils.getUserId());
        return watchListRepository.save(watchList);
    }

    @Transactional
    @Override
    public void removeTalentWatchList(List<Long> talentIds) {
        watchListRepository.deleteByTalentIdIn(talentIds);
    }

    @Transactional
    @Override
    public void removeTalentWatchListByIds(List<Long> ids) {
        watchListRepository.deleteByIdIn(ids);
    }

    @Override
    public Page<WatchListDTO> findAll(BooleanExpression allWatchlist, Pageable pageable) {
        Sort sort;
        if (pageable.getSort() != null) {
            sort = pageable.getSort();
        } else {
            sort = Sort.by(Sort.Direction.DESC, "createdDate");
        }
        PageRequest pageRequest = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), sort);
        return watchListRepository.findAll(allWatchlist, pageRequest).map(this::toWatchListDTO);
    }

    private WatchListDTO toWatchListDTO(WatchList watchList) {
        WatchListDTO watchListDTO = new WatchListDTO();
        if (watchList != null) {
            ServiceUtils.myCopyProperties(watchList, watchListDTO);
            TalentV3 talent = talentRepository.getById(watchList.getTalentId());
            watchListDTO.setFirstName(talent.getFirstName());
            watchListDTO.setLastName(talent.getLastName());
            watchListDTO.setFullName(talent.getFullName());
           List<TalentExperienceDTO> talentExperienceList = JSON.parseObject(talent.getTalentExtendedInfo()).getJSONArray("experiences").toJavaList(TalentExperienceDTO.class);
           if(ObjectUtil.isNotEmpty(talentExperienceList)) {
               watchListDTO.setTitle(ObjectUtil.toString(JSONUtil.parseArray(talentExperienceList.stream().map(TalentExperienceDTO::getTitle).collect(Collectors.toList()))));
               watchListDTO.setCompany(ObjectUtil.toString(JSONUtil.parseArray(talentExperienceList.stream().map(TalentExperienceDTO::getCompanyName).collect(Collectors.toList()))));
           }
            if (watchList.getJobId() != null) {
                JobV3 job = jobService.findById(watchList.getJobId()).getBody();
                if (job != null && job.getCompanyId() != null) {
                    CompanyDTO company = companyService.findById(job.getCompanyId()).getBody();
                    if(company != null) {
                        watchListDTO.setJobCompany(company.getName());
                    }
                }
                watchListDTO.setJobTitle(job.getTitle());
            }
        }
        return watchListDTO;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TalentDTOV3> getMyWatchListTalent(String search, Pageable pageable) {
        log.info("[TalentWatchList: getMyWatchListTalent @{}] REST request to get my watch list talent by search: {}", SecurityUtils.getUserId(), search);
        Page<TalentV3> page = talentRepository.findAll(TalentSearch.watchlist(search), pageable);
        return new PageImpl<>(toWatchListDtoList(page.getContent()), pageable, page.getTotalElements());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TalentDTOV3> getMyWatchListTalentsByJobId(String search, Long jobId, Pageable pageable) {
        log.info("[TalentWatchList: getMyWatchListTalentsByJobId @{}] REST request to get my watch list talents by job id: {}", SecurityUtils.getUserId(), jobId);
        Page<TalentV3> page = talentRepository.findAll(TalentSearch.myWatchingTalentsForJob(search, jobId), pageable);
        return new PageImpl<>(toWatchListDtoList(page.getContent()), pageable, page.getTotalElements());
    }

    @Resource
    private EnumCommonService enumCommonService;

    private List<TalentDTOV3> toWatchListDtoList(List<TalentV3> talents) {
        if (CollectionUtils.isEmpty(talents)) {
            return Collections.emptyList();
        }

        List<Long> talentId = talents.stream().map(TalentV3::getId).toList();
        Map<Long, List<TalentContact>> talentContactMap = talentContactRepository.findAllByTalentIdInAndStatus(talentId, TalentContactStatus.AVAILABLE)
            .stream().collect(Collectors.groupingBy(TalentContact::getTalentId, Collectors.toList()));
        return talents.stream().map(talent -> {
            TalentDTOV3 dto = TalentDTOV3.fromTalent(talent);
            dto.setContacts(Convert.toList(TalentContactDTO.class, talentContactMap.getOrDefault(talent.getId(), Collections.emptyList())));
            return dto;
        }).toList();
    }
}
