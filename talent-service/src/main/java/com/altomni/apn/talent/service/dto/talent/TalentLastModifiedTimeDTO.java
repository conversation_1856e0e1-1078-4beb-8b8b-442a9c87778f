package com.altomni.apn.talent.service.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 *
 */
public interface TalentLastModifiedTimeDTO {

    Instant getLastModifiedDate();

    Long getTalentId();

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    class TalentLastModifiedTimeRequest{
        private Instant lastModifiedDate;

        private Long talentId;
    }
}
