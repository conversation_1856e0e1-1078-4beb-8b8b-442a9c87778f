package com.altomni.apn.talent.domain.enumeration.record;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

/**
 * <AUTHOR>
 */
@Converter
public class TrackingTypeConverter extends AbstractAttributeConverter<TrackingType, Integer> {
    public TrackingTypeConverter() {
        super(TrackingType::toDbValue, TrackingType::fromDbValue);
    }
}
