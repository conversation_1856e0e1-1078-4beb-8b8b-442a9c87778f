package com.altomni.apn.talent.service.dto.tracking;


import com.altomni.apn.talent.domain.enumeration.tracking.TrackingTemplateCategory;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingTemplateCategoryConverter;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingTemplateStatus;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingTemplateStatusConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;


@Data
@ApiModel(value = "talent tracking search")
@NoArgsConstructor
@AllArgsConstructor
public class TalentTrackingTemplateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "title")
    @NotEmpty
    private String title;

    @ApiModelProperty(value = "template")
    @NotEmpty
    private String template;

    @ApiModelProperty(value = "operatorLinkedinId")
    private String operatorLinkedinId;

    @ApiModelProperty(value = "category")
    @Convert(converter = TrackingTemplateCategoryConverter.class)
    private TrackingTemplateCategory category;

}
