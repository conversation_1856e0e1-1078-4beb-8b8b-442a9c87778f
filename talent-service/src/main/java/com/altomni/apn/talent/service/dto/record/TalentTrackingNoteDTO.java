package com.altomni.apn.talent.service.dto.record;

import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatform;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;

@Data
public class TalentTrackingNoteDTO {

    private TrackingPlatform trackingPlatform;

    @NotNull
    private String platformId;

    private String note;

    private Long syncedTalentId;

    public TrackingPlatform getTrackingPlatform() {
        return trackingPlatform;
    }

    public void setTrackingPlatform(TrackingPlatform trackingPlatform) {
        this.trackingPlatform = trackingPlatform;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Long getSyncedTalentId() {
        return syncedTalentId;
    }

    public void setSyncedTalentId(Long syncedTalentId) {
        this.syncedTalentId = syncedTalentId;
    }
}
