package com.altomni.apn.talent.web.rest.vm;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * A TalentContactSearch VM.
 */
@ApiModel(description = "Talent's contacts. Talent can have many contact information, from different providers. They are also used to help identify if a talent " +
    "already exists, or should create a new one.")
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
//@JsonIgnoreProperties(ignoreUnknown = true)
public class TalentContactSearchVM {

    private Long ignoreTalentId;

    @NotNull
    private List<TalentContactVM> contacts;

}
