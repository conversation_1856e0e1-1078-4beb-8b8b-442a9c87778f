package com.altomni.apn.talent.web.rest.talent.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TalentDetailRetrievingRecordSearchDTO implements Serializable {

    private Set<Long> userIds;

    private Long talentId;

    private Long tenantId;

    private Instant from;

    private Instant to;
}
