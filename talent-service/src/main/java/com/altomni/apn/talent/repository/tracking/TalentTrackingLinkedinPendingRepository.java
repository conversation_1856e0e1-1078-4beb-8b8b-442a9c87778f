package com.altomni.apn.talent.repository.tracking;

import com.altomni.apn.talent.domain.enumeration.tracking.TrackingCategory;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingStatus;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinPending;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TalentTrackingLinkedinPendingRepository extends JpaRepository<TalentTrackingLinkedinPending, Long>, JpaSpecificationExecutor<TalentTrackingLinkedinPending> {

    TalentTrackingLinkedinPending findFirstByTenantIdAndCategoryAndStatusInAndOperatorLinkedinIdAndTalentLinkedinId(Long tenantId, TrackingCategory category, List<TrackingStatus> status, String operatorId, String talentLinkedinId);

    List<TalentTrackingLinkedinPending> findAllByIdInAndTenantIdAndStatusInAndOperatorLinkedinId(List<Long> ids, Long tenantId, List<TrackingStatus> status, String operatorId);

    List<TalentTrackingLinkedinPending> findAllByTenantIdAndStatusInAndOperatorLinkedinIdAndTalentLinkedinIdIn(Long tenantId, List<TrackingStatus> status, String operatorId, List<String> talentLinkedinIds);
}
