package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.talent.domain.talent.TalentCurrentLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Repository
public interface TalentLocationRepository extends JpaRepository<TalentCurrentLocation, Long> {

    @Modifying
    @Transactional
    @Query(value = "delete from talent_current_location where talent_id = ?1", nativeQuery = true)
    void deleteAllByTalentId(Long talentId);

    TalentCurrentLocation findByTalentId(Long talentId);

    @Modifying
    @Transactional
    @Query(value = "update talent_current_location set official_city = ?2, official_country = ?3, official_province = ?4, official_county = ?5, zip_code = ?6 where talent_id = ?1", nativeQuery = true)
    void updateOfficialInfoById(Long talentId, String officialCity, String officialCountry, String officialProvince, String officialCounty, String zipCode);

    List<TalentCurrentLocation> findByTalentIdIn(List<Long> talentIds);
}