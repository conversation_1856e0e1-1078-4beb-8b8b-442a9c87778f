package com.altomni.apn.talent.repository.linkedin;

import com.altomni.apn.talent.domain.enumeration.tracking.TrackingGroupStatus;
import com.altomni.apn.talent.domain.linkedin.TalentTrackingLinkedinPendingGroup;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the TalentTrackingLinkedinPendingGroup entity.
 */
@Repository
public interface TalentTrackingLinkedinPendingGroupRepository extends JpaRepository<TalentTrackingLinkedinPendingGroup, Long> {


    List<TalentTrackingLinkedinPendingGroup> findAllByTenantIdAndOperatorLinkedinIdAndStatusAndName(Long tenantId, String operatorId, TrackingGroupStatus trackingGroupStatus, String name);

    Page<TalentTrackingLinkedinPendingGroup> findAllByTenantIdAndOperatorLinkedinIdAndStatus(Long tenantId, String operatorLinkedinId, TrackingGroupStatus trackingGroupStatus, Pageable pageable);

    List<TalentTrackingLinkedinPendingGroup> findAllByTenantIdAndOperatorLinkedinIdAndStatusOrderByCreatedDateDesc(Long tenantId, String operatorLinkedinId, TrackingGroupStatus trackingGroupStatus);
}