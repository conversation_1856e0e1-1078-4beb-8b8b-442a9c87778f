package com.altomni.apn.talent.service.dto.email;

import com.altomni.apn.talent.domain.email.MailingListContent;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Email;

import java.util.Objects;

public class Recipient {

    @Email
    private String email;

    private String name;

    public Recipient(){}

    public Recipient(String email) {
        this.email = email.toLowerCase();
        this.name = email.split("@")[0];
    }

    public Recipient(String email, String name) {
        this.email = email.toLowerCase();
        this.name = name;
    }

    public Recipient(MailingListContent mailingListContent) {
        this.email = mailingListContent.getEmail().toLowerCase();
        this.name = mailingListContent.getName();
    }

    public String getEmail() { return email; }

    public void setEmail(String email) { this.email = email; }

    public String getName() { return name; }

    public void setName(String name) { this.name = name; }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        Recipient recipient = (Recipient) o;
        if (recipient.getEmail() == null || getEmail() == null) {
            return false;
        }
        return StringUtils.equalsIgnoreCase(getEmail(), recipient.getEmail());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getEmail());
    }

    @Override
    public String toString() {
        return "Recipient{" +
            "email='" + email + '\'' +
            ", name='" + name + '\'' +
            '}';
    }
}
