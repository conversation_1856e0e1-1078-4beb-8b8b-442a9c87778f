package com.altomni.apn.talent.domain.linkedin;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 领英待加好友分组成员表
 */
@Entity
@Table(name = "talent_tracking_linkedin_pending_group_member")
@Data
public class TalentTrackingLinkedinPendingGroupMember extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "pending_id", nullable = false)
    private Long pendingId;

    @Column(name = "group_id", nullable = false)
    private Long groupId;

    public static TalentTrackingLinkedinPendingGroupMember fromTalentTrackingGroupMemberDTO(Long pendingId, Long groupId) {
        TalentTrackingLinkedinPendingGroupMember ret = new TalentTrackingLinkedinPendingGroupMember();
        ret.setPendingId(pendingId);
        ret.setGroupId(groupId);
        return ret;
    }
}