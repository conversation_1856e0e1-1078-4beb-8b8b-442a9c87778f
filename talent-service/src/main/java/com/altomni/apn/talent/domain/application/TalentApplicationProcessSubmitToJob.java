package com.altomni.apn.talent.domain.application;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentRecruitmentProcessSubmitToJob.
 */
@Entity
@Table(name = "talent_recruitment_process_submit_to_job")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentApplicationProcessSubmitToJob extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 823584676743062580L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "note")
    private String note;

    @Column(name = "skills")
    private String skills;

    @Column(name = "recommend_comments")
    private String recommendComments;

    @Column(name = "talent_resume_relation_id")
    private Long talentResumeRelationId;

}
