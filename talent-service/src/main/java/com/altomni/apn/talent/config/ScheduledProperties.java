package com.altomni.apn.talent.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@Component
@RefreshScope
public class ScheduledProperties {

    @Value("${application.sync.threadNum}")
    private Integer threadNum;

    @Value("${application.sync.total}")
    private Integer total;

}
