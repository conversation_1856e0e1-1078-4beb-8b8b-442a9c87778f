package com.altomni.apn.talent.web.rest.linkedinproject;
;
import com.altomni.apn.common.errors.BadRequestAlertException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.LinkedinProjectTalentFilter;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectTalentDTO;
import com.altomni.apn.talent.service.linkedinproject.LinkedinProjectTalentService;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinProjectTalentStatusCountVO;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinProjectTalentVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing LinkedinProjectTalent.
 */
@RestController
@RequestMapping("/api/v3")
public class LinkedinProjectTalentResource {

    private final Logger log = LoggerFactory.getLogger(LinkedinProjectTalentResource.class);

    private static final String ENTITY_NAME = "LinkedinProjectTalent";

    private final LinkedinProjectTalentService linkedinProjectTalentService;

    public LinkedinProjectTalentResource(LinkedinProjectTalentService linkedinProjectTalentService) {
        this.linkedinProjectTalentService = linkedinProjectTalentService;
    }

    @PostMapping("/linkedin-project-talents")
    public ResponseEntity<LinkedinProjectTalentVO> create(@RequestBody LinkedinProjectTalentDTO linkedinProjectTalent) throws URISyntaxException {
        log.info("[APN: LinkedinProjectTalent @{}] REST request to create LinkedinProjectTalent: {}, ", SecurityUtils.getCurrentUserLogin(), linkedinProjectTalent);
        LinkedinProjectTalentVO result = linkedinProjectTalentService.create(linkedinProjectTalent);
        return ResponseEntity.created(new URI("/api/v1/linkedin-project-talents/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @PutMapping("/linkedin-project-talents/{id}")
    public ResponseEntity<LinkedinProjectTalentVO> update(@PathVariable Long id, @RequestBody LinkedinProjectTalentDTO linkedinProjectTalent) {
        log.info("[APN: LinkedinProjectTalent @{}] REST request to update id: {}, LinkedinProjectTalent: {}, ", SecurityUtils.getCurrentUserLogin(), id, linkedinProjectTalent);
        linkedinProjectTalent.setId(id);
        LinkedinProjectTalentVO result = linkedinProjectTalentService.update(linkedinProjectTalent);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, linkedinProjectTalent.getId().toString()))
            .body(result);
    }

    @GetMapping("/linkedin-project-talents/linkedinProjectId/{linkedinProjectId}")
    public ResponseEntity<List<LinkedinProjectTalentVO>> findAll(@PathVariable Long linkedinProjectId, @RequestParam(value = "filter", required = true) LinkedinProjectTalentFilter filter, Pageable pageable) {
        log.info("[APN: LinkedinProjectTalent @{}] REST request to get a page of filter: {}, LinkedinProjectTalents: {}, ", SecurityUtils.getCurrentUserLogin(), filter, linkedinProjectId);
        Page<LinkedinProjectTalentVO> page = linkedinProjectTalentService.findAll(linkedinProjectId, filter, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v1/linkedin-project-talents/linkedinProjectId/" + linkedinProjectId);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    @GetMapping("/linkedin-project-talents/get-talents-by-project/{projectId}")
    public ResponseEntity<List<LinkedinProjectTalentVO>> findLinkedinTalentsByProjectId(@PathVariable Long projectId,
                                                                                        @RequestParam(required = false) Boolean hasContactInfo,
                                                                                        @RequestParam(required = false) Boolean isInApplication,
                                                                                        @RequestParam(required = false) CandidateStatus candidateStatus,
                                                                                        @RequestParam(required = false) ContactStatus contactStatus,
                                                                                        Pageable pageable) {
        log.info("[APN: findLinkedinTalentsByProjectId @{}] REST request to findLinkedinTalentsByProjectId: {}, with param hasContactInfo:{}, isInApplication: {}, candidateStatus: {}, contactStatus: {}", SecurityUtils.getCurrentUserLogin(), projectId, hasContactInfo, isInApplication, candidateStatus, contactStatus);
        Page<LinkedinProjectTalentVO> page = linkedinProjectTalentService.findLinkedinTalentsByProjectId(projectId, hasContactInfo, isInApplication, candidateStatus, contactStatus, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v1/linkedin-project-talents/get-talents-by-project/" + projectId);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    @GetMapping("/linkedin-project-talents/statusCount/linkedinProjectId/{linkedinProjectId}")
    public ResponseEntity<LinkedinProjectTalentStatusCountVO> statusCount(@PathVariable Long linkedinProjectId) {
        log.info("[APN: LinkedinProjectTalent @{}] REST request to get status count: {}, ", SecurityUtils.getCurrentUserLogin(), linkedinProjectId);
        return ResponseEntity.ok().body(linkedinProjectTalentService.statusCount(linkedinProjectId));
    }
}
