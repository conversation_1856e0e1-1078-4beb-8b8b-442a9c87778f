package com.altomni.apn.talent.repository.linkedinproject;

import com.altomni.apn.talent.domain.linkedinproject.LinkedinProjectMember;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;


/**
 * Spring Data  repository for the LinkedinProjectMember entity.
 */
@Repository
public interface LinkedinProjectMemberRepository extends JpaRepository<LinkedinProjectMember, Long> {

    List<LinkedinProjectMember> findByLinkedinProjectId(Long linkedinProjectId);

    @Transactional
    @Modifying
    @Query(value = "delete from linkedin_project_member where linkedin_project_id = ?1", nativeQuery = true)
    void deleteByLinkedinProjectId(Long linkedinProjectId);

    List<LinkedinProjectMember> findByLinkedinProjectIdIn(List<Long> linkedinProjectIds);
}
