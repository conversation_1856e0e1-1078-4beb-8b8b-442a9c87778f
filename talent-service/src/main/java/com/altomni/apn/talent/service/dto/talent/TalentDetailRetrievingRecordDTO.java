package com.altomni.apn.talent.service.dto.talent;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TalentDetailRetrievingRecordDTO implements Serializable {

    private Long userId;

    private String userEmail;

    private String username;

    private String fullname;

    private Long talentId;

    private Long tenantId;

    private Instant retrievingTime;

}
