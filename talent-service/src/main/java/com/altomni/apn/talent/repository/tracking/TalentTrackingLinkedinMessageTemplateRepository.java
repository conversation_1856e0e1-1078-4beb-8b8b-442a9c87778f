package com.altomni.apn.talent.repository.tracking;

import com.altomni.apn.talent.domain.enumeration.tracking.TrackingTemplateStatus;
import com.altomni.apn.talent.domain.tracking.TalentTrackingLinkedinMessageTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TalentTrackingLinkedinMessageTemplateRepository extends JpaRepository<TalentTrackingLinkedinMessageTemplate, Long>, JpaSpecificationExecutor<TalentTrackingLinkedinMessageTemplate> {

    List<TalentTrackingLinkedinMessageTemplate> findAllByIdInAndTenantIdAndStatusAndPermissionUserId(List<Long> ids, Long tenantId, TrackingTemplateStatus status, Long userId);
}
