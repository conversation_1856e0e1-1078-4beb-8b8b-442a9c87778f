package com.altomni.apn.talent.service;

import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.company.ClientContactCompany;
import com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.web.rest.dto.FindAllByNamesDTO;
import com.altomni.apn.talent.service.dto.company.CompanyDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Component
@FeignClient(value = "company-service")
public interface CompanyService {

    @GetMapping("/company/api/v3/company/{id}")
    ResponseEntity<CompanyDTO> findById(@PathVariable("id") Long companyId);

    @GetMapping("/company/api/v3/saleslead/client-contact/info/{id}")
    ResponseEntity<SalesLeadClientContact> getSalesLeadClientContactById(@PathVariable("id") Long id);

    @GetMapping("/company/api/v3/company/count-company-account-manager")
    ResponseEntity<Integer> countCompanyAccountManager(@RequestParam("talentId") Long talentId, @RequestParam("userId") Long userId);

    @PostMapping("/company/api/v3/company/find-by-names")
    ResponseEntity<List<ClientContactCompany>> findByNames(@RequestBody FindAllByNamesDTO dto);

    @GetMapping("/company/api/v3/sales-leads/company/{talentId}")
    ResponseEntity<List<SalesLeadClientContact>> findClientContactByTalentId(@PathVariable("talentId") Long talentId);

    @GetMapping("/company/api/v3/saleslead/client-contacts/talent/{talentId}")
    ResponseEntity<Long> queryContactLocationIdByTalentId(@PathVariable("talentId") Long talentId);

    @PutMapping("/company/api/v3/saleslead/client-contacts/talent/{talentId}/location/{locationId}")
    void updateContactLocationIdByTalentId(@PathVariable ("talentId") Long talentId, @PathVariable ("locationId") Long locationId);

    @GetMapping("/company/api/v3/company/locations/{id}")
    ResponseEntity<LocationDTO> queryCompanyLocation(@PathVariable("id") Long id);

    @PostMapping("/company/api/v3/saleslead/client-contacts/talent-client-contact-status")
    ResponseEntity<List<TalentClientContactStatusDTO>> getTalentClientContactStatus(@RequestBody List<Long> talentIds);

}
