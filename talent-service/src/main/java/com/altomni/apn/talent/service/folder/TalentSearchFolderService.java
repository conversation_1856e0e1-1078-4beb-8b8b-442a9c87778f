package com.altomni.apn.talent.service.folder;

import com.altomni.apn.common.dto.folder.TeamUserSetRollList;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderDTO;
import com.altomni.apn.talent.web.rest.talent.dto.GetDailyCandidateRollListDTO;
import com.altomni.apn.talent.web.rest.talent.dto.GetDailyCandidateRollListVO;
import com.altomni.apn.talent.web.rest.talent.dto.TalentSearchFolderData;

import java.util.List;

public interface TalentSearchFolderService {

    /***
     * creat a new talent search folder entity by DTO
     * @param talentSearchFolderDTO
     * @return
     */
    TalentSearchFolderDTO createTalentSearchFolder(TalentSearchFolderDTO talentSearchFolderDTO, String rollListJump);


    TalentSearchFolderDTO updateTalentSearchFolder(Long id, TalentSearchFolderDTO talentSearchFolderDTO);

    /***
     * delete the talent search folder entity
     * @param id, talent search folder id cannot be null
     */
    void deleteTalentSearchFolder(Long id);

    /***
     * Get talent search folder
     * @param folderId
     * @return
     */

    TalentSearchFolderDTO getTalentSearchFolder(Long folderId, boolean checkUserFilter, String rollListJump);

    TalentSearchFolderDTO updateTalentSearchFolderCondition(Long folderId, TalentSearchFolderDTO talentSearchFolderDTO, String rollListJump);

    List<TalentSearchFolderData> getDailyCandidateRollListConfig();

    void updateDailyCandidateRollList(GetDailyCandidateRollListDTO input);

    Long countCandidatesInFolder(Long folderId);

    GetDailyCandidateRollListVO getDailyCandidateRollList(boolean refresh);

    void talentSearchFolderSetDefaultRollList();

    void teamAddUserSetRollList(TeamUserSetRollList input);

    void teamRemoveUserSetRollList(TeamUserSetRollList input);

    void updateSearchCondition(Long folderId);
}
