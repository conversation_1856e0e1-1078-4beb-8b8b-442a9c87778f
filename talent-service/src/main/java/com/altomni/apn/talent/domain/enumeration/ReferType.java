package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ReferType implements ConvertedEnum<Integer> {
    Recruiter(0), Self_Referred(1);

    private final int dbValue;

    ReferType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ReferType, Integer> resolver =
        new ReverseEnumResolver<>(ReferType.class, ReferType::toDbValue);

    public static ReferType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
