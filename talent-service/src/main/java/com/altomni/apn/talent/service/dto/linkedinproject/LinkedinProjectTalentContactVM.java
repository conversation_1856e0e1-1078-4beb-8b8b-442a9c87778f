package com.altomni.apn.talent.service.dto.linkedinproject;

import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.talent.domain.enumeration.record.TrackingType;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
public class LinkedinProjectTalentContactVM implements Serializable {

    private static final long serialVersionUID = -4526645409893933706L;

    private TrackingType contactType;

    private Instant touchTime;

    private Long userId;

    private User user;

    public TrackingType getContactType() {
        return contactType;
    }

    public void setContactType(TrackingType contactType) {
        this.contactType = contactType;
    }

    public Instant getTouchTime() {
        return touchTime;
    }

    public void setTouchTime(Instant touchTime) {
        this.touchTime = touchTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        User singleUser = new User();
        singleUser.setFirstName(user.getFirstName());
        singleUser.setLastName(user.getLastName());
        singleUser.setActivated(user.isActivated());
        singleUser.setMonthlyCredit(null);
        singleUser.setBulkCredit(null);
        this.user = singleUser;
    }

    @Override
    public String toString() {
        return "LinkedinProjectTalentContactVM{" +
            "contactType=" + contactType +
            ", touchTime=" + touchTime +
            ", userId=" + userId +
            ", user=" + user +
            '}';
    }
}
