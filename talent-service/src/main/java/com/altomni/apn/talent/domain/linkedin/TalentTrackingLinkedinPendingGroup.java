package com.altomni.apn.talent.domain.linkedin;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingGroupStatus;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingGroupStatusConverter;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingGroupVO;
import com.altomni.apn.talent.web.rest.talent.dto.LinkedinPendingGroupRequest;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 领英待加好友分组表
 */
@Entity
@Table(name = "talent_tracking_linkedin_pending_group")
@Data
public class TalentTrackingLinkedinPendingGroup extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Size(max = 255)
    @Column(name = "name", length = 255, nullable = false)
    private String name;

    @Column(name = "status", nullable = false)
    @Convert(converter = TrackingGroupStatusConverter.class)
    private TrackingGroupStatus status = TrackingGroupStatus.ACTIVE;

    @Size(max = 100)
    @Column(name = "operator_linkedin_id", length = 100, nullable = false)
    private String operatorLinkedinId;

    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;


    public static TalentTrackingLinkedinPendingGroup fromTalentTrackingGroupDTO(LinkedinPendingGroupRequest talentTrackingGroupDTO) {
        TalentTrackingLinkedinPendingGroup talentTrackingLinkedinGroup = new TalentTrackingLinkedinPendingGroup();
        ServiceUtils.myCopyProperties(talentTrackingGroupDTO, talentTrackingLinkedinGroup);
        return talentTrackingLinkedinGroup;
    }

    public static TalentTrackingGroupVO toTalentTrackingGroupVO(TalentTrackingLinkedinPendingGroup talentTrackingLinkedinGroup) {
        TalentTrackingGroupVO talentTrackingGroupVO = new TalentTrackingGroupVO();
        ServiceUtils.myCopyProperties(talentTrackingLinkedinGroup, talentTrackingGroupVO);
        return talentTrackingGroupVO;
    }
}