package com.altomni.apn.talent.web.rest.talent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 领英账号信息响应DTO
 */
@ApiModel(description = "领英账号信息响应")
public class LinkedinAccountInfoResponse {

    @ApiModelProperty(value = "好友数量(上次保存)", example = "500")
    @JsonProperty("friendCount")
    private Integer friendCount;

    @ApiModelProperty(value = "匹配好友的时间", example = "2024-01-15 10:30:00")
    @JsonProperty("matchFriendDatetime")
    private String matchFriendDatetime;

    public Integer getFriendCount() {
        return friendCount;
    }

    public void setFriendCount(Integer friendCount) {
        this.friendCount = friendCount;
    }

    public String getMatchFriendDatetime() {
        return matchFriendDatetime;
    }

    public void setMatchFriendDatetime(String matchFriendDatetime) {
        this.matchFriendDatetime = matchFriendDatetime;
    }

    @Override
    public String toString() {
        return "LinkedinAccountInfoResponse{" +
                "friendCount=" + friendCount +
                ", matchFriendDatetime='" + matchFriendDatetime + '\'' +
                '}';
    }
}