package com.altomni.apn.talent.web.rest.talent;


import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.folder.*;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderRelationDTO;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderWithCreatedFlag;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFoldersDTO;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.talent.AddTalentsToFoldersOutput;
import com.altomni.apn.talent.service.dto.folder.*;
import com.altomni.apn.talent.service.folder.*;
import com.altomni.apn.talent.web.rest.talent.dto.GetDailyCandidateRollListDTO;
import com.altomni.apn.talent.web.rest.talent.dto.GetDailyCandidateRollListVO;
import com.altomni.apn.talent.web.rest.talent.dto.TalentSearchFolderData;
import com.altomni.apn.talent.web.rest.talent.dto.TalentsToFoldersDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = {"Talent", "ATS-Candidates"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class TalentFolderResource {

    @Resource
    private TalentRelateJobFolderService talentRelateJobFolderService;

    @Resource
    private TalentCustomFolderService talentCustomFolderService;

    @Resource
    private TalentSearchFolderService talentSearchFolderService;

    @Resource
    private TalentCustomFolderListPageService talentCustomFolderListPageService;

    @Resource
    private TalentSearchFolderListPageService talentSearchFolderListPageService;

    @Resource
    private TalentRelateJobFolderJobSharingPlatformProcessService talentRelateJobFolderJobSharingPlatformProcessService;


    /***
     * Search custom/shared folder list page
     * @param folderSearchRequestDTO
     * @param pageable
     * @return
     */
    @PostMapping("/folders/search")
    public ResponseEntity<List<ListPageFolderDTO>> searchTalentFolders(@Valid @RequestBody FolderSearchRequestDTO folderSearchRequestDTO, @ApiParam Pageable pageable) {
        log.info("[APN: Talent @{}] REST request to search talent folder :{}", SecurityUtils.getUserId(), pageable);
        Page<ListPageFolderDTO> talentFolderDetails = talentCustomFolderListPageService.searchTalentFolders(folderSearchRequestDTO, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(talentFolderDetails, "/api/v3/folders/search");
        return new ResponseEntity<>(talentFolderDetails.getContent(), headers, HttpStatus.OK);
    }


//    /***
//     * get all talent folders under current user
//     * @param pageable
//     * @return
//     */
//    @GetMapping("/folders")
//    public ResponseEntity<List<TalentFolderBriefDTO>> getTalentFolders(@ApiParam Pageable pageable) {
//        log.info("[APN: Talent @{}] REST request to get talent folder :{}", SecurityUtils.getUserId(), pageable);
//        Page<TalentFolderBriefDTO> talentFolderDetails = talentCustomFolderService.getMyTalentFolderWithCountListByUserId(SecurityUtils.getUserId(), pageable);
//        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(talentFolderDetails, "/api/v3/folders");
//        return new ResponseEntity<>(talentFolderDetails.getContent(), headers, HttpStatus.OK);
//    }


    /***
     * get a search folder
     * @param folderId
     * @return
     */
    @ApiOperation(value = "get a talent folder by folder id.")
    @GetMapping("/folders/{folderId}")
    public ResponseEntity<ListPageFolderDTO> getTalentFolderById(@PathVariable Long folderId) {
        log.info("[APN] Talent Folder({},{}) REST request to get a Talent folder:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), folderId);
        return ResponseEntity.ok(talentCustomFolderService.getTalentFolderById(folderId));
    }

    /***
     * add a new Talent Folder
     * @return TalentFolderDetail
     */
    @PostMapping("/folders")
    @NoRepeatSubmit
    public ResponseEntity<TalentFolderDTO> addTalentFolder(@RequestBody @Valid TalentFolderDTO talentFolderDTO) {
        log.info("[APN: Talent @{}] REST request to add talent folder", SecurityUtils.getUserId());
        talentFolderDTO = talentCustomFolderService.createTalentFolder(talentFolderDTO);
        return ResponseEntity.ok(talentFolderDTO);
    }

    /***
     * Update Talent folder by FolderId
     * @Parameter talentFolderDTO
     * @Parameter folderId
     * @return TalentFolderDetail
     */
    @PutMapping("/folders/{folderId}")
    public ResponseEntity<TalentFolderDTO> updateTalentFolder(@Valid @RequestBody TalentFolderDTO talentFolderDTO, @PathVariable Long folderId) {
        log.info("[APN: Talent @{}] REST request to update talent folder : {}", SecurityUtils.getUserId(), folderId);
        talentFolderDTO = talentCustomFolderService.updateTalentFolder(talentFolderDTO, folderId);
        return ResponseEntity.ok(talentFolderDTO);
    }

    /***
     * Delete Talent folder by FolderId
     * @Parameter folderId
     */
    @DeleteMapping("/folders/{folderId}")
    public ResponseEntity<Void> deleteTalentFolder(@PathVariable Long folderId) {
        log.info("[APN: Talent @{}] REST request to update talent folder", SecurityUtils.getUserId(), folderId);
        talentCustomFolderService.deleteFolder(folderId);
        return ResponseEntity.noContent().build();
    }



    /***
     * get all talent's email list by folderIds;
     */
    @PostMapping("/folders/talents-email-in-folders")
    public ResponseEntity<List<String>> getTalentEmailList(@RequestBody FolderEmailRequestDTO folderEmailRequestDTO){
        log.info("[APN: Talent @{}] REST request to get talent email list", SecurityUtils.getUserId());
        List<String> emailList = talentCustomFolderService.getAllTalentsEmailInFolders(folderEmailRequestDTO);
        return ResponseEntity.ok(emailList);
    }



    /***
     * Add Talents into folders by TalentIds and FolderIds
     * @Parameter Void
     *
     */
    @PostMapping("/add-talents-to-folders")
    @NoRepeatSubmit
    public ResponseEntity<Void> addTalentsToFolders(@Valid @RequestBody TalentFolderRelationListDTO talentFolderRelationListDTO) {
        log.info("[APN: Talent @{}] REST request to add talents into folders", SecurityUtils.getUserId());
        talentCustomFolderService.addTalentsToFolders(talentFolderRelationListDTO);
        return ResponseEntity.ok().build();
    }


    /***
     * delete talents which assigned to current folder
     * @param folderId
     * @param talentFolderRelationListDTO
     * @return void
     */
    @DeleteMapping("/folders/{folderId}/talents")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteTalentsFromFolder(@PathVariable Long folderId, @Valid @RequestBody TalentFolderRelationListDTO talentFolderRelationListDTO) {
        log.info("[APN: Talent @{}] REST request to delete talent from fold with folderId", SecurityUtils.getUserId());
        talentCustomFolderService.deleteTalentsFromFolder(talentFolderRelationListDTO, folderId);
        return ResponseEntity.noContent().build();
    }

    /***
     * get shared team list for talent folder
     * @return return the team list under the talent folder
     * @throws Throwable
     */
    @ApiOperation(value = "get shared team list for talent folder by user")
    @GetMapping(value = "/folders/shared-teams")
    public ResponseEntity<List<FolderSharedTeamDTO>> getTalentFolderSharedTeams() {
        log.info("APN TalentFolder REST request to get shared team list for Talent folder by user:  {} and tenant {} ", SecurityUtils.getUserId(), SecurityUtils.getTenantId());
        List<FolderSharedTeamDTO> talentFolderSharedTeams= talentCustomFolderService.getDistinctSharedTeamsByUserId();
        return ResponseEntity.ok(talentFolderSharedTeams);
    }

    /***
     * get shared user list for talent folder
     * @return list of shared user under current user folder
     */
    @ApiOperation(value = "get shared user list for talent folder by user")
    @GetMapping(value = "/folders/shared-users")
    public ResponseEntity<List<FolderSharedUserDTO>> getTalentFolderSharedUsers() {
        log.info("({},{}) REST request to get shared team list for Talent folder", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        List<FolderSharedUserDTO> talentFolderSharedUsers= talentCustomFolderService.getDistinctSharedUsersByUserId();
        return ResponseEntity.ok(talentFolderSharedUsers);
    }


    /***
     * delete the folder sharing from current user and it will not delete real folder
     * @param folderId the folder Id
     * @return void
     */
    @DeleteMapping("/shared-folders/{folderId}")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteSharingForSharedFolder(@PathVariable Long folderId) {
        log.info("[APN: Talent @{}] REST request to delete talent from fold with folderId", SecurityUtils.getUserId());
        talentCustomFolderService.removeSharingForSharedFolder(folderId);
        return ResponseEntity.noContent().build();
    }

    /***
     * Search shared folder list page
     * @param folderSearchRequestDTO
     * @param pageable
     * @return
     */
    @PostMapping("/shared-folders/search")
    public ResponseEntity<List<ListPageFolderDTO>> searchSharedTalentFolders(@Valid @RequestBody FolderSearchRequestDTO folderSearchRequestDTO, @ApiParam Pageable pageable) {
        log.info("[APN: Talent @{}] REST request to search talent folder :{}", SecurityUtils.getUserId(), pageable);
        Page<ListPageFolderDTO> talentFolderDetails = talentCustomFolderListPageService.searchTalentFolders(folderSearchRequestDTO, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(talentFolderDetails, "/api/v3/folders/search");
        return new ResponseEntity<>(talentFolderDetails.getContent(), headers, HttpStatus.OK);
    }



    /***
     * get all owned and shared talent folders with READWRITE permission under current user
     * @return FolderListDTO
     */
    @GetMapping("/folders/collaborative-folders")
    public ResponseEntity<FolderListDTO> getAllCollaborativeTalentFolders() {
        log.info("[APN: Talent @{}] REST request to get owner and collaborative shared talent folders", SecurityUtils.getUserId());
        FolderListDTO folderListDTO = talentCustomFolderService.getCollaborativeTalentFolderList();
        return ResponseEntity.ok(folderListDTO);
    }

    /***
     * get all owner and shared job folders with permission info under current user
     * @return FolderListDTO
     */
    @GetMapping("/folders/custom-and-shared-folders")
    public ResponseEntity<FolderPermissionListDTO> getAllCustomAndSharedJobFolders() {
        log.info("[APN: Job @{}] REST request to get owner and shared job folders", SecurityUtils.getUserId());
        FolderPermissionListDTO folderListDTO = talentCustomFolderService.getCustomAndSharedJobFolderWithPermissionList();
        return ResponseEntity.ok(folderListDTO);
    }


//    /***
//     * get shared team list of these folders shared to me
//     * @return return the team list under the talent folder
//     */
//    @ApiOperation(value = "get shared team list for talent folder by user")
//    @GetMapping(value = "/shared-folders/shared-teams")
//    public ResponseEntity<List<FolderSharedTeamDTO>> getTalentFolderSharedTeamsForShared() {
//        log.info("APN TalentFolder REST request to get shared team list for Talent folder by user:  {} and tenant {} ", SecurityUtils.getUserId(), SecurityUtils.getTenantId());
//        List<FolderSharedTeamDTO> talentFolderSharedTeams= talentCustomFolderService.getDistinctSharedTeamsForSharedFolderByUserId(SecurityUtils.getUserId());
//        return ResponseEntity.ok(talentFolderSharedTeams);
//    }
//
//    /***
//     * get shared user list of these folders shared to me
//     * @return list of shared user under current user folder
//     */
//    @ApiOperation(value = "get shared user list for talent folder by user")
//    @GetMapping(value = "/shared-folders/shared-users")
//    public ResponseEntity<List<FolderSharedUserDTO>> getTalentFolderSharedUsersForShared() {
//        log.info("({},{}) REST request to get shared team list for Talent folder by user:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
//        List<FolderSharedUserDTO> talentFolderSharedUsers= talentCustomFolderService.getDistinctSharedUsersForSharedFolderByUserId(SecurityUtils.getUserId());
//        return ResponseEntity.ok(talentFolderSharedUsers);
//    }

    /***
     * create a new talent search folder
     * @param talentSearchFolderDTO
     * @return
     * @throws Throwable
     */
    @ApiOperation(value = "create a talent search folder")
    @PostMapping(value = "/search-folders")
    @NoRepeatSubmit
    public ResponseEntity<TalentSearchFolderDTO> addTalentSearchFolder(@RequestBody @Valid TalentSearchFolderDTO talentSearchFolderDTO
            , @RequestParam(value = "rollListJump", required = false) String rollListJump) throws Throwable {
        log.info("APN Talent Search Folder ({},{}) REST request to add a new Talent Search folder:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentSearchFolderDTO.getName());
        TalentSearchFolderDTO result  = talentSearchFolderService.createTalentSearchFolder(talentSearchFolderDTO, rollListJump);
        return ResponseEntity.ok(result);
    }

    /***
     * edit talent search folder search condition
     * @param talentSearchFolderDTO
     * @return
     * @throws Throwable
     */
    @ApiOperation(value = "edit a talent search folder")
    @PostMapping(value = "/search-folders/{folderId}/condition")
    @NoRepeatSubmit
    public ResponseEntity<TalentSearchFolderDTO> updateTalentSearchFolderCondition(@PathVariable Long folderId, @RequestBody @Valid TalentSearchFolderDTO talentSearchFolderDTO, @RequestParam(value = "rollListJump", required = false) String rollListJump) throws Throwable {
        TalentSearchFolderDTO result  = talentSearchFolderService.updateTalentSearchFolderCondition(folderId, talentSearchFolderDTO, rollListJump);
        return ResponseEntity.ok(result);
    }

    /***
     * edit talent search folder
     * @param talentSearchFolderDTO
     * @return
     * @throws Throwable
     */
    @ApiOperation(value = "edit a talent search folder")
    @PostMapping(value = "/search-folders/{folderId}")
    @NoRepeatSubmit
    public ResponseEntity<TalentSearchFolderDTO> editTalentSearchFolder(@PathVariable Long folderId, @RequestBody @Valid TalentSearchFolderDTO talentSearchFolderDTO) throws Throwable {
        log.info("APN Talent Search Folder ({},{}) REST request to edit Talent Search folder:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentSearchFolderDTO.getName());
        TalentSearchFolderDTO result  = talentSearchFolderService.updateTalentSearchFolder(folderId, talentSearchFolderDTO);
        return ResponseEntity.ok(result);
    }

    /***
     * delete a search folder
     * @param folderId
     * @return
     */
    @ApiOperation(value = "Delete a talent search folder")
    @DeleteMapping("/search-folders/{folderId}")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteTalentSearchFolder(@PathVariable Long folderId) {
        log.info("[APN] Talent Search Folder({},{}) REST request to Delete Talent Search folder:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), folderId);

        talentSearchFolderService.deleteTalentSearchFolder(folderId);
        return ResponseEntity.noContent().build();
    }


    @ApiOperation(value = "get a daily candidate roll list")
    @GetMapping("/daily-candidate-roll-list")
    public ResponseEntity<GetDailyCandidateRollListVO> getDailyCandidateRollList(@RequestParam(name = "refresh", defaultValue = "false") boolean refresh) {
        log.info("[APN] Talent Search Folder({},{}) REST request to getDailyCandidateRollList", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        return ResponseEntity.ok(talentSearchFolderService.getDailyCandidateRollList(refresh));
    }

    @ApiOperation(value = "get a talent search folder")
    @PutMapping("/daily-candidate-roll-list/config")
    @NoRepeatSubmit
    public ResponseEntity<Void> updateDailyCandidateRollList(@RequestBody GetDailyCandidateRollListDTO input) {
        log.info("[APN] Talent Search Folder({},{}) REST request to update daily candidate roll list config:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), input);
        talentSearchFolderService.updateDailyCandidateRollList(input);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "get a talent search folder")
    @GetMapping("/daily-candidate-roll-list/config")
    public ResponseEntity<List<TalentSearchFolderData>> getDailyCandidateRollListConfig() {
        log.info("[APN] Talent Search Folder({},{}) REST request to get daily candidate roll list config", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        return ResponseEntity.ok(talentSearchFolderService.getDailyCandidateRollListConfig());
    }

    /***
     * get a search folder
     * @param folderId
     * @return
     */
    @ApiOperation(value = "get a talent search folder")
    @GetMapping("/search-folders/{folderId}")
    public ResponseEntity<TalentSearchFolderDTO> getTalentSearchFolder(@PathVariable Long folderId, @RequestParam(value = "checkUserFilter", required = false, defaultValue = "false") boolean checkUserFilter
            , @RequestParam(value = "rollListJump", required = false) String rollListJump) {
        log.info("[APN] Talent Search Folder({},{}) REST request to get a Talent Search folder:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), folderId);
        return ResponseEntity.ok(talentSearchFolderService.getTalentSearchFolder(folderId, checkUserFilter, rollListJump));
    }

    @PostMapping("/search-folders/team-add-user/roll-list")
    public ResponseEntity<Void> teamAddUserSetRollList(@RequestBody TeamUserSetRollList input) {
        talentSearchFolderService.teamAddUserSetRollList(input);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/search-folders/team-remove-user/roll-list")
    public ResponseEntity<Void> teamRemoveUserSetRollList(@RequestBody TeamUserSetRollList input) {
        talentSearchFolderService.teamRemoveUserSetRollList(input);
        return ResponseEntity.ok().build();
    }


    //根据search-folder信息设置默认首页roll list， sql较难实现 使用代码实现
    @PostMapping("/search-folders/set-default-roll-list")
    public ResponseEntity<TalentSearchFolderDTO> talentSearchFolderSetDefaultRollList() {
        talentSearchFolderService.talentSearchFolderSetDefaultRollList();
        return ResponseEntity.ok().build();
    }

    /***
     * find all searchFolder based on condition
     * @param folderSearchRequestDTO
     * @param pageable
     * @return List<TalentSearchFoldeStatusDTO></TalentSearchFoldeStatusDTO>
     */
    @PostMapping("/search-folders/search")
    public ResponseEntity<List<TalentSearchFolderStatusDTO>> findSearchTalentFolders(@Valid @RequestBody FolderSearchRequestDTO folderSearchRequestDTO, @ApiParam Pageable pageable) {
        log.info("[APN: Talent @{}] REST request to search talent folder :{}", SecurityUtils.getUserId(), pageable);
        Page<TalentSearchFolderStatusDTO> talentFolderDetails = talentSearchFolderListPageService.findSearchFolders(folderSearchRequestDTO, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(talentFolderDetails, "/api/v3/search-folders/search");
        return new ResponseEntity<>(talentFolderDetails.getContent(), headers, HttpStatus.OK);
    }

    /***
     * Add relate job folders
     * @return FolderListDTO
     */
    @PostMapping("/relate-job-folders")
    @NoRepeatSubmit
    public ResponseEntity<TalentRelateJobFolderWithCreatedFlag> addRelateJobFolders(@RequestBody TalentRelateJobFoldersDTO talentRelateJobFoldersDTO) throws Exception {
        log.info("[APN: Job @{}] REST request to add relate job folders", SecurityUtils.getUserId());
        TalentRelateJobFolderWithCreatedFlag folderDTO = talentRelateJobFolderService.addRelateJobFolders(talentRelateJobFoldersDTO);
        return ResponseEntity.ok(folderDTO);
    }


    /***
     * Edit relate job folders
     * @return FolderListDTO
     */
    @PutMapping("/relate-job-folders/{folderId}")
    @NoRepeatSubmit
    public ResponseEntity<TalentRelateJobFoldersDTO> editRelateJobFolders(@PathVariable String folderId, @RequestBody TalentRelateJobFoldersDTO talentRelateJobFoldersDTO) {
        log.info("[APN: Job @{}] REST request to edit relate job folders", SecurityUtils.getUserId());
        TalentRelateJobFoldersDTO folderDTO = talentRelateJobFolderService.editRelateJobFolders(folderId, talentRelateJobFoldersDTO);
        return ResponseEntity.ok(folderDTO);
    }

    /***
     * Merge relate job folders request
     * @return FolderListDTO
     */
    @PostMapping("/relate-job-folders/merge/{folderId}")
    @NoRepeatSubmit
    public ResponseEntity<TalentRelateJobFoldersDTO> mergeRelateJobFoldersRequest(@PathVariable String folderId) {
        log.info("[APN: Job @{}] REST request to merge relate job folders request", SecurityUtils.getUserId());
        TalentRelateJobFoldersDTO folderDTO = talentRelateJobFolderService.mergeRelateJobFoldersRequest(folderId);
        return ResponseEntity.ok(folderDTO);
    }


    /***
     * Reject relate job folders request
     * @return FolderListDTO
     */
    @DeleteMapping("/relate-job-folders/merge/{folderId}")
    @NoRepeatSubmit
    public ResponseEntity<Void> rejectRelateJobFoldersRequest(@PathVariable String folderId) {
        log.info("[APN: Job @{}] REST request to reject relate job folders request", SecurityUtils.getUserId());
        talentRelateJobFolderService.rejectRelateJobFoldersRequest(folderId);
        return ResponseEntity.ok(null);
    }


    /***
     * delete relate job folders
     * @return FolderListDTO
     */
    @DeleteMapping("/relate-job-folders/{folderId}")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteRelateJobFolders(@PathVariable String folderId) {
        log.info("[APN: Job @{}] REST request to delete relate job folders", SecurityUtils.getUserId());
        talentRelateJobFolderService.deleteRelateJobFolders(folderId);
        return ResponseEntity.ok(null);
    }

    /***
     * Add talent to relate job folders
     * @return FolderListDTO
     */
    @PostMapping("/relate-job-folders/{folderId}/add-talents-to-folders")
    @NoRepeatSubmit
    public ResponseEntity<AddTalentsToFoldersOutput> addTalentsToFolders(@PathVariable String folderId, @RequestBody TalentsToFoldersDTO talentsToFoldersDTO) {
        log.info("[APN: Job @{}] REST request to add talents to relate job folders", SecurityUtils.getUserId());
        AddTalentsToFoldersOutput addTalentsToFoldersOutput = talentRelateJobFolderService.addTalentsToFolders(folderId, talentsToFoldersDTO);
        return ResponseEntity.ok(addTalentsToFoldersOutput);
    }

    /***
     * Remove talent to relate job folders
     * @return FolderListDTO
     */
    @PatchMapping("/relate-job-folders/{folderId}/remove-talents-to-folders")
    @NoRepeatSubmit
    public ResponseEntity<Void> removeTalentsToFolders(@PathVariable("folderId") String folderId, @RequestBody TalentsToFoldersDTO talentsToFoldersDTO) {
        log.info("[APN: Job @{}] REST request to remove talents to relate job folders", SecurityUtils.getUserId());
        talentRelateJobFolderService.removeTalentsToFolders(folderId, talentsToFoldersDTO);
        return ResponseEntity.ok(null);
    }

    /***
     * Get relate job folders
     * @return FolderListDTO
     */
    @GetMapping("/relate-job-folders/{folderId}")
    public ResponseEntity<TalentRelateJobFoldersDTO> getRelateJobFolders(@PathVariable String folderId) {
        log.info("[APN: Job @{}] REST request to get relate job folders", SecurityUtils.getUserId());
        TalentRelateJobFoldersDTO folderDTO = talentRelateJobFolderService.getRelateJobFolders(folderId);
        return ResponseEntity.ok(folderDTO);
    }

    /***
     * update url expiration time from relate job folder
     * @return FolderListDTO
     */
    @PutMapping("/relate-job-folders/{folderId}/expire-time")
    public ResponseEntity<TalentRelateJobFoldersDTO> updateSharedURLExpirationTimeOnRelateJobFolder(@PathVariable String folderId, @RequestBody TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO) {
        log.info("[APN: Job @{}] REST request to update relate job folders expire Time: {}", SecurityUtils.getUserId(), talentRelateJobFolderRelationDTO);
        String currentFolderId = talentRelateJobFolderJobSharingPlatformProcessService.updateSharingPlatformURLExpirationTime(folderId, talentRelateJobFolderRelationDTO);
        TalentRelateJobFoldersDTO folderDTO = talentRelateJobFolderService.getRelateJobFolders(folderId);
        return ResponseEntity.ok(folderDTO);
    }

    /**
     * create a relation between talent relate job folder and job sharing
     * @param talentRelateJobFolderRelationDTO
     * @return
     */
    @PostMapping("/relate-job-folders/expire-time")
    public ResponseEntity<String> createOrUpdateRelationOnRelatedJobFolderAndJobSharing(@RequestBody TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO) {
        log.info("[APN: Job @{}] REST request to update relate job folders expire Time: {}", SecurityUtils.getUserId(), talentRelateJobFolderRelationDTO);
        String currentFolderId = talentRelateJobFolderJobSharingPlatformProcessService.createOrUpdateJobSharingAndTalentRelateJobFolderRelation(talentRelateJobFolderRelationDTO);
        return ResponseEntity.ok(currentFolderId);
    }

    //更新旧格式为新的嵌套格式，sql不容易实现 使用代码实现
    @ApiOperation(value = "update a talent search folder condition")
    @GetMapping("/search-folders/update-search-condition")
    public ResponseEntity<Void> updateSearchCondition(@RequestParam(required = false) Long folderId) {
        talentSearchFolderService.updateSearchCondition(folderId);
        return ResponseEntity.ok().build();
    }
}
