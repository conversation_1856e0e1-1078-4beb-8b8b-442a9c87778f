package com.altomni.apn.talent.utils;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.errors.CustomParameterizedException;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.codec.digest.HmacUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;

public class NotificationUtils {


    private static final Logger log = LoggerFactory.getLogger(NotificationUtils.class);
    // HMAC encryption algorithm.
    private static final String HMAC_SHA_256 = "HmacSHA256";
    private static final okhttp3.MediaType JSON_TYPE = okhttp3.MediaType.parse("application/json; charset=utf-8");

    public static void sendWithOkHttp(String url, String bodyJson){
        try {
            // send with okhttp3
            okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON_TYPE, bodyJson);
            Request request = new Request.Builder().url(url).post(body).build();
            okhttp3.Response response = new OkHttpClient().newCall(request).execute();
            String responseBody = response.body() != null ? response.body().string() : null;
            log.info("[APN: sendLarkNotification @-1] get response from lark: {}", responseBody);
            if (!response.isSuccessful()) {
                throw new CustomParameterizedException(responseBody);
            }
        }catch (IOException e){
            log.error("[APN: sendLarkNotification @-1] send msg to lark error: {}", e.getMessage());
        }
    }

    public static String buildBasicLarkContent(String webhookKey, String msg){
        Long timestamp = Instant.now().getEpochSecond();
        String key = timestamp + "\n" + webhookKey;
        byte[] bytes = HmacUtils.getInitializedMac(HMAC_SHA_256, key.getBytes()).doFinal();
        String signature = Base64.getEncoder().encodeToString(bytes);

        JSONObject json = new JSONObject();
        json.fluentPut("timestamp", timestamp);
        json.fluentPut("sign", signature);
        json.fluentPut("msg_type", "text");
        json.fluentPut("content", new JSONObject(new HashMap<String, Object>(){{put("text", msg);}}));
        return json.toJSONString();
    }

    public static void sendAlertToLark(String webhookKey, String webhookUrl, String msg) {
        String json = buildBasicLarkContent(webhookKey, msg);

        log.info("[APN: sendAlertToLark @-1] send msg to lark: {}", json);

        sendWithOkHttp(webhookUrl, json);
        
    }
}

