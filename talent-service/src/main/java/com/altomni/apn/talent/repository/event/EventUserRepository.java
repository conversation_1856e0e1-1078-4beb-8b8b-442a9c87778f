package com.altomni.apn.talent.repository.event;

import com.altomni.apn.talent.domain.event.EventUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the EventUser entity.
 */
@Repository
public interface EventUserRepository extends JpaRepository<EventUser, Long> {

    List<EventUser> findAllByEventId(Long eventId);

    List<EventUser> findAllByEventIdAndEmail(Long eventId, String email);
}
