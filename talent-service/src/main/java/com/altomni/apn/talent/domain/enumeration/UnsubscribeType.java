package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The UnsubscribeType enumeration.
 */
public enum UnsubscribeType implements ConvertedEnum<Integer>{
    EMAIL_APPLY_CANDIDATE(0),
    EMAIL_APPLICATION_STATUS_CHANGE(5),
    EMAIL_ASSIGN_JOB(10);

    private final Integer dbValue;

    UnsubscribeType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<UnsubscribeType, Integer> resolver =
        new ReverseEnumResolver<>(UnsubscribeType.class, UnsubscribeType::toDbValue);

    public static UnsubscribeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
