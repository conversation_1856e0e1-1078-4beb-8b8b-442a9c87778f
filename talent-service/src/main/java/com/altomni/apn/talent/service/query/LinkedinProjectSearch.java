package com.altomni.apn.talent.service.query;

import com.altomni.apn.common.domain.job.QJobV3;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Status;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Visibility;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinProject;
import com.altomni.apn.talent.domain.linkedinproject.QLinkedinProject;
import com.altomni.apn.talent.domain.linkedinproject.QLinkedinProjectMember;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.JPAExpressions;
import org.apache.commons.lang3.StringUtils;


public class LinkedinProjectSearch {

    public static BooleanExpression allLinkedinProjects(String projectName, String jobName, Long createdBy, Status status) {
        QLinkedinProject qLinkedinProject = QLinkedinProject.linkedinProject;
        PathBuilder<LinkedinProject> entityPath = new PathBuilder<>(LinkedinProject.class, "linkedinProject");
        MyPredicateBuilder builder = new MyPredicateBuilder();
        BooleanExpression baseCondition = qLinkedinProject.visibility.eq(Visibility.VISIBLE_TO_ALL);
        if (StringUtils.isNotEmpty(projectName)) {
            baseCondition.and(qLinkedinProject.name.contains(projectName));
        }
        if (createdBy != null) {
            baseCondition.and(qLinkedinProject.createdUserId.eq(createdBy));
        }
        if (status != null) {
            baseCondition.and(qLinkedinProject.status.eq(status));
        }
        QJobV3 qJob = QJobV3.jobV3;
        BooleanExpression jobNameCondition = null;
        if (StringUtils.isNotEmpty(jobName)) {
            jobNameCondition = qLinkedinProject.jobId.in(JPAExpressions.selectFrom(qJob)
                .where(qJob.id.eq(qLinkedinProject.jobId).and(qJob.title.contains(jobName))).select(qLinkedinProject.jobId));
        }
        return baseCondition.and(qLinkedinProject.tenantId.eq(SecurityUtils.getTenantId()))
            .and(jobNameCondition).and(builder.build(entityPath));
    }

    public static BooleanExpression myLinkedinProjects(String projectName, String jobName, Long createdBy, Status status) {
        QLinkedinProject qLinkedinProject = QLinkedinProject.linkedinProject;
        PathBuilder<LinkedinProject> entityPath = new PathBuilder<>(LinkedinProject.class, "linkedinProject");
        MyPredicateBuilder builder = new MyPredicateBuilder();
        QLinkedinProjectMember qLinkedinProjectMember = QLinkedinProjectMember.linkedinProjectMember;
        BooleanExpression sharedToMeCondition = qLinkedinProject.id.in(JPAExpressions.selectFrom(qLinkedinProjectMember)
            .where(qLinkedinProjectMember.linkedinProjectId.eq(qLinkedinProject.id)
                .and(qLinkedinProjectMember.userId.eq(SecurityUtils.getUserId()))).select(qLinkedinProject.id));
        BooleanExpression baseCondition = qLinkedinProject.createdUserId.eq(SecurityUtils.getUserId()).or(sharedToMeCondition);
        if (StringUtils.isNotEmpty(projectName)) {
            baseCondition.and(qLinkedinProject.name.contains(projectName));
        }
        if (createdBy != null) {
            baseCondition.and(qLinkedinProject.createdUserId.eq(createdBy));
        }
        if (status != null) {
            baseCondition.and(qLinkedinProject.status.eq(status));
        }
        return baseCondition.and(qLinkedinProject.tenantId.eq(SecurityUtils.getTenantId())).and(builder.build(entityPath));
    }
}
