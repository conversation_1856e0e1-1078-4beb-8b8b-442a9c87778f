package com.altomni.apn.talent.domain.record;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "excel_update_talent_record")
public class ExcelUpdateTalentRecord extends AbstractAuditingEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "md5")
    private String md5;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "new_data", columnDefinition = "MEDIUMTEXT")
    private String newData;

    @Column(name = "old_data", columnDefinition = "MEDIUMTEXT")
    private String oldData;

}
