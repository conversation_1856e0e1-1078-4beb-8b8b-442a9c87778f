package com.altomni.apn.talent.service.dto.talent;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.talent.domain.talent.Tag;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.List;

/**
 * A DTO for the Tag entity.
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SpecialTagDTO implements Serializable {

    private static final long serialVersionUID = 5208842046839376339L;

    private Long id;

    private String type;

    private String tagName;

    private List<String> tagValues;

    private String description;

    public static SpecialTagDTO fromTag(Tag t) {
        SpecialTagDTO dto = new SpecialTagDTO();
        dto.setId(t.getId());
        dto.setType(t.getType());
        dto.setTagName(t.getTagName());
        dto.setDescription(t.getDescription());
        dto.setTagValues(JsonUtil.fromJsonArray(t.getTagValues()));
        return dto;
    }

    public JSONObject toJSON() {
        JSONObject result = new JSONObject();
        JsonUtil.fluentPut(result, "type", type);
        JsonUtil.fluentPut(result, "tagName", tagName);
        JsonUtil.fluentPut(result, "tagValues", tagValues);
        JsonUtil.fluentPut(result, "description", description);
        return result;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public List<String> getTagValues() {
        return tagValues;
    }

    public void setTagValues(List<String> tagValues) {
        this.tagValues = tagValues;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "SpecialTagDTO{" +
                "id=" + id +
                ", type='" + type + '\'' +
                ", tagName='" + tagName + '\'' +
                ", tagValues=" + tagValues +
                ", description='" + description + '\'' +
                '}';
    }
}
