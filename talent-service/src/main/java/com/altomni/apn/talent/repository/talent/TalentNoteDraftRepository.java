package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.talent.TalentNoteDraft;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


/**
 * Spring Data JPA repository for the TalentNoteDraft entity.
 */
@Repository
public interface TalentNoteDraftRepository extends JpaRepository<TalentNoteDraft, Long> {

    TalentNoteDraft findByTalentIdAndUserId(Long talentId, Long userId);

    @Transactional
    @Modifying
    @Query(value = "delete from talent_note_draft where talent_id = ?1 and user_id = ?2 ", nativeQuery = true)
    void deleteByTalentIdAndUserId(Long talentId, Long userId);

    @Modifying
    @Transactional
    @Query(value = """
            INSERT INTO talent_note_draft (talent_id, user_id, draft_data) 
                    VALUES (:talentId, :userId, :draftData) 
                    ON DUPLICATE KEY UPDATE draft_data = VALUES(draft_data)
             """, nativeQuery = true)
    void upsertByTalentIdAndUserId(
            @Param("talentId") Long talentId,
            @Param("userId") Long userId,
            @Param("draftData") String draftData
    );
}
