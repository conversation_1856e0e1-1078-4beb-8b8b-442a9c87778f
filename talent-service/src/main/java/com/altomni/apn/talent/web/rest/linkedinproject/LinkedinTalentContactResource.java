package com.altomni.apn.talent.web.rest.linkedinproject;

import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinTalentContactDTO;
import com.altomni.apn.talent.service.linkedinproject.LinkedinTalentContactService;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinTalentContactVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing LinkedinTalentContact.
 */
@RestController
@RequestMapping("/api/v3")
public class LinkedinTalentContactResource {

    private final Logger log = LoggerFactory.getLogger(LinkedinTalentContactResource.class);

    private static final String ENTITY_NAME = "LinkedinTalentContact";

    private final LinkedinTalentContactService linkedinTalentContactService;

    public LinkedinTalentContactResource(LinkedinTalentContactService linkedinTalentContactService) {
        this.linkedinTalentContactService = linkedinTalentContactService;
    }

    @PostMapping("/linkedin-talent-contacts/linkedinTalentId/{linkedinTalentId}")
    public ResponseEntity<List<LinkedinTalentContactVO>> replace(@PathVariable String linkedinTalentId, @RequestBody List<LinkedinTalentContactDTO> linkedinTalentContacts) {
        log.info("[APN: LinkedinTalentContact @{}] REST request to replace linkedinTalentId: {}, linkedinTalentContacts: {}, ",
            SecurityUtils.getCurrentUserLogin(), linkedinTalentId, linkedinTalentContacts);
        List<LinkedinTalentContactVO> result = linkedinTalentContactService.replace(linkedinTalentId, linkedinTalentContacts);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, linkedinTalentId.toString()))
            .body(result);
    }

    @GetMapping("/linkedin-talent-contacts/linkedinTalentId/{linkedinTalentId}")
    public List<LinkedinTalentContactVO> findAll(@PathVariable String linkedinTalentId) {
        log.info("[APN: LinkedinTalentContact @{}] REST request to get all linkedinTalentId: {}", SecurityUtils.getCurrentUserLogin(), linkedinTalentId);
        return linkedinTalentContactService.findAll(linkedinTalentId);
    }

    @DeleteMapping("/linkedin-talent-contacts/linkedinTalentId/{linkedinTalentId}")
    public ResponseEntity<Void> delete(@PathVariable String linkedinTalentId) {
        log.info("[APN: LinkedinTalentContact @{}] REST request to delete all linkedin talent contacts by linkedinTalentId: {}", SecurityUtils.getCurrentUserLogin(), linkedinTalentId);
        linkedinTalentContactService.deleteAllContactsByLinkedinTalentId(linkedinTalentId);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, linkedinTalentId)).build();
    }
}
