package com.altomni.apn.talent.domain.linkedinproject;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatusConverter;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatusConverter;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectTalentDTO;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * A LinkedinProjectTalent.
 */
@Entity
@Table(name = "linkedin_project_talent")
public class LinkedinProjectTalent extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 321172203542826437L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @NotNull
    @Column(name = "linkedin_project_id")
    private Long linkedinProjectId;

    @NotNull
    @Column(name = "linkedin_talent_id")
    private String linkedinTalentId;

    @NotNull
    @Convert(converter = CandidateStatusConverter.class)
    @Column(name = "candidate_status")
    private CandidateStatus candidateStatus;

    @NotNull
    @Convert(converter = ContactStatusConverter.class)
    @Column(name = "contact_status")
    private ContactStatus contactStatus;

    @Column(name = "has_contact_info")
    private Boolean hasContactInfo;

    @Column(name = "is_in_application")
    private Boolean isInApplication;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "tenantId", "linkedinProjectId", "linkedinTalentId"));

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getLinkedinProjectId() {
        return linkedinProjectId;
    }

    public void setLinkedinProjectId(Long linkedinProjectId) {
        this.linkedinProjectId = linkedinProjectId;
    }

    public String getLinkedinTalentId() {
        return linkedinTalentId;
    }

    public void setLinkedinTalentId(String linkedinTalentId) {
        this.linkedinTalentId = linkedinTalentId;
    }

    public CandidateStatus getCandidateStatus() {
        return candidateStatus;
    }

    public void setCandidateStatus(CandidateStatus candidateStatus) {
        this.candidateStatus = candidateStatus;
    }

    public ContactStatus getContactStatus() {
        return contactStatus;
    }

    public void setContactStatus(ContactStatus contactStatus) {
        this.contactStatus = contactStatus;
    }

    public Boolean getHasContactInfo() { return hasContactInfo; }

    public void setHasContactInfo(Boolean hasContactInfo) { this.hasContactInfo = hasContactInfo; }

    public Boolean getInApplication() { return isInApplication; }

    public void setInApplication(Boolean inApplication) { isInApplication = inApplication; }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinProjectTalent linkedinProjectTalent = (LinkedinProjectTalent) o;
        if (linkedinProjectTalent.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinProjectTalent.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinProjectTalent{" +
            "id=" + id +
            ", tenantId=" + tenantId +
            ", linkedinProjectId=" + linkedinProjectId +
            ", linkedinTalentId='" + linkedinTalentId + '\'' +
            ", candidateStatus=" + candidateStatus +
            ", contactStatus=" + contactStatus +
            ", hasContactInfo=" + hasContactInfo +
            ", isInApplication=" + isInApplication +
            '}';
    }

    public static LinkedinProjectTalent fromLinkedinProjectTalentDTO(LinkedinProjectTalentDTO linkedinProjectTalentDTO) {
        LinkedinProjectTalent linkedinProjectTalent = new LinkedinProjectTalent();
        ServiceUtils.myCopyProperties(linkedinProjectTalentDTO, linkedinProjectTalent);
        return linkedinProjectTalent;
    }
}
