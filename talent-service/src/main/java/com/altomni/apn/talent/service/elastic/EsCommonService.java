package com.altomni.apn.talent.service.elastic;

import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.common.dto.search.TalentSearchGroup;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentESConditionDTO;
import com.altomni.apn.common.dto.talent.TalentESDocument;
import com.altomni.apn.talent.service.dto.elastic.EsSearchByContactsDTO;
import com.altomni.apn.talent.service.dto.talent.TalentCategoryCountRequestDTO;
import com.altomni.apn.talent.service.elastic.dto.SearchEsBySource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Common search elastic search service
 * <AUTHOR>
 */
public interface EsCommonService {

    TalentDTOV3 getEsTalentByEsId(String esId) throws IOException;

    TalentDTOV3 getTalent(String id) throws IOException;

    TalentDTOV3 getTalentForInternal(String id, Long tenantId) throws IOException;

//    EsResponseDTO searchTalents(String searchRequest) throws IOException;

    Page<TalentDTOV3> searchEsTalentsByContacts(EsSearchByContactsDTO searchRequest) throws IOException;

    TalentESDocument getTalentESDocument(TalentESConditionDTO condition) throws IOException;

    String searchContactsFromCommonPool(String esId) throws IOException;

    HttpResponse searchTalentFromCommonService(TalentSearchGroup searchGroup, Pageable pageable, List<EnumUserResponsibility> userResponsibilityList) throws IOException;

    HttpResponse searchTalentFromCommonService(SearchGroup searchGroup) throws IOException;

    Map<String, Long> getTalentCategoryCount(TalentCategoryCountRequestDTO requestDto) throws IOException;

    HttpResponse searchTalentFromCommonService(SearchGroup dataSearchGroup, SearchGroup countSearchGroup, Pageable pageable, List<EnumUserResponsibility> enumUserResponsibilityList) throws IOException;

    HttpResponse searchTalentSourceFromCommonService(SearchEsBySource searchEsBySource, Pageable pageable) throws IOException;

    String likeSearchSchool(String schoolName) throws IOException;
}
