package com.altomni.apn.talent.service.dto.linkedinproject;

import com.altomni.apn.talent.domain.linkedinproject.LinkedinProjectMember;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Status;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Visibility;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
public class LinkedinProjectDTO implements Serializable {

    private static final long serialVersionUID = 8489586936391610005L;

    private Long id;

    private Long jobId;

    @NotNull
    private String name;

    private Visibility visibility;

    private Status status;

    private String description;

    private List<LinkedinProjectMember> members;

    private Boolean favorite = Boolean.FALSE;

    private Boolean isSystemGenerated = Boolean.FALSE;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Visibility getVisibility() {
        return visibility;
    }

    public void setVisibility(Visibility visibility) {
        this.visibility = visibility;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<LinkedinProjectMember> getMembers() {
        return members;
    }

    public void setMembers(List<LinkedinProjectMember> members) {
        this.members = members;
    }

    public Boolean getFavorite() {
        return favorite;
    }

    public void setFavorite(Boolean favorite) {
        this.favorite = favorite;
    }

    public Boolean getSystemGenerated() { return isSystemGenerated; }

    public void setSystemGenerated(Boolean systemGenerated) { isSystemGenerated = systemGenerated; }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinProjectDTO linkedinProject = (LinkedinProjectDTO) o;
        if (linkedinProject.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinProject.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinProjectDTO{" +
            "id=" + id +
            ", jobId=" + jobId +
            ", name='" + name + '\'' +
            ", visibility=" + visibility +
            ", status=" + status +
            ", description='" + description + '\'' +
            ", members=" + members +
            ", favorite=" + favorite +
            ", isSystemGenerated=" + isSystemGenerated +
            '}';
    }
}
