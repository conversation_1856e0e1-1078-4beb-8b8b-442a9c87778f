package com.altomni.apn.talent.repository.event;

import com.altomni.apn.talent.domain.enumeration.event.Status;
import com.altomni.apn.talent.domain.event.Event;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the Event entity.
 */
@Repository
public interface EventRepository extends JpaRepository<Event, Long> {

    List<Event> findAllByStatus(Status status);
}
