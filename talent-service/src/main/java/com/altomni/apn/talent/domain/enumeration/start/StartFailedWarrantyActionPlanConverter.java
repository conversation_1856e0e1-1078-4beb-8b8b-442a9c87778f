package com.altomni.apn.talent.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class StartFailedWarrantyActionPlanConverter extends AbstractAttributeConverter<StartFailedWarrantyActionPlan, Integer> {
    public StartFailedWarrantyActionPlanConverter() {
        super(StartFailedWarrantyActionPlan::toDbValue, StartFailedWarrantyActionPlan::fromDbValue);
    }
}
