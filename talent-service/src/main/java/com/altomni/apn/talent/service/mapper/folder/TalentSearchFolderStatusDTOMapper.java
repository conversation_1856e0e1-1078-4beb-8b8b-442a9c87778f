package com.altomni.apn.talent.service.mapper.folder;

import com.altomni.apn.common.dto.search.TalentSearchFolderConditionDTO;
import com.altomni.apn.talent.domain.folder.TalentSearchFolder;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderStatusDTO;
import com.altomni.apn.talent.utils.SearchCriteriaConverter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TalentSearchFolderStatusDTOMapper {

    @Mapping(target = "searchCriteria", source = "searchCriteria", qualifiedByName = "searchCriteriaToJsonString")
    TalentSearchFolder toEntity(TalentSearchFolderStatusDTO dto);

    @Mapping(target = "searchCriteria", source = "searchCriteria", qualifiedByName = "jsonStringToSearchCriteria")
    TalentSearchFolderStatusDTO toDto(TalentSearchFolder entity);

    @Named("mapWithoutData")
    @Mapping(target = "searchCriteria", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(source = "createdDate", target = "createdDate")
    TalentSearchFolderStatusDTO mapWithBasicData(TalentSearchFolder entity);

    @IterableMapping(qualifiedByName = "mapWithoutData")
    List<TalentSearchFolderStatusDTO> toSimpleDto(List<TalentSearchFolder> entities);


    default TalentSearchFolder fromId(Long id) {
        if (id == null) {
            return null;
        }
        TalentSearchFolder talentSearchFolder = new TalentSearchFolder();
        talentSearchFolder.setId(id);
        return talentSearchFolder;
    }


    @Named("searchCriteriaToJsonString")
    static String searchCriteriaToJsonString(TalentSearchFolderConditionDTO conditionDTO) {
        if (conditionDTO == null) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(conditionDTO);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting List<T> to JSON string", e);
        }
    }

    @Named("jsonStringToSearchCriteria")
    static TalentSearchFolderConditionDTO jsonStringToSearchCriteria(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        return SearchCriteriaConverter.fromJson(jsonString);
    }

}
