package com.altomni.apn.talent.service.dto.tracking;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(value = "talent tracking group")
@NoArgsConstructor
@AllArgsConstructor
public class TalentTrackingGroupDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "name")
    @NotEmpty
    private String name;

    @Valid
    @ApiModelProperty(value = "members")
    private List<TalentTrackingGroupMemberDTO> members;

    @ApiModelProperty(value = "operatorLinkedinId")
    @NotEmpty
    private String operatorLinkedinId;

}
