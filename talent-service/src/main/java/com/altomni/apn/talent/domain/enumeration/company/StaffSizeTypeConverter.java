package com.altomni.apn.talent.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class StaffSizeTypeConverter extends AbstractAttributeConverter<StaffSizeType, Integer> {
    public StaffSizeTypeConverter() {
        super(StaffSizeType::toDbValue, StaffSizeType::fromDbValue);
    }
}
