package com.altomni.apn.talent.constants;

public class EnReportPDFFormat implements ReportPDFFormat {
    private static final String HEADER_TITLE = "Candidate Report";
    private static final String HEADER_USER_NAME = "Consultant: {}";
    private static final String HEADER_USER_PHONE = "Tel: {}";
    private static final String HEADER_USER_EMAIL = "Email: {}";
    private static final String HEADER_CREATE_DATE = "Creation Date: {}";
    private static final String JOB_RECOMMENDED = "Positions applied for";
    private static final String RECOMMENDED_REASON = "Summary";
    private static final String TALENT_INFO = "PERSONAL INFORMATION";
    private static final String TALENT_NAME = "Candidate’s Name:";
    private static final String TALENT_BIRTHDAY = "Date of Birth:";
    private static final String TALENT_GENDER = "Gender:";
    private static final String CURRENT_LOCATION = "Current Location:";
    private static final String PREFERRED_LOCATION = "Preferred Location:";
    private static final String WORK_AUTHORIZATION = "Work Authorization:";
    private static final String TALENT_PHONE = "Tel:";
    private static final String TALENT_EMAIL = "Email:";
    private static final String PREFERRED_SALARY = "Preferred Salary:";
    private static final String WORK_EXPERIENCE = "EMPLOYMENT HISTORY";
    private static final String PROJECT_EXPERIENCE = "PROJECT EXPERIENCE";
    private static final String EDUCATION = "EDUCATION";

    private static final String SKILL = "WORK SKILLS";
    private static final String LANGUAGE = "LANGUAGE";
    private static final String FOOTER = "Confidential: This report is only prepared for the aforementioned client, and as it involves confidential information, it is limited to reading by relevant personnel of the client.";

    @Override
    public String headerTitle() {
        return HEADER_TITLE;
    }

    @Override
    public String headerUserName() {
        return HEADER_USER_NAME;
    }

    @Override
    public String headerUserPhone() {
        return HEADER_USER_PHONE;
    }

    @Override
    public String headerUserEmail() {
        return HEADER_USER_EMAIL;
    }

    @Override
    public String headerCreateDate() {
        return HEADER_CREATE_DATE;
    }

    @Override
    public String jobRecommended() {
        return JOB_RECOMMENDED;
    }

    @Override
    public String recommendedReason() {
        return RECOMMENDED_REASON;
    }

    @Override
    public String talentInfo() {
        return TALENT_INFO;
    }

    @Override
    public String talentName() {
        return TALENT_NAME;
    }

    @Override
    public String talentBirthday() {
        return TALENT_BIRTHDAY;
    }

    @Override
    public String talentGender() {
        return TALENT_GENDER;
    }

    @Override
    public String currentLocation() {
        return CURRENT_LOCATION;
    }

    @Override
    public String preferredLocation() {
        return PREFERRED_LOCATION;
    }

    @Override
    public String workAuthorization() {
        return WORK_AUTHORIZATION;
    }

    @Override
    public String talentPhone() {
        return TALENT_PHONE;
    }

    @Override
    public String talentEmail() {
        return TALENT_EMAIL;
    }

    @Override
    public String preferredSalary() {
        return PREFERRED_SALARY;
    }

    @Override
    public String workExperience() {
        return WORK_EXPERIENCE;
    }

    @Override
    public String projectExperience() {
        return PROJECT_EXPERIENCE;
    }

    @Override
    public String education() {
        return EDUCATION;
    }

    @Override
    public String skill() {
        return SKILL;
    }

    @Override
    public String language() {
        return LANGUAGE;
    }

    @Override
    public String footer() {
        return FOOTER;
    }
}
