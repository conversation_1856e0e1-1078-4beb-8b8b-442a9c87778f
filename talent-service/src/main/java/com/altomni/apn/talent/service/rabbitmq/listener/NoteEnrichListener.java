package com.altomni.apn.talent.service.rabbitmq.listener;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.talent.config.env.EsfillerMQProperties;
import com.altomni.apn.talent.service.talent.TalentNoteService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class NoteEnrichListener {

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource(name = "esfillerRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource
    private TalentNoteService talentNoteService;

    @RabbitListener(queues = {"${application.note-enrich.result-queue}"})
    @RabbitHandler
    public void noteEnrichResultResponseMsg(Message message, Channel channel) throws IOException {
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        try{
            log.info("Note enrich result MQ Response: receive data: {}", json);
            talentNoteService.saveParsedResult(json);
            // 检查通道状态后再确认
            if (channel.isOpen()) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            }
            log.info("Note enrich result MQ Response end.");
        } catch (Exception e) {
            log.error("Note enrich result MQ Response:  fail to process message: {}", e.getMessage());
            try {
                // 检查通道状态后再拒绝
                if (channel.isOpen()) {
                    channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
                }
            } catch (IOException ioException) {
                log.error("Failed to nack message: {}", ioException.getMessage());
            }
            String notifyMessage = "Receive note enrich result error. input: " + json + "\n\t" +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), notifyMessage);
        }
    }

    public void sendNoteEnrichNotify(JSONObject notify) {
        String requestBody = JSONUtil.toJsonStr(notify);
        log.info("SendNoteEnrichNotify, data: {}", requestBody);
        rabbitTemplate.convertAndSend(esfillerMQProperties.getNoteEnrichNotifyExchange(), esfillerMQProperties.getNoteEnrichNotifyRoutingKey(), requestBody, message -> {
            log.info("send note enrich notify rabbitMQ, Message: {}", message);
            return message;
        });
    }

}
