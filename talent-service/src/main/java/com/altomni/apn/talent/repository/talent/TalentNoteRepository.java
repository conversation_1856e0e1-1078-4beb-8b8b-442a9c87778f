package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.talent.TalentNote;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;


/**
 * Spring Data JPA repository for the TalentNote entity.
 */
@Repository
public interface TalentNoteRepository extends JpaRepository<TalentNote, Long> {

    @Query(value = "SELECT * FROM talent_note WHERE talent_id=?1 order by created_date DESC  ",nativeQuery = true)
//    @Query(value = "SELECT TalentNote FROM TalentNote WHERE talentId = ?1 ORDER BY createdDate DESC")
    List<TalentNote> findAllByTalentId(Long talentId);

    List<TalentNote> findAllByTalentIdAndAgencyIdOrderByCreatedDateDesc(Long talentId, Long agencyId);

    List<TalentNote> findAllByTalentIdAndParsedResultIsNotNullAndEnrichResultIsNull(Long talentId);

    @Query(value = "SELECT * FROM talent_note WHERE talent_id=?1 order by created_date DESC LIMIT 1 ",nativeQuery = true)
    TalentNote findLatestNoteByTalentId(Long talentId);

    @Modifying
    @Transactional
    @Query(value = "update talent_note set created_by = ?1, created_date = ?2, last_modified_by = ?3, last_modified_date = ?4 WHERE id = ?5", nativeQuery = true)
    int updateCreatedBy(String createdBy, Instant createdDate, String lastModifiedBy, Instant lastModifiedDate, Long id);

    List<TalentNote> findAllByTalentIdInOrderByCreatedDateDesc(List<Long> talentIds);

    @Modifying
    @Transactional
    @Query(value = "update talent_note set created_by = ?2, last_modified_by = ?2, puser_id = ?3 WHERE id = ?1", nativeQuery = true)
    int updateCreatedByAndLastModifiedByAndPUserId(Long id, String uid, Long puserId);


    @Modifying
    @Transactional
    @Query(value = "update talent_note set last_sync_time = ?2 WHERE id = ?1", nativeQuery = true)
    int updateLastSyncTime(Long id, Instant now);

    @Query(value = "SELECT t.id, t.title, t.note, t.additional_info, t.voip_phone_call_id FROM talent_note t WHERE t.voip_phone_call_id IN :phoneCallIds", nativeQuery = true)
    List<Object[]> findAllTalentNoteByPhoneCallIdIn(@Param("phoneCallIds") List<String> phoneCallIds);

    @Modifying
    @Transactional
    @Query(value = "update talent_note set parsed_result = ?2, enrich_result = ?3 WHERE id = ?1", nativeQuery = true)
    void updateEnrich(Long id, String parsedResult, String enrichResult);

    @Modifying
    @Transactional
    @Query(value = "update talent_note set enrich_result = ?2 WHERE id = ?1", nativeQuery = true)
    void updateOnlyEnrichNote(Long id, String enrichResult);
}
