package com.altomni.apn.talent.service.record.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.constants.Constants;
import com.altomni.apn.talent.domain.enumeration.record.TrackingType;
import com.altomni.apn.talent.domain.record.TalentTrackingRecord;
import com.altomni.apn.talent.repository.record.TalentTrackingRecordRepository;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.dto.record.TalentTrackingEmailDTO;
import com.altomni.apn.talent.service.dto.record.TalentTrackingRecordDTO;
import com.altomni.apn.talent.service.dto.record.TalentTrackingRecordSearchDTO;
import com.altomni.apn.talent.service.mail.MailService;
import com.altomni.apn.talent.service.record.TalentTrackingRecordService;
import com.altomni.apn.talent.service.vo.record.TalentTrackingRecordListVO;
import com.altomni.apn.talent.service.vo.record.TalentTrackingRecordVO;
import com.altomni.apn.talent.domain.vm.record.UserNameVM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.io.IOException;
import java.lang.reflect.Method;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class TalentTrackingRecordServiceImpl extends TalentTrackingBaseServiceImpl implements TalentTrackingRecordService {

    private final Logger log = LoggerFactory.getLogger(TalentTrackingRecordServiceImpl.class);

    private final TalentTrackingRecordRepository talentTrackingRecordRepository;

    private final MailService mailService;

    private final UserService userService;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final TalentApiPromptProperties talentApiPromptProperties;

    public TalentTrackingRecordServiceImpl(TalentTrackingRecordRepository talentTrackingRecordRepository,
                                           MailService mailService, UserService userService,CommonApiMultilingualConfig commonApiMultilingualConfig,TalentApiPromptProperties talentApiPromptProperties) {
        this.talentTrackingRecordRepository = talentTrackingRecordRepository;
        this.mailService = mailService;
        this.userService = userService;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.talentApiPromptProperties = talentApiPromptProperties;
    }

    @Override
    public TalentTrackingRecordVO save(TalentTrackingRecordDTO talentTrackingRecordDTO) {
        TalentTrackingRecord talentTrackingRecord = TalentTrackingRecord.fromTalentTrackingRecordDTO(talentTrackingRecordDTO);
        talentTrackingRecord.setTenantId(SecurityUtils.getTenantId());
        talentTrackingRecord.setUserId(SecurityUtils.getUserId());
        return TalentTrackingRecordVO.fromTalentTrackingRecord(talentTrackingRecordRepository.save(talentTrackingRecord));
    }

    @Override
    public void saveEmailTrackingRecord(MailVM mailVm) {
        if (CollectionUtils.isNotEmpty(mailVm.getTo())) {
            Set<String> removeDuplicateSets = new HashSet<>(mailVm.getTo());
            List<TalentTrackingRecord> trackingRecords = new ArrayList<>();
            for (String contact : removeDuplicateSets) {
                if (StringUtils.isNotEmpty(contact) && !contact.contains(Constants.INTELLIPRO_GROUP_COM)) {
                    trackingRecords.add(new TalentTrackingRecord().tenantId(SecurityUtils.getTenantId())
                            .userId(SecurityUtils.getUserId()).trackingType(TrackingType.EMAIL).touchTime(Instant.now()).contact(contact));
                }
            }
            if (CollectionUtils.isNotEmpty(trackingRecords)) {
                talentTrackingRecordRepository.saveAll(trackingRecords);
            }
        }
    }

    @Override
    public List<TalentTrackingRecordListVO> findAllTalentTrackingRecords(TalentTrackingRecordSearchDTO talentTrackingRecordSearchDTO) {
        List<String> contacts = talentTrackingRecordSearchDTO.getContacts().stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contacts)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.RECORD_FINDALLTALENTTRACKINGRECORDS_CONTACTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }

        List<TalentTrackingRecord> talentTrackingRecordList = talentTrackingRecordRepository.findAllByTenantIdAndContactList(SecurityUtils.getTenantId(), contacts);
        Map<Long, List<TalentTrackingRecord>> talentTrackingRecordListMap = talentTrackingRecordList.stream().collect(Collectors.groupingBy(TalentTrackingRecord::getUserId));

        List<Long> userIds = talentTrackingRecordList.stream().map(TalentTrackingRecord::getUserId).distinct().collect(Collectors.toList());
        List<UserNameVM> userNameList = searchUserNameByIds(userIds);
        Map<Long, UserNameVM> userNameMap = userNameList.stream().collect(Collectors.toMap(UserNameVM::getId, UserNameVM -> UserNameVM));

        List<TalentTrackingRecordListVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            for (Long userId : userIds) {
                List<TalentTrackingRecord> trackingRecords = talentTrackingRecordListMap.getOrDefault(userId, new ArrayList<>());
                if (CollectionUtils.isNotEmpty(trackingRecords)) {
                    TalentTrackingRecordListVO trackingRecordListDTO = new TalentTrackingRecordListVO();
                    if (userNameMap.containsKey(userId)) {
                        trackingRecordListDTO.setFullName(CommonUtils.formatFullName(userNameMap.get(userId).getFirstName(), userNameMap.get(userId).getLastName()));
                    }
                    trackingRecordListDTO.setUserId(userId);
                    List<TalentTrackingRecordDTO> trackingRecordDTOList = new ArrayList<>();
                    for (TalentTrackingRecord trackingRecord : trackingRecords) {
                        trackingRecordDTOList.add(TalentTrackingRecordDTO.fromTalentTrackingRecord(trackingRecord));
                    }
                    trackingRecordListDTO.setRecords(trackingRecordDTOList);
                    result.add(trackingRecordListDTO);
                }
            }
        }
        return result;
    }

    @Override
    public void sendRichEmail(TalentTrackingEmailDTO talentTrackingEmailDTO) {
        mailService.sendRichMail(talentTrackingEmailDTO.getFrom(), talentTrackingEmailDTO.getTo(), talentTrackingEmailDTO.getBcc(),
                talentTrackingEmailDTO.getCc(), talentTrackingEmailDTO.getSubject(), talentTrackingEmailDTO.getHtmlContent(), talentTrackingEmailDTO.getFiles());
        if (StringUtils.isEmpty(talentTrackingEmailDTO.getFrom())) {
            User user = userService.getUserById(SecurityUtils.getUserId()).getBody();
            if (Objects.isNull(user)) {
                log.error("[EmailService: CampaignServiceImpl] sentHtmlEmail no from attribute and cannot find current user!");
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.RECORD_SENDRICHEMAIL_USERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
            }
            talentTrackingEmailDTO.setFrom(user.getEmail());
        }
        MailVM mailVM = new MailVM(talentTrackingEmailDTO.getFrom(), talentTrackingEmailDTO.getTo(), talentTrackingEmailDTO.getBcc(),
                talentTrackingEmailDTO.getCc(), talentTrackingEmailDTO.getSubject(), talentTrackingEmailDTO.getHtmlContent(), talentTrackingEmailDTO.getFiles());
        saveEmailTrackingRecord(mailVM);
    }

    private List<UserNameVM> searchUserNameByIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        String selectSql ="SELECT id, first_name, last_name FROM `user` WHERE id IN (?1)";
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, userIdList);
        List<UserNameVM> userNameList =  searchData(selectSql, UserNameVM.class, paramMap);
        if (CollectionUtils.isEmpty(userNameList)) {
            return new ArrayList<>();
        }
        return userNameList;
    }

}
