package com.altomni.apn.talent.service.dto.elastic;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

public class EsSearchDTO {

    private Boolean hasPrivateEmail;

    private Set<String> languages;

    private Range age;

    private Set<String> skills;

    private Range salary;

    private Set<String> company;

    private Set<String> companyScope;

    private Set<String> industry;

    private Set<String> jobFunctions;

    private Set<String> title;

    private Range experience;

    private LevelRange jobLevel;

    private Set<String> degrees;

    private Set<String> collegeName;

    private Integer studiedInTopColleges;

    private Set<String> collegeLeague;

    private Set<Location> locations;

    private static final Set<String> otherIndustries = new HashSet<String>() {{
        add("AVIATION");
        add("ENERGY");
        add("INSURANCE");
        add("ACCOUNTING");
        add("LAW");
        add("FOOD");
        add("RETAIL");
        add("MANUFACTURING");
        add("ART_AND_DESIGN");
        add("MARKETING_ADVERTISING");
        add("RESEARCH");
    }};

    public boolean isAdvancedSearch() {
        return CollectionUtils.isNotEmpty(industry) || CollectionUtils.isNotEmpty(companyScope)
            || CollectionUtils.isNotEmpty(company) || Range.isNotEmpty(salary) ||
            CollectionUtils.isNotEmpty(skills) || Range.isNotEmpty(age) || CollectionUtils.isNotEmpty(languages);
    }

    public String toJson() {
        JSONObject res = new JSONObject();

        //private email
        if (ObjectUtils.isNotEmpty(this.hasPrivateEmail) && Boolean.TRUE.equals(this.hasPrivateEmail)) {
            res.fluentPut("hasPrivateEmail", true);
        }

        //languages
        if(CollectionUtils.isNotEmpty(this.languages)) {
            JSONObject languages = new JSONObject();
            this.languages = this.languages.stream().map(StringUtils::trimToNull).filter(Objects::nonNull).collect(Collectors.toSet());
            languages.fluentPut("data", this.languages).fluentPut("operator", "and");
            res.fluentPut("languages", languages);
        }

        //age
        if(ObjectUtils.isNotEmpty(this.age)) {
            if(ObjectUtils.isNotEmpty(this.age.getGte())) {
                res.fluentPut("minAge", this.age.getGte());
            }
            if(ObjectUtils.isNotEmpty(this.age.getLte())) {
                res.fluentPut("maxAge", this.age.getLte());
            }
        }

        //skills
        if(CollectionUtils.isNotEmpty(this.skills)) {
            this.skills = this.skills.stream().map(StringUtils::trimToNull).filter(Objects::nonNull).collect(Collectors.toSet());
            JSONObject skills = new JSONObject();
            skills.fluentPut("data", this.skills).fluentPut("operator", "and");
            res.fluentPut("skills", skills);
        }

        //salary
        if(ObjectUtils.isNotEmpty(this.salary)) {
            res.fluentPut("salary", this.salary);
        }

        //company
        if (CollectionUtils.isNotEmpty(this.company)) {
            this.company = this.company.stream().map(StringUtils::trimToNull).filter(Objects::nonNull).collect(Collectors.toSet());
            JSONObject company = new JSONObject();
            company.fluentPut("data", this.company).fluentPut("operator", "and");
            res.fluentPut("company", company);
        }

        //companyScope
        if(CollectionUtils.isNotEmpty(this.companyScope)) {
            this.companyScope = this.companyScope.stream().map(StringUtils::trimToNull).filter(Objects::nonNull).collect(Collectors.toSet());
            JSONObject companyScope = new JSONObject();
            companyScope.fluentPut("data", this.companyScope);
            res.fluentPut("companyScope", companyScope);
        }

        //industry
        if (CollectionUtils.isNotEmpty(this.industry)) {
            JSONObject industry = new JSONObject();
            this.industry = this.industry.stream().map(StringUtils::trimToNull).filter(Objects::nonNull).collect(Collectors.toSet());
            if (this.industry.contains("OTHERS")){
                this.industry.addAll(otherIndustries);
            }
            industry.fluentPut("data", this.industry);
            res.fluentPut("industry", industry);
        }

        //jobFunctions
        if(CollectionUtils.isNotEmpty(this.jobFunctions)) {
            this.jobFunctions = this.jobFunctions.stream().map(StringUtils::trimToNull).filter(Objects::nonNull).collect(Collectors.toSet());
            JSONObject jobFunctions = new JSONObject();
            jobFunctions.fluentPut("data", this.jobFunctions);
            res.fluentPut("jobFunctions", jobFunctions);
        }

        //title
        if (CollectionUtils.isNotEmpty(this.title)) {
            this.title = this.title.stream().map(StringUtils::trimToNull).filter(Objects::nonNull).collect(Collectors.toSet());
            JSONObject title = new JSONObject();
            title.fluentPut("data", this.title);
            res.fluentPut("title", title);
        }

        //experience
        if (ObjectUtils.isNotEmpty(this.experience)) {
            if(ObjectUtils.isNotEmpty(this.experience.getGte())) {
                res.fluentPut("minExperienceYears", this.experience.getGte());
            }
            if(ObjectUtils.isNotEmpty(this.experience.getLte())) {
                res.fluentPut("maxExperienceYears", this.experience.getLte());
            }
        }

        //level
        if(ObjectUtils.isNotEmpty(this.jobLevel)) {
            if(ObjectUtils.isNotEmpty(this.jobLevel.getGte())) {
                res.fluentPut("minLevelScore", this.jobLevel.getGte().getMinScore());
            }
            if(ObjectUtils.isNotEmpty(this.jobLevel.getLte())) {
                res.fluentPut("maxLevelScore", this.jobLevel.getLte().getMaxScore());
            }
        }

        //degrees
        if(CollectionUtils.isNotEmpty(this.degrees)) {
            this.degrees = this.degrees.stream().map(StringUtils::trimToNull).filter(Objects::nonNull).collect(Collectors.toSet());
            JSONObject degrees = new JSONObject();
            degrees.fluentPut("data", this.degrees);
            res.fluentPut("degrees", degrees);
        }

        //collegeName
        if (CollectionUtils.isNotEmpty(this.collegeName)) {
            JSONObject collegeName = new JSONObject();
            this.collegeName = this.collegeName.stream().map(StringUtils::trimToNull).filter(Objects::nonNull).collect(Collectors.toSet());
            collegeName.fluentPut("data", this.collegeName);
            collegeName.fluentPut("operator", "or");
            res.fluentPut("collegeName", collegeName);
        }

        //studiedInTopColleges
        if (ObjectUtils.isNotEmpty(this.studiedInTopColleges)) {
            res.fluentPut("studiedInTopColleges", this.studiedInTopColleges);
        }

        //collegeLeague
        if (CollectionUtils.isNotEmpty(this.collegeLeague)) {
            for(String league: this.collegeLeague) {
                if("Ivy".equalsIgnoreCase(league)) {
                    res.fluentPut("studiedInIvyColleges", "true"); //TODO do we need false here?
                } else if("985".equalsIgnoreCase(league)) {
                    res.fluentPut("studiedIn985Colleges", "true");
                } else if("211".equalsIgnoreCase(league)) {
                    res.fluentPut("studiedIn211Colleges", "true");
                }
            }
        }

        //locations
        if (CollectionUtils.isNotEmpty(this.locations)) {
            this.locations = this.locations.stream().filter(Objects::nonNull).collect(Collectors.toSet());
            res.fluentPut("locations", this.locations);
        }

        return res.toJSONString();
    }


    public Boolean getHasPrivateEmail() { return hasPrivateEmail; }

    public void setHasPrivateEmail(Boolean hasPrivateEmail) { this.hasPrivateEmail = hasPrivateEmail; }

    public Set<String> getLanguages() {
        return languages;
    }

    public void setLanguages(Set<String> languages) {
        this.languages = languages;
    }

    public Range getAge() {
        return age;
    }

    public void setAge(Range age) {
        this.age = age;
    }

    public Set<String> getSkills() {
        return skills;
    }

    public void setSkills(Set<String> skills) {
        this.skills = skills;
    }

    public Range getSalary() {
        return salary;
    }

    public void setSalary(Range salary) {
        this.salary = salary;
    }

    public Set<String> getCompany() {
        return company;
    }

    public void setCompany(Set<String> company) {
        this.company = company;
    }

    public Set<String> getCompanyScope() {
        return companyScope;
    }

    public void setCompanyScope(Set<String> companyScope) {
        this.companyScope = companyScope;
    }

    public Set<String> getIndustry() {
        return industry;
    }

    public void setIndustry(Set<String> industry) {
        this.industry = industry;
    }

    public Set<String> getJobFunctions() {
        return jobFunctions;
    }

    public void setJobFunctions(Set<String> jobFunctions) {
        this.jobFunctions = jobFunctions;
    }

    public Set<String> getTitle() {
        return title;
    }

    public void setTitle(Set<String> title) {
        this.title = title;
    }

    public Range getExperience() { return experience; }

    public void setExperience(Range experience) { this.experience = experience; }

    public LevelRange getJobLevel() {
        return jobLevel;
    }

    public void setJobLevel(LevelRange jobLevel) {
        this.jobLevel = jobLevel;
    }

    public Set<String> getDegrees() {
        return degrees;
    }

    public void setDegrees(Set<String> degrees) {
        this.degrees = degrees;
    }

    public Set<String> getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(Set<String> collegeName) {
        this.collegeName = collegeName;
    }

    public Integer getStudiedInTopColleges() {
        return studiedInTopColleges;
    }

    public void setStudiedInTopColleges(Integer studiedInTopColleges) {
        this.studiedInTopColleges = studiedInTopColleges;
    }

    public Set<String> getCollegeLeague() {
        return collegeLeague;
    }

    public void setCollegeLeague(Set<String> collegeLeague) {
        this.collegeLeague = collegeLeague;
    }

    public Set<Location> getLocations() {
        return locations;
    }

    public void setLocations(Set<Location> locations) {
        this.locations = locations;
    }


    @Override
    public String toString() {
        return "EsSearchDTO{" +
            "languages=" + languages +
            ", hasPrivateEmail" + hasPrivateEmail +
            ", age=" + age +
            ", skills=" + skills +
            ", salary=" + salary +
            ", company=" + company +
            ", companyScope=" + companyScope +
            ", industry=" + industry +
            ", jobFunctions=" + jobFunctions +
            ", title=" + title +
            ", experience=" + experience +
            ", jobLevel=" + jobLevel +
            ", degrees=" + degrees +
            ", collegeName=" + collegeName +
            ", studiedInTopColleges=" + studiedInTopColleges +
            ", collegeLeague=" + collegeLeague +
            ", locations=" + locations +
            '}';
    }
}
