package com.altomni.apn.talent.service.linkedinproject;

import com.altomni.apn.talent.domain.enumeration.linkedinproject.CandidateStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.ContactStatus;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.LinkedinProjectTalentFilter;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectTalentDTO;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinProjectTalentStatusCountVO;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinProjectTalentVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Service Interface for managing LinkedinProjectTalent.
 */
public interface LinkedinProjectTalentService {

    /**
     * Save a linkedinProjectTalent.
     *
     * @param linkedinProjectTalent the entity to save
     * @return the persisted entity
     */
    LinkedinProjectTalentVO create(LinkedinProjectTalentDTO linkedinProjectTalent);

    /**
     * Save a linkedinProjectTalent.
     *
     * @param linkedinProjectTalent the entity to save
     * @return the persisted entity
     */
    LinkedinProjectTalentVO update(LinkedinProjectTalentDTO linkedinProjectTalent);

    /**
     * Get all the linkedinProjectTalents.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    Page<LinkedinProjectTalentVO> findAll(Long linkedinProjectId, LinkedinProjectTalentFilter filter, Pageable pageable);

    Page<LinkedinProjectTalentVO> findLinkedinTalentsByProjectId(Long projectId, Boolean hasContactInfo, Boolean isInApplication, CandidateStatus candidateStatus, ContactStatus contactStatus, Pageable pageable);

    LinkedinProjectTalentStatusCountVO statusCount(Long linkedinProjectId);
}
