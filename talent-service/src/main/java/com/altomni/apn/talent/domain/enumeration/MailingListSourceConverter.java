package com.altomni.apn.talent.domain.enumeration;



import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class MailingListSourceConverter extends AbstractAttributeConverter<MailingListSource, Integer> {
    public MailingListSourceConverter() {
        super(MailingListSource::toDbValue, MailingListSource::fromDbValue);
    }
}
