package com.altomni.apn.talent.service.folder;

import com.altomni.apn.common.dto.folder.*;
import com.altomni.apn.talent.domain.folder.TalentFolder;
import com.altomni.apn.talent.domain.folder.TalentFolderDetail;
import com.altomni.apn.talent.service.dto.folder.FolderEmailRequestDTO;
import com.altomni.apn.talent.service.dto.folder.TalentFolderDTO;
import com.altomni.apn.talent.service.dto.folder.TalentFolderRelationListDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface TalentCustomFolderService {


    Page<TalentFolderDetail> getTalentFolderDetailsByUserId(Long userId, Pageable pageable);

    List<TalentFolder> getTalentFolders(List<Long> folderIds);

    Map<Long, Long> getTalentFolderUserMap(List<Long> folderIds);

    TalentFolderDTO createTalentFolder(TalentFolderDTO talentFolderDTO);

    TalentFolderDTO updateTalentFolder(TalentFolderDTO talentFolderDTO, Long talentFolderId);

    void deleteFolder(Long folderId);

    
    void addTalentsToFolders(TalentFolderRelationListDTO talentFolderRelationListDTO);
    
    void deleteTalentsFromFolder(TalentFolderRelationListDTO talentFolderRelationListDTO, Long folderId);

    
    void removeSharingForSharedFolder(Long folderId);

    FolderListDTO getCollaborativeTalentFolderList();

    List<FolderSharedTeamDTO> getDistinctSharedTeamsByUserId();

    List<FolderSharedUserDTO> getDistinctSharedUsersByUserId();

    FolderPermissionListDTO getCustomAndSharedJobFolderWithPermissionList();

    FolderListDTO getCustomAndSharedTalentFolderList();

    List<String> getAllTalentsEmailInFolders(FolderEmailRequestDTO folderEmailRequestDTO);

    ListPageFolderDTO getTalentFolderById(Long folderId);
}
