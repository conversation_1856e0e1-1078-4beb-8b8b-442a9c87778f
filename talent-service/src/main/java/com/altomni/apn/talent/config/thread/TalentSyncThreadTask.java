package com.altomni.apn.talent.config.thread;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.job.JobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Slf4j
public class TalentSyncThreadTask extends CopyTokenChildThread {

    private final EsFillerTalentService esFillerTalentService;

    private final JobService jobService;

    private final CountDownLatch countDownLatch;

    private final List<Long> ids;

    public TalentSyncThreadTask(EsFillerTalentService esFillerTalentService, JobService jobService, CountDownLatch countDownLatch, List<Long> ids) {
        super();
        this.esFillerTalentService = esFillerTalentService;
        this.jobService = jobService;
        this.countDownLatch = countDownLatch;
        this.ids = ids;
    }

    @Override
    public void runTask() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Long> successIds = new ArrayList<>();
        try {
            successIds = ids.stream().filter(esFillerTalentService::syncTalentToEs2).collect(Collectors.toList());
            if (!successIds.isEmpty()) {
                jobService.clearAllSyncRecordError(AsyncEnum.DATA_TYPE_TALENT, successIds);
            }
        } catch (Exception e) {
            log.error("[apn] scheduledSyncTalentsToES is error = [{}]", ExceptionUtils.getStackTrace(e));
        } finally {
            countDownLatch.countDown();
            stopWatch.stop();
            log.info("[apn] syncTalentToEsWithTimeAndIdsAndSuccess time = [{} ms], successIds = [{}], failIds = [{}]", stopWatch.getTotalTimeMillis(), successIds, CollUtil.disjunction(ids, successIds));
        }
    }

}
