package com.altomni.apn.talent.domain.folder;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "talent_folder")
public class TalentFolder extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "folder_note")
    private String folderNote;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getFolderNote() {
        return folderNote;
    }

    public void setFolderNote(String folderNote) {
        this.folderNote = folderNote;
    }
}

