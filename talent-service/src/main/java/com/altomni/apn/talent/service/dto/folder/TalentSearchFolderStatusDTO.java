package com.altomni.apn.talent.service.dto.folder;

import com.altomni.apn.common.dto.user.NameEmailUserDTO;
import com.altomni.apn.common.enumeration.folder.SearchFolderStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TalentSearchFolderStatusDTO extends TalentSearchFolderDTO {

    private SearchFolderStatus talentFolderStatus;

    private NameEmailUserDTO talentFolderCreator;


}
