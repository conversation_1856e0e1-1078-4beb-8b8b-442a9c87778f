package com.altomni.apn.talent.service.dto.elastic;

import java.util.Objects;

public class
Range {

    private Integer gte;

    private Integer lte;

    public static boolean isNotEmpty(Range range) {
        return Objects.nonNull(range) && (Objects.nonNull(range.getGte()) || Objects.nonNull(range.getLte()));
    }

    public Integer getGte() {
        return gte;
    }

    public void setGte(Integer gte) {
        this.gte = gte;
    }

    public Integer getLte() {
        return lte;
    }

    public void setLte(Integer lte) {
        this.lte = lte;
    }

    @Override
    public String toString() {
        return "Range{" +
            "gte=" + gte +
            ", lte=" + lte +
            '}';
    }
}
