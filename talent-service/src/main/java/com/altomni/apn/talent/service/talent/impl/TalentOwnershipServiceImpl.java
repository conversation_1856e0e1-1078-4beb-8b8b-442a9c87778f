package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.talent.TalentOwnershipDTO;
import com.altomni.apn.common.dto.user.NameOnlyUser;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.constants.Constants;
import com.altomni.apn.talent.repository.talent.TalentOwnershipRepository;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.application.ApplicationService;
import com.altomni.apn.talent.service.talent.TalentOwnershipService;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing TalentOwnership.
 */
@Service
@Transactional
public class TalentOwnershipServiceImpl implements TalentOwnershipService {

    private final Logger log = LoggerFactory.getLogger(TalentOwnershipServiceImpl.class);

    @Resource
    private TalentOwnershipRepository talentOwnershipRepository;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private UserService userService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Resource
    private HttpService httpService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private InitiationService initiationService;

    @Resource
    private ApplicationService applicationService;

    public static final String TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD = "TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD";
    public static final String TALENT_BROWSE_QUOTA = "TALENT_BROWSE_QUOTA";

    private static final String TALENT_OWNERSHIP_PROTECTION_PERIOD = "TALENT_OWNERSHIP_PROTECTION_PERIOD";

    private String crmInactivateUserUrl(Long userId) {
        return String.format("/contact/api/v1/contact/user/%s/reassign-resign-owner", userId);
    }


    @Override
    public List<TalentOwnershipDTO> create(Long talentId, List<TalentOwnershipDTO> talentOwnerships, Instant createDate) {
        List<TalentOwnershipDTO> result = new ArrayList<>();
        Instant plus3Day = createDate.plus(userService.getTenantParamValue(TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD).getBody(), ChronoUnit.MINUTES);
        for (TalentOwnershipDTO t : talentOwnerships) {
            if (t.getUserId() == null && !TalentOwnershipType.TENANT_SHARE.equals(t.getOwnershipType())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENTOWNERSHIP_CREATE_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
            }
            TalentOwnership talentOwnership = new TalentOwnership();
            talentOwnership.setTalentId(talentId);
            talentOwnership.setOwnershipType(t.getOwnershipType());
            talentOwnership.setExpireTime(plus3Day);
            if (TalentOwnershipType.TENANT_SHARE.equals(t.getOwnershipType())) {
                talentOwnership.setUserId(Constants.TALENT_OWNERSHIP_SHARE_WITH_ALL_USER_ID);
            } else {
                talentOwnership.setUserId(t.getUserId());
            }

            result.add(toDto(talentOwnershipRepository.saveAndFlush(talentOwnership)));
        }
        return result;
    }

    @Override
    public List<TalentOwnershipDTO> replace(Long talentId, List<Long> userIds) {
        TalentV3 talent = getAndCheckTalent(talentId);
        if (CollectionUtils.isEmpty(userIds)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENTOWNERSHIP_REPLACE_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        List<TalentOwnershipDTO> result = new ArrayList<>();
        List<TalentOwnership> talentOwnerships = talentOwnershipRepository.findAllByTalentIdAndOwnershipTypeAndExpireTimeGreaterThanEqual(talentId, TalentOwnershipType.SHARE, Instant.now());
        if (CollectionUtils.isNotEmpty(talentOwnerships)) {
            talentOwnershipRepository.deleteAll(talentOwnerships);
        }
        userIds.forEach(userId -> {
            TalentOwnership talentOwnership = new TalentOwnership();
            talentOwnership.setTalentId(talentId);
            talentOwnership.setUserId(userId);
            talentOwnership.setOwnershipType(TalentOwnershipType.SHARE);
            talentOwnership.setExpireTime(talent.getCreatedDate().plus(userService.getTenantParamValue(TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD).getBody(), ChronoUnit.MINUTES));
            result.add(toDto(talentOwnershipRepository.save(talentOwnership)));
        });
        return result;
    }

    private List<TalentOwnershipDTO> toDtos(List<TalentOwnership> talentOwnerships) {
        return talentOwnerships.stream().map(this::toDto).collect(Collectors.toList());
    }

    private TalentOwnershipDTO toDto(TalentOwnership talentOwnership) {
        TalentOwnershipDTO result = new TalentOwnershipDTO();
        ServiceUtils.myCopyProperties(talentOwnership, result);
        if (!TalentOwnershipType.TENANT_SHARE.equals(result.getOwnershipType())) {
            result.setUser(ServiceUtils.convert2DTO(userService.getUserById(talentOwnership.getUserId()).getBody(), NameOnlyUser.class));
        }

        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public List<TalentOwnershipDTO> findAll(Long talentId) {
        return toDtos(talentOwnershipRepository.findAllByTalentIdAndExpireTimeGreaterThanEqual(talentId, Instant.now()));
    }

    @Override
    public List<TalentOwnership> findAllByTalentIdAndOwnershipType(Long talentId, TalentOwnershipType ownershipType) {
        return talentOwnershipRepository.findAllByTalentIdAndOwnershipTypeAndExpireTimeGreaterThanEqual(talentId, ownershipType, Instant.now());
    }

    @Override
    public List<TalentOwnership> findAllByTalentIdAndOwnershipTypeIncludeExpired(Long talentId, List<TalentOwnershipType> ownershipType) {
        return talentOwnershipRepository.findAllByTalentIdAndOwnershipTypeIn(talentId, ownershipType);
    }

    private TalentOwnership toTalentOwnership(Long talentId, Long userId, Long talentRecruitmentProcessId) {
        TalentOwnership result = new TalentOwnership();
        result.setTalentId(talentId);
        result.setUserId(userId);
        result.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
        result.setOwnershipType(TalentOwnershipType.OWNER);
        result.setExpireTime(Instant.now().plus(userService.getTenantParamValue(TALENT_OWNERSHIP_PROTECTION_PERIOD).getBody(), ChronoUnit.MINUTES));
        return result;
    }

    private TalentV3 getAndCheckTalent(Long talentId) {
        TalentV3 talent = talentRepository.getById(talentId);
        if (!SecurityUtils.isCurrentTenant(talent.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.TALENT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), talentApiPromptProperties.getTalentService()));
        }
        return talent;
    }

    @Override
    public void delete(Long talentId) {
        getAndCheckTalent(talentId);
        List<TalentOwnership> talentOwnerships = talentOwnershipRepository.findAllByTalentIdAndOwnershipTypeAndExpireTimeGreaterThanEqual(talentId, TalentOwnershipType.SHARE, Instant.now());
        if (CollectionUtils.isNotEmpty(talentOwnerships)) {
            talentOwnerships.forEach(talentOwnership -> {
                talentOwnership.setExpireTime(Instant.now());
                talentOwnershipRepository.save(talentOwnership);
            });
        }
        talentRepository.updateTalentLastEditedTime(talentId);
    }

    @Override
    public void deleteAllByTalentId(Long talentId) {
        List<TalentOwnership> talentOwnerships = talentOwnershipRepository.findAllByTalentIdAndOwnershipTypeInAndExpireTimeGreaterThanEqual(talentId, List.of(TalentOwnershipType.SHARE, TalentOwnershipType.TALENT_OWNER), Instant.now());
        if (CollectionUtils.isNotEmpty(talentOwnerships)) {
            talentOwnerships.forEach(talentOwnership -> {
                talentOwnership.setExpireTime(Instant.now());
                talentOwnershipRepository.save(talentOwnership);
            });
        }
    }

    @Override
    public boolean validateTalentIsInProtectionPeriodOrNot(TalentV3 talent) {
        return Instant.now().compareTo(talent.getCreatedDate().plus(userService.getTenantParamValue(TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD).getBody(), ChronoUnit.MINUTES)) < 0;
    }

    @Override
    public List<TalentOwnership> saveAll(List<TalentOwnership> talentOwnerships) {
        return talentOwnershipRepository.saveAll(talentOwnerships);
    }

    @Override
    public void transferOwnership(Long userId, Long newOwnerId) {
        // 除了流程owner外，都转成新owner
        talentOwnershipRepository.transferOwnershipExceptRole(userId, newOwnerId, TalentOwnershipType.OWNER);
        // 流程owner修改过期时间
        talentOwnershipRepository.expireOwnershipByRole(userId, TalentOwnershipType.OWNER, Instant.now());

        // 重新计算流程owner commission
        applicationService.recalculateOwnerCommission(userId);
    }

    @Override
    public void removeDuplicateOwnerAndShare(Long userId) {
        talentOwnershipRepository.removeDuplicateOwnerAndShare(userId);
    }

    @Override
    public void inactivateCrmUser(Long apnUserId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", getAuthorizationHeader());
        httpHeaders.set("Content-Type", "application/json");
        try {
            HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + crmInactivateUserUrl(apnUserId), convertToOkHttpHeaders(httpHeaders), "");
            if (response == null || 200 != response.getCode()) {
                log.error("inactivate crm user error, response: {}", response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("inactivate crm user error: {}", e.getMessage());
        }
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }

    public static Headers convertToOkHttpHeaders(HttpHeaders httpHeaders) {
        Headers.Builder builder = new Headers.Builder();
        httpHeaders.forEach((key, values) -> {
            for (String value : values) {
                builder.add(key, value);
            }
        });
        return builder.build();
    }

    @Override
    public List<Long> getViewableTalentIdsOnUser(List<Long> talentIds, Long userId) {
        List<TalentOwnership> talentOwnerships = talentOwnershipRepository.findAllByTalentIdIn(talentIds);
        Map<Long, List<TalentOwnership>> talentOwnershipMap = talentOwnerships.stream().collect(Collectors.groupingBy(TalentOwnership::getTalentId));

        var viewPermissionCheckedTypes = List.of(TalentOwnershipType.TALENT_OWNER, TalentOwnershipType.SHARE);
        List<Long> viewableTalentIds = new ArrayList<>();
        for (Map.Entry<Long, List<TalentOwnership>> entry : talentOwnershipMap.entrySet()) {
            Long talentId = entry.getKey();
            var ownershipList = entry.getValue();
            if (isViewableByUser(ownershipList, userId, viewPermissionCheckedTypes)) {
                viewableTalentIds.add(talentId);
            }
        }

        return viewableTalentIds;

    }

    private boolean isViewableByUser(List<TalentOwnership> ownershipList, Long userId, List<TalentOwnershipType> viewPermissionCheckedTypes) {
        return ownershipList.stream().anyMatch(ownership ->
                (TalentOwnershipType.TENANT_SHARE.equals(ownership.getOwnershipType()) &&
                        Constants.TALENT_OWNERSHIP_SHARE_WITH_ALL_USER_ID.equals(ownership.getUserId()))
                        ||
                        (viewPermissionCheckedTypes.contains(ownership.getOwnershipType()) &&
                                userId.equals(ownership.getUserId()))
        );
    }

    @Override
    public Set<Long> filterValidTalents(List<Long> talentIds) {
        Long tenantId = SecurityUtils.getTenantId();
        Long userId = SecurityUtils.getUserId();
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateClientContactDataPermissionByUserId(tenantId, userId).getBody();
        log.info("DataPermission (user: {}) = {}", userId, teamDataPermission);
        if(teamDataPermission.getAll()) {
            return new HashSet<>(talentIds);
        }

        Set<Long> validTalentIds = new HashSet<>();

        List<TalentOwnership> talentOwnerships = talentOwnershipRepository.findAllByTalentIdIn(talentIds);
        Set<Long> pendingTalentIds = new HashSet<>();
        for (TalentOwnership talentOwnership : talentOwnerships) {
            if (talentOwnership.getOwnershipType().equals(TalentOwnershipType.TENANT_SHARE)){ //分享给所有人的都可以访问
                validTalentIds.add(talentOwnership.getTalentId());
                log.info("分享给所有人=" + talentOwnership.getTalentId());
            }else if (talentOwnership.getUserId().equals(userId)){ //owner 可以访问
                validTalentIds.add(talentOwnership.getTalentId());
                log.info("Owner type " + talentOwnership.getOwnershipType() + ", Owner=" + talentOwnership.getTalentId());
            } else if (talentOwnership.getOwnershipType().equals(TalentOwnershipType.TALENT_OWNER)) {
                pendingTalentIds.add(talentOwnership.getTalentId());
            }
        }

        //流程相关人员可以访问
        if (CollectionUtils.isNotEmpty(pendingTalentIds)){
            Set<Long> validTalentsByParticipant = applicationService.filterTalentIdsByUserIdAndTalentIds(userId, pendingTalentIds).getBody();
            validTalentIds.addAll(validTalentsByParticipant);
            log.info("validTalentsByParticipant=" + validTalentsByParticipant);
            pendingTalentIds = SetUtils.difference(pendingTalentIds, validTalentsByParticipant);
        }

        // 创建者可以访问
        if (CollectionUtils.isNotEmpty(pendingTalentIds)){
            Set<Long> validTalentsByPermissionUserId = talentRepository.filterByPermissionUserId(pendingTalentIds, userId);
            validTalentIds.addAll(validTalentsByPermissionUserId);
            log.info("validTalentsByPermissionUserId =" + validTalentsByPermissionUserId);
            pendingTalentIds = SetUtils.difference(pendingTalentIds, validTalentsByPermissionUserId);
        }

        if (CollectionUtils.isNotEmpty(pendingTalentIds) && BooleanUtils.isFalse(teamDataPermission.getSelf()) && CollUtil.isNotEmpty(teamDataPermission.getReadableTeamIds())) { //和创建者/owner同团队，有团队权限的人可以访问
            Set<Long> validTalentsByOwnerTeams = talentOwnershipRepository.getTalentsByTalentIdInAndTeamIdIn(pendingTalentIds, teamDataPermission.getReadableTeamIds());
            validTalentIds.addAll(validTalentsByOwnerTeams);
            log.info("validTalentsByOwnerTeams=" + validTalentsByOwnerTeams);
            pendingTalentIds = SetUtils.difference(pendingTalentIds, validTalentsByOwnerTeams);
            if (CollectionUtils.isNotEmpty(pendingTalentIds)){
                Set<Long> validTalentsByCreatorTeams = talentRepository.filterTalentsByTalentIdInAndTeamIdIn(pendingTalentIds, teamDataPermission.getReadableTeamIds());
                validTalentIds.addAll(validTalentsByCreatorTeams);
                log.info("validTalentsByCreatorTeams=" + validTalentsByCreatorTeams);
            }
        }
        log.info("validTalentIds=" + validTalentIds);
        log.info("invalidTalentIds=" + SetUtils.difference(new HashSet<>(talentIds), validTalentIds));
        return validTalentIds;
    }
}
