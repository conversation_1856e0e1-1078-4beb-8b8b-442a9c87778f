package com.altomni.apn.talent.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class StartFailedWarrantyReasonConverter extends AbstractAttributeConverter<StartFailedWarrantyReason, Integer> {
    public StartFailedWarrantyReasonConverter() {
        super(StartFailedWarrantyReason::toDbValue, StartFailedWarrantyReason::fromDbValue);
    }
}
