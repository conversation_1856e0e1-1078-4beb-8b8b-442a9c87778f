package com.altomni.apn.talent.service.talent;
/*
 * Created by <PERSON> on 10/30/2017.
 */

import com.altomni.apn.common.domain.talent.Resume;
import com.altomni.apn.common.domain.talent.TalentResumeRelation;
import com.altomni.apn.common.dto.redis.ImagesInfoDTO;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;
import com.altomni.apn.common.dto.talent.TalentResumeOutput;

import java.util.List;

public interface TalentResumeService {

    Resume saveResume(Resume resume);

    TalentResumeDTO createTalentResume(TalentResumeDTO TalentResumeDTO);

    TalentResumeRelation deleteTalentResume(Long id);

    TalentResumeDTO findOne(Long id);

    TalentResumeDTO findOneByTalentResumeRelationId(Long relationId);

    TalentResumeOutput findAllByTalentId(String talentId, Long jobId);

    List<TalentResumeDTO> findAllWithoutPortraitByTalentId(Long talentId);

    List<TalentResumeDTO> findAllWithoutPortraitByTalentIds(List<Long> talentIds);

    TalentResumeDTO findByUuidAndTenantId(String uuid, Long tenantId);

    TalentResumeDTO findByUuidAndTenantIdByCheckExistTalents(String uuid, Long tenantId);

    TalentResumeDTO generateTalentResumeDTO(TalentResumeRelation talentResumeRelation, Resume resume, ImagesInfoDTO imagesInfoDTO);

    List<Long> getTalentIdsByResumeDTOS(List<TalentResumeDTO> resumes);

    TalentResumeDTO findLastByTalentId(Long talentId);
}
