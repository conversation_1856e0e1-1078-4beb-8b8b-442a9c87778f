package com.altomni.apn.talent.domain.talent;

import com.altomni.apn.common.domain.enumeration.application.ActivityStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@ApiModel
@Data
public class MyPipelineSearchDTO
{

    @ApiModelProperty(value = "from date for job search")
    private Instant fromDate;
    @ApiModelProperty(value = "end date for job search")
    private Instant endDate;
    @ApiModelProperty(value = "user role")
    private List<UserRole> userRole;
    @ApiModelProperty(value = "job type ")
    private List<JobType> jobType;
    @ApiModelProperty(value = "job skill ")
    private String jobSkill;
    @ApiModelProperty(value = "candidate name")
    private String candidateName;
    @ApiModelProperty(value = "job title")
    private String jobTitle;
    @ApiModelProperty(value = "company  assign to job")
    private String companyName;
    @ApiModelProperty(value = "activity status ")
    private ActivityStatus status;
    @ApiModelProperty(value = "job location")
    private String jobLocation;
    @ApiModelProperty(value = "candidate email address")
    private String email;
    @ApiModelProperty(value = "job recruiter name")
    private String recruiterName;
    @ApiModelProperty(value = "job hr manager name")
    private String hmName;
    @ApiModelProperty(value = "job hr name")
    private String hrName;
    @ApiModelProperty(value = "job hr name")
    private String mspName;
    @ApiModelProperty(value = "job id in database")
    private Long jobId;

    @ApiModelProperty(value = "page number")
    private Integer pageNum=1;
    @ApiModelProperty(value = "page size")
    private Integer pageSize=10;


    public String userRoleSqlRole()
    {
        String s="";
        for(UserRole userRole:this.userRole)
        {
            s+=","+userRole.toDbValue();
        }
        s= "("+s.substring(1)+")";
        return s;
    }

    public String jobTypeSqlRole()
    {
        String s="";
        for(JobType jobType:this.jobType)
        {
            s+=","+jobType.toDbValue();
        }
        s= "("+s.substring(1)+")";
        return s;
    }


}
