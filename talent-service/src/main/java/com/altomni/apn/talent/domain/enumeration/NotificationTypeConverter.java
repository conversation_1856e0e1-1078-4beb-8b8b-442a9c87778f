package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class NotificationTypeConverter extends AbstractAttributeConverter<NotificationType, Integer> {
    public NotificationTypeConverter() {
        super(NotificationType::toDbValue, NotificationType::fromDbValue);
    }
}
