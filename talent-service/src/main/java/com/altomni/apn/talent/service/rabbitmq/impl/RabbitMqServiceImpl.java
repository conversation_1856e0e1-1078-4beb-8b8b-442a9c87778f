package com.altomni.apn.talent.service.rabbitmq.impl;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.EsfillerMQProperties;
import com.altomni.apn.talent.config.env.TalentProfileMQProperties;
import com.altomni.apn.talent.service.rabbitmq.RabbitMqService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.QueueInformation;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class RabbitMqServiceImpl implements RabbitMqService {

    private final Logger log = LoggerFactory.getLogger(RabbitMqServiceImpl.class);

    public static final String QUEUE_MESSAGE_COUNT = "QUEUE_MESSAGE_COUNT";

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource
    private TalentProfileMQProperties talentProfileMQProperties;

    @Resource(name = "esfillerRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource(name = "esfillerAmqpAdmin")
    private AmqpAdmin esfillerAmqpAdmin;


    /*private Connection getConnection() throws Exception {
        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(properties.getRabbitMQ().getHost());
        factory.setPort(properties.getRabbitMQ().getPort());
        factory.setUsername(properties.getRabbitMQ().getUsername());
        factory.setPassword(properties.getRabbitMQ().getPassword());
        return factory.newConnection();
    }*/

    @Override
    public void sendTalentProfile(String profile, int priority) {
        log.info("[EsFillerTalentService: syncTalentToMQ @{}] save talent profile to rabbitMQ : {}", SecurityUtils.getUserId(), profile);
        try {
            rabbitTemplate.convertAndSend(esfillerMQProperties.getEsfillerMQExchange(), esfillerMQProperties.getToEsFillerRoutingKey(), profile, message -> {
                message.getMessageProperties().setPriority(priority);
                return message;
            });
        }catch (Exception e) {
            log.error("[EsFillerTalentService: syncTalentToMQ @{}] send talent profile to rabbitMQ error: {}", SecurityUtils.getUserId(), e.getMessage());
        }
    }

    @Override
    public void sendTalentUnlock(String unlockInfo) {
        log.info("[EsFillerTalentService: syncTalentUnlockToMQ @{}] save talent unlock to rabbitMQ : {}", SecurityUtils.getUserId(), unlockInfo);
        try {
            rabbitTemplate.convertAndSend(esfillerMQProperties.getEsfillerMQExchange(), esfillerMQProperties.getToEsFillerRoutingKey(), unlockInfo, message -> {
                log.info("send talent unlock to rabbitMQ, Message: {}", message);
                return message;
            });
        }catch (Exception e) {
            log.error("[EsFillerTalentService: syncTalentToMQ @{}] send talent profile to rabbitMQ error: {}", SecurityUtils.getUserId(), e.getMessage());
        }
    }

    @Override
    public void sendTalentLinkedin(String linkedin) {
        log.info("send talent linkedin to rabbitMQ " + linkedin);
        try {
            rabbitTemplate.convertAndSend(talentProfileMQProperties.getExchange(), talentProfileMQProperties.getLinkedinRoutingKey(), linkedin, message -> {
                log.info("send talent linkedin to rabbitMQ, Message: {}", message);
                return message;
            });
        }catch (Exception e) {
            log.error("Send talent linkedin to rabbitMQ error: {}", e.getMessage());
        }
    }

    @Override
    public void sendTalentLinkedinToDelayQueue(String linkedin) {
        log.info("send talent linkedin to delay queue " + linkedin);
        try {
            rabbitTemplate.convertAndSend(talentProfileMQProperties.getExchange(), talentProfileMQProperties.getLinkedinDelayRoutingKey(), linkedin, message -> {
                log.info("send talent linkedin to delay queue, Message: {}", message);
                return message;
            });
        }catch (Exception e) {
            log.error("Send talent linkedin to delay queue error: {}", e.getMessage());
        }
    }

    @Override
    public void sendTalentId(Long talentId) {
        log.info("send talent id to rabbitMQ : {}", talentId);
        try {
            rabbitTemplate.convertAndSend(talentProfileMQProperties.getExchange(), talentProfileMQProperties.getTalentIdRoutingKey(), String.valueOf(talentId), message -> {
                log.info("send talent id to rabbitMQ, Message: {}", message);
                return message;
            });
        }catch (Exception e) {
            log.error("Send talent id to rabbitMQ error: {}", e.getMessage());
        }
    }

    @Override
    public Integer checkMessageCount(String queue) {
        final QueueInformation queueInfo = esfillerAmqpAdmin.getQueueInfo(queue);
        int messageCount = queueInfo.getMessageCount();
        if (messageCount > 0){
            log.info("check message count: {}", messageCount);
        }
        return messageCount;
    }
}
