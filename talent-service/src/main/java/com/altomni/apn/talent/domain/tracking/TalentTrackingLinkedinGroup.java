package com.altomni.apn.talent.domain.tracking;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.tracking.*;
import com.altomni.apn.talent.service.dto.tracking.TalentTrackingGroupDTO;
import com.altomni.apn.talent.service.vo.tracking.TalentTrackingGroupVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentTrackingLinkedinGroup.
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "talent_tracking_linkedin_group")
public class TalentTrackingLinkedinGroup extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Convert(converter = TrackingGroupStatusConverter.class)
    @Column(name = "status")
    private TrackingGroupStatus status;

    @Column(name = "operatorLinkedinId")
    private String operatorLinkedinId;

    @Column(name = "tenant_id")
    private Long tenantId;

    public static TalentTrackingLinkedinGroup fromTalentTrackingGroupDTO(TalentTrackingGroupDTO talentTrackingGroupDTO) {
        TalentTrackingLinkedinGroup talentTrackingLinkedinGroup = new TalentTrackingLinkedinGroup();
        ServiceUtils.myCopyProperties(talentTrackingGroupDTO, talentTrackingLinkedinGroup);
        return talentTrackingLinkedinGroup;
    }

    public static TalentTrackingGroupVO toTalentTrackingGroupVO(TalentTrackingLinkedinGroup talentTrackingLinkedinGroup) {
        TalentTrackingGroupVO talentTrackingGroupVO = new TalentTrackingGroupVO();
        ServiceUtils.myCopyProperties(talentTrackingLinkedinGroup, talentTrackingGroupVO);
        return talentTrackingGroupVO;
    }
}
