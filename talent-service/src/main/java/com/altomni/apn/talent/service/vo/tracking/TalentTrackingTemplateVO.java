package com.altomni.apn.talent.service.vo.tracking;


import com.altomni.apn.talent.domain.enumeration.tracking.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;
import java.time.Instant;


@Data
@ApiModel(value = "talent tracking")
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentTrackingTemplateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "title")
    private String title;

    @ApiModelProperty(value = "template")
    private String template;

    @ApiModelProperty(value = "operatorLinkedinId")
    private String operatorLinkedinId;

    @ApiModelProperty(value = "category")
    @Convert(converter = TrackingTemplateCategoryConverter.class)
    private TrackingTemplateCategory category;


}
