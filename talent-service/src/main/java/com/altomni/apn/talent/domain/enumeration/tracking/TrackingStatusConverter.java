package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class TrackingStatusConverter extends AbstractAttributeConverter<TrackingStatus, Integer> {
    public TrackingStatusConverter() {
        super(TrackingStatus::toDbValue, TrackingStatus::fromDbValue);
    }
}
