package com.altomni.apn.talent.web.rest.talent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 领英事件追踪请求DTO
 */
@ApiModel(description = "领英事件追踪请求")
public class LinkedinEventTrackingRequest {

    @ApiModelProperty(value = "事件追踪列表", required = true)
    @NotEmpty(message = "事件追踪列表不能为空")
    @Valid
    @JsonProperty("eventTrackingList")
    private List<EventTrackingItem> eventTrackingList;

    @ApiModel(description = "事件追踪项")
    public static class EventTrackingItem {

        @ApiModelProperty(value = "事件名称", required = true, example = "friend_request")
        @NotBlank(message = "事件名称不能为空")
        @JsonProperty("event")
        private String event;

        @ApiModelProperty(value = "事件发生时间", required = true, example = "2025-07-08T05:17:09.142Z")
        @NotBlank(message = "事件时间不能为空")
        @JsonProperty("eventDateTime")
        private String eventDateTime;

        @ApiModelProperty(value = "操作者领英ID", required = true, example = "operator-linkedin-123")
        @NotBlank(message = "操作者领英ID不能为空")
        @JsonProperty("operatorLinkedinId")
        private String operatorLinkedinId;

        @ApiModelProperty(value = "操作对象的领英ID", required = true, example = "target-linkedin-456")
        @NotBlank(message = "目标领英ID不能为空")
        @JsonProperty("linkedinId")
        private String linkedinId;

        @ApiModelProperty(value = "事件详情", example = "好友请求详细信息")
        @JsonProperty("detail")
        private String detail;

        // Getters and Setters
        public String getEvent() {
            return event;
        }

        public void setEvent(String event) {
            this.event = event;
        }

        public String getEventDateTime() {
            return eventDateTime;
        }

        public void setEventDateTime(String eventDateTime) {
            this.eventDateTime = eventDateTime;
        }

        public String getOperatorLinkedinId() {
            return operatorLinkedinId;
        }

        public void setOperatorLinkedinId(String operatorLinkedinId) {
            this.operatorLinkedinId = operatorLinkedinId;
        }

        public String getLinkedinId() {
            return linkedinId;
        }

        public void setLinkedinId(String linkedinId) {
            this.linkedinId = linkedinId;
        }

        public String getDetail() {
            return detail;
        }

        public void setDetail(String detail) {
            this.detail = detail;
        }
    }


    public List<EventTrackingItem> getEventTrackingList() {
        return eventTrackingList;
    }

    public void setEventTrackingList(List<EventTrackingItem> eventTrackingList) {
        this.eventTrackingList = eventTrackingList;
    }
}