package com.altomni.apn.talent.service.parser;

import com.altomni.apn.common.domain.parser.ResumeParseInfo;
import com.altomni.apn.common.dto.redis.ParserResponse;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.talent.config.FeignSupportConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Component
@FeignClient(value = "common-service", configuration = FeignSupportConfig.class)
public interface ParserService {

    @PostMapping("/common/api/v3/parsers/resume/parse-info/redis")
    ResponseEntity<Void> putResumeParseInfoToRedis(@RequestBody ResumeParseInfo resumeParseInfo);

    @PostMapping(value = "/common/api/v3/parsers/resume/migration/send-sqs-message")
    ResponseEntity<Void> sendSqsMessage(@RequestBody CloudFileObjectMetadata cloudFileObjectMetadata);

    @GetMapping(value = "/common/api/v3/s3/store/file/{uuid}/{uploadType}")
    ResponseEntity<CloudFileObjectMetadata> getFileFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @GetMapping("/common/api/v3/parsers/resume/result-status/{uuid}")
    ResponseEntity<ParserResponse> getResumeParseResultStatusOnly(@PathVariable("uuid") String uuid);

    @GetMapping("/common/api/v3/parsers/resume/result/{uuid}")
    ResponseEntity<ParserResponse> getParserResumeResult(@PathVariable("uuid") String uuid);

    @PostMapping(value = "/common/api/v3/s3/common/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ResponseEntity<String> uploadDocument(@RequestPart("file") MultipartFile file, @RequestParam("key") String key, @RequestParam("uploadType") String uploadType);
}
