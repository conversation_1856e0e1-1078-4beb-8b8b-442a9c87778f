package com.altomni.apn.talent.service.dto.talent;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class TalentTranslateEsDTO {

    private String firstName;

    private String lastName;

    private String fullName;

    private String nickName;

    private String website;

    private List<TalentContact> contacts;

    private List<String> industries;

    private List<String> jobFunctions;

    private List<TalentEducationDTO> educations;

    private List<TalentExperienceDTO> experiences;

    private List<String> languages;

    private List<JSONObject> skills;

}
