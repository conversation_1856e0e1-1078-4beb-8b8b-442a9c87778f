package com.altomni.apn.talent.service.linkedinproject.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.DuplicateException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinTalent;
import com.altomni.apn.talent.repository.linkedinproject.LinkedinTalentContactRepository;
import com.altomni.apn.talent.repository.linkedinproject.LinkedinTalentRepository;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinTalentDTO;
import com.altomni.apn.talent.service.linkedinproject.LinkedinTalentService;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinTalentVO;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service Implementation for managing LinkedinTalent.
 */
@Service
@Transactional
public class LinkedinTalentServiceImpl implements LinkedinTalentService {

    private final Logger log = LoggerFactory.getLogger(LinkedinTalentServiceImpl.class);

    private final LinkedinTalentRepository linkedinTalentRepository;

    private final LinkedinTalentContactRepository linkedinTalentContactRepository;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final TalentApiPromptProperties talentApiPromptProperties;

    public LinkedinTalentServiceImpl(LinkedinTalentRepository linkedinTalentRepository, LinkedinTalentContactRepository linkedinTalentContactRepository,CommonApiMultilingualConfig commonApiMultilingualConfig,TalentApiPromptProperties talentApiPromptProperties) {
        this.linkedinTalentRepository = linkedinTalentRepository;
        this.linkedinTalentContactRepository = linkedinTalentContactRepository;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.talentApiPromptProperties = talentApiPromptProperties;
    }

    /**
     * Create a linkedinTalent.
     *
     * @param linkedinTalentDTO the entity to save
     * @return the persisted entity
     */
    @Override
    public LinkedinTalentVO create(LinkedinTalentDTO linkedinTalentDTO) {
        LinkedinTalent exists = linkedinTalentRepository.findById(linkedinTalentDTO.getId()).orElse(null);
        if (exists != null) {
            return LinkedinTalentVO.fromLinkedinTalent(addContacts(exists));
        }
        LinkedinTalent createLinkedinTalent = LinkedinTalent.fromLinkedinTalentDTO(linkedinTalentDTO);
        createLinkedinTalent.setTenantId(SecurityUtils.getTenantId());
        LinkedinTalent save = linkedinTalentRepository.save(createLinkedinTalent);
        if (CollectionUtils.isNotEmpty(linkedinTalentDTO.getContacts())) {
            linkedinTalentDTO.getContacts().forEach(linkedinTalentContact -> linkedinTalentContact.setLinkedinTalentId(save.getId()));
            linkedinTalentContactRepository.saveAll(linkedinTalentDTO.getContacts());
        }
        return LinkedinTalentVO.fromLinkedinTalent(addContacts(save));
    }

    /**
     * Update a linkedinTalent Only
     *
     * @param linkedinTalentDTO the entity to save
     * @return the persisted entity
     */
    @Override
    public LinkedinTalentVO update(LinkedinTalentDTO linkedinTalentDTO) {
        LinkedinTalent exists = linkedinTalentRepository.findById(linkedinTalentDTO.getId()).orElse(null);
        if (exists == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_UPDATE_LINKEDINTALENTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
        ServiceUtils.myCopyProperties(linkedinTalentDTO, exists);
        return LinkedinTalentVO.fromLinkedinTalent(addContacts(linkedinTalentRepository.save(exists)));
    }

    private LinkedinTalent addContacts(LinkedinTalent linkedinTalent) {
        linkedinTalent.setContacts(linkedinTalentContactRepository.findAllByLinkedinTalentId(linkedinTalent.getId()));
        return linkedinTalent;
    }

    /**
     * Get all the linkedinTalents.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public Page<LinkedinTalentVO> findAll(Pageable pageable) {
        Page<LinkedinTalent> linkedinTalents = linkedinTalentRepository.findAllByTenantId(SecurityUtils.getTenantId(), pageable);
        return linkedinTalents.map(o -> LinkedinTalentVO.fromLinkedinTalent(addContacts(o)));
    }

    @Override
    public Page<LinkedinTalentVO> findAllByTalentIds(List<String> ids, Pageable pageable) {
        Page<LinkedinTalent> linkedinTalents = linkedinTalentRepository.findAllByTenantIdAndIdIn(SecurityUtils.getTenantId(), ids, pageable);
        return linkedinTalents.map(o -> LinkedinTalentVO.fromLinkedinTalent(addContacts(o)));
    }

    /**
     * Get one linkedinTalent by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public LinkedinTalentVO findOne(String id) {
        log.debug("Request to get LinkedinTalent : {}", id);
        LinkedinTalent linkedinTalent = linkedinTalentRepository.findById(id).orElse(null);
        return linkedinTalent != null ? LinkedinTalentVO.fromLinkedinTalent(addContacts(linkedinTalent)) : null;
    }

    /**
     * Delete the linkedinTalent by id.
     *
     * @param id the id of the entity
     */
    @Override
    public void delete(String id) {
        linkedinTalentContactRepository.deleteAllByLinkedinTalentId(id);
        linkedinTalentRepository.deleteById(id);
    }
}
