package com.altomni.apn.talent.service.dto.elastic;

import java.util.Objects;

public class Location {

    private String city;

    private String province;

    private String country;

    private String location;

    private String key;

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getLocation() { return location; }

    public void setLocation(String location) { this.location = location; }

    public String getKey() { return key; }

    public void setKey(String key) { this.key = key; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Location location = (Location) o;
        return Objects.equals(this.city, location.city) &&
                Objects.equals(this.province, location.province) &&
                Objects.equals(this.country, location.country) &&
                Objects.equals(this.location, location.location);
    }

    @Override
    public int hashCode() {
        return Objects.hash(city, province, country);
    }

    @Override
    public String toString() {
        return "Location{" +
                "city='" + city + '\'' +
                ", province='" + province + '\'' +
                ", country='" + country + '\'' +
                ", location='" + location + '\'' +
                ", key='" + key + '\'' +
                '}';
    }
}
