package com.altomni.apn.talent.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoiceActivityTypeConverter extends AbstractAttributeConverter<InvoiceActivityType, Integer> {
    public InvoiceActivityTypeConverter() {
        super(InvoiceActivityType::toDbValue, InvoiceActivityType::fromDbValue);
    }
}
