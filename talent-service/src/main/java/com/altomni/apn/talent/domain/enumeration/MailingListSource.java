package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The EmailListSource enumeration.
 */
public enum MailingListSource implements ConvertedEnum<Integer> {
    MANUALLY_ADDED(0),
    IMPORTED(1),
    CANDIDATE_PAGE(2),
    CLIENT_CONTACT(3);



    private final int dbValue;

    MailingListSource(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<MailingListSource, Integer> resolver =
        new ReverseEnumResolver<>(MailingListSource.class, MailingListSource::toDbValue);

    public static MailingListSource fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
