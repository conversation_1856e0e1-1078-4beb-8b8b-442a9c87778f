package com.altomni.apn.talent.service.vo.record;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.record.TrackingType;
import com.altomni.apn.talent.domain.record.TalentTrackingRecord;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentTrackingRecordVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -956520387234760691L;

    private Long id;

    private Long tenantId;

    private Long userId;

    private TrackingType trackingType;

    private String contact;

    private Instant touchTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public TrackingType getTrackingType() {
        return trackingType;
    }

    public void setTrackingType(TrackingType trackingType) {
        this.trackingType = trackingType;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public Instant getTouchTime() {
        return touchTime;
    }

    public void setTouchTime(Instant touchTime) {
        this.touchTime = touchTime;
    }

    public static TalentTrackingRecordVO fromTalentTrackingRecord(TalentTrackingRecord talentTrackingRecord) {
        TalentTrackingRecordVO  talentTrackingRecordVO = new TalentTrackingRecordVO();
        ServiceUtils.myCopyProperties(talentTrackingRecord, talentTrackingRecordVO);
        return talentTrackingRecordVO;
    }
}
