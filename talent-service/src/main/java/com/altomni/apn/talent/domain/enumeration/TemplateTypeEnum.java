package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;

public enum TemplateTypeEnum implements ConvertedEnum<Integer> {
    STANDARD(1), CUSTOMIZED(2);

    private final int dbValue;

    TemplateTypeEnum(int dbValue) {
        this.dbValue = dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public static TemplateTypeEnum fromDbValue(int dbValue) {
        for (TemplateTypeEnum reportTypeEnum : TemplateTypeEnum.values()) {
            if (reportTypeEnum.getDbValue() == dbValue) {
                return reportTypeEnum;
            }
        }
        return null;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }
}
