package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class TrackingTemplateStatusConverter extends AbstractAttributeConverter<TrackingTemplateStatus, Integer> {
    public TrackingTemplateStatusConverter() {
        super(TrackingTemplateStatus::toDbValue, TrackingTemplateStatus::fromDbValue);
    }
}
