package com.altomni.apn.talent.domain.user;


import com.altomni.apn.common.domain.AbstractAuditingEntity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A UserFavoriteTalent.
 */
@Entity
@Table(name = "user_favorite_talent")
public class UserFavoriteTalent extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "talent_id")
    private Long talentId;


    // jhipster-needle-entity-add-field - JHip<PERSON> will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public UserFavoriteTalent userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    // jhipster-needle-entity-add-getters-setters - JHip<PERSON> will add getters and setters here, do not remove


    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public UserFavoriteTalent talentId(Long talentId) {
        this.talentId = talentId;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UserFavoriteTalent userFavoriteTalent = (UserFavoriteTalent) o;
        if (userFavoriteTalent.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), userFavoriteTalent.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "UserFavoriteTalent{" +
            "id=" + id +
            ", userId=" + userId +
            ", talentId=" + talentId +
            '}';
    }
}
