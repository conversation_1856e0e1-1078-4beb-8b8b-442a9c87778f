package com.altomni.apn.talent.domain.folder;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@Table(name = "talent_folder_relation")
public class TalentFolderRelation extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    @Column(name = "talent_folder_id", nullable = false)
    private Long talentFolderId;

    public Long getId() {
        return id;
    }

    public String getStringId() {
        return String.valueOf(id);
    }
    public void setId(Long id) {
        this.id = id;
    }

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getTalentFolderId() {
        return talentFolderId;
    }

    public void setTalentFolderId(Long talentFolderId) {
        this.talentFolderId = talentFolderId;
    }



}
