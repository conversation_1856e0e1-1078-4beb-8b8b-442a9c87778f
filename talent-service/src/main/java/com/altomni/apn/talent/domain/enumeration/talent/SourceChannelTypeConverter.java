package com.altomni.apn.talent.domain.enumeration.talent;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

public class SourceChannelTypeConverter extends AbstractAttributeConverter<com.altomni.apn.talent.domain.enumeration.talent.SourceChannelType, Integer> {
    public SourceChannelTypeConverter() {
        super(com.altomni.apn.talent.domain.enumeration.talent.SourceChannelType::toDbValue, com.altomni.apn.talent.domain.enumeration.talent.SourceChannelType::fromDbValue);
    }
}
