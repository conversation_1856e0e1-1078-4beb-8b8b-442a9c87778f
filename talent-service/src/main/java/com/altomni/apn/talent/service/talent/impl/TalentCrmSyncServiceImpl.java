package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.WithDataException;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.domain.talent.TalentCurrentLocation;
import com.altomni.apn.talent.repository.talent.TalentContactRepository;
import com.altomni.apn.talent.repository.talent.TalentLocationRepository;
import com.altomni.apn.talent.repository.talent.TalentOwnershipRepository;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.CompanyService;
import com.altomni.apn.talent.service.talent.TalentCrmSyncService;
import com.altomni.apn.talent.web.rest.talent.dto.EditTalentSyncCrmDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TalentCrmSyncServiceImpl implements TalentCrmSyncService {

    private static final String CRM_SYNC_TALENT = "/contact/api/v1/contact/contact-info/talent/";

    private final ApplicationProperties applicationProperties;
    private final TalentRepository talentRepository;
    private final TalentContactRepository talentContactRepository;
    private final CompanyService companyService;
    private final TalentOwnershipRepository talentOwnershipRepository;
    private final TalentLocationRepository talentLocationRepository;

    @Override
    public void updateClientContact(Long id, TalentInfoInput input) {
        if (isClientContact(id)) {
            EditTalentSyncCrmDTO editTalentSyncCrmDTO = new EditTalentSyncCrmDTO();
            BeanUtil.copyProperties(input, editTalentSyncCrmDTO);
            fillClientContactOwnerAndShareUsers(input.getOwnerships(), editTalentSyncCrmDTO);
            syncCrmTalent(id, editTalentSyncCrmDTO);
        }
    }

    @Override
    public void syncClientOwnershipToCrm(Long talentId) {
        if (!isClientContact(talentId)) {
            return;
        }

        TalentV3 talentV3 = talentRepository.findById(talentId).orElseThrow(() -> new CustomParameterizedException("The talent to update dose not exist"));
        List<TalentContactDTO> contacts = talentContactRepository.findAllByTalentId(talentId).stream().map(c -> {
            TalentContactDTO talentContactDTO = new TalentContactDTO();
            BeanUtil.copyProperties(c, talentContactDTO);
            return talentContactDTO;
        }).toList();
        String talentExtendedInfo = talentV3.getTalentExtendedInfo();
        List<TalentExperienceDTO> talentExperienceList = null;
        if (talentExtendedInfo != null) {
            JSONArray experiences = JSON.parseObject(talentExtendedInfo).getJSONArray("experiences");
            if (experiences != null) {
                talentExperienceList = experiences.toJavaList(TalentExperienceDTO.class);
            }
        }
        TalentCurrentLocation talentLocation = talentLocationRepository.findByTalentId(talentId);
        LocationDTO locationDTO = talentLocation == null ? null : LocationDTO.fromOriginalLoc(talentLocation.getId(), talentLocation.getOriginalLoc());
        List<TalentOwnershipDTO> ownerships = talentOwnershipRepository.findAllByTalentId(talentId)
                .stream().map(o -> {
                    TalentOwnershipDTO dto = new TalentOwnershipDTO();
                    dto.setTalentId(o.getTalentId());
                    dto.setOwnershipType(o.getOwnershipType());
                    dto.setUserId(o.getUserId());
                    return dto;
                }).toList();
        EditTalentSyncCrmDTO editTalentSyncCrmDTO = new EditTalentSyncCrmDTO();
        editTalentSyncCrmDTO.setFirstName(talentV3.getFirstName());
        editTalentSyncCrmDTO.setLastName(talentV3.getLastName());
        editTalentSyncCrmDTO.setContacts(contacts);
        editTalentSyncCrmDTO.setExperiences(talentExperienceList);
        editTalentSyncCrmDTO.setCurrentLocation(locationDTO);
        fillClientContactOwnerAndShareUsers(ownerships, editTalentSyncCrmDTO);
        syncCrmTalent(talentId, editTalentSyncCrmDTO);
    }


    @Override
    public void updateClientContact(Long id, TalentV3 input) {
        TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(input);
        if (isClientContact(id)) {
            talentDTOV3.setContacts(Convert.toList(TalentContactDTO.class, talentContactRepository.findAllByTalentId(input.getId())));
            EditTalentSyncCrmDTO editTalentSyncCrmDTO = new EditTalentSyncCrmDTO();
            BeanUtil.copyProperties(talentDTOV3, editTalentSyncCrmDTO);
            syncCrmTalent(id, editTalentSyncCrmDTO);
        }
    }

    @Override
    public void syncClientContactToCrm(Long talentId) {
        if (isClientContact(talentId)) {
            log.info("[TalentServiceImpl: syncClientContactToCrm] sync client contact to crm, talentId: {}", talentId);
            TalentV3 dbTalent = talentRepository.findById(talentId).orElseThrow(() -> new CustomParameterizedException("The talent to update dose not exist"));
            TalentDTOV3 talentDTOV3 = TalentDTOV3.fromTalent(dbTalent);
            talentDTOV3.setContacts(Convert.toList(TalentContactDTO.class, talentContactRepository.findAllByTalentId(talentId)));

            EditTalentSyncCrmDTO editTalentSyncCrmDTO = new EditTalentSyncCrmDTO();
            BeanUtil.copyProperties(talentDTOV3, editTalentSyncCrmDTO);
            try {
                syncCrmTalent(talentId, editTalentSyncCrmDTO);
            } catch (Exception e) {
                log.error("[TalentServiceImpl: syncClientContactToCrm] sync client contact to crm error, talentId: {}, error msg: {}", talentId, e.getMessage(), e);
            }

        }
    }

    private boolean isClientContact(Long id) {
        var contactStatusList = companyService.getTalentClientContactStatus(List.of(id)).getBody();
        Map<Long, Boolean> talentClientContactStatusMap = contactStatusList
                .stream()
                .collect(Collectors.toMap(
                        TalentClientContactStatusDTO::getTalentId,
                        TalentClientContactStatusDTO::getIsClientContact
                ));
        return Boolean.TRUE.equals(talentClientContactStatusMap.get(id));
    }

    private void fillClientContactOwnerAndShareUsers(List<TalentOwnershipDTO> ownershipDTOS, EditTalentSyncCrmDTO editTalentSyncCrmDTO) {
        if (ownershipDTOS == null || ownershipDTOS.isEmpty() || editTalentSyncCrmDTO == null) {
            return;
        }
        List<Long> ownerUserIds = ownershipDTOS.stream().filter(ownership -> TalentOwnershipType.TALENT_OWNER.equals(ownership.getOwnershipType()))
                .map(TalentOwnershipDTO::getUserId)
                .filter(Objects::nonNull).toList();
        boolean shareToAll = ownershipDTOS.stream().anyMatch(ownership -> TalentOwnershipType.TENANT_SHARE.equals(ownership.getOwnershipType()));
        List<Long> shareUserIds = ownershipDTOS.stream().filter(ownership -> TalentOwnershipType.SHARE.equals(ownership.getOwnershipType()))
                .map(TalentOwnershipDTO::getUserId)
                .filter(Objects::nonNull).toList();
        editTalentSyncCrmDTO.setShareToAll(shareToAll);
        editTalentSyncCrmDTO.setOwnerUserIds(ownerUserIds);
        editTalentSyncCrmDTO.setShareUserIds(shareUserIds);
    }

    private void syncCrmTalent(Long id, EditTalentSyncCrmDTO editTalentSyncCrmDTO) {
        String syncCrmUrl = getSyncCrmUrl(id);
        String authorizationHeader = getAuthorizationHeader();
        String body = JsonUtil.toJson(editTalentSyncCrmDTO);
        log.info("sync crm client contact url : {}, authorizationHeader: {}, talentId: {}, body: {}", syncCrmUrl, authorizationHeader, id, body);
        cn.hutool.http.HttpResponse response = cn.hutool.http.HttpUtil.createRequest(Method.PUT, syncCrmUrl)
                .header("Authorization", authorizationHeader)
                .body(body).execute();

        log.info("sync crm client contact, talentId: {}, response code : {}, response message: {}", id, response.getStatus(), response.body());
        int code = response.getStatus();
        if (HttpStatus.UNAUTHORIZED.value() == code) {
            throw new ExternalServiceInterfaceException("No crm update permission", HttpStatus.FORBIDDEN.value());
        } else if (HttpStatus.OK.value() == code || HttpStatus.NOT_FOUND.value() == code) {
            return;
        } else {
            if (response.getStatus() == cn.hutool.http.Status.HTTP_PRECON_FAILED) {
                JSONObject res = JSONUtil.parseObj(response.body());
                throw new WithDataException(res.getStr("message"), cn.hutool.http.Status.HTTP_PRECON_FAILED, res.get("data"));
            } else {
                throw new ExternalServiceInterfaceException(response.body(), response.getStatus());
            }
        }
    }


    private String getAuthorizationHeader() {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }

    private String getSyncCrmUrl(Long id) {
        return applicationProperties.getCrmUrl() + CRM_SYNC_TALENT + id;
    }
}
