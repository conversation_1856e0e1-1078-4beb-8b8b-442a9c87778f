package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.dto.folder.FolderSharedTeamDTO;
import com.altomni.apn.common.enumeration.ReviewedByType;
import com.altomni.apn.talent.domain.talent.TalentReviewNote;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface TalentReviewNoteRepository  extends JpaRepository<TalentReviewNote, Long> {
    List<TalentReviewNote> findAllByTalentIdIsAndReviewedByIsAndReviewedByTypeIs(Long talentId, Long reviewedBy, ReviewedByType reviewedByType);
    List<TalentReviewNote> findAllByTalentIdIs(Long talentId);

    List<TalentReviewNote> findAllByTalentIdIsOrderByLastModifiedDateAsc(Long talentId);

    @Query("SELECT DISTINCT new com.altomni.apn.common.dto.folder.FolderSharedTeamDTO(t.id, t.name) " +
            "FROM PermissionTeam t " +
            "WHERE t.id in :ids")
    List<FolderSharedTeamDTO> findFolderSharedTeamDTO(@Param("ids") Set<Long> ids);
}
