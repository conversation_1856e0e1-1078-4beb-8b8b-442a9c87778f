package com.altomni.apn.talent.web.rest.talent;

import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.dto.talent.TalentOwnershipDTO;
import com.altomni.apn.talent.service.talent.TalentOwnershipService;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Set;

/**
 * REST controller for managing TalentOwnership.
 */
@RestController
@RequestMapping("/api/v3")
public class TalentOwnershipResource {

    private final Logger log = LoggerFactory.getLogger(TalentOwnershipResource.class);

    private static final String ENTITY_NAME = "TalentOwnership";

    @Resource
    private TalentOwnershipService talentOwnershipService;

    /**
     * @return the ResponseEntity with status 201 (Created) and with body the new talentOwner, or with status 400 (Bad Request) if the TalentOwnership has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/talent/{talentId}/ownerships")
    public ResponseEntity<List<TalentOwnershipDTO>> replace(@ApiParam(value = "talentId", required = true) @PathVariable Long talentId, @RequestBody List<Long> userIds) throws URISyntaxException {
        log.info("[APN: TalentOwnership @{}] REST request to replace TalentOwnerships by talentId : {}, userIds: {}", SecurityUtils.getUserId(), talentId, userIds);
        List<TalentOwnershipDTO> result = talentOwnershipService.replace(talentId, userIds);
        return ResponseEntity.created(new URI("/api/v3/talent/" + talentId + "/ownerships"))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, talentId.toString()))
            .body(result);
    }

    /**
     * @return the ResponseEntity with status 200 (OK) and the list of TalentOwnerships in body
     */
    @GetMapping("/talent/{talentId}/ownerships")
    public List<TalentOwnershipDTO> getAllTalentOwners(@ApiParam(value = "talentId", required = true) @PathVariable Long talentId) {
        log.info("[APN: TalentOwnership @{}] REST request to get TalentOwnerships by talentId : {}", SecurityUtils.getUserId(), talentId);
        return talentOwnershipService.findAll(talentId);
    }

    @DeleteMapping("/talent/{talentId}/ownerships")
    public ResponseEntity<Void> delete(@ApiParam(value = "talentId", required = true) @PathVariable Long talentId) {
        log.info("[APN: TalentOwnership @{}] REST request to delete TalentOwnership by talentId: {}", SecurityUtils.getUserId(), talentId);
        talentOwnershipService.delete(talentId);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, talentId.toString())).build();
    }



    @GetMapping("/talent-ownerships/talentId/{talentId}")
    public ResponseEntity<List<TalentOwnership>> getAllTalentOwners(@PathVariable("talentId") Long talentId, @RequestParam List<TalentOwnershipType> talentOwnershipType) throws URISyntaxException {
        log.info("[APN: TalentOwnership @{}] REST request to get TalentOwnerships by talentId : {}", SecurityUtils.getUserId(), talentId);
        List<TalentOwnership> result =  talentOwnershipService.findAllByTalentIdAndOwnershipTypeIncludeExpired(talentId, talentOwnershipType);
        return ResponseEntity.created(new URI("/api/v3/talent-ownerships/talentId/" + talentId))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, talentId.toString()))
                .body(result);
    }

    @PostMapping("/talent-ownerships")
    public ResponseEntity<List<TalentOwnership>> saveAll(@RequestBody List<TalentOwnership> talentOwnerships) throws URISyntaxException {
        log.info("[APN: TalentOwnership @{}] REST request to save all, TalentOwnerships: {}", SecurityUtils.getUserId(), talentOwnerships);
        List<TalentOwnership> result = talentOwnershipService.saveAll(talentOwnerships);
        return ResponseEntity.created(new URI("/api/v3/talent-ownerships"))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.toString()))
                .body(result);
    }

    @PutMapping("/talent-ownerships/transfer/from/{userId}/to/{newOwnerId}")
    public ResponseEntity<Void> transferOwnership(@PathVariable("userId") Long userId, @PathVariable("newOwnerId") Long newOwnerId) {
        log.info("[APN: TalentOwnership @{}] REST request to transfer TalentOwnerships from {} to {}", SecurityUtils.getUserId(), userId, newOwnerId);
        talentOwnershipService.transferOwnership(userId, newOwnerId);
        talentOwnershipService.removeDuplicateOwnerAndShare(newOwnerId);
        talentOwnershipService.inactivateCrmUser(userId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/talent-ownerships/get-valid-talents")
    public ResponseEntity<Set<Long>> getValidTalents(@RequestBody List<Long> talentIds) {
        log.info("[APN: TalentOwnership @{}] REST request to filter valid talents by data permission", SecurityUtils.getUserId());
        Set<Long> validTalents = talentOwnershipService.filterValidTalents(talentIds);
        return ResponseEntity.ok(validTalents);
    }
}
