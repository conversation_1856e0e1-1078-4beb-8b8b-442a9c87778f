package com.altomni.apn.talent.service.dto.tracking;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(value = "talent tracking group manage dto")
@NoArgsConstructor
@AllArgsConstructor
public class TalentTrackingGroupManageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "groupIds")
    @NotEmpty
    @UniqueElements
    private List<Long> groupIds;

    @Valid
    @ApiModelProperty(value = "members")
    @NotEmpty
    @UniqueElements
    private List<TalentTrackingGroupMemberDTO> members;

}
