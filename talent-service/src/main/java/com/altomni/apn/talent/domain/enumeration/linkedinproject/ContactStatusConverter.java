package com.altomni.apn.talent.domain.enumeration.linkedinproject;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class ContactStatusConverter extends AbstractAttributeConverter<ContactStatus, Integer> {
    public ContactStatusConverter() {
        super(ContactStatus::toDbValue, ContactStatus::fromDbValue);
    }
}
