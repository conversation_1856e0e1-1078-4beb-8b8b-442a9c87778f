package com.altomni.apn.talent.service.folder;

import java.util.List;

public interface TalentSearchFolderSearchParamService {


    void disableSearchFolder(Long talentFolderId);

    void disableSearchFolderWithFolderParamFromTeamShared(Long talentFolderId, List<Long> teamIds);

    void enableSearchFolderWithFolderParamFromTeamShared(Long talentFolderId, List<Long> teamIds);

    void disableSearchFolderWithFolderParamFromUserShared(Long talentFolderId, List<Long> userIds);

    void enableSearchFolderWithFolderParamFromUserShared(Long talentFolderId, List<Long> userIds);
}
