package com.altomni.apn.talent.domain.linkedin;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;

/**
 * 领英助手埋点表
 */
@Entity
@Table(name = "linkedin_assistant_event_tracking")
@Data
public class LinkedinAssistantEventTracking extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Size(max = 50)
    @Column(name = "event_name", length = 50, nullable = false)
    private String eventName;

    @NotNull
    @Size(max = 255)
    @Column(name = "operator_linkedin_id", length = 255, nullable = false)
    private String operatorLinkedinId;

    @NotNull
    @Size(max = 100)
    @Column(name = "linkedin_id", length = 100, nullable = false)
    private String linkedinId;

    @Size(max = 255)
    @Column(name = "detail", length = 255)
    private String detail;

    @NotNull
    @Column(name = "event_datetime", nullable = false)
    private Instant eventDatetime;
}