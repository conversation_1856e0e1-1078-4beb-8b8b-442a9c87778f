package com.altomni.apn.talent.service.dto.talent;

import com.altomni.apn.common.dto.talent.TalentESEmailStatusDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentCommonPoolConditionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private TalentESEmailStatusDTO emailStatus;

    public TalentCommonPoolConditionDTO(String id) {
        this.id = id;
    }
}
