package com.altomni.apn.talent.service.vo.record;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.NotePriority;
import com.altomni.apn.common.domain.enumeration.NotePriorityConverter;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteStatus;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteStatusConverter;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteTypeConverter;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * A TalentNote.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentNoteVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String title;

    private String note;

    private Boolean visible = true;

    private NotePriority priority = NotePriority.NORMAL;

    private Long talentId;

    private Long userId;

    private TalentNoteType noteType;

    private TalentNoteStatus noteStatus;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public TalentNoteVO title(String title) {
        this.title = title;
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getNote() {
        return note;
    }

    public TalentNoteVO note(String note) {
        this.note = note;
        return this;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Boolean isVisible() {
        return visible;
    }

    public TalentNoteVO visible(Boolean visible) {
        this.visible = visible;
        return this;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public NotePriority getPriority() {
        return priority;
    }

    public TalentNoteVO priority(NotePriority priority) {
        this.priority = priority;
        return this;
    }

    public TalentNoteVO priority(Integer value) {
        this.priority = NotePriority.fromDbValue(value);
        return this;
    }

    public void setPriority(NotePriority priority) {
        this.priority = priority;
    }

    public Boolean getVisible() {
        return visible;
    }

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TalentNoteVO talentNote = (TalentNoteVO) o;
        if (talentNote.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), talentNote.getId());
    }

    public TalentNoteType getNoteType() {
        return noteType;
    }

    public void setNoteType(TalentNoteType noteType) {
        this.noteType = noteType;
    }

    public TalentNoteStatus getNoteStatus() {
        return noteStatus;
    }

    public void setNoteStatus(TalentNoteStatus noteStatus) {
        this.noteStatus = noteStatus;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "TalentNote{" +
            "id=" + id +
            ", title='" + title + '\'' +
            ", note='" + note + '\'' +
            ", visible=" + visible +
            ", priority=" + priority +
            ", talentId=" + talentId +
            ", userId=" + userId +
            "} " + super.toString();
    }

    public static TalentNoteVO fromTalentNote(TalentNote talentNote) {
        TalentNoteVO talentNoteVO = new TalentNoteVO();
        ServiceUtils.myCopyProperties(talentNote, talentNoteVO);
        return talentNoteVO;
    }
}
