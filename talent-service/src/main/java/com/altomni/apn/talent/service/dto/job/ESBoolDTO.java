package com.altomni.apn.talent.service.dto.job;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ESBoolDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private ESBoolQueryDTO bool;

    private JSONObject term;

    private JSONObject terms;

    private JSONObject exists;

    private JSONObject match_phrase;

    private JSONObject multi_match;

    private JSONObject match;

    private JSONObject match_all;

    private JSONObject range;
}
