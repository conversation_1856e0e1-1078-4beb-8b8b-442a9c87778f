package com.altomni.apn.talent.service.dto.confidential;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.talent.domain.confidential.ConfidentialRule;
import com.altomni.apn.talent.domain.confidential.ConfidentialityRules;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;

@Getter
@Setter
public class ConfidentialRuleDto {

    private Long id;
    private boolean enabled;

    private String currency;
    private BigDecimal salary;
    private Set<String> industries = Collections.emptySet();
    private Set<String> jobFunctions = Collections.emptySet();
    private String degreeLevel;

    private Integer declassifyDays;
    private Set<Long> declassifyProcessIds = Collections.emptySet();
    private Set<NodeType> declassifyProcessTypes = Collections.emptySet();
    private Integer declassifyProcessDelayDays;

    private String createdBy;
    private Instant createdDate;
    private String lastModifiedBy;
    private Instant lastModifiedDate;


    public void validate() {
        // 进入保密规则，必须至少有一项规则不为空
        boolean confidentialRuleEmpty = (currency == null && salary == null) && industries.isEmpty() && jobFunctions.isEmpty() && (degreeLevel == null || degreeLevel.isBlank());
        // 自动解除保密周期不可为空
        boolean declassificationEmpty = declassifyDays == null;
        if (confidentialRuleEmpty || declassificationEmpty) {
           throw new CustomParameterizedException("Confidential rule cannot be empty");
        }
        // 自动解除保密周期必须在1-365之间
        if (declassifyDays < 1 || declassifyDays > 365) {
            throw new CustomParameterizedException("Declassification days should be between 1 and 365");
        }

        // 流程自动解除保密，必须同时设置流程类型和延迟天数
        if (!declassifyProcessTypes.isEmpty() || !declassifyProcessIds.isEmpty() || declassifyProcessDelayDays != null) {
            if (declassifyProcessTypes.isEmpty() || declassifyProcessIds.isEmpty() || declassifyProcessDelayDays == null) {
                throw new CustomParameterizedException("Declassification process type and delay days should be both present or both null");
            }
        }
        // 流程自动解除保密延迟天数必须在0-365之间
        if (declassifyProcessDelayDays != null && (declassifyProcessDelayDays < 0 || declassifyProcessDelayDays > 365)) {
            throw new CustomParameterizedException("Declassification process delay days should be between 0 and 365");
        }
    }

    public void fillWithEntity(ConfidentialRule rule) {
        this.id = rule.getId();
        this.enabled = rule.isEnabled();
        Optional.ofNullable(rule.getConfidentialityRules())
                .ifPresent(cr -> {
                    this.industries = cr.getIndustries();
                    this.jobFunctions = cr.getJobFunctions();
                    this.degreeLevel = cr.getDegreeLevel();
                    this.currency = Optional.ofNullable(cr.getSalary()).map(ConfidentialityRules.Salary::getCurrency).orElse(null);
                    this.salary = Optional.ofNullable(cr.getSalary()).map(ConfidentialityRules.Salary::getAmount).orElse(null);
                });
        Optional.ofNullable(rule.getDeclassificationRules())
                .ifPresent(dr -> {
                    this.declassifyDays = dr.getDeclassifyDays();
                    this.declassifyProcessIds = dr.getDeclassifyProcessIds();
                    this.declassifyProcessTypes = dr.getDeclassifyProcessTypes();
                    this.declassifyProcessDelayDays = dr.getDeclassifyProcessDelayDays();
                });
        this.createdBy = rule.getCreatedBy();
        this.createdDate = rule.getCreatedDate();
        this.lastModifiedBy = rule.getLastModifiedBy();
        this.lastModifiedDate = rule.getLastModifiedDate();
    }
}
