package com.altomni.apn.talent.domain.enumeration;

public enum ReportTypeEnum {
    PDF(1), DOC(2);

    private final int dbValue;

    ReportTypeEnum(int dbValue) {
        this.dbValue = dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public static ReportTypeEnum fromDbValue(int dbValue) {
        for (ReportTypeEnum reportTypeEnum : ReportTypeEnum.values()) {
            if (reportTypeEnum.getDbValue() == dbValue) {
                return reportTypeEnum;
            }
        }
        return null;
    }
}
