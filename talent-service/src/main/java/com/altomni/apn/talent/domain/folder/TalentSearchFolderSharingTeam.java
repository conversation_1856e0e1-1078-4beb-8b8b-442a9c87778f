package com.altomni.apn.talent.domain.folder;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonRawValue;
import com.google.common.base.Objects;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Getter
@Setter
@Entity
@Table(name = "talent_search_folder_sharing_team")
public class TalentSearchFolderSharingTeam extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "talent_search_folder_id")
    private Long talentSearchFolderId;

    @Column(name = "team_id")
    private Long teamId;

    @JsonRawValue
    @Column(name = "excluded_user_ids")
    private String excludedUserIds;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TalentSearchFolderSharingTeam that = (TalentSearchFolderSharingTeam) o;
        return Objects.equal(id, that.id) && Objects.equal(talentSearchFolderId, that.talentSearchFolderId) && Objects.equal(teamId, that.teamId) && Objects.equal(excludedUserIds, that.excludedUserIds);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id, talentSearchFolderId, teamId, excludedUserIds);
    }
}