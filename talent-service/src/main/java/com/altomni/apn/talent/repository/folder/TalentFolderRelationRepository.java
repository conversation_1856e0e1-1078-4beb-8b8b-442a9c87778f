package com.altomni.apn.talent.repository.folder;


import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.talent.domain.folder.TalentFolderRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

@Repository
public interface TalentFolderRelationRepository extends JpaRepository<TalentFolderRelation, Long> {

    //for batch delete
    @Modifying
    @Query("DELETE FROM TalentFolderRelation jfr WHERE jfr.talentId IN :talentIds AND jfr.talentFolderId = :folderId")
    void deleteAllByTalentIdInAndTalentFolderId(@Param("talentIds") List<Long> talentIds, @Param("folderId") Long folderId);

    List<TalentFolderRelation> getAllByTalentFolderId(Long talentFolderId);

    List<TalentFolderRelation> findAllByTalentFolderIdAndTalentId(Long talentFolderId, Long talentId);

    List<TalentFolderRelation> getAllByTalentFolderIdIn(List<Long> talentFolderId);

    @Query("SELECT jfr.talentFolderId FROM TalentFolderRelation jfr WHERE jfr.talentId =:talentIds")
    Set<Long> findFolderIdsByTalentId(@Param("talentIds") Long talentId);

    @Query(value =
            "SELECT tc.contact " +
                    "FROM (" +
                    "    SELECT tc.contact, " +
                    "           ROW_NUMBER() OVER (PARTITION BY tc.talent_id ORDER BY tc.jhi_type ASC) as rn " +
                    "    FROM talent_folder_relation tfr " +
                    "    JOIN talent_contact tc " +
                    "    ON tfr.talent_id = tc.talent_id " +
                    "    AND (tc.jhi_type IN :contactTypes) " +
                    "    AND tc.status = :contactStatus " +
                    "    WHERE tfr.talent_folder_id IN :folderIds" +
                    ") tc " +
                    "WHERE tc.rn = 1",
            nativeQuery = true)
    List<String> findTalentsContactsByFolderIds(@Param("folderIds") List<Long> folderIds, @Param("contactTypes") List<Integer> contactTypes, @Param("contactStatus") Integer status);

    default  List<String> findTalentsEmailByFolderIds(List<Long> folderIds){
        List<Integer> types = Arrays.asList(ContactType.PRIMARY_EMAIL.toDbValue(), ContactType.EMAIL.toDbValue());
        return findTalentsContactsByFolderIds(folderIds, types, TalentContactStatus.AVAILABLE.toDbValue());
    }

    Long countAllByTalentFolderIdIn(List<Long> folderIds);
}
