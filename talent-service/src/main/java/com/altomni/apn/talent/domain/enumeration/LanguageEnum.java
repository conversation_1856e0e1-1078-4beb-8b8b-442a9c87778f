package com.altomni.apn.talent.domain.enumeration;

public enum LanguageEnum {
    CHINESE(1, "chinese"), ENGLISH(2, "english");

    private final int dbValue;
    private final String desc;

    LanguageEnum(int dbValue, String desc) {
        this.dbValue = dbValue;
        this.desc = desc;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getDesc() {
        return desc;
    }

    public static LanguageEnum fromDbValue(int dbValue) {
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            if (languageEnum.getDbValue() == dbValue) {
                return languageEnum;
            }
        }
        return null;
    }

    public static LanguageEnum fromDescValue(String value) {
        for (LanguageEnum languageEnum : LanguageEnum.values()) {
            if (languageEnum.getDesc().equalsIgnoreCase(value)) {
                return languageEnum;
            }
        }
        return null;
    }
}
