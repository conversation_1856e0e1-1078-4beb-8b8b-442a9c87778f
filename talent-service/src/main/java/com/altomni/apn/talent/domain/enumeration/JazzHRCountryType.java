package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContactType enumeration.
 */
public enum JazzHRCountryType implements ConvertedEnum<Integer> {
    United_States(61),
    China(115)
    ;

    private final Integer dbValue;

    JazzHRCountryType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<JazzHRCountryType, Integer> resolver =
        new ReverseEnumResolver<>(JazzHRCountryType.class, JazzHRCountryType::toDbValue);

    public static JazzHRCountryType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
