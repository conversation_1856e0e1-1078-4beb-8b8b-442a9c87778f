package com.altomni.apn.talent.service.rabbitmq.listener;

import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@RefreshScope
public class NormalizedTalentListener {

    @Resource
    private EsFillerTalentService esFillerTalentService;

    @RabbitListener(queues = {"${application.esfillerMQ.apnNormalizedTalentQueue}"})
    @RabbitHandler
    public void processHandler(Channel channel, Message message) throws IOException {
//        log.info("Normalized talent message received：{}", message.toString());
        String normalizedTalent = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("normalizedTalent_from_es: {}", normalizedTalent);
        esFillerTalentService.saveNormalizedTalentInfos(normalizedTalent);
        //channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }
}
