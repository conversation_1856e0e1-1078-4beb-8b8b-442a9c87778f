package com.altomni.apn.talent.service.folder;

import com.altomni.apn.common.dto.folder.talentrelatejob.SharedLinkDTO;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderRelationDTO;
import com.altomni.apn.common.vo.job.jobsharing.JobSharingPublicResponseVO;

public interface TalentRelateJobFolderJobSharingPlatformProcessService {

    JobSharingPublicResponseVO getSharingPlatformURLExpirationTime(SharedLinkDTO sharedLinkDTO);

    String updateSharingPlatformURLExpirationTime(String folderId, TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO);

    String createOrUpdateJobSharingAndTalentRelateJobFolderRelation(TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO);
}
