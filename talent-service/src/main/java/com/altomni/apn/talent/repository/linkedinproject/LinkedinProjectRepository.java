package com.altomni.apn.talent.repository.linkedinproject;

import com.altomni.apn.talent.domain.linkedinproject.LinkedinProject;
import com.altomni.apn.talent.domain.linkedinproject.QLinkedinProject;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.jpa.repository.JpaRepository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Set;


/**
 * Spring Data  repository for the LinkedinProject entity.
 */
@Repository
public interface LinkedinProjectRepository extends JpaRepository<LinkedinProject, Long>, QuerydslPredicateExecutor<LinkedinProject>, QuerydslBinderCustomizer<QLinkedinProject> {

    @Override
    default public void customize(QuerydslBindings bindings, QLinkedinProject root) {
        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    }

    @Query(value = "select j.id from job j " +
            "inner join job_project jp on jp.id=j.pteam_id " +
            "where jp.tenant_id=:tenantId and j.id in :jobIds", nativeQuery = true)
    Set<Long> filterPrivateJob(@Param("tenantId") Long tenantId, @Param("jobIds") Collection<Long> jobIds);
}

