package com.altomni.apn.talent.repository.record;

import com.altomni.apn.talent.domain.record.TalentTrackingRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;


/**
 * Spring Data  repository for the TalentTrackingRecord entity.
 * <AUTHOR>
 */
@Repository
public interface TalentTrackingRecordRepository extends JpaRepository<TalentTrackingRecord, Long> {

    @Query(value = "SELECT * FROM talent_tracking_record t WHERE t.user_id = ?1 and t.contact IN ?2", nativeQuery = true)
    List<TalentTrackingRecord> findByUserIdAndContactList(Long userId, List<String> contacts);

    @Query(value = "SELECT user_id FROM talent_tracking_record t WHERE t.tenant_id = ?1 and t.contact IN ?2 group by t.user_id", nativeQuery = true)
    List<BigInteger> findUserIdByTenantIdAndContactList(Long tenantId, List<String> contacts);

    @Query(value = "SELECT t.* FROM talent_tracking_record t WHERE t.tenant_id = ?1 and t.contact IN ?2 ORDER BY t.touch_time DESC ", nativeQuery = true)
    List<TalentTrackingRecord> findAllByTenantIdAndContactList(Long tenantId, List<String> contacts);

    @Query(value = "SELECT * FROM talent_tracking_record t WHERE t.user_id IN ?1 and t.contact IN ?2", nativeQuery = true)
    List<TalentTrackingRecord> findByUserIdListAndContactList(List<Long> userIds, List<String> contacts);
}
