package com.altomni.apn.talent.service.talent.file;

import com.altomni.apn.talent.constants.ReportTemplateConstants;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;

public class LiePinHeaderFooterEventHandler extends PdfPageEventHelper {

    private String userName;

    public LiePinHeaderFooterEventHandler(String userName) {
        this.userName = userName;
    }

    @Override
    public void onStartPage(PdfWriter writer, Document document) {
        try {
            setPageHeader(writer, document);
        } catch (DocumentException e) {
            throw new RuntimeException(e);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void setPageHeader(PdfWriter writer, Document document) throws DocumentException, IOException {
        Font normalFont = FontFactory.getFont(ReportTemplateConstants.PINGFANG_FONT, BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 10);
        Font boldFont = new Font(normalFont.getBaseFont(), 18, Font.BOLD, BaseColor.BLACK);

        try {
            PdfContentByte canvas = writer.getDirectContent();
            float headerX = document.leftMargin();
            float headerY = document.top() + 10;

            // 绘制较粗的横线在文字上方
            canvas.setLineWidth(2f); // 增加线条宽度
            canvas.setColorStroke(BaseColor.BLACK);
            canvas.moveTo(document.leftMargin(), document.top() + 70); // 调整线条位置
            canvas.lineTo(document.right(), document.top() + 70);
            canvas.stroke();

            // 添加标题
            Phrase headerText = new Phrase("候选人简历", boldFont);
            ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, headerText, headerX, headerY + 40, 0); // 调整文字位置

            // 添加简历来源信息
            Font smallFont = new Font(normalFont.getBaseFont(), 8, Font.NORMAL, BaseColor.GRAY);
            String sourceInfo = org.apache.commons.lang3.StringUtils.replace("简历信息来源于APN用户{}的猎聘账号", "{}", userName);
            ColumnText.showTextAligned(canvas, Element.ALIGN_LEFT, new Phrase(sourceInfo, smallFont), headerX, headerY + 25, 0); // 调整文字位置

            // 添加logo
            ClassPathResource resource = new ClassPathResource(ReportTemplateConstants.LOGO);
            Image logo = Image.getInstance(resource.getInputStream().readAllBytes());
            logo.scaleToFit(100, 30);
            float logoX = document.right() - logo.getScaledWidth();
            float logoY = headerY + 25; // 调整logo位置
            logo.setAbsolutePosition(logoX, logoY);
            canvas.addImage(logo);

        } catch (Exception e) {
            // log.error("设置候选人简历PDF页眉时发生异常", e);
        }
    }
}
