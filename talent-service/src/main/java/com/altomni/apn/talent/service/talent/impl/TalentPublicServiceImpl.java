package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentFromSharingLinkDTO;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderRelatedInfoDTO;
import com.altomni.apn.common.dto.parser.ParserTalentData;
import com.altomni.apn.common.dto.redis.ParserResponse;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.dto.talent.TalentInfoInput;
import com.altomni.apn.common.dto.talent.TalentOwnershipDTO;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.common.errors.DuplicateException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.repository.talent.JobSharingPlatformRelatedFolderRepository;
import com.altomni.apn.common.service.enums.EnumLanguageService;
import com.altomni.apn.common.vo.talent.AddTalentsToFoldersOutput;
import com.altomni.apn.talent.service.dto.talent.TalentSimilarityDto;
import com.altomni.apn.talent.service.folder.TalentRelateJobFolderService;
import com.altomni.apn.talent.service.parser.ParserService;
import com.altomni.apn.talent.service.talent.TalentPublicService;
import com.altomni.apn.talent.service.talent.TalentService;
import com.altomni.apn.talent.web.rest.talent.dto.TalentsToFoldersDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service("talentPublicService")
public class TalentPublicServiceImpl implements TalentPublicService {

    @Resource
    private TalentService talentService;

    @Resource
    private TalentRelateJobFolderService talentRelateJobFolderService;

    @Resource
    private JobSharingPlatformRelatedFolderRepository jobSharingPlatformRelatedFolderRepository;


    @Resource
    private ParserService parserService;

    @Resource
    private EnumLanguageService enumLanguageService;


    @Override
    public Long createTalentFromSharedURL(TalentFromSharingLinkDTO talentFromSharingLinkDTO) {
        if (talentFromSharingLinkDTO.getTalent() == null) {
            //TODO: error handle
            return -1L;
        }

        List<Long> duplicatedTalentIds = findDuplicatedTalentIds(talentFromSharingLinkDTO);
        if(duplicatedTalentIds.size() == 1){
            addTalentIntoRelatedFolder(talentFromSharingLinkDTO.getSharedLinkUUID(), duplicatedTalentIds);
            return duplicatedTalentIds.get(0);
        }else if(duplicatedTalentIds.size() > 1){
            //todo: multi duplication
            return 0L;
        }

        TalentInfoInput talentInput = parseTalentResume(cleanTalentData(talentFromSharingLinkDTO.getTalent()));
        talentInput = generateTalentTenantAndOwnership(talentInput, talentFromSharingLinkDTO.getSharedLinkUUID());

        Long talentId = talentService.create(talentInput);

        addTalentIntoRelatedFolder(talentFromSharingLinkDTO.getSharedLinkUUID(), List.of(talentId));


        return talentId;
    }



    /*
    return duplicated talentIds list

     */
    private List<Long> findDuplicatedTalentIds(TalentFromSharingLinkDTO talentFromSharingLinkDTO){
        TalentInfoInput talentInput = talentFromSharingLinkDTO.getTalent();
        TalentSimilarityDto talentSimilarityDto = talentService.convertTalentInfoInputToSimilarityDTO(talentInput);
        try {
            log.info("startCheckDuplicationTalent");
            Set<Long> talentIdSet = talentService.searchTalentIdsByContactAndSimilarity(talentSimilarityDto);
            if (CollUtil.isNotEmpty(talentIdSet)) {
                return new ArrayList<>(talentIdSet);
            }
            return Collections.emptyList();
        } catch (NotFoundException ignored) {
            throw new DuplicateException("Please contact customer service to resolve this issue.");
            //todo: save to extra table
        }
    }

    private void addTalentIntoRelatedFolder(String sharedLinkUUID, List<Long> talentIds) {
        String folderId = jobSharingPlatformRelatedFolderRepository.findTalentAssociationJobFolderFolderIdByJobSharingPlatformUuid(sharedLinkUUID);
        TalentsToFoldersDTO talentsToFoldersDTO = new TalentsToFoldersDTO();
        talentsToFoldersDTO.setTalentIds(talentIds);
        AddTalentsToFoldersOutput output = talentRelateJobFolderService.addTalentsToFolders(folderId, talentsToFoldersDTO);
        //todo: check output;
    }


    /**
     * Cleanup input talent data
     *
     * @param input
     * @return
     */
    private TalentInfoInput cleanTalentData(TalentInfoInput input) {
        TalentInfoInput newInput = new TalentInfoInput();
        if (input == null) {
            return newInput;
        }


        if (input.getContacts() != null) {
            newInput.setContacts(input.getContacts());
        }
        if (input.getExperiences() != null) {
            newInput.setExperiences(input.getExperiences());
        }
        if (input.getFullName() != null) {
            newInput.setFullName(input.getFullName());
        }
        if (input.getParseResumeUuid() != null) {
            newInput.setParseResumeUuid(input.getParseResumeUuid());
        }
        if (input.getResumes() != null) {
            newInput.setResumes(input.getResumes());
        }
        return newInput;
    }


    private TalentInfoInput generateTalentTenantAndOwnership(TalentInfoInput talentInput, String sharedLinkUUID) {
        talentInput.setSource(ResumeSourceType.SHARED_LINK);
        Optional<TalentRelateJobFolderRelatedInfoDTO> relatedInfoOpt = jobSharingPlatformRelatedFolderRepository.findRelatedInfoByJobSharingPlatformUuid(sharedLinkUUID);
//        Optional<JobSharingPlatformTalentRelatedJobFolderRelation> relationOpt =  jobSharingPlatformRelatedFolderRepository.findByJobSharingPlatformUuid(sharedLinkUUID);
        relatedInfoOpt.ifPresentOrElse( relatedInfoDTO -> {
            talentInput.setTenantId(relatedInfoDTO.getTenantId());
            var talentOwnershipDTO = new TalentOwnershipDTO();
            talentOwnershipDTO.setOwnershipType(TalentOwnershipType.TALENT_OWNER);
            talentOwnershipDTO.setUserId(relatedInfoDTO.getUserId());
            talentInput.setOwnerships(List.of(talentOwnershipDTO));
        }, () -> {
            saveToFailedTable(talentInput, sharedLinkUUID, "Cannot get ownership");
        });

        return talentInput;
    }

    private TalentInfoInput parseTalentResume(TalentInfoInput input) {
        if (StringUtils.isBlank(input.getParseResumeUuid())) {
            return input;
        }

        String resumeUUID = input.getParseResumeUuid();

        ParseStatus status = null;
        while(status == null || !ParseStatus.FINISHED.equals(status)) {
            status = parserService.getResumeParseResultStatusOnly(resumeUUID).getBody().getStatus();
        }


        ParserResponse response = parserService.getParserResumeResult(resumeUUID).getBody();
        if (ObjectUtil.isNotEmpty(response.getStatus()) && ParseStatus.FINISHED.equals(response.getStatus())) {

            ParserTalentData parserTalentData = JSONUtil.toBean(response.getData(), ParserTalentData.class);

            JSONObject jsonObject = JSONUtil.parseObj(response.getData());


            //TODO: improve
            input.setFirstName(parserTalentData.getFirstName());
            input.setLastName(parserTalentData.getLastName());

            //personal info
            input.setGender(parserTalentData.getGender());

            //contacts
            input.setContacts(mergeContactFromInputAndParser(input.getContacts(), parserTalentData.getContacts()));

            //location
            input.setCurrentLocation(parserTalentData.getCurrentLocation());
            input.setPreferences(parserTalentData.getPreferences());
//            input.setPreferredLocations(parserTalentData.getPreferredLocations());
//            input.setPreferredCurrency(parserTalentData.getCurrency());
//            input.setPreferredSalaryRange(parserTalentData.getPreferredSalaryRange());

            input.setSkills(parserTalentData.getSkills());
            input.setJobFunctions(parserTalentData.getJobFunctions());
            input.setIndustries(parserTalentData.getIndustries());
            input.setLanguages(convertLanguageNameToIds(parserTalentData.getLanguages()));

            input.setExperiences(mergeExperienceFromInputAndParser(input.getExperiences(), parserTalentData.getExperiences()));
            input.setProjects(parserTalentData.getProjects());
            input.setEducations(parserTalentData.getEducations());

            input.setPayType(parserTalentData.getPayType());
            input.setSalaryRange(parserTalentData.getSalaryRange());

            //merge parts



        }else {
            //todo: save into queue or redis;
        }


        return input;
    }

    private List<TalentExperienceDTO> mergeExperienceFromInputAndParser(List<TalentExperienceDTO> inputExperiences, List<TalentExperienceDTO> parserExperiences) {
        if (CollectionUtils.isEmpty(inputExperiences) && CollectionUtils.isEmpty(parserExperiences)) {
            return Collections.emptyList();
        }

        if (CollectionUtils.isEmpty(parserExperiences)) {
            return inputExperiences;
        }

        if (CollectionUtils.isEmpty(inputExperiences)) {
            return parserExperiences;
        }

        Map<String, TalentExperienceDTO> experienceMap = new HashMap<>();


        for (TalentExperienceDTO experience : inputExperiences) {
            String key = generateExperienceKey(experience);
            experienceMap.put(key, experience);
        }


        for (TalentExperienceDTO experience : parserExperiences) {
            String key = generateExperienceKey(experience);
            if (!experienceMap.containsKey(key)) {
                experienceMap.put(key, experience);
            }
        }

        return new ArrayList<>(experienceMap.values());
    }

    private String generateExperienceKey(TalentExperienceDTO experience) {
        return experience.getCompanyName() + "|" +
                (Boolean.TRUE.equals(experience.getCompanyName()) ? "current" : "") + "|" +
                (experience.getStartDate() != null ? experience.getStartDate().toString() : "") + "|" +
                (experience.getEndDate() != null ? experience.getEndDate().toString() : "");
    }



    private List<TalentContactDTO> mergeContactFromInputAndParser(List<TalentContactDTO> inputContacts, List<TalentContactDTO> parserContacts) {
        if(CollectionUtils.isEmpty(inputContacts) && CollectionUtils.isEmpty(parserContacts) ){
            return Collections.emptyList();
        }

        if(CollectionUtils.isEmpty(parserContacts)){
            return inputContacts;
        }

        if(CollectionUtils.isEmpty(inputContacts)){
            return parserContacts;
        }

        Map<String, TalentContactDTO> contactMap = new HashMap<>();

        // Add inputContacts to the map
        for (TalentContactDTO contact : inputContacts) {
            String key = generateContactKey(contact);
            contactMap.put(key, contact);
        }

        // Merge parserContacts into the map
        for (TalentContactDTO contact : parserContacts) {
            String key = generateContactKey(contact);
            if (!contactMap.containsKey(key)) {
                contactMap.put(key, contact);
            }
        }

        return new ArrayList<>(contactMap.values());
    }

    private String generateContactKey(TalentContactDTO contact) {
        return contact.getType().toString() + "|" + contact.getContact();
    }

    // TODO: db enum is Long, current process is Integer
    private List<Integer> convertLanguageNameToIds(List<String> languageNames){
        if(CollectionUtils.isEmpty(languageNames)){
            return Collections.EMPTY_LIST;
        }
        Map<String, Long> map = enumLanguageService.getLanguagesNameToIdMap();
        return languageNames.stream().map(name -> map.getOrDefault(name, null)).filter(Objects::nonNull).map(Long::intValue).toList();

    }

    private void saveToFailedTable(TalentInfoInput talentInput, String sharedLinkUUID, String reason){
        //TODO: save to fail table;
    }

}
