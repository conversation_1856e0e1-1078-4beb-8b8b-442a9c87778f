package com.altomni.apn.talent.service.elastic.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.csp.sentinel.transport.command.http.StatusCode;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.config.constants.ContactTypeConstants;
import com.altomni.apn.common.constants.ResponsibilityConstants;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.common.dto.search.TalentSearchGroup;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.*;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.constants.ElasticSearchConstants;
import com.altomni.apn.talent.domain.report.ESStats;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.EnumCommonPoolService;
import com.altomni.apn.talent.service.UserService;
import com.altomni.apn.talent.service.common.HttpService;
import com.altomni.apn.talent.service.dto.elastic.EsSearchByContactsDTO;
import com.altomni.apn.talent.service.dto.talent.TalentCategoryCountRequestDTO;
import com.altomni.apn.talent.service.elastic.EsCommonService;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.elastic.dto.SearchEsBySource;
import com.altomni.apn.talent.service.report.EsStatsService;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Type;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * Service to talk to elastic search API
 *
 * <AUTHOR>
 */
@Service
public class EsCommonServiceImpl implements EsCommonService {

    private final Logger log = LoggerFactory.getLogger(EsCommonServiceImpl.class);

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private EsStatsService esStatsService;

    @Resource
    private EsFillerTalentService esFillerTalentService;

    @Resource
    private UserService userService;

    @Resource
    private HttpService httpService;

    @Resource
    private EnumLanguageService enumLanguageService;

    @Resource
    private EnumIndustryService enumIndustryService;

    @Resource
    private EnumJobFunctionService enumJobFunctionService;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private EnumDegreeService enumDegreeService;

    @Resource
    private EnumUserResponsibilityService enumUserResponsibilityService;

    @Resource
    private TalentRepository talentRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Resource
    EnumCommonPoolService enumCommonPoolService;

    @Resource
    CachePermission cachePermission;

    private OkHttpClient client = new OkHttpClient();

    private static final String API_V2 = "/api/v2/";

    private final OkHttpClient tardyClient = client.newBuilder()
            .connectTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(600, TimeUnit.SECONDS) // 10 minutes
            .readTimeout(600, TimeUnit.SECONDS) // 10 minutes
            .build();

    private static final okhttp3.MediaType JSON_TYPE = okhttp3.MediaType.parse("application/json; charset=utf-8");

    private static final String CONTACTS = "contacts";

    private static final String EDUCATIONS = "educations";

    private static final String GENDER = "gender";

    private static final String EXPERIENCES = "experiences";

    private static final String INDUSTRIES = "industries";

    private static final String JOB_FUNCTIONS = "jobFunctions";

    private String commonServiceUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/hitalent";
    }

    private String commonSearchCountServiceUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/hitalent/search-and-count";
    }

    private String commonServiceIdsUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/hitalent/ids";
    }

    private String searchEsSourceByIdUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/hitalent/ids";
    }

    private String likeSearchCollegeUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/auto-complete/college";
    }

    private String categoryCountCommonServiceUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/get-talent-count";
    }

    private boolean hasExtraContacts(TalentDTOV3 talent, List<String> contacts) {
        List<TalentContactDTO> talentContacts = talent.getContacts();
        if (CollectionUtils.isEmpty(talentContacts)) {
            return false;
        }
        if (CollectionUtils.isEmpty(contacts)) {
            return true;
        }
        Set<String> contactStringSet = talentContacts.stream().map(TalentContactDTO::getContact).collect(Collectors.toSet());
        Set<String> exists = new HashSet<>(contacts);
        contactStringSet.removeAll(exists);
        return CollectionUtils.isNotEmpty(contactStringSet);
    }

    @Override
    public TalentDTOV3 getEsTalentByEsId(String esId) throws IOException {

//        recordSearchRequest(esId);
        String url = applicationProperties.getApnCommonServiceUrl() + API_V2 + "es-talents";
        HttpResponse response = httpService.postPlainText(url, esId);
        String responseBody = (response != null) ? response.getBody() : null;
        Integer responseCode = response != null ? response.getCode() : null;
        log.info("[APN: EsCommonService @{}] es common service return response code: {}, body: {}", SecurityUtils.getUserId(), responseCode, responseBody);
        TalentDTOV3 talentDTO = toTalentDTO(JSONUtil.parseObj(responseBody));
        maskTalentContact(talentDTO);
        return talentDTO;
    }

    @Override
    public TalentDTOV3 getTalent(String id) throws IOException {
        TalentDTOV3 talentDTO = new TalentDTOV3();
        List<TalentContactDTO> contacts;
        TalentESConditionDTO condition = new TalentESConditionDTO();
        condition.setEsId(id);
        TalentESDocument document = getTalentESDocument(condition);
        if (ObjectUtil.isNull(document)) {
            return null;
        }
        String commonTalentContacts = esFillerTalentService.searchContactsFromCommonPool(condition);
        if (StringUtils.isNotBlank(commonTalentContacts)) {
            contacts = JSONUtil.toList(JSONUtil.parseArray(commonTalentContacts), TalentContactDTO.class);
            if (ObjectUtil.isNotEmpty(contacts)) {
                //translate contacts type
                contacts.forEach(c -> {
                    if (ContactTypeConstants.EXTRA_CONTACT_TYPES_PHONES.contains(c.getType())) {
                        c.setType(ContactType.PHONE);
                    }
                    if (ContactType.PRIMARY_EMAIL.equals(c.getType())) {
                        c.setType(ContactType.EMAIL);
                    }
                });
                talentDTO.setContacts(contacts.stream().filter(a -> StrUtil.isNotBlank(a.getContact())).collect(collectingAndThen(
                        toCollection(() -> new TreeSet<>(Comparator.comparing(TalentContactDTO::getContact))), ArrayList::new)));
            }
        }
        //Purchased or not
        CreditTransaction creditTransaction = userService.findByProfileIdAndTenantIdAndStatus(id, SecurityUtils.getTenantId(), Status.Available).getBody();
        if (ObjectUtil.isNotEmpty(creditTransaction)) {
            talentDTO.setPurchased(true);
            talentDTO.setCreditTransactionId(creditTransaction.getId());
            if (ObjectUtil.isNotNull(creditTransaction.getTalentId())) {
                talentDTO.setId(creditTransaction.getTalentId());
            }
        } else {
            maskContacts(talentDTO);
        }

        //translate common pool esDocument to talentDTO
        talentDTO = translateCommonPoolEsDocumentTalentDTO(talentDTO, document);
        talentDTO.setEsId(id);
        return talentDTO;
    }

    @Override
    public TalentDTOV3 getTalentForInternal(String id, Long tenantId) throws IOException {
        TalentDTOV3 talentDTO = new TalentDTOV3();
        List<TalentContactDTO> contacts;
        TalentESConditionDTO condition = new TalentESConditionDTO();
        condition.setEsId(id);
        TalentESDocument document = getTalentESDocument(condition);
        if (ObjectUtil.isNull(document)) {
            return null;
        }
        String commonTalentContacts = esFillerTalentService.searchContactsFromCommonPool(condition);
        if (StringUtils.isNotBlank(commonTalentContacts)) {
            contacts = JSONUtil.toList(JSONUtil.parseArray(commonTalentContacts), TalentContactDTO.class);
            if (ObjectUtil.isNotEmpty(contacts)) {
                //translate contacts type
                contacts.forEach(c -> {
                    if (ContactTypeConstants.EXTRA_CONTACT_TYPES_PHONES.contains(c.getType())) {
                        c.setType(ContactType.PHONE);
                    }
                    if (ContactType.PRIMARY_EMAIL.equals(c.getType())) {
                        c.setType(ContactType.EMAIL);
                    }
                });
                talentDTO.setContacts(contacts.stream().filter(a -> StrUtil.isNotBlank(a.getContact())).collect(collectingAndThen(
                        toCollection(() -> new TreeSet<>(Comparator.comparing(TalentContactDTO::getContact))), ArrayList::new)));
            }
        }
        //Purchased or not
        CreditTransaction creditTransaction = userService.findByProfileIdAndTenantIdAndStatus(id, tenantId, Status.Available).getBody();
        if (ObjectUtil.isNotEmpty(creditTransaction)) {
            talentDTO.setPurchased(true);
            talentDTO.setCreditTransactionId(creditTransaction.getId());
            if (ObjectUtil.isNotNull(creditTransaction.getTalentId())) {
                talentDTO.setId(creditTransaction.getTalentId());
            }
        } else {
            maskContacts(talentDTO);
        }
        talentDTO = translateCommonPoolEsDocumentTalentDTO(talentDTO, document);
        talentDTO.setEsId(id);
        return talentDTO;
    }

//    @Override
//    public EsResponseDTO searchTalents(String searchRequest) throws IOException {
//        recordSearchRequest(searchRequest);
//        String url = applicationProperties.getApnCommonServiceUrl() + "/api/v5/" + "search-talents";
//
//        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON_TYPE, searchRequest);
//        Request request = new Request.Builder().url(url).addHeader("uid", SecurityUtils.getCurrentUserLogin().get().getUsername()).addHeader("api", "apn").post(body).build();
//        try (Response response = client.newCall(request).execute()) {
//            String responseBody = response.body() != null ? response.body().string() : null;
//            log.debug("call common search service return response code: {}, message: {}, body: {}", response.code(), response.message(), responseBody);
//
//            if (!response.isSuccessful()) {
//                throw new CustomParameterizedException(responseBody);
//            }
//
//            List<TalentDTOV3> result = parseTalentFromResponse(responseBody, Collections.emptyList());
//            Page<TalentDTOV3> page;
//            if (result != null && result.size() > 0) {
//                page = new PageImpl<>(result, null, Long.parseLong(response.header("total")));
//            } else {
//                page = new PageImpl<>(new ArrayList<>(), null, 0);
//            }
//
//            String status = response.header("status");
//            String uuid = response.header("uuid");
//            return new EsResponseDTO().page(page).uuid(uuid).status("PENDING".equalsIgnoreCase(status) ? "PENDING" : "FINISHED");
//        }
//    }

    @Override
    public Page<TalentDTOV3> searchEsTalentsByContacts(EsSearchByContactsDTO searchRequest) throws IOException {
        //1. common search by search conditions
        Page<TalentDTOV3> res = new PageImpl<>(new ArrayList<>(), Pageable.unpaged(), 0);
        if (CollectionUtils.isEmpty(searchRequest.getSearchConditions())) {
            return res;
        }

        List<String> searchConditions = searchRequest.getSearchConditions() == null ? new ArrayList<>() : searchRequest.getSearchConditions();
        List<String> existContacts = searchRequest.getExistContacts() == null ? new ArrayList<>() : searchRequest.getExistContacts();

        String url = applicationProperties.getApnCommonServiceUrl() + "/api/v5" + "/search-by-contacts?encryptEsId=true";
//        String url = applicationProperties.getApnCommonServiceUrl() + "/api/v5" + "/search-by-contacts";
        okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON_TYPE, "[\"" + StringUtils.join(searchConditions, "\",\"") + "\"]");
        Request request = new Request.Builder().url(url).addHeader("pin", applicationProperties.getApnCommonServicePin()).post(body).build();
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            log.debug("call common search service return response code: {}, message: {}, body: {}", response.code(), response.message(), responseBody);

            if (!response.isSuccessful()) {
                throw new CustomParameterizedException(responseBody);
            }

            //2. filter talent with less contacts info
            List<TalentDTOV3> result = parseTalentFromResponse(responseBody, existContacts);
            if (!CollectionUtils.isEmpty(result)) {
                res = new PageImpl<>(result, Pageable.unpaged(), result.size());
            }
        }
        return res;
    }

    private TalentDTOV3 toTalentDTO(JSONObject data) {
        if (data == null) {
            return null;
        }
        JSONArray contactsJSON = null;
        if (CommonUtils.existsKey(data, CONTACTS)) {
            contactsJSON = data.getJSONArray(CONTACTS);
            data.remove(CONTACTS);
        }
        JSONArray educationsJSON = null;
        if (CommonUtils.existsKey(data, EDUCATIONS)) {
            educationsJSON = data.getJSONArray(EDUCATIONS);
            data.remove(EDUCATIONS);
        }
        JSONArray experiencesJSON = null;
        if (CommonUtils.existsKey(data, EXPERIENCES)) {
            experiencesJSON = data.getJSONArray(EXPERIENCES);
            data.remove(EXPERIENCES);
        }

        TalentDTOV3 talentDTO = Convert.convert(TalentDTOV3.class, data);

        List<TalentContactDTO> contacts = new ArrayList<>();
        if (contactsJSON != null) {
            for (int i = 0; i < contactsJSON.size(); i++) {
                JSONObject contactJSON = contactsJSON.getJSONObject(i);
                String type = contactJSON.getStr("type");
                String contactValue = contactJSON.getStr("contact");

                TalentContactDTO talentContact = new TalentContactDTO();
                talentContact.setContact(contactValue);
                if (StringUtils.equals("Email", type)) {
                    talentContact.setType(ContactType.EMAIL);
                    talentContact.setVerified(true);
                } else if (StringUtils.equals("Phone", type) || StringUtils.equals("Cell_Phone", type)) {
                    talentContact.setType(ContactType.PHONE);
                    talentContact.setVerified(true);
                } else if (StringUtils.equals("FaceBook", type)) {
                    talentContact.setType(ContactType.FACEBOOK);
                } else if (StringUtils.equals("LinkedIn", type)) {
                    talentContact.setType(ContactType.LINKEDIN);
                } else if (StringUtils.equals("WeChat", type)) {
                    talentContact.setType(ContactType.WECHAT);
                } else {
                    talentContact.setType(ContactType.PERSONAL_WEBSITE);
                }
                talentContact.setDetails(CommonUtils.getString(contactJSON, "details"));
                contacts.add(talentContact);
            }
            talentDTO.setContacts(contacts);
        }
        List<TalentEducationDTO> educations = new ArrayList<>();
        if (educationsJSON != null) {
            for (int i = 0; i < educationsJSON.size(); i++) {
                JSONObject educationJSON = educationsJSON.getJSONObject(i);
                TalentEducationDTO talentEducationDTO = new TalentEducationDTO();
                talentEducationDTO.setCollegeName(CommonUtils.getString(educationJSON, "collegeName"));
                talentEducationDTO.setDegreeName(CommonUtils.getString(educationJSON, "degreeName"));
                talentEducationDTO.setMajorName(CommonUtils.getString(educationJSON, "majorName"));
                talentEducationDTO.setStartDate(year2NormalLocalDate(CommonUtils.getString(educationJSON, "startDate")));
                talentEducationDTO.setEndDate(year2NormalLocalDate(CommonUtils.getString(educationJSON, "endDate")));
                educations.add(talentEducationDTO);
            }
            talentDTO.setEducations(educations);
        }

        if (experiencesJSON != null) {
            List<TalentExperienceDTO> experiences = new ArrayList<>();
            for (int i = 0; i < experiencesJSON.size(); i++) {
                JSONObject experienceJSON = experiencesJSON.getJSONObject(i);
                TalentExperienceDTO talentExperience = new TalentExperienceDTO();
                talentExperience.setCompanyName(CommonUtils.getString(experienceJSON, "companyName"));
                talentExperience.setTitle(CommonUtils.getString(experienceJSON, "title"));
                talentExperience.setStartDate(year2NormalLocalDate(CommonUtils.getString(experienceJSON, "startDate")));
                talentExperience.setEndDate(year2NormalLocalDate(CommonUtils.getString(experienceJSON, "endDate")));
                experiences.add(talentExperience);
            }
            talentDTO.setExperiences(experiences);
        }
        return talentDTO;
    }

    private LocalDate year2NormalLocalDate(String year) {
        if (StringUtils.isNotEmpty(year) && year.length() >= 4) {
            return DateUtil.stringToLocalDate(StringUtils.left(year, 4) + "-01-02");
        }
        return null;
    }

    @Resource
    private EnumCommonService enumCommonService;

    private TalentDTOV3 translateCommonPoolEsDocumentTalentDTO(TalentDTOV3 talentDTO, TalentESDocument talent) {
        if (ObjectUtil.isNull(talent)) {
            return null;
        }
        if (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            talentDTO.setGender(talent.getGender());
        }
        talentDTO.setFullName(talent.getFullName());
        talentDTO.setFirstName(talent.getFirstName());
        talentDTO.setLastName(talent.getLastName());
        //salary
        if (ObjectUtil.isNotNull(talent.getAnnualSalaryInUSD())) {
            talentDTO.setSalaryRange(talent.getAnnualSalaryInUSD());
        }
        if (ObjectUtil.isNotNull(talent.getPayType())) {
            talentDTO.setPayType(RateUnitType.valueOf(talent.getPayType()));
        }
        //TODO common pool等待数据源变为新结构
//        if (ObjectUtil.isNotNull(talent.getPreferredAnnualSalaryInUSD())) {
//            talentDTO.setPreferredSalaryRange(talent.getPreferredAnnualSalaryInUSD());
//        }
//        if (ObjectUtil.isNotNull(talent.getPreferredPayType())) {
//            talentDTO.setPreferredPayType(RateUnitType.valueOf(talent.getPreferredPayType()));
//        }
        //currentLocation
        if (ObjectUtil.isNotEmpty(talent.getCurrentLocation())) {
            LocationDTO currentLocation = new LocationDTO();
            if (ObjectUtil.isNotNull(talent.getCurrentLocation().getLocation())) {
                currentLocation.setLocation(talent.getCurrentLocation().getLocation());
            } else {
                if (ObjectUtil.isNotNull(talent.getCurrentLocation().getOfficialCountry())) {
                    currentLocation.setCountry(talent.getCurrentLocation().getOfficialCountry());
                } else if (ObjectUtil.isNotNull(talent.getCurrentLocation().getCountry())) {
                    currentLocation.setCountry(talent.getCurrentLocation().getCountry());
                }
                if (ObjectUtil.isNotNull(talent.getCurrentLocation().getOfficialProvince())) {
                    currentLocation.setProvince(talent.getCurrentLocation().getOfficialProvince());
                } else if (ObjectUtil.isNotNull(talent.getCurrentLocation().getProvince())) {
                    currentLocation.setProvince(talent.getCurrentLocation().getProvince());
                }
                if (ObjectUtil.isNotNull(talent.getCurrentLocation().getOfficialCity())) {
                    currentLocation.setCity(talent.getCurrentLocation().getOfficialCity());
                } else if (ObjectUtil.isNotNull(talent.getCurrentLocation().getCity())) {
                    currentLocation.setCity(talent.getCurrentLocation().getCity());
                }
            }
            talentDTO.setCurrentLocation(currentLocation);
        }
        //preferredLocations TODO common pool等待数据源变为新结构
//        if (ObjectUtil.isNotEmpty(talent.getPreferredLocations())) {
//            List<LocationDTO> locationDTOList = new ArrayList<>();
//            talent.getPreferredLocations().forEach(s -> {
//                LocationDTO preferredLocation = new LocationDTO();
//                if (ObjectUtil.isNotNull(s.getLocation())) {
//                    preferredLocation.setLocation(s.getLocation());
//                } else {
//                    if (ObjectUtil.isNotNull(s.getOfficialCountry())) {
//                        preferredLocation.setCountry(s.getOfficialCountry());
//                    } else if (ObjectUtil.isNotNull(s.getCountry())) {
//                        preferredLocation.setCountry(s.getCountry());
//                    }
//                    if (ObjectUtil.isNotNull(s.getOfficialProvince())) {
//                        preferredLocation.setProvince(s.getOfficialProvince());
//                    } else if (ObjectUtil.isNotNull(s.getProvince())) {
//                        preferredLocation.setProvince(s.getProvince());
//                    }
//                    if (ObjectUtil.isNotNull(s.getOfficialCity())) {
//                        preferredLocation.setCity(s.getOfficialCity());
//                    } else if (ObjectUtil.isNotNull(s.getCity())) {
//                        preferredLocation.setCity(s.getCity());
//                    }
//                }
//                locationDTOList.add(preferredLocation);
//            });
//            talentDTO.setPreferredLocations(locationDTOList);
//        }
        //educations
        if (ObjectUtil.isNotEmpty(talent.getTopEducation()) || ObjectUtil.isNotEmpty(talent.getNonTopEducations())) {
            List<TalentEducationDTO> educations = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(talent.getTopEducation())) {
                TalentEducationDTO education = Convert.convert(TalentEducationDTO.class, talent.getTopEducation());
                educations.add(education);
            }
            if (ObjectUtil.isNotEmpty(talent.getNonTopEducations())) {
                educations.addAll(Convert.toList(TalentEducationDTO.class, talent.getNonTopEducations()));
            }
            if (ObjectUtil.isNotEmpty(educations)) {
                talentDTO.setEducations(educations);
            }
        }
        //experiences
        if (ObjectUtil.isNotEmpty(talent.getCurrentExperiences()) || ObjectUtil.isNotEmpty(talent.getPastExperiences())) {
            List<TalentExperienceDTO> experiences = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(talent.getCurrentExperiences())) {
                talent.getCurrentExperiences().forEach(s -> {
                    TalentExperienceDTO experienceDTO = new TalentExperienceDTO();
                    if (ObjectUtil.isNotEmpty(s.getStartDate())) {
                        experienceDTO.setStartDate(DateUtil.stringToLocalDate(s.getStartDate()));
                    }
                    if (ObjectUtil.isNotEmpty(s.getCompanyName())) {
                        experienceDTO.setCompanyName(s.getCompanyName());
                    }
                    if (ObjectUtil.isNotEmpty(s.getTitle())) {
                        experienceDTO.setTitle(s.getTitle());
                    }
                    experienceDTO.setCurrent(true);
                    experiences.add(experienceDTO);
                });
            }
            if (ObjectUtil.isNotEmpty(talent.getPastExperiences())) {
                talent.getPastExperiences().forEach(s -> {
                    TalentExperienceDTO experienceDTO = new TalentExperienceDTO();
                    if (ObjectUtil.isNotEmpty(s.getStartDate())) {
                        experienceDTO.setStartDate(DateUtil.stringToLocalDate(s.getStartDate()));
                    }
                    if (ObjectUtil.isNotEmpty(s.getEndDate())) {
                        experienceDTO.setEndDate(DateUtil.stringToLocalDate(s.getEndDate()));
                    }
                    if (ObjectUtil.isNotEmpty(s.getCompanyName())) {
                        experienceDTO.setCompanyName(s.getCompanyName());
                    }
                    if (ObjectUtil.isNotEmpty(s.getTitle())) {
                        experienceDTO.setTitle(s.getTitle());
                    }
                    experiences.add(experienceDTO);
                });
            }
            if (ObjectUtil.isNotEmpty(experiences)) {
                talentDTO.setExperiences(experiences);
            }
        }
        //skills
        if (CollUtil.isNotEmpty(talent.getSkills())) {
            talentDTO.setSkills(talent.getSkills().stream().map(s ->{
              return  Convert.convert(SkillDTO.class, s);
            }).collect(Collectors.toList()));
        }
        if (ObjectUtil.isNotEmpty(talent.getIndustries())) {
            talentDTO.setIndustries(EnumRelationDTO.transfer(enumCommonPoolService.getIndustryEnumIdsByNames(talent.getIndustries())));
        }

//        if (ObjectUtil.isNotEmpty(talent.getJobFunctions())) {
//            Map<String, Long> jobFunctionMap = TalentServiceImplV3.getCommonPoolMap();
//            talentDTO.setJobFunctions(EnumRelationDTO.transfer(talent.getJobFunctions().stream().map(c -> jobFunctionMap.get(c)).toList()));
//        }
        if (ObjectUtil.isNotEmpty(talent.getJobFunctions())) {
            talentDTO.setJobFunctions(EnumRelationDTO.transfer(enumCommonPoolService.getJobFunctionEnumIdsByNames(talent.getJobFunctions())));
        }
        if (ObjectUtil.isNotEmpty(talent.getLanguages())) {
            talentDTO.setLanguages(EnumRelationDTO.transfer(enumLanguageService.transferLanguagesByNamesToId(talent.getLanguages())));
        }
        if (StrUtil.isNotBlank(talent.getCurrency())) {
//            Optional.of(enumCurrencyRepository.findOneByName(talent.getCurrency())).ifPresent(enumCurrency -> talentDTO.setCurrency(enumCurrency.getId()));
//            talentDTO.setCurrency(enumCurrencyService.findEnumCurrencyByName(talent.getCurrency()).getId());
            //TODO 整理旧数据前 currency preferredCurrency做下转换
            String currency = talent.getCurrency();
            if(NumberUtil.isNumber(currency)) {
                EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(Integer.valueOf(currency));
                talentDTO.setCurrency(enumCurrency.getName());
            } else {
                talentDTO.setCurrency(currency);
            }
        }
        //TODO common pool等待数据源变为新结构
//        if (StrUtil.isNotBlank(talent.getPreferredCurrency())) {
//            Optional.of(enumCurrencyRepository.findOneByName(talent.getPreferredCurrency())).ifPresent(enumCurrency -> talentDTO.setPreferredCurrency(enumCurrency.getId()));
//            talentDTO.setPreferredCurrency(enumCurrencyService.findEnumCurrencyByName(talent.getPreferredCurrency()).getId());
//            //TODO 整理旧数据前 currency preferredCurrency做下转换
//            String preferredCurrency = talent.getPreferredCurrency();
//            if(NumberUtil.isNumber(preferredCurrency)) {
//                EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(Integer.valueOf(preferredCurrency));
//                talentDTO.setPreferredCurrency(enumCurrency.getName());
//            } else {
//                talentDTO.setPreferredCurrency(preferredCurrency);
//            }
//        }
        return talentDTO;
    }

    private void maskTalentContact(TalentDTOV3 talentDTO) {
        CreditTransaction creditTransaction = userService.findByProfileIdAndTenantIdAndStatus(talentDTO.getEsId(), SecurityUtils.getTenantId(), Status.Available).getBody();
        if (creditTransaction != null) {
            talentDTO.setPurchased(true);
            return; // already purchased and no need to mask contact
        }
        talentDTO.setPurchased(false);
        maskContacts(talentDTO);
    }

    private void maskContacts(TalentDTOV3 talentDTO) {
        List<TalentContactDTO> contacts = talentDTO.getContacts();
        if (ObjectUtil.isNotEmpty(contacts)) {
            for (TalentContactDTO contact : contacts) {
                if (Objects.equals(contact.getType(), ContactType.PRIMARY_EMAIL) || Objects.equals(contact.getType(), ContactType.EMAIL)) {
                    contact.setContact(CommonUtils.maskEmail(contact.getContact()));
                } else if (Objects.equals(contact.getType(), ContactType.PRIMARY_PHONE) || Objects.equals(contact.getType(), ContactType.PHONE)) {
                    contact.setContact(CommonUtils.maskPhone(contact.getContact()));
                } else if (Objects.equals(contact.getType(), ContactType.WECHAT)) {
                    contact.setContact(CommonUtils.maskPhone(contact.getContact()));
                } else if (Objects.equals(contact.getType(), ContactType.LINKEDIN)) {
                    contact.setContact(CommonUtils.maskLinkedin(contact.getContact()));
                    contact.setDetails(CommonUtils.maskLinkedin(contact.getDetails()));
                } else if (Objects.equals(contact.getType(), ContactType.FACEBOOK)) {
                    contact.setContact(CommonUtils.maskFacebook(contact.getContact()));
                }
            }
        }
    }

    private List<TalentDTOV3> parseTalentFromResponse(String response, List<String> existsContacts) {
        if (StringUtils.isEmpty(response)) {
            return null;
        }

        List<TalentDTOV3> talentDTOList = new ArrayList<>();
        JSONArray hitsJSONArray = JSONUtil.parseArray(response);

        for (int i = 0; i < hitsJSONArray.size(); i++) {
            JSONObject hitJSON = hitsJSONArray.getJSONObject(i);
            if (CommonUtils.existsKey(hitJSON, ElasticSearchConstants.SOURCE)) {
                JSONObject sourceJSON = hitJSON.getJSONObject(ElasticSearchConstants.SOURCE);
                TalentDTOV3 talentDTO = toTalentDTO(sourceJSON);
                if (hasExtraContacts(talentDTO, existsContacts)) {
                    maskTalentContact(talentDTO);
                    talentDTOList.add(talentDTO);
                }
            }
        }
        return talentDTOList;
    }

    private void recordSearchRequest(String queryStr) {
        if (StringUtils.isEmpty(queryStr)) {
            return;
        }
        if (queryStr.contains(ElasticSearchConstants.MUST_NOT)) {
            JSONObject jsonObject = JSONUtil.parseObj(queryStr);
            JSONObject query = jsonObject.getJSONObject(ElasticSearchConstants.QUERY);
            if (query == null) {
                return;
            }
            JSONObject bool = query.getJSONObject(ElasticSearchConstants.BOOL);
            if (bool == null) {
                return;
            }
            JSONArray musts = bool.getJSONArray(ElasticSearchConstants.MUST);
            JSONArray mustNots = bool.getJSONArray(ElasticSearchConstants.MUST_NOT);
            boolean isMustNull = (musts == null || musts.isEmpty());
            boolean isMustNotNull = (mustNots == null || mustNots.isEmpty());
            if (isMustNull && isMustNotNull) {
                return;
            }
        }
        ESStats esStats = new ESStats();
        esStats.setUserId(SecurityUtils.getUserId());
        esStats.setTenantId(SecurityUtils.getTenantId());
        esStats.setSearchStr(queryStr);
        esStatsService.save(esStats);
    }

    @Override
    public TalentESDocument getTalentESDocument(TalentESConditionDTO condition) throws IOException {
        //search one talent from common pool
        String commonTalent = esFillerTalentService.searchFromCommonPool(JSONUtil.toJsonStr(JSONUtil.parse(condition)));
        TalentESDocument document = JsonUtil.fromJson(commonTalent, TalentESDocument.class);
        if (ObjectUtil.isEmpty(document)) {
            return null;
        }
        //search contacts from es
        List<String> domains = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(document.getEmailStatus())) {
            if (ObjectUtil.isNotEmpty(document.getEmailStatus().getCurrentCompanyDomains())) {
                domains.addAll(document.getEmailStatus().getCurrentCompanyDomains());
            }
            if (ObjectUtil.isNotEmpty(document.getEmailStatus().getEduDomains())) {
                domains.addAll(document.getEmailStatus().getEduDomains());
            }
            if (ObjectUtil.isNotEmpty(document.getEmailStatus().getPrivateDomains())) {
                domains.addAll(document.getEmailStatus().getPrivateDomains());
            }
            if (ObjectUtil.isNotEmpty(document.getEmailStatus().getUnknownDomains())) {
                domains.addAll(document.getEmailStatus().getUnknownDomains());
            }
            //if currentCompanyDomains、eduDomains、privateDomains、unknownDomainsd is null ,use suspectedInvalidDomains
            if (ObjectUtil.isEmpty(domains) && ObjectUtil.isNotEmpty(document.getEmailStatus().getSuspectedInvalidDomains())) {
                domains.addAll(document.getEmailStatus().getSuspectedInvalidDomains());
            }
        }
        document.setEsId(condition.getEsId());
        if (ObjectUtil.isNotEmpty(domains)) {
            document.setDomains(domains);
        }
        return document;
    }

    @Override
    public String searchContactsFromCommonPool(String esId) throws IOException {
        TalentESConditionDTO condition = new TalentESConditionDTO();
        condition.setEsId(esId);
        getTalentESDocument(condition);
        String commonTalentContacts = esFillerTalentService.searchContactsFromCommonPool(condition);
        List<TalentContact> contacts = JSONUtil.toList(JSONUtil.parseArray(commonTalentContacts), TalentContact.class);
        String esContacts = null;
        if (ObjectUtil.isNotEmpty(contacts)) {
            //translate contacts type
            contacts.forEach(c -> {
                if (ContactTypeConstants.EXTRA_CONTACT_TYPES_PHONES.contains(c.getType())) {
                    c.setType(ContactType.PHONE);
                }
                if (ContactType.PRIMARY_EMAIL.equals(c.getType())) {
                    c.setType(ContactType.EMAIL);
                }
            });
            esContacts = ObjectUtil.toString(JSONUtil.parseArray(contacts));
        }
        return esContacts;
    }

    @Override
    public Map<String, Long> getTalentCategoryCount(TalentCategoryCountRequestDTO requestDto) throws IOException {
        String url = categoryCountCommonServiceUrl();
        String requestBodyString = JSONUtil.toJsonStr(JSONUtil.parse(requestDto));
        HttpResponse response = httpService.post(url, requestBodyString);
        if (response == null || response.getBody() == null) {
            throw new IOException("ES category count Response or response body is null.");
        }
        if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
            throw new IOException("ES category count Unexpected response status: " + response.getCode());
        }
        Type type = new TypeToken<Map<String, Long>>() {}.getType();
        return new Gson().fromJson(response.getBody(), type);
    }

    @Override
    public HttpResponse searchTalentFromCommonService(TalentSearchGroup searchGroup, Pageable pageable, List<EnumUserResponsibility> enumUserResponsibilityList) throws IOException {
        if(searchGroup.getSearch() == null) {
            searchGroup.setSearch(new ArrayList<>());
        }
        String condition = JSONUtil.toJsonStr(JSONUtil.parse(searchGroup));

        if (ObjectUtil.isNull(condition)) {
            return null;
        }
        if (!hasSourceAgencySearchPermission(SecurityUtils.getUserId(), condition)){ //用户没有agency的search权限
            if (hasOtherSourceSearchParam(condition)) { //如果用户还搜索了其他跟agency并列的人才来源('source')
                condition = removeSourceAgencySearchParam(condition); //移除搜索agency的条件即可，按照其他的人才来源条件继续搜索
            } else { //如果只要求搜索agency的人才来源，同时又没有搜索其他的人才来源
                return null; //返回空
            }
        }
        String url = commonServiceUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = commonServiceUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                Iterator<Sort.Order> sorts = pageable.getSort().iterator();
                if (sorts.hasNext()) {
                    Sort.Order sort = sorts.next();
                    String sortName = getEnumUserResponsibilityName(sort.getProperty(), enumUserResponsibilityList);
                    url += "&sort=" + sortName + StrUtil.COMMA + sort.getDirection();
                }
            }
        }
        Instant start = Instant.now();
        log.info("[request data-service for search talent. url: {}, condition: {}", url, condition);
        HttpResponse response = httpService.post(url, condition);
        Instant end = Instant.now();
        log.info("[apn module={}, index={}, timeZone={}] esCommonService.searchTalentFromCommonService time = {}ms", searchGroup.getModule(), searchGroup.getIndex(), searchGroup.getTimeZone(), Duration.between(start, end).toMillis());
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsFillerTalentService @{}] search talent from common service success, searchRequest: {}, pageable:{}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition, pageable);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData = 0;
                if(ModuleType.CANDIDATE.getName().equals(searchGroup.getModule())){
                    //search talent by tenantId
                    countData = talentRepository.countByTenantId(SecurityUtils.getTenantId());
                }
                if(countData == 0){
                    return new HttpResponse();
                }else{
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: EsFillerTalentService @{}] search talent from common service error, searchRequest: {}, pageable:{}, response code: {}, response message: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition, pageable, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsFillerTalentService @{}] search talent from common service error and response is null, searchRequest: {}, pageable:{}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    private boolean hasSourceAgencySearchPermission(Long userId, String condition) {
        Pattern pattern = Pattern.compile("\"key\"\\s*:\\s*\"sourceAgency\"");
        Matcher matcher = pattern.matcher(condition);
        if (matcher.find()) {
            return cachePermission.hasUserPrivilegePermission(userId, applicationProperties.getTalentSearchAgencyPermissionUri());
        }
        return true;
    }

    private boolean hasOtherSourceSearchParam(String condition) {
        Pattern pattern = Pattern.compile("\"key\"\\s*:\\s*\"source\"");
        Matcher matcher = pattern.matcher(condition);
        return matcher.find();
    }

    private String removeSourceAgencySearchParam(String condition) {
        Pattern pattern = Pattern.compile("\"key\"\\s*:\\s*\"sourceAgency\"");
        return pattern.matcher(condition).replaceAll("\"key\":\"sourceAgencyINVALID\"");
    }

    @Override
    public HttpResponse searchTalentFromCommonService(SearchGroup searchGroup) throws IOException {
        if(searchGroup.getSearch() == null) {
            searchGroup.setSearch(new ArrayList<>());
        }
        String condition = JSONUtil.toJsonStr(JSONUtil.parse(searchGroup));
        if (ObjectUtil.isNull(condition)) {
            return null;
        }
        String url = commonServiceUrl();
        Instant start = Instant.now();
        log.info("[request data-service for search talent. condition: {}", condition);
        HttpResponse response = httpService.post(url, condition);
        Instant end = Instant.now();
        log.info("[apn module={}, index={}, timeZone={}] esCommonService.searchTalentFromCommonService time = {}ms", searchGroup.getModule(), searchGroup.getIndex(), searchGroup.getTimeZone(), Duration.between(start, end).toMillis());
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsFillerTalentService @{}] search talent from common service success, searchRequest: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData = 0;
                if(ModuleType.CANDIDATE.getName().equals(searchGroup.getModule())){
                    //search talent by tenantId
                    countData = talentRepository.countByTenantId(SecurityUtils.getTenantId());
                }
                if(countData == 0){
                    return new HttpResponse();
                }else{
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: EsFillerTalentService @{}] search talent from common service error, searchRequest: {}, response code: {}, response message: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsFillerTalentService @{}] search talent from common service error and response is null, searchRequest: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    @Override
    public HttpResponse searchTalentFromCommonService(SearchGroup dataSearchGroup, SearchGroup countSearchGroup, Pageable pageable, List<EnumUserResponsibility> enumUserResponsibilityList) throws IOException {
        String condition = convertSearchGroupToJsonString(dataSearchGroup, countSearchGroup);

        if (ObjectUtil.isNull(condition)) {
            return null;
        }

        String url = commonSearchCountServiceUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = commonSearchCountServiceUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                Iterator<Sort.Order> sorts = pageable.getSort().iterator();
                if (sorts.hasNext()) {
                    Sort.Order sort = sorts.next();
                    String sortName = getEnumUserResponsibilityName(sort.getProperty(), enumUserResponsibilityList);
                    url += "&sort=" + sortName + StrUtil.COMMA + sort.getDirection();
                }
            }
        }
        Instant start = Instant.now();
        log.info("[request data-service for search talent. condition: {}", condition);
        HttpResponse response = httpService.post(url, condition);
        Instant end = Instant.now();
        log.info("[apn module={}, index={}, timeZone={}] esCommonService.searchTalentFromCommonService time = {}ms", dataSearchGroup.getModule(), dataSearchGroup.getIndex(), dataSearchGroup.getTimeZone(), Duration.between(start, end).toMillis());
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsFillerTalentService @{}] search talent from common service success, searchRequest: {}, pageable:{}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition, pageable);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData = 0;
                if(ModuleType.CANDIDATE.getName().equals(dataSearchGroup.getModule())){
                    //search talent by tenantId
                    countData = talentRepository.countByTenantId(SecurityUtils.getTenantId());
                }
                if(countData == 0){
                    return new HttpResponse();
                }else{
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: EsFillerTalentService @{}] search talent from common service error, searchRequest: {}, pageable:{}, response code: {}, response message: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition, pageable, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsFillerTalentService @{}] search talent from common service error and response is null, searchRequest: {}, pageable:{}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    private String getEnumUserResponsibilityName(String property, List<EnumUserResponsibility> enumUserResponsibilityList) {
        Optional<EnumUserResponsibility> enumUserResponsibility = enumUserResponsibilityList.stream().filter(p -> p.getLabel().equals(property)).findFirst();
        if(!enumUserResponsibility.isPresent()) {
            return property;
        }
        switch (property) {
            case ResponsibilityConstants.CREATED_BY:
            case ResponsibilityConstants.OWNER:
                return enumUserResponsibility.get().getTalentEsKey();
            case ResponsibilityConstants.SHARED_BY:
                return enumUserResponsibility.get().getTalentEsKey();
            case ResponsibilityConstants.AM:
            case ResponsibilityConstants.DM:
            case ResponsibilityConstants.RECRUITER:
            case ResponsibilityConstants.PARTICIPANT:
            case ResponsibilityConstants.SALES_LEAD_OWNER:
            case ResponsibilityConstants.BD_OWNER:
                return enumUserResponsibility.get().getApplicationEsKey();

            default:
                return property;
        }
    }

    @Override
    public HttpResponse searchTalentSourceFromCommonService(SearchEsBySource searchEsBySource, Pageable pageable) throws IOException {
        String condition = JSONUtil.toJsonStr(searchEsBySource);
        if (ObjectUtil.isNull(condition)) {
            return null;
        }
        String url = searchEsSourceByIdUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = searchEsSourceByIdUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
        }
        Instant start = Instant.now();
        log.info("[request data-service for search talent partial source. condition: {}", condition);
        HttpResponse response = httpService.post(url, condition);
        Instant end = Instant.now();
        log.info("[apn module={}, index={}] esCommonService.searchTalentSourceFromCommonService time = {}ms", searchEsBySource.getModule(), searchEsBySource.getIndex(), Duration.between(start, end).toMillis());
        if (response != null) {
            return response;
        } else {
            log.error("[APN: EsFillerTalentService @{}] search talent from common service error and response is null, searchRequest: {}, pageable:{}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
    }

    @Override
    public String likeSearchSchool(String schoolName) throws IOException {
        String url = likeSearchCollegeUrl() + "?search=" + schoolName;
        HttpResponse response = httpService.get(url);
        if (response.getCode() == StatusCode.OK.getCode()) {
            return response.getBody();
        } else {
            log.error("[APN: EsFillerTalentService @{}] search school name error, response: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), JSONUtil.toJsonStr(response));
            throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
        }
    }

    private String convertSearchGroupToJsonString(SearchGroup resultSearchGroup, SearchGroup countSearchGroup) {
        if(resultSearchGroup.getSearch() == null) {
            resultSearchGroup.setSearch(new ArrayList<>());
        }
        if(countSearchGroup.getSearch() == null) {
            countSearchGroup.setSearch(new ArrayList<>());
        }
        JSONObject result = JSONUtil.parseObj(resultSearchGroup);
        JSONObject count = JSONUtil.parseObj(countSearchGroup);

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(result);
        jsonArray.add(count);
        return JSONUtil.toJsonStr(jsonArray);
    }
}
