package com.altomni.apn.talent.service.vo.record;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.enumeration.record.TrackingPlatform;
import com.altomni.apn.talent.domain.record.TalentTrackingNote;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentTrackingNoteVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long tenantId;

    private String userFullName;

    private Long userId;

    private TrackingPlatform trackingPlatform;

    private String platformId;

    private String note;

    private Long syncedTalentId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserFullName() {
        return userFullName;
    }

    public void setUserFullName(String userFullName) {
        this.userFullName = userFullName;
    }

    public TrackingPlatform getTrackingPlatform() {
        return trackingPlatform;
    }

    public void setTrackingPlatform(TrackingPlatform trackingPlatform) {
        this.trackingPlatform = trackingPlatform;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Long getSyncedTalentId() {
        return syncedTalentId;
    }

    public void setSyncedTalentId(Long syncedTalentId) {
        this.syncedTalentId = syncedTalentId;
    }

    public static TalentTrackingNoteVO fromTalentTrackingNote(TalentTrackingNote talentTrackingNote) {
        TalentTrackingNoteVO talentTrackingNoteVO = new TalentTrackingNoteVO();
        ServiceUtils.myCopyProperties(talentTrackingNote, talentTrackingNoteVO);
        return talentTrackingNoteVO;
    }
}
