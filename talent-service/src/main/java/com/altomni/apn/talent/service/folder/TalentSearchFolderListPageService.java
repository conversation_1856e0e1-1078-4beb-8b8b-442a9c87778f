package com.altomni.apn.talent.service.folder;

import com.altomni.apn.common.dto.folder.FolderSearchRequestDTO;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderStatusDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TalentSearchFolderListPageService {


    Page<TalentSearchFolderStatusDTO> findSearchFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable);
}
