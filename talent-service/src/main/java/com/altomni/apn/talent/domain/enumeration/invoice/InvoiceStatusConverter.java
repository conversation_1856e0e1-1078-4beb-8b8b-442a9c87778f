package com.altomni.apn.talent.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoiceStatusConverter extends AbstractAttributeConverter<InvoiceStatus, Integer> {
    public InvoiceStatusConverter() {
        super(InvoiceStatus::toDbValue, InvoiceStatus::fromDbValue);
    }
}
