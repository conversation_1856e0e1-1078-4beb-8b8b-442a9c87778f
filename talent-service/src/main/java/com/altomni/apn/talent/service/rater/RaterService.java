package com.altomni.apn.talent.service.rater;

import com.altomni.apn.talent.service.dto.redis.RecommendedTenantJobResponse;
import org.springframework.data.domain.Pageable;

import java.io.IOException;

public interface RaterService {

    void refreshTalentRater(Long tenantId, Long talentId);

    RecommendedTenantJobResponse recommendJobsForTenantTalent(Long talentId, Pageable pageable, boolean refresh) throws IOException;

    RecommendedTenantJobResponse recommendJobsForCommonTalent(String esId, Pageable pageable, boolean refresh) throws IOException;

    void invokeRecommendJobsForTenantTalent(Long talentId,Long tenantId);
}
