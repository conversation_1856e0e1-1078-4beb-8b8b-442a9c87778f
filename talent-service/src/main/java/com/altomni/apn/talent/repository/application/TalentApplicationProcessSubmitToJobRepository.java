package com.altomni.apn.talent.repository.application;

import com.altomni.apn.common.dto.talent.TalentResumeBindSubmitToJob;
import com.altomni.apn.talent.domain.application.TalentApplicationProcessSubmitToJob;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the TalentRecruitmentProcessSubmitToJob entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentApplicationProcessSubmitToJobRepository extends JpaRepository<TalentApplicationProcessSubmitToJob, Long> {

    List<TalentApplicationProcessSubmitToJob> findAllByTalentResumeRelationIdIs(Long talentResumeRelationId);
    List<TalentApplicationProcessSubmitToJob> findAllByTalentResumeRelationIdIn(List<Long> talentResumeRelationId);

    @Query(value = "select stj from TalentApplicationProcessSubmitToJob stj left join TalentApplicationProcess p on p.id = stj.talentRecruitmentProcessId where p.talentId = ?1")
    List<TalentApplicationProcessSubmitToJob> findAllByTalent(Long talentId);

    @Query(value = "select new com.altomni.apn.common.dto.talent.TalentResumeBindSubmitToJob(stj.talentResumeRelationId, stj.talentRecruitmentProcessId, stj.id, p.jobId, j.title, stj.id, stj.lastModifiedDate) from TalentApplicationProcessSubmitToJob stj left join TalentApplicationProcess p on p.id = stj.talentRecruitmentProcessId left join JobV3 j on p.jobId = j.id where p.talentId = ?1")
    List<TalentResumeBindSubmitToJob> findBindSubmitToJobByTalent(Long talentId);
    @Query(value = "select new com.altomni.apn.common.dto.talent.TalentResumeBindSubmitToJob(stj.talentResumeRelationId, stj.talentRecruitmentProcessId, stj.id, p.jobId, j.title, stj.id, stj.lastModifiedDate) from TalentApplicationProcessSubmitToJob stj left join TalentApplicationProcess p on p.id = stj.talentRecruitmentProcessId left join JobV3 j on p.jobId = j.id where stj.talentResumeRelationId = ?1")
    List<TalentResumeBindSubmitToJob> findBindSubmitToJobByTalentResumeRelationId(Long talentResumeRelationId);

}

