package com.altomni.apn.talent.service.rater.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.EsFillerConstants;
import com.altomni.apn.common.config.constants.RaterConstants;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.rater.RaterRequestApplication;
import com.altomni.apn.common.dto.rater.RaterRequestBody;
import com.altomni.apn.common.dto.redis.RaterResponseDTO;
import com.altomni.apn.common.dto.redis.RedisResponse;
import com.altomni.apn.common.dto.search.SearchRaterGroup;
import com.altomni.apn.common.enumeration.enums.TalentAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderRepository;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.TalentApiPromptProperties;
import com.altomni.apn.talent.constants.RedisConstants;
import com.altomni.apn.talent.domain.enumeration.start.StartStatus;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.application.ApplicationService;
import com.altomni.apn.talent.service.common.HttpService;
import com.altomni.apn.talent.service.dto.redis.RecommendedTenantJobResponse;
import com.altomni.apn.talent.service.dto.redis.RedisRecommendations;
import com.altomni.apn.talent.service.dto.start.StartDTO;
import com.altomni.apn.talent.service.finance.FinanceService;
import com.altomni.apn.talent.service.rater.RaterService;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.repository.user.CreditTransactionRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Tuple;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class RaterServiceImpl implements RaterService {

    private final Logger log = LoggerFactory.getLogger(RaterServiceImpl.class);

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private HttpService httpService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private FinanceService financeService;

    @Resource
    private EntityManager entityManager;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    TalentApiPromptProperties talentApiPromptProperties;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    @Resource
    private CommonRedisService commonRedisService;

    private String commonSearchByIdsServiceUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/hitalent/ids";
    }

    public static final List<String> JOB_SEARCH_BYIDS_SOURCE = Arrays.asList("title", "companyName", "code", "requiredSkills.skillName", "preferredSkills.skillName", "postingTime", "score", "requiredLanguages", "preferredLanguages", "experienceYearRange", "minimumDegreeLevel", "locations", "createdDate", "preferredDegreeLevels", "preferredDegreeLevels", "affiliations", "responsibility5.id", "responsibility10.id");

    @Override
    public void refreshTalentRater(Long tenantId, Long talentId) {
        verifyTalent(talentId);
        StartDTO start = financeService.findStartByTalentIdAndStatus(talentId, StartStatus.ACTIVE).getBody();
        if (ObjectUtil.isNotEmpty(start)) {
            if (ObjectUtil.isNotEmpty(start.getEndDate()) && DateUtil.afterNow(start.getEndDate())) {
                return;
            }
        }
        RaterRequestBody requestBody = new RaterRequestBody()
                .applications(getApplicationsByTalentId(talentId));
        requestBody.setTalentId(StrUtil.toString(talentId));
        setLastModifiedDate(talentId, requestBody);
        String url = applicationProperties.getRaterUrl() + RaterConstants.TENANT + SecurityUtils.getTenantId() + RaterConstants.FIND_JOBS_FOR_TALENT + talentId;
        try {
            HttpResponse response = httpService.post(url, JsonUtil.toJson(requestBody));
            log.info("refresh talent rater is success, code = {}", response.getCode());
        } catch (Exception e) {
            log.error("refresh talent rater is error, message = {}", ExceptionUtils.getStackTrace(e));
        }
    }

    @Override
    public RecommendedTenantJobResponse recommendJobsForTenantTalent(Long talentId, Pageable pageable, boolean refresh) throws IOException {
        verifyTalent(talentId);
        StartDTO start = financeService.findStartByTalentIdAndStatus(talentId, StartStatus.ACTIVE).getBody();
        if (ObjectUtil.isNotEmpty(start)) {
            if (ObjectUtil.isNotEmpty(start.getEndDate()) && DateUtil.afterNow(start.getEndDate())) {
                return new RecommendedTenantJobResponse().jobs(null).status("FINISHED");
            }
        }
        RaterRequestBody requestBody = getRaterRequestBody(talentId);
        String url = applicationProperties.getRaterUrl() + RaterConstants.TENANT + SecurityUtils.getTenantId() + RaterConstants.FIND_JOBS_FOR_TALENT + talentId;
        return getFromRedisAndcallRater(talentId + "", pageable, requestBody, url, SecurityUtils.getTenantId(), refresh, false);
    }

    private RaterRequestBody getRaterRequestBody(Long talentId) {
        RaterRequestBody requestBody = new RaterRequestBody().applications(getApplicationsByTalentId(talentId));
        //set lastModifiedDate
        setLastModifiedDate(talentId, requestBody);
        requestBody.setTalentId(StrUtil.toString(talentId));
        return requestBody;
    }

    private void setLastModifiedDate(Long talentId, RaterRequestBody requestBody) {
        entityManager.clear();
        TalentV3 talent = talentRepository.getById(talentId);
        if (ObjectUtil.isNotEmpty(talent)) {
            Instant talentLastEditTime = talentRepository.getTalentEsLastModifiedTime(talentId);
            requestBody.setTalentLastModifiedDate(DateUtil.fromInstantToUtcDateTimeWithMillisecond(talentLastEditTime));
        }
    }

    private void setJobLastModifiedDate(Long jobId, RaterRequestBody requestBody) {
        entityManager.clear();
        JobV3 job = jobRepository.getById(jobId);
        if (ObjectUtil.isNotEmpty(job)) {
            requestBody.setJobLastModifiedDate(DateUtil.fromInstantToUtcDateTimeWithMillisecond(job.getLastModifiedDate()));
        }
    }

    private void setRestrictions(List<JSONObject> filter) {
//        Integer dataScope = talentRepository.getDataScope(SecurityUtils.getUserId());
//        Integer dataScopeRole = talentRepository.getRoleDateScope(SecurityUtils.getUserId());
//        if (ObjectUtil.isNotEmpty(dataScopeRole)) {
//            dataScope = Math.max(dataScope, dataScopeRole);
//        }
//        List<Long> teamIdList = new ArrayList<>();
//        Long userId = null;
//        if (ObjectUtil.equal(DataScope.PERMISSION_SELF.toDbValue(), dataScope)) {
//            userId = SecurityUtils.getUserId();
//        } else if (ObjectUtil.equal(DataScope.PERMISSION_TEAM.toDbValue(), dataScope) || ObjectUtil.equal(DataScope.PERMISSION_EXTRA_TEAM.toDbValue(), dataScope)) {
//            teamIdList = talentRepository.getTeamIdsByUserId(SecurityUtils.getUserId());
//        } else if (ObjectUtil.equal(DataScope.PERMISSION_ALL.toDbValue(), dataScope)) {
//            return;
//        }
        Long userId = null;
        List<Long> teamIdList = new ArrayList<>();
        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
        if (Objects.isNull(teamDataPermission)) {
            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        }

        if (teamDataPermission.getSelf()) {
            userId = SecurityUtils.getUserId();
        } else if (CollUtil.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            teamIdList.addAll(new ArrayList<>(teamDataPermission.getReadableTeamIds()));
//            if (teamDataPermission.isPrivateJobPermission()){
//                teamIdList.add(teamDataPermission.getTeamIdForPrivateJob());
//            }
        }else if (teamDataPermission.getAll()){
            return;
        }

        JSONObject affiliations = new JSONObject();
        JSONObject userResponsibility = new JSONObject();
        List<String> pteamList = new ArrayList<>();
        if (CollUtil.isNotEmpty(teamIdList)) {
            teamIdList.forEach(t -> pteamList.add(EsFillerConstants.ES_PTEAM + t));
            affiliations.put("key", "affiliations");
            JSONObject dataJson = new JSONObject();
            dataJson.put("data", pteamList);
            affiliations.put("value", dataJson);
            filter.add(affiliations);
        }
        if (userId != null) {
            userResponsibility.put("key", EsFillerConstants.ES_CREATED_BY + ".id");
            JSONObject dataJson = new JSONObject();
            dataJson.put("data", Collections.singletonList(userId));
            dataJson.put("relation", "OR");
            userResponsibility.put("value", dataJson);
            filter.add(userResponsibility);
        }
    }

    @Resource
    private CreditTransactionRepository creditTransactionRepository;

    @Override
    public RecommendedTenantJobResponse recommendJobsForCommonTalent(String esId, Pageable pageable, boolean refresh) throws IOException {
        RaterRequestBody requestBody = new RaterRequestBody();
        CreditTransaction creditTransaction = creditTransactionRepository.findByProfileIdAndTenantIdAndStatus(esId, SecurityUtils.getTenantId(), Status.Available);
        if(creditTransaction != null && creditTransaction.getTalentId() != null) {
            requestBody.applications(getApplicationsByTalentId(creditTransaction.getTalentId()));
        }
        //add data permission restrictions
        requestBody.setTalentId(esId);
        String url = applicationProperties.getCommonUrl() + RaterConstants.TENANT + SecurityUtils.getTenantId() + RaterConstants.FIND_JOBS_FOR_COMMON_TALENT;
        return getFromRedisAndcallRater(esId, pageable, requestBody, url, SecurityUtils.getTenantId(), refresh, true);

//        return getCommonTalentFromRedisAndcallRater(esId, pageable, requestBody, url, SecurityUtils.getTenantId());
    }

//    private RecommendedJobResponse getCommonTalentFromRedisAndcallRater(String talentId, Pageable pageable, RaterRequestBody requestBody, String url, Long tenantId) {
//        RecommendedJobResponse jobResponse = new RecommendedJobResponse();
//        RedisResponse res1st = redisService.getDataForJobByCommonTalent(talentId, tenantId, null);
//        if (res1st != null && StringUtils.isNotEmpty(res1st.getStatus())) {
//            jobResponse.setJobs(this.parseJobFromResponse(res1st, pageable));
//            jobResponse.setStatus(res1st.getStatus());
//            jobResponse.setTotal(res1st.getTotal());
//            return jobResponse;
//        }
//        try {
//            HttpResponse response = httpService.post(url, JsonUtil.toJson(requestBody));
////            log.info("[APN: RaterService @{}] Get jobs and call raterService , id: {}, response code: {}, param: {}", SecurityUtils.getUserId(), response, response.getCode(), JsonUtil.toJson(requestBody));
//            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
////                log.info("[APN: RaterService @{}] Get jobs and call raterService success, id: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response.getCode(), response.getBody());
//                jobResponse.setStatus(JsonUtil.fromJson(response.getBody(), RaterResponseDTO.class).getStatus());
//            } else {
//                log.info("[APN: RaterService @{}] Get jobs and call raterService error, id: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
//                if (response != null) {
//                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
//                } else {
//                    throw new ExternalServiceInterfaceException();
//                }
//            }
//        } catch (IOException e) {
//            log.error("[APN: RaterService @{}] Get jobs and call raterService IOException, id: {}", SecurityUtils.getUserId(), e);
//        }
//        return jobResponse;
//    }

    public boolean hasNextPage(Pageable pageable, Integer total) {
        if (total == null || pageable == null) {
            return false;
        }

        int pageSize = pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        int totalPages = (total + pageSize - 1) / pageSize;

        return pageNumber < totalPages - 1;
    }

    private boolean recommendOngoing(String status) {
        return "STARTED".equals(status) || "ON_GOING".equals(status);
    }

    private RecommendedTenantJobResponse getRecommendedJobResponseByRedis(String talentId, Pageable pageable, RaterRequestBody requestBody, boolean refresh, Long tenantId, Boolean commonSearch) {
        String key = commonSearch ? RedisConstants.DATA_KEY_RATER + "tenant_" + tenantId + ":jobs_for_common_talent_" + talentId : RedisConstants.DATA_KEY_RATER + "tenant_" + tenantId + ":jobs_for_talent_" + talentId + "_" + requestBody.getTalentLastModifiedDate().replace(":","-");
        RedisResponse res1st = commonSearch ?  commonRedisService.getDataForJobByCommonTalent(key, null) : commonRedisService.getDataForJob(key, null);
        log.info("Redis_response: {}", res1st);
        if (res1st != null && StringUtils.isNotEmpty(res1st.getStatus())) {
            if (refresh && !recommendOngoing(res1st.getStatus())) {
                commonRedisService.deleteRaterRedisKeyWildcard(key);
            } else {
                RecommendedTenantJobResponse recommendedJobResponse = new RecommendedTenantJobResponse();
                recommendedJobResponse.setStatus(res1st.getStatus());
                recommendedJobResponse.setJobs(this.parseTenantTalentRecommendJobFromResponse(talentId, res1st, pageable, recommendedJobResponse, commonSearch));
                return recommendedJobResponse;
            }
        }
        return null;
    }


    private RecommendedTenantJobResponse getFromRedisAndcallRater(String talentId, Pageable pageable, RaterRequestBody requestBody, String url, Long tenantId, boolean refresh, Boolean commonSearch) {
        RecommendedTenantJobResponse jobResponse = getRecommendedJobResponseByRedis(talentId, pageable, requestBody, refresh, tenantId, commonSearch);
        if (jobResponse == null) {
            jobResponse = new RecommendedTenantJobResponse();
            jobResponse.setStatus(callRater(url, requestBody));
        }

        return jobResponse;
    }

    private String callRater(String url, RaterRequestBody requestBody) {
        String status = null;
        try {
            HttpResponse response = httpService.post(url, JsonUtil.toJson(requestBody));
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: RaterService @{}] Get jobs and call raterService success, id: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response.getCode(), response.getBody());
                status = JsonUtil.fromJson(response.getBody(), RaterResponseDTO.class).getStatus();
            } else {
                log.info("[APN: RaterService @{}] Get jobs and call raterService error, id: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                if (response != null) {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                } else {
                    throw new ExternalServiceInterfaceException();
                }
            }
        } catch (IOException e) {
            log.error("[APN: RaterService @{}] Get jobs and call raterService IOException, id: {}", SecurityUtils.getUserId(), e);
        }
        return status;
    }

    private void verifyTalent(Long talentId) {
        TalentV3 talent = talentRepository.findById(talentId).orElseThrow(() -> new ForbiddenException("Talent not exists"));
        if (!SecurityUtils.isCurrentTenant(talent.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(TalentAPIMultilingualEnum.LINKED_VERIFYTENANT_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),talentApiPromptProperties.getTalentService()));
        }
    }

    @Resource
    private TalentRelateJobFolderRepository talentRelateJobFolderRepository;

    private String parseTenantTalentRecommendJobFromResponse(String talentId, RedisResponse res1st, Pageable pageable, RecommendedTenantJobResponse recommendedJobResponse, Boolean commonSearch) {
        Set<Tuple> result = res1st.getData();
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        LinkedList<Map<String, Object>> mapLinkedList = new LinkedList<>();
        Set<Long> unauthorizedPrivateJobIds = jobRepository.findAllUnauthorizedPrivateJobIds(SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        log.debug("unauthorizedPrivateJobIds= {}", unauthorizedPrivateJobIds);
        for (Tuple t : result) {
            RedisRecommendations redisRecommendations = JsonUtil.fromJson(t.getElement(), RedisRecommendations.class);
            if (redisRecommendations != null) {
                Map<String, Object> map = new HashMap<>();
                String jobId = redisRecommendations.get_id();
                if (StringUtils.isEmpty(jobId) || unauthorizedPrivateJobIds.contains(Long.parseLong(jobId))){
                    continue;
                }
                map.put("id", jobId);
                map.put("score", t.getScore());
                mapLinkedList.addLast(map);
            }
        }
        //根据affiliations过滤mapLinkedList， 因为job列表就有查看权限的限制
        JSONArray originRet = filterByAffiliation(talentId, mapLinkedList, commonSearch);
        JSONArray ret = getPageFromJSONArray(originRet, pageable);
        recommendedJobResponse.setHasMore(hasNextPage(pageable, originRet.size()));
        recommendedJobResponse.setTotal(originRet.size());
        this.fillPrivateJob(ret);
        // search dataService job data by jobIds
        ret.sort((a, b) -> {
            JSONObject objA = (JSONObject) a;
            JSONObject jsonObjectA = objA.getJSONObject("_source");
            if(jsonObjectA == null) {
                jsonObjectA = new JSONObject();
            }
            JSONObject objB = (JSONObject) b;
            JSONObject jsonObjectB = objB.getJSONObject("_source");
            if(jsonObjectB == null) {
                jsonObjectB = new JSONObject();
            }

            // 获取score值，如果不存在则默认为0.0
            double scoreA = jsonObjectA.getDouble("score", 0.0);
            double scoreB = jsonObjectB.getDouble("score", 0.0);

            // 从高到低排序，所以用scoreB减scoreA
            return Double.compare(scoreB, scoreA);
        });
        return ret.toString();
    }

    public JSONArray getPageFromJSONArray(JSONArray array, Pageable pageable) {
        if (array == null || array.isEmpty()) {
            return new JSONArray();
        }

        int pageSize = pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        int totalSize = array.size();

        // 计算起始和结束索引
        int startIndex = pageNumber * pageSize;
        int endIndex = Math.min(startIndex + pageSize, totalSize);

        // 检查起始索引是否超出范围
        if (startIndex >= totalSize) {
            return new JSONArray();
        }

        JSONArray result = new JSONArray();
        for (int i = startIndex; i < endIndex; i++) {
            result.add(array.get(i));
        }

        return result;
    }

    private JSONArray filterByAffiliation(String talentId, LinkedList<Map<String, Object>> mapLinkedList, Boolean commonSearch) {
        Long lTalentId = commonSearch ? null : Long.valueOf(talentId);
        List<Long> jobIdList = commonSearch ? null : talentRelateJobFolderRepository.findJobIdByTalentId(lTalentId, SecurityUtils.getUserId());
        List<Long> applicationJobIdList = commonSearch ? null : applicationService.getTalentRecruitmentProcessBriefByTalentId(lTalentId).getBody().stream().filter(p -> {
            NodeType lastNodeType = p.getLastNodeType();
            NodeStatus lastNodeStatus = p.getLastNodeStatus();
            return !NodeType.ELIMINATED.equals(lastNodeType) && !NodeStatus.ELIMINATED.equals(lastNodeStatus);
        }).map(TalentRecruitmentProcessVO::getJobId).toList();

        final int PAGE_SIZE = 30;
        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
        if (Objects.isNull(teamDataPermission)) {
            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        }

        JSONArray ret = new JSONArray();
        // 计算总页数
        int totalPages = (mapLinkedList.size() + PAGE_SIZE - 1) / PAGE_SIZE;

        for (int pageNum = 0; pageNum < totalPages; pageNum++) {
            SearchRaterGroup searchGroup = new SearchRaterGroup();
            searchGroup.setIndex("jobs_" + SecurityUtils.getTenantId());
            searchGroup.setModule(ModuleType.ALL_JOB.getName());
            searchGroup.setIds(mapLinkedList);
            searchGroup.setSource(JOB_SEARCH_BYIDS_SOURCE);

            List<JSONObject> filter = new ArrayList<>();
            searchGroup.setFilter(filter);

            String url = commonSearchByIdsServiceUrl();
            try {
                Pageable pageable = PageRequest.of(pageNum, PAGE_SIZE);
                String postUrl = url + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();

                HttpResponse response = httpService.post(postUrl, JsonUtil.toJson(searchGroup));
                log.info("find job for talent url = {}, param = {}", url, JsonUtil.toJson(searchGroup));

                if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                    String json = response.getBody();
                    JSONArray jsonArray = JSONUtil.parseArray(json);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject searchData = jsonArray.getJSONObject(i);
                        if(!filterByAffiliations(searchData, teamDataPermission)) {
                            continue;
                        }
                        if(!commonSearch) {
                            Long jobId = searchData.getLong("_id");
                            searchData.put("_relateJobFolder", !jobIdList.contains(jobId));
                            searchData.put("_appendToPosition", !applicationJobIdList.contains(jobId));
                        }
                        ret.add(searchData);
                    }
                } else {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } catch (Exception e) {
                log.error("find job for talent is error, page = {}, message = {}", pageNum, ExceptionUtils.getStackTrace(e));
            }
        }
        return ret;
    }

    private boolean filterByAffiliations(JSONObject searchData, TeamDataPermissionRespDTO teamDataPermission) {
        if(teamDataPermission == null) {
            return true;
        }

        JSONObject source = searchData.getJSONObject("_source");
        JSONArray affiliationsArr = source.getJSONArray("affiliations");
        JSONArray responsibility5 = source.getJSONArray("responsibility5");
        JSONArray responsibility10 = source.getJSONArray("responsibility10");

        if(teamDataPermission.getAll()) {
            return true;
        } else if (CollUtil.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            if(createContainUser(SecurityUtils.getUserId(), responsibility10)) {
                return true;
            }
            Set<String> sReadableTeamIds = teamDataPermission.getReadableTeamIds().stream().map(f -> {
                return "pteam_" + f.toString();
            }).collect(Collectors.toSet());
            if(!affiliationTeamContainUser(sReadableTeamIds, affiliationsArr)) {
                return false;
            }
        } else if(teamDataPermission.getSelf()){
            if(createContainUser(SecurityUtils.getUserId(), responsibility10)) {
                return true;
            }
            if(affiliationsArr == null) {
                if(!createContainUser(SecurityUtils.getUserId(), responsibility5)) {
                    return false;
                }
            } else {
                String selfTeam = "pteam_" + SecurityUtils.getTeamId();
                Set<String> affiliationTeam = affiliationsArr.stream().map(String::valueOf).collect(Collectors.toSet());
                if(!affiliationTeam.contains(selfTeam)) {
                    return false;
                }
                if(!createContainUser(SecurityUtils.getUserId(), responsibility5)) {
                    return false;
                }
            }
        }

        return true;
    }

    private boolean affiliationTeamContainUser(Set<String> readableTeamIds, JSONArray affiliationsArr) {
        if(affiliationsArr == null) {
            return true;
        }
        Set<String> affiliationTeam = affiliationsArr.stream().map(String::valueOf).collect(Collectors.toSet());
        return !CollUtil.intersection(readableTeamIds, affiliationTeam).isEmpty();
    }

    private boolean createContainUser(Long userId, JSONArray responsibilityArray) {
        if(responsibilityArray == null) {
            return false;
        }
        for(int i = 0; i < responsibilityArray.size(); i++) {
            JSONObject responsibility = responsibilityArray.getJSONObject(i);
            Long id = responsibility.getLong("id");
            if(userId.equals(id)) {
                return true;
            }
        }
        return false;
    }

    private void fillPrivateJob(JSONArray jobList){
        List<Long> jobIds = jobList.stream().map(o -> {
            JSONObject jsonObject = JSONUtil.parseObj(o);
            return jsonObject.getLong("_id");
        }).toList();
        Set<Long> privateJobIds = jobRepository.findPrivateJobIds(jobIds);
        for(int i = 0; i < jobList.size(); i++) {
            JSONObject jsonObject = jobList.getJSONObject(i);
            Long id = jsonObject.getLong("_id");
            jsonObject.put("isPrivateJob", privateJobIds.contains(id));
        }
    }

    private List<RaterRequestApplication> getApplicationsByTalentId(Long talentId) {
        List<TalentRecruitmentProcessVO> applications = applicationService.getTalentRecruitmentProcessBriefByTalentId(talentId).getBody();
        if (CollectionUtils.isNotEmpty(applications)) {
            return applications.stream()
                    .filter(a -> !a.getResigned())
                    .sorted(Comparator.comparing(TalentRecruitmentProcessVO::getCreatedDate, Comparator.nullsLast(Comparator.reverseOrder())))
                    .limit(5)
                    .map(RaterRequestApplication::fromApplicationForTalent)
                    .collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public void invokeRecommendJobsForTenantTalent(Long talentId, Long tenantId) {
        log.info("[invokeRecommendJobsForTenantTalent]:talentId:{},tenantId:{}",talentId,tenantId);
        RaterRequestBody requestBody = getRaterRequestBody(talentId);
        String url = applicationProperties.getRaterUrl() + RaterConstants.TENANT + tenantId + RaterConstants.FIND_JOBS_FOR_TALENT + talentId;
        getFromRedisPrefixKeyAndCallRater(talentId + "", requestBody, url, tenantId);
    }

    private void getFromRedisPrefixKeyAndCallRater(String talentId, RaterRequestBody requestBody, String url, Long tenantId) {
        String key = RedisConstants.DATA_KEY_RATER + "tenant_" + tenantId + ":jobs_for_talent_" + talentId + "_*";
        log.info("[getFromRedisPrefixKeyAndCallRater] requestBody:{}", JSONUtil.toJsonStr(requestBody));
        log.info("[getFromRedisPrefixKeyAndCallRater] url:{},key:{}", url, key);
        if (!commonRedisService.checkRaterRedisKey(key)) {
            callRater(url, requestBody);
        }
    }
}
