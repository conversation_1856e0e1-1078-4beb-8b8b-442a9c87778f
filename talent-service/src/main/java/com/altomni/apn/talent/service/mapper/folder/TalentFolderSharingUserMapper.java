package com.altomni.apn.talent.service.mapper.folder;

import com.altomni.apn.common.dto.folder.CustomFolderSharingTargetDTO;
import com.altomni.apn.talent.domain.folder.TalentFolderSharingUser;
import com.altomni.apn.talent.service.dto.folder.TalentFolderSharingUserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface TalentFolderSharingUserMapper {


    TalentFolderSharingUserDTO toDto(TalentFolderSharingUser entity);

    @Mapping(target = "targetId", source = "userId")
    @Mapping(target = "targetCategory", constant = "USER")
    CustomFolderSharingTargetDTO toFolderSharingDto(TalentFolderSharingUser entity);
    @Mapping(target = "targetId", source = "userId")
    @Mapping(target = "targetCategory", constant = "USER")
    List<CustomFolderSharingTargetDTO> toFolderSharingDto(List<TalentFolderSharingUser> entity);


    TalentFolderSharingUser toEntity(TalentFolderSharingUserDTO dto);


    @Mapping(target = "userId", source = "targetId")
    TalentFolderSharingUser toEntity(CustomFolderSharingTargetDTO dto);


}
