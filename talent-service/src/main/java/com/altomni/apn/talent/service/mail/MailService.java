package com.altomni.apn.talent.service.mail;

import com.altomni.apn.common.dto.mail.MailVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@Component
@FeignClient(value = "common-service")
public interface MailService {

    @PostMapping("/common/api/v3/campaign/send_html_mail")
    ResponseEntity<Long> sendHtmlMail(@RequestBody MailVM mailVM);

    @PostMapping(value = "/common/api/v3/campaign/send_rich_mail", produces = {MediaType.APPLICATION_JSON_VALUE})
    ResponseEntity<Void> sendRichMail(@RequestParam(name = "from", required = false) String from,
                                     @RequestParam("to") List<String> to,
                                     @RequestParam(name = "bcc", required = false) List<String> bcc,
                                     @RequestParam(name = "cc", required = false) List<String> cc,
                                     @RequestParam("subject") String subject,
                                     @RequestParam("html_content") String htmlContent,
                                     @RequestParam(name = "files", required = false) List<MultipartFile> files);

}
