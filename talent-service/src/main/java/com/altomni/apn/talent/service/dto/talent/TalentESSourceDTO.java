package com.altomni.apn.talent.service.dto.talent;

import com.altomni.apn.common.dto.talent.TalentESDocument;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentESSourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String _index;

    private String _type;

    private Object _id;

    private BigDecimal _score;

    private TalentESDocument _source;
}
