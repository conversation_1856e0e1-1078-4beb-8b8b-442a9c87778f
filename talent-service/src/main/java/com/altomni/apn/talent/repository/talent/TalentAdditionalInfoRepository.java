package com.altomni.apn.talent.repository.talent;

import com.altomni.apn.common.domain.talent.TalentAdditionalInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

public interface TalentAdditionalInfoRepository extends JpaRepository<TalentAdditionalInfo, Long> {

    @Query(value = "SELECT t.id talentId, ti.id additionInfoId, ti.extended_info additionalInfo FROM talent_additional_info ti LEFT JOIN talent t ON ti.id = t.additional_info_id LEFT JOIN hot_list_talent hlt ON t.id = hlt.talent_id WHERE hlt.hot_list_id = ?1", nativeQuery = true)
    List<Map<String, Object>> findAllByHotListId(Long hotListId);

    @Modifying
    @Transactional
    @Query(value = " update talent_additional_info set extended_info = ?2 where id = ?1 ", nativeQuery = true)
    void updateExtendInfoById(Long id, String extendInfo);

}
