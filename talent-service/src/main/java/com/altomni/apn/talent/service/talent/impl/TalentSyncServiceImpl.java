package com.altomni.apn.talent.service.talent.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.talent.config.ScheduledProperties;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.config.env.EsfillerMQProperties;
import com.altomni.apn.talent.config.env.TalentProfileMQProperties;
import com.altomni.apn.talent.config.thread.TalentBulkSyncToMqThreadTask;
import com.altomni.apn.talent.config.thread.TalentSyncThreadTask;
import com.altomni.apn.talent.config.thread.TalentSyncToHrMqThreadTask;
import com.altomni.apn.talent.config.thread.TalentSyncToMqThreadTask;
import com.altomni.apn.talent.constants.RedisConstants;
import com.altomni.apn.talent.repository.talent.TalentRepository;
import com.altomni.apn.talent.service.dto.talent.TalentExperienceInfoDTO;
import com.altomni.apn.talent.service.dto.talent.TalentLinkedinDTO;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import com.altomni.apn.talent.service.job.JobService;
import com.altomni.apn.talent.service.rabbitmq.RabbitMqService;
import com.altomni.apn.talent.service.talent.TalentSyncService;
import com.altomni.apn.talent.web.rest.vm.MqMessageCountVM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
public class TalentSyncServiceImpl implements TalentSyncService {

    private final Logger log = LoggerFactory.getLogger(TalentSyncServiceImpl.class);

    @Resource
    private TalentRepository talentRepository;

    @Resource
    private EsFillerTalentService esFillerTalentService;

    @Resource
    private JobService jobService;

    @Resource
    private CanalService canalService;

    @Resource
    private ScheduledProperties properties;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource
    private JobdivaRabbitProperties jobdivaRabbitProperties;

    @Resource
    private TalentProfileMQProperties talentProfileMQProperties;

    @Resource
    private ApplicationProperties applicationProperties;

    private static final String TALENT_RUNNING = "TALENT_RUNNING";

    private volatile ExecutorService executorService;

    private static Integer localThreadNum = 0;

    @Override
    public String syncFailedTalentsToEs() {
        List<TalentV3> talents = talentRepository.findSyncFailedTalents();
        List<Long> succeedIds = new ArrayList<>();
        List<Long> failedIds = new ArrayList<>();
        if (!talents.isEmpty()) {
            talents.forEach(t -> {
                try {
                    esFillerTalentService.syncTranslateTalentToEs(t.getId());
                    succeedIds.add(t.getId());
                } catch (CustomParameterizedException e) {
                    failedIds.add(t.getId());
                }
            });
            jobService.updateSyncFailureTalentsToSuccess(succeedIds);
        }

        JSONArray successIdArray = new JSONArray();
        successIdArray.addAll(succeedIds);
        JSONArray failureIdArray = new JSONArray();
        failureIdArray.addAll(failedIds);

        JSONObject res = new JSONObject();
        res.put("successIds", successIdArray);
        res.put("failureIds", failureIdArray);
        return res.toString();
    }

    @Override
    public void syncTalentsToEs() {
        log.info("[ElasticSearchFillerService: scheduledSyncTalentsToES @-1] Scheduled sync Talents start at: {}", LocalDateTime.now());
        Long count = talentRepository.countOutOfSyncTalentIds();
        if (count <= 0) {
            return;
        }
        int startNum = 0;
        List<Long> talentIds = talentRepository.findOutOfSyncTalentIds(startNum, properties.getTotal());
        log.info("[TalentScheduled: scheduledSyncTalentsToES @-1] total talents size: {}", talentIds.size());
        if (CollectionUtils.isEmpty(talentIds)) {
            return;
        }
        List<List<Long>> listList = CollUtil.split(talentIds, 10);
        listList = listList.stream().filter(CollUtil::isNotEmpty).collect(Collectors.toList());
        CountDownLatch countDownLatch = new CountDownLatch(listList.size());
        listList.forEach(ids -> getExecutorService().execute(new TalentSyncThreadTask(esFillerTalentService, jobService, countDownLatch, ids)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[TalentScheduled] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
        }
        log.info("[TalentScheduled: scheduledSyncTalentsToES @-1] Scheduled sync Talents to ES Done!");
    }

    private ExecutorService getExecutorService() {
        if (executorService == null) {
            synchronized (TalentSyncServiceImpl.class) {
                if (executorService == null) {
                    localThreadNum = properties.getThreadNum();
                    executorService = new ThreadPoolExecutor(
                            properties.getThreadNum(),
                            properties.getThreadNum() * 2,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-scheduled-sync-talent-to-es", false));
                }
            }
        }
        if (!Objects.equals(localThreadNum, properties.getThreadNum())) {
            log.info("talentThreadSize refresh = [{}]", properties.getThreadNum());
            executorService.shutdownNow();
            executorService = null;
            return getExecutorService();
        }
        return executorService;
    }

    @Override
    public MqMessageCountVM checkTalentMqMessageCount() {
        Integer messageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getToEsFillerQueue());
        Integer talentMessageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getApnNormalizedTalentQueue());
        log.debug("PROFILE_TO_ES_QUEUE messageCount= " + messageCount);
        return new MqMessageCountVM(Math.max(messageCount, talentMessageCount), esfillerMQProperties.getToEsFillerMaximumMsgCount());
    }

    @Override
    public MqMessageCountVM checkTalentHrMqMessageCount() {
        Integer messageCount = rabbitMqService.checkMessageCount(jobdivaRabbitProperties.getApnToJobdivaQueue());
        log.debug("PROFILE_TO_HR_QUEUE messageCount= " + messageCount);
        return new MqMessageCountVM(Math.max(messageCount, 0), esfillerMQProperties.getToEsFillerMaximumMsgCount());
    }

    @Override
    public void syncTalentsToMQ(Collection<Long> talentIds, int priority) {
        log.info("[syncTalentsToMQ @-1] Scheduled sync Talents start at: {}", LocalDateTime.now());
        List<List<Long>> talentGroupList = CollUtil.split(talentIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(talentGroupList.size());
        talentGroupList.forEach(ids -> getExecutorService().execute(new TalentSyncToMqThreadTask(esFillerTalentService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "CountDownLatch Error" +
                    "\n\tTalent IDs: " + talentIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[syncTalentsToMQ @-1] Scheduled sync Talents to MQ Done!");
    }

    @Override
    public void bulkSyncTalentsToMQ(Collection<Long> talentIds, int priority) {
        log.info("[bulkSyncTalentsToMQ @-1] Scheduled sync Talents start at: {}", LocalDateTime.now());
        List<List<Long>> talentGroupList = CollUtil.split(talentIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(talentGroupList.size());
        talentGroupList.forEach(ids -> getExecutorService().execute(new TalentBulkSyncToMqThreadTask(esFillerTalentService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "CountDownLatch Error" +
                    "\n\tTalent IDs: " + talentIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[bulkSyncTalentsToMQ @-1] Scheduled sync Talents to MQ Done!");
    }

    @Override
    public void syncTalentsToHrMQ(Collection<Long> talentIds, int priority) {
        log.info("[syncTalentsToMQ @-1] Scheduled sync Talents to hr start at: {}", LocalDateTime.now());
        List<List<Long>> talentGroupList = CollUtil.split(talentIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(talentGroupList.size());
        talentGroupList.forEach(ids -> getExecutorService().execute(new TalentSyncToHrMqThreadTask(esFillerTalentService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "CountDownLatch Error" +
                    "\n\tHrTalent IDs: " + talentIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[syncTalentsToMQ @-1] Scheduled sync Talents to hr MQ Done!");
    }

    private boolean isLinkedinQueueAvailable() {
        Integer messageCount = rabbitMqService.checkMessageCount(talentProfileMQProperties.getLinkedinRequestQueue());
        return messageCount.compareTo(talentProfileMQProperties.getSocialProfileMaximumMsgCount()) < 0;
    }

    private boolean isLinkedinResponseQueueEmpty(){
        Integer messageCount = rabbitMqService.checkMessageCount(talentProfileMQProperties.getLinkedinResponseQueue());
        return messageCount == 0;
    }

    @Async
    @Override
    public void scanTalentsWithManagerTitle() {
        try {
            Integer pageSize = 500;
            Long maxId = 0L;
            List<TalentExperienceInfoDTO> talentExperienceInfoDtos = talentRepository.getTalentExperiencesByPage(maxId, pageSize);
            if (!org.springframework.util.CollectionUtils.isEmpty(talentExperienceInfoDtos)) {
                maxId = talentExperienceInfoDtos.get(talentExperienceInfoDtos.size() - 1).getTalentInfoId();
            }
            log.info(String.valueOf(talentExperienceInfoDtos));
            String keywordString = applicationProperties.getTitleKeywords();
            Set<String> managerKeywords = Arrays.stream(keywordString.split(",")).collect(Collectors.toSet());

            int checkTalentQueueTimes = 1;
            while(CollectionUtils.isNotEmpty(talentExperienceInfoDtos)){
                log.info("maxId=" + maxId);
                if (checkTalentQueueTimes % 720 == 0){
                    NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), "The talent queue is abnormal for 1 hour.");
                }
                if (!isLinkedinQueueAvailable()){
                    checkTalentQueueTimes ++;
                    Thread.sleep(5000);
                    continue;
                }
                talentExperienceInfoDtos.forEach(talentExperience -> {
                    String experiencesStr = talentExperience.getExperiences();
                    JSONArray experiences = new JSONArray(experiencesStr);
                    if (Objects.nonNull(experiences) && !experiences.isEmpty()){
                        JSONObject latestExperience = (JSONObject) experiences.get(0);
                        String title = latestExperience.getStr("title", "");
                        if (StringUtils.isNotBlank(title)){
                            Set<String> titleKeywords = Arrays.stream(title.toLowerCase().split(" ")).map(String::trim).collect(Collectors.toSet());
                            log.info("titleKeywords= " + titleKeywords);
                            if (!SetUtils.intersection(titleKeywords, managerKeywords).isEmpty()){
                                Long talentExtendInfoId = talentExperience.getTalentInfoId();
                                // get linkedin id and send to MQ
                                List<TalentLinkedinDTO> talentLinkedinList = talentRepository.getTalentLinkedin(talentExtendInfoId);
                                if (CollectionUtils.isNotEmpty(talentLinkedinList)){
                                    String company = latestExperience.getStr("company", "");
                                    TalentLinkedinDTO talentLinkedinDTO = talentLinkedinList.get(0);
                                    log.info("getLinkedinId= " + talentLinkedinDTO.getLinkedinId());
                                    String request = JSONUtil.toJsonStr(new TalentLinkedinDTO
                                            .TalentLinkedinRequest(
                                                    talentLinkedinDTO.getTalentContactId(),
                                                    talentLinkedinDTO.getTalentId(),
                                                    talentLinkedinDTO.getLinkedinId(),
                                                    company,
                                                    title)
                                    );
                                    log.info("talentLinkedinDTO= " + request);
                                    commonRedisService.set(String.format(RedisConstants.DATA_KEY_LINKEDIN_REQUEST, talentLinkedinDTO.getLinkedinId()),
                                            request,
                                            talentProfileMQProperties.getMsgExpire());
                                    rabbitMqService.sendTalentLinkedin(request);
                                }
                            }
                        }
                    }
                });
                talentExperienceInfoDtos = talentRepository.getTalentExperiencesByPage(maxId, pageSize);
                if (!org.springframework.util.CollectionUtils.isEmpty(talentExperienceInfoDtos)) {
                    maxId = talentExperienceInfoDtos.get(talentExperienceInfoDtos.size() - 1).getTalentInfoId();
                }
                checkTalentQueueTimes = 1;
            }
            while (!isLinkedinResponseQueueEmpty()) {
                checkTalentQueueTimes ++;
                if (checkTalentQueueTimes % 3600 == 0){
                    NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), "The talent profile queue is abnormal for 5 hours.");
                }
                Thread.sleep(5000);
            }
            Thread.sleep(60000);

            log.info("check talent finished");
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), "Scan Talent Job Change finished.");
        } catch (Exception e) {
            log.error("Scan Talent Job Change error: {} at: {}", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e), System.currentTimeMillis());
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), String.format("Scan Talent Job Change error:  %s", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e)));
        }
    }

    @Override
    public void syncTalentsToMQByOwner(Long userId) {
        List<Long> talentIdsByOwnerId = this.talentRepository.getTalentIdsByOwnerId(userId);
        this.syncTalentsToMQ(talentIdsByOwnerId, 1);
    }

}
