package com.altomni.apn.talent.web.rest.linkedinproject;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinTalent;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinTalentDTO;
import com.altomni.apn.talent.service.linkedinproject.LinkedinTalentService;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinTalentVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing LinkedinTalent.
 */
@RestController
@RequestMapping("/api/v3")
public class LinkedinTalentResource {

    private final Logger log = LoggerFactory.getLogger(LinkedinTalentResource.class);

    private static final String ENTITY_NAME = "LinkedinTalent";

    private final LinkedinTalentService linkedinTalentService;

    public LinkedinTalentResource(LinkedinTalentService linkedinTalentService) {
        this.linkedinTalentService = linkedinTalentService;
    }

    @PostMapping("/linkedin-talents")
    public ResponseEntity<LinkedinTalentVO> create(@RequestBody LinkedinTalentDTO linkedinTalentDTO) throws URISyntaxException {
        log.info("[APN V3: LinkedinTalent @{}] REST request to save LinkedinTalent: {}, ", SecurityUtils.getCurrentUserLogin(), linkedinTalentDTO);
        LinkedinTalentVO result = linkedinTalentService.create(linkedinTalentDTO);
        return ResponseEntity.created(new URI("/api/v1/linkedin-talents/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId()))
            .body(result);
    }

    @PutMapping("/linkedin-talents/{id}")
    public ResponseEntity<LinkedinTalentVO> update(@PathVariable String id, @RequestBody LinkedinTalentDTO linkedinTalentDTO) {
        log.info("[APN V3: LinkedinTalent @{}] REST request to update LinkedinTalent: {}, ", SecurityUtils.getCurrentUserLogin(), linkedinTalentDTO);
        linkedinTalentDTO.setId(id);
        LinkedinTalentVO result = linkedinTalentService.update(linkedinTalentDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId()))
            .body(result);
    }

    @GetMapping("/linkedin-talents")
    public ResponseEntity<List<LinkedinTalentVO>> findAll(Pageable pageable) {
        log.info("[APN V3: LinkedinTalent @{}] REST request to get all LinkedinTalents ", SecurityUtils.getCurrentUserLogin());
        Page<LinkedinTalentVO> page = linkedinTalentService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v1/linkedin-talents");
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * GET  /linkedin-talents/:id : get the "id" linkedinTalent.
     *
     * @param id the id of the linkedinTalent to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the linkedinTalent, or with status 404 (Not Found)
     */
    @GetMapping("/linkedin-talents/{id}")
    public ResponseEntity<LinkedinTalentVO> getLinkedinTalent(@PathVariable String id) {
        log.debug("REST request to get LinkedinTalent : {}", id);
        LinkedinTalentVO linkedinTalent = linkedinTalentService.findOne(id);
        return ResponseEntity.ok().body(linkedinTalent);
    }

    @PostMapping("/linkedin-talents/by-ids")
    public ResponseEntity<List<LinkedinTalentVO>> getLinkedinTalent(@RequestBody List<String> ids, Pageable pageable) {
        log.debug("REST request to get LinkedinTalents by talentIds : {}", ids);
        Page<LinkedinTalentVO> page = linkedinTalentService.findAllByTalentIds(ids, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v1/linkedin-talents/by-ids");
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * DELETE  /linkedin-talents/:id : delete the "id" linkedinTalent.
     *
     * @param id the id of the linkedinTalent to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/linkedin-talents/{id}")
    public ResponseEntity<Void> deleteLinkedinTalent(@PathVariable String id) {
        log.info("[APN V3: LinkedinTalent @{}] REST request to delete LinkedinTalent: {}, ", SecurityUtils.getCurrentUserLogin(), id);
        linkedinTalentService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id)).build();
    }
}
