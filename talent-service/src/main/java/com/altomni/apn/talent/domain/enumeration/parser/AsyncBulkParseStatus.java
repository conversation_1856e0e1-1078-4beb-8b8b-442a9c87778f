package com.altomni.apn.talent.domain.enumeration.parser;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobStatus enumeration.
 */
public enum AsyncBulkParseStatus implements ConvertedEnum<Integer>  {

    /* Parse success*/
    PARSE_SUCCESS(0),

    /* Parse failed */
    PARSE_FAILED(-1),

    /* Create talent success*/
    CREATE_TALENT_SUCCESS(1),

    /* Create talent failed */
    CREATE_TALENT_FAILED(-2);

    private final int dbValue;

    AsyncBulkParseStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<AsyncBulkParseStatus, Integer> resolver =
        new ReverseEnumResolver<>(AsyncBulkParseStatus.class, AsyncBulkParseStatus::toDbValue);

    public static AsyncBulkParseStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
