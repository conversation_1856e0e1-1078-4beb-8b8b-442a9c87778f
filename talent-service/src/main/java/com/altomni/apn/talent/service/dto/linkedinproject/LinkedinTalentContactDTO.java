package com.altomni.apn.talent.service.dto.linkedinproject;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.ContactTypeConverter;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

@Data
public class LinkedinTalentContactDTO implements Serializable {

    private static final long serialVersionUID = -7571338464020633550L;

    @Convert(converter = ContactTypeConverter.class)
    private ContactType type;

    private String contact;

    public ContactType getType() {
        return type;
    }

    public void setType(ContactType type) {
        this.type = type;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

}
