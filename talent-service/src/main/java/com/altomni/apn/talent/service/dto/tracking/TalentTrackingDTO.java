package com.altomni.apn.talent.service.dto.tracking;


import com.altomni.apn.talent.domain.enumeration.tracking.TrackingCategory;
import com.altomni.apn.talent.domain.enumeration.tracking.TrackingCategoryConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(value = "talent tracking")
@NoArgsConstructor
@AllArgsConstructor
public class TalentTrackingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "firstName")
    @NotEmpty
    private String firstName;

    @ApiModelProperty(value = "lastName")
    @NotEmpty
    private String lastName;

    @ApiModelProperty(value = "photoUrl")
    private String photoUrl;

    @ApiModelProperty(value = "title")
    private String title;

    @ApiModelProperty(value = "connectionDegree")
    private Integer connectionDegree;

    @ApiModelProperty(value = "talentLinkedinId")
    @NotEmpty
    private String talentLinkedinId;

    @ApiModelProperty(value = "operatorLinkedinId")
    @NotEmpty
    private String operatorLinkedinId;

    @ApiModelProperty(value = "linkUrl")
    @NotEmpty
    private String linkUrl;

    @ApiModelProperty(value = "category")
    @Convert(converter = TrackingCategoryConverter.class)
    @NotNull
    private TrackingCategory category;

    private List<Long> groupIdList;

}
