package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobStatus enumeration.
 */
public enum UserAccountType implements ConvertedEnum<Integer> {

    LIMIT_USER(1),

    USER(2),

    TENANT_ADMIN(3),

    ADMIN(4);

    private final int dbValue;

    UserAccountType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<UserAccountType, Integer> resolver =
        new ReverseEnumResolver<>(UserAccountType.class, UserAccountType::toDbValue);

    public static UserAccountType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
