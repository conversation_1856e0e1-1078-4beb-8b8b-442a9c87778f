package com.altomni.apn.talent.service.dto.user;

import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.user.HotListUser;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Objects;

/**
 * A HotListUserDTO.
 */
public class HotListUserDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "hot list id", required = true)
    private Long hotListId;

    @ApiModelProperty(value = "user id", required = true)
    private Long userId;

    @ApiModelProperty(value = "username")
    private String username;

    @ApiModelProperty(value = "first name")
    private String firstName;

    @ApiModelProperty(value = "last name")
    private String lastName;


    public static HotListUserDTO fromHotListUser(HotListUser hotListUser, User user) {
        HotListUserDTO dto = new HotListUserDTO();
        ServiceUtils.myCopyProperties(hotListUser, dto);
        if (user != null) {
            dto.setFirstName(user.getFirstName());
            dto.setLastName(user.getLastName());
            dto.setUsername(user.getUsername());
        }
        return dto;
    }

    public Long getHotListId() {
        return hotListId;
    }

    public HotListUserDTO hotListId(Long hotListId) {
        this.hotListId = hotListId;
        return this;
    }

    public void setHotListId(Long hotListId) {
        this.hotListId = hotListId;
    }

    public Long getUserId() {
        return userId;
    }

    public HotListUserDTO userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o){
            return true;
        }
        if (o == null || getClass() != o.getClass()){
            return false;
        }
        HotListUserDTO hotListUser = (HotListUserDTO) o;
        return Objects.equals(userId, hotListUser.userId) &&
            Objects.equals(hotListId, hotListUser.hotListId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, hotListId);
    }


    @Override
    public String toString() {
        return "HotListUserDTO{" +
            "id=" + id +
            ", hotListId=" + hotListId +
            ", userId=" + userId +
            ", username='" + username + '\'' +
            ", firstName='" + firstName + '\'' +
            ", lastName='" + lastName +
            '}';
    }
}
