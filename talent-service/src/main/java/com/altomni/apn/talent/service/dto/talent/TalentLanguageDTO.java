package com.altomni.apn.talent.service.dto.talent;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.utils.JsonUtil;

import java.io.Serializable;

/**
 * A TalentLanguageDTO.
 * <AUTHOR>
 */
public class TalentLanguageDTO implements Serializable {

    private static final long serialVersionUID = -845138754109985789L;

    private String regulatedName;

    private Float score;

    public JSONObject toJSON() {
        JSONObject result = new JSONObject();
        JsonUtil.fluentPut(result, "regulatedName", regulatedName);
        JsonUtil.fluentPut(result, "score", score);
        return result;
    }

    public String getRegulatedName() {
        return regulatedName;
    }

    public void setRegulatedName(String regulatedName) {
        this.regulatedName = regulatedName;
    }

    public Float getScore() {
        return score;
    }

    public void setScore(Float score) {
        this.score = score;
    }

    @Override
    public String toString() {
        return "TalentLanguageDTO{" +
                "regulatedName='" + regulatedName + '\'' +
                ", score=" + score +
                '}';
    }
}
