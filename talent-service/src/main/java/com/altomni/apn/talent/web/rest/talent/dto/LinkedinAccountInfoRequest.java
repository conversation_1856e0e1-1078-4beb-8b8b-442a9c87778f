package com.altomni.apn.talent.web.rest.talent.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 领英账号信息请求DTO
 */
@ApiModel(description = "领英账号信息请求")
public class LinkedinAccountInfoRequest {

    @ApiModelProperty(value = "领英ID(URL路径参数)", required = true, example = "john-doe-123456")
    @NotBlank(message = "领英ID不能为空")
    @JsonProperty("linkedinId")
    private String linkedinId;

    @ApiModelProperty(value = "当前好友数量", required = true, example = "500")
    @NotNull(message = "好友数量不能为空")
    @JsonProperty("friendCount")
    private Integer friendCount;

    @ApiModelProperty(value = "匹配好友的时间", required = true, example = "2024-01-15 10:30:00")
    @NotBlank(message = "匹配好友时间不能为空")
    @JsonProperty("matchFriendDatetime")
    private String matchFriendDatetime;

    public String getLinkedinId() {
        return linkedinId;
    }

    public void setLinkedinId(String linkedinId) {
        this.linkedinId = linkedinId;
    }

    public Integer getFriendCount() {
        return friendCount;
    }

    public void setFriendCount(Integer friendCount) {
        this.friendCount = friendCount;
    }

    public String getMatchFriendDatetime() {
        return matchFriendDatetime;
    }

    public void setMatchFriendDatetime(String matchFriendDatetime) {
        this.matchFriendDatetime = matchFriendDatetime;
    }

    @Override
    public String toString() {
        return "LinkedinAccountInfoRequest{" +
                "linkedinId='" + linkedinId + '\'' +
                ", friendCount=" + friendCount +
                ", matchFriendDatetime='" + matchFriendDatetime + '\'' +
                '}';
    }
}