package com.altomni.apn.talent.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The RateUnitType enumeration.
 */
public enum RateUnitType implements ConvertedEnum<Integer> {
    HOURLY(0, "每小时", "Hourly"),
    DAILY(1, "每天", "Daily"),
    WEEKLY(2, "每周", "Weekly"),
    MONTHLY(3, "每月", "Monthly"),
    YEARLY(4, "每年", "Yearly");

    private final int dbValue;
    private final String cnDisplay;
    private final String enDisplay;

    RateUnitType(int dbValue, String cnDisplay, String enDisplay) {
        this.dbValue = dbValue;
        this.cnDisplay = cnDisplay;
        this.enDisplay = enDisplay;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<RateUnitType, Integer> resolver =
        new ReverseEnumResolver<>(RateUnitType.class, RateUnitType::toDbValue);

    public static RateUnitType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static RateUnitType fromString(String str) {
        for (RateUnitType type : values()) {
            if(type.getEnDisplay().equalsIgnoreCase(str)) {
                return type;
            }
        }
        return null;
    }

    public String getCnDisplay() {
        return cnDisplay;
    }

    public String getEnDisplay() {
        return enDisplay;
    }
}
