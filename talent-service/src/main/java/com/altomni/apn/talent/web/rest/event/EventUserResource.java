package com.altomni.apn.talent.web.rest.event;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.service.dto.event.EventUserDTO;
import com.altomni.apn.talent.service.event.EventUserService;
import com.altomni.apn.talent.service.vo.event.EventUserVO;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing EventUser.
 */
@RestController
@RequestMapping("/api/v3")
public class EventUserResource {

    private final Logger log = LoggerFactory.getLogger(EventUserResource.class);

    private static final String ENTITY_NAME = "EventUser";

    private final EventUserService eventUserService;

    public EventUserResource(EventUserService eventUserService) {
        this.eventUserService = eventUserService;
    }

    /**
     * POST  /event-users : Create a new EventUser.
     *
     * @param EventUser the EventUser to create
     * @return the ResponseEntity with status 201 (Created) and with body the new EventUser, or with status 400 (Bad Request) if the EventUser has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/event-users")
    @NoRepeatSubmit
    @Timed
    public ResponseEntity<EventUserVO> createEventUser(@RequestBody EventUserDTO EventUser) throws URISyntaxException {
        log.info("[APN: EventUser @{}] REST request to save EventUser : {}", SecurityUtils.getUserId(), EventUser);
        EventUserVO result = eventUserService.create(EventUser);
        return ResponseEntity.created(new URI("/api/event-users/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * GET  /events/{id}/event-users : get all the EventUsers by event id.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of EventUsers in body
     */
    @GetMapping("/events/{id}/event-users")
    @Timed
    public ResponseEntity<List<EventUserVO>> getAllEventUsers(@PathVariable Long id) {
        log.info("[APN: EventUser @{}] REST request to get a page of EventUsers", SecurityUtils.getUserId());
        return new ResponseEntity<>(eventUserService.findAllByEventId(id), HttpStatus.OK);
    }

    /**
     * GET  /event-users/:id : get the "id" EventUser.
     *
     * @param id the id of the EventUser to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the EventUser, or with status 404 (Not Found)
     */
    @GetMapping("/event-users/{id}")
    @Timed
    public ResponseEntity<EventUserVO> getEventUser(@PathVariable Long id) {
        log.info("[APN: EventUser @{}] REST request to get EventUser : {}", SecurityUtils.getUserId(), id);
        EventUserVO EventUser = eventUserService.findOne(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(EventUser));
    }
}
