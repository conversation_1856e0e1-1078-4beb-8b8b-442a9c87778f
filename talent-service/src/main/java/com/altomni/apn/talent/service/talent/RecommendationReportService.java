package com.altomni.apn.talent.service.talent;

import com.altomni.apn.talent.web.rest.talent.dto.GetRecommendationReportInput;
import com.altomni.apn.talent.web.rest.talent.dto.GetRecommendedReasonInput;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.ResponseEntity;

public interface RecommendationReportService {

    ResponseEntity<ByteArrayResource> getRecommendationReport(GetRecommendationReportInput input);

    String getRecommendedReason(GetRecommendedReasonInput input);
}
