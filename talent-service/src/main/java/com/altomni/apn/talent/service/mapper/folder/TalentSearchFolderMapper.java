package com.altomni.apn.talent.service.mapper.folder;


import com.altomni.apn.common.dto.search.TalentSearchFolderConditionDTO;
import com.altomni.apn.talent.domain.folder.TalentSearchFolder;
import com.altomni.apn.talent.service.dto.folder.TalentSearchFolderDTO;
import com.altomni.apn.talent.utils.SearchCriteriaConverter;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring")
public interface TalentSearchFolderMapper {

    @Mapping(target = "searchCriteria", source = "searchCriteria", qualifiedByName = "searchCriteriaToJsonString")
    TalentSearchFolder toEntity(TalentSearchFolderDTO dto);

    @Mapping(target = "searchCriteria", source = "searchCriteria", qualifiedByName = "jsonStringToSearchCriteria")
    TalentSearchFolderDTO toDto(TalentSearchFolder entity);


    default TalentSearchFolder fromId(Long id) {
        if (id == null) {
            return null;
        }
        TalentSearchFolder talentSearchFolder = new TalentSearchFolder();
        talentSearchFolder.setId(id);
        return talentSearchFolder;
    }


    @Named("searchCriteriaToJsonString")
    public static String searchCriteriaToJsonString(TalentSearchFolderConditionDTO conditionDTO) {
        if (conditionDTO == null) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(conditionDTO);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting List<T> to JSON string", e);
        }
    }

    @Named("jsonStringToSearchCriteria")
    public static TalentSearchFolderConditionDTO jsonStringToSearchCriteria(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        return SearchCriteriaConverter.fromJson(jsonString);
    }

    // Additional mapping methods if needed
}
