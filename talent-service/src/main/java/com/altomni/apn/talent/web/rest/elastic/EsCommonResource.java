package com.altomni.apn.talent.web.rest.elastic;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.service.dto.elastic.EsSearchByContactsDTO;
import com.altomni.apn.talent.service.elastic.EsCommonService;
import com.altomni.apn.talent.service.elastic.EsFillerTalentService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3")
public class EsCommonResource {

    private final Logger log = LoggerFactory.getLogger(EsCommonResource.class);

    private static final double ONE_SECOND = 1000;

    @Resource
    private EsCommonService esCommonService;

    @Resource
    private EsFillerTalentService esFillerTalentService;

//    /**
//     * this api use for talent search from common es db
//     *
//     * @param request search request body
//     * @return talent object list
//     */
//    @PostMapping("/search-talents-es")
//    @Timed
//    @NoRepeatSubmit
//    public ResponseEntity<List<TalentDTOV3>> searchTalents(HttpServletRequest request) throws IOException {
//        String searchRequest = CommonUtils.httpRequestMessageToString(request);
//        log.info("[APN: EsCommon @{}] REST request to search talent from common library with search body : {}", SecurityUtils.getUserId(), searchRequest);
//
//        long startTime = System.currentTimeMillis();
//        EsResponseDTO response = esCommonService.searchTalents(searchRequest);
//        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(response.getPage(), "/search-talents-es");
//        headers.add("Status", response.getStatus());
//
//        long endTime = System.currentTimeMillis();
//
//        log.info("[APN: EsCommon @{}] search for uuid: {} finished in {} seconds", SecurityUtils.getUserId(), response.getUuid(), (endTime - startTime) / ONE_SECOND);
//
//        return new ResponseEntity<>(response.getPage().getContent(), headers, HttpStatus.OK);
//    }

    @ApiOperation(value = "Only for APN Pro ")
    @PostMapping("/search-talents-es/by-contacts")
    @Timed
    public ResponseEntity<List<TalentDTOV3>> searchEsTalentsByContacts(@RequestBody EsSearchByContactsDTO searchRequest) throws IOException {
        // todo :
        log.info("[APN: EsCommon @{}] REST request to search talent from common library by EsSearchByContactsDTO : {}", SecurityUtils.getUserId(), searchRequest);
        Page<TalentDTOV3> page = esCommonService.searchEsTalentsByContacts(searchRequest);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/search-talents-es/by-contacts");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * query common es db talent by es id
     *
     * @param esId talent es id
     * @return talent object
     */
    @GetMapping("/es-talents")
    @Timed
    public ResponseEntity<TalentDTOV3> getEsTalentByEsIdOld(@RequestParam("es_id") String esId) throws IOException {
        log.info("[APN: EsCommon @{}] REST request to get es talent from elastic search with esId : {}", SecurityUtils.getUserId(), esId);
        TalentDTOV3 talentDTO = esCommonService.getEsTalentByEsId(esId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentDTO));
    }


    /**
     * query common es db talent by es id
     *
     * @param  id: talent es id
     * @return talent object
     */
    @GetMapping("/es-talents/getTalent")
    @Timed
    public ResponseEntity<TalentDTOV3> getTalent(@RequestParam("id") String id) throws IOException {
        log.info("[APN: EsCommon @{}] REST request to get es talent from elastic search with condition : {}", SecurityUtils.getUserId(), id);
        TalentDTOV3 talentDTO = esCommonService.getTalent(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentDTO));
    }

    @GetMapping("/es-talents/getTalent/internal")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentDTOV3> getTalentForInternalService(@RequestParam("id") String id, @RequestParam("tenantId") Long tenantId) throws IOException {
        log.info("[APN: EsCommon @{}] REST request to get es talent for INTERNAL USE ONLY from elastic search with condition : {}", SecurityUtils.getUserId(), id);
        TalentDTOV3 talentDTO = esCommonService.getTalentForInternal(id, tenantId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(talentDTO));
    }

    /**
     * this api update username common es db
     *
     * @param userId
     * @return talent object list
     */
//    @GetMapping("/es-talents/update-username/{userId}")
//    @Timed
//    @NoRepeatSubmit
//    public void updateUserNameFromEsDb(@PathVariable("userId") Long userId) {
//        log.info("[APN: EsCommon @{}] REST request to update username by userId : {}", SecurityUtils.getUserId(), userId);
//        esFillerTalentService.updateUserName(userId);
//    }

    /**
     * this api update client contact name common es db
     *
     * @param clientId
     * @return talent object list
     */
    @GetMapping("/es-talents/update-client-contact-name/{clientId}")
    @Timed
    @NoRepeatSubmit
    public void updateClientContactNameFromEsDb(@PathVariable("clientId") Long clientId) {
        log.info("[APN: EsCommon @{}] REST request to update client contact name by userId : {}", SecurityUtils.getUserId(), clientId);
        esFillerTalentService.updateClientContactName(clientId);
    }


    @GetMapping("/es/search-shcool/{schoolName}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> getSearchSchool(@PathVariable("schoolName") String schoolName) throws IOException {
        log.info("[APN: EsCommon @{}] REST request to get schoolName : {}", SecurityUtils.getUserId(), schoolName);
        return ResponseEntity.ok().body(esCommonService.likeSearchSchool(schoolName));
    }
}
