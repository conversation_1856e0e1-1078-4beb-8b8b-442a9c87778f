package com.altomni.apn.talent.constants;

import java.util.List;
import java.util.Optional;

public enum SearchTalentEsSource {
    NAME("Educations", List.of("topEducation","nonTopEducations")),
    COMPANY("Experiences", List.of("currentExperiences","pastExperiences")),
    TITLE("Notes", List.of("notes"));
    private final String fieldName;
    private final List<String> esSourceName;

    SearchTalentEsSource(String fieldName, List<String> esSourceName) {
        this.fieldName = fieldName;
        this.esSourceName = esSourceName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public List<String> getEsSourceName() {
        return esSourceName;
    }

    public static Optional<SearchTalentEsSource> parse(String configName) {
        for(SearchTalentEsSource columnConfig : SearchTalentEsSource.values()) {
            if(columnConfig.getFieldName().contentEquals(configName)) {
                return Optional.of(columnConfig);
            }
        }
        return Optional.empty();
    }
}
