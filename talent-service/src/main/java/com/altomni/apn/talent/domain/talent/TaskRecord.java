package com.altomni.apn.talent.domain.talent;


import com.altomni.apn.common.domain.AbstractAuditingEntity;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A TaskRecord.
 */
@Entity
@Table(name = "task_record")
public class TaskRecord extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "total_num")
    private Integer totalNum;

    @Column(name = "success_num")
    private Integer successNum;

    @Column(name = "fail_num")
    private Integer failNum;

    @Column(name = "ignore_num")
    private Integer ignoreNum;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getTotalNum() {
        return totalNum;
    }

    public TaskRecord totalNum(Integer totalNum) {
        this.totalNum = totalNum;
        return this;
    }

    public void setTotalNum(Integer totalNum) {
        this.totalNum = totalNum;
    }

    public Integer getSuccessNum() {
        return successNum;
    }

    public TaskRecord successNum(Integer successNum) {
        this.successNum = successNum;
        return this;
    }

    public void setSuccessNum(Integer successNum) {
        this.successNum = successNum;
    }

    public Integer getFailNum() {
        return failNum;
    }

    public TaskRecord failNum(Integer failNum) {
        this.failNum = failNum;
        return this;
    }

    public void setFailNum(Integer failNum) {
        this.failNum = failNum;
    }

    public Integer getIgnoreNum() {
        return ignoreNum;
    }

    public TaskRecord ignoreNum(Integer ignoreNum) {
        this.ignoreNum = ignoreNum;
        return this;
    }

    public void setIgnoreNum(Integer ignoreNum) {
        this.ignoreNum = ignoreNum;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TaskRecord taskRecord = (TaskRecord) o;
        if (taskRecord.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), taskRecord.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "TaskRecord{" +
            "id=" + getId() +
            ", totalNum='" + getTotalNum() + "'" +
            ", successNum='" + getSuccessNum() + "'" +
            ", failNum='" + getFailNum() + "'" +
            ", ignoreNum='" + getIgnoreNum() + "'" +
            "}";
    }
}
