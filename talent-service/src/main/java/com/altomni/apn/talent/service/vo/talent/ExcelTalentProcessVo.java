package com.altomni.apn.talent.service.vo.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ExcelTalentProcessVo {

    private String uuid;

    private String fileName;

    private Long total;

    private Integer currentIndex;

    private String contentType;

    private String status;

    private String progress;

    private List<TalentFailReasonVo> failList = new ArrayList<>();

    private List<String> successList = new ArrayList<>();

    private String startRowIndex;

}
