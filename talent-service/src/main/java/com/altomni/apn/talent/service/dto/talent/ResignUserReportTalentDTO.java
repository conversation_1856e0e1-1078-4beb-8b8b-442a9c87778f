package com.altomni.apn.talent.service.dto.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.talent.domain.enumeration.Gender;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Email;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ResignUserReportTalentDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 8487241795942012560L;

    private Long id;

    @ApiModelProperty(value = "first name. required in US.")
    private String firstName;

    @ApiModelProperty(value = "last name. required in CN.")
    private String lastName;

    @ApiModelProperty(value = "full name. Either firstName & lastName (in US) or full name (in CN) are required. Either way, " +
        "the full name can not be null.")
    private String fullName;

    @ApiModelProperty(value = "url link to photo")
    private String photoUrl;

    @ApiModelProperty(value = "Birth Date")
    private LocalDate birthDate;

    @ApiModelProperty(allowableValues = "MALE, FEMALE")
    private Gender gender;

    @ApiModelProperty(value = "The primary email address. If this is available, it is also saved in TalentContact with type PRIMARY_EMAIL")
    @Email
    private String email;

    @ApiModelProperty(value = "The primary phone number. If this is available, it is also saved in TalentContact with type PRIMARY_PHONE")
    private String phone;

    private String website;

    @ApiModelProperty(value = "Talent expected minimum salary")
    private BigDecimal expectedSalaryFrom;

    @ApiModelProperty(value = "Talent expected maximum salary")
    private BigDecimal expectedSalaryTo;

    @ApiModelProperty(value = "Talent expected salary currency")
    private Integer currency;

    @ApiModelProperty(value = "the date talent is available for new job")
    private LocalDate availableDate;

    @ApiModelProperty(value = "talent's current job title")
    private List<String> title;

    @ApiModelProperty(value = "talents' current job's company name")
    private List<String> company;

    @ApiModelProperty(value = "whether talent is active. Inactive talent means the profile is deleted, and should not show " +
        "up in any search. Default is true.")
    private Boolean active;

    @ApiModelProperty(value = "common talent id in elastic search")
    private String esId;

    @ApiModelProperty(value = "The tenant id talent belongs to. This is saved from the user's tenant when creating talent.")
    @JsonIgnore
    private Long tenantId;

    @ApiModelProperty(value = "Comma separated list of industries talent specified")
    private List<String> industries;

    @ApiModelProperty(value = "Talent job functions")
    private List<String> jobFunctions;

    private List<String> fieldOfStudies;

    private List<TalentLanguageDTO> languages;

    private String expLevel;

    @ApiModelProperty(value = "Talent current location. Save to talent basic information table.")
    private LocationDTO currentLocation;

}
