package com.altomni.apn.talent.domain.enumeration.linkedinproject;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum Status implements ConvertedEnum<Integer>{

    ACTIVE(0),

    ARCHIVED(1);


    private final Integer dbValue;

    Status(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<Status, Integer> resolver =
        new ReverseEnumResolver<>(Status.class, Status::toDbValue);

    public static Status fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
