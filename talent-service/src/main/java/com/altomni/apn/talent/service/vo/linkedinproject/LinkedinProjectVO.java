package com.altomni.apn.talent.service.vo.linkedinproject;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinProject;
import com.altomni.apn.talent.domain.linkedinproject.LinkedinProjectMember;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Status;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LinkedinProjectVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 8489586936391610005L;

    private Long id;

    private Long tenantId;

    @JsonIgnore
    private Long jobId;

    private JobDTOV3 job;

    private Long createdUserId;

    private String name;

    private Visibility visibility;

    private Status status;

    private Boolean isSystemGenerated;

    private String description;

    private List<LinkedinProjectMember> members;

    private Boolean favorite = Boolean.FALSE;

    private Long totalCandidatesCount;

    private Long candidatesWithContactInfoCount;

    private Long candidatesInApplicationCount;

    private Boolean hasNewLinkedinTalent;

    private Boolean isNewLinkedinProject;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public JobDTOV3 getJob() {
        return job;
    }

    public void setJob(JobDTOV3 job) {
        if (job == null) {
            return;
        }
        JobDTOV3 simpleJob = new JobDTOV3();
        simpleJob.setId(job.getId());
        simpleJob.setTitle(job.getTitle());
//        simpleJob.setCompanyId(job.getCompanyId());
        simpleJob.setCompany(job.getCompany());
        simpleJob.setStatus(job.getStatus());
        simpleJob.setCurrency(null);
        simpleJob.setOpenings(null);
        simpleJob.setMaxSubmissions(null);
        simpleJob.setIsPrivateJob(job.getIsPrivateJob());
        this.job = simpleJob;
    }

    public Visibility getVisibility() {
        return visibility;
    }

    public void setVisibility(Visibility visibility) {
        this.visibility = visibility;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Boolean getSystemGenerated() { return isSystemGenerated; }

    public void setSystemGenerated(Boolean systemGenerated) { isSystemGenerated = systemGenerated; }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getCreatedUserId() {
        return createdUserId;
    }

    public void setCreatedUserId(Long createdUserId) {
        this.createdUserId = createdUserId;
    }

    public List<LinkedinProjectMember> getMembers() {
        return members;
    }

    public void setMembers(List<LinkedinProjectMember> members) {
        this.members = members;
    }

    public Boolean getFavorite() {
        return favorite;
    }

    public void setFavorite(Boolean favorite) { this.favorite = favorite; }

    public Long getTotalCandidatesCount() { return totalCandidatesCount; }

    public void setTotalCandidatesCount(Long totalCandidatesCount) { this.totalCandidatesCount = totalCandidatesCount; }

    public Long getCandidatesWithContactInfoCount() { return candidatesWithContactInfoCount; }

    public void setCandidatesWithContactInfoCount(Long candidatesWithContactInfoCount) { this.candidatesWithContactInfoCount = candidatesWithContactInfoCount; }

    public Long getCandidatesInApplicationCount() { return candidatesInApplicationCount; }

    public void setCandidatesInApplicationCount(Long candidatesInApplicationCount) { this.candidatesInApplicationCount = candidatesInApplicationCount; }

    public Boolean getHasNewLinkedinTalent() { return hasNewLinkedinTalent; }

    public void setHasNewLinkedinTalent(Boolean hasNewLinkedinTalent) { this.hasNewLinkedinTalent = hasNewLinkedinTalent; }

    public Boolean getNewLinkedinProject() { return isNewLinkedinProject; }

    public void setNewLinkedinProject(Boolean newLinkedinProject) { isNewLinkedinProject = newLinkedinProject; }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinProjectVO linkedinProject = (LinkedinProjectVO) o;
        if (linkedinProject.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinProject.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinProjectDTO{" +
                "id=" + id +
                ", tenantId=" + tenantId +
                ", job=" + job +
                ", createdUserId=" + createdUserId +
                ", name='" + name + '\'' +
                ", visibility=" + visibility +
                ", status=" + status +
                ", isSystemGenerated=" + isSystemGenerated +
                ", description='" + description + '\'' +
                ", members=" + members +
                ", favorite=" + favorite +
                ", totalCandidatesCount=" + totalCandidatesCount +
                ", candidatesWithContactInfoCount=" + candidatesWithContactInfoCount +
                ", candidatesInApplicationCount=" + candidatesInApplicationCount +
                ", hasNewLinkedinTalent=" + hasNewLinkedinTalent +
                ", isNewLinkedinProject=" + isNewLinkedinProject +
            '}';
    }

    public static LinkedinProjectVO fromLinkedinProject(LinkedinProject linkedinProject) {
        LinkedinProjectVO linkedinProjectVO = new LinkedinProjectVO();
        ServiceUtils.myCopyProperties(linkedinProject, linkedinProjectVO);
        return linkedinProjectVO;
    }
}
