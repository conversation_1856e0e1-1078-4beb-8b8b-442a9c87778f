package com.altomni.apn.talent.domain.enumeration.sparkpost.events;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * The ContractType enumeration.
 */
public enum BounceClass implements ConvertedEnum<Integer> {
    SOFT(21, Arrays.asList(20, 21, 22, 23, 24, 40, 60, 70, 100)),
    BLOCK(22 ,Arrays.asList(50, 51, 52, 53, 54)),
    ADMIN(23, Arrays.asList(25, 80)),
    UNDETERMINED(24, Arrays.asList(1)),
    HARD(25, Arrays.asList(10, 30, 90));

    private static final Map<Integer, BounceClass> BOUNCE_CODE_MAP = new HashMap<>();

    static{
        for(BounceClass bounceClass: BounceClass.values()) {
            for(Integer bounceCode: bounceClass.getBounceCode()) {
                BOUNCE_CODE_MAP.put(bounceCode, bounceClass);
            }
        }
    }

    private final Integer dbValue;

    private final List<Integer> bounceCode;

    BounceClass(Integer dbValue, List<Integer> bounceCode) {
        this.dbValue = dbValue;
        this.bounceCode = bounceCode;
    }

    @Override
    public Integer toDbValue() { return dbValue; }

    public List<Integer> getBounceCode() { return bounceCode; }

    // static resolving:
    public static final ReverseEnumResolver<BounceClass, Integer> resolver =
        new ReverseEnumResolver<>(BounceClass.class, BounceClass::toDbValue);

    public static BounceClass fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static BounceClass fromBounceCode(Integer bounceCode) {
        if(bounceCode == null) return null;
        return BOUNCE_CODE_MAP.get(bounceCode);
    }
}
