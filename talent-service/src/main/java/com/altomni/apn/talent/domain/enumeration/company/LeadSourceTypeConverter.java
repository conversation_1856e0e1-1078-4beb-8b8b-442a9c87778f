package com.altomni.apn.talent.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class LeadSourceTypeConverter extends AbstractAttributeConverter<LeadSourceType, Integer> {
    public LeadSourceTypeConverter() {
        super(LeadSourceType::toDbValue, LeadSourceType::fromDbValue);
    }
}
