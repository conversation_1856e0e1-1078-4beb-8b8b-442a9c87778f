package com.altomni.apn.talent.domain.enumeration.email;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The EmailBlastStatus enumeration.
 */
public enum MailingListStatus implements ConvertedEnum<Integer> {
    ARCHIVED(0),
    VALID(1);

    private final Integer dbValue;

    MailingListStatus(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<MailingListStatus, Integer> resolver =
        new ReverseEnumResolver<>(MailingListStatus.class, MailingListStatus::toDbValue);

    public static MailingListStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
