package com.altomni.apn.talent.service.talent;

import cn.hutool.json.JSONArray;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.search.GlobalSearchConditionDTO;
import com.altomni.apn.common.dto.search.TalentSearchConditionDTO;
import com.altomni.apn.common.dto.talent.GetTalentsForAiRecommendationDTO;
import com.altomni.apn.talent.service.dto.talent.CategoryCountDTO;
import com.altomni.apn.talent.web.rest.talent.dto.SearchTalentSourceInput;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.List;

public interface TalentServiceV3 {

    String searchTalentFromES(TalentSearchConditionDTO condition, Pageable pageable, HttpHeaders headers, boolean checkPermission, boolean isForGlobalSearch) throws IOException;

    String searchTalentFromESForApnGlobalSearch(GlobalSearchConditionDTO condition, HttpHeaders headers) throws IOException;

    String searchTalentFromES(TalentSearchConditionDTO condition, Pageable pageable, HttpHeaders headers, boolean isForGlobalSearch) throws IOException;

    JSONArray findTalentsForAiRecommendationByIds(GetTalentsForAiRecommendationDTO dto);

    List<String> querySearchHistory();

    List<CategoryCountDTO> getTalentSearchCategoryStatistics();

    String searchTalentSourceFromES(SearchTalentSourceInput condition, Pageable pageable, HttpHeaders headers) throws IOException;

    void recordTalentJobRecommend(RecommendFeedback dto);
}
