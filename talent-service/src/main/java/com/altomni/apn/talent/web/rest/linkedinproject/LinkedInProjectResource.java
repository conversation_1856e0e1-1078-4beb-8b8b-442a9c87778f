package com.altomni.apn.talent.web.rest.linkedinproject;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.aop.user.AttachSimpleUser;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Status;
import com.altomni.apn.talent.service.dto.linkedinproject.Filter;
import com.altomni.apn.talent.service.dto.linkedinproject.LinkedinProjectDTO;
import com.altomni.apn.talent.service.dto.linkedinproject.SearchHistory;
import com.altomni.apn.talent.service.linkedinproject.LinkedInProjectService;
import com.altomni.apn.talent.service.query.LinkedinProjectSearch;
import com.altomni.apn.talent.service.vo.linkedinproject.LinkedinProjectVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/v3")
public class LinkedInProjectResource {

    private final Logger log = LoggerFactory.getLogger(LinkedInProjectResource.class);

    private final LinkedInProjectService linkedInProjectService;

    public LinkedInProjectResource(LinkedInProjectService linkedInProjectService) {
        this.linkedInProjectService = linkedInProjectService;
    }

    @PostMapping("/linkedin-projects/filters")
    public ResponseEntity<String> saveFilters(@RequestBody Filter filter) throws IOException {
        log.info("[APN: LinkedInProjectResource @{}] REST request to save filter: {}", SecurityUtils.getUserId(), filter);
        return new ResponseEntity<>(linkedInProjectService.saveFilters(filter).getBody(), HttpStatus.OK);
    }

    @GetMapping("/linkedin-projects/filters")
    public ResponseEntity<String> getFilters() throws IOException {
        log.info("[APN: LinkedInProjectResource @{}] REST request to get filters", SecurityUtils.getUserId());
        return new ResponseEntity<>(linkedInProjectService.getFilters().getBody(), HttpStatus.OK);
    }

    @DeleteMapping("/linkedin-projects/filters/{id}")
    public ResponseEntity<String> deleteFilterById(@PathVariable String id) throws IOException {
        log.info("[APN: LinkedInProjectResource @{}] REST request to delete filter by id: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(linkedInProjectService.deleteFilterById(id).getBody(), HttpStatus.OK);
    }

    @PostMapping("/linkedin-projects/search-histories")
    public ResponseEntity<String> saveSearchHistory(@RequestBody SearchHistory searchHistory) throws IOException {
        log.info("[APN: LinkedInProjectResource @{}] REST request to create search-history: {}", SecurityUtils.getUserId(), searchHistory);
        return new ResponseEntity<>(linkedInProjectService.saveSearchHistory(searchHistory).getBody(), HttpStatus.OK);
    }

    @GetMapping("/linkedin-projects/search-histories")
    public ResponseEntity<String> getSearchHistories() throws IOException {
        log.info("[APN: LinkedInProjectResource @{}] REST request to get search history", SecurityUtils.getUserId());
        return new ResponseEntity<>(linkedInProjectService.getSearchHistories().getBody(), HttpStatus.OK);
    }

    @GetMapping("/linkedin-projects/search-histories/{id}")
    public ResponseEntity<String> getSearchHistoryById(@PathVariable String id) throws IOException {
        log.info("[APN: LinkedInProjectResource @{}] REST request to get search history by id: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(linkedInProjectService.getSearchHistoryById(id).getBody(), HttpStatus.OK);
    }

    @DeleteMapping("/linkedin-projects/search-histories/{id}")
    public ResponseEntity<String> deleteSearchHistoryById(@PathVariable String id) throws IOException {
        log.info("[APN: LinkedInProjectResource @{}] REST request to delete search history by id: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(linkedInProjectService.deleteSearchHistoryById(id).getBody(), HttpStatus.OK);
    }

    @PostMapping("/linkedin-projects")
    @AttachSimpleUser
    public ResponseEntity<LinkedinProjectVO> saveLinkedinProject(@RequestBody LinkedinProjectDTO linkedinProjectDto) {
        log.info("[APN V3: LinkedInProjectResource @{}] REST request to save LinkedinProject: {}", SecurityUtils.getUserId(), linkedinProjectDto);

        return new ResponseEntity<>(linkedInProjectService.saveLinkedinProject(linkedinProjectDto), HttpStatus.OK);
    }

    @PutMapping("/linkedin-projects/{id}")
    @AttachSimpleUser
    public ResponseEntity<LinkedinProjectVO> updateLinkedinProject(@PathVariable Long id, @RequestBody LinkedinProjectDTO linkedinProjectDto) {
        log.info("[APN V3: LinkedInProjectResource @{}] REST request to update LinkedinProject: {}", SecurityUtils.getUserId(), linkedinProjectDto);
        linkedinProjectDto.setId(id);
        return new ResponseEntity<>(linkedInProjectService.updateLinkedinProject(linkedinProjectDto), HttpStatus.OK);
    }

    //to mark Linkedin project id for current user as read
    @GetMapping("/linkedin-projects/{id}")
    @AttachSimpleUser
    public ResponseEntity<LinkedinProjectVO> getLinkedinProjectById(@PathVariable Long id) {
        log.info("[APN V3: LinkedInProjectResource @{}] REST request to get LinkedinProject: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(linkedInProjectService.getLinkedinProjectById(id), HttpStatus.OK);
    }

    @GetMapping("/all-linkedin-projects")
    @AttachSimpleUser
    public ResponseEntity<List<LinkedinProjectVO>> getAllLinkedinProject(
        @RequestParam(value = "projectName", required = false) String projectName,
        @RequestParam(value = "jobName", required = false) String jobName,
        @RequestParam(value = "createdBy", required = false) Long createdBy,
        @RequestParam(value = "status", required = false) Status status,
        @PageableDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN V3: LinkedInProjectResource @{}] REST request to get all linkedin projects by projectName: {}, " +
                "jobName:{}, createdBy:{}, status:{}", SecurityUtils.getUserId(), projectName, jobName, createdBy, status);
        Page<LinkedinProjectVO> page = linkedInProjectService.findAll(LinkedinProjectSearch.allLinkedinProjects(projectName, jobName, createdBy, status), pageable);
        return new ResponseEntity<>(page.getContent(), PaginationUtil.generatePaginationHttpHeaders(page, "/api/v1/all-linkedin-projects"), HttpStatus.OK);
    }

    @GetMapping("/my-linkedin-projects")
    @AttachSimpleUser
    public ResponseEntity<List<LinkedinProjectVO>> getMyLinkedinProject(
        @RequestParam(value = "projectName", required = false) String projectName,
        @RequestParam(value = "jobName", required = false) String jobName,
        @RequestParam(value = "createdBy", required = false) Long createdBy,
        @RequestParam(value = "status", required = false) Status status,
        @PageableDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN V3: LinkedInProjectResource @{}] REST request to get my linkedin projects by projectName: {}, jobName:{}, createdBy:{}, status:{}",
            SecurityUtils.getUserId(), projectName, jobName, createdBy, status);
        Page<LinkedinProjectVO> page = linkedInProjectService.findAll(LinkedinProjectSearch.myLinkedinProjects(projectName, jobName, createdBy, status), pageable);
        return new ResponseEntity<>(page.getContent(), PaginationUtil.generatePaginationHttpHeaders(page, "/api/v1/my-linkedin-projects"), HttpStatus.OK);
    }

    @PostMapping("/linkedin-projects/favorite/{id}")
    @AttachSimpleUser
    public ResponseEntity<LinkedinProjectVO> favoriteLinkedinProject(@PathVariable Long id) {
        log.info("[APN V3: LinkedInProjectResource @{}] REST request to favorite LinkedinProject: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(linkedInProjectService.favoriteLinkedinProject(id), HttpStatus.OK);
    }

    @PostMapping("/linkedin-projects/unfavorite/{id}")
    @AttachSimpleUser
    public ResponseEntity<LinkedinProjectVO> unFavoriteLinkedinProject(@PathVariable Long id) {
        log.info("[APN V3: LinkedInProjectResource @{}] REST request to favorite LinkedinProject: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(linkedInProjectService.unFavoriteLinkedinProject(id), HttpStatus.OK);
    }
}
