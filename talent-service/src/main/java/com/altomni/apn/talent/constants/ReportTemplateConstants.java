package com.altomni.apn.talent.constants;

public class ReportTemplateConstants {
    public static final String CN_RESUME_DOC_PATH = "report_template/cn_report_template_by_resume.docx";
    public static final String EN_RESUME_DOC_PATH = "report_template/en_report_template_by_resume.docx";
    public static final String CN_DOC_PATH = "report_template/cn_report_template.docx";
    public static final String EN_DOC_PATH = "report_template/en_report_template.docx";
    public static final String CN_OUTSOURCING_CONSULTANT_DOC_PATH = "report_template/cn_report_sourcing_consultant_resume.docx";
    public static final String HAN_SANS_FONT = "report_template/SourceHanSansCN-Normal.ttf";
    public static final String PINGFANG_FONT = "report_template/PingFang-SC-Regular.ttf";
    public static final String PREFIX_EMPTY = "  ";
    public static final String CN_SO_FAR_DISPLAY = "至今";
    public static final String EN_SO_FAR_DISPLAY = "Present";
    public static final String LOGO = "report_template/logo.jpg";
    public static final String DEFAULT_PHOTO = "report_template/default_photo.jpeg";

    public static final String FILENAME_VARIABLE_TITLE = "title";
    public static final String FILENAME_VARIABLE_FULLNAME = "fullName";

    //自定义模版通用key
    public static final String TEMPLATE_PARAM_JOB_TITLE = "jobTitle";
    public static final String TEMPLATE_PARAM_COMPANY_NAME = "companyName";
    public static final String TEMPLATE_PARAM_TALENT_NAME = "talentName";
    public static final String TEMPLATE_PARAM_GENDER = "gender";
    public static final String TEMPLATE_PARAM_AGE = "age";
    //所有电话使用,拼接
    public static final String TEMPLATE_PARAM_PHONES = "phones";
    //所有邮箱 使用,拼接
    public static final String TEMPLATE_PARAM_EMAILS = "emails";
    public static final String TEMPLATE_PARAM_CURRENT_LOCATION = "currentLocation";
    public static final String TEMPLATE_PARAM_PERFERRED_LOCATION = "preferredLocation";
    //education对象内涵educationSchool、educationMajor、educationDegree、educationDate
    public static final String TEMPLATE_PARAM_EDUCATION = "education";
    public static final String TEMPLATE_PARAM_EDUCATION_ASC = "educationAsc";
    public static final String TEMPLATE_PARAM_LANGUAGE = "language";
    public static final String TEMPLATE_PARAM_CURRENT_SALARY = "currentSalary";
    public static final String TEMPLATE_PARAM_PREFERRED_SALARY = "preferredSalary";
    //候选人姓名
    public static final String TEMPLATE_PARAM_FIRST_NAME = "firstName";
    public static final String TEMPLATE_PARAM_LAST_NAME = "lastName";
    //生成推荐报告时间
    public static final String TEMPLATE_PARAM_REPORT_DATE = "reportDate";
    //客户公司logo
    public static final String TEMPLATE_PARAM_CLIENT_COMPANY_LOGO = "clientCompanyLogo";
    //候选人简历图片
    public static final String TEMPLATE_PARAM_RESUME_DISPLAY = "resumeDisplay";
    // 候选人简历每一页的图片
    public static final String TEMPLATE_PARAM_RESUME_DISPLAY_PAGE = "resumeDisplayPage";
    //APN User name
    public static final String TEMPLATE_PARAM_APN_USER_NAME = "apnUserName";
    //APN User email
    public static final String TEMPLATE_PARAM_APN_USER_EMAIL = "apnUserEmail";
    //候选人教育经历
    public static final String TEMPLATE_PARAM_TALENT_EDUCATION = "talentEducation";
    //候选人工作经历日期
    public static final String TEMPLATE_PARAM_WORK_EXPERIENCE = "workExperience";
    //候选人工作经历日期
    public static final String TEMPLATE_PARAM_FIRST_WORK_EXPERIENCE = "firstWorkExperience";
    //候选人工作经历日期
    public static final String TEMPLATE_PARAM_WORK_EXPERIENCE_DATE = "workExperienceDate";
    //候选人第一段工作经历日期
    public static final String TEMPLATE_PARAM_FIRST_WORK_EXPERIENCE_DATE = "firstWorkExperienceDate";
    //候选人工作经历详情
    public static final String TEMPLATE_PARAM_WORK_EXPERIENCE_DISPLAY = "workExperienceDisplay";
    //候选人第一段工作经历公司
    public static final String TEMPLATE_PARAM_FIRST_WORK_EXPERIENCE_SCHOOL_DISPLAY = "firstWorkExperienceCompanyDisplay";
    //候选人第一段工作经历职位
    public static final String TEMPLATE_PARAM_FIRST_WORK_EXPERIENCE_POSITION_DISPLAY = "firstWorkExperiencePostionDisplay";
    //候选人教育日期
    public static final String TEMPLATE_PARAM_EDUCATION_DATE = "educationDate";
    //候选人教育学校
    public static final String TEMPLATE_PARAM_EDUCATION_SCHOOL_DISPLAY = "educationSchoolDisplay";
    //候选人教育学历
    public static final String TEMPLATE_PARAM_EDUCATION_DEGREE_DISPLAY = "educationDegreeDisplay";
    //候选人assessment说明
    public static final String TEMPLATE_PARAM_ASSESSMENT_DISPLAY = "assessmentDisplay";


    //外包模版定制key
    public static final String SOURCING_CONSULTANT_TEMPLATE_PARAM_RECOMMENDED_REASON = "sourcingConsultantRecommendedReason";
    public static final String SOURCING_CONSULTANT_TEMPLATE_PARAM_WORD_EXPERIENCE = "sourcingConsultantWorkExperience";
    public static final String SOURCING_CONSULTANT_TEMPLATE_PARAM_PROJECT_EXPERIENCE = "sourcingConsultantProjectExperience";
    //吉利模版定义key
    public static final String GEELY_TEMPLATE_PARAM_WORD_EXPERIENCE = "geelyWorkExperience";
    //CV Format模版定义key
    public static final String CV_FORMAT_TEMPLATE_PARAM_EDUCATION = "cvFormatEducation";
    public static final String CV_FORMAT_TEMPLATE_PARAM_WORD_EXPERIENCE = "cvFormatWorkExperience";
    //dreame 模版定义key
    public static final String DREAME_TEMPLATE_PARAM_WORD_EXPERIENCE = "dreameWorkExperience";
    public static final String DREAME_TEMPLATE_PARAM_PROJECT_EXPERIENCE = "dreameProjectExperience";
    //CV Format模版定义key
    public static final String MALAYSIA_TEMPLATE_PARAM_EDUCATION = "malaysiaCvFormatEducation";
    public static final String MALAYSIA_TEMPLATE_PARAM_WORD_EXPERIENCE = "malaysiaCvFormatWorkExperience";
    //Intellipro CV Format (TH) 2025模版定义key
    public static final String CV_FORMAT_TH_TEMPLATE_PARAM_WORD_EXPERIENCE = "cvFormatThWorkExperience";
    //Consultant Presentation 模版定义key
    public static final String CONSULTANT_PRESENTATION_EDUCATION = "consultantPresentationEducation";
    public static final String CONSULTANT_PRESENTATION_WORK_EXPERIENCE = "consultantPresentationWorkExperience";
    public static final String CONSULTANT_PRESENTATION_PROJECT_EXPERIENCE = "consultantPresentationProjectExperience";

}
