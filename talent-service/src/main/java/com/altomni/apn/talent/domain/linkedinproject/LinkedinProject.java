package com.altomni.apn.talent.domain.linkedinproject;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Status;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.StatusConverter;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.Visibility;
import com.altomni.apn.talent.domain.enumeration.linkedinproject.VisibilityConverter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * A LinkedinProject.
 */
@Entity
@Table(name = "linkedin_project")
public class LinkedinProject extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -6132167982729059688L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "job_id")
    private Long jobId;

    @NotNull
    @Column(name = "created_user_id")
    private Long createdUserId;

    @NotNull
    @Column(name = "name")
    private String name;

    @Convert(converter = VisibilityConverter.class)
    @Column(name = "visibility")
    private Visibility visibility;

    @Convert(converter = StatusConverter.class)
    @Column(name = "status")
    private Status status;

    @Column(name = "is_system_generated")
    private Boolean isSystemGenerated;

    @Column(name = "description")
    private String description;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Visibility getVisibility() {
        return visibility;
    }

    public void setVisibility(Visibility visibility) {
        this.visibility = visibility;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public Boolean getSystemGenerated() { return isSystemGenerated; }

    public void setSystemGenerated(Boolean systemGenerated) { isSystemGenerated = systemGenerated; }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getCreatedUserId() {
        return createdUserId;
    }

    public void setCreatedUserId(Long createdUserId) {
        this.createdUserId = createdUserId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinProject linkedinProject = (LinkedinProject) o;
        if (linkedinProject.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinProject.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinProject{" +
                "id=" + id +
                ", tenantId=" + tenantId +
                ", name='" + name + '\'' +
                ", jobId=" + jobId +
                ", visibility=" + visibility +
                ", status=" + status +
                ", isSystemGenerated=" + isSystemGenerated +
                ", description='" + description + '\'' +
                ", createdUserId=" + createdUserId +
            '}';
    }
}
