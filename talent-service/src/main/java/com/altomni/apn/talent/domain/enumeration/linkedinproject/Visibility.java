package com.altomni.apn.talent.domain.enumeration.linkedinproject;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum Visibility implements ConvertedEnum<Integer>{

    VISIBLE_TO_PROJECT_MEMBERS(0),

    VISIBLE_TO_ALL(1);


    private final Integer dbValue;

    Visibility(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<Visibility, Integer> resolver =
        new ReverseEnumResolver<>(Visibility.class, Visibility::toDbValue);

    public static Visibility fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
