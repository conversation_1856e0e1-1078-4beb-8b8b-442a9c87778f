package com.altomni.apn.talent.service.dto.talent;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentESResponseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer took;

    private Boolean timed_out;

    /*private JSONObject _shards;*/

    private TalentESHitsDTO hits;
}
