package com.altomni.apn.talent.domain.talent;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.talent.service.dto.talent.SpecialTagDTO;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A Tag.
 */
@Entity
@Table(name = "tag")
public class Tag extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tag_name")
    private String tagName;

    @Column(name = "jhi_type")
    private String type;

    @Column(name = "tag_values")
    private String tagValues;

    @Column(name = "description")
    private String description;

    public static Tag fromSpecialTagDTO(SpecialTagDTO dto) {
        Tag tag = new Tag();
        tag.setId(dto.getId());
        tag.setType(dto.getType());
        tag.setTagName(dto.getTagName());
        tag.setDescription(dto.getDescription());
        tag.setTagValues(JsonUtil.toJSONString(dto.getTagValues()));
        return tag;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTagName() {
        return tagName;
    }

    public Tag tagName(String tagName) {
        this.tagName = tagName;
        return this;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getType() {
        return type;
    }

    public Tag type(String type) {
        this.type = type;
        return this;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public Tag description(String description) {
        this.description = description;
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTagValues() {
        return tagValues;
    }

    public void setTagValues(String tagValues) {
        this.tagValues = tagValues;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Tag tag = (Tag) o;
        if (tag.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), tag.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "Tag{" +
                "id=" + id +
                ", tagName='" + tagName + '\'' +
                ", type='" + type + '\'' +
                ", tagValues='" + tagValues + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
