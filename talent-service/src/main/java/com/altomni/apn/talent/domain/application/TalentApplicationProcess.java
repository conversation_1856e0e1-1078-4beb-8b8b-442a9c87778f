package com.altomni.apn.talent.domain.application;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentRecruitmentProcess.
 */
@Entity
@Table(name = "talent_recruitment_process")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentApplicationProcess extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -386368362649529657L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "recruitment_process_id")
    private Long recruitmentProcessId;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "note")
    private String note;

    public static TalentApplicationProcess fromVO(TalentRecruitmentProcessVO talentRecruitmentProcessVO) {
        TalentApplicationProcess result = new TalentApplicationProcess();
        ServiceUtils.myCopyProperties(talentRecruitmentProcessVO, result);
        return result;
    }

    public static TalentRecruitmentProcessVO fromEntity(TalentApplicationProcess talentRecruitmentProcess) {
        TalentRecruitmentProcessVO result = new TalentRecruitmentProcessVO();
        ServiceUtils.myCopyProperties(talentRecruitmentProcess, result);
        return result;
    }

    public static TalentApplicationProcess fromTalentRecruitmentProcessSubmitToJobVO(TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
        TalentApplicationProcess result = new TalentApplicationProcess();
        result.setTenantId(SecurityUtils.getTenantId());
        result.setTalentId(submitToJobVO.getTalentId());
        result.setJobId(submitToJobVO.getJobId());
        result.setRecruitmentProcessId(submitToJobVO.getRecruitmentProcessId());
        result.setNote(submitToJobVO.getNote());
        return result;
    }
}
