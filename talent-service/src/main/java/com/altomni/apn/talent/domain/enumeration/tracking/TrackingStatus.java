package com.altomni.apn.talent.domain.enumeration.tracking;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TrackingCategory enumeration.
 */
public enum TrackingStatus implements ConvertedEnum<Integer> {
    IN_ACTIVE(10),
    NOT_SENT(20),
    SENT(30);


    private final Integer dbValue;

    TrackingStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TrackingStatus, Integer> resolver =
        new ReverseEnumResolver<>(TrackingStatus.class, TrackingStatus::toDbValue);

    public static TrackingStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
