package com.altomni.apn.talent.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.talent.config.env.ApplicationProperties;
import com.altomni.apn.talent.constants.RedisConstants;
import com.altomni.apn.talent.service.talent.TalentCreateByExcelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class ExcelCreateTalentClient {

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private ApplicationProperties applicationProperties;

    private static final Long SLEEP_SECONDS =  30L;

    @Resource
    private TalentCreateByExcelService talentCreateByExcelService;

    @PostConstruct
    public void initExcelCreateTalentClient() {
        //执行任务
        new Thread(() -> {
            int errors = 0;
            int countLarkAlerts = 0;
            while (true) {
                try (Jedis jedis = commonRedisService.getJedisParserInstance()) {
                    while (true) {
                        handler(jedis, 1);
                        errors = 0;
                        countLarkAlerts = 0;
                    }
                } catch (Exception e) {
                    log.error("excel create talent handler is error, msg = {}", ExceptionUtils.getStackTrace(e));
                    try {
                        errors++;
                        TimeUnit.SECONDS.sleep(errors);
                        if (errors % 5 == 0){
                            log.error("redis is abnormal, please go check!");
                            countLarkAlerts++;
                            TimeUnit.SECONDS.sleep(SLEEP_SECONDS * countLarkAlerts);
                        }
                    } catch (InterruptedException ex) {
                        continue;
                    }
                }
            }
        }, "create-talent-by-excel").start();
    }

    private void handler(Jedis jedis, int index) throws InterruptedException {
        List<String> dataList = jedis.lrange(applicationProperties.getCreateTalentByExcelQueue(), -index, -index);
        if (CollUtil.isNotEmpty(dataList)) {
            String taskId = dataList.get(0);
            //只有一个线程可以设置成功,避免多实例的情况下任务争抢。
            mockInternalCall(taskId, jedis);
            if (Objects.equals(setNxAndExpire(jedis , applicationProperties.getCreateTalentByExcelNodeQueue() + taskId , "1200", 1200L), 1L)) {
                //设置过期时间防止服务重启后无法在执行
                log.info("create talent by excel taskId = {} start", taskId);
                talentCreateByExcelService.createTalentByParserResultFromExcel(taskId);
                log.info("create talent by excel taskId = {} finished", taskId);
                jedis.del( applicationProperties.getCreateTalentByExcelNodeQueue() + taskId);
            } else {
                // 有多个节点的时候 处理下一个,知道找不到为止
                handler(jedis, index + 1);
            }
        } else {
            TimeUnit.SECONDS.sleep(10);
        }
    }

    private void mockInternalCall(String taskId, Jedis jedis) {
        String token = jedis.hget(applicationProperties.getCreateTalentByExcelProgress() + taskId + RedisConstants.DATA_KEY_METADATA, RedisConstants.METADATA_KEY_TOKEN);
        if (StrUtil.isBlank(token)) {
            log.error("create talent by excel, token is null, taskId = {}", taskId);
            throw new CustomParameterizedException("create talent by excel is error ,token is null");
        }
        LoginUtil.setSecurityContext(token);
    }

    public static Long setNxAndExpire(Jedis jedis, String key, String value, Long seconds) {
        String script = "local key = KEYS[1]\n" +
                "local value = ARGV[1]\n" +
                "local ttl = tonumber(ARGV[2])\n" +
                "\n" +
                "local exists = redis.call('SETNX', key, value)\n" +
                "\n" +
                "if exists == 1 then\n" +
                "    if ttl > 0 then\n" +
                "        redis.call('EXPIRE', key, ttl)\n" +
                "    end\n" +
                "end\n" +
                "\n" +
                "return exists\n";
        Object result = jedis.eval(script, 1, key, value, String.valueOf(seconds));
        return Long.parseLong(String.valueOf(result));
    }

}
