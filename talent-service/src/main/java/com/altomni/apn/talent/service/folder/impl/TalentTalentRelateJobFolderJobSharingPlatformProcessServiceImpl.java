package com.altomni.apn.talent.service.folder.impl;


import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.folder.JobSharingPlatformTalentRelatedJobFolderRelation;
import com.altomni.apn.common.domain.talent.TalentAssociationJobFolder;
import com.altomni.apn.common.dto.folder.talentrelatejob.SharedLinkDTO;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderRelationDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.repository.talent.JobSharingPlatformRelatedFolderRepository;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.job.jobsharing.JobSharingPublicResponseVO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPlatformDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPlatformRelatedFolderRelationDTO;
import com.altomni.apn.talent.service.folder.TalentRelateJobFolderJobSharingPlatformProcessService;
import com.altomni.apn.talent.service.folder.TalentRelateJobFolderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Optional;

@Slf4j
@Service
public class TalentTalentRelateJobFolderJobSharingPlatformProcessServiceImpl implements TalentRelateJobFolderJobSharingPlatformProcessService {

    @Resource
    private JobSharingPlatformRelatedFolderRepository jobSharingPlatformRelatedFolderRepository;

    @Resource
    private TalentRelateJobFolderService talentRelateJobFolderService;

    public Long processSharingPlatformTalentFolderSubmission(JobSharingPlatformDTO jobSharingPlatformDTO) {
        var jobSharingPlatformRelatedFolderRelationDTO = new JobSharingPlatformRelatedFolderRelationDTO();
        ServiceUtils.myCopyProperties(jobSharingPlatformDTO, jobSharingPlatformRelatedFolderRelationDTO);
        if(jobSharingPlatformRelatedFolderRelationDTO.getRelatedFolderId() == null){
            //call talent service to create the related Folder;
            var talentRelatedJobFolder = new TalentAssociationJobFolder();
//            talentRelatedJobFolder =
        }

        return 1L;
    }


    @Override
    public JobSharingPublicResponseVO getSharingPlatformURLExpirationTime(SharedLinkDTO sharedLinkDTO) {

        if(sharedLinkDTO == null){
            throw new CustomParameterizedException("Invalid Input");
        }
        //todo validate the uuid
        if(StrUtil.isEmpty(sharedLinkDTO.getSharedLinkUUID())) {
            throw new CustomParameterizedException("uuid id cannot be empty!");
        }
        Instant expireTime = jobSharingPlatformRelatedFolderRepository.findExpireTimeByJobSharingPlatformUuid(sharedLinkDTO.getSharedLinkUUID());
        return new JobSharingPublicResponseVO(sharedLinkDTO.getSharedLinkUUID(), expireTime);
    }


    @Override
    @Transactional
    public String updateSharingPlatformURLExpirationTime(String folderId, TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO) {

        validateInput(talentRelateJobFolderRelationDTO, folderId);

        Optional<JobSharingPlatformTalentRelatedJobFolderRelation> relatedFolderRelation = jobSharingPlatformRelatedFolderRepository.findByTalentAssociatedJobFolderFolderId(folderId);
        if(relatedFolderRelation.isEmpty()){
            throw new CustomParameterizedException("Invalid folder");
        }
        JobSharingPlatformTalentRelatedJobFolderRelation relation = relatedFolderRelation.get();
        relation.setSharedLinkExpireTime(talentRelateJobFolderRelationDTO.getSharedLinkExpireTime());
        jobSharingPlatformRelatedFolderRepository.saveAndFlush(relation);

        //todo:
        return folderId;

    }

    @Override
    @Transactional
    public String createOrUpdateJobSharingAndTalentRelateJobFolderRelation(TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO) {


        validateInput(talentRelateJobFolderRelationDTO, null);


        JobSharingPlatformTalentRelatedJobFolderRelation relation = new JobSharingPlatformTalentRelatedJobFolderRelation();
        Optional<JobSharingPlatformTalentRelatedJobFolderRelation> relationOpt = jobSharingPlatformRelatedFolderRepository.findByTalentAssociatedJobFolderFolderId(talentRelateJobFolderRelationDTO.getFolderId());
        if(relationOpt.isPresent()){
            relation = relationOpt.get();
        }else{
            relation.setTalentAssociatedJobFolderFolderId(talentRelateJobFolderRelationDTO.getFolderId());
        }
        relation.setSharedLinkExpireTime(talentRelateJobFolderRelationDTO.getSharedLinkExpireTime());
        var savedRelation = jobSharingPlatformRelatedFolderRepository.saveAndFlush(relation);

        return savedRelation.getTalentAssociatedJobFolderFolderId();

    }

    private void validateInput(TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO, String folderId){
        if(talentRelateJobFolderRelationDTO == null ){
            throw new CustomParameterizedException("Invalid Input");
        }

        if(StringUtils.isBlank(folderId) && StringUtils.isBlank(talentRelateJobFolderRelationDTO.getFolderId())){
            throw new CustomParameterizedException("Invalid input");
        }

        if(Boolean.FALSE.equals(talentRelateJobFolderService.isFolderIdValid(talentRelateJobFolderRelationDTO.getFolderId()))){
            throw new CustomParameterizedException("Invalid Folder Info");
        }
    }
}
