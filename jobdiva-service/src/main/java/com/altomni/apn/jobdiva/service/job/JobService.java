package com.altomni.apn.jobdiva.service.job;

import com.altomni.apn.common.dto.job.JobDTOV3;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;


@Component
@FeignClient(value = "job-service")
public interface JobService {

    @GetMapping("/job/api/v3/jobs/{id}")
    ResponseEntity<JobDTOV3> findById(@PathVariable("id") Long jobId);

    @PutMapping("/job/api/v3/jobs/sync-to-hr/{jobId}")
    void updateJobNeedSyncToHr(@PathVariable("jobId") Long jobId);

}
