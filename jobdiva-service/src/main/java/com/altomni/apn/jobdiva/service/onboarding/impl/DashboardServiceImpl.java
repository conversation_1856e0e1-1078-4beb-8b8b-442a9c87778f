package com.altomni.apn.jobdiva.service.onboarding.impl;

import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.jobdiva.repository.dashboard.DashboardRepository;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardDocumentResDto;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardPackageResDto;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardSearchDto;
import com.altomni.apn.jobdiva.service.onboarding.DashboardService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * dashboard implementation
 * <AUTHOR>
 */
@Service("dashboardService")
public class DashboardServiceImpl implements DashboardService {

    @Resource
    private DashboardRepository dashboardRepository;

    @Override
    public Page<DashboardDocumentResDto> findDocumentsPageBySearchDtoAndPage(DashboardSearchDto searchDto) {
        //check page and init
        Pageable pageable = checkPageAndInit(searchDto);
        return dashboardRepository.findDocumentsPageBySearchDtoAndPage(searchDto, pageable);
    }


    @Override
    public Page<DashboardPackageResDto> findPackagesPageBySearchDtoAndPage(DashboardSearchDto searchDto) {
        Pageable pageable = checkPageAndInit(searchDto);
        return dashboardRepository.findPackagesPageBySearchDtoAndPage(searchDto, pageable);
    }

    private Pageable checkPageAndInit(DashboardSearchDto searchDto) {
        //default sort
        List<Sort.Order> orders= new ArrayList<>();
        orders.add(new Sort.Order(Sort.Direction.DESC, "package_status"));
        orders.add(new Sort.Order(Sort.Direction.ASC, "package_assigned_on"));
        Pageable pageable;
        if (searchDto == null) {
            pageable = PageRequest.of(0, 25, Sort.by(orders));
        } else {
            SearchSortDTO sort = searchDto.getSort();
            if (sort == null) {
                pageable = PageRequest.of(searchDto.getPageNumber(), searchDto.getPageSize(), Sort.by(orders));
            } else {
                pageable = PageRequest.of(searchDto.getPageNumber(), searchDto.getPageSize(), Sort.by(Sort.Direction.fromString(sort.getDirection()), sort.getProperty()));
            }
        }
        return pageable;
    }

}
