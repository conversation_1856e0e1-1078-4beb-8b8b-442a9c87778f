package com.altomni.apn.jobdiva.repository.settings;

import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingPackages;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackagesDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * Spring Data JPA repository for the OnBoardingPackages entity.
 */
@Repository
public interface OnBoardingPackagesRepository extends JpaRepository<OnBoardingPackages, Long> {

    Optional<OnBoardingPackages> findAllByIdAndTenantIdAndActivated(Long id, Long tenantId, Boolean activated);

    @Query(value = "select count(p.id) from OnBoardingPackages p where p.name = ?1 and p.tenantId = ?2 and p.activated = true ")
    Integer countByNameAndTenantId(String name, Long tenantId);

    @Query(value = "select count(p.id) from OnBoardingPackages p where p.id<>?1 and p.name = ?2 and p.tenantId = ?3 and p.activated = true ")
    Integer countByPackageIdAndNameAndTenantId(Long packageId, String name, Long tenantId);

    @Query(value = "select new com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackagesDTO(p.id, p.name, p.description, u.username, p.lastModifiedDate) " +
        " from OnBoardingPackages p left join User u on p.lastModifiedBy = u.uid where p.activated = ?1 and p.tenantId = ?2 order by p.lastModifiedDate desc ")
    List<OnBoardingPackagesDTO> findAllByActivatedAndTenantIdOrderByLastModifiedDateDesc(Boolean activated, Long tenantId);
}
