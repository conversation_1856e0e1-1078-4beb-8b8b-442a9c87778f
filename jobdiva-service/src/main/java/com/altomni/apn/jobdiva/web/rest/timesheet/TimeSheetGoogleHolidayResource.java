package com.altomni.apn.jobdiva.web.rest.timesheet;


import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorGroupInvoiceEditDTO;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorGroupInvoiceSearchDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetRecordAmService;
import com.altomni.apn.jobdiva.service.timesheet.TimesheetGoogleHolidayService;
import com.altomni.apn.jobdiva.service.vo.invoice.ContractorGroupInvoiceListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.EndingDateListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimesheetGoogleHolidayVO;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@RestController
@RequestMapping("/api/v3/timesheet")
public class TimeSheetGoogleHolidayResource {

    @Resource
    private TimesheetGoogleHolidayService timesheetGoogleHolidayService;

    /**
     * 保存Holiday
     * @param dto
     * @return
     */
    @PostMapping("/google-holiday/save")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Map<String, Object>> save(@RequestBody List<TimesheetGoogleHolidayDTO> dto) {
        log.info("[timesheetGoogleHoliday: User @{}] save time sheet Holiday, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        return ResponseEntity.ok(timesheetGoogleHolidayService.insertRecord(dto));
    }


    @PostMapping("/google-holiday/search")
    @Timed
    public ResponseEntity<List<TimesheetGoogleHolidayVO>> search(@PageableDefault Pageable pageable) {
        log.info("[timesheetGoogleHoliday: User @{}] REST search google holiday list:", SecurityUtils.getUserId());
        Page<TimesheetGoogleHolidayVO> timesheetGoogleHolidayVOS = timesheetGoogleHolidayService.findHolidayList(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(timesheetGoogleHolidayVOS, "/api/v3/timesheet/googleHoliday/search");
        return ResponseEntity.ok().headers(headers).body(timesheetGoogleHolidayVOS.getContent());
    }

    @PutMapping("/google-holiday/modify")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity modify(@RequestBody TimesheetGoogleHolidayDTO dto) {
        log.info("[timesheetGoogleHoliday: User @{}] REST google holiday modify method:", SecurityUtils.getUserId());
        timesheetGoogleHolidayService.modify(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PutMapping("/google-holiday/delete/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity delete(@PathVariable Long id) {
        log.info("[timesheetGoogleHoliday: User @{}] REST google holiday delete method:", SecurityUtils.getUserId());
        timesheetGoogleHolidayService.deleteById(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * 保存Holiday
     * @param dto
     * @return
     */
    @PostMapping("/google-holiday/exist")
    @Timed
    public ResponseEntity<TimesheetGoogleHolidayVO> exist(@RequestBody TimesheetGoogleHolidayDTO dto) {
        log.info("[timesheetGoogleHoliday: User @{}] save time sheet Holiday, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        return ResponseEntity.ok().body(timesheetGoogleHolidayService.exist(dto));
    }
}
