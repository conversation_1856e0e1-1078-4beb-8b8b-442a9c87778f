package com.altomni.apn.jobdiva.service.vo.invoice;

import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.jobdiva.domain.invoice.TGroupInvoice;
import com.altomni.apn.jobdiva.domain.invoice.TRecordPaymentInfo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel(description = "ContractorGroupInvoiceViewVO")
public class ContractorGroupInvoiceViewVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigInteger id;
    private String groupInvoiceNumber;
    private String companyName;
    private BigInteger companyId;
    private String companyContact;
    private LocalDate invoiceDate;
    private String invoiceType;
    private String invoiceStatus;
    private Timestamp paymentDueDate;
    private BigDecimal amountDue;
    private String note;
    private BigDecimal totalAmount;
    private String sentBy;
    private Timestamp sendOn;
    private String currency;
    private Integer dueDate;
    private BigInteger assignmentId;
    private LocalDate weekEndingDate;
    private String clientLocation ;

    private String clientAddress ;

    private String clientName ;

    List<List<ContractorGroupInvoiceDetailVO>> invoiceList;

    List<PaymentRecordVO> paymentRecordList;


    public static ContractorGroupInvoiceViewVO transformToVo(TGroupInvoice source) {
        ContractorGroupInvoiceViewVO vo = new ContractorGroupInvoiceViewVO();
        ServiceUtils.myCopyProperties(source, vo);
        vo.setInvoiceType(source.getInvoiceType().name());
        vo.setInvoiceStatus(source.getGroupInvoiceStatus().name());
        vo.setInvoiceDate(source.getInvoiceDate());
        vo.setGroupInvoiceNumber(source.getGroupNumber());
        vo.setCompanyName(source.getCompanyName());
        vo.setPaymentDueDate(source.getPaymentDueDate());
        vo.setTotalAmount(source.getInvoiceAmount());
        vo.setSentBy(source.getOperatedByName());
        vo.setSendOn(source.getOperatedDate());
        vo.setDueDate(source.getDueWithinDays());
        vo.setCompanyId(source.getCompanyId());
        return vo;
    }

    public static List<PaymentRecordVO> transformToPaymentVO(List<TRecordPaymentInfo> recordPaymentInfos) {
        List<PaymentRecordVO> paymentRecordVOList = new ArrayList<>();
        if (!recordPaymentInfos.isEmpty()) {
            for (TRecordPaymentInfo t : recordPaymentInfos) {
                PaymentRecordVO bean = new PaymentRecordVO();
                bean.setNote(t.getNote());
                bean.setPaymentDate(t.getPaymentDate());
                bean.setPaymentMethod(t.getPaymentMethod().name());
                bean.setPaymentAmount(t.getPaymentAmount());
                bean.setId(t.getId());
                paymentRecordVOList.add(bean);
            }
        }
        return paymentRecordVOList;
    }
}
