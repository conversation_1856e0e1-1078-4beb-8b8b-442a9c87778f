package com.altomni.apn.jobdiva.service.vo.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentStatusType;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;

import static com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus.MISSING;
import static com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus.NO_RECORD;

@ApiModel
@Data
public class AssignmentGeneralInfoVO {

    protected Long id;

    protected LocalDate startDate;

    protected LocalDate endDate;

    protected Long startId;

    protected Boolean isClockIn;

    protected Long talentId;

    protected AssignmentType type;

    protected String jobTitle;

    protected String companyName;

    protected String createdBy;

    protected Instant createdTime;

    private Integer assignmentCount;

    private AssignmentStatusType status;

    private TimeSheetStatus timeSheetStatus;

    private Float workingHours;

    private Integer isWeekEnd;

    public AssignmentGeneralInfoVO(Long id, LocalDate startDate, LocalDate endDate, Long startId, Long record, Long talentId, AssignmentType type, String jobTitle, String companyName, String createdBy, Instant createdTime, AssignmentStatusType status, TimeSheetStatus timeSheetStatus)
    {
        this.id = id;
        this.startDate = startDate;
        this.endDate = endDate;
        this.startId = startId;
        this.talentId = talentId;
        this.type = type;
        this.jobTitle = jobTitle;
        this.companyName = companyName;
        this.createdBy = createdBy;
        this.createdTime = createdTime;
        this.status = status;
        this.timeSheetStatus = timeSheetStatus;
        this.isClockIn = record != null && (timeSheetStatus != null && (timeSheetStatus != NO_RECORD && timeSheetStatus != MISSING));
    }

    public AssignmentGeneralInfoVO(Long id, LocalDate startDate, LocalDate endDate, Long startId, Long record, Long talentId, AssignmentType type, String jobTitle, String companyName, String createdBy, Instant createdTime, AssignmentStatusType status, TimeSheetStatus timeSheetStatus, Float workingHours, Integer isWeekEnd)
    {
        this.id = id;
        this.startDate = startDate;
        this.endDate = endDate;
        this.startId = startId;
        this.talentId = talentId;
        this.type = type;
        this.jobTitle = jobTitle;
        this.companyName = companyName;
        this.createdBy = createdBy;
        this.createdTime = createdTime;
        this.status = status;
        this.timeSheetStatus = timeSheetStatus;
        this.isClockIn = record != null && (timeSheetStatus != null && (timeSheetStatus != NO_RECORD && timeSheetStatus != MISSING));
        this.workingHours = workingHours;
        this.isWeekEnd = isWeekEnd;
    }

    public AssignmentGeneralInfoVO()
    {
        assignmentCount = 0;
    }
}
