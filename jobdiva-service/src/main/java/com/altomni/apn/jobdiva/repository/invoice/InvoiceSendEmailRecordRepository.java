package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.TInvoiceSendEmailRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

/**
 * send email record repository
 */
@Repository
public interface InvoiceSendEmailRecordRepository extends JpaRepository<TInvoiceSendEmailRecord, BigInteger> {
}
