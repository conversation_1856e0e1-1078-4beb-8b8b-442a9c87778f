package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.vo.timesheet.EndingDateListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ExpenseRecordAmService extends BaseService {

    List<BreakTimeDTO> findRecords(RecordSearchDTO dto);

    BreakTimeDTO findRecordById(RecordSearchDTO dto);


    SummaryDataVO search(AdvanceSearchDTO dto);

    void downloadSummary(HttpServletResponse response, SummaryQueryDTO dto);

    void downloadReceipts(HttpServletResponse response, SummaryQueryDTO dto);

    Integer expenseApprove(ApproveDTO dto);

    Integer saveExpenseTime(BreakTimeDTO dto);

    List<EndingDateListVO> weekEndingDates(Long talentId);
}
