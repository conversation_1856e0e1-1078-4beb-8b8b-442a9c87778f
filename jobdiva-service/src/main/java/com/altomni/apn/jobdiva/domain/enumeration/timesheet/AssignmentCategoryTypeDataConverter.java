package com.altomni.apn.jobdiva.domain.enumeration.timesheet;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentCategoryType;

public class AssignmentCategoryTypeDataConverter implements Converter<AssignmentCategoryType> {

    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public AssignmentCategoryType convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public CellData convertToExcelData(AssignmentCategoryType categoryType, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (categoryType == null) {
            return new CellData();
        }
        return new CellData(categoryType.getDescription());
    }

}