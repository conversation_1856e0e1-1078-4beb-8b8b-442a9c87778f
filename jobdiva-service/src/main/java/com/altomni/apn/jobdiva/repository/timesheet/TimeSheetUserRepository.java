package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface TimeSheetUserRepository extends JpaRepository<TimeSheetUser, Long> {

    @EntityGraph(attributePaths = "roles")
    @Query("select user from TimeSheetUser user where user.username = ?1 or user.email = ?1")
    TimeSheetUser findByUsernameOrEmail(String username);

    @EntityGraph(attributePaths = "roles")
    TimeSheetUser findByUid(String uid);

    @Query(value = " select t.* from time_sheet_user t where SUBSTRING_INDEX(t.uid,'_',1) = ?1 and t.user_type = ?2 ", nativeQuery = true)
    TimeSheetUser findByUidLikeAndUserType(Long talentId, Integer userType);

    @Query(value = " select first_name from talent t where id = ?1 ", nativeQuery = true)
    String findByTalentId(Long talentId);

    @Query(value = " select full_name from talent t where id = ?1 ", nativeQuery = true)
    String findFullNameByTalentId(Long talentId);

    @EntityGraph(attributePaths = "roles")
    TimeSheetUser findByEmail(String email);

    @EntityGraph(attributePaths = "roles")
    TimeSheetUser findByUsername(String username);

    @EntityGraph(attributePaths = "roles")
    TimeSheetUser findByUidAndUserType(String uid, TimeSheetUserType type);

    @EntityGraph(attributePaths = "roles")
    @Query("select user from TimeSheetUser user where user.uid = :login or user.username = :login or user.email = :login")
    TimeSheetUser findUserWithAuthorities(@Param("login") String login);

    @EntityGraph(attributePaths = "roles")
    TimeSheetUser findOneWithRolesByUsername(String username);

    @Query("select user from TimeSheetUser user where (user.username in :emails or user.email in :emails) and user.tenantId = :tenantId and user.userType = :userType")
    List<TimeSheetUser> findAllByUsernameInOrEmailIn(@Param("emails") Set<String> emails,@Param("tenantId") Long tenantId,@Param("userType") TimeSheetUserType userType);

    @Query(value = " select count(1) from company_sales_lead_client_contact where approver_id = ?1 and status = true", nativeQuery = true)
    Integer countActiveClient(Long approverId);
}
