package com.altomni.apn.jobdiva.config;

import com.altomni.apn.common.auth.agency_auth.AgencyTokenFilter;
import com.altomni.apn.common.auth.agency_auth.AgencyUserTokenStore;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetTokenFilter;
import com.altomni.apn.common.auth.SkipOAuthTokenResolver;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetUserTokenStore;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.ProviderManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationEntryPoint;
import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;

/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class WebSecurityConfig {

    private final TimesheetUserTokenStore timesheetUserTokenStore;
    private final UserDetailsService userDetailsService;

    private static final String[] PUBLIC_ENDPOINTS = new String[]{
            "/actuator/**",
            "/api/v3/timesheet/user/login",
            "/api/v3/timesheet/user/forgetPass",
            "/api/v3/timesheet/user/renewPassword",
            "/api/v3/settings/liveness"
    };

    private static final String[] AGENCY_ENDPOINTS = {
            "/**"
//            "/api/v3/timesheet/user/findByUsernameOrEmailList"
    };

    private final AgencyUserTokenStore agencyUserTokenStore;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.oauth2ResourceServer().opaqueToken(Customizer.withDefaults());

        // 没有 token 或者 token 过期，返回 401
        http.exceptionHandling(exceptionHandling -> exceptionHandling.authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint()));

        // 无状态 session
        http.sessionManagement((session) -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        http.csrf().disable()
                .authorizeRequests()
                .requestMatchers(PUBLIC_ENDPOINTS).permitAll()
                .anyRequest().authenticated();

        http.addFilterBefore(agencyTokenFilter(agencyUserTokenStore), BearerTokenAuthenticationFilter.class);
        http.addFilterBefore(timesheetTokenFilter(timesheetUserTokenStore), BearerTokenAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public AuthenticationManager authenticationManager() {
        DaoAuthenticationProvider daoAuthenticationProvider = new DaoAuthenticationProvider();
        daoAuthenticationProvider.setUserDetailsService(userDetailsService);
        daoAuthenticationProvider.setPasswordEncoder(passwordEncoder());
        return new ProviderManager(daoAuthenticationProvider);
    }

    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        return web -> web.ignoring().requestMatchers(PUBLIC_ENDPOINTS);
    }

    @Bean
    public BearerTokenResolver bearerTokenResolver() {
        return new SkipOAuthTokenResolver();
    }

    public TimesheetTokenFilter timesheetTokenFilter(TimesheetUserTokenStore timesheetUserTokenStore) {
        return new TimesheetTokenFilter(timesheetUserTokenStore, "/**");
    }

    public AgencyTokenFilter agencyTokenFilter(AgencyUserTokenStore agencyUserTokenStore) {
        return new AgencyTokenFilter(agencyUserTokenStore, AGENCY_ENDPOINTS);
    }
}

