package com.altomni.apn.jobdiva.util;

import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import com.altomni.apn.common.utils.ChineseCharacterCheckerUtil;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorInvoicePrintDTO;
import com.altomni.apn.jobdiva.service.vo.invoice.ContractorGroupInvoiceDetailVO;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.altomni.apn.common.utils.DateUtil.MM_DD_YYYY;

@Slf4j
public class InvoicePdfUtil {


    public static final String ROUTING_NO_TITLE = "*Routing Number: ";

    public static final String ROUTING_NO = "*********";

    public static final String ACCOUNT_NO_TITLE = "*Account Number: ";

    public static final String ACCOUNT_NO = "************";

    public static final String ACCOUNT_NAME_TITLE = "*Account Name: ";

    public static final String ACCOUNT_NAME = "Intellipro Group Inc.";

    public static final String BANK_ADDRESS_TITLE = "*Bank Address: ";

    public static final String BANK_ADDRESS_1 = "2900 S. EI Camino Real,";

    public static final String BANK_ADDRESS_2 = "San Mateo CA 94403";

    public static final String NOTE = "Payment due within 30 days";

    public static final Font FONT_BOLD_9 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 9, BaseColor.BLACK);
    public static final Font FONT_BOLD_11 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 11, BaseColor.BLACK);
    public static final Font FONT_BOLD_8_BLACK = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 8, BaseColor.BLACK);
    public static final Font FONT_BOLD_8_WHITE = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 8, BaseColor.WHITE);
    public static final Font FONT_REGULAR_8 = FontFactory.getFont(FontFactory.HELVETICA, 8, BaseColor.BLACK);
    //public final Font FONT_CHINESE = FontFactory.getFont(getClass().getClassLoader().getResource("NotoSansCJKsc-Regular.otf").getFile(), BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);
    public static final Font FONT_CHINESE = FontFactory.getFont("simsun.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);

    public static final Font FONT_ARIAL = FontFactory.getFont("arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);

    private static final Font FONT_NOTO_SANS = FontFactory.getFont("NotoSansThai-VariableFont_wdth,wght.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);


    public static final String NEGATIVE = "-";

    public static final String CA_BANK_NAME_TITLE = "Bank Name: ";
    public static final String CA_BANK_NAME = "RBC Royal Bank of Canada";
    public static final String CA_ACCOUNT_NAME_TITLE = "Account Name: ";
    public static final String CA_ACCOUNT_NAME = "Intellipro Technologies Canada Inc.";
    public static final String CA_ROUTING_NO_TITLE = "ABA/Routing Number: ";
    public static final String CA_ROUTING_NO = "01457";
    public static final String CA_BANK_CODE_TITLE = "Bank Code: ";
    public static final String CA_BANK_CODE = "003";

    public static final String CA_ACCOUNT_NO_TITLE = "Account Number: ";
    public static final String CA_ACCOUNT_NO = "1006055";
    public static final String CA_INSTITUTION_NO_TITLE = "Institution Number: ";
    public static final String CA_INSTITUTION_NO = "003";
    public static final String CA_SWIFT_CODE_TITLE = "Swift Code: ";
    public static final String CA_SWIFT_CODE = "ROYCCAT2";
    public static final String CA_BANK_ADDRESS_TITLE = "Bank Address: ";
    public static final String CA_BANK_ADDRESS = "110-4000 No. 3 Road Richmond, BC V6X";


    public static final String LOGO_PATH = "images/intelliprogroup-logo.png";

    public static final String LOGO_PATH_SLASH = "/images/intelliprogroup-logo.png";

    public static final String VOID_PATH = "images/invoice-void.png";

    public static final String VOID_PATH_SLASH = "/images/invoice-void.png";

    public static void main(String[] args) throws Exception {
        String a = StringUtils.leftPad("1", 6, "0");
        System.out.println(a);
       /* String path = "/Users/<USER>/Documents/222.pdf";
        File sourcePdf = new File(path);
        sourcePdf.createNewFile();
        FileOutputStream out = new FileOutputStream(sourcePdf);
        Document document = new Document(PageSize.A4, 0, 0, 60, 0);
        PdfWriter.getInstance(document, out);
        document.open();
        ContractorInvoicePrintDTO dto = new ContractorInvoicePrintDTO();
        dto.setInvoiceDate(LocalDate.now());
        dto.setCustomerName("TEST");
        dto.setInvoiceNumber("C0000000001");
        dto.setPoNumber("10000000");
        dto.setCustomerAddress("San Mateo, CA 94403");
        dto.setCurrency(1);
        dto.setDueDate(10);
        dto.setAmountDue(new BigDecimal("2578.16"));
        List<List<ContractorGroupInvoiceDetailVO>> invoiceList = new ArrayList<>();
        List<ContractorGroupInvoiceDetailVO> voList = new ArrayList<>();
        ContractorGroupInvoiceDetailVO vo = new ContractorGroupInvoiceDetailVO();
        vo.setTotalAmount(new BigDecimal("200.99"));
        vo.setTimePeriod("From 12/26/2022 To 01/01/2023");
        vo.setUnit("Hour");
        vo.setBillRate("$32.33");
        vo.setItemDescription("Johann Cyril");
        vo.setQuantity(new BigDecimal("40"));
        vo.setCurrency("1");
        voList.add(vo);

        vo = new ContractorGroupInvoiceDetailVO();
        vo.setTotalAmount(new BigDecimal("200.99"));
        vo.setUnit("Hour");
        vo.setBillRate("$32.33");
        vo.setItemDescription("OT Hours in excess of standard for Jaswant Singh");
        vo.setQuantity(new BigDecimal("40"));
        vo.setCurrency("1");
        voList.add(vo);
        invoiceList.add(voList);
        dto.setInvoiceList(invoiceList);
        createInvoicePdfForCanada(document, dto);
        document.close();*/
    }


    public void createInvoicePdfForUS(Document document, ContractorInvoicePrintDTO dto, Boolean monthly) throws DocumentException {
        document.newPage();
        PdfPTable table = new PdfPTable(new float[]{1, 1, 1, 1, 1.5F, 1, 1});
        table.setWidthPercentage(90);
        table.setSpacingBefore(5f);
        table.setSpacingAfter(5f);

        // logo
        Image img = readImage(LOGO_PATH, LOGO_PATH_SLASH);
        PdfPCell cell;
        if (img != null) {
            cell = new PdfPCell(img, true);
        } else {
            cell = new PdfPCell(new Phrase("Intellipro Group"));
        }
        cell.setColspan(2);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // invoice before space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Invoice No
        cell = new PdfPCell(new Phrase("Invoice No." + dto.getInvoiceNumber(), FONT_BOLD_11));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // invoice after space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Name and Address
        cell = new PdfPCell(new Phrase("Customer Name and Address", FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // between Customer and Remit space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Remit To
        cell = new PdfPCell(new Phrase("Remit To", FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Name
        cell = new PdfPCell(new Phrase(dto.getCustomerName(), FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // between Customer Came and Please space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Please make payment via direct deposit to:
        cell = new PdfPCell(new Phrase("Please make payment via wire transfer to:", FONT_REGULAR_8));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Address
        cell = new PdfPCell(new Paragraph(dto.getCustomerAddress() + ", " + dto.getCustomerLocation(), FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // between Customer Address and Bank space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Bank of America
        cell = new PdfPCell(new Phrase("Bank of America", FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Contact # title
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("Customer Contact #: ", FONT_BOLD_8_BLACK));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Customer Contact # value
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase(dto.getCustomerContact(), FONT_CHINESE));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Account NO Title
        cell = new PdfPCell(new Phrase(ACCOUNT_NO_TITLE, FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Account #
        cell = new PdfPCell(new Phrase(ACCOUNT_NO, FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Invoice Date title
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("Invoice Date: ", FONT_BOLD_8_BLACK));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Invoice Date value
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase(DateTimeFormatter.ofPattern(MM_DD_YYYY).format(dto.getInvoiceDate()), FONT_REGULAR_8));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // ROUTING name
        cell = new PdfPCell(new Phrase(ROUTING_NO_TITLE, FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(ROUTING_NO, FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // PO # title
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("P.O.#: ", FONT_BOLD_8_BLACK));
        }
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        cell.setColspan(2);
        table.addCell(cell);
        // PO # value
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase(dto.getPoNumber(), FONT_BOLD_8_BLACK));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Account #
        cell = new PdfPCell(new Phrase(ACCOUNT_NAME_TITLE, FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(ACCOUNT_NAME, FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        // Customer Reference
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("Customer Reference #:", FONT_BOLD_8_BLACK));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Customer Reference # value
        cell = new PdfPCell(new Phrase("", FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Bank Address first part
        cell = new PdfPCell(new Phrase(BANK_ADDRESS_TITLE, FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(BANK_ADDRESS_1, FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Bank Address second part
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(BANK_ADDRESS_2, FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase("*Please make payment via check to:", FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(ACCOUNT_NAME, FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Address first
        cell = new PdfPCell(new Phrase("Address:", FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        /*if (null == dto.getCreatedDate()) {
            cell = new PdfPCell(new Phrase("160 E Tasman Drive, Suite 200-15. San Jose, CA 95134", FONT_BOLD_8_BLACK));
        } else {
            // 定义目标日期
            LocalDate targetDate = LocalDate.of(2025, 3, 6);
            if (dto.getCreatedDate().isBefore(targetDate)) {
                cell = new PdfPCell(new Phrase("3120 Scott Blvd, #301, Santa Clara, CA, 95054", FONT_BOLD_8_BLACK));
            } else {
                cell = new PdfPCell(new Phrase("160 E Tasman Drive, Suite 200-15. San Jose, CA 95134", FONT_BOLD_8_BLACK));
            }
        }*/
        cell = new PdfPCell(new Phrase("160 E Tasman Drive, Suite 200-15. San Jose, CA 95134", FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // before Item Description space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell(new Phrase("Customer Contact", FONT_BOLD_8_BLACK));
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell(new Phrase("Invoice Date", FONT_BOLD_8_BLACK));
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            //values
            cell = new PdfPCell(new Phrase(" "));
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell(new Phrase(dto.getCustomerContact(), FONT_CHINESE));
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell(new Phrase(DateTimeFormatter.ofPattern(MM_DD_YYYY).format(dto.getInvoiceDate()), FONT_BOLD_8_BLACK));
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        }

        // Quantity title
        cell = new PdfPCell(new Phrase("Quantity", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        //Item Description title
        cell = new PdfPCell(new Phrase("Item Description", FONT_BOLD_8_WHITE));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Time Period
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("Time Period", FONT_BOLD_8_WHITE));
        }
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Billing Rate title
        cell = new PdfPCell(new Phrase("Billing Rate", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Unit title
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("Unit", FONT_BOLD_8_WHITE));
        }
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Amount Due title
        cell = new PdfPCell(new Phrase("Amount Due", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // before Item Description value space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        for (int i = 0; i < dto.getInvoiceList().size(); i++) {
            List<ContractorGroupInvoiceDetailVO> list = dto.getInvoiceList().get(i);
            int x = 0;
            String to = "";
            for (ContractorGroupInvoiceDetailVO vo : list) {
                // Quantity title
                cell = new PdfPCell(new Phrase(null == vo.getQuantity() || vo.getQuantity().equals(BigDecimal.ZERO) ? " " : vo.getQuantity() + "", FONT_CHINESE));
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setPaddingLeft(10f);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                cell.setBorder(PdfPCell.NO_BORDER);
                table.addCell(cell);

                //Item Description title
                if (ChineseCharacterCheckerUtil.containsChineseCharacters(vo.getItemDescription())) {
                    cell = new PdfPCell(new Phrase(vo.getItemDescription(), FONT_CHINESE));
                } else {
                    cell = new PdfPCell(new Phrase(vo.getItemDescription(), FONT_ARIAL));
                }
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setPaddingLeft(10f);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                cell.setBorder(PdfPCell.NO_BORDER);
                table.addCell(cell);

                // Time Period
                if (monthly) {
                    if (vo.getInvoiceType() != null) {
                        if (vo.getInvoiceType().equals(InvoiceType.EXPENSE.toDbValue().toString())) {
                            if (vo.getTimePeriod().indexOf("To") != -1 && vo.getTimePeriod().indexOf("From") != -1) {
                                cell = new PdfPCell(new Phrase(vo.getFrom(), FONT_CHINESE));
                            } else {
                                cell = new PdfPCell(new Phrase(vo.getTimePeriod(), FONT_CHINESE));
                            }
                        } else {
                            cell = new PdfPCell(new Phrase(vo.getFrom(), FONT_CHINESE));
                        }
                    } else {
                        cell = new PdfPCell(new Phrase(vo.getFrom(), FONT_CHINESE));
                    }
                } else {
                    cell = new PdfPCell(new Phrase(vo.getTimePeriod(), FONT_CHINESE));
                }
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                table.addCell(cell);

                // Billing Rate title
                cell = StringUtils.isBlank(vo.getBillRate()) || vo.getBillRate().equals("0.00") ? new PdfPCell(new Phrase("", FONT_CHINESE)) : constructCell(dto.getCurrency(), new BigDecimal(vo.getBillRate()), FONT_ARIAL, true);
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                table.addCell(cell);

                // Unit title
                cell = new PdfPCell(new Phrase(vo.getUnit(), FONT_CHINESE));
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                table.addCell(cell);

                // Amount Due title
                cell = constructCell(vo.getCurrency(), vo.getTotalAmount(), FONT_ARIAL, true);
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                table.addCell(cell);

                //monthly 空一行增加 to
                if (monthly && x == 0) {
                    to = vo.getTo();
                }
                x++;
            }

            //增加ponumber 和 customer reference
            if (monthly) {
                addMonthlyData(table, "", "Customer Reference #");
                addMonthlyData(table, to, "PO # " + (StringUtils.isBlank(dto.getPoNumber()) ? "" : dto.getPoNumber()));
            }
        }

        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // before total amount above space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.TOP);
        cell.setBorderWidth(1.5F);
        cell.setBorderColor(new BaseColor(238, 238, 238));
        table.addCell(cell);


        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Subtotal title
        cell = new PdfPCell(new Phrase("Subtotal: ", FONT_BOLD_9));
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setRowspan(2);
        cell.setColspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Subtotal value
        if (dto.getAmountDue().compareTo(new BigDecimal("0.00")) == 0) {
            cell = new PdfPCell(new Phrase("0", FONT_ARIAL));
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setRowspan(2);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        } else {
            cell = constructCell(dto.getCurrency(), dto.getAmountDue(), FONT_ARIAL, true);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setRowspan(2);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        }

        String payment = " ";
        if (dto.getDueDate() != null) {
            payment = "Payment due within " + dto.getDueDate() + " days";
        }
        // Payment Due within 30 days
        cell = new PdfPCell(new Phrase(payment, FONT_BOLD_8_BLACK));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // total amount title
        cell = new PdfPCell(new Phrase("Total Amount: ", FONT_BOLD_9));
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setRowspan(2);
        cell.setColspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // total amount value
        if (dto.getAmountDue().compareTo(new BigDecimal("0.00")) == 0) {
            cell = new PdfPCell(new Phrase("0", FONT_ARIAL));
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setRowspan(2);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        } else {
            cell = constructCell(dto.getCurrency(), dto.getAmountDue(), FONT_ARIAL, true);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setRowspan(2);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        }


        // after total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        document.add(table);
    }

    private void addMonthlyData(PdfPTable table, String to, String description) {
        // Quantity title
        PdfPCell cell = new PdfPCell(new Phrase(""));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setPaddingLeft(10f);
        cell.setPaddingBottom(6f);
        cell.setPaddingTop(6f);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        //Item Description title
        cell = new PdfPCell(new Phrase(StringUtils.isBlank(description) ? "" : description, FONT_CHINESE));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setPaddingBottom(6f);
        cell.setPaddingTop(6f);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Time Period
        cell = new PdfPCell(new Phrase(StringUtils.isBlank(to) ? "" : to, FONT_CHINESE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        cell.setPaddingBottom(6f);
        cell.setPaddingTop(6f);
        table.addCell(cell);

        // Billing Rate title
        cell = new PdfPCell(new Phrase(""));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        cell.setPaddingBottom(6f);
        cell.setPaddingTop(6f);
        table.addCell(cell);

        // Unit title
        cell = new PdfPCell(new Phrase(""));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        cell.setPaddingBottom(6f);
        cell.setPaddingTop(6f);
        table.addCell(cell);

        // Amount Due title
        cell = new PdfPCell(new Phrase(""));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        cell.setPaddingBottom(6f);
        cell.setPaddingTop(6f);
        table.addCell(cell);
    }

    public void addWatermarkForVoidInvoice(PdfReader reader, PdfStamper stamper) throws IOException, DocumentException {
        // image watermark
        Image img = readImage(VOID_PATH, VOID_PATH_SLASH);
        float w = img.getScaledWidth();
        float h = img.getScaledHeight();
        // transparency
        PdfGState gs1 = new PdfGState();
        gs1.setFillOpacity(0.5f);
        // properties
        PdfContentByte over;
        over = stamper.getOverContent(1);
        over.saveState();
        over.setGState(gs1);
        over.addImage(img, w, 0, 0, h, (595.0F / 2) - (w / 2), (842.0F / 2) - (h / 2));
        over.restoreState();
        stamper.close();
        reader.close();
    }

    public Image readImage(String imagePath, String imagePathSlash) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        Image image = null;
        try {
            File folderInput = new File(imagePath);
            log.debug("[PdfUtil.readImage][{}] read by File: {}", imagePath, folderInput.exists());
            if (folderInput.exists()) {
                BufferedImage folderImage = ImageIO.read(folderInput);
                ImageIO.write(folderImage, "png", out);
                byte[] byteArray = out.toByteArray();
                image = Image.getInstance(byteArray);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by File error.", imagePath);
        }

        try {
            ClassPathResource imgFile = new ClassPathResource(imagePath);
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource: ", imagePath, imgFile.exists());
            if (imgFile.exists()) {
                byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
                image = Image.getInstance(bytes);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource error.", imagePath);
        }

        try {
            URL url = InvoicePdfUtil.class.getClassLoader().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() error.", imagePath);
        }

        try {
            URL url = this.getClass().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() error.", imagePath);
        }

        try {
            URL url = Thread.currentThread().getContextClassLoader().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() error.", imagePath);
        }

        try {
            URL url = ClassLoader.getSystemClassLoader().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() error.", imagePath);
        }

        // ********************************imagePathSlash************************************/

        try {
            File folderInput = new File(imagePathSlash);
            log.debug("[PdfUtil.readImage][{}] read by File: {}", imagePathSlash, folderInput.exists());
            if (folderInput.exists()) {
                BufferedImage folderImage = ImageIO.read(folderInput);
                ImageIO.write(folderImage, "png", out);
                byte[] byteArray = out.toByteArray();
                image = Image.getInstance(byteArray);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by File error.", imagePathSlash);
        }

        try {
            ClassPathResource imgFile = new ClassPathResource(imagePathSlash);
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource: ", imagePathSlash, imgFile.exists());
            if (imgFile.exists()) {
                byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
                image = Image.getInstance(bytes);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource error.", imagePathSlash);
        }

        try {
            URL url = InvoicePdfUtil.class.getClassLoader().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() error.", imagePathSlash);
        }

        try {
            URL url = this.getClass().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() error.", imagePathSlash);
        }

        try {
            URL url = Thread.currentThread().getContextClassLoader().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() error.", imagePathSlash);
        }

        try {
            URL url = ClassLoader.getSystemClassLoader().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() error.", imagePathSlash);
        }


        try {
            out.close();
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage] close ByteArrayOutputStream error.");
        }

        return image;
    }

    public void createInvoicePdfForCanada(Document document, ContractorInvoicePrintDTO dto, Boolean monthly) throws DocumentException {
        document.newPage();
        PdfPTable table = new PdfPTable(7);
        table.setWidthPercentage(90);
        table.setSpacingBefore(5f);
        table.setSpacingAfter(5f);


        // logo
        PdfPCell cell = new PdfPCell(new Phrase("Intellipro Technologies Canada Inc.", FONT_BOLD_11));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Invoice No
        cell = new PdfPCell(new Phrase("Invoice No." + dto.getInvoiceNumber(), FONT_BOLD_11));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // invoice after space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(7);
        cell.setRowspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Name and Address
        cell = new PdfPCell(new Phrase("Customer Name and Address", FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // between Customer and Remit space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Remit To
        cell = new PdfPCell(new Phrase("Remit To", FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Name
        cell = new PdfPCell(new Phrase(dto.getCustomerName(), FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // between Customer Came and Please space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Please make payment via direct deposit to:
        cell = new PdfPCell(new Phrase("Please make payment via wire transfer to:", FONT_REGULAR_8));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Address
        cell = new PdfPCell(new Paragraph(dto.getCustomerAddress() + ", " + dto.getCustomerLocation(), FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // between Customer Address and Bank space
        cell = new PdfPCell(new Phrase(" "));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Bank of canada
        cell = new PdfPCell(new Phrase(CA_BANK_NAME_TITLE, FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Bank of canada value
        cell = new PdfPCell(new Phrase(CA_BANK_NAME, FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Customer Reference # title
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("Customer Reference #: ", FONT_BOLD_8_BLACK));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Customer Reference # value
        cell = new PdfPCell(new Phrase("", FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Account Name Title
        cell = new PdfPCell(new Phrase("Account Name: ", FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Account Name #
        cell = new PdfPCell(new Phrase("Intellipro Technologies Canada Inc.", FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // PO title
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("P.O.#: ", FONT_BOLD_8_BLACK));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // PO # value
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase(dto.getPoNumber(), FONT_BOLD_8_BLACK));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Transit Number
        cell = new PdfPCell(new Phrase("Transit Number: ", FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase("01457", FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Invoice Date # title
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("Invoice Date: ", FONT_BOLD_8_BLACK));
        }
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        cell.setColspan(2);
        table.addCell(cell);
        // Invoice Date value
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase(DateTimeFormatter.ofPattern(MM_DD_YYYY).format(dto.getInvoiceDate()), FONT_REGULAR_8));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Bank Code #
        cell = new PdfPCell(new Phrase(CA_ACCOUNT_NO_TITLE, FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(CA_ACCOUNT_NO, FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        cell = new PdfPCell(new Phrase(" ", FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Customer Reference # value
        cell = new PdfPCell(new Phrase(dto.getCustomerReference(), FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Institution Number: part
        cell = new PdfPCell(new Phrase("Institution Number:", FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase("003", FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // swift code
        cell = new PdfPCell(new Phrase(CA_SWIFT_CODE_TITLE, FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(CA_SWIFT_CODE, FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Bank Address first part
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Bank Address first part
        cell = new PdfPCell(new Phrase(CA_BANK_ADDRESS_TITLE, FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(CA_BANK_ADDRESS, FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // before Item Description space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell(new Phrase("Customer Contact", FONT_BOLD_8_BLACK));
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell(new Phrase("Invoice Date", FONT_BOLD_8_BLACK));
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            //values
            cell = new PdfPCell(new Phrase(" "));
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell(new Phrase(dto.getCustomerContact(), FONT_CHINESE));
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);

            cell = new PdfPCell(new Phrase(DateTimeFormatter.ofPattern(MM_DD_YYYY).format(dto.getInvoiceDate()), FONT_BOLD_8_BLACK));
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        }

        // Quantity title
        cell = new PdfPCell(new Phrase("Quantity", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        //Item Description title
        cell = new PdfPCell(new Phrase("Item Description", FONT_BOLD_8_WHITE));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Time Period
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("Time Period", FONT_BOLD_8_WHITE));
        }
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Billing Rate title
        cell = new PdfPCell(new Phrase("Billing Rate", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Unit title
        if (monthly) {
            cell = new PdfPCell(new Phrase(" "));
        } else {
            cell = new PdfPCell(new Phrase("Unit", FONT_BOLD_8_WHITE));
        }
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Amount Due title
        cell = new PdfPCell(new Phrase("Amount Due", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // before Item Description value space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        for (int i = 0; i < dto.getInvoiceList().size(); i++) {
            List<ContractorGroupInvoiceDetailVO> list = dto.getInvoiceList().get(i);
            int x = 0;
            String to = "";
            for (ContractorGroupInvoiceDetailVO vo : list) {
                // Quantity title
                cell = new PdfPCell(new Phrase(null == vo.getQuantity() || vo.getQuantity().equals(BigDecimal.ZERO) ? " " : vo.getQuantity() + "", FONT_CHINESE));
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setPaddingLeft(10f);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                cell.setBorder(PdfPCell.NO_BORDER);
                table.addCell(cell);

                String item = vo.getItemDescription();
                //Item Description title
                if (vo.getCurrency().equals("CAD")) {
                    if (item.indexOf("GST + QST") != -1) {
                        item = "Tax(GST No.:77796 1483 RT 0001,QST No.:1231160392)";
                    } else if (item.indexOf("HST") != -1) {
                        item = "Tax(HST No.:77796 1483 RT 0001)";
                    } else if (item.indexOf("GST") != -1) {
                        item = "Tax(GST No.:77796 1483 RT 0001)";
                    } else if (item.indexOf("QST") != -1) {
                        item = "Tax(QST No.:1231160392)";
                    }
                }
                if (ChineseCharacterCheckerUtil.containsChineseCharacters(item)) {
                    cell = new PdfPCell(new Phrase(item, FONT_CHINESE));
                } else {
                    cell = new PdfPCell(new Phrase(item, FONT_ARIAL));
                }
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setPaddingLeft(10f);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                cell.setBorder(PdfPCell.NO_BORDER);
                table.addCell(cell);

                // Time Period
                if (monthly) {
                    if (vo.getInvoiceType() != null) {
                        if (vo.getInvoiceType().equals(InvoiceType.EXPENSE.toDbValue().toString())) {
                            if (vo.getTimePeriod().indexOf("To") != -1 && vo.getTimePeriod().indexOf("From") != -1) {
                                cell = new PdfPCell(new Phrase(vo.getFrom(), FONT_CHINESE));
                            } else {
                                cell = new PdfPCell(new Phrase(vo.getTimePeriod(), FONT_CHINESE));
                            }
                        } else {
                            cell = new PdfPCell(new Phrase(vo.getFrom(), FONT_CHINESE));
                        }
                    } else {
                        cell = new PdfPCell(new Phrase(vo.getFrom(), FONT_CHINESE));
                    }
                } else {
                    cell = new PdfPCell(new Phrase(vo.getTimePeriod(), FONT_CHINESE));
                }
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                table.addCell(cell);

                // Billing Rate title
                cell = StringUtils.isBlank(vo.getBillRate()) || vo.getBillRate().equals("0.00") ? new PdfPCell(new Phrase("", FONT_CHINESE)) : constructCell(dto.getCurrency(), new BigDecimal(vo.getBillRate()), FONT_ARIAL, true);
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                table.addCell(cell);

                // Unit title
                cell = new PdfPCell(new Phrase(vo.getUnit(), FONT_CHINESE));
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                table.addCell(cell);

                // Amount Due title
                cell = constructCell(vo.getCurrency(), vo.getTotalAmount(), FONT_ARIAL, true);
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingBottom(6f);
                cell.setPaddingTop(6f);
                table.addCell(cell);

                //monthly 空一行增加 to
                if (monthly && x == 0) {
                    to = vo.getTo();
                }
                x++;
            }

            //增加ponumber 和 customer reference
            if (monthly) {
                addMonthlyData(table, "", "Customer Reference #");
                addMonthlyData(table, to, "PO # " + (StringUtils.isBlank(dto.getPoNumber()) ? "" : dto.getPoNumber()));
            }
        }

        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // before total amount above space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.TOP);
        cell.setBorderWidth(1.5F);
        cell.setBorderColor(new BaseColor(238, 238, 238));
        table.addCell(cell);


        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Subtotal title
        cell = new PdfPCell(new Phrase("Subtotal: ", FONT_BOLD_9));
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setRowspan(2);
        cell.setColspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // Subtotal value
        if (dto.getAmountDue().compareTo(new BigDecimal("0.00")) == 0) {
            cell = new PdfPCell(new Phrase("0", FONT_ARIAL));
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setRowspan(2);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        } else {
            cell = constructCell(dto.getCurrency(), dto.getAmountDue(), FONT_ARIAL, true);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setRowspan(2);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        }

        String payment = " ";
        if (dto.getDueDate() != null) {
            payment = "Payment due within " + dto.getDueDate() + " days";
        }
        // Payment Due within 30 days
        cell = new PdfPCell(new Phrase(payment, FONT_BOLD_8_BLACK));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // total amount title
        cell = new PdfPCell(new Phrase("Total Amount: ", FONT_BOLD_9));
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setRowspan(2);
        cell.setColspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        // total amount value
        if (dto.getAmountDue().compareTo(new BigDecimal("0.00")) == 0) {
            cell = new PdfPCell(new Phrase("0", FONT_ARIAL));
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setRowspan(2);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        } else {
            cell = constructCell(dto.getCurrency(), dto.getAmountDue(), FONT_ARIAL, true);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setRowspan(2);
            cell.setBorder(PdfPCell.NO_BORDER);
            table.addCell(cell);
        }


        // after total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        document.add(table);
    }

    public static PdfPCell constructCell(String currency, BigDecimal amount, Font font, boolean positive) {
        StringBuilder amountStr = new StringBuilder();
        if (!positive) {
            amountStr.append(NEGATIVE);
        }
        amountStr.append(CURRENCY_MAP.get(currency))
                .append(formatDecimalwithComma(amount));
        // if currency == 1 ,the font must be FONT_CHINESE
        if (currency != null && currency.equals(1)) {
            return new PdfPCell(new Phrase(amountStr.toString(), FONT_ARIAL));
        }
        if (currency != null && currency.equals(29)) {
            return new PdfPCell(new Phrase(amountStr.toString(), FONT_NOTO_SANS));
        }
        if (amount.compareTo(new BigDecimal("0.00")) == 0) {
            return new PdfPCell(new Phrase("", font));
        }
        return new PdfPCell(new Phrase(amountStr.toString(), font));
    }

    public final static DecimalFormat DECIMAL_FORMAT_PDF_AMOUNT = new DecimalFormat("#,###.00");

    public final static DecimalFormat DECIMAL_FORMAT_PDF_AMOUNT2 = new DecimalFormat("0.00");

    public static String formatDecimalwithComma(BigDecimal number) {
        return number.compareTo(BigDecimal.ONE) == -1 ? DECIMAL_FORMAT_PDF_AMOUNT2.format(number) : DECIMAL_FORMAT_PDF_AMOUNT.format(number);
    }

    public final static Map<Integer, String> CURRENCY_STAIC_MAP = Collections.unmodifiableMap(new HashMap<>() {{
        put(0, "$");
        put(1, "￥");
        put(2, "€");
        put(3, "C$");
        put(4, "£");
        put(5, "AED");
        put(7, "A$");
        put(9, "SFr");
        put(12, "HK$");
        put(15, "₹");
        put(16, "JP¥");
        put(36, "RM");
        put(23, "NZ$");
        put(24, "₱");
        put(28, "S$");
        put(29, "฿");
        put(31, "NT$");
        put(33, "₫");
        put(54, "R$");
        put(55, "MX$");
        put(56, "SAR");
        put(9999, "$");
    }});

    public final static Map<String, String> CURRENCY_MAP = Collections.unmodifiableMap(new HashMap<>() {{
        put("USD", "$");
        put("CNY", "￥");
        put("EUR", "€");
        put("CAD", "C$");
        put("GBP", "£");
        put("HKD", "HK$");
        put("INR", "₹");
        put("MYR", "RM");
        put("PHP", "₱");
        put("SGD", "S$");
        put("JPY", "JP¥");
        put("AUD", "A$");
        put("VND", "₫");
        put("CHF", "SFr");
        put("THB", "฿");
        put("TWD", "NT$");
        put("BRL", "R$");
        put("MXN", "MX$");
        put("SAR", "SAR");
        put("AED", "AED");
        put("NZD","NZ$");

    }});


    public Cell getIntellLogoCell() throws IOException {
        InputStream inputStream = getClass().getResourceAsStream("/images/intelliprogroup-logo.png");
        com.itextpdf.layout.element.Image image = new com.itextpdf.layout.element.Image(ImageDataFactory.create(inputStream.readAllBytes()));
        image.scale(0.3f, 0.3f);
        com.itextpdf.layout.element.Paragraph paragraph = new com.itextpdf.layout.element.Paragraph().add(image);
        paragraph.setTextAlignment(TextAlignment.CENTER);
        Cell imageCell = new Cell().add(paragraph);
        imageCell.setPaddingTop(50).setPaddingBottom(30);
        return imageCell;
    }
}
