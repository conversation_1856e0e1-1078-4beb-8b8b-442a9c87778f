package com.altomni.apn.jobdiva.repository.settings;

import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingPackageDocuments;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data JPA repository for the OnBoardingPackageDocuments entity.
 */
@Repository
public interface OnBoardingPackageDocumentsRepository extends JpaRepository<OnBoardingPackageDocuments, Long> {

    @Modifying
    @Transactional
    void deleteAllByPackageIdAndTenantId(Long id, Long tenantId);

    @Modifying
    @Transactional
    void deleteAllByDocumentIdAndTenantId(Long id, Long tenantId);

    @Modifying
    @Transactional
    void deleteAllByPackageIdAndDocumentIdAndTenantId(Long packageId, Long documentId, Long tenantId);

    @Query(value = "select new com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO(pd.id, pd.packageId, pd.documentId, d.name ,d.s3Key ,d.actionRequired, pd.mandatory)" +
        " from OnBoardingPackageDocuments pd left join OnBoardingDocuments d on pd.documentId = d.id where d.activated = true and pd.packageId =  ?1 and d.tenantId = ?2 order by pd.mandatory desc, d.actionRequired")
    List<OnBoardingPackageDocumentsDTO> findDocumentsByPackageId(Long id, Long tenantId);

}
