package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.TGroupInvoiceRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.List;
import java.util.Set;

@Repository
public interface GroupInvoiceRecordRepository extends JpaRepository<TGroupInvoiceRecord,Long> {

    @Query(value = "select * from t_group_invoice_record t \n" +
            "where t.group_invoice_id in (\n" +
            "\tselect group_invoice_id from t_group_invoice_record where invoice_id in ( ?1 )  and t.status=1\n" +
            "\tGROUP BY group_invoice_id\n" +
            ")",nativeQuery = true)
    List<TGroupInvoiceRecord> findGroupInvoiceIdByInvoiceIds(Set<Long> invoiceIdList);


    @Modifying
    @Transactional
    @Query(value = "update  t_group_invoice_record  set status=0,last_modified_date=now() where id in ( ?1 ) ",nativeQuery = true)
    void updateStatusByIds(Set<Long> invoiceRecordIdList);


    @Query(value = "select * from t_group_invoice_record t where t.group_invoice_id in ( ?1 ) and t.status=1",nativeQuery = true)
    List<TGroupInvoiceRecord> findTGroupInvoiceRecordByGroupInvoiceIdAndStatus(List<BigInteger> groupInvoiceIdList);

    @Query(value = "select * from t_group_invoice_record t where t.group_invoice_id in ( ?1 )",nativeQuery = true)
    List<TGroupInvoiceRecord> findTGroupInvoiceRecordByGroupInvoiceId(List<BigInteger> groupInvoiceIdList);

}
