package com.altomni.apn.jobdiva.web.rest.onboarding;


import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.jobdiva.service.dto.onboarding.SearchDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackagesDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingSignatureDTO;
import com.altomni.apn.jobdiva.service.onboarding.OnBoardingSettingsService;
import com.altomni.apn.jobdiva.service.store.StoreService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for OnBoarding Settings .
 */
@Api(tags = {"APN-OnBoarding Settings"})
@RestController
@RequestMapping("/api/v3/settings")
public class SettingsResource {

    private final Logger log = LoggerFactory.getLogger(SettingsResource.class);

    @Resource
    private OnBoardingSettingsService onboardingSettingsService;

    @Resource
    private StoreService storeService;

    @ApiOperation(value = "Get all documents")
    @GetMapping("/documents")
    @Timed
    public ResponseEntity<List<OnBoardingDocumentsDTO>> getAllDocuments() {
        log.info("[APN: OnBoarding Settings @{}] REST request to get all documents", SecurityUtils.getUserId());
        List<OnBoardingDocumentsDTO> result = onboardingSettingsService.getAllDocuments();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation(value = "Get document info by document id")
    @GetMapping("/documents/{id}")
    @Timed
    public ResponseEntity<OnBoardingDocumentsDTO> getByDocumentId(@ApiParam(value = "document id", required = true) @Valid @PathVariable Long id) {
        log.info("[APN: OnBoarding Settings @{}] REST get document info by document id: {}", SecurityUtils.getUserId(), id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(onboardingSettingsService.getByDocumentId(id)));
    }

    @ApiOperation(value = "Delete document by document id")
    @DeleteMapping("/documents/{id}")
    @Timed
    public ResponseEntity<Void> deleteByDocumentId(@ApiParam(value = "document id", required = true) @Valid @PathVariable Long id) {
        log.info("[APN: OnBoarding Settings @{}] REST Delete document by document id: {}", SecurityUtils.getUserId(), id);
        onboardingSettingsService.deleteByDocumentId(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Update document info by document id")
    @PutMapping("/documents/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<OnBoardingDocumentsDTO> updateByDocumentId(
        @ApiParam(value = "document id", required = true) @Valid @PathVariable Long id
        ,@Valid @RequestBody OnBoardingDocumentsDTO dto) {
        log.info("[APN: OnBoarding Settings @{}] REST update document by document id: {} ,{}", SecurityUtils.getUserId(), id, dto);
        dto.setId(id);
        return new ResponseEntity<>(onboardingSettingsService.updateByDocumentId(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "Save document info")
    @PostMapping("/documents")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<OnBoardingDocumentsDTO> saveDocument(@Valid @RequestBody OnBoardingDocumentsDTO dto) {
        log.info("[APN: OnBoarding Settings @{}] REST save document info: {}", SecurityUtils.getUserId(), dto);
        return new ResponseEntity<>(onboardingSettingsService.saveDocument(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "Find documents in package by package id")
    @GetMapping("/packages-documents/{id}")
    @Timed
    public ResponseEntity<List<OnBoardingPackageDocumentsDTO>> findDocumentsByPackageId(
        @ApiParam(value = "package id", required = true) @Valid @PathVariable Long id) {
        log.info("[APN: OnBoarding Settings @{}] REST find documents in package by package id: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(onboardingSettingsService.findDocumentsByPackageId(id), HttpStatus.OK);
    }

    @ApiOperation(value = "Find all documents by package id")
    @PostMapping("/documents/{id}")
    @Timed
    public ResponseEntity<List<OnBoardingPackageDocumentsDTO>> findAllDocumentsByPackageId(
        @ApiParam(value = "package id", required = true) @Valid @PathVariable Long id,@Valid @RequestBody SearchDocumentsDTO conditionDto) {
        log.info("[APN: OnBoarding Settings @{}] REST find all documents by package id: {}, {}", SecurityUtils.getUserId(), id , conditionDto);
        return new ResponseEntity<>(onboardingSettingsService.findAllDocumentsByPackageId(id, conditionDto), HttpStatus.OK);
    }

    @ApiOperation(value = "Get all packages")
    @GetMapping("/packages")
    @Timed
    public ResponseEntity<List<OnBoardingPackagesDTO>> getAllPackages() {
        log.info("[APN: OnBoarding Settings @{}] REST get all packages ", SecurityUtils.getUserId());
        return new ResponseEntity<>(onboardingSettingsService.getAllPackages(), HttpStatus.OK);
    }

    @ApiOperation(value = "Get package info by package id")
    @GetMapping("/packages/{id}")
    @Timed
    public ResponseEntity<OnBoardingPackagesDTO> getByPackageId(@ApiParam(value = "package id", required = true) @Valid @PathVariable Long id) {
        log.info("[APN: OnBoarding Settings @{}] REST get package info by package id: {}", SecurityUtils.getUserId(), id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(onboardingSettingsService.getByPackageId(id)));
    }

    @ApiOperation(value = "Delete package and documents in package by package id")
    @DeleteMapping("/packages/{id}")
    @Timed
    public ResponseEntity<Void> deleteByPackageId(@ApiParam(value = "package id", required = true) @Valid @PathVariable Long id) {
        log.info("[APN: OnBoarding Settings @{}] REST Delete package and documents in package by package id: {}", SecurityUtils.getUserId(), id);
        onboardingSettingsService.deleteByPackageId(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Update package info by package id")
    @PutMapping("/packages/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<OnBoardingPackagesDTO> updateByPackageId(
        @ApiParam(value = "document id", required = true) @Valid @PathVariable Long id
        ,@Valid @RequestBody OnBoardingPackagesDTO dto) {
        log.info("[APN: OnBoarding Settings @{}] REST update package by package id: {} ,{}", SecurityUtils.getUserId(), id, dto);
        dto.setId(id);
        return new ResponseEntity<>(onboardingSettingsService.updateByPackageId(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "Save package info")
    @PostMapping("/packages")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<OnBoardingPackagesDTO> savePackage(@Valid @RequestBody OnBoardingPackagesDTO dto) {
        log.info("[APN: OnBoarding Settings @{}] REST save package info: {}", SecurityUtils.getUserId(), dto);
        return new ResponseEntity<>(onboardingSettingsService.savePackage(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "Save relationship of package and documents")
    @PostMapping("/packages/documents/{packageId}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> saveRelations(@ApiParam(value = "package id", required = true) @Valid @PathVariable Long packageId, @Valid @RequestBody List<OnBoardingPackageDocumentsDTO> dto) {
        log.info("[APN: OnBoarding Settings @{}] REST save relationship of package and documents: {},{}", SecurityUtils.getUserId(), packageId, dto);
        onboardingSettingsService.saveRelations(packageId, dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @ApiOperation(value = "Delete document in package")
    @DeleteMapping("/packages/documents/{packageId}/{documentId}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteDocumentInPackage(@ApiParam(value = "package id", required = true) @Valid @PathVariable Long packageId,@ApiParam(value = "document id", required = true) @Valid @PathVariable Long documentId) {
        log.info("[APN: OnBoarding Settings @{}] REST delete document in package , package id :{} ,document id :{} ", SecurityUtils.getUserId(), packageId, documentId);
        onboardingSettingsService.deleteDocumentInPackage(packageId, documentId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Find signature by tenantId")
    @GetMapping("/signature")
    @Timed
    public ResponseEntity<OnBoardingSignatureDTO> getSignature() {
        log.info("[APN: OnBoarding Settings @{}] REST Find signature by tenantId", SecurityUtils.getUserId());
        return new ResponseEntity<>(onboardingSettingsService.getSignature(), HttpStatus.OK);
    }

    @ApiOperation(value = "Save signature by tenantId")
    @PostMapping("/signature")
    @Timed
    public ResponseEntity<Void> saveSignature(@Valid @RequestBody OnBoardingSignatureDTO dto) {
        log.info("[APN: OnBoarding Settings @{}] REST Save signature by tenantId", SecurityUtils.getUserId());
        onboardingSettingsService.saveSignature(dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }
}
