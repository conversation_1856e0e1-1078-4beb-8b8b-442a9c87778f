package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.jobdiva.SearchConfigType;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetSearchConfig;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetSearchConfigRepository;
import com.altomni.apn.jobdiva.service.dto.timesheet.SearchFilterDTO;
import com.altomni.apn.jobdiva.service.timesheet.SearchConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;

@Service("searchConfigService")
public class SearchConfigServiceImpl implements SearchConfigService {

    @Resource
    private TimeSheetSearchConfigRepository repository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TimeSheetSearchConfig saveFilter(SearchFilterDTO dto) {
        if (dto.getName() == null || dto.getSearchType() == null || dto.getConfig() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ADVANCE_COMPOSE_SQL_DATA_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (dto.getSearchType() != SearchConfigType.EXPENSE_FILTER && dto.getSearchType() != SearchConfigType.TIME_SHEET_FILTER ) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_SAVE_FILTER_TYPE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return saveData(dto);
    }

    @Override
    public TimeSheetSearchConfig saveTableHeader(SearchFilterDTO dto) {
        if (dto.getSearchType() != SearchConfigType.EXPENSE_TABLE && dto.getSearchType() != SearchConfigType.TIME_SHEET_TABLE ) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_SAVE_FILTER_TYPE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        List<TimeSheetSearchConfig> configs = findMyConfig(dto.getSearchType());
        if (configs != null && configs.size() == 1) {
            saveData(dto);
        } else {
            if (CollUtil.isNotEmpty(configs)) {
                for (TimeSheetSearchConfig config: configs) {
                    if (config.getIsDefault()) {continue;}
                    config.setIsDefault(false);
                    config.setConfig(dto.getConfig());
                    config.setId(config.getId());
                    config.setUid(SecurityUtils.getUserId());
                    config.setSearchType(dto.getSearchType());
                    config.setFilterType(dto.getFilterType());
                    config.setTenantId(SecurityUtils.getTenantId());
                    return repository.save(config);
                }
            }
        }
        return null;
    }

    @Override
    public List<TimeSheetSearchConfig> searchFilter(SearchConfigType type,String keyWords) {
        if (keyWords == null || keyWords.trim().length()==0) {
            return repository.searchFilter(SecurityUtils.getUserId(),type.toDbValue());
        }
        return repository.searchFilter(SecurityUtils.getUserId(),type.toDbValue(),keyWords.toLowerCase());
    }

    @Override
    public List<TimeSheetSearchConfig> findMyConfig(SearchConfigType type) {
        return repository.searchFilter(SecurityUtils.getUserId(),type.toDbValue());
    }

    @Override
    public TimeSheetSearchConfig initMyConfig(SearchConfigType type, Long uid, Long tenantId) {
        TimeSheetSearchConfig config = new TimeSheetSearchConfig(type, uid, tenantId);
        return repository.save(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFilter(Long id) {
        TimeSheetSearchConfig config = repository.findById(id).orElse(null);
        if (config == null || config.getIsDefault()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DELETE_FILTER_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        repository.deleteById(id);
    }

    private TimeSheetSearchConfig saveData(SearchFilterDTO dto) {
        TimeSheetSearchConfig config = new TimeSheetSearchConfig();
        config.setName(dto.getName());
        config.setUid(SecurityUtils.getUserId());
        config.setTenantId(SecurityUtils.getTenantId());
        config.setConfig(dto.getConfig());
        config.setSearchType(dto.getSearchType());
        config.setFilterType(dto.getFilterType());
        config.setIsDefault(false);
        config.setCreatedTime(Instant.now());
        return repository.save(config);
    }


}
