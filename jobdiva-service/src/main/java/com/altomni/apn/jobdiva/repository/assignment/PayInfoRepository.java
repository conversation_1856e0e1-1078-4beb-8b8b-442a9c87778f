package com.altomni.apn.jobdiva.repository.assignment;

import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface PayInfoRepository extends JpaRepository<AssignmentPayInfo, Long> {

    void deleteByAssignmentId(Long assignmentId);

    AssignmentPayInfo findByAssignmentId(Long assignmentId);

    List<AssignmentPayInfo> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
