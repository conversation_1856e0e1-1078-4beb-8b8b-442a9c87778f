package com.altomni.apn.jobdiva.service.vo.assignment;

import lombok.Data;

import java.time.LocalDate;

@Data
public class WeekDataVO {

    private LocalDate weekStart;

    private LocalDate weekEnd;

    private LocalDate workDate;

    private LocalDate weekendingDate;

    @Override
    public String toString() {
        return "WeekDataEntity{" +
                "weekStart=" + weekStart +
                ", weekEnd=" + weekEnd +
                ", workDate=" + workDate +
                ", weekendingDate=" + weekendingDate +
                '}';
    }

}
