package com.altomni.apn.jobdiva.service.vo.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * A record
 */
@Data
public class AssignmentVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private LocalDate endingDate;

    private String talentName;

    private String jobTitle;

    private String companyName;

    private LocalDate startDate;

    private LocalDate endDate;

    private String primaryManager;

    private Integer weekEnding;
    private TimeSheetType sheetType;
    private CalculateMethodType method;

    private WeekEndingType weekEndingType;

    private Long jobId;
    private Long companyId;

    private Boolean allowSubmitTimeSheet;
    private Boolean allowSubmitExpense;

    private OverTimeType overtimeType;

    @ApiModelProperty(value = "Returns whether overtime is exempt")
    private Boolean isExcept;

    private TimeSheetFrequencyType frequency;

    private Long talentId;

    private Long tenantId;

    private Integer isWeekEnd;

    public AssignmentVO() {
    }

    public AssignmentVO(String talentName, String jobTitle, String companyName, LocalDate startDate, LocalDate endDate, String primaryManger,Integer weekEnding, TimeSheetType sheetType, CalculateMethodType method) {
        this.talentName = talentName;
        this.jobTitle = jobTitle;
        this.companyName = companyName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.primaryManager= primaryManger;
        this.weekEnding = weekEnding;
        this.sheetType = sheetType;
        this.method = method;
    }

    public AssignmentVO(Long id,LocalDate startDate, LocalDate endDate,WeekEndingType weekEnding,TimeSheetType sheetType, CalculateMethodType method,Long jobId,Long companyId,Boolean allowSubmitTimeSheet,Boolean allowSubmitExpense,OverTimeType overTimeType,Boolean isExcept) {
        this.id = id;
        this.startDate = startDate;
        this.endDate = endDate;
        this.weekEnding = weekEnding.toDbValue();
        this.sheetType = sheetType;
        this.method = method;
        this.jobId =jobId;
        this.companyId = companyId;
        this.allowSubmitExpense= allowSubmitExpense;
        this.allowSubmitTimeSheet = allowSubmitTimeSheet;
        this.overtimeType = overTimeType;
        this.isExcept = isExcept;
    }

    public AssignmentVO(Long id,LocalDate startDate, LocalDate endDate,WeekEndingType weekEnding,TimeSheetType sheetType, CalculateMethodType method,Long jobId,Long companyId,Boolean allowSubmitTimeSheet,
                        Boolean allowSubmitExpense,OverTimeType overTimeType,Boolean isExcept, TimeSheetFrequencyType frequency, Long talentId, Long tenantId, Integer isWeekEnd) {
        this.id = id;
        this.startDate = startDate;
        this.endDate = endDate;
        this.weekEnding = weekEnding.toDbValue();
        this.sheetType = sheetType;
        this.method = method;
        this.jobId =jobId;
        this.companyId = companyId;
        this.allowSubmitExpense= allowSubmitExpense;
        this.allowSubmitTimeSheet = allowSubmitTimeSheet;
        this.overtimeType = overTimeType;
        this.isExcept = isExcept;
        this.frequency = frequency;
        this.talentId = talentId;
        this.tenantId = tenantId;
        this.isWeekEnd = isWeekEnd;
    }

}
