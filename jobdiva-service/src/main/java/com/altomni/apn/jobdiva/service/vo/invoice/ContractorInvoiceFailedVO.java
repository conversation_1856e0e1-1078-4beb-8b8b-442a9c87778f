package com.altomni.apn.jobdiva.service.vo.invoice;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.StringUtils;

import java.math.BigInteger;
import java.sql.Timestamp;

/**
 * failed VO info
 */
@ApiModel(description = "contractorInvoiceFailedVO")
public class ContractorInvoiceFailedVO {

    @ColumnWidth(20)
    @ExcelProperty(value = "Week Ending Date", index = 0)
    private Timestamp weekEndingDate;

    @ColumnWidth(20)
    @ExcelProperty(value = "Employee Name", index = 1)
    private String employeeName;

    @ColumnWidth(30)
    @ExcelProperty(value = "Job Title", index = 2)
    private String jobTitle;

    @ColumnWidth(30)
    @ExcelProperty(value = "Company", index = 3)
    private String company;

    @ColumnWidth(30)
    @ExcelProperty(value = "Assignment Division", index = 4)
    private String assignmentDivision;

    @ColumnWidth(30)
    @ExcelProperty(value = "AM", index = 5)
    private String am;

    @ColumnWidth(30)
    @ExcelProperty(value = "Approver", index = 6)
    private String approver;

    @ColumnWidth(30)
    @ExcelProperty(value = "Type", index = 7)
    private String type;

    @ColumnWidth(30)
    @ExcelProperty(value = "Status", index = 8)
    private String status;

    @ExcelIgnore
    private BigInteger id;

    @ExcelIgnore
    private Timestamp startDate;

    @ExcelIgnore
    private Timestamp endDate;

    @ExcelIgnore
    private Timestamp weekEnd;

    @ExcelIgnore
    private Timestamp weekStart;

    public Timestamp getWeekEndingDate() {
        return weekEndingDate;
    }

    public void setWeekEndingDate(Timestamp weekEndingDate) {
        this.weekEndingDate = weekEndingDate;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getAssignmentDivision() {
        return assignmentDivision;
    }

    public void setAssignmentDivision(String assignmentDivision) {
        this.assignmentDivision = StringUtils.isNotBlank(assignmentDivision) ? AssignmentDivision.getNameFormDbValue(Integer.valueOf(assignmentDivision)) : "";
    }

    public String getAm() {
        return am;
    }

    public void setAm(String am) {
        this.am = am;
    }

    public String getApprover() {
        return approver;
    }

    public void setApprover(String approver) {
        this.approver = approver;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        if (type != null) {
            if (type.equals("1")) {
                this.type = "TimeSheet";
            } else if (type.equals("2")) {
                this.type = "Expense";
            } else {
                this.type = type;
            }
        } else {
            this.type = "";
        }
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        if (null != status) {
            TimeSheetStatus timeSheetStatus = TimeSheetStatus.fromDbValue(Integer.valueOf(status));
            this.status = timeSheetStatus.name();
        }
    }

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public Timestamp getStartDate() {
        return startDate;
    }

    public void setStartDate(Timestamp startDate) {
        this.startDate = startDate;
    }

    public Timestamp getEndDate() {
        return endDate;
    }

    public void setEndDate(Timestamp endDate) {
        this.endDate = endDate;
    }

    public Timestamp getWeekEnd() {
        return weekEnd;
    }

    public void setWeekEnd(Timestamp weekEnd) {
        this.weekEnd = weekEnd;
    }

    public Timestamp getWeekStart() {
        return weekStart;
    }

    public void setWeekStart(Timestamp weekStart) {
        this.weekStart = weekStart;
    }
}
