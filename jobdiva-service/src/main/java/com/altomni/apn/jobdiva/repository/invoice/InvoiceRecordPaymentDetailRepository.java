package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.InvoiceRecordPaymentDetail;
import com.altomni.apn.jobdiva.domain.invoice.TEmailAttachmentRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.List;

/**
 * 回款候选人明细;
 */
@Repository
public interface InvoiceRecordPaymentDetailRepository extends JpaRepository<InvoiceRecordPaymentDetail, BigInteger> {

    @Modifying
    @Transactional
    @Query(value = "update  invoice_record_payment_detail  set status=0,last_modified_date=now() where group_id in ?1 ",nativeQuery = true)
    void updateStatusByGroupInvoiceIds(List<BigInteger> groupInvoiceIdList);

    @Modifying
    @Transactional
    @Query(value = "update  invoice_record_payment_detail  set status=0,last_modified_date=now() where payment_id= ?1 ",nativeQuery = true)
    void updateStatusByPaymentId(Long id);

    List<InvoiceRecordPaymentDetail> findByGroupIdAndStatus(Long groupId,Integer status);

    List<InvoiceRecordPaymentDetail> findByGroupIdInAndStatus(List<Long> groupId,Integer status);
}
