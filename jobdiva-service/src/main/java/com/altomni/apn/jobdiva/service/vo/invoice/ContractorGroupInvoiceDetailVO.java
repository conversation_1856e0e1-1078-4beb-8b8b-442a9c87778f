package com.altomni.apn.jobdiva.service.vo.invoice;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

@ApiModel(description = "ContractorGroupInvoiceDetailVO")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ContractorGroupInvoiceDetailVO implements Serializable {

    private BigInteger id;

    private BigDecimal quantity;

    private String timePeriod;

    private String from;

    private String to;

    private String itemDescription;

    private String billRate;

    private String unit;

    private BigDecimal totalAmount;

    private String currency;

    private String poNumber;

    private String invoiceType;
}
