package com.altomni.apn.jobdiva.config.rabbit;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetFrequencyType;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentTimeSheet;
import com.altomni.apn.jobdiva.domain.invoice.TContractorInvoice;
import com.altomni.apn.jobdiva.domain.invoice.TGroupInvoice;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetWeekEndingRecord;
import com.altomni.apn.jobdiva.repository.invoice.ContractorInvoiceNativeRepository;
import com.altomni.apn.jobdiva.repository.invoice.ContractorInvoiceRepository;
import com.altomni.apn.jobdiva.repository.invoice.GroupInvoiceRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRecordRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetWeekEndingRecordRepository;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorInvoicePrintDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.RecordSearchDTO;
import com.altomni.apn.jobdiva.service.invoice.ContractorGroupInvoiceService;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.timesheet.ClientService;
import com.altomni.apn.jobdiva.service.vo.invoice.ContractorGroupInvoiceViewVO;
import com.altomni.apn.jobdiva.util.InvoicePdfUtil;
import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class InvoiceRabbit {

    @Resource(name = "invoiceRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource
    ContractorGroupInvoiceService contractorGroupInvoiceService;

    @Resource
    ContractorInvoiceRepository contractorInvoiceRepository;

    @Resource
    ContractorInvoiceNativeRepository contractorInvoiceNativeRepository;

    @Resource
    GroupInvoiceRepository groupInvoiceRepository;

    @Resource
    TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    ClientService clientService;

    @Resource
    StoreService storeService;

    @Resource
    TimeSheetRepository timeSheetRepository;

    @Resource
    TimeSheetWeekEndingRecordRepository timeSheetWeekEndingRecordRepository;

    @Resource
    TalentAssignmentRepository talentAssignmentRepository;


    @RabbitListener(containerFactory = "invoiceFactory", queues = {"${application.invoice-mq.queue}"})
    @RabbitHandler
    public void process(Message message, Channel channel) throws IOException {
        log.info("invoice rabbit,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String id = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            String[] param = id.replace("\"", "").split("&");
            LoginUtil.simulateLoginWithClient();
            execute(new BigInteger(param[0]), param[1]);
            log.info("invoice rabbit, is success");
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("invoice rabbit,id:{}, is error, error message：{}", id, e.getMessage());
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            //rabbitTemplate.convertAndSend(queueConfig.getExchange(), queueConfig.getRoutingKey(), new Message(JSONUtil.toJsonStr(id).getBytes()));
        }
    }


    protected void execute(BigInteger id, String timezone) throws Exception {
        Optional<TGroupInvoice> groupInvoice = groupInvoiceRepository.findById(id);
        if (!groupInvoice.isPresent()) {
            log.info("invoice rabbit, group invoice is not found,id: {}", id);
            return;
        }
        TGroupInvoice tGroupInvoice = groupInvoice.get();
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        Document document = new Document(PageSize.A4, 0, 0, 60, 0);
        boolean created = createPdf(id, bos, document);
        document.close();
        if (!created) {
            bos.close();
            return;
        }
        String deCodeFileName = URLDecoder.decode(tGroupInvoice.getGroupNumber() + ".pdf", "utf-8");
        MultipartFile file = new MockMultipartFile(deCodeFileName, deCodeFileName, ContentType.APPLICATION_OCTET_STREAM.toString(), bos.toByteArray());
        String mk5Key = DigestUtils.md5Hex(file.getInputStream());
        storeService.uploadDocument(file, mk5Key, UploadTypeEnum.GROUP_INVOICE.getKey()).getBody();
        log.info("invoice rabbit, file upload to s3,does not include timesheet, file name:{}", mk5Key);
        tGroupInvoice.setFileUrl(mk5Key);
        bos.close();

        log.info("invoice rabbit, start create include timesheet pdf info");

        bos = new ByteArrayOutputStream();
        Document includeTimesheetDocument = new Document(PageSize.A4, 0, 0, 60, 0);
        createPdf(id, bos, includeTimesheetDocument);

        List<TContractorInvoice> contractorInvoices = contractorInvoiceRepository.queryTimesheetInvoiceByGroupInvoiceId(id);
        for (TContractorInvoice invoice : contractorInvoices) {
            TimeSheetWeekEndingRecord record = timeSheetWeekEndingRecordRepository.findTimeSheetWeekEndingRecordByAssignmentIdAndWorkDate(invoice.getAssignmentId().longValue(), invoice.getWeekEndingDate().toLocalDateTime().toLocalDate());
            RecordSearchDTO dto = new RecordSearchDTO();
            LocalDate endDate = record.getWeekEnd();
            LocalDate startDate = record.getWeekStart();
            dto.setStartDate(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            dto.setEndDate(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            TimeSheetRecord timeSheetRecord = timeSheetRecordRepository.findByDate(invoice.getWeekEndingDate().toLocalDateTime().toLocalDate(), invoice.getTalentId().longValue(), invoice.getAssignmentId().longValue());
            if (null != timeSheetRecord) {
                dto.setId(timeSheetRecord.getId());
            }
            dto.setTimezone(timezone);
            clientService.timesheetDetailSearchAndBatchPdf(dto, includeTimesheetDocument);
        }
        includeTimesheetDocument.close();
        String timesheetCodeFileName = URLDecoder.decode(tGroupInvoice.getGroupNumber() + "_timesheet.pdf", "utf-8");
        file = new MockMultipartFile(timesheetCodeFileName, timesheetCodeFileName, ContentType.APPLICATION_OCTET_STREAM.toString(), bos.toByteArray());
        mk5Key = DigestUtils.md5Hex(file.getInputStream());
        storeService.uploadDocument(file, mk5Key, UploadTypeEnum.GROUP_INVOICE.getKey()).getBody();
        log.info("invoice rabbit, file upload to s3, include timesheet, file name:{}", mk5Key);
        tGroupInvoice.setTimesheetFileUrl(mk5Key);
        bos.close();

        groupInvoiceRepository.save(tGroupInvoice);
        log.info("invoice rabbit, update group invoice,param: {}", JSON.toJSONString(tGroupInvoice));
    }

    /**
     * 构建pdf信息
     *
     * @param id
     * @param bos
     * @param document
     * @throws Exception
     */
    private boolean createPdf(BigInteger id, ByteArrayOutputStream bos, Document document) throws Exception {
        ContractorGroupInvoiceViewVO vo = contractorGroupInvoiceService.view(id);
        if (CollectionUtils.isEmpty(vo.getInvoiceList())) {
            return false;
        }
        List<AssignmentTimeSheet> assignmentTimeSheetList = timeSheetRepository.findFrequencyByGroupInvoiceId(id.longValue());

        Boolean monthly = false;
        Set<TimeSheetFrequencyType> frequencyList = assignmentTimeSheetList.stream().map(AssignmentTimeSheet::getFrequency).collect(Collectors.toSet());
        if (frequencyList.size() == 1 && (frequencyList.contains(TimeSheetFrequencyType.MONTHLY) || frequencyList.contains(TimeSheetFrequencyType.SEMI_MONTHLY)) || frequencyList.contains(TimeSheetFrequencyType.QUARTERLY)) {
            monthly = true;
        }

        ContractorInvoicePrintDTO printDTO = new ContractorInvoicePrintDTO();
        printDTO.setInvoiceList(vo.getInvoiceList());
        printDTO.setInvoiceDate(vo.getInvoiceDate());
        printDTO.setInvoiceNumber(vo.getGroupInvoiceNumber());
        printDTO.setAmountDue(vo.getTotalAmount());
        printDTO.setCustomerContact(vo.getCompanyContact());
        printDTO.setCurrency(vo.getCurrency());
        printDTO.setDueDate(vo.getDueDate());

        InvoicePdfUtil pdfUtil = new InvoicePdfUtil();
        PdfWriter.getInstance(document, bos);
        document.open();

        Map<String, Object> address = null;
        if (StringUtils.isNotBlank(vo.getClientAddress()) && !vo.getClientAddress().equals("null")) {
            address = new HashMap<>();
            address.put("relation_client_address", vo.getClientAddress());
            address.put("relation_client_name", vo.getClientName());
            address.put("relation_client_location", vo.getClientLocation());
        } else {
            TalentAssigment talentAssigment = talentAssignmentRepository.findById(vo.getAssignmentId().longValue()).get();
            List<Map<String, Object>> startList = contractorInvoiceNativeRepository.findStartInfo(talentAssigment.getTalentRecruitmentProcessId());
            if (!startList.isEmpty()) {
                if (startList.size() == 1) {
                    BigInteger startId = (BigInteger) startList.get(0).get("id");
                    address = contractorInvoiceNativeRepository.findCompanyAddressAndNameByStartId(startId);
                } else {
                    LocalDate weekEndDate = vo.getWeekEndingDate();
                    for (Map<String, Object> start : startList) {
                        java.sql.Date startDate = (java.sql.Date) start.get("start_date");
                        java.sql.Date endDate = (java.sql.Date) start.get("end_date");
                        if (!(weekEndDate.isBefore(startDate.toLocalDate())) && !(weekEndDate.isAfter(endDate.toLocalDate()))) {
                            BigInteger startId = (BigInteger) start.get("id");
                            address = contractorInvoiceNativeRepository.findCompanyAddressAndNameByStartId(startId);
                            break;
                        }
                    }

                    if (address == null) {
                        address = contractorInvoiceNativeRepository.findCanadaProvinceByAssignmentId(vo.getAssignmentId().longValue());
                    }
                }
            }
        }
        if (address != null) {
            printDTO.setCustomerAddress(address.get("relation_client_address") + "");
            printDTO.setCustomerName(address.get("relation_client_name") + "");
            String location = address.get("relation_client_location") + "";
            if (StringUtils.isNotBlank(location)) {
                printDTO.setCustomerLocation(location);
            }
        }
        if (vo.getCurrency().equals("CAD")) {
            pdfUtil.createInvoicePdfForCanada(document, printDTO, monthly);
        } else {
            pdfUtil.createInvoicePdfForUS(document, printDTO, monthly);
        }
        return true;
    }

}
