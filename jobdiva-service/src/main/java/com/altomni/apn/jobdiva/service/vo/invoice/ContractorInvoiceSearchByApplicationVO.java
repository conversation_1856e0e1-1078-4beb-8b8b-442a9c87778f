package com.altomni.apn.jobdiva.service.vo.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
public class ContractorInvoiceSearchByApplicationVO {

    @Id
    private Long invoiceId;

    private LocalDateTime invoiceDate;

    private Instant createdDate;

    private String invoiceNumber;

    private String groupNumber;

    private Long groupInvoiceId;

    private BigDecimal totalAmount;

    private Long talentId;

    private String fullName;

    @Convert(converter = InvoiceStatusConverter.class)
    private InvoiceStatusType invoiceStatus;

    @Convert(converter = GroupInvoiceStatusConverter.class)
    private GroupInvoiceStatus groupInvoiceStatus;

    private String companyName;

    private Long companyId;

    @Convert(converter = InvoiceTypeConverter.class)
    private InvoiceType invoiceType;

    private Integer currency;

}
