package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.TContractorInvoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Spring Data JPA repository for the ContractorInvoiceRepository entity.
 * <AUTHOR> zhang.lei
 * @date : 2023-6-6
 */
@Repository
public interface ContractorInvoiceRepository extends JpaRepository<TContractorInvoice, BigInteger> {

    @Query(value = " select id from t_contractor_invoice where invoice_status!=3 and  assignment_id =?1 and week_ending_date =?2 and invoice_type=?3",nativeQuery = true)
    List<BigInteger> getIdByAssignmentIdAndWeekEndingDate(BigInteger assignmentId, Timestamp weekEndingDate,Integer invoiceType);

    @Query(value = " select id from t_contractor_invoice where invoice_status!=3 and  assignment_id =?1 and week_ending_date =?2 and invoice_type=?3 and record_index = ?4", nativeQuery = true)
    List<BigInteger> getIdByAssignmentIdAndWeekEndingDateAndRecordIndex(BigInteger assignmentId, Timestamp weekEndingDate, Integer invoiceType, Integer recordIndex);

    @Query(value = "select max(invoice_number) as maxNumber from t_contractor_invoice",nativeQuery = true)
    String getMaxInvoiceNumber();

    @Query(value = " select id from t_contractor_invoice where invoice_status in ( ?2 ) and id in ( ?1 )",nativeQuery = true)
    List<BigInteger> queryGroupedStatusByIds(Set<Long> invoiceIdList, List<Integer> status);

    @Modifying
    @Transactional
    @Query(value = "update  t_contractor_invoice  set invoice_status=?2,last_modified_date=now() where id in ( ?1 ) ",nativeQuery = true)
    void updateInvoiceStatusByIds(Set<Long> invoiceIdList, Integer status);

    @Modifying
    @Transactional
    @Query(value = "update  t_contractor_invoice  set invoice_status=?2,last_modified_date=now(),group_invoice_number = null where id in ?1 and invoice_status=2 ",nativeQuery = true)
    void updateInvoiceStatusByIdsAndInvoiceStatus(Set<Long> invoiceIdList, Integer status);

    @Query(value = "select *\n" +
            "from t_contractor_invoice t\n" +
            "where t.tenant_id=?1 and t.company_id =?2 and t.talent_id in ( ?3 ) and t.invoice_type in (?6) and t.invoice_status=1 and t.week_ending_date BETWEEN ?4 and ?5",nativeQuery = true)
    List<TContractorInvoice> queryInvoiceByCondition(BigInteger tenantId,BigInteger companyId,List<BigInteger> talentId,Timestamp startDate,Timestamp endDate,List<Integer> invoiceType);

    @Modifying
    @Transactional
    @Query(value = "update  t_contractor_invoice  set invoice_status=?2,group_invoice_number=?3,last_modified_date=now() where id in ( ?1 ) ",nativeQuery = true)
    void updateInvoiceStatusAndGroupInvoiceNumberByIds(Set<Long> invoiceIdList, Integer status,String groupInvoiceNumber);


    @Query(value = "select c.* from t_group_invoice_record t\n" +
            "join t_contractor_invoice c on c.id = t.invoice_id\n" +
            "where t.internal_invoice_type=2 and t.`status`=1 and t.group_invoice_id=?1 order by c.talent_id asc,c.invoice_type asc,c.week_ending_date asc",nativeQuery = true)
    List<TContractorInvoice> queryTimesheetInvoiceByGroupInvoiceId(BigInteger groupInvoiceId);

    @Query(value = "select distinct c.* from t_group_invoice_record t " +
            " join t_contractor_invoice c on c.id = t.invoice_id " +
            " left join t_invoice_timesheet_info ti on ti.invoice_id = c.id " +
            "where t.`status`=1 and t.group_invoice_id in ?1 order by c.talent_id asc,c.week_ending_date asc,c.invoice_type desc,c.id asc,ti.quantity_type asc ",nativeQuery = true)
    List<TContractorInvoice> queryInvoiceByGroupInvoiceId(List<BigInteger> groupInvoiceIds);

    @Query(value = " select * from t_contractor_invoice where invoice_status!=3 and  assignment_id in ( ?1 ) and week_ending_date in ( ?2 ) and invoice_type=?3",nativeQuery = true)
    List<TContractorInvoice> getAllByAssignmentIdAndWeekEndingDate(List<Long> assignmentId, List<LocalDate> weekEndingDate, Integer invoiceType);

    @Query(value = " select * from t_contractor_invoice where invoice_status!=3 and  assignment_id in ( ?1 ) and week_ending_date in ( ?2 ) and record_index in (?4) and invoice_type=?3", nativeQuery = true)
    List<TContractorInvoice> getAllByAssignmentIdAndWeekEndingDateAndRecordIndex(List<Long> assignmentId, List<LocalDate> weekEndingDate, Integer invoiceType, List<Integer> recordIndex);

    TContractorInvoice findByIdAndTenantId(BigInteger id,BigInteger tenantId);

    @Query(value = " select * from t_contractor_invoice where id in ( ?1 ) ", nativeQuery = true)
    List<TContractorInvoice> queryInvoiceByIds(List<BigInteger> idList);

    @Query(value = " select * from t_contractor_invoice where id in ( ?1 ) ", nativeQuery = true)
    List<TContractorInvoice> queryInvoiceByIdIn(Set<Long> idList);


    @Query(value = " select count(1) from t_contractor_invoice where assignment_id = ?1 and invoice_status != 3; ",nativeQuery = true)
    Long searchNoVoidInvoiceCountByAssignmentId(Long assignmentId);

    @Query(value = " select * from t_contractor_invoice where assignment_id = ?1 and week_ending_date in( ?2 ) and invoice_status != 3; ",nativeQuery = true)
    List<TContractorInvoice> searchNoVoidInvoiceCountByAssignmentIdAndWeekEndingDate(Long assignmentId,List<LocalDate> weekEndingDate);

    List<TContractorInvoice> findAllByAssignmentIdIn(Collection<BigInteger> assignmentIds);
}