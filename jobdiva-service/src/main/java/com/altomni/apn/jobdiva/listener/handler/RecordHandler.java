package com.altomni.apn.jobdiva.listener.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetBreakTimeRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetComments;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.repository.timesheet.ExpenseRecordRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetBreakTimeRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetCommentsRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class RecordHandler implements JobdivaToApnHandler{

    @Resource
    private TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    private TimeSheetBreakTimeRepository breakTimeRepository;

    @Resource
    private ExpenseRecordRepository expenseRecordRepository;

    @Resource
    private TimeSheetCommentsRepository commentsRepository;

    @Override
    public void execute(JSONObject jsonObject) {
        log.info("[APN RecordHandler] apn to jobdiva record save to db is start");
        //直接2个数据一起处理
        //comments
        Optional.ofNullable(jsonObject.getJSONObject(TIMESHEET_COMMENTS)).ifPresent(obj -> {
            TimeSheetComments timeSheetComments = obj.toJavaObject(TimeSheetComments.class);
            commentsRepository.saveAndFlush(timeSheetComments);
            log.info("[APN RecordHandler] comment save or update is success, id = {}", timeSheetComments.getId());
        });
        JSONArray timeSheetArray = jsonObject.getJSONArray(TIMESHEET);
        if (CollUtil.isNotEmpty(timeSheetArray)) {
            List<TimeSheetRecord> list = timeSheetArray.toJavaList(TimeSheetRecord.class);
            if (CollUtil.isNotEmpty(list)) {
                TimeSheetRecord timeSheetRecord = list.get(0);
                timeSheetRecordRepository.deleteByAssignmentIdAndWeekEndingDate(timeSheetRecord.getAssignmentId(), timeSheetRecord.getWeekEndingDate());
                timeSheetRecordRepository.saveAllAndFlush(list.stream().filter(record -> !record.getWorkDate().isEqual(record.getWeekEnd())).toList());
                timeSheetRecordRepository.saveAllAndFlush(list.stream().filter(record -> record.getWorkDate().isEqual(record.getWeekEnd())).toList());
                log.info("[hr svr RecordHandler] timeSheetRecord save or update is success, assignmentId = {}, weekEndingDate = {}", timeSheetRecord.getAssignmentId(), timeSheetRecord.getWeekEndingDate());
                //breakTime
                Optional.ofNullable(jsonObject.getJSONArray(TIMESHEET_BREAKTIME)).ifPresent(array -> {
                    List<TimeSheetBreakTimeRecord> breakTimeRecordList = array.toJavaList(TimeSheetBreakTimeRecord.class);
                    LocalDate startDate = timeSheetRecord.getWeekEndingDate().plusDays(-6);
                    breakTimeRepository.deleteByDate(startDate, timeSheetRecord.getWeekEndingDate(), timeSheetRecord.getTalentId(), timeSheetRecord.getAssignmentId());
                    breakTimeRepository.saveAllAndFlush(breakTimeRecordList);
                    log.info("[hr svr RecordHandler] breakTime save or update is success");
                });
            }
        }
        Optional.ofNullable(jsonObject.getJSONArray(TIMESHEET_EXPENSE)).ifPresent(array -> {
            List<ExpenseRecord> list = array.toJavaList(ExpenseRecord.class);
            //打卡的数据直接保存的时候,是做的先删除在新增,这个时候需要处理
            ExpenseRecord expenseRecord = list.get(0);
            expenseRecordRepository.deleteAllByWeekEndAndTalentIdAndAssignmentIdAndExpenseIndex(expenseRecord.getWeekEnd(), expenseRecord.getTalentId(), expenseRecord.getAssignmentId(), expenseRecord.getExpenseIndex());
            expenseRecordRepository.saveAllAndFlush(list.stream().filter(record -> !record.getWorkDate().isEqual(record.getWeekEnd())).toList());
            expenseRecordRepository.saveAllAndFlush(list.stream().filter(record -> record.getWorkDate().isEqual(record.getWeekEnd())).toList());
            log.info("[APN RecordHandler] assignment save or update is success");
        });
        log.info("[APN RecordHandler] apn to jobdiva record save to db is success");
    }

    @Override
    public boolean isSupport(JobdivaDataSyncTypeEnum typeEnum) {
        return List.of(JobdivaDataSyncTypeEnum.TIME_SHEET_RECORD,
                JobdivaDataSyncTypeEnum.TIME_SHEET_EXPENSE).contains(typeEnum);
    }

}
