package com.altomni.apn.jobdiva.service.dto.timesheet;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.enumeration.jobdiva.OverTimeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetHolidayRecord;
import com.altomni.apn.jobdiva.service.vo.timesheet.RecordHeadInfoVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Data
@ApiModel
public class BreakTimeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    protected List<Map<String,Object>> records;
    protected List<TimeSheetRecordDTO> timeSheet;
    protected TimeSheetStatus status;
    protected String comments;
    protected String weekEndingDate;
    protected LocalDate weekStart;
    protected LocalDate weekEnd;
    protected Long assignmentId;
    protected RecordHeadInfoVO headInfo;
    private TimeSheetType type;
    private OverTimeType overTimeType;
    private List<TimeSheetHolidayRecordDto> holidayRecordSaveList;
    private List<TimeSheetHolidayRecord> holidayRecordList;
    private BigDecimal rate;
    private Integer currency;
    private Boolean invoiceFlag = false;
    private Instant appliedDate;
    private Instant approvedDate;
    private Integer expenseIndex = 0;

    public void hiddenRate() {
        if (CollUtil.isNotEmpty(holidayRecordList)) {
            for (TimeSheetHolidayRecord timeSheetHolidayRecord : holidayRecordList) {
                timeSheetHolidayRecord.setRate(null);
            }
        }
    }

}
