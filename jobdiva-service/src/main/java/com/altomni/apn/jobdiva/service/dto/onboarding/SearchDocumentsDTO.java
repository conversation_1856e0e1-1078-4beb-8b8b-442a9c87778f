package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SearchDocumentsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long talentRecruitmentProcessId;

    private List<Long> historyIds;

    private List<Long> documents;

    @Pattern(regexp = "^[A-Za-z0-9_\\-\\u4e00-\\u9fa5\\s]+$",message = "The name format is incorrect")
    private String name;

    private String timezone;
}
