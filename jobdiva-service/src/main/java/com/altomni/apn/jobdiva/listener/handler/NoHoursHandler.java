package com.altomni.apn.jobdiva.listener.handler;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.jobdiva.domain.timesheet.ApproveRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetComments;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.repository.timesheet.ApproveRecordRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetBreakTimeRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetCommentsRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class NoHoursHandler implements JobdivaToApnHandler {

    @Resource
    private TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    private TimeSheetCommentsRepository timeSheetCommentsRepository;

    @Resource
    private ApproveRecordRepository approveRecordRepository;;

    @Resource
    private TimeSheetBreakTimeRepository breakTimeRepository;

    @Override
    public void execute(JSONObject jsonObject) {
        log.info("[APN NoHoursHandler] jobdiva to apn nohours is start");
        JSONArray jsonArray = jsonObject.getJSONArray(TIMESHEET);
        if (jsonArray.isEmpty()) {
            return;
        }
        List<TimeSheetRecord> list = jsonArray.toJavaList(TimeSheetRecord.class);
        if (list == null || list.isEmpty()) {
            return;
        }
        TimeSheetRecord timeSheetRecord = list.get(0);
        //save
        Optional.ofNullable(jsonObject.getJSONObject(TIMESHEET_COMMENTS)).ifPresent(array -> {
            TimeSheetComments comments = array.toJavaObject(TimeSheetComments.class);
            timeSheetCommentsRepository.saveAndFlush(comments);
            log.info("[APN NoHoursHandler] comments save or update is success, assignmentId = {}, weekEnd = {}", comments.getAssignmentId(), comments.getWorkDate());
        });
        //approve
        Optional.ofNullable(jsonObject.getJSONObject(TIMESHEET_APPROVE)).ifPresent(obj -> {
            ApproveRecord approveRecord = obj.toJavaObject(ApproveRecord.class);
            approveRecordRepository.saveAndFlush(approveRecord);
            log.info("[APN NoHoursHandler] approve save or update is success, assignmentId = {}, weekEnd = {}", approveRecord.getAssignmentId(), approveRecord.getWeekEnd());
        });
        //timeSheetRecord
        breakTimeRepository.deleteByDate(timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEnd(), timeSheetRecord.getTalentId(), timeSheetRecord.getAssignmentId());
        timeSheetRecordRepository.deleteByAssignmentIdAndWeekEnd(timeSheetRecord.getAssignmentId(), timeSheetRecord.getWeekEnd());
        timeSheetRecordRepository.saveAllAndFlush(list.stream().filter(record -> !record.getWorkDate().isEqual(record.getWeekEnd())).toList());
        timeSheetRecordRepository.saveAllAndFlush(list.stream().filter(record -> record.getWorkDate().isEqual(record.getWeekEnd())).toList());
        log.info("[APN NoHoursHandler] assignment save or update is success, assignmentId = {}, weekEndingDate = {}", timeSheetRecord.getAssignmentId(), timeSheetRecord.getWeekEndingDate());
        log.info("[APN NoHoursHandler] jobdiva to apn nohours is success");
    }

    @Override
    public boolean isSupport(JobdivaDataSyncTypeEnum typeEnum) {
        return JobdivaDataSyncTypeEnum.TIME_SHEET_NO_HOURS == typeEnum;
    }

}
