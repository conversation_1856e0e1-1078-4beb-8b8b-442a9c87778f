package com.altomni.apn.jobdiva.web.rest.timesheet;


import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetRecordAmService;
import com.altomni.apn.jobdiva.service.vo.timesheet.EndingDateListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Slf4j
@Api(tags = {"TimeSheetRecordAM"})
@RestController
@RequestMapping("/api/v3/timesheet/am/record")
public class TimeSheetRecordAmResource {

    @Resource
    @Qualifier("timeSheetRecordAmService")
    private TimeSheetRecordAmService recordService;

    /**
     *
     * @param record
     * @return
     */
    @PostMapping("/save")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<TimeSheetRecord>> save(@RequestBody BreakTimeAmDTO record) {
        log.info("[timesheet: User @{}] am to save time sheet record, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(record));
        checkAuthEdit(record.getAssignmentId());
        if (!TimeSheetUtil.checkTimeTypeAndOverTimeType(record.getAssignmentId(), record.getType(), record.getOverTimeType())) {
            throw new CustomParameterizedException("please check the time sheet format");
        }
        List<TimeSheetRecord> newRecord = recordService.saveRecord(record);
        return ResponseEntity.ok(newRecord);
    }

    /**
     *
     * @param dto
     * @return
     */
    @PostMapping("/dateSelect")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<TimeSheetRecord>> saveRecordForDateSelect(@RequestBody BreakTimeAmDTO  dto) {
        log.info("[timesheet: User @{}] am to save select time sheet record, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        checkAuthEdit(dto.getAssignmentId());
        if (!TimeSheetUtil.checkTimeTypeAndOverTimeType(dto.getAssignmentId(), dto.getType(), dto.getOverTimeType())) {
            throw new CustomParameterizedException("please check the time sheet format");
        }
        List<TimeSheetRecord>  newRecord = recordService.saveRecordForDateSelect(dto);
        return ResponseEntity.ok(newRecord);
    }

    @PostMapping("/list")
    @Timed
    public ResponseEntity<BreakTimeDTO> list(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] am to query to break list", SecurityUtils.getUserId());
        checkAuthView(dto.getAssignmentId());
        BreakTimeDTO records = recordService.findRecords(dto);
        return ResponseEntity.ok(records);
    }

    /**
     *
     * @param dto
     * @return
     */
    @PostMapping("/client")
    @Timed
    public ResponseEntity<BreakTimeDTO> findById(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] am to find detail", SecurityUtils.getUserId());
        BreakTimeDTO records = recordService.findRecordById(dto);
        return ResponseEntity.ok(records);
    }

    /**
     *GET talent breaktime record
     * @param dto
     * @return
     */
    @PostMapping("/breaktime/list")
    @Timed
    public ResponseEntity<BreakTimeDTO> getBreaktime(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] am to get break time", SecurityUtils.getUserId());
        checkAuthView(dto.getAssignmentId());
        BreakTimeDTO records = recordService.findBreakTimeRecord(dto);
        return ResponseEntity.ok(records);
    }

    @PostMapping("/breaktime/save")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> saveBreakTime(@RequestBody BreakTimeAmDTO dto) {
        log.info("[timesheet: User @{}] am to save break time record, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        checkAuthEdit(dto.getAssignmentId());
        if (!TimeSheetUtil.checkTimeTypeAndOverTimeType(dto.getAssignmentId(), dto.getType(), dto.getOverTimeType())) {
            throw new CustomParameterizedException("please check the time sheet format");
        }
        Integer records = recordService.saveBreakTime(dto);
        return ResponseEntity.ok(records);
    }

    @Deprecated
    @PostMapping("/summary")
    @Timed
    public ResponseEntity<SummaryDataVO> summary(@RequestBody SummaryQueryAmDTO dto) {
        log.info("[timesheet: User @{}] am to query summary", SecurityUtils.getUserId());
        checkAuthView(dto.getRecordIds());
        SummaryDataVO result = recordService.summary(dto);
        return ResponseEntity.ok(result);
    }

    /**
     * no hour
     * @param dto
     * @return
     */
    @PostMapping("/nohour")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> noHour(@RequestBody NoHourDTO dto) {
        log.info("[timesheet: User @{}] am to noHour, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        checkAuthEdit(dto.getAssignmentId());
        if (!TimeSheetUtil.checkTimeTypeAndOverTimeType(dto.getAssignmentId(), dto.getType(), dto.getOverTimeType())) {
            throw new CustomParameterizedException("please check the time sheet format");
        }
        Integer result = recordService.noHour(dto);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/search")
    @Timed
    public ResponseEntity<SummaryDataVO> search(@RequestBody AdvanceSearchDTO dto) {
        log.info("[timesheet: User @{}] am to search, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        SummaryDataVO result= recordService.search(dto);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/approve")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> timeSheetApprove(@RequestBody ApproveDTO dto) {
        log.info("[timesheet: User @{}] am to approve timesheet record, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        checkAuthEdit(dto.getRecordIds());
        Integer records = recordService.timeSheetApprove(dto);
        return ResponseEntity.ok(records);
    }

    /**
     * get weekending date by assignment id
     * @param talentId
     * @return
     */
    @GetMapping("/weekEndingList")
    @Timed
    public ResponseEntity<List<EndingDateListVO>> weekEndingList(@RequestParam(required = true) Long talentId ) {
        log.info("[timesheet: User @{}] am to get weekEndingList", SecurityUtils.getUserId());
        List<EndingDateListVO> records = recordService.weekEndingList(talentId);
        return ResponseEntity.ok(records);
    }

    @PostMapping("/holiday/ot")
    public ResponseEntity<Integer> saveHolidayRecord(@RequestBody HolidayRecordSaveDto saveDto) {
        log.info("[timesheet: User @{}] am to save holiday record, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(saveDto));
        return ResponseEntity.ok(recordService.saveHolidayRecords(saveDto));
    }


    /**
     * timesheet 通过 excel 上传
     * @param dtoList
     * @return
     */
    @PostMapping("/import-timesheet-by-excel")
    public ResponseEntity<Map<String, Object>> importTimesheetByExcel(@RequestBody List<ImportTimesheetForExcelDTO> dtoList) {
        return ResponseEntity.ok(recordService.importTimesheetByExcel(dtoList));
    }

    /**
     *
     * @param response
     * @param dto
     * @return
     */
    @PostMapping("/downloadSummary")
    @Timed
    public void downloadSummary(HttpServletResponse response, @RequestBody SummaryQueryDTO dto) {
        log.info("[downloadSummary {}:", SecurityUtils.getUserId());
        recordService.timeSheetSummaryDownload(dto,response);
    }

    private void checkAuthEdit(Long assignmentId) {
        if (!SecurityUtils.isAdmin() && !SecurityUtils.isTimesheetAdmin() && !recordService.hasPermissionEdit(assignmentId, RecordType.TIME_SHEET, SecurityUtils.getUserId()) && !SecurityUtils.isTimesheetAdmin()) {
            throw new CustomParameterizedException("Access denied !");
        }
    }

    private void checkAuthView(Long assignmentId) {
        if (!SecurityUtils.isAdmin() && !SecurityUtils.isTimesheetAdmin() && !recordService.hasPermission(assignmentId, RecordType.TIME_SHEET, SecurityUtils.getUserId()) && !SecurityUtils.isTimesheetAdmin()) {
            throw new CustomParameterizedException("Access denied !");
        }
    }

    @Deprecated
    private void checkAuthView(Set<Long> recordIds) {
        if (!SecurityUtils.isAdmin() && !SecurityUtils.isTimesheetAdmin() && !recordService.hasPermission(recordIds, RecordType.TIME_SHEET, SecurityUtils.getUserId()) && !SecurityUtils.isTimesheetAdmin()) {
            throw new CustomParameterizedException("Access denied !");
        }
    }

    private void checkAuthEdit(Set<Long> recordIds) {
        if (!SecurityUtils.isAdmin() && !SecurityUtils.isTimesheetAdmin() && !recordService.hasPermissionEdit(recordIds, RecordType.TIME_SHEET, SecurityUtils.getUserId()) && !SecurityUtils.isTimesheetAdmin()) {
            throw new CustomParameterizedException("Access denied !");
        }
    }

}
