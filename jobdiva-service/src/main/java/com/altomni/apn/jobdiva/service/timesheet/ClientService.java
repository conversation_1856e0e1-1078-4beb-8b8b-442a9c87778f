package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.ManagerRoleType;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.vo.timesheet.RecordDetailVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimeSheetSummaryVO;
import com.itextpdf.text.Document;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.Set;

public interface ClientService {

    Integer timeSheetApprove(ApproveDTO dto, ManagerRoleType am);

    Integer expenseApprove(ApproveDTO dto, ManagerRoleType am);

    Boolean isTimeSheetClientUser(Set<Long> recordIds, Long userId, RecordType recordType);

    SummaryDataVO timeSheetSummary(SummaryQueryDTO dto);

    SummaryDataVO expenseSummary(SummaryQueryDTO dto);

    void timeSheetSummaryDownload(SummaryQueryDTO dto, HttpServletResponse response);

    void expenseSummaryDownload(SummaryQueryDTO dto, HttpServletResponse response);

    void timeSheetRecordDetailDownload(RecordSearchDTO dto, HttpServletResponse response, Boolean is24timeFlag);

    void timeSheetRecordDetailDownloadPdf(RecordSearchDTO dto, HttpServletResponse response, Boolean is24timeFlag);

    void expenseRecordDetailDownload(RecordSearchDTO dto, HttpServletResponse response);

    void expenseRecordDetailDownloadPdf(RecordSearchDTO dto, HttpServletResponse response);

    RecordDetailVO expenseRecordDetail(RecordSearchDTO dto);

    RecordDetailVO timesheetRecordDetail(RecordSearchDTO dto);

    void timeSheetRecordDetailDownloadBatchPdf(DownloadPdfDto dto, HttpServletResponse response, Boolean is24timeFlag) throws IOException;

    void expenseRecordDetailDownloadBatchPdf(DownloadPdfDto dto, HttpServletResponse response) throws IOException;

    void timesheetDetailSearchAndBatchPdf(RecordSearchDTO dto, Document document);
}
