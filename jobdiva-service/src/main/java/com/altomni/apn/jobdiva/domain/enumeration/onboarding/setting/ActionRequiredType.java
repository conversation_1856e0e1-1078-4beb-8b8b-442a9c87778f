package com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ActionRequiredType enumeration.
 */
public enum ActionRequiredType implements ConvertedEnum<Integer> {
    MUST_BE_SIGNED_AND_RETURNED(0, "MUST_BE_SIGNED_AND_RETURNED"),
    READ_ONLY(1, "READ_ONLY");

    // static resolving:
    public static final ReverseEnumResolver<ActionRequiredType, Integer> resolver =
        new ReverseEnumResolver<>(ActionRequiredType.class, ActionRequiredType::toDbValue);
    private final int dbValue;
    private final String name;

    ActionRequiredType(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static ActionRequiredType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
