package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.service.vo.timesheet.RecordHeadInfoVO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface TimeSheetRecordRepository extends JpaRepository<TimeSheetRecord, Long> {

    @Query(value = " select * from time_sheet_record tsr where talent_id = ?3 and work_date between ?1 and ?2 and assignment_id = ?4 ", nativeQuery = true)
    List<TimeSheetRecord> findAllByDate(LocalDate startDate, LocalDate endDate, Long talentId, Long assignmentId);

    @Query(value = " select * from time_sheet_record tsr where talent_id = ?2 and work_date <= ?1 and assignment_id = ?3 ", nativeQuery = true)
    List<TimeSheetRecord> findAllByStartDate(LocalDate startDate, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from time_sheet_record where assignment_id = ?1 and week_end = ?2 ", nativeQuery = true)
    void deleteByAssignmentIdAndWeekEnd(Long assignmentId, LocalDate weekEnd);

    @Query(value = " select * from time_sheet_record tsr where talent_id = ?2 and work_date >= ?1 and assignment_id = ?3 ", nativeQuery = true)
    List<TimeSheetRecord> findAllByEndDate(LocalDate endDate, Long talentId, Long assignmentId);

    @Query(value = " select * from time_sheet_record tsr where talent_id = ?3 and work_date between ?1 and ?2 and assignment_id = ?4 and status in (?5) ", nativeQuery = true)
    List<TimeSheetRecord> findAllByDateAndStatus(LocalDate startDate, LocalDate endDate, Long talentId, Long assignmentId, List<Integer> statusList);

    List<TimeSheetRecord> findAllByWorkDateInAndAssignmentId(List<LocalDate> localDateList, Long assignmentId);

    @Query(value = " select * from time_sheet_record tsr where talent_id = ?2 and work_date = ?1 and assignment_id = ?3 ", nativeQuery = true)
    TimeSheetRecord findByDate(LocalDate workDate, Long talentId, Long assignmentId);

    List<TimeSheetRecord> findAllByAssignmentIdAndAndWeekEndingDate(Long assignmentId, LocalDate weekEndingDate);

    @Query(value = " select * from time_sheet_record tsr where assignment_id = ?1 ", nativeQuery = true)
    List<TimeSheetRecord> findAllByAssignmentId(Long assignmentId);

    @Query(value = """
    select re.* from time_sheet_week_ending_record tsr 
    inner join time_sheet_record re on re.id = tsr.record_id 
    inner join assignment_bill_info abi on abi.assignment_id = tsr.assignment_id and abi.overtime_type = 0 
    where tsr.start_date <= ?1 and tsr.end_date >= ?2 and tsr.week_ending_date = ?2 and tsr.assignment_id in (?3) and tsr.tenant_id = ?4
""", nativeQuery = true)
    List<TimeSheetRecord> findTimesheetRecordListByStartDateAndEndDateAndAssignmentIdList(LocalDate startDate, LocalDate endDate, List<Long> assignmentIdList, Long tenantId);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM time_sheet_record where work_date in (?1) and talent_id = ?2 and assignment_id = ?3", nativeQuery = true)
    void deleteAllByWorkDateInAndTalentIdAndAssignmentId(Set<LocalDate> dates, Long talentId, Long assignmentId);

    @Transactional
    @Modifying
    @Query(value = " DELETE FROM time_sheet_record where id = ?1 ", nativeQuery = true)
    void deleteById(Long id);

    @Query(value = " select * from time_sheet_record where id  in ?1 ", nativeQuery = true)
    List<TimeSheetRecord> findAllByIdIs(Set<Long> ids);

    @Query(value = " select * from time_sheet_record where id in ?1 ", nativeQuery = true)
    List<TimeSheetRecord> findAllByIdIn(List<Long> ids);

    @Modifying
    @Transactional
    @Query(value = " update time_sheet_record set status = ?3  where work_date in ?1 and talent_id = ?2 and status != ?4 and assignment_id = ?5 ", nativeQuery = true)
    void updateStatusByDates(Set<LocalDate> value, Long talentId, int status, int noRecord, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " update time_sheet_record set status = ?3  where work_date in ?1 and talent_id = ?2 and assignment_id = ?4 ", nativeQuery = true)
    void updateStatusByDates(Set<LocalDate> value, Long talentId, int status, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " update time_sheet_record set week_start = ?4, week_end = ?5  where work_date in ?1 and talent_id = ?2 and assignment_id = ?3 ", nativeQuery = true)
    void updateWeekStartAndWeekEndByDates(Set<LocalDate> localDates, Long talentId, Long assignmentId, LocalDate weekStart, LocalDate weekEnd);

    @Query(value = "select * from time_sheet_record where work_date = week_end and assignment_id = ?1 ", nativeQuery = true)
    List<TimeSheetRecord> findAllByAssignmentIdAndWeekEndEqWorkDate(Long assignment);

    @Modifying
    @Transactional
    @Query(value = "update time_sheet_record set time_sheet_type = ?3 where talent_id=?1  and assignment_id = ?2", nativeQuery = true)
    Integer updateTimeSheetTypeByAssignmentIdAndTalentId(Long talentId, Long assignmentId, Integer timeSheetType);

    @Modifying
    @Transactional
    @Query(value = "update time_sheet_record set status = 5 where status = 3 and work_date < ?1", nativeQuery = true)
    Integer updateRecordStatusInCurrentDate(LocalDate currentDate);

    @Modifying
    @Transactional
    @Query(value = " delete from time_sheet_record where assignment_id = ?1 ", nativeQuery = true)
    void deleteByAssignmentId(Long assignmentId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.timesheet.RecordHeadInfoVO(T.fullName,T.id,J.title,J.id,CO.fullBusinessName,CCT.fullName,PCCT.fullName,concat(U.firstName, ' ',U.lastName)) " +
            " from TimeSheetRecord as TSR " +
            " left join TimeSheetManager tm on tm.talentId = TSR.talentId and tm.assignmentId = TSR.assignmentId and tm.role =0 " +
            " left join SalesLeadClientContact pcc on tm.clientId = pcc.id " +
            " left join TalentV3 PCCT on PCCT.id = pcc.talentId " +
            " left join TalentAssigment TA on TSR.assignmentId = TA.id " +
            " left join JobV3 J on J.id =  TA.jobId " +
            " left join TalentV3 T on T.id =  TA.talentId " +
            " left join Company CO on CO.id = TA.companyId " +
            " left join ApproveRecord AR on AR.recordId = TSR.id and AR.role = 2 and AR.type = 0 " +
            " left join SalesLeadClientContact CC on CC.id = AR.operator " +
            " left join TalentV3 CCT on CCT.id = CC.talentId " +
            " left join ApproveRecord AAR on AAR.recordId = TSR.id and AAR.role =3 and AAR.type = 0 " +
            " left join User U on U.id  =AAR.operator " +
            " where TSR.assignmentId = ?1 and TSR.workDate = ?2 " +
            " group by TA.id ")
    RecordHeadInfoVO findTimeSheetJobInfo(Long assignmentId, LocalDate weekend);

    @Modifying
    @Transactional
    @Query(value = " delete from time_sheet_record  where work_date between ?1 and ?2 and talent_id = ?3 and assignment_id = ?4 ", nativeQuery = true)
    void deleteByDate(LocalDate startDate, LocalDate endDate, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from time_sheet_record  where work_date <= ?1 and talent_id = ?2 and assignment_id = ?3 ", nativeQuery = true)
    void deleteByStartDate(LocalDate startDate, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from time_sheet_record  where work_date >= ?1 and talent_id = ?2 and assignment_id = ?3 ", nativeQuery = true)
    void deleteByEndDate(LocalDate endDate, Long talentId, Long assignmentId);

    List<TimeSheetRecord> findAllByStatusInAndTalentIdAndAssignmentId(List<TimeSheetStatus> statusList, Long talentId, Long assignmentId);

    @Query(value = " select count(tsr.id) from time_sheet_record tsr where tsr.status in ?1 and tsr.talent_id = ?2 and tsr.assignment_id = ?3 ", nativeQuery = true)
    Integer countByStatusInAndTalentIdAndAssignmentId(List<Integer> statusList, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " update time_sheet_record set work_hours = null, regular_hours = null, over_time = null, double_time = null, total_hours = null, status = ?3 where work_date between ?1 and ?2 and talent_id = ?4 and assignment_id = ?5 ", nativeQuery = true)
    void resetData(LocalDate starDate, LocalDate endDate, Integer status, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " update time_sheet_record set work_hours = null, regular_hours = null, over_time = null, double_time = null, total_hours = null, status = ?2, submitted_date = ?5 where work_date = ?1 and talent_id = ?3 and assignment_id = ?4 ", nativeQuery = true)
    void resetData(LocalDate workDate, Integer status, Long talentId, Long assignmentId, Instant now);

    @Modifying
    @Transactional
    @Query(value = " update time_sheet_record set submitted_date = null, status = ?3 where work_date between ?1 and ?2 and talent_id = ?4 and assignment_id = ?5 ", nativeQuery = true)
    void resetSubmittedDateAndStatus(LocalDate starDate, LocalDate endDate, Integer status, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " update time_sheet_record set submitted_date = null, status = ?1 where id in (?2) ", nativeQuery = true)
    void updateTimeSheetStatusByIdList(Integer status, List<Long> idList);

    @Query(value = " select id from time_sheet_record where work_date between ?1 and ?2 and talent_id = ?3 and assignment_id = ?4 ", nativeQuery = true)
    List<Long> findByDateAndTalentIdAndAssignmentId(LocalDate starDate, LocalDate endDate, Long talentId, Long assignmentId);

    @Query(value = " SELECT DISTINCT tsr.id FROM time_sheet_record tsr " +
            " INNER JOIN timesheet_talent_assignment tta ON tsr.assignment_id = tta.id " +
            " INNER JOIN business_flow_administrator psl ON psl.company_id = tta.company_id  " +
            " where tsr.id in ?1 and psl.sales_lead_role in (0,3) and psl.user_id = ?2 ", nativeQuery = true)
    List<Long> findRecordIdsByCompanyAM(Set<Long> ids, Long userId);

    @Query(value = " SELECT tsr.id FROM time_sheet_record tsr " +
        " INNER JOIN timesheet_talent_assignment tta ON tta.id = tsr.assignment_id " +
        " INNER JOIN business_flow_administrator AS psl ON psl.company_id = tta.company_id " +
        " where tsr.id in ?1 and psl.sales_lead_role in (0,3) and psl.user_id = ?2 limit 1 ", nativeQuery = true)
    Long findRecordIdByCompanyAM(Set<Long> ids, Long userId);

    @Query(value = " SELECT tta.id FROM timesheet_talent_assignment tta  " +
        " inner JOIN business_flow_administrator AS psl ON psl.company_id = tta.company_id " +
        " where tta.id = ?1 and psl.sales_lead_role in (0,3) and psl.user_id = ?2 LIMIT 1 ", nativeQuery = true)
    Long findAssignmentIdByCompanyAM(Long assignmentId, Long userId);

    @Query(value = " SELECT DISTINCT TTA.id FROM timesheet_talent_assignment TTA " +
        " inner join START s ON TTA.start_id = s.id " +
        " inner JOIN talent_recruitment_process_kpi_user AC ON ac.talent_recruitment_process_id = s.talent_recruitment_process_id " +
        " inner JOIN time_sheet_record tsr ON tsr.assignment_id = TTA.id " +
        " WHERE AC.user_role in (1,2) AND AC.user_id = ?1 AND tsr.id in ?2 ", nativeQuery = true)
    List<Long> findAssignmentIdByApplicationRecruiterOrSourcer(Long userId, List<Long> recordIds);

    @Query(value = " SELECT DISTINCT TTA.id FROM timesheet_talent_assignment TTA " +
        " INNER JOIN START s ON TTA.start_id = s.id " +
        " INNER JOIN talent_recruitment_process_kpi_user AC ON ac.talent_recruitment_process_id = s.talent_recruitment_process_id " +
        " WHERE AC.user_role in (1,2) AND AC.user_id = ?1 AND TTA.id = ?2 ", nativeQuery = true)
    List<Long> findAssignmentIdByApplicationRecruiterOrSourcer(Long userId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = "update time_sheet_record set created_by = ?2, last_modified_by = ?2 where id in (?1)", nativeQuery = true)
    void updateCreateByAndLastModifiedByById(List<Long> idList, String customBy);


    @Modifying
    @Transactional
    @Query(value = "update time_sheet_record set created_by = ?2, last_modified_by = ?2 where assignment_id in (?1) and status in (3,5) ", nativeQuery = true)
    void updateCreateByAndLastModifiedByByAssignmentId(Long assignmentId, String customBy);

    @Modifying
    @Transactional
    @Query(value = "update time_sheet_record set last_modified_date = ?2 where id in (?1)", nativeQuery = true)
    void updateLastModifiedDateById(List<Long> idList, Instant updateDate);


    @Query(nativeQuery = true, value = """
    select tsr.* from time_sheet_record tsr
    where tsr.assignment_id = ?1 and tsr.week_end = tsr.work_date
    """)
    List<TimeSheetRecord> findAllByAssignmentIdAndWeekEndIsEqualWorkDate(Long assignmentId);

    @Query(nativeQuery = true, value = """
    select tsr.* from time_sheet_record tsr
    where tsr.assignment_id = ?1 and tsr.week_end = tsr.work_date and tsr.work_date <= now() 
    and tsr.status in (?2)
    """)
    List<TimeSheetRecord> findExpiredRecordList(Long assignmentId, List<Integer> statusList);


    @Modifying
    @Transactional
    @Query(value = " UPDATE time_sheet_record SET week_id = ?1 WHERE assignment_id = ?2 and week_end = ?3 ", nativeQuery = true)
    void updateWeekId(Long recordId, Long assignmentId, LocalDate weekEnd);

    @Modifying
    @Transactional
    @Query(value = " update time_sheet_record set created_by = ?2 , last_modified_by = ?3 where id = ?1 ", nativeQuery = true)
    void updateCreateAndModifiedById(Long id, String createdBy, String modifiedBy);

    List<TimeSheetRecord> findAllByAssignmentIdAndAndWeekEnd(Long assignmentId, LocalDate weekEnd);

    @Modifying
    @Transactional
    @Query(value = " delete from time_sheet_record where assignment_id = ?1 and week_ending_date = ?2 ", nativeQuery = true)
    void deleteByAssignmentIdAndWeekEndingDate(Long assignmentId, LocalDate weekEndingDate);

    List<TimeSheetRecord> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
