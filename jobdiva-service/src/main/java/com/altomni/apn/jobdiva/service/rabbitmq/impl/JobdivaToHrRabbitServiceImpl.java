package com.altomni.apn.jobdiva.service.rabbitmq.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.service.rabbitmq.JobdivaToHrRabbitService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.time.LocalDate;

import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;

@Slf4j
@RefreshScope
@Service("jobdivaToHrRabbitService")
public class JobdivaToHrRabbitServiceImpl implements JobdivaToHrRabbitService {


    @Resource(name = "jobdivaRabbitTemplate")
    private RabbitTemplate jobdivaRabbitTemplate;

    @Resource
    private JobdivaRabbitProperties jobdivaRabbitProperties;

    @Override
    public void sendDataToHr(JSONObject object) {
        log.info("[JobdivaToHrRabbitService: syncDataToMQ @{}] send apn jobdiva to hr rabbitMQ", SecurityUtils.getUserId());
        try {
            JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
            jsonConfig.setIgnoreNullValue(true);
            JSONObject resultJsonObject = new JSONObject(object, jsonConfig);
            toConverter(resultJsonObject);
            jobdivaRabbitTemplate.convertAndSend(jobdivaRabbitProperties.getApnToJobdivaExchange(), jobdivaRabbitProperties.getApnToJobdivaRoutingKey(), JSONUtil.toJsonStr(resultJsonObject), message -> {
                message.getMessageProperties().setPriority(1);
                return message;
            });
        } catch (Exception e) {
            log.error("[JobdivaToHrRabbitService: syncDataToMQ @{}] send {} to rabbitMQ error: {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(object), e.getMessage());
        }
    }

    private void toConverter(JSONObject jsonObject) {
        for (String key : jsonObject.keySet()) {
            Object value = jsonObject.get(key);  // 获取当前键的值
            // 判断该值是否是 LocalDate 类型
            if (value instanceof LocalDate localDateValue) {
                // 在这里做特殊处理，例如格式化日期
                jsonObject.put(key, localDateValue.toString());
            }
            if (value instanceof JSONArray jsonArray) {
                jsonArray.forEach(obj -> {
                    if (obj instanceof JSONObject jsonObj) {
                        toConverter(jsonObj);
                    }
                });
            }
            if (value instanceof JSONObject obj) {
                toConverter(obj);
            }
        }

    }

}
