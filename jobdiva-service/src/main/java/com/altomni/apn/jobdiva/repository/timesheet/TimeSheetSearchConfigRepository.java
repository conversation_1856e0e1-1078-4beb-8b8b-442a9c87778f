package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetSearchConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TimeSheetSearchConfigRepository extends JpaRepository<TimeSheetSearchConfig, Long> {

    @Query(value = " select * from timesheet_search_config tsc where tsc.uid = ?1 and tsc.search_type = ?2 and (lower(tsc.name) like %?3% or lower(tsc.config) like %?3%) order by tsc.created_time DESC ",nativeQuery = true)
    List<TimeSheetSearchConfig> searchFilter(Long userId, Integer toDbValue, String keyWord);

    @Query(value = " select * from timesheet_search_config tsc where tsc.search_type = ?2 and tsc.uid = ?1 order by tsc.is_default DESC, tsc.created_time DESC ",nativeQuery = true)
    List<TimeSheetSearchConfig> searchFilter(Long userId, Integer toDbValue);

}