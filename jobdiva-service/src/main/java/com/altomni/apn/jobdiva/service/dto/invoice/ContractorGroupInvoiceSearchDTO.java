package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.GroupInvoiceStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * ContractorInvoiceDTO
 *
 * <AUTHOR> zhang.lei
 * @date : 2023-6-15
 */
@ApiModel(description = "ContractorInvoiceSearchDTO")
@Data
public class ContractorGroupInvoiceSearchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<String> groupInvoiceNumber;

    private List<Long> employeeId;

    private Timestamp invoiceDateStart;

    private Timestamp invoiceDateEnd;

    private Timestamp createdDateStart;

    private Timestamp createdDateEnd;

    private List<Long> companyId;

    private List<Long> contactId;

    private List<GroupInvoiceStatus> invoiceStatusList;

    private List<InvoiceType> invoiceTypeList;

    private List<AssignmentDivision> assignmentDivisionList;

    private Long tenantId;
}
