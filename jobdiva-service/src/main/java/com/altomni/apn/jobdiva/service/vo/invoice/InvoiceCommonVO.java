package com.altomni.apn.jobdiva.service.vo.invoice;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigInteger;

@Data
@ApiModel(description = "InvoiceCommonVO")
public class InvoiceCommonVO implements Serializable, AttachConfidentialTalent {

    private BigInteger id;

    private String resultName;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    @Override
    public Long getTalentId() {
        return id.longValue();
    }

    @Override
    public void encrypt() {

    }
}
