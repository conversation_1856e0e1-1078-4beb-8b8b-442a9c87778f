package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.PaymentMethodType;
import com.altomni.apn.common.domain.enumeration.jobdiva.PaymentMethodTypeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;

/**
 * 分组发票;
 * <AUTHOR> zhang.lei
 * @date : 2023-6-9
 */
@ApiModel(value = "支付记录",description = "")
@Entity
@Table(name="t_record_payment_info")
@Data
public class TRecordPaymentInfo extends AbstractAuditingEntity implements Serializable,Cloneable{

    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id ;

    /** 分组编码 业务主键 */
    @ApiModelProperty(name = "分组编码 业务主键")
    @Column(name = "group_invoice_number")
    private String groupInvoiceNumber ;

    /** 分组发票id */
    @ApiModelProperty(name = "分组发票id")
    @Column(name = "group_invoice_id")
    private BigInteger groupInvoiceId ;

    /** 支付日期 */
    @ApiModelProperty(name = "支付日期")
    @Column(name = "payment_date")
    private Timestamp paymentDate ;

    /** 支付金额 */
    @ApiModelProperty(name = "支付金额")
    @Column(name = "payment_amount")
    private BigDecimal paymentAmount ;

    /** 支付方式 1-Wire Transfer 2-Credit Card 3-Debit Card 4-Check */
    @ApiModelProperty(name = "支付方式 1-Wire Transfer 2-Credit Card 3-Debit Card 4-Check")
    @Column(name = "payment_method")
    @Convert(converter = PaymentMethodTypeConverter.class)
    private PaymentMethodType paymentMethod ;

    @ApiModelProperty(name = "")
    @Column(name = "purchase_order_id")
    private BigInteger purchaseOrderId ;

    /** 备注 */
    @ApiModelProperty(name = "备注")
    @Column(name = "note")
    private String note ;

    /** 状态 0-无效 1-有效 */
    @ApiModelProperty(name = "状态 0-无效 1-有效")
    @Column(name = "status")
    private Integer status ;
}
