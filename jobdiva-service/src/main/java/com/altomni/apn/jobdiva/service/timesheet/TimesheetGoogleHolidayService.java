package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.jobdiva.service.dto.timesheet.TimesheetGoogleHolidayDTO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimesheetGoogleHolidayVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

public interface TimesheetGoogleHolidayService {

    Page<TimesheetGoogleHolidayVO> findHolidayList(Pageable pageable);

    Map<String, Object> insertRecord(List<TimesheetGoogleHolidayDTO> dtoList);

    void deleteById(Long id);

    void modify(TimesheetGoogleHolidayDTO dto);

    TimesheetGoogleHolidayVO exist(TimesheetGoogleHolidayDTO dto);
}
