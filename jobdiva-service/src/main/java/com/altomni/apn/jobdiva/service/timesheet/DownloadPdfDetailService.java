package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeDTO;
import com.altomni.apn.jobdiva.service.vo.timesheet.ExpenseListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimeSheetSummaryVO;
import com.itextpdf.layout.Document;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface DownloadPdfDetailService {

    void downloadTimeSheetDetail(BreakTimeDTO record, TimeSheetSummaryVO vo, HttpServletResponse response, Boolean is24timeFlag);

    void downloadTimeSheetDetail(BreakTimeDTO record, TimeSheetSummaryVO vo, Document document, Boolean is24timeFlag);

    void downloadTimesheetDetailByInvoice(BreakTimeDTO record, TimeSheetSummaryVO vo, com.itextpdf.text.Document document);

    void downloadExpenseDetail(Integer is, BreakTimeDTO breakTimeDTO, ExpenseListVO vo, HttpServletResponse response);

    void downloadExpenseDetail(BreakTimeDTO breakTimeDTO, ExpenseListVO vo, Document document) throws IOException;

}
