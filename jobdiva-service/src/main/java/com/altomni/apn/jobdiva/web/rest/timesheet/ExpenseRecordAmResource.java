package com.altomni.apn.jobdiva.web.rest.timesheet;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.timesheet.ExpenseRecordAmService;
import com.altomni.apn.jobdiva.service.vo.timesheet.EndingDateListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@Api(tags = {"TimeSheetExpenseRecordAM"})
@RestController
@RequestMapping("/api/v3/expense/am/record")
public class ExpenseRecordAmResource {


    @Resource
    private ExpenseRecordAmService recordService;

    @PostMapping("/list")
    @Timed
    public ResponseEntity<List<BreakTimeDTO>> list(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] REST break list detail param = {}", SecurityUtils.getUserId(), dto);
        List<BreakTimeDTO> records = recordService.findRecords(dto);
        return ResponseEntity.ok(records);
    }

    /**
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> saveBreakTime(@RequestBody BreakTimeDTO dto) {
        log.info("[expense: User @{}] save expense:", SecurityUtils.getUserId());
        Integer records = recordService.saveExpenseTime(dto);
        return ResponseEntity.ok(records);
    }

    /**
     *
     * @param dto
     * @return
     */
    @PostMapping("/detail")
    @Timed
    public ResponseEntity<BreakTimeDTO> findById(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] REST detail param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        BreakTimeDTO records = recordService.findRecordById(dto);
        return ResponseEntity.ok(records);
    }

    @PostMapping("/search")
    @Timed
    public ResponseEntity<SummaryDataVO> search(@RequestBody AdvanceSearchDTO dto) {
        log.info("[timesheet: User @{}] REST search param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        SummaryDataVO result = recordService.search(dto);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/downloadSummary")
    @Timed
    public void downloadSummary(HttpServletResponse response, @RequestBody SummaryQueryDTO dto) {
        log.info("[timesheet: User @{}] REST download summary", SecurityUtils.getUserId());
        recordService.downloadSummary(response,dto);
    }

    @PostMapping("/downloadReceipts")
    @Timed
    public void downloadReceipts(HttpServletResponse response, @RequestBody SummaryQueryDTO dto) {
        log.info("[timesheet: User @{}] REST download receipts", SecurityUtils.getUserId());
        recordService.downloadReceipts(response,dto);
    }

    @PostMapping("/approve")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> expenseApprove(@RequestBody ApproveDTO dto) {
        log.info("[timesheet: User @{}] REST expense approve param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        if (!SecurityUtils.isAdmin() && !SecurityUtils.isTimesheetAdmin() && !recordService.hasPermissionEdit(dto.getRecordIds(), RecordType.EXPENSE, SecurityUtils.getUserId()) && !SecurityUtils.isTimesheetAdmin()) {
            throw new CustomParameterizedException("Access denied !");
        }
        Integer records = recordService.expenseApprove(dto);
        return ResponseEntity.ok(records);

    }

    /**
     * get week ending dates as list
     *
     * @return
     */
    @GetMapping("/dateList")
    @Timed
    public ResponseEntity<List<EndingDateListVO>> getWeekEndingDates(@RequestParam(name = "talentId") Long talentId) {
        log.info("[timesheet: User @{}] REST to getWeekEndingDates", SecurityUtils.getUserId());
        List<EndingDateListVO> result = recordService.weekEndingDates(talentId);
        return ResponseEntity.ok(result);
    }

}
