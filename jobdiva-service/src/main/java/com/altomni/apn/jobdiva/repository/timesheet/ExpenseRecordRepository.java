package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.service.vo.timesheet.RecordHeadInfoVO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface ExpenseRecordRepository extends JpaRepository<ExpenseRecord, Long> {

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_expense_record where assignment_id = ?1 ", nativeQuery = true)
    void deleteByAssignmentId(Long assignmentId);

    @Transactional
    @Modifying
    @Query(value = " DELETE FROM timesheet_expense_record where id in ?1 ", nativeQuery = true)
    void deleteByIdList(List<Long> idList);

    @Query(value = " select * from timesheet_expense_record tsr where assignment_id = ?1 ", nativeQuery = true)
    List<ExpenseRecord> findAllByAssignmentId(Long assignmentId);


    @Query(value = " select * from timesheet_expense_record ter where talent_id = ?3 and work_date between ?1 and ?2 and assignment_id = ?4 ",nativeQuery = true)
    List<ExpenseRecord> findAllByDate(LocalDate startDate, LocalDate endDate, Long talentId,Long assignmentId);

    @Query(value = " select * from timesheet_expense_record ter where talent_id = ?3 and work_date between ?1 and ?2 and assignment_id = ?4 and expense_index = ?5 ", nativeQuery = true)
    List<ExpenseRecord> findAllByDateAndIndex(LocalDate startDate, LocalDate endDate, Long talentId, Long assignmentId, Integer expenseIndex);

    @Query(value = " select * from timesheet_expense_record ter where talent_id = ?2 and work_date <= ?1 and assignment_id = ?3 ",nativeQuery = true)
    List<ExpenseRecord> findAllByStartDate(LocalDate startDate, Long talentId,Long assignmentId);

    @Query(value = " select * from timesheet_expense_record ter where talent_id = ?2 and work_date >= ?1 and assignment_id = ?3 ",nativeQuery = true)
    List<ExpenseRecord> findAllByEndDate(LocalDate endDate, Long talentId,Long assignmentId);

    @Query(value = " select * from timesheet_expense_record ter where talent_id = ?3 and work_date between ?1 and ?2 and assignment_id = ?4 and expense_index < ?5 ", nativeQuery = true)
    List<ExpenseRecord> findAllByEndDateAndIndexLessThan(LocalDate startDate, LocalDate endDate, Long talentId, Long assignmentId, Integer recordIndex);

    @Transactional
    @Modifying
    @Query(value = " DELETE FROM timesheet_expense_record where work_date in (?1) and talent_id = ?2 and assignment_id = ?3 ", nativeQuery = true)
    void deleteAllByWorkDateInAndTalentIdAndAssignmentId(Set<LocalDate> dates, Long talentId,Long assignmentId);

    @Transactional
    @Modifying
    @Query(value = " DELETE FROM timesheet_expense_record where work_date in (?1) and talent_id = ?2 and assignment_id = ?3 and expense_index = ?4", nativeQuery = true)
    void deleteAllByWorkDateInAndTalentIdAndAssignmentIdAndExpenseIndex(Set<LocalDate> dates, Long talentId, Long assignmentId, Integer expenseIndex);

    @Query(value = " select * from timesheet_expense_record where id in ?1 ",nativeQuery = true)
    List<ExpenseRecord> findAllByIdIs(List<Long> ids);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_expense_record where talent_id=?1 and work_date between ?2 and ?3 and index = ?4 ",nativeQuery = true)
    Integer deleteByLine(Long talentId, String startDate, String endDate, Integer lineIndex);

    @Modifying
    @Transactional
    @Query(value = "update  timesheet_expense_record  set status=?3 where talent_id=?2 and work_date  in ?1 and assignment_id=?4 and expense_index = ?5 ", nativeQuery = true)
    void updateStatusByDatesAndIndex(Set<LocalDate> dates, Long talentId, Integer status, Long assignmentId, Integer expenseIndex);

    @Query(value = " select new  com.altomni.apn.jobdiva.service.vo.timesheet.RecordHeadInfoVO(T.fullName,T.id,J.title,J.id,CO.fullBusinessName,CCT.fullName,PCCT.fullName,concat(U.firstName,U.lastName) ) from " +
            " TalentAssigment TA "+
            " left join JobV3 J on J.id =  TA.jobId " +
            " left join TalentV3 T on T.id =  TA.talentId " +
            " left join Company CO on CO.id = TA.companyId " +
            " left join ExpenseRecord ER on ER.assignmentId = TA.id  and ER.workDate = ?2 and TA.talentId  = ER.talentId "+
            " left join ApproveRecord AR on AR.recordId = ER.id and (AR.role =2 or AR.role =3) "+
            " left join SalesLeadClientContact CC on CC.id = AR.operator and AR.role =2 "+
            " left join TalentV3 CCT on CCT.id = CC.talentId " +
            " left join TimeSheetManager TM on TM.talentId = TA.talentId and TM.role = 0  "+
            " left join SalesLeadClientContact PCC on PCC.id = TM.clientId  "+
            " left join TalentV3 PCCT on PCCT.id = PCC.talentId " +
            " left join User U on U.id = AR.operator and AR.role = 3 "+
            " where TA.id = ?1 group by TA.id "
            ,  nativeQuery = false)
    RecordHeadInfoVO findExpenseJobInfo(Long assignmentId, LocalDate weekend);

    @Query(value = " select new  com.altomni.apn.jobdiva.service.vo.timesheet.RecordHeadInfoVO(T.fullName,T.id,J.title,J.id,CO.fullBusinessName,CCT.fullName,PCCT.fullName,concat(U.firstName,U.lastName) ) from " +
            " TalentAssigment TA " +
            " left join JobV3 J on J.id =  TA.jobId " +
            " left join TalentV3 T on T.id =  TA.talentId " +
            " left join Company CO on CO.id = TA.companyId " +
            " left join ExpenseRecord ER on ER.assignmentId = TA.id and ER.workDate = ?2 and TA.talentId  = ER.talentId and ER.expenseIndex = ?3" +
            " left join ApproveRecord AR on AR.recordId = ER.id and AR.role = 2 and AR.type = 1 " +
            " left join SalesLeadClientContact CC on CC.id = AR.operator and AR.role =2 " +
            " left join TalentV3 CCT on CCT.id = CC.talentId " +
            " left join TimeSheetManager TM on TM.talentId = TA.talentId and TM.assignmentId = ER.assignmentId and TM.role = 0  " +
            " left join SalesLeadClientContact PCC on PCC.id = TM.clientId  " +
            " left join TalentV3 PCCT on PCCT.id = PCC.talentId " +
            " left join ApproveRecord AAR on AAR.recordId = ER.id and AAR.role =3 and AAR.type = 1 " +
            " left join User U on U.id = AAR.operator " +
            " where TA.id = ?1 group by TA.id "
            , nativeQuery = false)
    RecordHeadInfoVO findExpenseJobInfoByIndex(Long assignmentId, LocalDate weekend, Integer expenseIndex);


    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_expense_record where work_date between ?1 and ?2 and talent_id = ?3 and assignment_id = ?4 ",nativeQuery = true)
    void deleteByDate(LocalDate startDate, LocalDate endDate, Long talentId, Long id);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_expense_record where work_date <= ?1 and talent_id = ?2 and assignment_id = ?3 ",nativeQuery = true)
    void deleteByStartDate(LocalDate startDate, Long talentId, Long id);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_expense_record where work_date >= ?1 and talent_id = ?2 and assignment_id = ?3 ",nativeQuery = true)
    void deleteByEndDate(LocalDate endDate, Long talentId, Long id);

    @Query(value = " select * from timesheet_expense_record ter where talent_id = ?2 and work_date = ?1 and assignment_id = ?3 limit 1 ",nativeQuery = true)
    ExpenseRecord findByDate(LocalDate workDate, Long talentId, Long  assignmentId);

    @Query(value = " select * from timesheet_expense_record ter where talent_id = ?2 and work_date = ?1 and assignment_id = ?3 ",nativeQuery = true)
    List<ExpenseRecord> findAllByDateRange(LocalDate workDate, Long talentId, Long  assignmentId);

    @Modifying
    @Transactional
    @Query(value = " update timesheet_expense_record ter set cost = null, s3_key = null , status = ?3  where work_date between ?1 and ?2 and talent_id = ?4  and assignment_id = ?5 ",nativeQuery = true)
    void resetData(LocalDate starDate, LocalDate endDate,Integer status, Long talentId, Long assignment);

    @Modifying
    @Transactional
    @Query(value = " update timesheet_expense_record ter set cost = null, s3_key = null , status = ?3  where work_date between ?1 and ?2 and talent_id = ?4  and assignment_id = ?5 and expense_index = ?6",nativeQuery = true)
    void resetData(LocalDate starDate, LocalDate endDate,Integer status, Long talentId, Long assignment, Integer expenseIndex);

    @Modifying
    @Transactional
    @Query(value = " update timesheet_expense_record set week_start = ?4, week_end = ?5  where work_date in ?1 and talent_id = ?2 and assignment_id = ?3 ", nativeQuery = true)
    void updateWeekStartAndWeekEndByDates(Set<LocalDate> localDates, Long talentId, Long assignmentId, LocalDate weekStart, LocalDate weekEnd);

    @Query(value = " select * from timesheet_expense_record where work_date between ?1 and ?2 and talent_id = ?3 and assignment_id = ?4 and s3_key is not null and expense_index = ?5 ",nativeQuery = true)
    List<ExpenseRecord> findAllByStartDateAndEndDateAndTalentIdAndAssignmentIdAndExpenseIndex(LocalDate startDate, LocalDate endDate, Long talentId, Long assignmentId, Integer expenseIndex);

    @Modifying
    @Transactional
    @Query(value = " update timesheet_expense_record ter set submitted_date = null, status = ?3 where work_date between ?1 and ?2 and talent_id = ?4 and assignment_id = ?5 and expense_index = ?6 ",nativeQuery = true)
    void resetSubmittedDateAndStatus(LocalDate starDate, LocalDate endDate, Integer toDbValue, Long talentId, Long id, Integer expenseIndex);

    @Query(value = " select id from timesheet_expense_record where work_date between ?1 and ?2 and  talent_id = ?3 and assignment_id = ?4 and expense_index = ?5 ",nativeQuery = true)
    List<Long> findByDateAndTalentIdAndAssignmentId(LocalDate starDate, LocalDate endDate, Long talentId, Long assignmentId, Integer expenseIndex);

    @Query(value = " select count(ter.id) from timesheet_expense_record ter where ter.assignment_id = ?1 ", nativeQuery = true)
    Integer countByAssignmentId(Long assignmentId);

    @Query(value = " SELECT DISTINCT ter.id FROM timesheet_expense_record ter " +
        " INNER JOIN timesheet_talent_assignment tta ON ter.assignment_id = tta.id " +
        " INNER JOIN business_flow_administrator psl ON psl.company_id = tta.company_id " +
        " where ter.id in ?1 and psl.sales_lead_role in (0,3) and psl.user_id = ?2 ", nativeQuery = true)
    List<Long> findRecordIdsByCompanyAM(Set<Long> ids, Long userId);

    @Query(value = " SELECT ter.id FROM timesheet_expense_record ter " +
        " INNER JOIN timesheet_talent_assignment tta ON tta.id = ter.assignment_id " +
        " INNER JOIN business_flow_administrator AS psl ON psl.company_id = tta.company_id " +
        " where ter.id in ?1 and psl.sales_lead_role in (0,3) AND psl.user_id = ?2 limit 1 ", nativeQuery = true)
    Long findRecordIdByCompanyAM(Set<Long> ids, Long userId);

    @Query(value = " SELECT DISTINCT TTA.id FROM timesheet_talent_assignment TTA " +
        " LEFT JOIN START s ON TTA.start_id = s.id " +
        " LEFT JOIN talent_recruitment_process_kpi_user AC ON AC.talent_recruitment_process_id = s.talent_recruitment_process_id " +
        " LEFT JOIN timesheet_expense_record ter ON ter.assignment_id = TTA.id " +
        " WHERE AC.user_role in (1,2) AND AC.user_id = ?1 AND ter.id in ?2 ", nativeQuery = true)
    List<Long> findAssignmentIdByApplicationRecruiterOrSourcer(Long userId, List<Long> recordIds);

    @Modifying
    @Transactional
    @Query(value = " update timesheet_expense_record ter set last_modified_date = ?5 where work_date between ?1 and ?2 and talent_id = ?3 and assignment_id = ?4 ", nativeQuery = true)
    void updateLastModifyDatedByDateAndStatus(LocalDate starDate, LocalDate endDate, Long talentId, Long assignmentId, Instant nowDatetime);

    @Query(value = " select * from timesheet_expense_record ter where talent_id = ?3 and work_date between ?1 and ?2 and assignment_id = ?4 and status=4 and expense_index=?5",nativeQuery = true)
    List<ExpenseRecord> findAllByDateAndStatus(LocalDate startDate, LocalDate endDate, Long talentId,Long assignmentId,Integer index);

    @Query(value = " select * from timesheet_expense_record ter where talent_id = ?3 and work_date between ?1 and ?2 and assignment_id = ?4 and status in (?5)",nativeQuery = true)
    List<ExpenseRecord> findAllByDateAndStatus(LocalDate startDate, LocalDate endDate, Long talentId, Long assignmentId, List<Integer> statusList);

    @Modifying
    @Transactional
    @Query(value = "update timesheet_expense_record set last_modified_date = ?2 where id in (?1)", nativeQuery = true)
    void updateLastModifiedDateById(List<Long> idList, Instant updateDate);

    List<ExpenseRecord> findAllByAssignmentIdAndWeekEndAndExpenseIndex(Long assignmentId, LocalDate weekEnd, Integer expenseIndex);

    @Transactional
    @Modifying
    @Query(value = " DELETE FROM timesheet_expense_record where week_end = ?1 and talent_id = ?2 and assignment_id = ?3 and expense_index = ?4", nativeQuery = true)
    void deleteAllByWeekEndAndTalentIdAndAssignmentIdAndExpenseIndex(LocalDate weekEnd, Long talentId, Long assignmentId, Integer expenseIndex);

    List<ExpenseRecord> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
