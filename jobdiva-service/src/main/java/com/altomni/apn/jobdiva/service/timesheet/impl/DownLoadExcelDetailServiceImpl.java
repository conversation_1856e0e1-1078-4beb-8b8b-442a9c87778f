package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetHolidayRecord;
import com.altomni.apn.jobdiva.repository.assignment.PayRateRepository;
import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.TimeSheetRecordDTO;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.timesheet.DownLoadExcelDetailService;
import com.altomni.apn.jobdiva.service.vo.timesheet.ExpenseListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimeSheetSummaryVO;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.validator.GenericValidator;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.altomni.apn.common.config.constants.Constants.*;
import static com.altomni.apn.common.domain.enumeration.jobdiva.CalculateMethodType.CALIFORNIA;
import static com.altomni.apn.common.utils.DateUtil.*;

@Slf4j
@Service("downLoadExcelDetailService")
public class DownLoadExcelDetailServiceImpl implements DownLoadExcelDetailService {

    private static final String BREAK_TIME_TYPE = "breakTimeType";

    private static final String BREAK_TIME_TYPE_UNIT = "(hour:min)";

    private static final String TIME_IN = "Time In";

    private static final String MEAL_BREAK_OUT = "Meal Break Out";

    private static final String MEAL_BREAK_IN = "Meal Break In";

    private static final String TIME_OUT = "Time Out";

    private static final Map<String, String> TIME_SHEET_BREAK_TIME_TYPE_MAP = ImmutableMap.of(TimeSheetBreakTimeType.TIME_IN.name(), TIME_IN,
            TimeSheetBreakTimeType.TIME_OUT.name(), TIME_OUT, TimeSheetBreakTimeType.MEAL_BREAK_IN.name(), MEAL_BREAK_IN,
            TimeSheetBreakTimeType.MEAL_BREAK_OUT.name(), MEAL_BREAK_OUT);

    @Resource
    private HttpService httpService;

    @Resource
    private StoreService storeService;

    @Resource
    private PayRateRepository payRateRepository;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Override
    public void downloadTimeSheetDetail(BreakTimeDTO record, TimeSheetSummaryVO vo, HttpServletResponse response, Boolean is24timeFlag) {
        try {
            HSSFWorkbook workBook = getSheet("templates/timeSheetDetailTemplate.xls");
            Sheet sheet = workBook.getSheetAt(0);//获取Excel的工作表sheet，下标从0开始。
            Integer trLength = sheet.getLastRowNum() + 1;//获取Excel的行数
            //write summary
            //需要的数据
            Set<LocalDate> needLocalDateSet = TimeSheetUtil.getWeekByWeekEndingDate(vo.getWeekStart(), vo.getWeekEnd());
            WriteRecord writeRecord = new WriteRecord(record, vo, sheet, trLength, CommentsType.TIME_SHEET, needLocalDateSet, is24timeFlag).invoke();
            trLength = writeRecord.getTrLength();
            List<String> heads = writeRecord.getHeads();
            boolean displayDoubleTime = true;
            boolean displayOverTimeType = vo.getOvertimeType() == OverTimeType.AUTO;
            //write total hours
            List<TimeSheetRecordDTO> hours = record.getTimeSheet().stream().filter(timesheet -> needLocalDateSet.contains(timesheet.getWorkDate())).collect(Collectors.toList());
            Set<LocalDate> localDateSet = hours.stream().map(TimeSheetRecordDTO::getWorkDate).collect(Collectors.toSet());
            //整周的localDate
            Set<LocalDate> allLocalDate = TimeSheetUtil.getWeekByWeekEndingDate(TimeSheetUtil.findDateInWeek(vo.getWeekEnd(), vo.getWeekEndingDate().getDayOfWeek().getValue(), 1).toString());
            TimeSheetRecordDTO timeSheetRecordDTO = hours.get(hours.size() - 1);
            for (LocalDate localDate : allLocalDate) {
                //已存在的数据中是否存在,不存在则补全
                if (!localDateSet.contains(localDate)) {
                    TimeSheetRecordDTO dto = TimeSheetUtil.copyTimeSheetRecordDTO(timeSheetRecordDTO);
                    dto.setWorkDate(localDate);
                    dto.setWeekDay(localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                    hours.add(dto);
                } else {
                    if (StrUtil.isBlank(timeSheetRecordDTO.getWeekDay())) {
                        timeSheetRecordDTO.setWeekDay(timeSheetRecordDTO.getWorkDate().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                        hours.add(timeSheetRecordDTO);
                    }
                }
            }
            hours = hours.stream().sorted(Comparator.comparing(TimeSheetRecordDTO::getWorkDate)).toList();
            if (CollUtil.isNotEmpty(hours)) {
                if (hours.get(0).getTimeSheetType() == TimeSheetType.WEEK) {
                    trLength += 2;
                    trLength = getWorkHours(sheet, trLength, heads, hours, needLocalDateSet);
                } else if (hours.get(0).getTimeSheetType() == TimeSheetType.WEEK_AM_PM) {
                    trLength += 2;
                    trLength = getTimeSheetHours(sheet, trLength, heads, hours, false, displayDoubleTime, displayOverTimeType, needLocalDateSet);
                }
                createRowWithHolidayOt(sheet, trLength, record, vo);
            }
            response.setContentType("application/octet-stream");
            String fileName = "timesheet_" + vo.getTalentName().replace(" ", "") + vo.getEndingDate().toString() + ".xls";
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            workBook.write(response.getOutputStream());
            workBook.close();
        } catch (Exception e) {
            log.error("error", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DOWNLOAD_FAILED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
    }

    @Override
    public void downloadExpenseDetail(Integer isRecipt, BreakTimeDTO breakTimeDTO, ExpenseListVO vo, HttpServletResponse response) {
        try {
            if (isRecipt == null) {
                isRecipt = 0;
            }
            HSSFWorkbook workBook = getSheet("templates/expenseDetailTemplate.xls");
            Sheet sheet = workBook.getSheetAt(0);//获取Excel的工作表sheet，下标从0开始。
            int trLength = sheet.getLastRowNum() + 1;//获取Excel的行数
            //write summary
            List<Map<String, Object>> maps = ObjectUtil.cloneByStream(breakTimeDTO.getRecords());
            Set<LocalDate> needLocalDateSet = TimeSheetUtil.getWeekByWeekEndingDate(vo.getWeekStart(), vo.getWeekEnd());
            new WriteRecord(breakTimeDTO, vo, sheet, trLength, CommentsType.EXPENSE, needLocalDateSet, false).invoke();
            List<ExpenseRecord> bList = new ArrayList<>();
            maps.forEach(map -> mapToEntity(map, bList));
            Map<String, List<String>> mapList = new HashMap<>();
            Map<Integer, List<ExpenseRecord>> mapListExpenseRecord = bList.stream().collect(Collectors.groupingBy(ExpenseRecord::getLineIndex));
            mapListExpenseRecord.forEach((k, v) -> {
                List<String> linkList = new LinkedList<>();
                v.stream().filter(a -> StrUtil.isNotBlank(a.getS3Key()))
                        .sorted(Comparator.comparing(ExpenseRecord::getWorkDate))
                        .filter(expenseRecord -> needLocalDateSet.contains(expenseRecord.getWorkDate()))
                        .forEach(expenseRecord -> linkList.add(expenseRecord.getS3Key()));
                if (CollUtil.isNotEmpty(linkList)) {
                    String expenseTypeStr = v.get(0).getExpenseType().toString();
                    if (mapList.containsKey(expenseTypeStr)) {
                        mapList.get(expenseTypeStr).addAll(linkList);
                    } else {
                        mapList.put(expenseTypeStr, linkList);
                    }
                }
            });
            String filename = "";
            if (isRecipt == 0) {
                response.setContentType("application/octet-stream");
                filename = "Expense_" + vo.getTalentName().replace(" ", "") + vo.getWeekEnd().toString() + ".xls";
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
                workBook.write(response.getOutputStream());
                workBook.close();
                return;
            }
            response.setContentType("application/x-msdownload");
            filename = "Expense_Receipts_" + vo.getTalentName().replace(" ", "") + vo.getWeekEnd().toString() + ".zip";
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
            ZipOutputStream zip = new ZipOutputStream(response.getOutputStream());
            String weekendStr = localDateToString(vo.getWeekEnd());
            TreeMap<String, List<String>> paramTreeMap = new TreeMap<>(mapList);
            for (Map.Entry<String, List<String>> stringListEntry : paramTreeMap.entrySet()) {
                String key = stringListEntry.getKey();
                List<String> links = stringListEntry.getValue();
                for (int i = 0; i < links.size(); i++) {
                    String link = links.get(i);
                    String fileKey = link.substring(link.lastIndexOf("/") + 1);
                    StringBuilder fileName = new StringBuilder();
                    fileName.append(key).append("-").append(weekendStr);
                    if (links.size() > 1) {
                        fileName.append("-");
                        fileName.append(i + 1);
                    }
                    CloudFileObjectMetadata cloudFileObject = storeService.downloadDocument(fileKey, UploadTypeEnum.RECEIPT.getKey()).getBody();
                    if (cloudFileObject != null) {
                        String fileType = cloudFileObject.getFileName().substring(cloudFileObject.getFileName().lastIndexOf("."));
                        fileName.append(fileType);
                        zip.putNextEntry(new ZipEntry(fileName.toString()));
                        zip.write(cloudFileObject.getContent());
                        zip.flush();
                    }
                }
            }
            if (isRecipt == 2) {
                zip.putNextEntry(new ZipEntry("expense.xls"));
                workBook.write(zip);
                zip.flush();
            }
            workBook.close();
            zip.flush();
            zip.finish();
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    private void mapToEntity(Map<String, Object> map, List<ExpenseRecord> bList) {
        ExpenseType type= ExpenseType.valueOf(map.get(EXPENSE_TYPE).toString());
        Integer index = (Integer) map.get(ROW_INDEX);
        map.remove(ROW_INDEX);
        map.remove(EXPENSE_TYPE);
        List<ExpenseRecord> records = new LinkedList<>();
        for (Map.Entry<String,Object> entry:map.entrySet()) {
            ExpenseRecord record = new ExpenseRecord();
            String[] dateWeek = entry.getKey().split(DATE_WEEK_SEPERTATEOR);
            record.setWeekDay(dateWeek[0]);
            if (entry.getValue() != null) {
                String value =  (String)entry.getValue();
                String[] costLink = value.split(DATE_WEEK_SEPERTATEOR);
                Float cost = Float.valueOf(costLink[0]);
                String slink = costLink[1];
                record.setCost(cost);
                record.setS3Key(slink);
            }
            LocalDate date = LocalDate.parse(dateWeek[1]);
            record.setWorkDate(date);
            record.setLineIndex(index);
            record.setExpenseType(type);
            records.add(record);
        }
        bList.addAll(records);
    }

    public HSSFWorkbook getSheet(String path) throws IOException {
        InputStream stream = CommonUtils.getResource(path);
        POIFSFileSystem fs = new POIFSFileSystem(stream);
        HSSFWorkbook workBook = new HSSFWorkbook(fs);
        return workBook;
    }

    private Integer getWorkHours(Sheet sheet, Integer trLength, List<String> heads, List<TimeSheetRecordDTO> hours, Set<LocalDate> localDates) {
        Row row = sheet.createRow(trLength++);
        Cell tCell = row.createCell(0);
        tCell.setCellValue("Worked");
        for (int h = 1; h < heads.size(); h++) {
            Cell cell = row.createCell(h);
            for (TimeSheetRecordDTO dto : hours) {
                if (!localDates.contains(dto.getWorkDate())) {
                    continue;
                }
                if (dto.getWorkDate().toString().equals(heads.get(h).split(DATE_WEEK_SEPERTATEOR)[1])) {
                    cell.setCellValue(dto.getTotalHours() > 0 ? "1" : "0");
                }
            }
        }
        return trLength;
    }

    private Integer getTimeSheetHours(Sheet sheet, Integer trLength, List<String> heads, List<TimeSheetRecordDTO> hours,
                                      boolean displayHours, boolean displayDoubleTime, boolean displayOverTimeType, Set<LocalDate> localDates) {
        String[] item;
        int length = 4;
        String overTimeTypeString = "Auto Calculated";
        if (!displayOverTimeType) {
            overTimeTypeString = "Manually";
        }
        if (displayHours) {
            item = new String[]{"Hours", "Regular Hours(Decimal)", "Overtime(" + overTimeTypeString + ")", "Total Hours"};
            if (displayDoubleTime) {
                item = new String[]{"Hours", "Regular Hours(Decimal)", "Overtime(" + overTimeTypeString + ")", "Doubletime(" + overTimeTypeString + ")", "Total Hours"};
                length = 5;
            }
        } else {
            item = new String[]{"Regular Hours(Decimal)", "Overtime(" + overTimeTypeString + ")", "Total Hours"};
            length = 3;
            if (displayDoubleTime) {
                item = new String[]{"Regular Hours(Decimal)", "Overtime(" + overTimeTypeString + ")", "Doubletime(" + overTimeTypeString + ")", "Total Hours"};
                length = 4;
            }
        }
        for (int i = 0; i < length; i++) {
            Row row = sheet.createRow(trLength++);
            Cell tCell = row.createCell(0);
            tCell.setCellValue(item[i]);
            Float total = 0.00f;
            for (int h = 1; h < heads.size(); h++) {
                Cell cell = row.createCell(h);
                for (TimeSheetRecordDTO dto : hours) {
                    if (!localDates.contains(dto.getWorkDate())) {
                        continue;
                    }
                    if (dto.getWorkDate().toString().equals(heads.get(h).split(DATE_WEEK_SEPERTATEOR)[1])) {
                        if (item[i].equals("Hours")) {
                            cell.setCellValue(String.valueOf(dto.getWorkHours()));
                        } else if (item[i].equals("Regular Hours(Decimal)")) {
                            cell.setCellValue(String.valueOf(dto.getRegularHours()));
                            total += dto.getRegularHours();
                        } else if (item[i].equals("Overtime(" + overTimeTypeString + ")")) {
                            cell.setCellValue(String.valueOf(dto.getOverTime()));
                            total += dto.getOverTime();
                        } else if (item[i].equals("Doubletime(" + overTimeTypeString + ")")) {
                            cell.setCellValue(String.valueOf(dto.getDoubleTime()));
                            total += dto.getDoubleTime();
                        } else if (item[i].equals("Total Hours")) {
                            cell.setCellValue(String.valueOf(dto.getWorkHours()));
                            total += dto.getWorkHours();
                        }
                    }
                }
            }
            Cell totalCell = row.createCell(heads.size());
            totalCell.setCellValue(String.valueOf(roundFloat(total)));
        }
        return trLength;
    }

    private void createRowWithHolidayOt(Sheet sheet, int trLength, BreakTimeDTO record, TimeSheetSummaryVO vo) {
        if (CollUtil.isNotEmpty(record.getHolidayRecordList())) {
            sheet.createRow(trLength++);
            sheet.createRow(trLength++);
            Row row = sheet.createRow(trLength++);
            Cell t1Cell = row.createCell(0);
            t1Cell.setCellValue("Holiday Item Description");
            Cell t2Cell = row.createCell(3);
            t2Cell.setCellValue("Holiday Hours");
            Cell t3Cell = row.createCell(5);
            t3Cell.setCellValue("Holiday OT Rate");
            Cell t4Cell = row.createCell(7);
            t4Cell.setCellValue("Amount");
            List<TimeSheetHolidayRecord> holidayRecordList = record.getHolidayRecordList();
            for (TimeSheetHolidayRecord timeSheetHolidayRecord : holidayRecordList) {
                Row valueRow = sheet.createRow(trLength++);
                Cell v1Cell = valueRow.createCell(0);
                v1Cell.setCellValue("Holiday OT adjustment for " + vo.getTalentName());
                Cell v2Cell = valueRow.createCell(3);
                v2Cell.setCellValue(String.format("%.2f", timeSheetHolidayRecord.getWorkHours()));
                Cell v3Cell = valueRow.createCell(5);
                String label1 = enumCurrencyService.findEnumCurrencyById(timeSheetHolidayRecord.getCurrency()).getLabel1();
                v3Cell.setCellValue(label1 + timeSheetHolidayRecord.getRate());
                Cell v4Cell = valueRow.createCell(7);
                v4Cell.setCellValue(label1 + timeSheetHolidayRecord.getRate().multiply(new BigDecimal(String.format("%.2f", timeSheetHolidayRecord.getWorkHours()))).setScale(2, RoundingMode.HALF_UP).toPlainString());
            }
        }
    }

    public Float roundFloat(Float f) {
        if (f == null) {
            return 0.0f;
        }
        BigDecimal b = new BigDecimal(f);
        return b.setScale(2, BigDecimal.ROUND_HALF_UP).floatValue();
    }

    private class WriteRecord {
        private BreakTimeDTO record;
        private Object vo;
        private Sheet sheet;
        private int trLength;
        private List<String> heads;
        private CommentsType type;
        private Boolean is24timeFlag;

        private Set<LocalDate> needLocalDateList;
        public WriteRecord(BreakTimeDTO record, Object vo, Sheet sheet, int trLength, CommentsType type, Set<LocalDate> needLocalDateSet, Boolean is24timeFlag) {
            this.record = record;
            this.vo = vo;
            this.sheet = sheet;
            this.trLength = trLength;
            this.type = type;
            this.needLocalDateList = needLocalDateSet;
            this.is24timeFlag = is24timeFlag;
        }
        public int getTrLength() {
            return trLength;
        }
        public List<String> getHeads() {
            return heads;
        }
        public WriteRecord invoke() throws IllegalAccessException {
            Map<String, String> summaryMap = summaryToMap(vo);
            writeSummaryDate(summaryMap, sheet, trLength);
            //write records
            List<Map<String, Object>> lineRecord = record.getRecords();
            trLength++;
            trLength++;
            heads = null;
            if (CollUtil.isNotEmpty(lineRecord)) {
                heads = setHeads(lineRecord.get(0));
                Row excelHard = sheet.createRow(trLength++);
                // set header
                for (int k = 0; k < heads.size(); k++) {
                    Cell cell = excelHard.createCell(k);
                    String cellValue = heads.get(k);
                    if (StringUtils.equalsIgnoreCase(cellValue, EXPENSE_TYPE)) {
                        cellValue = EXPENSE_CATEGORY;
                    } else {
                        String[] dateStr = cellValue.split(DATE_WEEK_SEPERTATEOR);
                        StringBuilder sb = new StringBuilder();
                        if (ArrayUtils.isNotEmpty(dateStr) && dateStr.length == 2) {
                            sb.append(dateStr[0]);
                            sb.append(" ");
                            sb.append(getMonthAndDay(dateStr[1]));
                        } else {
                            sb.append("Hour Types");
                        }
                        cellValue = sb.toString();
                    }
                    cell.setCellValue(cellValue);
                }
                Cell tCell = excelHard.createCell(heads.size());
                if (this.type == CommentsType.TIME_SHEET) {
                    tCell.setCellValue("Total Hours");
                } else {
                    tCell.setCellValue("Total");
                }
                Float totalByWeek = 0.0f;
                Map<String, Float> grandTotal = new LinkedHashMap<>();
                for (int i = 0; i < lineRecord.size(); i++) {
                    Row row = sheet.createRow(trLength++);
                    Map<String, Object> map = lineRecord.get(i);
                    map.remove("index");
                    totalByWeek = 0.0f;
                    for (int k = 0; k < heads.size(); k++) {
                        Cell cell = row.createCell(k);
                        String key = heads.get(k);
                        if (CollUtil.isNotEmpty(needLocalDateList) && key.contains(DATE_WEEK_SEPERTATEOR)) {
                            LocalDate localDate = LocalDate.parse(key.split(DATE_WEEK_SEPERTATEOR)[1]);
                            if (!needLocalDateList.contains(localDate)) {
                                grandTotal.putIfAbsent(key, 0.0f);
                                continue;
                            }
                        }
                        Object o = map.get(key);
                        grandTotal.putIfAbsent(key, 0.0f);
                        // contains data on that day
                        if (o != null) {
                            String value = o instanceof ExpenseType ? ((ExpenseType) o).getDescription() : o.toString();
                            if (StringUtils.isBlank(value)) {
                                value = "0";
                            } else if (StringUtils.equalsIgnoreCase(key, BREAK_TIME_TYPE) && TIME_SHEET_BREAK_TIME_TYPE_MAP.containsKey(value)) {
                                value = TIME_SHEET_BREAK_TIME_TYPE_MAP.get(value);
                                value += BREAK_TIME_TYPE_UNIT;
                            } else if (value.indexOf(DATE_WEEK_SEPERTATEOR) > 0) {
                                value = value.split(DATE_WEEK_SEPERTATEOR)[0];
                            }
                            cell.setCellValue(TimeSheetUtil.getBreakTimeByType(is24timeFlag, value));
                            if (this.type == CommentsType.EXPENSE && k > 0) {
                                totalByWeek += Float.parseFloat(value);
                                Float grand = grandTotal.get(key);
                                grandTotal.put(key, Float.parseFloat(value) + grand);
                            }
                        }
                    }
                    if (this.type == CommentsType.EXPENSE) {
                        Cell cell = row.createCell(heads.size());
                        cell.setCellValue(String.valueOf(roundFloat(totalByWeek)));
                        Float total = grandTotal.get("total");
                        if (total == null) {
                            total = 0.0f;
                        }
                        grandTotal.put("total", total + totalByWeek);
                    }
                }
                if (this.type == CommentsType.EXPENSE) {
                    Row row = sheet.createRow(trLength++);
                    Cell cell = row.createCell(0);
                    String moneyStr = "Grand Total: ($)";
                    if (vo instanceof ExpenseListVO) {
                        Long assignmentId = ((ExpenseListVO) vo).getAssignmentId();
                        AssignmentPayRateInfo payRateInfo = payRateRepository.findPayUnit(assignmentId);
                        if (payRateInfo != null) {
                            EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(payRateInfo.getCurrency());
                            moneyStr = "Grand Total: (" + enumCurrency.getLabel1() + ")";
                        }
                    }
                    cell.setCellValue(moneyStr);
                    int i = 1;
                    grandTotal.remove(EXPENSE_TYPE);
                    for (Map.Entry<String, Float> entry : grandTotal.entrySet()) {
                        String key = entry.getKey();
                        if (CollUtil.isNotEmpty(needLocalDateList) && key.contains(DATE_WEEK_SEPERTATEOR)) {
                            LocalDate localDate = LocalDate.parse(key.split(DATE_WEEK_SEPERTATEOR)[1]);
                            if (!needLocalDateList.contains(localDate)) {
                                Cell gCell = row.createCell(i++);
                                gCell.setCellValue("");
                                continue;
                            }
                        }
                        Cell gCell = row.createCell(i++);
                        gCell.setCellValue(String.valueOf(roundFloat(entry.getValue())));
                    }
                }
            } else {
                List<TimeSheetRecordDTO> list = record.getTimeSheet();
                if (CollectionUtils.isNotEmpty(list)) {
                    heads = setHeads(list);
                    Row excelHard = sheet.createRow(trLength++);
                    TimeSheetSummaryVO timeSheetSummaryVO = vo instanceof TimeSheetSummaryVO ? (TimeSheetSummaryVO) vo : null;
                    int k = 0;
                    if (this.type == CommentsType.TIME_SHEET && ObjectUtils.isNotEmpty(timeSheetSummaryVO) && TimeSheetType.WEEK_AM_PM == timeSheetSummaryVO.getSheetType()) {
                        Cell tCell = excelHard.createCell(0);
                        tCell.setCellValue("Hour Types");
                        k = 1;
                    }
                    // set header
                    for (; k < heads.size(); k++) {
                        Cell cell = excelHard.createCell(k);
                        String[] dateStr = heads.get(k).split(DATE_WEEK_SEPERTATEOR);
                        StringBuilder sb = new StringBuilder();

                        if (ArrayUtils.isNotEmpty(dateStr) && dateStr.length == 2) {
                            sb.append(dateStr[0]);
                            sb.append(" ");
                            sb.append(getMonthAndDay(dateStr[1]));
                        } else if (ArrayUtils.isNotEmpty(dateStr) && dateStr.length == 1) {
                            sb.append(dateStr[0]);
                        }
                        cell.setCellValue(sb.toString());
                    }
                    if (this.type == CommentsType.TIME_SHEET && ObjectUtils.isNotEmpty(timeSheetSummaryVO) && TimeSheetType.WEEK_AM_PM == timeSheetSummaryVO.getSheetType()) {
                        Cell tCell = excelHard.createCell(heads.size());
                        tCell.setCellValue("Total Hours");
                        // fill out TimeSheetBreakTime
                        fillTimeSheetBreakTime(TIME_IN);
                        fillTimeSheetBreakTime(MEAL_BREAK_OUT);
                        fillTimeSheetBreakTime(MEAL_BREAK_IN);
                        fillTimeSheetBreakTime(TIME_OUT);
                    }

                    if (list.get(0).getTimeSheetType() == TimeSheetType.WEEK_HOUR) {
                        boolean displayDoubleTime = false;
                        boolean displayOverTimeType = false;
                        if (ObjectUtils.isNotEmpty(timeSheetSummaryVO)) {
                            //自动计算存在dt, 所有的手动计算都要显示dt
                            displayDoubleTime = true;
                            displayOverTimeType = timeSheetSummaryVO.getOvertimeType() == OverTimeType.AUTO;
                        }
                        Cell cell = excelHard.createCell(heads.size());
                        cell.setCellValue("Total Hours");
                        getTimeSheetHours(sheet, trLength, heads, list, true, displayDoubleTime, displayOverTimeType);
                    }
                }


            }
            return this;
        }

        private void getTimeSheetHours(Sheet sheet, int trLength, List<String> heads, List<TimeSheetRecordDTO> hours, boolean displayHours, boolean displayDoubleTime, boolean displayOverTimeType) {
            String[] item;
            int length = 4;
            String overTimeTypeString = "Auto Calculated";
            if (!displayOverTimeType) {
                overTimeTypeString = "Manually";
            }
            if (displayHours) {
                item = new String[]{"Hours", "Regular Hours(Decimal)", "Overtime(" + overTimeTypeString + ")", "Total Hours"};
                if (displayDoubleTime) {
                    item = new String[]{"Hours", "Regular Hours(Decimal)", "Overtime(" + overTimeTypeString + ")", "Doubletime(" + overTimeTypeString + ")", "Total Hours"};
                    length = 5;
                }
            } else {
                item = new String[]{"Regular Hours(Decimal)", "Overtime(" + overTimeTypeString + ")", "Total Hours"};
                length = 3;
                if (displayDoubleTime) {
                    item = new String[]{"Regular Hours(Decimal)", "Overtime(" + overTimeTypeString + ")", "Doubletime(" + overTimeTypeString + ")", "Total Hours"};
                    length = 4;
                }
            }
            for (int i = 0; i < length; i++) {
                Row row = sheet.createRow(this.trLength++);
                Cell tCell = row.createCell(0);
                tCell.setCellValue(item[i]);
                Float total = 0.00f;
                for (int h = 1; h < heads.size(); h++) {
                    Cell cell = row.createCell(h);
                    for (TimeSheetRecordDTO dto : hours) {
                        if (!needLocalDateList.contains(dto.getWorkDate())) {
                            continue;
                        }
                        if (dto.getWorkDate().toString().equals(heads.get(h).split(DATE_WEEK_SEPERTATEOR)[1])) {
                            if (item[i].equals("Hours")) {
                                cell.setCellValue(String.valueOf(dto.getWorkHours()));
                            } else if (item[i].equals("Regular Hours(Decimal)")) {
                                cell.setCellValue(String.valueOf(dto.getRegularHours()));
                                total += dto.getRegularHours();
                            } else if (item[i].equals("Overtime(" + overTimeTypeString + ")")) {
                                cell.setCellValue(String.valueOf(dto.getOverTime()));
                                total += dto.getOverTime();
                            } else if (item[i].equals("Doubletime(" + overTimeTypeString + ")")) {
                                cell.setCellValue(String.valueOf(dto.getDoubleTime()));
                                total += dto.getDoubleTime();
                            } else if (item[i].equals("Total Hours")) {
                                cell.setCellValue(String.valueOf(dto.getWorkHours()));
                                total += dto.getWorkHours();
                            }
                        }
                    }
                }
                Cell totalCell = row.createCell(heads.size());
                totalCell.setCellValue(String.valueOf(roundFloat(total)));
            }
        }

        private void fillTimeSheetBreakTime(String label) {
            Row row = sheet.createRow(trLength++);
            Cell labelCell = row.createCell(0);
            label += BREAK_TIME_TYPE_UNIT;
            labelCell.setCellValue(label);
            for (int k = 1; k < heads.size(); k++) {
                Cell cell = row.createCell(k);
                cell.setCellValue("0");
            }
        }

        private void writeSummaryDate(Map<String, String> summaryMap, Sheet sheet, int trLength) {
            for (int i = 0; i < trLength; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {continue;}
                int minColIx = row.getFirstCellNum();
                int maxColIx = row.getLastCellNum();
                for (int colIx = minColIx; colIx < maxColIx; colIx++) {
                    Cell cell = row.getCell(colIx);
                    if (cell == null) {continue;}
                    String cellText = cell.getStringCellValue();
                    if (cellText == null || cellText.trim().length() == 0) {
                        continue;
                    }
                    String replaceValue = summaryMap.get(cellText.trim());
                    if (StringUtils.isNotBlank(replaceValue)) {
                        if (GenericValidator.isDate(replaceValue, YYYY_MM_DD, true)) {
                            replaceValue = DateUtil.getMonthDayYear(replaceValue);
                        } else if (GenericValidator.isDate(replaceValue, YYYY_MM_DD_T_HH_MM_SS_Z, false)) {
                            replaceValue = DateUtil.getDateTimeInCSTWithTimeZone(replaceValue);
                        }
                        cell.setCellValue(replaceValue);
                    } else {
                        if (StringUtils.equalsIgnoreCase(cellText, "#{jobId}({jobTitle})")) {
                            String jobId = summaryMap.get("{jobId}");
                            String jobTitle = summaryMap.get("{jobTitle}");
                            replaceValue = "#" + jobId + "(" + jobTitle + ")";
                            cell.setCellValue(replaceValue);
                        } else if (summaryMap.containsKey(cellText.trim())) {
                            cell.setCellValue("");
                        }
                    }
                }
            }
        }

        private List<String> setHeads(Map<String, Object> map) {
            List<String> keys = new ArrayList<>();
            map.remove("index");
            for (Map.Entry<String, Object> entry: map.entrySet()) {
                keys.add(entry.getKey());
            }
            Collections.sort(keys, new Comparator<String>() {
                public int compare(String a1, String a2) {
                    if (a1.contains(BREAK_TYPE)) return -1;
                    if (a2.contains(BREAK_TYPE)) return 1;
                    if (a1.contains(EXPENSE_TYPE)) return -1;
                    if (a2.contains(EXPENSE_TYPE)) return 1;
                    LocalDate day1 = LocalDate.parse(a1.split(DATE_WEEK_SEPERTATEOR)[1]);
                    LocalDate day2 = LocalDate.parse(a2.split(DATE_WEEK_SEPERTATEOR)[1]);
                    return day1.isAfter(day2) ? 1 : -1;
                }
            });
            return keys;
        }

        private List<String> setHeads(List<TimeSheetRecordDTO> records) {
            String hourType = "Hour Types";
            List<String> keys = new ArrayList<>();
            keys.add(hourType);
            for (TimeSheetRecordDTO record : records) {
                keys.add(record.getWeekDay() + "@" + record.getWorkDate().toString());
            }
            Collections.sort(keys, new Comparator<String>() {
                public int compare(String a1, String a2) {
                    if (a1.contains(hourType)) return -1;
                    if (a2.contains(hourType)) return 1;
                    LocalDate day1 = LocalDate.parse(a1.split(DATE_WEEK_SEPERTATEOR)[1]);
                    LocalDate day2 = LocalDate.parse(a2.split(DATE_WEEK_SEPERTATEOR)[1]);
                    return day1.isAfter(day2) ? 1 : -1;
                }
            });
            return keys;
        }

        private Map<String, String> summaryToMap(Object vo) throws IllegalAccessException {
            Map<String, String> map = new HashMap<>();
            Class clazz = vo.getClass();
            Field[] fs = clazz.getDeclaredFields();
            for (Field field : fs) {
                field.setAccessible(true);
                Object value = field.get(vo);
                if (value == null) {
                    map.put("{" + field.getName() + "}", "");
                    continue;
                }
                if (value instanceof TimeSheetStatus) {
                    TimeSheetStatus d = (TimeSheetStatus) value;
                    map.put("{" + field.getName() + "}", d.getDescription());
                } else {
                    map.put("{" + field.getName() + "}", field.get(vo).toString());
                }
            }
            return map;
        }

    }

}
