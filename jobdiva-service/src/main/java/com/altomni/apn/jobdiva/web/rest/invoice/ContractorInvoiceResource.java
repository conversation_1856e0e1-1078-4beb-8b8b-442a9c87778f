package com.altomni.apn.jobdiva.web.rest.invoice;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.service.dto.invoice.*;
import com.altomni.apn.jobdiva.service.invoice.ContractorInvoiceService;
import com.altomni.apn.jobdiva.service.vo.invoice.*;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.List;

/**
 * invoice controller
 *
 * <AUTHOR> zhang.lei
 * @date : 2023-6-6
 * @link : interface document address :https://intelliprogroup.larksuite.com/docx/D3CMdWMJDojvFYx3ytTuzYtJsze
 */
@Api(tags = {"invoiceResource"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ContractorInvoiceResource {

    @Autowired
    ContractorInvoiceService contractorInvoiceService;

    /**
     * list search
     *
     * @param dto
     * @param pageable
     * @return
     */
    @PostMapping("/contractor/invoice/search")
    @Timed
    public ResponseEntity<List<ContractorInvoiceListVO>> search(@RequestBody ContractorInvoiceSearchDTO dto, @PageableDefault Pageable pageable) {
        log.info("[invoice: User @{}] REST search invoice list:", SecurityUtils.getUserId());
        Page<ContractorInvoiceListVO> contractorInvoiceListVOPage = contractorInvoiceService.searchInvoiceList(dto, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(contractorInvoiceListVOPage, "/api/v3/contractor/invoice/search");
        return ResponseEntity.ok().headers(headers).body(contractorInvoiceListVOPage.getContent());
    }

    @PostMapping("/contractor/invoice")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<ContractorInvoiceCreateVO> save(@RequestBody ContractorInvoiceCreateDTO dto) {
        log.info("[invoice: User @{}] REST create invoice method:", SecurityUtils.getUserId());
        ContractorInvoiceCreateVO contractorInvoiceCreateVO = contractorInvoiceService.save(dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(contractorInvoiceCreateVO);
    }

    @GetMapping("/contractor/invoice/failedTimesheet/search")
    @Timed
    public ResponseEntity<List<ContractorInvoiceFailedVO>> searchFailedInfo(ContractorInvoiceCreateDTO dto,
                                                                            Pageable pageable) {
        log.info("[invoice: User @{}] REST search failed timesheet and expense method:", SecurityUtils.getUserId());
        Page<ContractorInvoiceFailedVO> contractorInvoiceFailedVOPage = contractorInvoiceService.searchFailedTimesheetAndExpenseInfo(dto, pageable, false);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(contractorInvoiceFailedVOPage, "/api/v3/contractor/invoice/failedTimesheet/search");
        return ResponseEntity.ok().headers(headers).body(contractorInvoiceFailedVOPage.getContent());
    }

    @PostMapping("/contractor/invoice/failedTimesheet/download")
    @Timed
    public void getFailedTimesheetAndExpense(@RequestBody ContractorInvoiceCreateDTO dto, HttpServletResponse response) {
        log.info("[invoice: User @{}] REST download failed timesheet and expense:", SecurityUtils.getUserId());
        log.info(" download failed timesheet param {}:", JSON.toJSONString(dto));
        contractorInvoiceService.downloadFailedTimesheetAndExpense(dto, response);
    }

    @GetMapping("/contractor/invoice/{id}")
    @Timed
    public ResponseEntity<ContractorInvoiceViewVO> getInvoice(@PathVariable BigInteger id) {
        log.info("[assignment: User @{}] getInvoice:", SecurityUtils.getUserId());
        ContractorInvoiceViewVO vo = contractorInvoiceService.view(id);
        vo.setExpenseList(null);
        return ResponseEntity.ok(vo);
    }

    @PutMapping("/contractor/invoice/void")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity invoiceVoid(@RequestBody ContractorInvoiceVoidAndPrintDTO dto) {
        log.info("[invoice: User @{}] REST invoice void method:", SecurityUtils.getUserId());
        if (null == dto.getInvoiceIdList() || dto.getInvoiceIdList().isEmpty()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        contractorInvoiceService.invoiceVoid(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PutMapping("/contractor/invoice/ungroup")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity invoiceUngroup(@RequestBody ContractorInvoiceVoidAndPrintDTO dto) {
        log.info("[invoice: User @{}] REST invoice ungroup method:", SecurityUtils.getUserId());
        if (null == dto.getInvoiceIdList() || dto.getInvoiceIdList().isEmpty()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        contractorInvoiceService.ungroup(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PutMapping("/contractor/invoice")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity modify(@RequestBody ContractorInvoiceEditDTO dto) {
        log.info("[invoice: User @{}] REST invoice modify method:", SecurityUtils.getUserId());
        if (null == dto.getId() || null == dto.getInvoiceType()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        contractorInvoiceService.update(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/contractor/invoice/print")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity print(HttpServletResponse response, @RequestBody ContractorInvoiceVoidAndPrintDTO dto) {
        log.info("[invoice: User @{}] REST invoice print method:", SecurityUtils.getUserId());
        if (null == dto.getInvoiceIdList() || dto.getInvoiceIdList().isEmpty()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        contractorInvoiceService.print(dto, response);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @GetMapping("/contractor/invoice/checkInvoice")
    @Timed
    public ResponseEntity<List<ContractorInvoiceCheckInvoiceVO>> invoiceExist(@RequestBody ContractorInvoiceExistDTO dto) {
        log.info("[invoice: User @{}] REST invoice exist method:", SecurityUtils.getUserId());
        if (null == dto.getCheckInvoiceList() || dto.getCheckInvoiceList().isEmpty()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        List<ContractorInvoiceCheckInvoiceVO> voList = contractorInvoiceService.getIdByAssignmentIdAndWeekEndingDate(dto);
        return ResponseEntity.ok(voList);
    }

    @PostMapping("/contractor/invoice/dataMigrateInvoice")
    @Timed
    public ResponseEntity dataMigrateInvoice() {
        contractorInvoiceService.dataMigrateInvoice();
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/contractor/invoice/migrateInvoiceByDeleteCaTax")
    @Timed
    public ResponseEntity migrateInvoiceByDeleteCaTax(){
        contractorInvoiceService.migrateInvoiceByDeleteCaTax();
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/contractor/invoice/search-page-by-application")
    @Timed
    public ResponseEntity<List<ContractorInvoiceSearchByApplicationVO>> searchPageByApplication(@RequestBody ContractorInvoiceSearchByApplicationDTO dto) {
        log.info("[invoice: User @{}] REST request to search page by application , param = {}:", SecurityUtils.getUserId(), dto);
        Page<ContractorInvoiceSearchByApplicationVO> page = contractorInvoiceService.searchPageByApplication(dto);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/contractor/invoice/search-page-by-application");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }


}