package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingProcessesHistoriesDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String processId;

    private String detail;

    private Long packageId;

    private String packageName;

    private List<OnBoardingPackageDocumentsDTO> documents;

}
