package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.ApprovalStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingApprovementDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    private Long id;

    private Long talentRecruitmentProcessId;

    private ApprovalStatus approveStatus;

    @Size(max = 1000, message = "The notes is too long.")
    private String notes;

    private String timeZone;

}
