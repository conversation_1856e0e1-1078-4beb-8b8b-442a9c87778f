package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.CalculateMethodType;
import com.altomni.apn.common.domain.enumeration.jobdiva.CalculateMethodTypeConverter;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A record
 */
@Entity
@Data
@Table(name = "timesheet_calculate_state")
public class CalculateTypeAndState implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @Column(name = "is_default", nullable = false)
    private Boolean isDefault;

    @Column(name = "calculate_type", nullable = false)
    @Convert(converter = CalculateMethodTypeConverter.class)
    private CalculateMethodType calculateMethodType;

    @Column(name = "state", nullable = false)
    private String  state;

    @Column(name = "country_code", nullable = false)
    private String  countryCode;







}
