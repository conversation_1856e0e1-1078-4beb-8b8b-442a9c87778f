package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseType;
import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseTypeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;

/**
 * 报销发票明细;
 * <AUTHOR> zhang.lei
 * @date : 2023-6-6
 */
@ApiModel(value = "报销发票明细",description = "")
@Entity
@Table(name="t_invoice_expense_info")
@Data
public class TInvoiceExpenseInfo extends AbstractAuditingEntity implements Serializable,Cloneable{
    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id ;

    /** 发票id 业务主键 */
    @ApiModelProperty(name = "发票id 业务主键")
    @Column(name = "invoice_id")
    private BigInteger invoiceId ;

    /** 货币类型 */
    @ApiModelProperty(name = "货币类型")
    @Column(name = "currency_type")
    private Integer currencyType ;

    /** 报销种类枚举值 0-MEALS 1-OFFICE_TOOLS 2-MILEAGE 3-TRAVEL 4LODGING */
    @ApiModelProperty(name = "报销种类枚举值 0-MEALS 1-OFFICE_TOOLS 2-MILEAGE 3-TRAVEL 4LODGING")
    @Column(name = "expense_category")
    @Convert(converter = ExpenseTypeConverter.class)
    private ExpenseType expenseCategory ;

    /** 报销日期 */
    @ApiModelProperty(name = "报销日期")
    @Column(name = "expense_date")
    private LocalDate expenseDate ;

    /** 金额 */
    @ApiModelProperty(name = "金额")
    @Column(name = "expense_amount")
    private BigDecimal expenseAmount ;

    /** 排序 */
    @ApiModelProperty(name = "排序")
    @Column(name = "sort_index")
    private Integer sortIndex ;

    /**
     * 报销记录的索引
     * 同一个 assignment 在同一个 weekending 下，可以有多个报销单，多个报销单需要支持生成多个 invoice
     */
    @Column(name = "expense_index")
    private Integer expenseIndex = 0;

    /** 周几 */
    @ApiModelProperty(name = "周几")
    @Column(name = "week_day")
    private String weekDay ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "work_date")
    private LocalDate workDate ;
    
}
