package com.altomni.apn.jobdiva.service.common;

import com.altomni.apn.common.dto.translation.TextTranslationDTO;
import com.altomni.apn.common.dto.translation.TranslationResultDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Component
@FeignClient(value = "common-service")
public interface CommonService {

    @PostMapping("/common/api/v3/translation/text/translate")
    ResponseEntity<TranslationResultDTO> getTextTranslate(@RequestBody TextTranslationDTO textTranslationDTO);

}
