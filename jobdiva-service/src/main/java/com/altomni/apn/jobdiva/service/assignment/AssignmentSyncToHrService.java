package com.altomni.apn.jobdiva.service.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.jobdiva.domain.timesheet.ApproveRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetHolidayRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.service.dto.assignment.AssignmentDetailInfoDTO;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentGeneralInfoVO;

import java.time.LocalDate;
import java.util.List;

public interface AssignmentSyncToHrService {

    void buildAssignmentSyncToHrMq(List<String> fieldList, Long assignmentId);

    void buildAssignmentListSyncToHrMq(List<Long> assignmentIdList);

    void buildApproveListSyncToHrMq(List<ApproveRecord> approveRecordList, CommentsType commentsType);

    void buildTimeSheetRecordListSyncToHrMq(Long assignmentId, LocalDate weekEnd, LocalDate weekendingDate);

    void buildExpenseRecordListSyncToHrMq(Long assignmentId, LocalDate weekendingDate, Integer expenseIndex);

    void buildHolidayListSyncToHrMq(List<TimeSheetHolidayRecord> holidayRecordList);

    void buildNoHoursSyncToHrMq(ApproveRecord approveRecord, List<TimeSheetRecord> timeSheetRecords, TimeSheetStatus status);

    void buildTimeSheetRecordListByExcelSyncToHrMq(List<TimeSheetRecord> weekendingDateList, List<ApproveRecord> approveRecordList);

    void buildTimeSheetUserSyncToHrMq(TimeSheetUser user);

    void deleteAssignment(List<Long> assignmentId);

}
