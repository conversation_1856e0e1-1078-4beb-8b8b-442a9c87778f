package com.altomni.apn.jobdiva.service.dto.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetBreakTimeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

@Data
public class TimeSheetBreakTimeRecordDto implements Serializable {

    private Long id;

    @JsonIgnore
    private Long tenantId;

    @JsonIgnore
    private Long talentId;

    @JsonIgnore
    private LocalDate workDate;

    @JsonIgnore
    private String time;

    private String  weekDay;

    private Integer lineIndex;

    private Long assignmentId;

    private TimeSheetStatus status;

    private TimeSheetBreakTimeType breakTimeType;

    private String createdBy;

    private Instant createdDate;

    private String lastModifiedBy;

    private Instant lastModifiedDate;


}
