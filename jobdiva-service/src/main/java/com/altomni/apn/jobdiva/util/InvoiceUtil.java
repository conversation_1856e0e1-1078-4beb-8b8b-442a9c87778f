package com.altomni.apn.jobdiva.util;

import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseType;
import lombok.experimental.UtilityClass;

import java.util.regex.Pattern;

@UtilityClass
public class InvoiceUtil {

    /**
     * invoice number incr
     */
    public final static String INVOICE_NUMBER_KEY = "invoice_number_key";

    public final static String GROUP_INVOICE_NUMBER_KEY = "group_invoice_number_key";

    public final static String INVOICE_EXIST_KEY = "invoice:exist:key";

    public final static String NO_PREFIX = "NO_PREFIX";

    //s3获取文件 nacos中配置的名称
    public final static String S3_INVOICE_PREFIX = "groupinvoice";

    //s3获取邮件上传链接 nacos中配置的名称
    public final static String S3_EMAIL_ATTACHMENT_PREFIX = "emailAttachment";

    public static String getExpenseCategory(String expenseCategory) {
        StringBuilder sb = new StringBuilder();
        String[] category = expenseCategory.split(",");
        for (String str : category) {
            ExpenseType expenseType = ExpenseType.fromDbValue(Integer.valueOf(str));
            if (sb.length() == 0) {
                sb.append(expenseType.getDescription());
            } else {
                sb.append("," + expenseType.getDescription());
            }
        }
        return sb.toString();
    }

    private static final Pattern NUMBER_PATTERN = Pattern.compile("-?\\d+(\\.\\d+)?");
    public static boolean isNumeric2(String str) {
        return str != null && NUMBER_PATTERN.matcher(str).matches();
    }
}
