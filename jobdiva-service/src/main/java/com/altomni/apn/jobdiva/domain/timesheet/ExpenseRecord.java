package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.AutoAbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseType;
import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatusConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

/**
 * A record
 */
@ApiModel(description = "record for expense")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Data
@Table(name = "timesheet_expense_record")
public class ExpenseRecord extends AutoAbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GenericGenerator(name = "RedisIdGenerator",
            strategy = "com.altomni.apn.jobdiva.config.idgenerator.RedisIdGenerator",
            parameters = { @org.hibernate.annotations.Parameter(name = "table", value = "timesheet_expense_record")})
    @GeneratedValue(generator = "RedisIdGenerator")
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "tenant_id", nullable = false)
    @JsonIgnore
    private Long tenantId;

    @ApiModelProperty(value = "[Internal] uid is used for Spring security to identify user internally. It has the format of <userId>,<tenantId>")
    @Column(name = "talent_id", nullable = false)
    @JsonIgnore
    private Long talentId;


    @ApiModelProperty(value = "")
    @Column(name = "work_date", nullable = false)
    @JsonIgnore
    private LocalDate workDate;

    @ApiModelProperty(value = "")
    @Column(name = "cost", nullable = false)
    @JsonIgnore
    private Float cost;

    @ApiModelProperty(value = "bill file")
    @Column(name = "s3_key")
    private String s3Key;

    @ApiModelProperty(value = "week day")
    @Column(name = "week_day")
    private String  weekDay;

    @ApiModelProperty(value = "line index")
    @Column(name = "line_index")
    private Integer lineIndex;

    @Column(name = "expense_index")
    private Integer expenseIndex = 0;

    @ApiModelProperty(value = "submitted date")
    @Column(name = "submitted_date")
    private Instant submittedDate;

    @ApiModelProperty(value = "assgignment id")
    @Column(name = "assignment_id")
    private Long assignmentId;

    @ApiModelProperty(value = "approved")
    @Column(name = "status")
    @Convert(converter = TimeSheetStatusConverter.class)
    private TimeSheetStatus status;
    @ApiModelProperty(value = "expense type ")
    @Column(name = "expense_type")
    @Convert(converter = ExpenseTypeConverter.class)
    private ExpenseType expenseType;

    @Column(name = "week_start", nullable = false)
    private LocalDate weekStart;

    @Column(name = "week_end", nullable = false)
    private LocalDate weekEnd;

    @Column(name = "week_ending_date", nullable = false)
    private LocalDate weekEndingDate;

}
