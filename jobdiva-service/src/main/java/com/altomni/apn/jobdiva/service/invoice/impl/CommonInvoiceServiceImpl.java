package com.altomni.apn.jobdiva.service.invoice.impl;

import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.jobdiva.repository.invoice.CommonInvoiceNativeRepository;
import com.altomni.apn.jobdiva.service.invoice.CommonInvoiceService;
import com.altomni.apn.jobdiva.service.vo.invoice.InvoiceCommonVO;
import com.altomni.apn.jobdiva.service.vo.invoice.TenantUserAndEmailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service("commonInvoiceService")
public class CommonInvoiceServiceImpl implements CommonInvoiceService {

    @Resource
    CommonInvoiceNativeRepository commonInvoiceNativeRepository;

    @Override
    public List<InvoiceCommonVO> queryEmployeeName(Long tenantId, String name, BigInteger companyId) {
        return commonInvoiceNativeRepository.queryEmployeeName(tenantId, name, companyId);
    }

    @Override
    @ProcessConfidentialTalent(operation = ProcessConfidentialTalent.operation.FILTER)
    public List<InvoiceCommonVO> queryEmployeeNameByNameOrId(Long tenantId, String name) {
        return commonInvoiceNativeRepository.queryEmployeeNameByNameOrId(tenantId, name);
    }

    @Override
    public List<InvoiceCommonVO> queryCompanyEmployeeName(Long tenantId, BigInteger companyId) {
        List<InvoiceCommonVO> employeeNameVOS = commonInvoiceNativeRepository.queryCompanyEmployeeName(tenantId, companyId);
        List<InvoiceCommonVO> employeeNameTerminateVOS = commonInvoiceNativeRepository.queryEmployeeNameByTermination(tenantId, companyId);
        if(null == employeeNameVOS){
            employeeNameVOS = new ArrayList<>();
        }

        if(null == employeeNameTerminateVOS){
            employeeNameTerminateVOS = new ArrayList<>();
        }
        employeeNameVOS.addAll(employeeNameTerminateVOS);
        employeeNameVOS = employeeNameVOS.stream().distinct().collect(Collectors.toList());
        return employeeNameVOS;
    }

    @Override
    public List<InvoiceCommonVO> queryBillCompany(Long tenantId, String name) {
        return commonInvoiceNativeRepository.queryBillCompany(tenantId, name);
    }

    @Override
    public List<InvoiceCommonVO> searchBillCompanyByName(Long tenantId, String name) {
        return commonInvoiceNativeRepository.searchBillCompanyByName(tenantId, name);
    }

    @Override
    public List<InvoiceCommonVO> queryBillContact(Long tenantId, String name) {
        return commonInvoiceNativeRepository.queryBillContact(tenantId, name);
    }

    @Override
    public List<TenantUserAndEmailVO> queryUserAndEmail(Long tenantId) {
        return commonInvoiceNativeRepository.queryUserAndEmail(tenantId);
    }
}
