package com.altomni.apn.jobdiva.domain.timesheet;


import com.altomni.apn.common.domain.enumeration.jobdiva.ManagerRoleConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.ManagerRoleType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@Table(name = "timesheet_manager")
public class TimeSheetManager  implements Serializable
{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @Column(name = "talent_id", nullable = false)
    @JsonIgnore
    private Long talentId;

    @Column(name = "client_id", nullable = false)
    @JsonIgnore
    private Long clientId;

    @Column(name = "role", nullable = false)
    @Convert(converter = ManagerRoleConverter.class)
    private ManagerRoleType role;


    @Column(name = "assignment_id", nullable = false)
    private Long assignmentId;



}
