package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * InvoiceExpenseInfoDTO
 * <AUTHOR> zhang.lei
 * @date : 2023-6-6
 */
@ApiModel(description = "invoiceExpenseInfoDTO")
@Data
public class InvoiceExpenseInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long invoiceId ;

    private Integer currencyType ;

    private ExpenseType expenseCategory ;

    private Date expenseDate ;

    private BigDecimal expenseAmount ;

    private Integer sortIndex ;

    private String weekDay ;

    private LocalDate workDate ;

    private String createdBy ;

    private LocalDate createdDate ;

    private String lastModifiedBy ;

    private LocalDate lastModifiedDate ;
}
