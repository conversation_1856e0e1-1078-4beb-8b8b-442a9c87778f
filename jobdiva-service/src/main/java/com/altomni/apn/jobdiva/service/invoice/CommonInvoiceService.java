package com.altomni.apn.jobdiva.service.invoice;

import com.altomni.apn.jobdiva.service.vo.invoice.InvoiceCommonVO;
import com.altomni.apn.jobdiva.service.vo.invoice.TenantUserAndEmailVO;

import java.math.BigInteger;
import java.util.List;

public interface CommonInvoiceService {

    List<InvoiceCommonVO> queryEmployeeName(Long tenantId, String name, BigInteger companyId);

    List<InvoiceCommonVO> queryEmployeeNameByNameOrId(Long tenantId, String name);

    List<InvoiceCommonVO> queryCompanyEmployeeName(Long tenantId, BigInteger companyId);

    List<InvoiceCommonVO> queryBillCompany(Long tenantId,String name);

    List<InvoiceCommonVO> searchBillCompanyByName(Long tenantId,String name);

    List<InvoiceCommonVO> queryBillContact(Long tenantId,String name);

    List<TenantUserAndEmailVO> queryUserAndEmail(Long tenantId);
}
