package com.altomni.apn.jobdiva.service.dto.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * A BillInfo
 */
@ApiModel(description = "BillInfo")
@Data
public class BillInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "contactId ")
    private Long contactId ;
    private Long assignmentId;
    private Boolean isExcept;
    private OverTimeType overtimeType;
    private GroupInvoiceType groupInvoiceType;
    private String groupInvoiceContent;
    private GroupInvoiceContentType invoiceContentType;
    private ExpenseInvoiceType expenseInvoice;
    private DiscountType discountType;
    private BigDecimal paymentTerms;
    private BigDecimal netBillRate;
    private BigDecimal netOverTimeRate;
    private BigDecimal netDoubleTimeRate;
    private BigDecimal hourlyGM;
    private List<AssignmentPayRateInfo> payRateInfo;
    private AssignmentDivision assignmentDivision;

}
