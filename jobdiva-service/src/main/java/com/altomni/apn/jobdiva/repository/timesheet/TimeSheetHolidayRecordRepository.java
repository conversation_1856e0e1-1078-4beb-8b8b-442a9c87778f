package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetHolidayRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Repository
public interface TimeSheetHolidayRecordRepository extends JpaRepository<TimeSheetHolidayRecord, Long> {

    @Modifying
    @Transactional
    @Query(value = " delete from time_sheet_holiday_record where assignment_id = ?1 and week_end = ?2 ", nativeQuery = true)
    void deleteByAssignmentIdAndWeekend(Long assignmentId, LocalDate weekend);

    List<TimeSheetHolidayRecord> findOneByAssignmentIdAndWeekEnd(Long assignmentId, LocalDate weekend);

    @Modifying
    @Transactional
    @Query(value = " update time_sheet_holiday_record set rate = ?3, currency = ?2  where assignment_id = ?1 ", nativeQuery = true)
    void updateHolidayOtByAssignmentId(Long assignmentId, Integer currency, BigDecimal rate);


    @Modifying
    @Transactional
    @Query(value = " update time_sheet_holiday_record set week_end = ?2 where assignment_id = ?1 and week_end between ?3 and ?2 ", nativeQuery = true)
    void updateHolidayWeekendByWeekendingDate(Long assignmentId, LocalDate weekendingDate, LocalDate weekendingDateStart);

    @Modifying
    @Transactional
    @Query(value = " delete from time_sheet_holiday_record where assignment_id = ?1 and week_end between ?2 and ?3 ", nativeQuery = true)
    void deleteHolidayByWeekEnd(Long assignmentId, LocalDate startDate, LocalDate endDate);

    List<TimeSheetHolidayRecord> findAllByAssignmentId(Long assignmentId);

    List<TimeSheetHolidayRecord> findAllByAssignmentIdIn(List<Long> assignmentId);

}
