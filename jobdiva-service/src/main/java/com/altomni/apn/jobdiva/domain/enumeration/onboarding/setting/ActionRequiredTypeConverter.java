package com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class ActionRequiredTypeConverter extends AbstractAttributeConverter<ActionRequiredType, Integer> {
    public ActionRequiredTypeConverter() {
        super(ActionRequiredType::toDbValue, ActionRequiredType::fromDbValue);
    }
}
