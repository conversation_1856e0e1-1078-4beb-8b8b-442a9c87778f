package com.altomni.apn.jobdiva.service.vo.invoice;

import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.jobdiva.domain.invoice.TContractorInvoice;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(description = "ContractorInvoiceViewVO")
public class ContractorInvoiceViewVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigInteger id;
    private String invoiceNumber;
    private String groupInvoiceNumber;
    private String poNumber;
    private String clientInvoiceNumber;
    private String employeeName;
    private BigInteger assignmentId;
    private String companyName;
    private String companyContact;
    private String invoiceType;
    private String invoiceStatus;
    private String approver;
    private Timestamp approverDate;
    private String assignmentDivision;
    private String note;
    private BigDecimal totalAmount;
    private BigInteger jobId;
    private String jobTitle;
    private String commentsNote;
    private String expenseComments;
    private Timestamp expenseSubmitDate;
    private Timestamp invoiceDate;
    private LocalDate createdDate;
    private Timestamp weekEndingDate;
    private String countryCode;
    private LocalDate weekEnd;
    private LocalDate weekStart;
    private String clientLocation ;
    private String clientAddress ;
    private String clientName ;
    List<ContractorInvoiceTimesheetVO> regularInvoiceList;

    List<Map<String,Object>> expenseInvoiceList;

    List<String> expenseCategoryList;

    List<ContractorInvoiceExpenseVO> expenseList;

    public static ContractorInvoiceViewVO transformToVo(TContractorInvoice source){
        ContractorInvoiceViewVO vo = new ContractorInvoiceViewVO();
        ServiceUtils.myCopyProperties(source,vo);
        vo.setInvoiceType(source.getInvoiceType().name());
        if(source.getAssignmentDivision() != null){
            vo.setAssignmentDivision(source.getAssignmentDivision().name());
        }
        vo.setInvoiceStatus(source.getInvoiceStatus().name());
        vo.setExpenseComments(source.getExpenseComments());
        vo.setExpenseSubmitDate(source.getExpenseSubmitDate());
        vo.setPoNumber(source.getPONumber());
        vo.setEmployeeName(source.getTalentName());
        vo.setCompanyContact(source.getCompanyContactName());
        vo.setApprover(source.getApproverName());
        vo.setApproverDate(source.getApprovalDate());
        vo.setInvoiceDate(source.getInvoiceDate());
        vo.setCreatedDate(source.getCreatedDate().atZone(ZoneId.systemDefault()).toLocalDate());
        vo.setExpenseComments(source.getExpenseComments());
        vo.setExpenseSubmitDate(source.getExpenseSubmitDate());
        vo.setGroupInvoiceNumber(source.getGroupInvoiceNumber());
        vo.setWeekEndingDate(source.getWeekEndingDate());
        vo.setAssignmentId(source.getAssignmentId());
        vo.setCommentsNote(source.getCommentNote());
        vo.setClientAddress(source.getClientAddress());
        vo.setClientLocation(source.getClientLocation());
        vo.setClientName(source.getClientName());
        return vo;
    }
}
