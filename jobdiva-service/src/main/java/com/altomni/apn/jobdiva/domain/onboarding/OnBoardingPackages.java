package com.altomni.apn.jobdiva.domain.onboarding;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackagesDTO;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(description = "onBoarding packages entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "onboarding_packages")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingPackages extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @Column(nullable = false)
    private String name;

    @Column
    private String description;

    @ApiModelProperty(value = "Whether user is activated. Default is true. Read Only.")
    @NotNull
    @Column(nullable = false)
    private boolean activated = true;

    public static OnBoardingPackagesDTO toDto(OnBoardingPackages packages) {
        OnBoardingPackagesDTO dto = new OnBoardingPackagesDTO();
        ServiceUtils.myCopyProperties(packages, dto);
        return dto;
    }
}
