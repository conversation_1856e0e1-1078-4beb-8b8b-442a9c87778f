package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.AutoAbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatusConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

/**
 * A record
 */
@ApiModel(description = "record for timeSheet")
@Entity
@Data
@Table(name = "time_sheet_record")
public class TimeSheetRecord extends AutoAbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GenericGenerator(name = "RedisIdGenerator",
            strategy = "com.altomni.apn.jobdiva.config.idgenerator.RedisIdGenerator",
            parameters = { @org.hibernate.annotations.Parameter(name = "table", value = "time_sheet_record")})
    @GeneratedValue(generator = "RedisIdGenerator")
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "tenant_id", nullable = false)
    @JsonIgnore
    private Long tenantId;

    @ApiModelProperty(value = "talent id")
    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    @ApiModelProperty(value = "assignment id")
    @Column(name = "assignment_id")
    private Long assignmentId;

    @ApiModelProperty(value = "work date")
    @Column(name = "work_date")
    private LocalDate workDate;

    @ApiModelProperty(value = "work hours")
    @Column(name = "work_hours")
    private Float workHours;


    @ApiModelProperty(value = "regularHours")
    @Column(name = "regular_hours")
    private Float regularHours;


    @ApiModelProperty(value = "regularHours")
    @Column(name = "over_time")
    private Float overTime;

    @ApiModelProperty(value = "doubleTime")
    @Column(name = "double_time")
    private Float doubleTime;

    @ApiModelProperty(value = "week day")
    @Column(name = "week_day")
    private String  weekDay;


    @ApiModelProperty(value = "totalTime")
    @Column(name = "total_hours")
    private Float totalHours;


    @ApiModelProperty(value = "submitted date")
    @Column(name = "submitted_date")
    private Instant submittedDate;

    @ApiModelProperty(value = "totalTime")
    @Column(name = "status")
    @Convert(converter = TimeSheetStatusConverter.class)
    private TimeSheetStatus status;

    @ApiModelProperty(value = "time sheet type ")
    @Column(name = "time_sheet_type")
    @Convert(converter = TimeSheetTypeConverter.class)
    private TimeSheetType timeSheetType;

    /***
     * 真实的每周最后一天，用于计算 otdt
     */
    @Column(name = "week_ending_date")
    private LocalDate weekEndingDate;

    @Column(name = "week_start")
    private LocalDate weekStart;

    @Column(name = "week_end")
    private LocalDate weekEnd;

    @Column(name = "week_id")
    private Long weekId;


}
