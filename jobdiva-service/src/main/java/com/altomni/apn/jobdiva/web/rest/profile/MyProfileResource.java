package com.altomni.apn.jobdiva.web.rest.profile;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;
import com.altomni.apn.common.dto.talent.TalentResumeOutput;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactProfile;
import com.altomni.apn.jobdiva.service.company.CompanyService;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.talent.TalentService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = {"MyProfile Management"})
@RestController
@RequestMapping("/api/v3/profile")
public class MyProfileResource {

    private final Logger log = LoggerFactory.getLogger(MyProfileResource.class);

    @Resource
    private TalentService talentService;
    @Resource
    private CompanyService companyService;
    @Resource
    private StoreService storeService;

    /**
     * GET get profile info by client contact id
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "Get profile info by client contact id")
    @GetMapping("/contacts/{id}")
    public ResponseEntity<SalesLeadClientContactProfile> getContactInfo(@ApiParam(value = "client contact id", required = true) @PathVariable Long id) {
        log.info("[APN: MyProfile @{}] REST request to get ContactInfo by talent id: {}", SecurityUtils.getUserId(), id);
        SalesLeadClientContactProfile result = companyService.findSalesLeadClientContactProfile(id).getBody();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * GET get profile info by talentId
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "Get profile info by talent id")
    @GetMapping("/talents/{id}")
    public ResponseEntity<TalentDTOV3> getTalentInfo(@ApiParam(value = "talent id", required = true) @PathVariable Long id) {
        log.info("[APN: MyProfile @{}] REST request to get TalentInfo list by talent id: {}", SecurityUtils.getUserId(), id);
        TalentDTOV3 result = talentService.getTalent(id).getBody();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * PUT update profile info by talent id
     *
     * @param id
     * @param dto
     * @return
     */
    @ApiOperation(value = "Update profile info by talent id")
    @PutMapping("/talents/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentDTOV3> updateTalentInfo(@ApiParam(value = "talent id", required = true) @PathVariable Long id, @Valid @RequestBody TalentDTOV3 dto) {
        log.info("[APN: MyProfile @{}] REST request to update talentInfo by talent id: {}", SecurityUtils.getUserId(), id);
        dto.setId(id);
        TalentDTOV3 result = talentService.updateTalentInfo(id, dto).getBody();
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @ApiOperation(value = "Get resume list by talent id")
    @GetMapping("/talent-resume/{id}")
    public ResponseEntity<List<TalentResumeDTO>> getResumeList(@ApiParam(value = "talent id", required = true) @PathVariable Long id) {
        log.info("[APN: MyProfile @{}] REST request to get TalentResume list by talent id: {}", SecurityUtils.getUserId(), id);
        TalentResumeOutput body = talentService.getTalentResumeByTalentId(id).getBody();
        if(body == null) {
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
        List<TalentResumeDTO> result = body.getResumeInfo() == null ? new ArrayList<>() : body.getResumeInfo().
                stream().filter(talentResumeDTO -> ResumeSourceType.CONTRACTOR_PORTAL.equals(talentResumeDTO.getSourceType())).collect(Collectors.toList());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


}
