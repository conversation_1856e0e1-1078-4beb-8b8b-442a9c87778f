package com.altomni.apn.jobdiva.web.rest.timesheet;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.jobdiva.ManagerRoleType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.timesheet.ClientService;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetExpenseService;
import com.altomni.apn.jobdiva.service.vo.timesheet.RecordDetailVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@Api(tags = {"TimeSheetClientResource"})
@RestController
@RequestMapping("/api/v3/client")
public class ClientResource {

    @Resource
    private ClientService clientService;

    @Resource
    private TimeSheetExpenseService expenseService;

    @PostMapping("/timeSheet/approve")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> timeSheetApprove(@RequestBody ApproveDTO dto) {
        log.info("[timesheet: User @{}] was trying to approve timesheet", SecurityUtils.getUserId());
        if (!clientService.isTimeSheetClientUser(dto.getRecordIds(), SecurityUtils.getUserId(), RecordType.TIME_SHEET)) {
            throw new CustomParameterizedException("Access denied !");
        }
        Integer records = clientService.timeSheetApprove(dto, ManagerRoleType.CLIENT);
        return ResponseEntity.ok(records);
    }

    @PostMapping("/expense/approve")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> expenseApprove(@RequestBody ApproveDTO dto) {
        log.info("[timesheet: User @{}] REST expense approve param : {}", SecurityUtils.getUserId(), dto);
        if (!clientService.isTimeSheetClientUser(dto.getRecordIds(), SecurityUtils.getUserId(), RecordType.EXPENSE)) {
            throw new CustomParameterizedException("Access denied !");
        }
        Integer records = clientService.expenseApprove(dto, ManagerRoleType.CLIENT);
        return ResponseEntity.ok(records);
    }

    @PostMapping("/timeSheet/summary")
    @Timed
    public ResponseEntity<SummaryDataVO> timeSheetSummary(@RequestBody SummaryQueryDTO dto) {
        log.info("[client: User @{}] REST timeSheetSummary:", SecurityUtils.getUserId());
        dto.setClientId(SecurityUtils.getUserId());
        dto.setRecordIds(null);
        SummaryDataVO records = clientService.timeSheetSummary(dto);
        return ResponseEntity.ok(records);
    }

    @PostMapping("/expense/summary")
    @Timed
    public ResponseEntity<SummaryDataVO> expenseSummary(@RequestBody SummaryQueryDTO dto) {
        log.info("[client: User @{}] REST expenseSummary:", SecurityUtils.getUserId());
        dto.setClientId(SecurityUtils.getUserId());
        dto.setRecordIds(null);
        SummaryDataVO records = clientService.expenseSummary(dto);
        return ResponseEntity.ok(records);
    }

    @PostMapping("/timeSheet/summary/download")
    @Timed
    public void timeSheetSummaryDownload(HttpServletResponse response, @RequestBody SummaryQueryDTO dto) {
        log.info("[client: User @{}] REST timeSheet summary download", SecurityUtils.getUserId());
        clientService.timeSheetSummaryDownload(dto,response);
    }

    @PostMapping("/expense/summary/download")
    @Timed
    public void expenseSummaryDownload(HttpServletResponse response, @RequestBody SummaryQueryDTO dto) {
        log.info("[client: User @{}] REST expense summary download", SecurityUtils.getUserId());
        clientService.expenseSummaryDownload(dto,response);
    }

    /**
     * find expense by id for client
     * @param dto
     * @return
     */

    @PostMapping("/expense/detail/info")
    @Timed
    public ResponseEntity<BreakTimeDTO> expenseInfo(@RequestBody RecordSearchDTO dto) {
        log.info("[client: User @{}] REST expenseInfo:", SecurityUtils.getUserId());
        BreakTimeDTO result = expenseService.findRecordById(dto);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/timeSheet/detail/download")
    @Timed
    public void timeSheetDetailDownload(HttpServletResponse response, @RequestBody RecordSearchDTO dto, @RequestParam(name = "is24timeFlag", required = false, defaultValue = "false") Boolean is24timeFlag) {
        log.info("[client: User @{}] REST timeSheetDetailDownload:", SecurityUtils.getUserId());
        clientService.timeSheetRecordDetailDownload(dto,response,is24timeFlag);
    }

    @PostMapping("/timeSheet/detail/download/pdf")
    @Timed
    public void timeSheetDetailDownloadPdf(HttpServletResponse response, @RequestBody RecordSearchDTO dto, @RequestParam(name = "is24timeFlag", required = false, defaultValue = "false") Boolean is24timeFlag) {
        log.info("[client: User @{}] REST timeSheet detail download pdf:", SecurityUtils.getUserId());
        clientService.timeSheetRecordDetailDownloadPdf(dto,response,is24timeFlag);
    }

    @PostMapping("/timeSheet/detail/download/batch/pdf")
    @Timed
    public void timeSheetDetailDownloadBatchPdf(HttpServletResponse response, @RequestBody DownloadPdfDto dto, @RequestParam(name = "is24timeFlag", required = false, defaultValue = "false") Boolean is24timeFlag) throws IOException {
        log.info("[client: User @{}] REST timeSheet detail download batch pdf:", SecurityUtils.getUserId());
        clientService.timeSheetRecordDetailDownloadBatchPdf(dto,response,is24timeFlag);
    }

    /**
     *
     * @param response
     * @param dto
     * @return
     */
    @PostMapping("/expense/detail/download")
    @Timed
    public void expenseDetailDownload(HttpServletResponse response, @RequestBody RecordSearchDTO dto) {
        log.info("[client: User @{}] REST expense detail download", SecurityUtils.getUserId());
        clientService.expenseRecordDetailDownload(dto,response);
    }

    /**
     *
     * @param response
     * @param dto
     * @return
     */
    @PostMapping("/expense/detail/download/pdf")
    @Timed
    public void expenseDetailDownloadPdf(HttpServletResponse response, @RequestBody RecordSearchDTO dto) {
        log.info("[client: User @{}] REST expense detail download pdf", SecurityUtils.getUserId());
        clientService.expenseRecordDetailDownloadPdf(dto,response);
    }

    /**
     *
     * @param response
     * @param dto
     * @return
     */
    @PostMapping("/expense/detail/download/batch/pdf")
    @Timed
    public void expenseDetailDownloadBatchPdf(HttpServletResponse response, @RequestBody DownloadPdfDto dto) throws IOException {
        log.info("[apn client @{}] download batch expense pdf:", SecurityUtils.getUserId());
        clientService.expenseRecordDetailDownloadBatchPdf(dto,response);
    }

    @PostMapping("/expense/recordDetail")
    public ResponseEntity<RecordDetailVO> expenseDetail(@RequestBody RecordSearchDTO dto) {
        log.info("[client: User @{}] REST expense detail", SecurityUtils.getUserId());
        RecordDetailVO data = clientService.expenseRecordDetail(dto);
        return ResponseEntity.ok(data);
    }

    @PostMapping("/timeSheet/recordDetail")
    public ResponseEntity<RecordDetailVO> timeSheetDetail(@RequestBody RecordSearchDTO dto) {
        log.info("[client: User @{}] REST time sheet detail", SecurityUtils.getUserId());
        RecordDetailVO data = clientService.timesheetRecordDetail(dto);
        return ResponseEntity.ok(data);
    }

}
