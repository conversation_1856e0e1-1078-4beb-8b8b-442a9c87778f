package com.altomni.apn.jobdiva.domain.enumeration.onboarding.process;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class OperationStatusConverter extends AbstractAttributeConverter<OperationStatus, Integer> {
    public OperationStatusConverter() {
        super(OperationStatus::toDbValue, OperationStatus::fromDbValue);
    }
}
