package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;

@ApiModel(value = "附件记录",description = "")
@Entity
@Table(name="t_email_attachment_record")
public class TEmailAttachmentRecord extends AbstractAuditingEntity implements Serializable,Cloneable{

    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private  BigInteger id ;
    
    /** 邮件id */
    @ApiModelProperty(name = "邮件id")
    @Column(name = "email_id")
    private  BigInteger emailId ;
    /** 附件地址 */
    @ApiModelProperty(name = "附件地址")
    @Column(name = "attachment_url")
    private String attachmentUrl ;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getEmailId() {
        return emailId;
    }

    public void setEmailId(BigInteger emailId) {
        this.emailId = emailId;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }
}
