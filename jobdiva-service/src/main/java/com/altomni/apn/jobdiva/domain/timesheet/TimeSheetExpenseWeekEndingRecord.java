package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

/**
 * A record
 */
@ApiModel(description = "week ending record for expense")
@Entity
@Data
@Table(name = "time_sheet_expense_week_ending_record")
public class TimeSheetExpenseWeekEndingRecord extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "tenant_id", nullable = false)
    @JsonIgnore
    private Long tenantId;

    @ApiModelProperty(value = "record id")
    @Column(name = "record_id", nullable = false)
    @JsonIgnore
    private Long recordId;

    @ApiModelProperty(value = "talent id")
    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "manager")
    private String manager;

    @Column(name = "manager_id")
    private Long managerId;

    @Column(name = "am_approver")
    private String amApprover;

    @Column(name = "am_approver_id")
    private Long amApproverId;

    @Column(name = "primary_manager")
    private String primaryManager;

    @Column(name = "primary_manager_id")
    private Long primaryManagerId;

    @Column(name = "am")
    private String am;

    @Column(name = "am_ids")
    private String amIds;

    @Column(name = "client_ids")
    private String clientIds;

    @Column(name = "allow_submit_expense")
    private Boolean allowSubmitExpense;

    @Column(name = "job_title")
    private String jobTitle;

    @Column(name = "company_name")
    private String companyName;

    @Column(name = "full_name")
    private String fullName;

    @Column(name = "approved_date")
    private Instant approvedDate;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Column(name = "assignment_status")
    @Convert(converter = AssignmentStatusTypeConverter.class )
    private AssignmentStatusType assignmentStatus;

    @ApiModelProperty(value = "assignment id")
    @Column(name = "assignment_id")
    private Long assignmentId;

    @ApiModelProperty(value = "work date")
    @Column(name = "work_date")
    private LocalDate workDate;

    @ApiModelProperty(value = "cost")
    @Column(name = "cost")
    private Float cost;

    @ApiModelProperty(value = "submitted date")
    @Column(name = "submitted_date")
    private Instant submittedDate;

    @ApiModelProperty(value = "totalTime")
    @Column(name = "status")
    @Convert(converter = TimeSheetStatusConverter.class)
    private TimeSheetStatus status;

    @ApiModelProperty(value = "expense type ")
    @Column(name = "expense_type")
    @Convert(converter = ExpenseTypeConverter.class)
    private ExpenseType expenseType;

    @Column(name = "expense_index")
    private Integer expenseIndex = 0;


}
