package com.altomni.apn.jobdiva.service.timesheet.impl;

import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetUserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class TimesheetUserMapping implements UserDetailsService {
    private final TimeSheetUserRepository timeSheetUserRepository;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        TimeSheetUser timesheetUser = timeSheetUserRepository.findByUsernameOrEmail(username);
        if (timesheetUser != null) {
            timesheetUser.getAuthorities();
        }
        return timesheetUser;
    }
}
