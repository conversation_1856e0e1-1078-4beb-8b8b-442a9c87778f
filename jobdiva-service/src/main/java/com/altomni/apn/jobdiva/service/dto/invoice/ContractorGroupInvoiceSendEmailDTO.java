package com.altomni.apn.jobdiva.service.dto.invoice;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;

@ApiModel(description = "ContractorGroupInvoiceSendEmailDTO")
@Data
public class ContractorGroupInvoiceSendEmailDTO implements Serializable {

    private List<BigInteger>  invoiceIdList;

    private String subject;

    private List<String> sendToList;

    private List<String> ccList;

    private List<AttachmentFileDTO> attachmentList;

    private String content;

    private Integer sentToClient;

}


