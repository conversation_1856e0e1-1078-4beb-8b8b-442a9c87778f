package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.TInvoiceExpenseInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * Spring Data JPA repository for the InvoiceExpenseInfoRepository entity.
 * <AUTHOR> zhang.lei
 * @date : 2023-6-6
 */
@Repository
public interface InvoiceExpenseInfoRepository extends JpaRepository<TInvoiceExpenseInfo, BigInteger> {

    List<TInvoiceExpenseInfo> findByInvoiceId(BigInteger invoiceId);

}
