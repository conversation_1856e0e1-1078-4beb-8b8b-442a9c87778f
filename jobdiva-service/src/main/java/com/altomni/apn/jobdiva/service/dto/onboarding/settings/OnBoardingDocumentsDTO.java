package com.altomni.apn.jobdiva.service.dto.onboarding.settings;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.ActionRequiredType;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.DocumentType;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.SpecialDocumentType;
import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingDocuments;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingDocumentsDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    private Long id;

    @NotNull(message = "document name can not be null")
    @Pattern(regexp = "^[A-Za-z0-9_\\-\\u4e00-\\u9fa5\\s]+$",message = "The name format is incorrect")
    private String name;

    @NotNull(message = "s3Link can not be null")
    private String s3Key;

    @NotNull(message = "actionRequired can not be null")
    private ActionRequiredType actionRequired;

    @NotNull(message = "specialDocument can not be null")
    private SpecialDocumentType specialDocument;

    private DocumentType documentType;

    private Long packageId;

    private Integer ordering;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    //1、入职 2、附加文件
    private Integer onboardingType;

    public OnBoardingDocumentsDTO(Long packageId, Long id, String name, String s3Key, Integer ordering){
        this.packageId = packageId;
        this.id = id;
        this.name = name;
        this.s3Key = s3Key;
        this.ordering = ordering;
    }

    public OnBoardingDocumentsDTO(Long packageId, Long id, String name, String s3Key, Integer ordering,Integer onboardingType){
        this.packageId = packageId;
        this.id = id;
        this.name = name;
        this.s3Key = s3Key;
        this.ordering = ordering;
        this.onboardingType = onboardingType;
    }

    public OnBoardingDocumentsDTO(Long id, String name , String s3Key, ActionRequiredType actionRequired, String lastModifiedBy, Instant lastModifiedDate){
        this.id = id;
        this.name = name;
        this.s3Key = s3Key;
        this.actionRequired = actionRequired;
        this.lastModifiedBy = lastModifiedBy;
        this.lastModifiedDate = lastModifiedDate;
    }

    public static OnBoardingDocuments fromDto(OnBoardingDocumentsDTO dto) {
        OnBoardingDocuments document = new OnBoardingDocuments();
        ServiceUtils.myCopyProperties(dto, document);
        document.setTenantId(SecurityUtils.getTenantId());
        return document;
    }

}
