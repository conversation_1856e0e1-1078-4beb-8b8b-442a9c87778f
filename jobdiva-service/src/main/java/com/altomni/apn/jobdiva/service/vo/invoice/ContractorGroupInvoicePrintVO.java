package com.altomni.apn.jobdiva.service.vo.invoice;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.util.List;

/**
 * return print group invoice info
 */
@ApiModel(description = "ContractorGroupInvoicePrintVO")
public class ContractorGroupInvoicePrintVO implements Serializable{

    private Integer status;

    private Integer progressValue;

    List<FileInfo> fileList;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getProgressValue() {
        return progressValue;
    }

    public void setProgressValue(Integer progressValue) {
        this.progressValue = progressValue;
    }

    public List<FileInfo> getFileList() {
        return fileList;
    }

    public void setFileList(List<FileInfo> fileList) {
        this.fileList = fileList;
    }

    public static class FileInfo{
        String fileUrl;

        String fileName;

        public String getFileUrl() {
            return fileUrl;
        }

        public void setFileUrl(String fileUrl) {
            this.fileUrl = fileUrl;
        }

        public String getFileName() {
            return fileName;
        }

        public void setFileName(String fileName) {
            this.fileName = fileName;
        }
    }
}
