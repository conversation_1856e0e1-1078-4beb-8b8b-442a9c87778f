package com.altomni.apn.jobdiva.repository.process;

import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingProcessApprovalDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the OnBoardingPackageDocuments entity.
 */
@Repository
public interface OnBoardingProcessApprovalDetailsRepository extends JpaRepository<OnBoardingProcessApprovalDetails, Long> {

    @Query(value = "select a.* from onboarding_process_approval_details a where a.history_id = ?1 order by a.created_date desc limit 1",nativeQuery = true)
    OnBoardingProcessApprovalDetails findOneByHistoryId(Long historyId);

    @Query(value = "select count(a.id) from onboarding_process_approval_details a where a.history_id = ?1 and a.approval_status = ?2 ", nativeQuery = true)
    Integer countByHistoryIdAndApprovalStatus(Long historyId, Integer approvalStatus);

    List<OnBoardingProcessApprovalDetails> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
