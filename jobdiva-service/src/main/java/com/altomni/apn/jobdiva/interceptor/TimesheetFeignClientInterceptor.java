package com.altomni.apn.jobdiva.interceptor;

import com.altomni.apn.common.auth.timesheet_auth.TimesheetExternalAuthenticationToken;
import com.altomni.apn.common.utils.SecurityUtils;
import com.ipg.resourceserver.client.ClientTokenHolder;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * pass token between microservices
 *
 * <AUTHOR>
 */
@Component
@RefreshScope
public class TimesheetFeignClientInterceptor implements RequestInterceptor {
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String APN_INTERNAL_PIN = "APN_INTERNAL_PIN";
    private static final String TOKEN_TYPE = "Bearer";

    @Value("${application.apnInternalPin:3hlo7PZn}")
    private String apnInternalPin;


    @Override
    public void apply(RequestTemplate requestTemplate) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return;
        }
        requestTemplate.header(APN_INTERNAL_PIN, apnInternalPin);
        boolean timesheetUserAuthentication = authentication instanceof TimesheetExternalAuthenticationToken;
        boolean systemMail = requestTemplate.path().startsWith("/common/api/v3/campaign");
        // 外部用户请求，或者系统邮件请求调用，使用客户端模式token
        if (timesheetUserAuthentication || systemMail) {
            requestTemplate.removeHeader(AUTHORIZATION_HEADER);
            Optional.ofNullable(ClientTokenHolder.getInstance().getClientToken())
                    .ifPresent(token -> requestTemplate.header(AUTHORIZATION_HEADER, String.format("%s %s", TOKEN_TYPE, token.access_token())));
        } else {
            Optional.ofNullable(SecurityUtils.getCurrentUserToken())
                    .ifPresent(token -> requestTemplate.header(AUTHORIZATION_HEADER, String.format("%s %s", TOKEN_TYPE, token)));
        }

    }
}
