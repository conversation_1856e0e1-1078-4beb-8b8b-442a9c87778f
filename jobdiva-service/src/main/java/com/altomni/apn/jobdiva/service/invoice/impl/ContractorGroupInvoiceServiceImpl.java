package com.altomni.apn.jobdiva.service.invoice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.auth.LoginInformation;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarRelationEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.dto.calendar.CompleteSystemCalendarDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.store.UploadUrlDto;
import com.altomni.apn.common.dto.xxljob.XxlJobInvoiceOverdueDTO;
import com.altomni.apn.common.enumeration.enums.InvoiceTypeEnum;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.repository.enums.EnumCurrencyRepository;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import com.altomni.apn.company.domain.company.CompanyPurchaseOrder;
import com.altomni.apn.company.domain.company.CompanyPurchaseOrderDetail;
import com.altomni.apn.company.repository.company.CompanyPurchaseOrderDetailRepository;
import com.altomni.apn.company.repository.company.CompanyPurchaseOrderRepository;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.config.rabbit.QueueConfig;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentTimeSheet;
import com.altomni.apn.jobdiva.domain.invoice.*;
import com.altomni.apn.jobdiva.repository.invoice.*;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRepository;
import com.altomni.apn.jobdiva.service.application.ApplicationService;
import com.altomni.apn.jobdiva.service.dto.invoice.*;
import com.altomni.apn.jobdiva.service.finance.FinanceService;
import com.altomni.apn.jobdiva.service.invoice.ContractorGroupInvoiceService;
import com.altomni.apn.jobdiva.service.invoice.GroupInvoiceSendEmailService;
import com.altomni.apn.jobdiva.service.mail.MailService;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.vo.invoice.*;
import com.altomni.apn.jobdiva.util.InvoicePdfUtil;
import com.altomni.apn.jobdiva.util.InvoiceUtil;
import com.altomni.apn.user.service.calendar.CalendarService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Service("contractorGroupInvoiceService")
public class ContractorGroupInvoiceServiceImpl implements ContractorGroupInvoiceService {

    @Resource
    ContractorGroupInvoiceNativeRepository contractorGroupInvoiceNativeRepository;

    @Resource
    GroupInvoiceRepository groupInvoiceRepository;

    @Resource
    GroupInvoiceRecordRepository groupInvoiceRecordRepository;

    @Resource
    ContractorInvoiceRepository contractorInvoiceRepository;

    @Resource
    InvoiceTimesheetInfoRepository invoiceTimesheetInfoRepository;

    @Resource
    RecordPaymentRepository recordPaymentRepository;

    @Resource
    private StoreService storeService;

    @Resource
    GroupInvoiceSendEmailService groupInvoiceSendEmailService;

    @Resource
    MailService mailService;

    @Resource
    FinanceService financeService;

    @Resource
    ContractorInvoiceNativeRepository contractorInvoiceNativeRepository;

    @Resource(name = "invoiceRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource
    QueueConfig queueConfig;

    @Resource
    EnumCurrencyRepository enumCurrencyRepository;

    @Resource
    TMongodbToJobdivaTempRepository tMongodbToJobdivaTempRepository;

    @Resource
    TMongodbToJobdivaTempNativeRepository tMongodbToJobdivaTempNativeRepository;

    @Resource
    private ApplicationService applicationService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    TimeSheetRepository timeSheetRepository;

    @Resource
    CompanyPurchaseOrderDetailRepository companyPurchaseOrderDetailRepository;

    @Resource
    CompanyPurchaseOrderRepository companyPurchaseOrderRepository;

    @Value("${company_detail_url}")
    String companyDetailUrl;

    @Resource
    CommonRedisService commonRedisService;

    @Resource
    InvoiceRecordPaymentDetailRepository invoiceRecordPaymentDetailRepository;


    private final static String HTML_A_HREF_PREFIX = "<a href=\"";

    private final static String HTML_A_HREF_SUFFIX = "</a>";

    private final static String RIGHT_BRACKET = "\">";

    /**
     * Query invoice pagination information
     *
     * @param dto
     * @param pageable
     * @return
     */
    @Override
    public Page<ContractorGroupInvoiceListVO> searchGroupInvoice(ContractorGroupInvoiceSearchDTO dto, Pageable pageable) {
        dto.setTenantId(SecurityUtils.getTenantId());
        return contractorGroupInvoiceNativeRepository.searchGroupInvoiceList(dto, pageable);
    }

    /**
     * create group invoice
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractorGroupInvoiceCreateVO save(ContractorGroupInvoiceCreateDTO dto) {

        dto.setTenantId(SecurityUtils.getTenantId());
        List<Integer> invoiceList = dto.getInvoiceTypeList().stream().filter(s -> s.name() != "ALL").mapToInt(InvoiceType::toDbValue).boxed().collect(Collectors.toList());
        if (invoiceList.size() == 0) {
            invoiceList.add(InvoiceType.EXPENSE.toDbValue());
            invoiceList.add(InvoiceType.REGULAR.toDbValue());
        }

        //用于search fail info
        List<InvoiceType> invoiceTypeList = dto.getInvoiceTypeList().stream().filter(s -> s.name() != "ALL").collect(Collectors.toList());
        if (invoiceTypeList.size() == 0) {
            invoiceTypeList.add(InvoiceType.EXPENSE);
            invoiceTypeList.add(InvoiceType.REGULAR);
        }

        //返回结果
        ContractorGroupInvoiceCreateVO vo = new ContractorGroupInvoiceCreateVO();
        int successCount = 0;

        //group type
        if (dto.getGroupInvoiceBy().equals(GroupType.COMPANY.toDbValue())) {

            List<ContractorInvoiceFailedVO> invoiceFailedVOList = searchInvoiceFailedVO(dto, invoiceTypeList, dto.getEmployeeIdList());
            if (!invoiceFailedVOList.isEmpty()) {
                return searchFailedInvoice(vo, successCount, dto, invoiceTypeList);
            }

            //query data
            List<TContractorInvoice> tContractorInvoiceList = contractorInvoiceRepository.
                    queryInvoiceByCondition(BigInteger.valueOf(dto.getTenantId()), dto.getCompanyId(), dto.getEmployeeIdList(), dto.getFrom(), dto.getTo(), invoiceList);

            if (!tContractorInvoiceList.isEmpty()) {

                checkFrequencyMonthly(tContractorInvoiceList);

                //查询数据类型是否包含expense和timesheet
                Map<Integer, List<TContractorInvoice>> invoiceTypeMap = tContractorInvoiceList.stream().collect(groupingBy(s -> {
                    return s.getInvoiceType().toDbValue();
                }));
                if (invoiceTypeMap.size() != 2) {
                    if (invoiceTypeMap.containsKey(InvoiceType.REGULAR.toDbValue())) {
                        dto.setInvoiceTypeList(Arrays.asList(InvoiceType.REGULAR));
                    }
                    if (invoiceTypeMap.containsKey(InvoiceType.EXPENSE.toDbValue())) {
                        dto.setInvoiceTypeList(Arrays.asList(InvoiceType.EXPENSE));
                    }
                }

                //Is the current the same
                Integer currency = determineCurrencyConsistent(tContractorInvoiceList);

                log.info("group invoice create,starting to assemble group invoices based on the company");
                successCount = doSaveCompanyGroupInvoice(dto, tContractorInvoiceList, currency);
            }
        } else if (dto.getGroupInvoiceBy().equals(GroupType.EMPLOYEE.toDbValue())) {
            log.info("group invoice create,starting to assemble group invoices based on the company");
            successCount = doSaveEmployeeGroupInvoice(dto, invoiceList, invoiceTypeList);
        }


        searchFailedInvoice(vo, successCount, dto, invoiceTypeList);

        return vo;
    }

    /**
     * 检查bill frequency是否一致
     *
     * @param tContractorInvoiceList
     */
    private TimeSheetFrequencyType checkFrequencyMonthly(List<TContractorInvoice> tContractorInvoiceList) {

        List<Long> assignmentIds = tContractorInvoiceList.stream().map(TContractorInvoice::getAssignmentId).map(a -> a.longValue()).collect(Collectors.toList());

        List<AssignmentTimeSheet> assignmentTimeSheetList = timeSheetRepository.findFrequencyByAssignmentId(assignmentIds);

        if (assignmentTimeSheetList.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_CREATE_CHECK_FREQUENCY_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        Set<TimeSheetFrequencyType> frequencyList = assignmentTimeSheetList.stream().map(AssignmentTimeSheet::getFrequency).collect(Collectors.toSet());
        if (frequencyList.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_CREATE_CHECK_FREQUENCY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        return assignmentTimeSheetList.get(0).getFrequency();
    }

    /**
     * 查询未生成的Invoice 数据
     *
     * @param vo
     * @param successCount
     * @param dto
     * @param invoiceTypeList
     * @return
     */
    private ContractorGroupInvoiceCreateVO searchFailedInvoice(ContractorGroupInvoiceCreateVO vo,
                                                               Integer successCount, ContractorGroupInvoiceCreateDTO dto,
                                                               List<InvoiceType> invoiceTypeList) {
        vo.setSuccessCount(successCount);
        vo.setMissingCount(0);
        vo.setFailedCount(0);

        //search fail info
        List<ContractorInvoiceFailedVO> invoiceFailedVOList = searchInvoiceFailedVO(dto, invoiceTypeList, dto.getEmployeeIdList());
        log.info("create group invoice, Failed to query data ");
        if (null != invoiceFailedVOList && !invoiceFailedVOList.isEmpty()) {
            vo.setData(invoiceFailedVOList);
            if (dto.getGroupInvoiceBy().equals(GroupType.COMPANY.toDbValue())) {
                vo.setFailedCount(1);
            } else {
                Set<String> set = invoiceFailedVOList.stream().map(ContractorInvoiceFailedVO::getEmployeeName).collect(Collectors.toSet());
                vo.setFailedCount(set.size());
            }
            vo.setMissingCount(invoiceFailedVOList.size());
        }
        return vo;
    }

    /**
     * 查询ContractorInvoiceFailedVO
     *
     * @param dto
     * @param invoiceTypeList
     * @return
     */
    private List<ContractorInvoiceFailedVO> searchInvoiceFailedVO(ContractorGroupInvoiceCreateDTO dto, List<InvoiceType> invoiceTypeList, List<BigInteger> empIds) {

        SimpleDateFormat df = new SimpleDateFormat(DateUtil.YYYY_MM_DD);
        ContractorInvoiceCreateDTO invoiceCreateDTO = new ContractorInvoiceCreateDTO();
        invoiceCreateDTO.setTenantId(dto.getTenantId());
        invoiceCreateDTO.setFrom(df.format(dto.getFrom()));
        invoiceCreateDTO.setTo(df.format(dto.getTo()));
        invoiceCreateDTO.setCompanyId(dto.getCompanyId());
        invoiceCreateDTO.setEmployeeIdList(empIds);
        invoiceCreateDTO.setInvoiceTypeList(invoiceTypeList);
        List<ContractorInvoiceFailedVO> invoiceFailedVOList = contractorInvoiceNativeRepository.queryFailedTimesheetAndExpenseList(invoiceCreateDTO, true);
        return invoiceFailedVOList;
    }

    /**
     * 判断币种是否一致
     *
     * @param tContractorInvoiceList
     */
    private Integer determineCurrencyConsistent(List<TContractorInvoice> tContractorInvoiceList) {
        List<BigInteger> invoiceIdList = tContractorInvoiceList.stream().map(TContractorInvoice::getId).collect(Collectors.toList());
        List<Integer> currencyList = invoiceTimesheetInfoRepository.queryCurrencyTypeByInvoiceId(invoiceIdList);
        if (null == currencyList || currencyList.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_GROUP_CREATE_CURRENCY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        return currencyList.get(0);
    }

    /**
     * Create group invoice based on employee dimension
     * rule: Create invoices separately for each employee
     *
     * @param dto
     */
    private int doSaveEmployeeGroupInvoice(ContractorGroupInvoiceCreateDTO dto, List<Integer> invoiceList, List<InvoiceType> invoiceTypeList) {
        //Group based on talentId data
        //Map<BigInteger, List<TContractorInvoice>> talentIdMap = tContractorInvoiceList.stream().collect(groupingBy(TContractorInvoice::getTalentId));
        List<TGroupInvoice> tGroupInvoiceList = new ArrayList<>();
        Map<String, List<TGroupInvoiceRecord>> mapTGroupInvoiceRecords = new HashMap<>();
        List<TContractorInvoice> invoiceIdsList = new ArrayList<>();
        for (BigInteger empId : dto.getEmployeeIdList()) {

            //查询是否存在失败的数据
            List<ContractorInvoiceFailedVO> failedVOList = searchInvoiceFailedVO(dto, invoiceTypeList, Arrays.asList(empId));
            if (!failedVOList.isEmpty()) {
                continue;
            }

            //query data
            List<TContractorInvoice> invoices = contractorInvoiceRepository.
                    queryInvoiceByCondition(BigInteger.valueOf(dto.getTenantId()), dto.getCompanyId(), Arrays.asList(empId), dto.getFrom(), dto.getTo(), invoiceList);

            if (invoices.isEmpty()) {
                continue;
            }

            checkFrequencyMonthly(invoices);

            //Is the current the same
            Integer currency = determineCurrencyConsistent(invoices);

            TGroupInvoice tGroupInvoice = createGroupInvoice(dto, getSequence(), invoices, currency, GroupType.EMPLOYEE);

            Map<Integer, List<TContractorInvoice>> invoiceTypeMap = invoices.stream().collect(groupingBy(s -> {
                return s.getInvoiceType().toDbValue();
            }));
            if (invoiceTypeMap.size() != 2) {
                if (invoiceTypeMap.containsKey(InvoiceType.REGULAR.toDbValue())) {
                    tGroupInvoice.setInvoiceType(InvoiceType.REGULAR);
                }
                if (invoiceTypeMap.containsKey(InvoiceType.EXPENSE.toDbValue())) {
                    tGroupInvoice.setInvoiceType(InvoiceType.EXPENSE);
                }
            } else {
                tGroupInvoice.setInvoiceType(InvoiceType.ALL);
            }

            log.info("group invoice create,assemble group invoice data based on the company, group number :{}", tGroupInvoice.getGroupNumber());

            List<TGroupInvoiceRecord> tGroupInvoiceRecords = new ArrayList<>();
            assembleGroupInvoiceDetail(invoices, tGroupInvoiceRecords, mapTGroupInvoiceRecords, tGroupInvoice);

            tGroupInvoiceList.add(tGroupInvoice);
            invoiceIdsList.addAll(invoices);
        }

        if (!tGroupInvoiceList.isEmpty()) {
            groupInvoiceRepository.saveAll(tGroupInvoiceList);
            log.info("group invoice create,save all group invoice list");

            //Batch Insert IDs into Objects
            for (TGroupInvoice bean : tGroupInvoiceList) {
                if (mapTGroupInvoiceRecords.containsKey(bean.getGroupNumber() + bean.getTenantId() + bean.getCompanyId())) {
                    List<TGroupInvoiceRecord> invoices = mapTGroupInvoiceRecords.get(bean.getGroupNumber() + bean.getTenantId() + bean.getCompanyId());
                    invoices.forEach(i -> {
                        i.setGroupInvoiceId(bean.getId());
                    });

                    groupInvoiceRecordRepository.saveAll(invoices);
                    log.info("group invoice create,save group invoice record,groupInvoiceId:{}", bean.getId());

                    Set<Long> invoiceIdSet = invoices.stream().map(TGroupInvoiceRecord::getInvoiceId).map(i -> i.longValue()).collect(Collectors.toSet());
                    updateInvoiceStatusAndGroupInvoiceNumber(invoiceIdSet, bean);
                }

                //send to rabbit
                rabbitTemplate.convertAndSend(queueConfig.getExchange(), queueConfig.getRoutingKey(), bean.getId() + "&" + dto.getTimezone());
                log.info("group invoice send to mq,id:{},exchange:{},routeKey:{}", bean.getId(), queueConfig.getExchange(), queueConfig.getRoutingKey());
            }
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                applicationService.createInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(invoiceIdsList.stream()
                        .map(TContractorInvoice::getId).map(BigInteger::longValue).collect(Collectors.toList())).build());
            });

            return invoiceIdsList.stream().map(TContractorInvoice::getTalentId).collect(Collectors.toSet()).size();
        }
        return 0;
    }

    /**
     * Create invoice class general method
     *
     * @param dto
     * @param sequence
     * @param tContractorInvoiceList
     * @param currency
     * @return
     */
    private TGroupInvoice createGroupInvoice(ContractorGroupInvoiceCreateDTO dto, String sequence,
                                             List<TContractorInvoice> tContractorInvoiceList,
                                             Integer currency, GroupType type) {
        //创建group invoice 主体
        TGroupInvoice tGroupInvoice = new TGroupInvoice();
        tGroupInvoice.setGroupNumber(sequence);
        tGroupInvoice.setCompanyId(dto.getCompanyId());
        tGroupInvoice.setInvoiceType(dto.getInvoiceTypeList().get(0));
        tGroupInvoice.setGroupInvoiceStatus(GroupInvoiceStatus.INVOICED);
        tGroupInvoice.setCompanyName(tContractorInvoiceList.get(0).getCompanyName());
        tGroupInvoice.setCurrencyId(currency);
        tGroupInvoice.setGroupType(type);
        tGroupInvoice.setStatus(1);
        tGroupInvoice.setTenantId(BigInteger.valueOf(dto.getTenantId()));
        tGroupInvoice.setIncludeTimesheet(IncludeTimesheetType.fromDbValue(dto.getTimesheetIncluded()));
        tGroupInvoice.setDueWithinDays(dto.getDueDate());
        tGroupInvoice.setInvoiceDate(LocalDate.now());
        tGroupInvoice.setPaymentDueDate(DateUtil.getDayAddition(new Timestamp(new Date().getTime()), dto.getDueDate()));

        //获取assignmentDivisionSet 字符形式保存
        Set<String> assignmentDivisionSet = tContractorInvoiceList.stream().filter(a -> a.getAssignmentDivision() != null).map(TContractorInvoice::getAssignmentDivision).map(a -> a.name()).collect(Collectors.toSet());
        tGroupInvoice.setAssignmentDivision(StringUtils.join(assignmentDivisionSet.toArray(), ","));
        return tGroupInvoice;
    }

    private String getSequence() {
        //get invoice number
        String sequence = financeService.getCommonSequence(InvoiceUtil.GROUP_INVOICE_NUMBER_KEY, 10, "2", 1000000000L).getBody();
        if (null == sequence) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_CREATE_SEQUECE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        return sequence;
    }

    /**
     * Create group invoice data based on the company dimension
     * rule:Create an invoice
     *
     * @param dto
     * @param tContractorInvoiceList
     * @param currency
     */
    private int doSaveCompanyGroupInvoice(ContractorGroupInvoiceCreateDTO dto, List<TContractorInvoice> tContractorInvoiceList, Integer currency) {

        TGroupInvoice tGroupInvoice = createGroupInvoice(dto, getSequence(), tContractorInvoiceList, currency, GroupType.COMPANY);
        log.info("group invoice create,assemble group invoice data based on the company,data:{}", JSON.toJSONString(tGroupInvoice));

        //create group invoice record
        List<TGroupInvoiceRecord> tGroupInvoiceRecords = new ArrayList<>();
        assembleGroupInvoiceDetail(tContractorInvoiceList, tGroupInvoiceRecords, null, tGroupInvoice);

        if (!tGroupInvoiceRecords.isEmpty()) {
            groupInvoiceRepository.save(tGroupInvoice);
            log.info("group invoice create,save group invoice");

            tGroupInvoiceRecords.forEach(t -> {
                t.setGroupInvoiceId(tGroupInvoice.getId());
            });
            groupInvoiceRecordRepository.saveAll(tGroupInvoiceRecords);
            log.info("group invoice create,save group invoice record,groupInvoiceId:{}", tGroupInvoice.getId());

            Set<Long> invoiceIdList = tContractorInvoiceList.stream().map(TContractorInvoice::getId).map(i -> i.longValue()).collect(Collectors.toSet());
            updateInvoiceStatusAndGroupInvoiceNumber(invoiceIdList, tGroupInvoice);

            //send to rabbit
            rabbitTemplate.convertAndSend(queueConfig.getExchange(), queueConfig.getRoutingKey(), tGroupInvoice.getId() + "&" + dto.getTimezone());
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                applicationService.createInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(tContractorInvoiceList.stream()
                        .map(TContractorInvoice::getId).map(BigInteger::longValue).collect(Collectors.toList())).build());
            });
            log.info("group invoice send to mq,id:{},exchange:{},routeKey:{}", tGroupInvoice.getId(), queueConfig.getExchange(), queueConfig.getRoutingKey());
            return 1;
        } else {
            log.error("group invoice create,assembly group invoice record  data error,{}", JSON.toJSONString(dto));
        }
        return 0;
    }

    /**
     * update invoice status and group invoice number by id
     *
     * @param invoiceIdList invoice ids
     * @param tGroupInvoice
     */
    private void updateInvoiceStatusAndGroupInvoiceNumber(Set<Long> invoiceIdList, TGroupInvoice tGroupInvoice) {
        //修改invoice status
        contractorInvoiceRepository.updateInvoiceStatusAndGroupInvoiceNumberByIds(invoiceIdList, InvoiceStatusType.GROUPED.toDbValue(), tGroupInvoice.getGroupNumber());
        log.info("group invoice create,update invoice status is grouped,invoice ids :{}", JSON.toJSONString(invoiceIdList));
    }

    /**
     * set group invoice amount
     * assemble group invoice record
     *
     * @param tContractorInvoiceList
     * @param tGroupInvoiceRecords    group type = company use
     * @param mapTGroupInvoiceRecords group type = employee use
     * @param tGroupInvoice
     */
    private void assembleGroupInvoiceDetail(List<TContractorInvoice> tContractorInvoiceList,
                                            List<TGroupInvoiceRecord> tGroupInvoiceRecords,
                                            Map<String, List<TGroupInvoiceRecord>> mapTGroupInvoiceRecords,
                                            TGroupInvoice tGroupInvoice) {
        BigDecimal total = new BigDecimal(0);
        for (TContractorInvoice t : tContractorInvoiceList) {
            TGroupInvoiceRecord bean = new TGroupInvoiceRecord();
            bean.setInvoiceId(t.getId());
            bean.setInternalInvoiceType(t.getInvoiceType());
            bean.setTalentId(t.getTalentId());
            bean.setStatus(1);
            tGroupInvoiceRecords.add(bean);
            total = total.add(t.getTotalAmount() == null ? BigDecimal.ZERO : t.getTotalAmount());
        }

        tGroupInvoice.setInvoiceAmount(total);
        tGroupInvoice.setAmountDue(total);

        //存入map 批量插入group invoice时 进行分别id赋值
        if (mapTGroupInvoiceRecords != null) {
            mapTGroupInvoiceRecords.put(tGroupInvoice.getGroupNumber() + tGroupInvoice.getTenantId() + tGroupInvoice.getCompanyId(), tGroupInvoiceRecords);
        }
    }

    /**
     * modify group invoice
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void modify(ContractorGroupInvoiceEditDTO dto) {
        TGroupInvoice tGroupInvoice = groupInvoiceRepository.findByIdAndStatusAndTenantId(dto.getId(), 1, BigInteger.valueOf(SecurityUtils.getTenantId()));
        if (null != tGroupInvoice) {
            if (tGroupInvoice.getGroupInvoiceStatus().equals(InvoiceStatusType.VOID)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_NOT_MODIFY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        LocalDate oldInvoiceDate = tGroupInvoice.getInvoiceDate();

        tGroupInvoice.setInvoiceDate(LocalDate.parse(dto.getInvoiceDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        tGroupInvoice.setPaymentDueDate(DateUtil.getDayAddition(Timestamp.valueOf(tGroupInvoice.getInvoiceDate().atStartOfDay()), tGroupInvoice.getDueWithinDays()));
        groupInvoiceRepository.saveAndFlush(tGroupInvoice);
        log.info("group invoice:contractor group invoice modify , update group invoice by id:{},param:{}", dto.getId(), JSON.toJSONString(dto));

        //send to rabbit
        rabbitTemplate.convertAndSend(queueConfig.getExchange(), queueConfig.getRoutingKey(), tGroupInvoice.getId() + "&" + dto.getTimezone());
        if (!Objects.equals(oldInvoiceDate, LocalDate.parse(dto.getInvoiceDate()))) {
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                List<TGroupInvoiceRecord> recordList = groupInvoiceRecordRepository.findTGroupInvoiceRecordByGroupInvoiceIdAndStatus(CollUtil.newArrayList(dto.getId()));
                if (CollUtil.isNotEmpty(recordList)) {
                    applicationService.updateInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(recordList.stream()
                            .map(TGroupInvoiceRecord::getInvoiceId).map(BigInteger::longValue).collect(Collectors.toList())).build());
                }
            });
        }
        log.info("group invoice: send to rabbit,group invoice id:{}", tGroupInvoice.getId());
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            Timestamp paymentDueDate = tGroupInvoice.getPaymentDueDate();
            Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());
            if (paymentDueDate.after(currentTimestamp)) {
                notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(tGroupInvoice.getId().longValue(), InvoiceTypeEnum.T_GROUP_INVOICE), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
            }
        });
    }

    /**
     * view group invoice detail
     *
     * @param id
     * @return
     */
    @Override
    public ContractorGroupInvoiceViewVO view(BigInteger id) {
        Optional<TGroupInvoice> tGroupInvoice = Optional.ofNullable(groupInvoiceRepository.findByIdAndStatus(id, 1));
        if (!tGroupInvoice.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        ContractorGroupInvoiceViewVO viewVO = new ContractorGroupInvoiceViewVO().transformToVo(tGroupInvoice.get());
        viewVO.setCompanyContact(contractorGroupInvoiceNativeRepository.selectCompanyContactByGroupInvoiceId(id));

        Optional<EnumCurrency> enumCurrency = enumCurrencyRepository.findById(tGroupInvoice.get().getCurrencyId());
        if (enumCurrency.isPresent()) {
            viewVO.setCurrency(enumCurrency.get().getName());
        }
        //query the associated data between invoice and group invoice
        List<ContractorGroupInvoiceRecordVO> recordVOList = contractorGroupInvoiceNativeRepository.selectGroupInvoiceTimesheetRecord(id, true);
        if (!recordVOList.isEmpty()) {
            viewVO.setAssignmentId(recordVOList.get(0).getAssignmentId());
            viewVO.setClientAddress(recordVOList.get(0).getClientAddress());
            viewVO.setClientName(recordVOList.get(0).getClientName());
            viewVO.setClientLocation(recordVOList.get(0).getClientLocation());
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            viewVO.setWeekEndingDate(LocalDate.parse(recordVOList.get(0).getWeekEndingDate(), fmt));
            if (recordVOList.get(0).getFrequency().equals(TimeSheetFrequencyType.WEEKLY.toDbValue().toString()) || recordVOList.get(0).getFrequency().equals(TimeSheetFrequencyType.BI_WEEKLY.toDbValue().toString())) {
                timesheetFrequencyTypeByWeekly(recordVOList, viewVO);
            } else if (recordVOList.get(0).getFrequency().equals(TimeSheetFrequencyType.MONTHLY.toDbValue().toString())) {
                recordVOList = contractorGroupInvoiceNativeRepository.selectGroupInvoiceTimesheetRecord(id, false);
                timesheetFrequencyTypeByMonthly(recordVOList, viewVO);
            } else if (recordVOList.get(0).getFrequency().equals(TimeSheetFrequencyType.SEMI_MONTHLY.toDbValue().toString())) {
                recordVOList = contractorGroupInvoiceNativeRepository.selectGroupInvoiceTimesheetRecord(id, false);
                timesheetFrequencyTypeBySemiMonthly(recordVOList, viewVO);
            } else if (recordVOList.get(0).getFrequency().equals(TimeSheetFrequencyType.QUARTERLY.toDbValue().toString())) {
                recordVOList = contractorGroupInvoiceNativeRepository.selectGroupInvoiceTimesheetRecord(id, false);
                timesheetFrequencyTypeByQuarterly(recordVOList, viewVO);
            }
        }

        //search payment info
        List<TRecordPaymentInfo> recordPaymentInfos = recordPaymentRepository.findByGroupInvoiceIdAndStatus(viewVO.getId(), 1);
        viewVO.setPaymentRecordList(viewVO.transformToPaymentVO(recordPaymentInfos));
        return viewVO;
    }

    /**
     * quarterly
     *
     * @param recordVOList
     * @param viewVO
     */
    private void timesheetFrequencyTypeByQuarterly(List<ContractorGroupInvoiceRecordVO> recordVOList, ContractorGroupInvoiceViewVO viewVO) {
        List detailVO = new ArrayList();
        TreeMap<LocalDate, LocalDate> monthly = new TreeMap<>();
        recordVOList.forEach(v -> {
            String yearAndMonth = DateUtil.currentYearAndMonth(v.getWeekEndingDate());
            Pair<LocalDate, LocalDate> quarterlyDate = getQuarterlyDate(yearAndMonth);
            if (!monthly.containsKey(quarterlyDate.getKey())) {
                monthly.put(quarterlyDate.getKey(), quarterlyDate.getValue());
            }
        });
        for (Map.Entry<LocalDate, LocalDate> entry : monthly.entrySet()) {

            List<ContractorGroupInvoiceDetailVO> groupDetailVO = new ArrayList<>();
            searchRecordData(recordVOList, entry.getKey(), entry.getValue(), groupDetailVO);

            if (!groupDetailVO.isEmpty()) {
                detailVO.add(groupDetailVO);
            }
        }

        if (!detailVO.isEmpty()) {
            viewVO.setInvoiceList(detailVO);
        }
    }

    private Pair<LocalDate, LocalDate> getQuarterlyDate(String date) {
        String year = date.split("-")[0];
        Integer month = Integer.valueOf(date.split("-")[1]);
        String firstMonth = "01";
        String lastMonth = date.split("-")[1];
        if (month <= 3) {
            lastMonth = "03";
        } else if (month > 3 && month <= 6) {
            firstMonth = "04";
            lastMonth = "06";
        } else if (month > 6 && month <= 9) {
            firstMonth = "07";
            lastMonth = "09";
        } else {
            firstMonth = "10";
            lastMonth = "12";
        }
        LocalDate firstDay = DateUtil.stringToLocalDate(year + "-" + firstMonth + "-01");
        LocalDate lastDay = DateUtil.getLastDayOfCurrentMonth(year + "-" + lastMonth + "-01");
        return new Pair<>(firstDay, lastDay);
    }


    /**
     * semi-monthly
     *
     * @param recordVOList
     * @param viewVO
     */
    private void timesheetFrequencyTypeBySemiMonthly(List<ContractorGroupInvoiceRecordVO> recordVOList, ContractorGroupInvoiceViewVO viewVO) {
        List detailVO = new ArrayList();
        TreeSet<String> monthlySet = new TreeSet<>();
        recordVOList.forEach(v -> {
            monthlySet.add(DateUtil.currentYearAndMonth(v.getWeekEndingDate()));
        });
        for (String date : monthlySet) {
            LocalDate firstDay = DateUtil.getFirstDayOfCurrentMonth(date + "-01");
            LocalDate lastDay = DateUtil.stringToLocalDate(date + "-15");

            List<ContractorGroupInvoiceDetailVO> groupDetailVO = new ArrayList<>();
            searchRecordData(recordVOList, firstDay, lastDay, groupDetailVO);
            if (!groupDetailVO.isEmpty()) {
                detailVO.add(groupDetailVO);
            }
            groupDetailVO = new ArrayList<>();
            firstDay = DateUtil.stringToLocalDate(date + "-16");
            lastDay = DateUtil.getLastDayOfCurrentMonth(date + "-01");
            searchRecordData(recordVOList, firstDay, lastDay, groupDetailVO);

            if (!groupDetailVO.isEmpty()) {
                detailVO.add(groupDetailVO);
            }
        }

        if (!detailVO.isEmpty()) {
            viewVO.setInvoiceList(detailVO);
        }
    }


    /**
     * 循环处理record 记录
     *
     * @param recordVOList
     * @param firstDay
     * @param lastDay
     * @param groupDetailVO
     */
    private void searchRecordData(List<ContractorGroupInvoiceRecordVO> recordVOList, LocalDate firstDay, LocalDate lastDay, List<ContractorGroupInvoiceDetailVO> groupDetailVO) {
        int i = 0;
        LinkedHashMap<String, ContractorGroupInvoiceDetailVO> detailVOMap = new LinkedHashMap<>();
        for (ContractorGroupInvoiceRecordVO vo : recordVOList) {
            LocalDate weekEndingDate = DateUtil.stringToLocalDate(vo.getWeekEndingDate());
            if ((firstDay.isBefore(weekEndingDate) || firstDay.isEqual(weekEndingDate)) && (lastDay.isAfter(weekEndingDate) || lastDay.isEqual(weekEndingDate))) {
                i = operateDate(vo, i, firstDay, lastDay, groupDetailVO, detailVOMap, weekEndingDate.getYear() + "" + weekEndingDate.getMonthValue());
            }
        }

        if (!detailVOMap.isEmpty()) {
            groupDetailVO.clear();
            for (Map.Entry<String, ContractorGroupInvoiceDetailVO> entry : detailVOMap.entrySet()) {
                groupDetailVO.add(entry.getValue());
            }
        }
    }

    /**
     * monthly
     *
     * @param recordVOList
     * @param viewVO
     */
    private void timesheetFrequencyTypeByMonthly(List<ContractorGroupInvoiceRecordVO> recordVOList, ContractorGroupInvoiceViewVO viewVO) {
        List detailVO = new ArrayList();
        TreeSet<String> monthlySet = new TreeSet<>();
        recordVOList.forEach(v -> {
            monthlySet.add(DateUtil.currentYearAndMonth(v.getWeekEndingDate()));
        });
        for (String date : monthlySet) {
            LocalDate firstDay = DateUtil.getFirstDayOfCurrentMonth(date + "-01");
            LocalDate lastDay = DateUtil.getLastDayOfCurrentMonth(date + "-01");

            List<ContractorGroupInvoiceDetailVO> groupDetailVO = new ArrayList<>();
            searchRecordData(recordVOList, firstDay, lastDay, groupDetailVO);
            if (!groupDetailVO.isEmpty()) {
                detailVO.add(groupDetailVO);
            }
        }

        if (!detailVO.isEmpty()) {
            viewVO.setInvoiceList(detailVO);
        }
    }

    /**
     * 组装明细数据
     *
     * @param vo
     * @param i
     * @param firstDay
     * @param lastDay
     * @param groupDetailVO
     */
    private int operateDate(ContractorGroupInvoiceRecordVO vo, int i, LocalDate firstDay, LocalDate lastDay, List<ContractorGroupInvoiceDetailVO> groupDetailVO, LinkedHashMap<String, ContractorGroupInvoiceDetailVO> detailVoMap, String yearMonth) {
        if (vo.getInvoiceType().equals("2")) {
            if (null == vo.getTotalAmount() || vo.getTotalAmount().compareTo(BigDecimal.ZERO) == 0) {
                return i;
            }
        }
        //expense
        if (vo.getInvoiceType().equals(InvoiceType.EXPENSE.toDbValue().toString())) {
            List<ContractorInvoiceExpenseVO> expenseVOList = contractorInvoiceNativeRepository.findExpenseAllByInvoiceId(vo.getInvoiceId());
            if (!expenseVOList.isEmpty()) {
                for (ContractorInvoiceExpenseVO expenseVO : expenseVOList) {
                    if (expenseVO.getExpenseMoney().compareTo(BigDecimal.valueOf(0l)) == 1) {
                        if (i == 0) {
                            ContractorGroupInvoiceDetailVO bean = createViewVO(vo, i, firstDay, lastDay);
                            bean.setItemDescription(vo.getTalentName());
                            bean.setCurrency(expenseVO.getCurrency());
                            bean.setInvoiceType(InvoiceType.EXPENSE.toDbValue().toString());
                            groupDetailVO.add(bean);
                            i++;
                            if (null != detailVoMap) {
                                detailVoMap.put("" + i, bean);
                            }
                            i = addExpenseByView(vo, expenseVO, i, firstDay, lastDay, groupDetailVO, detailVoMap);
                        } else {
                            i = addExpenseByView(vo, expenseVO, i, firstDay, lastDay, groupDetailVO, detailVoMap);
                        }
                    }
                }
            }
        } else {
            ContractorGroupInvoiceDetailVO bean = createViewVO(vo, i, firstDay, lastDay);
            groupDetailVO.add(bean);
            if (null != detailVoMap) {
                if (detailVoMap.containsKey(yearMonth + vo.getTalentId() + vo.getItemDescription() + "" + vo.getBillRate())) {
                    ContractorGroupInvoiceDetailVO value = detailVoMap.get(yearMonth + vo.getTalentId() + vo.getItemDescription() + "" + vo.getBillRate());
                    if (value.getQuantity() != null) {
                        value.setQuantity(value.getQuantity().add(vo.getQuantity() == null ? BigDecimal.ZERO : vo.getQuantity()));
                    } else {
                        value.setQuantity(vo.getQuantity() == null ? BigDecimal.ZERO : vo.getQuantity());
                    }
                    value.setTotalAmount(value.getTotalAmount().add(vo.getTotalAmount() == null ? BigDecimal.ZERO : vo.getTotalAmount()));
                } else {
                    detailVoMap.put(yearMonth + vo.getTalentId() + vo.getItemDescription() + "" + vo.getBillRate(), bean);
                }
            }
        }
        i++;
        return i;
    }

    private Integer addExpenseByView(ContractorGroupInvoiceRecordVO vo, ContractorInvoiceExpenseVO expenseVO, int i, LocalDate firstDay, LocalDate lastDay, List<ContractorGroupInvoiceDetailVO> groupDetailVO, LinkedHashMap<String, ContractorGroupInvoiceDetailVO> detailVoMap) {
        ContractorGroupInvoiceDetailVO bean = createViewVO(vo, i, firstDay, lastDay);
        bean.setTotalAmount(expenseVO.getExpenseMoney());
        bean.setItemDescription(expenseVO.getExpenseCategory());
        if (i != 0) {
            bean.setTimePeriod(expenseVO.getExpenseDate());
        }
        bean.setCurrency(expenseVO.getCurrency());
        bean.setInvoiceType(InvoiceType.EXPENSE.toDbValue().toString());
        groupDetailVO.add(bean);
        i++;
        if (null != detailVoMap) {
            detailVoMap.put("" + i, bean);
        }
        return i;
    }

    /**
     * Weekly bi-Weekly
     *
     * @param recordVOList
     * @param viewVO
     */
    private void timesheetFrequencyTypeByWeekly(List<ContractorGroupInvoiceRecordVO> recordVOList, ContractorGroupInvoiceViewVO viewVO) {
        List detailVO = new ArrayList();
        //用于区分分组
        String weekEnding = "";
        List<ContractorGroupInvoiceDetailVO> groupDetailVO = new ArrayList<>();
        int i = 0;
        for (ContractorGroupInvoiceRecordVO vo : recordVOList) {
            //重置并加入到list
            if (!weekEnding.equals(vo.getWeekEndingDate() + vo.getTalentId())) {
                weekEnding = vo.getWeekEndingDate() + vo.getTalentId();
                if (!groupDetailVO.isEmpty()) {
                    detailVO.add(groupDetailVO);
                }
                groupDetailVO = new ArrayList<>();
                i = 0;
            }
            operateDate(vo, i, null, null, groupDetailVO, null, null);
            i++;
        }

        //循环最后一次时，加入到list
        if (!groupDetailVO.isEmpty()) {
            detailVO.add(groupDetailVO);
        }

        if (!detailVO.isEmpty()) {
            viewVO.setInvoiceList(detailVO);
        }
    }

    /**
     * create ContractorGroupInvoiceDetailVO
     *
     * @param vo
     * @param i
     * @return
     */
    private ContractorGroupInvoiceDetailVO createViewVO(ContractorGroupInvoiceRecordVO vo, int i, LocalDate from, LocalDate to) {

        ContractorGroupInvoiceDetailVO bean = new ContractorGroupInvoiceDetailVO();
        bean.setCurrency(StringUtils.isNotBlank(vo.getCurrency()) ? vo.getCurrency() : "");
        bean.setQuantity(vo.getQuantity());
        bean.setTotalAmount(null == vo.getTotalAmount() ? new BigDecimal("0") : vo.getTotalAmount());
        bean.setUnit(StringUtils.isNotBlank(vo.getUnit()) ? "Hour" : "");
        bean.setItemDescription(StringUtils.isNotBlank(vo.getItemDescription()) ? vo.getItemDescription() : "");
        bean.setBillRate(null != vo.getBillRate() ? vo.getBillRate().toString() : "");
        //assignment monthly 类型 pdf使用
        bean.setPoNumber(vo.getPoNumber());
        if (i == 0) {
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("MM/dd/yyyy");
            if (from != null) {
                bean.setTimePeriod("From " + fmt.format(from) + " To " + fmt.format(to));
                //assignment monthly 类型 pdf使用
                bean.setFrom("From " + fmt.format(from));
                bean.setTo("To " + fmt.format(to));
            } else {
                LocalDate endDate = LocalDate.parse(vo.getWeekEndingDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalDate startDate = endDate.minusDays(6l);
                bean.setTimePeriod("From " + fmt.format(startDate) + " To " + fmt.format(endDate));
                //assignment monthly 类型 pdf使用
                bean.setFrom("From " + fmt.format(startDate));
                bean.setTo("To " + fmt.format(endDate));
            }
        }

        return bean;
    }

    /**
     * add record payment
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addRecordPayment(RecordPaymentCreateDTO dto) {

        Optional<TGroupInvoice> tGroupInvoice = groupInvoiceRepository.findById(dto.getGroupInvoiceId());
        if (!tGroupInvoice.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        //判断amountDue是否是0或者状态是paid
        TGroupInvoice bean = tGroupInvoice.get();
        if (bean.getAmountDue().compareTo(new BigDecimal("0.00")) == 0 ||
                bean.getGroupInvoiceStatus().equals(GroupInvoiceStatus.PAID)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_ADDRECORDPAYMENT_STATUSISPAID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        if (bean.getGroupInvoiceStatus().equals(GroupInvoiceStatus.VOID) || bean.getGroupInvoiceStatus().equals(GroupInvoiceStatus.INVOICED)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_ADDRECORDPAYMENT_STATUSISVOIDORINVOICED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        //判断paymentAmount 是否大于amountDue
        if (bean.getAmountDue().compareTo(dto.getPaymentAmount()) == -1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_ADDRECORDPAYMENT_AMOUNTTHENDUE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        TRecordPaymentInfo tRecordPaymentInfo = new TRecordPaymentInfo();
        tRecordPaymentInfo.setGroupInvoiceId(dto.getGroupInvoiceId());
        tRecordPaymentInfo.setPaymentDate(dto.getPaymentDate());
        tRecordPaymentInfo.setPaymentAmount(dto.getPaymentAmount());
        tRecordPaymentInfo.setStatus(1);
        tRecordPaymentInfo.setNote(dto.getNote());
        tRecordPaymentInfo.setPaymentMethod(dto.getPaymentMethod());
        tRecordPaymentInfo.setGroupInvoiceNumber(bean.getGroupNumber());
        log.info("group invoice: add record payment,instance :{}", JSON.toJSONString(tRecordPaymentInfo));

        recordPaymentRepository.save(tRecordPaymentInfo);
        log.info("group invoice: add record payment,save payment param:{} ", JSON.toJSONString(tRecordPaymentInfo));

        //group invoice update
        bean.setAmountDue(bean.getAmountDue().subtract(dto.getPaymentAmount()));
        setGroupStatus(bean);
        groupInvoiceRepository.save(bean);
        log.info("group invoice: add record payment,update group invoice param:{} ", JSON.toJSONString(bean));

        savePaymentDetailRecord(bean, tRecordPaymentInfo);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            if(GroupInvoiceStatus.PAID.equals(bean.getGroupInvoiceStatus())) {
                SecurityContextHolder.setContext(context);
                notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(dto.getGroupInvoiceId().longValue(), InvoiceTypeEnum.T_GROUP_INVOICE), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
            }
        });
    }

    @Resource
    private CalendarService calendarService;

    private void notifyCompleteSystemCalendar(Long relateId, CalendarRelationEnum calendarRelation, List<CalendarTypeEnum> type) {
        if(relateId == null) {
            return;
        }
        CompleteSystemCalendarDTO dto = new CompleteSystemCalendarDTO();
        dto.setRelationType(calendarRelation);
        dto.setType(type);
        dto.setUniqueReferenceId(relateId);
        calendarService.completeSystemCalendar(dto);
    }

    private void setGroupStatus(TGroupInvoice bean) {
        if (bean.getAmountDue().compareTo(new BigDecimal("0.00")) == 0) {
            bean.setGroupInvoiceStatus(GroupInvoiceStatus.PAID);
        } else {
            //paymentDueDate
            if (bean.getPaymentDueDate().after(new Timestamp(new Date().getTime()))) {
                bean.setGroupInvoiceStatus(GroupInvoiceStatus.PARTIALLY_PAID);
            } else {
                bean.setGroupInvoiceStatus(GroupInvoiceStatus.OVERDUE);
            }

        }
        if (GroupInvoiceStatus.PAID == bean.getGroupInvoiceStatus()) {
            List<TGroupInvoiceRecord> recordList = groupInvoiceRecordRepository.findTGroupInvoiceRecordByGroupInvoiceIdAndStatus(CollUtil.newArrayList(bean.getId()));
            if (CollUtil.isNotEmpty(recordList)) {
                applicationService.deleteInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(recordList.stream()
                        .map(TGroupInvoiceRecord::getInvoiceId).map(BigInteger::longValue).collect(Collectors.toList())).build());
            }
        }
    }

    //添加支付明细
    private void savePaymentDetailRecord(TGroupInvoice bean, TRecordPaymentInfo tRecordPaymentInfo) {

        List<InvoiceRecordPaymentDetail> paymentDetails = invoiceRecordPaymentDetailRepository.findByGroupIdAndStatus(bean.getId().longValue(), 1);
        Map<Long, BigDecimal> talentPaymentMap = paymentDetails.stream()
                .collect(Collectors.groupingBy(
                        InvoiceRecordPaymentDetail::getInvoiceId, // 按userId分组
                        Collectors.reducing(BigDecimal.ZERO, InvoiceRecordPaymentDetail::getPaymentAmount, BigDecimal::add)));

        //查询invoice detail
        List<TContractorInvoice> recordList = contractorInvoiceRepository.queryInvoiceByGroupInvoiceId(Arrays.asList(bean.getId()));

        if (recordList.size() == 1) {
            InvoiceRecordPaymentDetail paymentDetail = paymentDetail(tRecordPaymentInfo.getPaymentAmount(), tRecordPaymentInfo, bean, recordList.get(0), null);
            invoiceRecordPaymentDetailRepository.save(paymentDetail);
            log.info("group invoice: add record payment detail param:{} ", JSON.toJSONString(paymentDetail));
        } else {
            List<InvoiceRecordPaymentDetail> detailList = new ArrayList<>();
            BigDecimal payment = tRecordPaymentInfo.getPaymentAmount();
            for (int i = 0; i < recordList.size(); i++) {
                TContractorInvoice invoice = recordList.get(i);
                if (invoice.getTotalAmount().compareTo(new BigDecimal("0.00")) == 0) {
                    continue;
                }
                if (payment.compareTo(new BigDecimal("0.00")) == 0) {
                    break;
                }
                BigDecimal amountPaid = talentPaymentMap.get(invoice.getId().longValue()) == null ? BigDecimal.ZERO : talentPaymentMap.get(invoice.getId().longValue());

                //未支付金额
                BigDecimal notPay = invoice.getTotalAmount().subtract(amountPaid);
                if (notPay.compareTo(new BigDecimal("0.00")) == 0) {
                    continue;
                }

                if (payment.compareTo(notPay) >= 0) {
                    payment = payment.subtract(notPay);
                    InvoiceRecordPaymentDetail paymentDetail = paymentDetail(notPay, tRecordPaymentInfo, bean, invoice, null);
                    detailList.add(paymentDetail);
                } else {
                    InvoiceRecordPaymentDetail paymentDetail = paymentDetail(payment, tRecordPaymentInfo, bean, invoice, null);
                    detailList.add(paymentDetail);
                    payment = BigDecimal.ZERO;
                }
            }

            if (!detailList.isEmpty()) {
                invoiceRecordPaymentDetailRepository.saveAll(detailList);
                log.info("group invoice: add record payment detail param size:{} ", detailList.size());
            }
        }
    }

    private InvoiceRecordPaymentDetail paymentDetail(BigDecimal paymentAmount, TRecordPaymentInfo tRecordPaymentInfo,
                                                     TGroupInvoice bean, TContractorInvoice invoice,
                                                     ContractorInvoiceRatioVO vo) {
        InvoiceRecordPaymentDetail paymentDetail = new InvoiceRecordPaymentDetail();
        paymentDetail.setGroupId(bean.getId().longValue());
        paymentDetail.setPaymentAmount(paymentAmount);
        paymentDetail.setPaymentId(tRecordPaymentInfo.getId().longValue());
        paymentDetail.setGpAmount(bean.getInvoiceAmount());
        paymentDetail.setStatus(1);
        paymentDetail.setPaymentDate(tRecordPaymentInfo.getPaymentDate());
        if (null != invoice) {
            paymentDetail.setInvoiceId(invoice.getId().longValue());
            paymentDetail.setTalentId(invoice.getTalentId().longValue());
            paymentDetail.setTalentName(invoice.getTalentName());
        } else {
            paymentDetail.setInvoiceId(vo.getId().longValue());
            paymentDetail.setTalentId(vo.getTalentId().longValue());
            paymentDetail.setTalentName(vo.getTalentName());
        }
        return paymentDetail;
    }

    /**
     * record purchase order payment start
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPurchaseOrderRecordPayment(PurchaseOrderRecordPaymentCreateDTO dto) {

        //查询group Invoice currency
        List<TGroupInvoice> groupInvoiceList = groupInvoiceRepository.queryIdByIds(dto.getGroupInvoiceIdList());
        if (groupInvoiceList.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        Set<Long> companyIdSet = groupInvoiceList.stream().map(TGroupInvoice::getCompanyId).map(a -> a.longValue()).collect(Collectors.toSet());
        if (companyIdSet.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_PURCHASE_ORDER_CONTAIN_DIFFERENT_COMPANY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }


        //check group invoice currency
        Set<Integer> currencyIdList = groupInvoiceList.stream().map(TGroupInvoice::getCurrencyId).collect(Collectors.toSet());
        if (null == currencyIdList || currencyIdList.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_PURCHASE_ORDER_INVOICE_MULTIPLE_CURRENCIES.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        Set<Integer> currencyIdSet = dto.getPurchaseOrderList().stream().map(PurchaseOrderInfoDTO::getCurrencyId).collect(Collectors.toSet());
        if (currencyIdSet.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_PURCHASE_ORDER_MULTIPLE_CURRENCIES.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        if (!currencyIdSet.contains(currencyIdList.stream().findFirst().get())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_PURCHASE_ORDER_CONTAIN_DIFFERENT_CURRENCIES.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        //查询purchase order
        List<Long> orderDetailIdList = dto.getPurchaseOrderList().stream().map(PurchaseOrderInfoDTO::getPurchaseOrderId).collect(Collectors.toList());
        List<CompanyPurchaseOrderDetail> purchaseOrderDetailList = companyPurchaseOrderDetailRepository.findAllById(orderDetailIdList);
        Map<Long, List<CompanyPurchaseOrderDetail>> orderDetailMap = purchaseOrderDetailList.stream().collect(Collectors.groupingBy(CompanyPurchaseOrderDetail::getPurchaseOrderId));

        //判断是不是同一个公司
        List<Long> orderIdList = purchaseOrderDetailList.stream().map(CompanyPurchaseOrderDetail::getPurchaseOrderId).collect(Collectors.toList());
        List<CompanyPurchaseOrder> companyPurchaseOrderList = companyPurchaseOrderRepository.findByIdIn(orderIdList);
        Set<Long> purchaseOrderCompanyIdSet = companyPurchaseOrderList.stream().map(CompanyPurchaseOrder::getCompanyId).collect(Collectors.toSet());
        if (purchaseOrderCompanyIdSet.size() > 1 || !companyIdSet.contains(purchaseOrderCompanyIdSet.stream().findFirst().get())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_PURCHASE_ORDER_CONTAIN_DIFFERENT_COMPANY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        List<TRecordPaymentInfo> recordList = new ArrayList<>();
        List<PurchaseOrderNotifyDTO> notifyDTOList = new ArrayList<>();
        Set<Long> notifyCompanyId = new HashSet<>();

        BigDecimal paymentAmount = dto.getPaymentAmount();
        //循环扣金额
        for (TGroupInvoice bean : groupInvoiceList) {

            if (bean.getAmountDue().compareTo(new BigDecimal("0.00")) == 0 ||
                    bean.getGroupInvoiceStatus().equals(GroupInvoiceStatus.PAID)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_ADDRECORDPAYMENT_STATUSISPAID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }

            if (bean.getGroupInvoiceStatus().equals(GroupInvoiceStatus.VOID) || bean.getGroupInvoiceStatus().equals(GroupInvoiceStatus.INVOICED)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_ADDRECORDPAYMENT_STATUSISVOIDORINVOICED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }

            if (paymentAmount.compareTo(new BigDecimal("0.00")) == 0) {
                break;
            }

            //待扣除金额
            BigDecimal subtractAmount = BigDecimal.ZERO;

            BigDecimal x = bean.getAmountDue().subtract(paymentAmount);
            if (x.compareTo(new BigDecimal("0.00")) == 0) {
                bean.setAmountDue(BigDecimal.ZERO);
                subtractAmount = paymentAmount;
                paymentAmount = x;
            } else if (x.compareTo(new BigDecimal("0.00")) == 1) {
                bean.setAmountDue(x);
                subtractAmount = paymentAmount;
                paymentAmount = BigDecimal.ZERO;
            } else if (x.compareTo(new BigDecimal("0.00")) == -1) {
                subtractAmount = bean.getAmountDue();
                paymentAmount = paymentAmount.subtract(bean.getAmountDue());
                bean.setAmountDue(BigDecimal.ZERO);
            }

            for (Long orderId : orderDetailMap.keySet()) {
                if (subtractAmount.compareTo(new BigDecimal("0.00")) == 0) {
                    break;
                }
                List<CompanyPurchaseOrderDetail> orderDetails = orderDetailMap.get(orderId);
                for (CompanyPurchaseOrderDetail d : orderDetails) {
                    if (d.getBalance().compareTo(new BigDecimal("0.00")) == 0) {
                        continue;
                    }
                    if (subtractAmount.compareTo(new BigDecimal("0.00")) == 0) {
                        break;
                    }
                    BigDecimal v = d.getBalance().subtract(subtractAmount);
                    if (v.compareTo(new BigDecimal("0.00")) == 0) {
                        d.setBalance(BigDecimal.ZERO);
                        addGroupPayRecord(bean, subtractAmount, recordList, d.getId());
                        subtractAmount = BigDecimal.ZERO;
                        break;
                    } else if (v.compareTo(new BigDecimal("0.00")) == 1) {
                        addGroupPayRecord(bean, subtractAmount, recordList, d.getId());
                        d.setBalance(v);
                        subtractAmount = BigDecimal.ZERO;
                    } else if (v.compareTo(new BigDecimal("0.00")) == -1) {
                        subtractAmount = subtractAmount.subtract(d.getBalance());
                        addGroupPayRecord(bean, d.getBalance(), recordList, d.getId());
                        d.setBalance(BigDecimal.ZERO);
                    }
                }
            }

            if (subtractAmount.compareTo(new BigDecimal("0.00")) == 1) {
                bean.setAmountDue(bean.getAmountDue().add(subtractAmount));
            }
            setGroupStatus(bean);
        }

        //更新groupInvoice
        groupInvoiceRepository.saveAllAndFlush(groupInvoiceList);
        log.info("[addPurchaseOrderRecordPayment] update group invoice param:{}", JSON.toJSONString(groupInvoiceList));

        //添加支付记录
        if (!recordList.isEmpty()) {
            recordPaymentRepository.saveAll(recordList);
            log.info("[addPurchaseOrderRecordPayment] add record payment param:{}", JSON.toJSONString(recordList));
        }

        recordList.forEach(v -> {
            groupInvoiceList.forEach(x -> {
                if (v.getGroupInvoiceId().equals(x.getId())) {
                    savePaymentDetailRecord(x, v);
                }
            });
        });

        //更新 purchase order
        if (!orderDetailMap.isEmpty()) {
            for (Long orderId : orderDetailMap.keySet()) {
                List<CompanyPurchaseOrderDetail> orderDetails = orderDetailMap.get(orderId);
                companyPurchaseOrderDetailRepository.saveAllAndFlush(orderDetails);
                log.info("[addPurchaseOrderRecordPayment] add record payment param:{}", JSON.toJSONString(orderDetails));

                CompanyPurchaseOrder order = companyPurchaseOrderRepository.findById(orderId).get();
                //是否到达通知限额
                BigDecimal totalBalance = order.getPurchaseOrderDetailList().stream()
                        .map(p -> p.getBalance())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                if (order.getNotificationBalance().compareTo(totalBalance) >= 0) {
                    if (!notifyCompanyId.contains(order.getCompanyId())) {
                        PurchaseOrderNotifyDTO notifyDTO = new PurchaseOrderNotifyDTO();
                        notifyDTO.setCompanyId(order.getCompanyId());
                        notifyDTO.setPurchaseOrderId(order.getId());
                        notifyDTO.setNotifyBalance(order.getNotificationBalance());
                        notifyDTO.setAm(order.getAm());
                        notifyDTOList.add(notifyDTO);
                        notifyCompanyId.add(order.getCompanyId());
                    }
                }
            }
        }


        //发送通知
        if (!notifyDTOList.isEmpty()) {
            sendPurchaseOrderNotify(notifyDTOList, InvoicePdfUtil.CURRENCY_STAIC_MAP.get(currencyIdList.stream().findFirst().get()));
        }
    }

    private void sendPurchaseOrderNotify(List<PurchaseOrderNotifyDTO> notifyDTOList, String currency) {
        List<Long> companyIds = notifyDTOList.stream().map(PurchaseOrderNotifyDTO::getCompanyId).collect(Collectors.toList());
        List<Long> amList = notifyDTOList.stream().map(PurchaseOrderNotifyDTO::getAm).collect(Collectors.toList());
        List<Map<String, Object>> companyInfo = contractorGroupInvoiceNativeRepository.selectCompanyInfo(companyIds, amList);
        log.info("[addPurchaseOrderRecordPayment] send company notify param:{}", JSON.toJSONString(companyInfo));
        if (companyInfo != null) {
            companyInfo.forEach((k) -> {
                String companyName = k.get("name").toString();
                String email = k.get("email").toString();
                BigInteger id = (BigInteger) k.get("id");
                String username = k.get("first_name").toString();
                for (PurchaseOrderNotifyDTO n : notifyDTOList) {
                    if (n.getCompanyId().equals(id.longValue())) {
                        //<EMAIL>
                        sendEmailNotify(companyName, email, username, n.getNotifyBalance(), id.longValue(), currency);
                    }
                }
            });
        }
    }

    private void sendEmailNotify(String companyName, String email, String name, BigDecimal balance, Long companyId, String currency) {
        StringBuilder sb = new StringBuilder();
        sb.append("<body>");
        HtmlUtil.appendParagraphCell(sb, "Hi " + name + ",");
        HtmlUtil.appendParagraphCell(sb, "The " + companyName + " has a remaining purchased balance less than " + currency + " " + balance.toString() + ". Please kindly remember to check with the client and update information if needed.");

        HtmlUtil.appendParagraphCell(sb, " Click the following link for more details:" + HTML_A_HREF_PREFIX + companyDetailUrl + companyId + "/1" + RIGHT_BRACKET + companyName + HTML_A_HREF_SUFFIX);

        HtmlUtil.appendParagraphCell(sb, " Best regards,");

        HtmlUtil.appendParagraphCell(sb, " APN Support Team");
        sb.append("</body>");
        MailVM mailVM = new MailVM();
        mailVM.setContent(sb.toString());
        mailVM.setSubject("Purchase Order Balance Notification");
        mailVM.setTo(Arrays.asList(email));
        mailVM.setFrom("<EMAIL>");
        mailVM.setIsSystemEmail(true);
        mailService.sendHtmlMail(mailVM);
        log.info("[addPurchaseOrderRecordPayment] send to Email service param:{}", JSON.toJSONString(mailVM));
    }

    private void addGroupPayRecord(TGroupInvoice bean, BigDecimal paymentAmount, List<TRecordPaymentInfo> paymentInfos, Long purchaseOrderId) {
        TRecordPaymentInfo tRecordPaymentInfo = new TRecordPaymentInfo();
        tRecordPaymentInfo.setGroupInvoiceId(bean.getId());
        tRecordPaymentInfo.setPaymentDate(new Timestamp(new Date().getTime()));
        tRecordPaymentInfo.setPaymentAmount(paymentAmount);
        tRecordPaymentInfo.setStatus(1);
        tRecordPaymentInfo.setNote("");
        tRecordPaymentInfo.setPaymentMethod(PaymentMethodType.PURCHASE_ORDER);
        tRecordPaymentInfo.setGroupInvoiceNumber(bean.getGroupNumber());
        tRecordPaymentInfo.setPurchaseOrderId(BigInteger.valueOf(purchaseOrderId));
        paymentInfos.add(tRecordPaymentInfo);
        log.info("[addPurchaseOrderRecordPayment] add record payment,instance :{}", JSON.toJSONString(tRecordPaymentInfo));

    }

    /**
     * bulk record payment
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void bulkRecordPayment(BulkRecordPaymentCreateDTO dto) {
        List<TGroupInvoice> groupInvoiceIds = groupInvoiceRepository.queryIdByIdsAndGroupInvoiceStatus(dto.getInvoiceIdList(),
                Arrays.asList(GroupInvoiceStatus.PARTIALLY_PAID.toDbValue(), GroupInvoiceStatus.UNPAID.toDbValue(), GroupInvoiceStatus.OVERDUE.toDbValue()));
        if (groupInvoiceIds.size() != dto.getInvoiceIdList().size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_BULKRECORDPAYMENT_STATUSERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        List<TRecordPaymentInfo> tRecordPaymentInfos = new ArrayList<>();
        for (TGroupInvoice t : groupInvoiceIds) {

            TRecordPaymentInfo bean = new TRecordPaymentInfo();
            bean.setPaymentMethod(dto.getPaymentMethod());
            bean.setGroupInvoiceId(t.getId());
            bean.setPaymentDate(dto.getPaymentDate());
            bean.setNote(dto.getNote());
            bean.setGroupInvoiceNumber(t.getGroupNumber());
            bean.setStatus(1);
            bean.setPaymentAmount(t.getAmountDue());
            tRecordPaymentInfos.add(bean);
        }

        //save TRecordPaymentInfo
        recordPaymentRepository.saveAll(tRecordPaymentInfos);
        log.info("group invoice: bulk record payment,save record payment");

        // update group invoice status and amount due =0
        groupInvoiceRepository.updateGroupInvoiceStatusAndAmountDueByIds(dto.getInvoiceIdList());
        List<TGroupInvoiceRecord> recordList = groupInvoiceRecordRepository.findTGroupInvoiceRecordByGroupInvoiceIdAndStatus(dto.getInvoiceIdList());
        if (CollUtil.isNotEmpty(recordList)) {
            applicationService.deleteInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(recordList.stream()
                    .map(TGroupInvoiceRecord::getInvoiceId).map(BigInteger::longValue).collect(Collectors.toList())).build());
        }
        log.info("group invoice: bulk record payment,update group invoice status is paid and amount due is 0,param:{}", JSON.toJSONString(dto));

        bulkSavePaymentDetailRecord(groupInvoiceIds, tRecordPaymentInfos);
    }

    //添加支付明细
    private void bulkSavePaymentDetailRecord(List<TGroupInvoice> groupInvoiceIds, List<TRecordPaymentInfo> tRecordPaymentInfos) {

        List<BigInteger> groupIdList = groupInvoiceIds.stream().map(TGroupInvoice::getId).collect(Collectors.toList());
        //查询invoice detail
        List<ContractorInvoiceRatioVO> contractorInvoiceRatioVOS = contractorInvoiceNativeRepository.findInvoiceByGroupInvoiceId(groupIdList);
        Map<BigInteger, List<ContractorInvoiceRatioVO>> mapInvoice = contractorInvoiceRatioVOS.stream().collect(Collectors.groupingBy(ContractorInvoiceRatioVO::getGroupInvoiceId));

        Map<BigInteger, TRecordPaymentInfo> mapPayment = tRecordPaymentInfos.stream().collect(Collectors.toMap(TRecordPaymentInfo::getGroupInvoiceId, p -> p));

        List<Long> groupIds = groupIdList.stream().map(v -> v.longValue()).collect(Collectors.toList());
        List<InvoiceRecordPaymentDetail> paymentDetails = invoiceRecordPaymentDetailRepository.findByGroupIdInAndStatus(groupIds, 1);
        Map<Long, List<InvoiceRecordPaymentDetail>> paymentDetailsMap = paymentDetails.stream().collect(Collectors.groupingBy(InvoiceRecordPaymentDetail::getGroupId));

        List<InvoiceRecordPaymentDetail> detailList = new ArrayList<>();
        for (TGroupInvoice bean : groupInvoiceIds) {
            List<ContractorInvoiceRatioVO> recordList = mapInvoice.get(bean.getId());
            TRecordPaymentInfo tRecordPaymentInfo = mapPayment.get(bean.getId());

            //已支付金额
            List<InvoiceRecordPaymentDetail> recordPaymentDetails = paymentDetailsMap.getOrDefault(bean.getId().longValue(),new ArrayList<>());
            Map<Long, BigDecimal> talentPaymentMap = recordPaymentDetails.stream()
                    .collect(Collectors.groupingBy(
                            InvoiceRecordPaymentDetail::getInvoiceId, // 按userId分组
                            Collectors.reducing(BigDecimal.ZERO, InvoiceRecordPaymentDetail::getPaymentAmount, BigDecimal::add)));

            if (recordList.size() == 1) {
                InvoiceRecordPaymentDetail paymentDetail = paymentDetail(tRecordPaymentInfo.getPaymentAmount(), tRecordPaymentInfo, bean, null, recordList.get(0));
                detailList.add(paymentDetail);
            } else {
                BigDecimal payment = tRecordPaymentInfo.getPaymentAmount();
                for (int i = 0; i < recordList.size(); i++) {
                    ContractorInvoiceRatioVO invoice = recordList.get(i);
                    if (invoice.getTotalAmount().compareTo(new BigDecimal("0.00")) == 0) {
                        continue;
                    }
                    if (payment.compareTo(new BigDecimal("0.00")) == 0) {
                        break;
                    }
                    BigDecimal amountPaid = talentPaymentMap.get(invoice.getId().longValue()) == null ? BigDecimal.ZERO : talentPaymentMap.get(invoice.getId().longValue());

                    //未支付金额
                    BigDecimal notPay = invoice.getTotalAmount().subtract(amountPaid);
                    if (notPay.compareTo(new BigDecimal("0.00")) == 0) {
                        continue;
                    }

                    if (payment.compareTo(notPay) >= 0) {
                        payment = payment.subtract(notPay);
                        InvoiceRecordPaymentDetail paymentDetail = paymentDetail(notPay, tRecordPaymentInfo, bean, null, invoice);
                        detailList.add(paymentDetail);
                    } else {
                        InvoiceRecordPaymentDetail paymentDetail = paymentDetail(payment, tRecordPaymentInfo, bean, null, invoice);
                        detailList.add(paymentDetail);
                        payment = BigDecimal.ZERO;
                    }
                }
            }
        }
        if (!detailList.isEmpty()) {
            invoiceRecordPaymentDetailRepository.saveAll(detailList);
            log.info("group invoice: add record payment detail param size:{} ", detailList.size());
        }
    }

    /**
     * query payment record list
     *
     * @param groupInvoiceId
     * @return
     */
    @Override
    public List<PaymentRecordListVO> getPaymentRecordList(BigInteger groupInvoiceId) {
        List<TRecordPaymentInfo> recordPaymentInfos = recordPaymentRepository.findByGroupInvoiceIdAndStatus(groupInvoiceId, 1);
        List<PaymentRecordListVO> vo = new ArrayList<>();
        if (!recordPaymentInfos.isEmpty()) {
            for (TRecordPaymentInfo pr : recordPaymentInfos) {
                PaymentRecordListVO bean = new PaymentRecordListVO();
                bean.setId(pr.getId());
                bean.setNote(pr.getNote());
                bean.setGroupInvoiceId(pr.getGroupInvoiceId());
                bean.setPaymentMethod(pr.getPaymentMethod().name());
                bean.setPaymentDate(pr.getPaymentDate());
                bean.setPaymentAmount(pr.getPaymentAmount());
                vo.add(bean);
            }
        }
        return vo;
    }

    /**
     * unRecord payment
     *
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void unRecordPayment(BigInteger id) {
        final String key = "contractor:invoice:unRecord:payment:" + id;
        if (Objects.equals(commonRedisService.setNxAndExpire(key, "1", 120L), 1L)) {

            Optional<TRecordPaymentInfo> recordPaymentInfo = recordPaymentRepository.findById(id);
            if (!recordPaymentInfo.isPresent()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }

            TRecordPaymentInfo bean = recordPaymentInfo.get();
            if (bean.getStatus() == 0) {
                return;
            }

            bean.setStatus(0);

            if (bean.getPurchaseOrderId() != null) {
                Optional<CompanyPurchaseOrderDetail> detail = companyPurchaseOrderDetailRepository.findById(bean.getPurchaseOrderId().longValue());
                if (detail.isPresent()) {
                    CompanyPurchaseOrderDetail companyPurchaseOrderDetail = detail.get();
                    companyPurchaseOrderDetail.setBalance(companyPurchaseOrderDetail.getBalance().add(bean.getPaymentAmount()));
                    companyPurchaseOrderDetailRepository.save(companyPurchaseOrderDetail);
                    log.info("group invoice: update company purchase order balance:{},id:{}", bean.getPaymentAmount(), bean.getPurchaseOrderId());
                }
            }

            Optional<TGroupInvoice> tGroupInvoice = groupInvoiceRepository.findById(bean.getGroupInvoiceId());
            if (!tGroupInvoice.isPresent()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }

            TGroupInvoice groupBean = tGroupInvoice.get();
            GroupInvoiceStatus oldGroupInvoiceStatus = groupBean.getGroupInvoiceStatus();
            groupBean.setAmountDue(groupBean.getAmountDue().add(bean.getPaymentAmount()));
            if (groupBean.getPaymentDueDate().after(new Timestamp(new Date().getTime()))) {
                if (groupBean.getAmountDue().compareTo(groupBean.getInvoiceAmount()) == 0) {
                    groupBean.setGroupInvoiceStatus(GroupInvoiceStatus.UNPAID);
                } else {
                    groupBean.setGroupInvoiceStatus(GroupInvoiceStatus.PARTIALLY_PAID);
                }
            } else {
                groupBean.setGroupInvoiceStatus(GroupInvoiceStatus.OVERDUE);
            }

            recordPaymentRepository.save(bean);
            log.info("group invoice: unrecord payment,update payment status is 0 by id:{}", bean.getId());
            groupInvoiceRepository.save(groupBean);

            if (oldGroupInvoiceStatus == GroupInvoiceStatus.PAID) {
                SecurityContext context = SecurityContextHolder.getContext();
                CompletableFuture.runAsync(() -> {
                    SecurityContextHolder.setContext(context);
                    List<TGroupInvoiceRecord> recordList = groupInvoiceRecordRepository.findTGroupInvoiceRecordByGroupInvoiceIdAndStatus(CollUtil.newArrayList(groupBean.getId()));
                    if (CollUtil.isNotEmpty(recordList)) {
                        applicationService.createInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(recordList.stream()
                                .map(TGroupInvoiceRecord::getInvoiceId).map(BigInteger::longValue).collect(Collectors.toList())).build());
                    }
                });
            }
            log.info("group invoice: unrecord payment,update group invoice status is {} by id:{}", groupBean.getGroupInvoiceStatus().toDbValue(), bean.getId());

            //更新明细支付状态
            invoiceRecordPaymentDetailRepository.updateStatusByPaymentId(bean.getId().longValue());

            commonRedisService.delete(key);
        }
    }

    /**
     * ungroup invoice
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void ungroup(ContractorGroupInvoiceVoidAndPrintDTO dto) {

        List<TGroupInvoice> groupInvoiceList = groupInvoiceRepository.queryIdByIdsAndGroupInvoiceStatus(dto.getInvoiceIdList(),
                Arrays.asList(GroupInvoiceStatus.INVOICED.toDbValue()));
        if (groupInvoiceList.size() != dto.getInvoiceIdList().size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_GROUP_UNGROUP.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        String operateByNameList = groupInvoiceList.stream().filter(m -> StringUtils.isNotBlank(m.getOperatedByName())).map(TGroupInvoice::getOperatedByName).collect(Collectors.joining());
        if (!operateByNameList.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_GROUP_UNGROUP.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        //query group invoice and invoice mapping info
        List<TGroupInvoiceRecord> recordList = getGroupInvoiceRecordList(dto);

        //update invoice status is ungroup and record status is 0
        updateContractorInvoiceStatusAndRecordStatus(recordList, "ungroup");

        Set<Long> groupInvoiceIdList = groupInvoiceList.stream().map(TGroupInvoice::getId).map(a -> a.longValue()).collect(Collectors.toSet());
        groupInvoiceRepository.updateStatusByIds(groupInvoiceIdList);
        if (CollUtil.isNotEmpty(recordList)) {
            applicationService.deleteInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(recordList.stream()
                    .map(TGroupInvoiceRecord::getInvoiceId).map(BigInteger::longValue).collect(Collectors.toList())).build());
        }
        log.info("group invoice: ungroup invoice, update group invoice status is 0,ids:{}", JSON.toJSONString(groupInvoiceIdList));
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            List<Long> ids = dto.getInvoiceIdList().stream().map(BigInteger::longValue).toList();
            for(Long id : ids) {
                notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(id, InvoiceTypeEnum.T_GROUP_INVOICE), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
            }
        });
    }

    /**
     * 查询group invoice 与 group 映射信息
     *
     * @param dto
     * @return
     */
    private List<TGroupInvoiceRecord> getGroupInvoiceRecordList(ContractorGroupInvoiceVoidAndPrintDTO dto) {
        //query group invoice record list
        List<TGroupInvoiceRecord> recordList = groupInvoiceRecordRepository.findTGroupInvoiceRecordByGroupInvoiceIdAndStatus(dto.getInvoiceIdList());
        if (recordList.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_GROUP_RECORD.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        return recordList;
    }

    /**
     * update invoice status is ungroup
     * update group invoice record status is 0
     *
     * @param recordList
     */
    private void updateContractorInvoiceStatusAndRecordStatus(List<TGroupInvoiceRecord> recordList, String title) {
        Set<Long> invoiceIdList = recordList.stream().map(TGroupInvoiceRecord::getInvoiceId).map(m -> m.longValue()).collect(Collectors.toSet());
        contractorInvoiceRepository.updateInvoiceStatusByIdsAndInvoiceStatus(invoiceIdList, InvoiceStatusType.UNGROUPED.toDbValue());
        log.info("group invoice: {} invoice,update invoice status is ungroup and groupInvoiceNumber is null by id:{}", title, JSON.toJSONString(invoiceIdList));

        Set<Long> recordIdList = recordList.stream().map(TGroupInvoiceRecord::getId).map(m -> m.longValue()).collect(Collectors.toSet());
        groupInvoiceRecordRepository.updateStatusByIds(recordIdList);
        log.info("group invoice: {} invoice,update group invoice record status is void by id:{}", title, JSON.toJSONString(recordIdList));
    }

    /**
     * group invoice void
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void groupInvoiceVoid(ContractorGroupInvoiceVoidAndPrintDTO dto) {

        List<TGroupInvoice> groupInvoiceList = groupInvoiceRepository.queryIdByIdsAndGroupInvoiceStatus(dto.getInvoiceIdList(),
                Arrays.asList(GroupInvoiceStatus.PAID.toDbValue(),
                        GroupInvoiceStatus.PARTIALLY_PAID.toDbValue(),
                        GroupInvoiceStatus.UNPAID.toDbValue(),
                        GroupInvoiceStatus.OVERDUE.toDbValue()));
        if (groupInvoiceList.size() != dto.getInvoiceIdList().size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_GROUP_VOID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        List<TGroupInvoiceRecord> recordList = getGroupInvoiceRecordList(dto);

        //update invoice status is ungroup and record status is 0
        updateContractorInvoiceStatusAndRecordStatus(recordList, "void group ");

        groupInvoiceRepository.updateGroupInvoiceStatusAndAmountDueByIds(dto.getInvoiceIdList(), GroupInvoiceStatus.VOID.toDbValue());
        log.info("group invoice: void group invoice, update group invoice status is 0,ids:{}", JSON.toJSONString(dto.getInvoiceIdList()));

        List<TRecordPaymentInfo> recordPaymentInfoList = recordPaymentRepository.findByGroupInvoiceIdInAndStatus(dto.getInvoiceIdList(), 1);
        if (!recordPaymentInfoList.isEmpty()) {
            for (TRecordPaymentInfo tRecordPaymentInfo : recordPaymentInfoList) {
                if (tRecordPaymentInfo.getPurchaseOrderId() != null) {
                    Optional<CompanyPurchaseOrderDetail> detail = companyPurchaseOrderDetailRepository.findById(tRecordPaymentInfo.getPurchaseOrderId().longValue());
                    if (detail.isPresent()) {
                        CompanyPurchaseOrderDetail companyPurchaseOrderDetail = detail.get();
                        companyPurchaseOrderDetail.setBalance(companyPurchaseOrderDetail.getBalance().add(tRecordPaymentInfo.getPaymentAmount()));
                        companyPurchaseOrderDetailRepository.save(companyPurchaseOrderDetail);
                        log.info("group invoice: void group invoice, update company purchase order balance:{},ids:{}", tRecordPaymentInfo.getPaymentAmount(), tRecordPaymentInfo.getPurchaseOrderId());
                    }
                }
            }
        }
        recordPaymentRepository.updateStatusByIds(dto.getInvoiceIdList());

        //更新支付明细状态
        invoiceRecordPaymentDetailRepository.updateStatusByGroupInvoiceIds(dto.getInvoiceIdList());

        if (CollUtil.isNotEmpty(recordList)) {
            applicationService.deleteInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(recordList.stream()
                    .map(TGroupInvoiceRecord::getInvoiceId).map(BigInteger::longValue).collect(Collectors.toList())).build());
        }
        log.info("group invoice: void record payment, update status is 0,ids:{}", JSON.toJSONString(dto.getInvoiceIdList()));
        SecurityContext context = SecurityContextHolder.getContext();
        List<BigInteger> tgiIds = groupInvoiceList.stream().map(TGroupInvoice::getId).toList();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            for(BigInteger tgiId : tgiIds) {
                notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(tgiId.longValue(), InvoiceTypeEnum.T_GROUP_INVOICE), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
            }
        });

    }

    /**
     * get group invoice attachment info
     *
     * @param dto
     * @return ContractorGroupInvoiceAttachmentVO
     */
    @Override
    public List<ContractorGroupInvoiceAttachmentVO> getAttachmentList(ContractorGroupInvoiceVoidAndPrintDTO dto) {
        List<Map<String, Object>> attachmentVOS = contractorGroupInvoiceNativeRepository.selectGroupInvoiceAttachmentByIds(dto.getInvoiceIdList(), SecurityUtils.getTenantId());
        if (dto.getInvoiceIdList().size() != attachmentVOS.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_GROUP_ATTACHMENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        List<ContractorGroupInvoiceAttachmentVO> voList = new ArrayList<>();
        for (int i = 0; i < attachmentVOS.size(); i++) {
            Map<String, Object> map = attachmentVOS.get(i);
            ContractorGroupInvoiceAttachmentVO vo = new ContractorGroupInvoiceAttachmentVO();
            if (i == 0) {
                String email = contractorGroupInvoiceNativeRepository.selectEmailByGroupInvoiceId(dto.getInvoiceIdList().get(0).longValue());
                if (StringUtils.isNotBlank(email)) {
                    vo.setEmail(email);
                } else {
                    BigInteger companyId = new BigInteger(map.get("company_id").toString());
                    email = contractorGroupInvoiceNativeRepository.selectEmailByCompanyId(SecurityUtils.getTenantId(), companyId, dto.getInvoiceIdList().get(0).longValue());
                    if (StringUtils.isBlank(email)) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_GROUP_ATTACHMENT_COMPANY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                    }
                    vo.setEmail(email);
                }
            }
            String fileUrl = null;
            if (map.containsKey("includeTimesheet")) {
                fileUrl = Integer.valueOf(map.get("includeTimesheet").toString()) == 1 ? map.get("timesheetFileUrl") + "" : map.get("fileUrl") + "";
            }
            if (StringUtils.isBlank(fileUrl) || fileUrl.equals("null")) {
                throw new NotFoundException("document " + map.get("group_number") + " s3Link is null, please wait for generation.", SecurityUtils.getUserId());
            }
            //get file info
            CloudFileObjectMetadata cloudFileObjectByInvoice = storeService.getFileFromS3(fileUrl, UploadTypeEnum.GROUP_INVOICE.getKey()).getBody();
            if (ObjectUtil.isNull(cloudFileObjectByInvoice)) {
                log.error("[invoice: User @{}] REST request to group invoice file process , document s3Link is invalid .", SecurityUtils.getUserId());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.S3_DOCUMENT_INVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }

            vo.setViewUrl(cloudFileObjectByInvoice.getS3Link());
            vo.setFileName(cloudFileObjectByInvoice.getFileName());
            vo.setFileSize(cloudFileObjectByInvoice.getContent().length + "");
            vo.setFileMd5(fileUrl);
            voList.add(vo);
        }

        return voList;
    }

    /**
     * group invoice send email
     *
     * @param dto sendToClient 0不发送 1发送
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendEmail(ContractorGroupInvoiceSendEmailDTO dto) throws Exception {

        //用于发送邮件
        List<String> links = new ArrayList<>();
        //用于保存email attachment
        List<String> md5Links = new ArrayList<>();
        //获取前端上传的文件链接
        if (null != dto.getAttachmentList() || !dto.getAttachmentList().isEmpty()) {
            for (AttachmentFileDTO attachmentFile : dto.getAttachmentList()) {
                md5Links.add(attachmentFile.getFileMd5());
                if (attachmentFile.getType() == 1) {
                    links.add(attachmentFile.getFileMd5());
                    continue;
                }
                CloudFileObjectMetadata cloudFileObjectByInvoice = storeService.getFileFromS3(attachmentFile.getFileMd5(), UploadTypeEnum.GROUP_INVOICE.getKey()).getBody();
                if (ObjectUtil.isNull(cloudFileObjectByInvoice)) {
                    log.error("[invoice: User @{}] REST request to group invoice file process , document s3Link is invalid .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.S3_DOCUMENT_INVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }

                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                bos.write(cloudFileObjectByInvoice.getContent());
                MultipartFile file = new MockMultipartFile(cloudFileObjectByInvoice.getFileName(), cloudFileObjectByInvoice.getFileName(), ContentType.APPLICATION_OCTET_STREAM.toString(), bos.toByteArray());
                String mk5Key = DigestUtils.md5Hex(file.getInputStream());
                String s3Links = storeService.uploadDocument(file, mk5Key, UploadTypeEnum.EMAIL_ATTACHMENT.getKey()).getBody();

                links.add(s3Links);
            }
        }


        //构建发送email数据
        MailVM mailVM = new MailVM();
        mailVM.setContent(dto.getContent());
        mailVM.setSubject(dto.getSubject());
        mailVM.setTo(dto.getSendToList());
        mailVM.setCc(dto.getCcList());
        mailVM.setFrom(SecurityUtils.getCurrentUserLogin().get().getEmail());
        mailVM.setLinks(links);
        mailVM.setIsSystemEmail(false);
        mailService.sendHtmlMail(mailVM);
        log.info("group invoice: send to Email service param:{}", JSON.toJSONString(mailVM));

        doSendToClient(dto, "send to Email");

        //async save email record info
        groupInvoiceSendEmailService.saveEmailRecord(dto, md5Links);
    }

    private void doSendToClient(ContractorGroupInvoiceSendEmailDTO dto, String title) {
        if (dto.getSentToClient() == 1) {
            List<TGroupInvoice> groupInvoiceList = groupInvoiceRepository.queryIdByIdsAndGroupInvoiceStatus(dto.getInvoiceIdList(), Arrays.asList(GroupInvoiceStatus.INVOICED.toDbValue()));
            if (null != groupInvoiceList && !groupInvoiceList.isEmpty()) {
                List<BigInteger> groupInvoiceIdList = groupInvoiceList.stream().map(TGroupInvoice::getId).collect(Collectors.toList());
                LoginInformation loginUserDTO = SecurityUtils.getCurrentUserLogin().get();
                groupInvoiceRepository.updateOperateUserByIds(groupInvoiceIdList, CommonUtils.formatFullName(loginUserDTO.getFirstName(), loginUserDTO.getLastName()), loginUserDTO.getId());
                log.info("group invoice: {} update group invoice operate user info operate id: {},group invoice ids: {}", title, loginUserDTO.getId(), JSON.toJSONString(dto.getInvoiceIdList()));
            } else {
                log.info("group invoice: {}, not found group invoice info,group invoice ids: {}", title, JSON.toJSONString(dto.getInvoiceIdList()));
            }
        }
    }

    @Override
    public void sendToClient(ContractorGroupInvoiceSendEmailDTO dto) {
        doSendToClient(dto, "send to client");
    }

    /**
     * 前端上传获取地址
     *
     * @param uuid
     * @param fileName
     * @return
     */
    @Override
    public StoreGetUploadUrlVO getUploadUrl(String uuid, String fileName) {
        UploadUrlDto uploadUrlDto = new UploadUrlDto();
        uploadUrlDto.setUuid(uuid);
        uploadUrlDto.setUploadType(UploadTypeEnum.EMAIL_INVOICE_ATTACHMENT.getKey());
        uploadUrlDto.setContentType("multipart/form-data");
        StoreGetUploadUrlVO postPolicy = storeService.getPresignedCommonUploadUrlFromS3WithPostPolicy(uploadUrlDto).getBody();
        return postPolicy;
    }

    /**
     * print info
     *
     * @param dto
     */
    @Override
    public ContractorGroupInvoicePrintVO print(ContractorGroupInvoiceVoidAndPrintDTO dto) {
        List<TGroupInvoice> tGroupInvoiceList = groupInvoiceRepository.queryIdByIds(dto.getInvoiceIdList());
        ContractorGroupInvoicePrintVO vo = new ContractorGroupInvoicePrintVO();
        if (null != tGroupInvoiceList && !tGroupInvoiceList.isEmpty()) {
            vo.setStatus(2);
            vo.setProgressValue(100);
            List<ContractorGroupInvoicePrintVO.FileInfo> fileList = new ArrayList<>();
            for (TGroupInvoice invoice : tGroupInvoiceList) {
                if (invoice.getInvoiceAmount().equals(new BigDecimal("0.00"))) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.INVOICE_GROUP_ATTACHMENT_INVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }
                String fileUrl = invoice.getIncludeTimesheet().toDbValue() == 1 ? invoice.getTimesheetFileUrl() : invoice.getFileUrl();
                if (StringUtils.isBlank(fileUrl)) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_GROUP_ATTACHMENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }
                CloudFileObjectMetadata cloudFileObjectByInvoice = storeService.getFileFromS3(fileUrl, UploadTypeEnum.GROUP_INVOICE.getKey()).getBody();
                if (ObjectUtil.isNull(cloudFileObjectByInvoice)) {
                    log.error("[invoice: User @{}] REST request to group invoice file process , document s3Link is invalid .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.S3_DOCUMENT_INVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }
                ContractorGroupInvoicePrintVO.FileInfo fileInfo = new ContractorGroupInvoicePrintVO.FileInfo();
                fileInfo.setFileUrl(cloudFileObjectByInvoice.getS3Link());
                fileInfo.setFileName(cloudFileObjectByInvoice.getFileName());
                fileList.add(fileInfo);
            }
            vo.setFileList(fileList);
        }

        if (dto.getSentToClient() == 1) {
            LoginInformation loginUserDTO = SecurityUtils.getCurrentUserLogin().get();
            List<TGroupInvoice> groupInvoiceList = groupInvoiceRepository.queryIdByIdsAndGroupInvoiceStatus(dto.getInvoiceIdList(), Arrays.asList(GroupInvoiceStatus.INVOICED.toDbValue()));
            if (null != groupInvoiceList && !groupInvoiceList.isEmpty()) {
                List<BigInteger> groupInvoiceIdList = groupInvoiceList.stream().map(TGroupInvoice::getId).collect(Collectors.toList());
                groupInvoiceRepository.updateOperateUserByIds(groupInvoiceIdList, CommonUtils.formatFullName(loginUserDTO.getFirstName(), loginUserDTO.getLastName()), loginUserDTO.getId());
                log.info("group invoice: print update group invoice operate user info operate id: {},group invoice ids: {}", loginUserDTO.getId(), JSON.toJSONString(dto.getInvoiceIdList()));
            }
        }

        return vo;
    }

    /**
     * 定时任务执行更新group inovice status
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateGroupInvoiceStatus() {
        log.info("[Group Invoice Status Job Executor] Job start at: {}", System.currentTimeMillis());
        List<BigInteger> groupIdList = groupInvoiceRepository.selectGroupInvoiceIdByGroupInvoiceStatus();
        log.info("[Group Invoice Status Job Executor] Number of data pieces to be processed: {}", groupIdList.size());
        if (null != groupIdList && !groupIdList.isEmpty()) {
            groupInvoiceRepository.updateGroupInvoiceStatusByIds(groupIdList, GroupInvoiceStatus.OVERDUE.toDbValue());
            log.info("[Group Invoice Status Job Executor] update group status: 3 over due，id list :{}", JSON.toJSONString(groupIdList));
        }
        log.info("[Group Invoice Status Job Executor] Job start at: {}", System.currentTimeMillis());
        return "success";
    }

    /**
     * 定时任务 查询文件未生产的数据 发送到mq
     *
     * @return
     */
    @Override
    public String searchFileDoesNotExist() {
        log.info("[Group Invoice file is null Job Executor] Job start at: {}", System.currentTimeMillis());
        List<BigInteger> groupIdList = groupInvoiceRepository.selectGroupInvoiceIdByFileIsNull();
        log.info("[Group Invoice file is null Job Executor] Number of data pieces to be processed: {}", groupIdList.size());
        if (null != groupIdList && !groupIdList.isEmpty()) {
            for (BigInteger id : groupIdList) {
                //send to rabbit
                rabbitTemplate.convertAndSend(queueConfig.getExchange(), queueConfig.getRoutingKey(), id + "&UTC");
                log.info("[Group Invoice file is null Job Executor] send mq send to mq id:{},exchange:{},routeKey:{}", id, queueConfig.getExchange(), queueConfig.getRoutingKey());
            }
        }
        log.info("[Group Invoice file is null Job Executor] Job start at: {}", System.currentTimeMillis());
        return "success";
    }

    /**
     * group invoice 数据迁移
     *
     * @return
     */
    @Override
    public String dataMigrateGroupInvoice() {

        //查询status=3 3是group invoice number 不为空的数据
        List<String> groupNumberList = tMongodbToJobdivaTempRepository.selectGroupNumberByStatus();
        log.info("migrate group invoice, 查询出{}数据", groupNumberList.size());
        if (groupNumberList.isEmpty()) {
            return null;
        }

        LoginUtil.simulateLoginWithClient();

        Long tenantId = tMongodbToJobdivaTempRepository.findTenantIdByGroupNumbers(groupNumberList);

        //group 下的所有Invoice
        for (String num : groupNumberList) {
            // 手动控制事务
            DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
            TransactionStatus status = transactionManager.getTransaction(definition);
            try {
                log.info("migrate group invoice, group invoice number{},", num);
                //删除group invoice
                groupInvoiceRepository.updateStatusByGroupNumberAndTenantId(num, tenantId);
                log.info("migrate group invoice, 根据{} 删除数据,", num);
                List<MongodbToJobDivaTempDTO> invoiceIdList = tMongodbToJobdivaTempNativeRepository.queryInvoiceIdByTempGroupNumber(num, tenantId);
                if (null != invoiceIdList && !invoiceIdList.isEmpty()) {

                    List<BigInteger> idList = invoiceIdList.stream().map(MongodbToJobDivaTempDTO::getInvoiceId).collect(Collectors.toList());

                    //查询Invoice 数据
                    List<TContractorInvoice> tContractorInvoiceList = contractorInvoiceRepository.queryInvoiceByIds(idList);
                    if (null != tContractorInvoiceList) {

                        BigDecimal total = tContractorInvoiceList.stream().map(TContractorInvoice::getTotalAmount).reduce(BigDecimal.ZERO, (q, p) -> p.add(q).setScale(2, BigDecimal.ROUND_HALF_UP));

                        TGroupInvoice tGroupInvoice = new TGroupInvoice();
                        tGroupInvoice.setInvoiceAmount(total);
                        tGroupInvoice.setGroupType(GroupType.COMPANY);

                        tGroupInvoice.setInvoiceDate(invoiceIdList.get(0).getInvoiceDate().toLocalDateTime().toLocalDate());
                        tGroupInvoice.setStatus(1);
                        tGroupInvoice.setGroupNumber(num);
                        tGroupInvoice.setAmountDue(total);
                        String groupInvoiceStatus = invoiceIdList.get(0).getGroupInvoiceStatus();
                        if (StringUtils.isNotBlank(groupInvoiceStatus)) {
                            if (groupInvoiceStatus.equals(GroupInvoiceStatus.UNPAID.toDbValue().toString())) {
                                tGroupInvoice.setGroupInvoiceStatus(GroupInvoiceStatus.UNPAID);
                            } else {
                                tGroupInvoice.setGroupInvoiceStatus(GroupInvoiceStatus.VOID);
                            }
                        } else {
                            tGroupInvoice.setGroupInvoiceStatus(GroupInvoiceStatus.UNPAID);
                        }
                        tGroupInvoice.setCompanyId(tContractorInvoiceList.get(0).getCompanyId());
                        tGroupInvoice.setCompanyName(tContractorInvoiceList.get(0).getCompanyName());
                        tGroupInvoice.setTenantId(BigInteger.valueOf(tenantId));
                        if (invoiceIdList.get(0).getGroupInvoiceType().equals("2")) {
                            tGroupInvoice.setInvoiceType(InvoiceType.REGULAR);
                        } else {
                            tGroupInvoice.setInvoiceType(InvoiceType.EXPENSE);
                        }

                        //查询currency
                        List<Integer> currencyList = invoiceTimesheetInfoRepository.queryCurrencyTypeByInvoiceId(idList);
                        if (null != currencyList) {
                            tGroupInvoice.setCurrencyId(currencyList.get(0));
                        }

                        groupInvoiceRepository.save(tGroupInvoice);
                        log.info("migrate group invoice, insert group invoice,group invoice id{}", tGroupInvoice.getId());

                        //插入记录表
                        List<TGroupInvoiceRecord> recordList = new ArrayList<>();
                        for (TContractorInvoice t : tContractorInvoiceList) {
                            TGroupInvoiceRecord bean = new TGroupInvoiceRecord();
                            bean.setInvoiceId(t.getId());
                            bean.setGroupInvoiceId(tGroupInvoice.getId());
                            bean.setStatus(1);
                            bean.setInternalInvoiceType(t.getInvoiceType());
                            bean.setTalentId(t.getTalentId());
                            recordList.add(bean);
                        }

                        if (!recordList.isEmpty()) {
                            groupInvoiceRecordRepository.saveAll(recordList);
                            log.info("migrate group invoice, insert group invoice record");
                        }

                        //插入支付记录
                        TRecordPaymentInfo recordPaymentInfo = new TRecordPaymentInfo();
                        recordPaymentInfo.setGroupInvoiceNumber(tGroupInvoice.getGroupNumber());
                        recordPaymentInfo.setGroupInvoiceId(tGroupInvoice.getId());
                        recordPaymentInfo.setPaymentDate(Timestamp.valueOf(tGroupInvoice.getInvoiceDate().atStartOfDay()));
                        recordPaymentInfo.setPaymentAmount(BigDecimal.ZERO);
                        recordPaymentInfo.setStatus(1);
                        recordPaymentInfo.setPaymentMethod(PaymentMethodType.WIRE_TRANSFER);
                        recordPaymentRepository.save(recordPaymentInfo);
                        log.info("migrate group invoice, insert record payment info");

                        List<BigInteger> tempIdList = invoiceIdList.stream().map(MongodbToJobDivaTempDTO::getId).collect(Collectors.toList());
                        if (!tempIdList.isEmpty()) {
                            tMongodbToJobdivaTempRepository.updateStatusByIds(tempIdList, 4);
                            log.info("migrate group invoice, update tMongodbToJobdivaTemp status=4, ids:{}", JSON.toJSONString(tempIdList));
                        }
                    }
                }
                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                throw e;
            }
        }

        return "success";
    }

    @Override
    public String initPaymentDetail() {

        Integer id = 14;
        while (true) {
            List<TGroupInvoice> groupInvoiceList = groupInvoiceRepository.queryPaidAndPartiallyPaidLimit(id);
            if (groupInvoiceList.isEmpty()) {
                return "not find data";
            }
            List<BigInteger> groupIds = groupInvoiceList.stream().map(TGroupInvoice::getId).collect(Collectors.toList());
            id = groupInvoiceList.get(groupInvoiceList.size() - 1).getId().intValue();

            List<ContractorInvoiceInitPaymentDetailVO> paymentDetailVOS = contractorInvoiceNativeRepository.findPaymentInfoByGroupInvoiceId(groupIds);
            if (paymentDetailVOS.isEmpty()) {
                return "not find data";
            }

            List<Long> groupIdList = groupIds.stream().map(v -> v.longValue()).collect(Collectors.toList());
            List<InvoiceRecordPaymentDetail> paymentDetails = invoiceRecordPaymentDetailRepository.findByGroupIdInAndStatus(groupIdList, 1);
            Map<Long, List<InvoiceRecordPaymentDetail>> paymentDetailsMap = paymentDetails.stream().collect(Collectors.groupingBy(InvoiceRecordPaymentDetail::getGroupId));

            // 手动控制事务
            DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
            TransactionStatus status = transactionManager.getTransaction(definition);
            try {
                Map<BigInteger, List<ContractorInvoiceInitPaymentDetailVO>> paymentRecordMap = paymentDetailVOS.stream().collect(Collectors.groupingBy(ContractorInvoiceInitPaymentDetailVO::getPaymentId));
                List<InvoiceRecordPaymentDetail> detailList = new ArrayList<>();
                for (Map.Entry<BigInteger, List<ContractorInvoiceInitPaymentDetailVO>> m : paymentRecordMap.entrySet()) {
                    List<ContractorInvoiceInitPaymentDetailVO> recordList = m.getValue();

                    //已支付金额
                    List<InvoiceRecordPaymentDetail> recordPaymentDetails = paymentDetailsMap.get(recordList.get(0).getGroupInvoiceId().longValue());
                    Map<Long, BigDecimal> talentPaymentMap = new HashMap<>();
                    if(recordPaymentDetails != null && !recordPaymentDetails.isEmpty()){
                        talentPaymentMap = recordPaymentDetails.stream()
                                .collect(Collectors.groupingBy(
                                        InvoiceRecordPaymentDetail::getInvoiceId, // 按userId分组
                                        Collectors.reducing(BigDecimal.ZERO, InvoiceRecordPaymentDetail::getPaymentAmount, BigDecimal::add)));
                    }

                    if (!detailList.isEmpty()) {
                        for (InvoiceRecordPaymentDetail detail : detailList) {
                            if (talentPaymentMap.containsKey(detail.getInvoiceId().longValue())) {
                                talentPaymentMap.put(detail.getInvoiceId().longValue(), talentPaymentMap.get(detail.getInvoiceId().longValue()).add(detail.getPaymentAmount()));
                            } else {
                                talentPaymentMap.put(detail.getInvoiceId().longValue(), detail.getPaymentAmount());
                            }
                        }
                    }

                    BigDecimal payment = recordList.get(0).getPaymentAmount();
                    for (int i = 0; i < recordList.size(); i++) {
                        ContractorInvoiceInitPaymentDetailVO invoice = recordList.get(i);
                        if (invoice.getTotalAmount().compareTo(new BigDecimal("0.00")) == 0) {
                            continue;
                        }
                        if (payment.compareTo(new BigDecimal("0.00")) == 0) {
                            break;
                        }
                        BigDecimal amountPaid = talentPaymentMap.get(invoice.getId().longValue()) == null ? BigDecimal.ZERO : talentPaymentMap.get(invoice.getId().longValue());

                        //未支付金额
                        BigDecimal notPay = invoice.getTotalAmount().subtract(amountPaid);
                        if (notPay.compareTo(new BigDecimal("0.00")) == 0) {
                            continue;
                        }

                        if (payment.compareTo(notPay) >= 0) {
                            payment = payment.subtract(notPay);
                            InvoiceRecordPaymentDetail paymentDetail = initPaymentDetail(invoice, notPay);
                            detailList.add(paymentDetail);
                        } else {
                            InvoiceRecordPaymentDetail paymentDetail = initPaymentDetail(invoice, payment);
                            detailList.add(paymentDetail);
                            payment = BigDecimal.ZERO;
                        }
                    }
                }

                if (!detailList.isEmpty()) {
                    invoiceRecordPaymentDetailRepository.saveAll(detailList);
                    log.info("group invoice: add record payment detail param size:{} ", detailList.size());
                }
                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                throw e;
            }
        }
        //return null;
    }

    private InvoiceRecordPaymentDetail initPaymentDetail(ContractorInvoiceInitPaymentDetailVO vo, BigDecimal paymentAmount) {
        InvoiceRecordPaymentDetail paymentDetail = new InvoiceRecordPaymentDetail();
        paymentDetail.setGroupId(vo.getGroupInvoiceId().longValue());
        if (null != paymentAmount) {
            paymentDetail.setPaymentAmount(paymentAmount);
        } else {
            paymentDetail.setPaymentAmount(vo.getTotalAmount());
        }
        paymentDetail.setPaymentId(vo.getPaymentId().longValue());
        paymentDetail.setGpAmount(vo.getInvoiceAmount());
        paymentDetail.setStatus(1);
        paymentDetail.setPaymentDate(vo.getPaymentDate());
        paymentDetail.setInvoiceId(vo.getId().longValue());
        paymentDetail.setTalentId(vo.getTalentId().longValue());
        paymentDetail.setTalentName(vo.getTalentName());
        return paymentDetail;
    }
}