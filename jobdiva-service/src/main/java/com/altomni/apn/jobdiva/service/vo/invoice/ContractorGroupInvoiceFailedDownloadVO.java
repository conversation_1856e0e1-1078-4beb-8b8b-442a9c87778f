package com.altomni.apn.jobdiva.service.vo.invoice;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * failed VO info
 * <AUTHOR>
 */
@Data
@ApiModel(description = "ContractorGroupInvoiceFailedDownloadVO")
public class ContractorGroupInvoiceFailedDownloadVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ColumnWidth(30)
    @ExcelProperty(value = "Week Ending Date",index = 0)
    private String weekEndingDate;

    @ColumnWidth(20)
    @ExcelProperty(value = "Employee Name",index = 1)
    private String employeeName;

    @ColumnWidth(30)
    @ExcelProperty(value = "Job Title",index = 2)
    private String jobTitle;

    @ColumnWidth(30)
    @ExcelProperty(value = "Miss Invoice Type",index = 3)
    private String missInvoiceType;

    @ColumnWidth(30)
    @ExcelProperty(value = "Company",index = 4)
    private String company;

    @ColumnWidth(30)
    @ExcelProperty(value = "AM",index = 5)
    private String am;

    @ColumnWidth(30)
    @ExcelProperty(value = "Approver",index = 6)
    private String approver;

}
