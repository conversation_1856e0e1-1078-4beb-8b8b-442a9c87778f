package com.altomni.apn.jobdiva.service.company;

import com.altomni.apn.common.dto.company.ClientContactDTO;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactProfile;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Component
@FeignClient(value = "company-service")
public interface CompanyService {

    @PostMapping("/company/api/v3/saleslead/client-contacts/by-contactId-and-receiveEmail")
    ResponseEntity<List<ClientContactDTO>> findBriefContactByIdAndReceiveEmail(@RequestBody List<Long> contactIds);

    @GetMapping("/company/api/v3/company/{id}")
    ResponseEntity<CompanyDTO> getCompany(@PathVariable("id") Long id);

    @GetMapping("/company/api/v3/saleslead/client-contact/info-profile/{id}")
    ResponseEntity<SalesLeadClientContactProfile> findSalesLeadClientContactProfile(@PathVariable("id") Long id);

    @PutMapping("/company/api/v3/company/sync-to-hr/{companyId}")
    void updateCompanyNeedSyncToHr(@PathVariable("companyId") Long companyId);

}
