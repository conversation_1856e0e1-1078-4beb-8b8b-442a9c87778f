package com.altomni.apn.jobdiva.service.vo.timesheet;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * A record
 */
@ApiModel(description = "record for week ending in page")
@Data
public class SummaryDataVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "work ending date list")
    private Object data;

    @ApiModelProperty(value = "total items amount")
    private Integer totalItems;

    @ApiModelProperty(value = "pageNum")
    private Integer pageNum;

}
