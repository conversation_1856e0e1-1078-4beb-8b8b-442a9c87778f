package com.altomni.apn.jobdiva.service.timesheet.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetComments;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetCommentsRepository;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetCommentsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

@Service("timeSheetCommentsService")
public class TimeSheetCommentsServiceImpl implements TimeSheetCommentsService {

    @Resource
    private TimeSheetCommentsRepository commentsRepository;

    @Resource
    private TalentAssignmentRepository assignmentRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Override
    public TimeSheetComments findByWorkDateAndType(String workDate, CommentsType type, Long assignmentId, Integer recordIndex) {
        LocalDate localDate = LocalDate.parse(workDate);
        if(assignmentId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_COMMENT_NOT_FIND_ASSIGNMENTID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return commentsRepository.findAllByDateAndType(localDate, SecurityUtils.getUserId(), type.toDbValue(), assignmentId, recordIndex);
    }

    @Override
    public List<TimeSheetComments> findByWorkDateAndTypeByAm(String workDate, CommentsType type, Long assignmentId) {
        LocalDate localDate = LocalDate.parse(workDate);
        if (assignmentId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_COMMENT_NOT_FIND_ASSIGNMENTID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        TalentAssigment assigment;
        if (SecurityUtils.isAdmin() || SecurityUtils.isTimesheetAdmin()) {
            assigment = assignmentRepository.findById(assignmentId).get();
        } else {
            assigment = assignmentRepository.findByIdAndAmId(SecurityUtils.getUserId(),assignmentId);
        }
        if (assigment == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_COMMENT_NOT_FIND_ASSIGNMENTID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        return commentsRepository.findListByDateAndType(localDate, assigment.getTalentId(), type.toDbValue(), assignmentId);
    }

    @Override
    public TimeSheetComments save(String comment, String endingDate, CommentsType type, Long talentId, Long assignmentId, Integer recordIndex) {
        TimeSheetComments comments = findByWorkDateAndType(endingDate, type, talentId, assignmentId, recordIndex);
        if(comments == null) {
            if(comment == null || comment.trim().length() == 0) {
                return  null;
            }
            comments = new TimeSheetComments();
        } else {
            if(comment == null || comment.trim().length() == 0) {
                commentsRepository.deleteById(comments.getId());
                return null;
            }
        }
        comments.setComments(comment);
        comments.setAssignmentId(assignmentId);
        comments.setTalentId(talentId);
        comments.setCommentsType(type);
        comments.setTenantId(SecurityUtils.getTenantId());
        comments.setWorkDate(LocalDate.parse(endingDate));
        comments.setRecordIndex(recordIndex);
        comments = commentsRepository.save(comments);
        return comments;
    }

    @Override
    public TimeSheetComments findByWorkDateAndType(String endDate, CommentsType type, Long talentId, Long assignmentId, Integer recordIndex) {
        LocalDate localDate = LocalDate.parse(endDate);
        return commentsRepository.findAllByDateAndType(localDate, talentId, type.toDbValue(), assignmentId, recordIndex);
    }

    @Override
    public void deleteComment(LocalDate endDate, CommentsType type, Long talentId, Long assignmentId, Integer recordIndex) {
        commentsRepository.deleteComment(endDate, type.toDbValue(), talentId, assignmentId, recordIndex);
    }


}
