package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ResetingPortalAccountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String id;

    private String password;

    private String repeatPassword;

    @NotNull
    private boolean inactive;

    @ApiModelProperty(value = "only use in onBoarding Account Management, has timeSheet account or not")
    private boolean hasAccount;
}
