package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.jobdiva.service.vo.assignment.*;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface TalentAssignmentRepository extends JpaRepository<TalentAssigment, Long> {

    @Query(value = " select id from timesheet_talent_assignment order by id desc limit ?1, ?2 ", nativeQuery = true)
    List<Long> findAllOrderByIdDesc(Integer page, Integer size);

    @Query(value = " select * from timesheet_talent_assignment where start_id = ?1 and status !=2 order by created_date DESC limit 1 ", nativeQuery = true)
    TalentAssigment findLatestAssigmentByStartId(Long startId);

    @Query(value = " select * from timesheet_talent_assignment where start_id = ?1 and status !=2 order by created_date DESC ", nativeQuery = true)
    List<TalentAssigment> findLatestAssigmentByStartIdList(Long startId);

    @Query(value = " select * from timesheet_talent_assignment where start_id = ?1 and status = 2 order by created_date DESC ", nativeQuery = true)
    List<TalentAssigment> findCloseAssigmentByStartIdAndStatusList(Long startId);

    @Query(value = " select * from timesheet_talent_assignment where start_id = ?1 and id != ?2 order by created_date DESC limit 1 ", nativeQuery = true)
    TalentAssigment findLatestAssigmentByStartIdAndisNoEqAssignmentId(Long startId, Long assignmentId);

    @Query(value = " select distinct new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO (TA.id,TA.startDate,TA.endDate,AT.weekEnding,AT.timeSheetType,AT.calculateType,TA.jobId,TA.companyId,AT.allowSubmitTimeSheet,AT.allowSubmitExpense,ABI.overtimeType,ABI.isExcept) " +
            " FROM TalentAssigment TA " +
            " left join AssignmentTimeSheet AT ON TA.id = AT.assignmentId " +
            " left join AssignmentBillInfo ABI ON ABI.assignmentId = TA.id " +
            " where TA.talentId = ?1 and TA.status = 1 ")
    List<AssignmentVO> findAssigmentInfoByTalentId(Long talentId);

    @Query(value = " select distinct new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO (TA.id,TA.startDate,TA.endDate,AT.weekEnding,AT.timeSheetType,AT.calculateType,TA.jobId,TA.companyId,AT.allowSubmitTimeSheet,AT.allowSubmitExpense,ABI.overtimeType,ABI.isExcept) " +
            " FROM TalentAssigment TA " +
            " left join AssignmentTimeSheet AT ON TA.id = AT.assignmentId " +
            " left join AssignmentBillInfo ABI ON ABI.assignmentId = TA.id " +
            " left join BusinessFlowAdministrator BFA on BFA.companyId = TA.companyId " +
            " where TA.talentId = ?1 and TA.status = 1 and BFA.salesLeadRoleType = 0 and BFA.userId = ?2 ")
    List<AssignmentVO> findAllByTalentIdAndAmId(Long talentId, Long amId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO (TA.id,TA.startDate,TA.endDate,AT.weekEnding,AT.timeSheetType,AT.calculateType,TA.jobId,TA.companyId,AT.allowSubmitTimeSheet,AT.allowSubmitExpense,ABI.overtimeType,ABI.isExcept, API.frequency, TA.talentId, TA.tenantId, TA.isWeekEnd) " +
            " FROM TalentAssigment TA " +
            " left join AssignmentTimeSheet AT ON TA.id = AT.assignmentId " +
            " left join AssignmentBillInfo ABI ON ABI.assignmentId = TA.id " +
            " left join AssignmentPayInfo API on API.assignmentId = TA.id " +
            " where TA.id = ?1 and TA.status = 1 ")
    AssignmentVO findAssigmentInfoByAssignmentIdAndStatus(Long assignmentId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO (TA.id,TA.startDate,TA.endDate,AT.weekEnding,AT.timeSheetType,AT.calculateType,TA.jobId,TA.companyId,AT.allowSubmitTimeSheet,AT.allowSubmitExpense,ABI.overtimeType,ABI.isExcept, AT.frequency, TA.talentId, TA.tenantId, TA.isWeekEnd) " +
            " FROM TalentAssigment TA " +
            " left join AssignmentTimeSheet AT ON TA.id = AT.assignmentId " +
            " left join AssignmentBillInfo ABI ON ABI.assignmentId = TA.id " +
            " left join AssignmentPayInfo API on API.assignmentId = TA.id " +
            " where TA.id = ?1")
    AssignmentVO findAssigmentInfoByAssignmentId(Long assignmentId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO (TA.id,TA.startDate,TA.endDate,AT.weekEnding,AT.timeSheetType,AT.calculateType,TA.jobId,TA.companyId,AT.allowSubmitTimeSheet,AT.allowSubmitExpense,ABI.overtimeType,ABI.isExcept) " +
            " FROM TalentAssigment  TA    " +
            " left join AssignmentTimeSheet  AT  ON TA.id = AT.assignmentId " +
            " left join AssignmentBillInfo  ABI ON ABI.assignmentId = TA.id " +
            " where TA.talentId= ?1 ")
    List<AssignmentVO> findAllAssigmentInfoByTalentId(Long talentId);

    List<TalentAssigment> findAllByStartIdOrderByStartDate(Long startId);

    @Query(value = """
        select * from timesheet_talent_assignment tta 
        left join business_flow_administrator bfa on bfa.company_id = tta.company_id
        where tta.id = ?2 and tta.status = 1 and bfa.sales_lead_role in (0,3) and bfa.user_id = ?1
""", nativeQuery = true)
    TalentAssigment findByIdAndAmId(Long amId, Long assignmentId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentGeneralInfoVO(ASS.id,ASS.startDate,ASS.endDate,ASS.startId,TSR.id,ASS.talentId,ASS.type,J.title,C.fullBusinessName,concat(U.firstName,'  ',U.lastName) ,ASS.createdDate,ASS.status,TSR.status) from TalentAssigment ASS " +
            " LEFT JOIN JobV3 J on ASS.jobId = J.id " +
            " LEFT JOIN Company C on ASS.companyId = C.id " +
            " LEFT JOIN User U on U.id = ASS.createdUserId " +
            " LEFT JOIN TimeSheetRecord TSR on TSR.assignmentId = ASS.id and (TSR.status=0 or TSR.status=1 or TSR.status=2 or TSR.status=4 ) WHERE ASS.startId=?1 group by ASS.id order by ASS.status ASC, ASS.endDate DESC ", nativeQuery = false)
    List<AssignmentGeneralInfoVO> findGeneralInfoByStartId(Long startId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentEliminateVO(ASS.id,ASS.startDate,ASS.endDate,ASS.talentId) from TalentAssigment ASS " +
            " WHERE ASS.startId=?1 order by ASS.endDate DESC ")
    List<AssignmentEliminateVO> findCancelEliminateInfoByStartId(Long startId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentActiveVO(ASS.id,ASS.startDate,ASS.endDate,ASS.talentId) from TalentAssigment ASS " +
            " WHERE ASS.talentId=?1 and ASS.status !=2 order by ASS.endDate DESC ")
    List<AssignmentActiveVO> findStartDateAndEndDateByTalentId(Long talentId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentGeneralInfoVO(ASS.id,ASS.startDate,ASS.endDate,ASS.startId,TSR.id,ASS.talentId,ASS.type,J.title,C.fullBusinessName,concat(U.firstName,'  ',U.lastName) ,ASS.createdDate,ASS.status,TSR.status) from TalentAssigment ASS " +
            " LEFT JOIN JobV3 J on ASS.jobId = J.id " +
            " LEFT JOIN Company C on ASS.companyId = C.id " +
            " LEFT JOIN User U on U.id = ASS.createdUserId " +
            " LEFT JOIN TimeSheetRecord TSR on TSR.id = ASS.id  WHERE ASS.startDate <= ?1 and ASS.endDate >= ?1 and ASS.startId = ?2 ", nativeQuery = false)
    AssignmentGeneralInfoVO findByDateIn(LocalDate now, Long startId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentBriefInfoVO(ASS.id,ASS.startId,ASS.workingHours,AL.province,AL.zipCode) from TalentAssigment ASS " +
            " LEFT JOIN AssignmentLocation AL on ASS.id = AL.assignmentId " +
            " WHERE ASS.startDate <= ?1 and ASS.endDate >= ?1 and ASS.startId = ?2 ", nativeQuery = false)
    AssignmentBriefInfoVO findBriefByDateIn(LocalDate now, Long startId);

    @Query(value = " select new com.altomni.apn.jobdiva.service.vo.assignment.AssignmentGeneralInfoVO(ASS.id,ASS.startDate,ASS.endDate,ASS.startId,TSR.id,ASS.talentId,ASS.type,J.title,C.fullBusinessName,concat(U.firstName,'  ',U.lastName) ,ASS.createdDate,ASS.status,TSR.status, ASS.workingHours, ASS.isWeekEnd) from TalentAssigment ASS " +
            " LEFT JOIN JobV3 J on ASS.jobId = J.id " +
            " LEFT JOIN Company C on ASS.companyId = C.id " +
            " LEFT JOIN User U on U.id = ASS.createdUserId " +
            " LEFT JOIN AssignmentTimeSheet ATS on ATS.assignmentId = ASS.id " +
            " LEFT JOIN TimeSheetRecord TSR on TSR.assignmentId = ASS.id and TSR.createdBy <> 'system' " +
            " LEFT JOIN ApproveRecord AR on AR.recordId = TSR.id " +
            " WHERE ASS.id = ?1 group by ASS.id ")
    AssignmentGeneralInfoVO findGeneralInfoById(Long id);

    @Query(value = " select * from timesheet_talent_assignment where start_id = ?1 and type = ?2 order by end_date ASC ", nativeQuery = true)
    List<TalentAssigment> findByType(Long startId, Integer extension);

    @Query(value = " select * from timesheet_talent_assignment tts where tts.start_date >= ?1 and tts.start_date <= ?2 and tts.talent_Id = ?3 and tts.status !=2" +
            " union all " +
            " select * from timesheet_talent_assignment tts where tts.end_date >= ?1 and tts.end_date <= ?2 and tts.talent_Id = ?3 and tts.status !=2" +
            " union all " +
            " select * from timesheet_talent_assignment tts where tts.start_date <= ?1 and tts.end_date >= ?2 and tts.talent_Id = ?3 and tts.status !=2", nativeQuery = true)
    List<TalentAssigment> findByStartDateRangeByTalentId(LocalDate startDate, LocalDate endDate, Long talentId);

    @Query(value = "select * from timesheet_talent_assignment tts where tts.start_date >= ?1 and tts.start_date <= ?2 and tts.talent_Id = ?3 and tts.id <> ?4 and tts.status !=2" +
            " union all " +
            " select * from timesheet_talent_assignment tts where tts.end_date >= ?1 and tts.end_date <= ?2 and tts.talent_Id = ?3 and tts.id <> ?4 and tts.status !=2" +
            " union all " +
            " select * from timesheet_talent_assignment tts where tts.start_date <= ?1 and tts.end_date >= ?2 and tts.talent_Id = ?3 and tts.id <> ?4 and tts.status !=2", nativeQuery = true)
    List<TalentAssigment> findByStartDateRangeByStartIdNotIn(LocalDate startDate, LocalDate endDate, Long talentId, Long assignmentId);

    List<TalentAssigment> findAllByIdIn(List<Long> idList);

    @Query(value = " select distinct tta.id from timesheet_talent_assignment as tta " +
            " inner join business_flow_administrator csla on csla.company_id = tta.company_id " +
            " where csla.user_id = ?1 and csla.sales_lead_role in (0,3) ",nativeQuery = true)
    List<Long> findAssignmentIdsByUserIdForCompanyAM(Long userId);

    @Query(value = " SELECT DISTINCT TTA.id FROM timesheet_talent_assignment TTA " +
            " LEFT JOIN START s ON TTA.start_id = s.id " +
            " LEFT JOIN talent_recruitment_process_kpi_user AC ON ac.talent_recruitment_process_id = s.talent_recruitment_process_id " +
            " WHERE AC.user_role in (1,2) AND AC.user_id = ?1 ", nativeQuery = true)
    List<Long> findAssignmentIdsForApplicationRecruiterOrSourcer(Long userId);


    @Query(value = " select * from timesheet_talent_assignment tta where tta.status = 1 and tta.end_date > now()", nativeQuery = true)
    List<TalentAssigment> findAllAssignmentByStatus(Integer status);

    @Query(value = "select tta.* from timesheet_talent_assignment tta " +
            "left join start s on tta.start_id = s.id " +
            "left join talent_recruitment_process trp on s.talent_recruitment_process_id = trp.id " +
            "where s.talent_recruitment_process_id = ?1 and s.start_type = ?2", nativeQuery = true)
    List<TalentAssigment> findAllByTalentRecruitmentProcessIdAndStartType(Long talentRecruitmentProcessId, Integer startType);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_comments where assignment_id = ?1 ", nativeQuery = true)
    void updateStatusBy(Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " update timesheet_talent_assignment set status = ?2 where id = ?1 ", nativeQuery = true)
    void updateStatusByAssignmentId(Long assignmentId, Integer status);

    @Query(value = """
        SELECT
            distinct tsu.email
        FROM
            company c
            inner join timesheet_talent_assignment tta on tta.company_id = c.id
            inner join time_sheet_user tsu on tsu.uid_prefix = tta.talent_id and tsu.user_type = 0
        WHERE
            c.id in (?1);
""", nativeQuery = true)
    List<String> findEmailByCompanyId(List<Long> companyIdList);


    @Query(value = "select j.id from job j " +
            "inner join job_project jp on jp.id=j.pteam_id " +
            "where j.id in :jobIds", nativeQuery = true)
    Set<Long> findPrivateJobIds(@Param("jobIds") List<Long> jobIds);

    @Query(value = """
    select 
    tta.id assignmentId,
    c.full_business_name companyName,
    j.title jobTitle
    from 
    timesheet_talent_assignment tta 
    inner join company c on c.id = tta.company_id
    inner join job j on j.id = tta.job_id
    where tta.id = ?1
    group by tta.id
    """, nativeQuery = true)
    AssignmentExtraHrVO findAssignmentExtraHrById(Long assignmentId);

    List<TalentAssigment> findAllByJobId(Long jobId);
}
