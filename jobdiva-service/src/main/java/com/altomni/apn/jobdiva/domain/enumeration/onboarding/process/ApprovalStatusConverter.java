package com.altomni.apn.jobdiva.domain.enumeration.onboarding.process;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class ApprovalStatusConverter extends AbstractAttributeConverter<ApprovalStatus, Integer> {
    public ApprovalStatusConverter() {
        super(ApprovalStatus::toDbValue, ApprovalStatus::fromDbValue);
    }
}
