package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingStartProcessEmailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    private String to;

    private List<String> cc;

    private List<String> bcc;

    @NotNull
    private String subject;

    @NotNull
    private String htmlContents;

    private List<String> links;

    private List<MultipartFile> files;

}
