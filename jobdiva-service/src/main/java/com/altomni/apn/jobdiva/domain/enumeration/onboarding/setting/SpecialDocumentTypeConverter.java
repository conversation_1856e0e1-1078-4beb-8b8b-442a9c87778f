package com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class SpecialDocumentTypeConverter extends AbstractAttributeConverter<SpecialDocumentType, Integer> {
    public SpecialDocumentTypeConverter() {
        super(SpecialDocumentType::toDbValue, SpecialDocumentType::fromDbValue);
    }
}
