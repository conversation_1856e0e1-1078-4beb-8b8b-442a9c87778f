package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import com.altomni.apn.jobdiva.domain.timesheet.ApproveRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface ApproveRecordRepository extends JpaRepository<ApproveRecord, Long> {

   @Modifying
   @Transactional
   @Query(value = " DELETE FROM timesheet_approve_record where record_id in (?1) and record_type = ?2 ", nativeQuery = true)
   void deleteAllByRecordIdInAndType(List<Long> recordIdList, Integer recordType);

   ApproveRecord findApproveRecordByRecordIdAndType(Long recordId, CommentsType type);

   List<ApproveRecord> findAllByAssignmentId(Long assignmentId);

   @Modifying
   @Transactional
   @Query(value = " delete from timesheet_approve_record where assignment_id = ?1 ", nativeQuery = true)
   void deleteByAssignmentId(Long assignmentId);

   @Query(value = " select * FROM timesheet_approve_record where assignment_id = ?1 and record_type = ?2 and week_end = ?3 order by id desc limit 1 ", nativeQuery = true)
   ApproveRecord findApproveRecordByWeekEndingDateAndAssignmentIdAndRecordTypeOrderByIdDesc(Long assignmentId, Integer recordType, LocalDate weekEnd);


   @Modifying
   @Transactional
   @Query(value = " update timesheet_approve_record set created_by = ?2 , last_modified_by = ?3, created_date = ?4, last_modified_date = ?5  where id = ?1 ", nativeQuery = true)
   void updateCreateAndModifiedById(Long id, String createdBy, String modifiedBy, Instant createdDate, Instant lastModifiedDate);

   List<ApproveRecord> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
