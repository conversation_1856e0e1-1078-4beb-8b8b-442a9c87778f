package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * A record
 */
@ApiModel(description = "comment for timeSheet")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Data
@Table(name = "timesheet_comments")
public class TimeSheetComments  implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GenericGenerator(name = "RedisIdGenerator",
            strategy = "com.altomni.apn.jobdiva.config.idgenerator.RedisIdGenerator",
            parameters = { @org.hibernate.annotations.Parameter(name = "table", value = "timesheet_comments")})
    @GeneratedValue(generator = "RedisIdGenerator")
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "tenant_id", nullable = false)
    @JsonIgnore
    private Long tenantId;

    @ApiModelProperty(value = "[Internal] uid is used for Spring security to identify user internally. It has the format of <userId>,<tenantId>")
    @Column(name = "talent_id", nullable = false)
    @JsonIgnore
    private Long talentId;

    @Column(name = "assignment_id", nullable = false)
    @JsonIgnore
    private Long assignmentId;

    @ApiModelProperty(value = "comments")
    @Column(name = "comments")
    private String  comments;


    @ApiModelProperty(value = "work date")
    @Column(name = "work_date")
    private LocalDate  workDate;

    @ApiModelProperty(value = "comments type ")
    @Column(name = "comments_type")
    @Convert(converter = CommentsTypeConverter.class)
    private CommentsType commentsType;

    /**
     * 记录的索引
     * 为了在同一个 assignment 同一个 weekending 内，有多个 expense 时，记录不同 expense 的 comments
     */
    @Column(name = "record_index")
    private Integer recordIndex;




}
