package com.altomni.apn.jobdiva.service.vo.invoice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractorInvoiceInitPaymentDetailVO implements Serializable {

    private BigInteger id;

    private BigInteger talentId;

    private String talentName;

    private BigInteger groupInvoiceId;

    private BigDecimal totalAmount;

    private BigInteger paymentId;

    private Timestamp paymentDate;

    private BigDecimal paymentAmount;

    private BigDecimal invoiceAmount;

    private String groupInvoiceStatus;

}
