package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.dto.company.ClientContactDTO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.EmailUtil;
import com.altomni.apn.common.utils.HtmlUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.config.env.ApplicationProperties;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetComments;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetManager;
import com.altomni.apn.jobdiva.repository.timesheet.ExpenseRecordRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetManagerRepository;
import com.altomni.apn.jobdiva.service.assignment.AssignmentSyncToHrService;
import com.altomni.apn.jobdiva.service.company.CompanyService;
import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.RecordSearchDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.SummaryQueryDTO;
import com.altomni.apn.jobdiva.service.job.JobService;
import com.altomni.apn.jobdiva.service.mail.MailService;
import com.altomni.apn.jobdiva.service.rabbitmq.JobdivaToHrRabbitService;
import com.altomni.apn.jobdiva.service.talent.TalentService;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetCommentsService;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetExpenseService;
import com.altomni.apn.jobdiva.service.user.UserService;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO;
import com.altomni.apn.jobdiva.service.vo.assignment.WeekDataVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.EndingDateListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.ExpenseListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.RecordHeadInfoVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import com.altomni.apn.jobdiva.util.CommonAmIdUtil;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.altomni.apn.common.config.constants.Constants.*;
import static com.altomni.apn.common.utils.SqlUtil.PARTITION_COUNT_999;

@Slf4j
@Service("timeSheetExpenseService")
public class TimeSheetExpenseServiceImpl implements TimeSheetExpenseService {

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private JobService jobService;

    @Resource
    private CompanyService companyService;

    @Resource
    private UserService userService;

    @Resource
    private TalentService talentService;

    @Resource
    private TalentAssignmentRepository assigmentRepository;

    @Resource
    private ExpenseRecordRepository recordRepository;

    @Resource
    private TimeSheetCommentsService commentsService;

    @Resource
    private TimeSheetManagerRepository timeSheetManagerRepository;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    private static final String AM_RECEIVE_EMAIL_JUMP_PATH = "/timesheetAM/#/manageTimesheets/ExpensesDetails";

    private static final String CLIENT_RECEIVE_EMAIL_JUMP_PATH = "/clientExpenses/ExpensesDetails";

    @Resource
    private MailService mailService;

    @Resource
    private AssignmentSyncToHrService assignmentSyncToHrService;

    @Resource
    private JobdivaToHrRabbitService jobdivaToHrRabbitService;

    @Override
    public BreakTimeDTO findExpenseRecord(RecordSearchDTO dto,Long talentId) {
        if (dto.getAssignmentId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RECORD_ASSIGNMENTID_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        BreakTimeDTO data = new BreakTimeDTO();
        data.setWeekStart(LocalDate.parse(dto.getStartDate()));
        data.setWeekEnd(LocalDate.parse(dto.getEndDate()));
        //获取完整的周
        LocalDate startDate = LocalDate.parse(dto.getStartDate());
        LocalDate endDate = LocalDate.parse(dto.getEndDate());
        Integer expenseIndex = 0;
        if (dto.getId() != null) {
            ExpenseRecord expenseRecord = recordRepository.findById(dto.getId()).orElseThrow(() -> {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            });
            expenseIndex = expenseRecord.getExpenseIndex();
        }
        data.setExpenseIndex(expenseIndex);
        List<ExpenseRecord> records = recordRepository.findAllByDateAndIndex(startDate, endDate, talentId, dto.getAssignmentId(), expenseIndex);
        //需要获取 weekend 的那天的状态
        records.stream().filter(expenseRecord -> expenseRecord.getWorkDate().isEqual(data.getWeekEnd()))
                .findAny().ifPresent(expenseRecord -> {
            data.setWeekEndingDate(expenseRecord.getWeekEndingDate().toString());
            data.setStatus(expenseRecord.getStatus());
        });
        //返回的时候需要返回完整周数据
        data.setRecords(entityToMap(records, startDate, endDate, dto.getAssignmentId()));
        RecordHeadInfoVO head = recordRepository.findExpenseJobInfoByIndex(dto.getAssignmentId(), data.getWeekEnd(), 0);
        data.setHeadInfo(head);
        return data;
    }

    @Override
    public List<BreakTimeDTO> amFindExpenseRecord(RecordSearchDTO dto, Long talentId) {
        if (dto.getAssignmentId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RECORD_ASSIGNMENTID_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        //获取完整的周
        LocalDate startDate = LocalDate.parse(dto.getStartDate());
        LocalDate endDate = LocalDate.parse(dto.getEndDate());
        List<ExpenseRecord> records = recordRepository.findAllByDate(startDate, endDate, talentId, dto.getAssignmentId());
        // 没有 record 的时候，返回一条带有 head info 的空数据
        if (records.isEmpty()) {
            BreakTimeDTO data = new BreakTimeDTO();
            data.setWeekStart(LocalDate.parse(dto.getStartDate()));
            data.setWeekEnd(LocalDate.parse(dto.getEndDate()));
            RecordHeadInfoVO head = recordRepository.findExpenseJobInfoByIndex(dto.getAssignmentId(), data.getWeekEnd(), 0);
            data.setHeadInfo(head);
            data.setExpenseIndex(0);
            data.setAssignmentId(dto.getAssignmentId());
            return List.of(data);
        }

        Map<Integer, List<ExpenseRecord>> indexedRecords = records.stream().collect(Collectors.groupingBy(ExpenseRecord::getExpenseIndex, Collectors.toList()));

        List<BreakTimeDTO> result = new ArrayList<>();

        indexedRecords.forEach((index, recordList) -> {
            BreakTimeDTO data = new BreakTimeDTO();
            data.setWeekStart(LocalDate.parse(dto.getStartDate()));
            data.setWeekEnd(LocalDate.parse(dto.getEndDate()));
            //需要获取 weekend 的那天的状态
            recordList.stream().filter(expenseRecord -> expenseRecord.getWorkDate().isEqual(data.getWeekEnd()))
                    .findAny().ifPresent(expenseRecord -> {
                        data.setWeekEndingDate(expenseRecord.getWeekEndingDate().toString());
                        data.setStatus(expenseRecord.getStatus());
                    });
            RecordHeadInfoVO head = recordRepository.findExpenseJobInfoByIndex(dto.getAssignmentId(), data.getWeekEnd(), index);
            Optional<String> invoiceNumberByExpenseRecord = TimeSheetUtil.getInvoiceNumberByExpenseRecord(recordList);
            invoiceNumberByExpenseRecord.ifPresent(invoiceNumber -> {
                head.setInvoiceNumber(invoiceNumber);
                data.setInvoiceFlag(true);
            });
            data.setHeadInfo(head);
            data.setExpenseIndex(index);
            data.setAssignmentId(dto.getAssignmentId());
            //返回的时候需要返回完整周数据
            data.setRecords(entityToMap(recordList, startDate, endDate, dto.getAssignmentId()));
            result.add(index, data);
        });

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveExpenseTime(BreakTimeDTO dto, Long talentId) {
        if (CollectionUtils.isEmpty(dto.getRecords())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_APPROVE_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (dto.getAssignmentId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RECORD_ASSIGNMENTID_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        // expense index 大于0时，前面的 expense 必须全部是 approved 状态
        if (dto.getExpenseIndex() > 0) {
            List<ExpenseRecord> records = recordRepository.findAllByEndDateAndIndexLessThan(dto.getWeekStart(), dto.getWeekEnd(), talentId, dto.getAssignmentId(), dto.getExpenseIndex());
            if (records.stream().anyMatch(record -> !record.getStatus().equals(TimeSheetStatus.APPROVED))) {
                //todo zzc 国际化
                throw new CustomParameterizedException("The previous expenses must be approved");
            }
        }
        Long tenantId = SecurityUtils.getTenantId();
        List<Map<String,Object>> breakTime = dto.getRecords();
        List<ExpenseRecord> bList = new ArrayList<>();
        for (Map<String,Object> bDto : breakTime) {
            mapToEntity(bDto,tenantId,talentId,dto, bList);
        }
        Set<LocalDate> saveLocalDateSet = bList.stream().map(ExpenseRecord::getWorkDate).collect(Collectors.toSet());
        Set<LocalDate> localDateSet = TimeSheetUtil.getWeekByWeekEndingDate(dto.getWeekStart(), dto.getWeekEnd());
        recordRepository.deleteAllByWorkDateInAndTalentIdAndAssignmentIdAndExpenseIndex(localDateSet, talentId, dto.getAssignmentId(), dto.getExpenseIndex());
        if (!saveLocalDateSet.contains(dto.getWeekEnd())) {
            ExpenseRecord expenseRecord = bList.get(0);
            ExpenseRecord weekendExpenseRecord = new ExpenseRecord();
            BeanUtil.copyProperties(expenseRecord, weekendExpenseRecord, "id", "workDate", "cost", "s3Key", "weekDay", "lineIndex");
            weekendExpenseRecord.setLineIndex(0);
            weekendExpenseRecord.setExpenseIndex(dto.getExpenseIndex());
            weekendExpenseRecord.setWorkDate(dto.getWeekEnd());
            weekendExpenseRecord.setWeekDay(dto.getWeekEnd().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
            weekendExpenseRecord.setWeekStart(dto.getWeekStart());
            weekendExpenseRecord.setWeekEnd(dto.getWeekEnd());
            weekendExpenseRecord.setWeekEndingDate(LocalDate.parse(dto.getWeekEndingDate()));
            bList.add(weekendExpenseRecord);
        }
        bList = bList.stream().filter(expenseRecord -> !(expenseRecord.getWorkDate().isBefore(dto.getWeekStart()) || expenseRecord.getWorkDate().isAfter(dto.getWeekEnd())))
                .sorted(Comparator.comparing(ExpenseRecord::getWorkDate)).collect(Collectors.toList());
        List<ExpenseRecord> expenseRecordList = recordRepository.saveAll(bList);
        commentsService.save(dto.getComments(), dto.getWeekEnd().toString(), CommentsType.EXPENSE, talentId, dto.getAssignmentId(), dto.getExpenseIndex());
        Integer result = breakTime.size();
        //send email
        if (TimeSheetStatus.APPLIED_APPROVE.equals(dto.getStatus())) {
            sendSaveRecordEmail(expenseRecordList, dto.getAssignmentId(), dto.getWeekStart(), dto.getWeekEnd(), dto.getWeekEndingDate(), talentId);
        }
        return result;
    }

    @Override
    public Integer deleteByLineIndex(RecordSearchDTO dto) {
        if (dto.getStartDate() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (dto.getEndDate() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (dto.getLineIndex() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        Long talentId = SecurityUtils.getUserId();
        return recordRepository.deleteByLine(talentId,dto.getStartDate(),dto.getEndDate(),dto.getLineIndex());
    }

    @Override
    public SummaryDataVO expenseList(SummaryQueryDTO dto, Long talentId) {
        SummaryDataVO vo = new SummaryDataVO();
        vo.setPageNum(dto.getPageNum());
        if (dto.getPageNum()<=0) {
            dto.setPageNum(1);
        }
        int startItem  =(dto.getPageNum()-1)*dto.getPageSize();
        int endItem = dto.getPageSize();
        String oderBy;
        String sort;
        if (dto.getOrder() == null || dto.getOrderBy() == null) {
            oderBy = TimeSheetTableOrderType.WEEK_END.getOrderSql() + TimeSheetTableSortType.DESC.getOrderSql();
            sort = " ";
        } else if (dto.getOrderBy() == TimeSheetTableOrderType.MANAGER) {
            oderBy = TimeSheetTableOrderType.MANAGER_EXPENSE_CLIENT.getOrderSql();
            sort = dto.getOrder().getOrderSql();
        } else {
            oderBy = dto.getOrderBy().getOrderSql();
            sort = dto.getOrder().getOrderSql();
        }
        String recordIdSqlLimit = "";
        if (CollUtil.isNotEmpty(dto.getRecordIds())) {
            StringBuilder sb = new StringBuilder();
            sb.append(" AND ( ");
            Iterator<List<Long>> iterator = CollUtil.split(dto.getRecordIds(), PARTITION_COUNT_999).iterator();
            while (iterator.hasNext()) {
                List<Long> list = iterator.next();
                List<String> idStrList = JSONUtil.toList(JSONUtil.parseArray(list), String.class);
                sb.append(" TER.record_id in ( ").append(String.join(",", idStrList)).append(" ) ");
                if (iterator.hasNext()) {
                    sb.append(" or ");
                }
            }
            sb.append(" ) ");
            recordIdSqlLimit = sb.toString();
        }
        String sql = """
                select AAA.* from (select TER.record_id as id,TER.work_date as ending_date, TER.cost as amount , apr.currency,
                TER.full_name as talent_name,TER.talent_id as talent_id,TER.job_title as job_title,TER.company_name as company_name ,TER.submitted_date as applied_date,
                TER.approved_date approved_date, TER.status as status,TER.start_date as start_date,TER.end_date as end_date,TER.job_id as job_id,TER.manager as manager,TER.primary_manager as primary_manager ,
                TER.am_approver_id, TER.am_approver as am,TER.assignment_id as assignment_id, TER.expense_type as expense_type, TER.week_start, TER.week_end, TER.week_ending_date, TER.expense_index
                from time_sheet_expense_week_ending_record TER
                LEFT JOIN assignment_pay_rate apr ON apr.assignment_id = TER.assignment_id and apr.content_type = 1 AND apr.pay_type = 3
                where TER.assignment_status = 1 and TER.talent_id=?3 and TER.allow_submit_expense = true
                """ + recordIdSqlLimit + " ) AAA " + oderBy+sort + ", id desc " + "  limit ?1,?2";
        String countSql = """
                select count(TER.record_id)
                from time_sheet_expense_week_ending_record TER
                where TER.assignment_status = 1 and TER.talent_id=?1 and TER.allow_submit_expense = true
                """ + recordIdSqlLimit;
        try {
            Query query = entityManager.createNativeQuery(countSql);
            query.setParameter(1,talentId);
            List<Object> totalItems = query.getResultList();
            vo.setTotalItems(((BigInteger)totalItems.get(0)).intValue());
            query = entityManager.createNativeQuery(sql, ExpenseListVO.class);
            query.setParameter(1,startItem);
            query.setParameter(2,endItem);
            query.setParameter(3,talentId);
            List<ExpenseListVO> result = query.getResultList();
            if (CollectionUtils.isNotEmpty(result)) {
                List<Long> combinedIds = CommonAmIdUtil.getCombinedIds(result, null, ExpenseListVO::getAmApproverId);
                Map<Long, String> userIdToNameMap = CommonAmIdUtil.createUserIdToNameMap(combinedIds, userService);
                CommonAmIdUtil.mapNamesToObjects(result, null, ExpenseListVO::getAmApproverId, null, ExpenseListVO::setAm, userIdToNameMap);
                List<EnumCurrency> enumCurrencyList = enumCurrencyService.findAllEnumCurrency();
                Map<Integer, EnumCurrency> currencyMap = enumCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, a -> a));
                result = result.stream().peek(expenseListVO -> {
                    String mr = expenseListVO.getAm();
                    if (StringUtils.isBlank(mr)) {
                        mr = expenseListVO.getManager();
                    }
                    if (StringUtils.isBlank(mr)) {
                        mr = expenseListVO.getPrimaryManager();
                    }
                    expenseListVO.setManager(mr);
                    expenseListVO.setAmountFormat(currencyMap.get(expenseListVO.getCurrency()).getLabel1() + new BigDecimal(String.valueOf(expenseListVO.getAmount() == null? 0F: expenseListVO.getAmount())).setScale(2));
                }).collect(Collectors.toList());
            }
            vo.setData(result);
        } catch (Exception e) {
            log.error("Exception occurred in expenseList query", e);
        }
        return vo;
    }

    @Override
    public List<EndingDateListVO> weekEndingDates(Long talentId, boolean isAm) {
        List<AssignmentVO> assignments;
        if (BooleanUtil.isTrue(isAm)) {
            if (SecurityUtils.isTimesheetAdmin() || SecurityUtils.isAdmin()) {
                assignments = assigmentRepository.findAssigmentInfoByTalentId(talentId);
            } else {
                assignments = assigmentRepository.findAllByTalentIdAndAmId(talentId, SecurityUtils.getUserId());
            }
        } else {
            assignments = assigmentRepository.findAssigmentInfoByTalentId(talentId);
        }
        if (CollectionUtils.isEmpty(assignments)) {return null;}
        List<EndingDateListVO> result = new LinkedList<>();
        LocalDate now = LocalDate.now();
        for (AssignmentVO assigment : assignments) {
            if (now.compareTo(assigment.getStartDate()) < 0) {
                continue;
            }
            if (!assigment.getAllowSubmitExpense() && !isAm) {
                continue;
            }
            EndingDateListVO vo  = new EndingDateListVO();
            result.add(vo);
            JobDTOV3 jobV3 = jobService.findById(assigment.getJobId()).getBody();
            vo.setJobId(jobV3.getId());
            vo.setJobTitle(jobV3.getTitle());
            vo.setCompanyName(companyService.getCompany(assigment.getCompanyId()).getBody().getName());
            vo.setAssignmentId(assigment.getId());
            vo.setStartDate(assigment.getStartDate());
            vo.setEndDate(assigment.getEndDate());
            vo.setIsExcept(assigment.getIsExcept());
            AssignmentVO assignmentVO = assigmentRepository.findAssigmentInfoByAssignmentId(assigment.getId());
            List<WeekDataVO> weekDataVOList = TimeSheetUtil.getWeekEnding(assigment.getStartDate(), assigment.getEndDate(), assigment.getWeekEnding(), assignmentVO.getIsWeekEnd(), assignmentVO.getFrequency());
            weekDataVOList = weekDataVOList.stream().filter(weekDataVO -> weekDataVO.getWorkDate().isEqual(weekDataVO.getWeekEnd()))
                    .sorted(Comparator.comparing(WeekDataVO::getWeekEnd).reversed()).toList();
            vo.setDates(weekDataVOList);
        }
        return result;
    }

    @Override
    public BreakTimeDTO findRecordById(RecordSearchDTO dto) {
        if (dto.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        BreakTimeDTO bDto = new BreakTimeDTO();
        bDto.setWeekStart(LocalDate.parse(dto.getStartDate()));
        bDto.setWeekEnd(LocalDate.parse(dto.getEndDate()));
        ExpenseRecord expenseRecord = recordRepository.findById(dto.getId()).orElseThrow(() -> {throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));});
        LocalDate startDate = LocalDate.parse(dto.getStartDate());
        LocalDate endDate = LocalDate.parse(dto.getEndDate());
        List<ExpenseRecord> records = recordRepository.findAllByDateAndIndex(startDate, endDate, expenseRecord.getTalentId(), expenseRecord.getAssignmentId(), expenseRecord.getExpenseIndex());
        if (CollectionUtils.isNotEmpty(records)) {
            bDto.setRecords(entityToMap(records, startDate, endDate, expenseRecord.getAssignmentId()));
            bDto.setWeekEndingDate(expenseRecord.getWeekEndingDate().toString());
            bDto.setStatus(expenseRecord.getStatus());
        }
        TimeSheetComments comments = commentsService.findByWorkDateAndType(expenseRecord.getWorkDate().toString(), CommentsType.EXPENSE, expenseRecord.getTalentId(), expenseRecord.getAssignmentId(), expenseRecord.getExpenseIndex());
        if (comments != null) {
            bDto.setComments(comments.getComments());
        }
        return bDto;
    }

    public void sendSaveRecordEmail(List<ExpenseRecord> result, Long assignmentId, LocalDate weekStart, LocalDate weekEnd, String weekEndingDate, Long talentId) {
        if (ObjectUtil.isNull(assignmentId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RECORD_ASSIGNMENTID_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        List<Long> clientContactIds = new ArrayList<>();
        List<Long> amIds = new ArrayList<>();
        List<String> amEmails = new ArrayList<>();
        List<String> clientContactEmails = new ArrayList<>();
        //get link in email condition
        String startDate = weekStart.toString();
        AtomicReference<Long> recordId = new AtomicReference<>();
        if (CollUtil.isNotEmpty(result)) {
            result.stream().filter(tsr -> tsr.getWorkDate().isEqual(weekEnd)).findFirst().ifPresent(expenseRecord -> recordId.set(expenseRecord.getId()));
        }
        String talentFullName = talentService.getTalentWithEntity(talentId).getBody().getFullName();
        List<TimeSheetManager> managerList = timeSheetManagerRepository.findByAssignmentId(assignmentId);
        if(CollUtil.isNotEmpty(managerList)){
            managerList.forEach(m -> {
                if (ManagerRoleType.AM.equals(m.getRole())) {
                    amIds.add(m.getClientId());
                } else if (ManagerRoleType.PRIMARY_CLIENT.equals(m.getRole()) || ManagerRoleType.NORMAL_CLIENT.equals(m.getRole())) {
                    clientContactIds.add(m.getClientId());
                }
            });
        }
        //send email to AM
        if (CollUtil.isNotEmpty(amIds)) {
            List<UserBriefDTO> amUsers = userService.getBriefUsersByIds(amIds).getBody();
            if (CollUtil.isNotEmpty(amUsers)) {
                amEmails = amUsers.stream().map(UserBriefDTO::getEmail).toList();
            }
        }
        if (CollUtil.isNotEmpty(amEmails)) {
            sendEmail(applicationProperties.getBaseUrl() + AM_RECEIVE_EMAIL_JUMP_PATH, startDate, weekEnd.toString(), recordId.get(), amEmails, weekEndingDate, talentFullName);
        }
        //send email to client
        if (CollUtil.isNotEmpty(clientContactIds)) {
            List<ClientContactDTO> clientContactList = companyService.findBriefContactByIdAndReceiveEmail(clientContactIds).getBody();
            if (CollUtil.isNotEmpty(clientContactList)) {
                clientContactEmails = clientContactList.stream().map(ClientContactDTO::getEmail).collect(Collectors.toList());
            }
        }
        if (CollUtil.isNotEmpty(clientContactEmails)) {
            sendEmail(applicationProperties.getJobDivaUrl() + CLIENT_RECEIVE_EMAIL_JUMP_PATH, startDate, weekEnd.toString(), recordId.get(), clientContactEmails, weekEndingDate, talentFullName);
        }
    }

    public void sendEmail(String jumpPath, String startDate, String endDate, Long recordId, List<String> emails, String weekEndingDate, String talentFullName) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                String subject = "Expense Notification";
                String url = jumpPath + "?startDate=" + startDate + "&endDate=" + endDate + "&id=" + recordId + "&endingDate=" + weekEndingDate + "&weekStart=" + startDate+ "&weekEnd=" + endDate;
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                HtmlUtil.appendParagraphCell(sb, talentFullName + " has entered an expense for the week-ending date");
                HtmlUtil.appendParagraphCell(sb, endDate);
                HtmlUtil.appendParagraphCell(sb, "Please click <a href=" + url + ">here</a> to view this entry.");
                HtmlUtil.appendParagraphCell(sb, "Links:");
                HtmlUtil.appendParagraphCell(sb, "------");
                HtmlUtil.appendParagraphCell(sb, url);
                sb.append("</body>");
                mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, sb.toString(), null, true));
            }
        });
    }

    private List<Map<String,Object>> entityToMap(List<ExpenseRecord> records, LocalDate startDate, LocalDate endDate, Long assignmentId) {
        List<Map<String,Object>> result = new LinkedList<>();
        if (CollectionUtils.isEmpty(records)) {
            return result;
        }
        AssignmentVO assignmentVO = assigmentRepository.findAssigmentInfoByAssignmentId(assignmentId);
        LocalDate allWeekStartDate = TimeSheetUtil.findDateInWeek(startDate, assignmentVO.getWeekEnding(), 2);
        LocalDate allWeekEndDate = TimeSheetUtil.findDateInWeek(endDate, assignmentVO.getWeekEnding(), 1);
        Set<Integer> indexAllSet = records.stream().map(ExpenseRecord::getLineIndex).collect(Collectors.toSet());
        Set<LocalDate> localDateSet = TimeSheetUtil.getWeekByWeekEndingDate(allWeekStartDate, allWeekEndDate);
        Set<LocalDate> ascLocalDateSet = new TreeSet<>(localDateSet);
        Map<Integer, List<ExpenseRecord>> indexMap = records.stream().collect(Collectors.groupingBy(ExpenseRecord::getLineIndex));
        indexAllSet.forEach(index -> {
            List<ExpenseRecord> expenseRecordList = indexMap.get(index);
            ascLocalDateSet.forEach(localDate -> {
                if (expenseRecordList.stream().map(ExpenseRecord::getWorkDate).collect(Collectors.toList()).stream()
                        .noneMatch(date -> Objects.equals(localDate, date))) {
                    ExpenseRecord expenseRecord = expenseRecordList.get(0);
                    setFillExpenseRecord(records, localDate, expenseRecord);
                }
            });
        });

        Map<Integer,List<ExpenseRecord>> dateMap =  records.stream().collect(Collectors.groupingBy(ExpenseRecord::getLineIndex));
        Set<Integer> indexSet = dateMap.keySet();
        Set<Integer> sortSet = new TreeSet<>(Comparator.comparingInt(o -> o));
        sortSet.addAll(indexSet);
        String cost;
        for (Integer index:sortSet) {
            Map<String,Object> map  = new HashMap<>(16);
            List<ExpenseRecord> cell = dateMap.get(index);
            for (ExpenseRecord timeRecord : cell) {
                map.put(ROW_INDEX, index);
                map.put(EXPENSE_TYPE,timeRecord.getExpenseType());
                if (timeRecord.getCost() != null && timeRecord.getCost()>0) {
                    cost = timeRecord.getCost() + DATE_WEEK_SEPERTATEOR + timeRecord.getS3Key();
                } else {
                    cost = null;
                }
                map.put(timeRecord.getWeekDay() + DATE_WEEK_SEPERTATEOR + timeRecord.getWorkDate(),cost);
            }
            result.add(map);
        }
        return result;
    }

    private void mapToEntity(Map<String, Object> map, Long tenantId, Long talentId, BreakTimeDTO dto, List<ExpenseRecord> bList) {
        ExpenseType type= ExpenseType.valueOf((String)map.get(EXPENSE_TYPE));
        Integer index = (Integer) map.get(ROW_INDEX);
        map.remove(ROW_INDEX);
        map.remove(EXPENSE_TYPE);
        List<ExpenseRecord> records = new LinkedList<>();
        Instant instant = Instant.now();
        boolean isSubmit = dto.getStatus() == TimeSheetStatus.APPLIED_APPROVE;
        for (Map.Entry<String,Object> entry:map.entrySet()) {
            ExpenseRecord record = new ExpenseRecord();
            record.setTenantId(tenantId);
            record.setTalentId(talentId);
            record.setAssignmentId(dto.getAssignmentId());
            String[] dateWeek = entry.getKey().split(DATE_WEEK_SEPERTATEOR);
            record.setWeekDay(dateWeek[0]);
            if (entry.getValue() != null) {
                String value =  (String)entry.getValue();
                String[] costLink = value.split(DATE_WEEK_SEPERTATEOR);
                Float cost = Float.valueOf(costLink[0]);
                String slink = costLink[1];
                record.setCost(cost);
                record.setS3Key(slink);
            }
            LocalDate date = LocalDate.parse(dateWeek[1]);
            record.setWorkDate(date);
            record.setStatus(dto.getStatus());
            record.setLineIndex(index);
            record.setExpenseIndex(dto.getExpenseIndex());
            record.setExpenseType(type);
            record.setWeekStart(dto.getWeekStart());
            record.setWeekEnd(dto.getWeekEnd());
            record.setWeekEndingDate(LocalDate.parse(dto.getWeekEndingDate()));
            if(isSubmit) {
                record.setSubmittedDate(instant);
            }
            records.add(record);
        }
        bList.addAll(records);
    }

    private void setFillExpenseRecord(List<ExpenseRecord> records, LocalDate localDate, ExpenseRecord expenseMaxRecord) {
        ExpenseRecord expenseRecord = new ExpenseRecord();
        BeanUtil.copyProperties(expenseMaxRecord, expenseRecord, "id", "workDate", "cost", "s3Key", "weekDay");
        expenseRecord.setWorkDate(localDate);
        expenseRecord.setWeekDay(localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
        expenseRecord.setLineIndex(expenseMaxRecord.getLineIndex());
        expenseRecord.setExpenseType(expenseMaxRecord.getExpenseType());
        records.add(expenseRecord);
    }


}
