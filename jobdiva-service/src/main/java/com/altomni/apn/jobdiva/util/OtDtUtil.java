package com.altomni.apn.jobdiva.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.jobdiva.CalculateMethodType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetBreakTimeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SpringUtil;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetBreakTimeRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetBreakTimeRepository;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.altomni.apn.common.domain.enumeration.jobdiva.CalculateMethodType.CANADA_SK_8;


/**
 * OT,DT Util
 * <AUTHOR>
 */
@UtilityClass
public class OtDtUtil {

    /** default Federal workweek */
    private final BigDecimal DEFAULT_HRS_A_WORKWEEK_40 = new BigDecimal("40");

    private final BigDecimal DEFAULT_HRS_A_WORKWEEK_44 = new BigDecimal("44");

    private final BigDecimal DEFAULT_HRS_A_WORKWEEK_48 = new BigDecimal("48");
    /**default value*/
    private final BigDecimal OT_8_DEFAULT_TIME = new BigDecimal("8");

    private final BigDecimal OT_10_DEFAULT_TIME = new BigDecimal("10");
    private final BigDecimal DT_DEFAULT_TIME = new BigDecimal("12");
    private final Integer SUNDAY_COUNT = 7;
    private final Integer SATURDAY_COUNT = 6;
    private final String END_OF_DAY = "23:59";
    private final String BEGIN_OF_DAY = "00:00";
    private final Float DEFAULT_HOURS_ZERO = 0F;
    private final String DEFAULT_SYSTEM = "system";

    private volatile TimeSheetBreakTimeRepository timeSheetBreakTimeRepository;

    private void getTimeSheetBreakTimeRepository() {
        if (timeSheetBreakTimeRepository == null) {
            synchronized (OtDtUtil.class) {
                if (timeSheetBreakTimeRepository == null) {
                    timeSheetBreakTimeRepository = SpringUtil.getBean(TimeSheetBreakTimeRepository.class);
                }
            }
        }
    }

    static {
        getTimeSheetBreakTimeRepository();
    }
    /**
     * calculate Ot And Dt By TimeSheetType and CalculateMethodType
     * @param recordList  time sheet record
     * @param timeSheetType  day;hour;am_pm
     * @param calculateMethodType  FEDERAL;CALIFORNIA...
     */
    public void calculateOtDtByLocationAndTimeSheetType(List<TimeSheetRecord> recordList, TimeSheetType timeSheetType, CalculateMethodType calculateMethodType) {
        recordList = recordList.stream().filter(timeSheetRecord -> !Objects.equals(timeSheetRecord.getCreatedBy(), DEFAULT_SYSTEM)).toList();
        if (CollUtil.isEmpty(recordList)) {
            return;
        }
        recordList = recordList.stream().sorted(Comparator.comparing(TimeSheetRecord::getWorkDate)).toList();
        switch (timeSheetType) {
            case WEEK_HOUR, WEEK_AM_PM -> calculateOtDtByLocation(recordList, calculateMethodType);
            default -> {
            }
            //no have OT DT
        }
    }


    /**
     * calculate Ot And Dt By CalculateMethodType
     * @param recordList           time sheet record
     * @param calculateMethodType  FEDERAL;CALIFORNIA...
     */
    private void calculateOtDtByLocation(List<TimeSheetRecord> recordList, CalculateMethodType calculateMethodType) {
        switch (calculateMethodType) {
            case CALIFORNIA -> calculateByCalifornia(recordList);
            case COLORADO -> calculateByColorado(recordList);
            case KENTUCKY -> calculateByKentucky(recordList);
            case US_VIRGIN_ISLAND -> calculateByUsVirginIsland(recordList);
            case PUERTO_RICO -> calculateByPuertoRico(recordList);
            case CANADA_BC -> calculateByWeekHours40AndDayHoursWithOtAndDt(recordList);
            case CANADA_ON, CANADA_NB -> calculateByWeekHours44(recordList);
            case CANADA_AB -> calculateByWeekHours44AndDayHoursOnlyOt(recordList);
            case CANADA_MB, CANADA_NT, CANADA_NU, CANADA_FEDERAL, CANADA_YT -> calculateByWeekHours40AndDayHoursOnlyOt(recordList);
            case CANADA_NS, CANADA_PE -> calculateByWeekHours48(recordList);
            case CANADA_SK_8 -> calculateByWeekHoursAndDayHours(recordList, DEFAULT_HRS_A_WORKWEEK_40, false, true, OT_8_DEFAULT_TIME);
            case CANADA_SK_10 -> calculateByWeekHoursAndDayHours(recordList, DEFAULT_HRS_A_WORKWEEK_40, false, true, OT_10_DEFAULT_TIME);
            default ->
                //美国通用算法,canada_qc算法, canada_nl
                    calculateByWeekHours40(recordList);
        }
    }

    /**
     * 加拿大 sk 省份的otdt计算
     * 2个规则
     * 1.24小时内工作时间超过8/10 小时
     * 2.每周工作时长超过40小时这为rt, 超过部分为ot
     * @param recordList
     */
    private void calculateByFirstTimesheet24Hours(List<TimeSheetRecord> recordList, CalculateMethodType calculateMethodType) {
        if (CollUtil.isEmpty(recordList)) {return;}
        TimeSheetRecord timeSheetRecord = recordList.get(0);
        BigDecimal otStandardHours = calculateMethodType == CANADA_SK_8? OT_8_DEFAULT_TIME: OT_10_DEFAULT_TIME;
        //获取所有的breakTime 数据
        LocalDate endDate = timeSheetRecord.getWeekEndingDate();
        LocalDate startDate = endDate.minusDays(6);
        List<TimeSheetBreakTimeRecord> breakTimeRecordList = timeSheetBreakTimeRepository.findAllByDate(startDate, endDate, timeSheetRecord.getTalentId(), timeSheetRecord.getAssignmentId());
        if (CollUtil.isEmpty(breakTimeRecordList)) {
            //在不存在breakTimeList 的时候作为不同的计算方式
            calculateByWeekHoursAndDayHours(recordList, DEFAULT_HRS_A_WORKWEEK_40, false, true, otStandardHours);
            return;
        }
        Map<LocalDate, List<TimeSheetBreakTimeRecord>> workDateBreakTimeListMap = breakTimeRecordList.stream().collect(Collectors.groupingBy(TimeSheetBreakTimeRecord::getWorkDate));
        Set<LocalDate> localDateSet = TimeSheetUtil.getWeekByWeekEndingDate(startDate, endDate);
        localDateSet = localDateSet.stream().sorted(LocalDate::compareTo).collect(Collectors.toCollection(LinkedHashSet::new));
        List<TimeSheetBreakTimeRecord> firstTimeSheetBreakTimeRecordList = null;
        LocalDate currentLocalDate = localDateSet.stream().findFirst().orElseThrow();
        for (LocalDate localDate : localDateSet) {
            firstTimeSheetBreakTimeRecordList = workDateBreakTimeListMap.get(localDate);
            if (CollUtil.isNotEmpty(firstTimeSheetBreakTimeRecordList)) {break;}
        }
        if (CollUtil.isEmpty(firstTimeSheetBreakTimeRecordList)) {return;}
        //获取到第一次打卡记录来确定24小的的开始时间
        TimeSheetBreakTimeRecord firstBreakTimeRecord = firstTimeSheetBreakTimeRecordList.stream().filter(
                timeSheetBreakTimeRecord -> Objects.equals(timeSheetBreakTimeRecord.getBreakTimeType(), TimeSheetBreakTimeType.TIME_IN)).findFirst().orElse(null);
        if (firstBreakTimeRecord == null) {return;}
        //整周的第一次打卡记录,作为24小时时间的开始节点
        String firstTimeStr = firstBreakTimeRecord.getTime();
        LocalTime firstTime = LocalTime.parse(firstTimeStr);
        if (Objects.equals(firstTimeStr, END_OF_DAY) || Objects.equals(firstTimeStr, BEGIN_OF_DAY)) {
            //按照普通的算法计算,打卡时间是24点即为0点
            calculateByWeekHoursAndDayHours(recordList, DEFAULT_HRS_A_WORKWEEK_40, false, true, otStandardHours);
            return;
        }
        //用来记录上一次
        AtomicReference<LocalDate> lastTime = new AtomicReference<>(currentLocalDate.minusDays(1));
        //前一天24小时工作时长
        AtomicReference<Long> yesterdayWorkMinutes = new AtomicReference<>(0L);
        //总正常工作时长
        AtomicReference<Long> sumRegularMinutes = new AtomicReference<>(0L);
        //当天rt
        AtomicReference<Long> dayRtMinutes = new AtomicReference<>(0L);
        //当天ot
        AtomicReference<Long> dayOtMinutes = new AtomicReference<>(0L);
        //当天的昨天24小时工作时长
        AtomicReference<Long> currentDayYesterday24Minutes = new AtomicReference<>(0L);
        //当天的24小时工作时长
        AtomicReference<Long> currentDay24Minutes = new AtomicReference<>(0L);
        //是不是23:59分
        AtomicReference<Boolean> is2359Flag = new AtomicReference<>(false);
        recordList.forEach(timeSheet -> {
            if (!lastTime.get().plusDays(1).isEqual(timeSheet.getWorkDate())) {
                //不连续的情况下
                yesterdayWorkMinutes.set(0L);
            }
            lastTime.set(timeSheet.getWorkDate());
            //初始化数据
            dayRtMinutes.set(0L);
            dayOtMinutes.set(0L);
            currentDay24Minutes.set(0L);
            currentDayYesterday24Minutes.set(0L);
            is2359Flag.set(false);
            List<TimeSheetBreakTimeRecord> timeSheetBreakTimeRecordList = workDateBreakTimeListMap.get(timeSheet.getWorkDate());
            //没有打卡记录结束
            if (CollUtil.isEmpty(timeSheetBreakTimeRecordList)) {return;}
            //上班下班时间
            TimeSheetBreakTimeRecord timeInRecord = timeSheetBreakTimeRecordList.stream().filter(breakTime -> breakTime.getBreakTimeType().equals(TimeSheetBreakTimeType.TIME_IN)).findFirst().orElseThrow();
            TimeSheetBreakTimeRecord timeOutRecord = timeSheetBreakTimeRecordList.stream().filter(breakTime -> breakTime.getBreakTimeType().equals(TimeSheetBreakTimeType.TIME_OUT)).findFirst().orElseThrow();
            List<TimeSheetBreakTimeRecord> breakTimeRecordOutList = timeSheetBreakTimeRecordList.stream().filter(breakTime -> StrUtil.isNotBlank(breakTime.getTime()) && breakTime.getBreakTimeType().equals(TimeSheetBreakTimeType.MEAL_BREAK_OUT))
                    .sorted(Comparator.comparing(breakTime -> LocalTime.parse(breakTime.getTime()))).toList();
            List<TimeSheetBreakTimeRecord> breakTimeRecordInList = timeSheetBreakTimeRecordList.stream().filter(breakTime -> StrUtil.isNotBlank(breakTime.getTime()) && breakTime.getBreakTimeType().equals(TimeSheetBreakTimeType.MEAL_BREAK_IN))
                    .sorted(Comparator.comparing(breakTime -> LocalTime.parse(breakTime.getTime()))).toList();
            LocalTime timeIn = LocalTime.parse(timeInRecord.getTime());
            LocalTime timeOut = LocalTime.parse(timeOutRecord.getTime());
            if (Objects.equals(timeOutRecord.getTime(), END_OF_DAY)) {
                is2359Flag.set(true);
            }
            //第一次的打卡时间在当天上班之后
            if (firstTime.isAfter(timeIn)) {
                Duration duration;
                Duration nowDuration = null;
                if (firstTime.isAfter(timeOut)) {
                    duration = Duration.between(timeIn, timeOut);
                    duration = is2359Flag.get()? duration.plus(1, ChronoUnit.MINUTES): duration;
                } else {
                    duration = Duration.between(timeIn, firstTime);
                    nowDuration = Duration.between(firstTime, timeOut);
                    nowDuration = is2359Flag.get()? nowDuration.plus(1, ChronoUnit.MINUTES): nowDuration;
                }
                if (CollUtil.isEmpty(breakTimeRecordOutList) || CollUtil.isEmpty(breakTimeRecordInList)) {
                    //没有中途出去的数据 2.种处理方式
                    long lastMinutes = duration.toMinutes();
                    //24小时内,属于当天的 ot minutes
                    long lastDay24OtMinutes = lastMinutes - (Math.max(otStandardHours.longValue() * 60L - yesterdayWorkMinutes.get(), 0L));
                    //一周内,超过40小时的 ot minutes
                    long lastWeekMore40HoursMinutes = lastMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get(), 0L));
                    long lastOtMinutes = Math.max(Math.max(lastDay24OtMinutes, lastWeekMore40HoursMinutes), 0L);
                    long lastRtMinutes = lastMinutes - lastOtMinutes;
                    dayOtMinutes.set(dayOtMinutes.get() + lastOtMinutes);
                    dayRtMinutes.set(dayRtMinutes.get() + lastRtMinutes);
                    if (nowDuration != null) {
                        //今天24小时的分钟数
                        long nowMinutes = nowDuration.toMinutes();
                        long nowDay24OtMinutes = nowMinutes - (otStandardHours.longValue() * 60L);
                        long weekMore40HoursMinutes = nowMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get() - lastRtMinutes, 0L));
                        long otMinutes = Math.max(Math.max(nowDay24OtMinutes, weekMore40HoursMinutes), 0L);
                        long rtMinutes = nowMinutes - otMinutes;
                        dayOtMinutes.set(dayOtMinutes.get() + otMinutes);
                        dayRtMinutes.set(dayRtMinutes.get() + rtMinutes);
                        currentDay24Minutes.set(currentDay24Minutes.get() + nowMinutes);
                    }
                } else {
                    if (breakTimeRecordOutList.size() != breakTimeRecordInList.size()) {
                        throw new CustomParameterizedException("date is error");
                    }
                    LocalTime currentBeginLocationTime = timeIn;
                    LocalTime bewteenTime;
                    for (int i = 0; i < breakTimeRecordOutList.size(); i++) {
                        TimeSheetBreakTimeRecord breakTimeOut = breakTimeRecordOutList.get(i);
                        TimeSheetBreakTimeRecord breakTimeIn = breakTimeRecordInList.get(i);
                        LocalTime breakTimeOutTime = LocalTime.parse(breakTimeOut.getTime());
                        LocalTime breakTimeInTime = LocalTime.parse(breakTimeIn.getTime());
                        bewteenTime = firstTime.isAfter(currentBeginLocationTime) && firstTime.isBefore(breakTimeOutTime)? firstTime: breakTimeOutTime;
                        //判断firstTime 在breakTimeOutTime之后, breakTimeInTime之前
                        if (firstTime.isBefore(breakTimeOutTime)) {
                            //计算之前的24小时内的工作时长
                            if (firstTime.isAfter(currentBeginLocationTime)) {
                                Duration last24Duration = Duration.between(currentBeginLocationTime, bewteenTime);
                                //中途需要减去的时间
                                long lastMinutes = last24Duration.toMinutes();
                                //24小时内,属于当天的 ot minutes   链接昨天的24小的的时间 - (8/10 小时  - 昨天已经工作时长  - 今天所属的昨天的工作时长)
                                long lastDay24OtMinutes = lastMinutes - Math.max(otStandardHours.longValue() * 60L - yesterdayWorkMinutes.get() - currentDayYesterday24Minutes.get(), 0);
                                currentDayYesterday24Minutes.set(currentDayYesterday24Minutes.get() + lastMinutes);
                                //一周内,超过40小时的 ot minutes
                                long lastWeekMore40HoursMinutes = lastMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get(), 0L));
                                long lastOtMinutes = Math.max(Math.max(lastDay24OtMinutes, lastWeekMore40HoursMinutes), 0L);
                                long lastRtMinutes = lastMinutes - lastOtMinutes;
                                dayOtMinutes.set(dayOtMinutes.get() + lastOtMinutes);
                                dayRtMinutes.set(dayRtMinutes.get() + lastRtMinutes);
                                //今天24小时的分钟数
                                Duration current24Duration = Duration.between(bewteenTime, breakTimeOutTime);
                                long nowMinutes = current24Duration.toMinutes();
                                long nowDay24OtMinutes = nowMinutes - (Math.max(otStandardHours.longValue() * 60L - currentDay24Minutes.get(), 0));
                                long weekMore40HoursMinutes = nowMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get() - dayRtMinutes.get(), 0L));
                                long otMinutes = Math.max(Math.max(nowDay24OtMinutes, weekMore40HoursMinutes), 0L);
                                dayOtMinutes.set(dayOtMinutes.get() + otMinutes);
                                long rtMinutes = nowMinutes - otMinutes;
                                dayRtMinutes.set(dayRtMinutes.get() + rtMinutes);
                                currentDay24Minutes.set(currentDay24Minutes.get() + nowMinutes);
                            } else {
                                //今天24小时的分钟数
                                Duration current24Duration = Duration.between(currentBeginLocationTime, breakTimeOutTime);
                                long nowMinutes = current24Duration.toMinutes();
                                long nowDay24OtMinutes = nowMinutes - (Math.max(otStandardHours.longValue() * 60L - currentDay24Minutes.get(), 0));
                                long weekMore40HoursMinutes = nowMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get() - dayRtMinutes.get(), 0L));
                                long otMinutes = Math.max(Math.max(nowDay24OtMinutes, weekMore40HoursMinutes), 0L);
                                dayOtMinutes.set(dayOtMinutes.get() + otMinutes);
                                long rtMinutes = nowMinutes - otMinutes;
                                dayRtMinutes.set(dayRtMinutes.get() + rtMinutes);
                                currentDay24Minutes.set(currentDay24Minutes.get() + nowMinutes);
                            }
                        } else if (firstTime.isAfter(breakTimeOutTime)) {
                            //firstTime 在breakTimeOutTime之后
                            long lastMinutes;
                            if (firstTime.isBefore(breakTimeInTime)) {
                                //在first time 在回来之前则,上班时间是 begin到out的时间
                                Duration timeInToFirstDuration = Duration.between(currentBeginLocationTime, breakTimeOutTime);
                                lastMinutes = timeInToFirstDuration.toMinutes();
                            } else {
                                //在breakTimeInTime 之后
                                Duration timeInToFirstDuration = Duration.between(currentBeginLocationTime, bewteenTime);
                                Duration firstToOutDuration = Duration.between(bewteenTime, breakTimeOutTime);
                                lastMinutes = timeInToFirstDuration.toMinutes() + firstToOutDuration.toMinutes();
                            }
                            //全部是yesterday minutes
                            long lastDay24OtMinutes = lastMinutes - (Math.max(otStandardHours.longValue() * 60L - yesterdayWorkMinutes.get() - currentDayYesterday24Minutes.get(), 0L));
                            currentDayYesterday24Minutes.set(currentDayYesterday24Minutes.get() + lastMinutes);
                            long lastWeekMore40HoursMinutes = lastMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get() - dayRtMinutes.get(), 0L));
                            long lastOtMinutes = Math.max(Math.max(lastDay24OtMinutes, lastWeekMore40HoursMinutes), 0L);
                            long lastRtMinutes = lastMinutes - lastOtMinutes;
                            dayOtMinutes.set(dayOtMinutes.get() + lastOtMinutes);
                            dayRtMinutes.set(dayRtMinutes.get() + lastRtMinutes);
                        }
                        //重置开始时间
                        currentBeginLocationTime = breakTimeInTime;
                    }
                    if (firstTime.isBefore(currentBeginLocationTime)) {
                        //剩下的全部是今天的
                        Duration currentDayDuration = Duration.between(currentBeginLocationTime, timeOut);
                        currentDayDuration = is2359Flag.get()? currentDayDuration.plus(1, ChronoUnit.MINUTES): currentDayDuration;
                        long nowMinutes = currentDayDuration.toMinutes();
                        long nowDay24OtMinutes = nowMinutes - (Math.max(otStandardHours.longValue() * 60L - currentDay24Minutes.get(), 0L));
                        long weekMore40HoursMinutes = nowMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get() - dayRtMinutes.get(), 0L));
                        long otMinutes = Math.max(Math.max(nowDay24OtMinutes, weekMore40HoursMinutes), 0L);
                        long rtMinutes = nowMinutes - otMinutes;
                        dayOtMinutes.set(dayOtMinutes.get() + otMinutes);
                        dayRtMinutes.set(dayRtMinutes.get() + rtMinutes);
                        currentDay24Minutes.set(currentDay24Minutes.get() + nowMinutes);
                    } else {
                        if (firstTime.isBefore(timeOut)) {
                            //存在昨天24小时内的数据
                            Duration last24DayDuration = Duration.between(currentBeginLocationTime, firstTime);
                            long lastMinutes = last24DayDuration.toMinutes();
                            //24小时内,属于当天的 ot minutes   链接昨天的24小的的时间 - (8/10 小时  - 昨天已经工作时长  - 今天所属的昨天的工作时长)
                            long lastDay24OtMinutes = lastMinutes - (Math.max(otStandardHours.longValue() * 60L - yesterdayWorkMinutes.get() - currentDayYesterday24Minutes.get(), 0L));
                            //一周内,超过40小时的 ot minutes
                            long lastWeekMore40HoursMinutes = lastMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get() - dayRtMinutes.get(), 0L));
                            long lastOtMinutes = Math.max(Math.max(lastDay24OtMinutes, lastWeekMore40HoursMinutes), 0L);
                            long lastRtMinutes = lastMinutes - lastOtMinutes;
                            dayOtMinutes.set(dayOtMinutes.get() + lastOtMinutes);
                            dayRtMinutes.set(dayRtMinutes.get() + lastRtMinutes);
                            Duration current24Duration = Duration.between(firstTime, timeOut);
                            current24Duration = is2359Flag.get()? current24Duration.plus(1, ChronoUnit.MINUTES): current24Duration;
                            long nowMinutes = current24Duration.toMinutes();
                            long nowDay24OtMinutes = nowMinutes - (Math.max(otStandardHours.longValue() * 60L - currentDay24Minutes.get(), 0L));
                            long weekMore40HoursMinutes = nowMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get() - dayRtMinutes.get(), 0L));
                            long otMinutes = Math.max(Math.max(nowDay24OtMinutes, weekMore40HoursMinutes), 0L);
                            long rtMinutes = nowMinutes - otMinutes;
                            dayOtMinutes.set(dayOtMinutes.get() + otMinutes);
                            dayRtMinutes.set(dayRtMinutes.get() + rtMinutes);
                            currentDay24Minutes.set(currentDay24Minutes.get() + nowMinutes);
                        } else {
                            Duration last24DayDuration = Duration.between(currentBeginLocationTime, timeOut);
                            last24DayDuration = is2359Flag.get()? last24DayDuration.plus(1, ChronoUnit.MINUTES): last24DayDuration;
                            long lastMinutes = last24DayDuration.toMinutes();
                            long lastDay24OtMinutes = lastMinutes - (Math.max(otStandardHours.longValue() * 60L - yesterdayWorkMinutes.get() - currentDayYesterday24Minutes.get(), 0L));
                            long lastWeekMore40HoursMinutes = lastMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get() - dayRtMinutes.get(), 0L));
                            long lastOtMinutes = Math.max(Math.max(lastDay24OtMinutes, lastWeekMore40HoursMinutes), 0L);
                            long lastRtMinutes = lastMinutes - lastOtMinutes;
                            dayOtMinutes.set(dayOtMinutes.get() + lastOtMinutes);
                            dayRtMinutes.set(dayRtMinutes.get() + lastRtMinutes);
                        }
                    }
                }
            } else {
                //firstTime 在timeIn之前,当天全部属于当天24小时内的工作时间
                Duration duration = Duration.between(timeIn, timeOut);
                duration = is2359Flag.get()? duration.plus(1, ChronoUnit.MINUTES): duration;
                long currentMinutes = duration.toMinutes();
                for (int i = 0; i < breakTimeRecordOutList.size(); i++) {
                    TimeSheetBreakTimeRecord breakTimeOut = breakTimeRecordOutList.get(i);
                    TimeSheetBreakTimeRecord breakTimeIn = breakTimeRecordInList.get(i);
                    LocalTime breakTimeOutTime = LocalTime.parse(breakTimeOut.getTime());
                    LocalTime breakTimeInTime = LocalTime.parse(breakTimeIn.getTime());
                    Duration firstToOutDuration = Duration.between(breakTimeOutTime, breakTimeInTime);
                    currentMinutes = currentMinutes - firstToOutDuration.toMinutes();
                }
                //24小时内,属于当天的 ot minutes
                long currentDay24OtMinutes = currentMinutes - otStandardHours.longValue() * 60L;
                //一周内,超过40小时的 ot minutes
                long currentWeekMore40HoursMinutes = currentMinutes - (Math.max(DEFAULT_HRS_A_WORKWEEK_40.longValue() * 60L - sumRegularMinutes.get(), 0L));
                long currentOtMinutes = Math.max(Math.max(currentDay24OtMinutes, currentWeekMore40HoursMinutes), 0L);
                long currentRtMinutes = currentMinutes - currentOtMinutes;
                dayOtMinutes.set(dayOtMinutes.get() + currentOtMinutes);
                dayRtMinutes.set(dayRtMinutes.get() + currentRtMinutes);
                currentDay24Minutes.set(currentDay24Minutes.get() + currentMinutes);
            }
            //设置时间 yesterdayWorkMinutes
            sumRegularMinutes.set(sumRegularMinutes.get() + dayRtMinutes.get());
            yesterdayWorkMinutes.set(currentDay24Minutes.get());
            timeSheet.setRegularHours(BigDecimal.valueOf(dayRtMinutes.get()).divide(BigDecimal.valueOf(60L), 2, RoundingMode.HALF_UP).floatValue());
            timeSheet.setOverTime(BigDecimal.valueOf(dayOtMinutes.get()).divide(BigDecimal.valueOf(60L), 2, RoundingMode.HALF_UP).floatValue());
        });

    }

    /**
     * calculateByPuertoRico rules:
     * (1)48 hrs a workweek
     * @param recordList
     */
    private void calculateByWeekHours48(List<TimeSheetRecord> recordList) {
        calculateByWeekHours(recordList, DEFAULT_HRS_A_WORKWEEK_48);
    }

    /**
     * calculateByPuertoRico rules:
     * (1)40 hrs a workweek
     * (2)>8 hrs a workday
     * @param recordList
     */
    private void calculateByWeekHours40AndDayHoursOnlyOt(List<TimeSheetRecord> recordList) {
        calculateByWeekHoursAndDayHours(recordList, DEFAULT_HRS_A_WORKWEEK_40, false, true, OT_8_DEFAULT_TIME);
    }

    /**
     * calculateByPuertoRico rules:
     * (1)44 hrs a workweek
     * @param recordList
     */
    private static void calculateByWeekHours44(List<TimeSheetRecord> recordList) {
        calculateByWeekHours(recordList, DEFAULT_HRS_A_WORKWEEK_44);
    }

    /**
     * calculateByPuertoRico rules:
     * (1)44 hrs a workweek
     * (2)>8 hrs a workday
     * @param recordList
     */
    private static void calculateByWeekHours44AndDayHoursOnlyOt(List<TimeSheetRecord> recordList) {
        calculateByWeekHoursAndDayHours(recordList, DEFAULT_HRS_A_WORKWEEK_44, false, true, OT_8_DEFAULT_TIME);
    }

    /**
     * calculateByPuertoRico rules:
     * (1)40 hrs a workweek
     * (2)>8 hrs a workday , < 12 hrs
     * @param recordList
     */
    private static void calculateByWeekHours40AndDayHoursWithOtAndDt(List<TimeSheetRecord> recordList) {
        calculateByWeekHoursAndDayHours(recordList, DEFAULT_HRS_A_WORKWEEK_40, true, true, OT_8_DEFAULT_TIME);
    }

    /**
     * 只根据 一周工作总时长计算ot dt 的方法
     * @param recordList
     * @param weekHoursTag
     */
    private static void calculateByWeekHours(List<TimeSheetRecord> recordList, BigDecimal weekHoursTag) {
        AtomicReference<BigDecimal> sumWorkHours = new AtomicReference<>(BigDecimal.ZERO);
        AtomicBoolean moreThanFlag = new AtomicBoolean(false);
        recordList.forEach(timeSheetRecord -> {
            if (ObjectUtil.isNull(timeSheetRecord.getWorkHours()) || timeSheetRecord.getWorkHours() <= DEFAULT_HOURS_ZERO) {
                return;
            }
            sumWorkHours.set(sumWorkHours.get().add(new BigDecimal(timeSheetRecord.getWorkHours().toString())));
            BigDecimal currentSum = sumWorkHours.get();
            //>40 hrs a workweek
            if (BooleanUtil.isFalse(doHandlerRecordByAlreadyMoreThanWeekHoursOrCurrenMoreThanWeekHours(moreThanFlag, timeSheetRecord, currentSum, false, false, weekHoursTag))) {
                initRegularHours(timeSheetRecord);
            }
        });

    }

    /**
     * 只根据 一周工作总时长计算ot dt 的方法
     * @param recordList
     * @param weekHoursTag
     */
    private static void calculateByWeekHoursAndDayHours(List<TimeSheetRecord> recordList, BigDecimal weekHoursTag, boolean hasDt, boolean hasOt, BigDecimal dayHoursTag) {
        AtomicReference<BigDecimal> sumRegularHours = new AtomicReference<>(BigDecimal.ZERO);
        AtomicBoolean moreThanFlag = new AtomicBoolean(false);
        recordList.forEach(timeSheetRecord -> {
            if (ObjectUtil.isNull(timeSheetRecord.getWorkHours()) || timeSheetRecord.getWorkHours() <= DEFAULT_HOURS_ZERO) {
                return;
            }
            BigDecimal workHours = getRegularHoursWithoutDoubleCalculate(timeSheetRecord, dayHoursTag);
            sumRegularHours.set(sumRegularHours.get().add(workHours));
            BigDecimal currentSum = sumRegularHours.get();
            if (BooleanUtil.isTrue(doHandlerRecordByAlreadyMoreThanWeekHoursOrCurrenMoreThanWeekHoursWithoutDoubleCalculate(moreThanFlag, timeSheetRecord, currentSum, hasDt, hasOt, weekHoursTag, dayHoursTag))) {
                return;
            }
            //<40
            doHandlerCurrenDayWorkHoursMoreThan8OrMoreThan12(timeSheetRecord, hasDt, hasOt, dayHoursTag);
        });
    }

    /**
     * calculateByPuertoRico rules:
     * (1)40 hrs a workweek
     * (2)>8 hrs a workday
     * @param recordList
     */
    private void calculateByPuertoRico(List<TimeSheetRecord> recordList) {
        calculateByWeekHoursAndDayHours(recordList, DEFAULT_HRS_A_WORKWEEK_40, false, true, OT_8_DEFAULT_TIME);
    }


    /**
     * 》8 小时为ot 没有dt
     * 连续工作的第6天河第7天未 ot
     * @param recordList
     */
    private void calculateByUsVirginIsland(List<TimeSheetRecord> recordList) {
        AtomicReference<BigDecimal> sumRegularHours = new AtomicReference<>(BigDecimal.ZERO);
        AtomicInteger count = new AtomicInteger();
        AtomicBoolean moreThan40Flag = new AtomicBoolean(false);
        AtomicReference<LocalDate> lastTime = new AtomicReference<>();
        lastTime.set(recordList.stream().map(TimeSheetRecord::getWorkDate).max(LocalDate::compareTo).get().plusDays(-7));
        recordList.forEach(timeSheetRecord -> {
            //calculate count
            count.addAndGet(1);
            if (ObjectUtil.isNull(timeSheetRecord.getWorkHours()) || timeSheetRecord.getWorkHours() <= DEFAULT_HOURS_ZERO) {
                count.set(0);
                lastTime.set(timeSheetRecord.getWorkDate());
                return;
            }
            if (!Objects.equals(lastTime.get().plusDays(1), timeSheetRecord.getWorkDate())) {
                count.set(1);
            }
            lastTime.set(timeSheetRecord.getWorkDate());
            //Work continuously for a week
            if (BooleanUtil.isTrue(doHandlerEndDay(count, Arrays.asList(SATURDAY_COUNT, SUNDAY_COUNT), timeSheetRecord, false))) {
                return;
            }
            BigDecimal workHours = getRegularHoursWithoutDoubleCalculate(timeSheetRecord, OT_8_DEFAULT_TIME);
            sumRegularHours.set(sumRegularHours.get().add(workHours));
            BigDecimal currentSum = sumRegularHours.get();
            //40 hrs a workweek
            if (BooleanUtil.isTrue(doHandlerRecordByAlreadyMoreThanWeekHoursOrCurrenMoreThanWeekHoursWithoutDoubleCalculate(moreThan40Flag, timeSheetRecord, currentSum, false, true, DEFAULT_HRS_A_WORKWEEK_40, OT_8_DEFAULT_TIME))) {
                return;
            }
            //<40
            doHandlerCurrenDayWorkHoursMoreThan8OrMoreThan12(timeSheetRecord, false, true, OT_8_DEFAULT_TIME);
        });
    }

    /**
     * calculateByKentucky rules:
     * (1)40 hrs a workweek
     * (2)the first 8 hrs on the seventh consecutive day in a single workweek
     * @param recordList
     */
    private void calculateByKentucky(List<TimeSheetRecord> recordList) {
        AtomicReference<BigDecimal> sumRegularHours = new AtomicReference<>(BigDecimal.ZERO);
        AtomicInteger count = new AtomicInteger();
        AtomicBoolean moreThan40Flag = new AtomicBoolean(false);
        AtomicReference<LocalDate> lastTime = new AtomicReference<>();
        lastTime.set(recordList.stream().map(TimeSheetRecord::getWorkDate).max(LocalDate::compareTo).get().plusDays(-7));
        recordList.forEach(timeSheetRecord -> {
            //calculate count
            count.addAndGet(1);
            if (ObjectUtil.isNull(timeSheetRecord.getWorkHours()) || timeSheetRecord.getWorkHours() <= DEFAULT_HOURS_ZERO) {
                count.set(0);
                lastTime.set(timeSheetRecord.getWorkDate());
                return;
            }
            if (!Objects.equals(lastTime.get().plusDays(1), timeSheetRecord.getWorkDate())) {
                count.set(1);
            }
            lastTime.set(timeSheetRecord.getWorkDate());
            //Work continuously for a week
            if (BooleanUtil.isTrue(doHandlerEndDay(count, Collections.singletonList(SUNDAY_COUNT), timeSheetRecord, false))) {
                return;
            }
            sumRegularHours.set(sumRegularHours.get().add(new BigDecimal(timeSheetRecord.getWorkHours().toString())));
            BigDecimal currentSum = sumRegularHours.get();
            //>40 hrs a workweek
            if (BooleanUtil.isFalse(doHandlerRecordByAlreadyMoreThanWeekHoursOrCurrenMoreThanWeekHours(moreThan40Flag, timeSheetRecord, currentSum, false, false, DEFAULT_HRS_A_WORKWEEK_40))) {
                initRegularHours(timeSheetRecord);
            }
        });
    }

    /**
     * calculateByColorado rules:
     * (1)40 hrs a workweek
     * (2)>12 hrs a workday
     * (3)12 consecutive hrs (regardless of the start/ending time of workday & excluding duty free meal time)
     * @param recordList
     */
    private void calculateByColorado(List<TimeSheetRecord> recordList) {
        //search week timesheet time
        TimeSheetRecord first = recordList.get(0);
        LocalDate startDate = first.getWorkDate();
        LocalDate endDate = recordList.get(recordList.size() - 1).getWorkDate();
        List<TimeSheetBreakTimeRecord> breakTimeRecordList = timeSheetBreakTimeRepository.findAllByDate(startDate, endDate, first.getTalentId(), first.getAssignmentId());
        // key : workDate-breakTimeType
        Map<String, TimeSheetBreakTimeRecord> breakTimeMap = breakTimeRecordList.stream().filter(breakTimeRecord -> TimeSheetBreakTimeType.TIME_IN == breakTimeRecord.getBreakTimeType()
                || TimeSheetBreakTimeType.TIME_OUT == breakTimeRecord.getBreakTimeType()).collect(Collectors.toMap(k -> k.getWorkDate() + "-" + k.getBreakTimeType().toDbValue(), a -> a));
        Map<LocalDate, TimeSheetRecord> timeSheetRecordMap = recordList.stream().collect(Collectors.toMap(TimeSheetRecord::getWorkDate,a -> a));
        //获取连轴转的几个时间点
        List<List<Integer>> listList = doHandlerConsecutiveHrs(recordList, breakTimeMap);
        AtomicReference<List<Integer>> list = new AtomicReference<>(new ArrayList<>());
        //正常工作中时长
        AtomicReference<BigDecimal> sumRtHours = new AtomicReference<>(BigDecimal.ZERO);
        AtomicBoolean moreThan40Flag = new AtomicBoolean(false);
        AtomicReference<BigDecimal> consecutiveHours = new AtomicReference<>(BigDecimal.ZERO);
        AtomicInteger count = new AtomicInteger();
        recordList.forEach(timeSheetRecord -> {
            //no work
            Integer thCount = count.addAndGet(1);
            if (ObjectUtil.isNull(timeSheetRecord.getWorkHours()) || timeSheetRecord.getWorkHours() <= DEFAULT_HOURS_ZERO) {
                return;
            }
            timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
            if (CollUtil.isNotEmpty(list.get())) {
                //存在连续工作的直接陪陪
                if (list.get().contains(thCount)) {
                    consecutiveHours.getAndSet(consecutiveHours.get().add(new BigDecimal(timeSheetRecord.getWorkHours().toString())));
                } else {
                    //连续工作已经结束,连轴转工作时长归0,重新寻找连轴转周期
                    consecutiveHours.set(BigDecimal.ZERO);
                    list.set(listList.stream().filter(countList -> countList.contains(thCount)).findAny().orElse(new ArrayList<>()));
                    if (CollUtil.isNotEmpty(list.get())) {
                        consecutiveHours.getAndSet(consecutiveHours.get().add(new BigDecimal(timeSheetRecord.getWorkHours().toString())));
                    }
                }
            } else {
                // 寻找存在当天的连轴转 集合
                list.set(listList.stream().filter(countList -> countList.contains(thCount)).findAny().orElse(new ArrayList<>()));
                if (CollUtil.isNotEmpty(list.get())) {
                    // 获取连轴转的时长
                    consecutiveHours.getAndSet(consecutiveHours.get().add(new BigDecimal(timeSheetRecord.getWorkHours().toString())));
                }
            }
            //获取当前工作时长
            //当存在连轴转的时候, 返回当前所有连轴转的 打卡记录3,4,5  如果当前是4 ,则返回 3 的数据, 当前是3 返回空数据
            List<TimeSheetRecord> onaTimeSheetRecordList = getOnaTimeSheetRecordList(thCount, timeSheetRecord.getWorkDate(), list.get(), timeSheetRecordMap);
            // 获取连轴转的当天之前的所有 rt ot 之和
            BigDecimal onaBigDecimal = CollUtil.isEmpty(onaTimeSheetRecordList)?
                    //不存在连轴转数据
                    BigDecimal.ZERO:
                    //存在连轴转当天之前的数据
                    onaTimeSheetRecordList.stream().map(onaRecord -> new BigDecimal(onaRecord.getRegularHours().toString()).add(new BigDecimal(onaRecord.getOverTime().toString()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            //当天的真正工作时长
            BigDecimal workHoursBigDecimal = consecutiveHours.get().compareTo(BigDecimal.ZERO) == 0? new BigDecimal(timeSheetRecord.getWorkHours().toString()): consecutiveHours.get();
            //连轴转之当天之前的工作时长
            BigDecimal subtractBigDecimal = DT_DEFAULT_TIME.compareTo(onaBigDecimal) >= 0? DT_DEFAULT_TIME: onaBigDecimal;
            //>40
            BigDecimal lastSumRegularHours = sumRtHours.get();
            BigDecimal currentWorkHours = new BigDecimal(timeSheetRecord.getWorkHours().toString());
            BigDecimal currentWorkAndSumRegularHours = lastSumRegularHours.add(currentWorkHours);
            if (moreThan40Flag.get() || currentWorkAndSumRegularHours.compareTo(DEFAULT_HRS_A_WORKWEEK_40) >= 0) {
                if (moreThan40Flag.get()) {
                    //大于40, 则全是 ot
                    timeSheetRecord.setOverTime(timeSheetRecord.getWorkHours());
                    timeSheetRecord.setRegularHours(DEFAULT_HOURS_ZERO);
                    timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
                    return;
                }
                //存在2中情况,谁先符合条件则,谁的ot时间更长
                //1) >12
                BigDecimal moreThan12Ot = workHoursBigDecimal.subtract(subtractBigDecimal);
                moreThan12Ot = moreThan12Ot.compareTo(BigDecimal.ZERO) >= 0? moreThan12Ot: BigDecimal.ZERO;
                //2) >40
                BigDecimal moreThan40Ot = currentWorkAndSumRegularHours.subtract(DEFAULT_HRS_A_WORKWEEK_40);
                if (moreThan12Ot.compareTo(moreThan40Ot) >= 0) {
                    timeSheetRecord.setOverTime(moreThan12Ot.floatValue());
                    timeSheetRecord.setRegularHours(workHoursBigDecimal.subtract(moreThan12Ot).subtract(onaBigDecimal).floatValue());
                } else {
                    // 当使用大于40 个小时的作为结果时, 才是真正的 rt时间超过40小时
                    moreThan40Flag.set(true);
                    timeSheetRecord.setOverTime(moreThan40Ot.floatValue());
                    timeSheetRecord.setRegularHours(workHoursBigDecimal.subtract(moreThan40Ot).subtract(onaBigDecimal).floatValue());
                }
            } else {
                // <40
                if (workHoursBigDecimal.compareTo(subtractBigDecimal) >= 0) {
                    // >12
                    timeSheetRecord.setOverTime(workHoursBigDecimal.subtract(subtractBigDecimal).floatValue());
                    timeSheetRecord.setRegularHours(workHoursBigDecimal.subtract(new BigDecimal(timeSheetRecord.getOverTime().toString())).subtract(onaBigDecimal).floatValue());
                } else {
                    timeSheetRecord.setOverTime(DEFAULT_HOURS_ZERO);
                    timeSheetRecord.setRegularHours(workHoursBigDecimal.subtract(onaBigDecimal).floatValue());
                }
            }
            sumRtHours.set(sumRtHours.get().add(new BigDecimal(timeSheetRecord.getRegularHours().toString())));
        });
    }

    /**
     * calculateByCalifornia rules:
     * (1)40 hrs a workweek
     * (2)>8 hrs but <12
     * (3)the first 8 hrs on the seventh consecutive day in a single workweek
     * @param recordList
     */
    private void calculateByCalifornia(List<TimeSheetRecord> recordList) {
        AtomicReference<BigDecimal> sumRegularHours = new AtomicReference<>(BigDecimal.ZERO);
        AtomicInteger count = new AtomicInteger();
        AtomicBoolean moreThan40Flag = new AtomicBoolean(false);
        AtomicReference<LocalDate> lastTime = new AtomicReference<>();
        lastTime.set(recordList.stream().map(TimeSheetRecord::getWorkDate).max(LocalDate::compareTo).get().plusDays(-7));
        recordList.forEach(timeSheetRecord -> {
            //calculate count
            count.addAndGet(1);
            //continue
            if (ObjectUtil.isNull(timeSheetRecord.getWorkHours()) || timeSheetRecord.getWorkHours() <= DEFAULT_HOURS_ZERO) {
                count.set(0);
                lastTime.set(timeSheetRecord.getWorkDate());
                return;
            }
            if (!Objects.equals(lastTime.get().plusDays(1), timeSheetRecord.getWorkDate())) {
                count.set(1);
            }
            lastTime.set(timeSheetRecord.getWorkDate());
            //Work continuously for a week
            if (BooleanUtil.isTrue(doHandlerEndDay(count, Collections.singletonList(SUNDAY_COUNT), timeSheetRecord, true))) {
                return;
            }
            BigDecimal workHours = getRegularHoursWithoutDoubleCalculate(timeSheetRecord, OT_8_DEFAULT_TIME);
            sumRegularHours.set(sumRegularHours.get().add(workHours));
            BigDecimal currentSum = sumRegularHours.get();
            //>40 hrs a workweek
            if (BooleanUtil.isTrue(doHandlerRecordByAlreadyMoreThanWeekHoursOrCurrenMoreThanWeekHoursWithoutDoubleCalculate(moreThan40Flag, timeSheetRecord, currentSum, true, true, DEFAULT_HRS_A_WORKWEEK_40, OT_8_DEFAULT_TIME))) {
                return;
            }
            //<40
            doHandlerCurrenDayWorkHoursMoreThan8OrMoreThan12(timeSheetRecord, true, true, OT_8_DEFAULT_TIME);
        });
    }

    /**
     * 获取12个小时候 和 连轴转12个小时算 ot
     * @param timeSheetRecord 当天打卡记录
     * @return 连轴转所欲时间
     */
    private static BigDecimal getRegular12HoursOrConsecutiveHoursWithoutDoubleCalculate(BigDecimal lastSumWorkHours, TimeSheetRecord timeSheetRecord, BigDecimal consecutiveHours) {
        BigDecimal workHours = new BigDecimal(timeSheetRecord.getWorkHours().toString());
        if (consecutiveHours.compareTo(BigDecimal.ZERO) > 0) {
            //存在连轴转的数据
            if (consecutiveHours.compareTo(DT_DEFAULT_TIME) > 0) {
                //如果大于12 则不计入40个小时的统计
                BigDecimal regularHours = DT_DEFAULT_TIME.subtract(consecutiveHours.subtract(new BigDecimal(timeSheetRecord.getWorkHours().toString())));
                return regularHours.compareTo(BigDecimal.ZERO) < 0? BigDecimal.ZERO: regularHours;
            } else {
                //小于12 全部记入 40个小时的计算
                return consecutiveHours;
            }
        } else if (workHours.compareTo(DT_DEFAULT_TIME) <= 0) {
            return workHours;
        } else {
            return OT_8_DEFAULT_TIME;
        }
    }

    /**
     * 获取12 个小时候算 ot 的 正常工作时长的方法
     * @param timeSheetRecord
     * @return
     */
    private static BigDecimal getRegular12HoursWithoutDoubleCalculate(TimeSheetRecord timeSheetRecord) {
        BigDecimal workHours = new BigDecimal(timeSheetRecord.getWorkHours().toString());
        if (workHours.compareTo(DT_DEFAULT_TIME) <= 0) {
            return workHours;
        } else {
            return OT_8_DEFAULT_TIME;
        }
    }

    /**
     * 8 个小时候算 ot 的 正常工作时长的方法
     * @param timeSheetRecord
     * @return
     */
    private static BigDecimal getRegularHoursWithoutDoubleCalculate(TimeSheetRecord timeSheetRecord, BigDecimal dayHoursTag) {
        BigDecimal workHours = new BigDecimal(timeSheetRecord.getWorkHours().toString());
        if (workHours.compareTo(dayHoursTag) <= 0) {
            return workHours;
        } else {
            return dayHoursTag;
        }
    }

    /**
     * calculateByFederal rules:
     * (1)40 hrs a workweek
     * @param recordList
     */
    private void calculateByWeekHours40(List<TimeSheetRecord> recordList) {
        calculateByWeekHours(recordList, DEFAULT_HRS_A_WORKWEEK_40);
    }

    private void initRegularHours(TimeSheetRecord timeSheetRecord) {
        timeSheetRecord.setOverTime(DEFAULT_HOURS_ZERO);
        timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
        timeSheetRecord.setRegularHours(timeSheetRecord.getWorkHours());
    }


    /**
     * doBuildCurrenMoreThan40
     * @param timeSheetRecord
     * @param currentSum
     * @param hasDt
     */
    private void doHandlerRecordByCurrenMoreThanWeekHoursWithoutDoubleCalculate(TimeSheetRecord timeSheetRecord, BigDecimal currentSum, Boolean hasDt, Boolean have8HoursLimitOt, BigDecimal weekHours, BigDecimal dayHoursTag) {
        BigDecimal regularHours = getRegularHoursWithoutDoubleCalculate(timeSheetRecord, dayHoursTag);
        BigDecimal lastSumBigDecimal = currentSum.subtract(regularHours);
        BigDecimal regularBigDecimal = weekHours.subtract(lastSumBigDecimal);
        if (BooleanUtil.isTrue(have8HoursLimitOt) && regularBigDecimal.compareTo(dayHoursTag) > 0) {
            regularBigDecimal = dayHoursTag;
        }
        timeSheetRecord.setRegularHours(regularBigDecimal.floatValue());
        if (BooleanUtil.isTrue(hasDt)) {
            BigDecimal doubleBigDecimal = new BigDecimal(timeSheetRecord.getWorkHours().toString()).subtract(DT_DEFAULT_TIME);
            timeSheetRecord.setDoubleTime(doubleBigDecimal.compareTo(BigDecimal.ZERO) > 0? doubleBigDecimal.floatValue(): 0F);
        } else {
            timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
        }
        BigDecimal overTimeBigDecimal = new BigDecimal(timeSheetRecord.getWorkHours().toString()).subtract(regularBigDecimal).subtract(new BigDecimal(timeSheetRecord.getDoubleTime().toString()));
        timeSheetRecord.setOverTime(overTimeBigDecimal.floatValue());
    }

    /**
     * doBuildCurrenMoreThan40
     * @param timeSheetRecord
     * @param currentSum
     * @param hasDt
     */
    private void doHandlerRecordByCurrenMoreThanWeekHours(TimeSheetRecord timeSheetRecord, BigDecimal currentSum, Boolean hasDt, Boolean have8HoursLimitOt, BigDecimal weekHours) {
        BigDecimal lastSumBigDecimal = currentSum.subtract(new BigDecimal(timeSheetRecord.getWorkHours().toString()));
        BigDecimal regularBigDecimal = weekHours.subtract(lastSumBigDecimal);
        if (BooleanUtil.isTrue(have8HoursLimitOt) && regularBigDecimal.compareTo(OT_8_DEFAULT_TIME) > 0) {
            regularBigDecimal = OT_8_DEFAULT_TIME;
        }
        timeSheetRecord.setRegularHours(regularBigDecimal.floatValue());
        if (BooleanUtil.isTrue(hasDt)) {
            BigDecimal doubleBigDecimal = new BigDecimal(timeSheetRecord.getWorkHours().toString()).subtract(DT_DEFAULT_TIME);
            timeSheetRecord.setDoubleTime(doubleBigDecimal.compareTo(BigDecimal.ZERO) > 0? doubleBigDecimal.floatValue(): 0F);
        } else {
            timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
        }
        BigDecimal overTimeBigDecimal = new BigDecimal(timeSheetRecord.getWorkHours().toString()).subtract(regularBigDecimal).subtract(new BigDecimal(timeSheetRecord.getDoubleTime().toString()));
        timeSheetRecord.setOverTime(overTimeBigDecimal.floatValue());
    }

    /**
     * doHandlerCurrenMoreThan8OrMoreThan12
     * @param timeSheetRecord
     * @param hasDt
     */
    private void doHandlerCurrenDayWorkHoursMoreThan8OrMoreThan12(TimeSheetRecord timeSheetRecord, Boolean hasDt, Boolean hasOt, BigDecimal dayHoursTag) {
        if (BooleanUtil.isTrue(hasDt) && new BigDecimal(timeSheetRecord.getWorkHours().toString()).compareTo(DT_DEFAULT_TIME) > 0) {
            // > 12
            float doubleTime = new BigDecimal(timeSheetRecord.getWorkHours().toString()).subtract(DT_DEFAULT_TIME).floatValue();
            timeSheetRecord.setDoubleTime(doubleTime);
            timeSheetRecord.setOverTime(DT_DEFAULT_TIME.subtract(dayHoursTag).floatValue());
            timeSheetRecord.setRegularHours(dayHoursTag.floatValue());
        } else if (BooleanUtil.isTrue(hasOt) && new BigDecimal(timeSheetRecord.getWorkHours().toString()).compareTo(dayHoursTag) > 0) {
            // (have dt <=12 && >8) || (have no dt && > 8)
            float overTime = new BigDecimal(timeSheetRecord.getWorkHours().toString()).subtract(dayHoursTag).floatValue();
            timeSheetRecord.setOverTime(overTime);
            timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
            timeSheetRecord.setRegularHours(dayHoursTag.floatValue());
        } else {
            //<=8
            timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
            timeSheetRecord.setOverTime(DEFAULT_HOURS_ZERO);
            timeSheetRecord.setRegularHours(timeSheetRecord.getWorkHours());
        }
    }

    /**
     * doHandlerEndDay
     * @param count
     * @param endWeekDay
     * @param timeSheetRecord
     * @param hasDt
     */
    private Boolean doHandlerEndDay(AtomicInteger count, List<Integer> endWeekDay, TimeSheetRecord timeSheetRecord, Boolean hasDt) {
        boolean continueFlag = Boolean.FALSE;
        if (endWeekDay.contains(count.get())) {
            continueFlag = true;
            timeSheetRecord.setRegularHours(DEFAULT_HOURS_ZERO);
            if (BooleanUtil.isTrue(hasDt) && new BigDecimal(timeSheetRecord.getWorkHours().toString()).compareTo(OT_8_DEFAULT_TIME) > 0) {
                //>8
                float doubleTime = new BigDecimal(timeSheetRecord.getWorkHours().toString()).subtract(OT_8_DEFAULT_TIME).floatValue();
                timeSheetRecord.setDoubleTime(doubleTime);
                timeSheetRecord.setOverTime(OT_8_DEFAULT_TIME.floatValue());
            } else {
                // (<=8) || (no have dt && > 8)
                timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
                timeSheetRecord.setOverTime(timeSheetRecord.getWorkHours());
            }
        }
        return continueFlag;
    }

    /**
     * doHandlerRecordByHasMoreThan40OrCurrenMoreThan40
     * @param moreThan40Flag
     * @param timeSheetRecord
     * @param currentSum
     * @param hasDt
     * @param have8HoursLimitOt
     * @return
     */
    private Boolean doHandlerRecordByAlreadyMoreThanWeekHoursOrCurrenMoreThanWeekHoursWithoutDoubleCalculate(AtomicBoolean moreThan40Flag, TimeSheetRecord timeSheetRecord, BigDecimal currentSum, Boolean hasDt, Boolean have8HoursLimitOt
            , BigDecimal weekHours, BigDecimal dayHoursTag) {
        boolean continueFlag = Boolean.FALSE;
        if (moreThan40Flag.get() || currentSum.compareTo(weekHours) > 0) {
            continueFlag = Boolean.TRUE;
            //It's been over 40 hours
            if (moreThan40Flag.get()) {
                timeSheetRecord.setRegularHours(DEFAULT_HOURS_ZERO);
                BigDecimal dtTimeBigDecimal = new BigDecimal(timeSheetRecord.getWorkHours().toString()).subtract(DT_DEFAULT_TIME);
                if (BooleanUtil.isTrue(hasDt) && dtTimeBigDecimal.compareTo(BigDecimal.ZERO) > 0) {
                    // >12
                    timeSheetRecord.setDoubleTime(dtTimeBigDecimal.floatValue());
                    timeSheetRecord.setOverTime(DT_DEFAULT_TIME.floatValue());
                } else {
                    // <=12
                    timeSheetRecord.setOverTime(timeSheetRecord.getWorkHours());
                    timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
                }
                return true;
            }
            moreThan40Flag.set(true);
            doHandlerRecordByCurrenMoreThanWeekHoursWithoutDoubleCalculate(timeSheetRecord, currentSum, hasDt, have8HoursLimitOt, weekHours, dayHoursTag);
        }
        return continueFlag;
    }


    /**
     * doHandlerRecordByHasMoreThan40OrCurrenMoreThan40
     * @param moreThan40Flag
     * @param timeSheetRecord
     * @param currentSum
     * @param hasDt
     * @param have8HoursLimitOt
     * @return
     */
    private Boolean doHandlerRecordByAlreadyMoreThanWeekHoursOrCurrenMoreThanWeekHours(AtomicBoolean moreThan40Flag, TimeSheetRecord timeSheetRecord, BigDecimal currentSum, Boolean hasDt, Boolean have8HoursLimitOt, BigDecimal weekHours) {
        boolean continueFlag = Boolean.FALSE;
        if (moreThan40Flag.get() || currentSum.compareTo(weekHours) > 0) {
            continueFlag = Boolean.TRUE;
            //It's been over 40 hours
            if (moreThan40Flag.get()) {
                timeSheetRecord.setRegularHours(DEFAULT_HOURS_ZERO);
                BigDecimal dtTimeBigDecimal = new BigDecimal(timeSheetRecord.getWorkHours().toString()).subtract(DT_DEFAULT_TIME);
                if (BooleanUtil.isTrue(hasDt) && dtTimeBigDecimal.compareTo(BigDecimal.ZERO) > 0) {
                    // >12
                    timeSheetRecord.setDoubleTime(dtTimeBigDecimal.floatValue());
                    timeSheetRecord.setOverTime(DT_DEFAULT_TIME.floatValue());
                } else {
                    // <=12
                    timeSheetRecord.setOverTime(timeSheetRecord.getWorkHours());
                    timeSheetRecord.setDoubleTime(DEFAULT_HOURS_ZERO);
                }
                return true;
            }
            moreThan40Flag.set(true);
            doHandlerRecordByCurrenMoreThanWeekHours(timeSheetRecord, currentSum, hasDt, have8HoursLimitOt, weekHours);
        }
        return continueFlag;
    }

    /**
     * 获得连续工作的week的第几天集合
     * @param recordList
     * @param breakTimeMap
     * @return
     */
    private List<List<Integer>> doHandlerConsecutiveHrs(List<TimeSheetRecord> recordList, Map<String, TimeSheetBreakTimeRecord> breakTimeMap) {
        List<List<Integer>> result = new ArrayList<>();
        if (CollUtil.isEmpty(breakTimeMap)) {
            return result;
        }
        AtomicInteger counter = new AtomicInteger();
        AtomicReference<List<Integer>> continues = new AtomicReference<>(new ArrayList<>());
        recordList.forEach(timeSheetRecord -> {
            Integer count = counter.addAndGet(1);
            //2022-05-04 00:00
            TimeSheetBreakTimeRecord end = breakTimeMap.get(timeSheetRecord.getWorkDate().plusDays(1) + "-" + TimeSheetBreakTimeType.TIME_IN.toDbValue());
            //2022-05-03 23:59
            TimeSheetBreakTimeRecord currenEnd = breakTimeMap.get(timeSheetRecord.getWorkDate() + "-" + TimeSheetBreakTimeType.TIME_OUT.toDbValue());
            //2022-05-03 00:00
            TimeSheetBreakTimeRecord currenBegin = breakTimeMap.get(timeSheetRecord.getWorkDate() + "-" + TimeSheetBreakTimeType.TIME_IN.toDbValue());
            //2022-05-02 23:59
            TimeSheetBreakTimeRecord begin = breakTimeMap.get(timeSheetRecord.getWorkDate().minusDays(1) + "-" + TimeSheetBreakTimeType.TIME_OUT.toDbValue());
            boolean notExists = (ObjectUtil.isEmpty(currenBegin) || StrUtil.isBlank(currenBegin.getTime())) || (ObjectUtil.isEmpty(currenEnd) || StrUtil.isBlank(currenEnd.getTime()));
            if (notExists) {
                result.add(continues.get().stream().distinct().toList());
                continues.set(new ArrayList<>());
                return;
            }
            //不记录连轴转
            if (!Objects.equals(currenBegin.getTime(), BEGIN_OF_DAY) && !Objects.equals(currenEnd.getTime(), END_OF_DAY)) {
                result.add(continues.get().stream().distinct().toList());
                continues.set(new ArrayList<>());
                return;
            }
            // 00:00
            if (Objects.equals(currenBegin.getTime(), BEGIN_OF_DAY)) {
                if (ObjectUtil.isNotNull(begin) && Objects.equals(begin.getTime(), END_OF_DAY)) {
                    continues.get().add(count);
                    if (!Objects.equals(currenEnd.getTime(), END_OF_DAY)) {
                        result.add(continues.get().stream().distinct().toList());
                        continues.set(new ArrayList<>());
                    }
                    return;
                }
            }
            // 23:59
            if (Objects.equals(currenEnd.getTime(), END_OF_DAY)) {
                if (ObjectUtil.isNotNull(end) && Objects.equals(end.getTime(), BEGIN_OF_DAY)) {
                    if (!Objects.equals(currenBegin.getTime(), BEGIN_OF_DAY)) {
                        result.add(continues.get().stream().distinct().toList());
                        continues.set(new ArrayList<>());
                    }
                    continues.get().add(count);
                    return;
                }
            }
            if (ObjectUtil.isNull(end)) {
                result.add(continues.get().stream().distinct().toList());
                continues.set(new ArrayList<>());
                return;
            }
            result.add(continues.get().stream().distinct().toList());
            continues.set(new ArrayList<>());
        });
        if (CollUtil.isNotEmpty(continues.get())) {
            result.add(continues.get().stream().distinct().toList());
        }
        return result.stream().filter(CollUtil::isNotEmpty).collect(Collectors.toList());
    }

    /**
     * Get all consecutive date clocking records
     * @param count
     * @param workDate
     * @param list
     * @param timeSheetRecordMap
     * @return
     */
    private List<TimeSheetRecord> getOnaTimeSheetRecordList(Integer count, LocalDate workDate, List<Integer> list, Map<LocalDate, TimeSheetRecord> timeSheetRecordMap) {
        List<TimeSheetRecord> onaTimeSheetRecordList = new ArrayList<>();
        int subtractNum = 1;
        while (list.contains(count) && list.contains(--count)) {
            TimeSheetRecord record = timeSheetRecordMap.get(workDate.minusDays(subtractNum++));
            if (ObjectUtil.isNull(record)) {
                continue;
            }
            onaTimeSheetRecordList.add(record);
        }
        return onaTimeSheetRecordList;
    }

}

















