package com.altomni.apn.jobdiva.web.rest.assignment;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import com.altomni.apn.jobdiva.service.assignment.AssignmentService;
import com.altomni.apn.jobdiva.service.dto.assignment.AssignmentBatchInfoDTO;
import com.altomni.apn.jobdiva.service.dto.assignment.AssignmentCheckDateDTO;
import com.altomni.apn.jobdiva.service.dto.assignment.AssignmentDetailInfoDTO;
import com.altomni.apn.jobdiva.service.dto.assignment.CurrentContractorDTO;
import com.altomni.apn.jobdiva.service.vo.assignment.*;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * assignment controller
 * <AUTHOR>
 */
@Api(tags = {"AssignmentResource"})
@Slf4j
@RestController
@RequestMapping("/api/v3/assignment")
public class AssignmentResource {

    @Resource
    private AssignmentService assignmentService;

    @PostMapping("/save")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Long> save(@RequestBody AssignmentDetailInfoDTO dto) {
        log.info("[assignment: User @{}] REST save assignment:", SecurityUtils.getUserId());
        Long records = assignmentService.save(dto);
        return ResponseEntity.ok(records);

    }

    @PostMapping("/update")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Long> update(@RequestBody AssignmentDetailInfoDTO dto) {
        log.info("[assignment: User @{}] REST request to update assignment param : {}:", SecurityUtils.getUserId(), dto);
        Long records = assignmentService.update(dto);
        return ResponseEntity.ok(records);
    }

    @GetMapping("/current")
    @Timed
    public ResponseEntity<AssignmentGeneralInfoVO> currentAssignment(@RequestParam Long startId) {
        log.info("[assignment: User @{}] REST currentAssignment:", SecurityUtils.getUserId());
        AssignmentGeneralInfoVO info = assignmentService.currentAssignment(startId);
        return ResponseEntity.ok(info);
    }

    @GetMapping("/latestAssignment")
    @Timed
    public ResponseEntity<AssignmentDetailInfoDTO> findLatestAssignment(@RequestParam Long startId) {
        log.info("[assignment: User @{}] REST findLatestAssignment:", SecurityUtils.getUserId());
        AssignmentDetailInfoDTO info = assignmentService.findLatestAssignment(startId);
        return ResponseEntity.ok(info);
    }

    @GetMapping("/detail")
    @Timed
    public ResponseEntity<AssignmentDetailInfoDTO> detail(@RequestParam Long id) {
        log.info("[assignment: User @{}] REST get assignment detail :", SecurityUtils.getUserId());
        AssignmentDetailInfoDTO result = assignmentService.detail(id);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/list")
    @Timed
    public  ResponseEntity<List<AssignmentGeneralInfoVO>> list(Long  startId) {
        log.info("[assignment: User @{}] REST find assignment list:", SecurityUtils.getUserId());
        List<AssignmentGeneralInfoVO> result = assignmentService.list(startId);
        return ResponseEntity.ok(result);
    }

    /**
     * 根据startId查询淘汰的assignment 数据
     * @param talentRecruitmentProcessId
     * @return
     */
    @GetMapping("/cancelEliminate/list")
    public  ResponseEntity<AssignmentCancelEliminateInfoVO> cancelEliminateList(Long  talentRecruitmentProcessId,Long talentId) {
        log.info("[assignment: User @{}] REST to cancelEliminateList:", SecurityUtils.getUserId());
        AssignmentCancelEliminateInfoVO result = assignmentService.assignmentList(talentRecruitmentProcessId,talentId);
        return ResponseEntity.ok().body(result);
    }

    /**
     * 根据startId查询淘汰的assignment 数据
     * @param dto
     * @return
     */
    @PostMapping("/batch/update")
    @Timed
    @NoRepeatSubmit
    public  ResponseEntity batchUpdate(@RequestBody AssignmentBatchInfoDTO dto) {
        log.info("[assignment: User @{}] REST to batchUpdate:", SecurityUtils.getUserId());
        assignmentService.batchUpdate(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }



    @GetMapping("/payList")
    @Timed
    public  ResponseEntity<List<PayInfoVO>> findPayList(Long startId) {
        log.info("[assignment: User @{}] findPayList:", SecurityUtils.getUserId());
        List<PayInfoVO> result = assignmentService.findPayList(startId);
        return ResponseEntity.ok(result);
    }

    @DeleteMapping("/delete")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> delete(@RequestParam Long id) {
        log.info("[assignment: User @{}] delete assignment:", SecurityUtils.getUserId());
        assignmentService.delete(id);
        return ResponseEntity.ok(HttpStatus.OK.value());
    }

    /**
     * 用于onboard后，删除timesheet数据，将assignment设置为close状态
     * @param startId
     * @return
     */
    @DeleteMapping("/deleteByStartId")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> deleteByStartId(@RequestParam Long startId) {
        log.info("[assignment: User @{}] delete assignment by startId : {}", SecurityUtils.getUserId(), startId);
        assignmentService.deleteByStartId(startId);
        return ResponseEntity.ok(HttpStatus.OK.value());
    }

    /**
     * 用于onboard后，添加timesheet数据，将assignment设置为approved状态
     * @param startId
     * @return
     */
    @PostMapping("/createTimesheetByStartId")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> createTimesheetByStartId(@RequestParam Long startId) {
        log.info("[assignment: User @{}] createTimesheetByStartId: {}", SecurityUtils.getUserId(), startId);
        assignmentService.createTimesheetByStartId(startId);
        return ResponseEntity.ok(HttpStatus.OK.value());
    }

    @GetMapping("/order")
    @Timed
    public  ResponseEntity<Integer> findLatestAssignmentOrder(@RequestParam(required = true) Long startId) {
        log.info("[assignment: User @{}] findLatestAssignmentOrder by startId", SecurityUtils.getUserId(), startId);
        Integer result = assignmentService.findLatestAssignmentOrder(startId);
        return ResponseEntity.ok(result);
    }



    @GetMapping("/billingUnit")
    @Timed
    public  ResponseEntity<AssignmentPayRateInfo> getBillingUnit(@RequestParam(required = true)  Long assignmentId) {
        log.info("[assignment: User @{}] getBillingUnit: {}", SecurityUtils.getUserId(), assignmentId);
        AssignmentPayRateInfo result = assignmentService.getBillUnit(assignmentId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/paying-unit")
    @Timed
    public  ResponseEntity<AssignmentPayRateInfo> getPayingUnit(@RequestParam(required = true)  Long assignmentId) {
        log.info("[assignment: User @{}] getPayingUnit:", SecurityUtils.getUserId(), assignmentId);
        AssignmentPayRateInfo result = assignmentService.getPayUnit(assignmentId);
        return ResponseEntity.ok(result);
    }


    @PostMapping("/check-assignment-date")
    @Timed
    public ResponseEntity<AssignmentCheckDateVO> checkAssignmentDate(@RequestBody AssignmentCheckDateDTO assignmentCheckDateDTO) {
        log.info("[apn @{}] checkAssignmentDate param : {}", SecurityUtils.getUserId(), assignmentCheckDateDTO);
        AssignmentCheckDateVO assignmentCheckDateVO = new AssignmentCheckDateVO();
        try {
            assignmentService.checkAssignmentDate(assignmentCheckDateDTO);
        } catch (CustomParameterizedException e) {
            log.info("[apn @{}] checkAssignmentDate is success, have date", SecurityUtils.getUserId());
            assignmentCheckDateVO.setMessage(e.getMessage());
            return ResponseEntity.ok(assignmentCheckDateVO);
        }
        log.info("[apn @{}] checkAssignmentDate is success, no date", SecurityUtils.getUserId());
        return ResponseEntity.ok(assignmentCheckDateVO);
    }

//    @Deprecated
//    @GetMapping("/history-data-process")
//    public void historyDataProcess() {
//        assignmentService.historyDataProcess();
//    }

    @PostMapping("/find-current-assignment-list")
    public ResponseEntity<List<CurrentContractorVO>> findCurrentContractorList(@RequestBody CurrentContractorDTO dto, Pageable pageable) {
        log.info("[apn @{}] find current assignment list , param {}, page = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto), JSONUtil.toJsonStr(pageable));
        Page<CurrentContractorVO> page = assignmentService.findCurrentContractorPage(dto, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/jobdiva");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/current-assignment-list-excel")
    public void currentContractorListExcel(@RequestBody CurrentContractorDTO dto, HttpServletResponse response) {
        log.info("[apn @{}] current assignment list excel, param {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        assignmentService.currentContractorListExcel(dto, response);
    }

    @PostMapping("/activities")
    public ResponseEntity<List<AssignmentActivityVO>> getActivities(@RequestBody List<Long> assigmentIds, Pageable pageable) throws IOException {
        log.info("[APN: Es AssignmentActivity @{}] REST request to query assignment: {} activity.", SecurityUtils.getUserId(), assigmentIds);
        Page<AssignmentActivityVO> jobActivities = assignmentService.getJobActivities(assigmentIds, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(jobActivities, "/api/v3/assignment/activities");
        return new ResponseEntity<>(jobActivities.getContent(), headers, HttpStatus.OK);
    }

    @DeleteMapping("/job/{jobId}")
    public ResponseEntity<JSONArray> deleteAssignmentByJobId(@PathVariable("jobId") Long jobId) throws IOException {
        log.info("[APN: Es AssignmentActivity @{}] REST request to delete assignment by jobId: {}", SecurityUtils.getUserId(), jobId);
        JSONArray result = assignmentService.deleteAssignmentByJobId(jobId);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
