package com.altomni.apn.jobdiva.service.onboarding;


import com.altomni.apn.jobdiva.service.dto.onboarding.SearchDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackagesDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingSignatureDTO;

import java.util.List;

public interface OnBoardingSettingsService {

    List<OnBoardingDocumentsDTO> getAllDocuments();

    OnBoardingDocumentsDTO getByDocumentId(Long id);

    void deleteByDocumentId(Long id);

    OnBoardingDocumentsDTO updateByDocumentId(OnBoardingDocumentsDTO dto);

    OnBoardingDocumentsDTO saveDocument(OnBoardingDocumentsDTO dto);

    List<OnBoardingPackageDocumentsDTO> findDocumentsByPackageId(Long id);

    List<OnBoardingPackageDocumentsDTO> findAllDocumentsByPackageId(Long id, SearchDocumentsDTO conditionDto);

    List<OnBoardingPackagesDTO> getAllPackages();

    OnBoardingPackagesDTO getByPackageId(Long id);

    void deleteByPackageId(Long id);

    OnBoardingPackagesDTO updateByPackageId(OnBoardingPackagesDTO dto);

    OnBoardingPackagesDTO savePackage(OnBoardingPackagesDTO dto);

    void saveRelations(Long packageId, List<OnBoardingPackageDocumentsDTO> dto);

    void deleteDocumentInPackage(Long packageId, Long documentId);

    OnBoardingSignatureDTO getSignature();

    void saveSignature(OnBoardingSignatureDTO dto);

}
