package com.altomni.apn.jobdiva.domain.onboarding;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.ApprovalStatus;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.ApprovalStatusConverter;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "onBoarding process approval details. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "onboarding_process_approval_details")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingProcessApprovalDetails extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "process_id")
    private String processId;

    @Column(name = "history_id")
    private Long historyId;

    @Column(name = "s3_key")
    private String s3Key;

    @Column(name = "approval_status")
    @Convert(converter = ApprovalStatusConverter.class)
    private ApprovalStatus approvalStatus;

    @Column
    private String ip;

}
