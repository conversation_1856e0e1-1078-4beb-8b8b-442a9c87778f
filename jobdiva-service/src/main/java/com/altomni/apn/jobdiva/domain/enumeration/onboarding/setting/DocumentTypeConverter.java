package com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class DocumentTypeConverter extends AbstractAttributeConverter<DocumentType, Integer> {
    public DocumentTypeConverter() {
        super(DocumentType::toDbValue, DocumentType::fromDbValue);
    }
}
