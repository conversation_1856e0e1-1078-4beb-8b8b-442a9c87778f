package com.altomni.apn.jobdiva.service.dto.timesheet;

import java.util.Objects;

public class TimesheetGoogleHolidayDTO {

    private String holidayDate;

    private String hours;

    private String companyName;

    private Long companyId;

    private Long id;

    public String getHolidayDate() {
        return holidayDate;
    }

    public void setHolidayDate(String holidayDate) {
        this.holidayDate = holidayDate;
    }

    public String getHours() {
        return hours;
    }

    public void setHours(String hours) {
        this.hours = hours;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public int hashCode() {
        return Objects.hash(holidayDate,companyId);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TimesheetGoogleHolidayDTO dto = (TimesheetGoogleHolidayDTO) o;
        return Objects.equals(holidayDate, dto.holidayDate) &&
                Objects.equals(companyId, dto.companyId);
    }
}
