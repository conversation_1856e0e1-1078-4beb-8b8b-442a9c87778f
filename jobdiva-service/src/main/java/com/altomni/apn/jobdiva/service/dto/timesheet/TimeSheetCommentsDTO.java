package com.altomni.apn.jobdiva.service.dto.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * A record
 */
@ApiModel(description = "time sheet comment search")
@Data
public class TimeSheetCommentsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "work date")
    private String workDate;

    @ApiModelProperty(value = "assignmentId")
    private Long  assignmentId;

    @ApiModelProperty(value = "type")
    private CommentsType type;

    private Long recordId;

}
