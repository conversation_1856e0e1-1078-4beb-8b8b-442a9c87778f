package com.altomni.apn.jobdiva.service.vo.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.GroupInvoiceStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceStatusType;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import io.swagger.annotations.ApiModel;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;

/**
 * invoice search List
 */
@ApiModel(description = "ContractorInvoiceListVO")
public class ContractorInvoiceListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigInteger id;
    private Timestamp invoiceDate;
    private Timestamp createdDate;
    private String invoiceNumber;
    private String groupInvoiceNumber;
    private BigInteger groupInvoiceId;
    private String groupInvoiceStatus;
    private String employeeName;
    private BigInteger employeeId;
    private String invoiceType;
    private String invoiceStatus;
    private BigDecimal invoiceAmount;
    private String billingCompany;
    private BigInteger companyId;
    private String assignmentDivision;
    private String sentBy;
    private Timestamp sentOn;

    private String currency;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public Timestamp getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Timestamp invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public String getGroupInvoiceNumber() {
        return groupInvoiceNumber;
    }

    public void setGroupInvoiceNumber(String groupInvoiceNumber) {
        this.groupInvoiceNumber = groupInvoiceNumber;
    }

    public BigInteger getGroupInvoiceId() {
        return groupInvoiceId;
    }

    public void setGroupInvoiceId(BigInteger groupInvoiceId) {
        this.groupInvoiceId = groupInvoiceId;
    }

    public String getGroupInvoiceStatus() {
        return groupInvoiceStatus;
    }

    public void setGroupInvoiceStatus(String groupInvoiceStatus) {
        this.groupInvoiceStatus = StringUtils.isNotBlank(groupInvoiceStatus) ? GroupInvoiceStatus.getNameFromDbValue(Integer.valueOf(groupInvoiceStatus)) : "";;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public BigInteger getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(BigInteger employeeId) {
        this.employeeId = employeeId;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType != null ? InvoiceType.getNameFromDbValue(Integer.valueOf(invoiceType)) : "";
    }

    public String getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(String invoiceStatus) {
        this.invoiceStatus = StringUtils.isNotBlank(invoiceStatus) ? InvoiceStatusType.getNameFromDbValue(Integer.valueOf(invoiceStatus)) : "";
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public String getBillingCompany() {
        return billingCompany;
    }

    public void setBillingCompany(String billingCompany) {
        this.billingCompany = billingCompany;
    }

    public BigInteger getCompanyId() {
        return companyId;
    }

    public void setCompanyId(BigInteger companyId) {
        this.companyId = companyId;
    }

    public String getAssignmentDivision() {
        return assignmentDivision;
    }

    public void setAssignmentDivision(String assignmentDivision) {
        this.assignmentDivision = StringUtils.isNotBlank(assignmentDivision) ? AssignmentDivision.getNameFormDbValue(Integer.valueOf(assignmentDivision)) : "";
    }

    public String getSentBy() {
        return sentBy;
    }

    public void setSentBy(String sentBy) {
        this.sentBy = sentBy;
    }

    public Timestamp getSentOn() {
        return sentOn;
    }

    public void setSentOn(Timestamp sentOn) {
        this.sentOn = sentOn;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
