package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.jobdiva.domain.timesheet.TimesheetGoogleHoliday;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDate;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface TimeSheetGoogleHolidayRepository extends JpaRepository<TimesheetGoogleHoliday, Long> {

    TimesheetGoogleHoliday findByTenantIdAndHolidayDayAndCompanyId(Long tenantId, LocalDate holidayDay, Long companyId);
}
