package com.altomni.apn.jobdiva.config.rabbit;

import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Configuration
@RefreshScope
public class QueueConfig {

    @Resource(name = "invoiceRabbitAdmin")
    private RabbitAdmin invoiceRabbitAdmin;

    @Value("${application.invoice-mq.exchange}")
    private String exchange;

    @Value("${application.invoice-mq.queue}")
    private String queue;

    @Value("${application.invoice-mq.routing-key}")
    private String routingKey;

    @PostConstruct
    public void invoiceRabbitInit() {
        //声明交换机
        invoiceRabbitAdmin.declareExchange(new DirectExchange(exchange));

        //声明队列
        invoiceRabbitAdmin.declareQueue(new Queue(queue, true));

        //绑定队列及交换机
        invoiceRabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(queue, true)).to(new DirectExchange(exchange)).with(routingKey));
    }

    public String getExchange() {
        return exchange;
    }

    public void setExchange(String exchange) {
        this.exchange = exchange;
    }

    public String getQueue() {
        return queue;
    }

    public void setQueue(String queue) {
        this.queue = queue;
    }

    public String getRoutingKey() {
        return routingKey;
    }

    public void setRoutingKey(String routingKey) {
        this.routingKey = routingKey;
    }
}
