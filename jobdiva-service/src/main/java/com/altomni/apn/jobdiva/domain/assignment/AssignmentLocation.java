package com.altomni.apn.jobdiva.domain.assignment;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A record
 */
@Entity
@Data
@Table(name = "assignment_location")
public class AssignmentLocation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;


    @Column(name = "assignment_id", nullable = false)
    private Long assignmentId;

    @Column(name = "city", nullable = false)
    private String  city;

    @Column(name = "province", nullable = false)
    private String  province;

    @Column(name = "province_code", nullable = false)
    private String provinceCode;

    @Column(name = "country", nullable = false)
    private String  country;

    @Column(name = "country_code")
    private String  countryCode;

    @Column(name = "detailed_address", nullable = false)
    private String  detailedAddress;

    @Column(name = "zip_code", nullable = false)
    private String  zipCode;

    @Column(name = "time_zone", nullable = false)
    private String  timeZone;



}
