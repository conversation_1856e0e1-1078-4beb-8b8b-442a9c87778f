package com.altomni.apn.jobdiva.repository.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.PayRateContentType;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface PayRateRepository extends JpaRepository<AssignmentPayRateInfo, Long> {

    void deleteByAssignmentId(Long assignmentId);

    List<AssignmentPayRateInfo> findAllByAssignmentId(Long assignmentId);

    List<AssignmentPayRateInfo> findByAssignmentIdAndContentType(Long assignmentId, PayRateContentType type);

    @Query(value = " select * from assignment_pay_rate where content_type = 0 and pay_type = 2 and assignment_id = ?1 ",nativeQuery = true)
    AssignmentPayRateInfo findBillUnit(Long assignmentId);

    @Query(value = " select * from assignment_pay_rate where content_type = 1 and pay_type = 3 and assignment_id = ?1 ",nativeQuery = true)
    AssignmentPayRateInfo findPayUnit(Long assignmentId);

    @Query(value = " select * from assignment_pay_rate where content_type = 0 and pay_type = 2 and assignment_id in ?1 ",nativeQuery = true)
    List<AssignmentPayRateInfo> findBillUnitByAssignmentIds(Set<Long> assignmentIdSet);

    List<AssignmentPayRateInfo> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
