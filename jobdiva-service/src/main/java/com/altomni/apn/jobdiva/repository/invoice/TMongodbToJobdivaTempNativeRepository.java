package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorInvoiceCreateDTO;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorInvoiceTimesheetDTO;
import com.altomni.apn.jobdiva.service.dto.invoice.MongodbToJobDivaTempDTO;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Repository
@Slf4j
public class TMongodbToJobdivaTempNativeRepository {

    @Resource
    private EntityManager entityManager;


    /**
     * 查询timesheet数据用于生成invoice
     * Query timesheet data for generating invoices
     *
     * @param flag 1-timesheet 2-expense
     * @return
     */
    @Transactional(readOnly = true)
    public List<ContractorInvoiceTimesheetDTO> queryTimesheetAndExpenseList(Long assignmentId, String from, String to, Integer flag, Long tenantId) {

        StringBuilder dataSql = new StringBuilder("select t.tenant_id as tenantId,t.talent_id as talentId,\n" +
                "tal.full_name as talentName,\n" +
                "t.job_id as jobId,t.job_title as jobTitle,t.assignment_id as assignmentId,\n" +
                "t.work_date as weekEndingDate,\n" +
                "t.company_id as companyId,t.company_name as companyName,\n" +
                "ass.contact_id as companyContactId,ct.full_name as companyContactName,\n" +
                "t.approved_date as approvalDate,  \n" +
                "case when t.am_approver is not null then t.am_approver \n" +
                "else t.primary_manager end as approverName,\n" +
                "case when t.am_approver is not null then t.am_approver_id \n" +
                "else t.primary_manager_id end as approverId,cast(t.assignment_division as char) as assignmentDivision,al.country_code as countryCode,\n");
        if (flag.equals(InvoiceType.REGULAR.toDbValue())) {
            dataSql.append(" t.regular_hours as regularHours,t.over_time as overTime,t.double_time as doubleTime");
            dataSql.append(" from time_sheet_week_ending_record t");
        } else {
            dataSql.append(" t.cost,t.submitted_date as expenseSubmitDate ");
            dataSql.append(" from time_sheet_expense_week_ending_record t");
        }

        dataSql.append(" left join assignment_location al on al.assignment_id = t.assignment_id " +
                " left join talent tal on tal.id = t.talent_id\n" +
                " left join assignment_bill_info ass on ass.assignment_id = t.assignment_id\n" +
                " left join company_sales_lead_client_contact con on con.id = ass.contact_id left join talent ct on ct.id = con.talent_id where t.`status`=4 ");

        dataSql.append(" and t.work_date between :startDate and :to and t.assignment_id = :assignmentId and t.tenant_id = :tenantId ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("startDate", from);
        dataQuery.setParameter("to", to);
        dataQuery.setParameter("assignmentId", assignmentId);
        dataQuery.setParameter("tenantId", tenantId);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceTimesheetDTO.class));

        return dataQuery.getResultList();
    }


    /**
     * 根据group number 查询 invoice id
     * @param groupNumber
     * @param tenantId
     * @return
     */
    public List<MongodbToJobDivaTempDTO> queryInvoiceIdByTempGroupNumber(String groupNumber, Long tenantId) {

        entityManager.clear();

        StringBuilder dataSql = new StringBuilder("select ci.id as invoiceId,t.id,t.group_invoice_number as groupInvoiceNumber,\n" +
                "cast(t.group_invoice_status as char) as groupInvoiceStatus,t.invoice_date as InvoiceDate,cast(t.group_invoice_type as char) as groupInvoiceType \n" +
                "from t_mongodb_to_jobdiva_temp t\n" +
                " join t_contractor_invoice ci on ci.id = t.invoice_id\n" +
                "where t.group_invoice_number= :groupNumber and t.`status`=3 and ci.tenant_id= :tenantId");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("groupNumber", groupNumber);
        dataQuery.setParameter("tenantId", tenantId);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(MongodbToJobDivaTempDTO.class));
        return dataQuery.getResultList();
    }
}