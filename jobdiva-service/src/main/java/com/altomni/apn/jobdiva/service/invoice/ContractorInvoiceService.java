package com.altomni.apn.jobdiva.service.invoice;

import cn.hutool.json.JSONArray;
import com.altomni.apn.jobdiva.service.dto.invoice.*;
import com.altomni.apn.jobdiva.service.vo.invoice.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.Collection;
import java.util.List;

public interface ContractorInvoiceService {

    ContractorInvoiceCreateVO save(ContractorInvoiceCreateDTO dto);

    Long update(ContractorInvoiceEditDTO dto);

    Page<ContractorInvoiceListVO> searchInvoiceList(ContractorInvoiceSearchDTO dto, Pageable pageable);

    void invoiceVoid(ContractorInvoiceVoidAndPrintDTO dto);

    void ungroup(ContractorInvoiceVoidAndPrintDTO dto);

    Page<ContractorInvoiceFailedVO> searchFailedTimesheetAndExpenseInfo(ContractorInvoiceCreateDTO dto, Pageable pageable, boolean flag);

    void downloadFailedTimesheetAndExpense(ContractorInvoiceCreateDTO dto, HttpServletResponse response);

    void downloadGroupFailedInvoice(ContractorInvoiceCreateDTO dto, HttpServletResponse response);

    ContractorInvoiceViewVO view(BigInteger id);

    void print(ContractorInvoiceVoidAndPrintDTO dto, HttpServletResponse response);

    List<ContractorInvoiceCheckInvoiceVO> getIdByAssignmentIdAndWeekEndingDate(ContractorInvoiceExistDTO dto);

    String dataMigrateInvoice();

    String migrateInvoiceByDeleteCaTax();

    Page<ContractorInvoiceSearchByApplicationVO> searchPageByApplication(ContractorInvoiceSearchByApplicationDTO dto);

    JSONArray deleteByAssignment(Collection<Long> assignmentIds);
}
