package com.altomni.apn.jobdiva.service.dto.onboarding.settings;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingPackages;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingPackagesDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @NotNull(message = "package name can not be null")
    private String name;

    private String description;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    public OnBoardingPackagesDTO (Long id, String name, String description, String lastModifiedBy, Instant lastModifiedDate){
        this.id = id;
        this.name = name;
        this.description = description;
        this.lastModifiedBy = lastModifiedBy;
        this.lastModifiedDate = lastModifiedDate;
    }

    public static OnBoardingPackages fromDto(OnBoardingPackagesDTO dto) {
        OnBoardingPackages packages = new OnBoardingPackages();
        ServiceUtils.myCopyProperties(dto, packages);
        packages.setTenantId(SecurityUtils.getTenantId());
        return packages;
    }
}
