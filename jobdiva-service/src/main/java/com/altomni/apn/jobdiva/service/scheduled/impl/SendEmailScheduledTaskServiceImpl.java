package com.altomni.apn.jobdiva.service.scheduled.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentStatusType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.StringUtil;
import com.altomni.apn.jobdiva.config.JobdivaNoClockInEmailConfig;
import com.altomni.apn.jobdiva.config.env.ApplicationProperties;
import com.altomni.apn.jobdiva.config.env.ScheduledProperties;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentTimeSheet;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.repository.assignment.AssignmentContributionRepository;
import com.altomni.apn.jobdiva.repository.assignment.AssignmentLocationRepository;
import com.altomni.apn.jobdiva.repository.timesheet.*;
import com.altomni.apn.jobdiva.service.mail.MailService;
import com.altomni.apn.jobdiva.service.scheduled.SendEmailScheduledTaskService;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentContributionVO;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service("sendEmailScheduledTaskService")
public class SendEmailScheduledTaskServiceImpl implements SendEmailScheduledTaskService {

    private static final Integer TIME_9AM = 9;

    private static final Integer TIME_5PM = 17;

    private static final Integer TIME_3PM = 15;

    private static final Integer FRIDAY_OF_WEEK = -2;

    private static final Integer SATURDAY_OF_WEEK = -1;

    private static final List<TimeSheetStatus> SUBMIT_STATUS_LIST = CollUtil.newArrayList(TimeSheetStatus.REJECTED, TimeSheetStatus.APPLIED_APPROVE, TimeSheetStatus.APPROVED);

    private static final List<TimeSheetStatus> NO_SUBMIT_STATUS_LIST = CollUtil.newArrayList(TimeSheetStatus.MISSING, TimeSheetStatus.NO_RECORD, TimeSheetStatus.REJECTED, TimeSheetStatus.DRAFT);

    @Resource
    private AssignmentContributionRepository assignmentContributionRepository;

    @Resource
    private AssignmentLocationRepository assignmentLocationRepository;

    @Resource
    private TalentAssignmentRepository talentAssignmentRepository;

    @Resource
    private TimeSheetManagerRepository timeSheetManagerRepository;

    @Resource
    private TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    private TimeSheetUserRepository timeSheetUserRepository;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private TimeSheetRepository timeSheetRepository;

    @Resource
    private MailService mailService;

    @Resource
    private ScheduledProperties scheduledProperties;

    @Resource
    private JobdivaNoClockInEmailConfig jobdivaNoClockInEmailConfig;

    private static final String AM_RECEIVE_EMAIL_JUMP_PATH = "/timesheetAM/#/manageTimesheets/TimesheetDetails";


    /**
     * 在assignment 快到期的前N天发送通知提醒给am
     */
    @Scheduled(cron = "0 0 9 * * ?", zone = "UTC")
    @Override
    public void sendEmailAssignmentExpirationReminder() {
        List<TalentAssigment> talentAssigmentList = talentAssignmentRepository.findAllAssignmentByStatus(AssignmentStatusType.APPROVED.toDbValue());
        LoginUtil.simulateLoginWithClient();
        List<String> companyNoSendEmailList = getNoSendEmailByCompanyIdList();
        SecurityContext context = SecurityContextHolder.getContext();
        talentAssigmentList.forEach(talentAssigment -> {
            SecurityContextHolder.setContext(context);
            Instant instantNow = Instant.now();
            ZonedDateTime utcNow = instantNow.atZone(ZoneOffset.UTC);
            LocalTime utcLocalTime = utcNow.toLocalTime();
            LocalDate endDate = talentAssigment.getEndDate();
            Instant endInstant = endDate.atStartOfDay().toInstant(ZoneOffset.UTC);
            log.info("send email expiration reminder, days = {}, hour = {}", Duration.between(instantNow, endInstant).toDays(), utcLocalTime.getHour());
            if (Objects.equals(Duration.between(instantNow, endInstant).toDays(), scheduledProperties.getExpirationReminderDayNum()) && Objects.equals(utcLocalTime.getHour(), TIME_9AM)) {
                // Gets the am email address to be sent
                List<Object[]> emailDataList = getDataToSent(talentAssigment);
                if (CollUtil.isEmpty(emailDataList)) {
                    return;
                }
                emailDataList.parallelStream().forEach(emailData -> {
                    SecurityContextHolder.setContext(context);
                    String amFirstName = StringUtil.valueOf(emailData[0]);
                    String amEmailAddress = StringUtil.valueOf(emailData[1]);
                    String companyName = StringUtil.valueOf(emailData[2]);
                    String talentName = StringUtil.valueOf(emailData[3]);
                    String subject = "[Reminder] Contract Renewal - " + talentName + " at " + companyName + " - " + scheduledProperties.getExpirationReminderDayNum() + "Days to Go";
                    String content = "<html ><body><h2>Hi " + amFirstName + ",</h2>" +
                            "<p>This is an automated reminder to notify you that the contract for " + talentName + " with </p>" +
                            "<p>" + companyName + " is set to expire in " + scheduledProperties.getExpirationReminderDayNum() + " days on " + endDate + ". As the account  </p>" +
                            "<p>manager, please get in touch with both the contractor and the client to discuss their plans for </p>" +
                            "<p>contract renewal or extension. </p>" +
                            "<p>If you require any assistance, feel free to reach out to your manager or the support team. </p>" +
                            "<p>Best regards,</p>" +
                            "<p>APN Support Team</p></body></html>";
                    doSendEmail(amEmailAddress, subject, content, companyNoSendEmailList);
                    log.info("send email expiration reminder success");
                });
            }
        });
    }


    @Scheduled(cron = "0 0 * * * ?")
    public void sendEmailByHour() {
        log.info("send email by hour now = {}", Instant.now());
        List<TalentAssigment> talentAssigmentList = talentAssignmentRepository.findAllAssignmentByStatus(AssignmentStatusType.APPROVED.toDbValue());
        LoginUtil.simulateLoginWithClient();
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            sendEmailFriday5PM(talentAssigmentList);
        });
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            sendEmailSaturday3PM(talentAssigmentList);
        });
        //todo 发送邮件过多,需要业务提供更加具体的邮件发送策略,后期会做修改
//        CompletableFuture.runAsync(() -> {
//            SecurityContextHolder.setContext(context);
//            sendEmailByTimeSheetExpiredWithManager(talentAssigmentList);
//        });
    }

    @Override
    public void sendEmailFriday5PM(List<TalentAssigment> talentAssigmentList) {
        log.info("[apn@-1] send email friday 5PM start ");
        List<String> companyNoSendEmailList = getNoSendEmailByCompanyIdList();
        SecurityContext context = SecurityContextHolder.getContext();
        talentAssigmentList.forEach(talentAssignment -> {
            SecurityContextHolder.setContext(context);
            log.info("send email friday 5PM start , talentId = {}, assignmentId = {}", talentAssignment.getTalentId(), talentAssignment.getId());
            ZonedDateTime zonedDateTime = getZonedDateTime(talentAssignment);
            LocalDate nowLocalDate = zonedDateTime.toLocalDate();
            LocalTime localTime = zonedDateTime.toLocalTime();
            AssignmentTimeSheet assignmentTimeSheet = timeSheetRepository.findByAssignmentId(talentAssignment.getId());
            LocalDate endLocalDate = TimeSheetUtil.findDateInWeek(nowLocalDate, assignmentTimeSheet.getWeekEnding().toDbValue(), 1);
            log.info("send email friday 5PM, now = {}, weekEnding = {}, hour = {}, talentId = {}, flag = {}", nowLocalDate, endLocalDate, localTime.getHour(), talentAssignment.getTalentId(), endLocalDate.plusDays(FRIDAY_OF_WEEK).equals(nowLocalDate) && Objects.equals(localTime.getHour(), TIME_5PM));
            if (endLocalDate.plusDays(FRIDAY_OF_WEEK).equals(nowLocalDate) && Objects.equals(localTime.getHour(), TIME_5PM)) {
                SecurityContextHolder.setContext(context);
                TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUidLikeAndUserType(talentAssignment.getTalentId(), TimeSheetUserType.TALENT.toDbValue());
                String firstName = timeSheetUserRepository.findByTalentId(talentAssignment.getTalentId());
                if (timeSheetUser == null || StrUtil.isBlank(firstName)) {
                    return;
                }
                String subject = "Friendly Reminder: Timesheet Submission Due for This Week";
                String content = "<html ><body><h2>Dear " + firstName  + ",</h2>" +
                        "<p>I hope this email finds you well. As the end of the week is approaching, we would like to kindly </p>" +
                        "<p>remind you to submit your timesheet for the current week by the end of today.</p>" +
                        "<p>In case you need any assistance or have any questions, please do not hesitate to reach out to our </p>" +
                        "<p>support team. We are always here to help you.</p>" +
                        "<p>Have a lovely weekend!</p>" +
                        "<p>Best regards,</p>" +
                        "<p>APN Support Team</p></body></html>";
                doSendEmail(timeSheetUser.getEmail(), subject, content, companyNoSendEmailList);
                log.info("send email friday 5PM success, now = {}, weekEnding = {}, hour = {}, talentId = {}, flag = {}", nowLocalDate, endLocalDate, localTime.getHour(), talentAssignment.getTalentId(), endLocalDate.plusDays(FRIDAY_OF_WEEK).equals(nowLocalDate) && Objects.equals(localTime.getHour(), TIME_5PM));
            }
        });
        log.info("[apn@-1] send email friday 5PM end ");
    }


    @Override
    public void sendEmailSaturday3PM(List<TalentAssigment> talentAssigmentList) {
        log.info("[apn@-1] send email saturday 3PM start ");
        List<String> companyNoSendEmailList = getNoSendEmailByCompanyIdList();
        SecurityContext context = SecurityContextHolder.getContext();
        talentAssigmentList.forEach(talentAssignment -> {
            SecurityContextHolder.setContext(context);
            log.info("send email saturday 3PM start , talentId = {}, assignmentId = {}", talentAssignment.getTalentId(), talentAssignment.getId());
            ZonedDateTime zonedDateTime = getZonedDateTime(talentAssignment);
            LocalDate nowLocalDate = zonedDateTime.toLocalDate();
            LocalTime localTime = zonedDateTime.toLocalTime();
            AssignmentTimeSheet assignmentTimeSheet = timeSheetRepository.findByAssignmentId(talentAssignment.getId());
            LocalDate endLocalDate = TimeSheetUtil.findDateInWeek(nowLocalDate, assignmentTimeSheet.getWeekEnding().toDbValue(), 1);
            log.info("send email saturday 3PM, now = {}, weekEnding = {}, hour = {}, talentId = {}", nowLocalDate, endLocalDate, localTime.getHour(), talentAssignment.getTalentId());
            if (endLocalDate.plusDays(SATURDAY_OF_WEEK).equals(nowLocalDate) && Objects.equals(localTime.getHour(), TIME_3PM)) {
                LocalDate startLocal = TimeSheetUtil.findDateInWeek(nowLocalDate, assignmentTimeSheet.getWeekEnding().toDbValue(), 0).plusDays(1);
                List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findAllByDate(startLocal, endLocalDate, talentAssignment.getTalentId(), talentAssignment.getId());
                if (CollUtil.isEmpty(timeSheetRecordList)) {
                    return;
                }
                TimeSheetRecord timeSheetRecord = timeSheetRecordList.get(0);
                if (!SUBMIT_STATUS_LIST.contains(timeSheetRecord.getStatus())) {
                    log.info("send email saturday 3PM, now = {}, weekEnding = {}, hour = {}, talentId = {}, status = {}", nowLocalDate, endLocalDate, localTime.getHour(), talentAssignment.getTalentId(), timeSheetRecord.getStatus());
                    SecurityContextHolder.setContext(context);
                    TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUidLikeAndUserType(talentAssignment.getTalentId(), TimeSheetUserType.TALENT.toDbValue());
                    String firstName = timeSheetUserRepository.findByTalentId(talentAssignment.getTalentId());
                    if (timeSheetUser == null || StrUtil.isBlank(firstName)) {
                        return;
                    }
                    String subject = "Friendly Reminder: Missing Timesheet Submission Due for This Week";
                    String content = "<html ><body><h2>Dear " + firstName + ",</h2>" +
                            "<p>Hope this email finds you well. Just a friendly reminder that we haven't yet received your </p>" +
                            "<p>timesheet for the week ending " + endLocalDate + ". To ensure timely approval and processing, </p>" +
                            "<p>please submit your completed timesheet through our online portal as soon as possible.</p>" +
                            "<p>In case you have any questions or face any issues, please don't hesitate to reach out to our </p>" +
                            "<p>support team. We are always here to help you.</p>" +
                            "<p>Have a lovely weekend!</p>" +
                            "<p>Best regards,</p>" +
                            "<p>APN Support Team</p></body></html>";
                    doSendEmail(timeSheetUser.getEmail(), subject, content, companyNoSendEmailList);
                    log.info("send email saturday 3PM success, now = {}, weekEnding = {}, hour = {}, talentId = {}, status = {}", nowLocalDate, endLocalDate, localTime.getHour(), talentAssignment.getTalentId(), timeSheetRecord.getStatus());
                }
            }
        });
        log.info("[apn@-1] send email saturday 3PM end ");
    }

    /**
     * 定时任务：发送过期或到达weekending的timesheet提醒
     * 此方法是一个定时任务，使用Cron表达式控制执行周期。它查询所有已经生效的assignment，
     * 并找出其中已经过期或到达weekending的数据，然后向相关的AM、DM和Recruiter发送提醒邮件。
     */
    @Override
    public void sendEmailByTimeSheetExpiredWithManager(List<TalentAssigment> talentAssigmentList) {
        Instant startDate = Instant.now(); // 记录开始时间
        log.info("[apn@-1] send timesheet expired reminder  is start ");
        // 查询需要发送邮件的所有assignment
        if (StrUtil.isNotBlank(scheduledProperties.getTimesheetExpirationReminderTalentIds())) {
            //测试环境只发送指定的talentId 对应的assignment的数据,防止大量邮件发送
            String[] ids = scheduledProperties.getTimesheetExpirationReminderTalentIds().split(",");
            talentAssigmentList = talentAssigmentList.stream().filter(talentAssigment -> Arrays.asList(ids).contains(talentAssigment.getTalentId().toString())).toList();
        }
        // 模拟登录以获取权限
        LoginUtil.simulateLoginWithClient();
        // 获取不需要发送邮件的公司邮箱列表
        List<String> companyNoSendEmailList = getNoSendEmailByCompanyIdList();
        // 获取安全上下文
        SecurityContext context = SecurityContextHolder.getContext();
        // 准备需要接收邮件的角色列表
        List<UserRole> needEmailUserRoleList = CollUtil.newArrayList(UserRole.AM, UserRole.CO_AM, UserRole.DM, UserRole.RECRUITER);
        // 获取当前时间
        Instant instantNow = Instant.now();
        // 遍历assignment，为每个相关的人员发送提醒
        talentAssigmentList.parallelStream().forEach(talentAssignment -> {
            // 设置安全上下文
            SecurityContextHolder.setContext(context);
            // 查询未提交的timesheet记录
            List<Integer> noSubmitStatusList = NO_SUBMIT_STATUS_LIST.stream().map(TimeSheetStatus::toDbValue).toList();
            List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findExpiredRecordList(talentAssignment.getId(), noSubmitStatusList);
            if (CollUtil.isEmpty(timeSheetRecordList)) {
                return;
            }
            log.info("[apn@-1] send timesheet expired reminder  , have no submit record, assignmentId = {}, talentId = {}", talentAssignment.getId(), talentAssignment.getTalentId());
            // 当有过期的打卡记录时, 且状态符合要求的情况下, 获取候选人的TimeSheetUser信息,包含email, 这个时候直接发送email
            TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUidLikeAndUserType(talentAssignment.getTalentId(), TimeSheetUserType.TALENT.toDbValue());
            // 获取候选人的名称
            String firstName = timeSheetUserRepository.findByTalentId(talentAssignment.getTalentId());
            if (timeSheetUser != null && StrUtil.isNotBlank(timeSheetUser.getEmail())) {
                //给候选人发送打卡提醒, 也是每天早上的9点
                ZonedDateTime zonedDateTime = getZonedDateTime(talentAssignment);
                log.info("[apn@-1] send timesheet expired reminder  , assignmentId = {}, talentId = {}, timezone = {}, hour = {}", talentAssignment.getId(), talentAssignment.getTalentId(), zonedDateTime.getZone(), zonedDateTime.getHour());
                if (Objects.equals(zonedDateTime.getHour(), scheduledProperties.getTimesheetExpirationReminderTime())) {
                    Map<String, String> map = new HashMap<>();
                    map.put("firstName", firstName);
                    timeSheetRecordList.forEach(timeSheetRecord -> {
                        if (zonedDateTime.toLocalDate().isAfter(timeSheetRecord.getWorkDate())) {
                            map.put("weekEnd", timeSheetRecord.getWorkDate().toString());
                            String subject = StrUtil.format("Friendly Reminder: Timesheet Submission Due for Week {weekEnd}", map);
                            String content = StrUtil.format("""
                            <html><body><h2>Dear {firstName},</h2>
                            <p>I hope this email finds you well. This email is a reminder to submit your timesheet for the week of {weekEnd} at your earliest convenience.</p>
                            <p>In case you need any assistance or have any questions, please do not hesitate to reach out to our support team. We are always here to help you.</p>
                            <p>Best regards,</p>
                            <p>APN Support Team</p></body></html>
                            """, map);
                            doSendEmail(timeSheetUser.getEmail(), subject, content, companyNoSendEmailList);
                            log.info("[apn@-1] send timesheet expired reminder for talent is success, assignmentId = {}, talentId = {}, weekend = {}", talentAssignment.getTalentId(), talentAssignment.getTalentId(), timeSheetRecord.getWeekEnd());
                        }
                    });
                }
            }

            // 查询需要提醒的人员信息
            List<AssignmentContributionVO> assignmentContributionList = assignmentContributionRepository.findByAssignmentIdWithNameAndTimezone(talentAssignment.getId());
            if (CollUtil.isEmpty(assignmentContributionList)) {
                return;
            }
            // 过滤出需要接收邮件的角色且具有时区信息的人员
            assignmentContributionList = assignmentContributionList.stream().filter(contribution ->
                    needEmailUserRoleList.contains(contribution.getUserRole()) && StrUtil.isNotBlank(contribution.getTimezone())).collect(Collectors.toList());
            if (CollUtil.isEmpty(assignmentContributionList)) {
                return; // 如果过滤后没有人员，则跳过
            }
            // 将timesheet记录按照工作日期映射，以便后续处理
            Map<LocalDate, TimeSheetRecord> timeSheetRecordMap = timeSheetRecordList.stream().collect(Collectors.toMap(TimeSheetRecord::getWorkDate, a -> a, (a1, a2) -> a1));
            String fullName = timeSheetUserRepository.findFullNameByTalentId(talentAssignment.getTalentId());
            Map<String, String> map = new HashMap<>(); // 用于邮件内容的参数映射
            map.put("fullName", fullName);
            // 遍历过滤后的人员列表，为每(AM,DM,RECRUITER)发送定制的邮件提醒
            assignmentContributionList.forEach(contribution -> {
                // 转换当前时间到人员的时区
                ZonedDateTime zone = instantNow.atZone(ZoneId.of(contribution.getTimezone()));
                log.info("[apn@-1] send timesheet expired reminder  , assignmentId = {}, userId = {}, userRole = {}, timezone = {}, hour = {}", talentAssignment.getId(), contribution.getUserId(), contribution.getUserRole(), contribution.getTimezone(), zone.getHour());
                // 判断是否为当天早上9点（根据时区）
                if (Objects.equals(zone.getHour(), scheduledProperties.getTimesheetExpirationReminderTime())) {
                    timeSheetRecordMap.keySet().forEach(weekendDate -> {
                        TimeSheetRecord timeSheetRecord = timeSheetRecordMap.get(weekendDate);
                        map.put("weekEnd", weekendDate.toString());
                        String hrefUrl = applicationProperties.getBaseUrl() + AM_RECEIVE_EMAIL_JUMP_PATH + "?startDate=" + talentAssignment.getStartDate()
                                + "&endDate=" + talentAssignment.getEndDate() + "&id=" + timeSheetRecord.getId() + "&endingDate=" + timeSheetRecord.getWeekEndingDate()
                                + "&weekStart=" + timeSheetRecord.getWeekStart() + "&weekEnd=" + timeSheetRecord.getWeekEnd();
                        String clickableUrl = "<a href='" + hrefUrl + "'>" + hrefUrl + "</a>";
                        map.put("weekTimesheetUrl", clickableUrl);
                        // 构造邮件主题和内容  todo 去除 am 角色和名称
                        String subject = fullName + " Timesheet Missing Notification" + "for role : " + contribution.getUserRole() + " name : " + CommonUtils.formatFullName(contribution.getFirstName(), contribution.getLastName());
                        String content = """
                            <html><body><p>{fullName} has not yet submitted the timesheet for the week ending {weekEnd}.Please kindly remind the candidate to submit it at his/her earliest convenience.</p>
                            <p>Click the following link for more details: {weekTimesheetUrl}</p></body></html>
                            """;
                        content = StrUtil.format(content, map);
                        // 发送邮件，忽略不需要发送的公司邮箱
                        doSendEmail(contribution.getEmail(), subject, content, companyNoSendEmailList);
                        log.info("[apn@-1] send timesheet expired reminder for manager is success, assignmentId = {}, talentId = {}, userId = {}, weekend = {}", talentAssignment.getTalentId(), talentAssignment.getTalentId(), contribution.getUserId(), weekendDate);
                    });
                }
            });
        });
        Instant endDate = Instant.now();
        log.info("[apn@-1] send timesheet expired reminder is end, cost = {}s", Duration.between(startDate, endDate).toSeconds());
    }


    private ZonedDateTime getZonedDateTime(TalentAssigment talentAssigment) {
        log.info("assignmentId = {}", talentAssigment.getId());
        String timeZone = assignmentLocationRepository.getTimeZoneByAssignmentId(talentAssigment.getId());
        if (StrUtil.isBlank(timeZone)) {
            log.error("timeZone is null, assignmentId = {}", talentAssigment.getId());
            timeZone = DateUtil.US_LA_TIMEZONE;
        }
        Instant now = Instant.now();
        ZoneId targetZoneId = ZoneId.of(timeZone);
        return now.atZone(targetZoneId);
    }

    private List<Object[]> getDataToSent(TalentAssigment talentAssigment) {
        return timeSheetManagerRepository.findAmEmailByAssignmentId(talentAssigment.getId());
    }

    private void doSendEmail(String amEmailAddress, String subject, String content, List<String> noSendEmailList) {
        if (jobdivaNoClockInEmailConfig.getNoClockInEmail().contains(amEmailAddress)
                || (CollUtil.isNotEmpty(noSendEmailList) && noSendEmailList.contains(amEmailAddress))) {
            //jobdiva 中不需要发送邮件提醒的邮件
            log.info("jobdiva 不需要发送邮件提醒 email = {}", amEmailAddress);
            return;
        }
        List<String> emails = new ArrayList<>();
        emails.add(amEmailAddress);
        MailVM mailVM = new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, content, null, true);
        try {
            mailService.sendHtmlMail(mailVM);
        } catch (Exception e) {
            log.error("send email failed, param = {}, msg = {}", JSONUtil.toJsonStr(mailVM), ExceptionUtils.getStackTrace(e));
        }
    }

    private List<String> getNoSendEmailByCompanyIdList() {
        if (StrUtil.isBlank(scheduledProperties.getJobdivaNoClockInEmailCompanyId())) {
            return new ArrayList<>();
        }
        List<Long> companyIdList = Arrays.stream(scheduledProperties.getJobdivaNoClockInEmailCompanyId().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        if (CollUtil.isEmpty(companyIdList)) {
            return new ArrayList<>();
        }
        return talentAssignmentRepository.findEmailByCompanyId(companyIdList);
    }

}
