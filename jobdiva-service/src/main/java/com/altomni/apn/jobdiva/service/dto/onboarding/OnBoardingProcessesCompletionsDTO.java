package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.ApprovalStatus;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.OperationStatus;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.ActionRequiredType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingProcessesCompletionsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long talentRecruitmentProcessId;

    private String processId;

    private Long documentId;

    private String documentName;

    private String documentNameUploaded;

    private String s3Key;

    private String documentSourceName;

    private String s3KeySource;

    private String s3KeyRejected;

    private ActionRequiredType actionRequired;

    private CompletionStatus completionStatus;

    private ApprovalStatus approvalStatus;

    private Instant assignedOnDate;

    private Instant lastApprovalDate;

    private String lastApprovalBy;

    private OperationStatus operationStatus;

    private Instant lastOperationDate;

    private String lastOperationBy;

    private String operationDetails;

    private String approvalDetails;

    private String onboardingType;

    private String packageName;
}
