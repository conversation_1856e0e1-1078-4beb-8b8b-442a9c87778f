package com.altomni.apn.jobdiva.config.thread;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingDocuments;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.service.common.CommonService;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.util.FillPDFIInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class FillDocumentsThreadTask extends CopyTokenChildThread {

    private final CountDownLatch countDownLatch;

    private final OnBoardingDocuments document;

    private final StoreService storeService;

    private final List<OnBoardingDocuments> fillDocumentsList;

    private final Long talentId;

    private final Long talentRecruitmentProcessId;

    private final String mainPath;

    private final EnumCurrencyService enumCurrencyService;

    private TalentAssignmentRepository assignmentRepository;

    private CommonService commonService;

    private final String timeZone;

    public FillDocumentsThreadTask(StoreService storeService, CountDownLatch countDownLatch, OnBoardingDocuments document
            , List<OnBoardingDocuments> fillDocumentsList, Long talentId, Long talentRecruitmentProcessId, String mainPath, EnumCurrencyService enumCurrencyService
            , TalentAssignmentRepository assignmentRepository, CommonService commonService, String timeZone) {
        super();
        this.countDownLatch = countDownLatch;
        this.storeService = storeService;
        this.document = document;
        this.fillDocumentsList = fillDocumentsList;
        this.talentId = talentId;
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
        this.mainPath = mainPath;
        this.enumCurrencyService = enumCurrencyService;
        this.assignmentRepository = assignmentRepository;
        this.commonService = commonService;
        this.timeZone = timeZone;
    }

    @Override
    public void runTask() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            //download pdf file with s3Link
            CloudFileObjectMetadata fileObject = storeService.downloadDocument(CommonUtils.splitS3LinkFromUrl(document.getS3Key()), UploadTypeEnum.ONBOARDING.getKey()).getBody();
            if (ObjectUtil.isNull(fileObject)) {
                log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , document s3Link is invalid .", SecurityUtils.getUserId());
                throw new NotFoundException("document s3Link is invalid .", SecurityUtils.getUserId());
            }
            //fill pdf info
            OnBoardingDocuments doc = new OnBoardingDocuments();
            doc.setId(document.getId());
            doc.setS3Key(FillPDFIInfoUtil.fillPDFInfo(enumCurrencyService, assignmentRepository, commonService, fileObject, talentId, talentRecruitmentProcessId, mainPath, timeZone));
            fillDocumentsList.add(doc);
        } catch (Exception e) {
            log.error("[apn] fillPdfDocumentInfo is error = [{}]", ExceptionUtils.getStackTrace(e));
        } finally {
            countDownLatch.countDown();
            stopWatch.stop();
            log.info("[apn] fillPdfDocumentInfo time = [{}ms], ids = [{}]", stopWatch.getTotalTimeMillis(), document.getId());
        }
    }

}
