package com.altomni.apn.jobdiva.service.vo.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.CalculateMethodType;
import com.altomni.apn.common.domain.enumeration.jobdiva.OverTimeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.altomni.apn.jobdiva.service.vo.assignment.WeekDataVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@ApiModel
public class EndingDateListVO {

    private LocalDate startDate;
    private LocalDate endDate;
    private List<WeekDataVO> dates;
    private String companyName;
    private String jobTitle;
    private Long jobId;
    private Long assignmentId;
    private CalculateMethodType calculateMethodType;
    private TimeSheetType timeSheetType;
    private OverTimeType overTimeType;
    @ApiModelProperty(value = "Returns whether overtime is exempt")
    private Boolean isExcept;


}
