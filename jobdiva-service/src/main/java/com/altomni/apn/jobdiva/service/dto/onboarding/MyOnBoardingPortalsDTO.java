package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MyOnBoardingPortalsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long talentRecruitmentProcessId;

    private String processId;

    private String jobTitle;

    private LocalDate startDate;

    private List<OnBoardingProcessesCompletionsDTO> todos;

    private List<OnBoardingProcessesCompletionsDTO> completions;

}
