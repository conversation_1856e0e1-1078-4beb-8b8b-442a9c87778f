package com.altomni.apn.jobdiva.domain.onboarding;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.*;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsDTO;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@ApiModel(description = "onBoarding documents entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "onboarding_documents")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingDocuments extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @Column(nullable = false)
    private String name;

    @Column(name = "s3_key")
    private String s3Key;

    @Column(name = "action_required")
    @Convert(converter = ActionRequiredTypeConverter.class)
    private ActionRequiredType actionRequired;

    @Column(name = "special_document")
    @Convert(converter = SpecialDocumentTypeConverter.class)
    private SpecialDocumentType specialDocument;

    @Column(name = "document_type")
    @Convert(converter = DocumentTypeConverter.class)
    private DocumentType documentType;

    @ApiModelProperty(value = "Whether document is activated. Default is true.")
    @NotNull
    @Column(nullable = false)
    private boolean activated = true;

    public static OnBoardingDocumentsDTO toDto(OnBoardingDocuments document) {
        OnBoardingDocumentsDTO dto = new OnBoardingDocumentsDTO();
        ServiceUtils.myCopyProperties(document, dto);
        return dto;
    }
}
