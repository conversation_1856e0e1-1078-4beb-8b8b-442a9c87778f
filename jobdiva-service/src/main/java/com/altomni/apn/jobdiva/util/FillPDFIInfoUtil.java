package com.altomni.apn.jobdiva.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.talent.TalentEducationDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.dto.translation.TextTranslationDTO;
import com.altomni.apn.common.dto.translation.TranslationResultDTO;
import com.altomni.apn.common.enumeration.enums.LanguageCode;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SpringUtil;
import com.altomni.apn.job.domain.enumeration.start.StartType;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.service.common.CommonService;
import com.altomni.apn.jobdiva.service.dto.onboarding.TagInfoDTO;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentBriefInfoVO;
import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.forms.fields.PdfTextFormField;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import lombok.experimental.UtilityClass;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManager;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.hutool.core.util.ReUtil.RE_CHINESES;
import static com.altomni.apn.jobdiva.constants.Constants.*;


/**
 * set timeSheet util
 *
 * <AUTHOR>
 */
@UtilityClass
public class FillPDFIInfoUtil {

    private final Logger log = LoggerFactory.getLogger(FillPDFIInfoUtil.class);

    private EntityManager getEntityManager() {
        return SpringUtil.getBean(EntityManager.class);
    }

    private StoreService getStoreService() {
        return SpringUtil.getBean(StoreService.class);
    }

    private static String DATE_FORMAT_MM_DD_YYYY = "MM/dd/yyyy";

    public String fillPDFInfo(EnumCurrencyService enumCurrencyService, TalentAssignmentRepository assignmentRepository
            , CommonService commonService, CloudFileObjectMetadata fileObject, Long talentId, Long talentRecruitmentProcessId, String mainPath, String timeZone) {
        //get template data
        Map<String, Object> data = new HashMap<>();
        //1.对data数据提取到异步之前，查询到data后，再传for循环中的每个异步处理任务
        StopWatch stopWatch = new StopWatch("echocheng5 start");
        stopWatch.start("[echocheng 5.1] getTemplateData");
        getTemplateData(enumCurrencyService, assignmentRepository, commonService, talentId, talentRecruitmentProcessId, data, mainPath, timeZone);
        stopWatch.stop();
        //initialize itext
//        BaseFont baseFont = null;
        String s3Key = null;
        //文件输出路径
        try(ByteArrayInputStream inputStream = new ByteArrayInputStream(fileObject.getContent());
            com.itextpdf.kernel.pdf.PdfReader reader = new com.itextpdf.kernel.pdf.PdfReader(inputStream);
            // 使用内存流替代文件写入
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            com.itextpdf.kernel.pdf.PdfWriter writer = new com.itextpdf.kernel.pdf.PdfWriter(outputStream);
            com.itextpdf.kernel.pdf.PdfDocument pdfDoc = new com.itextpdf.kernel.pdf.PdfDocument(reader, writer)) {

            /// 处理PDF表单
            stopWatch.start("[echocheng 5.2] processPDF");
            PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDoc, true);
            form.getFormFields().forEach((key, item) -> {
                if(ObjectUtil.isNotNull(data.get(key))) {
                    PdfTextFormField jobDescField = (PdfTextFormField) item;
                    jobDescField.setValue(StrUtil.toString(data.get(key)));
                    jobDescField.regenerateField();
                }
            });

            // 必须关闭PdfDocument以确保数据写入完成
            pdfDoc.close();

            // 生成MultipartFile（直接从内存获取字节）
            String fileName = URLDecoder.decode(fileObject.getFileName(), StandardCharsets.UTF_8.name());
            byte[] pdfBytes = outputStream.toByteArray();
            MultipartFile multipartFile = new MockMultipartFile(
                    fileName,
                    fileName,
                    MediaType.APPLICATION_PDF_VALUE,
                    new ByteArrayInputStream(pdfBytes)
            );
            stopWatch.stop();
            // 上传到S3
            stopWatch.start("[echocheng 5.3] upload s3");
            s3Key = DigestUtils.md5Hex(multipartFile.getInputStream());
            getStoreService().uploadDocument(multipartFile, s3Key, UploadTypeEnum.ONBOARDING.getKey());
            stopWatch.stop();
            log.info("[apn @{}] echocheng5 time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        } catch (IOException e) {
            log.error("PDF Error", e);
            throw new RuntimeException("PDF Error", e);
        }
        return s3Key;
    }

    private void getTemplateData(EnumCurrencyService enumCurrencyService, TalentAssignmentRepository assignmentRepository, CommonService commonService
            , Long talentId, Long talentRecruitmentProcessId, Map<String, Object> data, String mainPath, String timeZone) {
        List<Object[]> queryResult = new ArrayList<>();
        String dataSql = """
                SELECT
                                                                                    	c.full_business_name,
                                                                                    	j.title AS jobTitle,
                                                                                    	j.id,
                                                                                    	j.CODE,
                                                                                    	u.username,
                                                                                    	jl.official_city,
                                                                                    	jl.official_province,
                                                                                    	apr.currency,
                                                                                    	apr.rate_unit_type,
                                                                                    	apr.agreed_pay_rate,
                                                                                    	j.start_date,
                                                                                    	j.end_date,
                                                                                    	jai.summary AS summary,
                                                                                    	jai.responsibilities AS responsibilities,
                                                                                    	jai.requirements AS requirements,
                                                                                    	t.first_name,
                                                                                    	t.last_name,
                                                                                    	t.full_name,
                                                                                    	tc.contact AS email,
                                                                                    	tcc.contact AS phone,
                                                                                    	tai.extended_info ->> '$.experiences' AS experiences,
                                                                                    	tai.extended_info ->> '$.educations' AS educations,
                                                                                    	tai.extended_info ->> '$.nickFirstName' AS nickFirstName,
                                                                                    	tai.extended_info ->> '$.nickLastName' AS nickLastName,
                                                                                    	tcl.original_loc AS currentLocation,
                                                                                    	GROUP_CONCAT( DISTINCT CONCAT( u.first_name, ' ', u.last_name ) SEPARATOR ',' ) amNames,
                                                                                    	l.locations,
                                                                                    	s.start_date s_start_date,
                                                                                    	s.end_date s_end_date,
                                                                                    	s.tvc_number,
                                                                                    	tcl.zip_code,
                                                                                    	case when sci.client_info_id is null  then sci.client_name
                                                                                        when itc.label ='china fte' then cici.client_name
                                                                                        else ici.client_name end as client_name,
                                                                                        case when sci.client_info_id is null  then sci.client_address
                                                                                        when itc.label ='china fte' then cici.invoicing_address
                                                                                        else ici.client_address end as client_address,
                                                                                    	scr.currency scr_currency,
                                                                                    	scr.rate_unit_type scr_rate_unit_type,
                                                                                    	scr.final_pay_rate,
                                                                                    	cc.contact AS companyPhone, 
                                                                                    	GROUP_CONCAT(DISTINCT IF(u.job_title != '', u.job_title, NULL) SEPARATOR ',') AS amJobTitles,
                                                                                    	al.detailed_address detailed_address,
                                                                                    	j.contract_duration
                                                                                    FROM
                                                                                    	talent_recruitment_process a
                                                                                    	LEFT JOIN job j ON a.job_id = j.id
                                                                                    	LEFT JOIN company c ON j.company_id = c.id
                                                                                    	LEFT JOIN company_contact cc on cc.company_id = c.id and cc.type=?8
                                                                                    	LEFT JOIN talent t ON a.talent_id = t.id
                																		LEFT JOIN talent_recruitment_process_kpi_user ku on ku.talent_recruitment_process_id = a.id
                                                                                    	LEFT JOIN USER u ON ku.user_id = u.id
                                                                                    	LEFT JOIN job_additional_info jai ON jai.id = j.additional_info_id
                                                                                    	LEFT JOIN talent_additional_info tai ON t.additional_info_id = tai.id
                                                                                    	LEFT JOIN talent_current_location tcl ON tcl.talent_id = t.id
                                                                                    	LEFT JOIN (
                                                                                    	SELECT
                                                                                    		loc.job_id,
                                                                                    		concat( '[', GROUP_CONCAT( loc.original_loc ORDER BY loc.id ASC ), ']' ) AS locations
                                                                                    	FROM
                                                                                    		job_location loc
                                                                                    	WHERE
                                                                                    		loc.original_loc IS NOT NULL
                                                                                    	GROUP BY
                                                                                    		loc.job_id
                                                                                    	) l ON l.job_id = j.id
                                                                                    	LEFT JOIN (
                                                                                    	SELECT
                                                                                    		job_id,
                                                                                    		GROUP_CONCAT(
                                                                                    		DISTINCT
                                                                                    		IF
                                                                                    		( official_city = '', NULL, official_city )) AS official_city,
                                                                                    		GROUP_CONCAT(
                                                                                    		DISTINCT
                                                                                    		IF
                                                                                    		( official_province = '', NULL, official_province )) AS official_province
                                                                                    	FROM
                                                                                    		job_location
                                                                                    	GROUP BY
                                                                                    		job_id
                                                                                    	) jl ON j.id = jl.job_id
                                                                                    	LEFT JOIN ( SELECT talent_id, contact FROM talent_contact WHERE STATUS = ?1 AND jhi_type = ?2 AND talent_id = ?4 ORDER BY sort LIMIT 1 ) tc ON t.id = tc.talent_id
                                                                                    	LEFT JOIN ( SELECT talent_id, contact FROM talent_contact WHERE STATUS = ?1 AND jhi_type = ?3 AND talent_id = ?4 ORDER BY sort LIMIT 1 ) tcc ON t.id = tcc.talent_id
                                                                                    	LEFT JOIN talent_recruitment_process_ipg_agreed_pay_rate apr ON a.id = apr.talent_recruitment_process_id
                                                                                    	LEFT JOIN (
                                                                                         SELECT 
                                                                                           id,
                                                                                           talent_recruitment_process_id,
                                                                                           company_id,
                                                                                           start_date,
                                                                                           end_date,
                                                                                           tvc_number
                                                                                         FROM (
                                                                                           SELECT
                                                                                             id,
                                                                                             talent_recruitment_process_id,
                                                                                             company_id,
                                                                                             start_date,
                                                                                             end_date,
                                                                                             tvc_number,
                                                                                             ROW_NUMBER() OVER (
                                                                                               PARTITION BY talent_recruitment_process_id
                                                                                               ORDER BY created_date DESC
                                                                                             ) as rn
                                                                                           FROM
                                                                                             START
                                                                                           WHERE
                                                                                             start_type IN ( ?6 )
                                                                                         ) s_sub
                                                                                         WHERE s_sub.rn = 1
                                                                                       ) s ON s.talent_recruitment_process_id = a.id
                                                                                    	LEFT JOIN start_client_info sci ON sci.start_id = s.id
                                                                                    	LEFT JOIN invoice_type_config itc on itc.id = sci.invoice_type_id
                                                                                    	LEFT JOIN company_invoice_client_info ici on ici.id = sci.client_info_id
                                                                                    	LEFT JOIN company_client_invoicing_info cici on cici.id = sci.client_info_id
                                                                                    	LEFT JOIN (
                                                                                          SELECT
                                                                                          start_id,
                                                                                          currency,
                                                                                          rate_unit_type,
                                                                                          final_pay_rate
                                                                                          FROM (
                                                                                           SELECT
                                                                                           *,
                                                                                           ROW_NUMBER() OVER (
                                                                                             PARTITION BY start_id
                                                                                             ORDER BY created_date DESC
                                                                                             ) as rate_rank
                                                                                           FROM
                                                                                             start_contract_rate
                                                                                           ) ranked_rates
                                                                                           WHERE rate_rank = 1
                                                                                         ) scr ON scr.start_id = s.id
                                                                                    	left join (select min(id) as id, talent_recruitment_process_id from timesheet_talent_assignment group by talent_recruitment_process_id) tta on tta.talent_recruitment_process_id = a.id
                                                                                    	left join assignment_location al on al.assignment_id = tta.id
                                                                                    WHERE
                                                                                    	a.id = ?5 and ku.user_role in ( ?7 )
                                                                                    	LIMIT 1
                       """;
        queryResult = getEntityManager().createNativeQuery(dataSql)
                .setParameter(1, TalentContactStatus.AVAILABLE.toDbValue())
                .setParameter(2, ContactType.EMAIL.toDbValue())
                .setParameter(3, ContactType.PHONE.toDbValue())
                .setParameter(4, talentId)
                .setParameter(5, talentRecruitmentProcessId)
                .setParameter(6, Arrays.asList(StartType.CONTRACT_NEW_HIRE.toDbValue(), StartType.CONTRACT_EXTENSION.toDbValue()))
                .setParameter(7, Arrays.asList(UserRole.AM.toDbValue(),UserRole.CO_AM.toDbValue()))
                .setParameter(8, ContactType.PHONE.toDbValue()).getResultList();
        if (CollectionUtil.isNotEmpty(queryResult)) {
            queryResult.forEach(s -> {
                data.put("{{{COMPANYNAME}}}", s[0]);
                data.put("{{{JOBTITLE}}}", s[1]);
                data.put("{{{JOBID}}}", s[2]);
                data.put("{{{JOBCODE}}}", s[3]);
                data.put("{{{JOBLINK}}}", mainPath + "/jobs/detail/" + s[2]);
                if (ObjectUtil.isNotNull(s[25])) {
                    data.put("{{{ACCOUNTMANAGER}}}", StrUtil.toString(s[25]));
                }
//                data.put("{{{JOBCITY}}}", s[5]);
//                data.put("{{{JOBSTATE}}}", s[6]);
                if (ObjectUtil.isNotNull(s[7]) && ObjectUtil.isNotNull(s[8])) {
                    data.put("{{{AGREEDPAYUNIT}}}", Objects.requireNonNull(enumCurrencyService.findEnumCurrencyById(Integer.parseInt(StrUtil.toString(s[7])))).getName()
                            + StrUtil.SLASH
                            + RateUnitType.fromDbValue(Integer.parseInt(StrUtil.toString(s[8]))));
                }
                if (ObjectUtil.isNotNull(s[9])) {
                    data.put("{{{AGREEDPAYRATE}}}", StrUtil.toString(s[9]));
                }
                if (ObjectUtil.isNotNull(s[10]) && ObjectUtil.isNotNull(s[11])) {
                    data.put("{{{MONTHDURATION}}}", cn.hutool.core.date.DateUtil.betweenMonth(cn.hutool.core.date.DateUtil.parseDateTime(StrUtil.toString(s[10]))
                            , cn.hutool.core.date.DateUtil.offsetDay(cn.hutool.core.date.DateUtil.parseDateTime(StrUtil.toString(s[11])), 1), false));
                }
                StringBuilder stringBuilder = new StringBuilder();
                if (ObjectUtil.isNotNull(s[13])) {
                    stringBuilder.append(StrUtil.toString(s[13]));
                }
                if (ObjectUtil.isNotNull(s[14])) {
                    stringBuilder.append(StrUtil.toString(s[14]));
                }
                if (ObjectUtil.isNotNull(s[12])) {
                    stringBuilder.append(StrUtil.toString(s[12]));
                }
                if (ObjectUtil.isNotNull(stringBuilder)) {
                    data.put("{{{JOBDESCRIPTION}}}", convertHtmlToPdfFormText(stringBuilder.toString()));
                }
                data.put("{{{CANDIDATEFIRSTNAME}}}", s[15]);
                data.put("{{{CANDIDATELASTNAME}}}", s[16]);
//                if (ObjectUtil.isNotNull(s[15]) && ObjectUtil.isNotNull(s[16])) {
//                    StringBuilder pinyin = new StringBuilder();
//                    getFirstInitialByName(pinyin, s[15]);
//                    pinyin.append(" ");
//                    getFirstInitialByName(pinyin, s[16]);
//                    data.put("{{{INITIAL}}}", pinyin.toString().toUpperCase());
//                }
                data.put("{{{STARTSTARTDATE}}}", StringDateFormat(StrUtil.toString(s[27]), DATE_FORMAT_MM_DD_YYYY));
                if (ObjectUtil.isNotNull(s[27])) {
                    data.put("{{{STARTSTARTDATE+3M}}}", StringDateFormat(StringDateAddOrReduce(StrUtil.toString(s[27]), true, false, 3), DATE_FORMAT_MM_DD_YYYY));
                    data.put("{{{STARTSTARTDATE-1D}}}", StringDateFormat(StringDateAddOrReduce(StrUtil.toString(s[27]), false, true, 1), DATE_FORMAT_MM_DD_YYYY));
                }
                data.put("{{{STARTENDDATE}}}", StringDateFormat(StrUtil.toString(s[28]), DATE_FORMAT_MM_DD_YYYY));
                data.put("{{{TVCNUMBER}}}", s[29]);
                data.put("{{{TODAYDATE}}}", StringDateFormat(Instant.now().atZone(ZoneId.of(StrUtil.isNotBlank(timeZone) ? timeZone : DateUtil.US_LA_TIMEZONE)).toLocalDate().toString(), DATE_FORMAT_MM_DD_YYYY));
                data.put("{{{TODAYDATE+5D}}}", StringDateFormat(Instant.now().atZone(ZoneId.of(StrUtil.isNotBlank(timeZone) ? timeZone : DateUtil.US_LA_TIMEZONE)).toLocalDate().plusDays(5).toString(), DATE_FORMAT_MM_DD_YYYY));
                data.put("{{{ZIPCODE}}}", s[30]);
                data.put("{{{CANDIDATEFULLNAME}}}", s[17]);
                data.put("{{{CANDIDATEEMAIL}}}", s[18]);
                data.put("{{{CANDIDATEPHONE}}}", s[19]);
                //experiences
                if (ObjectUtil.isNotNull(s[20])) {
                    List<TalentExperienceDTO> experienceDTOList = JSONUtil.toList(JSONUtil.parseArray(ObjectUtil.toString(s[20])), TalentExperienceDTO.class);
                    data.put("{{{CANDIDATECOMPANY}}}", experienceDTOList.stream().findFirst().get().getCompanyName());
                    data.put("{{{CANDIDATETITLE}}}", experienceDTOList.stream().findFirst().get().getTitle());
                }
                //educations
                if (ObjectUtil.isNotNull(s[21])) {
                    List<TalentEducationDTO> educationDTOList = JSONUtil.toList(JSONUtil.parseArray(ObjectUtil.toString(s[21])), TalentEducationDTO.class);
                    data.put("{{{CANDIDATESCHOOL}}}", educationDTOList.stream().findFirst().get().getCollegeName());
                }
                if (ObjectUtil.isNotNull(s[22])) {
                    data.put("{{{NICKFIRSTNAME}}}", StrUtil.toString(s[22]));
                }
                if (ObjectUtil.isNotNull(s[23])) {
                    data.put("{{{NICKLASTNAME}}}", StrUtil.toString(s[23]));
                }
                if (ObjectUtil.isNotNull(s[24])) {
                    LocationDTO location = JSONUtil.toBean(StrUtil.toString(s[24]), LocationDTO.class);
                    if (ObjectUtil.isNotNull(location)) {
                        if (StrUtil.isNotBlank(location.getLocation())) {
                            data.put("{{{CANDIDATELOCATION}}}", location.getLocation());
                        }
                        if (StrUtil.isNotBlank(location.getCity())) {
                            data.put("{{{CANDIDATECITY}}}", location.getCity());
                        }
                        if (StrUtil.isNotBlank(location.getProvince())) {
                            data.put("{{{CANDIDATESTATE}}}", location.getProvince());
                        }
                    }
                }
                if (ObjectUtil.isNotNull(s[26])) {
                    List<LocationDTO> locationDtoList = JSONUtil.toList(JSONUtil.parseArray(StrUtil.toString(s[26])), LocationDTO.class);
                    if (CollUtil.isNotEmpty(locationDtoList)) {
                        String jobLocation = locationDtoList.stream().map(LocationDTO::getLocation).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
                        data.put("{{{JOBLOCATION}}}", jobLocation);
                        String city = locationDtoList.stream().map(LocationDTO::getCity).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
                        data.put("{{{JOBCITY}}}", city);
                        String province = locationDtoList.stream().map(LocationDTO::getProvince).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
                        data.put("{{{JOBSTATE}}}", province);
                    }
                }
                data.put("{{{INVOICEINFOCLIENTNAME}}}", s[31]);
                data.put("{{{INVOICEINFOCLIENTADDRESS}}}", s[32]);
                if (ObjectUtil.isNotNull(s[33]) && ObjectUtil.isNotNull(s[34])) {
                    data.put("{{{STARTPAYRATEUNIT}}}", Objects.requireNonNull(enumCurrencyService.findEnumCurrencyById(Integer.parseInt(StrUtil.toString(s[33])))).getName()
                            + StrUtil.SLASH
                            + RateUnitType.fromDbValue(Integer.parseInt(StrUtil.toString(s[34]))));
                }
                data.put("{{{STARTPAYRATE}}}", s[35]);
                data.put("{{{COMPANYPHONENUMBER}}}", s[36]);
                data.put("{{{USERJOBTITLE}}}", s[37]);
                data.put("{{{ASSIGNMENTWORKINGADDRESS}}}", s[38]);
                if (!data.containsKey("{{{MONTHDURATION}}}") && ObjectUtil.isNotNull(s[39])){
                    // 将s[39]除以30作为MONTHDURATION的值，取值为整数部分
                    data.put("{{{MONTHDURATION}}}", String.valueOf(Integer.parseInt(StrUtil.toString(s[39])) / 30));
                }
            });
        }
        //set assignment tags
        setAssignmentTags(assignmentRepository, data, talentRecruitmentProcessId);
        //set other language tags
        setOtherLanguageTags(commonService, data);
    }

    public static String convertHtmlToPdfFormText(String html) {
        Document doc = Jsoup.parse(html);

        // 1. 处理br标签为CRLF（iText 5.x要求）
        doc.select("br").forEach(br -> {
            br.replaceWith(new TextNode("\r\n"));  // 网页1/网页8
        });

        // 2. 段落间距处理
        doc.select("p, div").forEach(el -> {
            el.before(new TextNode("\r\n"));
            el.after(new TextNode("\r\n\r\n"));  // 网页4
        });

        // 3. 禁用自动换行干扰
        doc.outputSettings().prettyPrint(false);

        // 4. 生成纯文本并清理
        String rawText = doc.body().html()
                .replaceAll("<[^>]+>", "")
                .replaceAll("(\r\n){3,}", "\r\n\r\n")
                .trim();

        // 5. 转义特殊字符（如&符号）
        return StringEscapeUtils.unescapeHtml4(rawText);
    }

    private static void setOtherLanguageTags(CommonService commonService, Map<String, Object> data) {
        //translate jobTitle to FR language
        if (ObjectUtil.isNotNull(data.get("{{{JOBTITLE}}}"))) {
            TextTranslationDTO paramDTO = new TextTranslationDTO();
            paramDTO.setText(StrUtil.toString(data.get("{{{JOBTITLE}}}")));
            paramDTO.setTargetLanguage(LanguageCode.FR);

            TranslationResultDTO resultDTO = commonService.getTextTranslate(paramDTO).getBody();
            if (ObjectUtil.isNotEmpty(resultDTO) && ObjectUtil.isNotEmpty(resultDTO.getData()) && ObjectUtil.isNotNull(resultDTO.getData().getText())) {
                data.put("{{{JOBTITLE_FR}}}", resultDTO.getData().getText());
            }
        }

    }

    private void setAssignmentTags(TalentAssignmentRepository assignmentRepository, Map<String, Object> data
            , Long talentRecruitmentProcessId) {
        List<TalentAssigment> assignments = assignmentRepository.findAllByTalentRecruitmentProcessIdAndStartType(talentRecruitmentProcessId, StartType.CONTRACT_NEW_HIRE.toDbValue());
        if (CollUtil.isNotEmpty(assignments)) {
            Long startId = assignments.stream().findAny().get().getStartId();
            LocalDate recently = LocalDate.now();
            if (assignments.size() == 1) {
                recently = assignments.get(0).getStartDate();
            }
            AssignmentBriefInfoVO assigment = assignmentRepository.findBriefByDateIn(recently, startId);
            if (ObjectUtil.isNull(assigment)) {
                Long min = null;
                LocalDate now = LocalDate.now();
                for (TalentAssigment talentAssigment : assignments) {
                    if (talentAssigment.getStartDate().isEqual(now) || talentAssigment.getEndDate().isEqual(now) ||
                            (talentAssigment.getStartDate().isBefore(now) && talentAssigment.getEndDate().isAfter(now))) {
                        recently = talentAssigment.getStartDate();
                        break;
                    }
                    long startAbs = Math.abs(talentAssigment.getStartDate().toEpochDay() - now.toEpochDay());
                    long endAbs = Math.abs(talentAssigment.getEndDate().toEpochDay() - now.toEpochDay());
                    long assignmentMin = Math.min(startAbs, endAbs);
                    if (min == null || min >= assignmentMin) {
                        min = assignmentMin;
                        recently = talentAssigment.getStartDate();
                    }
                }
                assigment = assignmentRepository.findBriefByDateIn(recently, startId);
            }
            //set tags
            if (ObjectUtil.isNotNull(assigment)) {
                if (ObjectUtil.isNotNull(assigment.getWorkingHours())) {
                    data.put("{{{ASSIGNMENTWORKINGHOURS}}}", assigment.getWorkingHours());
                }
                if (ObjectUtil.isNotNull(assigment.getProvince())) {
                    data.put("{{{ASSIGNMENTPROVINCE}}}", assigment.getProvince());
                }
                if (ObjectUtil.isNotNull(assigment.getZipCode())) {
                    data.put("{{{ASSIGNMENTZIPCODE}}}", assigment.getZipCode());
                }
            }
        }
    }

    private static String StringDateAddOrReduce(String timeStr, Boolean isAdd, Boolean isDayUnit, int count) {
        // 定义日期时间格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 将字符串转换为LocalDateTime对象
        LocalDate originalDate = LocalDate.parse(timeStr, formatter);
        // 增加/减少    日/月
        LocalDate resultDate = null;
        if (isAdd) {
            if (isDayUnit) {
                resultDate = originalDate.plusDays(count);
            } else {
                resultDate = originalDate.plusMonths(count);
            }
        } else {
            if (isDayUnit) {
                resultDate = originalDate.minusDays(count);
            } else {
                resultDate = originalDate.minusMonths(count);
            }
        }
        // 将结果格式化为相同的字符串格式
        return resultDate.format(formatter);
    }

    private static String StringDateFormat(String inputDateStr, String format){
        java.util.Date inputDate = cn.hutool.core.date.DateUtil.parse(inputDateStr);
        // 将日期对象格式化为 "MM-dd-yyyy" 格式的字符串
        return cn.hutool.core.date.DateUtil.format(inputDate, format);
    }

    private static void getFirstInitialByName(StringBuilder pinyin, Object o) {
        if (ReUtil.isMatch(RE_CHINESES, StrUtil.toString(o))) {
            String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(StrUtil.toString(o).toCharArray()[0]);
            if (pinyinArray != null && pinyinArray.length > 0) {
                pinyin.append(pinyinArray[0].charAt(0));
            } else {
                pinyin.append(StrUtil.toString(o));
            }
        } else {
            pinyin.append(StrUtil.toString(o).charAt(0));
        }
    }

    public static List<TagInfoDTO> getTagsInfo(CloudFileObjectMetadata fileObject) {
        List<TagInfoDTO> result = new ArrayList<>();
        File baseFile = new File(System.getProperty("java.io.tmpdir") + "onBoardingPDF");
        String path = baseFile.getAbsolutePath() + File.separator + IdUtil.simpleUUID() + ".pdf";
        File f = null;
        PdfReader pdfReader = null;
        FileOutputStream outputStream = null;
        try {
            f = new File(path);
            if (!baseFile.exists()) {
                baseFile.mkdir();
            }
            f.createNewFile();
            pdfReader = new PdfReader(fileObject.getContent());
            outputStream = new FileOutputStream(f);
            PdfStamper pdfStamper = new PdfStamper(pdfReader, outputStream);
            AcroFields form = pdfStamper.getAcroFields();
            // 检查指定标签   initial tags
            List<AcroFields.FieldPosition> signaturePointList = null;
            signaturePointList = form.getFieldPositions(INITIAL_TAG);
            if(CollUtil.isNotEmpty(signaturePointList)){
                signaturePointList.forEach(s ->{
                    TagInfoDTO tagInfo = new TagInfoDTO(s.position.getLeft(), s.position.getRight(), s.position.getBottom()
                            , s.position.getTop(), s.page, TAG_TYPE_INPUT, INITIAL_TAG);
                    result.add(tagInfo);
                });
            }
            // 检查指定标签   checkbox tags
            Map<String, AcroFields.Item> fields = form.getFields();
            // 遍历所有表单域
            List<String> requiredTags = new ArrayList<>();
            List<String> optionalTags = new ArrayList<>();
            for (Map.Entry<String, AcroFields.Item> entry : fields.entrySet()) {
                String fieldName = entry.getKey();
                AcroFields.Item item = entry.getValue();
                // 获取字段的字典对象
                PdfDictionary dict = item.getMerged(0);
                // 判断表单域类型是否为复选框
                if (dict.get(PdfName.FT).equals(PdfName.BTN)) {
                    //过滤掉不需要处理的checkbox tag
                    if (fieldName.contains(CHECKBOX_TAG_DEFAULT_CONTAINS)) {
                        // 获取复选框的位置信息
                        List<AcroFields.FieldPosition> positions = form.getFieldPositions(fieldName);
                        // 输出复选框的位置信息
                        for (AcroFields.FieldPosition fieldPosition : positions) {
                            Rectangle rect = fieldPosition.position;
                            TagInfoDTO tagInfo = new TagInfoDTO(rect.getLeft(), rect.getRight(), rect.getBottom()
                                    , rect.getTop(), fieldPosition.page, TAG_TYPE_CHECKBOX, fieldName
                                    , form.getField(fieldName).equals(CHECKBOX_CHECK_ACTION) ? CHECKBOX_DISPLAY_TRUE : CHECKBOX_DISPLAY_FALSE);
                            result.add(tagInfo);
                        }
                    }
                }else {
                    //非checkbox类型的tag
                    if (fieldName.contains(REQUIRED_TAG_PRE)) {
                        requiredTags.add(fieldName);
                    } else if (fieldName.contains(OPTIONAL_TAG_PRE)) {
                        optionalTags.add(fieldName);
                    }
                }
            }
            // 检查指定标签   required tags
            if(CollUtil.isNotEmpty(requiredTags)) {
                requiredTags.forEach(tag -> {
                    List<AcroFields.FieldPosition> requiredTagPointList = null;
                    requiredTagPointList = form.getFieldPositions(tag);
                    requiredTagPointList.forEach(s -> {
                        TagInfoDTO tagInfo = new TagInfoDTO(s.position.getLeft(), s.position.getRight(), s.position.getBottom()
                                , s.position.getTop(), s.page, TAG_TYPE_INPUT, tag);
                        result.add(tagInfo);
                    });
                });
            }

            // 检查指定标签   Optional tags
            if(CollUtil.isNotEmpty(optionalTags)) {
                optionalTags.forEach(tag -> {
                    List<AcroFields.FieldPosition> optionalTagPointList = null;
                    optionalTagPointList = form.getFieldPositions(tag);
                    optionalTagPointList.forEach(s -> {
                        TagInfoDTO tagInfo = new TagInfoDTO(s.position.getLeft(), s.position.getRight(), s.position.getBottom()
                                , s.position.getTop(), s.page, TAG_TYPE_INPUT, tag);
                        result.add(tagInfo);
                    });
                });
            }

            //设置order字段
            if(CollUtil.isNotEmpty(result)){
                IntStream.range(0, result.size()).forEach(i -> result.get(i).setOrder(i + 1));
            }
        } catch (DocumentException | IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if (pdfReader != null) {
                    pdfReader.close();
                }
                if (f != null) {
                    f.delete();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                log.error("error", e);
            }
        }
        return result;
    }


}
