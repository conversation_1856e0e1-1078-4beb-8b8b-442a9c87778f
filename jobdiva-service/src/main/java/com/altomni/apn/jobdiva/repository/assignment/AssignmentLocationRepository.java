package com.altomni.apn.jobdiva.repository.assignment;

import com.altomni.apn.jobdiva.domain.assignment.AssignmentLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface AssignmentLocationRepository extends JpaRepository<AssignmentLocation, Long> {

    void deleteByAssignmentId(Long assignmentId);

    AssignmentLocation findByAssignmentId(Long assignmentId);

    @Query(value = " select distinct cle.time_zone from assignment_location al left join city_locations_en cle on al.city = cle.city_name where al.assignment_id = ?1 and al.country_code = cle.country_iso_code and al.province = cle.subdivision_1_name group by al.assignment_id ", nativeQuery = true)
    String getTimeZoneByAssignmentId(Long assignmentId);

    List<AssignmentLocation> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
