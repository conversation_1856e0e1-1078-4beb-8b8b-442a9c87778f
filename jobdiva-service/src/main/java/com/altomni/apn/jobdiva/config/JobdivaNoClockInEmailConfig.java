package com.altomni.apn.jobdiva.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "jobdiva")
public class JobdivaNoClockInEmailConfig {

    private List<String> noClockInEmail;

}
