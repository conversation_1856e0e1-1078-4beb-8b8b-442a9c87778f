package com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The SpecialDocumentType enumeration.
 */
public enum DocumentType implements ConvertedEnum<Integer> {
    BACKGROUND_CHECK(0,"BACKGROUND_CHECK"),
    DRUG_TEST(1,"DRUG_TEST"),
    EMPLOYMENT_AGREEMENT(2,"EMPLOYMENT_AGREEMENT"),
    I_9_MATERIALS(3,"I-9_MATERIALS"),
    OFFER_LETTER(4,"OFFER_LETTER"),
    PERSONAL_INFORMATION_FORM(5,"PERSONAL_INFORMATION_FORM"),
    TAX_FORM(6,"TAX_FORM");

    // static resolving:
    public static final ReverseEnumResolver<DocumentType, Integer> resolver =
        new ReverseEnumResolver<>(DocumentType.class, DocumentType::toDbValue);
    private final int dbValue;
    private final String name;

    DocumentType(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static DocumentType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
