package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

@ApiModel(description = "contractorInvoiceCreateDTO")
@Data
public class ContractorInvoiceEditDTO implements Serializable {

    private BigInteger id;

    private String poNumber;

    private String clientInvoiceNumber;

    private String note;

    private InvoiceType invoiceType;

    private BigDecimal totalAmount;

    List<InvoiceTimesheetInfoDTO> regularInvoiceList;
}
