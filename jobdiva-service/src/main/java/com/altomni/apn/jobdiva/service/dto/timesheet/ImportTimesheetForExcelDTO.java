package com.altomni.apn.jobdiva.service.dto.timesheet;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ImportTimesheetForExcelDTO {

    private String talentName;

    private String companyName;

    private BigDecimal regularHours;

    private BigDecimal overTimes;

    private BigDecimal doubleTimes;

    private String weekEnding;

    private Integer index;

    public BigDecimal getRegularHours() {
        if (ObjectUtil.isNull(regularHours)) {
            return BigDecimal.ZERO;
        }
        return regularHours;
    }

    public BigDecimal getOverTimes() {
        if (ObjectUtil.isNull(overTimes)) {
            return BigDecimal.ZERO;
        }
        return overTimes;
    }

    public BigDecimal getDoubleTimes() {
        if (ObjectUtil.isNull(doubleTimes)) {
            return BigDecimal.ZERO;
        }
        return doubleTimes;
    }
}
