package com.altomni.apn.jobdiva.domain.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A record
 */
@Entity
@Data
@Table(name = "assignment_timesheet")
public class AssignmentTimeSheet implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @Column(name = "assignment_id", nullable = false)
    private Long assignmentId;

    @Column(name = "timesheet_type", nullable = false)
    private TimeSheetType timeSheetType ;

    @Column(name = "calculate_type", nullable = false)
    @Convert(converter = CalculateMethodTypeConverter.class)
    private CalculateMethodType calculateType ;

    @Column(name = "frequency", nullable = false)
    @Convert(converter = TimeSheetFrequencyTypeConverter.class)
    private TimeSheetFrequencyType frequency;

    @Column(name = "week_ending", nullable = false)
    @Convert(converter = WeekEndingTypeConverter.class)
    private WeekEndingType weekEnding;

    @Column(name = "allow_submit_Timesheet", nullable = false)
    private Boolean allowSubmitTimeSheet;

    @Column(name = "allow_submit_expense", nullable = false)
    private Boolean allowSubmitExpense;

    @Column(name = "instructions", nullable = false)
    private String instructions;



}
