package com.altomni.apn.jobdiva.service.vo.assignment;

import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AssignmentCheckDateVO {

    private String message;

    private List<TimeSheetRecord> timeSheetRecordList;

    private List<ExpenseRecord> expenseRecordList;

}
