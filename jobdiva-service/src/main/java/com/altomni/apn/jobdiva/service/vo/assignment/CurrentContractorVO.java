package com.altomni.apn.jobdiva.service.vo.assignment;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivisionConverter;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.AssignmentDivisionDataConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
public class CurrentContractorVO implements AttachConfidentialTalent {

    @Id
    @ExcelIgnore
    private Long assignmentId;

    @ExcelProperty(index = 0, value = "Candidate Name")
    private String fullName;

    @ExcelIgnore
    private Long talentId;

    @ExcelProperty(index = 1, value = "Job Title")
    private String title;

    @ExcelProperty(index = 2, value = "Job ID")
    private Long jobId;

    @ExcelProperty(index = 3, value = "Recruiter")
    private String recruiter;

    @ExcelProperty(index = 4, value = "Sourcer")
    private String sourcer;

    @ExcelProperty(index = 5, value = "AM")
    private String am;

    @ExcelProperty(index = 6, value = "DM")
    private String dm;

    @ExcelProperty(index = 7, value = "AC")
    private String ac;

    @ExcelProperty(index = 8, value = "Assignment Division", converter = AssignmentDivisionDataConverter.class)
    @Convert(converter = AssignmentDivisionConverter.class)
    private AssignmentDivision assignmentDivision;

    @ExcelProperty(index = 9, value = "Start Date")
    private String startDate;

    @ExcelProperty(index = 10, value = "End Date")
    private String endDate;

    @ExcelProperty(index = 11, value = "Working City")
    private String city;

    @ExcelProperty(index = 12, value = "Working State")
    private String province;

    @ExcelProperty(index = 13, value = "Hourly Bill Rate")
    private String hourlyBillRate;

    @ExcelIgnore
    private String symbol;

    @ExcelIgnore
    @Transient
    private Boolean isPrivateJob;

    public String getHourlyBillRate() {
        if (symbol == null || hourlyBillRate == null) {
            return null;
        }
        return symbol + hourlyBillRate;
    }


    @Transient
    @ExcelIgnore
    private Boolean confidentialTalentViewAble;

    @Transient
    @ExcelIgnore
    private ConfidentialInfoDto confidentialInfo;


    @Override
    public void encrypt() {
        this.fullName = null;
        this.title = null;
        this.recruiter = null;
        this.sourcer = null;
        this.jobId = null;
        this.am = null;
        this.dm = null;
        this.ac = null;
        this.assignmentDivision = null;
        this.startDate = null;
        this.endDate = null;
        this.city = null;
        this.province = null;
        this.hourlyBillRate = null;
        this.symbol = null;
        this.assignmentId = null;
        this.isPrivateJob = null;
    }
}
