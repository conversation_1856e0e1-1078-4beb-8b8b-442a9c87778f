package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.NoHourDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.RecordSearchDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.SummaryQueryDTO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;

import java.util.List;

public interface TimeSheetRecordService {

    List<TimeSheetRecord> saveRecord(BreakTimeDTO dto, Long tenantId, Long talentId, Boolean isAm);

    List<TimeSheetRecord> saveRecordForDateSelect(BreakTimeDTO dto, Long userId, Long tenantId, Boolean isAm);

    BreakTimeDTO findRecords(RecordSearchDTO dto, Long talentId, Long assignmentId);

    BreakTimeDTO findRecordById(RecordSearchDTO dto);

    BreakTimeDTO findBreakTimeRecord(RecordSearchDTO timeSheetRecord,Long talentId);

    Integer saveBreakTime(BreakTimeDTO dto,Long talentId,Long tenantId, Boolean isAm);

    SummaryDataVO summary(SummaryQueryDTO dto, Long talentId);

    Integer noHour(NoHourDTO dates, Long talentId, Boolean isAm);

}
