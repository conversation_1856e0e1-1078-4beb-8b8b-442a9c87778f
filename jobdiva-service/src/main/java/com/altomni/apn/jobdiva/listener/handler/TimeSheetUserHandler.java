package com.altomni.apn.jobdiva.listener.handler;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetUserRepository;
import com.altomni.apn.user.repository.permission.PermissionRoleRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TimeSheetUserHand<PERSON> implements JobdivaToApnHandler {

    @Resource
    private TimeSheetUserRepository timeSheetUserRepository;

    @Resource
    private PermissionRoleRepository roleRepository;

    @Override
    public void execute(JSONObject jsonObject) {
        log.info("[apn TimeSheetUserHandler] jobdiva sync timeSheetUser start");
        if (jsonObject.isEmpty()) {
            return;
        }
        TimeSheetUserDTO userDto = jsonObject.toJavaObject(TimeSheetUserDTO.class);
        Set<Role> roles = userDto.getRoles().stream().map(role -> {
            return roleRepository.getById(role.getId());
        }).collect(Collectors.toSet());
        TimeSheetUser timeSheetUser = Convert.convert(TimeSheetUser.class, userDto);
        timeSheetUser.setCreatedBy(jsonObject.getString("createdBy"));
        timeSheetUser.setLastModifiedBy(jsonObject.getString("lastModifiedBy"));
        timeSheetUser.setRoles(roles);
        log.info("[apn TimeSheetUserHandler] jobdiva sync timeSheetUser id = {}", userDto.getId());
        timeSheetUserRepository.save(timeSheetUser);
        log.info("[apn TimeSheetUserHandler] jobdiva sync timeSheetUser id = {} , success", userDto.getId());
    }

    @Override
    public boolean isSupport(JobdivaDataSyncTypeEnum typeEnum) {
        return typeEnum == JobdivaDataSyncTypeEnum.TIME_SHEET_USER;
    }

}
