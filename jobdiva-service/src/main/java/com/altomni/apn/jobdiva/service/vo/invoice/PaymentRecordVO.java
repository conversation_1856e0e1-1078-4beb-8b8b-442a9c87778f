package com.altomni.apn.jobdiva.service.vo.invoice;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;

@ApiModel(description = "PaymentRecordVO")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaymentRecordVO implements Serializable {

    private Timestamp paymentDate;

    private BigDecimal paymentAmount;

    private String paymentMethod;

    private String note;

    private BigInteger id;

}
