package com.altomni.apn.jobdiva.web.rest.timesheet;

import cn.hutool.core.convert.Convert;
import cn.hutool.extra.servlet.ServletUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.user.LoginVM;
import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetUserRepository;
import com.altomni.apn.jobdiva.service.dto.timesheet.ForgetPassDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.ForgetPassResetDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.ResetPasswordDTO;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetUserService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Api(tags = {"TimeSheetUser Management"})
@RestController
@RequestMapping("/api/v3/timesheet/user")
public class TimeSheetUserResource {

    @Resource
    private TimeSheetUserRepository timeSheetUserRepository;

    @Resource
    private TimeSheetUserService timeSheetUserService;



    @GetMapping("/getById")
    @Timed
    public ResponseEntity<TimeSheetUserDTO> getById(@RequestParam("id") Long id) {
        TimeSheetUser timeSheetUser = timeSheetUserRepository.getById(id);
        return ResponseEntity.ok(Convert.convert(TimeSheetUserDTO.class, timeSheetUser));
    }


    @GetMapping("/findByUsernameOrEmail")
    @Timed
    public ResponseEntity<TimeSheetUserDTO> findByUsernameOrEmail(@RequestParam("username") String username) {
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUsernameOrEmail(username);
        return ResponseEntity.ok(Convert.convert(TimeSheetUserDTO.class, timeSheetUser));
    }

    @PostMapping("/findByUsernameOrEmailList")
    public ResponseEntity<List<TimeSheetUserDTO>> findByUsernameOrEmailList(@RequestBody Set<String> emailList) {
        List<TimeSheetUser> timeSheetUsers = timeSheetUserRepository.findAllByUsernameInOrEmailIn(emailList, SecurityUtils.getTenantId(), TimeSheetUserType.CLIENT);
        return ResponseEntity.ok(timeSheetUsers.stream().map(t -> Convert.convert(TimeSheetUserDTO.class, t)).collect(Collectors.toList()));
    }

    @PostMapping("/save")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TimeSheetUserDTO> save(@RequestBody TimeSheetUserDTO timeSheetUserDTO) {
        timeSheetUserDTO = timeSheetUserService.save(timeSheetUserDTO);
        return ResponseEntity.ok(timeSheetUserDTO);
    }

    @GetMapping("/findOneWithRolesByUsername")
    @Timed
    public ResponseEntity<TimeSheetUserDTO> findOneWithRolesByUsername(@RequestParam("username") String username) {
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findOneWithRolesByUsername(username);
        return ResponseEntity.ok(Convert.convert(TimeSheetUserDTO.class, timeSheetUser));
    }

    @GetMapping("/findUserWithAuthorities")
    @Timed
    public ResponseEntity<TimeSheetUserDTO> findUserWithAuthorities(@RequestParam("login") String login) {
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findUserWithAuthorities(login);
        return ResponseEntity.ok(Convert.convert(TimeSheetUserDTO.class, timeSheetUser));
    }


    /**
     * user login
     *
     * @param loginVM
     * @return
     */
    @PostMapping("/login")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TimeSheetUserDTO> login(@RequestBody LoginVM loginVM, HttpServletRequest request) {
        log.info("[timesheet: User @{}] REST request to get login user", SecurityUtils.getUserId());
        loginVM.setIp(ServletUtil.getClientIP(request));
        TimeSheetUser user = timeSheetUserService.login(loginVM);
        return ResponseEntity.ok(Convert.convert(TimeSheetUserDTO.class, user));
    }

    @GetMapping("/completionStatus")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<CompletionStatus> completionStatus() {
        log.info("[timesheet: User @{}] REST request to get completionStatus", SecurityUtils.getUserId());
        CompletionStatus user = timeSheetUserService.getCompletionStatus();
        return ResponseEntity.ok(user);
    }

    @ApiOperation(value = "User Logout", response = Void.class, tags = {"Logout"})
    @PutMapping(path = "/logout")
    public ResponseEntity<Void> logout() {
        return ResponseEntity.ok().build();
    }

    /**
     * validate email for forget password;
     *
     * @param forgetPassDTO
     * @return
     */
    @PostMapping("/forgetPass")
    @Timed
    @NoRepeatSubmit
    public void forgetPass(@RequestBody ForgetPassDTO forgetPassDTO) {
        log.info("[timesheet: User @{}] forget password", SecurityUtils.getUserId());
        timeSheetUserService.forgetPass(forgetPassDTO);
    }

    /**
     * renew password for user forget password;
     *
     * @param resetPasswordDTO
     * @return
     */
    @PostMapping("/renewPassword")
    @Timed
    @NoRepeatSubmit
    public void renewPassForForget(@RequestBody ForgetPassResetDTO resetPasswordDTO) {
        timeSheetUserService.resetPassForForget(resetPasswordDTO);
    }

    @PostMapping("/resetPassword")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TimeSheetUserDTO> updatePassword(@RequestBody ResetPasswordDTO resetPasswordDTO) {
        log.info("[timesheet: User @{}] REST reset password:", SecurityUtils.getUserId());
        TimeSheetUser user = timeSheetUserService.resetPassword(resetPasswordDTO);
        return ResponseEntity.ok(Convert.convert(TimeSheetUserDTO.class, user));
    }


}
