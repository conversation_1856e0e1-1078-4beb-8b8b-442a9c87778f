package com.altomni.apn.jobdiva.service.vo.invoice;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigInteger;

@ApiModel(description = "PaymentRecordListVO")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PaymentRecordListVO extends PaymentRecordVO implements Serializable {

    private BigInteger id;

    private BigInteger groupInvoiceId;
}
