package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.vo.timesheet.EndingDateListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface TimeSheetRecordAmService extends BaseService {

    List<TimeSheetRecord> saveRecord(BreakTimeAmDTO record);

    List<TimeSheetRecord> saveRecordForDateSelect(BreakTimeAmDTO dto);

    BreakTimeDTO findRecords(RecordSearchDTO dto);

    BreakTimeDTO findRecordById(RecordSearchDTO dto);

    BreakTimeDTO findBreakTimeRecord(RecordSearchDTO timeSheetRecord);

    Integer saveBreakTime(BreakTimeAmDTO dto);

    SummaryDataVO summary(SummaryQueryAmDTO dto);

    Integer noHour(NoHourDTO dates);

    SummaryDataVO search(AdvanceSearchDTO dto);

    Integer timeSheetApprove(ApproveDTO dto);

    List<EndingDateListVO> weekEndingList(Long talentId);

    void timeSheetSummaryDownload(SummaryQueryDTO dto, HttpServletResponse response);

    Integer saveHolidayRecords(HolidayRecordSaveDto saveDto);

    Map<String, Object> importTimesheetByExcel(List<ImportTimesheetForExcelDTO> dtoList);

}
