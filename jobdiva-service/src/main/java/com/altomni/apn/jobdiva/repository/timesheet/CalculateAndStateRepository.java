package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.jobdiva.domain.timesheet.CalculateTypeAndState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface CalculateAndStateRepository extends JpaRepository<CalculateTypeAndState, Long> {


    CalculateTypeAndState findByStateAndCountryCode(String state, String countryCode);

    @Query(value = " select * from timesheet_calculate_state where is_default = 1 and country_code = ?1 ",nativeQuery = true)
    CalculateTypeAndState  findDefaultCalculateType(String countryCode);

}
