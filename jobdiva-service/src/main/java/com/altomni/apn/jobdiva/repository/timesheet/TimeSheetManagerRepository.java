package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetManager;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data JPA repository for the TimeSheetManager.
 */
@Repository
public interface TimeSheetManagerRepository extends JpaRepository<TimeSheetManager, Long> {

    @Query(value = "select * from timesheet_manager where talent_id =?1 and role = 0 and assignment_id = ?2 limit 1",nativeQuery = true)
    TimeSheetManager findPrimaryManagerByTalentId(Long talent, Long assignmentId);

    void deleteByAssignmentId(Long assignment);

    @Query(value = " select * from timesheet_manager tm where tm.assignment_id = ?1 order by tm.role ASC ",nativeQuery = true)
    List<TimeSheetManager> findByAssignmentId(Long assignmentId);

    @Query(value = " select * from timesheet_manager tm where tm.assignment_id = ?1 and tm.role in (0,1) order by tm.role ASC",nativeQuery = true)
    List<TimeSheetManager>  findClientApprover(Long assignmentId);

    @Query(value = " select * from timesheet_manager tm where tm.assignment_id = ?1 and tm.role = 3 order by tm.id ASC",nativeQuery = true)
    List<TimeSheetManager>  findAmApprover(Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_manager where assignment_id = ?1 and role = ?2 ",nativeQuery = true)
    void deleteByAssignmentIdAndUserRole(Long assignmentId, Integer toDbValue);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_manager where id in ?1 ",nativeQuery = true)
    void deleteByIdIn(List<Long> idList);

    @Query(value = " select u.first_name, u.email,c.`full_business_name`,t.full_name from timesheet_manager tm " +
            "left join user u on u.id = tm.client_id " +
            "left join timesheet_talent_assignment tta on tta.id = tm.assignment_id " +
            "left join company c on c.id = tta.company_id " +
            "left join talent t on t.id = tta.talent_id " +
            "where tm.assignment_id = ?1 and tm.role = 3  ",nativeQuery = true)
    List<Object[]> findAmEmailByAssignmentId(Long assignmentId);

    List<TimeSheetManager> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
