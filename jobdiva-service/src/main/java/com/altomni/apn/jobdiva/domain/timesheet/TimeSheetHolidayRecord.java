package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.AutoAbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Entity
@Data
@Table(name = "time_sheet_holiday_record")
public class TimeSheetHolidayRecord extends AutoAbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "assignment_id")
    private Long assignmentId;

    @Column(name = "work_hours")
    private Float workHours;

    @Column(name = "week_end")
    private LocalDate weekEnd;

    @Column(name = "rate")
    private BigDecimal rate;

    @Column(name = "currency")
    private Integer currency;

}
