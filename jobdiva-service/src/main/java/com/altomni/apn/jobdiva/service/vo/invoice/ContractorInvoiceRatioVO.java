package com.altomni.apn.jobdiva.service.vo.invoice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractorInvoiceRatioVO implements Serializable {

    private BigInteger id;

    private BigInteger talentId;

    private String talentName;

    private BigInteger groupInvoiceId;

    private BigDecimal totalAmount;

}
