package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MyOnBoardingPortalsHistoriesDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long talentRecruitmentProcessId;

    private LocalDate startDate;

    private String company;

    private String jobTitle;

    private List<OnBoardingProcessesCompletionsDTO> documents;

}
