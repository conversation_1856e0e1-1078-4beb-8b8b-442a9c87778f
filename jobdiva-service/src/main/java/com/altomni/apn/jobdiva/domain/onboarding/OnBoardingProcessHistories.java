package com.altomni.apn.jobdiva.domain.onboarding;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.common.domain.enumeration.user.CompletionStatusConverter;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.*;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@ApiModel(description = "onBoarding process histories. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "onboarding_process_histories")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingProcessHistories extends AbstractAuditingEntity implements Serializable {

    public static Set<String> TransferSkipProperties = new HashSet<>(Arrays.asList("id", "s3Link", "s3Key"));

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "process_id")
    private String processId;

    @Column(name = "package_id")
    private Long packageId;

    @Column(name = "package_name")
    private String packageName;

    @Column(name = "document_id")
    private Long documentId;

    @Column(name = "document_name")
    private String documentName;

    @Column(name = "document_name_uploaded")
    private String documentNameUploaded;

    @Column(name = "s3_key_source")
    private String s3KeySource;

    @Column(name = "s3_key")
    private String s3Key;

    @Column(name = "action_required")
    @Convert(converter = ActionRequiredTypeConverter.class)
    private ActionRequiredType actionRequired;

    @Column(name = "special_document")
    @Convert(converter = SpecialDocumentTypeConverter.class)
    private SpecialDocumentType specialDocument;

    @Column(name = "document_type")
    @Convert(converter = DocumentTypeConverter.class)
    private DocumentType documentType;

    @Column(name = "completion_status", nullable = false)
    @Convert(converter = CompletionStatusConverter.class)
    private CompletionStatus completionStatus = CompletionStatus.INCOMPLETE;

    @Column(name = "package_status", nullable = false)
    @Convert(converter = CompletionStatusConverter.class)
    private CompletionStatus packageStatus = CompletionStatus.INCOMPLETE;

    @Column
    private Integer ordering;

    @ApiModelProperty(value = "Whether document is activated. Default is true.")
    @NotNull
    @Column(nullable = false)
    private boolean activated = true;

    @Column(name = "onboarding_type")
    private Integer onboardingType;

    public static OnBoardingPackageDocumentsDTO toRelationsDTO(OnBoardingProcessHistories in) {
        OnBoardingPackageDocumentsDTO out = new OnBoardingPackageDocumentsDTO();
        ServiceUtils.myCopyProperties(in, out, TransferSkipProperties);
        out.setName(in.getDocumentName());
        out.setS3Key(in.getS3Key());
        return out;
    }

}
