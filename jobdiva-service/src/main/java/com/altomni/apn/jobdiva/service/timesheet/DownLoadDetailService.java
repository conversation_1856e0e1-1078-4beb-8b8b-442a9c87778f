package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeDTO;
import com.altomni.apn.jobdiva.service.vo.timesheet.ExpenseListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimeSheetSummaryVO;

import javax.servlet.http.HttpServletResponse;

public interface DownLoadDetailService {

    void downloadTimeSheetDetail(BreakTimeDTO record, TimeSheetSummaryVO vo, HttpServletResponse response);

    void downloadExpenseDetail(Integer is, BreakTimeDTO breakTimeDTO, ExpenseListVO vo, HttpServletResponse response);

}
