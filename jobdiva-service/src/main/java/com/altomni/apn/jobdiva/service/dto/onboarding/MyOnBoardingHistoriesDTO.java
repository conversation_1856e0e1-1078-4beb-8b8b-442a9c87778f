package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.OperationStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MyOnBoardingHistoriesDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long historyId;

    private Long detailsId;

    private Long talentRecruitmentProcessId;

    private String JobTitle;

    private String company;

    private LocalDate startDate;

    private CompletionStatus completionStatus;

    private String documentName;

    private String documentNameUploaded;

    private String documentSourceName;

    private Instant assignedOnDate;

    private String lastOperationBy;

    private Instant lastOperationDate;

    private OperationStatus operationStatus;

    private String s3Key;

}
