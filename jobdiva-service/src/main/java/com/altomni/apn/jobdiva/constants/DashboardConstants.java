package com.altomni.apn.jobdiva.constants;

import java.util.ArrayList;
import java.util.List;

/**
 * dashboard constants
 * <AUTHOR>
 */
public interface DashboardConstants {

    String EMPLOYEE_NAME = "employeeName";

    String DOCUMENT_TYPE = "documentType";

    String DOCUMENT_ID = "documentId";

    String DOCUMENT_NAME = "documentName";

    String DOCUMENT_STATUS = "documentStatus";

    String PACKAGE_ASSIGNED_ON = "packageAssignedOn";

    String PACKAGE_ID = "packageId";

    String PACKAGE_NAME = "packageName";

    String PACKAGE_STATUS = "packageStatus";

    String STARTING_ON = "startingOn";

    String JOB_ID = "jobId";

    String COMPANY_ID = "companyId";

    String COMPANY = "company";

    String JOB_TITLE = "jobTitle";

    String JOB_CODE = "jobCode";

    String DEPARTMENT = "department";

    String START_BY_USER_ID = "startByUserId";

    String ASSIGNED_BY_ID = "assignedById";

    String START_BY_USER = "startByUser";

    String ASSIGNED_BY = "assignedBy";

    /** Multilingual mixed sort field */
    List<String> MULTILINGUAL_MIXED_ORDER_COLUMN_LIST = new ArrayList<String>() {
        {
            add(EMPLOYEE_NAME);
            add(DOCUMENT_NAME);
            add(PACKAGE_NAME);
            add(COMPANY);
            add(JOB_TITLE);
            add(DEPARTMENT);
            add(START_BY_USER);
            add(ASSIGNED_BY);
        }
    };

}
