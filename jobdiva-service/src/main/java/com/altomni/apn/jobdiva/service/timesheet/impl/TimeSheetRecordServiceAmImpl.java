package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import com.altomni.apn.common.domain.enumeration.jobdiva.ManagerRoleType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.jobdiva.config.env.ApplicationProperties;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.TimesheetExcelUploadFailReason;
import com.altomni.apn.jobdiva.domain.timesheet.*;
import com.altomni.apn.jobdiva.repository.assignment.BillInfoRepository;
import com.altomni.apn.jobdiva.repository.assignment.PayRateRepository;
import com.altomni.apn.jobdiva.repository.timesheet.*;
import com.altomni.apn.jobdiva.service.company.CompanyService;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.job.JobService;
import com.altomni.apn.jobdiva.service.mail.MailService;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.timesheet.AdvanceSearchService;
import com.altomni.apn.jobdiva.service.timesheet.ClientService;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetCommentsService;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetRecordAmService;
import com.altomni.apn.jobdiva.service.user.UserService;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentExtendInfoVO;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO;
import com.altomni.apn.jobdiva.service.vo.assignment.WeekDataVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.*;
import com.altomni.apn.common.utils.RateUnitUtil;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service("timeSheetRecordAmService")
public class TimeSheetRecordServiceAmImpl extends TimeSheetRecordServiceImpl implements TimeSheetRecordAmService {

    @Resource
    private JobService jobService;

    @Resource
    private CompanyService companyService;

    @Resource
    private ClientService clientService;

    @Resource
    private TimeSheetRepository timeSheetRepository;

    @Resource
    private BillInfoRepository billInfoRepository;

    @Resource
    private TimeSheetRecordRepository recordRepository;

    @Resource
    private ApproveRecordRepository approveRecordRepository;

    @Resource
    private MailService mailService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private TimeSheetManagerRepository managerRepository;

    @Resource
    private TimeSheetCommentsService commentsService;

    @Resource
    private TalentAssignmentRepository assigmentRepository;

    @Resource
    private AdvanceSearchService searchService;

    @Resource
    private PayRateRepository payRateRepository;

    @Resource
    private TimeSheetHolidayRecordRepository timeSheetHolidayRecordRepository;

    @Resource
    private UserService userService;

    @Resource
    private StoreService storeService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TimeSheetRecord> saveRecord(BreakTimeAmDTO bDto) {
        TalentAssigment assigment = findAssignment(bDto.getAssignmentId());
        Long talentId = assigment.getTalentId();
        Long tenantId = assigment.getTenantId();
        List<TimeSheetRecord> recordList = super.saveRecord(bDto, tenantId, talentId, true);
        TimeSheetUtil.updateOtDt(bDto.getStatus(), bDto.getAssignmentId(), bDto.getType(), LocalDate.parse(bDto.getWeekEndingDate()));
        TimeSheetRecord timeSheetRecord = recordList.get(0);
        assignmentSyncToHrService.buildTimeSheetRecordListSyncToHrMq(timeSheetRecord.getAssignmentId(), timeSheetRecord.getWeekEnd(), timeSheetRecord.getWeekEndingDate());
        return recordList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TimeSheetRecord> saveRecordForDateSelect(BreakTimeAmDTO dto) {
        TalentAssigment assigment = findAssignment(dto.getAssignmentId());
        Long talentId = assigment.getTalentId();
        Long tenantId = assigment.getTenantId();
        return super.saveRecordForDateSelect(dto, talentId, tenantId, true);
    }

    @Override
    public BreakTimeDTO findRecords(RecordSearchDTO dto) {
        RecordHeadInfoVO headInfoVO = recordRepository.findTimeSheetJobInfo(dto.getAssignmentId(), LocalDate.parse(dto.getEndDate()));
        BreakTimeDTO result = super.findRecords(dto, findAssignment(dto.getAssignmentId()).getTalentId(), dto.getAssignmentId());
        if (ObjectUtil.isNotNull(headInfoVO)) {
            TimeSheetComments comment = commentsService.findByWorkDateAndType(dto.getEndDate(), CommentsType.TIME_SHEET, headInfoVO.getTalentId(), dto.getAssignmentId());
            if (ObjectUtil.isNotNull(comment)) {
                result.setComments(comment.getComments());
            }
        }
        result.setHeadInfo(headInfoVO);
        return result;
    }

    @Override
    public BreakTimeDTO findBreakTimeRecord(RecordSearchDTO dto) {
        TalentAssigment assigment = findAssignment(dto.getAssignmentId());
        RecordHeadInfoVO headInfoVO = recordRepository.findTimeSheetJobInfo(dto.getAssignmentId(), LocalDate.parse(dto.getEndDate()));
        BreakTimeDTO result = super.findBreakTimeRecord(dto, assigment.getTalentId());
        result.setHeadInfo(headInfoVO);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveBreakTime(BreakTimeAmDTO dto) {
        TalentAssigment assigment = findAssignment(dto.getAssignmentId());
        Long talentId = assigment.getTalentId();
        Long tenantId = assigment.getTenantId();
        return super.saveBreakTime(dto, talentId, tenantId, true);
    }

    @Override
    public SummaryDataVO summary(SummaryQueryAmDTO dto) {
        TalentAssigment assigment = findAssignment(dto.getAssignmentId());
        Long talentId = assigment.getTalentId();
        return super.summary(dto, talentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer noHour(NoHourDTO dates) {
        TalentAssigment assigment = findAssignment(dates.getAssignmentId());
        Long talentId = assigment.getTalentId();
        dates.setRoleType(ManagerRoleType.AM);
        return super.noHour(dates, talentId, true);
    }

    @Override
    public SummaryDataVO search(AdvanceSearchDTO dto) {
        return searchService.searchForTimeSheet(dto, null, getTalentIdsAndAssignments());
    }

    @Override
    public Integer timeSheetApprove(ApproveDTO dto) {
        return clientService.timeSheetApprove(dto, ManagerRoleType.AM);
    }

    @Override
    public Integer saveHolidayRecords(HolidayRecordSaveDto saveDto) {
        List<TimeSheetRecord> timeSheetRecordList = recordRepository.findAllByIdIs(new HashSet<>(saveDto.getRecordIds()));
        TimeSheetUtil.checkGeneratedInvoice(timeSheetRecordList);
        Set<Long> assignmentIdSet = timeSheetRecordList.stream().map(TimeSheetRecord::getAssignmentId).collect(Collectors.toSet());
        List<AssignmentPayRateInfo> payRateInfoList = payRateRepository.findBillUnitByAssignmentIds(assignmentIdSet);
        Map<Long, AssignmentPayRateInfo> payRateInfoMap = payRateInfoList.stream().collect(Collectors.toMap(AssignmentPayRateInfo::getAssignmentId, a -> a));
        List<TimeSheetHolidayRecord> holidayRecordList = new ArrayList<>();
        timeSheetRecordList.forEach(timeSheetRecord -> {
            TimeSheetHolidayRecord timeSheetHolidayRecord = new TimeSheetHolidayRecord();
            timeSheetHolidayRecord.setAssignmentId(timeSheetRecord.getAssignmentId());
            timeSheetHolidayRecord.setWeekEnd(timeSheetRecord.getWeekEnd());
            AssignmentPayRateInfo assignmentPayRateInfo = payRateInfoMap.get(timeSheetRecord.getAssignmentId());
            timeSheetHolidayRecord.setCurrency(assignmentPayRateInfo.getCurrency());
            timeSheetHolidayRecord.setWorkHours(saveDto.getWorkHours());
            timeSheetHolidayRecord.setRate(RateUnitUtil.rateConvert(assignmentPayRateInfo.getPayRate(), assignmentPayRateInfo.getTimeUnit()));
            holidayRecordList.add(timeSheetHolidayRecord);
        });
        timeSheetHolidayRecordRepository.saveAll(holidayRecordList);
        assignmentSyncToHrService.buildHolidayListSyncToHrMq(holidayRecordList);
        return holidayRecordList.size();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> importTimesheetByExcel(List<ImportTimesheetForExcelDTO> dtoList) {
        log.info("[apn @{}] timesheet import is start", SecurityUtils.getUserId());
        Long tenantId = SecurityUtils.getTenantId();
        Map<String, Object> resultMap = new HashMap<>(16);
        resultMap.put("successNum", 0);
        resultMap.put("failNum", 0);
        if (CollUtil.isEmpty(dtoList)) {
            return resultMap;
        }
        IntStream.range(0, dtoList.size() - 1).forEach(i -> dtoList.get(i).setIndex(i));
        Map<Integer, ImportTimesheetForExcelDTO> map = dtoList.stream().collect(Collectors.toMap(ImportTimesheetForExcelDTO::getIndex, a -> a));
        Map<Integer, TimesheetExcelUploadFailReason> failMap = new HashMap<>(16);
        Map<String, List<ImportTimesheetForExcelDTO>> companyMap = dtoList.stream().collect(Collectors.groupingBy(ImportTimesheetForExcelDTO::getCompanyName));
        Map<Long, TimeSheetManager> timeSheetManagerMap = new HashMap<>(16);
        List<ApproveRecord> approveRecordList = new ArrayList<>();
        Set<String> approvedRecordIdAndAssignmentId = new HashSet<>();
        List<List<TimeSheetRecord>> recordListList = new ArrayList<>();
        List<TimeSheetRecord> weekendingDateList = new ArrayList<>();
        for (String companyName : companyMap.keySet()) {
            Map<String, List<ImportTimesheetForExcelDTO>> talentMap = companyMap.get(companyName).stream().collect(Collectors.groupingBy(ImportTimesheetForExcelDTO::getTalentName));
            for (String talentName : talentMap.keySet()) {
                log.info("[apn @{}] timesheet import , companyName = {}, talentName = {}", SecurityUtils.getUserId(), companyName, talentName);
                //1.匹配talentName和companyName 获取assignment信息
                List<AssignmentExtendInfoVO> assignmentTimeSheetList = timeSheetRepository.findAssignmentByTalentNameAndCompanyName(talentName, companyName, tenantId);
                if (CollUtil.isEmpty(assignmentTimeSheetList)) {
                    //没有匹配数据
                    talentMap.get(talentName).forEach(indexDto -> failMap.put(indexDto.getIndex(), TimesheetExcelUploadFailReason.NONE));
                    log.info("[apn @{}] timesheet import is fail, companyName = {}, talentName = {}, reason = {}", SecurityUtils.getUserId(), companyName, talentName, TimesheetExcelUploadFailReason.NONE);
                    continue;
                }
                Set<Long> companySet = assignmentTimeSheetList.stream().map(AssignmentExtendInfoVO::getCompanyId).collect(Collectors.toSet());
                if (companySet.size() > 1) {
                    //talentName,companyName 相同的情况下 companyId匹配到多个
                    talentMap.get(talentName).forEach(indexDto -> failMap.put(indexDto.getIndex(), TimesheetExcelUploadFailReason.COMPANY_DUPLICATION));
                    log.info("[apn @{}] timesheet import is fail, companyName = {}, talentName = {}, reason = {}, companySet = {}", SecurityUtils.getUserId(), companyName, talentName, TimesheetExcelUploadFailReason.COMPANY_DUPLICATION, companySet);
                    continue;
                }
                Set<Long> talentSet = assignmentTimeSheetList.stream().map(AssignmentExtendInfoVO::getTalentId).collect(Collectors.toSet());
                if (talentSet.size() > 1) {
                    //talentName,companyName 相同的情况下 talentId匹配到多个
                    talentMap.get(talentName).forEach(indexDto -> failMap.put(indexDto.getIndex(), TimesheetExcelUploadFailReason.TALENT_DUPLICATION));
                    log.info("[apn @{}] timesheet import is fail, companyName = {}, talentName = {}, reason = {}, talentSet = {}", SecurityUtils.getUserId(), companyName, talentName, TimesheetExcelUploadFailReason.TALENT_DUPLICATION, talentSet);
                    continue;
                }
                //根据assignmentId来匹配性能更好
                List<Long> assignmentIdList = assignmentTimeSheetList.stream().map(AssignmentExtendInfoVO::getAssignmentId).toList();
                List<ImportTimesheetForExcelDTO> dateDtoList = talentMap.get(talentName).stream().sorted(Comparator.comparingInt(ImportTimesheetForExcelDTO::getIndex)).toList();
                for (ImportTimesheetForExcelDTO dateDto : dateDtoList) {
                    log.info("[apn @{}] timesheet import, companyName = {}, talentName = {}, weekending = {} is start", SecurityUtils.getUserId(), companyName, talentName, dateDto.getWeekEnding());
                    LocalDate endDate = LocalDate.parse(dateDto.getWeekEnding());
                    LocalDate startDate = endDate.minusDays(6);
                    List<TimeSheetRecord> timeSheetRecordList = recordRepository.findTimesheetRecordListByStartDateAndEndDateAndAssignmentIdList(startDate, endDate, assignmentIdList, tenantId);
                    if (CollUtil.isEmpty(timeSheetRecordList)) {
                        failMap.put(dateDto.getIndex(), TimesheetExcelUploadFailReason.WEEK_INCOMPLETE);
                        log.info("[apn @{}] timesheet import is fail, companyName = {}, talentName = {}, weekending = {} reason = {}", SecurityUtils.getUserId(), companyName, talentName, dateDto.getWeekEnding(), TimesheetExcelUploadFailReason.WEEK_INCOMPLETE);
                        continue;
                    }
                    if (timeSheetRecordList.size() > 1) {
                        failMap.put(dateDto.getIndex(), TimesheetExcelUploadFailReason.WEEKENDING_DUPLICATION);
                        log.info("[apn @{}] timesheet import is fail, companyName = {}, talentName = {}, weekending = {} reason = {}", SecurityUtils.getUserId(), companyName, talentName, dateDto.getWeekEnding(), TimesheetExcelUploadFailReason.WEEKENDING_DUPLICATION);
                        continue;
                    }
                    TimeSheetRecord weekendingRecord = timeSheetRecordList.get(0);
                    if (weekendingRecord.getTimeSheetType() != TimeSheetType.WEEK_HOUR) {
                        failMap.put(dateDto.getIndex(), TimesheetExcelUploadFailReason.TIMESHEET_MODE_DOES_NOT_MATCH);
                        log.info("[apn @{}] timesheet import is fail, companyName = {}, talentName = {}, weekending = {} reason = {}", SecurityUtils.getUserId(), companyName, talentName, dateDto.getWeekEnding(), TimesheetExcelUploadFailReason.TIMESHEET_MODE_DOES_NOT_MATCH);
                        continue;
                    }
                    log.info("[apn @{}] timesheet import, companyName = {}, talentName = {}, weekending = {}, assignmentId = {} is doHandler", SecurityUtils.getUserId(), companyName, talentName, dateDto.getWeekEnding(), weekendingRecord.getAssignmentId());
                    BigDecimal regular = dateDto.getRegularHours().divide(BigDecimal.valueOf(5L), 2, RoundingMode.HALF_DOWN);
                    BigDecimal regularRemainder = dateDto.getRegularHours().subtract((regular.multiply(BigDecimal.valueOf(5L))));
                    BigDecimal over = dateDto.getOverTimes().divide(BigDecimal.valueOf(5L), 2, RoundingMode.HALF_DOWN);
                    BigDecimal overRemainder = dateDto.getOverTimes().subtract((over.multiply(BigDecimal.valueOf(5L))));
                    BigDecimal doubleHour = dateDto.getDoubleTimes().divide(BigDecimal.valueOf(5L), 2, RoundingMode.HALF_DOWN);
                    BigDecimal doubleHourRemainder = dateDto.getDoubleTimes().subtract((doubleHour.multiply(BigDecimal.valueOf(5L))));
                    weekendingRecord.setStatus(TimeSheetStatus.APPROVED);
                    List<TimeSheetRecord> list = new ArrayList<>();
                    for (int i = 6; i >= 2 ; i--) {
                        TimeSheetRecord timeSheetRecord = new TimeSheetRecord();
                        BeanUtil.copyProperties(weekendingRecord, timeSheetRecord, "id","workDate", "workHours", "regularHours", "overTime", "doubleTime", "weekDay", "totalHours");
                        timeSheetRecord.setWorkDate(endDate.minusDays(i));
                        timeSheetRecord.setWeekDay(endDate.minusDays(i).getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                        timeSheetRecord.setSubmittedDate(Instant.now());
                        if (i == 2) {
                            timeSheetRecord.setRegularHours(regular.add(regularRemainder).floatValue());
                            timeSheetRecord.setOverTime(over.add(overRemainder).floatValue());
                            timeSheetRecord.setDoubleTime(doubleHour.add(doubleHourRemainder).floatValue());
                            timeSheetRecord.setTotalHours(regular.add(regularRemainder).add(over).add(overRemainder).add(doubleHour).add(doubleHourRemainder).floatValue());
                        } else {
                            timeSheetRecord.setRegularHours(regular.floatValue());
                            timeSheetRecord.setOverTime(over.floatValue());
                            timeSheetRecord.setDoubleTime(doubleHour.floatValue());
                            timeSheetRecord.setTotalHours(regular.add(over).add(doubleHour).floatValue());
                        }
                        timeSheetRecord.setWorkHours(timeSheetRecord.getTotalHours());
                        list.add(timeSheetRecord);
                    }
                    TimeSheetRecord endingRecord = new TimeSheetRecord();
                    BeanUtil.copyProperties(weekendingRecord, endingRecord, "id", "workHours", "regularHours", "overTime", "doubleTime", "totalHours");
                    endingRecord.setSubmittedDate(Instant.now());
                    endingRecord.setWeekDay(endingRecord.getWorkDate().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                    list.add(endingRecord);
                    Set<LocalDate> localDateSet = TimeSheetUtil.getWeekByWeekEndingDate(endingRecord.getWeekEndingDate().toString());
                    recordRepository.deleteAllByWorkDateInAndTalentIdAndAssignmentId(localDateSet, endingRecord.getTalentId(), endingRecord.getAssignmentId());
                    recordRepository.saveAllAndFlush(list);
                    TimeSheetUtil.setWeekId(list);
                    TimeSheetManager timeSheetManager;
                    weekendingDateList.add(endingRecord);
                    if (timeSheetManagerMap.containsKey(endingRecord.getAssignmentId())) {
                        timeSheetManager = timeSheetManagerMap.get(endingRecord.getAssignmentId());
                    } else {
                        List<TimeSheetManager> timeSheetManagerList = managerRepository.findAmApprover(endingRecord.getAssignmentId());
                        timeSheetManager = timeSheetManagerList.get(0);
                        timeSheetManagerMap.put(endingRecord.getAssignmentId(), timeSheetManager);
                    }
                    if (!approvedRecordIdAndAssignmentId.contains(endingRecord.getId() + "-" + endingRecord.getAssignmentId())) {
                        ApproveRecord ap = new ApproveRecord();
                        ap.setOperator(timeSheetManager.getClientId());
                        ap.setRecordId(endingRecord.getId());
                        ap.setStatus(TimeSheetStatus.APPROVED);
                        ap.setTenantId(SecurityUtils.getTenantId());
                        ap.setType(CommentsType.TIME_SHEET);
                        ap.setRole(ManagerRoleType.AM);
                        ap.setAssignmentId(endingRecord.getAssignmentId());
                        ap.setWeekEnd(endingRecord.getWeekEndingDate());
                        approveRecordList.add(ap);
                        approvedRecordIdAndAssignmentId.add(endingRecord.getId() + "-" + endingRecord.getAssignmentId());
                        log.info("[apn @{}] timesheet import add approved record, record = {} ", SecurityUtils.getUserId(), JSONUtil.toJsonStr(ap));
                    } else {
                        log.info("[apn @{}] timesheet import approve record is repeat, param = {}, weekendingRecord = {} ", SecurityUtils.getUserId(), dateDto, endingRecord);
                    }
                    recordListList.add(list);
                }
            }
            if (CollUtil.isNotEmpty(approveRecordList)) {
                Map<String, ApproveRecord> uniqueRecords = approveRecordList.stream()
                        .collect(Collectors.toMap(
                                record -> record.getRecordId() + "-" + record.getAssignmentId(),
                                Function.identity(),
                                (existing, replacement) -> {
                                    log.info("timesheet import approve record is repeat, approve record = {} ", existing);
                                    return existing;
                                }
                        ));
                List<ApproveRecord> uniqueRecordList = uniqueRecords.values().stream().toList();
                approveRecordRepository.saveAllAndFlush(uniqueRecordList);
                recordListList.forEach(this::updateTimesheetStatus);
            }
        }
        //同步hr
        assignmentSyncToHrService.buildTimeSheetRecordListByExcelSyncToHrMq(weekendingDateList, approveRecordList);

        resultMap.put("successNum", dtoList.size() - failMap.size());
        resultMap.put("failNum", failMap.size());
        sendEmailTimeSheetApprove(dtoList.size() - failMap.size(), failMap.size(), failMap, map);
        log.info("[apn @{}] timesheet import is finish, successNum = {}, failNum = {}", SecurityUtils.getUserId(), dtoList.size() - failMap.size(), failMap.size());
        return resultMap;
    }

    private void updateTimesheetStatus(List<TimeSheetRecord> list) {
        if (CollUtil.isNotEmpty(list)) {
            TimeSheetRecord record = list.get(0);
            Set<LocalDate> dates = list.stream().map(TimeSheetRecord::getWorkDate).collect(Collectors.toSet());
            recordRepository.updateStatusByDates(dates,record.getTalentId(),record.getStatus().toDbValue(),record.getAssignmentId());
        }
    }

    private void sendEmailTimeSheetApprove(Integer successNum, Integer failNum, Map<Integer, TimesheetExcelUploadFailReason> failMap, Map<Integer, ImportTimesheetForExcelDTO> map) {
        SecurityContext context = SecurityContextHolder.getContext();
        EmailUtil.executorService.execute(() -> {
            SecurityContextHolder.setContext(context);
            //查询当前登录账号的email
            UserBriefDTO userBriefDTO = userService.findById(SecurityUtils.getUserId()).getBody();
            if (userBriefDTO == null) {
                return;
            }
            String subject = "Subtitle：Timesheet Import has been finished";
            StringBuilder sb = new StringBuilder();
            sb.append("<body>");
            HtmlUtil.appendParagraphCell(sb, successNum + " timesheets have been imported into APN.");
            HtmlUtil.appendParagraphCell(sb, failNum + " timesheets failed to be imported. You may refer to the attachment below for more details.");
            appendReason(sb, failMap, map);
            sb.append("</body>");
            String s3Link = createExcelUploadS3(failMap, map);
            try {
                mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), CollUtil.newArrayList(userBriefDTO.getEmail()), null, null, subject, sb.toString(), StrUtil.isNotBlank(s3Link)?CollUtil.newArrayList(s3Link):null, true));
                log.info("[apn @{}] timesheet import result send email success, email = {}", SecurityUtils.getUserId(), userBriefDTO.getEmail());
            } catch (Exception e) {
                log.error("[apn @{}] timesheet import result send email fail, email = {}, msg = {}", SecurityUtils.getUserId(), userBriefDTO.getEmail(), ExceptionUtils.getStackTrace(e));
            }
        });
    }


    private void appendReason(StringBuilder sb, Map<Integer, TimesheetExcelUploadFailReason> failMap, Map<Integer, ImportTimesheetForExcelDTO> map) {
        if (!failMap.isEmpty()) {
            HtmlUtil.appendBrTags(sb, 3);
            sb.append("<table border=\"1\" style=\"border-collapse: collapse;\">");
            sb.append("<tr><th style=\"width:150px;text-align: center;background-color: rgb(214, 214, 214);\">Contractor Name</th>");
            sb.append("<th style=\"width:150px;text-align: center;background-color: rgb(214, 214, 214);\">Company Name</th>");
            sb.append("<th style=\"width:150px;text-align: center;background-color: rgb(214, 214, 214);\">RT</th>");
            sb.append("<th style=\"width:150px;text-align: center;background-color: rgb(214, 214, 214);\">OT</th>");
            sb.append("<th style=\"width:150px;text-align: center;background-color: rgb(214, 214, 214);\">DT</th>");
            sb.append("<th style=\"width:150px;text-align: center;background-color: rgb(214, 214, 214);\">Week Ending On</th>");
            sb.append("<th style=\"width:150px;text-align: center;background-color: rgb(214, 214, 214);\">Fail Reason</th></tr>");
            failMap.keySet().stream().sorted().forEach(k -> {
                ImportTimesheetForExcelDTO importTimesheetForExcelDTO = map.get(k);
                sb.append("<tr>");
                sb.append("<td style=\"width:150px;text-align: center;\">").append(importTimesheetForExcelDTO.getTalentName()).append("</td>");
                sb.append("<td style=\"width:150px;text-align: center;\">").append(importTimesheetForExcelDTO.getCompanyName()).append("</td>");
                sb.append("<td style=\"width:150px;text-align: center;\">").append(importTimesheetForExcelDTO.getRegularHours().setScale(2, RoundingMode.DOWN).toPlainString()).append("</td>");
                sb.append("<td style=\"width:150px;text-align: center;\">").append(importTimesheetForExcelDTO.getOverTimes().setScale(2, RoundingMode.DOWN).toPlainString()).append("</td>");
                sb.append("<td style=\"width:150px;text-align: center;\">").append(importTimesheetForExcelDTO.getDoubleTimes().setScale(2, RoundingMode.DOWN).toPlainString()).append("</td>");
                sb.append("<td style=\"width:150px;text-align: center;\">").append(importTimesheetForExcelDTO.getWeekEnding()).append("</td>");
                sb.append("<td style=\"width:150px;text-align: center;\">").append(failMap.get(k).getDescription()).append("</td>");
                sb.append("</tr>");
            });
            sb.append("</table>");
        }
    }

    private String createExcelUploadS3(Map<Integer, TimesheetExcelUploadFailReason> failMap, Map<Integer, ImportTimesheetForExcelDTO> map) {
        if (!failMap.isEmpty()) {
            log.info("[apn @{}] timesheet import create excel is start", SecurityUtils.getUserId());
            List<TimesheetExcelVO> voList = new ArrayList<>();
            failMap.keySet().stream().sorted().forEach(k -> {
                ImportTimesheetForExcelDTO importTimesheetForExcelDTO = map.get(k);
                TimesheetExcelVO vo = new TimesheetExcelVO();
                vo.setTalentName(importTimesheetForExcelDTO.getTalentName());
                vo.setCompanyName(importTimesheetForExcelDTO.getCompanyName());
                vo.setRegularHours(importTimesheetForExcelDTO.getRegularHours().setScale(2, RoundingMode.DOWN).toPlainString());
                vo.setOverTimes(importTimesheetForExcelDTO.getOverTimes().setScale(2, RoundingMode.DOWN).toPlainString());
                vo.setDoubleTimes(importTimesheetForExcelDTO.getDoubleTimes().setScale(2, RoundingMode.DOWN).toPlainString());
                vo.setWeekEnding(importTimesheetForExcelDTO.getWeekEnding());
                vo.setFailReason(failMap.get(k).getDescription());
                voList.add(vo);
            });
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()){
                // 创建 ExcelWriterBuilder
                ExcelWriterBuilder excelWriterBuilder = EasyExcelFactory.write();
                // 将 Excel 写入 ByteArrayOutputStream
                excelWriterBuilder.file(outputStream).sheet("Sheet1").head(TimesheetExcelVO.class).doWrite(voList);
                // 将 ByteArrayOutputStream 的内容转换为字节数组
                MultipartFile file = new MockMultipartFile("timesheet_fail_excel.xlsx", "timesheet_fail_excel.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", outputStream.toByteArray());
                String mk5Key = DigestUtils.md5Hex(file.getInputStream());
                String s3Link = storeService.uploadDocument(file, mk5Key, UploadTypeEnum.EMAIL_ATTACHMENT.getKey()).getBody();
                log.info("[apn @{}] timesheet import create excel is success, s3Link = {}", SecurityUtils.getUserId(), s3Link);
                return s3Link;
            } catch (Exception e) {
                log.error("[apn @{}] timesheet import upload excel is error, msg = {}", SecurityUtils.getUserId(), ExceptionUtils.getStackTrace(e));
            }
        }
        return null;
    }

    @Override
    public List<EndingDateListVO> weekEndingList(Long talent) {
        List<AssignmentVO> assignments = assigmentRepository.findAssigmentInfoByTalentId(talent);
        if (CollectionUtils.isEmpty(assignments)) {
            return new LinkedList<>();
        }
        List<EndingDateListVO> result = new LinkedList<>();
        LocalDate now = LocalDate.now();
        for (AssignmentVO assigment : assignments) {
            if (now.compareTo(assigment.getStartDate()) < 0) {continue;}
            EndingDateListVO vo = new EndingDateListVO();
            result.add(vo);
            JobDTOV3 jobV3 = jobService.findById(assigment.getJobId()).getBody();
            vo.setJobId(jobV3.getId());
            vo.setJobTitle(jobV3.getTitle());
            vo.setCompanyName(companyService.getCompany(assigment.getCompanyId()).getBody().getName());
            vo.setOverTimeType(assigment.getOvertimeType());
            vo.setAssignmentId(assigment.getId());
            vo.setTimeSheetType(assigment.getSheetType());
            vo.setCalculateMethodType(assigment.getMethod());
            vo.setStartDate(assigment.getStartDate());
            vo.setEndDate(assigment.getEndDate());
            vo.setIsExcept(assigment.getIsExcept());
            //查询所有的timesheet 取所有的weekend = weekEndingDate 的数据
            LocalDate startDate = assigment.getStartDate();
            LocalDate endDate = assigment.getEndDate();
            AssignmentVO assignmentVO = assigmentRepository.findAssigmentInfoByAssignmentId(assigment.getId());
            List<WeekDataVO> weekDataVOList = TimeSheetUtil.getWeekEnding(startDate, endDate, assigment.getWeekEnding(), assignmentVO.getIsWeekEnd(), assignmentVO.getFrequency());
            weekDataVOList = weekDataVOList.stream().filter(weekDataVO -> weekDataVO.getWorkDate().isEqual(weekDataVO.getWeekEnd()))
                    .sorted(Comparator.comparing(WeekDataVO::getWeekEnd).reversed()).toList();
            vo.setDates(weekDataVOList);
        }
        return result;
    }

    @Override
    public void timeSheetSummaryDownload(SummaryQueryDTO dto, HttpServletResponse response) {
        if (CollectionUtils.isEmpty(dto.getRecordIds())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        AdvanceSearchDTO searchDTO = new AdvanceSearchDTO();
        searchDTO.setRecordIds(dto.getRecordIds());
        searchDTO.setOrder(dto.getOrder());
        searchDTO.setOrderBy(dto.getOrderBy());
        searchDTO.setPageNum(0);
        searchDTO.setPageSize(100000);
        searchDTO.setTimezone(dto.getTimezone());
        SummaryDataVO result = searchService.searchForTimeSheet(searchDTO, null, null);
        ExcelWriter excelWriter = null;
        List<TimeSheetSummaryAmVO> data = (List<TimeSheetSummaryAmVO>) result.getData();
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            addFileDownLoadHeader(response);
            response.setHeader("Content-Disposition", "attachment;filename=" + "TimeSheet" + DateUtil.fromInstantToDate(Instant.now(), dto.getTimezone(), DateUtil.YYYY_MM_DD) + ".xls");
            excelWriter = EasyExcelFactory.write(outputStream).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, "timeSheet-summary").head(TimeSheetSummaryAmVO.class).needHead(true).build();
            excelWriter.write(data, writeSheet);
        } catch (Exception e) {
            log.error("error", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("error", e);
                }
            }
        }
    }

    private void addFileDownLoadHeader(HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Headers", "*");
//        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "*");
        response.setHeader("Access-Control-Expose-Headers", "*");
    }


    private List<Long> getTalentIdsAndAssignments() {
        List<Long> assignmentIds = new ArrayList<>();
        if (SecurityUtils.isAdmin() || SecurityUtils.isTimesheetAdmin()) {
            return assignmentIds;
        }
        Long userId = SecurityUtils.getUserId();
        Set<Long> assignmentIdSet = new HashSet<>();
        assignmentIdSet.addAll(assigmentRepository.findAssignmentIdsByUserIdForCompanyAM(userId));
        assignmentIdSet.addAll(assigmentRepository.findAssignmentIdsForApplicationRecruiterOrSourcer(userId));
        if (CollectionUtils.isEmpty(assignmentIdSet)) {
            assignmentIdSet.add(-1L);
        }
        assignmentIds.addAll(assignmentIdSet);
        return assignmentIds;
    }

    private LocalDate getNextWeekEndingDate(AssignmentVO assigment) {
        LocalDate now = LocalDate.now();
        Integer setWeekDate = assigment.getWeekEnding();
        now = now.plusDays(7);
        int week = now.getDayOfWeek().getValue();
        int diff = setWeekDate - week;
        LocalDate weekEndDate = now.plus(diff, ChronoUnit.DAYS);
        if (diff >= 0) {
            return weekEndDate;
        } else {
            return weekEndDate.plusDays(7);
        }
    }

    private TalentAssigment findAssignment(Long assignmentId) {
        TalentAssigment talentAssigment = assigmentRepository.findById(assignmentId).orElse(null);
        if (talentAssigment == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return talentAssigment;
    }


}
