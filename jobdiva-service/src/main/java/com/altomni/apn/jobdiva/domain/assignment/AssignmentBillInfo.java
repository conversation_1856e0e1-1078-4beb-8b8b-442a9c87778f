package com.altomni.apn.jobdiva.domain.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A record
 */
@Entity
@Data
@Table(name = "assignment_bill_info")
public class AssignmentBillInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;


    @Column(name = "assignment_id")
    private Long assignmentId;


    @Column(name = "contact_id")
    private Long contactId;


    @Column(name = "is_except")
    private Boolean isExcept;

    @Column(name = "overtime_type")
    @Convert(converter = OverTimeConverter.class)
    private OverTimeType overtimeType;


    @Column(name = "group_invoice_type")
    @Convert(converter = GroupInvoiceTypeConverter.class)
    private GroupInvoiceType groupInvoiceType;


    @Column(name = "group_invoice_content")
    private String groupInvoiceContent;

    @Column(name = "group_invoice_content_type")
    @Convert(converter = GroupInvoiceContentTypeConverter.class)
    private GroupInvoiceContentType invoiceContentType;

    @Column(name = "expense_invoice")
    @Convert(converter = ExpenseInvoiceTypeConverter.class)
    private ExpenseInvoiceType expenseInvoice;

    @Column(name = "discount_type")
    @Convert(converter = DiscountTypeConverter.class)
    private DiscountType discountType;

    @Column(name = "assignment_division")
    @Convert(converter = AssignmentDivisionConverter.class)
    private AssignmentDivision assignmentDivision;

    @Column(name = "payment_terms")
    private BigDecimal paymentTerms;


    @Column(name = "net_bill_Rate")
    private BigDecimal netBillRate;

    @Column(name = "net_overtime_rate")
    private BigDecimal netOverTimeRate;


    @Column(name = "net_doubletime_rate")
    private BigDecimal netDoubleTimeRate;

    @Column(name = "hourly_gm")
    private BigDecimal hourlyGM;


}
