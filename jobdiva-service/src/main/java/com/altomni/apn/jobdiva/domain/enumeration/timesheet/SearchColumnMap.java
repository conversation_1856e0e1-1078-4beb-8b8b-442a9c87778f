package com.altomni.apn.jobdiva.domain.enumeration.timesheet;

import java.util.HashMap;
import java.util.Map;

public class SearchColumnMap {

    public static Map<String,String> COLUMN_MAP = new HashMap<>();

    public static String SPEC_COLUMN_STATUS="status";
    public static String SPEC_COLUMN_JOB_ID="job_id";
    public static String SPEC_COLUMN_WEEK_ENDING="work_date";

    public static String SPEC_COLUMN_WEEK_END="week_end";
    public static String SPEC_COLUMN_MANAGER="manager";
    public static String SPEC_COLUMN_AM="am";
    public static String SPEC_COLUMN_AM_APPROVER="am_approver";
    public static String SPEC_COLUMN_PRIMARY_MANAGER="primary_manager";
    public static String SPEC_COLUMN_REGULAR_HOURS="regular_hours";

    public static String SPEC_EMPLOYMENT_CATEGORY = "employment_category";

    public static String SPEC_BILLING_FREQUENCY = "billing_frequency";

    public static String SPEC_PAYING_FREQUENCY = "payment_frequency";

    public static String SPEC_ASSIGNMENT_DIVISION = "assignment_division";


    static {
        composeColumnMap();
    }

    public static void composeColumnMap() {
        COLUMN_MAP.put("Employee Name","full_name");
        COLUMN_MAP.put("Job Title","job_title");
        COLUMN_MAP.put("Job ID","job_id");
        COLUMN_MAP.put("Manager/Approver","manager");
        COLUMN_MAP.put("Company","company_name");
        COLUMN_MAP.put("Status","status");
        COLUMN_MAP.put("Week Ending","work_date");
        COLUMN_MAP.put("Week end", "week_end");
        COLUMN_MAP.put("AM","am");
        COLUMN_MAP.put("Employee Category","employment_category");
        COLUMN_MAP.put("Billing Frequency","billing_frequency");
        COLUMN_MAP.put("Payment Frequency","payment_frequency");
        COLUMN_MAP.put("Assignment Division","assignment_division");

    }

}
