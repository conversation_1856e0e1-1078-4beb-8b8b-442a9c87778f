package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetComments;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface TimeSheetCommentsRepository extends JpaRepository<TimeSheetComments, Long> {

    @Query(value = " select * from timesheet_comments tc where talent_id = ?2 and work_date = ?1 and  comments_type = ?3 and assignment_id = ?4 ", nativeQuery = true)
    TimeSheetComments findAllByDateAndType(LocalDate startDate, Long talentId, Integer type, Long assignmentId);

    @Query(value = " select * from timesheet_comments tc where talent_id = ?2 and work_date = ?1 and  comments_type = ?3 and assignment_id = ?4 and record_index = ?5 ", nativeQuery = true)
    TimeSheetComments findAllByDateAndType(LocalDate startDate, Long talentId, Integer type, Long assignmentId, Integer recordIndex);

    @Query(value = " select * from timesheet_comments tc where talent_id = ?2 and work_date = ?1 and  comments_type = ?3 and assignment_id = ?4", nativeQuery = true)
    List<TimeSheetComments> findListByDateAndType(LocalDate startDate, Long talentId, Integer type, Long assignmentId);

    List<TimeSheetComments> findAllByAssignmentId(Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_comments where talent_id = ?3 and work_date = ?1 and  comments_type = ?2 and assignment_id = ?4 ", nativeQuery = true)
    void deleteComment(LocalDate endDate, Integer commentType, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_comments where talent_id = ?3 and work_date = ?1 and  comments_type = ?2 and assignment_id = ?4 and record_index = ?5 ", nativeQuery = true)
    void deleteComment(LocalDate endDate, Integer commentType, Long talentId, Long assignmentId, Integer recordIndex);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_comments where talent_id = ?3 and work_date >= ?1 and  comments_type = ?2 and assignment_id = ?4 ", nativeQuery = true)
    void deleteCommentEndDate(LocalDate endDate, Integer commentType, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_comments where talent_id = ?3 and work_date <= ?1 and  comments_type = ?2 and assignment_id = ?4 ", nativeQuery = true)
    void deleteCommentStartDate(LocalDate startDate, Integer commentType, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    void deleteAllByWorkDateBetweenAndTalentIdAndAssignmentIdAndCommentsType(LocalDate startTime, LocalDate endTime, Long talentId, Long assignmentId, CommentsType type);

    @Modifying
    @Transactional
    void deleteAllByTalentIdAndAssignmentIdAndWorkDateIsInAndCommentsType(Long talentId, Long assignmentId, List<LocalDate> workDateList, CommentsType commentsType);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_comments where assignment_id = ?1 ", nativeQuery = true)
    void deleteByAssignmentId(Long assignmentId);

    List<TimeSheetComments> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
