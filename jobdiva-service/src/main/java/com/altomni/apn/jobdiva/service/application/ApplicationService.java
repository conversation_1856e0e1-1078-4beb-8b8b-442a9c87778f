package com.altomni.apn.jobdiva.service.application;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessNodeVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.xxljob.XxlJobInvoiceOverdueDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(value = "application-service")
public interface ApplicationService {

    @GetMapping("/application/api/v3/talent-recruitment-processes-brief/{id}")
    ResponseEntity<TalentRecruitmentProcessVO> getTalentRecruitmentProcessBrief(@PathVariable("id") Long id);

    @PostMapping("/application/api/v3/xxl-job/invoice-overdue-reminder")
    ResponseEntity<Void> createInvoiceOverDueReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    @DeleteMapping("/application/api/v3/xxl-job/invoice-overdue-reminder")
    ResponseEntity<Void> deleteInvoiceOverDueReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    @PutMapping("/application/api/v3/xxl-job/invoice-overdue-reminder")
    ResponseEntity<Void> updateInvoiceOverDueReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    @DeleteMapping("/application/api/v3/xxl-job/onboard-no-invoice-reminder")
    ResponseEntity<Void> deleteOnboardNoInvoiceReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    @PostMapping("/application/api/v3/xxl-job/onboard-no-invoice-reminder-by-invoice-void")
    ResponseEntity<Void> onboardNoInvoiceReminderByInvoiceVoid(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    @PostMapping("/application/api/v3/talent-recruitment-processes/onboard/cancel-eliminate/talentRecruitmentProcessId/{talentRecruitmentProcessId}")
    ResponseEntity<TalentRecruitmentProcessVO> onboardCancelEliminate(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @GetMapping("/application/api/v3/talent-recruitment-process-node/talentRecruitmentProcessId/{talentRecruitmentProcessId}/{nodeType}")
    ResponseEntity<TalentRecruitmentProcessNodeVO> getNodeByTalentRecruitmentProcessNodeIdAndNodeType(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId, @PathVariable("nodeType") NodeType nodeType);
}
