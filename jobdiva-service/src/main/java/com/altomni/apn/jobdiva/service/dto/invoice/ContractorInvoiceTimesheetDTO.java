package com.altomni.apn.jobdiva.service.dto.invoice;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;

/**
 * query timesheet info and save invoice
 */
@Data
@ApiModel(description = "ContractorInvoiceTimesheetDTO")
public class ContractorInvoiceTimesheetDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigInteger tenantId;

    private String talentName;

    private BigInteger talentId;

    private BigInteger companyId;

    private String companyName;

    private BigInteger companyContactId;

    private String companyContactName;

    private BigInteger jobId;

    private String jobTitle;

    private BigInteger assignmentId;

    private Timestamp weekEndingDate;

    private Timestamp weekStart;

    private Timestamp weekEnd;

    private String assignmentDivision;

    private BigInteger approverId;

    private String approverName;

    private String countryCode;

    private Timestamp approvalDate;

    private BigDecimal regularHours;

    private BigDecimal overTime;

    private BigDecimal doubleTime;

    //查询expense时使用
    private BigDecimal cost;

    //查询expense时使用
    private Timestamp expenseSubmitDate;

    //查询expense时使用
    private Integer expenseIndex = 0;

}
