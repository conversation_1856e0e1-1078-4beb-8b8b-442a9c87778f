package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.jobdiva.domain.assignment.AssignmentTimeSheet;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentExtendInfoVO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface TimeSheetRepository extends JpaRepository<AssignmentTimeSheet, Long> {

    void deleteByAssignmentId(Long assignmentId);

    AssignmentTimeSheet findByAssignmentId(Long assignmentId);

    @Query(value = " select ast.week_ending from assignment_timesheet ast left join timesheet_talent_assignment tta on ast.assignment_id = tta.id where tta.start_date<=?1 and tta.end_date>=?1 and tta.talent_id = ?2 and tta.status = 1 ", nativeQuery = true)
    Integer findCurrentDateWeekEndingType(LocalDate date, Long talentId);

    @Query(value = " select tta.id assignmentId, t.id talentId, t.full_name talentName, c.full_business_name companyName" +
            " from timesheet_talent_assignment tta " +
            " inner join talent t on t.id = tta.talent_id inner join company c on c.id = tta.company_id " +
            " where t.full_name = ?1 and c.`full_business_name` = ?2 and tta.tenant_id = ?3 ", nativeQuery = true)
    List<AssignmentExtendInfoVO> findAssignmentByTalentNameAndCompanyName(String talentName, String companyName, Long tenantId);

    @Query(value = " select * from assignment_timesheet t " +
            " where assignment_id in ?1 ", nativeQuery = true)
    List<AssignmentTimeSheet> findFrequencyByAssignmentId(List<Long> assignmentIds);

    @Query(value = " select * from assignment_timesheet t " +
            "where assignment_id in (select assignment_id from t_contractor_invoice " +
            "where id in (select invoice_id from t_group_invoice_record where group_invoice_id= ?1 and status=1))", nativeQuery = true)
    List<AssignmentTimeSheet> findFrequencyByGroupInvoiceId(Long groupInvoiceId);

    List<AssignmentTimeSheet> findAllByAssignmentIdIn(List<Long> assignmentIds);
}
