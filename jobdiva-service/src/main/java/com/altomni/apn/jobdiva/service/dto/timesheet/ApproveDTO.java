package com.altomni.apn.jobdiva.service.dto.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * A record
 */
@ApiModel(description = "record for expense")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class ApproveDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "record ids ")
    private Set<Long> recordIds;

    @ApiModelProperty(value = "approve status reject or approve")
    private TimeSheetStatus status;

    @ApiModelProperty(value = "approval opinions")
    private String opinion;

}
