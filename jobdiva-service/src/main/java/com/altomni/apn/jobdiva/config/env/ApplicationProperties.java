package com.altomni.apn.jobdiva.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.storeService.onBoarding.bucket:apn-onboarding-staging}")
    private String onboardingBucket;

    @Value("${application.mainPath.baseUrl}")
    private String baseUrl;

    @Value("${application.mainPath.jobDivaUrl}")
    private String jobDivaUrl;

    @Value("${application.emailService.supportSender}")
    private String supportSender;

    @Value("${application.sync.threadNum:8}")
    private Integer threadNum;

    @Value("${application.security.aes.secretKey}")
    private String secret;

    @Value("${application.activeUserPeriod:3600}")
    private Integer activeUserPeriod;

    @Value("${application.elasticrecord.url}")
    private String activityESUrl;

}
