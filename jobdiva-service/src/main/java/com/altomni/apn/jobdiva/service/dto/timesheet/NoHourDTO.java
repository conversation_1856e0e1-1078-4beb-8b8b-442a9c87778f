package com.altomni.apn.jobdiva.service.dto.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.ManagerRoleType;
import com.altomni.apn.common.domain.enumeration.jobdiva.OverTimeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.OperationType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Data
@ApiModel
public class NoHourDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Set<LocalDate> dates;
    private String comments;
    private LocalDate weekEndingDate;

    private LocalDate weekStart;

    private LocalDate weekEnd;

    private TimeSheetType type;

    private List<TimeSheetHolidayRecordDto> holidayRecordSaveList;

    private Long assignmentId;

    private OperationType operationType;

    private ManagerRoleType roleType;

    private OverTimeType overTimeType;

}
