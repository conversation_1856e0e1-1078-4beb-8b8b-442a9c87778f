package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.AutoAbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * A record
 */
@Entity
@Data
@Table(name = "timesheet_approve_record")
public class ApproveRecord extends AutoAbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GenericGenerator(name = "RedisIdGenerator",
            strategy = "com.altomni.apn.jobdiva.config.idgenerator.RedisIdGenerator",
            parameters = { @org.hibernate.annotations.Parameter(name = "table", value = "timesheet_approve_record")})
    @GeneratedValue(generator = "RedisIdGenerator")
    @Access(AccessType.PROPERTY)
    private Long id;


    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;


    @Column(name = "record_id", nullable = false)
    private Long recordId;

    @Column(name = "week_end")
    private LocalDate weekEnd;

    @Column(name = "assignment_id")
    private Long assignmentId;

    @Column(name = "operator_id")
    private Long operator;

    @Column(name = "role", nullable = false)
    private ManagerRoleType role;


    @Column(name = "status", nullable = false)
    @Convert(converter = TimeSheetStatusConverter.class)
    private TimeSheetStatus status;


    @Column(name = "record_type", nullable = false)
    @Convert(converter = CommentsTypeConverter.class)
    private CommentsType type;

    @Column(name = "opinion")
    private String opinion;


}
