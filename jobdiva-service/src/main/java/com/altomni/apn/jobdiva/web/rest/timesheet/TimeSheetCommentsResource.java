package com.altomni.apn.jobdiva.web.rest.timesheet;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetComments;
import com.altomni.apn.jobdiva.repository.timesheet.ExpenseRecordRepository;
import com.altomni.apn.jobdiva.service.dto.timesheet.TimeSheetCommentsDTO;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetCommentsService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = {"TimeSheetComments"})
@RestController
@RequestMapping("/api/v3/timesheet/comments")
public class TimeSheetCommentsResource {

    private final Logger log = LoggerFactory.getLogger(TimeSheetCommentsResource.class);

    @Resource
    private TimeSheetCommentsService commentsService;

    @Resource
    private ExpenseRecordRepository expenseRecordRepository;

    @PostMapping("/workDate")
    @Timed
    public ResponseEntity<TimeSheetComments> findByWorkEndingDate(@RequestBody TimeSheetCommentsDTO dto) {
        log.info("[timesheet: User @{}] REST find time sheet comments , param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        TimeSheetComments record;
        if (ObjectUtil.isNotEmpty(dto.getRecordId())) {
            ExpenseRecord expenseRecord = expenseRecordRepository.findById(dto.getRecordId()).get();
            record = commentsService.findByWorkDateAndType(dto.getWorkDate(),dto.getType(),dto.getAssignmentId(), expenseRecord.getExpenseIndex());
        } else {
            record = commentsService.findByWorkDateAndType(dto.getWorkDate(),dto.getType(),dto.getAssignmentId());
        }
        return ResponseEntity.ok(record);

    }

    @PostMapping("/am/workDate")
    @Timed
    public ResponseEntity<List<TimeSheetComments>> findByWorkEndingDateByAm(@RequestBody TimeSheetCommentsDTO dto) {
        log.info("[timesheet: User @{}] REST find time comments , param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        List<TimeSheetComments> records = commentsService.findByWorkDateAndTypeByAm(dto.getWorkDate(), dto.getType(), dto.getAssignmentId());
        return ResponseEntity.ok(records);

    }


}
