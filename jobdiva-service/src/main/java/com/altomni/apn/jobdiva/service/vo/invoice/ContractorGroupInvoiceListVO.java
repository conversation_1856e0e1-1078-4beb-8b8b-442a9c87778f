package com.altomni.apn.jobdiva.service.vo.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.GroupInvoiceStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;

/**
 * return group invoice list
 */
@ApiModel(description = "ContractorGroupInvoiceListVO")
public class ContractorGroupInvoiceListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private BigInteger id;
    private Timestamp groupInvoiceDate;
    private String groupInvoiceNumber;
    private String groupInvoiceStatus;
    private String invoiceType;
    private BigDecimal invoiceAmount;
    private BigDecimal amountDue;
    private String currency;
    private String billingCompany;
    private BigInteger companyId;
    private String assignmentDivision;
    private String sentBy;
    private Timestamp sentOn;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public Timestamp getGroupInvoiceDate() {
        return groupInvoiceDate;
    }

    public void setGroupInvoiceDate(Timestamp groupInvoiceDate) {
        this.groupInvoiceDate = groupInvoiceDate;
    }

    public String getGroupInvoiceNumber() {
        return groupInvoiceNumber;
    }

    public void setGroupInvoiceNumber(String groupInvoiceNumber) {
        this.groupInvoiceNumber = groupInvoiceNumber;
    }

    public String getGroupInvoiceStatus() {
        return groupInvoiceStatus;
    }

    public void setGroupInvoiceStatus(String groupInvoiceStatus) {
        this.groupInvoiceStatus = groupInvoiceStatus != null ? GroupInvoiceStatus.getNameFromDbValue(Integer.valueOf(groupInvoiceStatus)) : "";
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType != null ? InvoiceType.getNameFromDbValue(Integer.valueOf(invoiceType)) : "";
    }

    public BigDecimal getInvoiceAmount() {
        return invoiceAmount;
    }

    public void setInvoiceAmount(BigDecimal invoiceAmount) {
        this.invoiceAmount = invoiceAmount;
    }

    public BigDecimal getAmountDue() {
        return amountDue;
    }

    public void setAmountDue(BigDecimal amountDue) {
        this.amountDue = amountDue;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getBillingCompany() {
        return billingCompany;
    }

    public void setBillingCompany(String billingCompany) {
        this.billingCompany = billingCompany;
    }

    public BigInteger getCompanyId() {
        return companyId;
    }

    public void setCompanyId(BigInteger companyId) {
        this.companyId = companyId;
    }

    public String getAssignmentDivision() {
        return assignmentDivision;
    }

    public void setAssignmentDivision(String assignmentDivision) {
        this.assignmentDivision =  assignmentDivision;
    }

    public String getSentBy() {
        return sentBy;
    }

    public void setSentBy(String sentBy) {
        this.sentBy = sentBy;
    }

    public Timestamp getSentOn() {
        return sentOn;
    }

    public void setSentOn(Timestamp sentOn) {
        this.sentOn = sentOn;
    }
}
