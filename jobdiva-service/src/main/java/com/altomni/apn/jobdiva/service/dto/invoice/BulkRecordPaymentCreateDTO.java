package com.altomni.apn.jobdiva.service.dto.invoice;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;

/**
 * used in create payment interface
 */
@ApiModel(description = "BulkRecordPaymentCreateDTO")
@Data
public class BulkRecordPaymentCreateDTO extends RecordPaymentCommonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    List<BigInteger> invoiceIdList;
}
