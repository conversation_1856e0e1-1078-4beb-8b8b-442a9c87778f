package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.SearchConfigType;
import com.altomni.apn.common.domain.enumeration.jobdiva.SearchConfigTypeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

import static com.altomni.apn.common.domain.enumeration.jobdiva.SearchConfigType.*;

/**
 * A record
 */
@ApiModel(description = "config for timeSheet search")
@Entity
@Data
@Table(name = "timesheet_search_config")
public class TimeSheetSearchConfig implements Serializable {
    private static final long serialVersionUID = 1L;
    private static final String DEFAULT_NAME = "Default";
    private static final String DEFAULT_TIME_SHEET_TABLE_CONFIG = "[{\"colId\":\"checkbox\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"\",\"width\":54,\"column\":\"\"},{\"colId\":\"WORK_DATE\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Week Ending\",\"width\":200,\"column\":\"endingDate\"},{\"colId\":\"TALENT_NAME\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Employee Name\",\"width\":200,\"column\":\"talentName\"},{\"colId\":\"employee_category\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"Employee Category\",\"width\":200,\"column\":\"employeeCategory\"},{\"colId\":\"COMPANY_NAME\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Company\",\"width\":200,\"column\":\"companyName\"},{\"colId\":\"JOB_TITLE\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Job Title\",\"width\":200,\"column\":\"jobTitle\"},{\"colId\":\"REGULAR_HOURS\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Regular Hours\",\"width\":200,\"column\":\"regularHours\"},{\"colId\":\"OT\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"OT\",\"width\":200,\"column\":\"overTime\"},{\"colId\":\"DT\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"DT\",\"width\":200,\"column\":\"doubleTime\"},{\"colId\":\"billing_frequency\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"Billing Frequency\",\"width\":200,\"column\":\"billingFrequency\"},{\"colId\":\"payment_frequency\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"Payment Frequency\",\"width\":200,\"column\":\"paymentFrequency\"},{\"colId\":\"assignment_division\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"Assignment Division\",\"width\":200,\"column\":\"assignmentDivision\"},{\"colId\":\"SUBMITTED_DATE\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Submitted Time\",\"width\":200,\"column\":\"appliedDate\"},{\"colId\":\"MANAGER\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Manager/Approver\",\"width\":200,\"column\":\"manager\"},{\"colId\":\"STATUS\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Status\",\"width\":200,\"column\":\"status\"},{\"colId\":\"ON\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"On\",\"width\":200,\"column\":\"approvedDate\"},{\"colId\":\"JOB_ID\",\"showFlag\":false,\"sortFlag\":true,\"label\":\"Job ID\",\"width\":200,\"column\":\"jobId\"},{\"colId\":\"AM\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"AM\",\"width\":200,\"column\":\"am\"}]";
    private static final String DEFAULT_EXPENSE_TABLE_CONFIG = "[{\"colId\":\"checkbox\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"\",\"width\":54,\"column\":\"\"},{\"colId\":\"WORK_DATE\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Week Ending\",\"width\":200,\"column\":\"endingDate\"},{\"colId\":\"TALENT_NAME\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Employee Name\",\"width\":200,\"column\":\"talentName\"},{\"colId\":\"employee_category\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"Employee Category\",\"width\":200,\"column\":\"employeeCategory\"},{\"colId\":\"COMPANY_NAME\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Company\",\"width\":200,\"column\":\"companyName\"},{\"colId\":\"JOB_TITLE\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Job Title\",\"width\":200,\"column\":\"jobTitle\"},{\"colId\":\"AMOUNT\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Total\",\"width\":200,\"column\":\"amountFormat\"},{\"colId\":\"billing_frequency\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"Billing Frequency\",\"width\":200,\"column\":\"billingFrequency\"},{\"colId\":\"payment_frequency\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"Payment Frequency\",\"width\":200,\"column\":\"paymentFrequency\"},{\"colId\":\"assignment_division\",\"showFlag\":true,\"sortFlag\":false,\"label\":\"Assignment Division\",\"width\":200,\"column\":\"assignmentDivision\"},{\"colId\":\"SUBMITTED_DATE\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Submitted Time\",\"width\":200,\"column\":\"appliedDate\"},{\"colId\":\"MANAGER\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Manager/Approver\",\"width\":200,\"column\":\"manager\"},{\"colId\":\"STATUS\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"Status\",\"width\":200,\"column\":\"status\"},{\"colId\":\"ON\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"On\",\"width\":200,\"column\":\"approvedDate\"},{\"colId\":\"JOB_ID\",\"showFlag\":false,\"sortFlag\":true,\"label\":\"Job ID\",\"width\":200,\"column\":\"jobId\"},{\"colId\":\"AM\",\"showFlag\":true,\"sortFlag\":true,\"label\":\"AM\",\"width\":200,\"column\":\"am\"}]";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "uid", nullable = false)
    private Long uid;

    @ApiModelProperty(value = "tenant id ")
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @ApiModelProperty(value = "config")
    @Column(name = "config")
    private String  config;

    @ApiModelProperty(value = "name")
    @Column(name = "name")
    private String  name;

    @ApiModelProperty(value = "is default ")
    @Column(name = "is_default")
    private Boolean  isDefault;

    @ApiModelProperty(value = "config type ")
    @Column(name = "search_type")
    @Convert(converter = SearchConfigTypeConverter.class)
    private SearchConfigType searchType;

    @ApiModelProperty(value = "filter type ")
    @Column(name = "filter_type")
    @Convert(converter = SearchConfigTypeConverter.class)
    private SearchConfigType filterType;

    @Column(name = "created_time")
    private Instant createdTime;

    public TimeSheetSearchConfig() {
    }

    public TimeSheetSearchConfig(SearchConfigType searchType, Long uid, Long tenantId) {
        switch (searchType) {
            case TIME_SHEET_TABLE:
                loadDefaultTimeSheetTable(uid, tenantId);
                break;
            case EXPENSE_TABLE:
                loadDefaultExpenseTable(uid, tenantId);
                break;
            case COMMON_FILTER:
            case ADVANCE_FILTER:
            case TIME_SHEET_FILTER:
            case EXPENSE_FILTER:
            default:
                // do nothing
        }
    }

    private void loadDefaultTimeSheetTable(Long uid, Long tenantId) {
        this.setUid(uid);
        this.setTenantId(tenantId);
        this.setConfig(DEFAULT_TIME_SHEET_TABLE_CONFIG);
        this.setName(DEFAULT_NAME);
        this.setIsDefault(true);
        this.setSearchType(TIME_SHEET_TABLE);
        this.setFilterType(TIME_SHEET_FILTER);
        this.setCreatedTime(Instant.now());
    }

    private void loadDefaultExpenseTable(Long uid, Long tenantId) {
        this.setUid(uid);
        this.setTenantId(tenantId);
        this.setConfig(DEFAULT_EXPENSE_TABLE_CONFIG);
        this.setName(DEFAULT_NAME);
        this.setIsDefault(true);
        this.setSearchType(EXPENSE_TABLE);
        this.setFilterType(EXPENSE_FILTER);
        this.setCreatedTime(Instant.now());
    }

}
