package com.altomni.apn.jobdiva.repository.settings;

import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingDocuments;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * Spring Data JPA repository for the OnBoardingDocuments entity.
 */
@Repository
public interface OnBoardingDocumentsRepository extends JpaRepository<OnBoardingDocuments, Long> {

    List<OnBoardingDocuments> findAllByIdInAndTenantIdAndActivated(List<Long> ids, Long tenantId, Bo<PERSON>an activated);

    Optional<OnBoardingDocuments> findAllByIdAndTenantIdAndActivated(Long id, Long tenantId, <PERSON><PERSON><PERSON> activated);

    @Query(value = "select count(d.id) from OnBoardingDocuments d where d.name = ?1 and d.tenantId = ?2 and d.activated = true")
    Integer countByNameAndTenantId(String name, Long tenantId);

    @Query(value = "select count(d.id) from OnBoardingDocuments d where d.id<>?1 and d.name = ?2 and d.tenantId = ?3 and d.activated = true")
    Integer countByDocumentIdAndNameAndTenantId(Long documentId, String name, Long tenantId);

    @Query(value = "select new com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsDTO(d.id, d.name, d.s3Key, d.actionRequired, u.username, d.lastModifiedDate) " +
        " from OnBoardingDocuments d left join User u on d.lastModifiedBy = u.uid where d.activated = ?1 and d.tenantId = ?2 order by d.actionRequired asc, d.lastModifiedDate desc ")
    List<OnBoardingDocumentsDTO> findAllByActivatedAndTenantIdOrderByActionRequiredAscLastModifiedDateDesc(Boolean activated, Long tenantId);

    @Query(value = "select new com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsDTO(t.packageId, t.documentId,d.name, d.s3Key, t.ordering,t.onboardingType) from OnBoardingDrafts t left join OnBoardingDocuments d on t.documentId = d.id " +
        "where t.talentRecruitmentProcessId = ?1 and t.tenantId = ?2 ")
    List<OnBoardingDocumentsDTO> findAllByTalentRecruitmentProcessIdAndTenantId(Long talentRecruitmentProcessId, Long tenantId);
}
