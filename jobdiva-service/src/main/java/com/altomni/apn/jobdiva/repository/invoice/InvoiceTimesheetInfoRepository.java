package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.TInvoiceTimesheetInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * Spring Data JPA repository for the InvoiceTimesheetInfoRepository entity.
 * <AUTHOR> zhang.lei
 * @date : 2023-6-6
 */
@Repository
public interface InvoiceTimesheetInfoRepository extends JpaRepository<TInvoiceTimesheetInfo, BigInteger> {

    @Modifying
    @Query(value = "delete from t_invoice_timesheet_info where invoice_id = ?1 and quantity_type=?2", nativeQuery = true)
    void deleteByInvoiceIdAnAndQuantityType(BigInteger invoiceId,Integer quantityType);

    @Modifying
    @Query(value = "delete from t_invoice_timesheet_info where invoice_id = ?1 ", nativeQuery = true)
    void deleteByInvoiceId(BigInteger invoiceId);

    @Query(value = "select DISTINCT tab.currency_type from (\n" +
            "select currency_type from t_invoice_timesheet_info t\n" +
            "where t.invoice_id in(?1) GROUP BY currency_type\n" +
            "union ALL\n" +
            "select currency_type from t_invoice_expense_info t\n" +
            "where t.invoice_id in(?1) GROUP BY currency_type\n" +
            ") tab",nativeQuery = true)
    List<Integer> queryCurrencyTypeByInvoiceId(List<BigInteger> invoiceIdList);

    List<TInvoiceTimesheetInfo> findByInvoiceId(BigInteger invoiceId);
}
