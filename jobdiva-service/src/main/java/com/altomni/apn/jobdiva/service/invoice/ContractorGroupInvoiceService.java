package com.altomni.apn.jobdiva.service.invoice;

import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import com.altomni.apn.jobdiva.service.dto.invoice.*;
import com.altomni.apn.jobdiva.service.vo.invoice.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigInteger;
import java.util.List;

public interface ContractorGroupInvoiceService {

    Page<ContractorGroupInvoiceListVO> searchGroupInvoice(ContractorGroupInvoiceSearchDTO dto, Pageable pageable);

    ContractorGroupInvoiceCreateVO save(ContractorGroupInvoiceCreateDTO dto);

    void modify(ContractorGroupInvoiceEditDTO dto);

    ContractorGroupInvoiceViewVO view(BigInteger id);

    void addRecordPayment(RecordPaymentCreateDTO dto);

    void addPurchaseOrderRecordPayment(PurchaseOrderRecordPaymentCreateDTO dto);

    void bulkRecordPayment(BulkRecordPaymentCreateDTO dto);

    void unRecordPayment(BigInteger id);

    List<PaymentRecordListVO> getPaymentRecordList(BigInteger groupInvoiceId);

    void ungroup(ContractorGroupInvoiceVoidAndPrintDTO dto);

    void groupInvoiceVoid(ContractorGroupInvoiceVoidAndPrintDTO dto);

    List<ContractorGroupInvoiceAttachmentVO> getAttachmentList(ContractorGroupInvoiceVoidAndPrintDTO dto);

    void sendEmail(ContractorGroupInvoiceSendEmailDTO dto) throws Exception;

    void sendToClient(ContractorGroupInvoiceSendEmailDTO dto) throws Exception;

    StoreGetUploadUrlVO getUploadUrl(String uuid, String fileName);

    ContractorGroupInvoicePrintVO print(ContractorGroupInvoiceVoidAndPrintDTO dto);

    String updateGroupInvoiceStatus();

    String searchFileDoesNotExist();

    String dataMigrateGroupInvoice();

    String initPaymentDetail();
}
