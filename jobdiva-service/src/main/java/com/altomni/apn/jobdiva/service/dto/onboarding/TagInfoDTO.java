package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class TagInfoDTO {

    private Integer order;

    private float left;

    private float right;

    private float bottom;

    private float top;

    private Integer page;

    private String type;

    private String value;

    //checkbox only
    private boolean check;

    private String tag;

    public TagInfoDTO(float left, float right, float bottom, float top, int page, String type, String tag) {
        this.left = left;
        this.right = right;
        this.bottom = bottom;
        this.top = top;
        this.page = page;
        this.type = type;
        this.tag = tag;
    }

    public TagInfoDTO(float left, float right, float bottom, float top, int page, String type, String tag, boolean check) {
        this.left = left;
        this.right = right;
        this.bottom = bottom;
        this.top = top;
        this.page = page;
        this.type = type;
        this.tag = tag;
        this.check = check;
    }
}
