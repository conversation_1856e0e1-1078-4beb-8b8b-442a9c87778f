package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.TGroupInvoice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.List;
import java.util.Set;

@Repository
public interface GroupInvoiceRepository extends JpaRepository<TGroupInvoice,BigInteger> {

    @Modifying
    @Transactional
    @Query(value = "update  t_group_invoice  set status=0,last_modified_date=now() where id in ?1 ",nativeQuery = true)
    void updateStatusByIds(Set<Long> groupInvoiceIdList);

    @Query(value = " select operated_by_id from t_group_invoice where operated_by_id is not null and status=1 and id in ( ?1 )",nativeQuery = true)
    List<BigInteger> querySentOnByIds(Set<Long> groupInvoiceIdList);

    TGroupInvoice findByIdAndStatusAndTenantId(BigInteger id,Integer status,BigInteger tenantId);

    TGroupInvoice findByIdAndStatus(BigInteger id,Integer status);

    TGroupInvoice findByGroupNumberAndStatus(String groupNumber,Integer status);

    /**
     * 查询出的id与入参id数量是否相等
     * @param groupInvoiceIdList
     * @return
     */
    @Query(value = "select * from t_group_invoice where status=1 and group_invoice_status in ( ?2 ) and id in ( ?1 )",nativeQuery = true)
    List<TGroupInvoice> queryIdByIdsAndGroupInvoiceStatus(List<BigInteger> groupInvoiceIdList,List<Integer> groupInvoiceStatus);

    @Modifying
    @Transactional
    @Query(value = "update  t_group_invoice  set group_invoice_status=3,amount_due=0,last_modified_date=now() where id in ( ?1 ) ",nativeQuery = true)
    void updateGroupInvoiceStatusAndAmountDueByIds(List<BigInteger> groupInvoiceIdList);

    @Modifying
    @Transactional
    @Query(value = "update  t_group_invoice  set group_invoice_status=?2,last_modified_date=now() where id in ( ?1 ) ",nativeQuery = true)
    void updateGroupInvoiceStatusByIds(List<BigInteger> groupInvoiceIdList,Integer groupInvoiceStatus);

    @Modifying
    @Transactional
    @Query(value = "update  t_group_invoice  set group_invoice_status=?2,last_modified_date=now(),amount_due = invoice_amount where id in ( ?1 ) ",nativeQuery = true)
    void updateGroupInvoiceStatusAndAmountDueByIds(List<BigInteger> groupInvoiceIdList,Integer groupInvoiceStatus);

    @Modifying
    @Transactional
    @Query(value = "update  t_group_invoice  set operated_by_name=?2,operated_by_id=?3,group_invoice_status=5,operated_date=now(),last_modified_date=now(),invoice_date=now() where id in ?1 ",nativeQuery = true)
    void updateOperateUserByIds(List<BigInteger> groupInvoiceIdList,String operateName,Long operateId);

    /**
     * 根据id查询信息
     * @param groupInvoiceIdList
     * @return
     */
    @Query(value = "select * from t_group_invoice where status=1 and id in ( ?1 )",nativeQuery = true)
    List<TGroupInvoice> queryIdByIds(List<BigInteger> groupInvoiceIdList);

    /**
     * 查询所有数据信息
     * @return
     */
    @Query(value = "select id from t_group_invoice where payment_due_date<now() and group_invoice_status in(4,5) and status=1",nativeQuery = true)
    List<BigInteger> selectGroupInvoiceIdByGroupInvoiceStatus();

    /**
     * 查询文件是空的数据
     * @return
     */
    @Query(value = "select id from t_group_invoice where (file_url is null or timesheet_file_url is null ) and status=1",nativeQuery = true)
    List<BigInteger> selectGroupInvoiceIdByFileIsNull();

    /**
     * 根据group number 删除group invoice信息，数据迁移使用
     * @param groupNumber
     * @param tenantId
     * @return
     */
    @Modifying
    @Transactional
    @Query(value = "update  t_group_invoice  set status=0,last_modified_date=now() where group_number=?1 and tenant_id=?2 ",nativeQuery = true)
    void updateStatusByGroupNumberAndTenantId(String groupNumber,Long tenantId);

    /**
     * 查询部分支付和全部支付的数据
     * @return
     */
    @Query(value = "select * from t_group_invoice where status=1 and group_invoice_status in (3,4) and id >?1 order by id asc limit 20",nativeQuery = true)
    List<TGroupInvoice> queryPaidAndPartiallyPaidLimit(Integer count);

    @Query(value = """
    select distinct s.talent_recruitment_process_id from t_group_invoice tgi
    	LEFT JOIN t_group_invoice_record tgir ON tgir.group_invoice_id = tgi.id
    LEFT JOIN t_contractor_invoice tci on tgir.invoice_id = tci.id
    	LEFT JOIN timesheet_talent_assignment tta ON tci.assignment_id = tta.id
    	LEFT JOIN start s ON tta.start_id = s.id
    where tgi.id = ?1
""",nativeQuery = true)
    List<Long> queryTalentRecruitmentProcessByGroupInvoice(Long id);


    @Query(value = """
    	select distinct tci.talent_id FROM t_group_invoice tgi
    	LEFT JOIN t_group_invoice_record tgir ON tgir.group_invoice_id = tgi.id
    	LEFT JOIN t_contractor_invoice tci on tgir.invoice_id = tci.id
    	where tgi.id in (?1)
    """,nativeQuery = true)
    List<Long> getRelateTalentId(List<Long> ids);
}