package com.altomni.apn.jobdiva.service.vo.invoice;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "ContractorInvoiceCreateVO")
public class ContractorInvoiceCreateVO implements Serializable {

    private BigInteger id;

    private List<ContractorInvoiceFailedVO> data;

    private String message;
}
