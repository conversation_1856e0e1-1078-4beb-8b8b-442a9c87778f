package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;

import java.util.Set;

public interface BaseService {
    Boolean isNoHourStatus(TimeSheetStatus status, Float regularHours);

    Boolean hasPermission(Set<Long> recordIds, RecordType recordType, Long userId);

    Boolean hasPermissionEdit(Set<Long> recordIds, RecordType recordType, Long userId);

    Boolean hasPermissionEdit(Long assignmentId, RecordType recordType, Long userId);

    Boolean hasPermission(Long assignmentId, RecordType recordType, Long userId);

}
