package com.altomni.apn.jobdiva.service.vo.timesheet;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.LocalDateExcelDateConverter;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.TimeSheetStatusDateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

/**
 * A record
 */
@ApiModel(description = "record for expense")
@Entity
@Data
public class ExpenseListVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long  id;

    @ApiModelProperty(value = "work date")
    @ExcelIgnore
    private LocalDate endingDate;

    @ApiModelProperty(value = "applied date")
    @ColumnWidth(20)
    @ExcelIgnore
    private Instant appliedDate;

    @ExcelProperty(value = "Submitted Time",index = 4)
    @Transient
    private String appliedDateStr;

    @ApiModelProperty(value = "approved date")
    @ColumnWidth(20)
    @ExcelIgnore
    private Instant approvedDate;

    @ExcelProperty(value = "On",index = 9)
    @Transient
    private String approvedDateStr;

    @ApiModelProperty(value = "total expense")
//    @ColumnWidth(20)
//    @ExcelProperty(value = "Amount",index = 3,converter = FloatConverter.class)
    @ExcelIgnore
    private Float amount;

    @ApiModelProperty(value = "total expense format")
    @Transient
    @ColumnWidth(20)
    @ExcelProperty(value = "Amount",index = 3)
    private String amountFormat;

    @ExcelIgnore
    private Integer currency;

    @ColumnWidth(20)
    @ExcelProperty(value = "Manager",index = 7)
    @ApiModelProperty(value = "manager")
    private String manager;

    @ApiModelProperty(value = "talent name")
    @ColumnWidth(20)
    @ExcelProperty(value = "Employee Name",index = 1)
    private String talentName;

    @ApiModelProperty(value = "talent id")
    @ExcelIgnore
    private String talentId;

    @ApiModelProperty(value = "talentName")
    @ColumnWidth(20)
    @ExcelProperty(value = "Job Title",index = 6)
    private String jobTitle;

    @ApiModelProperty(value = "talentName")
    @ColumnWidth(20)
    @ExcelProperty(value = "Company",index = 5)
    private String companyName;

    @ApiModelProperty(value = "job start date")
    @ExcelIgnore
    private LocalDate startDate;

    @ApiModelProperty(value = "job end date")
    @ExcelIgnore
    private LocalDate endDate;

    @ApiModelProperty(value = "status")
    @ColumnWidth(20)
    @ExcelProperty(value = "Status",index = 8,converter = TimeSheetStatusDateConverter.class)
    @Convert(converter = TimeSheetStatusConverter.class)
    private TimeSheetStatus status;

    @ExcelIgnore
    @Transient
    private String comments;


    @ApiModelProperty(value = "am name")
    @ExcelIgnore
    private String am;

    @ApiModelProperty(value = "assignment id")
    @ExcelIgnore
    private Long assignmentId;

    @ExcelIgnore
    private String primaryManager;

    @ColumnWidth(20)
    @ExcelIgnore
    private Long  jobId;

    @ApiModelProperty(value = "expense type")
    @ExcelIgnore
    private ExpenseType expenseType;

    @ExcelIgnore
    private LocalDate weekStart;

    @ColumnWidth(20)
    @ExcelProperty(value = "Week Ending",index = 2,converter = LocalDateExcelDateConverter.class)
    private LocalDate weekEnd;

    @ExcelIgnore
    private LocalDate weekEndingDate;

    @ExcelIgnore
    private Integer expenseIndex;

    @ExcelIgnore
    private Long amApproverId;

}
