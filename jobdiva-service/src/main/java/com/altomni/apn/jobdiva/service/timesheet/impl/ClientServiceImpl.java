package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.jobdiva.config.env.ApplicationProperties;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;
import com.altomni.apn.jobdiva.domain.timesheet.ApproveRecord;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetComments;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.repository.timesheet.*;
import com.altomni.apn.jobdiva.service.assignment.AssignmentSyncToHrService;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.mail.MailService;
import com.altomni.apn.jobdiva.service.rabbitmq.JobdivaToHrRabbitService;
import com.altomni.apn.jobdiva.service.talent.TalentService;
import com.altomni.apn.jobdiva.service.timesheet.*;
import com.altomni.apn.jobdiva.service.user.UserService;
import com.altomni.apn.jobdiva.service.vo.timesheet.*;
import com.altomni.apn.jobdiva.util.CommonAmIdUtil;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.altomni.apn.common.utils.DateUtil.fromInstantToDate;
import static com.altomni.apn.common.utils.DateUtil.fromInstantToDateString;
import static com.altomni.apn.common.utils.SqlUtil.PARTITION_COUNT_999;

@Slf4j
@Service("clientService")
public class ClientServiceImpl extends BaseServiceImpl implements ClientService {

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private EntityManager entityManager;

    @Resource
    private ApproveRecordRepository approve;

    @Resource
    private TimeSheetRecordRepository timeSheet;

    @Resource
    private DownLoadExcelDetailService downLoadExcelDetailService;

    @Resource
    private DownloadPdfDetailService downloadPdfDetailService;

    @Resource
    @Qualifier("timeSheetRecordService")
    private TimeSheetRecordService timeSheetRecordService;

    @Resource
    private TalentService talentService;

    @Resource
    private TimeSheetExpenseService expenseService;

    @Resource
    private ExpenseRecordRepository expenseRecord;

    @Resource
    private TimeSheetBreakTimeRepository breakTimeRepository;

    @Resource
    private TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    private TalentAssignmentRepository talentAssignmentRepository;

    @Resource
    private ExpenseRecordRepository expenseRecordRepository;

    @Resource
    private TimeSheetCommentsService commentsService;

    @Resource
    private TimeSheetUserRepository timeSheetUserRepository;

    @Resource
    private MailService mailService;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Resource
    private UserService userService;

    private static final String TIMESHEET_RECEIVE_EMAIL_JUMP_PATH = "/myTimesheets/myTimesheetsEditor";

    private static final String EXPENSE_RECEIVE_EMAIL_JUMP_PATH = "/MyExpenses/MyExpensesDetails";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer timeSheetApprove(ApproveDTO dto,ManagerRoleType roleType) {
        Set<Long> recordIds = dto.getRecordIds();
        if (CollUtil.isEmpty(recordIds)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_APPROVE_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        Long clientId = SecurityUtils.getUserId();
        Long tenantId = SecurityUtils.getTenantId();
        Map<Long, TimeSheetRecord> timeSheetRecordMap = timeSheetRecordRepository.findAllByIdIs(dto.getRecordIds()).stream().collect(Collectors.toMap(TimeSheetRecord::getId, a -> a));
        List<ApproveRecord> list  = new LinkedList<>();
        for (Long recordId:recordIds) {
            TimeSheetRecord timeSheetRecord = timeSheetRecordMap.get(recordId);
            ApproveRecord ap = new ApproveRecord();
            ap.setOperator(clientId);
            ap.setRecordId(recordId);
            ap.setStatus(dto.getStatus());
            ap.setTenantId(tenantId);
            ap.setType(CommentsType.TIME_SHEET);
            ap.setRole(roleType);
            ap.setOpinion(dto.getOpinion());
            ap.setAssignmentId(timeSheetRecord.getAssignmentId());
            ap.setWeekEnd(timeSheetRecord.getWeekEnd());
            list.add(ap);
        }
        approve.saveAll(list);
        updateTimeSheetStatus(dto.getRecordIds(),dto.getStatus());
        if (dto.getStatus() == TimeSheetStatus.REJECTED || dto.getStatus() == TimeSheetStatus.APPROVED) {
            sendEmailTimeSheet(dto.getStatus(), recordIds, list);
        }
        assignmentSyncToHrService.buildApproveListSyncToHrMq(list, CommentsType.TIME_SHEET);
        return list.size();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer expenseApprove(ApproveDTO dto,ManagerRoleType roleType) {
        Set<Long> recordIds = dto.getRecordIds();
        if (CollUtil.isEmpty(recordIds)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_APPROVE_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        Long clientId = SecurityUtils.getUserId();
        Long tenantId = SecurityUtils.getTenantId();
        List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByIdIs(new ArrayList<>(dto.getRecordIds()));
        Map<Long, ExpenseRecord> map = expenseRecordList.stream().collect(Collectors.toMap(ExpenseRecord::getId, a -> a, (a1, a2) -> a1));
        List<ApproveRecord> list  = new LinkedList<>();
        for (Long recordId:recordIds) {
            ExpenseRecord expenseRecord = map.get(recordId);
            ApproveRecord ap = new ApproveRecord();
            ap.setOperator(clientId);
            ap.setRecordId(recordId);
            ap.setStatus(dto.getStatus());
            ap.setTenantId(tenantId);
            ap.setType(CommentsType.EXPENSE);
            ap.setRole(roleType);
            ap.setOpinion(dto.getOpinion());
            ap.setAssignmentId(expenseRecord.getAssignmentId());
            ap.setWeekEnd(expenseRecord.getWeekEnd());
            list.add(ap);
        }
        approve.saveAll(list);
        updateExpenseStatus(dto.getRecordIds(),dto.getStatus());
        if (dto.getStatus() == TimeSheetStatus.REJECTED || dto.getStatus() == TimeSheetStatus.APPROVED) {
            sendEmailExpense(dto.getStatus(), recordIds, list);
        }
        assignmentSyncToHrService.buildApproveListSyncToHrMq(list, CommentsType.EXPENSE);
        return list.size();
    }

    @Override
    public Boolean isTimeSheetClientUser(Set<Long> recordIds, Long userId, RecordType recordType) {
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUidLikeAndUserType(userId, TimeSheetUserType.CLIENT.toDbValue());
        return ObjectUtils.isNotEmpty(timeSheetUser);
    }

    @Override
    public SummaryDataVO timeSheetSummary(SummaryQueryDTO dto) {
        SummaryDataVO vo = new SummaryDataVO();
        vo.setPageNum(dto.getPageNum());
        if (dto.getPageNum() <= 0) {
            dto.setPageNum(1);
        }
        int startItem = (dto.getPageNum()-1) * dto.getPageSize();
        int endItem = dto.getPageSize();
        String conditionStr = " and 1=1 ";
        if (dto.getJobTitle() != null && dto.getJobTitle().size() > 0) {
            conditionStr += " and " + toSelectStr(" lower(TSWER.job_title) like ", dto.getJobTitle());
        }
        if (dto.getTalentName() != null && dto.getTalentName().size() > 0) {
            conditionStr += " and " + toSelectStr(" lower(TSWER.full_name) like ", dto.getTalentName());
        }
        if (dto.getStatus() != null) {
            conditionStr += " and TSWER.status in " + toSelectStatus(dto.getStatus());
        }
        if (dto.getStartDate() != null) {
            conditionStr += " and TSWER.work_date >= STR_TO_DATE('" + dto.getStartDate().toString() + "','%Y-%m-%d') ";
        }
        if (dto.getEndDate() != null) {
            conditionStr += " and TSWER.work_date <= STR_TO_DATE('" + dto.getEndDate().toString() + "','%Y-%m-%d') ";
        }
        if (CollUtil.isNotEmpty(dto.getRecordIds())) {
            String ids = CommonUtils.convertListToString(dto.getRecordIds());
            conditionStr += " and TSWER.record_id in ( " + ids + " ) ";
        } else {
            conditionStr += " and TSWER.status in (1,2,4) ";
        }
        if (dto.getClientId() != null) {
            String clientSql = """
         AND EXISTS (
		SELECT
		1
	    FROM
		timesheet_manager tm
		where tm.client_id in  ( SELECT ca.id as id FROM company_sales_lead_client_contact cslcc LEFT JOIN company_sales_lead_client_contact ca ON ca.approver_id = cslcc.approver_id WHERE cslcc.id = {} ) and tm.assignment_id = TSWER.assignment_id and tm.role in (0,1) ) 
		""";
            conditionStr += StrUtil.format(clientSql, dto.getClientId());
        }
        String oderBy;
        String sort;
        if (dto.getOrder() == null || dto.getOrderBy() == null) {
            oderBy = TimeSheetTableOrderType.WEEK_END.getOrderSql() + TimeSheetTableSortType.DESC.getOrderSql();
            sort = " ";
        }
        else if (dto.getOrderBy() == TimeSheetTableOrderType.MANAGER) {
            oderBy = TimeSheetTableOrderType.MANAGER_CLIENT.getOrderSql();
            sort = dto.getOrder().getOrderSql();
        } else {
            oderBy = dto.getOrderBy().getOrderSql();
            sort = dto.getOrder().getOrderSql();
        }
        String sql = """
                select AAA.* from (
                select TSWER.work_date as ending_date,  TSWER.total_hours as total_hours ,TSWER.regular_hours as regular_hours ,TSWER.over_time as over_time,
                TSWER.double_time as double_time,TSWER.full_name as talent_name,TSWER.job_title as job_title,TSWER.company_name as company_name ,TSWER.submitted_date as applied_date,
                TSWER.approved_date approved_date,TSWER.record_id as id, TSWER.status as status,TSWER.time_sheet_type as sheet_type,TSWER.start_date as start_date,
                TSWER.calculate_type as calculate_method,TSWER.end_date as end_date,TSWER.job_id as job_id,TSWER.manager as manager,TSWER.primary_manager as primary_manager ,
                TSWER.am_approver_id, TSWER.am_approver as am, TSWER.assignment_id  as assignment_id,TSWER.instructions,TSWER.overtime_type as overtime_type, TSWER.is_except as is_except, TSWER.week_ending_date,
                TSWER.week_start, TSWER.week_end
                from time_sheet_week_ending_record TSWER
                WHERE assignment_status = 1
                """ + conditionStr
                + " ) AAA " + oderBy + sort  + ", id desc " + " limit ?1,?2";
        String countSql = """
                select count(TSWER.id)
                from time_sheet_week_ending_record TSWER
                where TSWER.assignment_status = 1
                """ + conditionStr;
        List<TimeSheetSummaryVO> result = null;
        try {
            CompletableFuture<List<TimeSheetSummaryVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
                Query query = entityManager.createNativeQuery(sql, TimeSheetSummaryVO.class);
                query.setParameter(1,startItem);
                query.setParameter(2,endItem);
                return query.getResultList();
            });
            CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> {
                Query query = entityManager.createNativeQuery(countSql);
                List<Object> totalItems = query.getResultList();
                return ((BigInteger)totalItems.get(0)).intValue();
            });
            result = dataFuture.get();
            vo.setTotalItems(countFuture.get());
        } catch (Exception e) {
            log.error("Exception occurred in timeSheetSummary query", e);
        }
        if (CollectionUtils.isNotEmpty(result)) {
            List<Long> combinedIds = CommonAmIdUtil.getCombinedIds(result, null, TimeSheetSummaryVO::getAmApproverId);
            Map<Long, String> userIdToNameMap = CommonAmIdUtil.createUserIdToNameMap(combinedIds, userService);
            CommonAmIdUtil.mapNamesToObjects(result, null, TimeSheetSummaryVO::getAmApproverId, null, TimeSheetSummaryVO::setAm, userIdToNameMap);
            result = result.stream().peek(timeSheetSummaryVO -> {
                String mr;
                if (super.isNoHourStatus(timeSheetSummaryVO.getStatus(), timeSheetSummaryVO.getRegularHours())) {
                    if (StrUtil.isNotBlank(timeSheetSummaryVO.getAm())) {
                        mr = timeSheetSummaryVO.getAm();
                    } else {
                        mr = timeSheetSummaryVO.getPrimaryManager();
                    }
                } else {
                    TimeSheetStatus status = timeSheetSummaryVO.getStatus();
                    switch (status) {
                        case MISSING:
                        case APPLIED_APPROVE:
                        case DRAFT:
                        case NO_RECORD:
                            mr = timeSheetSummaryVO.getPrimaryManager();
                            break;
                        case APPROVED:
                        case REJECTED:
                        default:
                            mr = timeSheetSummaryVO.getAm();
                            if (StringUtils.isBlank(mr)) {
                                mr = timeSheetSummaryVO.getManager();
                            }
                            if (StringUtils.isBlank(mr)) {
                                mr = timeSheetSummaryVO.getPrimaryManager();
                            }
                            break;
                    }
                }
                timeSheetSummaryVO.setManager(mr);
                timeSheetSummaryVO.setAppliedDateStr(fromInstantToDateString(timeSheetSummaryVO.getAppliedDate(), dto.getTimezone()));
                timeSheetSummaryVO.setApprovedDateStr(fromInstantToDateString(timeSheetSummaryVO.getApprovedDate(), dto.getTimezone()));
            }).toList();
        }
        vo.setData(result);
        return vo;
    }

    @Override
    public SummaryDataVO expenseSummary(SummaryQueryDTO dto) {
        SummaryDataVO vo = new SummaryDataVO();
        vo.setPageNum(dto.getPageNum());
        if (dto.getPageNum() <= 0) {
            dto.setPageNum(1);
        }
        int startItem = (dto.getPageNum()-1) * dto.getPageSize();
        int endItem = dto.getPageSize();
        String conditionStr = " and 1=1 ";
        if (CollUtil.isNotEmpty(dto.getJobTitle())) {
            conditionStr += " and " + toSelectStr(" lower(TER.job_title) like ", dto.getJobTitle());
        }
        if (CollUtil.isNotEmpty(dto.getTalentName())) {
            conditionStr += " and " + toSelectStr(" lower(TER.full_name) like ", dto.getTalentName());
        }
        if (dto.getStatus() != null) {
            conditionStr += " and TER.status in " + toSelectStatus(dto.getStatus());
        }
        if (dto.getStartDate() != null) {
            conditionStr += " and TER.work_date >= STR_TO_DATE('" + dto.getStartDate().toString() + "','%Y-%m-%d') ";
        }
        if (dto.getEndDate() != null) {
            conditionStr += " and TER.work_date <= STR_TO_DATE('" + dto.getEndDate().toString() + "','%Y-%m-%d') ";
        }
        if (CollUtil.isNotEmpty(dto.getRecordIds())) {
            String ids = CommonUtils.convertListToString(dto.getRecordIds());
            conditionStr += " and TER.record_id in ( " + ids + " )";
        } else {
            conditionStr += " and TER.status in (1,2,4) ";
        }
        if (dto.getClientId() != null) {
            String clientSql = """
         AND EXISTS (
		SELECT
		1
	    FROM
		timesheet_manager tm
		where tm.client_id in  ( SELECT ca.id as id FROM company_sales_lead_client_contact cslcc LEFT JOIN company_sales_lead_client_contact ca ON ca.approver_id = cslcc.approver_id WHERE cslcc.id = {} ) and tm.assignment_id = TER.assignment_id and tm.role in (0,1) ) 
		""";
            conditionStr += StrUtil.format(clientSql, dto.getClientId());
        }
        String oderBy;
        String sort;
        if (dto.getOrder() == null || dto.getOrderBy() == null) {
            oderBy = TimeSheetTableOrderType.WEEK_END.getOrderSql() + TimeSheetTableSortType.DESC.getOrderSql();
            sort = " ";
        } else if (dto.getOrderBy() == TimeSheetTableOrderType.MANAGER) {
            oderBy = TimeSheetTableOrderType.MANAGER_EXPENSE_CLIENT.getOrderSql();
            sort = dto.getOrder().getOrderSql();
        } else {
            oderBy = dto.getOrderBy().getOrderSql();
            sort = dto.getOrder().getOrderSql();
        }
        String sql = """
                select AAA.* from (
                select TER.work_date as ending_date, TER.cost as amount, apr.currency,
                TER.full_name as talent_name,TER.talent_id as talent_id,TER.job_title job_title,TER.company_name as company_name ,TER.submitted_date as applied_date,
                TER.approved_date as approved_date,TER.record_id as id, TER.status as status,TER.start_date as start_date,TER.end_date as end_date,TER.job_id as job_id,
                TER.manager as manager,TER.primary_manager as primary_manager , TER.am_approver_id,TER.am_approver as am,TER.assignment_id  as assignment_id, TER.expense_type as expense_type,
                TER.week_start, TER.week_end, TER.week_ending_date, TER.expense_index
                from time_sheet_expense_week_ending_record TER
                LEFT JOIN assignment_pay_rate apr ON apr.assignment_id = TER.assignment_id and apr.content_type = 1 AND apr.pay_type = 3
                where TER.assignment_status = 1
                """ + conditionStr +
                " ) AAA " + oderBy + sort  + ", id desc " + " limit ?1,?2 ";
        String countSql = """
                select count(TER.id)
                from time_sheet_expense_week_ending_record TER
                LEFT JOIN assignment_pay_rate apr ON apr.assignment_id = TER.assignment_id and apr.content_type = 1 AND apr.pay_type = 3
                where TER.assignment_status = 1
                """ + conditionStr;
        try {
            Query query = entityManager.createNativeQuery(countSql);
            List<Object> totalItems = query.getResultList();
            vo.setTotalItems(((BigInteger)totalItems.get(0)).intValue());
            query = entityManager.createNativeQuery(sql, ExpenseListVO.class);
            query.setParameter(1,startItem);
            query.setParameter(2,endItem);
            List<ExpenseListVO> result = query.getResultList();
            if (CollUtil.isNotEmpty(result)) {
                List<Long> combinedIds = CommonAmIdUtil.getCombinedIds(result, null, ExpenseListVO::getAmApproverId);
                Map<Long, String> userIdToNameMap = CommonAmIdUtil.createUserIdToNameMap(combinedIds, userService);
                CommonAmIdUtil.mapNamesToObjects(result, null, ExpenseListVO::getAmApproverId, null, ExpenseListVO::setAm, userIdToNameMap);
                List<EnumCurrency> enumCurrencyList = enumCurrencyService.findAllEnumCurrency();
                Map<Integer, EnumCurrency> currencyMap = enumCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, a -> a));
                result = result.stream().peek(expenseListVO -> {
                    String mr = expenseListVO.getAm();
                    if (StrUtil.isBlank(mr)) {
                        mr = expenseListVO.getManager();
                    }
                    if (StrUtil.isBlank(mr)) {
                        mr = expenseListVO.getPrimaryManager();
                    }
                    expenseListVO.setManager(mr);
                    expenseListVO.setAppliedDateStr(fromInstantToDateString(expenseListVO.getAppliedDate(), dto.getTimezone()));
                    expenseListVO.setApprovedDateStr(fromInstantToDateString(expenseListVO.getApprovedDate(), dto.getTimezone()));
                    String moneyStr = currencyMap.get(expenseListVO.getCurrency()).getLabel1();
                    expenseListVO.setAmountFormat(moneyStr + new BigDecimal(String.valueOf(expenseListVO.getAmount() == null? 0F: expenseListVO.getAmount())).setScale(2));
                }).toList();
            }
            vo.setData(result);
        } catch (Exception e) {
            log.error("Exception occurred in expenseSummary query", e);
        }
        return vo;
    }

    @Override
    public void timeSheetSummaryDownload(SummaryQueryDTO dto, HttpServletResponse response) {
        if (CollUtil.isEmpty(dto.getRecordIds())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_APPROVE_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        dto.setPageNum(1);
        dto.setPageSize(20000);
        SummaryDataVO result = timeSheetSummary(dto);
        ExcelWriter excelWriter = null;
        List<TimeSheetSummaryVO> data = (List<TimeSheetSummaryVO>) result.getData();
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            addFileDownLoadHeader(response);
            response.setHeader("Content-Disposition", "attachment;filename=" + "TimeSheet" + fromInstantToDate(Instant.now(), dto.getTimezone()) + ".xls");
            excelWriter  = EasyExcelFactory.write(outputStream).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0,"timeSheet-summary").head(TimeSheetSummaryVO.class).needHead(true).build();
            excelWriter.write(data, writeSheet);
        } catch (Exception e) {
            log.error("error", e);
        } finally {
            if (excelWriter != null) {excelWriter.finish();}
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("error", e);
                }
            }
        }
    }

    @Override
    public void expenseSummaryDownload(SummaryQueryDTO dto, HttpServletResponse response) {
        if (CollUtil.isEmpty(dto.getRecordIds())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_APPROVE_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        dto.setPageNum(1);
        dto.setPageSize(20000);
        SummaryDataVO result = expenseSummary(dto);
        ExcelWriter excelWriter = null;
        List<ExpenseListVO> data = (List<ExpenseListVO>) result.getData();
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            addFileDownLoadHeader(response);
            response.setHeader("Content-Disposition", "attachment;filename=Expense" + fromInstantToDate(Instant.now(), dto.getTimezone()) +".xls");
            excelWriter = EasyExcelFactory.write(outputStream).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0,"expense-summary").head(ExpenseListVO.class).needHead(true).build();
            excelWriter.write(data, writeSheet);
        } catch (Exception e) {
            log.error("error", e);
        } finally {
            if (excelWriter != null) {excelWriter.finish();}
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("error", e);
                }
            }
        }
    }

    @Override
    public void timeSheetRecordDetailDownload(RecordSearchDTO dto, HttpServletResponse response, Boolean is24timeFlag) {
        Map.Entry<BreakTimeDTO, TimeSheetSummaryVO> map = timesheetDetailSearch(dto);
        addFileDownLoadHeader(response);
        downLoadExcelDetailService.downloadTimeSheetDetail(map.getKey(), map.getValue(),response, is24timeFlag);
    }

    @Override
    public void timeSheetRecordDetailDownloadPdf(RecordSearchDTO dto, HttpServletResponse response, Boolean is24timeFlag) {
        Map.Entry<BreakTimeDTO, TimeSheetSummaryVO> map = timesheetDetailSearch(dto);
        addFileDownLoadHeader(response);
        downloadPdfDetailService.downloadTimeSheetDetail(map.getKey(), map.getValue(),response, is24timeFlag);
    }

    public Map.Entry<BreakTimeDTO, TimeSheetSummaryVO> timesheetDetailSearch(RecordSearchDTO dto) {
        SummaryQueryDTO summaryQueryDTO =  new SummaryQueryDTO();
        Set<Long> records = new HashSet<>();
        records.add(dto.getId());
        summaryQueryDTO.setRecordIds(records);
        summaryQueryDTO.setTimezone(dto.getTimezone());
        SummaryDataVO result = timeSheetSummary(summaryQueryDTO);
        List<TimeSheetSummaryVO> data = (List<TimeSheetSummaryVO>) result.getData();
        BreakTimeDTO record = timeSheetRecordService.findRecordById(dto);
        TimeSheetSummaryVO vo = data.get(0);
        vo.setComments(record.getComments());
        return Map.entry(record, vo);
    }

    @Override
    public void timeSheetRecordDetailDownloadBatchPdf(DownloadPdfDto pdfDto, HttpServletResponse response, Boolean is24timeFlag) throws IOException {
        List<RecordSearchDTO> recordList = pdfDto.getRecordList();
        PdfDocument pdf = new PdfDocument(new PdfWriter(response.getOutputStream()));
        Document document = new Document(pdf);
        String timeZone = recordList.get(0).getTimezone();
        String fileName = "TimeSheet" + DateUtil.fromInstantToDate(Instant.now(), timeZone, DateUtil.YYYY_MM_DD) + ".pdf";
        addFileDownLoadHeader(response);
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        for (RecordSearchDTO dto : recordList) {
            Map.Entry<BreakTimeDTO, TimeSheetSummaryVO> map = timesheetDetailSearch(dto);
            downloadPdfDetailService.downloadTimeSheetDetail(map.getKey(), map.getValue(), document, is24timeFlag);
            if (recordList.indexOf(dto) != recordList.size()-1) {
                document.add(new AreaBreak());
            }
        }
        document.close();
        pdf.close();
    }

    @Override
    public void expenseRecordDetailDownloadBatchPdf(DownloadPdfDto pdfDto, HttpServletResponse response) throws IOException {
        List<RecordSearchDTO> recordList = pdfDto.getRecordList();
        PdfDocument pdf = new PdfDocument(new PdfWriter(response.getOutputStream()));
        Document document = new Document(pdf);
        String timeZone = recordList.get(0).getTimezone();
        String fileName = "Expense" + DateUtil.fromInstantToDate(Instant.now(), timeZone, DateUtil.YYYY_MM_DD) + ".pdf";
        addFileDownLoadHeader(response);
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        for (RecordSearchDTO dto : recordList) {
            Map.Entry<BreakTimeDTO, ExpenseListVO> map = expenseDetailSearch(dto);
            downloadPdfDetailService.downloadExpenseDetail(map.getKey(),map.getValue(), document);
            if (recordList.indexOf(dto) != recordList.size()-1) {
                document.add(new AreaBreak());
            }
        }
        document.close();
        pdf.close();
    }

    @Override
    public void expenseRecordDetailDownload(RecordSearchDTO dto, HttpServletResponse response) {
        Map.Entry<BreakTimeDTO, ExpenseListVO> map = expenseDetailSearch(dto);
        addFileDownLoadHeader(response);
        downLoadExcelDetailService.downloadExpenseDetail(dto.getReceipts(),map.getKey(),map.getValue(),response);
    }

    private Map.Entry<BreakTimeDTO, ExpenseListVO> expenseDetailSearch(RecordSearchDTO dto) {
        SummaryQueryDTO summaryQueryDTO =  new SummaryQueryDTO();
        Set<Long> records = new HashSet<>();
        records.add(dto.getId());
        summaryQueryDTO.setRecordIds(records);
        summaryQueryDTO.setTimezone(dto.getTimezone());
        SummaryDataVO result = expenseSummary(summaryQueryDTO);
        List<ExpenseListVO> data = (List<ExpenseListVO>) result.getData();
        BreakTimeDTO record = expenseService.findRecordById(dto);
        ExpenseListVO vo = data.get(0);
        vo.setComments(record.getComments());
        return Map.entry(record, vo);
    }

    @Override
    public void expenseRecordDetailDownloadPdf(RecordSearchDTO dto, HttpServletResponse response) {
        Map.Entry<BreakTimeDTO, ExpenseListVO> map = expenseDetailSearch(dto);
        addFileDownLoadHeader(response);
        downloadPdfDetailService.downloadExpenseDetail(dto.getReceipts(),map.getKey(),map.getValue(),response);
    }

    @Override
    public RecordDetailVO expenseRecordDetail(RecordSearchDTO dto) {
        SummaryQueryDTO summaryQueryDTO =  new SummaryQueryDTO();
        Set<Long> records = new HashSet<>();
        records.add(dto.getId());
        summaryQueryDTO.setRecordIds(records);
        SummaryDataVO sum = expenseSummary(summaryQueryDTO);
        List<ExpenseListVO> data = (List<ExpenseListVO>) sum.getData();
        ExpenseRecord expenseRecord = expenseRecordRepository.findById(dto.getId()).orElseThrow(() -> {throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.EXPENSE_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));});
        TimeSheetComments comments = commentsService.findByWorkDateAndType(expenseRecord.getWorkDate().toString(),CommentsType.EXPENSE,expenseRecord.getTalentId(),expenseRecord.getAssignmentId());
        ExpenseListVO vo = data.get(0);
        if (comments != null) {
            vo.setComments(comments.getComments());
        }
        RecordDetailVO result = new RecordDetailVO();
        result.setDetailInfo(vo);
        return result;
    }

    @Override
    public RecordDetailVO timesheetRecordDetail(RecordSearchDTO dto) {
        SummaryQueryDTO summaryQueryDTO =  new SummaryQueryDTO();
        Set<Long> records = new HashSet<>();
        records.add(dto.getId());
        summaryQueryDTO.setRecordIds(records);
        SummaryDataVO sum = timeSheetSummary(summaryQueryDTO);
        List<TimeSheetSummaryVO> data = (List<TimeSheetSummaryVO>) sum.getData();
        BreakTimeDTO record = timeSheetRecordService.findRecordById(dto);
        TimeSheetSummaryVO vo = data.get(0);
        vo.setComments(record.getComments());
        RecordDetailVO result = new RecordDetailVO();
        result.setRecords(record);
        result.setDetailInfo(vo);
        return result;
    }

    private void addFileDownLoadHeader(HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Headers","*");
//        response.setHeader("Access-Control-Allow-Origin","*");
        response.setHeader("Access-Control-Allow-Methods","*");
        response.setHeader("Access-Control-Expose-Headers","*");
    }

    private String toSelectStr(String field,List<String> list) {
        StringBuilder str = new StringBuilder();
        for(String s: list) {
            str.append(" or ").append(field).append("'%").append(s.toLowerCase()).append("%'");
        }
        str = new StringBuilder("( " + str.substring(3) + " )");
        return str.toString();
    }

    private String toSelectStatus(List<TimeSheetStatus> list) {
        StringBuilder str = new StringBuilder();
        for (TimeSheetStatus s: list) {
            str.append(",").append(s.toDbValue());
        }
        str = new StringBuilder("( " + str.substring(1) + " )");
        return str.toString();
    }

    private void updateExpenseStatus(Set<Long> recordIds, TimeSheetStatus status) {
        List<ExpenseRecord> expenseRecords = expenseRecord.findAllByIdIs(new ArrayList<>(recordIds));
        if (CollectionUtils.isEmpty(expenseRecords)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.EXPENSE_UPDATE_RECORD_ID_ILLEGAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        for (ExpenseRecord expenseRecord: expenseRecords) {
            this.expenseRecord.updateStatusByDatesAndIndex(TimeSheetUtil.getWeekByWeekEndingDate(expenseRecord.getWeekStart(), expenseRecord.getWeekEnd()), expenseRecord.getTalentId(), status.toDbValue(), expenseRecord.getAssignmentId(), expenseRecord.getExpenseIndex());
        }
    }

    private void sendEmailTimeSheet(TimeSheetStatus status, Set<Long> recordIds, List<ApproveRecord> list) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                boolean approvedFlag = status == TimeSheetStatus.APPROVED;
                List<TimeSheetRecord> timeSheetRecordList = SqlUtil.findByIdInByPartition1000(timeSheetRecordRepository::findAllByIdIn, new ArrayList<>(recordIds));
                List<Long> assignmentIdList = timeSheetRecordList.stream().map(TimeSheetRecord::getAssignmentId).distinct().toList();
                List<TalentAssigment> talentAssigmentList = SqlUtil.findByIdInByPartition1000(talentAssignmentRepository::findAllByIdIn, assignmentIdList);
                Map<Long, TalentAssigment> talentAssigmentMap = talentAssigmentList.stream().collect(Collectors.toMap(TalentAssigment::getId, a -> a, (a1, a2) -> a1));
                List<Long> talentIdList = timeSheetRecordList.stream().map(TimeSheetRecord::getTalentId).distinct().toList();
                List<TalentContact> talentContactList = CollUtil.split(talentIdList, PARTITION_COUNT_999).stream()
                        .map(talentIdSplit -> talentService.findAllByTalentIdInAndTypeAndStatus(talentIdSplit).getBody()).filter(Objects::nonNull).flatMap(List::stream).toList();
                Map<Long, TalentContact> contactMap = talentContactList.stream().collect(Collectors.toMap(TalentContact::getTalentId, a -> a, (a1, a2) -> a1.getSort() >= a2.getSort()? a2: a1));
                Map<Long, TimeSheetRecord> timeSheetRecordMap = timeSheetRecordList.stream().collect(Collectors.toMap(TimeSheetRecord::getId, a -> a, (a1, a2) -> a1));
                list.forEach(approveRecord -> {
                    TimeSheetRecord timeSheetRecord = timeSheetRecordMap.get(approveRecord.getRecordId());
                    if (timeSheetRecord == null) {
                        return;
                    }
                    TalentContact talentContact = contactMap.get(timeSheetRecord.getTalentId());
                    if (talentContact == null) {
                        return;
                    }
                    TalentAssigment talentAssigment = talentAssigmentMap.get(timeSheetRecord.getAssignmentId());
                    if (talentAssigment == null) {
                        return;
                    }
                    LocalDate startDate = talentAssigment.getStartDate();
                    LocalDate endDate = talentAssigment.getEndDate();
                    String jumpPath = applicationProperties.getJobDivaUrl() + TIMESHEET_RECEIVE_EMAIL_JUMP_PATH;
                    if (approvedFlag) {
                        sendEmailTimeSheetApprove(jumpPath, Collections.singletonList(talentContact.getContact()), startDate.toString(), endDate.toString(), approveRecord.getRecordId(), timeSheetRecord.getWeekEnd().toString());
                    } else {
                        sendEmailTimeSheetReject(jumpPath, Collections.singletonList(talentContact.getContact()), startDate.toString(), endDate.toString(), approveRecord.getRecordId(), approveRecord.getOpinion(), timeSheetRecord.getWeekEnd().toString());
                    }
                });
            }
        });
    }

    private void sendEmailExpense(TimeSheetStatus status, Set<Long> recordIds, List<ApproveRecord> list) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                boolean approvedFlag = status == TimeSheetStatus.APPROVED;
                List<ExpenseRecord> expenseRecordList = SqlUtil.findByIdInByPartition1000(expenseRecordRepository::findAllByIdIs, new ArrayList<>(recordIds));
                List<Long> talentIdList = expenseRecordList.stream().map(ExpenseRecord::getTalentId).distinct().collect(Collectors.toList());
                List<TalentContact> talentContactList = CollUtil.split(talentIdList, PARTITION_COUNT_999).stream()
                        .map(talentIdSplit -> talentService.findAllByTalentIdInAndTypeAndStatus(talentIdSplit).getBody()).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
                List<Long> assignmentIdList = expenseRecordList.stream().map(ExpenseRecord::getAssignmentId).distinct().collect(Collectors.toList());
                List<TalentAssigment> talentAssigmentList = SqlUtil.findByIdInByPartition1000(talentAssignmentRepository::findAllByIdIn, assignmentIdList);
                Map<Long, TalentAssigment> talentAssigmentMap = talentAssigmentList.stream().collect(Collectors.toMap(TalentAssigment::getId, a -> a, (a1, a2) -> a1));
                Map<Long, TalentContact> contactMap = talentContactList.stream().collect(Collectors.toMap(TalentContact::getTalentId, a -> a, (a1, a2) -> a1.getSort() >= a2.getSort()? a2: a1));
                Map<Long, ExpenseRecord> expenseRecordMap = expenseRecordList.stream().collect(Collectors.toMap(ExpenseRecord::getId, a -> a, (a1, a2) -> a1));
                list.forEach(approveRecord -> {
                    ExpenseRecord expenseRecord = expenseRecordMap.get(approveRecord.getRecordId());
                    if (expenseRecord == null) {
                        return;
                    }
                    TalentContact talentContact = contactMap.get(expenseRecord.getTalentId());
                    if (talentContact == null) {
                        return;
                    }
                    TalentAssigment talentAssigment = talentAssigmentMap.get(expenseRecord.getAssignmentId());
                    if (talentAssigment == null) {
                        return;
                    }
                    LocalDate startDate = talentAssigment.getStartDate();
                    LocalDate endDate = talentAssigment.getEndDate();
                    String jumpPath = applicationProperties.getJobDivaUrl() + EXPENSE_RECEIVE_EMAIL_JUMP_PATH;
                    if (approvedFlag) {
                        sendEmailExpenseApprove(jumpPath, Collections.singletonList(talentContact.getContact()), startDate.toString(), endDate.toString(), expenseRecord.getAssignmentId(), expenseRecord.getWeekStart().toString(), expenseRecord.getWeekEnd().toString(), expenseRecord.getWeekEndingDate(), expenseRecord.getId());
                    } else {
                        sendEmailExpenseReject(jumpPath, Collections.singletonList(talentContact.getContact()), startDate.toString(), endDate.toString(), expenseRecord.getAssignmentId(), approveRecord.getOpinion(), expenseRecord.getWeekStart().toString(), expenseRecord.getWeekEnd().toString(), expenseRecord.getWeekEndingDate(), expenseRecord.getId());
                    }
                });
            }
        });
    }

    private void sendEmailTimeSheetApprove(String jumpPath, List<String> emails, String startDate, String endDate, Long recordId, String weekEnd) {
        String subject = "Timesheet Approval Notification";
        String url = jumpPath +"?startDate=" + startDate + "&endDate=" + endDate + "&recordId=" + recordId;
        StringBuilder sb = new StringBuilder();
        sb.append("<body>");
        HtmlUtil.appendParagraphCell(sb, "Your Timesheet for week ending " + weekEnd + " has been approved.");
        HtmlUtil.appendParagraphCell(sb, "Please click <a href=\"" + url + "\">here</a> to view this entry.");
        sb.append("</body>");
        mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, sb.toString(), null, true));
    }

    private void sendEmailTimeSheetReject(String jumpPath, List<String> emails, String startDate, String endDate, Long recordId, String opinion, String weekEnd) {
        String subject = "Timesheet Rejection Notification";
        String url = jumpPath +"?startDate=" + startDate + "&endDate=" + endDate + "&recordId=" + recordId;
        StringBuilder sb = new StringBuilder();
        opinion = StrUtil.isBlank(opinion)? "": opinion;
        sb.append("<body>");
        HtmlUtil.appendParagraphCell(sb, "Your Timesheet for week ending " + weekEnd + " has been rejected.");
        HtmlUtil.appendParagraphCell(sb, "Rejection Comments: " + opinion);
        HtmlUtil.appendParagraphCell(sb, "Please click <a href=\"" + url + "\">here</a> to view this entry.");
        sb.append("</body>");
        mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, sb.toString(), null, true));
    }

    private void sendEmailExpenseApprove(String jumpPath, List<String> emails, String startDate, String endDate, Long assignmentId, String weekStart, String weekEnd, LocalDate weekEndingDate, Long recordId) {
        String subject = "Expenses Approval Notification";
        String url = jumpPath +"?startDate=" + startDate + "&endDate=" + endDate + "&assignmentId=" + assignmentId + "&weekEndingDate=" + weekEndingDate + "&weekStart=" + weekStart + "&weekEnd=" + weekEnd + "&recordId=" + recordId;
        StringBuilder sb = new StringBuilder();
        sb.append("<body>");
        HtmlUtil.appendParagraphCell(sb, "Your expense for week ending " + weekEnd + " has been approved.");
        HtmlUtil.appendParagraphCell(sb, "Please click <a href=\"" + url + "\">here</a> to view this entry.");
        sb.append("</body>");
        mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, sb.toString(), null, true));
    }

    private void sendEmailExpenseReject(String jumpPath, List<String> emails, String startDate, String endDate, Long assignmentId, String opinion, String weekStart, String weekEnd, LocalDate weekEndingDate, Long recordId) {
        String subject = "Expenses Rejection Notification";
        String url = jumpPath +"?startDate=" + startDate + "&endDate=" + endDate + "&assignmentId=" + assignmentId + "&weekEndingDate=" + weekEndingDate + "&weekStart=" + weekStart + "&weekEnd=" + weekEnd + "&recordId=" + recordId;
        StringBuilder sb = new StringBuilder();
        opinion = StrUtil.isBlank(opinion)? "": opinion;
        sb.append("<body>");
        HtmlUtil.appendParagraphCell(sb, "Your expense for week ending " + weekEnd + " has been rejected.");
        HtmlUtil.appendParagraphCell(sb, "Rejection Comments: " + opinion);
        HtmlUtil.appendParagraphCell(sb, "Please click <a href=\"" + url + "\">here</a> to view this entry.");
        sb.append("</body>");
        mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, sb.toString(), null, true));
    }

    private void updateTimeSheetStatus(Set<Long> recordIds, TimeSheetStatus status) {
        List<TimeSheetRecord> timeSheetRecords = timeSheet.findAllByIdIs(recordIds);
        if(CollectionUtils.isEmpty(timeSheetRecords)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.EXPENSE_UPDATE_RECORD_ID_ILLEGAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        for(TimeSheetRecord timeSheetRecord: timeSheetRecords) {
            Set<LocalDate> dates = TimeSheetUtil.getWeekByWeekEndingDate(timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEnd());
            timeSheet.updateStatusByDates(dates,timeSheetRecord.getTalentId(),status.toDbValue(),TimeSheetStatus.NO_RECORD.toDbValue(),timeSheetRecord.getAssignmentId());
            //update timeSheet breakTime timeSheetRecord data
            breakTimeRepository.updateStatusByDates(dates,timeSheetRecord.getTalentId(),status.toDbValue(),TimeSheetStatus.NO_RECORD.toDbValue(),timeSheetRecord.getAssignmentId());
        }
    }

    /**
     * 用于group invoice 生成timesheet数据
     * @param dto
     * @param document
     */
    @Override
    public void timesheetDetailSearchAndBatchPdf(RecordSearchDTO dto, com.itextpdf.text.Document document) {
        Map.Entry<BreakTimeDTO, TimeSheetSummaryVO> map = timesheetDetailSearch(dto);
        downloadPdfDetailService.downloadTimesheetDetailByInvoice(map.getKey(), map.getValue(), document);
    }

}
