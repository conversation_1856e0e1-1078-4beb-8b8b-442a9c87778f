package com.altomni.apn.jobdiva.service.invoice.impl;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.jobdiva.domain.invoice.TEmailAttachmentRecord;
import com.altomni.apn.jobdiva.domain.invoice.TInvoiceSendEmailRecord;
import com.altomni.apn.jobdiva.repository.invoice.EmailAttachmentRecordRepository;
import com.altomni.apn.jobdiva.repository.invoice.InvoiceSendEmailRecordRepository;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorGroupInvoiceSendEmailDTO;
import com.altomni.apn.jobdiva.service.invoice.GroupInvoiceSendEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service("groupInvoiceSendEmailService")
public class GroupInvoiceSendEmailServiceImpl implements GroupInvoiceSendEmailService {

    @Value("${application.emailService.supportSender:<EMAIL>}")
    private String supportSender;

    @Resource
    EmailAttachmentRecordRepository emailAttachmentRecordRepository;

    @Resource
    InvoiceSendEmailRecordRepository invoiceSendEmailRecordRepository;

    @Async
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveEmailRecord(ContractorGroupInvoiceSendEmailDTO dto, List<String> md5Links) {
        //insert record to email record table
        List<TInvoiceSendEmailRecord> tInvoiceSendEmailRecords = new ArrayList<>();
        for (BigInteger id : dto.getInvoiceIdList()) {
            TInvoiceSendEmailRecord tInvoiceSendEmailRecord = new TInvoiceSendEmailRecord();
            tInvoiceSendEmailRecord.setSendContent(dto.getContent());
            tInvoiceSendEmailRecord.setSendBy(supportSender);
            tInvoiceSendEmailRecord.setSendStatus(1);
            tInvoiceSendEmailRecord.setSendDate(new Timestamp(new Date().getTime()));
            tInvoiceSendEmailRecord.setCarbonCopy(JSON.toJSONString(dto.getCcList()));
            tInvoiceSendEmailRecord.setSubject(dto.getSubject());
            tInvoiceSendEmailRecord.setSendTo(JSON.toJSONString(dto.getSendToList()));
            tInvoiceSendEmailRecord.setGroupInvoiceId(id);
            tInvoiceSendEmailRecords.add(tInvoiceSendEmailRecord);
        }

        List<TEmailAttachmentRecord> tEmailAttachmentRecords = new ArrayList<>();
        if (!tInvoiceSendEmailRecords.isEmpty()) {
            invoiceSendEmailRecordRepository.saveAll(tInvoiceSendEmailRecords);
            log.info("group invoice: save send mail record info");

            //组装附件记录信息
            for (TInvoiceSendEmailRecord record : tInvoiceSendEmailRecords) {
                for(String md5:md5Links){
                    TEmailAttachmentRecord bean = new TEmailAttachmentRecord();
                    bean.setAttachmentUrl(md5);
                    bean.setEmailId(record.getId());
                    tEmailAttachmentRecords.add(bean);
                }
            }

            if(!tEmailAttachmentRecords.isEmpty()){
                emailAttachmentRecordRepository.saveAll(tEmailAttachmentRecords);
                log.info("group invoice: save mail attachment record info");
            }
        }
    }
}
