package com.altomni.apn.jobdiva.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SpringUtil;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentBillInfo;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentTimeSheet;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.repository.assignment.BillInfoRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRecordRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRepository;
import com.altomni.apn.jobdiva.service.dto.invoice.CheckInvoiceDTO;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorInvoiceExistDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.TimeSheetRecordDTO;
import com.altomni.apn.jobdiva.service.invoice.ContractorInvoiceService;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO;
import com.altomni.apn.jobdiva.service.vo.assignment.WeekDataVO;
import com.altomni.apn.jobdiva.service.vo.invoice.ContractorInvoiceCheckInvoiceVO;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import javax.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Month;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.altomni.apn.common.utils.SqlUtil.PARTITION_COUNT_999;
import static com.altomni.apn.company.constants.Constants.SYSTEM_ACCOUNT;


/**
 * set timeSheet util
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class TimeSheetUtil {


    private TalentAssignmentRepository getAssignmentRepository() {
        return SpringUtil.getBean(TalentAssignmentRepository.class);
    }

    private BillInfoRepository getBillInfoRepository() {
        return SpringUtil.getBean(BillInfoRepository.class);
    }

    private ContractorInvoiceService getContractorInvoiceService() {
        return SpringUtil.getBean(ContractorInvoiceService.class);
    }

    private TimeSheetRepository getTimeSheetRepository() {
        return SpringUtil.getBean(TimeSheetRepository.class);
    }

    private TimeSheetRecordRepository getTimeSheetRecordRepository() {
        return SpringUtil.getBean(TimeSheetRecordRepository.class);
    }

    private EntityManager getEntityManager() {
        return SpringUtil.getBean(EntityManager.class);
    }

    public boolean checkTimeTypeAndOverTimeType(Long assignmentId, TimeSheetType timeSheetType, OverTimeType overTimeType) {
        return checkTimeSheetTypeChange(assignmentId, timeSheetType) && checkTimeSheetBillInfoChange(assignmentId, overTimeType);
    }

    public boolean checkTimeSheetTypeChange(Long assignmentId, TimeSheetType timeSheetType) {
        AssignmentTimeSheet assignmentTimeSheet = getTimeSheetRepository().findByAssignmentId(assignmentId);
        return timeSheetType == assignmentTimeSheet.getTimeSheetType();
    }

    public boolean checkTimeSheetBillInfoChange(Long assignmentId, OverTimeType overTimeType) {
        AssignmentBillInfo assignmentBillInfo = getBillInfoRepository().findByAssignmentId(assignmentId);
        return overTimeType == assignmentBillInfo.getOvertimeType();
    }

    public void checkGeneratedInvoice(List<TimeSheetRecord> timeSheetRecordList) {
        List<ContractorInvoiceCheckInvoiceVO> contractorInvoiceCheckInvoiceVOList = getIdByAssignmentIdAndWeekEndingDate(timeSheetRecordList);
        if (contractorInvoiceCheckInvoiceVOList.stream().anyMatch(vo -> BooleanUtil.isTrue(vo.isExist()))) {
            throw new CustomParameterizedException("Existence of generated invoice records.");
        }
    }

    public List<ContractorInvoiceCheckInvoiceVO> getIdByAssignmentIdAndWeekEndingDate(List<TimeSheetRecord> timeSheetRecordList) {
        ContractorInvoiceExistDTO contractorInvoiceExistDTO = new ContractorInvoiceExistDTO();
        List<CheckInvoiceDTO> checkInvoiceList = new ArrayList<>();
        timeSheetRecordList.forEach(timeSheetRecord -> {
            CheckInvoiceDTO checkInvoiceDTO = new CheckInvoiceDTO();
            checkInvoiceDTO.setAssignmentId(timeSheetRecord.getAssignmentId());
            checkInvoiceDTO.setWeekEndingDate(timeSheetRecord.getWeekEnd());
            checkInvoiceList.add(checkInvoiceDTO);
        });
        contractorInvoiceExistDTO.setCheckInvoiceList(checkInvoiceList);
        return getContractorInvoiceService().getIdByAssignmentIdAndWeekEndingDate(contractorInvoiceExistDTO);
    }

    public Optional<String> getInvoiceNumberByExpenseRecord(List<ExpenseRecord> expenseRecords) {
        ContractorInvoiceExistDTO contractorInvoiceExistDTO = new ContractorInvoiceExistDTO();
        List<CheckInvoiceDTO> checkInvoiceList = new ArrayList<>();
        Optional<Integer> expenseIndex = expenseRecords.stream().map(ExpenseRecord::getExpenseIndex).distinct().findFirst();
        expenseRecords.forEach(timeSheetRecord -> {
            CheckInvoiceDTO checkInvoiceDTO = new CheckInvoiceDTO();
            checkInvoiceDTO.setAssignmentId(timeSheetRecord.getAssignmentId());
            checkInvoiceDTO.setWeekEndingDate(timeSheetRecord.getWeekEnd());
            expenseIndex.ifPresent(checkInvoiceDTO::setRecordIndex);
            checkInvoiceList.add(checkInvoiceDTO);
        });
        contractorInvoiceExistDTO.setCheckInvoiceList(checkInvoiceList);
        contractorInvoiceExistDTO.setInvoiceType(InvoiceType.EXPENSE.toDbValue());
        List<ContractorInvoiceCheckInvoiceVO> invoiceCheckVos = getContractorInvoiceService().getIdByAssignmentIdAndWeekEndingDate(contractorInvoiceExistDTO);
        return invoiceCheckVos.stream().filter(ContractorInvoiceCheckInvoiceVO::isExist).map(ContractorInvoiceCheckInvoiceVO::getInvoiceNumber)
                .distinct().findFirst();
    }

    public boolean checkGeneratedInvoiceWithResult(List<TimeSheetRecord> timeSheetRecordList) {
        List<ContractorInvoiceCheckInvoiceVO> contractorInvoiceCheckInvoiceVOList = getIdByAssignmentIdAndWeekEndingDate(timeSheetRecordList);
        if (contractorInvoiceCheckInvoiceVOList.stream().anyMatch(vo -> BooleanUtil.isTrue(vo.isExist()))) {
            return true;
        }
        return false;
    }

    /**
     * 创建assignment 的record记录
     * @param userType
     * @param talentId
     * @param tenantId
     * @param assignmentId
     * @param frequencyType
     */
    public void setTimeSheetStatus(TimeSheetUserType userType, Long talentId, Long tenantId, Long assignmentId, TimeSheetFrequencyType frequencyType) {
        if (userType != TimeSheetUserType.TALENT) {
            return;
        }
        AssignmentVO assignment = getAssignmentRepository().findAssigmentInfoByAssignmentIdAndStatus(assignmentId);
        if (ObjectUtil.isNull(assignment)) {
            return;
        }
        //获取所有的localDates, 所有的数据是 workDate = weekEnd 的数据, weekEndingDate 是用来计算otdt 的数据
        //获取完整的有序的timesheet week列表
        List<WeekDataVO> weekDataVOList = getWeekEnding(assignment.getStartDate(), assignment.getEndDate(), assignment.getWeekEnding(), assignment.getIsWeekEnd(), frequencyType);
        //只需要weekend 的数据来创建初始化数据
        weekDataVOList = weekDataVOList.stream().filter(weekDataVO -> Objects.equals(weekDataVO.getWorkDate(), weekDataVO.getWeekEnd())).toList();
        //获取到所有已经存在的打卡记录,用于修改数据,已经生成缺失的打卡记录
        getEntityManager().clear();
        List<TimeSheetRecord> records = getTimeSheetRecordRepository().findAllByAssignmentIdAndWeekEndEqWorkDate(assignment.getId());
        if (CollUtil.isEmpty(records)) {
            createOrUpdateTimesheetRecord(assignment, weekDataVOList, talentId, tenantId);
        } else {
            List<LocalDate> recordDates = records.stream().map(TimeSheetRecord::getWeekEnd).toList();
            List<WeekDataVO> missList = new LinkedList<>();
            for (WeekDataVO weekDataVO : weekDataVOList) {
                if (recordDates.contains(weekDataVO.getWeekEnd())) {
                    continue;
                }
                missList.add(weekDataVO);
            }
            createOrUpdateTimesheetRecord(assignment, missList, talentId, tenantId);
        }
    }

    /**
     * 获取合同区间内所有的工作日数据
     * @param startDate   合同开始时间
     * @param endDate     合同结束时间
     * @param weekEnding  合同以一周的哪一天作为结束时间
     * @param isWeekEnd   是否需要切割0:不需要;1:不需要
     * @param type        切割数据的类型
     * @return
     */
    public List<WeekDataVO> getWeekEnding(LocalDate startDate, LocalDate endDate, Integer weekEnding, Integer isWeekEnd, TimeSheetFrequencyType type) {
        if (Objects.equals(isWeekEnd, 0)) {
            type = TimeSheetFrequencyType.WEEKLY;
        }
        LinkedList<WeekDataVO> dates = new LinkedList<>();
        int i = 0;
        LocalDate workDate;
        LocalDate weekStart = null;
        LocalDate weekEnd = null;
        LocalDate weekendingDate = null;
        WeekDataVO weekDataEntity = new WeekDataVO();
        LocalDate breakDate = endDate.plus(1, ChronoUnit.DAYS);
        // 获取当前日期和dayOfWeek
        while ((workDate = startDate.plus(i++, ChronoUnit.DAYS)).isBefore(breakDate)) {
            switch (type) {
                case SEMI_MONTHLY -> {
                    // 1-15, 16-本月最后一天
                    //1. 获取weekStart, 先计算获取正常的开始时间
                    weekStart = findDateInWeek(workDate, weekEnding, 2);
                    weekendingDate = findDateInWeek(workDate, weekEnding, 1);
                    weekEnd = weekendingDate;
                    // 是需要切割的时间 1号
                    if (weekStart.isBefore(workDate.withDayOfMonth(1))) {
                        weekStart = workDate.withDayOfMonth(1);
                    }
                    // 15号
                    if (workDate.isAfter(workDate.withDayOfMonth(15))
                            && weekStart.isBefore(workDate.withDayOfMonth(16))) {
                        weekStart = workDate.withDayOfMonth(16);
                    }
                    // 开始时间不能小于assignment 的开始时间
                    if (weekStart.isBefore(startDate)) {
                        weekStart = startDate;
                    }
                    //2. 获取weekend
                    if (weekStart.isBefore(workDate.withDayOfMonth(16)) && weekendingDate.isAfter(workDate.withDayOfMonth(15))) {
                        weekEnd = workDate.withDayOfMonth(15);
                    }
                    if (weekendingDate.isAfter(workDate.withDayOfMonth(workDate.lengthOfMonth()))) {
                        weekEnd = workDate.withDayOfMonth(workDate.lengthOfMonth());
                    }
                }
                case MONTHLY -> {
                    // 1号和 31号切割
                    //1. 获取weekStart, 先计算获取正常的开始时间
                    weekStart = findDateInWeek(workDate, weekEnding, 2);
                    weekendingDate = findDateInWeek(workDate, weekEnding, 1);
                    weekEnd = weekendingDate;
                    // 是需要切割的时间 1号
                    if (weekStart.isBefore(workDate.withDayOfMonth(1))) {
                        weekStart = workDate.withDayOfMonth(1);
                    }
                    // 开始时间不能小于assignment 的开始时间
                    if (weekStart.isBefore(startDate)) {
                        weekStart = startDate;
                    }
                    //2. 获取weekend
                    if (weekendingDate.isAfter(workDate.withDayOfMonth(workDate.lengthOfMonth()))) {
                        weekEnd = workDate.withDayOfMonth(workDate.lengthOfMonth());
                    }
                }
                case QUARTERLY -> {
                    //1.1-3.31  4.1-6.30  7.1-9.30 - 10.1-12.31
                    List<Month> monthEndList = CollUtil.newArrayList(Month.MARCH, Month.JUNE, Month.SEPTEMBER, Month.DECEMBER);
                    List<Month> monthStartList = CollUtil.newArrayList(Month.JANUARY, Month.APRIL, Month.JULY, Month.OCTOBER);
                    boolean isQuarterlyFlag = (monthEndList.contains(workDate.getMonth()) && workDate.getDayOfMonth() == workDate.lengthOfMonth());
                    //1. 获取weekStart, 先计算获取正常的开始时间
                    weekStart = findDateInWeek(workDate, weekEnding, 2);
                    weekendingDate = findDateInWeek(workDate, weekEnding, 1);
                    weekEnd = weekendingDate;
                    // 是需要切割的时间 1号
                    if (monthStartList.contains(workDate.getMonth()) && weekStart.isBefore(workDate.withDayOfMonth(1))) {
                        weekStart = workDate.withDayOfMonth(1);
                    }
                    // 开始时间不能小于assignment 的开始时间
                    if (weekStart.isBefore(startDate)) {
                        weekStart = startDate;
                    }
                    //2. 获取weekend
                    if (isQuarterlyFlag && weekendingDate.isAfter(workDate.withDayOfMonth(workDate.lengthOfMonth()))) {
                        weekEnd = workDate.withDayOfMonth(workDate.lengthOfMonth());
                    }
                }
                default -> {
                    //WEEKLY,BI_WEEKLY
                    weekStart = findDateInWeek(workDate, weekEnding, 2);
                    if (weekStart.isBefore(startDate)) {
                        weekStart = startDate;
                    }
                    weekendingDate = findDateInWeek(workDate, weekEnding, 1);
                    weekEnd = weekendingDate;
                }
            }
            weekDataEntity.setWeekStart(weekStart);
            weekDataEntity.setWeekEnd(weekEnd);
            weekDataEntity.setWeekendingDate(weekendingDate);
            weekDataEntity.setWorkDate(workDate);
            dates.addLast(weekDataEntity);
            weekDataEntity = new WeekDataVO();
        }
        log.info("workDate = " + workDate + ", weekEnd = " + weekEnd);
        //走完了合同范围内所有数据,但是到了最后一天不是weekEnde,需要补充数据
        log.info("finish weekending fill workDate = " + workDate + ", weekEnd = " + weekEnd + ", endDate = " + endDate);
        if (weekEnd != null && !workDate.minusDays(1).isEqual(weekEnd)) {
            weekDataEntity = new WeekDataVO();
            weekDataEntity.setWeekStart(weekStart);
            weekDataEntity.setWeekEnd(weekEnd);
            weekDataEntity.setWeekendingDate(weekendingDate);
            weekDataEntity.setWorkDate(weekEnd);
            dates.addLast(weekDataEntity);
            log.info("week Data = {}", JSONUtil.toJsonStr(weekDataEntity));
        }
        dates.sort(Comparator.comparing(WeekDataVO::getWorkDate));
        return dates;
    }

    private void createOrUpdateTimesheetRecord(AssignmentVO assigment, List<WeekDataVO> weekDataVOList, Long talentId, Long tenantId) {
        List<TimeSheetRecord> list = new LinkedList<>();
        for (WeekDataVO weekDataVO : weekDataVOList) {
            TimeSheetRecord timeSheetRecord = new TimeSheetRecord();
            timeSheetRecord.setTalentId(talentId);
            timeSheetRecord.setTenantId(tenantId);
            timeSheetRecord.setAssignmentId(assigment.getId());
            timeSheetRecord.setTimeSheetType(assigment.getSheetType());
            if (weekDataVO.getWorkDate().isAfter(LocalDate.now())) {
                timeSheetRecord.setStatus(TimeSheetStatus.NO_RECORD);
            } else {
                timeSheetRecord.setStatus(TimeSheetStatus.MISSING);
            }
            timeSheetRecord.setWorkDate(weekDataVO.getWorkDate());
            timeSheetRecord.setWeekEndingDate(weekDataVO.getWeekendingDate());
            timeSheetRecord.setWeekStart(weekDataVO.getWeekStart());
            timeSheetRecord.setWeekEnd(weekDataVO.getWeekEnd());
            list.add(timeSheetRecord);
        }
        getTimeSheetRecordRepository().saveAll(list);
        setWeekId(list);
        if (CollUtil.isNotEmpty(list)) {
            List<Long> idList = list.stream().map(TimeSheetRecord::getId).distinct().toList();
            CollUtil.split(idList, PARTITION_COUNT_999).forEach(splitList -> getTimeSheetRecordRepository().updateCreateByAndLastModifiedByById(splitList, SYSTEM_ACCOUNT));
        }
    }

    /**
     * type is not exists, return week end day
     *
     * @param startDate
     * @param setWeekEnd 设置哪一天为一周的结束 1-7
     * @param type  0.获取上周的最后一天， 1.获取一周的结束一天  2.获取一周的开始第一天
     * @return
     */
    public LocalDate findDateInWeek(LocalDate startDate, Integer setWeekEnd, int type) {
        int week = startDate.getDayOfWeek().getValue();
        int diff = setWeekEnd - week;
        LocalDate weekEndDate = startDate.plus(diff, ChronoUnit.DAYS);
        if (type == 0) {
            if (diff >= 0) {
                return weekEndDate.minusDays(7);
            } else {
                return weekEndDate;
            }
        } else if (type == 1) {
            if (diff >= 0) {
                return weekEndDate;
            } else {
                return weekEndDate.plusDays(7);
            }
        } else if (type == 2) {
            if (diff >= 0) {
                return weekEndDate.minusDays(6);
            } else {
                return weekEndDate.plusDays(1);
            }
        }
        return weekEndDate;
    }

    public Set<LocalDate> getWeekByWeekEndingDate(LocalDate weekStart, LocalDate weekEnd) {
        Set<LocalDate> dateSet = new HashSet<>();
        dateSet.add(weekEnd);
        int i = 0;
        LocalDate currentDate;
        while (!(currentDate = weekEnd.minusDays(i++)).isBefore(weekStart)) {
            dateSet.add(currentDate);
        }
        return dateSet;
    }

    public Set<LocalDate> getWeekByWeekEndingDate(String weekEndingDate) {
        Set<LocalDate> dateSet = new HashSet<>();
        LocalDate endDate = LocalDate.parse(weekEndingDate);
        dateSet.add(endDate);
        int count = 6;
        for (int i = 1; i <= count; i++) {
            dateSet.add(endDate.minusDays(i));
        }
        return dateSet.stream().sorted().collect(Collectors.toCollection(LinkedHashSet::new));
    }

    public void setWeekId(List<TimeSheetRecord> timeSheetRecordList) {
        timeSheetRecordList.stream().filter(timeSheetRecord -> Objects.equals(timeSheetRecord.getWeekEnd(), timeSheetRecord.getWorkDate()))
                .forEach(weekEnd -> getTimeSheetRecordRepository().updateWeekId(weekEnd.getId(), weekEnd.getAssignmentId(), weekEnd.getWeekEnd()));
    }

    public void updateOtDt(TimeSheetStatus status, Long assignmentId, TimeSheetType timeSheetType, LocalDate weekEndingDate) {
        if (status != TimeSheetStatus.APPROVED) {
            getEntityManager().clear();
            AssignmentBillInfo assignmentBillInfo = getBillInfoRepository().findByAssignmentId(assignmentId);
            if (!assignmentBillInfo.getIsExcept() && assignmentBillInfo.getOvertimeType() == OverTimeType.AUTO) {
                AssignmentTimeSheet assignmentTimeSheet = getTimeSheetRepository().findByAssignmentId(assignmentId);
                //查询出整周的记录
                List<TimeSheetRecord> timeSheetRecordList = getTimeSheetRecordRepository().findAllByWorkDateInAndAssignmentId(new ArrayList<>(getWeekByWeekEndingDate(weekEndingDate.toString())), assignmentId);
                OtDtUtil.calculateOtDtByLocationAndTimeSheetType(timeSheetRecordList, timeSheetType, assignmentTimeSheet.getCalculateType());
                getTimeSheetRecordRepository().saveAll(timeSheetRecordList);
                timeSheetRecordList.stream().filter(timeSheetRecord1 -> timeSheetRecord1.getWorkDate().isEqual(timeSheetRecord1.getWeekEnd())).forEach(weekendRecord ->
                        getTimeSheetRecordRepository().updateLastModifiedDateById(CollUtil.newArrayList(weekendRecord.getId()), Instant.now()));
            }
        }
    }

    public String getBreakTimeByType(Boolean is24TimeFlag, String time) {
        if (is24TimeFlag) {
            return time;
        }
        // 处理将24小时制转换为12小时制
        if (StrUtil.isBlank(time) || !time.contains(":")) {
            return time;
        }
        String[] timeParts = time.split(":");
        if (timeParts.length != 2 || !NumberUtil.isNumber(timeParts[0]) || !NumberUtil.isNumber(timeParts[1])) {
            return time;
        }
        int hours = Integer.parseInt(timeParts[0]);
        String minutes = timeParts[1];
        String period = hours >= 12 ? "PM" : "AM";
        // 将24小时制转换为12小时制
        hours = hours % 12;
        if (hours == 0) {
            hours = 12;
        }
        return String.format("%02d:%s %s", hours, minutes, period);
    }

    public TimeSheetRecordDTO copyTimeSheetRecordDTO(TimeSheetRecordDTO timeSheetRecordDTO) {
        TimeSheetRecordDTO dto = new TimeSheetRecordDTO();
        BeanUtils.copyProperties(timeSheetRecordDTO, dto, "id", "workHours", "regularHours", "overTime", "doubleTime", "totalHours", "timeIn", "timeOut");
        if (CollUtil.isNotEmpty(timeSheetRecordDTO.getBreakTime())) {
            dto.setBreakTime(ObjectUtil.cloneByStream(timeSheetRecordDTO.getBreakTime()));
            dto.getBreakTime().forEach(time -> {
                time.setBreakIn(null);
                time.setBreakOut(null);
            });
        }
        return dto;
    }

}
