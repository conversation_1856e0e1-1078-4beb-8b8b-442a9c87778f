package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetBreakTimeRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface TimeSheetBreakTimeRepository extends JpaRepository<TimeSheetBreakTimeRecord, Long> {


    @Query(value = " select * from timesheet_breaktime_record  tsr where talent_id = ?3 and work_date between ?1 and ?2 and assignment_id = ?4 ", nativeQuery = true)
    List<TimeSheetBreakTimeRecord> findAllByDate(LocalDate startDate, LocalDate endDate, Long talentId, Long assignment);

    List<TimeSheetBreakTimeRecord> findAllByAssignmentId(Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_breaktime_record  where work_date between ?1 and ?2 and talent_id = ?3 and assignment_id = ?4 ", nativeQuery = true)
    void deleteByDate(LocalDate startDate, LocalDate endDate, Long talentId, Long id);

    void deleteAllByStatusInAndTalentIdAndAssignmentId(List<TimeSheetStatus> statusList, Long talentId, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_breaktime_record where work_date in ?1  and talent_id = ?2 and assignment_id = ?3 ", nativeQuery = true)
    void deleteAllByWorkDateInAndTalentIdAndAssignmentId(Set<LocalDate> dates,Long talentId,Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " update timesheet_breaktime_record set time = null where work_date between ?1 and ?2 and talent_id = ?3 and assignment_id = ?4 ", nativeQuery = true)
    void resetData(LocalDate plusDays, LocalDate starDate, Long talentId, Long id);

    @Modifying
    @Transactional
    @Query(value = " update timesheet_breaktime_record set status = ?3  where work_date in ?1 and talent_id =?2 and status != ?4 and assignment_id = ?5 ", nativeQuery = true)
    void updateStatusByDates(Set<LocalDate> value, Long talentId, int status, int noRecord, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " update timesheet_breaktime_record set status = ?3  where work_date in ?1 and talent_id =?2 and assignment_id = ?4 ", nativeQuery = true)
    void updateStatusByDates(Set<LocalDate> value, Long talentId, int status, Long assignmentId);

    @Modifying
    @Transactional
    @Query(value = " delete from timesheet_breaktime_record where assignment_id = ?1 ", nativeQuery = true)
    void deleteByAssignmentId(Long assignmentId);

    List<TimeSheetBreakTimeRecord> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
