package com.altomni.apn.jobdiva.config.idgenerator;

import com.altomni.apn.common.service.cache.CommonRedisService;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;

/**
 * 雪花算法
 */
@Slf4j
@Component
public class RedisId implements ApplicationContextAware {

  @Resource
  private CommonRedisService redisClient;

  @Resource
  private EntityManager entityManager;

  private static ApplicationContext applicationContext;

  @Override
  public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
    RedisId.applicationContext = applicationContext;
  }

  public static RedisId getBean() {
    return applicationContext.getBean(RedisId.class);
  }

  public Long getMaxId(String table, String redisKey) {
    if (redisClient.exists(redisKey)) {
      return redisClient.incr(redisKey);
    } else {
      String sql = " select max(id) from " + table;
      Query query = entityManager.createNativeQuery(sql);
      Long maxId = Long.parseLong(String.valueOf(query.getSingleResult()));
      redisClient.set(redisKey, maxId + "");
      return redisClient.incr(redisKey);
    }
  }


}

