package com.altomni.apn.jobdiva.service.vo.assignment;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AssignmentCancelEliminateInfoVO implements Serializable {

    private static final long serialVersionUID = 6244691076239820949L;

    List<AssignmentEliminateVO> eliminateInfoVOList;

    List<AssignmentActiveVO> activeDateList;
}
