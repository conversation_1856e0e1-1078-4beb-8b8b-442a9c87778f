package com.altomni.apn.jobdiva.web.rest.timesheet;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.jobdiva.SearchConfigType;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetSearchConfig;
import com.altomni.apn.jobdiva.service.dto.timesheet.SearchFilterDTO;
import com.altomni.apn.jobdiva.service.timesheet.SearchConfigService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Api(tags = {"AdvanceSearchFilterConfigResource"})
@RestController
@RequestMapping("/api/v3/filter")
public class AdvanceSearchFilterConfigResource {

    @Resource
    private SearchConfigService configService;

    @PostMapping("/saveFilter")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TimeSheetSearchConfig> saveFilter(@RequestBody SearchFilterDTO dto) {
        log.info("[timesheet: User @{}] REST saveFilter", SecurityUtils.getUserId());
        return ResponseEntity.ok(configService.saveFilter(dto));
    }

    @PostMapping("/saveTableHeader")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TimeSheetSearchConfig> saveTableHeader(@RequestBody SearchFilterDTO dto) {
        log.info("[timesheet: User @{}] REST saveTableHeader", SecurityUtils.getUserId());
        return ResponseEntity.ok(configService.saveTableHeader(dto));
    }

    @GetMapping("/searchFilter")
    @Timed
    public ResponseEntity<List<TimeSheetSearchConfig>> searchFilter(@RequestParam(required = true) SearchConfigType type, @RequestParam(required = false) String keyWord) {
        log.info("[timesheet: User @{}] REST searchFilter", SecurityUtils.getUserId());
        return ResponseEntity.ok(configService.searchFilter(type,keyWord));
    }

    @GetMapping("/list")
    @Timed
    public ResponseEntity<List<TimeSheetSearchConfig>> findFilter(@RequestParam SearchConfigType type) {
        log.info("[timesheet: User @{}] REST findFilter", SecurityUtils.getUserId());
        log.info("[timesheet: findFilter] Parameter SearchConfigType: {}", ObjectUtils.isEmpty(type) ? "Empty SearchConfigType" : type.toDbValue());
        List<TimeSheetSearchConfig> records = configService.findMyConfig(type);
        if (CollectionUtils.isEmpty(records)) {
            records = Arrays.asList(configService.initMyConfig(type, SecurityUtils.getUserId(), SecurityUtils.getTenantId()));
        }
        return ResponseEntity.ok(records);
    }

    @GetMapping("/delete")
    @Timed
    public ResponseEntity<Void> deleteFilter(@RequestParam Long id) {
        log.info("[timesheet: User @{}] REST deleteFilter", SecurityUtils.getUserId());
        configService.deleteFilter(id);
        return ResponseEntity.ok().build();
    }

}
