package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.user.LoginVM;
import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.ForgetPassDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.ForgetPassResetDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.ResetPasswordDTO;

public interface TimeSheetUserService {

    TimeSheetUser resetPassword(ResetPasswordDTO resetPasswordDTO);

    void forgetPass(ForgetPassDTO forgetPassDTO);

    void resetPassForForget(ForgetPassResetDTO dto);

    TimeSheetUserDTO save(TimeSheetUserDTO timeSheetUserDTO);

    TimeSheetUser login(LoginVM loginVM);

    CompletionStatus getCompletionStatus();
}
