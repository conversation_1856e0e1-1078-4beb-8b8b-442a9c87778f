package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Timestamp;

@ApiModel(value = "发票发送email",description = "")
@Entity
@Table(name="t_invoice_send_email_record")
public class TInvoiceSendEmailRecord extends AbstractAuditingEntity implements Serializable,Cloneable{
    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private  BigInteger id ;
    
    /** 分组发票id */
    @ApiModelProperty(name = "分组发票id")
    @Column(name = "group_invoice_id")
    private  BigInteger groupInvoiceId ;
    /** 发票id */
    @ApiModelProperty(name = "发票id")
    @Column(name = "invoice_id")
    private  BigInteger invoiceId ;
    /** 主题 */
    @ApiModelProperty(name = "主题")
    @Column(name = "subject")
    private String subject ;
    /** 发送人 */
    @ApiModelProperty(name = "发送人")
    @Column(name = "send_by")
    private String sendBy ;
    /** 发送时间 */
    @ApiModelProperty(name = "发送时间")
    @Column(name = "send_date")
    private Timestamp sendDate ;
    /** 接收人 */
    @ApiModelProperty(name = "接收人")
    @Column(name = "send_to")
    private String sendTo ;
    /** 抄送人 */
    @ApiModelProperty(name = "抄送人")
    @Column(name = "carbon_copy")
    private String carbonCopy ;
    /** 发送状态 1-发送成功 2-发送失败 */
    @ApiModelProperty(name = "发送状态 1-发送成功 2-发送失败")
    @Column(name = "send_status")
    private Integer sendStatus ;
    /** 发送内容 */
    @ApiModelProperty(name = "发送内容")
    @Column(name = "send_content")
    private String sendContent ;
    /** 状态 0-无效 1-有效 */
    @ApiModelProperty(name = "状态 0-无效 1-有效")
    @Column(name = "status")
    private Integer status ;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigInteger getGroupInvoiceId() {
        return groupInvoiceId;
    }

    public void setGroupInvoiceId(BigInteger groupInvoiceId) {
        this.groupInvoiceId = groupInvoiceId;
    }

    public BigInteger getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(BigInteger invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getSendBy() {
        return sendBy;
    }

    public void setSendBy(String sendBy) {
        this.sendBy = sendBy;
    }

    public Timestamp getSendDate() {
        return sendDate;
    }

    public void setSendDate(Timestamp sendDate) {
        this.sendDate = sendDate;
    }

    public String getSendTo() {
        return sendTo;
    }

    public void setSendTo(String sendTo) {
        this.sendTo = sendTo;
    }

    public String getCarbonCopy() {
        return carbonCopy;
    }

    public void setCarbonCopy(String carbonCopy) {
        this.carbonCopy = carbonCopy;
    }

    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }

    public String getSendContent() {
        return sendContent;
    }

    public void setSendContent(String sendContent) {
        this.sendContent = sendContent;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
