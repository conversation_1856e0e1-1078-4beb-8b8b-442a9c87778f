package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.GroupInvoiceStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorGroupInvoiceSearchDTO;
import com.altomni.apn.jobdiva.service.vo.invoice.ContractorGroupInvoiceListVO;
import com.altomni.apn.jobdiva.service.vo.invoice.ContractorGroupInvoiceRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class ContractorGroupInvoiceNativeRepository {

    @Resource
    private EntityManager entityManager;


    /**
     * Paging Query Data
     *
     * @param dto
     * @param pageable
     * @return
     */
    @Transactional(readOnly = true)
    public Page<ContractorGroupInvoiceListVO> searchGroupInvoiceList(ContractorGroupInvoiceSearchDTO dto, Pageable pageable) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("select t.id,t.invoice_date as groupInvoiceDate,\n" +
                "t.group_number as groupInvoiceNumber,cast(t.group_invoice_status as char) as groupInvoiceStatus,\n" +
                "cast(t.invoice_type as char) as invoiceType,\n" +
                "t.invoice_amount as invoiceAmount,\n" +
                "t.amount_due as amountDue,\n" +
                "t.company_name as billingCompany,\n" +
                "t.company_id as companyId,\n" +
                "ec.name as currency,\n" +
                "cast(t.assignment_division as char) as assignmentDivision,\n" +
                "t.operated_by_name as sentBy,t.operated_date as sentOn\n" +
                " from t_group_invoice t left join enum_currency ec on ec.id = t.currency_id where status=1 ");
        String whereSql = assemblyQueryCondition(dto);

        dataSql.append(whereSql);

        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (!Objects.isNull(order)) {
            if (order.getProperty().equals("company_name")) {
                dataSql.append(" order by CONVERT(t." + order.getProperty() + "  USING GBK) " + order.getDirection());
            } else {
                dataSql.append(" order by t." + order.getProperty() + " " + order.getDirection());
            }
        } else {
            dataSql.append(" order by t.invoice_date asc");

        }

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(countSql);

        assemblyQueryParam(dataQuery, countQuery, dto);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorGroupInvoiceListVO.class));
        dataQuery.setFirstResult((int) pageable.getOffset());
        dataQuery.setMaxResults(pageable.getPageSize());

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<ContractorGroupInvoiceListVO> groupInvoiceListVOS = total > pageable.getOffset() ? dataQuery.getResultList() : new ArrayList<>();
        return new PageImpl<>(groupInvoiceListVOS, pageable, total);
    }

    /**
     * 拼装查询条件
     * Assembly query criteria
     *
     * @param dto
     * @return
     */
    private String assemblyQueryCondition(ContractorGroupInvoiceSearchDTO dto) {

        StringBuilder whereSql = new StringBuilder(" ");
        if (dto.getInvoiceTypeList() != null) {
            whereSql.append(" and t.invoice_type in (:invoiceType)");
        }

        if (null != dto.getGroupInvoiceNumber() && !dto.getGroupInvoiceNumber().isEmpty()) {
            whereSql.append(" and t.group_number in ( :groupInvoiceNumber )");
        }

        if (null != dto.getEmployeeId() && !dto.getEmployeeId().isEmpty()) {
            whereSql.append(" and t.id in ( select distinct group_invoice_id from t_group_invoice_record r, t_contractor_invoice ci  ");
            whereSql.append(" where  ci.talent_id in ( :talentId ) and r.invoice_id = ci.id )");
        }

        if (null != dto.getInvoiceDateStart() && null != dto.getInvoiceDateEnd()) {
            whereSql.append(" and t.invoice_date between :invoiceDateStart and :invoiceDateEnd ");
        }

        if (null != dto.getCreatedDateStart() && null != dto.getCreatedDateEnd()) {
            whereSql.append(" and t.created_date between :createdDateStart and :createdDateEnd ");
        }

        if (dto.getCompanyId() != null) {
            whereSql.append(" and t.company_id in ( :companyId )");
        }

        if (dto.getContactId() != null) {
            whereSql.append(" and t.id in ( select distinct group_invoice_id from t_group_invoice_record r, t_contractor_invoice ci  ");
            whereSql.append(" where  ci.company_contact_id in ( :companyContactId ) and r.invoice_id = ci.id)");
        }

        if (null != dto.getInvoiceStatusList()) {
            whereSql.append(" and t.group_invoice_status in ( :invoiceStatus )");
        }

        if (null != dto.getAssignmentDivisionList()) {
            whereSql.append(" and (");
            for (int i = 0; i < dto.getAssignmentDivisionList().size(); i++) {
                if (i == 0) {
                    whereSql.append(" t.assignment_division like :ass" + i);
                } else {
                    whereSql.append(" or t.assignment_division like :ass" + i);
                }
            }
            whereSql.append(" )");
        }

        if (null != dto.getTenantId()) {
            whereSql.append(" and t.tenant_id = :tenantId ");
        }

        return whereSql.toString();
    }

    /**
     * 设置参数
     * set param
     *
     * @param dto
     * @return
     */
    private void assemblyQueryParam(Query dataQuery, Query countQuery, ContractorGroupInvoiceSearchDTO dto) {

        if (dto.getInvoiceTypeList() != null) {
            dataQuery.setParameter("invoiceType", dto.getInvoiceTypeList().stream().mapToInt(InvoiceType::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoiceType", dto.getInvoiceTypeList().stream().mapToInt(InvoiceType::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (null != dto.getGroupInvoiceNumber() && !dto.getGroupInvoiceNumber().isEmpty()) {
            dataQuery.setParameter("groupInvoiceNumber", dto.getGroupInvoiceNumber());
            countQuery.setParameter("groupInvoiceNumber", dto.getGroupInvoiceNumber());
        }

        if (null != dto.getEmployeeId() && !dto.getEmployeeId().isEmpty()) {
            dataQuery.setParameter("talentId", dto.getEmployeeId());
            countQuery.setParameter("talentId", dto.getEmployeeId());
        }

        if (null != dto.getInvoiceDateStart() && null != dto.getInvoiceDateEnd()) {
            dataQuery.setParameter("invoiceDateStart", dto.getInvoiceDateStart());
            countQuery.setParameter("invoiceDateStart", dto.getInvoiceDateStart());
            dataQuery.setParameter("invoiceDateEnd", dto.getInvoiceDateEnd());
            countQuery.setParameter("invoiceDateEnd", dto.getInvoiceDateEnd());
        }

        if (null != dto.getCreatedDateStart() && null != dto.getCreatedDateEnd()) {
            dataQuery.setParameter("createdDateStart", dto.getCreatedDateStart());
            countQuery.setParameter("createdDateStart", dto.getCreatedDateStart());
            dataQuery.setParameter("createdDateEnd", dto.getCreatedDateEnd());
            countQuery.setParameter("createdDateEnd", dto.getCreatedDateEnd());
        }

        if (dto.getCompanyId() != null) {
            dataQuery.setParameter("companyId", dto.getCompanyId());
            countQuery.setParameter("companyId", dto.getCompanyId());
        }

        if (dto.getContactId() != null) {
            dataQuery.setParameter("companyContactId", dto.getContactId());
            countQuery.setParameter("companyContactId", dto.getContactId());
        }

        if (dto.getInvoiceStatusList() != null) {
            dataQuery.setParameter("invoiceStatus", dto.getInvoiceStatusList().stream().mapToInt(GroupInvoiceStatus::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoiceStatus", dto.getInvoiceStatusList().stream().mapToInt(GroupInvoiceStatus::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (dto.getAssignmentDivisionList() != null) {
            for (int i = 0; i < dto.getAssignmentDivisionList().size(); i++) {
                AssignmentDivision a = dto.getAssignmentDivisionList().get(i);
                dataQuery.setParameter("ass" + i, "%" + a.name() + "%");
                countQuery.setParameter("ass" + i, "%" + a.name() + "%");
            }
        }

        if (null != dto.getTenantId()) {
            dataQuery.setParameter("tenantId", dto.getTenantId());
            if (null != countQuery) {
                countQuery.setParameter("tenantId", dto.getTenantId());
            }
        }
    }


    /**
     * 查询group invoice 与 invoice timesheet 关联数据
     *
     * @param groupInvoiceId
     * @param flag
     * @return
     */
    @Transactional(readOnly = true)
    public List<ContractorGroupInvoiceRecordVO> selectGroupInvoiceTimesheetRecord(BigInteger groupInvoiceId, boolean flag) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("select t.id,inc.talent_id as talentId,an.full_name as talentName,\n" +
                "date_format(inc.week_ending_date,'%Y-%m-%d') as weekEndingDate,\n" +
                "inc.assignment_id as assignmentId,inc.p_o_number as poNumber,\n" +
                "cast(inc.invoice_type as char) as invoiceType,\n" +
                "inc.total_amount as invoiceTotalAmount,inc.client_name as clientName,\n" +
                "inc.client_address as clientAddress,inc.client_location as clientLocation,\n" +
                "c.name as currency,\n" +
                "ti.bill_rate as billRate,ti.unit,ti.total_amount as totalAmount,\n" +
                "ti.quantity,case when ti.quantity_type is null then '6' else cast(ti.quantity_type as char)  end as quantityType,\n" +
                "ti.item_description as itemDescription,inc.id as invoiceId,cast(ati.frequency as char) as frequency\n" +
                " from t_group_invoice_record t\n" +
                "join t_contractor_invoice inc on inc.id = t.invoice_id\n" +
                "left join t_invoice_timesheet_info ti on ti.invoice_id = inc.id\n" +
                "left join enum_currency c on c.id = ti.currency_type\n" +
                "left join talent an on an.id = t.talent_id \n" +
                "left join assignment_timesheet ati on ati.assignment_id = inc.assignment_id \n" +
                " where t.group_invoice_id = :groupInvoiceId\n");

        if (flag) {
            dataSql.append(" order by inc.talent_id asc,inc.week_ending_date asc,inc.invoice_type desc,inc.id asc,ti.quantity_type asc");
        } else {
            dataSql.append(" order by inc.talent_id asc,quantityType asc,inc.week_ending_date asc");
        }

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("groupInvoiceId", groupInvoiceId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorGroupInvoiceRecordVO.class));
        return dataQuery.getResultList();
    }


    /**
     * 查询公司联系人邮箱，用于email invoice 接口
     */
    public String selectEmailByCompanyId(Long tenantId, BigInteger companyId, Long groupInvoiceId) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("SELECT\n" +
                "	cs.company_id AS companyId,\n" +
                "	tc.contact AS email\n" +
                "FROM\n" +
                "	company_sales_lead_client_contact cs \n" +
                "	LEFT JOIN talent t ON t.id = cs.talent_id\n" +
                "	LEFT JOIN talent_contact tc ON tc.talent_id = t.id AND tc.jhi_type = 2\n" +
                "WHERE\n" +
                "	cs.company_id = :companyId \n" +
                "	AND cs.tenant_id = :tenantId\n" +
                "	AND cs.id = (\n" +
                "	SELECT t.company_contact_id FROM t_contractor_invoice t WHERE t.id = \n" +
                "		( SELECT t.invoice_id FROM t_group_invoice_record t WHERE t.group_invoice_id = :groupInvoiceId LIMIT 1 ) \n" +
                "	) GROUP BY cs.company_id");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("companyId", companyId);
        dataQuery.setParameter("tenantId", tenantId);
        dataQuery.setParameter("groupInvoiceId", groupInvoiceId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return "";
        }
        String email = mapList.get(0).get("email") + "";
        if (StringUtils.isBlank(email) || email.equals("null")) {
            return "";
        }
        return email;
    }

    /**
     * 查询email数据，通过start_client_info表
     *
     * @param groupInvoiceId
     * @return
     */
    public String selectEmailByGroupInvoiceId(Long groupInvoiceId) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("select ci.client_email as email from start t\n" +
                "join (SELECT t.tenant_id,t.talent_id,t.company_id,t.job_id FROM t_contractor_invoice t WHERE t.id =  \n" +
                "( SELECT t.invoice_id FROM t_group_invoice_record t WHERE t.group_invoice_id = :groupInvoiceId LIMIT 1 ) ) inc \n" +
                "on inc.tenant_id = t.tenant_id and inc.company_id = t.company_id and inc.talent_id = t.talent_id\n" +
                "join start_client_info ci on ci.start_id = t.id where t.status=0 ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());
        dataQuery.setParameter("groupInvoiceId", groupInvoiceId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return "";
        }
        String email = mapList.get(0).get("email") + "";
        if (StringUtils.isBlank(email) || email.equals("null")) {
            return "";
        }
        return email;
    }

    /**
     * 查询invoice下的currency
     */
    public Integer selectCurrencyByInvoiceId(BigInteger invoiceId) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("select currency_type from t_invoice_timesheet_info t where t.invoice_id=:invoiceId \n" +
                "union \n" +
                "select currency_type from t_invoice_expense_info t where t.invoice_id=:invoiceId ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("invoiceId", invoiceId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return null;
        }
        String currencyType = mapList.get(0).get("currency_type") + "";
        if (StringUtils.isBlank(currencyType) || currencyType.equals("null")) {
            return null;
        }
        return Integer.valueOf(currencyType);
    }

    /**
     * 查询group invoice 附件信息
     *
     * @param invoiceIdList
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectGroupInvoiceAttachmentByIds(List<BigInteger> invoiceIdList, Long tenantId) {

        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("select t.file_url as fileUrl,t.timesheet_file_url as timesheetFileUrl,t.company_id,t.group_number,cast(t.include_timesheet as char) as includeTimesheet  from t_group_invoice t \n" +
                "where t.`status`=1 and t.id in( :groupInvoiceId ) and tenant_id=:tenantId\n" +
                "order by FIELD(id, :groupInvoiceId) ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("groupInvoiceId", invoiceIdList);
        dataQuery.setParameter("tenantId", tenantId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return dataQuery.getResultList();
    }

    /**
     * 查询公司联系人 用于view展示
     */
    public String selectCompanyContactByGroupInvoiceId(BigInteger groupInvoiceId) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("select t.company_contact_name as companyContact from  t_contractor_invoice t where t.id = (\n" +
                "select t.invoice_id from t_group_invoice_record t where t.group_invoice_id=:groupInvoiceId limit 1 ) ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("groupInvoiceId", groupInvoiceId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return "";
        }
        String companyContact = mapList.get(0).get("companyContact") + "";
        if (StringUtils.isBlank(companyContact) || companyContact.equals("null")) {
            return "";
        }
        return companyContact;
    }

    /**
     * 查询公司和am
     * @param companyIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectCompanyInfo(List<Long> companyIds,List<Long> amList) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select distinct t.id,t.full_business_name as name,u.email,u.first_name from company t
                							left join business_flow_administrator bfa on bfa.company_id = t.id and bfa.sales_lead_role in (0,3)
                							left join user u on u.id = bfa.user_id
                							where u.activated=1 and t.id in (:companyId) and bfa.user_id in(:amList)
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("companyId", companyIds);
        dataQuery.setParameter("amList", amList);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return null;
        }
        return mapList;
    }
}
