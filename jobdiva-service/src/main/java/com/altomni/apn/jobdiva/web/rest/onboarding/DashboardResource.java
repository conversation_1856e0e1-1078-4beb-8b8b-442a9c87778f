package com.altomni.apn.jobdiva.web.rest.onboarding;

import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardDocumentResDto;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardPackageResDto;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardSearchDto;
import com.altomni.apn.jobdiva.service.onboarding.DashboardService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * REST controller for dashboard .
 * <AUTHOR>
 */
@Api(tags = {"APN-OnBoarding dashboard"})
@RestController
@RequestMapping("/api/v3/dashboard")
public class DashboardResource {

    private final Logger log = LoggerFactory.getLogger(DashboardResource.class);

    @Resource
    private DashboardService dashboardService;

    @ApiOperation(value = "search dashboard documents page data by search dto")
    @PostMapping("/document/page")
    @Timed
    public ResponseEntity<List<DashboardDocumentResDto>> pageDocument(@RequestBody DashboardSearchDto searchDto) {
        log.info("[APN: dashboard document page @{}] REST request to search dashboard documents page data by search dto, searchDto  : {}", SecurityUtils.getUserId(), searchDto);
        Page<DashboardDocumentResDto> page = dashboardService.findDocumentsPageBySearchDtoAndPage(searchDto);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/dashboard");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @ApiOperation(value = "search dashboard packages page data by search dto")
    @PostMapping("/package/page")
    @Timed
    public ResponseEntity<List<DashboardPackageResDto>> pagePackage(@RequestBody DashboardSearchDto searchDto) {
        log.info("[APN: dashboard package page @{}] REST request to search dashboard packages page data by search dto, searchDto : {}", SecurityUtils.getUserId(), searchDto);
        Page<DashboardPackageResDto> page = dashboardService.findPackagesPageBySearchDtoAndPage(searchDto);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/dashboard");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

}
