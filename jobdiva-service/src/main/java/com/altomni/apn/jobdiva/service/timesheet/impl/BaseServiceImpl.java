package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;
import com.altomni.apn.jobdiva.repository.timesheet.ExpenseRecordRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRecordRepository;
import com.altomni.apn.jobdiva.service.assignment.AssignmentSyncToHrService;
import com.altomni.apn.jobdiva.service.rabbitmq.JobdivaToHrRabbitService;
import com.altomni.apn.jobdiva.service.timesheet.BaseService;
import org.apache.commons.lang3.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

public class BaseServiceImpl implements BaseService {

    @Resource
    private TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    private ExpenseRecordRepository expenseRecordRepository;

    @Resource
    public AssignmentSyncToHrService assignmentSyncToHrService;

    @Resource
    public JobdivaToHrRabbitService jobdivaToHrRabbitService;


    @Override
    public Boolean isNoHourStatus(TimeSheetStatus status, Float regularHours) {
        return TimeSheetStatus.APPROVED == status && (ObjectUtils.isEmpty(regularHours) || regularHours == 0.0f);
    }

    @Deprecated
    @Override
    public Boolean hasPermission(Set<Long> recordIds, RecordType recordType, Long userId) {
        boolean result;
        if (RecordType.TIME_SHEET == recordType) {
            result = Optional.ofNullable(timeSheetRecordRepository.findRecordIdByCompanyAM(recordIds, userId)).isPresent();
            if (!result) {
                result = CollUtil.isNotEmpty(timeSheetRecordRepository.findAssignmentIdByApplicationRecruiterOrSourcer(userId, new ArrayList<>(recordIds)));
            }
        } else {
            result = Optional.ofNullable(expenseRecordRepository.findRecordIdByCompanyAM(recordIds, userId)).isPresent();
            if (!result) {
                result = CollUtil.isNotEmpty(expenseRecordRepository.findAssignmentIdByApplicationRecruiterOrSourcer(userId, new ArrayList<>(recordIds)));
            }
        }
        return result;
    }

    @Override
    public Boolean hasPermissionEdit(Set<Long> recordIds, RecordType recordType, Long userId) {
        List<Long> resultIds = new ArrayList<>();
        if (RecordType.TIME_SHEET == recordType) {
            resultIds.addAll(timeSheetRecordRepository.findRecordIdsByCompanyAM(recordIds, userId));
        } else {
            resultIds.addAll(expenseRecordRepository.findRecordIdsByCompanyAM(recordIds, userId));
        }
        resultIds = JSONUtil.toList(JSONUtil.parseArray(JSONUtil.toJsonStr(resultIds)), Long.class);
        String paramJson = recordIds.stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
        String resultJson = resultIds.stream().distinct().sorted().map(String::valueOf).collect(Collectors.joining(","));
        return Objects.equals(paramJson, resultJson);
    }

    @Override
    public Boolean hasPermissionEdit(Long assignmentId, RecordType recordType, Long userId) {
        return Optional.ofNullable(timeSheetRecordRepository.findAssignmentIdByCompanyAM(assignmentId, userId)).isPresent();
    }


    @Override
    public Boolean hasPermission(Long assignmentId, RecordType recordType, Long userId) {
        boolean result = Optional.ofNullable(timeSheetRecordRepository.findAssignmentIdByCompanyAM(assignmentId, userId)).isPresent();
        if (!result) {
            result = CollUtil.isNotEmpty(timeSheetRecordRepository.findAssignmentIdByApplicationRecruiterOrSourcer(userId, assignmentId));
        }
        return result;
    }

}
