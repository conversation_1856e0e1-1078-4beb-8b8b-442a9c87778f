package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.jobdiva.service.vo.invoice.ContractorGroupInvoiceDetailVO;
import com.altomni.apn.jobdiva.service.vo.invoice.ContractorInvoiceExpenseVO;
import com.altomni.apn.jobdiva.service.vo.invoice.ContractorInvoiceTimesheetVO;
import com.altomni.apn.jobdiva.service.vo.invoice.ContractorInvoiceViewVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * ContractorInvoicePrintDTO Only Used for the print method
 *
 * <AUTHOR> zhang.lei
 * @date : 2023-6-6
 */
@ApiModel(description = "ContractorInvoicePrintDTO")
@Data
public class ContractorInvoicePrintDTO implements Serializable {

    private String invoiceNumber;

    private String customerName;

    private String customerAddress;

    private String customerLocation;

    private String poNumber;

    private LocalDate invoiceDate;

    private LocalDate createdDate;

    private String customerContact;

    private String customerReference;

    private BigDecimal amountDue;

    private String currency;

    private Integer dueDate;

    List<List<ContractorGroupInvoiceDetailVO>> invoiceList;

    public static ContractorInvoicePrintDTO transformToDTO(ContractorInvoiceViewVO vo) {

        ContractorInvoicePrintDTO dto = new ContractorInvoicePrintDTO();
        dto.setInvoiceNumber(vo.getInvoiceNumber());
        dto.setPoNumber(vo.getPoNumber());
        dto.setInvoiceDate(vo.getInvoiceDate().toLocalDateTime().toLocalDate());
        dto.setCreatedDate(vo.getCreatedDate());
        dto.setCustomerContact(vo.getCompanyContact());
        dto.setAmountDue(vo.getTotalAmount());
        List data = new ArrayList();
        if (null != vo.getRegularInvoiceList() && !vo.getRegularInvoiceList().isEmpty()) {
            List<ContractorGroupInvoiceDetailVO> detailVOS = new ArrayList<>();
            int i = 0;
            for (ContractorInvoiceTimesheetVO timesheetVO : vo.getRegularInvoiceList()) {
                if (null == vo.getTotalAmount() || timesheetVO.getTotalAmount().compareTo(BigDecimal.ZERO)==0) {
                    continue;
                }
                ContractorGroupInvoiceDetailVO bean = new ContractorGroupInvoiceDetailVO();
                bean.setCurrency(timesheetVO.getCurrency());
                bean.setQuantity(timesheetVO.getQuantity());
                bean.setUnit(StringUtils.isNotBlank(timesheetVO.getUnit()) ? "Hour" : "");
                bean.setBillRate(timesheetVO.getBillRate());
                bean.setItemDescription(timesheetVO.getItemDescription());
                bean.setTotalAmount(timesheetVO.getTotalAmount());
                setTimePeriod(i, vo, bean);
                i++;
                detailVOS.add(bean);
            }
            data.add(detailVOS);
        }
        if (null != vo.getExpenseList() && !vo.getExpenseList().isEmpty()) {
            List<ContractorGroupInvoiceDetailVO> detailVOS = new ArrayList<>();
            int i = 0;
            for (ContractorInvoiceExpenseVO expenseVO : vo.getExpenseList()) {
                if (expenseVO.getExpenseMoney().compareTo(BigDecimal.valueOf(0l)) == 1) {
                    if (i == 0) {
                        ContractorGroupInvoiceDetailVO bean = new ContractorGroupInvoiceDetailVO();
                        bean.setCurrency(expenseVO.getCurrency());
                        bean.setItemDescription(vo.getEmployeeName());
                        bean.setTotalAmount(new BigDecimal("0.00"));
                        setTimePeriod(i, vo, bean);
                        detailVOS.add(bean);
                        i++;
                    }
                    ContractorGroupInvoiceDetailVO bean = new ContractorGroupInvoiceDetailVO();
                    bean.setCurrency(expenseVO.getCurrency());
                    bean.setItemDescription(expenseVO.getExpenseCategory());
                    bean.setTotalAmount(expenseVO.getExpenseMoney());
                    bean.setTimePeriod(expenseVO.getExpenseDate());
                    setTimePeriod(i, vo, bean);
                    i++;
                    detailVOS.add(bean);
                }
            }
            data.add(detailVOS);
        }
        dto.setInvoiceList(data);
        return dto;
    }

    private static void setTimePeriod(int i, ContractorInvoiceViewVO vo, ContractorGroupInvoiceDetailVO bean) {
        if (i == 0) {
            LocalDate endDate = vo.getWeekEnd();
            LocalDate startDate = vo.getWeekStart();
            DateTimeFormatter fmt = DateTimeFormatter.ofPattern("MM/dd/yyyy");
            bean.setTimePeriod("From " + fmt.format(startDate) + " To " + fmt.format(endDate));
        }
    }
}
