package com.altomni.apn.jobdiva.service.dto.onboarding.settings;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingSignatureDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String text;

}
