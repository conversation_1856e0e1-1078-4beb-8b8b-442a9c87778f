package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseType;
import com.altomni.apn.common.domain.enumeration.jobdiva.ManagerRoleType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.repository.timesheet.ExpenseRecordRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.timesheet.AdvanceSearchService;
import com.altomni.apn.jobdiva.service.timesheet.ClientService;
import com.altomni.apn.jobdiva.service.timesheet.ExpenseRecordAmService;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetExpenseService;
import com.altomni.apn.jobdiva.service.vo.timesheet.EndingDateListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.ExpenseListAmVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import com.altomni.apn.jobdiva.util.ZipCompressor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.SystemUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.altomni.apn.common.utils.DateUtil.fromInstantToDate;
import static com.altomni.apn.common.utils.DateUtil.localDateToString;


@Slf4j
@Service("expenseRecordAmService")
public class ExpenseRecordAmServiceImpl extends BaseServiceImpl implements ExpenseRecordAmService {

    @Resource
    private ClientService clientService;

    @Resource
    private TimeSheetExpenseService expenseService;

    @Resource
    private TalentAssignmentRepository assigmentRepository;

    @Resource
    private AdvanceSearchService searchService;

    @Resource
    private ExpenseRecordRepository expenseRecordRepository;

    @Resource
    private StoreService storeService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Override
    public List<BreakTimeDTO> findRecords(RecordSearchDTO dto) {
        TalentAssigment assigment = findAssignment(SecurityUtils.getUserId(),dto.getAssignmentId());
        Long talentId = assigment.getTalentId();
        return expenseService.amFindExpenseRecord(dto, talentId);
    }

    @Override
    public Integer saveExpenseTime(BreakTimeDTO dto) {
        TalentAssigment assigment = findAssignment(SecurityUtils.getUserId(), dto.getAssignmentId());
        Long talentId = assigment.getTalentId();
        dto.setStatus(TimeSheetStatus.APPLIED_APPROVE);
        Integer count = expenseService.saveExpenseTime(dto, talentId);
        assignmentSyncToHrService.buildExpenseRecordListSyncToHrMq(dto.getAssignmentId(), dto.getWeekEnd(), dto.getExpenseIndex());
        return count;
    }

    @Override
    public List<EndingDateListVO> weekEndingDates(Long talentId) {
        return expenseService.weekEndingDates(talentId, true);
    }

    @Override
    public BreakTimeDTO findRecordById(RecordSearchDTO dto) {
        return expenseService.findRecordById(dto);
    }

    @Override
    public SummaryDataVO search(AdvanceSearchDTO dto) {
        return searchService.searchForExpense(dto, null, getTalentIdsAndAssignments());
    }

    @Override
    public void downloadSummary(HttpServletResponse response, SummaryQueryDTO dto) {
        if (CollectionUtils.isEmpty(dto.getRecordIds())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RECORD_ID_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        AdvanceSearchDTO searchDTO = new AdvanceSearchDTO();
        searchDTO.setRecordIds(dto.getRecordIds());
        searchDTO.setOrder(dto.getOrder());
        searchDTO.setOrderBy(dto.getOrderBy());
        searchDTO.setPageSize(100000);
        searchDTO.setPageNum(1);
        searchDTO.setTimezone(dto.getTimezone());
        SummaryDataVO result = searchService.searchForExpense(searchDTO,null, null);
        ExcelWriter excelWriter = null;
        List<ExpenseListAmVO> data = (List<ExpenseListAmVO>) result.getData();
        OutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();
            addFileDownLoadHeader(response);
            response.setHeader("Content-Disposition", "attachment;filename=Expense" + fromInstantToDate(Instant.now(), dto.getTimezone(), DateUtil.YYYY_MM_DD) + ".xls");
            excelWriter = EasyExcelFactory.write(outputStream).build();
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0,"expense-summary").head(ExpenseListAmVO.class).needHead(true).build();
            excelWriter.write(data, writeSheet);
        } catch (Exception e) {
            log.error("error", e);
        } finally {
            if (excelWriter != null) { excelWriter.finish();}
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("error", e);
                }
            }
        }
    }

    @Override
    public void downloadReceipts(HttpServletResponse response, SummaryQueryDTO dto) {
        if (CollUtil.isEmpty(dto.getRecordIds())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RECORD_ID_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        AdvanceSearchDTO searchDTO = new AdvanceSearchDTO();
        searchDTO.setRecordIds(dto.getRecordIds());
        searchDTO.setOrder(dto.getOrder());
        searchDTO.setOrderBy(dto.getOrderBy());
        searchDTO.setPageSize(100000);
        searchDTO.setPageNum(1);
        searchDTO.setTimezone(dto.getTimezone());
        SummaryDataVO result = searchService.searchForExpense(searchDTO,null, null);
        List<ExpenseListAmVO> data = (List<ExpenseListAmVO>) result.getData();
        if(CollectionUtils.isEmpty(data)) {return;}
        Map<Long, List<ExpenseListAmVO>> talentRecords = data.stream().collect(Collectors.groupingBy(ExpenseListAmVO::getTalentId));
        String basePath = SystemUtils.USER_HOME + File.separator + SecurityUtils.getUserId();
        if (!FileUtil.isDirectory(basePath)) {
            FileUtil.mkdir(basePath);
        }
        try {
            response.setContentType("application/x-msdownload");
            String filename = "Expense_Receipts_"+fromInstantToDate(Instant.now(), dto.getTimezone(), DateUtil.YYYY_MM_DD)+".zip";
            response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode(filename, "UTF-8"));
            List<String> parentFilePath = new ArrayList<>();
            Map<String, String> uuidTalentNameMap = new HashMap<>(16);
            for (Map.Entry<Long, List<ExpenseListAmVO>> entry:talentRecords.entrySet()) {
                Map<ExpenseType, Map<String, List<String>>> expenseTypeS3Links = new HashMap<>(16);
                List<ExpenseListAmVO> listAmVos = entry.getValue();
                String talentName = listAmVos.get(0).getTalentName() ;
                String talentNameUuid = IdUtil.fastSimpleUUID();
                uuidTalentNameMap.put(talentNameUuid, talentName);
                // FIXME: Step 1 - check here and see if the number of expenses for the talent was correct
                log.info("The number of expenses for talent {} : {}", entry.getKey(), listAmVos.size());
                for (ExpenseListAmVO vo: listAmVos) {
                    List<ExpenseRecord> s3LinkRecords = expenseRecordRepository.findAllByStartDateAndEndDateAndTalentIdAndAssignmentIdAndExpenseIndex(vo.getWeekStart(), vo.getWeekEnd(), vo.getTalentId(), vo.getAssignmentId(), vo.getExpenseIndex());
                    if (CollUtil.isNotEmpty(s3LinkRecords)) {
                        s3LinkRecords = s3LinkRecords.stream().sorted(Comparator.comparing((ExpenseRecord o) -> o.getExpenseType().name()).thenComparing(ExpenseRecord::getLineIndex)).toList();
                        String endingDateStr = localDateToString(vo.getWeekEnd());
                        s3LinkRecords.forEach(r -> {
                            ExpenseType expenseType = r.getExpenseType();
                            if (expenseTypeS3Links.containsKey(expenseType)) {
                                Map<String, List<String>> endingDates3LinkMap = expenseTypeS3Links.get(expenseType);
                                if (MapUtils.isNotEmpty(endingDates3LinkMap) && endingDates3LinkMap.containsKey(endingDateStr)) {
                                    endingDates3LinkMap.get(endingDateStr).add(r.getS3Key());
                                } else {
                                    endingDates3LinkMap.put(endingDateStr, CollUtil.newArrayList(r.getS3Key()));
                                }
                            } else {
                                Map<String, List<String>> endingDates3LinkMap = new HashMap<>(16);
                                endingDates3LinkMap.put(endingDateStr, CollUtil.newArrayList(r.getS3Key()));
                                expenseTypeS3Links.put(expenseType, endingDates3LinkMap);
                            }
                        });
                    }
                }
                if (MapUtils.isNotEmpty(expenseTypeS3Links)) {
                    String parentPath = basePath+File.separator + talentNameUuid;
                    parentFilePath.add(parentPath);
                    File parentFile = new File(parentPath);
                    if (!FileUtil.isDirectory(parentFile)) {FileUtil.mkdir(parentFile);}
                    for (Map.Entry<ExpenseType, Map<String, List<String>>> expenseTypeS3Link : expenseTypeS3Links.entrySet()) {
                        ExpenseType expenseType = expenseTypeS3Link.getKey();
                        Map<String, List<String>> endingDates3LinkMap = expenseTypeS3Link.getValue();
                        for (Map.Entry<String, List<String>> endingDates3Link: endingDates3LinkMap.entrySet()) {
                            StringBuilder filePathBase = new StringBuilder();
                            filePathBase.append(parentPath);
                            filePathBase.append(File.separator);
                            filePathBase.append(expenseType.name());
                            filePathBase.append("-");
                            filePathBase.append(endingDates3Link.getKey());
                            List<String> s3Links = endingDates3Link.getValue();
                            for (int i = 0; i < s3Links.size(); i++) {
                                String link = s3Links.get(i);
                                String key = link.substring(link.lastIndexOf("/") + 1);
                                StringBuilder filePath = new StringBuilder();
                                filePath.append(filePathBase);
                                if (s3Links.size() > 1) {
                                    filePath.append("-");
                                    filePath.append(i + 1);
                                }
                                try {
                                    CloudFileObjectMetadata cloudFileObject = storeService.downloadDocument(key, UploadTypeEnum.RECEIPT.getKey()).getBody();
                                    String fileType = cloudFileObject.getFileName().substring(cloudFileObject.getFileName().lastIndexOf("."));
                                    filePath.append(fileType);
                                    FileUtil.writeBytes(cloudFileObject.getContent(), filePath.toString());
                                } catch (Exception e) {
                                    // FIXME: Step 3 - check here and see if any exception happened here to cause files missing
                                    log.error("Could not write file from s3Link {} for talent {}", link, entry.getKey(), e);
                                }
                            }
                        }
                    }
                }
            }
            addFileDownLoadHeader(response);
            String zipFilePath = basePath + File.separator + UUID.randomUUID() + ".zip";
            ZipCompressor zc = new ZipCompressor(zipFilePath, uuidTalentNameMap);
            zc.compress(parentFilePath);
            response.getOutputStream().write(FileUtil.readBytes(new File(zipFilePath)));
        } catch (IOException e) {
            log.error("error", e);
        } finally {
            FileUtil.del(basePath);
        }
    }

    @Override
    public Integer expenseApprove(ApproveDTO dto) {
        return clientService.expenseApprove(dto, ManagerRoleType.AM);
    }

    private void addFileDownLoadHeader(HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Headers","*");
        //全局 gateway 也配置了跨域,等于是同一个可以有2个值,会导致报错跨域,但是以前没有问题很奇怪
//        response.setHeader("Access-Control-Allow-Origin","*");
        response.setHeader("Access-Control-Allow-Methods","*");
        response.setHeader("Access-Control-Expose-Headers","*");
    }

    private List<Long> getTalentIdsAndAssignments() {
        List<Long> assignmentIds = new ArrayList<>();
        if(SecurityUtils.isAdmin() || SecurityUtils.isTimesheetAdmin()) {
            return assignmentIds;
        } else {
            Long userId = SecurityUtils.getUserId();
            Set<Long> assignmentIdSet = new HashSet<>();
            assignmentIdSet.addAll(assigmentRepository.findAssignmentIdsByUserIdForCompanyAM(userId));
            assignmentIdSet.addAll(assigmentRepository.findAssignmentIdsForApplicationRecruiterOrSourcer(userId));
            if (CollectionUtils.isEmpty(assignmentIdSet)) {
                assignmentIdSet.add(-1L);
            }
            assignmentIds.addAll(assignmentIdSet);
        }
        return assignmentIds;
    }

    private TalentAssigment findAssignment(Long amId,Long assignmentId) {
        TalentAssigment assigment;
        if (SecurityUtils.isAdmin() || SecurityUtils.isTimesheetAdmin()) {
            assigment = assigmentRepository.findById(assignmentId).get();
        } else {
            assigment = assigmentRepository.findByIdAndAmId(amId,assignmentId);
        }
        if (assigment == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.EXPENSE_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        return assigment;
    }

}
