package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.SearchConfigType;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetSearchConfig;
import com.altomni.apn.jobdiva.service.dto.timesheet.SearchFilterDTO;

import java.util.List;

public interface SearchConfigService {

    TimeSheetSearchConfig saveFilter(SearchFilterDTO dto);

    TimeSheetSearchConfig saveTableHeader(SearchFilterDTO dto);

    List<TimeSheetSearchConfig> searchFilter(SearchConfigType type, String keyWord);

    List<TimeSheetSearchConfig> findMyConfig(SearchConfigType type);

    TimeSheetSearchConfig initMyConfig(SearchConfigType type, Long uid, Long tenantId);

    void deleteFilter(Long id);

}
