package com.altomni.apn.jobdiva.service.scheduled;

import com.altomni.apn.common.domain.talent.TalentAssigment;

import java.util.List;

public interface SendEmailScheduledTaskService {

    void sendEmailAssignmentExpirationReminder();


    void sendEmailFriday5PM(List<TalentAssigment> talentAssigmentList);

    void sendEmailSaturday3PM(List<TalentAssigment> talentAssigmentList);

    void sendEmailByTimeSheetExpiredWithManager(List<TalentAssigment> talentAssigmentList);

}
