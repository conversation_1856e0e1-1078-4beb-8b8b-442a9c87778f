package com.altomni.apn.jobdiva.service.assignment;

import cn.hutool.json.JSONArray;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import com.altomni.apn.jobdiva.service.dto.assignment.AssignmentBatchInfoDTO;
import com.altomni.apn.jobdiva.service.dto.assignment.AssignmentCheckDateDTO;
import com.altomni.apn.jobdiva.service.dto.assignment.AssignmentDetailInfoDTO;
import com.altomni.apn.jobdiva.service.dto.assignment.CurrentContractorDTO;
import com.altomni.apn.jobdiva.service.vo.assignment.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface AssignmentService {

    Long save(AssignmentDetailInfoDTO dto);

    Long update(AssignmentDetailInfoDTO dto);

    AssignmentGeneralInfoVO currentAssignment(Long talent);

    AssignmentDetailInfoDTO findLatestAssignment(Long startId);

    AssignmentDetailInfoDTO detail (Long assignmentId);

    List<AssignmentGeneralInfoVO> list(Long startId);

    AssignmentCancelEliminateInfoVO assignmentList(Long talentRecruitmentProcessId,Long talentId);

    void batchUpdate(AssignmentBatchInfoDTO dto);

    List<PayInfoVO> findPayList(Long startId);

    void delete(Long id);

    void deleteByStartId(Long startId);

    void createTimesheetByStartId(Long startId);

    Integer findLatestAssignmentOrder(Long startId);

    AssignmentPayRateInfo getBillUnit(Long assignmentId);

    AssignmentPayRateInfo getPayUnit(Long assignmentId);

//    void historyDataProcess();
    void checkAssignmentDate(AssignmentCheckDateDTO assignmentCheckDateDTO);

    Page<CurrentContractorVO> findCurrentContractorPage(CurrentContractorDTO dto, Pageable pageable);

    void currentContractorListExcel(CurrentContractorDTO dto, HttpServletResponse response);

    Page<AssignmentActivityVO> getJobActivities(List<Long> assigmentIds, Pageable pageable) throws IOException;

    JSONArray deleteAssignmentByJobId(Long jobId);
}
