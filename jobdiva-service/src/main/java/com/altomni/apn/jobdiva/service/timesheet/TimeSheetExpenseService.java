package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.RecordSearchDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.SummaryQueryDTO;
import com.altomni.apn.jobdiva.service.vo.timesheet.EndingDateListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;

import java.util.List;

public interface TimeSheetExpenseService {

    BreakTimeDTO findExpenseRecord(RecordSearchDTO dto, Long userId);

    List<BreakTimeDTO> amFindExpenseRecord(RecordSearchDTO dto, Long talentId);

    Integer saveExpenseTime(BreakTimeDTO dto, Long talentId);

    Integer deleteByLineIndex(RecordSearchDTO dto);

    SummaryDataVO expenseList(SummaryQueryDTO dto, Long userId);

    List<EndingDateListVO> weekEndingDates(Long talentId, boolean isAm);

    BreakTimeDTO findRecordById(RecordSearchDTO dto);

}
