package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;

/**
 * used in create invoice interface and
 * search failed approval timesheet interface
 */
@ApiModel(description = "contractorInvoiceCreateDTO")
@Data
public class ContractorInvoiceCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String from;

    private String to;

    private BigInteger companyId;

    private List<InvoiceType> invoiceTypeList;

    private List<AssignmentDivision> assignmentDivisionList;

    private Long tenantId;

    private List<BigInteger> employeeIdList;
}
