package com.altomni.apn.jobdiva.service.dto.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentStatusType;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentType;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentContribution;
import com.altomni.apn.jobdiva.service.dto.timesheet.TimeSheetInfoDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@ApiModel
@Data
public class AssignmentDetailInfoDTO {

    private Long id;

    private LocalDate startDate;

    private LocalDate endDate;

    private Long startId;

    private Boolean isClockIn;

    private Boolean isSubmitExpenses;

    private Long talentId;

    private Long jobId;

    private Long companyId;

    private AssignmentType type;

    private Integer order;

    private String createdBy;

    private Instant createdTime;

    private AssignmentStatusType status;

    private BillInfoDTO billInfo;

    private TimeSheetInfoDTO timeSheet;

    private WorkInfoDTO workLocation;

    private Long talentRecruitmentProcessId;

    private List<AssignmentContribution> userInfo;

    private PayInfoDTO payInfo;

    private Float workingHours;

    private boolean frequencyChanged = false;

    public boolean overlaps(AssignmentDetailInfoDTO other) {
        return this.startDate.isBefore(other.endDate) && this.endDate.isAfter(other.startDate);
    }
}
