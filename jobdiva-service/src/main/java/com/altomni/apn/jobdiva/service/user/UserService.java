package com.altomni.apn.jobdiva.service.user;

import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.user.domain.user.ApnParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(value = "user-service")
public interface UserService {


    @GetMapping("/user/api/v3/users/apnparam/find-by-paramkey")
    ResponseEntity<ApnParam> findByParamKey(@RequestParam("paramKey") String paramKey, @RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status);

    @PostMapping("/user/api/v3/users/apnparam/save")
    ResponseEntity<ApnParam> saveApnParam(@RequestBody ApnParam apnParam);

    @PostMapping("/user/api/v3/users/all-brief-by-ids")
    ResponseEntity<List<UserBriefDTO>> getBriefUsersByIds(@RequestBody List<Long> ids);

    @GetMapping("/user/api/v3/users/find-by-id")
    ResponseEntity<UserBriefDTO> findById(@RequestParam("id") Long id);

    @PostMapping("/user/api/v3/users/get-user-names-by-uid-in")
    ResponseEntity<Map<String, UserUidNameDTO>> findUsersByUidIn(@RequestBody List<String> uids);


}
