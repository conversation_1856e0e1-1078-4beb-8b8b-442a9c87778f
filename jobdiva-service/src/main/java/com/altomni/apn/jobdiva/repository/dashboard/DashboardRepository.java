package com.altomni.apn.jobdiva.repository.dashboard;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.common.dto.search.ConditionParam;
import com.altomni.apn.common.dto.search.Relation;
import com.altomni.apn.common.dto.search.SearchParam;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.ApprovalStatus;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.DocumentType;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardDocumentResDto;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardPackageResDto;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardSearchDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.altomni.apn.jobdiva.constants.DashboardConstants.*;

/**
 * dashboard repository
 * <AUTHOR>
 */
@Repository
public class DashboardRepository {

    private final Logger log = LoggerFactory.getLogger(DashboardRepository.class);

    private final static String DOCUMENT_SEARCH = "document_search";

    private final static String PACKAGE_SEARCH = "package_search";

    @PersistenceContext
    private EntityManager entityManager;

    public Page<DashboardDocumentResDto> findDocumentsPageBySearchDtoAndPage(DashboardSearchDto searchDto, Pageable pageable) {
        StringBuilder conditionSql = new StringBuilder();
        conditionSql = buildConditionSql(searchDto, conditionSql, DOCUMENT_SEARCH);

        StringBuilder commonSql = new StringBuilder();
        StringBuilder dataSql = new StringBuilder();
        StringBuilder countSql = new StringBuilder();
        //build search field
        //data
        dataSql.append("select AAA.* from (SELECT t.full_name AS employee_name,")
            .append("t.id as talent_id,")
            .append("obph.id as id,")
            .append("obph.document_type AS document_type,")
            .append("obph.document_name AS document_name,")
            .append("obpad.approval_status AS document_status,")
            .append("obu.first_name as assigned_by_first_name,")
            .append("obu.last_name as assigned_by_last_name,")
            .append("(case CONCAT(obu.first_name,obu.last_name)  regexp '[一-龥]' when 1 then CONCAT(obu.last_name,obu.first_name) ELSE CONCAT(obu.first_name, \" \",obu.last_name) END) as assigned_by,")
            .append("(case CONCAT(u.first_name,u.last_name)  regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, \" \",u.last_name) END) as start_by_user,")
            .append("obph.created_date as package_assigned_on,")
            .append("obph.package_name as package_name,")
            .append("obph.package_status as package_status,")
            .append("u.first_name AS start_by_user_first_name,")
            .append("u.last_name AS start_by_user_last_name,")
            .append("aol.start_date AS starting_on,")
            .append("j.title as job_title,")
            .append("j.id as job_id,")
            .append("j.code as job_code,")
            .append("c.`full_business_name` as company,")
            .append("c.`id` as company_id,")
            .append("u.id AS start_by_user_id,")
            .append("obph.package_id as package_id,")
            .append("obph.document_id AS document_id,")
            .append("obu.id as assigned_by_id,")
            .append("jai.department as department ");
        //count
        countSql.append("SELECT count(obph.id) ");

        //build from tables
        doBuildTablesSql(commonSql);

        //build search condition
        commonSql.append("where obph.tenant_id = ?1 and obpad.approval_status != 4 ").append(conditionSql);

        //do count search
        String searchCountSql = countSql.append(commonSql).toString();
        log.info("[APN: dashboard document page @{}] REST request to search dashboard documents page searchCountSql = [{}]", SecurityUtils.getUserId(), searchCountSql);
        long count = searchCount(searchCountSql);
        //do data search
        commonSql.append(" ) AAA ").append(doBuildOrderSql(pageable));
        String searchDataSql = dataSql.append(commonSql).toString();
        log.info("[APN: dashboard document page @{}] REST request to search dashboard documents page searchDataSql = [{}]", SecurityUtils.getUserId(), searchDataSql);
        List<DashboardDocumentResDto> resultList = searchData(searchDataSql, DashboardDocumentResDto.class, pageable);
        return new PageImpl<>(resultList, Pageable.unpaged(), count);
    }

    public Page<DashboardPackageResDto> findPackagesPageBySearchDtoAndPage(DashboardSearchDto searchDto, Pageable pageable) {
        StringBuilder conditionSql = new StringBuilder();
        conditionSql = buildConditionSql(searchDto, conditionSql, PACKAGE_SEARCH);

        StringBuilder commonSql = new StringBuilder();
        StringBuilder dataSql = new StringBuilder();
        StringBuilder countSql = new StringBuilder();

        //build search field
        //count
        countSql.append("select count(1) from (SELECT obph.id ");
        //data
        dataSql.append("SELECT AAA.* FROM (SELECT t.full_name AS employee_name,")
            .append("t.id as talent_id,")
            .append("obph.id as id,")
            .append("obu.first_name as assigned_by_first_name,")
            .append("obu.last_name as assigned_by_last_name,")
            .append("obph.created_date as package_assigned_on,")
            .append("obph.package_name as package_name,")
            .append("obph.package_status as package_status,")
            .append("u.first_name AS start_by_user_first_name,")
            .append("u.last_name AS start_by_user_last_name,")
            .append("(case CONCAT(obu.first_name,obu.last_name)  regexp '[一-龥]' when 1 then CONCAT(obu.last_name,obu.first_name) ELSE CONCAT(obu.first_name, \" \",obu.last_name) END) as assigned_by,")
            .append("(case CONCAT(u.first_name,u.last_name)  regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, \" \",u.last_name) END) as start_by_user,")
            .append("aol.start_date AS starting_on,")
            .append("j.title as job_title,")
            .append("j.id as job_id,")
            .append("j.code as job_code,")
            .append("c.`full_business_name` as company,")
            .append("c.`id` as company_id,")
            .append("u.id AS start_by_user_id,")
            .append("obph.package_id as package_id,")
            .append("obph.document_id AS document_id,")
            .append("obu.id as assigned_by_id,")
            .append("jai.department as department ");

        //build from tables
        doBuildTablesSql(commonSql);

        //build search condition
        commonSql.append("where obph.tenant_id = ?1 and obpad.approval_status != 4 ").append(conditionSql)
            .append("GROUP BY obph.process_id");

        //do count search
        String searchCountSql = countSql.append(commonSql).append(") a").toString();
        log.info("[APN: dashboard package page @{}] REST request to search dashboard packages page searchCountSql = [{}]", SecurityUtils.getUserId(), searchCountSql);
        long count = searchCount(searchCountSql);
        //do data search
        commonSql.append(") AAA ").append(doBuildOrderSql(pageable));
        //do data search
        String searchDataSql = dataSql.append(commonSql).toString();
        log.info("[APN: dashboard package page @{}] REST request to search dashboard packages page searchDataSql = [{}]", SecurityUtils.getUserId(), searchDataSql);
        List<DashboardPackageResDto> resultList = searchData(searchDataSql, DashboardPackageResDto.class, pageable);
        return new PageImpl<>(resultList, Pageable.unpaged(), count);
    }

    private <T> List<T> searchData(String dataQuery, Class<T> clazz, Pageable pageable) {
        Query dataQ = entityManager.createNativeQuery(dataQuery, clazz);
        dataQ.setParameter(1, SecurityUtils.getTenantId());
        //do handler offset
        dataQ.setFirstResult((pageable.getPageNumber() <= 0? 0: pageable.getPageNumber() - 1) * pageable.getPageSize());
        dataQ.setMaxResults(pageable.getPageSize());
        return dataQ.getResultList();
    }

    private long searchCount(String countQuery) {
        Query countQ = entityManager.createNativeQuery(countQuery);
        countQ.setParameter(1, SecurityUtils.getTenantId());
        return Long.parseLong(String.valueOf(countQ.getSingleResult()));
    }

    private void doBuildTablesSql(StringBuilder commonSql) {
        commonSql.append("FROM onboarding_process_histories obph LEFT JOIN talent_recruitment_process a ON a.id = obph.talent_recruitment_process_id ")
            .append("LEFT JOIN start aol ON aol.talent_recruitment_process_id = a.id and aol.start_type = 0 ")
            .append("LEFT JOIN talent t ON t.id = a.talent_id ")
            .append("LEFT JOIN user u ON u.id = substring_index( aol.created_by, ',', 1 )")
            .append("LEFT JOIN user obu ON obu.id = substring_index( obph.created_by, ',', 1 ) ")
            .append("LEFT JOIN (SELECT opad.id,approval_status,history_id FROM onboarding_process_approval_details opad RIGHT JOIN ( SELECT max( id ) AS id FROM onboarding_process_approval_details GROUP BY history_id ) a ON opad.id = a.id ) obpad ON obpad.history_id = obph.id ")
            .append("left join job j on j.id = a.job_id ")
            .append(" left join job_additional_info jai on jai.id = j.additional_info_id ")
            .append("left join company c on c.id = j.company_id ");
    }

    /**
     * build condition sql
     * @param searchDto
     * @param conditionSql
     */
    private StringBuilder buildConditionSql(DashboardSearchDto searchDto, StringBuilder conditionSql, String searchType) {
        if (ObjectUtil.isNotEmpty(searchDto)) {
            List<SearchParam> searchParamList = searchDto.getSearch();
            if (CollectionUtil.isNotEmpty(searchParamList)) {
                for (SearchParam searchParam : searchParamList) {
                    List<ConditionParam> conditionParamList = searchParam.getCondition();
                    if (CollectionUtil.isNotEmpty(conditionParamList)) {
                        conditionSql = doBuildWhereConditionSql(conditionSql, conditionParamList, searchParam.getRelation());
                    } else {
                        if (Objects.equals(searchType, PACKAGE_SEARCH)) {
                            //do build package search default date
                            ConditionParam conditionParam = new ConditionParam();
                            conditionParam.setKey(STARTING_ON);
                            Map<String, Object> objMap = new HashMap<>(16);
                            objMap.put("data", new ArrayList<String>(){{add(searchDto.getTimezone());}});
                            conditionParam.setValue(objMap);
                            //init array
                            conditionParamList = new ArrayList<>();
                            conditionParamList.add(conditionParam);
                            conditionSql = doBuildWhereConditionSql(conditionSql, conditionParamList, searchParam.getRelation());
                        }
                    }
                }
            }
        }
        return conditionSql;
    }

    /**
     * build order by sql
     * @param pageable
     */
    private String doBuildOrderSql(Pageable pageable) {
        StringBuilder orderSb = new StringBuilder();
        if (pageable.getSort() == null) {
            return orderSb.toString();
        }
        //order by CONVERT(job_title USING gbk)
        Iterator<Sort.Order> iterator = pageable.getSort().iterator();
        orderSb.append(" ").append("order by ");
        while (iterator.hasNext()) {
            Sort.Order order = iterator.next();
            //Uppercase to underscore
            orderSb.append(" CASE WHEN IFNULL( ").append(StrUtil.toUnderlineCase(order.getProperty())).append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            if (MULTILINGUAL_MIXED_ORDER_COLUMN_LIST.contains(order.getProperty())) {
                orderSb.append("CONVERT( ").append(StrUtil.toUnderlineCase(order.getProperty())).append(" USING gbk) ");
                orderSb.append(" ").append(order.getDirection().name());
            } else {
                orderSb.append(StrUtil.toUnderlineCase(order.getProperty()));
                orderSb.append(" ").append(order.getDirection().name());
            }

            if (iterator.hasNext()) {
                orderSb.append(",");
            }
        }
        return orderSb.toString();
    }

    /**
     * build dynamic condition sql
     * @param conditionSql
     * @param conditionParamList
     * @param relation
     */
    private StringBuilder doBuildWhereConditionSql(StringBuilder conditionSql, List<ConditionParam> conditionParamList, Relation relation) {
        if (CollUtil.isNotEmpty(conditionParamList)) {
            conditionSql.append(" ").append("and").append(" ").append("(");
            AtomicBoolean firstTag = new AtomicBoolean(true);
            for (ConditionParam condition : conditionParamList) {
                if (!firstTag.get()) {
                    conditionSql.append(" ").append(relation == null? "and": relation.name());
                } else {
                    firstTag.set(false);
                }
                List<String> list = getDataValue(condition);
                conditionSql.append(" ").append("(");
                switch (condition.getKey()) {
                    case EMPLOYEE_NAME:
                        for (String value : list) {
                            conditionSql.append(" ").append("LOWER(t.full_name) like").append("'%").append(value.toLowerCase()).append("%'").append(" ").append("or");
                        }
                        break;
                    case DOCUMENT_TYPE:
                        List<Integer> documentTypeList = new ArrayList<>(list.size());
                        for (String value : list) {
                            Integer documentType = DocumentType.valueOf(value).toDbValue();
                            documentTypeList.add(documentType);
                        }
                        conditionSql.append(" ").append("obph.document_type in ").append("(").append(documentTypeList.stream().map(String::valueOf).collect(Collectors.joining(","))).append(")").append(" ").append("or");
                        break;
                    case DOCUMENT_ID:
                        conditionSql.append(" ").append("obph.document_id in ").append("(").append(String.join(",", list)).append(")").append(" ").append("or");
                        break;
                    case DOCUMENT_NAME:
                        for (String value : list) {
                            conditionSql.append(" ").append("LOWER(obph.document_name) like").append("'%").append(value.toLowerCase()).append("%'").append(" ").append("or");
                        }
                        break;
                    case DOCUMENT_STATUS:
                        List<Integer> documentStatusList = new ArrayList<>(list.size());
                        for (String value : list) {
                            Integer approvalStatus = ApprovalStatus.valueOf(value).toDbValue();
                            documentStatusList.add(approvalStatus);
                        }
                        conditionSql.append(" ").append("obpad.approval_status in ").append("(").append(documentStatusList.stream().map(String::valueOf).collect(Collectors.joining(","))).append(")").append(" ").append("or");
                        break;
                    case PACKAGE_ASSIGNED_ON:
                        for (String timeStr : list) {
                            //convention format beginTimestamp-endTimestamp
                            String[] times = timeStr.split("-");
                            conditionSql.append(" ").append("obph.created_date between '").append(Instant.ofEpochMilli(Long.parseLong(times[0]))).append("' ").append("and").append(" '").append(Instant.ofEpochMilli(Long.parseLong(times[1]))).append("' ").append("or");
                        }
                        break;
                    case PACKAGE_ID:
                        conditionSql.append(" ").append("obph.package_id in ").append("(").append(String.join(",", list)).append(")").append(" ").append("or");
                        break;
                    case PACKAGE_NAME:
                        for (String value : list) {
                            conditionSql.append(" ").append("LOWER(obph.package_name) like").append("'%").append(value.toLowerCase()).append("%'").append(" ").append("or");
                        }
                        break;
                    case PACKAGE_STATUS:
                        List<Integer> packageStatusList = new ArrayList<>(list.size());
                        for (String value : list) {
                            Integer completionStatus = CompletionStatus.valueOf(value).toDbValue();
                            packageStatusList.add(completionStatus);
                        }
                        conditionSql.append(" ").append("obph.package_status in ").append("(").append(packageStatusList.stream().map(String::valueOf).collect(Collectors.joining(","))).append(")").append(" ").append("or");
                        break;
                    case STARTING_ON:
                        for (String timeStr : list) {
                            String[] startTimes = timeStr.split("-");
                            if (startTimes.length > 1) {
                                conditionSql.append(" ").append("aol.start_date between '").append(Instant.ofEpochMilli(Long.parseLong(startTimes[0]))).append("' ").append("and").append(" '").append(Instant.ofEpochMilli(Long.parseLong(startTimes[1]))).append("' ").append("or");
                            } else {
                                //default date limit
                                conditionSql.append(" ").append("aol.start_date >= '").append(Instant.ofEpochMilli(Long.parseLong(startTimes[0]))).append("' ").append("or");
                            }
                        }
                        break;
                    case JOB_ID:
                        conditionSql.append(" ").append("j.id in ").append("(").append(String.join(",", list)).append(")").append(" ").append("or");
                        break;
                    case COMPANY_ID:
                        conditionSql.append(" ").append("c.`id` in ").append("(").append(String.join(",", list)).append(")").append(" ").append("or");
                        break;
                    case JOB_TITLE:
                        for (String value : list) {
                            conditionSql.append(" ").append("LOWER(j.`title`) like ").append("'%").append(value.toLowerCase()).append("%'").append(" ").append("or");
                        }
                        break;
                    case JOB_CODE:
                        for (String value : list) {
                            conditionSql.append(" ").append("LOWER(j.`CODE`) like ").append("'%").append(value.toLowerCase()).append("%'").append(" ").append("or");
                        }
                        break;
                    case DEPARTMENT:
                        for (String value : list) {
                            conditionSql.append(" ").append("LOWER(jai.department) like ").append("'%").append(value.toLowerCase()).append("%'").append(" ").append("or");
                        }
                        break;
                    case START_BY_USER_ID:
                        conditionSql.append(" ").append("u.id in ").append("(").append(String.join(",", list)).append(")").append(" ").append("or");
                        break;
                    case ASSIGNED_BY_ID:
                        conditionSql.append(" ").append("obu.id in ").append("(").append(String.join(",", list)).append(")").append(" ").append("or");
                        break;
                    default:
                        log.error("[APN: dashboard page @{}] REST request to search dashboard page [error], doBuildWhereConditionSql condition is not exists, key : {}, value : {}", SecurityUtils.getUserId(), condition.getKey(), condition.getValue());
                        throw new CustomParameterizedException("dashboard condition is not exists , key = " + condition.getKey());
                }
                conditionSql = new StringBuilder(conditionSql.substring(0, conditionSql.length() - 2));
                conditionSql.append(")");
            }
            conditionSql.append(")");
        }
        return conditionSql;
    }

    private List<String> getDataValue(ConditionParam conditionParam) {
        Object value = conditionParam.getValue();
        String key = conditionParam.getKey();
        if (StrUtil.isBlank(key) || ObjectUtil.isEmpty(value)) {
            log.error("[APN: dashboard page @{}] REST request to search dashboard page [error], dashboard search condition id null, key = [{}], value = [{}]", SecurityUtils.getUserId(), key, JSONUtil.toJsonStr(value));
            throw new CustomParameterizedException("dashboard search dto condition is null, key = [{}], value = [{}]", key, JSONUtil.toJsonStr(value));
        }
        List<String> list = new ArrayList<>();
        Object data = new JSONObject(value).get("data");
        if (data instanceof String) {
            list.add(String.valueOf(data));
            return list;
        }
        if (data instanceof List) {
            list.addAll((Collection<? extends String>) data);
            return list;
        }
        return list;
    }

}
