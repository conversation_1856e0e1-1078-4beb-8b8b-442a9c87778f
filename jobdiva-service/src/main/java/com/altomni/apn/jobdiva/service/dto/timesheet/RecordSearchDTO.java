package com.altomni.apn.jobdiva.service.dto.timesheet;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel
public class RecordSearchDTO {

   private String startDate;
   private String endDate;
   private Long id;
   private Integer lineIndex;
   private Integer receipts;//0 :only download excel,1.only download recipts;2:download all

    private Long assignmentId;
    private String timezone;

}
