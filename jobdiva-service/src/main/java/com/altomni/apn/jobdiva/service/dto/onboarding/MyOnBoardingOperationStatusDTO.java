package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.OperationStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MyOnBoardingOperationStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    private Long id;

    private String s3Key;

    private String documentName;

    private MyOnBoardingSignatureDTO signature;

    private OperationStatus operationStatus;

    private String timezone;

    private List<TagInfoDTO> tagsInfo;
}
