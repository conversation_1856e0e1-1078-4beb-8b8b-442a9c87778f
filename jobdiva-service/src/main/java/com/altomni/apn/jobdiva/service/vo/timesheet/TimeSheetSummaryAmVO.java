package com.altomni.apn.jobdiva.service.vo.timesheet;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

/**
 * A record
 */
@ApiModel(description = "record for week ending")
@Entity
@Data
public class TimeSheetSummaryAmVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @ExcelIgnore
    private Long  id;

    @ApiModelProperty(value = "work date")
    @ColumnWidth(20)
    @ExcelProperty(value = "Week Ending",index = 0,converter = LocalDateExcelDateConverter.class)
    private LocalDate endingDate;

    @ExcelIgnore
    private LocalDate weekStart;

    @ExcelIgnore
    private LocalDate weekEnd;

    @ApiModelProperty(value = "applied date")
    @ColumnWidth(20)
    @ExcelIgnore
    private Instant appliedDate;

    @ExcelProperty(value = "Submitted Time",index = 11)
    @Transient
    private String appliedDateStr;

    @ApiModelProperty(value = "approved date")
    @ColumnWidth(20)
    @ExcelIgnore
    private Instant approvedDate;

    @ExcelProperty(value = "On",index = 14)
    @Transient
    private String approvedDateStr;

    @ApiModelProperty(value = "work hours")
    @ExcelIgnore
    // @ColumnWidth(20)
    // @ExcelProperty(value = "Total(hrs)",index = 5,converter = FloatConverter.class)
    private Float totalHours;

    @ApiModelProperty(value = "regularHours")
    @ColumnWidth(20)
    @ExcelProperty(value = "Regular Hours",index = 5,converter = FloatConverter.class)
    private Float regularHours;

    @ApiModelProperty(value = "OT")
    @ColumnWidth(20)
    @ExcelProperty(value = "OT",index = 6,converter = FloatConverter.class)
    private Float overTime;

    @ApiModelProperty(value = "doubleTime")
    @ColumnWidth(20)
    @ExcelProperty(value = "DT",index = 7,converter = FloatConverter.class)
    private Float doubleTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "Employee Name",index = 1)
    @ApiModelProperty(value = "talentName")
    private String talentName;

    @ExcelIgnore
    private Long talentId;

    @ApiModelProperty(value = "jobTitle")
    @ColumnWidth(20)
    @ExcelProperty(value = "Job Title",index = 4)
    private String jobTitle;

    @ApiModelProperty(value = "job id")
    @ColumnWidth(20)
    @ExcelProperty(value = "Job ID",index = 15)
    private Long  jobId;

    @ApiModelProperty(value = "manager")
    @ColumnWidth(20)
    @ExcelProperty(value = "Manager/Approver",index = 12)
    private String manager;


    @ApiModelProperty(value = "company name")
    @ColumnWidth(20)
    @ExcelProperty(value = "Company",index = 3)
    private String companyName;


    @ApiModelProperty(value = "job start date")
    @ExcelIgnore
    private LocalDate startDate;

    @ApiModelProperty(value = "job end date")
    @ExcelIgnore
    private LocalDate endDate;

    @ApiModelProperty(value = "time sheet type")
    @Convert(converter = TimeSheetTypeConverter.class)
    @ExcelIgnore
    private TimeSheetType sheetType;

    @ApiModelProperty(value = "status")
    @Convert(converter = TimeSheetStatusConverter.class)
    @ColumnWidth(20)
    @ExcelProperty(value = "Status",index = 13,converter = TimeSheetStatusDateConverter.class)
    private TimeSheetStatus status;

    @ApiModelProperty(value = "calculate method")
    @ExcelIgnore
    @Convert(converter = CalculateMethodTypeConverter.class)
    private CalculateMethodType calculateMethod;

    @ApiModelProperty(value = "assignment id")
    @ExcelIgnore
    private Long assignmentId;

    @ExcelIgnore
    @Transient
    private String comments;


    @ExcelIgnore
    private String primaryManager;

    @ExcelIgnore
    private String amApprover;

    @Transient
    @ExcelIgnore
    private Integer weekEnding;

    @ColumnWidth(20)
    @ExcelProperty(value = "AM",index = 16)
    private  String am;

    @ExcelIgnore
    private String amIds;

    @ExcelIgnore
    private Long amApproverId;

    @ExcelIgnore
    private Long tenantId;

    @Convert(converter = AssignmentCategoryTypeConverter.class)
    @ColumnWidth(20)
    @ExcelProperty(value = "Employee Category",index = 2,converter = AssignmentCategoryTypeDataConverter.class)
    private AssignmentCategoryType employmentCategory;

    @Convert(converter = TimeSheetFrequencyTypeConverter.class)
    @ColumnWidth(20)
    @ExcelProperty(value = "Billing Frequency",index = 8,converter = TimeSheetFrequencyTypeDataConverter.class)
    private TimeSheetFrequencyType billingFrequency;

    @Convert(converter = TimeSheetFrequencyTypeConverter.class)
    @ColumnWidth(20)
    @ExcelProperty(value = "Payment Frequency",index = 9,converter = TimeSheetFrequencyTypeDataConverter.class)
    private TimeSheetFrequencyType paymentFrequency;

    @Convert(converter = AssignmentDivisionConverter.class)
    @ColumnWidth(20)
    @ExcelProperty(value = "Assignment Division",index = 10,converter = AssignmentDivisionDataConverter.class)
    private AssignmentDivision assignmentDivision;

    @Transient
    @ExcelIgnore
    private Boolean invoiceFlag = false;

    @ExcelIgnore
    private LocalDate weekEndingDate;

    public TimeSheetSummaryAmVO() {
    }

}




