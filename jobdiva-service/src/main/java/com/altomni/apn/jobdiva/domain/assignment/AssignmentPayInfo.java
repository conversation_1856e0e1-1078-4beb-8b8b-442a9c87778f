package com.altomni.apn.jobdiva.domain.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.EmploymentCategoryType;
import com.altomni.apn.common.domain.enumeration.jobdiva.EmploymentCategoryTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetFrequencyType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetFrequencyTypeConverter;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A record
 */
@Entity
@Data
@Table(name = "assignment_pay_info")
public class AssignmentPayInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;


    @Column(name = "assignment_id")
    private Long assignmentId;

    @Column(name = "is_except")
    private Boolean isExcept;

    @Column(name = "employment_category")
    @Convert(converter = EmploymentCategoryTypeConverter.class)
    private EmploymentCategoryType employmentCategory ;

    @Column(name = "corporation")
    private String corporation;

    @Column(name = "comments")
    private String  comments;

    @Column(name = "frequency", nullable = false)
    @Convert(converter = TimeSheetFrequencyTypeConverter.class)
    private TimeSheetFrequencyType frequency;



}
