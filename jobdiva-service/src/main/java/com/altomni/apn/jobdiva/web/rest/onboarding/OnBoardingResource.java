package com.altomni.apn.jobdiva.web.rest.onboarding;

import cn.hutool.core.convert.Convert;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.constants.ReportConstants;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import com.altomni.apn.common.utils.ExcelUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingProcessHistories;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetUserRepository;
import com.altomni.apn.jobdiva.service.assignment.AssignmentSyncToHrService;
import com.altomni.apn.jobdiva.service.dto.onboarding.*;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsBriefDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackagesDTO;
import com.altomni.apn.jobdiva.service.onboarding.OnBoardingService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * REST controller for OnBoarding .
 */
@Api(tags = {"APN-OnBoarding OnBoarding"})
@RestController
@RequestMapping("/api/v3/onboarding")
public class OnBoardingResource {

    private final Logger log = LoggerFactory.getLogger(OnBoardingResource.class);

    @Resource
    private OnBoardingService onBoardingService;
    @Resource
    private TimeSheetUserRepository timeSheetUserRepository;
    @Resource
    private AssignmentSyncToHrService assignmentSyncToHrService;

    @ApiOperation(value = "Get OnBoarding draft by application id")
    @GetMapping("/processing/drafts/{id}")
    @Timed
    public ResponseEntity<OnBoardingDraftsDTO> getDraft(@ApiParam(value = "talentRecruitmentProcess id", required = true) @Valid @PathVariable Long id) {
        log.info("[APN: OnBoarding processing @{}] REST request to get OnBoarding draft by talentRecruitmentProcess id, application id: {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(onBoardingService.getDraftByTalentRecruitmentProcessId(id), HttpStatus.OK);
    }

    @ApiOperation(value = "Save OnBoarding draft by application id")
    @PutMapping("/processing/drafts/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> saveDraft(@ApiParam(value = "application id", required = true) @Valid @PathVariable Long id
        , @Valid @RequestBody OnBoardingDraftsDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to save OnBoarding draft, application id : {}, {}", SecurityUtils.getUserId(), id, dto);
        dto.setTalentRecruitmentProcessId(id);
        onBoardingService.saveDraft(dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Delete OnBoarding draft by application id")
    @DeleteMapping("/processing/drafts/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteDraft(@ApiParam(value = "application id", required = true) @Valid @PathVariable Long id) {
        log.info("[APN: OnBoarding processing @{}] REST request to delete OnBoarding draft, application id : {}", SecurityUtils.getUserId(), id);
        onBoardingService.deleteDraft(id);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Get all packages")
    @GetMapping("/packages/{talentRecruitmentProcessId}")
    @Timed
    public ResponseEntity<List<OnBoardingPackagesDTO>> getAllPackages(@ApiParam(value = "application id", required = true) @Valid @PathVariable Long talentRecruitmentProcessId) {
        log.info("[APN: OnBoarding Settings @{}] REST get all packages , {} ", SecurityUtils.getUserId(), talentRecruitmentProcessId);
        return new ResponseEntity<>(onBoardingService.getAllPackages(talentRecruitmentProcessId), HttpStatus.OK);
    }

    @ApiOperation(value = "Find documents in package by application id and package id")
    @GetMapping("/packages-documents/{talentRecruitmentProcessId}/{id}")
    @Timed
    public ResponseEntity<List<OnBoardingPackageDocumentsDTO>> findDocumentsByPackageId(@ApiParam(value = "application id", required = true) @Valid @PathVariable Long talentRecruitmentProcessId,
                                                                                        @ApiParam(value = "package id", required = true) @Valid @PathVariable Long id) {
        log.info("[APN: OnBoarding Settings @{}] REST find documents in package by application id {} and  package id: {}", SecurityUtils.getUserId(), talentRecruitmentProcessId, id);
        return new ResponseEntity<>(onBoardingService.findDocumentsByPackageId(talentRecruitmentProcessId, id), HttpStatus.OK);
    }

    @ApiOperation(value = "Find additional document list by documentIds and name")
    @PostMapping("/documents")
    @Timed
    public ResponseEntity<List<OnBoardingPackageDocumentsDTO>> findAllDocuments(@Valid @RequestBody SearchDocumentsDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to find additional document list by documentIds and name :{} ", SecurityUtils.getUserId(), dto);
        return new ResponseEntity<>(onBoardingService.findAllDocuments(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "Get OnBoarding history list")
    @PostMapping("/processing/histories/{id}")
    @Timed
    public ResponseEntity<List<OnBoardingProcessesHistoriesDTO>> getHistories(@ApiParam(value = "application id", required = true) @Valid @PathVariable Long id,
                                                                              @RequestBody SearchDocumentsDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to get OnBoarding history list :{},{} ", SecurityUtils.getUserId(), id, dto.getTimezone());
        return new ResponseEntity<>(onBoardingService.getHistories(id, dto.getTimezone()), HttpStatus.OK);
    }

    @ApiOperation(value = "Get OnBoarding completions list")
    @PostMapping("/processing/completions/{id}")
    @Timed
    public ResponseEntity<List<OnBoardingProcessesCompletionsDTO>> getCompletions(@ApiParam(value = "application id", required = true) @Valid @PathVariable Long id
    , @RequestBody SearchDocumentsDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to get OnBoarding completions list :{} ", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(onBoardingService.getCompletions(id, dto.getTimezone()), HttpStatus.OK);
    }

    @ApiOperation(value = "download OnBoarding completions list")
    @PostMapping("/processing/completions/download")
    @Timed
    public ResponseEntity<Void> downloadCompletions(@RequestBody  DownloadCompletionDTO downloadCompletionDTO, HttpServletResponse response) {
        log.info("[APN: OnBoarding processing @{}] REST request to download OnBoarding completions list :{} ", SecurityUtils.getUserId(), downloadCompletionDTO);
        onBoardingService.downloadCompletions(downloadCompletionDTO, response);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    @ApiOperation(value = "Save approvement and send email")
    @PostMapping("/processing/approvement")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> saveApproval(HttpServletRequest request, @Valid @RequestBody OnBoardingApprovementDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to save approvement :{} ", SecurityUtils.getUserId(), dto);
        onBoardingService.saveApproval(request, dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Remind candidate and send email")
    @PostMapping("/processing/reminding/{talentRecruitmentProcessId}")
    @Timed
    public ResponseEntity<Void> remindCandidate(@ApiParam(value = "application id", required = true) @Valid @PathVariable Long talentRecruitmentProcessId) {
        log.info("[APN: OnBoarding processing @{}] REST request to remind candidate :{} ", SecurityUtils.getUserId(), talentRecruitmentProcessId);
        onBoardingService.remindCandidate(talentRecruitmentProcessId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Download completion documents list")
    @PostMapping("/processing/downloading/{talentRecruitmentProcessId}")
    @Timed
    public void exportCompletionDocumentsByExcel(HttpServletResponse response,@ApiParam(value = "application id", required = true) @Valid @PathVariable Long talentRecruitmentProcessId, @RequestBody CompleteDocumentsDownloadDTO dto) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        log.info("[APN: OnBoarding processing @{}] REST request to download completion documents list by excel . reportParam:{},{}", SecurityUtils.getUserId(), talentRecruitmentProcessId, dto);
        ExcelUtil.getResource(response, onBoardingService.getDownloadFileName(talentRecruitmentProcessId, dto.getTimezone()), ReportConstants.ONBOARDING_COMPLETION_DOCUMENTS_HEADERS
            , ReportConstants.ONBOARDING_COMPLETION_DOCUMENTS_FIELDS, onBoardingService.getDownloadCompletionDocuments(talentRecruitmentProcessId, dto.getDocuments())
            , null, null,null);
    }

    @ApiOperation(value = "Get my onBoarding portals list")
    @PostMapping("/processing/portals/list")
    @Timed
    public ResponseEntity<MyOnBoardingPortalsDTO> getMyOnBoardingPortals(@Valid @RequestBody SearchDocumentsDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to get my onBoarding portals list .", SecurityUtils.getUserId());
        return new ResponseEntity<>(onBoardingService.getMyOnBoardingPortals(dto.getTimezone()), HttpStatus.OK);
    }

    @ApiOperation(value = "Get onBoarding document tags info")
    @GetMapping("/processing/portals/document/{s3key}/tagsInfo")
    @Timed
    public ResponseEntity<List<TagInfoDTO>> getTagsInfo(@ApiParam(value = "s3key", required = true) @Valid @PathVariable String s3key) {
        log.info("[APN: OnBoarding processing @{}] REST request to onBoarding document tags info .", SecurityUtils.getUserId());
        return new ResponseEntity<>(onBoardingService.getTagsInfo(s3key), HttpStatus.OK);
    }

    @ApiOperation(value = "Get my onBoarding portals history list")
    @PostMapping("/processing/portals/histories")
    @Timed
    public ResponseEntity<List<MyOnBoardingPortalsHistoriesDTO>> getMyOnBoardingPortalsHistories(@Valid @RequestBody SearchDocumentsDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to get my onBoarding portals history list .", SecurityUtils.getUserId());
        return new ResponseEntity<>(onBoardingService.getMyOnBoardingPortalsHistories(dto.getTimezone()), HttpStatus.OK);
    }

    @ApiOperation(value = "Operate My onBoarding list")
    @PostMapping("/processing/portals")
    @Timed
    public ResponseEntity<Void> updateOperationStatus(HttpServletRequest request, @Valid @RequestBody MyOnBoardingOperationStatusDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to operate My onBoarding list . {}", SecurityUtils.getUserId(), dto);
        onBoardingService.updateOperationStatus(request, dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Completed documents list upload document by AM")
    @PostMapping("/processing/approvement/upload-by-am")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> uploadDocumentByAM(HttpServletRequest request, @Valid @RequestBody MyOnBoardingOperationStatusDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to upload document by AM :{} ", SecurityUtils.getUserId(), dto);
        onBoardingService.uploadDocumentByAM(request, dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Preview selected documents before start onBoarding process")
    @PostMapping("/processing/preview")
    @Timed
    public ResponseEntity<List<OnBoardingProcessHistories>> getPreviewDocuments(@Valid @RequestBody OnBoardingStartProcessDTO dto) throws InterruptedException{
        log.info("[APN: OnBoarding processing @{}] REST request to preview selected documents before start onBoarding process . {}", SecurityUtils.getUserId(), dto);
        return new ResponseEntity<>(onBoardingService.getPreviewDocuments(dto), HttpStatus.OK);
    }

    @ApiOperation(value = "Get start onBoarding email info")
    @PostMapping("/processing/emails/{talentRecruitmentProcessId}")
    @Timed
    public ResponseEntity<OnBoardingStartProcessEmailDTO> getStartEmailContents(@ApiParam(value = "application id", required = true) @Valid @PathVariable Long talentRecruitmentProcessId,
                                                                                @Valid @RequestBody List<Long> documents) {
        log.info("[APN: OnBoarding processing @{}] REST request to get start onBoarding email info . {} ,{}", SecurityUtils.getUserId(), talentRecruitmentProcessId, documents);
        return new ResponseEntity<>(onBoardingService.getStartEmailInfo(talentRecruitmentProcessId, documents), HttpStatus.OK);
    }

    @ApiOperation(value = "Start onBoarding application send email and create account")
    @PostMapping("/processing/emails")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> startProcessAndSendEmail(HttpServletRequest request,
                                                         @Valid @RequestParam Long talentRecruitmentProcessId,
                                                         @RequestParam(required = false) Long packageId,
                                                         @RequestParam(required = false) String packageName,
                                                         @RequestParam String documents,
                                                         @RequestParam String to,
                                                         @RequestParam(required = false) List<String> bcc,
                                                         @RequestParam(required = false) List<String> cc,
                                                         @RequestParam String subject,
                                                         @RequestParam String htmlContents,
                                                         @RequestParam(required = false) List<String> links,
                                                         @RequestParam(required = false) List<MultipartFile> files,
                                                         @RequestParam String timeZone) throws InterruptedException{
        OnBoardingStartProcessDTO dto = new OnBoardingStartProcessDTO(talentRecruitmentProcessId, packageId, packageName, JSONUtil.toList(JSONUtil.parseArray(documents), OnBoardingDocumentsBriefDTO.class), new OnBoardingStartProcessEmailDTO(to, cc, bcc, subject, htmlContents, links, files), timeZone);
        log.info("[APN: OnBoarding processing @{}] REST request to start onBoarding send email and create account . {}", SecurityUtils.getUserId(), dto);
        onBoardingService.startProcessAndSendEmail(request, dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Get portal email info")
    @GetMapping("/processing/portal/emails/{talentId}")
    @Timed
    public ResponseEntity<OnBoardingStartProcessEmailDTO> getPortalEmailContents(@ApiParam(value = "talent id", required = true) @Valid @PathVariable Long talentId) {
        log.info("[APN: OnBoarding processing @{}] REST request to get portal email info . {}", SecurityUtils.getUserId(), talentId);
        return new ResponseEntity<>(onBoardingService.getPortalEmailInfo(talentId), HttpStatus.OK);
    }

    @ApiOperation(value = "Send portal email")
    @PostMapping("/processing/portal/emails/{talentId}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> sendPortalEmail(@ApiParam(value = "talent id", required = true) @Valid @PathVariable Long talentId,
                                                @RequestParam String to,
                                                @RequestParam(required = false) List<String> bcc,
                                                @RequestParam(required = false) List<String> cc,
                                                @RequestParam String subject,
                                                @RequestParam String htmlContents,
                                                @RequestParam(required = false) List<String> links,
                                                @RequestParam(required = false) List<MultipartFile> files) {
        OnBoardingStartProcessEmailDTO dto = new OnBoardingStartProcessEmailDTO(to, cc, bcc, subject, htmlContents, links, files);
        log.info("[APN: OnBoarding processing @{}] REST request to send portal email . {}", SecurityUtils.getUserId(), dto);
        onBoardingService.sendPortalEmail(talentId, dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Portal reset password")
    @PostMapping("/processing/accounts/reseting/{talentId}")
    @Timed
    public ResponseEntity<Void> resetPortalAccount(@ApiParam(value = "talent id", required = true) @Valid @PathVariable Long talentId
        , @Valid @RequestBody ResetingPortalAccountDTO dto) {
        log.info("[APN: OnBoarding processing @{}] REST request to reset portal account .{}, {}", SecurityUtils.getUserId(), talentId, dto);
        onBoardingService.resetPortalAccount(talentId, dto);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Get talent jobDiva account status info")
    @GetMapping("/processing/portal/{talentId}")
    @Timed
    public ResponseEntity<ResetingPortalAccountDTO> getTalentAccountStatusInfo(@ApiParam(value = "talent id", required = true) @Valid @PathVariable Long talentId) {
        log.info("[APN: OnBoarding processing @{}] REST request to get talent jobDiva account status info . {}", SecurityUtils.getUserId(), talentId);
        return new ResponseEntity<>(onBoardingService.getTalentAccountStatusInfo(talentId), HttpStatus.OK);
    }

    /**
     * only use for talent-service
     */
    @ApiOperation(value = "Get TimeSheetUser info by talentId")
    @GetMapping("/timesheet-user/talent/{talentId}")
    @Timed
    public ResponseEntity<TimeSheetUserDTO> getTimeSheetUser(@PathVariable("talentId") Long talentId) {
        log.info("[APN: OnBoarding processing @{}] REST request to get timeSheetUser info by talentId . {}", SecurityUtils.getUserId(), talentId);
        return new ResponseEntity<>(Convert.convert(TimeSheetUserDTO.class, timeSheetUserRepository.findByUidLikeAndUserType(talentId, TimeSheetUserType.TALENT.toDbValue())), HttpStatus.OK);
    }

    @ApiOperation(value = "Save TimeSheetUser")
    @PostMapping("/timesheet-user/save")
    @Timed
    public ResponseEntity<Boolean> saveTimeSheetUser(@RequestBody TimeSheetUserDTO timeSheetUser) {
        log.info("[APN: OnBoarding processing @{}] REST request to save TimeSheetUser . {}", SecurityUtils.getUserId(), timeSheetUser);
        TimeSheetUser save = Convert.convert(TimeSheetUser.class, timeSheetUser);
        TimeSheetUser exist = timeSheetUserRepository.findByUsername(timeSheetUser.getUsername());
        if(exist != null && !exist.getId().equals(save.getId())) {
            return new ResponseEntity<>(false, HttpStatus.OK);
        }
        timeSheetUserRepository.save(save);
        assignmentSyncToHrService.buildTimeSheetUserSyncToHrMq(save);
        return new ResponseEntity<>(true, HttpStatus.OK);
    }

}
