package com.altomni.apn.jobdiva.repository.invoice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.GroupInvoiceStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceStatusType;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorInvoiceCreateDTO;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorInvoiceSearchByApplicationDTO;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorInvoiceSearchDTO;
import com.altomni.apn.jobdiva.service.dto.invoice.ContractorInvoiceTimesheetDTO;
import com.altomni.apn.jobdiva.service.vo.invoice.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class ContractorInvoiceNativeRepository {

    @Resource
    private EntityManager entityManager;

    @Resource
    CommonRedisService commonRedisService;

    private static final String LOCK_KEY = "contractor_invoice_lock";

    private static final String LOCK_SUCCESS = "OK";
    private static final String SET_IF_NOT_EXIST = "NX";
    private static final String SET_WITH_EXPIRE_TIME = "EX";

    private boolean getRedisLock(String value, Long expireTime) {

        String result = commonRedisService.set(LOCK_KEY, value, SET_IF_NOT_EXIST,
                SET_WITH_EXPIRE_TIME, expireTime);
        if (LOCK_SUCCESS.equals(result)) {
            return true;
        }
        return false;
    }

    public void unlock(String lockValue) {
        // 确保是自己加的锁，避免误解锁
        if (lockValue.equals(commonRedisService.get(LOCK_KEY))) {
            commonRedisService.delete(LOCK_KEY);
        }
    }

    /**
     * 查询timesheet数据用于生成invoice
     * Query timesheet data for generating invoices
     *
     * @param dto
     * @param flag 1-timesheet 2-expense
     * @return
     */
    @Transactional(readOnly = true)
    public List<ContractorInvoiceTimesheetDTO> queryTimesheetAndExpenseList(ContractorInvoiceCreateDTO dto, Integer flag) {
        String uuid = UUID.randomUUID().toString();
        try {
            while (true) {
                if (getRedisLock(uuid, 600L)) {
                    StringBuilder dataSql = new StringBuilder("select t.tenant_id as tenantId,t.talent_id as talentId,\n" +
                            "tal.full_name as talentName,\n" +
                            "t.job_id as jobId,t.job_title as jobTitle,t.assignment_id as assignmentId,\n" +
                            "t.work_date as weekEndingDate,t.week_end as weekEnd,t.week_start as weekStart, \n" +
                            "t.company_id as companyId,t.company_name as companyName,\n" +
                            "ass.contact_id as companyContactId,ct.full_name as companyContactName,\n" +
                            "t.approved_date as approvalDate,  \n" +
                            "case when t.am_approver is not null then t.am_approver \n" +
                            "else t.primary_manager end as approverName,\n" +
                            "case when t.am_approver is not null then t.am_approver_id \n" +
                            "else t.primary_manager_id end as approverId,cast(t.assignment_division as char) as assignmentDivision,al.country_code as countryCode,\n");
                    if (flag.equals(InvoiceType.REGULAR.toDbValue())) {
                        dataSql.append(" t.regular_hours as regularHours,t.over_time as overTime,t.double_time as doubleTime");
                        dataSql.append(" from time_sheet_week_ending_record t");
                    } else {
                        dataSql.append(" t.cost,t.submitted_date as expenseSubmitDate, t.expense_index as expenseIndex ");
                        dataSql.append(" from time_sheet_expense_week_ending_record t");
                    }

                    dataSql.append(" left join assignment_location al on al.assignment_id = t.assignment_id " +
                            " left join talent tal on tal.id = t.talent_id\n" +
                            " left join assignment_bill_info ass on ass.assignment_id = t.assignment_id\n" +
                            " left join company_sales_lead_client_contact con on con.id = ass.contact_id left join talent ct on ct.id = con.talent_id ");
                    dataSql.append(" left join timesheet_talent_assignment tta on tta.id = t.assignment_id \n" +
                            " inner join start s on s.id = tta.start_id\n" +
                            " inner join start_client_info sci on sci.start_id = s.id\n" +
                            " left join invoice_type_config itc on itc.tenant_id = s.tenant_id and itc.id = sci.invoice_type_id and itc.status=1 ");
                    dataSql.append(" where t.`status`=4 and itc.label='oversea contract' ");
                    dataSql.append(assemblyFailedQueryCondition(dto));

                    Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

                    assemblyFailedQueryParam(dataQuery, null, dto);

                    dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceTimesheetDTO.class));

                    return dataQuery.getResultList();

                }
                try {
                    Thread.sleep(1000); // 等待一段时间再重试
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 恢复中断状态
                }
            }
        } catch (Exception e) {
            log.error("queryTimesheetAndExpenseList error:" + e);
        } finally {
            unlock(uuid);
        }
        return new ArrayList<>();
    }

    /**
     * 查询是否存在无效数据
     * Query for invalid data
     *
     * @param dto
     * @param flag true表示group invoice
     * @return
     */
    @Transactional(readOnly = true)
    public List<ContractorInvoiceFailedVO> queryFailedTimesheetAndExpenseList(ContractorInvoiceCreateDTO dto, boolean flag) {
        entityManager.clear();

        StringBuilder dataSql = new StringBuilder();
        getFailedSql(dto, dataSql, flag);

        if (dataSql.length() == 0) {
            throw new CustomParameterizedException(" invoice type is null");
        }

        dataSql.append(" order by weekEndingDate desc limit 0,10");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        assemblyFailedQueryParam(dataQuery, null, dto);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceFailedVO.class));

        return dataQuery.getResultList();
    }

    /**
     * 组装失败的数据sql
     * SQL for failed data assembly
     *
     * @param dto
     * @param dataSql
     */
    private void getFailedSql(ContractorInvoiceCreateDTO dto, StringBuilder dataSql, boolean flag) {
        if (dto.getInvoiceTypeList().size() == 2) {
            dataSql.append(failedTimesheetSQL(flag));
            dataSql.append(assemblyFailedQueryCondition(dto));
            dataSql.append(" UNION ALL");
            dataSql.append(failedExpenseSQL(flag));
            dataSql.append(assemblyFailedQueryCondition(dto));

        } else if (dto.getInvoiceTypeList().get(0).name().equals(InvoiceType.EXPENSE.name())) {
            dataSql.append(failedExpenseSQL(flag));
            dataSql.append(assemblyFailedQueryCondition(dto));
        } else if (dto.getInvoiceTypeList().get(0).name().equals(InvoiceType.REGULAR.name())) {
            dataSql.append(failedTimesheetSQL(flag));
            dataSql.append(assemblyFailedQueryCondition(dto));
        }
    }

    private String failedTimesheetSQL(boolean flag) {
        StringBuilder str = new StringBuilder(" select t.work_date as weekEndingDate,tal.full_name as employeeName,\n" +
                "t.job_title as jobTitle,cast(t.assignment_division as char) as assignmentDivision,\n" +
                "t.company_name as company,t.am,\n" +
                "'1' as 'type',cast(t.status as char) as status,t.record_id as id,\n" +
                "t.start_date as startDate,t.end_date as endDate,t.week_start as weekStart,t.week_end as weekEnd,case when t.am_approver is not null then t.am_approver \n" +
                "when t.manager is not null then t.manager \n" +
                "else t.primary_manager end as approver \n" +
                "from time_sheet_week_ending_record t \n" +
                "left join talent tal on tal.id = t.talent_id  ");
        if (flag) {
            str.append(" left join t_contractor_invoice ci on ci.week_ending_date = t.work_date \n" +
                    " and ci.talent_id =t.talent_id  and ci.invoice_type=2 and ci.job_id = t.job_id and ci.invoice_status !=3 \n");
            str.append(" where ci.id is null ");
        } else {
            str.append(" WHERE t.status in (0,1,2,5) ");
        }
        str.append(" and t.assignment_status = 1 ");
        return str.toString();
    }

    private String failedExpenseSQL(boolean flag) {
        StringBuilder str = new StringBuilder(" select t.work_date as weekEndingDate,tal.full_name as employeeName,\n" +
                "t.job_title as jobTitle,cast(t.assignment_division as char) as assignmentDivision,\n" +
                "t.company_name as company,t.am,\n" +
                "'2' as 'type',cast(t.status as char) as status,t.record_id as id,\n" +
                "t.start_date as startDate,t.end_date as endDate,t.week_start as weekStart,t.week_end as weekEnd,case when t.am_approver is not null then t.am_approver \n" +
                "when t.manager is not null then t.manager \n" +
                "else t.primary_manager end as approver \n" +
                "from \n" +
                "time_sheet_expense_week_ending_record t \n" +
                "left join talent tal on tal.id = t.talent_id ");
        if (flag) {
            str.append(" left join t_contractor_invoice ci on ci.week_ending_date = t.work_date \n" +
                    " and ci.talent_id =t.talent_id  and ci.invoice_type=1 and ci.job_id = t.job_id and ci.invoice_status !=3 \n");
            str.append(" where ci.id is null and t.status in(1,4)");
        } else {
            str.append(" WHERE status = 1 ");
        }
        return str.toString();
    }

    /**
     * 拼装失败数据的查询条件
     * Assembly query criteria
     *
     * @param dto
     * @return
     */
    private String assemblyFailedQueryCondition(ContractorInvoiceCreateDTO dto) {
        StringBuilder whereSql = new StringBuilder();

        if (dto.getFrom() != null && dto.getTo() != null) {
            whereSql.append(" and t.work_date between :startDate and :to ");
        }

        if (dto.getCompanyId() != null) {
            whereSql.append(" and t.company_id = :companyId ");
        }

        if (null != dto.getAssignmentDivisionList()) {
            whereSql.append(" and t.assignment_division in ( :assignmentDivision )");
        }

        if (null != dto.getTenantId()) {
            whereSql.append(" and t.tenant_id = :tenantId ");
        }

        if (null != dto.getEmployeeIdList()) {
            whereSql.append(" and t.talent_id in ( :talentId )");
        }

        return whereSql.toString();
    }

    /**
     * 设置参数
     * set param
     *
     * @param dto
     * @return
     */
    private void assemblyFailedQueryParam(Query dataQuery, Query countQuery, ContractorInvoiceCreateDTO dto) {

        if (dto.getFrom() != null && dto.getTo() != null) {

            dataQuery.setParameter("startDate", dto.getFrom());
            dataQuery.setParameter("to", dto.getTo());

            if (null != countQuery) {
                countQuery.setParameter("startDate", dto.getFrom());
                countQuery.setParameter("to", dto.getTo());
            }
        }

        if (dto.getCompanyId() != null) {
            dataQuery.setParameter("companyId", dto.getCompanyId());
            if (null != countQuery) {
                countQuery.setParameter("companyId", dto.getCompanyId());
            }
        }

        if (dto.getAssignmentDivisionList() != null) {

            dataQuery.setParameter("assignmentDivision", dto.getAssignmentDivisionList().stream().mapToInt(AssignmentDivision::toDbValue).boxed().collect(Collectors.toList()));
            if (null != countQuery) {
                countQuery.setParameter("assignmentDivision", dto.getAssignmentDivisionList().stream().mapToInt(AssignmentDivision::toDbValue).boxed().collect(Collectors.toList()));
            }
        }

        if (null != dto.getTenantId()) {
            dataQuery.setParameter("tenantId", dto.getTenantId());
            if (null != countQuery) {
                countQuery.setParameter("tenantId", dto.getTenantId());
            }
        }

        if (dto.getEmployeeIdList() != null) {

            dataQuery.setParameter("talentId", dto.getEmployeeIdList());
            if (null != countQuery) {
                countQuery.setParameter("talentId", dto.getEmployeeIdList());
            }
        }
    }

    /**
     * Paging Query Data
     *
     * @param dto
     * @param pageable
     * @return
     */
    @Transactional(readOnly = true)
    public Page<ContractorInvoiceListVO> searchInvoiceList(ContractorInvoiceSearchDTO dto, Pageable pageable) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("select id,invoiceNumber,invoiceDate,createdDate,groupInvoiceNumber,employeeId,employeeName,\n" +
                "invoiceType,invoiceStatus,invoiceAmount,billingCompany,companyId,assignmentDivision,\n" +
                "sentBy,sentOn,groupInvoiceStatus,groupInvoiceId," +
                "case when tab.currency_tim is null then tab.currency_ex else tab.currency_tim end as currency from ( \n" +
                "select t.id,t.invoice_number as invoiceNumber,t.invoice_date as invoiceDate,\n" +
                "t.created_date as createdDate\n" +
                ",t.group_invoice_number as groupInvoiceNumber,t.talent_id as employeeId\n" +
                ",t.talent_name as employeeName,\n" +
                "cast(t.invoice_type as char) as invoiceType,\n" +
                "cast(t.invoice_status as char) as invoiceStatus,\n" +
                "t.total_amount as invoiceAmount,\n" +
                "t.company_name as billingCompany,t.company_id as companyId,\n" +
                "cast(t.assignment_division as char) as assignmentDivision,\n" +
                "i.operated_by_name as sentBy,i.operated_date as sentOn,cast(i.group_invoice_status as char) as groupInvoiceStatus, i.id as groupInvoiceId,\n" +
                " (select c.name from t_invoice_timesheet_info z,enum_currency c  where c.id = z.currency_type and invoice_id = t.id limit 1) as currency_tim,\n" +
                " (select c.name from t_invoice_expense_info z,enum_currency c  where c.id = z.currency_type and invoice_id = t.id limit 1)  as currency_ex \n" +
                " from t_contractor_invoice t\n" +
                " left join t_group_invoice i on i.group_number = t.group_invoice_number and i.STATUS=1 and i.tenant_id = :tenantId ");
        String whereSql = assemblyQueryCondition(dto);

        dataSql.append(whereSql);

        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (!Objects.isNull(order)) {
            if (order.getProperty().equals("operated_date")) {
                dataSql.append(" order by i." + order.getProperty() + " " + order.getDirection());
            } else if (order.getProperty().equals("talent_name") || order.getProperty().equals("company_name")) {
                dataSql.append(" order by CONVERT(t." + order.getProperty() + "  USING GBK) " + order.getDirection());
            } else {
                dataSql.append(" order by t." + order.getProperty() + " " + order.getDirection());
            }
        } else {
            dataSql.append(" order by t.invoice_date asc");
        }

        dataSql.append(") tab ");

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(countSql);

        assemblyQueryParam(dataQuery, countQuery, dto);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceListVO.class));
        dataQuery.setFirstResult((int) pageable.getOffset());
        dataQuery.setMaxResults(pageable.getPageSize());

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<ContractorInvoiceListVO> invoiceListVOS = total > pageable.getOffset() ? dataQuery.getResultList() : new ArrayList<>();
        return new PageImpl<>(invoiceListVOS, pageable, total);
    }

    /**
     * 拼装查询条件
     * Assembly query criteria
     *
     * @param dto
     * @return
     */
    private String assemblyQueryCondition(ContractorInvoiceSearchDTO dto) {

        StringBuilder whereSql = new StringBuilder(" where 1=1");
        if (dto.getInvoiceTypeList() != null) {
            whereSql.append(" and t.invoice_type in (:invoiceType)");
        }

        if (StringUtils.isNotBlank(dto.getInvoiceNumber())) {
            whereSql.append(" and t.invoice_number = :invoiceNumber");
        }

        if (null != dto.getGroupInvoiceNumber() && !dto.getGroupInvoiceNumber().isEmpty()) {
            whereSql.append(" and t.group_invoice_number in ( :groupInvoiceNumber )");
        }

        if (null != dto.getEmployeeId() && !dto.getEmployeeId().isEmpty()) {
            whereSql.append(" and t.talent_id in ( :talentId )");
        }

        if (null != dto.getInvoiceDateStart() && null != dto.getInvoiceDateEnd()) {
            whereSql.append(" and t.invoice_date between :invoiceDateStart and :invoiceDateEnd ");
        }

        if (null != dto.getCreatedDateStart() && null != dto.getCreatedDateEnd()) {
            whereSql.append(" and t.created_date between :createdDateStart and :createdDateEnd ");
        }

        if (dto.getCompanyId() != null) {
            whereSql.append(" and t.company_id in ( :companyId )");
        }

        if (dto.getContactId() != null) {
            whereSql.append(" and t.company_contact_id in ( :companyContactId )");
        }

        if (null != dto.getInvoiceStatusList()) {
            whereSql.append(" and t.invoice_status in ( :invoiceStatus )");
        }

        if (null != dto.getAssignmentDivisionList()) {
            whereSql.append(" and t.assignment_division in ( :assignmentDivision )");
        }

        if (null != dto.getTenantId()) {
            whereSql.append(" and t.tenant_id = :tenantId ");
        }

        return whereSql.toString();
    }

    /**
     * 设置参数
     * set param
     *
     * @param dto
     * @return
     */
    private void assemblyQueryParam(Query dataQuery, Query countQuery, ContractorInvoiceSearchDTO dto) {

        if (dto.getInvoiceTypeList() != null) {
            dataQuery.setParameter("invoiceType", dto.getInvoiceTypeList().stream().mapToInt(InvoiceType::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoiceType", dto.getInvoiceTypeList().stream().mapToInt(InvoiceType::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (StringUtils.isNotBlank(dto.getInvoiceNumber())) {
            dataQuery.setParameter("invoiceNumber", dto.getInvoiceNumber());
            countQuery.setParameter("invoiceNumber", dto.getInvoiceNumber());
        }

        if (null != dto.getGroupInvoiceNumber() && !dto.getGroupInvoiceNumber().isEmpty()) {
            dataQuery.setParameter("groupInvoiceNumber", dto.getGroupInvoiceNumber());
            countQuery.setParameter("groupInvoiceNumber", dto.getGroupInvoiceNumber());
        }

        if (null != dto.getEmployeeId() && !dto.getEmployeeId().isEmpty()) {
            dataQuery.setParameter("talentId", dto.getEmployeeId());
            countQuery.setParameter("talentId", dto.getEmployeeId());
        }

        if (null != dto.getInvoiceDateStart() && null != dto.getInvoiceDateEnd()) {
            dataQuery.setParameter("invoiceDateStart", dto.getInvoiceDateStart());
            countQuery.setParameter("invoiceDateStart", dto.getInvoiceDateStart());
            dataQuery.setParameter("invoiceDateEnd", dto.getInvoiceDateEnd());
            countQuery.setParameter("invoiceDateEnd", dto.getInvoiceDateEnd());
        }

        if (null != dto.getCreatedDateStart() && null != dto.getCreatedDateEnd()) {
            dataQuery.setParameter("createdDateStart", dto.getCreatedDateStart());
            countQuery.setParameter("createdDateStart", dto.getCreatedDateStart());
            dataQuery.setParameter("createdDateEnd", dto.getCreatedDateEnd());
            countQuery.setParameter("createdDateEnd", dto.getCreatedDateEnd());
        }

        if (dto.getCompanyId() != null) {
            dataQuery.setParameter("companyId", dto.getCompanyId());
            countQuery.setParameter("companyId", dto.getCompanyId());
        }

        if (dto.getContactId() != null) {
            dataQuery.setParameter("companyContactId", dto.getContactId());
            countQuery.setParameter("companyContactId", dto.getContactId());
        }

        if (dto.getInvoiceStatusList() != null) {
            dataQuery.setParameter("invoiceStatus", dto.getInvoiceStatusList().stream().mapToInt(InvoiceStatusType::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoiceStatus", dto.getInvoiceStatusList().stream().mapToInt(InvoiceStatusType::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (dto.getAssignmentDivisionList() != null) {
            dataQuery.setParameter("assignmentDivision", dto.getAssignmentDivisionList().stream().mapToInt(AssignmentDivision::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("assignmentDivision", dto.getAssignmentDivisionList().stream().mapToInt(AssignmentDivision::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (null != dto.getTenantId()) {
            dataQuery.setParameter("tenantId", dto.getTenantId());
            if (null != countQuery) {
                countQuery.setParameter("tenantId", dto.getTenantId());
            }
        }
    }

    @Transactional(readOnly = true)
    public Page<ContractorInvoiceFailedVO> searchFailedTimesheetAndExpenseInfo(ContractorInvoiceCreateDTO dto, Pageable pageable, boolean flag) {
        entityManager.clear();

        StringBuilder dataSql = new StringBuilder();
        getFailedSql(dto, dataSql, flag);

        if (dataSql.length() == 0) {
            throw new CustomParameterizedException(" invoice type is null");
        }

        dataSql.append(" order by weekEndingDate desc");

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(countSql);

        assemblyFailedQueryParam(dataQuery, countQuery, dto);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceFailedVO.class));
        dataQuery.setFirstResult((int) pageable.getOffset());
        dataQuery.setMaxResults(pageable.getPageSize());

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<ContractorInvoiceFailedVO> contractorInvoiceFailedVOS = total > pageable.getOffset() ? dataQuery.getResultList() : new ArrayList<>();
        return new PageImpl<>(contractorInvoiceFailedVOS, pageable, total);
    }


    /**
     * 根据invoice id 查询expense数据
     *
     * @param invoiceId
     * @return
     */
    @Transactional(readOnly = true)
    public List<ContractorInvoiceExpenseVO> findExpenseAllByInvoiceId(BigInteger invoiceId) {
        StringBuilder dataSql = new StringBuilder("select cast(t.expense_category as char) as expenseCategory,\n" +
                "                date_format(t.expense_date,'%m/%d/%Y') as expenseDate,t.expense_amount as expenseMoney,\n" +
                "                t.week_day as weekDay,c.name as currency\n" +
                "                from t_invoice_expense_info t\n" +
                "                left join enum_currency c on c.id = t.currency_type\n" +
                "where t.invoice_id= :invoiceId ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("invoiceId", invoiceId);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceExpenseVO.class));
        return dataQuery.getResultList();
    }

    /**
     * 根据invoice id 查询expense category 数据
     *
     * @param invoiceId
     * @return
     */
    @Transactional(readOnly = true)
    public List<ContractorInvoiceExpenseVO> findExpenseCategoryByInvoiceId(BigInteger invoiceId) {
        StringBuilder dataSql = new StringBuilder("select cast(t.expense_category as char) as expenseCategory\n" +
                "                from t_invoice_expense_info t\n" +
                "where t.invoice_id= :invoiceId and t.expense_category is not null group by t.expense_category");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("invoiceId", invoiceId);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceExpenseVO.class));
        return dataQuery.getResultList();
    }

    /**
     * 根据invoice id 查询expense数据
     *
     * @param invoiceId
     * @return
     */
    @Transactional(readOnly = true)
    public List<ContractorInvoiceTimesheetVO> findTimesheetAllByInvoiceId(BigInteger invoiceId) {
        StringBuilder dataSql = new StringBuilder("select t.id,t.quantity,cast(t.quantity_type as char) as quantityType,t.item_description as itemDescription ,\n" +
                "cast(t.bill_rate as char) as billRate,t.unit,t.total_amount as totalAmount,c.name as currency\n" +
                "from t_invoice_timesheet_info t\n" +
                "left join enum_currency c on c.id = t.currency_type\n" +
                "where t.invoice_id= :invoiceId order by t.quantity_type asc");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("invoiceId", invoiceId);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceTimesheetVO.class));
        return dataQuery.getResultList();
    }

    /**
     * 查询公司地址和名称
     *
     * @param startId
     * @return
     */
    @Transactional(readOnly = true)
    public Map<String, Object> findCompanyAddressAndNameByStartId(BigInteger startId) {
        StringBuilder dataSql = new StringBuilder();
        dataSql.append("""
                select t.client_name,t.client_address,t.client_location,
                case when tc.label ='china fte' then ccii.client_name else cici.client_name end as relation_client_name,
                case when tc.label ='china fte' then ccii.invoicing_address else cici.client_address end as relation_client_address,
                case when tc.label ='china fte' then '' else cici.client_location end as relation_client_location
                from start_client_info t
                left join invoice_type_config tc on tc.id = t.invoice_type_id
                left join company_client_invoicing_info ccii on ccii.id = t.client_info_id
                left join company_invoice_client_info cici  on cici.id = t.client_info_id
                where start_id=:startId
                """);
        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("startId", startId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return null;
        }
        return mapList.get(0);
    }

    /**
     * 查询公司地址和名称
     *
     * @param assignmentId
     * @return
     */
    @Transactional(readOnly = true)
    public Map<String, Object> findCompanyAddressAndNameByCompanyId(BigInteger assignmentId) {
        StringBuilder dataSql = new StringBuilder("""
                select t.client_name,t.client_address,t.client_location,
                case when tc.label ='china fte' then ccii.client_name else cici.client_name end as relation_client_name,
                case when tc.label ='china fte' then ccii.invoicing_address else cici.client_address end as relation_client_address,
                case when tc.label ='china fte' then '' else cici.client_location end as relation_client_location
                from start_client_info t
                join timesheet_talent_assignment ass on ass.start_id = t.start_id
                                left join invoice_type_config tc on tc.id = t.invoice_type_id
                                left join company_client_invoicing_info ccii on ccii.id = t.client_info_id
                                left join company_invoice_client_info cici  on cici.id = t.client_info_id
                where ass.id=:assignmentId
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("assignmentId", assignmentId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return null;
        }
        return mapList.get(0);
    }

    /**
     * 查询start id
     *
     * @param talentRecruitmentProcessId
     * @return
     */
    @Transactional(readOnly = true)
    public List<Map<String, Object>> findStartInfo(Long talentRecruitmentProcessId) {
        StringBuilder dataSql = new StringBuilder("select id,start_date,end_date from start where talent_recruitment_process_id=:processId order by start_date desc");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("processId", talentRecruitmentProcessId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return null;
        }
        return mapList;
    }

    /**
     * 根据AssignmentId查询start中的省份信息
     *
     * @param assignmentId
     * @return
     */
    public Map<String, Object> findCanadaProvinceByAssignmentId(Long assignmentId) {
        StringBuilder dataSql = new StringBuilder();

        dataSql.append("""
                select ci.client_location,ci.client_address,ci.client_name,
                case when tc.label ='china fte' then ccii.client_name else cici.client_name end as relation_client_name,
                case when tc.label ='china fte' then ccii.invoicing_address else cici.client_address end as relation_client_address,
                case when tc.label ='china fte' then '' else cici.client_location end as relation_client_location
                from start_client_info ci
                 inner join timesheet_talent_assignment t on ci.start_id = t.start_id
                 left join invoice_type_config tc on tc.id = ci.invoice_type_id
                left join company_client_invoicing_info ccii on ccii.id = ci.client_info_id
                left join company_invoice_client_info cici  on cici.id = ci.client_info_id
                where t.id=:assignmentId
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("assignmentId", assignmentId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return null;
        }
        return mapList.get(0);
    }

    public List<ContractorInvoiceSearchByApplicationVO> searchDataByApplication(ContractorInvoiceSearchByApplicationDTO dto) {
        String dataSql = " SELECT tci.invoice_date, tci.created_date, tci.invoice_number, tci.id invoice_id, tgi.group_number, tgi.id group_invoice_id, tci.total_amount, t.full_name," +
                " t.id talent_id, tci.invoice_status, tgi.group_invoice_status, tci.company_name, tci.company_id, tci.invoice_type, IF(ti.id is null, ex.currency_type, ti.currency_type) currency " +
                " FROM t_contractor_invoice tci " +
                " INNER JOIN timesheet_talent_assignment tta ON tta.id = tci.assignment_id " +
                " INNER JOIN talent_recruitment_process_kpi_user kpi ON kpi.talent_recruitment_process_id = tta.talent_recruitment_process_id " +
                " INNER JOIN talent_recruitment_process trp ON trp.id = kpi.talent_recruitment_process_id " +
                " INNER JOIN talent t ON t.id = trp.talent_id " +
                " LEFT JOIN t_group_invoice_record tgir ON tgir.invoice_id = tci.id " +
                " LEFT JOIN t_group_invoice tgi ON tgi.id = tgir.group_invoice_id " +
                " LEFT JOIN t_invoice_expense_info ex ON ex.invoice_id = tci.id " +
                " LEFT JOIN t_invoice_timesheet_info ti ON ti.invoice_id = tci.id " +
                " INNER JOIN permission_user_team put ON put.user_id = kpi.user_id " +
                " WHERE 1 = 1 " + getWhereSql(dto) +
                " GROUP BY kpi.talent_recruitment_process_id, tci.id " + getOrderBySql(dto) +
                " limit :startOffset, :size ";
        Query dataQuery = entityManager.createNativeQuery(dataSql, ContractorInvoiceSearchByApplicationVO.class);

        int startOffset = (dto.getPage() - 1) * dto.getSize();
        dataQuery.setParameter("startOffset", startOffset);
        dataQuery.setParameter("size", dto.getSize());
        setParameter(dataQuery, dto);
        return dataQuery.getResultList();
    }

    public Long searchCountByApplication(ContractorInvoiceSearchByApplicationDTO dto) {
        String countSql = " select count(1) from ( SELECT tci.id " +
                " FROM t_contractor_invoice tci " +
                " INNER JOIN timesheet_talent_assignment tta ON tta.id = tci.assignment_id " +
                " INNER JOIN talent_recruitment_process_kpi_user kpi ON kpi.talent_recruitment_process_id = tta.talent_recruitment_process_id " +
                " INNER JOIN talent_recruitment_process trp ON trp.id = kpi.talent_recruitment_process_id " +
                " INNER JOIN talent t ON t.id = trp.talent_id " +
                " LEFT JOIN t_group_invoice_record tgir ON tgir.invoice_id = tci.id " +
                " LEFT JOIN t_group_invoice tgi ON tgi.id = tgir.group_invoice_id " +
                " INNER JOIN permission_user_team put ON put.user_id = kpi.user_id " +
                " WHERE 1 = 1 " + getWhereSql(dto) +
                " GROUP BY kpi.talent_recruitment_process_id, tci.id ) temp ";
        Query countQuery = entityManager.createNativeQuery(countSql);
        setParameter(countQuery, dto);
        return Long.parseLong(String.valueOf(countQuery.getSingleResult()));
    }

    private void setParameter(Query query, ContractorInvoiceSearchByApplicationDTO dto) {
        if (ObjectUtil.isNotNull(dto.getUserId())) {
            query.setParameter("userId", dto.getUserId());
        }
        if (ObjectUtil.isNotNull(dto.getPrimaryTeamId())) {
            query.setParameter("primaryTeamId", dto.getPrimaryTeamId());
        }
        if (StrUtil.isNotBlank(dto.getInvoiceNumber())) {
            query.setParameter("invoiceNumber", "%" + dto.getInvoiceNumber() + "%");
        }
        if (StrUtil.isNotBlank(dto.getGroupNumber())) {
            query.setParameter("groupNumber", "%" + dto.getGroupNumber() + "%");
        }
        if (StrUtil.isNotBlank(dto.getFullName())) {
            query.setParameter("fullName", "%" + dto.getFullName() + "%");
        }
        if (ObjectUtil.isNotEmpty(dto.getTalentId())) {
            query.setParameter("talentId", dto.getTalentId());
        }
        if (ObjectUtil.isNotEmpty(dto.getInvoiceStartTime()) && ObjectUtil.isNotEmpty(dto.getInvoiceEndTime())) {
            query.setParameter("invoiceStartTime", dto.getInvoiceStartTime());
            query.setParameter("invoiceEndTime", dto.getInvoiceEndTime());
        }
        if (ObjectUtil.isNotEmpty(dto.getCreatedStartTime()) && ObjectUtil.isNotEmpty(dto.getCreatedEndTime())) {
            query.setParameter("createdStartTime", dto.getCreatedStartTime());
            query.setParameter("createdEndTime", dto.getCreatedEndTime());
        }
        if (ObjectUtil.isNotEmpty(dto.getCompanyId())) {
            query.setParameter("companyId", dto.getCompanyId());
        }
        if (StrUtil.isNotBlank(dto.getCompanyName())) {
            query.setParameter("companyName", "%" + dto.getCompanyName() + "%");
        }
        if (StrUtil.isNotBlank(dto.getCompanyContactName())) {
            query.setParameter("companyContactName", "%" + dto.getCompanyContactName() + "%");
        }
        if (ObjectUtil.isNotEmpty(dto.getCompanyContactId())) {
            query.setParameter("companyContactId", dto.getCompanyContactId());
        }
        if (CollUtil.isNotEmpty(dto.getInvoiceStatusList())) {
            query.setParameter("invoiceStatusList", dto.getInvoiceStatusList().stream().map(InvoiceStatusType::toDbValue).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(dto.getInvoiceTypeList())) {
            query.setParameter("invoiceTypeList", dto.getInvoiceTypeList().stream().map(InvoiceType::toDbValue).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(dto.getGroupInvoiceStatusList())) {
            query.setParameter("groupInvoiceStatusList", dto.getGroupInvoiceStatusList().stream().map(GroupInvoiceStatus::toDbValue).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(dto.getAssignmentDivisionList())) {
            query.setParameter("assignmentDivisionList", dto.getAssignmentDivisionList().stream().map(AssignmentDivision::toDbValue).collect(Collectors.toList()));
        }
    }

    private String getOrderBySql(ContractorInvoiceSearchByApplicationDTO dto) {
        String orderBySql;
        SearchSortDTO sortDTO = dto.getSort();
        if (ObjectUtil.isEmpty(sortDTO)) {
            orderBySql = " order by tci.invoice_date desc ";
        } else {
            Map<String, String> chineseMap = new HashMap<>(16);
            chineseMap.put("fullName", "t.full_name");
            chineseMap.put("companyName", "tci.company_name");
            if (chineseMap.containsKey(sortDTO.getProperty())) {
                orderBySql = " order by CONVERT(" + chineseMap.get(sortDTO.getProperty()) + "  USING GBK) " + sortDTO.getDirection() + " ";
            } else {
                orderBySql = " order by " + StrUtil.toUnderlineCase(sortDTO.getProperty()) + " " + sortDTO.getDirection() + " ";
            }
        }
        return orderBySql;
    }

    private String getWhereSql(ContractorInvoiceSearchByApplicationDTO dto) {
        StringBuilder whereSb = new StringBuilder();
        if (ObjectUtil.isNotEmpty(dto.getUserId())) {
            whereSb.append(" and kpi.user_id = :userId ");
        }
        if (ObjectUtil.isNotEmpty(dto.getPrimaryTeamId())) {
            whereSb.append(" AND put.team_id = :primaryTeamId ");
        }
        if (StrUtil.isNotBlank(dto.getInvoiceNumber())) {
            whereSb.append(" and tci.invoice_number like :invoiceNumber ");
        }
        if (StrUtil.isNotBlank(dto.getGroupNumber())) {
            whereSb.append(" and tgi.group_number like :groupNumber ");
        }
        if (StrUtil.isNotBlank(dto.getFullName())) {
            whereSb.append(" and t.full_name like :fullName ");
        }
        if (ObjectUtil.isNotEmpty(dto.getTalentId())) {
            whereSb.append(" and t.id = :talentId ");
        }
        if (ObjectUtil.isNotEmpty(dto.getInvoiceStartTime()) && ObjectUtil.isNotEmpty(dto.getInvoiceEndTime())) {
            whereSb.append(" and tci.invoice_date BETWEEN :invoiceStartTime and :invoiceEndTime ");
        }
        if (ObjectUtil.isNotEmpty(dto.getCreatedStartTime()) && ObjectUtil.isNotEmpty(dto.getCreatedEndTime())) {
            whereSb.append(" and tci.created_date BETWEEN :createdStartTime and :createdEndTime ");
        }
        if (ObjectUtil.isNotEmpty(dto.getCompanyId())) {
            whereSb.append(" and tci.company_id = :companyId ");
        }
        if (StrUtil.isNotBlank(dto.getCompanyName())) {
            whereSb.append(" and tci.company_name like :companyName ");
        }
        if (StrUtil.isNotBlank(dto.getCompanyContactName())) {
            whereSb.append(" and tci.company_contact_name like :companyContactName ");
        }
        if (ObjectUtil.isNotEmpty(dto.getCompanyContactId())) {
            whereSb.append(" and tci.company_contact_id = :companyContactId ");
        }
        if (CollUtil.isNotEmpty(dto.getInvoiceStatusList())) {
            whereSb.append(" and tci.invoice_status in ( :invoiceStatusList ) ");
        }
        if (CollUtil.isNotEmpty(dto.getInvoiceTypeList())) {
            whereSb.append(" and tci.invoice_type in ( :invoiceTypeList ) ");
        }
        if (CollUtil.isNotEmpty(dto.getGroupInvoiceStatusList())) {
            whereSb.append(" and tgi.group_invoice_status in ( :groupInvoiceStatusList ) ");
        }
        if (CollUtil.isNotEmpty(dto.getAssignmentDivisionList())) {
            whereSb.append(" and tgi.assignment_division in ( :assignmentDivisionList ) ");
        }
        return whereSb.toString();
    }

    /**
     * 根据groupInvoiceIdList 查询Invoice
     *
     * @param groupInvoiceIdList
     * @return
     */
    @Transactional(readOnly = true)
    public List<ContractorInvoiceRatioVO> findInvoiceByGroupInvoiceId(List<BigInteger> groupInvoiceIdList) {
        StringBuilder dataSql = new StringBuilder("""
                select c.id,t.group_invoice_id as groupInvoiceId,c.talent_id as talentId,
                 c.talent_name as talentName,c.total_amount as totalAmount
                 from t_group_invoice_record t
                join t_contractor_invoice c on c.id = t.invoice_id
                where t.`status`=1 and t.group_invoice_id in (:groupInvoiceIdList)
                order by c.invoice_date asc
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("groupInvoiceIdList", groupInvoiceIdList);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceRatioVO.class));
        return dataQuery.getResultList();
    }

    /**
     * 根据groupInvoiceIdList 查询Invoice
     *
     * @param groupInvoiceIdList
     * @return
     */
    @Transactional(readOnly = true)
    public List<ContractorInvoiceInitPaymentDetailVO> findPaymentInfoByGroupInvoiceId(List<BigInteger> groupInvoiceIdList) {
        StringBuilder dataSql = new StringBuilder("""
                select c.id,t.group_invoice_id as groupInvoiceId,c.talent_id as talentId,
                   c.talent_name as talentName,c.total_amount as totalAmount,
                                 pi.id as paymentId,pi.payment_date as paymentDate,
                                 pi.payment_amount as paymentAmount,gi.invoice_amount as invoiceAmount,
                                 cast(gi.group_invoice_status as char) as groupInvoiceStatus
                   from t_group_invoice_record t
                  join t_contractor_invoice c on c.id = t.invoice_id
                                left join t_record_payment_info pi on pi.group_invoice_id = t.group_invoice_id
                                left join t_group_invoice gi on gi.id = t.group_invoice_id
                  where t.`status`=1 and pi.status =1 and t.group_invoice_id in (:groupInvoiceIdList) and  gi.`status`=1\s
                                and gi.group_invoice_status in (3,4)
                  order by t.group_invoice_id asc,pi.id asc,c.invoice_date asc
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("groupInvoiceIdList", groupInvoiceIdList);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ContractorInvoiceInitPaymentDetailVO.class));
        return dataQuery.getResultList();
    }
}
