package com.altomni.apn.jobdiva.service.dto.invoice;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * used in create payment interface
 */
@ApiModel(description = "RecordPaymentCreateDTO")
@Data
public class RecordPaymentCreateDTO extends RecordPaymentCommonDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    BigInteger groupInvoiceId;

    BigDecimal paymentAmount;
}
