package com.altomni.apn.jobdiva.service.dto.invoice;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;

@ApiModel(description = "ContractorGroupInvoiceVoidAndPrintDTO")
@Data
public class ContractorGroupInvoiceVoidAndPrintDTO implements Serializable {

    private List<BigInteger>  invoiceIdList;

    private Integer sentToClient;

    private Integer timesheetIncluded;
}
