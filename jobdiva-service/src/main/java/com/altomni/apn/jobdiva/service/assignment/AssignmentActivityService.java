package com.altomni.apn.jobdiva.service.assignment;

import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentActivityVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;

public interface AssignmentActivityService {
    Page<AssignmentActivityVO> getJobActivities(List<Long> assigmentId, Pageable pageable) throws IOException;
}
