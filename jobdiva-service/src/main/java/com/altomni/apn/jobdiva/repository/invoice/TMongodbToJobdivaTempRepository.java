package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.TMongodbToJobdivaTemp;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.List;

@Repository
public interface TMongodbToJobdivaTempRepository extends JpaRepository<TMongodbToJobdivaTemp, BigInteger> {

    @Query(value = "select DISTINCT group_invoice_number from t_mongodb_to_jobdiva_temp where status = 3 ",nativeQuery = true)
    List<String> selectGroupNumberByStatus();

    @Query(value = "select * from t_mongodb_to_jobdiva_temp where status in (0,2) limit 20 ",nativeQuery = true)
    List<TMongodbToJobdivaTemp> selectAllByStatus();

    @Query(value = "select * from t_mongodb_to_jobdiva_temp where status = 9 limit 20 ",nativeQuery = true)
    List<TMongodbToJobdivaTemp> selectDeleteByStatus();

    @Modifying
    @Transactional
    @Query(value = "update  t_mongodb_to_jobdiva_temp  set status=?2,last_modified_date=now() where id in (?1) ",nativeQuery = true)
    void updateStatusByIds(List<BigInteger> idList,Integer status);

    @Query(value = "select * from t_mongodb_to_jobdiva_temp where status =4 and invoice_id is not null and country ='CA' and invoice_type='2' limit 20 ",nativeQuery = true)
    List<TMongodbToJobdivaTemp> selectAllByCountryIsCa();

    @Query(value = """
            select distinct tenant_id
               from t_mongodb_to_jobdiva_temp as temp
                        left join timesheet_talent_assignment as tta on temp.assignment_id = tta.id
               where temp.group_invoice_number in (?1)
               limit 1
            """, nativeQuery = true)
    Long findTenantIdByGroupNumbers(List<String> groupNumber);

    @Query(value = """
            select distinct tenant_id
               from t_mongodb_to_jobdiva_temp as temp
                        left join timesheet_talent_assignment as tta on temp.assignment_id = tta.id
               where temp.id in (?1)
               limit 1
            """, nativeQuery = true)
    Long findTenantIdByIds(List<BigInteger> ids);
}