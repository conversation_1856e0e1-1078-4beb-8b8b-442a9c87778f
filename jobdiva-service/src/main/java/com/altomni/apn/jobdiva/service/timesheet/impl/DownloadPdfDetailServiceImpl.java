package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.jobdiva.CalculateMethodType;
import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseType;
import com.altomni.apn.common.domain.enumeration.jobdiva.OverTimeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.ChineseCharacterCheckerUtil;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetHolidayRecord;
import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeSaveDto;
import com.altomni.apn.jobdiva.service.dto.timesheet.TimeSheetRecordDTO;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.timesheet.DownloadPdfDetailService;
import com.altomni.apn.jobdiva.service.vo.timesheet.ExpenseListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimeSheetSummaryVO;
import com.altomni.apn.jobdiva.util.InvoicePdfUtil;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.borders.Border;
import com.itextpdf.layout.borders.SolidBorder;
import com.itextpdf.layout.element.Cell;
import com.itextpdf.layout.element.Image;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.property.HorizontalAlignment;
import com.itextpdf.layout.property.TextAlignment;
import com.itextpdf.layout.property.UnitValue;
import com.itextpdf.layout.property.VerticalAlignment;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Element;
import com.itextpdf.text.Phrase;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.altomni.apn.common.config.constants.Constants.*;
import static com.altomni.apn.common.domain.enumeration.jobdiva.CalculateMethodType.CALIFORNIA;

@Slf4j
@Service("downloadPdfDetailService")
public class DownloadPdfDetailServiceImpl implements DownloadPdfDetailService {

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private StoreService storeService;

    @Override
    public void downloadTimeSheetDetail(BreakTimeDTO record, TimeSheetSummaryVO vo, HttpServletResponse response, Boolean is24timeFlag) {
        try {
            PdfDocument pdf = new PdfDocument(new PdfWriter(response.getOutputStream()));
            Document document = new Document(pdf);
            createTimeSheetPdf(record, vo, document, is24timeFlag);
            String fileName = "timesheet_" + vo.getTalentName().replace(" ", "") + vo.getWeekEnd().toString() + ".pdf";
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            document.close();
            pdf.close();
        } catch (Exception e) {
            log.error("timesheet download is error , message = {}", ExceptionUtils.getStackTrace(e));
        }
    }

    public Cell getIntellLogoCell() throws IOException {
        InputStream inputStream = getClass().getResourceAsStream("/images/intelliprogroup-logo.png");
        Image image = new Image(ImageDataFactory.create(inputStream.readAllBytes()));
        image.scale(0.3f,0.3f);
        Paragraph paragraph = new Paragraph().add(image);
        paragraph.setTextAlignment(TextAlignment.CENTER);
        Cell imageCell = new Cell().add(paragraph);
        imageCell.setPaddingTop(50).setPaddingBottom(30);
        return imageCell;
    }

    private void createTimeSheetPdf(BreakTimeDTO record, TimeSheetSummaryVO vo, Document document, Boolean is24timeFlag) throws IOException {
        PdfFont simsunFont = PdfFontFactory.createFont("simsun.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        PdfFont arialFont = PdfFontFactory.createFont("arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
        //logo
        document.add(getIntellLogoCell());
        Table table = new Table(new float[] {1.5F, 1, 1, 1, 1, 1.5F, 1, 1, 1, 1});
        table.setFixedLayout();
        table.setWidth(UnitValue.createPercentValue(100));
        //title
        Cell cellTitle = new Cell(1,10);
        cellTitle.setBorder(new SolidBorder(ColorConstants.BLACK, 1));
        cellTitle.setVerticalAlignment(VerticalAlignment.MIDDLE);
        cellTitle.setTextAlignment(TextAlignment.CENTER);
        cellTitle.add(new Paragraph("Timesheet").setHorizontalAlignment(HorizontalAlignment.CENTER).setFont(arialFont));
        table.addCell(cellTitle);

        // Week Ending
        Cell weekEndingCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1)).setPaddingTop(10F);
        weekEndingCellKey.add(new Paragraph("Week Ending:").setFontSize(8).setFont(arialFont));
        table.addCell(weekEndingCellKey);
        Cell weekEndingCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setPaddingTop(10F);
        weekEndingCellValue.add(new Paragraph(vo.getEndingDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))).setFontSize(8).setFont(simsunFont));
        table.addCell(weekEndingCellValue);
        // Job
        Cell jobCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setPaddingTop(10F);
        jobCellKey.add(new Paragraph("Job:").setFontSize(8).setFont(arialFont));
        table.addCell(jobCellKey);
        Cell jobCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1)).setPaddingTop(10F);
        jobCellValue.add(new Paragraph(StrUtil.blankToDefault("#" + vo.getJobId() + " " + vo.getJobTitle(), "")).setFontSize(8).setFont(simsunFont));
        table.addCell(jobCellValue);

        //Employee Name
        Cell employeeNameCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1));
        employeeNameCellKey.add(new Paragraph("Employee Name:").setFontSize(8).setFont(arialFont));
        table.addCell(employeeNameCellKey);
        Cell employeeNameCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
        if (ChineseCharacterCheckerUtil.containsChineseCharacters(StrUtil.blankToDefault(vo.getTalentName(), ""))) {
            employeeNameCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getTalentName(), "")).setFontSize(8).setFont(simsunFont));
        } else {
            employeeNameCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getTalentName(), "")).setFontSize(8).setFont(arialFont));
        }
        table.addCell(employeeNameCellValue);
        //Submitted Date
        Cell submittedDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
        submittedDateCellKey.add(new Paragraph("Submitted Time:").setFontSize(8).setFont(arialFont));
        table.addCell(submittedDateCellKey);
        Cell submittedCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1));
        submittedCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getAppliedDateStr(), "")).setFontSize(8).setFont(simsunFont));
        table.addCell(submittedCellValue);

        //Status
        Cell statusCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1));
        statusCellKey.add(new Paragraph("Status:").setFontSize(8).setFont(arialFont));
        table.addCell(statusCellKey);
        Cell statusCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
        statusCellValue.add(new Paragraph(vo.getStatus().getDescription()).setFontSize(8).setFont(simsunFont));
        table.addCell(statusCellValue);
        //Approval Date
        Cell approvalDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
        approvalDateCellKey.add(new Paragraph("Approval Time:").setFontSize(8).setFont(arialFont));
        table.addCell(approvalDateCellKey);
        Cell approvalDateCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1));
        approvalDateCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getApprovedDateStr(), "")).setFontSize(8).setFont(simsunFont));
        table.addCell(approvalDateCellValue);

        //Start Date=
        Cell startDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1));
        startDateCellKey.add(new Paragraph("Start Date:").setFontSize(8).setFont(arialFont));
        table.addCell(startDateCellKey);
        Cell startDateCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
        startDateCellValue.add(new Paragraph(vo.getStartDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))).setFontSize(8).setFont(simsunFont));
        table.addCell(startDateCellValue);
        //End Date
        Cell endDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
        endDateCellKey.add(new Paragraph("End Date:").setFontSize(8).setFont(arialFont));
        table.addCell(endDateCellKey);
        Cell endDateCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1));
        endDateCellValue.add(new Paragraph(vo.getEndDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))).setFontSize(8).setFont(simsunFont));
        table.addCell(endDateCellValue);

        //Company
        Cell companyDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1)).setPaddingBottom(10F);
        companyDateCellKey.add(new Paragraph("Company:").setFontSize(8).setFont(arialFont));
        table.addCell(companyDateCellKey);
        Cell companyCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setPaddingBottom(10F);
        companyCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getCompanyName(), "")).setFontSize(8).setFont(simsunFont));
        table.addCell(companyCellValue);
        //Approver
        Cell approverCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setPaddingBottom(10F);
        approverCellKey.add(new Paragraph("Approver:").setFontSize(8).setFont(arialFont));
        table.addCell(approverCellKey);
        Cell approverCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1)).setPaddingBottom(10F);
        approverCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getManager(), "")).setFontSize(8).setFont(simsunFont));
        table.addCell(approverCellValue);
        document.add(table);

        // set null top
        Table table1 = new Table(new float[] {1, 1, 1, 1, 1, 1, 1, 1, 1, 1});
        table1.setFixedLayout();
        table1.setWidth(UnitValue.createPercentValue(100));
        Cell topNullCell = new Cell(1, 10).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1))
                .setBorderTop(new SolidBorder(1)).setBorderRight(new SolidBorder(1)).setPaddingTop(5F);
        table1.addCell(topNullCell);
        // timesheet date
        Cell nullCellKey = new Cell(1,2).setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(ColorConstants.WHITE, 1))
                .setBorderLeft(new SolidBorder(1)).setBackgroundColor(new DeviceRgb(230,238,238));
        nullCellKey.add(new Paragraph("Hour Types").setFontSize(8).setFont(arialFont));
        table1.addCell(nullCellKey);

        boolean displayDoubleTime = true;
        boolean displayOverTimeType = vo.getOvertimeType() == OverTimeType.AUTO;

        // write total hours
        // 当前需要的localDate
        Set<LocalDate> needLocalDateSet = TimeSheetUtil.getWeekByWeekEndingDate(vo.getWeekStart(), vo.getWeekEnd());
        // 所有的localDate
        Set<LocalDate> allLocalDate = TimeSheetUtil.getWeekByWeekEndingDate(TimeSheetUtil.findDateInWeek(vo.getWeekEnd(), vo.getWeekEndingDate().getDayOfWeek().getValue(), 1).toString());
        // 只有需要的localDate 需要有数据
        List<TimeSheetRecordDTO> hours = record.getTimeSheet().stream().filter(timesheet -> needLocalDateSet.contains(timesheet.getWorkDate())).collect(Collectors.toList());
        Set<LocalDate> localDateSet = hours.stream().map(TimeSheetRecordDTO::getWorkDate).collect(Collectors.toSet());
        TimeSheetRecordDTO timeSheetRecordDTO = hours.get(hours.size() - 1);
        List<TimeSheetRecordDTO> finalHours = hours;
        allLocalDate.forEach(localDate -> {
            if (!localDateSet.contains(localDate)) {
                TimeSheetRecordDTO dto = TimeSheetUtil.copyTimeSheetRecordDTO(timeSheetRecordDTO);
                dto.setWorkDate(localDate);
                dto.setWeekDay(localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                finalHours.add(dto);
            } else {
                if (StrUtil.isBlank(timeSheetRecordDTO.getWeekDay())) {
                    timeSheetRecordDTO.setWeekDay(timeSheetRecordDTO.getWorkDate().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                }
            }
        });
        hours = hours.stream().sorted(Comparator.comparing(TimeSheetRecordDTO::getWorkDate)).toList();
        for (TimeSheetRecordDTO timeSheetRecord : hours) {
            String weekDay = timeSheetRecord.getWeekDay();
            String workDay = timeSheetRecord.getWorkDate().format(DateTimeFormatter.ofPattern("MM/dd"));
            Cell cell = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setBorder(new SolidBorder(ColorConstants.WHITE, 1)).setBackgroundColor(new DeviceRgb(230,238,238));
            cell.add(new Paragraph(weekDay + " " + workDay).setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
            table1.addCell(cell);
        }

        TimeSheetType timeSheetType = hours.get(0).getTimeSheetType();
        String[][] timeSheetRecordArray;
        Cell totalCell = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                .setBorder(new SolidBorder(ColorConstants.WHITE, 1)).setBorderRight(new SolidBorder(1)).setBackgroundColor(new DeviceRgb(230,238,238));
        if (timeSheetType == TimeSheetType.WEEK_AM_PM) {
            totalCell.add(new Paragraph("Total").setFontSize(8).setFont(arialFont));
            table1.addCell(totalCell);
            timeSheetRecordArray = constructArrayTimeSheetBreakTimeRecord(hours, displayDoubleTime, displayOverTimeType, vo, needLocalDateSet, is24timeFlag);
        } else if (timeSheetType == TimeSheetType.WEEK_HOUR){
            totalCell.add(new Paragraph("Total").setFontSize(8).setFont(arialFont));
            table1.addCell(totalCell);
            timeSheetRecordArray = constructArrayTimeSheetWeekHourRecord(hours, displayDoubleTime, displayOverTimeType, vo, needLocalDateSet);
        } else {
            totalCell.add(new Paragraph("").setFontSize(8).setFont(arialFont));
            table1.addCell(totalCell);
            timeSheetRecordArray = constructArrayTimeSheetWeekRecord(hours, needLocalDateSet);
        }
        constructTimeCell(timeSheetRecordArray, table1, arialFont);

        List<TimeSheetHolidayRecord> holidayRecordList = record.getHolidayRecordList();
        if (CollUtil.isNotEmpty(holidayRecordList)) {
            Cell nullCell = new Cell(1, 10).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1)).setBorderRight(new SolidBorder(1)).setPaddingTop(20F);
            table1.addCell(nullCell);

            Cell holidayCellT1 = new Cell(1, 4).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setBorder(new SolidBorder(ColorConstants.WHITE, 1)).setBorderLeft(new SolidBorder(1)).setBackgroundColor(new DeviceRgb(230,238,238));
            holidayCellT1.add(new Paragraph("Holiday Item Description").setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
            table1.addCell(holidayCellT1);

            Cell holidayCellT2 = new Cell(1, 2).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setBorder(new SolidBorder(ColorConstants.WHITE, 1)).setBackgroundColor(new DeviceRgb(230,238,238));
            holidayCellT2.add(new Paragraph("Holiday Hours").setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
            table1.addCell(holidayCellT2);

            Cell holidayCellT3 = new Cell(1, 2).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setBorder(new SolidBorder(ColorConstants.WHITE, 1)).setBackgroundColor(new DeviceRgb(230,238,238));
            holidayCellT3.add(new Paragraph("Holiday OT Rate").setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
            table1.addCell(holidayCellT3);

            Cell holidayCellT4 = new Cell(1, 2).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setBorder(new SolidBorder(ColorConstants.WHITE, 1)).setBorderRight(new SolidBorder(1)).setBackgroundColor(new DeviceRgb(230,238,238));
            holidayCellT4.add(new Paragraph("Amount").setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
            table1.addCell(holidayCellT4);

            for (TimeSheetHolidayRecord timeSheetHolidayRecord : holidayRecordList) {
                Cell holidayCellV1 = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                        .setBorder(new SolidBorder(ColorConstants.WHITE, 1)).setBorderLeft(new SolidBorder(1));
                holidayCellV1.add(new Paragraph("Holiday OT adjustment for " + vo.getTalentName()).setFontSize(8).setFont(simsunFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                table1.addCell(holidayCellV1);
                Cell holidayCellV2 = new Cell(1,2).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                        .setBorder(new SolidBorder(ColorConstants.WHITE, 1));
                holidayCellV2.add(new Paragraph(String.format("%.2f", timeSheetHolidayRecord.getWorkHours())).setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                table1.addCell(holidayCellV2);
                String label1 = enumCurrencyService.findEnumCurrencyById(timeSheetHolidayRecord.getCurrency()).getLabel1();
                Cell holidayCellV3 = new Cell(1,2).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                        .setBorder(new SolidBorder(ColorConstants.WHITE, 1));
                holidayCellV3.add(new Paragraph(label1 + timeSheetHolidayRecord.getRate())
                        .setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                table1.addCell(holidayCellV3);
                Cell holidayCellV4 = new Cell(1,2).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                        .setBorder(new SolidBorder(ColorConstants.WHITE, 1)).setBorderRight(new SolidBorder(1));
                holidayCellV4.add(new Paragraph(label1 + timeSheetHolidayRecord.getRate().multiply(new BigDecimal(String.format("%.2f", timeSheetHolidayRecord.getWorkHours()))).setScale(2, RoundingMode.HALF_UP).toPlainString())
                        .setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                table1.addCell(holidayCellV4);
            }
        }

        //null cell
        Cell nullCell = new Cell(1, 10).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1)).setBorderRight(new SolidBorder(1)).setPaddingTop(20F);
        table1.addCell(nullCell);
        //Employee Name
        Cell employeeNameCell = new Cell(1,2).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1));
        employeeNameCell.add(new Paragraph("Employee Name:").setFontSize(8).setFont(arialFont));
        table1.addCell(employeeNameCell);
        Cell employeeNameValueCell = new Cell(1,3).setBorder(Border.NO_BORDER);
        if (ChineseCharacterCheckerUtil.containsChineseCharacters(StrUtil.blankToDefault(vo.getTalentName(), ""))) {
            employeeNameValueCell.add(new Paragraph(StrUtil.blankToDefault(vo.getTalentName(), "")).setFontSize(8).setFont(simsunFont));
        } else {
            employeeNameValueCell.add(new Paragraph(StrUtil.blankToDefault(vo.getTalentName(), "")).setFontSize(8).setFont(arialFont));
        }
        table1.addCell(employeeNameValueCell);
        //Approver Name
        Cell approverNameCell = new Cell(1,2).setBorder(Border.NO_BORDER);
        approverNameCell.add(new Paragraph("Approver Name:").setFontSize(8).setFont(arialFont));
        table1.addCell(approverNameCell);
        Cell approverNameValueCell = new Cell(1,3).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1));
        approverNameValueCell.add(new Paragraph(StrUtil.blankToDefault(vo.getManager(), "")).setFontSize(8).setFont(simsunFont));
        table1.addCell(approverNameValueCell);

        Cell bottomCell = new Cell(1, 10).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1)).setBorderBottom(new SolidBorder(1))
                .setBorderRight(new SolidBorder(1)).setPaddingTop(10F);
        table1.addCell(bottomCell);
        document.add(table1);
    }

    @Override
    public void downloadTimeSheetDetail(BreakTimeDTO record, TimeSheetSummaryVO vo, Document document, Boolean is24timeFlag) {
        try {
            createTimeSheetPdf(record, vo, document, is24timeFlag);
        } catch (Exception e) {
            log.error("timesheet download is error , message = {}", ExceptionUtils.getStackTrace(e));
        }
    }

    @Override
    public void downloadTimesheetDetailByInvoice(BreakTimeDTO record, TimeSheetSummaryVO vo, com.itextpdf.text.Document document) {
        try {
            timesheetPdfByInvoice(record,vo,document);
        }catch (Exception e){
            log.error("timesheet download is error , message = {}", ExceptionUtils.getStackTrace(e));
        }
    }

    private String[][] constructArrayTimeSheetWeekRecord(List<TimeSheetRecordDTO> hours, Set<LocalDate> needLocalDateSet) {
        LinkedList<List<String>> listList = new LinkedList<>();
        List<String> workedList = new ArrayList<>();
        workedList.add("Worked");
        hours.forEach(hour -> {
            if (CollUtil.isNotEmpty(needLocalDateSet) && !needLocalDateSet.contains(hour.getWorkDate())) {
                workedList.add("");
                return;
            }
            if (hour.isSelected()) {
                workedList.add("1");
            } else {
                workedList.add("0");
            }
        });
        workedList.add("");
        listList.addLast(workedList);
        return JSON.parseObject(JSON.toJSONString(listList), String[][].class);
    }

    private String[][] constructArrayTimeSheetWeekHourRecord(List<TimeSheetRecordDTO> hours, boolean displayDoubleTime, boolean displayOverTimeType, TimeSheetSummaryVO vo, Set<LocalDate> needLocalDate) {
        LinkedList<List<String>> listList = new LinkedList<>();
        String overTimeTypeString = "Auto Calculated";
        if (!displayOverTimeType) {
            overTimeTypeString = "Manually";
        }
        // hours
        List<String> hoursList = new ArrayList<>();
        hoursList.add("Hours");
        //regular hours
        List<String> regularHoursList = new ArrayList<>();
        regularHoursList.add("Regular Hours\n(Decimal)");
        //overtime
        List<String> overTimeList = new ArrayList<>();
        overTimeList.add("Overtime\n(" + overTimeTypeString + ")");
        //double time
        List<String> doubleTimeList = null;
        if (displayDoubleTime) {
            doubleTimeList = new ArrayList<>();
            doubleTimeList.add("Doubletime\n(" + overTimeTypeString + ")");
        }
        //total time
        List<String> totalTimeList = new ArrayList<>();
        totalTimeList.add("Total Hours");
        listList.addLast(hoursList);
        listList.addLast(regularHoursList);
        listList.addLast(overTimeList);
        listList.addLast(doubleTimeList);
        listList.addLast(totalTimeList);
        for (int i = 0; i < hours.size(); i++) {
            TimeSheetRecordDTO hour = hours.get(i);
            if (CollUtil.isNotEmpty(needLocalDate) && !needLocalDate.contains(hour.getWorkDate())) {
                hoursList.add("");
                regularHoursList.add("");
                overTimeList.add("");
                if (CollUtil.isNotEmpty(doubleTimeList)) {
                    doubleTimeList.add("");
                }
                totalTimeList.add("");
            } else {
                hoursList.add(getFloat(hour.getWorkHours()));
                regularHoursList.add(getFloat(hour.getRegularHours()));
                overTimeList.add(getFloat(hour.getOverTime()));
                if (CollUtil.isNotEmpty(doubleTimeList)) {
                    doubleTimeList.add(getFloat(hour.getDoubleTime()));
                }
                totalTimeList.add(getFloat(hour.getTotalHours()));
            }
        }
        hoursList.add("");
        regularHoursList.add(getFloat(vo.getRegularHours()));
        overTimeList.add(getFloat(vo.getOverTime()));
        if (CollUtil.isNotEmpty(doubleTimeList)) {
            doubleTimeList.add(getFloat(vo.getDoubleTime()));
        }
        totalTimeList.add(getFloat(vo.getTotalHours()));
        if (CollUtil.isEmpty(doubleTimeList)) {
            listList.remove(doubleTimeList);
        }
        return JSON.parseObject(JSON.toJSONString(listList), String[][].class);
    }

    public String getFloat(Float floatValue) {
        if (ObjectUtil.isNull(floatValue)) {
            return "";
        }
        return floatValue.toString();
    }

    private void constructTimeCell(String[][] timeSheetRecordArray, Table table1, PdfFont arialFont) {
        int iLength = timeSheetRecordArray.length;
        int jLength = timeSheetRecordArray[0].length;
        for (int i = 0; i < iLength; i++) {
            String[] timesheetRecord = timeSheetRecordArray[i];
            for (int j = 0; j < jLength; j++) {
                String time = timesheetRecord[j];
                time = StrUtil.isBlank(time)? "": time;
                if (j == 0) {
                    Cell cell = new Cell(1,2).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder( 1)).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE);
                    cell.add(new Paragraph(time).setFontSize(8).setFont(arialFont));
                    table1.addCell(cell);
                    continue;
                }
                Cell cell = new Cell(1,1).setBorder(Border.NO_BORDER).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE);
                if (j == jLength - 1) {
                    cell.setBorderRight(new SolidBorder(1));
                }
                cell.add(new Paragraph(time).setFontSize(8).setFont(arialFont));
                table1.addCell(cell);
            }
        }
    }

    private String[][] constructArrayTimeSheetBreakTimeRecord(List<TimeSheetRecordDTO> hours, boolean displayDoubleTime, boolean displayOverTimeType, TimeSheetSummaryVO vo, Set<LocalDate> needLocalDateSet, Boolean is24timeFlag) {
        LinkedList<List<String>> listList = new LinkedList<>();
        TimeSheetRecordDTO timeSheetRecordDTO = hours.get(0);
        // time in
        List<String> timeInList = new ArrayList<>();
        timeInList.add("Time In\n(hour:min)");
        hours.forEach(hour -> timeInList.add(TimeSheetUtil.getBreakTimeByType(is24timeFlag, hour.getTimeIn())));
        timeInList.add("");
        listList.addLast(timeInList);
        //break out/in
        List<BreakTimeSaveDto> dtoList = timeSheetRecordDTO.getBreakTime();
        if (CollUtil.isNotEmpty(dtoList)) {
            int size = dtoList.size();
            for (int i = 0; i < size; i++) {
                int index = 2;
                for (int j = 0; j < index; j++) {
                    List<String> breakList = new ArrayList<>();
                    boolean flag = (j + 1)%2 == 0;
                    if (flag) {
                        breakList.add("Meal Break In\n(hour:min)");
                    } else {
                        breakList.add("Meal Break Out\n(hour:min)");
                    }
                    for (TimeSheetRecordDTO hour : hours) {
                        BreakTimeSaveDto breakTimeSaveDto = hour.getBreakTime().get(i);
                        if (CollUtil.isNotEmpty(needLocalDateSet) && !needLocalDateSet.contains(hour.getWorkDate())) {
                            breakList.add(" ");
                            continue;
                        }
                        String breakTime;
                        if (flag) {
                            breakTime = breakTimeSaveDto.getBreakIn();
                        } else {
                            breakTime = breakTimeSaveDto.getBreakOut();
                        }
                        breakList.add(TimeSheetUtil.getBreakTimeByType(is24timeFlag, breakTime));
                    }
                    breakList.add("");
                    listList.addLast(breakList);
                }
            }
        }
        //time out
        List<String> timeOutList = new ArrayList<>();
        timeOutList.add("Time out\n(hour:min)");
        hours.forEach(hour -> timeOutList.add(TimeSheetUtil.getBreakTimeByType(is24timeFlag, hour.getTimeOut())));
        timeOutList.add("");
        listList.addLast(timeOutList);
        //Regular
        List<String> regularList = new ArrayList<>();
        regularList.add("Regular Hours\n(Decimal)");
        hours.forEach(hour -> {
            if (CollUtil.isNotEmpty(needLocalDateSet) && !needLocalDateSet.contains(hour.getWorkDate())) {
                regularList.add("");
            } else {
                regularList.add(getFloat(hour.getRegularHours()));
            }
        });
        regularList.add(getFloat(vo.getRegularHours()));
        listList.addLast(regularList);
        //Overtime
        String overTimeTypeString = "Auto Calculated";
        if (!displayOverTimeType) {
            overTimeTypeString = "Manually";
        }
        List<String> overtimeList = new ArrayList<>();
        overtimeList.add("Overtime\n(" + overTimeTypeString + ")");
        hours.forEach(hour -> {
            if (CollUtil.isNotEmpty(needLocalDateSet) && !needLocalDateSet.contains(hour.getWorkDate())) {
                overtimeList.add("");
            } else {
                overtimeList.add(getFloat(hour.getOverTime()));
            }
        });
        overtimeList.add(getFloat(vo.getOverTime()));
        listList.addLast(overtimeList);
        //Doubletime
        if (displayDoubleTime) {
            List<String> doubleTimeList = new ArrayList<>();
            doubleTimeList.add("Doubletime\n(" + overTimeTypeString + ")");
            hours.forEach(hour -> {
                if (CollUtil.isNotEmpty(needLocalDateSet) && !needLocalDateSet.contains(hour.getWorkDate())) {
                    doubleTimeList.add("");
                } else {
                    doubleTimeList.add(getFloat(hour.getDoubleTime()));
                }
            });
            doubleTimeList.add(getFloat(vo.getDoubleTime()));
            listList.addLast(doubleTimeList);
        }
        //total
        List<String> totalList = new ArrayList<>();
        totalList.add("Total");
        hours.forEach(hour -> {
            if (CollUtil.isNotEmpty(needLocalDateSet) && !needLocalDateSet.contains(hour.getWorkDate())) {
                totalList.add("");
            } else {
                totalList.add(getFloat(hour.getTotalHours()));
            }
        });
        totalList.add(getFloat(vo.getTotalHours()));
        listList.addLast(totalList);
        return JSON.parseObject(JSON.toJSONString(listList), String[][].class);
    }

    @Override
    public void downloadExpenseDetail(Integer isRecipt, BreakTimeDTO breakTimeDTO, ExpenseListVO vo, HttpServletResponse response) {
        try {
            if (isRecipt == null) {
                isRecipt = 0;
            }
            //write summary
            List<Map<String, Object>> maps = ObjectUtil.cloneByStream(breakTimeDTO.getRecords());
            List<ExpenseRecord> bList = new ArrayList<>();
            maps.forEach(map -> mapToEntity(map, bList));
            Map<Integer, List<ExpenseRecord>> mapListExpenseRecord = bList.stream().collect(Collectors.groupingBy(ExpenseRecord::getLineIndex));
            Table table = new Table(new float[] {1.5F, 1, 1, 1, 1, 1.5F, 1, 1, 1, 1});
            Table table1 = new Table(new float[] {1, 1, 1, 1, 1, 1, 1, 1, 1, 1});
            createExpensePdf(vo, mapListExpenseRecord, table, table1);

            String filename = "";
            if (isRecipt == 0) {
                filename = "Expense_" + vo.getTalentName().replace(" ", "") + vo.getWeekEnd().toString() + ".pdf";
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
                PdfDocument pdf = new PdfDocument(new PdfWriter(response.getOutputStream()));
                Document document = new Document(pdf);
                //logo
                document.add(getIntellLogoCell());
                document.add(table);
                document.add(table1);
                document.close();
                pdf.close();
                return;
            }
            response.setContentType("application/x-msdownload");
            filename = "Expense_Receipts_" + vo.getTalentName().replace(" ", "") + vo.getWeekEnd().toString() + ".zip";
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(filename, StandardCharsets.UTF_8));
            ZipOutputStream zip = new ZipOutputStream(response.getOutputStream());
            String weekendStr = vo.getWeekEnd().toString();
            Map<String, List<String>> mapList = new HashMap<>();
            mapListExpenseRecord.forEach((k, v) -> {
                List<String> linkList = new LinkedList<>();
                v.stream().filter(a -> StrUtil.isNotBlank(a.getS3Key())).sorted(Comparator.comparing(ExpenseRecord::getWorkDate)).forEach(expenseRecord -> linkList.add(expenseRecord.getS3Key()));
                if (CollUtil.isNotEmpty(linkList)) {
                    String expenseTypeStr = v.get(0).getExpenseType().toString();
                    if (mapList.containsKey(expenseTypeStr)) {
                        mapList.get(expenseTypeStr).addAll(linkList);
                    } else {
                        mapList.put(expenseTypeStr, linkList);
                    }
                }
            });
            TreeMap<String, List<String>> paramTreeMap = new TreeMap<>(mapList);
            for (Map.Entry<String, List<String>> stringListEntry : paramTreeMap.entrySet()) {
                String key = stringListEntry.getKey();
                List<String> links = stringListEntry.getValue();
                for (int i = 0; i < links.size(); i++) {
                    String link = links.get(i);
                    String fileKey = link.substring(link.lastIndexOf("/") + 1);
                    StringBuilder fileName = new StringBuilder();
                    fileName.append(key).append("-").append(weekendStr);
                    if (links.size() > 1) {
                        fileName.append("-");
                        fileName.append(i + 1);
                    }
                    CloudFileObjectMetadata cloudFileObject = storeService.downloadDocument(fileKey, UploadTypeEnum.RECEIPT.getKey()).getBody();
                    if (cloudFileObject != null) {
                        String fileType = cloudFileObject.getFileName().substring(cloudFileObject.getFileName().lastIndexOf("."));
                        fileName.append(fileType);
                        zip.putNextEntry(new ZipEntry(fileName.toString()));
                        zip.write(cloudFileObject.getContent());
                        zip.flush();
                    }
                }
            }
            if (isRecipt == 2) {
                // 创建输出流
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                PdfWriter writer = new PdfWriter(baos);
                PdfDocument pdf = new PdfDocument(writer);
                Document document = new Document(pdf);
                //logo
                document.add(getIntellLogoCell());
                document.add(table);
                document.add(table1);
                document.close();
                pdf.close();
                zip.putNextEntry(new ZipEntry("expense.pdf"));
                zip.write(baos.toByteArray());
                zip.flush();
            }
            zip.flush();
            zip.finish();
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    @Override
    public void downloadExpenseDetail(BreakTimeDTO breakTimeDTO, ExpenseListVO vo, Document document) throws IOException {
        //write summary
        List<Map<String, Object>> maps = ObjectUtil.cloneByStream(breakTimeDTO.getRecords());
        List<ExpenseRecord> bList = new ArrayList<>();
        maps.forEach(map -> mapToEntity(map, bList));
        Map<Integer, List<ExpenseRecord>> mapListExpenseRecord = bList.stream().collect(Collectors.groupingBy(ExpenseRecord::getLineIndex));
        Table table = new Table(new float[] {1.5F, 1, 1, 1, 1, 1.5F, 1, 1, 1, 1});
        Table table1 = new Table(new float[] {1, 1, 1, 1, 1, 1, 1, 1, 1, 1});
        createExpensePdf(vo, mapListExpenseRecord, table, table1);
        document.add(getIntellLogoCell());
        document.add(table);
        document.add(table1);
    }

    private void createExpensePdf(ExpenseListVO vo, Map<Integer, List<ExpenseRecord>> mapListExpenseRecord, Table table, Table table1) {
        try {
            PdfFont simsunFont = PdfFontFactory.createFont("simsun.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            PdfFont arialFont = PdfFontFactory.createFont("arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);

            table.setFixedLayout();
            table.setWidth(UnitValue.createPercentValue(100));
            //title
            Cell cellTitle = new Cell(1,10);
            cellTitle.setBorder(new SolidBorder(ColorConstants.BLACK, 1));
            cellTitle.setVerticalAlignment(VerticalAlignment.MIDDLE);
            cellTitle.setTextAlignment(TextAlignment.CENTER);
            cellTitle.add(new Paragraph("Expense").setHorizontalAlignment(HorizontalAlignment.CENTER).setFont(arialFont));
            table.addCell(cellTitle);

            // Week Ending
            Cell weekEndingCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1)).setPaddingTop(10F);
            weekEndingCellKey.add(new Paragraph("Week Ending:").setFontSize(8).setFont(arialFont));
            table.addCell(weekEndingCellKey);
            Cell weekEndingCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setPaddingTop(10F);
            weekEndingCellValue.add(new Paragraph(vo.getEndingDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))).setFontSize(8).setFont(simsunFont));
            table.addCell(weekEndingCellValue);
            // Job
            Cell jobCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setPaddingTop(10F);
            jobCellKey.add(new Paragraph("Job:").setFontSize(8).setFont(arialFont));
            table.addCell(jobCellKey);
            Cell jobCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1)).setPaddingTop(10F);
            jobCellValue.add(new Paragraph(StrUtil.blankToDefault("#" + vo.getJobId() + " " + vo.getJobTitle(), "")).setFontSize(8).setFont(simsunFont));
            table.addCell(jobCellValue);

            //Employee Name
            Cell employeeNameCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1));
            employeeNameCellKey.add(new Paragraph("Employee Name:").setFontSize(8).setFont(arialFont));
            table.addCell(employeeNameCellKey);
            Cell employeeNameCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
            employeeNameCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getTalentName(), "")).setFontSize(8).setFont(simsunFont));
            table.addCell(employeeNameCellValue);
            //Submitted Date
            Cell submittedDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
            submittedDateCellKey.add(new Paragraph("Submitted Time:").setFontSize(8).setFont(arialFont));
            table.addCell(submittedDateCellKey);
            Cell submittedCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1));
            submittedCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getAppliedDateStr(), "")).setFontSize(8).setFont(simsunFont));
            table.addCell(submittedCellValue);

            //Status
            Cell statusCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1));
            statusCellKey.add(new Paragraph("Status:").setFontSize(8).setFont(arialFont));
            table.addCell(statusCellKey);
            Cell statusCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
            statusCellValue.add(new Paragraph(vo.getStatus().getDescription()).setFontSize(8).setFont(simsunFont));
            table.addCell(statusCellValue);
            //Approval Date
            Cell approvalDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
            approvalDateCellKey.add(new Paragraph("Approval Time:").setFontSize(8).setFont(arialFont));
            table.addCell(approvalDateCellKey);
            Cell approvalDateCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1));
            approvalDateCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getApprovedDateStr(), "")).setFontSize(8).setFont(simsunFont));
            table.addCell(approvalDateCellValue);

            //Start Date
            Cell startDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1));
            startDateCellKey.add(new Paragraph("Start Date:").setFontSize(8).setFont(arialFont));
            table.addCell(startDateCellKey);
            Cell startDateCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
            startDateCellValue.add(new Paragraph(vo.getStartDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))).setFontSize(8).setFont(simsunFont));
            table.addCell(startDateCellValue);
            //End Date
            Cell endDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER);
            endDateCellKey.add(new Paragraph("End Date:").setFontSize(8).setFont(arialFont));
            table.addCell(endDateCellKey);
            Cell endDateCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1));
            endDateCellValue.add(new Paragraph(vo.getEndDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy"))).setFontSize(8).setFont(simsunFont));
            table.addCell(endDateCellValue);

            //Company
            Cell companyDateCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1)).setPaddingBottom(10F);
            companyDateCellKey.add(new Paragraph("Company:").setFontSize(8).setFont(arialFont));
            table.addCell(companyDateCellKey);
            Cell companyCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setPaddingBottom(10F);
            companyCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getCompanyName(), "")).setFontSize(8).setFont(simsunFont));
            table.addCell(companyCellValue);
            //Approver
            Cell approverCellKey = new Cell(1,1).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setPaddingBottom(10F);
            approverCellKey.add(new Paragraph("Approver:").setFontSize(8).setFont(arialFont));
            table.addCell(approverCellKey);
            Cell approverCellValue = new Cell(1,4).setTextAlignment(TextAlignment.LEFT).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1)).setPaddingBottom(10F);
            approverCellValue.add(new Paragraph(StrUtil.blankToDefault(vo.getManager(), "")).setFontSize(8).setFont(simsunFont));
            table.addCell(approverCellValue);

            // set null top

            table1.setFixedLayout();
            table1.setWidth(UnitValue.createPercentValue(100));
            Cell topNullCell = new Cell(1, 10).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1))
                    .setBorderTop(new SolidBorder(1)).setBorderRight(new SolidBorder(1)).setPaddingTop(5F);
            table1.addCell(topNullCell);

            // timesheet date
            Cell nullCellKey = new Cell(1,2).setTextAlignment(TextAlignment.LEFT).setBorder(new SolidBorder(ColorConstants.WHITE, 1))
                    .setBorderLeft(new SolidBorder(1));
            nullCellKey.add(new Paragraph("Expense Category").setFontSize(8).setFont(arialFont));
            table1.addCell(nullCellKey);
            Set<LocalDate> allLocalDateSet = TimeSheetUtil.getWeekByWeekEndingDate(vo.getWeekEndingDate().toString());
            for (LocalDate localDate : allLocalDateSet) {
                String weekDay = localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH);
                String workDay = localDate.format(DateTimeFormatter.ofPattern("MM/dd"));
                Cell cell = new Cell(1,1).setBorder(Border.NO_BORDER).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE);
                cell.add(new Paragraph(weekDay + " " + workDay).setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                table1.addCell(cell);
            }
            Cell totalCell = new Cell(1,1).setBorder(Border.NO_BORDER).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                    .setBorderRight(new SolidBorder(1));
            totalCell.add(new Paragraph("Total").setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
            table1.addCell(totalCell);

            List<EnumCurrency> enumCurrencyList = enumCurrencyService.findAllEnumCurrency();
            Map<Integer, EnumCurrency> map = enumCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, a -> a));
            Map<LocalDate, BigDecimal> totalMap = new TreeMap<>();
            String currency = map.get(vo.getCurrency()).getLabel1();
            Set<LocalDate> needLocalDateSet = TimeSheetUtil.getWeekByWeekEndingDate(vo.getWeekStart(), vo.getWeekEnd());
            //write total expense
            mapListExpenseRecord.forEach((k, v) -> {
                List<ExpenseRecord> expenseRecordList = v.stream().sorted(Comparator.comparing(ExpenseRecord::getWorkDate)).toList();
                ExpenseType expenseType = expenseRecordList.get(0).getExpenseType();
                Map<LocalDate, ExpenseRecord> expenseRecordMap = v.stream().collect(Collectors.toMap(ExpenseRecord::getWorkDate, a -> a, (a1,a2) -> a1));
                Cell texpenseTypeCell = new Cell(1,2).setBorder(Border.NO_BORDER).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE)
                        .setBorderLeft(new SolidBorder(1));
                texpenseTypeCell.add(new Paragraph(expenseType.getDescription()).setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                table1.addCell(texpenseTypeCell);
                for (LocalDate localDate : allLocalDateSet) {
                    ExpenseRecord expenseRecord = expenseRecordMap.get(localDate);
                    Cell cell = new Cell(1,1).setBorder(Border.NO_BORDER).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE);
                    if (expenseRecord == null || !needLocalDateSet.contains(expenseRecord.getWorkDate())) {
                        cell.add(new Paragraph("").setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                        BigDecimal value = totalMap.getOrDefault(localDate, BigDecimal.ZERO).add(BigDecimal.ZERO).setScale(2, RoundingMode.DOWN);
                        totalMap.put(localDate, value);
                    } else {
                        cell.add(new Paragraph(getCostWithCurrency(currency, expenseRecord.getCost())).setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                        BigDecimal value = totalMap.getOrDefault(expenseRecord.getWorkDate(), BigDecimal.ZERO).add(getCost(expenseRecord.getCost())).setScale(2, RoundingMode.DOWN);
                        totalMap.put(expenseRecord.getWorkDate(), value);
                    }
                    table1.addCell(cell);
                }
                Cell cell = new Cell(1,1).setBorder(Border.NO_BORDER).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE);
                cell.setBorderRight(new SolidBorder(1));
                BigDecimal sum = expenseRecordList.stream().filter(expenseRecord -> needLocalDateSet.contains(expenseRecord.getWorkDate())).map(ExpenseRecord::getCost).filter(ObjectUtil::isNotNull).map(this::getCost).reduce(BigDecimal.ZERO, BigDecimal::add);
                cell.add(new Paragraph(currency + sum.setScale(2, RoundingMode.DOWN)).setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                table1.addCell(cell);
            });

            Cell grandTotalCell = new Cell(1,2).setBorder(Border.NO_BORDER).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE).setBorderLeft(new SolidBorder(1));
            grandTotalCell.add(new Paragraph("Grand Total")).setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE);
            table1.addCell(grandTotalCell);
            totalMap.forEach((k, v) -> {
                Cell cell = new Cell(1,1).setBorder(Border.NO_BORDER).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE);
                if (!needLocalDateSet.contains(k)) {
                    cell.add(new Paragraph("").setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                    table1.addCell(cell);
                } else {
                    cell.add(new Paragraph(currency + totalMap.getOrDefault(k, BigDecimal.ZERO)).setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE));
                    table1.addCell(cell);
                }
            });
            Cell grandAllTotalCell = new Cell(1,1).setBorder(Border.NO_BORDER).setTextAlignment(TextAlignment.LEFT).setVerticalAlignment(VerticalAlignment.MIDDLE).setBorderRight(new SolidBorder(1));
            grandAllTotalCell.add(new Paragraph(currency + getCost(vo.getAmount()))).setFontSize(8).setFont(arialFont).setVerticalAlignment(VerticalAlignment.MIDDLE);
            table1.addCell(grandAllTotalCell);

            //null cell
            Cell nullCell = new Cell(1, 10).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1)).setBorderRight(new SolidBorder(1)).setPaddingTop(20F);
            table1.addCell(nullCell);
            //Employee Name
            Cell employeeNameCell = new Cell(1,2).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1));
            employeeNameCell.add(new Paragraph("Employee Name:").setFontSize(8).setFont(arialFont));
            table1.addCell(employeeNameCell);
            Cell employeeNameValueCell = new Cell(1,3).setBorder(Border.NO_BORDER);
            employeeNameValueCell.add(new Paragraph(StrUtil.blankToDefault(vo.getTalentName(), "")).setFontSize(8).setFont(simsunFont));
            table1.addCell(employeeNameValueCell);
            //Approver Name
            Cell approverNameCell = new Cell(1,2).setBorder(Border.NO_BORDER);
            approverNameCell.add(new Paragraph("Approver Name:").setFontSize(8).setFont(arialFont));
            table1.addCell(approverNameCell);
            Cell approverNameValueCell = new Cell(1,3).setBorder(Border.NO_BORDER).setBorderRight(new SolidBorder(1));
            approverNameValueCell.add(new Paragraph(StrUtil.blankToDefault(vo.getManager(), "")).setFontSize(8).setFont(simsunFont));
            table1.addCell(approverNameValueCell);

            Cell bottomCell = new Cell(1, 10).setBorder(Border.NO_BORDER).setBorderLeft(new SolidBorder(1)).setBorderBottom(new SolidBorder(1))
                    .setBorderRight(new SolidBorder(1)).setPaddingTop(10F);
            table1.addCell(bottomCell);
        } catch (Exception e) {
            log.error("createExpensePdf is error, message = {}", ExceptionUtils.getStackTrace(e));
        }
    }

    private String getCostWithCurrency(String currency, Float cost) {
        if (cost == null) {
            return currency + "0.00";
        }
        return currency + new BigDecimal(Float.toString(cost), new MathContext(10, RoundingMode.HALF_UP)).setScale(2, RoundingMode.DOWN);
    }

    private BigDecimal getCost(Float cost) {
        if (cost == null) {
            return BigDecimal.ZERO.setScale(2, RoundingMode.DOWN);
        }
        return new BigDecimal(Float.toString(cost), new MathContext(10, RoundingMode.HALF_UP)).setScale(2, RoundingMode.DOWN);
    }

    private void mapToEntity(Map<String, Object> map, List<ExpenseRecord> bList) {
        ExpenseType type= ExpenseType.valueOf(map.get(EXPENSE_TYPE).toString());
        Integer index = (Integer) map.get(ROW_INDEX);
        map.remove(ROW_INDEX);
        map.remove(EXPENSE_TYPE);
        List<ExpenseRecord> records = new LinkedList<>();
        for (Map.Entry<String,Object> entry:map.entrySet()) {
            ExpenseRecord record = new ExpenseRecord();
            String[] dateWeek = entry.getKey().split(DATE_WEEK_SEPERTATEOR);
            record.setWeekDay(dateWeek[0]);
            if (entry.getValue() != null) {
                String value =  (String)entry.getValue();
                String[] costLink = value.split(DATE_WEEK_SEPERTATEOR);
                Float cost = Float.valueOf(costLink[0]);
                String slink = costLink[1];
                record.setCost(cost);
                record.setS3Key(slink);
            }
            LocalDate date = LocalDate.parse(dateWeek[1]);
            record.setWorkDate(date);
            record.setLineIndex(index);
            record.setExpenseType(type);
            records.add(record);
        }
        bList.addAll(records);
    }

    /**
     * group invoice create pdf
     * @param record
     * @param vo
     * @param document
     * @throws Exception
     */
    public void timesheetPdfByInvoice(BreakTimeDTO record, TimeSheetSummaryVO vo, com.itextpdf.text.Document document) throws Exception{
        InvoicePdfUtil pdfUtil = new InvoicePdfUtil();
        document.newPage();
        PdfPTable table = new PdfPTable(new float[] {1.5F, 1, 1, 1, 1, 1.5F, 1, 1, 1, 1});
        table.setWidthPercentage(85);
        table.setSpacingBefore(5f);
        table.setSpacingAfter(5f);
        table.getDefaultCell().setMinimumHeight(35f);

        //logo
        com.itextpdf.text.Image img = pdfUtil.readImage(InvoicePdfUtil.LOGO_PATH, InvoicePdfUtil.LOGO_PATH_SLASH);
        img.scaleToFit(270,66);
        PdfPCell cell;
        if (img != null) {
            cell = new PdfPCell(img, false);
        } else {
            cell = new PdfPCell(new Phrase("Intellipro Group"));
        }
        cell.setColspan(10);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        //title
        cell = new PdfPCell(new Phrase("Timesheet",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(10);
        cell.setRowspan(2);
        cell.setPaddingTop(8f);
        cell.setPaddingBottom(8f);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        table.addCell(cell);

        // Week Ending
        cell = new PdfPCell(new Phrase("Week Ending:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(11);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(vo.getEndingDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(15);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);

        // Job
        cell = new PdfPCell(new Phrase("Job:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(15);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(StrUtil.blankToDefault("#" + vo.getJobId() + " " + vo.getJobTitle(), ""),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(7);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);

        //Employee Name
        cell = new PdfPCell(new Phrase("Employee Name:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(11);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(StrUtil.blankToDefault(vo.getTalentName(), ""),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(15);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);

        //Submitted Date
        cell = new PdfPCell(new Phrase("Submitted Time:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(15);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(StrUtil.blankToDefault(vo.getAppliedDateStr(), ""),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(7);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);

        //Status
        cell = new PdfPCell(new Phrase("Status:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(11);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(vo.getStatus().getDescription(),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(15);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);


        //Approval Date
        cell = new PdfPCell(new Phrase("Approval Time:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(15);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(StrUtil.blankToDefault(vo.getApprovedDateStr(), ""),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(7);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);

        //Start Date=
        cell = new PdfPCell(new Phrase("Start Date:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(11);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(vo.getStartDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(15);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);


        //End Date
        cell = new PdfPCell(new Phrase("End Date:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(15);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(vo.getEndDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(7);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(3f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);

        //Company
        cell = new PdfPCell(new Phrase("Company:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(9);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(6f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(StrUtil.blankToDefault(vo.getCompanyName(), ""),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(13);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(6f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);

        //Approver
        cell = new PdfPCell(new Phrase("Approver:",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setRowspan(2);
        cell.disableBorderSide(13);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(6f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(StrUtil.blankToDefault(vo.getManager(), ""),InvoicePdfUtil.FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.disableBorderSide(5);
        cell.setPaddingTop(3f);
        cell.setPaddingBottom(6f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(cell);
        //document.add(table);

        // set null top
        /*PdfPTable table1 = new PdfPTable(new float[] {1, 1, 1, 1, 1, 1, 1, 1, 1, 1});
        table1.setWidthPercentage(85);
        table1.setSpacingBefore(5f);
        table1.setSpacingAfter(5f);
        table1.getDefaultCell().setMinimumHeight(30F);*/

        PdfPCell topNullCell = new PdfPCell(new Phrase(" "));
        topNullCell.setColspan(10);
        topNullCell.setRowspan(1);
        topNullCell.disableBorderSide(2);
        topNullCell.setPaddingTop(5f);
        topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        topNullCell.setVerticalAlignment(Element.ALIGN_LEFT);
        table.addCell(topNullCell);

        // timesheet date
        topNullCell = new PdfPCell(new Phrase("Hour Types",InvoicePdfUtil.FONT_BOLD_8_BLACK));
        topNullCell.setColspan(2);
        topNullCell.setRowspan(1);
        topNullCell.disableBorderSide(11);
        topNullCell.setBackgroundColor(new BaseColor(230,238,238));
        topNullCell.setHorizontalAlignment(Element.ALIGN_CENTER);
        table.addCell(topNullCell);

        boolean displayDoubleTime = true;
        boolean displayOverTimeType = vo.getOvertimeType() == OverTimeType.AUTO;
        // write total hours
        // 当前需要的localDate
        Set<LocalDate> needLocalDateSet = TimeSheetUtil.getWeekByWeekEndingDate(vo.getWeekStart(), vo.getWeekEnd());
        // 所有的localDate
        Set<LocalDate> allLocalDate = TimeSheetUtil.getWeekByWeekEndingDate(TimeSheetUtil.findDateInWeek(vo.getWeekEnd(), vo.getWeekEndingDate().getDayOfWeek().getValue(), 1).toString());
        // 只有需要的localDate 需要有数据
        List<TimeSheetRecordDTO> hours = record.getTimeSheet().stream().filter(timesheet -> needLocalDateSet.contains(timesheet.getWorkDate())).collect(Collectors.toList());
        Set<LocalDate> localDateSet = hours.stream().map(TimeSheetRecordDTO::getWorkDate).collect(Collectors.toSet());
        TimeSheetRecordDTO timeSheetRecordDTO = hours.get(hours.size() - 1);
        List<TimeSheetRecordDTO> finalHours = hours;
        allLocalDate.forEach(localDate -> {
            if (!localDateSet.contains(localDate)) {
                TimeSheetRecordDTO dto = TimeSheetUtil.copyTimeSheetRecordDTO(timeSheetRecordDTO);
                dto.setWorkDate(localDate);
                dto.setWeekDay(localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                finalHours.add(dto);
            } else {
                if (StrUtil.isBlank(timeSheetRecordDTO.getWeekDay())) {
                    timeSheetRecordDTO.setWeekDay(timeSheetRecordDTO.getWorkDate().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                }
            }
        });
        hours = hours.stream().sorted(Comparator.comparing(TimeSheetRecordDTO::getWorkDate)).collect(Collectors.toList());
        for (TimeSheetRecordDTO timeSheetRecord : hours) {
            String weekDay = timeSheetRecord.getWeekDay();
            String workDay = timeSheetRecord.getWorkDate().format(DateTimeFormatter.ofPattern("MM/dd"));

            topNullCell = new PdfPCell(new Phrase(weekDay + " " + workDay, InvoicePdfUtil.FONT_BOLD_8_BLACK));
            topNullCell.setColspan(1);
            topNullCell.setRowspan(1);
            topNullCell.disableBorderSide(15);
            topNullCell.setPaddingTop(3f);
            topNullCell.setPaddingBottom(3f);
            topNullCell.setBackgroundColor(new BaseColor(230,238,238));
            topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            table.addCell(topNullCell);
        }

        TimeSheetType timeSheetType = hours.get(0).getTimeSheetType();
        String[][] timeSheetRecordArray;
        if (timeSheetType == TimeSheetType.WEEK_AM_PM) {
            topNullCell = new PdfPCell(new Phrase("Total",InvoicePdfUtil.FONT_ARIAL));
            topNullCell.setColspan(1);
            topNullCell.setRowspan(1);
            topNullCell.disableBorderSide(7);
            topNullCell.setPaddingTop(3f);
            topNullCell.setPaddingBottom(3f);
            topNullCell.setBackgroundColor(new BaseColor(230,238,238));
            topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            table.addCell(topNullCell);
            timeSheetRecordArray = constructArrayTimeSheetBreakTimeRecord(hours, displayDoubleTime, displayOverTimeType, vo, needLocalDateSet, false);
        } else if (timeSheetType == TimeSheetType.WEEK_HOUR){
            topNullCell = new PdfPCell(new Phrase("Total",InvoicePdfUtil.FONT_ARIAL));
            topNullCell.setColspan(1);
            topNullCell.setRowspan(1);
            topNullCell.disableBorderSide(7);
            topNullCell.setPaddingTop(3f);
            topNullCell.setPaddingBottom(3f);
            topNullCell.setBackgroundColor(new BaseColor(230,238,238));
            topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            table.addCell(topNullCell);
            timeSheetRecordArray = constructArrayTimeSheetWeekHourRecord(hours, displayDoubleTime, displayOverTimeType, vo, needLocalDateSet);
        } else {
            topNullCell = new PdfPCell(new Phrase("",InvoicePdfUtil.FONT_ARIAL));
            topNullCell.setColspan(1);
            topNullCell.setRowspan(1);
            topNullCell.disableBorderSide(7);
            topNullCell.setPaddingTop(3f);
            topNullCell.setPaddingBottom(3f);
            topNullCell.setBackgroundColor(new BaseColor(230,238,238));
            topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            table.addCell(topNullCell);
            timeSheetRecordArray = constructArrayTimeSheetWeekRecord(hours, needLocalDateSet);
        }
        constructTimeCellByInvoice(timeSheetRecordArray, table);

        List<TimeSheetHolidayRecord> holidayRecordList = record.getHolidayRecordList();
        if (CollUtil.isNotEmpty(holidayRecordList)) {
            topNullCell = new PdfPCell(new Phrase("",InvoicePdfUtil.FONT_ARIAL));
            topNullCell.setColspan(10);
            topNullCell.setRowspan(1);
            topNullCell.disableBorderSide(15);
            topNullCell.setPaddingTop(20f);
            table.addCell(topNullCell);


            topNullCell = new PdfPCell(new Phrase("Holiday Item Description",InvoicePdfUtil.FONT_ARIAL));
            topNullCell.setColspan(4);
            topNullCell.setRowspan(1);
            topNullCell.disableBorderSide(11);
            topNullCell.setPaddingTop(3f);
            topNullCell.setPaddingBottom(3f);
            topNullCell.setBackgroundColor(new BaseColor(230,238,238));
            topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            table.addCell(topNullCell);

            topNullCell = new PdfPCell(new Phrase("Holiday Hours",InvoicePdfUtil.FONT_ARIAL));
            topNullCell.setColspan(2);
            topNullCell.setRowspan(1);
            topNullCell.disableBorderSide(15);
            topNullCell.setPaddingTop(3f);
            topNullCell.setPaddingBottom(3f);
            topNullCell.setBackgroundColor(new BaseColor(230,238,238));
            topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            table.addCell(topNullCell);

            topNullCell = new PdfPCell(new Phrase("Holiday OT Rate",InvoicePdfUtil.FONT_ARIAL));
            topNullCell.setColspan(2);
            topNullCell.setRowspan(1);
            topNullCell.disableBorderSide(15);
            topNullCell.setPaddingTop(3f);
            topNullCell.setPaddingBottom(3f);
            topNullCell.setBackgroundColor(new BaseColor(230,238,238));
            topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            table.addCell(topNullCell);



            topNullCell = new PdfPCell(new Phrase("Amount",InvoicePdfUtil.FONT_ARIAL));
            topNullCell.setColspan(2);
            topNullCell.setRowspan(1);
            topNullCell.disableBorderSide(7);
            topNullCell.setPaddingTop(3f);
            topNullCell.setPaddingBottom(3f);
            topNullCell.setBackgroundColor(new BaseColor(230,238,238));
            topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
            topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
            table.addCell(topNullCell);

            for (TimeSheetHolidayRecord timeSheetHolidayRecord : holidayRecordList) {


                topNullCell = new PdfPCell(new Phrase("Holiday OT adjustment for ",InvoicePdfUtil.FONT_ARIAL));
                topNullCell.setColspan(4);
                topNullCell.setRowspan(1);
                topNullCell.disableBorderSide(11);
                topNullCell.setPaddingTop(3f);
                topNullCell.setPaddingBottom(3f);
                topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                table.addCell(topNullCell);

                topNullCell = new PdfPCell(new Phrase(String.format("%.2f", timeSheetHolidayRecord.getWorkHours()),InvoicePdfUtil.FONT_ARIAL));
                topNullCell.setColspan(2);
                topNullCell.setRowspan(1);
                topNullCell.disableBorderSide(15);
                topNullCell.setPaddingTop(3f);
                topNullCell.setPaddingBottom(3f);
                topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                table.addCell(topNullCell);

                String label1 = enumCurrencyService.findEnumCurrencyById(timeSheetHolidayRecord.getCurrency()).getLabel1();

                topNullCell = new PdfPCell(new Phrase(label1 + timeSheetHolidayRecord.getRate(),InvoicePdfUtil.FONT_ARIAL));
                topNullCell.setColspan(2);
                topNullCell.setRowspan(1);
                topNullCell.disableBorderSide(15);
                topNullCell.setPaddingTop(3f);
                topNullCell.setPaddingBottom(3f);
                topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                table.addCell(topNullCell);

                topNullCell = new PdfPCell(new Phrase(label1 + timeSheetHolidayRecord.getRate().multiply(new BigDecimal(String.format("%.2f", timeSheetHolidayRecord.getWorkHours()))).setScale(2, RoundingMode.HALF_UP).toPlainString(),InvoicePdfUtil.FONT_ARIAL));
                topNullCell.setColspan(2);
                topNullCell.setRowspan(1);
                topNullCell.disableBorderSide(7);
                topNullCell.setPaddingTop(3f);
                topNullCell.setPaddingBottom(3f);
                topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                table.addCell(topNullCell);
            }
        }

        //null cell
        topNullCell = new PdfPCell(new Phrase(""));
        topNullCell.setColspan(10);
        topNullCell.setRowspan(1);
        topNullCell.setPaddingTop(30f);
        topNullCell.disableBorderSide(15);
        topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
        table.addCell(topNullCell);

        //Employee Name
        topNullCell = new PdfPCell(new Phrase("Employee Name:",InvoicePdfUtil.FONT_ARIAL));
        topNullCell.setColspan(2);
        topNullCell.setRowspan(1);
        topNullCell.setPaddingTop(12f);
        topNullCell.setPaddingBottom(12f);
        topNullCell.disableBorderSide(9);
        table.addCell(topNullCell);

        topNullCell = new PdfPCell(new Phrase(StrUtil.blankToDefault(vo.getTalentName(), ""),InvoicePdfUtil.FONT_CHINESE));
        topNullCell.setColspan(3);
        topNullCell.setRowspan(1);
        topNullCell.setPaddingTop(12f);
        topNullCell.setPaddingBottom(12f);
        topNullCell.disableBorderSide(13);
        table.addCell(topNullCell);

        //Approver Name
        topNullCell = new PdfPCell(new Phrase("Approver Name:",InvoicePdfUtil.FONT_ARIAL));
        topNullCell.setColspan(2);
        topNullCell.setRowspan(1);
        topNullCell.setPaddingTop(12f);
        topNullCell.setPaddingBottom(12f);
        topNullCell.disableBorderSide(13);
        table.addCell(topNullCell);


        topNullCell = new PdfPCell(new Phrase(StrUtil.blankToDefault(vo.getManager(), ""),InvoicePdfUtil.FONT_CHINESE));
        topNullCell.setColspan(3);
        topNullCell.setRowspan(1);
        topNullCell.setPaddingTop(12f);
        topNullCell.setPaddingBottom(12f);
        topNullCell.disableBorderSide(5);
        table.addCell(topNullCell);


        topNullCell = new PdfPCell(new Phrase(""));
        topNullCell.setColspan(10);
        topNullCell.setRowspan(1);
        topNullCell.disableBorderSide(15);
        topNullCell.setPaddingTop(15f);
        table.addCell(topNullCell);

        document.add(table);
    }

    public void constructTimeCellByInvoice(String[][] timeSheetRecordArray, PdfPTable table1) {
        int iLength = timeSheetRecordArray.length;
        int jLength = timeSheetRecordArray[0].length;
        for (int i = 0; i < iLength; i++) {
            String[] timesheetRecord = timeSheetRecordArray[i];
            for (int j = 0; j < jLength; j++) {
                String time = timesheetRecord[j];
                time = StrUtil.isBlank(time)? "": time;
                if (j == 0) {
                    PdfPCell topNullCell = new PdfPCell(new Phrase(time,InvoicePdfUtil.FONT_ARIAL));
                    topNullCell.setColspan(2);
                    topNullCell.setRowspan(1);
                    topNullCell.disableBorderSide(11);
                    topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                    topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                    table1.addCell(topNullCell);
                    continue;
                }

                PdfPCell topNullCell = new PdfPCell(new Phrase(time,InvoicePdfUtil.FONT_ARIAL));
                topNullCell.setColspan(1);
                topNullCell.setRowspan(1);
                if (j == jLength - 1) {
                    topNullCell.disableBorderSide(7);
                }else {
                    topNullCell.disableBorderSide(15);
                }
                topNullCell.setHorizontalAlignment(Element.ALIGN_LEFT);
                topNullCell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                table1.addCell(topNullCell);
            }
        }
    }
}
