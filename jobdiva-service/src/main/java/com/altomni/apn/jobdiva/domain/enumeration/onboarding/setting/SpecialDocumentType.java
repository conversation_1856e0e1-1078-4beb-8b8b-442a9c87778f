package com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The SpecialDocumentType enumeration.
 */
public enum SpecialDocumentType implements ConvertedEnum<Integer> {
    NONE(0,"NONE"),
    I_9_RELATED_FORMS_AND_ADDITIONAL_SECURITY_REQUIRED(1,"I_9_RELATED_FORMS_AND_ADDITIONAL_SECURITY_REQUIRED");

    // static resolving:
    public static final ReverseEnumResolver<SpecialDocumentType, Integer> resolver =
        new ReverseEnumResolver<>(SpecialDocumentType.class, SpecialDocumentType::toDbValue);
    private final int dbValue;
    private final String name;

    SpecialDocumentType(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static SpecialDocumentType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
