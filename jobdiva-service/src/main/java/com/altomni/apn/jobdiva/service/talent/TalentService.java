package com.altomni.apn.jobdiva.service.talent;

import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentResumeOutput;
import com.altomni.apn.jobdiva.service.vo.timesheet.TalentToHrVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(value = "talent-service") // service name
public interface TalentService {

    @PostMapping("/talent/api/v3/talent-contacts/by-talentId-and-type-and-status")
    ResponseEntity<List<TalentContact>> findAllByTalentIdInAndTypeAndStatus(@RequestBody List<Long> talentIds);

    @PostMapping("/talent/api/v3/talent-primary-email/by-talentId-and-type-and-status")
    ResponseEntity<List<TalentContact>> findPrimaryEmailAllByTalentIdInAndTypeAndStatus(@RequestBody List<Long> talentIds);


    @GetMapping("/talent/api/v3/talents/{id}")
    ResponseEntity<TalentDTOV3> getTalent(@PathVariable("id") Long id);

    @GetMapping("/talent/api/v3/talents/without-entity/{id}")
    ResponseEntity<TalentDTOV3> getTalentWithEntity(@PathVariable("id") Long id);

    @GetMapping("/talent/api/v3/talents/findTalentPhotoUrl/{id}")
    ResponseEntity<String> findTalentPhotoUrl(@PathVariable("id") Long id);

    @PutMapping("/talent/api/v3/talents/info/{id}")
    ResponseEntity<TalentDTOV3> updateTalentInfo(@PathVariable("id") Long id, @RequestBody TalentDTOV3 talent);

    @GetMapping("/talent/api/v3/talent-resumes/talent/{talentId}")
    ResponseEntity<TalentResumeOutput> getTalentResumeByTalentId(@PathVariable("talentId") Long talentId);

    @PutMapping("/talent/api/v3/talent/sync-to-hr/{talentId}")
    void updateTalentNeedSyncToHr(@PathVariable("talentId") Long talentId);

    @GetMapping("/talent/api/v3/talent/sync-to-hr/{talentId}")
    ResponseEntity<TalentToHrVO> getTalentDataSyncToHr(@PathVariable("talentId") Long talentId);

}
