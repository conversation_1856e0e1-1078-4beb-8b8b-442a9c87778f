package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.dto.company.ClientContactDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.jobdiva.config.env.ApplicationProperties;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentTimeSheet;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.OperationType;
import com.altomni.apn.jobdiva.domain.timesheet.*;
import com.altomni.apn.jobdiva.repository.assignment.PayRateRepository;
import com.altomni.apn.jobdiva.repository.timesheet.*;
import com.altomni.apn.jobdiva.service.company.CompanyService;
import com.altomni.apn.jobdiva.service.dto.timesheet.*;
import com.altomni.apn.jobdiva.service.mail.MailService;
import com.altomni.apn.jobdiva.service.rabbitmq.impl.JobdivaToHrRabbitServiceImpl;
import com.altomni.apn.jobdiva.service.talent.TalentService;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetCommentsService;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetRecordService;
import com.altomni.apn.jobdiva.service.user.UserService;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimeSheetSummaryVO;
import com.altomni.apn.jobdiva.util.CommonAmIdUtil;
import com.altomni.apn.common.utils.RateUnitUtil;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.TextStyle;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.altomni.apn.common.domain.enumeration.jobdiva.CalculateMethodType.CALIFORNIA;
import static com.altomni.apn.common.utils.SqlUtil.PARTITION_COUNT_999;
import static com.altomni.apn.company.constants.Constants.*;

@Slf4j
@Service("timeSheetRecordService")
public class TimeSheetRecordServiceImpl extends BaseServiceImpl implements TimeSheetRecordService {

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private CompanyService companyService;

    @Resource
    private TalentService talentService;

    @Resource
    private UserService userService;

    @Resource
    private MailService mailService;

    @Resource
    private TimeSheetCommentsService commentsService;

    @Resource
    private TimeSheetRecordRepository recordRepository;

    @Resource
    private ApproveRecordRepository approveRecordRepository;

    @Resource
    private TimeSheetHolidayRecordRepository timeSheetHolidayRecordRepository;

    @Resource
    private TimeSheetRepository timeSheetRepository;

    @Resource
    private TalentAssignmentRepository talentAssigmentRepository;

    @Resource
    private TimeSheetBreakTimeRepository breakTimeRepository;

    @Resource
    private TimeSheetManagerRepository timeSheetManagerRepository;

    @Resource
    private PayRateRepository payRateRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    private static final String AM_RECEIVE_EMAIL_JUMP_PATH = "/timesheetAM/#/manageTimesheets/TimesheetDetails";

    private static final String CLIENT_RECEIVE_EMAIL_JUMP_PATH = "/clientTimesheets/myClientsTimesheets";
    @Autowired
    private JobdivaToHrRabbitServiceImpl jobdivaToHrRabbitService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TimeSheetRecord> saveRecord(BreakTimeDTO breakTimeDTO, Long tenantId, Long talentId, Boolean isAm) {
        if (CollectionUtils.isEmpty(breakTimeDTO.getTimeSheet())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (breakTimeDTO.getAssignmentId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        List<TimeSheetRecord> data = new LinkedList<>();
        Set<LocalDate> dates = new HashSet<>();
        Instant instant = Instant.now();
        boolean isSubmit = breakTimeDTO.getStatus() == TimeSheetStatus.APPLIED_APPROVE;
        for (TimeSheetRecordDTO dto : breakTimeDTO.getTimeSheet()) {
            if (dto.getWorkDate().isBefore(breakTimeDTO.getWeekStart()) || dto.getWorkDate().isAfter(breakTimeDTO.getWeekEnd())) {
                continue;
            }
            TimeSheetRecord sheetRecord = new TimeSheetRecord();
            ServiceUtils.myCopyProperties(dto, sheetRecord);
            sheetRecord.setTenantId(tenantId);
            sheetRecord.setTalentId(talentId);
            sheetRecord.setAssignmentId(breakTimeDTO.getAssignmentId());
            sheetRecord.setStatus(breakTimeDTO.getStatus());
            sheetRecord.setWeekEndingDate(LocalDate.parse(breakTimeDTO.getWeekEndingDate()));
            sheetRecord.setWeekStart(breakTimeDTO.getWeekStart());
            sheetRecord.setWeekEnd(breakTimeDTO.getWeekEnd());
            if (isSubmit) {
                sheetRecord.setSubmittedDate(instant);
            }
            data.add(sheetRecord);
            dates.add(dto.getWorkDate());
        }
        if (breakTimeDTO.getComments() != null && breakTimeDTO.getComments().trim().length()>0) {
            commentsService.save(breakTimeDTO.getComments(), breakTimeDTO.getWeekEnd().toString(), CommentsType.TIME_SHEET, talentId, breakTimeDTO.getAssignmentId());
        }
        Set<LocalDate> localDateSet = TimeSheetUtil.getWeekByWeekEndingDate(breakTimeDTO.getWeekStart(), breakTimeDTO.getWeekEnd());
        recordRepository.deleteAllByWorkDateInAndTalentIdAndAssignmentId(localDateSet,talentId,breakTimeDTO.getAssignmentId());
        if (!dates.contains(breakTimeDTO.getWeekEnd())) {
            //在做 noHours 的时候会出现数组越界的问题
            TimeSheetRecord weekEndTimeSheetRecord = new TimeSheetRecord();
            weekEndTimeSheetRecord.setTalentId(talentId);
            weekEndTimeSheetRecord.setTenantId(tenantId);
            weekEndTimeSheetRecord.setStatus(breakTimeDTO.getStatus());
            weekEndTimeSheetRecord.setWeekEndingDate(LocalDate.parse(breakTimeDTO.getWeekEndingDate()));
            weekEndTimeSheetRecord.setWeekStart(breakTimeDTO.getWeekStart());
            weekEndTimeSheetRecord.setWeekEnd(breakTimeDTO.getWeekEnd());
            weekEndTimeSheetRecord.setWorkDate(breakTimeDTO.getWeekEnd());
            weekEndTimeSheetRecord.setWeekDay(breakTimeDTO.getWeekEnd().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
            weekEndTimeSheetRecord.setAssignmentId(breakTimeDTO.getAssignmentId());
            weekEndTimeSheetRecord.setTimeSheetType(breakTimeDTO.getType());
            if (isSubmit) {
                weekEndTimeSheetRecord.setSubmittedDate(instant);
            }
            data.add(weekEndTimeSheetRecord);
        }
        if (isAm) {
            saveHolidayRecord(breakTimeDTO);
            TimeSheetUtil.checkGeneratedInvoice(data.stream().filter(timeSheetRecord ->
                    Objects.equals(timeSheetRecord.getWorkDate(), breakTimeDTO.getWeekEnd())).toList());
        }
        List<TimeSheetRecord> result = recordRepository.saveAll(data);
        TimeSheetUtil.setWeekId(result);
        //send email
        if (!isAm && TimeSheetStatus.APPLIED_APPROVE.equals(breakTimeDTO.getStatus())) {
            sendSaveRecordEmail(result, breakTimeDTO.getAssignmentId(), breakTimeDTO.getWeekStart(), breakTimeDTO.getWeekEnd(), LocalDate.parse(breakTimeDTO.getWeekEndingDate()), talentId);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TimeSheetRecord> saveRecordForDateSelect(BreakTimeDTO dto,Long talentId,Long tenantId, Boolean isAm) {
        List<TimeSheetRecordDTO> records = dto.getTimeSheet();
        if (CollectionUtils.isEmpty(records)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        List<TimeSheetRecord> list = new LinkedList<>();
        Set<LocalDate> set = new HashSet<>();
        boolean isAppliedStatus = TimeSheetStatus.APPLIED_APPROVE == dto.getStatus();
        Instant instant = Instant.now();
        for (TimeSheetRecordDTO timeSheetRecordDTO: records) {
            if (timeSheetRecordDTO.getWorkDate().isBefore(dto.getWeekStart()) || timeSheetRecordDTO.getWorkDate().isAfter(dto.getWeekEnd())) {
                continue;
            }
            TimeSheetRecord tr = new TimeSheetRecord();
            tr.setWorkDate(timeSheetRecordDTO.getWorkDate());
            tr.setStatus(dto.getStatus());
            tr.setAssignmentId(dto.getAssignmentId());
            tr.setWeekEndingDate(LocalDate.parse(dto.getWeekEndingDate()));
            tr.setWeekStart(dto.getWeekStart());
            tr.setWeekEnd(dto.getWeekEnd());
            if (isAppliedStatus) {
                tr.setSubmittedDate(instant);
            }
            tr.setWeekDay(timeSheetRecordDTO.getWeekDay());
            set.add(timeSheetRecordDTO.getWorkDate());
            if (timeSheetRecordDTO.isSelected()) {
                tr.setTotalHours(8.0f);
                tr.setRegularHours(8.0f);
                tr.setWorkHours(8.0f);
            } else {
                tr.setTotalHours(0.0f);
                tr.setRegularHours(0.0f);
                tr.setWorkHours(0.0f);
            }
            tr.setTimeSheetType(TimeSheetType.WEEK);
            tr.setTalentId(talentId);
            tr.setTenantId(tenantId);
            list.add(tr);
        }
        commentsService.save(dto.getComments(),dto.getWeekEnd().toString(),CommentsType.TIME_SHEET,talentId,dto.getAssignmentId());
        Set<LocalDate> localDateSet = TimeSheetUtil.getWeekByWeekEndingDate(dto.getWeekStart(), dto.getWeekEnd());
        recordRepository.deleteAllByWorkDateInAndTalentIdAndAssignmentId(localDateSet,talentId,dto.getAssignmentId());
        if (!set.contains(dto.getWeekEnd())) {
            TimeSheetRecord weekEndTimeSheetRecord = new TimeSheetRecord();
            weekEndTimeSheetRecord.setTalentId(talentId);
            weekEndTimeSheetRecord.setTenantId(tenantId);
            weekEndTimeSheetRecord.setStatus(dto.getStatus());
            if (isAppliedStatus) {
                weekEndTimeSheetRecord.setSubmittedDate(instant);
            }
            weekEndTimeSheetRecord.setWeekEndingDate(LocalDate.parse(dto.getWeekEndingDate()));
            weekEndTimeSheetRecord.setWeekStart(dto.getWeekStart());
            weekEndTimeSheetRecord.setWeekEnd(dto.getWeekEnd());
            weekEndTimeSheetRecord.setTotalHours(0.0f);
            weekEndTimeSheetRecord.setRegularHours(0.0f);
            weekEndTimeSheetRecord.setWorkHours(0.0f);
            weekEndTimeSheetRecord.setWorkDate(dto.getWeekEnd());
            weekEndTimeSheetRecord.setWeekDay(dto.getWeekEnd().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
            weekEndTimeSheetRecord.setAssignmentId(dto.getAssignmentId());
            weekEndTimeSheetRecord.setTimeSheetType(dto.getType());
            list.add(weekEndTimeSheetRecord);
        }
        if (isAm) {
            saveHolidayRecord(dto);
            TimeSheetUtil.checkGeneratedInvoice(list.stream().filter(timeSheetRecord ->
                    Objects.equals(timeSheetRecord.getWorkDate(), dto.getWeekEnd())).toList());
        }
        List<TimeSheetRecord> result = recordRepository.saveAll(list);
        TimeSheetUtil.setWeekId(result);
        //send email
        if (!isAm && isAppliedStatus) {
            sendSaveRecordEmail(result, dto.getAssignmentId(), dto.getWeekStart(), dto.getWeekEnd(), LocalDate.parse(dto.getWeekEndingDate()), talentId);
        }
        TimeSheetRecord timeSheetRecord = result.get(0);
        assignmentSyncToHrService.buildTimeSheetRecordListSyncToHrMq(timeSheetRecord.getAssignmentId(), timeSheetRecord.getWeekEnd(), timeSheetRecord.getWeekEndingDate());
        return result;
    }

    private void saveHolidayRecord(BreakTimeDTO dto) {
        doSaveHolidayRecord(dto.getAssignmentId(), dto.getWeekEnd(), dto.getHolidayRecordSaveList());
    }

    private void doSaveHolidayRecord(Long assignmentId, LocalDate weekEnd, List<TimeSheetHolidayRecordDto> holidayRecordSaveList) {
        timeSheetHolidayRecordRepository.deleteByAssignmentIdAndWeekend(assignmentId, weekEnd);
        if (CollUtil.isNotEmpty(holidayRecordSaveList)) {
            List<TimeSheetHolidayRecord> holidayRecordList = new ArrayList<>();
            holidayRecordSaveList.forEach(holidayRecordDto -> {
                TimeSheetHolidayRecord holidayRecord = new TimeSheetHolidayRecord();
                BeanUtil.copyProperties(holidayRecordDto, holidayRecord);
                holidayRecord.setAssignmentId(assignmentId);
                holidayRecord.setWeekEnd(weekEnd);
                holidayRecordList.add(holidayRecord);
            });
            timeSheetHolidayRecordRepository.saveAll(holidayRecordList);
        }
    }

    @Override
    public BreakTimeDTO findRecords(RecordSearchDTO dto, Long talentId, Long assignmentId) {
        //获取整周数据
        AssignmentVO assignmentVO = talentAssigmentRepository.findAssigmentInfoByAssignmentId(assignmentId);
        LocalDate startDate = TimeSheetUtil.findDateInWeek(LocalDate.parse(dto.getStartDate()), assignmentVO.getWeekEnding(), 2);
        LocalDate endDate = TimeSheetUtil.findDateInWeek(LocalDate.parse(dto.getStartDate()), assignmentVO.getWeekEnding(), 1);
        BreakTimeDTO breakTimeDto  = new BreakTimeDTO();
        breakTimeDto.setWeekStart(LocalDate.parse(dto.getStartDate()));
        breakTimeDto.setWeekEnd(LocalDate.parse(dto.getEndDate()));
        List<TimeSheetRecord> list = recordRepository.findAllByDate(startDate,endDate,talentId,dto.getAssignmentId());
        //现有的localDate
        Set<LocalDate> localDateSet = list.stream().map(TimeSheetRecord::getWorkDate).collect(Collectors.toSet());
        //所有需要的localDate
        Set<LocalDate> allLocalDateSet = TimeSheetUtil.getWeekByWeekEndingDate(startDate, endDate);
        list.forEach(timeSheetRecord -> {
            if (StrUtil.isBlank(timeSheetRecord.getWeekDay())) {
                timeSheetRecord.setWeekDay(timeSheetRecord.getWorkDate().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
            }
        });
        TimeSheetRecord timeSheetRecord = list.get(list.size() - 1);
        List<TimeSheetRecord> finalList = list;
        allLocalDateSet.forEach(localDate -> {
            if (!localDateSet.contains(localDate)) {
                TimeSheetRecord sheetRecord = new TimeSheetRecord();
                BeanUtil.copyProperties(timeSheetRecord, sheetRecord, "id", "workHours", "regularHours", "overTime", "doubleTime", "totalHours", "timeIn", "timeOut", "breakTime");
                sheetRecord.setWorkDate(localDate);
                sheetRecord.setWeekDay(localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                finalList.add(sheetRecord);
            }
        });
        list = list.stream().sorted(Comparator.comparing(TimeSheetRecord::getWorkDate)).toList();
        List<TimeSheetRecordDTO> dList = new LinkedList<>();
        TimeSheetRecord weekEndRecord = null;
        for (TimeSheetRecord td: list) {
            if (td.getWorkDate().isEqual(LocalDate.parse(dto.getEndDate()))) {
                breakTimeDto.setStatus(td.getStatus());
                breakTimeDto.setAppliedDate(td.getSubmittedDate());
                weekEndRecord = td;
            }
            TimeSheetRecordDTO tDto = new TimeSheetRecordDTO();
            ServiceUtils.myCopyProperties(td,tDto);
            tDto.setSelected(td.getTotalHours() != null && td.getTotalHours() > 0);
            dList.add(tDto);
        }
        if (weekEndRecord != null) {
            ApproveRecord approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(weekEndRecord.getId(), CommentsType.TIME_SHEET);
            if (approveRecord != null) {
                breakTimeDto.setApprovedDate(approveRecord.getCreatedDate());
            }
        }
        breakTimeDto.setTimeSheet(dList);
        AssignmentPayRateInfo payRateInfo = payRateRepository.findBillUnit(dto.getAssignmentId());
        breakTimeDto.setCurrency(payRateInfo.getCurrency());
        breakTimeDto.setRate(RateUnitUtil.rateConvert(payRateInfo.getPayRate(), payRateInfo.getTimeUnit()));
        breakTimeDto.setHolidayRecordList(timeSheetHolidayRecordRepository.findOneByAssignmentIdAndWeekEnd(dto.getAssignmentId(), LocalDate.parse(dto.getEndDate())));
        breakTimeDto.setInvoiceFlag(TimeSheetUtil.checkGeneratedInvoiceWithResult(list.stream().filter(timeSheetRecord1 ->
                Objects.equals(timeSheetRecord1.getWorkDate(), LocalDate.parse(dto.getEndDate()))).toList()));
        return breakTimeDto;
    }

    @Override
    public BreakTimeDTO findRecordById(RecordSearchDTO dto) {
        if (dto.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        TimeSheetRecord record = recordRepository.findById(dto.getId()).orElse(null);
        if (record == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.EXPENSE_UPDATE_RECORD_ID_ILLEGAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        BreakTimeDTO breakTimeDTO;
        dto.setAssignmentId(record.getAssignmentId());
        if (record.getTimeSheetType() == TimeSheetType.WEEK_AM_PM) {
            breakTimeDTO = getBreakTimeDTO(dto,record.getTalentId(), record.getAssignmentId());
        } else {
            breakTimeDTO = findRecords(dto,record.getTalentId(), record.getAssignmentId());
        }
        TimeSheetComments comments = commentsService.findByWorkDateAndType(dto.getEndDate(),CommentsType.TIME_SHEET,record.getTalentId(),record.getAssignmentId());
        if (comments != null) {
            breakTimeDTO.setComments(comments.getComments());
        }
        return breakTimeDTO;
    }

    @Override
    public BreakTimeDTO findBreakTimeRecord(RecordSearchDTO dto, Long talentId) {
        return getBreakTimeDTO(dto, talentId, dto.getAssignmentId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer saveBreakTime(BreakTimeDTO dto, Long talentId, Long tenantId, Boolean isAm) {
        if (CollectionUtils.isEmpty(dto.getTimeSheet())) {
            throw  new CustomParameterizedException("data is null");
        }
        if (dto.getAssignmentId() == null) {
            throw  new CustomParameterizedException("assignment id is null");
        }
        List<TimeSheetBreakTimeRecord> bList = saveBreakTimeByDto(dto, talentId, tenantId);
        Set<LocalDate> localDateSet = TimeSheetUtil.getWeekByWeekEndingDate(dto.getWeekStart(), dto.getWeekEnd());
        breakTimeRepository.deleteAllByWorkDateInAndTalentIdAndAssignmentId(localDateSet,talentId,dto.getAssignmentId());
        saveRecord(dto,tenantId,talentId, isAm);
        breakTimeRepository.saveAll(bList);
        commentsService.save(dto.getComments(),dto.getWeekEnd().toString(),CommentsType.TIME_SHEET,talentId,dto.getAssignmentId());
        TimeSheetUtil.updateOtDt(dto.getStatus(), dto.getAssignmentId(), dto.getType(), LocalDate.parse(dto.getWeekEndingDate()));
        assignmentSyncToHrService.buildTimeSheetRecordListSyncToHrMq(dto.getAssignmentId(), dto.getWeekEnd(), LocalDate.parse(dto.getWeekEndingDate()));
        return bList.size();
    }

    private List<TimeSheetBreakTimeRecord> saveBreakTimeByDto(BreakTimeDTO dto, Long talentId, Long tenantId) {
        if (CollUtil.isEmpty(dto.getTimeSheet())) {
            return new ArrayList<>();
        }
        TimeSheetStatus status = dto.getStatus();
        List<TimeSheetBreakTimeRecord> records = new LinkedList<>();
        int breakSize = dto.getTimeSheet().stream().filter(e -> CollUtil.isNotEmpty(e.getBreakTime())).map(e -> e.getBreakTime().size()).max(Integer::compareTo).orElse(0);
        dto.getTimeSheet().forEach(timeSheetRecordDTO -> {
            if (timeSheetRecordDTO.getWorkDate().isBefore(dto.getWeekStart()) || timeSheetRecordDTO.getWorkDate().isAfter(dto.getWeekEnd())) {
                return;
            }
            List<BreakTimeSaveDto> breakTimeSaveDtoList = timeSheetRecordDTO.getBreakTime();
            AtomicInteger atomicInteger = new AtomicInteger(0);
            TimeSheetBreakTimeRecord timeInRecord = new TimeSheetBreakTimeRecord();
            LocalDate workDate = timeSheetRecordDTO.getWorkDate();
            setBreakTimeEntity(timeInRecord, timeSheetRecordDTO.getTimeIn(), timeSheetRecordDTO.getWeekDay(), workDate, talentId, tenantId, status, atomicInteger.get(), dto.getAssignmentId(), TimeSheetBreakTimeType.TIME_IN);
            atomicInteger.getAndIncrement();
            records.add(timeInRecord);
            if (CollUtil.isNotEmpty(breakTimeSaveDtoList)) {
                //filter null values
                boolean flag = breakTimeSaveDtoList.stream().allMatch(breakTime -> StrUtil.isBlank(breakTime.getBreakIn()) && StrUtil.isBlank(breakTime.getBreakOut()));
                breakTimeSaveDtoList.forEach(breakTimeDto -> {
                    TimeSheetBreakTimeRecord breakTimeOutRecord = new TimeSheetBreakTimeRecord();
                    setBreakTimeEntity(breakTimeOutRecord, breakTimeDto.getBreakOut(), timeSheetRecordDTO.getWeekDay(), workDate, talentId, tenantId, status, atomicInteger.get(), dto.getAssignmentId(), TimeSheetBreakTimeType.MEAL_BREAK_OUT);
                    atomicInteger.incrementAndGet();
                    if (!flag) {
                        records.add(breakTimeOutRecord);
                    }
                    TimeSheetBreakTimeRecord breakTimeInRecord = new TimeSheetBreakTimeRecord();
                    setBreakTimeEntity(breakTimeInRecord, breakTimeDto.getBreakIn(), timeSheetRecordDTO.getWeekDay(), workDate, talentId, tenantId, status, atomicInteger.get(), dto.getAssignmentId(), TimeSheetBreakTimeType.MEAL_BREAK_IN);
                    atomicInteger.incrementAndGet();
                    if (!flag) {
                        records.add(breakTimeInRecord);
                    }
                });
            }
            TimeSheetBreakTimeRecord timeOutRecord = new TimeSheetBreakTimeRecord();
            setBreakTimeEntity(timeOutRecord, timeSheetRecordDTO.getTimeOut(), timeSheetRecordDTO.getWeekDay(), workDate, talentId, tenantId, status, breakSize * 2 + 1, dto.getAssignmentId(), TimeSheetBreakTimeType.TIME_OUT);
            records.add(timeOutRecord);
        });
        return records;
    }

    private void setBreakTimeEntity(TimeSheetBreakTimeRecord timeInRecord, String time, String dayOfWeek, LocalDate weekDay
            , Long talentId, Long tenantId, TimeSheetStatus status, int index, Long assignmentId, TimeSheetBreakTimeType type) {
        timeInRecord.setBreakTimeType(type);
        timeInRecord.setTenantId(tenantId);
        timeInRecord.setTalentId(talentId);
        timeInRecord.setAssignmentId(assignmentId);
        timeInRecord.setTime(time);
        timeInRecord.setWeekDay(dayOfWeek);
        timeInRecord.setWorkDate(weekDay);
        timeInRecord.setStatus(status);
        timeInRecord.setLineIndex(index);
    }

    @Override
    public SummaryDataVO summary(SummaryQueryDTO dto, Long talentId) {
        SummaryDataVO vo = new SummaryDataVO();
        vo.setPageNum(dto.getPageNum());
        if (dto.getPageNum() <= 0) {
            dto.setPageNum(1);
        }
        int startItem = (dto.getPageNum()-1) * dto.getPageSize();
        int endItem = dto.getPageSize();
        String oderBy = " ";
        String sort = " ";
        if (dto.getOrder() == null || dto.getOrderBy() == null) {
            oderBy = TimeSheetTableOrderType.WEEK_END.getOrderSql() + TimeSheetTableSortType.DESC.getOrderSql();
            sort = " ";
        } else {
            if (dto.getOrderBy() == TimeSheetTableOrderType.STATUS) {
                if (dto.getOrder() == TimeSheetTableSortType.ASC) {
                    oderBy = "ORDER BY CASE status WHEN 2 THEN 0 WHEN 5 THEN 1 WHEN 0 THEN 2 WHEN 3 THEN 3 WHEN 1 THEN 4 WHEN 4 THEN 5 END ";
                } else {
                    oderBy = "ORDER BY CASE status WHEN 2 THEN 5 WHEN 5 THEN 4 WHEN 0 THEN 3 WHEN 3 THEN 2 WHEN 1 THEN 1 WHEN 4 THEN 0 END ";
                }
            } else if (dto.getOrderBy() == TimeSheetTableOrderType.MANAGER) {
                oderBy = TimeSheetTableOrderType.MANAGER_CLIENT.getOrderSql();
                sort = dto.getOrder().getOrderSql();
            } else {
                oderBy = dto.getOrderBy().getOrderSql();
                sort = dto.getOrder().getOrderSql();
            }
        }
        String recordIdSqlLimit = " ";
        if (CollUtil.isNotEmpty(dto.getRecordIds())) {
            StringBuilder sb = new StringBuilder();
            sb.append(" AND (");
            Iterator<List<Long>> iterator = CollUtil.split(dto.getRecordIds(), PARTITION_COUNT_999).iterator();
            while (iterator.hasNext()) {
                List<Long> list = iterator.next();
                List<String> idStrList = JSONUtil.toList(JSONUtil.parseArray(list), String.class);
                sb.append(" tswer.record_id in (").append(String.join(",", idStrList)).append(") ");
                if (iterator.hasNext()) {
                    sb.append(" or");
                }
            }
            sb.append(" ) ");
            recordIdSqlLimit = sb.toString();
        }
        String sql = """
                select AAA.* from (select tswer.work_date as ending_date,  tswer.total_hours as total_hours ,tswer.regular_hours as regular_hours ,tswer.over_time as over_time,
                tswer.double_time as double_time,tswer.full_name as talent_name,tswer.job_title job_title,tswer.company_name as company_name ,tswer.submitted_date as applied_date,
                tswer.approved_date approved_date,tswer.record_id as id, tswer.status as status,tswer.time_sheet_type as sheet_type,tswer.start_date as start_date,
                tswer.calculate_type as calculate_method,tswer.end_date as end_date,tswer.job_id as job_id ,tswer.manager as manager,tswer.primary_manager as primary_manager, tswer.am_approver_id,tswer.am_approver as am,
                tswer.assignment_id as assignment_id ,tswer.instructions as instructions,tswer.overtime_type as overtime_type, tswer.is_except as is_except, 
                tswer.week_start, tswer.week_end, tswer.week_ending_date
                from time_sheet_week_ending_record tswer
                where tswer.assignment_status = 1 and tswer.work_date<=?4 and tswer.talent_id=?3 and tswer.start_date <= current_date and tswer.allow_submit_timesheet = true
                """ + recordIdSqlLimit + " ) AAA " + oderBy + sort  + ", id desc " + " limit ?1,?2 ";
        String countSql = """
                select count(tswer.id)
                from time_sheet_week_ending_record tswer
                where tswer.assignment_status = 1 and tswer.work_date<=?2 and tswer.talent_id=?1 and tswer.start_date <= current_date and tswer.allow_submit_timesheet = true
                """ + recordIdSqlLimit;
        List<TimeSheetSummaryVO> result = null;
        LocalDate nextWeek = getNextWeekEndingDate(SecurityUtils.getUserId());
        try {
            CompletableFuture<List<TimeSheetSummaryVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
                Query query = entityManager.createNativeQuery(sql, TimeSheetSummaryVO.class);
                query.setParameter(1,startItem);
                query.setParameter(2,endItem);
                query.setParameter(3,talentId);
                query.setParameter(4,nextWeek);
                return query.getResultList();
            });
            CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> {
                Query query = entityManager.createNativeQuery(countSql);
                query.setParameter(1,talentId);
                query.setParameter(2,nextWeek);
                List<Object> totalItems = query.getResultList();
                return ((BigInteger)totalItems.get(0)).intValue();
            });
            result = dataFuture.get();
            vo.setTotalItems(countFuture.get());
        } catch (Exception e) {
            log.error("Exception occurred in summary query", e);
        }
        if (CollectionUtils.isNotEmpty(result)) {
            List<Long> combinedIds = CommonAmIdUtil.getCombinedIds(result, null, TimeSheetSummaryVO::getAmApproverId);
            Map<Long, String> userIdToNameMap = CommonAmIdUtil.createUserIdToNameMap(combinedIds, userService);
            CommonAmIdUtil.mapNamesToObjects(result, null, TimeSheetSummaryVO::getAmApproverId, null, TimeSheetSummaryVO::setAm, userIdToNameMap);
            result = result.stream().peek(timeSheetSummaryVO -> {
                String mr;
                if (super.isNoHourStatus(timeSheetSummaryVO.getStatus(), timeSheetSummaryVO.getRegularHours())) {
                    if (StrUtil.isNotBlank(timeSheetSummaryVO.getAm())) {
                        mr = timeSheetSummaryVO.getAm();
                    } else {
                        mr = timeSheetSummaryVO.getPrimaryManager();
                    }
                } else {
                    TimeSheetStatus status = timeSheetSummaryVO.getStatus();
                    switch (status) {
                        case MISSING:
                        case APPLIED_APPROVE:
                        case DRAFT:
                        case NO_RECORD:
                            mr = timeSheetSummaryVO.getPrimaryManager();
                            break;
                        case APPROVED:
                        case REJECTED:
                        default:
                            mr = timeSheetSummaryVO.getAm();
                            if (StringUtils.isBlank(mr)) {
                                mr = timeSheetSummaryVO.getManager();
                            }
                            if (StringUtils.isBlank(mr)) {
                                mr = timeSheetSummaryVO.getPrimaryManager();
                            }
                            break;
                    }
                }
                timeSheetSummaryVO.setManager(mr);
            }).toList();
        }
        vo.setData(result);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer noHour(NoHourDTO dates, Long talentId, Boolean isAm) {
        if (CollectionUtils.isEmpty(dates.getDates())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_NO_HOUR_DATE_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (dates.getAssignmentId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        breakTimeRepository.deleteAllByWorkDateInAndTalentIdAndAssignmentId(dates.getDates(),talentId,dates.getAssignmentId());
        if (dates.getComments() != null && dates.getComments().trim().length() > 0) {
            commentsService.save(dates.getComments(), dates.getWeekEnd().toString(), CommentsType.TIME_SHEET, talentId, dates.getAssignmentId());
        }
        recordRepository.deleteAllByWorkDateInAndTalentIdAndAssignmentId(dates.getDates(), talentId, dates.getAssignmentId());
        List<TimeSheetRecord> timeSheetRecords = new LinkedList<>();
        Instant instant = Instant.now();
        LocalDate currentDate = LocalDate.now();
        TimeSheetStatus status = getNoHourStatus(currentDate, dates.getWeekEnd(), dates.getOperationType());
        AssignmentTimeSheet assignmentTimeSheet = timeSheetRepository.findByAssignmentId(dates.getAssignmentId());
        for (LocalDate date :dates.getDates()) {
            if (date.isEqual(dates.getWeekEnd())) {
                continue;
            }
            TimeSheetRecord timeSheetRecord = new TimeSheetRecord();
            timeSheetRecord.setWorkDate(date);
            setTimeSheetRecord(timeSheetRecord, status, talentId, dates, assignmentTimeSheet, instant);
            timeSheetRecord.setWeekDay(date.getDayOfWeek().getDisplayName(TextStyle.SHORT,Locale.ENGLISH));
            timeSheetRecord.setTenantId(SecurityUtils.getTenantId());
            timeSheetRecords.add(timeSheetRecord);
        }
        recordRepository.saveAll(timeSheetRecords);
        TimeSheetRecord timeSheetRecord = new TimeSheetRecord();
        timeSheetRecord.setWorkDate(dates.getWeekEnd());
        setTimeSheetRecord(timeSheetRecord, status, talentId, dates, assignmentTimeSheet, instant);
        timeSheetRecord.setWeekDay(dates.getWeekEnd().getDayOfWeek().getDisplayName(TextStyle.SHORT,Locale.ENGLISH));
        timeSheetRecord.setTenantId(SecurityUtils.getTenantId());
        timeSheetRecords.add(timeSheetRecord);
        timeSheetRecord = recordRepository.save(timeSheetRecord);
        TimeSheetUtil.setWeekId(List.of(timeSheetRecord));
        List<Long> ids = timeSheetRecords.stream().map(TimeSheetRecord::getId).collect(Collectors.toList());
        ids.add(timeSheetRecord.getId());
        ApproveRecord approveRecord = null;
        if (TimeSheetStatus.APPROVED == status) {
            approveRecord = new ApproveRecord();
            approveRecord.setType(CommentsType.TIME_SHEET);
            approveRecord.setRole(dates.getRoleType());
            approveRecord.setRecordId(timeSheetRecord.getId());
            approveRecord.setStatus(TimeSheetStatus.APPROVED);
            approveRecord.setTenantId(timeSheetRecord.getTenantId());
            approveRecord.setOperator(SecurityUtils.getUserId());
            approveRecord.setWeekEnd(dates.getWeekEnd());
            approveRecordRepository.save(approveRecord);
            //to update trigger
            recordRepository.updateLastModifiedDateById(ids, Instant.now());
        } else {
            recordRepository.updateCreateByAndLastModifiedByById(ids, SYSTEM_ACCOUNT);
        }
        if (OperationType.DELETE == dates.getOperationType() || OperationType.CANCEL == dates.getOperationType()) {
            commentsService.deleteComment(dates.getWeekEndingDate(),CommentsType.TIME_SHEET,talentId,dates.getAssignmentId());
        }
        if (BooleanUtil.isTrue(isAm)) {
            doSaveHolidayRecord(dates.getAssignmentId(), dates.getWeekEnd(), dates.getHolidayRecordSaveList());
            TimeSheetUtil.checkGeneratedInvoice(timeSheetRecords.stream().filter(sheetRecord ->
                    Objects.equals(sheetRecord.getWorkDate(), dates.getWeekEnd())).toList());
        }
        assignmentSyncToHrService.buildNoHoursSyncToHrMq(approveRecord, timeSheetRecords, status);
        return 1;
    }

    private void setTimeSheetRecord(TimeSheetRecord timeSheetRecord, TimeSheetStatus status, Long talentId, NoHourDTO dates, AssignmentTimeSheet assignmentTimeSheet, Instant instant) {
        timeSheetRecord.setStatus(status);
        timeSheetRecord.setSubmittedDate(status == TimeSheetStatus.APPROVED?instant:null);
        timeSheetRecord.setTalentId(talentId);
        timeSheetRecord.setRegularHours(status == TimeSheetStatus.APPROVED?0.0F:null);
        timeSheetRecord.setWorkHours(status == TimeSheetStatus.APPROVED?0.0F:null);
        timeSheetRecord.setOverTime(status == TimeSheetStatus.APPROVED?0.0F:null);
        timeSheetRecord.setDoubleTime(status == TimeSheetStatus.APPROVED && assignmentTimeSheet.getCalculateType() == CALIFORNIA ?0.0F:null);
        timeSheetRecord.setTotalHours(status == TimeSheetStatus.APPROVED?0.0F:null);
        timeSheetRecord.setAssignmentId(dates.getAssignmentId());
        timeSheetRecord.setWeekStart(dates.getWeekStart());
        timeSheetRecord.setWeekEnd(dates.getWeekEnd());
        timeSheetRecord.setWeekEndingDate(dates.getWeekEndingDate());
        timeSheetRecord.setTimeSheetType(dates.getType());
    }

    private TimeSheetStatus getNoHourStatus(LocalDate now ,LocalDate weekEnding, OperationType operationType) {
        if (operationType == null || operationType == OperationType.SAVE) {
            return TimeSheetStatus.APPROVED;
        }
        return now.compareTo(weekEnding)>0? TimeSheetStatus.MISSING: TimeSheetStatus.NO_RECORD;
    }

    private LocalDate getNextWeekEndingDate(Long talentId) {
        LocalDate now = LocalDate.now();
        Integer setWeekDate = timeSheetRepository.findCurrentDateWeekEndingType(now, talentId);
        if (setWeekDate == null) {
            return now.plus(10000, ChronoUnit.DAYS);
        }
        now = now.plusDays(7);
        int week = now.getDayOfWeek().getValue();
        int diff = setWeekDate - week;
        LocalDate weekEndDate = now.plus(diff, ChronoUnit.DAYS);
        if (diff >= 0) {
            return weekEndDate;
        } else {
            return weekEndDate.plusDays(7);
        }
    }

    private BreakTimeDTO getBreakTimeDTO(RecordSearchDTO dto,Long talentId, Long assignmentId) {
        if (dto.getAssignmentId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        BreakTimeDTO breakTimeDTO = new BreakTimeDTO();
        //补全所有的数据,这个时候需要完整周的数据
        breakTimeDTO.setWeekStart(LocalDate.parse(dto.getStartDate()));
        breakTimeDTO.setWeekEnd(LocalDate.parse(dto.getEndDate()));
        AssignmentVO assignmentVO = talentAssigmentRepository.findAssigmentInfoByAssignmentId(assignmentId);
        LocalDate startDate = TimeSheetUtil.findDateInWeek(LocalDate.parse(dto.getStartDate()), assignmentVO.getWeekEnding(), 2);
        LocalDate endDate = TimeSheetUtil.findDateInWeek(LocalDate.parse(dto.getStartDate()), assignmentVO.getWeekEnding(), 1);
        //获取完整周的打卡时间数据
        List<TimeSheetBreakTimeRecord> records = breakTimeRepository.findAllByDate(startDate,endDate,talentId,dto.getAssignmentId());
        List<TimeSheetBreakTimeRecordDto> timeSheetBreakTimeRecordDtoList = new ArrayList<>();
        breakTimeDTO.setRecords(entityToMap(records, dto, timeSheetBreakTimeRecordDtoList, startDate, endDate));
        //获取完整周的打卡记录
        List<TimeSheetRecord> timeSheetRecords = recordRepository.findAllByDate(startDate,endDate,talentId,dto.getAssignmentId());
        List<TimeSheetRecordDTO> dtoList = new LinkedList<>();
        Map<LocalDate, List<TimeSheetBreakTimeRecordDto>> localDateListMap = new HashMap<>();
        if (CollUtil.isNotEmpty(timeSheetBreakTimeRecordDtoList)) {
            localDateListMap = timeSheetBreakTimeRecordDtoList.stream().collect(Collectors.groupingBy(TimeSheetBreakTimeRecordDto::getWorkDate));
        }
        TimeSheetRecord weekEndRecord = null;
        for (TimeSheetRecord td: timeSheetRecords) {
            if (td.getWorkDate().isEqual(LocalDate.parse(dto.getEndDate()))) {
                breakTimeDTO.setStatus(td.getStatus());
                breakTimeDTO.setAppliedDate(td.getSubmittedDate());
                breakTimeDTO.setWeekEndingDate(td.getWeekEndingDate().toString());
                weekEndRecord = td;
            }
            TimeSheetRecordDTO tDto = new TimeSheetRecordDTO();
            ServiceUtils.myCopyProperties(td,tDto);
            if (StrUtil.isBlank(tDto.getWeekDay())) {
                tDto.setWeekDay(tDto.getWorkDate().getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
            }
            if (CollUtil.isNotEmpty(localDateListMap)) {
                setBreakTimeDto(tDto, localDateListMap.get(tDto.getWorkDate()));
            }
            dtoList.add(tDto);
        }
        breakTimeDTO.setTimeSheet(dtoList);
        TimeSheetComments comments = commentsService.findByWorkDateAndType(dto.getEndDate(), CommentsType.TIME_SHEET,talentId,dto.getAssignmentId());
        if (comments != null) {
            breakTimeDTO.setComments(comments.getComments());
        }
        if (weekEndRecord != null) {
            ApproveRecord approveRecord = approveRecordRepository.findApproveRecordByRecordIdAndType(weekEndRecord.getId(), CommentsType.TIME_SHEET);
            if (approveRecord != null) {
                breakTimeDTO.setApprovedDate(approveRecord.getCreatedDate());
            }
        }
        AssignmentPayRateInfo payRateInfo = payRateRepository.findBillUnit(dto.getAssignmentId());
        breakTimeDTO.setCurrency(payRateInfo.getCurrency());
        breakTimeDTO.setRate(RateUnitUtil.rateConvert(payRateInfo.getPayRate(), payRateInfo.getTimeUnit()));
        breakTimeDTO.setHolidayRecordList(timeSheetHolidayRecordRepository.findOneByAssignmentIdAndWeekEnd(dto.getAssignmentId(), LocalDate.parse(dto.getEndDate())));
        breakTimeDTO.setInvoiceFlag(TimeSheetUtil.checkGeneratedInvoiceWithResult(timeSheetRecords.stream().filter(timeSheetRecord ->
                Objects.equals(timeSheetRecord.getWorkDate(), LocalDate.parse(dto.getEndDate()))).toList()));
        return breakTimeDTO;
    }

    private void setBreakTimeDto(TimeSheetRecordDTO tDto, List<TimeSheetBreakTimeRecordDto> timeSheetBreakTimeRecordList) {
        if (CollUtil.isEmpty(timeSheetBreakTimeRecordList)) {
            // Front-end requirement
            tDto.setBreakTime(new ArrayList<>());
            return;
        }
        timeSheetBreakTimeRecordList.stream().filter(breakTimeRecord -> Objects.equals(breakTimeRecord.getBreakTimeType(), TimeSheetBreakTimeType.TIME_IN))
                .findFirst().ifPresent(breakTimeRecord -> tDto.setTimeIn(breakTimeRecord.getTime()));
        timeSheetBreakTimeRecordList.stream().filter(breakTimeRecord -> Objects.equals(breakTimeRecord.getBreakTimeType(), TimeSheetBreakTimeType.TIME_OUT))
                .findFirst().ifPresent(breakTimeRecord -> tDto.setTimeOut(breakTimeRecord.getTime()));
        List<TimeSheetBreakTimeRecordDto> breakTimeInOrOutList = timeSheetBreakTimeRecordList.stream().filter(breakTimeRecord -> !(Objects.equals(breakTimeRecord.getBreakTimeType(), TimeSheetBreakTimeType.TIME_IN)
                || Objects.equals(breakTimeRecord.getBreakTimeType(), TimeSheetBreakTimeType.TIME_OUT))).toList();
        if (CollUtil.isNotEmpty(breakTimeInOrOutList)) {
            List<BreakTimeSaveDto> breakTimeSaveDtoList = new LinkedList<>();
            breakTimeInOrOutList = breakTimeInOrOutList.stream().sorted(Comparator.comparingInt(TimeSheetBreakTimeRecordDto::getLineIndex)).toList();
            CollUtil.split(breakTimeInOrOutList, 2).forEach(breakTimeList -> {
                BreakTimeSaveDto breakTimeSaveDto = new BreakTimeSaveDto();
                breakTimeList.stream().filter(breakTimeRecord -> Objects.equals(breakTimeRecord.getBreakTimeType(), TimeSheetBreakTimeType.MEAL_BREAK_IN))
                        .findFirst().ifPresent(breakTimeRecord -> breakTimeSaveDto.setBreakIn(breakTimeRecord.getTime()));
                breakTimeList.stream().filter(breakTimeRecord -> Objects.equals(breakTimeRecord.getBreakTimeType(), TimeSheetBreakTimeType.MEAL_BREAK_OUT))
                        .findFirst().ifPresent(breakTimeRecord -> breakTimeSaveDto.setBreakOut(breakTimeRecord.getTime()));
                breakTimeSaveDtoList.add(breakTimeSaveDto);
            });
            tDto.setBreakTime(breakTimeSaveDtoList);
        } else {
            tDto.setBreakTime(new ArrayList<>());
        }
    }

    private List<Map<String,Object>> entityToMap(List<TimeSheetBreakTimeRecord> records, RecordSearchDTO recordSearchDTO, List<TimeSheetBreakTimeRecordDto> resultDtoList, LocalDate startDate, LocalDate endDate) {
        List<Map<String,Object>> result = new LinkedList<>();
        if (CollectionUtils.isEmpty(records)) {
            setTimeSheetRecord(records, LocalDate.parse(recordSearchDTO.getEndDate()), TimeSheetBreakTimeType.TIME_IN, 0);
            setTimeSheetRecord(records, LocalDate.parse(recordSearchDTO.getEndDate()), TimeSheetBreakTimeType.MEAL_BREAK_OUT, 1);
            setTimeSheetRecord(records, LocalDate.parse(recordSearchDTO.getEndDate()), TimeSheetBreakTimeType.MEAL_BREAK_IN, 2);
            setTimeSheetRecord(records, LocalDate.parse(recordSearchDTO.getEndDate()), TimeSheetBreakTimeType.TIME_OUT, 3);
        } else {
            if (records.stream().noneMatch(e -> CollUtil.newArrayList(TimeSheetBreakTimeType.MEAL_BREAK_OUT, TimeSheetBreakTimeType.MEAL_BREAK_IN).contains(e.getBreakTimeType()))) {
                LocalDate maxDate = records.stream().map(TimeSheetBreakTimeRecord::getWorkDate).max(LocalDate::compareTo).get();
                setTimeSheetRecord(records, maxDate, TimeSheetBreakTimeType.MEAL_BREAK_OUT, 1);
                setTimeSheetRecord(records, maxDate, TimeSheetBreakTimeType.MEAL_BREAK_IN, 2);
            }
        }
        Map<LocalDate, List<TimeSheetBreakTimeRecord>> workDateMap = records.stream().collect(Collectors.groupingBy(TimeSheetBreakTimeRecord::getWorkDate));
        List<TimeSheetBreakTimeRecord> maxSizeRecordList = workDateMap.entrySet().stream().max(Comparator.comparingInt(e -> e.getValue().size())).get().getValue();
        Map<Integer, TimeSheetBreakTimeRecord> indexMaxMap = maxSizeRecordList.stream().collect(Collectors.toMap(TimeSheetBreakTimeRecord::getLineIndex, a -> a));
        Set<Integer> indexMaxSet = indexMaxMap.keySet();
        Set<LocalDate> localDateSet = TimeSheetUtil.getWeekByWeekEndingDate(startDate, endDate);
        Set<LocalDate> existsSet = records.stream().map(TimeSheetBreakTimeRecord::getWorkDate).collect(Collectors.toSet());
        localDateSet.forEach(localDate -> {
            if (existsSet.contains(localDate)) {
                List<TimeSheetBreakTimeRecord> localRecordList = workDateMap.get(localDate);
                Map<Integer, TimeSheetBreakTimeRecord> indexMap = localRecordList.stream().collect(Collectors.toMap(TimeSheetBreakTimeRecord::getLineIndex, a -> a));
                Set<Integer> indexSet = indexMap.keySet();
                indexMaxSet.forEach(max -> {
                    TimeSheetBreakTimeRecordDto dto = new TimeSheetBreakTimeRecordDto();
                    if (indexSet.contains(max)) {
                        TimeSheetBreakTimeRecord timeSheetBreakTimeRecord = indexMap.get(max);
                        BeanUtil.copyProperties(timeSheetBreakTimeRecord, dto);
                    } else {
                        TimeSheetBreakTimeRecord timeSheetBreakTimeRecord = indexMaxMap.get(max);
                        BeanUtil.copyProperties(timeSheetBreakTimeRecord, dto, "id", "time", "weekDay", "workDate");
                        dto.setWorkDate(localDate);
                        dto.setWeekDay(localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                    }
                    resultDtoList.add(dto);
                });
            } else {
                indexMaxSet.forEach(max -> {
                    TimeSheetBreakTimeRecordDto dto = new TimeSheetBreakTimeRecordDto();
                    TimeSheetBreakTimeRecord timeSheetBreakTimeRecord = indexMaxMap.get(max);
                    BeanUtil.copyProperties(timeSheetBreakTimeRecord, dto, "id", "time", "weekDay", "workDate");
                    dto.setWorkDate(localDate);
                    dto.setWeekDay(localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
                    resultDtoList.add(dto);
                });
            }
        });
        Map<Integer,List<TimeSheetBreakTimeRecordDto>> dateMap = resultDtoList.stream().collect(Collectors.groupingBy(TimeSheetBreakTimeRecordDto::getLineIndex));
        Set<Integer> indexSet = dateMap.keySet();
        Set<Integer> sortSet = new TreeSet<>(Comparator.comparingInt(o -> o));
        sortSet.addAll(indexSet);
        for (Integer index: sortSet) {
            Map<String,Object> map  = new HashMap<>(16);
            List<TimeSheetBreakTimeRecordDto> cell = dateMap.get(index);
            for(TimeSheetBreakTimeRecordDto timeRecord : cell) {
                map.put(BREAK_TYPE, timeRecord.getBreakTimeType());
                map.put(ROW_INDEX, timeRecord.getLineIndex());
                map.put(timeRecord.getWeekDay() + DATE_WEEK_SEPERTATEOR + timeRecord.getWorkDate(), timeRecord.getTime());
            }
            result.add(map);
        }
        return result;
    }

    private void setTimeSheetRecord(List<TimeSheetBreakTimeRecord> result, LocalDate endDate, TimeSheetBreakTimeType type, int i) {
        TimeSheetBreakTimeRecord timeSheetBreakTimeRecord = new TimeSheetBreakTimeRecord();
        timeSheetBreakTimeRecord.setBreakTimeType(type);
        timeSheetBreakTimeRecord.setLineIndex(i);
        timeSheetBreakTimeRecord.setWorkDate(endDate);
        timeSheetBreakTimeRecord.setWeekDay(endDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
        result.add(timeSheetBreakTimeRecord);
    }

    public void sendSaveRecordEmail(List<TimeSheetRecord> result, Long assignmentId, LocalDate weekStart, LocalDate weekend, LocalDate weekEndingDate, Long talentId) {
        if (ObjectUtil.isNull(assignmentId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_DATA_RECORD_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        List<Long> clientContactIds = new ArrayList<>();
        List<Long> amIds = new ArrayList<>();
        List<String> amEmails = new ArrayList<>();
        List<String> clientContactEmails = new ArrayList<>();
        //get link-in-email condition
        String startDate = weekStart.toString();
        AtomicReference<Long> recordId = new AtomicReference<>();
        if (CollUtil.isNotEmpty(result)) {
            result.stream().filter(tsr -> tsr.getWorkDate().isEqual(weekend)).findFirst().ifPresent(timeSheet -> recordId.set(timeSheet.getId()));
        }
        String talentFullName = talentService.getTalentWithEntity(talentId).getBody().getFullName();
        List<TimeSheetManager> managerList = timeSheetManagerRepository.findByAssignmentId(assignmentId);
        if (CollUtil.isNotEmpty(managerList)) {
            managerList.forEach(m -> {
                if (ManagerRoleType.AM.equals(m.getRole())) {
                    amIds.add(m.getClientId());
                } else if (ManagerRoleType.PRIMARY_CLIENT.equals(m.getRole()) || ManagerRoleType.NORMAL_CLIENT.equals(m.getRole())) {
                    clientContactIds.add(m.getClientId());
                }
            });
        }
        //send email to AM
        if (CollUtil.isNotEmpty(amIds)) {
            List<UserBriefDTO> amUsers = userService.getBriefUsersByIds(amIds).getBody();
            if (CollUtil.isNotEmpty(amUsers)) {
                amEmails = amUsers.stream().map(UserBriefDTO::getEmail).toList();
            }
        }
        if (CollUtil.isNotEmpty(amEmails)) {
            sendEmail(applicationProperties.getBaseUrl() + AM_RECEIVE_EMAIL_JUMP_PATH, startDate, weekend.toString(), recordId.get(), amEmails, weekEndingDate.toString(), talentFullName);
        }
        //send email to client
        if (CollUtil.isNotEmpty(clientContactIds)) {
            List<ClientContactDTO> clientContactList = companyService.findBriefContactByIdAndReceiveEmail(clientContactIds).getBody();
            if (CollUtil.isNotEmpty(clientContactList)) {
                clientContactEmails = clientContactList.stream().map(ClientContactDTO::getEmail).toList();
            }
        }
        if (CollUtil.isNotEmpty(clientContactEmails)) {
            sendEmail(applicationProperties.getJobDivaUrl() + CLIENT_RECEIVE_EMAIL_JUMP_PATH, startDate, weekend.toString(), recordId.get(), clientContactEmails, weekEndingDate.toString(), talentFullName);
        }
    }

    protected void sendEmail(String jumpPath, String startDate, String endDate, Long recordId, List<String> emails, String weekEndingDate, String talentFullName) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                String subject = "Timesheet Notification";
                String url = jumpPath +"?startDate=" + startDate + "&endDate=" + endDate + "&id=" + recordId + "&endingDate=" + weekEndingDate + "&weekStart=" + startDate + "&weekEnd=" + endDate;
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                HtmlUtil.appendParagraphCell(sb, talentFullName + " has entered a timesheet for the week-ending date");
                HtmlUtil.appendParagraphCell(sb, endDate);
                HtmlUtil.appendParagraphCell(sb, "Please click <a href=\"" + url + "\">here</a> to view this entry.");
                HtmlUtil.appendParagraphCell(sb, "Links:");
                HtmlUtil.appendParagraphCell(sb, "------");
                HtmlUtil.appendParagraphCell(sb, url);
                sb.append("</body>");
                mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, sb.toString(), null, true));
            }
        });
    }

    @Scheduled(cron = "1 0 0 * * ?", zone = "UTC")
    protected void updateStatusMissingByNoRecord() {
        log.info("[apn] update status missing is start");
        recordRepository.updateRecordStatusInCurrentDate(LocalDate.now());
        log.info("[apn] update status missing is finish");
    }

}
