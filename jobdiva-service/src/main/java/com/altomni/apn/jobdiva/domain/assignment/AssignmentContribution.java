package com.altomni.apn.jobdiva.domain.assignment;

import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.enumeration.user.UserRoleConverter;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A record
 */
@Entity
@Data
@Table(name = "assignment_contribution")
public class AssignmentContribution implements Serializable
{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;


    @Column(name = "assignment_id")
    private Long assignmentId;

    @Column(name = "user_id")
    private Long  userId;

    @Column(name = "user_role")
    @Convert(converter = UserRoleConverter.class)
    private UserRole userRole;

    @Column(name = "percentage")
    private BigDecimal percentage;


    @Transient
    private  String firstName;

    @Transient
    private  String lastName;


    public AssignmentContribution()
    {
    }

    public AssignmentContribution(Long id, Long assignmentId, Long userId, UserRole role, BigDecimal contributionRate, String firstName,String lastName)
    {
        this.id = id;
        this.assignmentId = assignmentId;
        this.userId = userId;
        this.userRole = role;
        this.percentage = contributionRate;
        this.firstName = firstName;
        this.lastName = lastName;
    }

    public AssignmentContribution(Long id, Long assignmentId, Long userId, UserRole role, BigDecimal contributionRate) {
        this.id = id;
        this.assignmentId = assignmentId;
        this.userId = userId;
        this.userRole = role;
        this.percentage = contributionRate;
    }
}
