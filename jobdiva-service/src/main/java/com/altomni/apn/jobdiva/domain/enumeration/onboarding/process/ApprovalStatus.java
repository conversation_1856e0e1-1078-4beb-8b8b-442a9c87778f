package com.altomni.apn.jobdiva.domain.enumeration.onboarding.process;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The SpecialDocumentType enumeration.
 */
public enum ApprovalStatus implements ConvertedEnum<Integer> {
    PENDING(0,"Pending"),
    MISSING(1,"Missing"),
    REJECTED(2,"Rejected"),
    ACCEPTED(3,"Accepted"),
    DELETED(4,"Deleted");

    // static resolving:
    public static final ReverseEnumResolver<ApprovalStatus, Integer> resolver =
        new ReverseEnumResolver<>(ApprovalStatus.class, ApprovalStatus::toDbValue);
    private final int dbValue;
    private final String name;

    ApprovalStatus(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static ApprovalStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
