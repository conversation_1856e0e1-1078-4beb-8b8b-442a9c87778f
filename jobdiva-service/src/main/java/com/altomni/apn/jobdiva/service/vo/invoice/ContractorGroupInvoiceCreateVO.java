package com.altomni.apn.jobdiva.service.vo.invoice;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "ContractorGroupInvoiceCreateVO")
public class ContractorGroupInvoiceCreateVO implements Serializable {

    private List<ContractorInvoiceFailedVO> data;

    private Integer successCount;

    private Integer failedCount;

    private Integer missingCount;
}
