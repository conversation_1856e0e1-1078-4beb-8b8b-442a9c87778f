package com.altomni.apn.jobdiva.service.assignment.impl;

import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.dto.activity.*;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.activity.ActivityConfigService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.config.env.ApplicationProperties;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.service.assignment.AssignmentActivityService;
import com.altomni.apn.jobdiva.service.talent.TalentService;
import com.altomni.apn.jobdiva.service.user.UserService;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentActivityVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AssignmentActivityServiceImpl implements AssignmentActivityService {

    final static String UNKNOWNUSER = "Unknown user";

    final static String DEFAULT_SORT_KEY = "createdDate";

    final static String DEFAULT_SORT_ORDER = "desc";

    private final Gson gson = new Gson();

    private final HttpService httpService;
    private final TalentAssignmentRepository assignmentRepository;
    private final UserService userService;
    private final TalentService talentService;
    private final ActivityConfigService activityConfigService;
    private final ApplicationProperties applicationProperties;

    public AssignmentActivityServiceImpl(HttpService httpService, TalentAssignmentRepository assignmentRepository, UserService userService, TalentService talentService,
                                         ActivityConfigService activityConfigService, ApplicationProperties applicationProperties) {
        this.httpService = httpService;
        this.assignmentRepository = assignmentRepository;
        this.userService = userService;
        this.talentService = talentService;
        this.activityConfigService = activityConfigService;
        this.applicationProperties = applicationProperties;
    }

    @Override
    public Page<AssignmentActivityVO> getJobActivities(List<Long> assigmentIds, Pageable pageable) throws IOException {

        List<TalentAssigment> assignments = assignmentRepository.findAllById(assigmentIds);
        if (assignments.isEmpty()) {
            log.error("[APN User {} get assignment {} Activity: assignment not found", SecurityUtils.getUserId(), assigmentIds);
            return new PageImpl<>(Collections.emptyList(), pageable, 0L);
        }
        List<Long> talentIds = assignments.stream().map(TalentAssigment::getTalentId).distinct().toList();
        if (talentIds.size() != 1) {
            throw new CustomParameterizedException("Only can get assignment activity for one talent");
        }
        Long talentId = talentIds.get(0);


        Sort sort = pageable.getSort();
        String sortKey = sort.isSorted() ? sort.iterator().next().getProperty() : DEFAULT_SORT_KEY;
        String sortOrder = sort.isSorted() ? sort.iterator().next().getDirection().name() : DEFAULT_SORT_ORDER;
        ActivityESRequestDTO activityESRequestDTO = activityConfigService.generateESRequest("assignmentId", assigmentIds, pageable, sortKey, sortOrder);
        String condition = gson.toJson(activityESRequestDTO);
        String url = getCommonServiceUrl();

        List<AssignmentActivityVO> assignmentActivityVOS = new ArrayList<>();
        Long total = 0L;

        try {
            HttpResponse response = httpService.post(url, condition);
            if (response != null) {
                if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.info("[APN: Es JobActivity @{}] search jobActivity from activity service success, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                    ESResponse esResponse = parseESResponse(response.getBody());
                    total = esResponse.getHits().getTotal().getValue();
                    List<EsAssignmentActivityDto> sourceChangeFromResponse = getSourceChangeFromResponse(esResponse);
                    List<AssignmentActivityVO> activityVOS = fotmatAssignmentActivity(sourceChangeFromResponse, assignments, talentId);
                    assignmentActivityVOS.addAll(activityVOS);
                } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                    log.error("[APN: Es AssignmentActivity @{}] search AssignmentActivity from activity service failed, searchRequest: {}, pageable:{}, responseCode:{}", SecurityUtils.getUserId(), condition, pageable, response.getCode());
                }
            }
        } catch (Exception ex) {
            log.error("[APN: Es AssignmentActivity @{}] assignmentIds: {}, Error:", SecurityUtils.getUserId(), assigmentIds, ex);
            throw ex;
        }
        return new PageImpl<>(assignmentActivityVOS, pageable, total);
    }

    private List<AssignmentActivityVO> fotmatAssignmentActivity(List<EsAssignmentActivityDto> esAssignmentActivityDtos, List<TalentAssigment> assignments, Long talentId) {
        log.info("[APN User {} get assignment Activity: format assignment Activity ", SecurityUtils.getUserId());
        if (esAssignmentActivityDtos == null || esAssignmentActivityDtos.isEmpty()) {
            return Collections.emptyList();
        }
        Map<Long, TalentAssigment> assigmentMap = assignments.stream().collect(Collectors.toMap(TalentAssigment::getId, Function.identity()));

        List<Long> userIds = esAssignmentActivityDtos.stream().map(EsAssignmentActivityDto::getCreatedBy)
                .map(NumberUtils::toLong).toList();
        List<UserBriefDTO> responseFindByIds = userService.getBriefUsersByIds(userIds).getBody();
        Map<Long, UserBriefDTO> userMap = responseFindByIds == null ? Collections.emptyMap() : responseFindByIds.stream().collect(Collectors.toMap(UserBriefDTO::getId, user -> user));

        TalentDTOV3 talent = talentService.getTalentWithEntity(talentId).getBody();

        return esAssignmentActivityDtos.stream().map(dto -> {
            AssignmentActivityVO activityVO = new AssignmentActivityVO();
            Long assignmentId = dto.getAssignmentId();
            TalentAssigment assignment = assigmentMap.get(assignmentId);
            if (assignment == null) {
                throw new CustomParameterizedException("Assignment not found");
            }
            activityVO.setAssignmentId(assignmentId);
            activityVO.setAssigmentStartDate(assignment.getStartDate());
            activityVO.setJobId(assignment.getJobId());
            activityVO.setTalentId(assignment.getTalentId());
            activityVO.setCandidateFullname(CommonUtils.formatFullName(talent.getFirstName(), talent.getLastName(), talent.getFullName()));
            activityVO.setRecordType(dto.getRecordType());
            activityVO.setCreatedDate(dto.getCreatedDate());
            activityVO.setOperationType(dto.getOperationType());
            List<ChangeFieldDTO> changeDTOList = new ArrayList<>();
            dto.getChangeFields().forEach(change -> {
                ChangeFieldDTO changeDTO = new ChangeFieldDTO();
                changeDTO.setChangeFieldDTOFromActivityDTO(change);
                changeDTOList.add(changeDTO);
            });
            activityVO.setChangeFields(changeDTOList);
            FullNameUserDTO userDTO = new FullNameUserDTO();
            long createUserId = Long.parseLong(dto.getCreatedBy());
            userDTO.setId(createUserId);
            UserBriefDTO userBriefDTO = userMap.get(createUserId);
            if (userBriefDTO != null) {
                userDTO.setFullName(CommonUtils.formatFullNameWithBlankCheck(userBriefDTO.getFirstName(), userBriefDTO.getLastName()));
            } else {
                userDTO.setFullName(UNKNOWNUSER);
            }
            activityVO.setCreatedBy(userDTO);
            return activityVO;
        }).toList();
    }


    private String getCommonServiceUrl() {
        return applicationProperties.getActivityESUrl() + "activity_assignment_" + SecurityUtils.getTenantId() + "/_search";
    }

    private ESResponse parseESResponse(String response) throws JsonProcessingException {
        log.info("[APN User {} get assignment Activity: process parse from response", SecurityUtils.getUserId());

        if (StringUtils.isEmpty(response)) {
            return null;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper.readValue(response, ESResponse.class);
    }

    private List<EsAssignmentActivityDto> getSourceChangeFromResponse(ESResponse esResponse) {
        log.info("[APN User {} get assignment Activity: get changeFields Source data from response", SecurityUtils.getUserId());
        if (esResponse.getHits() == null || esResponse.getHits().getHits() == null) {
            return null;
        }

        List<ESHit> activityHits = esResponse.getHits().getHits();
        List<EsAssignmentActivityDto> activityDTOList = new ArrayList<>();
        for (ESHit hit : activityHits) {
            Map<String, Object> source = hit.getSource();
            activityDTOList.add(JsonUtil.fromJson(JsonUtil.toJson(source), EsAssignmentActivityDto.class));
        }

        return activityDTOList;

    }
}
