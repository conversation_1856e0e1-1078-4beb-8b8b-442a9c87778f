package com.altomni.apn.jobdiva.service.dto.invoice;

import lombok.Data;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;

@Data
public class MongodbToJobDivaTempDTO {

    private BigInteger id;

    private BigInteger invoiceId ;

    private String groupInvoiceNumber ;

    private String groupInvoiceStatus ;

    private Timestamp InvoiceDate;

    private String groupInvoiceType;
}