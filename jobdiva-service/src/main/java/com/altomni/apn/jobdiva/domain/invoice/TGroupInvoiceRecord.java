package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceTypeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;

/**
 * 分组和发票映射;
 * <AUTHOR> zhang.lei
 * @date : 2023-6-9
 */
@ApiModel(value = "分组和发票映射",description = "")
@Entity
@Table(name="t_group_invoice_record")
@Data
public class TGroupInvoiceRecord extends AbstractAuditingEntity implements Serializable,Cloneable{
    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id ;

    /** 分组发票id */
    @ApiModelProperty(name = "分组发票id")
    @Column(name = "group_invoice_id")
    private BigInteger groupInvoiceId ;

    /** 发票id */
    @ApiModelProperty(name = "发票id")
    @Column(name = "invoice_id")
    private BigInteger invoiceId ;

    /** 员工id */
    @ApiModelProperty(name = "员工id")
    @Column(name = "talent_id")
    private BigInteger talentId ;

    /** 发票类型 1-Regular，2-Expense */
    @ApiModelProperty(name = "发票类型 1-Regular，2-Expense",notes = "")
    @Column(name = "internal_invoice_type")
    @Convert(converter = InvoiceTypeConverter.class)
    private InvoiceType internalInvoiceType ;

    /** 状态 0-无效 1-有效 */
    @ApiModelProperty(name = "状态 0-无效 1-有效")
    @Column(name = "status")
    private Integer status ;
}
