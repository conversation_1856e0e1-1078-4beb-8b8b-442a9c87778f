package com.altomni.apn.jobdiva.repository.process;

import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingProcessOperationDetails;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the OnBoardingPackageDocuments entity.
 */
@Repository
public interface OnBoardingProcessOperationDetailsRepository extends JpaRepository<OnBoardingProcessOperationDetails, Long> {

    @Query(value = "select count(a.id) from onboarding_process_operation_details a where a.history_id = ?1 and a.operation_status = ?2 ", nativeQuery = true)
    Integer countByHistoryIdAndOperationStatus(Long historyId, Integer operationStatus);
}
