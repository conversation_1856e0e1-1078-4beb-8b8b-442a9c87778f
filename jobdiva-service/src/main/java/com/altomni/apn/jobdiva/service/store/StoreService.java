package com.altomni.apn.jobdiva.service.store;

import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.store.UploadUrlDto;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;


@Component
@FeignClient(value = "common-service")
public interface StoreService {

    @PostMapping(value = "/common/api/v3/s3/common/upload", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    ResponseEntity<String> uploadDocument(@RequestPart(value = "file") MultipartFile file , @RequestParam("key") String key, @RequestParam("uploadType") String uploadType) throws IOException;
    @PostMapping(value = "/common/api/v3/s3/jobdiva/invoice/upload-document", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    ResponseEntity<String> uploadInvoiceDocument(@RequestPart(value = "file") MultipartFile file , @RequestParam("key") String key) throws IOException;

    @GetMapping("/common/api/v3/s3/store/file/{uuid}/{uploadType}")
    ResponseEntity<CloudFileObjectMetadata> downloadDocument(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @GetMapping("/common/api/v3/s3/store/file/{uuid}/{uploadType}")
    ResponseEntity<CloudFileObjectMetadata> getFileFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);


    @PostMapping("/common/api/v3/s3/store/upload-url")
    ResponseEntity<StoreGetUploadUrlVO> getPresignedCommonUploadUrlFromS3WithPostPolicy(@RequestBody UploadUrlDto uploadUrlDto);

    @GetMapping("/common/api/v3/s3/store/url/{uuid}/{uploadType}")
    ResponseEntity<String> getDisplayUrlFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

}
