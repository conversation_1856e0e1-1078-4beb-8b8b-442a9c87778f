package com.altomni.apn.jobdiva.service.dto.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * A record
 */
@ApiModel(description = "record for timeSheet")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class TimeSheetRecordDTO  implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "work date")
    private LocalDate workDate;

    @ApiModelProperty(value = "week date")
    private String  weekDay;

    @ApiModelProperty(value = "work hours")
    private Float workHours=0.0f;


    @ApiModelProperty(value = "regularHours")
    private Float regularHours=0.0f;


    @ApiModelProperty(value = "regularHours")
    private Float overTime=0.0f;

    @ApiModelProperty(value = "doubleTime")
    private Float doubleTime=0.0f;


    @ApiModelProperty(value = "totalTime")
    private Float totalHours=0.0f;

    @ApiModelProperty(value = "status")
    private TimeSheetStatus status;

    @ApiModelProperty(value = "time sheet type ")
    private TimeSheetType timeSheetType;


    @ApiModelProperty(value = "date selected ")
    private boolean isSelected;

    @ApiModelProperty(value = "time in ")
    private String timeIn;

    @ApiModelProperty(value = "time out ")
    private String timeOut;

    @ApiModelProperty(value = "break in/out ")
    private List<BreakTimeSaveDto> breakTime;
}
