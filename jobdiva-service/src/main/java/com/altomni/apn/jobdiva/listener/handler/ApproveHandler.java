package com.altomni.apn.jobdiva.listener.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.jobdiva.domain.timesheet.ApproveRecord;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.repository.timesheet.ApproveRecordRepository;
import com.altomni.apn.jobdiva.repository.timesheet.ExpenseRecordRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetBreakTimeRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetRecordRepository;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Component
@AllArgsConstructor
public class ApproveHandler implements JobdivaToApnHandler {

    @Resource
    private ApproveRecordRepository approveRecordRepository;

    @Resource
    private TimeSheetBreakTimeRepository breakTimeRepository;

    @Resource
    private TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    private ExpenseRecordRepository expenseRecordRepository;

    @Override
    public void execute(JSONObject jsonObject) {
        log.info("[hr svr ApproveHandler] jobdiva to apn approve save to db is start");
        JSONArray jsonArray = jsonObject.getJSONArray(TIMESHEET_APPROVE);
        if (jsonArray.isEmpty()) {
            return;
        }
        List<ApproveRecord> approveRecordList = jsonArray.toJavaList(ApproveRecord.class);
        if (CollectionUtils.isEmpty(approveRecordList)) {
            log.info("[hr svr ApproveHandler] jobdiva to apn approve save to db is error, approve is null");
            return;
        }
        approveRecordRepository.saveAllAndFlush(approveRecordList);
        ApproveRecord approveRecord = approveRecordList.get(0);
        if (CommentsType.TIME_SHEET == approveRecord.getType()) {
            updateTimeSheetStatus(approveRecordList.stream().map(ApproveRecord::getRecordId).collect(Collectors.toSet()), approveRecord.getStatus());
        } else {
            updateExpenseStatus(approveRecordList.stream().map(ApproveRecord::getRecordId).collect(Collectors.toSet()), approveRecord.getStatus());
        }
        log.info("[hr svr ApproveHandler] jobdiva to apn approve save to db is success");
    }

    private void updateTimeSheetStatus(Set<Long> recordIds, TimeSheetStatus status) {
        List<TimeSheetRecord> timeSheetRecords = timeSheetRecordRepository.findAllByIdIs(recordIds);
        if(CollectionUtils.isEmpty(timeSheetRecords)) {
            return;
        }
        for(TimeSheetRecord timeSheetRecord: timeSheetRecords) {
            Set<LocalDate> dates = TimeSheetUtil.getWeekByWeekEndingDate(timeSheetRecord.getWeekStart(), timeSheetRecord.getWeekEnd());
            timeSheetRecordRepository.updateStatusByDates(dates,timeSheetRecord.getTalentId(),status.toDbValue(),TimeSheetStatus.NO_RECORD.toDbValue(),timeSheetRecord.getAssignmentId());
            breakTimeRepository.updateStatusByDates(dates,timeSheetRecord.getTalentId(),status.toDbValue(),TimeSheetStatus.NO_RECORD.toDbValue(),timeSheetRecord.getAssignmentId());
        }
    }

    private void updateExpenseStatus(Set<Long> recordIds, TimeSheetStatus status) {
        List<ExpenseRecord> expenseRecords = expenseRecordRepository.findAllByIdIs(new ArrayList<>(recordIds));
        if (CollectionUtils.isEmpty(expenseRecords)) {
            return;
        }
        for (ExpenseRecord expenseRecord: expenseRecords) {
            expenseRecordRepository.updateStatusByDatesAndIndex(TimeSheetUtil.getWeekByWeekEndingDate(expenseRecord.getWeekStart(), expenseRecord.getWeekEnd()), expenseRecord.getTalentId(), status.toDbValue(), expenseRecord.getAssignmentId(), expenseRecord.getExpenseIndex());
        }
    }

    @Override
    public boolean isSupport(JobdivaDataSyncTypeEnum typeEnum) {
        return JobdivaDataSyncTypeEnum.TIME_SHEET_APPROVE == typeEnum;
    }

}
