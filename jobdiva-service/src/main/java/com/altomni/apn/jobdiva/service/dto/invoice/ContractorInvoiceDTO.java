package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceStatusType;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * ContractorInvoiceDTO
 * <AUTHOR> zhang.lei
 * @date : 2023-6-6
 */
@ApiModel(description = "contractorInvoiceDTO")
@Data
public class ContractorInvoiceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer id;

    private String invoiceNumber ;

    private String groupInvoiceNumber ;

    private String pONumber ;

    private String clientInvoiceNumber ;

    private Long tenantId ;

    private Long talentId ;

    private String talentName ;

    private Long companyId ;

    private String companyName ;

    private Long companyContactId ;

    private String companyContactName ;

    private Long jobId ;

    private String jobTitle ;

    private Long assignmentId ;

    private LocalDate weekEndingDate ;

    private String fileUrl ;

    private InvoiceType invoiceType ;

    private LocalDate invoiceDate ;

    private InvoiceStatusType invoiceStatus ;

    private AssignmentDivision assignmentDivision ;

    private String note ;

    private BigDecimal totalAmount ;

    private Long approverId ;

    private String approverName ;

    private String expenseComments ;

    private String expenseSubmitDate ;

    private LocalDate approvalDate ;

    private String createdBy ;

    private LocalDate createdDate ;

    private String lastModifiedBy ;

    private LocalDate lastModifiedDate ;
}
