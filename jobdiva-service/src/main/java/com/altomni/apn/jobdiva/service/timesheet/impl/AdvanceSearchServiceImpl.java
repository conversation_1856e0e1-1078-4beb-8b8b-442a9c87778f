package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.SearchColumnMap;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.SearchRelationType;
import com.altomni.apn.jobdiva.service.dto.timesheet.AdvanceSearchDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.ColumnSearchUnitDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.SearchConditionDTO;
import com.altomni.apn.jobdiva.service.timesheet.AdvanceSearchService;
import com.altomni.apn.jobdiva.service.user.UserService;
import com.altomni.apn.jobdiva.service.vo.timesheet.ExpenseListAmVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimeSheetSummaryAmVO;
import com.altomni.apn.jobdiva.util.CommonAmIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.altomni.apn.common.utils.DateUtil.fromInstantToDateString;
import static com.altomni.apn.common.utils.SqlUtil.PARTITION_COUNT_999;

@Slf4j
@Service("advanceSearchService")
public class AdvanceSearchServiceImpl extends BaseServiceImpl implements AdvanceSearchService {

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Resource
    private UserService userService;

    @Override
    public SummaryDataVO searchForTimeSheet(AdvanceSearchDTO dto, List<Long> talents, List<Long> assignmentIds) {
        SummaryDataVO vo = new SummaryDataVO();
        vo.setPageNum(dto.getPageNum());
        if (dto.getPageSize() == null) {
            dto.setPageSize(10);
        }
        if (dto.getPageNum() == null || dto.getPageNum()<=0) {
            dto.setPageNum(1);
        }
        int startItem = (dto.getPageNum()-1) * dto.getPageSize();
        int endItem = dto.getPageSize();
        String oderBy = "";
        String sort = "";
        if (dto.getOrder() == null || dto.getOrderBy() == null) {
            oderBy = TimeSheetTableOrderType.WEEK_END.getOrderSql() + TimeSheetTableSortType.DESC.getOrderSql();
            sort = " ";
        } else {
            if (dto.getOrderBy() ==TimeSheetTableOrderType.STATUS) {
                if (dto.getOrder() == TimeSheetTableSortType.ASC) {
                    oderBy = "ORDER BY CASE status WHEN 2 THEN 0 WHEN 5 THEN 1 WHEN 0 THEN 2 WHEN 3 THEN 3 WHEN 1 THEN 4 WHEN 4 THEN 5 END ";
                } else {
                    oderBy = "ORDER BY CASE status WHEN 2 THEN 5 WHEN 5 THEN 4 WHEN 0 THEN 3 WHEN 3 THEN 2 WHEN 1 THEN 1 WHEN 4 THEN 0 END ";
                }
            } else {
                oderBy = dto.getOrderBy().getOrderSql();
                sort = dto.getOrder().getOrderSql();
            }
        }
        String recordIdsCondition = "";
        if (CollUtil.isNotEmpty(dto.getRecordIds())) {
            StringBuilder sb = new StringBuilder();
            sb.append(" and (");
            Iterator<List<Long>> iterator = CollUtil.split(dto.getRecordIds(), PARTITION_COUNT_999).iterator();
            while (iterator.hasNext()) {
                List<Long> list = iterator.next();
                List<String> idStrList = JSONUtil.toList(JSONUtil.parseArray(list), String.class);
                sb.append(" record_id in (").append(String.join(",", idStrList)).append(")");
                if (iterator.hasNext()) {
                    sb.append(" or");
                }
            }
            sb.append(") ");
            recordIdsCondition = sb.toString();
        }
        String limitSql = doGetLimitSql(talents, assignmentIds);
        StringBuilder conditionStr = new StringBuilder();
        dto.setRecordType(RecordType.TIME_SHEET);
        conditionStr.append(composeFilterSql(dto));
        String sql = """
                select AAA.* from (select TSR.work_date as ending_date,TSR.total_hours as total_hours ,TSR.regular_hours as regular_hours ,TSR.over_time as over_time,
                TSR.double_time as double_time,TSR.full_name as talent_name, TSR.talent_id ,TSR.job_title as job_title,TSR.company_name as company_name ,TSR.submitted_date as applied_date,
                TSR.approved_date as approved_date,TSR.record_id as id, TSR.status as status,TSR.time_sheet_type as sheet_type,TSR.start_date as start_date,
                TSR.calculate_type as calculate_method,TSR.end_date as end_date,TSR.job_id as job_id,TSR.manager as manager,TSR.am_ids as am_ids ,TSR.am as am, TSR.am_approver_id as am_approver_id,TSR.am_approver as am_approver,
                TSR.primary_manager as primary_manager ,TSR.assignment_id as assignment_id, TSR.tenant_id as tenant_id,TSR.employment_category, TSR.billing_frequency, 
                TSR.payment_frequency, TSR.assignment_division, TSR.week_start as week_start, TSR.week_end as week_end, TSR.week_ending_date
                from time_sheet_week_ending_record TSR
                where TSR.assignment_status = 1 and TSR.tenant_id = ?3 and TSR.start_date <= now()
                """ + limitSql + recordIdsCondition + conditionStr + " ) AAA " + oderBy + sort + ", id desc " + " limit ?1,?2 ";
        String countSql = """
                select count(TSR.id)
                from time_sheet_week_ending_record TSR
                where TSR.assignment_status = 1 and TSR.tenant_id = ?3 and TSR.start_date <= now()
                """ + limitSql + recordIdsCondition + conditionStr;
        List<TimeSheetSummaryAmVO> result = null;
        try {
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture<List<TimeSheetSummaryAmVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                Query query = entityManager.createNativeQuery(sql, TimeSheetSummaryAmVO.class);
                query.setParameter(1,startItem);
                query.setParameter(2,endItem);
                query.setParameter(3, SecurityUtils.getTenantId());
                return query.getResultList();
            });
            CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                Query query = entityManager.createNativeQuery(countSql);
                query.setParameter(3, SecurityUtils.getTenantId());
                List<Object> totalItems = query.getResultList();
                return((BigInteger)totalItems.get(0)).intValue();
            });
            result = dataFuture.get();
            vo.setTotalItems(countFuture.get());
        } catch (Exception e) {
            log.error("error", e);
        }
        if (CollUtil.isNotEmpty(result)) {
            //手动mapping 一下 am 和 am approver 将 amids 和 apApproverId 变成一个 id 集合
            List<Long> combinedIds = CommonAmIdUtil.getCombinedIds(result, TimeSheetSummaryAmVO::getAmIds, TimeSheetSummaryAmVO::getAmApproverId);
            Map<Long, String> userIdToNameMap = CommonAmIdUtil.createUserIdToNameMap(combinedIds, userService);
            CommonAmIdUtil.mapNamesToObjects(result, TimeSheetSummaryAmVO::getAmIds, TimeSheetSummaryAmVO::getAmApproverId, TimeSheetSummaryAmVO::setAm, TimeSheetSummaryAmVO::setAmApprover, userIdToNameMap);
            result = result.stream().peek(timeSheetSummaryAmVO -> {
                String mr;
                if (super.isNoHourStatus(timeSheetSummaryAmVO.getStatus(), timeSheetSummaryAmVO.getRegularHours())) {
                    if (StrUtil.isNotBlank(timeSheetSummaryAmVO.getAmApprover())) {
                        mr = timeSheetSummaryAmVO.getAmApprover();
                    } else {
                        mr = timeSheetSummaryAmVO.getPrimaryManager();
                    }
                } else {
                    TimeSheetStatus status = timeSheetSummaryAmVO.getStatus();
                    switch (status) {
                        case MISSING:
                        case APPLIED_APPROVE:
                        case DRAFT:
                        case NO_RECORD:
                            mr = timeSheetSummaryAmVO.getPrimaryManager();
                            break;
                        case APPROVED:
                        case REJECTED:
                        default:
                            mr = timeSheetSummaryAmVO.getAmApprover();
                            if(StringUtils.isBlank(mr))
                            {
                                mr = timeSheetSummaryAmVO.getManager();
                            }
                            if(StringUtils.isBlank(mr))
                            {
                                mr = timeSheetSummaryAmVO.getAm();
                            }
                            if(StringUtils.isBlank(mr))
                            {
                                mr = timeSheetSummaryAmVO.getPrimaryManager();
                            }
                            break;
                    }
                }
                timeSheetSummaryAmVO.setManager(mr);
                timeSheetSummaryAmVO.setAppliedDateStr(fromInstantToDateString(timeSheetSummaryAmVO.getAppliedDate(), dto.getTimezone()));
                timeSheetSummaryAmVO.setApprovedDateStr(fromInstantToDateString(timeSheetSummaryAmVO.getApprovedDate(), dto.getTimezone()));
            }).toList();
        }
        vo.setData(result);
        return vo;
    }

    @Override
    public SummaryDataVO searchForExpense(AdvanceSearchDTO dto,List<Long> talents, List<Long> assignmentIds) {
        SummaryDataVO vo = new SummaryDataVO();
        if (dto.getPageNum() != null) {vo.setPageNum(dto.getPageNum());}
        if (dto.getPageSize() == null) { dto.setPageSize(10);}
        if (dto.getPageNum() == null || dto.getPageNum() <= 0) {
            dto.setPageNum(1);
        }
        int startItem = (dto.getPageNum()-1) * dto.getPageSize();
        int endItem = dto.getPageSize();
        StringBuilder conditionStr = new StringBuilder();
        dto.setRecordType(RecordType.EXPENSE);
        conditionStr.append(composeFilterSql(dto));
        String oderBy="";
        String sort="";
        if (dto.getOrder() == null || dto.getOrderBy() == null) {
            oderBy = TimeSheetTableOrderType.WEEK_END.getOrderSql() + " " + TimeSheetTableSortType.DESC.getOrderSql();
            sort = " ";
        } else {
            if (dto.getOrderBy() == TimeSheetTableOrderType.STATUS) {
                if (dto.getOrder() == TimeSheetTableSortType.ASC) {
                    oderBy = "ORDER BY CASE status WHEN 2 THEN 0 WHEN 1 THEN 1 WHEN 4 THEN 2  END ";
                } else {
                    oderBy = "ORDER BY CASE status WHEN 4 THEN 0 WHEN 1 THEN 1 WHEN 2 THEN 2  END  ";
                }
            } else if (dto.getOrderBy() == TimeSheetTableOrderType.MANAGER) {
                oderBy = TimeSheetTableOrderType.MANAGER_EXPENSE_AM.getOrderSql();
                sort = dto.getOrder().getOrderSql();
            } else {
                oderBy = dto.getOrderBy().getOrderSql();
                sort = dto.getOrder().getOrderSql();
            }
        }
        String recordIds = "";
        if (CollUtil.isNotEmpty(dto.getRecordIds())) {
            StringBuilder sb = new StringBuilder();
            sb.append(" and ( ");
            Iterator<List<Long>> iterator = CollUtil.split(dto.getRecordIds(), PARTITION_COUNT_999).iterator();
            while (iterator.hasNext()) {
                List<Long> list = iterator.next();
                List<String> idStrList = JSONUtil.toList(JSONUtil.parseArray(list), String.class);
                sb.append(" TSR.record_id in (").append(String.join(",", idStrList)).append(") ");
                if (iterator.hasNext()) {
                    sb.append(" or ");
                }
            }
            sb.append(" ) ");
            recordIds = sb.toString();
        }
        String limitSql = doGetExpenseLimitSql(talents, assignmentIds);
        String sql = """
                select AAA.* from (select TSR.work_date as ending_date, TSR.cost AS amount , apr.currency,
                TSR.full_name as talent_name,TSR.talent_id as talent_id,TSR.job_title job_title,TSR.company_name as company_name ,TSR.submitted_date as applied_date,
                TSR.approved_date as approved_date,TSR.record_id as id, TSR.status as status,TSR.start_date as start_date,TSR.end_date as end_date,TSR.job_id as job_id,
                TSR.manager as manager,TSR.am_ids, TSR.am_approver_id, TSR.am as am,TSR.am_approver as am_approver,TSR.primary_manager as primary_manager ,TSR.assignment_id as assignment_id, TSR.expense_type as expense_type, TSR.tenant_id as tenant_id,
                TSR.employment_category, TSR.billing_frequency, TSR.payment_frequency, TSR.assignment_division, TSR.week_start as week_start, TSR.week_end as week_end, TSR.week_ending_date, TSR.expense_index
                from time_sheet_expense_week_ending_record TSR
                LEFT JOIN assignment_pay_rate apr ON apr.assignment_id = TSR.assignment_id AND apr.content_type = 1 AND apr.pay_type = 3
                where TSR.tenant_id = ?3 and TSR.assignment_status = 1 and TSR.status in (5,1,2,4)
                """ + limitSql + recordIds + conditionStr + " ) AAA  " + oderBy + sort + ", id desc " + " limit ?1,?2";
        String countSql = """
                select count(TSR.id)
                from time_sheet_expense_week_ending_record TSR
                LEFT JOIN assignment_pay_rate apr ON apr.assignment_id = TSR.assignment_id AND apr.content_type = 1 AND apr.pay_type = 3
                where TSR.tenant_id = ?1 and TSR.assignment_status = 1 and TSR.status in (5,1,2,4)
                """ + limitSql + recordIds + conditionStr;
        try {
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture<List<ExpenseListAmVO>> dateFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                Query query = entityManager.createNativeQuery(sql, ExpenseListAmVO.class);
                query.setParameter(1,startItem);
                query.setParameter(2,endItem);
                query.setParameter(3, SecurityUtils.getTenantId());
                return query.getResultList();
            });
            CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                Query query = entityManager.createNativeQuery(countSql);
                query.setParameter(1, SecurityUtils.getTenantId());
                List<Object> totalItems = query.getResultList();
                return ((BigInteger)totalItems.get(0)).intValue();
            });
            List<ExpenseListAmVO> result = dateFuture.get();
            vo.setTotalItems(countFuture.get());
            if (CollUtil.isNotEmpty(result)) {
                List<Long> combinedIds = CommonAmIdUtil.getCombinedIds(result, ExpenseListAmVO::getAmIds, ExpenseListAmVO::getAmApproverId);
                Map<Long, String> userIdToNameMap = CommonAmIdUtil.createUserIdToNameMap(combinedIds, userService);
                CommonAmIdUtil.mapNamesToObjects(result, ExpenseListAmVO::getAmIds, ExpenseListAmVO::getAmApproverId, ExpenseListAmVO::setAm, ExpenseListAmVO::setAmApprover, userIdToNameMap);
                List<EnumCurrency> enumCurrencyList = enumCurrencyService.findAllEnumCurrency();
                Map<Integer, EnumCurrency> currencyMap = enumCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, a -> a));
                result = result.stream().peek(expenseListAmVO -> {
                    String mr = expenseListAmVO.getAmApprover();
                    if (StrUtil.isBlank(mr)) {
                        mr = expenseListAmVO.getManager();
                    }
                    if (StrUtil.isBlank(mr)) {
                        mr = expenseListAmVO.getPrimaryManager();
                    }
                    expenseListAmVO.setManager(mr);
                    expenseListAmVO.setAppliedDateStr(fromInstantToDateString(expenseListAmVO.getAppliedDate(), dto.getTimezone()));
                    expenseListAmVO.setApprovedDateStr(fromInstantToDateString(expenseListAmVO.getApprovedDate(), dto.getTimezone()));
                    String moneyStr = currencyMap.get(expenseListAmVO.getCurrency()).getLabel1();
                    expenseListAmVO.setAmountFormat(moneyStr + new BigDecimal(String.valueOf(expenseListAmVO.getAmount() == null ? 0F: expenseListAmVO.getAmount())).setScale(2));
                }).toList();
            }
            vo.setData(result);
        } catch (Exception e) {
            log.error("error", e);
        }
        return vo;
    }

    /**
     * 构建限制条件的sql，为空则无限制，否则去最大集，即使用or
     * @param talents
     * @param assignmentIds
     * @return
     */
    private String doGetExpenseLimitSql(List<Long> talents, List<Long> assignmentIds) {
        if (CollUtil.isEmpty(talents) && CollUtil.isEmpty(assignmentIds)) {
            return StrUtil.EMPTY;
        }
        StringBuilder talentIdsSqlSb = new StringBuilder();
        if (CollUtil.isNotEmpty(talents)) {
            Iterator<List<Long>> iterator = CollUtil.split(talents, PARTITION_COUNT_999).iterator();
            while (iterator.hasNext()) {
                List<Long> list = iterator.next();
                List<String> idStrList = JSONUtil.toList(JSONUtil.parseArray(list), String.class);
                talentIdsSqlSb.append(" TSR.talent_id in (").append(String.join(",", idStrList)).append(") ");
                if (iterator.hasNext()) {
                    talentIdsSqlSb.append(" or");
                }
            }
        }
        StringBuilder assignmentIdsSqlSb = new StringBuilder();
        if (CollUtil.isNotEmpty(assignmentIds)) {
            Iterator<List<Long>> iterator = CollUtil.split(assignmentIds, PARTITION_COUNT_999).iterator();
            while (iterator.hasNext()) {
                List<Long> list = iterator.next();
                List<String> idStrList = JSONUtil.toList(JSONUtil.parseArray(list), String.class);
                assignmentIdsSqlSb.append(" TSR.assignment_id in (").append(String.join(",", idStrList)).append(") ");
                if (iterator.hasNext()) {
                    assignmentIdsSqlSb.append(" or");
                }
            }
        }
        if (StrUtil.isNotBlank(talentIdsSqlSb) && StrUtil.isNotBlank(assignmentIdsSqlSb)) {
            return " AND (" + talentIdsSqlSb + " or " + assignmentIdsSqlSb + ") ";
        }
        return " AND (" + talentIdsSqlSb + assignmentIdsSqlSb + ") ";
    }


    /**
     * 构建限制条件的sql，为空则无限制，否则去最大集，即使用or
     * @param talents
     * @param assignmentIds
     * @return
     */
    private String doGetLimitSql(List<Long> talents, List<Long> assignmentIds) {
        if (CollUtil.isEmpty(talents) && CollUtil.isEmpty(assignmentIds)) {
            return StrUtil.EMPTY;
        }
        StringBuilder talentIdsSqlSb = new StringBuilder();
        if (CollUtil.isNotEmpty(talents)) {
            Iterator<List<Long>> iterator = CollUtil.split(talents, PARTITION_COUNT_999).iterator();
            while (iterator.hasNext()) {
                List<Long> list = iterator.next();
                List<String> idStrList = JSONUtil.toList(JSONUtil.parseArray(list), String.class);
                talentIdsSqlSb.append(" talent_id in (").append(String.join(",", idStrList)).append(") ");
                if (iterator.hasNext()) {
                    talentIdsSqlSb.append(" or");
                }
            }
        }
        StringBuilder assignmentIdsSqlSb = new StringBuilder();
        if (CollUtil.isNotEmpty(assignmentIds)) {
            Iterator<List<Long>> iterator = CollUtil.split(assignmentIds, PARTITION_COUNT_999).iterator();
            while (iterator.hasNext()) {
                List<Long> list = iterator.next();
                List<String> idStrList = JSONUtil.toList(JSONUtil.parseArray(list), String.class);
                assignmentIdsSqlSb.append(" assignment_id in (").append(String.join(",", idStrList)).append(") ");
                if (iterator.hasNext()) {
                    assignmentIdsSqlSb.append(" or");
                }
            }
        }
        if (StrUtil.isNotBlank(talentIdsSqlSb) && StrUtil.isNotBlank(assignmentIdsSqlSb)) {
            return " AND (" + talentIdsSqlSb + " or " + assignmentIdsSqlSb + ") ";
        }
        return " AND (" + talentIdsSqlSb + assignmentIdsSqlSb + ") ";
    }

    private String composeFilterSql(AdvanceSearchDTO dto) {
        List<SearchConditionDTO> conditions = dto.getConditions();
        if (CollectionUtils.isEmpty(conditions)) {return " ";}
        StringBuilder tSql = new StringBuilder();
        for (SearchConditionDTO condition: conditions) {
            SearchRelationType relation  = condition.getRelation();
            List<ColumnSearchUnitDTO> columns = condition.getSearches();
            if (CollectionUtils.isEmpty(columns)) {return " ";}
            tSql.append(SearchRelationType.OR.getSql()).append(" (");
            StringBuilder cSql = new StringBuilder(" ");
            for (ColumnSearchUnitDTO column:columns) {
                cSql.append(relation.getSql()).append(" ");
                String columnName = SearchColumnMap.COLUMN_MAP.get(column.getColumnName());
                if (columnName == null) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ADVANCE_COMPOSE_SQL_COLUMN_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
                }
                List<String> values = column.getValues();
                if (columnName.equals(SearchColumnMap.SPEC_COLUMN_STATUS) ) {
                    cSql.append(toSelectStatus(columnName,values));
                } else if (Objects.equals(SearchColumnMap.SPEC_EMPLOYMENT_CATEGORY, columnName)) {
                    cSql.append(toSelectCategory(columnName, values));
                } else if (Objects.equals(SearchColumnMap.SPEC_BILLING_FREQUENCY, columnName)) {
                    cSql.append(toSelectFrequency(columnName, values));
                } else if (Objects.equals(SearchColumnMap.SPEC_PAYING_FREQUENCY, columnName)) {
                    cSql.append(toSelectFrequency(columnName, values));
                } else if (Objects.equals(SearchColumnMap.SPEC_ASSIGNMENT_DIVISION, columnName)) {
                    cSql.append(toSelectDivision(columnName, values));
                } else if(columnName.equals(SearchColumnMap.SPEC_COLUMN_JOB_ID)) {
                    cSql.append(toSelectJobId(columnName,values));
                } else if (SearchColumnMap.SPEC_COLUMN_WEEK_ENDING.equals(columnName)
                        || SearchColumnMap.SPEC_COLUMN_WEEK_END.equals(columnName)) {
                    if (values.size() != 2) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ADVANCE_COMPOSE_SQL_DATA_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
                    }
                    cSql.append(" ").append(columnName).append(" between  STR_TO_DATE('").append(values.get(0)).append("','%Y-%m-%d') and STR_TO_DATE('").append(values.get(1)).append("','%Y-%m-%d')");
                } else if (columnName.equals(SearchColumnMap.SPEC_COLUMN_MANAGER)) {
                    if (RecordType.TIME_SHEET == dto.getRecordType()) {
                        cSql.append(" (CASE WHEN ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_STATUS);
                        cSql.append(" = 0 or ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_STATUS);
                        cSql.append(" = 1 or ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_STATUS);
                        cSql.append(" = 3 or ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_STATUS);
                        cSql.append(" = 5 or ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_STATUS);
                        cSql.append(" = 4 and ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_REGULAR_HOURS);
                        cSql.append(" is null THEN ");
                        cSql.append(toLikeStr(SearchColumnMap.SPEC_COLUMN_PRIMARY_MANAGER + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" ELSE CASE WHEN ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_STATUS);
                        cSql.append(" = 2 or ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_STATUS);
                        cSql.append(" = 4 and ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_REGULAR_HOURS);
                        cSql.append(" is not null THEN ");
                        cSql.append(" CASE WHEN ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_MANAGER);
                        cSql.append(" is not null THEN ");
                        cSql.append(toLikeStr(SearchColumnMap.SPEC_COLUMN_MANAGER + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" ELSE CASE WHEN ");
                        String amField = SearchColumnMap.SPEC_COLUMN_AM_APPROVER;
                        cSql.append(amField);
                        cSql.append(" is not null THEN ");
                        cSql.append(toLikeStr(amField + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" ELSE ");
                        cSql.append(toLikeStr(SearchColumnMap.SPEC_COLUMN_PRIMARY_MANAGER + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" END ");
                        cSql.append(" END ");
                        cSql.append(" ELSE CASE WHEN ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_MANAGER);
                        cSql.append(" is not null THEN ");
                        cSql.append(toLikeStr(SearchColumnMap.SPEC_COLUMN_MANAGER + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" ELSE CASE WHEN ");
                        cSql.append(amField);
                        cSql.append(" is not null THEN ");
                        cSql.append(toLikeStr(amField + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" ELSE ");
                        cSql.append(toLikeStr(SearchColumnMap.SPEC_COLUMN_PRIMARY_MANAGER + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" END ");
                        cSql.append(" END ");
                        cSql.append(" END ");
                        cSql.append(" END) ");
                    } else {
                        cSql.append(" (CASE WHEN ");
                        cSql.append(SearchColumnMap.SPEC_COLUMN_MANAGER);
                        cSql.append(" is not null THEN ");
                        cSql.append(toLikeStr(SearchColumnMap.SPEC_COLUMN_MANAGER + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" ELSE CASE WHEN ");
                        String amField = SearchColumnMap.SPEC_COLUMN_AM;
                        if (RecordType.EXPENSE == dto.getRecordType()) {
                            amField = SearchColumnMap.SPEC_COLUMN_AM_APPROVER;
                        }
                        cSql.append(amField);
                        cSql.append(" is not null THEN ");
                        cSql.append(toLikeStr(amField + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" ELSE ");
                        cSql.append(toLikeStr(SearchColumnMap.SPEC_COLUMN_PRIMARY_MANAGER + " like ", values, column.getRelation().getSql(), false));
                        cSql.append(" END ");
                        cSql.append(" END) ");
                    }
                } else {
                    cSql.append(toSelectStr("lower("+columnName+") like ",values , column.getRelation().getSql()));
                }
                cSql.append("  ");

            }
            String cq = cSql.substring(5);
            tSql.append(cq).append(" )");
        }
        log.info("----------------------------------------conditionSql=" + tSql.substring(5));
        if (tSql.toString().trim().length() == 0) {return "";}
        return " and "+ tSql.substring(5);
    }

    private String toSelectStatus(String filed, List<String> list) {
        StringBuilder str = new StringBuilder(" ");
        for(String s: list) {
            TimeSheetStatus value = TimeSheetStatus.valueOf(s);
            str.append(",").append(value.toDbValue());
        }
        String result = filed + " in " + "( " + str.substring(2) + " )";
        log.info("---------------------------------srt:" + result);
        return result;
    }

    private String toSelectJobId(String filed,List<String> list) {
        StringBuilder str = new StringBuilder(" ");
        for (String s: list) {
            str.append(",").append(s);
        }
        String result = filed + " in " + "( " + str.substring(2) + " )";
        log.info("---------------------------------srt:" + result);
        return result;
    }

    private String toSelectCategory(String filed,List<String> list) {
        StringBuilder str = new StringBuilder(" ");
        for (String s: list) {
            AssignmentCategoryType assignmentCategoryType = AssignmentCategoryType.valueOf(s);
            str.append(",").append(assignmentCategoryType.toDbValue());
        }
        String result = filed + " in " + "( " + str.substring(2) + " )";
        log.info("---------------------------------srt:" + result);
        return result;
    }

    private String toSelectFrequency(String filed,List<String> list) {
        StringBuilder str = new StringBuilder(" ");
        for (String s: list) {
            TimeSheetFrequencyType timeSheetFrequencyType = TimeSheetFrequencyType.valueOf(s);
            str.append(",").append(timeSheetFrequencyType.toDbValue());
        }
        String result = filed + " in " + "( " + str.substring(2) + " )";
        log.info("---------------------------------srt:" + result);
        return result;
    }

    private String toSelectDivision(String filed,List<String> list) {
        StringBuilder str = new StringBuilder(" ");
        for (String s: list) {
            AssignmentDivision assignmentDivision = AssignmentDivision.valueOf(s);
            str.append(",").append(assignmentDivision.toDbValue());
        }
        String result = filed + " in " + "( " + str.substring(2) + " )";
        log.info("---------------------------------srt:" + result);
        return result;
    }

    private String toLikeStr(String field,List<String> list,String relation, boolean startWithRelation) {
        StringBuilder sb = new StringBuilder();
        sb.append("( ");
        for (int i = 0; i < list.size(); i++) {
            String value = list.get(i);
            if (i == 0 && startWithRelation) {
                sb.append(relation);
            } else if (i > 0) {
                sb.append(relation);
            }
            sb.append(" ").append(field).append("'%").append(value.toLowerCase()).append("%'");
        }
        sb.append(" )");
        log.info("---------------------------------srt:" + sb);
        return sb.toString();
    }

    private String toSelectStr(String field,List<String> list,String relation) {
        StringBuilder str = new StringBuilder();
        for (String s: list) {
            str.append(relation).append(" ").append(field).append("'%").append(s.toLowerCase()).append("%'");
        }
        String result = "( "+ str.substring(relation.length()) + " )";
        log.info("---------------------------------srt:" + result);
        return result;
    }


}
