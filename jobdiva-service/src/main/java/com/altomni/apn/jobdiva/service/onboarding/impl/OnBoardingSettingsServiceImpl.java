package com.altomni.apn.jobdiva.service.onboarding.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.ActionRequiredType;
import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingDocuments;
import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingPackages;
import com.altomni.apn.jobdiva.repository.process.OnBoardingDraftsRepository;
import com.altomni.apn.jobdiva.repository.settings.OnBoardingDocumentsRepository;
import com.altomni.apn.jobdiva.repository.settings.OnBoardingPackageDocumentsRepository;
import com.altomni.apn.jobdiva.repository.settings.OnBoardingPackagesRepository;
import com.altomni.apn.jobdiva.service.dto.onboarding.SearchDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackagesDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingSignatureDTO;
import com.altomni.apn.jobdiva.service.onboarding.OnBoardingSettingsService;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.user.UserService;
import com.altomni.apn.user.domain.user.ApnParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class OnBoardingSettingsServiceImpl implements OnBoardingSettingsService {

    private final Logger log = LoggerFactory.getLogger(OnBoardingSettingsServiceImpl.class);

    @PersistenceContext
    private EntityManager entityManager;
    @Resource
    private OnBoardingDocumentsRepository onboardingDocumentsRepository;
    @Resource
    private OnBoardingPackagesRepository onBoardingPackagesRepository;
    @Resource
    private OnBoardingPackageDocumentsRepository onBoardingPackageDocumentsRepository;
    @Resource
    private OnBoardingDraftsRepository onBoardingDraftsRepository;
    @Resource
    private UserService userService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Override
    public List<OnBoardingDocumentsDTO> getAllDocuments() {
        return onboardingDocumentsRepository.findAllByActivatedAndTenantIdOrderByActionRequiredAscLastModifiedDateDesc(Boolean.TRUE, SecurityUtils.getTenantId());
    }

    @Override
    public OnBoardingDocumentsDTO getByDocumentId(Long id) {
        Optional<OnBoardingDocuments> optionalDocument = onboardingDocumentsRepository.findAllByIdAndTenantIdAndActivated(id, SecurityUtils.getTenantId(), Boolean.TRUE);
        if (!optionalDocument.isPresent()) {
            log.error("[APN: OnBoarding Settings @{}] REST find document by document id error, can not find this document: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_GET_DOCUMENTS_ID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check permission
        OnBoardingDocuments documents = checkDocumentPermission(optionalDocument);
        if (ObjectUtil.isNull(documents)) {
            log.error("[APN: OnBoarding Settings @{}] REST find document by document id error, you have no permission to find this document: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NO_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return OnBoardingDocuments.toDto(documents);
    }

    private OnBoardingDocuments checkDocumentPermission(Optional<OnBoardingDocuments> optionalDocument) {
        OnBoardingDocuments document = optionalDocument.get();
        if (!document.getTenantId().equals(SecurityUtils.getTenantId())) {
            return null;
        }
        return document;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void deleteByDocumentId(Long id) {
        Optional<OnBoardingDocuments> optionalDocument = onboardingDocumentsRepository.findAllByIdAndTenantIdAndActivated(id, SecurityUtils.getTenantId(), Boolean.TRUE);
        if (!optionalDocument.isPresent()) {
            log.error("[APN: OnBoarding Settings @{}] REST delete document by document id error, can not find this document: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NOT_FIND_DOCUMENT_BY_PACKAGE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check permission
        if (ObjectUtil.isNull(checkDocumentPermission(optionalDocument))) {
            log.error("[APN: OnBoarding Settings @{}] REST delete document by document id error, can not find this document: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NO_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        OnBoardingDocuments boardingDocument = optionalDocument.get();
        if (!boardingDocument.isActivated()) {
            log.error("[APN: OnBoarding Settings @{}] REST delete document by document id error, this document has been deleted: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_APPROVAL_DELETE_STATUS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //soft delete document
        boardingDocument.setActivated(false);
        boardingDocument.setLastModifiedDate(Instant.now());
        boardingDocument.setLastModifiedBy(SecurityUtils.getUserName());
        onboardingDocumentsRepository.save(boardingDocument);
        //delete documents relationship
        onBoardingPackageDocumentsRepository.deleteAllByDocumentIdAndTenantId(id, SecurityUtils.getTenantId());
        //delete onBoarding-drafts
        onBoardingDraftsRepository.deleteAllByDocumentIdAndTenantId(id, SecurityUtils.getTenantId());
    }

    @Override
    public OnBoardingDocumentsDTO updateByDocumentId(OnBoardingDocumentsDTO dto) {
        Optional<OnBoardingDocuments> optionalDocument = onboardingDocumentsRepository.findAllByIdAndTenantIdAndActivated(dto.getId(), SecurityUtils.getTenantId(), Boolean.TRUE);
        if (!optionalDocument.isPresent()) {
            log.error("[APN: OnBoarding Settings @{}] REST update document by document id error, can not find this document: {}", SecurityUtils.getUserId(), dto.getId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_GET_DOCUMENTS_ID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check permission
        if (ObjectUtil.isNull(checkDocumentPermission(optionalDocument))) {
            log.error("[APN: OnBoarding Settings @{}] REST update document by document id error, you have no permission to update this document: {}", SecurityUtils.getUserId(), dto.getId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NO_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check duplicate
        Integer count = onboardingDocumentsRepository.countByDocumentIdAndNameAndTenantId(dto.getId(), dto.getName(), SecurityUtils.getTenantId());
        if (count > 0) {
            log.error("[APN: OnBoarding Settings @{}] REST update document by document id error, Document has duplicate document name: {}", SecurityUtils.getUserId(), dto.getId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_UPDATE_DOCUMENTS_ID_DUPLICATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        OnBoardingDocumentsDTO dtoResult = OnBoardingDocuments.toDto(onboardingDocumentsRepository.save(OnBoardingDocumentsDTO.fromDto(dto)));
        //delete unused s3Link TODO
//        if (ObjectUtil.isNotNull(optionalDocument.get().getS3Link())) {
//            storeService.s3Delete(properties.getStoreService().getOnboardingBucket(), optionalDocument.get().getS3Link());
//        }
        return dtoResult;
    }

    @Override
    public OnBoardingDocumentsDTO saveDocument(OnBoardingDocumentsDTO dto) {
        if (ObjectUtil.isNotNull(dto.getId())) {
            log.error("[APN: OnBoarding Settings @{}] REST save document info, document id is not null: {}", SecurityUtils.getUserId(), dto);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_DOCUMENTS_ID_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check duplicate
        Integer count = onboardingDocumentsRepository.countByNameAndTenantId(dto.getName(), SecurityUtils.getTenantId());
        if (count > 0) {
            log.error("[APN: OnBoarding Settings @{}] REST save document by document id error, Document has duplicate document name: {}", SecurityUtils.getUserId(), dto.getId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_UPDATE_DOCUMENTS_ID_DUPLICATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return OnBoardingDocuments.toDto(onboardingDocumentsRepository.save(OnBoardingDocumentsDTO.fromDto(dto)));
    }

    @Override
    public List<OnBoardingPackageDocumentsDTO> findDocumentsByPackageId(Long id) {
        Optional<OnBoardingPackages> optionalPackage = onBoardingPackagesRepository.findAllByIdAndTenantIdAndActivated(id, SecurityUtils.getTenantId(), Boolean.TRUE);
        if (!optionalPackage.isPresent()) {
            log.error("[APN: OnBoarding Settings @{}] REST find documents by package id error, can not find this package: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NOT_FIND_DOCUMENT_BY_PACKAGE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check permission
        if (ObjectUtil.isNull(checkPackagePermission(optionalPackage))) {
            log.error("[APN: OnBoarding Settings @{}] REST find documents by package id error, you have no permission to visit this package : {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NO_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return onBoardingPackageDocumentsRepository.findDocumentsByPackageId(id, SecurityUtils.getTenantId());
    }

    @Override
    public List<OnBoardingPackageDocumentsDTO> findAllDocumentsByPackageId(Long id, SearchDocumentsDTO conditionDto) {
        Optional<OnBoardingPackages> optionalPackage = onBoardingPackagesRepository.findAllByIdAndTenantIdAndActivated(id, SecurityUtils.getTenantId(), Boolean.TRUE);
        if (ObjectUtil.isNull(checkPackagePermission(optionalPackage))) {
            log.error("[APN: OnBoarding Settings @{}] REST find all documents by package id error, you have no permission to visit this package : {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NO_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        List<OnBoardingPackageDocumentsDTO> result = new ArrayList<>();
        List<Object[]> queryResult = new ArrayList<>();
        StringBuilder datasql = new StringBuilder("select d.id as id ,d.name as name ,d.action_required as actionRequired ,b.mandatory as mandatory ,ifnull(b.selected,'0') as selected from onboarding_documents d " +
                "        left join (select pd.document_id ,pd.package_id ,pd.mandatory , '1' as selected " +
                "        from onboarding_package_documents pd where pd.package_id = ?1 and pd.tenant_id = ?2) as b " +
                "        on d.id = b.document_id where d.activated = true and d.tenant_id = ?2 ");
        if (ObjectUtil.isNotNull(conditionDto.getName())) {
            datasql.append(" and LOCATE('").append(conditionDto.getName()).append("',d.name) > 0 ");
        }
        datasql.append(" order by ifnull(b.selected,'0') desc, b.mandatory desc,d.action_required");
        queryResult = entityManager.createNativeQuery(datasql.toString())
                .setParameter(1, id)
                .setParameter(2, SecurityUtils.getTenantId()).getResultList();
        if (CollectionUtil.isNotEmpty(queryResult)) {
            queryResult.forEach(s -> {
                OnBoardingPackageDocumentsDTO dto = new OnBoardingPackageDocumentsDTO();
                dto.setId(Long.parseLong(StrUtil.toString(s[0])));
                dto.setName(StrUtil.toString(s[1]));
                if (ObjectUtil.isNotNull(s[2])) {
                    dto.setActionRequired(ActionRequiredType.fromDbValue(Integer.parseInt(StrUtil.toString(s[2]))));
                }
                if (ObjectUtil.isNotNull(s[3])) {
                    dto.setMandatory(BooleanUtil.toBoolean(StrUtil.toString(s[3])));
                } else {
                    dto.setMandatory(false);
                }
                if (ObjectUtil.isNotNull(s[4])) {
                    dto.setSelected(BooleanUtil.toBoolean(StrUtil.toString(s[4])));
                } else {
                    dto.setSelected(false);
                }
                result.add(dto);
            });
        }
        return result;
    }

    @Override
    public List<OnBoardingPackagesDTO> getAllPackages() {
        return onBoardingPackagesRepository.findAllByActivatedAndTenantIdOrderByLastModifiedDateDesc(Boolean.TRUE, SecurityUtils.getTenantId());
    }

    @Override
    public OnBoardingPackagesDTO getByPackageId(Long id) {
        Optional<OnBoardingPackages> optionalPackage = onBoardingPackagesRepository.findAllByIdAndTenantIdAndActivated(id, SecurityUtils.getTenantId(), Boolean.TRUE);
        if (!optionalPackage.isPresent()) {
            log.error("[APN: OnBoarding Settings @{}] REST find package by package id error, can not find this package: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NOT_FIND_DOCUMENT_BY_PACKAGE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (ObjectUtil.isNull(checkPackagePermission(optionalPackage))) {
            log.error("[APN: OnBoarding Settings @{}] REST find package by package id error, you have no permission to find this package: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NO_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return OnBoardingPackages.toDto(checkPackagePermission(optionalPackage));
    }

    private OnBoardingPackages checkPackagePermission(Optional<OnBoardingPackages> optionalPackage) {
        OnBoardingPackages packages = optionalPackage.get();
        if (!packages.getTenantId().equals(SecurityUtils.getTenantId())) {
            return null;
        }
        return packages;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void deleteByPackageId(Long id) {
        Optional<OnBoardingPackages> optionalPackage = onBoardingPackagesRepository.findAllByIdAndTenantIdAndActivated(id, SecurityUtils.getTenantId(), Boolean.TRUE);
        if (!optionalPackage.isPresent()) {
            log.error("[APN: OnBoarding Settings @{}] REST delete package by package id error, can not find this package: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NOT_FIND_DOCUMENT_BY_PACKAGE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check permission
       if (ObjectUtil.isNull(checkPackagePermission(optionalPackage))) {
           log.error("[APN: OnBoarding Settings @{}] REST delete package by package id error, you have no permission to delete this package: {}", SecurityUtils.getUserId(), id);
           throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NO_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
       }
        OnBoardingPackages boardingPackage = optionalPackage.get();
        if (!boardingPackage.isActivated()) {
            log.error("[APN: OnBoarding Settings @{}] REST delete package by package id error, this package has been deleted: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_DELETE_PACKAGE_NOT_FOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //soft delete package
        boardingPackage.setActivated(false);
        boardingPackage.setLastModifiedDate(Instant.now());
        boardingPackage.setLastModifiedBy(SecurityUtils.getUserName());
        onBoardingPackagesRepository.save(boardingPackage);
        //delete documents relationship
        onBoardingPackageDocumentsRepository.deleteAllByPackageIdAndTenantId(id, SecurityUtils.getTenantId());
        //delete onBoarding-drafts
        onBoardingDraftsRepository.deleteAllByPackageIdAndTenantId(id, SecurityUtils.getTenantId());
    }

    @Override
    public OnBoardingPackagesDTO updateByPackageId(OnBoardingPackagesDTO dto) {
        Optional<OnBoardingPackages> optionalPackage = onBoardingPackagesRepository.findAllByIdAndTenantIdAndActivated(dto.getId(), SecurityUtils.getTenantId(), Boolean.TRUE);
        if (!optionalPackage.isPresent()) {
            log.error("[APN: OnBoarding Settings @{}] REST update package by package id error, can not find this package: {}", SecurityUtils.getUserId(), dto.getId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NOT_FIND_DOCUMENT_BY_PACKAGE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check permission
       if (ObjectUtil.isNull(checkPackagePermission(optionalPackage))) {
           log.error("[APN: OnBoarding Settings @{}] REST update package by package id error, you have no permission to update this package .: {}", SecurityUtils.getUserId(), dto.getId());
           throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NO_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
       }
        //check duplicate
        Integer count = onBoardingPackagesRepository.countByPackageIdAndNameAndTenantId(dto.getId(), dto.getName(), SecurityUtils.getTenantId());
        if (count > 0) {
            log.error("[APN: OnBoarding Settings @{}] REST update package by package id error, Package has duplicate package name: {}", SecurityUtils.getUserId(), dto.getId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_UPDATE_PACKAGE_NAME_DUPLICATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return OnBoardingPackages.toDto(onBoardingPackagesRepository.save(OnBoardingPackagesDTO.fromDto(dto)));
    }

    @Override
    public OnBoardingPackagesDTO savePackage(OnBoardingPackagesDTO dto) {
        if (ObjectUtil.isNotNull(dto.getId())) {
            log.error("[APN: OnBoarding Settings @{}] REST save package info, package id is not null: {}", SecurityUtils.getUserId(), dto);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_PACKAGE_ID_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check duplicate
        Integer count = onBoardingPackagesRepository.countByNameAndTenantId(dto.getName(), SecurityUtils.getTenantId());
        if (count > 0) {
            log.error("[APN: OnBoarding Settings @{}] REST save package by package id error, Package has duplicate package name: {}", SecurityUtils.getUserId(), dto.getId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_UPDATE_PACKAGE_NAME_DUPLICATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return OnBoardingPackages.toDto(onBoardingPackagesRepository.save(OnBoardingPackagesDTO.fromDto(dto)));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void saveRelations(Long packageId, List<OnBoardingPackageDocumentsDTO> dto) {
        //delete package documents relation
        onBoardingPackageDocumentsRepository.deleteAllByPackageIdAndTenantId(packageId, SecurityUtils.getTenantId());
        if (ObjectUtil.isNotEmpty(dto)) {
            dto.forEach(s -> {
                s.setPackageId(packageId);
            });
            //save package documents relation
            onBoardingPackageDocumentsRepository.saveAll(dto.stream().map(OnBoardingPackageDocumentsDTO::fromDto).collect(Collectors.toList()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void deleteDocumentInPackage(Long packageId, Long documentId) {
        onBoardingPackageDocumentsRepository.deleteAllByPackageIdAndDocumentIdAndTenantId(packageId, documentId, SecurityUtils.getTenantId());
    }

    @Override
    public OnBoardingSignatureDTO getSignature() {
        OnBoardingSignatureDTO result = new OnBoardingSignatureDTO();
        ApnParam apnParam = userService.findByParamKey(Constants.ONBOARDING_SIGNATURE, SecurityUtils.getTenantId(), Status.Available).getBody();
        if (ObjectUtil.isNotNull(apnParam)) {
            result.setText(apnParam.getParamValue());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void saveSignature(OnBoardingSignatureDTO dto) {
        if (ObjectUtil.isNull(dto)) {
            log.error("[APN: OnBoarding Settings @{}] REST save signature error, signature text can not be null .: {}", SecurityUtils.getUserId(), dto);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_SIGNATURE_TEXT_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        ApnParam apnParam = userService.findByParamKey(Constants.ONBOARDING_SIGNATURE, SecurityUtils.getTenantId(), Status.Available).getBody();
        if (ObjectUtil.isNull(apnParam)) {
            ApnParam param = new ApnParam();
            param.setParamKey(Constants.ONBOARDING_SIGNATURE);
            param.setTenantId(SecurityUtils.getTenantId());
            param.setParamName(Constants.ONBOARDING_SIGNATURE);
            param.setStatus(Status.Available);
            param.setParamValue(dto.getText());
            userService.saveApnParam(param);
        } else {
            apnParam.setParamValue(dto.getText());
            userService.saveApnParam(apnParam);
        }
    }


}
