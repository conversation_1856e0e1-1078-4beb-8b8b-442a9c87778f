package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;

/**
 * 发票信息
 */
@ApiModel(value = "派遣发票",description = "")
@Entity
@Table(name="t_contractor_invoice")
@Data
public class TContractorInvoice extends AbstractAuditingEntity implements Serializable,Cloneable{
    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id ;

    /** 发票编码 业务主键 */
    @ApiModelProperty(name = "发票编码 业务主键",notes = "")
    @Column(name = "invoice_number")
    private String invoiceNumber ;

    /** 组发票编码 业务主键 */
    @ApiModelProperty(name = "组发票编码 业务主键",notes = "")
    @Column(name = "group_invoice_number")
    private String groupInvoiceNumber ;


    @Column(name = "p_o_number")
    private String pONumber ;

    /** 公司发票编码 */
    @ApiModelProperty(name = "公司发票编码",notes = "")
    @Column(name = "client_invoice_number")
    private String clientInvoiceNumber ;

    /** 租户id */
    @ApiModelProperty(name = "租户id",notes = "")
    @Column(name = "tenant_id")
    private BigInteger tenantId ;

    /** 员工id */
    @ApiModelProperty(name = "员工id",notes = "")
    @Column(name = "talent_id")
    private BigInteger talentId ;

    /** 员工名称 */
    @ApiModelProperty(name = "员工名称",notes = "")
    @Column(name = "talent_name")
    private String talentName ;

    /** 公司id */
    @ApiModelProperty(name = "公司id",notes = "")
    @Column(name = "company_id")
    private BigInteger companyId ;

    /** 公司名称 */
    @ApiModelProperty(name = "公司名称",notes = "")
    @Column(name = "company_name")
    private String companyName ;

    /** 公司联系人id */
    @ApiModelProperty(name = "公司联系人id",notes = "")
    @Column(name = "company_contact_id")
    private BigInteger companyContactId ;

    /** 公司联系人名称 */
    @ApiModelProperty(name = "公司联系人名称",notes = "")
    @Column(name = "company_contact_name")
    private String companyContactName ;

    /** 职位id */
    @ApiModelProperty(name = "职位id",notes = "")
    @Column(name = "job_id")
    private BigInteger jobId ;

    /** 职位标题 */
    @ApiModelProperty(name = "职位标题",notes = "")
    @Column(name = "job_title")
    private String jobTitle ;

    /**  */
    @Column(name = "assignment_id")
    private BigInteger assignmentId ;
    /**  */
    @Column(name = "week_ending_date")
    private Timestamp weekEndingDate ;

    /** 文件地址 */
    @ApiModelProperty(name = "文件地址",notes = "")
    @Column(name = "file_url")
    private String fileUrl ;

    /** 发票类型 1-Regular，2-Expense */
    @ApiModelProperty(name = "发票类型 1-Regular，2-Expense",notes = "")
    @Column(name = "invoice_type")
    @Convert(converter = InvoiceTypeConverter.class)
    private InvoiceType invoiceType ;

    /** 发票日期 */
    @ApiModelProperty(name = "发票日期",notes = "")
    @Column(name = "invoice_date")
    private Timestamp invoiceDate ;

    /** 发票状态 1-Ungrouped，2-Grouped，3-Void */
    @ApiModelProperty(name = "发票状态 1-Ungrouped，2-Grouped，3-Void",notes = "")
    @Column(name = "invoice_status")
    @Convert(converter = InvoiceStatusConverter.class)
    private InvoiceStatusType invoiceStatus ;

    /** 1-Altomni， 2-IPG， 3-Independent， 4-Other */
    @ApiModelProperty(name = "1-Altomni， 2-IPG， 3-Independent， 4-Other",notes = "")
    @Column(name = "assignment_division")
    @Convert(converter = AssignmentDivisionConverter.class)
    private AssignmentDivision assignmentDivision ;


    /** 备注 */
    @ApiModelProperty(name = "备注",notes = "")
    @Column(name = "note")
    private String note ;

    /** 打卡或者报销的评论信息 */
    @ApiModelProperty(name = "打卡或者报销的评论信息",notes = "")
    @Column(name = "comment_note")
    private String commentNote ;


    /** 总计 */
    @ApiModelProperty(name = "总计",notes = "")
    @Column(name = "total_amount")
    private BigDecimal totalAmount ;


    /** 审批人id */
    @ApiModelProperty(name = "审批人id",notes = "")
    @Column(name = "approver_id")
    private BigInteger approverId ;

    /** 审批人名称 */
    @ApiModelProperty(name = "审批人名称",notes = "")
    @Column(name = "approver_name")
    private String approverName ;


    /** 评论 */
    @ApiModelProperty(name = "评论",notes = "")
    @Column(name = "expense_comments")
    private String expenseComments ;


    @Column(name = "expense_submit_date")
    private Timestamp expenseSubmitDate ;

    @ApiModelProperty(name = "",notes = "")
    @Column(name = "approval_date")
    private Timestamp approvalDate ;

    @Column(name = "client_location")
    private String clientLocation ;

    @Column(name = "client_address")
    private String clientAddress ;

    @Column(name = "client_name")
    private String clientName ;

    /**
     * 报销记录的索引
     * 同一个 assignment 在同一个 weekending 下，可以有多个报销单，多个报销单需要支持生成多个 invoice
     */
    @Column(name = "record_index")
    private Integer recordIndex = 0;

}
