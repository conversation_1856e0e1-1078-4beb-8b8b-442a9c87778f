package com.altomni.apn.jobdiva.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ScheduledProperties {

    @Value("${application.assignment.expiration_reminder_day_num:90}")
    private Long expirationReminderDayNum;

    @Value("${jobdiva.no_clock_email_company_ids:72}")
    private String jobdivaNoClockInEmailCompanyId;

    @Value("${application.timesheet.expiration_reminder_time:9}")
    private Integer timesheetExpirationReminderTime;

    @Value("${application.timesheet.expiration_reminder_talentId:}")
    private String timesheetExpirationReminderTalentIds;

}
