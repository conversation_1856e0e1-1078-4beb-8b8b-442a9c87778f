package com.altomni.apn.jobdiva.service.dto.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CurrentContractorDTO {

    private String nowDate;

    private String companyId;

    private String fullName;

    private String title;

    private Long jobId;

    private String recruiter;

    private String sourcer;

    private String am;

    private String dm;

    private String ac;

    private List<AssignmentDivision> assignmentDivisionList;

    private String province;

    private SearchSortDTO sort;

}
