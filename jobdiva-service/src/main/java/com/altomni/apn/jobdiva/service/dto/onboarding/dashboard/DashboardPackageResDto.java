package com.altomni.apn.jobdiva.service.dto.onboarding.dashboard;

import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.common.utils.CommonUtils;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.Instant;

/**
 * dashboard package result dto
 * <AUTHOR>
 */
@Entity
@Data
public class DashboardPackageResDto implements Serializable {

    @Id
    private Long id;

    private Long talentId;

    private String employeeName;

    private Instant packageAssignedOn;

    private String packageName;

    private CompletionStatus packageStatus;

    private Instant startingOn;

    private String jobTitle;

    private String company;

    private String jobId;

    private String jobCode;

    private String department;

    private String startByUser;

    private String startByUserFirstName;

    private String startByUserLastName;

    private String assignedBy;

    private String assignedByFirstName;

    private String assignedByLastName;

    private Long companyId;

    private Long startByUserId;

    private Long packageId;

    private Long documentId;

    private Long assignedById;

    public String getStartByUser() {
        if (StrUtil.isBlank(startByUserFirstName) || StrUtil.isBlank(startByUserFirstName)) {
            return null;
        }
        return CommonUtils.formatFullName(startByUserFirstName, startByUserLastName);
    }

    public String getAssignedBy() {
        if (StrUtil.isBlank(assignedByFirstName) || StrUtil.isBlank(assignedByLastName)) {
            return null;
        }
        return CommonUtils.formatFullName(assignedByFirstName, assignedByLastName);
    }

}
