package com.altomni.apn.jobdiva.service.invoice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.dto.CanadaProvinceTaxDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobInvoiceOverdueDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.jobdiva.config.env.CanadaProvinceListProperties;
import com.altomni.apn.jobdiva.config.env.CanadaProvinceTaxProperties;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import com.altomni.apn.jobdiva.domain.invoice.*;
import com.altomni.apn.jobdiva.domain.timesheet.ExpenseRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetComments;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetHolidayRecord;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.repository.assignment.PayRateRepository;
import com.altomni.apn.jobdiva.repository.invoice.*;
import com.altomni.apn.jobdiva.repository.timesheet.*;
import com.altomni.apn.jobdiva.service.application.ApplicationService;
import com.altomni.apn.jobdiva.service.dto.invoice.*;
import com.altomni.apn.jobdiva.service.finance.FinanceService;
import com.altomni.apn.jobdiva.service.invoice.ContractorInvoiceService;
import com.altomni.apn.jobdiva.service.vo.invoice.*;
import com.altomni.apn.jobdiva.util.InvoicePdfUtil;
import com.altomni.apn.jobdiva.util.InvoiceUtil;
import com.altomni.apn.common.utils.RateUnitUtil;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.TextStyle;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@Service("contractorInvoiceService")
public class ContractorInvoiceServiceImpl implements ContractorInvoiceService {

    @Resource
    ContractorInvoiceNativeRepository contractorInvoiceNativeRepository;

    @Resource
    ContractorInvoiceRepository contractorInvoiceRepository;

    @Resource
    PayRateRepository payRateRepository;

    @Resource
    InvoiceTimesheetInfoRepository invoiceTimesheetInfoRepository;

    @Resource
    InvoiceExpenseInfoRepository invoiceExpenseInfoRepository;

    @Resource
    TimeSheetCommentsRepository timeSheetCommentsRepository;

    @Resource
    ExpenseRecordRepository expenseRecordRepository;

    @Resource
    TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    GroupInvoiceRecordRepository groupInvoiceRecordRepository;

    @Resource
    GroupInvoiceRepository groupInvoiceRepository;

    @Resource
    TimeSheetHolidayRecordRepository timeSheetHolidayRecordRepository;

    @Resource
    FinanceService financeService;

    @Resource
    TMongodbToJobdivaTempRepository tMongodbToJobdivaTempRepository;

    @Resource
    TMongodbToJobdivaTempNativeRepository tMongodbToJobdivaTempNativeRepository;

    @Resource
    private ApplicationService applicationService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Resource
    CanadaProvinceTaxProperties canadaProvinceTaxProperties;

    @Resource
    private PlatformTransactionManager transactionManager;


    @Resource
    CanadaProvinceListProperties canadaProvinceListProperties;

    @Resource
    TalentAssignmentRepository talentAssignmentRepository;

    @Resource
    CommonRedisService commonRedisService;

    /**
     * This is a method that saves a contractor invoice based on the information provided in the ContractorInvoiceCreateDTO. The method first searches for any failed timesheet and expense information related to the invoice and returns a list of ContractorInvoiceFailedVO if any are found. If there are no failed entries, the method creates the invoice based on the invoice type(s) specified in the DTO. If both timesheet and expense types are specified, the method creates invoices for both. If only one type is specified, the method creates an invoice for that type. The method returns a ContractorInvoiceCreateVO object containing the invoice information.
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractorInvoiceCreateVO save(ContractorInvoiceCreateDTO dto) {

        ContractorInvoiceCreateVO vo = new ContractorInvoiceCreateVO();

        //login user tenant info
        dto.setTenantId(SecurityUtils.getTenantId());

        int count = 0;
        //查询信息
        if (dto.getInvoiceTypeList().size() == 2) {
            int timesheet = createInvoiceByTimesheet(dto);
            count += timesheet;
            int expenseFlag = createInvoiceByExpense(dto);
            count += expenseFlag;
        } else if (dto.getInvoiceTypeList().get(0).name().equals(InvoiceType.EXPENSE.name())) {
            int expenseFlag = createInvoiceByExpense(dto);
            count += expenseFlag;
        } else if (dto.getInvoiceTypeList().get(0).name().equals(InvoiceType.REGULAR.name())) {
            int timesheet = createInvoiceByTimesheet(dto);
            count += timesheet;
        }

        //search failed info
        List<ContractorInvoiceFailedVO> invoiceFailedVOList = contractorInvoiceNativeRepository.queryFailedTimesheetAndExpenseList(dto, false);
        log.info("create invoice, Failed to query data ");
        if (null != invoiceFailedVOList && !invoiceFailedVOList.isEmpty()) {
            vo.setData(invoiceFailedVOList);
            List<ContractorInvoiceFailedVO> regularList = invoiceFailedVOList.stream().filter(s -> s.getType().equals("TimeSheet")).collect(Collectors.toList());
            StringBuilder sb = new StringBuilder(count + " internal invoices have been generated,");
            sb.append(" and " + invoiceFailedVOList.size() + " failed due to " + regularList.size() + " unapproved timesheets and ");
            sb.append((invoiceFailedVOList.size() - regularList.size()) + " pending expenses.");
            sb.append(" Please approve the submissions first, then generate invoices again.");
            vo.setMessage(sb.toString());
            return vo;
        }

        if (count == 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_CREATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        return vo;
    }

    /**
     * 判断查询到数据
     *
     * @param flag   未查到数据flag++
     * @param result
     */
    private void searchDataResultJudgment(int flag, int result) {
        if (flag == result) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_CREATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
    }


    /**
     * @param dto
     */
    private int createInvoiceByExpense(ContractorInvoiceCreateDTO dto) {
        List<ContractorInvoiceTimesheetDTO> expenseList = contractorInvoiceNativeRepository.queryTimesheetAndExpenseList(dto, InvoiceType.EXPENSE.toDbValue());
        log.info("create invoice, Found {} expense data", expenseList.size());
        if (null != expenseList && !expenseList.isEmpty()) {

            List<TContractorInvoice> tContractorInvoiceList = new ArrayList<>();

            //invoice expense detail info
            Map<String, List<TInvoiceExpenseInfo>> invoiceExpense = new HashMap<>();
            log.info("create invoice, start assembly expense info");
            assemblyInvoiceDetailInfo(expenseList, tContractorInvoiceList, null, invoiceExpense, InvoiceType.EXPENSE.toDbValue(), null);
            log.info("create invoice, start inserting expense info");
            doSaveDetail(tContractorInvoiceList, null, invoiceExpense);
            return tContractorInvoiceList.size();
        }
        return 0;
    }

    /**
     * Execute Insert
     *
     * @param tContractorInvoiceList
     * @param invoiceTimesheet
     * @param invoiceExpense
     */
    private void doSaveDetail(List<TContractorInvoice> tContractorInvoiceList,
                              Map<String, List<TInvoiceTimesheetInfo>> invoiceTimesheet,
                              Map<String, List<TInvoiceExpenseInfo>> invoiceExpense) {
        if (!tContractorInvoiceList.isEmpty()) {
            contractorInvoiceRepository.saveAll(tContractorInvoiceList);
            applicationService.deleteOnboardNoInvoiceReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(tContractorInvoiceList.stream()
                    .map(TContractorInvoice::getId).map(BigInteger::longValue).collect(Collectors.toList())).build());
            log.info("create invoice,insert invoice");
        }

        if (null != invoiceTimesheet && !invoiceTimesheet.isEmpty()) {
            tContractorInvoiceList.forEach(in -> {
                if (invoiceTimesheet.containsKey(in.getAssignmentId().toString() + in.getWeekEndingDate().toString())) {
                    List<TInvoiceTimesheetInfo> invoiceTimesheetInfos = invoiceTimesheet.get(in.getAssignmentId().toString() + in.getWeekEndingDate().toString());

                    invoiceTimesheetInfos.forEach(i -> {
                        i.setInvoiceId(in.getId());
                    });
                    invoiceTimesheetInfoRepository.saveAll(invoiceTimesheetInfos);
                    log.info("create invoice, insert invoice timesheet infos");
                }
            });
        }

        if (null != invoiceExpense && !invoiceExpense.isEmpty()) {
            tContractorInvoiceList.forEach(in -> {
                String key = in.getAssignmentId().toString() + in.getWeekEndingDate().toString() + in.getRecordIndex();
                if (invoiceExpense.containsKey(key)) {
                    List<TInvoiceExpenseInfo> invoiceExpenseInfos = invoiceExpense.get(key);

                    invoiceExpenseInfos.forEach(i -> {
                        i.setInvoiceId(in.getId());
                    });
                    invoiceExpenseInfoRepository.saveAll(invoiceExpenseInfos);
                    log.info("create invoice, insert invoice expense infos");
                }
            });
        }
    }

    /**
     * Generate invoice based on timesheet
     *
     * @param dto
     */
    private int createInvoiceByTimesheet(ContractorInvoiceCreateDTO dto) {
        List<ContractorInvoiceTimesheetDTO> timesheetList = contractorInvoiceNativeRepository.queryTimesheetAndExpenseList(dto, InvoiceType.REGULAR.toDbValue());
        log.info("create invoice, Found {} timesheet data", timesheetList.size());
        if (null != timesheetList && !timesheetList.isEmpty()) {
            //invoice list info
            List<TContractorInvoice> tContractorInvoiceList = new ArrayList<>();
            //invoice timesheet detail info
            Map<String, List<TInvoiceTimesheetInfo>> invoiceTimesheet = new HashMap<>();
            log.info("create invoice, start assembly timesheet info");
            assemblyInvoiceDetailInfo(timesheetList, tContractorInvoiceList, invoiceTimesheet, null, InvoiceType.REGULAR.toDbValue(), null);
            log.info("create invoice, start inserting timesheet info");
            doSaveDetail(tContractorInvoiceList, invoiceTimesheet, null);
            return tContractorInvoiceList.size();
        }
        return 0;
    }


    /**
     * assembly invoice timesheet info
     *
     * @param timesheetList
     * @param tContractorInvoiceList
     * @param invoiceTimesheet
     * @param invoiceExpense
     * @param invoiceType            1-timesheet 2expense
     */
    private void assemblyInvoiceDetailInfo(List<ContractorInvoiceTimesheetDTO> timesheetList,
                                           List<TContractorInvoice> tContractorInvoiceList,
                                           Map<String, List<TInvoiceTimesheetInfo>> invoiceTimesheet,
                                           Map<String, List<TInvoiceExpenseInfo>> invoiceExpense,
                                           Integer invoiceType, String existSequence) {
        for (ContractorInvoiceTimesheetDTO obj : timesheetList) {

            //Determine if it has been generated
            // timesheet 打卡记录一周只能有一个发票，index = 0
            Integer recordIndex = InvoiceType.EXPENSE.toDbValue().equals(invoiceType) ? obj.getExpenseIndex() : 0;

            String redisKey = InvoiceUtil.INVOICE_EXIST_KEY + ":" + obj.getAssignmentId() + ":" + obj.getWeekEndingDate() + ":" + invoiceType + ":" + recordIndex;
            String redisValue = commonRedisService.get(redisKey);
            if (StringUtils.isNotBlank(redisValue)) {
                continue;
            }
            commonRedisService.set(redisKey, "1", "NX", "EX", 3600);

            List<BigInteger> id = contractorInvoiceRepository.getIdByAssignmentIdAndWeekEndingDateAndRecordIndex(obj.getAssignmentId(), obj.getWeekEndingDate(), invoiceType, recordIndex);
            if (!id.isEmpty()) {
                continue;
            }

            String sequence = existSequence;

            //get invoice number
            if (StringUtils.isBlank(sequence)) {
                sequence = financeService.getCommonSequence(InvoiceUtil.INVOICE_NUMBER_KEY, 10, "C", 1000000000L).getBody();
                if (null == sequence) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_CREATE_SEQUECE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }
            }

            TContractorInvoice tContractorInvoice = new TContractorInvoice();
            ServiceUtils.myCopyProperties(obj, tContractorInvoice);
            tContractorInvoice.setInvoiceNumber(sequence);
            tContractorInvoice.setInvoiceStatus(InvoiceStatusType.UNGROUPED);
            if (StringUtils.isNotBlank(obj.getAssignmentDivision())) {
                tContractorInvoice.setAssignmentDivision(AssignmentDivision.fromDbValue(Integer.valueOf(obj.getAssignmentDivision())));
            }

            Map<String, Object> map = null;
            TalentAssigment talentAssigment = talentAssignmentRepository.findById(obj.getAssignmentId().longValue()).get();
            List<Map<String, Object>> startList = contractorInvoiceNativeRepository.findStartInfo(talentAssigment.getTalentRecruitmentProcessId());
            if (startList.size() == 1) {
                BigInteger startId = (BigInteger) startList.get(0).get("id");
                map = contractorInvoiceNativeRepository.findCompanyAddressAndNameByStartId(startId);
            } else {
                LocalDate weekEndDate = obj.getWeekEndingDate().toLocalDateTime().toLocalDate();
                for (Map<String, Object> start : startList) {
                    java.sql.Date startDate = (java.sql.Date) start.get("start_date");
                    java.sql.Date endDate = (java.sql.Date) start.get("end_date");
                    if (!(weekEndDate.isBefore(startDate.toLocalDate())) && !(weekEndDate.isAfter(endDate.toLocalDate()))) {
                        BigInteger startId = (BigInteger) start.get("id");
                        map = contractorInvoiceNativeRepository.findCompanyAddressAndNameByStartId(startId);
                        break;
                    }
                }
                if (map == null) {
                    map = contractorInvoiceNativeRepository.findCanadaProvinceByAssignmentId(obj.getAssignmentId().longValue());
                }
            }
            if (map != null) {
                tContractorInvoice.setClientLocation(map.get("relation_client_location") + "");
                tContractorInvoice.setClientAddress(map.get("relation_client_address") + "");
                tContractorInvoice.setClientName(map.get("relation_client_name") + "");
            }


            //timesheet detail
            if (invoiceType.equals(InvoiceType.REGULAR.toDbValue())) {
                //查询
                List<AssignmentPayRateInfo> assignmentPayRateInfos = payRateRepository.findByAssignmentIdAndContentType(obj.getAssignmentId().longValue(), PayRateContentType.BILLING);

                List<TInvoiceTimesheetInfo> invoiceTimesheetInfos = new ArrayList<>();

                //query timesheet comments
                TimeSheetComments comments = timeSheetCommentsRepository.findAllByDateAndType(obj.getWeekEndingDate().toLocalDateTime().toLocalDate(), obj.getTalentId().longValue(), CommentsType.TIME_SHEET.toDbValue(), obj.getAssignmentId().longValue());
                if (null != comments) {
                    tContractorInvoice.setCommentNote(comments.getComments());
                }

                tContractorInvoice.setInvoiceDate(DateUtil.getDayAddition(obj.getWeekEndingDate(), 1));
                tContractorInvoice.setInvoiceType(InvoiceType.REGULAR);
                tContractorInvoice.setTotalAmount(calculateTotalAmount(obj, assignmentPayRateInfos, invoiceTimesheetInfos, tContractorInvoice.getClientLocation()));
                tContractorInvoiceList.add(tContractorInvoice);
                //Save data in kV format key = assigmentId+workEndingDate value = timesheetList
                if (!invoiceTimesheetInfos.isEmpty()) {
                    invoiceTimesheet.put(obj.getAssignmentId().toString() + obj.getWeekEndingDate().toString(), invoiceTimesheetInfos);
                }
            } else if (invoiceType.equals(InvoiceType.EXPENSE.toDbValue())) {

                //query expense comments
                TimeSheetComments comments = timeSheetCommentsRepository.findAllByDateAndType(obj.getWeekEndingDate().toLocalDateTime().toLocalDate(), obj.getTalentId().longValue(), CommentsType.EXPENSE.toDbValue(), obj.getAssignmentId().longValue(), recordIndex);

                tContractorInvoice.setInvoiceDate(obj.getApprovalDate());
                tContractorInvoice.setInvoiceType(InvoiceType.EXPENSE);
                tContractorInvoice.setRecordIndex(recordIndex);
                tContractorInvoice.setTotalAmount(obj.getCost() == null ? BigDecimal.ZERO : obj.getCost());
                tContractorInvoice.setExpenseSubmitDate(obj.getExpenseSubmitDate());
                if (null != comments) {
                    tContractorInvoice.setExpenseComments(comments.getComments());
                    tContractorInvoice.setCommentNote(comments.getComments());
                }
                tContractorInvoiceList.add(tContractorInvoice);
                List<TInvoiceExpenseInfo> invoiceExpenseInfos = new ArrayList<>();
                //Save data in kV format key = assigmentId+workEndingDate value = expenseList
                assemblyInvoiceExpense(obj, invoiceExpenseInfos);
                if (!invoiceExpenseInfos.isEmpty()) {
                    invoiceExpense.put(obj.getAssignmentId().toString() + obj.getWeekEndingDate().toString() + obj.getExpenseIndex(), invoiceExpenseInfos);
                }
            }
        }
    }

    /**
     * assembly invoice expense list
     *
     * @param obj
     * @param invoiceExpenseInfos
     */
    private void assemblyInvoiceExpense(ContractorInvoiceTimesheetDTO obj, List<TInvoiceExpenseInfo> invoiceExpenseInfos) {
        LocalDate endDate = obj.getWeekEnd().toLocalDateTime().toLocalDate();
        LocalDate startDate = obj.getWeekStart().toLocalDateTime().toLocalDate();
        // query expense currency type
        AssignmentPayRateInfo assignmentPayRateInfo = payRateRepository.findPayUnit(obj.getAssignmentId().longValue());
        // query expense detail
        List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByDateAndStatus(startDate, endDate, obj.getTalentId().longValue(), obj.getAssignmentId().longValue(),obj.getExpenseIndex());
        if (null != expenseRecordList && !expenseRecordList.isEmpty()) {
            expenseRecordList.forEach(ex -> {
                TInvoiceExpenseInfo bean = new TInvoiceExpenseInfo();
                bean.setExpenseAmount(ex.getCost() == null ? new BigDecimal(0) : BigDecimal.valueOf(ex.getCost()));
                bean.setExpenseCategory(ex.getExpenseType());
                bean.setSortIndex(ex.getLineIndex());
                bean.setExpenseIndex(obj.getExpenseIndex());
                bean.setWeekDay(ex.getWeekDay());
                bean.setWorkDate(ex.getWorkDate());
                bean.setCurrencyType(assignmentPayRateInfo.getCurrency());
                bean.setExpenseDate(ex.getWorkDate());
                invoiceExpenseInfos.add(bean);
            });
        }
    }

    public static String captureName(String name) {
        name = name.substring(0, 1).toUpperCase() + name.substring(1).toLowerCase();
        return name;
    }

    /**
     * calculate total and assembly invoice timesheet list
     *
     * @param obj
     * @param assignmentPayRateInfos
     * @param invoiceTimesheetInfos
     * @return
     */
    private BigDecimal calculateTotalAmount(ContractorInvoiceTimesheetDTO obj,
                                            List<AssignmentPayRateInfo> assignmentPayRateInfos,
                                            List<TInvoiceTimesheetInfo> invoiceTimesheetInfos, String clientLocation) {
        BigDecimal total = new BigDecimal(0);
        if (!assignmentPayRateInfos.isEmpty()) {
            Integer currency = 0;
            Long assignmentId = 0L;
            for (AssignmentPayRateInfo ass : assignmentPayRateInfos) {
                BigDecimal payRate = ass.getPayRate();
                if (!ass.getTimeUnit().toDbValue().equals(RateUnitType.HOURLY.toDbValue())) {
                    payRate = RateUnitUtil.rateConvert(payRate, ass.getTimeUnit());
                }
                if (payRate == null) {
                    payRate = new BigDecimal(0);
                }
                currency = ass.getCurrency();
                assignmentId = ass.getAssignmentId();
                //Assembly Details Data
                TInvoiceTimesheetInfo bean = new TInvoiceTimesheetInfo();
                bean.setBillRate(payRate);
                bean.setCurrencyType(ass.getCurrency());
                bean.setUnit("Hour");
                if (ass.getType().toDbValue().equals(PayRateType.BILL_RATE.toDbValue())) {
                    bean.setItemDescription(obj.getTalentName());
                    bean.setQuantity(obj.getRegularHours());
                    bean.setQuantityType(QuantityType.RT);
                    total = total.add(obj.getRegularHours() == null ? BigDecimal.valueOf(0) : obj.getRegularHours().multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP));
                    bean.setTotalAmount(obj.getRegularHours() == null ? BigDecimal.valueOf(0) : obj.getRegularHours().multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP));
                } else if (ass.getType().toDbValue().equals(PayRateType.OVER_TIME.toDbValue())) {
                    bean.setItemDescription("OT hours in excess of standards for ".concat(obj.getTalentName()));
                    bean.setQuantity(obj.getOverTime());
                    bean.setQuantityType(QuantityType.OT);
                    total = total.add(obj.getOverTime() == null ? BigDecimal.valueOf(0) : obj.getOverTime().multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP));
                    bean.setTotalAmount(obj.getOverTime() == null ? BigDecimal.valueOf(0) : obj.getOverTime().multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP));
                } else if (ass.getType().toDbValue().equals(PayRateType.DOUBLE_TIME.toDbValue())) {
                    bean.setItemDescription("DT hours in excess of standards for ".concat(obj.getTalentName()));
                    bean.setQuantity(obj.getDoubleTime());
                    bean.setQuantityType(QuantityType.DT);
                    total = total.add(obj.getDoubleTime() == null ? BigDecimal.valueOf(0) : obj.getDoubleTime().multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP));
                    bean.setTotalAmount(obj.getDoubleTime() == null ? BigDecimal.valueOf(0) : obj.getDoubleTime().multiply(payRate).setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                invoiceTimesheetInfos.add(bean);
            }

            //query Timesheet Holiday OT
            List<TimeSheetHolidayRecord> timeSheetHolidayRecordList = timeSheetHolidayRecordRepository.findOneByAssignmentIdAndWeekEnd(obj.getAssignmentId().longValue(), obj.getWeekEndingDate().toLocalDateTime().toLocalDate());
            if (null != timeSheetHolidayRecordList && timeSheetHolidayRecordList.size() > 0) {
                for (TimeSheetHolidayRecord holidayRecord : timeSheetHolidayRecordList) {
                    TInvoiceTimesheetInfo bean = new TInvoiceTimesheetInfo();
                    bean.setBillRate(holidayRecord.getRate());
                    bean.setCurrencyType(holidayRecord.getCurrency());
                    bean.setUnit("Hour");
                    bean.setItemDescription("Holiday OT Adjustment for ".concat(obj.getTalentName()));
                    bean.setQuantityType(QuantityType.HT);
                    BigDecimal bigDecimal = new BigDecimal(holidayRecord.getWorkHours() == null ? "0" : Float.toString(holidayRecord.getWorkHours()));
                    bean.setTotalAmount(holidayRecord.getWorkHours() == null ? BigDecimal.valueOf(0) : bigDecimal.multiply(holidayRecord.getRate()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    total = total.add(holidayRecord.getWorkHours() == null ? BigDecimal.valueOf(0) : bigDecimal.multiply(holidayRecord.getRate()).setScale(2, BigDecimal.ROUND_HALF_UP));
                    bean.setQuantity(bigDecimal);
                    invoiceTimesheetInfos.add(bean);
                }
            }

            //can timesheet total special calculate by CAD
            if (currency == 3) {
                if (null != clientLocation) {
                    String province = CanadaProvinceTaxUtil.containsCanadaProvince(clientLocation, canadaProvinceListProperties.getInclude());
                    if (clientLocation.indexOf("Canada") != -1 && province != null) {

                        CanadaProvinceTaxDTO dto = canadaProvinceTaxProperties.getCanadaProvinceTax().get(province);

                        BigDecimal hstTax = total.multiply(new BigDecimal(dto.getTaxRate())).divide(new BigDecimal("100"));

                        //canada add
                        TInvoiceTimesheetInfo bean = new TInvoiceTimesheetInfo();
                        bean.setItemDescription(dto.getTaxRate() + "% " + dto.getTaxName() + " Tax");
                        bean.setTotalAmount(hstTax.setScale(2, BigDecimal.ROUND_HALF_UP));
                        bean.setQuantityType(QuantityType.ADJUSTMENT);
                        bean.setCurrencyType(currency);
                        invoiceTimesheetInfos.add(bean);
                        total = total.add(hstTax.setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }
            }
        }
        return total;
    }

    /**
     * update invoice info and adjustment info
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(ContractorInvoiceEditDTO dto) {

        Optional<TContractorInvoice> invoice = Optional.ofNullable(contractorInvoiceRepository.findByIdAndTenantId(dto.getId(), BigInteger.valueOf(SecurityUtils.getTenantId())));
        if (invoice.isPresent()) {
            if (invoice.get().getInvoiceStatus().equals(InvoiceStatusType.VOID)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_UPDATE_VOID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        TContractorInvoice ci = invoice.get();
        if (StringUtils.isNotEmpty(dto.getNote())) {
            ci.setNote(dto.getNote());
        }
        if (StringUtils.isNotEmpty(dto.getPoNumber())) {
            ci.setPONumber(dto.getPoNumber());
        }
        if (StringUtils.isNotEmpty(dto.getClientInvoiceNumber())) {
            ci.setClientInvoiceNumber(dto.getClientInvoiceNumber());
        }
        if (null != dto.getTotalAmount()) {
            ci.setTotalAmount(dto.getTotalAmount());
        }
        contractorInvoiceRepository.save(ci);
        log.info("contractor invoice modify , update invoice by id:{},param:{}", dto.getId(), JSON.toJSONString(dto));

        invoiceTimesheetInfoRepository.deleteByInvoiceIdAnAndQuantityType(dto.getId(), QuantityType.ADJUSTMENT.toDbValue());
        log.info("contractor invoice modify , delete quantity type is 5 and invoice id:{}", dto.getId());

        if (null != dto.getRegularInvoiceList() && dto.getInvoiceType().equals(InvoiceType.REGULAR)) {
            List<TInvoiceTimesheetInfo> tInvoiceTimesheetInfos = new ArrayList<>();
            dto.getRegularInvoiceList().stream().filter(z -> z.getQuantityType().equals(QuantityType.ADJUSTMENT)).forEach(d -> {
                TInvoiceTimesheetInfo bean = new TInvoiceTimesheetInfo();
                bean.setInvoiceId(dto.getId());
                bean.setQuantityType(QuantityType.ADJUSTMENT);
                bean.setItemDescription(d.getItemDescription());
                bean.setTotalAmount(d.getTotalAmount());
                bean.setCurrencyType(d.getCurrency());
                tInvoiceTimesheetInfos.add(bean);
            });

            if (!tInvoiceTimesheetInfos.isEmpty()) {
                invoiceTimesheetInfoRepository.saveAll(tInvoiceTimesheetInfos);
                log.info("contractor invoice modify , insert adjustment timesheet detail invoice id:{}", dto.getId());
            }
        }
        return dto.getId().longValue();
    }

    /**
     * void invoice
     * 单张invoice 在grouped状态下不能执行void
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void invoiceVoid(ContractorInvoiceVoidAndPrintDTO dto) {
        Set<Long> invoiceIds = dto.getInvoiceIdList().stream().collect(Collectors.toSet());
        List<BigInteger> invoiceIdList = contractorInvoiceRepository.queryGroupedStatusByIds(invoiceIds, Arrays.asList(InvoiceStatusType.GROUPED.toDbValue()));
        if (!invoiceIdList.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_VOID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        log.info("invoice void operation start ids:{}", JSON.toJSONString(dto.getInvoiceIdList()));
        contractorInvoiceRepository.updateInvoiceStatusByIds(invoiceIds, InvoiceStatusType.VOID.toDbValue());

        List<TContractorInvoice> invoices = contractorInvoiceRepository.queryInvoiceByIdIn(invoiceIds);
        List<String> keys = new ArrayList<>();
        for (TContractorInvoice obj : invoices) {
            String redisKey = InvoiceUtil.INVOICE_EXIST_KEY + ":" + obj.getAssignmentId() + ":" + obj.getWeekEndingDate() + ":" + obj.getInvoiceType().toDbValue() + ":" + obj.getRecordIndex();
            keys.add(redisKey);
        }
        if (!keys.isEmpty()) {
            commonRedisService.deleteBatch(keys);
        }

        applicationService.onboardNoInvoiceReminderByInvoiceVoid(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(invoiceIdList.stream()
                .map(BigInteger::longValue).collect(Collectors.toList())).build());

        log.info("invoice void operation finnish");
    }

    /**
     * ungroup
     * 如果没有sent to client 才可执行
     *
     * @param dto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ungroup(ContractorInvoiceVoidAndPrintDTO dto) {

        log.info("invoice ungroup operation start");
        Set<Long> idList = dto.getInvoiceIdList().stream().collect(Collectors.toSet());

        //Query data that is not in a grouped state
        List<BigInteger> invoiceIds = contractorInvoiceRepository.queryGroupedStatusByIds(idList, Arrays.asList(InvoiceStatusType.UNGROUPED.toDbValue(), InvoiceStatusType.VOID.toDbValue()));
        if (!invoiceIds.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_UNGROUP.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        //query group invoice and invoice mapping
        List<TGroupInvoiceRecord> tGroupInvoiceRecords = groupInvoiceRecordRepository.findGroupInvoiceIdByInvoiceIds(idList);
        if (null != tGroupInvoiceRecords && !tGroupInvoiceRecords.isEmpty()) {

            Set<Long> groupInvoiceIdList = tGroupInvoiceRecords.stream().map(TGroupInvoiceRecord::getGroupInvoiceId).map(a -> a.longValue()).collect(Collectors.toSet());
            //is sent to client
            if (sendToClient(groupInvoiceIdList)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_UNGROUP_SEND_CLIENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }

            groupInvoiceRepository.updateStatusByIds(groupInvoiceIdList);
            log.info("invoice ungroup operation, update group invoice status is invalid,ids:{}", JSON.toJSONString(groupInvoiceIdList));

            Set<Long> invoiceRecordIdList = tGroupInvoiceRecords.stream().map(TGroupInvoiceRecord::getId).map(a -> a.longValue()).collect(Collectors.toSet());

            Set<Long> invoiceIdList = tGroupInvoiceRecords.stream().map(TGroupInvoiceRecord::getInvoiceId).map(a -> a.longValue()).collect(Collectors.toSet());

            contractorInvoiceRepository.updateInvoiceStatusByIdsAndInvoiceStatus(invoiceIdList, InvoiceStatusType.UNGROUPED.toDbValue());
            log.info("invoice ungroup operation, update invoice status is ungroup and groupInvoiceNumber is null,ids:{}", JSON.toJSONString(invoiceIdList));

            groupInvoiceRecordRepository.updateStatusByIds(invoiceRecordIdList);

            if (CollUtil.isNotEmpty(tGroupInvoiceRecords)) {
                applicationService.deleteInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().contractInvoiceIdList(tGroupInvoiceRecords.stream()
                        .map(TGroupInvoiceRecord::getInvoiceId).map(BigInteger::longValue).collect(Collectors.toList())).build());
            }
            log.info("invoice ungroup operation, update invoice record status is invalid,ids:{}", JSON.toJSONString(invoiceRecordIdList));
        }
    }

    /**
     * send to client
     *
     * @param groupInvoiceList
     * @return
     */
    private boolean sendToClient(Set<Long> groupInvoiceList) {
        List<BigInteger> operatedByIdList = groupInvoiceRepository.querySentOnByIds(groupInvoiceList);
        if (operatedByIdList.isEmpty()) {
            return false;
        }
        return true;
    }

    /**
     * Query invoice pagination information
     *
     * @param dto
     * @param pageable
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public Page<ContractorInvoiceListVO> searchInvoiceList(ContractorInvoiceSearchDTO dto, Pageable pageable) {
        dto.setTenantId(SecurityUtils.getTenantId());
        return contractorInvoiceNativeRepository.searchInvoiceList(dto, pageable);
    }

    /**
     * Query failed approval timesheet pagination data
     *
     * @param dto
     * @param pageable
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    public Page<ContractorInvoiceFailedVO> searchFailedTimesheetAndExpenseInfo(ContractorInvoiceCreateDTO dto, Pageable pageable, boolean flag) {
        //login user tenant info
        dto.setTenantId(SecurityUtils.getTenantId());
        return contractorInvoiceNativeRepository.searchFailedTimesheetAndExpenseInfo(dto, pageable, flag);
    }

    /**
     * download failed approval timesheet data
     *
     * @param dto
     * @param response
     */
    @Override
    public void downloadFailedTimesheetAndExpense(ContractorInvoiceCreateDTO dto, HttpServletResponse response) {
        try {
            SimpleDateFormat df = new SimpleDateFormat(DateUtil.YYYY_MM_DD);
            DateFormat sdf1 = new SimpleDateFormat("MMddyy");
            Date fromDate = df.parse(dto.getFrom());
            Date toDate = df.parse(dto.getTo());
            String fileName = "Grouping Error_Missing Timesheet_" + sdf1.format(fromDate) + "-" + sdf1.format(toDate) + ".xls";
            HeaderUtil.setInvoiceHeader(response, fileName, "application/vnd.ms-excel");
            Pageable pageable = PageRequest.of(0, 10000);
            //login user tenant info
            dto.setTenantId(SecurityUtils.getTenantId());
            Page<ContractorInvoiceFailedVO> failedVOPage = contractorInvoiceNativeRepository.searchFailedTimesheetAndExpenseInfo(dto, pageable, false);
            List<ContractorInvoiceFailedDownloadVO> list = new ArrayList<>();
            List<ContractorInvoiceFailedVO> failedVOS = failedVOPage.getContent();
            DateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
            failedVOS.forEach(vo -> {
                ContractorInvoiceFailedDownloadVO bean = new ContractorInvoiceFailedDownloadVO();
                bean.setAm(vo.getAm());
                bean.setApprover(vo.getApprover());
                bean.setCompany(vo.getCompany());
                bean.setAssignmentDivision(vo.getAssignmentDivision());
                bean.setType(vo.getType());
                bean.setStatus(vo.getStatus());
                bean.setJobTitle(vo.getJobTitle());
                Date d = new Date(vo.getWeekEndingDate().getTime());
                bean.setWeekEndingDate(sdf.format(d));
                bean.setEmployeeName(vo.getEmployeeName());
                list.add(bean);
            });
            createFailedExcel(list, response.getOutputStream());
        } catch (Exception e) {
            log.error("download failed timesheet error", e);
        }
    }

    /**
     * do download
     *
     * @param content
     * @param outputStream
     */
    private void createFailedExcel(List<ContractorInvoiceFailedDownloadVO> content, ServletOutputStream outputStream) {
        try {
            EasyExcel.write(outputStream, ContractorInvoiceFailedDownloadVO.class)
                    .sheet("error_missing")
                    .doWrite(content);
            outputStream.flush();
        } catch (Exception e) {
            log.error("download failed timesheet error", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("download failed timesheet error", e);
                }
            }
        }
    }

    /**
     * download group failed approval timesheet data
     *
     * @param dto
     * @param response
     */
    @Override
    public void downloadGroupFailedInvoice(ContractorInvoiceCreateDTO dto, HttpServletResponse response) {
        try {
            SimpleDateFormat df = new SimpleDateFormat(DateUtil.YYYY_MM_DD);
            DateFormat sdf1 = new SimpleDateFormat("MMddyy");
            Date fromDate = df.parse(dto.getFrom());
            Date toDate = df.parse(dto.getTo());
            String fileName = "Grouping Error_Missing Internal Invoice_" + sdf1.format(fromDate) + "-" + sdf1.format(toDate) + ".xls";
            HeaderUtil.setInvoiceHeader(response, fileName, "application/vnd.ms-excel");
            Pageable pageable = PageRequest.of(0, 10000);
            //login user tenant info
            dto.setTenantId(SecurityUtils.getTenantId());

            Page<ContractorInvoiceFailedVO> failedVOPage = contractorInvoiceNativeRepository.searchFailedTimesheetAndExpenseInfo(dto, pageable, true);
            List<ContractorGroupInvoiceFailedDownloadVO> list = new ArrayList<>();
            List<ContractorInvoiceFailedVO> failedVOS = failedVOPage.getContent();
            DateFormat sdf = new SimpleDateFormat("MM/dd/yyyy");
            failedVOS.forEach(vo -> {
                ContractorGroupInvoiceFailedDownloadVO bean = new ContractorGroupInvoiceFailedDownloadVO();
                bean.setAm(vo.getAm());
                bean.setApprover(vo.getApprover());
                bean.setCompany(vo.getCompany());
                bean.setMissInvoiceType(vo.getType());
                bean.setJobTitle(vo.getJobTitle());
                Date d = new Date(vo.getWeekEndingDate().getTime());
                bean.setWeekEndingDate(sdf.format(d));
                bean.setEmployeeName(vo.getEmployeeName());
                list.add(bean);
            });
            createGroupFailedExcel(list, response.getOutputStream());
        } catch (Exception e) {
            log.error("download failed timesheet error", e);
        }
    }

    /**
     * do download
     *
     * @param content
     * @param outputStream
     */
    private void createGroupFailedExcel(List<ContractorGroupInvoiceFailedDownloadVO> content, ServletOutputStream outputStream) {
        try {
            EasyExcel.write(outputStream, ContractorGroupInvoiceFailedDownloadVO.class)
                    .sheet("error_missing")
                    .doWrite(content);
            outputStream.flush();
        } catch (Exception e) {
            log.error("download failed timesheet error", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("download failed timesheet error", e);
                }
            }
        }
    }

    /**
     * Query invoice detailed data
     *
     * @param id
     * @return
     */
    @Override
    public ContractorInvoiceViewVO view(BigInteger id) {
        Optional<TContractorInvoice> tContractorInvoice = Optional.ofNullable(contractorInvoiceRepository.findByIdAndTenantId(id, BigInteger.valueOf(SecurityUtils.getTenantId())));
        if (tContractorInvoice.isPresent()) {

            ContractorInvoiceViewVO vo = ContractorInvoiceViewVO.transformToVo(tContractorInvoice.get());

            LocalDate weekEndIngDate = tContractorInvoice.get().getWeekEndingDate().toLocalDateTime().toLocalDate();

            if (vo.getInvoiceType().equals(InvoiceType.REGULAR.name())) {
                List<ContractorInvoiceTimesheetVO> invoiceTimesheetInfos = contractorInvoiceNativeRepository.findTimesheetAllByInvoiceId(vo.getId());
                if (!invoiceTimesheetInfos.isEmpty()) {
                    vo.setRegularInvoiceList(invoiceTimesheetInfos);
                }
                TimeSheetRecord timeSheetRecord = timeSheetRecordRepository.findByDate(weekEndIngDate, tContractorInvoice.get().getTalentId().longValue(), tContractorInvoice.get().getAssignmentId().longValue());
                if (null == timeSheetRecord) {
                    throw new CustomParameterizedException("Timesheet or expenseRecord data not found");
                }
                vo.setWeekEnd(timeSheetRecord.getWeekEnd());
                vo.setWeekStart(timeSheetRecord.getWeekStart());
            } else if (vo.getInvoiceType().equals(InvoiceType.EXPENSE.name())) {
                ExpenseRecord expenseRecordInfo = expenseRecordRepository.findByDate(weekEndIngDate, tContractorInvoice.get().getTalentId().longValue(), tContractorInvoice.get().getAssignmentId().longValue());
                if (null == expenseRecordInfo) {
                    throw new CustomParameterizedException("Timesheet or expenseRecord data not found");
                }
                vo.setWeekEnd(expenseRecordInfo.getWeekEnd());
                vo.setWeekStart(expenseRecordInfo.getWeekStart());
                List<Map<String, Object>> result = new LinkedList<>();
                List<ContractorInvoiceExpenseVO> expenseVOList = contractorInvoiceNativeRepository.findExpenseAllByInvoiceId(vo.getId());
                List<TInvoiceExpenseInfo> invoiceExpenseInfos = invoiceExpenseInfoRepository.findByInvoiceId(vo.getId());
                if (!invoiceExpenseInfos.isEmpty()) {
                    Set<Integer> indexAllSet = invoiceExpenseInfos.stream().map(TInvoiceExpenseInfo::getSortIndex).collect(Collectors.toSet());
                    Set<LocalDate> localDateSet = TimeSheetUtil.getWeekByWeekEndingDate(expenseRecordInfo.getWeekEndingDate().toString());
                    Set<LocalDate> ascLocalDateSet = new TreeSet<>(localDateSet);
                    Map<Integer, List<TInvoiceExpenseInfo>> indexMap = invoiceExpenseInfos.stream().collect(Collectors.groupingBy(TInvoiceExpenseInfo::getSortIndex));
                    indexAllSet.forEach(index -> {
                        List<TInvoiceExpenseInfo> expenseRecordList = indexMap.get(index);
                        ascLocalDateSet.forEach(localDate -> {
                            if (expenseRecordList.stream().map(TInvoiceExpenseInfo::getExpenseDate).collect(Collectors.toList()).stream()
                                    .noneMatch(date -> Objects.equals(localDate, date))) {
                                TInvoiceExpenseInfo expenseRecord = expenseRecordList.get(0);
                                setFillExpenseRecord(invoiceExpenseInfos, localDate, expenseRecord);
                            }
                        });
                    });

                    Map<Integer, List<TInvoiceExpenseInfo>> dateMap = invoiceExpenseInfos.stream().collect(Collectors.groupingBy(TInvoiceExpenseInfo::getSortIndex));
                    Set<Integer> indexSet = dateMap.keySet();
                    Set<Integer> sortSet = new TreeSet<>(Comparator.comparingInt(o -> o));
                    sortSet.addAll(indexSet);
                    for (Integer index : sortSet) {
                        Map<String, Object> map = new HashMap<>(16);
                        List<TInvoiceExpenseInfo> cell = dateMap.get(index);
                        for (TInvoiceExpenseInfo timeRecord : cell) {
                            map.put("index", index);
                            map.put("expenseType", timeRecord.getExpenseCategory());
                            map.put(timeRecord.getWeekDay() + "@" + timeRecord.getExpenseDate(), timeRecord.getExpenseAmount());
                            map.put("currency", expenseVOList.get(0).getCurrency());
                        }
                        result.add(map);
                    }
                    vo.setExpenseInvoiceList(result);
                    vo.setExpenseList(expenseVOList);
                }
            }
            return vo;
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
    }

    private void setFillExpenseRecord(List<TInvoiceExpenseInfo> records, LocalDate localDate, TInvoiceExpenseInfo expenseMaxRecord) {
        TInvoiceExpenseInfo expenseRecord = new TInvoiceExpenseInfo();
        expenseRecord.setExpenseDate(localDate);
        expenseRecord.setWeekDay(localDate.getDayOfWeek().getDisplayName(TextStyle.SHORT, Locale.ENGLISH));
        expenseRecord.setSortIndex(expenseMaxRecord.getSortIndex());
        expenseRecord.setExpenseCategory(expenseMaxRecord.getExpenseCategory());
        expenseRecord.setCurrencyType(expenseMaxRecord.getCurrencyType());
        expenseRecord.setExpenseAmount(new BigDecimal(0));
        records.add(expenseRecord);
    }

    /**
     * print invoice
     *
     * @param dto
     * @param response
     */
    @Override
    public void print(ContractorInvoiceVoidAndPrintDTO dto, HttpServletResponse response) {

        if (dto.getInvoiceIdList().size() == 1) {
            doDownloadPdf(dto.getInvoiceIdList().get(0), response);
        } else {
            ZipOutputStream zipstream = null;
            try {

                String downloadName = "invoice.zip";
                HeaderUtil.setInvoiceHeader(response, downloadName, "application/x-zip-compressed");

                zipstream = new ZipOutputStream(response.getOutputStream());
                for (Long id : dto.getInvoiceIdList()) {
                    ContractorInvoiceViewVO viewVO = view(BigInteger.valueOf(id));
                    ByteArrayOutputStream bos = new ByteArrayOutputStream();
                    Document document = new Document(PageSize.A4, 0, 0, 60, 0);
                    try {
                        createInvoicePdf(viewVO, bos, document);
                        document.close();
                        ZipEntry entry = new ZipEntry(viewVO.getInvoiceNumber() + ".pdf");
                        zipstream.putNextEntry(entry);
                        zipstream.write(bos.toByteArray());
                        zipstream.flush();
                    } catch (Exception e) {
                        log.error("invoice: print error,{}", e);
                    } finally {
                        document.close();
                        try {
                            bos.close();
                        } catch (IOException e1) {
                            e1.printStackTrace();
                        }
                    }
                }
            } catch (Exception e) {
                log.error("invoice: print error,{}", e);
            } finally {
                try {
                    zipstream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void doDownloadPdf(Long id, HttpServletResponse response) {
        ContractorInvoiceViewVO viewVO = view(BigInteger.valueOf(id));
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        Document document = new Document(PageSize.A4, 0, 0, 60, 0);
        try {
            createInvoicePdf(viewVO, bos, document);
            document.close();
            String fileName = viewVO.getInvoiceNumber() + ".pdf";
            HeaderUtil.setInvoiceHeader(response, fileName, "application/pdf");
            OutputStream outputStream = response.getOutputStream();
            IOUtils.write(bos.toByteArray(), outputStream);
        } catch (Exception e) {
            log.error("invoice: print error,{}", e);
        } finally {
            document.close();
            try {
                bos.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }
    }

    private void createInvoicePdf(ContractorInvoiceViewVO viewVO, ByteArrayOutputStream bos, Document document) throws Exception {

        Map<String, Object> address = null;
        if (StringUtils.isNotBlank(viewVO.getClientAddress()) && !viewVO.getClientAddress().equals("null")) {
            address = new HashMap<>();
            address.put("relation_client_address", viewVO.getClientAddress());
            address.put("relation_client_name", viewVO.getClientName());
            address.put("relation_client_location", viewVO.getClientLocation());
        } else {
            TalentAssigment talentAssigment = talentAssignmentRepository.findById(viewVO.getAssignmentId().longValue()).get();
            List<Map<String, Object>> startList = contractorInvoiceNativeRepository.findStartInfo(talentAssigment.getTalentRecruitmentProcessId());
            if (!startList.isEmpty()) {
                LocalDate weekEndDate = viewVO.getWeekEndingDate().toLocalDateTime().toLocalDate();
                if (startList.size() == 1) {
                    BigInteger startId = (BigInteger) startList.get(0).get("id");
                    address = contractorInvoiceNativeRepository.findCompanyAddressAndNameByStartId(startId);
                } else {
                    for (Map<String, Object> start : startList) {
                        java.sql.Date startDate = (java.sql.Date) start.get("start_date");
                        java.sql.Date endDate = (java.sql.Date) start.get("end_date");
                        if (!(weekEndDate.isBefore(startDate.toLocalDate())) && !(weekEndDate.isAfter(endDate.toLocalDate()))) {
                            BigInteger startId = (BigInteger) start.get("id");
                            address = contractorInvoiceNativeRepository.findCompanyAddressAndNameByStartId(startId);
                            break;
                        }
                    }

                    if (address == null) {
                        address = contractorInvoiceNativeRepository.findCanadaProvinceByAssignmentId(viewVO.getAssignmentId().longValue());
                    }
                }
            }
        }

        ContractorInvoicePrintDTO printDTO = ContractorInvoicePrintDTO.transformToDTO(viewVO);
        InvoicePdfUtil pdfUtil = new InvoicePdfUtil();
        PdfWriter.getInstance(document, bos);
        document.open();
        String currency = null;
        if (null != viewVO.getRegularInvoiceList() && !viewVO.getRegularInvoiceList().isEmpty()) {
            currency = viewVO.getRegularInvoiceList().get(0).getCurrency();
        } else if (null != viewVO.getExpenseList() && !viewVO.getExpenseList().isEmpty()) {
            currency = viewVO.getExpenseList().get(0).getCurrency();
        }
        printDTO.setCurrency(currency);
        if (address != null) {
            printDTO.setCustomerAddress(address.get("relation_client_address") + "");
            printDTO.setCustomerName(address.get("relation_client_name") + "");
            String location = address.get("relation_client_location") + "";
            if (StringUtils.isNotBlank(location)) {
                printDTO.setCustomerLocation(location);
            }
        }
        if (currency.equals("CAD")) {
            pdfUtil.createInvoicePdfForCanada(document, printDTO, false);
        } else {
            pdfUtil.createInvoicePdfForUS(document, printDTO, false);
        }
    }

    /**
     * 查询是否生成invoice
     *
     * @param dto
     * @return
     */
    @Override
    public List<ContractorInvoiceCheckInvoiceVO> getIdByAssignmentIdAndWeekEndingDate(ContractorInvoiceExistDTO dto) {
        List<Long> assignmentId = dto.getCheckInvoiceList().stream().map(CheckInvoiceDTO::getAssignmentId).collect(Collectors.toList());
        List<LocalDate> weekEndingDate = dto.getCheckInvoiceList().stream().map(CheckInvoiceDTO::getWeekEndingDate).collect(Collectors.toList());
        List<Integer> recordIndex = dto.getCheckInvoiceList().stream().map(CheckInvoiceDTO::getRecordIndex).toList();
        List<TContractorInvoice> tContractorInvoiceList = contractorInvoiceRepository.getAllByAssignmentIdAndWeekEndingDateAndRecordIndex(assignmentId, weekEndingDate, dto.getInvoiceType(), recordIndex);
        Map<String, String> strMap = new HashMap<>();
        if (!tContractorInvoiceList.isEmpty()) {
            for (TContractorInvoice t : tContractorInvoiceList) {
                strMap.put(t.getAssignmentId() + "" + t.getWeekEndingDate().toLocalDateTime().toLocalDate() + t.getRecordIndex(), t.getInvoiceNumber());
            }
        }
        List<ContractorInvoiceCheckInvoiceVO> vo = new ArrayList<>();
        dto.getCheckInvoiceList().forEach(c -> {
            ContractorInvoiceCheckInvoiceVO bean = new ContractorInvoiceCheckInvoiceVO();
            bean.setAssignmentId(c.getAssignmentId());
            bean.setWeekEndingDate(c.getWeekEndingDate());
            String key = c.getAssignmentId() + "" + c.getWeekEndingDate() + c.getRecordIndex();
            bean.setExist(strMap.containsKey(key));
            bean.setInvoiceNumber(strMap.get(key));
            vo.add(bean);
        });
        return vo;
    }

    /**
     * contractor invoice数据迁移
     *
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Exception.class)
    public String dataMigrateInvoice() {

        LoginUtil.simulateLoginWithClient();

        deleteTemp();

        while (true) {
            //创建单张invoice
            List<TMongodbToJobdivaTemp> tMongodbToJobdivaTemps = tMongodbToJobdivaTempRepository.selectAllByStatus();
            if (tMongodbToJobdivaTemps.isEmpty()) {
                return "not find data";
            }
            Long tenantId = tMongodbToJobdivaTempRepository.findTenantIdByIds(tMongodbToJobdivaTemps.stream().map(TMongodbToJobdivaTemp::getId).toList());

            // 手动控制事务
            DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
            TransactionStatus status = transactionManager.getTransaction(definition);
            try {
                for (TMongodbToJobdivaTemp temp : tMongodbToJobdivaTemps) {
                    log.info("migrate invoice, temp：{}", JSON.toJSONString(temp));
                    Integer invoiceType = InvoiceType.REGULAR.toDbValue();
                    List<ContractorInvoiceTimesheetDTO> timesheetList = null;
                    if (temp.getInvoiceType().equals(InvoiceType.REGULAR.toDbValue())) {
                        timesheetList = tMongodbToJobdivaTempNativeRepository.queryTimesheetAndExpenseList(temp.getAssignmentId().longValue(), DateUtil.localDateToString(temp.getWeedEndDate()), DateUtil.localDateToString(temp.getWeedEndDate()), InvoiceType.REGULAR.toDbValue(), tenantId);
                        log.info("migrate invoice, Found {} timesheet data", timesheetList.size());
                    } else {
                        timesheetList = tMongodbToJobdivaTempNativeRepository.queryTimesheetAndExpenseList(temp.getAssignmentId().longValue(), DateUtil.localDateToString(temp.getWeedEndDate()), DateUtil.localDateToString(temp.getWeedEndDate()), InvoiceType.EXPENSE.toDbValue(), tenantId);
                        invoiceType = InvoiceType.EXPENSE.toDbValue();
                    }
                    if (null != timesheetList && !timesheetList.isEmpty() && StringUtils.isNotBlank(timesheetList.get(0).getTalentName())) {
                        //invoice list info
                        List<TContractorInvoice> tContractorInvoiceList = new ArrayList<>();
                        //invoice timesheet detail info
                        Map<String, List<TInvoiceExpenseInfo>> invoiceExpense = new HashMap<>();
                        Map<String, List<TInvoiceTimesheetInfo>> invoiceTimesheet = new HashMap<>();
                        log.info("migrate invoice, start assembly timesheet info");
                        assemblyInvoiceDetailInfo(timesheetList, tContractorInvoiceList, invoiceTimesheet, invoiceExpense, invoiceType, temp.getInvoiceNumber());
                        log.info("migrate invoice, start inserting timesheet info");

                        if (StringUtils.isNotBlank(temp.getGroupInvoiceNumber())) {
                            temp.setStatus(3);//需要group invoice 进一步处理

                            if (!tContractorInvoiceList.isEmpty()) {
                                for (TContractorInvoice t : tContractorInvoiceList) {
                                    t.setGroupInvoiceNumber(temp.getGroupInvoiceNumber());
                                }
                            }

                        } else {
                            temp.setStatus(1);
                        }

                        //赋值invoice Date
                        if (!tContractorInvoiceList.isEmpty()) {
                            for (TContractorInvoice t : tContractorInvoiceList) {
                                t.setInvoiceDate(Timestamp.valueOf(temp.getInvoiceDate().atTime(LocalTime.MIDNIGHT)));
                                if (temp.getInvoiceStatus().equals(InvoiceStatusType.VOID.toDbValue())) {
                                    t.setInvoiceStatus(InvoiceStatusType.VOID);
                                } else {
                                    t.setInvoiceStatus(InvoiceStatusType.GROUPED);
                                }
                            }
                        }

                        doSaveDetail(tContractorInvoiceList, invoiceTimesheet, invoiceExpense);

                        if (!tContractorInvoiceList.isEmpty()) {
                            temp.setInvoiceId(tContractorInvoiceList.get(0).getId() + "");
                            tMongodbToJobdivaTempRepository.save(temp);
                            log.info("migrate invoice, update MongodbToJobdivaTemp status ={},tempId:{} ", temp.getStatus(), temp.getId());
                        } else {
                            temp.setStatus(5);
                            tMongodbToJobdivaTempRepository.save(temp);
                            log.error("migrate invoice, Unable to find data information,tempId:{}", temp.getId());
                        }
                    } else {
                        temp.setStatus(5);
                        tMongodbToJobdivaTempRepository.save(temp);
                        log.error("migrate invoice, Unable to find data information,tempId:{}", temp.getId());
                    }
                }
                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                throw e;
            }
        }
    }


    public void deleteTemp() {
        while (true) {
            List<TMongodbToJobdivaTemp> deleteTemp = tMongodbToJobdivaTempRepository.selectDeleteByStatus();
            if (deleteTemp.isEmpty()) {
                break;
            }
            // 手动控制事务
            DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
            TransactionStatus status = transactionManager.getTransaction(definition);
            try {
                for (TMongodbToJobdivaTemp temp : deleteTemp) {
                    Set<Long> idList = Arrays.stream(temp.getInvoiceId().split(",")).map(m -> Long.valueOf(m)).collect(Collectors.toSet());

                    List<TGroupInvoiceRecord> tGroupInvoiceRecords = groupInvoiceRecordRepository.findGroupInvoiceIdByInvoiceIds(idList);
                    if (null != tGroupInvoiceRecords && !tGroupInvoiceRecords.isEmpty()) {

                        Set<Long> groupInvoiceIdList = tGroupInvoiceRecords.stream().map(TGroupInvoiceRecord::getGroupInvoiceId).map(a -> a.longValue()).collect(Collectors.toSet());

                        groupInvoiceRepository.updateStatusByIds(groupInvoiceIdList);
                        log.info("migrate invoice, update group invoice status is invalid,ids:{}", JSON.toJSONString(groupInvoiceIdList));

                        Set<Long> invoiceRecordIdList = tGroupInvoiceRecords.stream().map(TGroupInvoiceRecord::getId).map(a -> a.longValue()).collect(Collectors.toSet());

                        Set<BigInteger> invoiceIdList = tGroupInvoiceRecords.stream().map(TGroupInvoiceRecord::getInvoiceId).collect(Collectors.toSet());
                        contractorInvoiceRepository.deleteAllById(invoiceIdList);

                        log.info("migrate invoice, delete contractor invoice,ids:{}", JSON.toJSONString(invoiceIdList));

                        groupInvoiceRecordRepository.updateStatusByIds(invoiceRecordIdList);
                        log.info("migrate invoice, update invoice record status is invalid,ids:{}", JSON.toJSONString(invoiceRecordIdList));

                        if (!invoiceIdList.isEmpty()) {
                            for (BigInteger id : invoiceIdList) {
                                invoiceTimesheetInfoRepository.deleteByInvoiceId(id);
                                log.info("migrate invoice, delete invoice timesheet info,id:{}", id);
                            }
                        }
                    } else {
                        Set<BigInteger> invoiceIdList = Arrays.stream(temp.getInvoiceId().split(",")).map(m -> BigInteger.valueOf(Long.valueOf(m))).collect(Collectors.toSet());
                        Set<BigInteger> invoiceIds = new HashSet<>();
                        for (BigInteger id : invoiceIdList) {
                            if (contractorInvoiceRepository.existsById(id)) {
                                invoiceIds.add(id);
                            }
                        }
                        if (!invoiceIds.isEmpty()) {
                            contractorInvoiceRepository.deleteAllById(invoiceIdList);
                        }
                        log.info("migrate invoice, delete contractor invoice,ids:{}", JSON.toJSONString(invoiceIdList));
                    }

                    tMongodbToJobdivaTempRepository.deleteById(temp.getId());
                    log.info("migrate invoice, delete mongodbToJobdiva,temp json:{}", JSON.toJSONString(temp));
                }
                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                throw e;
            }
        }
    }

    /**
     * contractor invoice数据迁移
     * country is canada 删除 13%Tax
     *
     * @return
     */
    @Override
    //@Transactional(rollbackFor = Exception.class)
    public String migrateInvoiceByDeleteCaTax() {

        LoginUtil.simulateLoginWithClient();

        while (true) {
            // 手动控制事务
            DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
            TransactionStatus status = transactionManager.getTransaction(definition);
            try {
                //创建单张invoice
                List<TMongodbToJobdivaTemp> tMongodbToJobdivaTemps = tMongodbToJobdivaTempRepository.selectAllByCountryIsCa();
                if (tMongodbToJobdivaTemps.isEmpty()) {
                    return "not find data";
                }
                log.info("migrate invoice delete Canada tax, data size:{}", tMongodbToJobdivaTemps.size());
                for (TMongodbToJobdivaTemp temp : tMongodbToJobdivaTemps) {
                    TContractorInvoice invoice = contractorInvoiceRepository.getById(BigInteger.valueOf(Long.valueOf(temp.getInvoiceId())));

                    //查询发票明细
                    List<TInvoiceTimesheetInfo> timesheetInfoList = invoiceTimesheetInfoRepository.findByInvoiceId(invoice.getId());
                    if (!timesheetInfoList.isEmpty()) {
                        List<TInvoiceTimesheetInfo> taxList = timesheetInfoList.stream()
                                .filter(m -> m.getQuantityType().toDbValue() == 5 && m.getItemDescription().equals("13% HST Tax"))
                                .collect(Collectors.toList());
                        if (!taxList.isEmpty()) {

                            TInvoiceTimesheetInfo invoiceTimesheetInfo = taxList.get(0);

                            //更新invoice totalAmount
                            invoice.setTotalAmount(invoice.getTotalAmount().subtract(invoiceTimesheetInfo.getTotalAmount()).setScale(2, BigDecimal.ROUND_HALF_UP));
                            contractorInvoiceRepository.save(invoice);
                            log.info("migrate invoice delete Canada tax, update invoice id:{}", invoice.getId());

                            //更新group invoice total amount
                            if (StringUtils.isNotBlank(invoice.getGroupInvoiceNumber())) {
                                TGroupInvoice tGroupInvoice = groupInvoiceRepository.findByGroupNumberAndStatus(invoice.getGroupInvoiceNumber(), 1);
                                if (null != tGroupInvoice) {
                                    tGroupInvoice.setInvoiceAmount(tGroupInvoice.getInvoiceAmount().subtract(invoiceTimesheetInfo.getTotalAmount()).setScale(2, BigDecimal.ROUND_HALF_UP));
                                    groupInvoiceRepository.save(tGroupInvoice);
                                }
                            }


                            //删除13% Tax
                            invoiceTimesheetInfoRepository.deleteById(invoiceTimesheetInfo.getId());
                            log.info("migrate invoice delete Canada tax, delete timesheet info,id:{}", invoiceTimesheetInfo.getId());

                        }

                        //更新TMongodbToJobdivaTemp status 状态
                        temp.setStatus(6);
                        tMongodbToJobdivaTempRepository.save(temp);
                    } else {
                        //更新TMongodbToJobdivaTemp status 状态
                        temp.setStatus(6);
                        tMongodbToJobdivaTempRepository.save(temp);
                    }
                }
                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                throw e;
            }
        }
    }

    @Override
    public Page<ContractorInvoiceSearchByApplicationVO> searchPageByApplication(ContractorInvoiceSearchByApplicationDTO dto) {
        List<ContractorInvoiceSearchByApplicationVO> voList = contractorInvoiceNativeRepository.searchDataByApplication(dto);
        Long count = contractorInvoiceNativeRepository.searchCountByApplication(dto);
        return new PageImpl<>(voList, Pageable.unpaged(), count);
    }

    @Override
    @Transactional
    public JSONArray deleteByAssignment(Collection<Long> assignmentIds) {
        JSONArray result = new JSONArray();
        List<TContractorInvoice> contractorInvoice = contractorInvoiceRepository.findAllByAssignmentIdIn(assignmentIds.stream().map(a -> new BigInteger(a.toString())).toList());
        if (contractorInvoice.isEmpty()) {
            return result;
        }
        for (TContractorInvoice t : contractorInvoice) {
            JSONObject item = DtoToJsonUtil.toJsonWithColumnNames(t);
            item.put("WARN!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", "Contractor 发票未执行删除");
            result.add(item);
        }
        return result;
    }

}