package com.altomni.apn.jobdiva.domain.enumeration.timesheet;

public enum TimesheetExcelUploadFailReason {

    COMPANY_DUPLICATION(0, "Company Duplication"),
    TALENT_DUPLICATION(1, "Candidate Duplication"),
    NONE(2, "No Match"),

    WEEK_INCOMPLETE(3, "Cycle Incompleteness Or No MANUALLY"),

    WEEKENDING_DUPLICATION(4, "Weekending Duplication"),

    CALCULATION_METHODS_DO_NOT_MATCH(5, "Calculation Methods Do Not Match"),

    TIMESHEET_MODE_DOES_NOT_MATCH(6, "Timesheet Mode Does Not Match"),
    ;

    private Integer code;

    private String description;

    TimesheetExcelUploadFailReason(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
