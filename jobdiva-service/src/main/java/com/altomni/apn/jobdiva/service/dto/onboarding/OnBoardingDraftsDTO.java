package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingDraftsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long talentRecruitmentProcessId;

    private Long packageId;

    private List<OnBoardingDocumentsDTO> documents;

}
