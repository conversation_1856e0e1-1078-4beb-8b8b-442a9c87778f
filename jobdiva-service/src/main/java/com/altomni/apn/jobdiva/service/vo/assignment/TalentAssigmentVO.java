package com.altomni.apn.jobdiva.service.vo.assignment;

import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * A record
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class TalentAssigmentVO extends TalentAssigment implements Serializable {

    private String jobTitle;

    private String companyName;

}
