package com.altomni.apn.jobdiva.service.dto.onboarding.settings;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.ActionRequiredType;
import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingPackageDocuments;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingPackageDocumentsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id"));

    private Long id;

    private Long packageId;

    private Long documentId;

    private String name;

    private String s3Key;

    private ActionRequiredType actionRequired;

    private Boolean mandatory;

    private Boolean selected;

    private Integer ordering;

    private Integer onboardingType;

    public OnBoardingPackageDocumentsDTO(Long id, Long packageId, Long documentId, String name ,String s3Key
        , ActionRequiredType actionRequired, Boolean mandatory) {
        this.id = id;
        this.packageId = packageId;
        this.documentId = documentId;
        this.name = name;
        this.s3Key = s3Key;
        this.actionRequired = actionRequired;
        this.mandatory = mandatory;
    }

    public static OnBoardingPackageDocuments fromDto(OnBoardingPackageDocumentsDTO dto) {
        OnBoardingPackageDocuments relation = new OnBoardingPackageDocuments();
        ServiceUtils.myCopyProperties(dto, relation, UpdateSkipProperties);
        relation.setTenantId(SecurityUtils.getTenantId());
        return relation;
    }

}
