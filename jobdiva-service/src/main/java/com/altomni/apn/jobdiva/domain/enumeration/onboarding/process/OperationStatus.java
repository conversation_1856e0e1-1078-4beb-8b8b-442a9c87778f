package com.altomni.apn.jobdiva.domain.enumeration.onboarding.process;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The SpecialDocumentType enumeration.
 */
public enum OperationStatus implements ConvertedEnum<Integer> {
    READ(0,"read"),
    MARK_AS_COMPLETED(1,"mark as Completed"),
    COMPLETE_AND_SIGNED(2,"complete and Signed"),
    UPLOADED(3,"uploaded"),
    REWORKED(4,"redid this item"),
    FIRST_VIEWED(9,"first viewed");

    // static resolving:
    public static final ReverseEnumResolver<OperationStatus, Integer> resolver =
        new ReverseEnumResolver<>(OperationStatus.class, OperationStatus::toDbValue);
    private final int dbValue;
    private final String name;

    OperationStatus(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }

    public static OperationStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
