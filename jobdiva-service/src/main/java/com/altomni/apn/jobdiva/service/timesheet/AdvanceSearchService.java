package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.jobdiva.service.dto.timesheet.AdvanceSearchDTO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;

import java.util.List;

public interface AdvanceSearchService {

    SummaryDataVO searchForTimeSheet(AdvanceSearchDTO dto, List<Long> talents, List<Long> assignmentIds);

    SummaryDataVO searchForExpense(AdvanceSearchDTO dto,List<Long> talents, List<Long> assignmentIds);

}
