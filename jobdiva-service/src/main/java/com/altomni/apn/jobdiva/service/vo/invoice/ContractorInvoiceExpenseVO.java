package com.altomni.apn.jobdiva.service.vo.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseType;
import com.altomni.apn.jobdiva.util.InvoiceUtil;
import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.math.BigDecimal;

@ApiModel(description = "ContractorInvoiceExpenseVO")
public class ContractorInvoiceExpenseVO implements Serializable {

    private String currency;

    private String expenseCategory;

    private String expenseDate;

    private BigDecimal expenseMoney;

    private String weekDay;

    private Integer sortIndex;

    public ContractorInvoiceExpenseVO(String currency, String expenseCategory, String expenseDate, BigDecimal expenseMoney, String weekDay) {
        this.currency = currency;
        this.expenseCategory = expenseCategory;
        this.expenseDate = expenseDate;
        this.expenseMoney = expenseMoney;
        this.weekDay = weekDay;
    }

    public ContractorInvoiceExpenseVO() {
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getExpenseCategory() {
        return expenseCategory;
    }

    public void setExpenseCategory(String expenseCategory) {
        if (InvoiceUtil.isNumeric2(expenseCategory)) {
            this.expenseCategory = expenseCategory != null ? ExpenseType.fromDbValue(Integer.valueOf(expenseCategory)).getDescription() : "";
        } else {
            this.expenseCategory = expenseCategory;
        }
    }

    public String getExpenseDate() {
        return expenseDate;
    }

    public void setExpenseDate(String expenseDate) {
        this.expenseDate = expenseDate;
    }

    public BigDecimal getExpenseMoney() {
        return expenseMoney;
    }

    public void setExpenseMoney(BigDecimal expenseMoney) {
        this.expenseMoney = expenseMoney;
    }

    public String getWeekDay() {
        return weekDay;
    }

    public void setWeekDay(String weekDay) {
        this.weekDay = weekDay;
    }

    public Integer getSortIndex() {
        return sortIndex;
    }

    public void setSortIndex(Integer sortIndex) {
        this.sortIndex = sortIndex;
    }
}
