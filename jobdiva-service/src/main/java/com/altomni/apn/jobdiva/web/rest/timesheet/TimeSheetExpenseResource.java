package com.altomni.apn.jobdiva.web.rest.timesheet;


import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.RecordSearchDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.SummaryQueryDTO;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetExpenseService;
import com.altomni.apn.jobdiva.service.vo.timesheet.EndingDateListVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = {"TimeSheetExpense"})
@RestController
@RequestMapping("/api/v3/expense")
public class TimeSheetExpenseResource {

    @Resource
    private TimeSheetExpenseService recordService;

    /**
     * @param dto
     * @return
     */
    @PostMapping("/list")
    @Timed
    public ResponseEntity<BreakTimeDTO> findExpenseRecord(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] REST find expense record , param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        BreakTimeDTO records = recordService.findExpenseRecord(dto,SecurityUtils.getUserId());
        return ResponseEntity.ok(records);
    }

    /**
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @Timed
    public ResponseEntity<Integer> saveBreakTime(@RequestBody BreakTimeDTO dto) {
        log.info("[expense: User @{}] save expense:", SecurityUtils.getUserId());
        Integer records = recordService.saveExpenseTime(dto, SecurityUtils.getUserId());
        return ResponseEntity.ok(records);
    }

    @PostMapping("/delete")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> deleteByLineIndex(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] REST deleteByLineIndex", SecurityUtils.getUserId());
        Integer records = recordService.deleteByLineIndex(dto);
        return ResponseEntity.ok(records);
    }


    @PostMapping("/summary")
    @Timed
    public ResponseEntity<SummaryDataVO> summary(@RequestBody SummaryQueryDTO dto) {
        log.info("[timesheet: User @{}] REST to expense summary:", SecurityUtils.getUserId());
        SummaryDataVO result = recordService.expenseList(dto,SecurityUtils.getUserId());
        return ResponseEntity.ok(result);
    }

    /**
     * get week ending dates as list
     * @return
     */
    @GetMapping("/dateList")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<EndingDateListVO>> getWeekEndingDates() {
        log.info("[timesheet: User @{}] REST to getWeekEndingDates", SecurityUtils.getUserId());
        List<EndingDateListVO> result = recordService.weekEndingDates(SecurityUtils.getUserId(), false);
        return ResponseEntity.ok(result);
    }



}
