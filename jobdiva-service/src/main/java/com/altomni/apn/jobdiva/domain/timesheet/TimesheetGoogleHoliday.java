package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

@Entity
@Data
@Table(name = "timesheet_google_holiday")
public class TimesheetGoogleHoliday extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @Column(name = "company_id")
    private Long companyId ;

    @Column(name = "holiday_day")
    private LocalDate holidayDay ;

    @Column(name = "hours")
    private Double hours ;
}
