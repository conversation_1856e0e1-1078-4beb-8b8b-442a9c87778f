package com.altomni.apn.jobdiva.service.onboarding.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.*;
import cn.hutool.crypto.SecureUtil;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetUserTokenStore;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.jobdiva.config.env.ApplicationProperties;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.config.thread.FillDocumentsThreadTask;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.ApprovalStatus;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.OperationStatus;
import com.altomni.apn.jobdiva.domain.enumeration.onboarding.setting.ActionRequiredType;
import com.altomni.apn.jobdiva.domain.onboarding.*;
import com.altomni.apn.jobdiva.repository.process.*;
import com.altomni.apn.jobdiva.repository.settings.OnBoardingDocumentsRepository;
import com.altomni.apn.jobdiva.repository.settings.OnBoardingPackageDocumentsRepository;
import com.altomni.apn.jobdiva.repository.settings.OnBoardingPackagesRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetUserRepository;
import com.altomni.apn.jobdiva.service.application.ApplicationService;
import com.altomni.apn.jobdiva.service.assignment.AssignmentSyncToHrService;
import com.altomni.apn.jobdiva.service.common.CommonService;
import com.altomni.apn.jobdiva.service.dto.onboarding.*;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsBriefDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackagesDTO;
import com.altomni.apn.jobdiva.service.mail.MailService;
import com.altomni.apn.jobdiva.service.onboarding.OnBoardingService;
import com.altomni.apn.jobdiva.service.store.StoreService;
import com.altomni.apn.jobdiva.service.talent.TalentService;
import com.altomni.apn.jobdiva.service.user.UserService;
import com.altomni.apn.jobdiva.util.FillPDFIInfoUtil;
import com.altomni.apn.user.repository.permission.PermissionRoleRepository;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.pdfbox.multipdf.Splitter;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.imageio.stream.ImageOutputStream;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.altomni.apn.jobdiva.constants.Constants.*;

@Slf4j
@Service
public class OnBoardingServiceImpl implements OnBoardingService {

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private OnBoardingDraftsRepository onBoardingDraftsRepository;

    @Resource
    private OnBoardingDocumentsRepository onBoardingDocumentsRepository;

    @Resource
    private OnBoardingProcessHistoriesRepository onBoardingProcessHistoriesRepository;

    @Resource
    private OnBoardingProcessApprovalDetailsRepository onBoardingProcessApprovalDetailsRepository;

    @Resource
    private OnBoardingProcessOperationDetailsRepository onBoardingProcessOperationDetailsRepository;

    @Resource
    private TimeSheetUserRepository timeSheetUserRepository;

    @Resource
    private AssignmentSyncToHrService assignmentSyncToHrService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private StoreService storeService;

    @Resource
    private OnBoardingProcessHistorySignaturesRepository signaturesRepository;

    @Resource
    private OnBoardingPackagesRepository onBoardingPackagesRepository;

    @Resource
    private OnBoardingPackageDocumentsRepository onBoardingPackageDocumentsRepository;

    @Resource
    private TalentService talentService;

    @Resource
    private UserService userService;

    @Resource
    private MailService mailService;

    private volatile ExecutorService executorService;

    @Resource
    private PermissionRoleRepository roleRepository;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private TalentAssignmentRepository assignmentRepository;

    @Resource
    private CommonService commonService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Resource
    TimesheetUserTokenStore timesheetUserTokenStore;

    private static String DATE_FORMAT_MM_DD_YYYY = "MM/dd/yyyy";

    @Override
    public OnBoardingDraftsDTO getDraftByTalentRecruitmentProcessId(Long id) {
        checkPermission(id, "[APN: OnBoarding Service @{}] REST request to get draft by application id error", "Sorry, you have no permission to get this draft .");
        List<OnBoardingDocumentsDTO> documentsDTOList = onBoardingDocumentsRepository.findAllByTalentRecruitmentProcessIdAndTenantId(id, SecurityUtils.getTenantId());
        OnBoardingDraftsDTO draftsDTO = new OnBoardingDraftsDTO();
        if (CollectionUtil.isNotEmpty(documentsDTOList)) {
            Optional<OnBoardingDocumentsDTO> draft = documentsDTOList.stream().filter(s -> ObjectUtil.isNotNull(s.getPackageId())).findFirst();
            draft.ifPresent(dto -> draftsDTO.setPackageId(dto.getPackageId()));
            draftsDTO.setTalentRecruitmentProcessId(id);
            draftsDTO.setDocuments(documentsDTOList);
        }
        return draftsDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void saveDraft(OnBoardingDraftsDTO dto) {
        checkPermission(dto.getTalentRecruitmentProcessId(), "[APN: OnBoarding Service @{}] REST request to save draft error", "Sorry, you have no permission to save draft .");
        //delete history drafts
        onBoardingDraftsRepository.deleteAllByTalentRecruitmentProcessIdAndTenantId(dto.getTalentRecruitmentProcessId(), SecurityUtils.getTenantId());
        //save drafts
        if (CollectionUtil.isEmpty(dto.getDocuments())) {
            log.error("[APN: OnBoarding Service @{}] REST request to save draft error, documents can not be null  .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_DRAFT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //filter disabled documents
        List<OnBoardingDocuments> documents = onBoardingDocumentsRepository.findAllByIdInAndTenantIdAndActivated(
                dto.getDocuments().stream().map(OnBoardingDocumentsDTO::getId).collect(Collectors.toList()), SecurityUtils.getTenantId(), Boolean.TRUE);
        if (CollectionUtil.isEmpty(documents)) {
            log.error("[APN: OnBoarding Service @{}] REST request to save draft error, documents ids are all disabled .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_DRAFT_DISABLED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        onBoardingDraftsRepository.saveAll(documents.stream().map(d -> {
            OnBoardingDrafts drafts = new OnBoardingDrafts();
            ServiceUtils.myCopyProperties(dto, drafts);
            drafts.setDocumentId(d.getId());
            drafts.setTenantId(SecurityUtils.getTenantId());
            dto.getDocuments().forEach(o -> {
                if (o.getId().equals(d.getId())) {
                    drafts.setOnboardingType(o.getOnboardingType());
                }
            });
            return drafts;
        }).collect(Collectors.toList()));
    }

    @Override
    public void deleteDraft(Long id) {
        checkPermission(id, "[APN: OnBoarding Service @{}] REST request to delete draft error", "Sorry, you have no permission to delete draft .");
        //delete history drafts
        onBoardingDraftsRepository.deleteAllByTalentRecruitmentProcessIdAndTenantId(id, SecurityUtils.getTenantId());
    }

    @Override
    public List<OnBoardingPackagesDTO> getAllPackages(Long talentRecruitmentProcessId) {
        checkPermission(talentRecruitmentProcessId, "[APN: OnBoarding Service @{}] REST get all packages, you have no permission to get all packages .", "Sorry, you have no permission to get all packages .");
        return onBoardingPackagesRepository.findAllByActivatedAndTenantIdOrderByLastModifiedDateDesc(Boolean.TRUE, SecurityUtils.getTenantId());
    }

    @Override
    public List<OnBoardingPackageDocumentsDTO> findDocumentsByPackageId(Long talentRecruitmentProcessId, Long id) {
        Optional<OnBoardingPackages> optionalPackage = onBoardingPackagesRepository.findAllByIdAndTenantIdAndActivated(id, SecurityUtils.getTenantId(), Boolean.TRUE);
        if (!optionalPackage.isPresent()) {
            log.error("[APN: OnBoarding Service @{}] REST find documents by package id error, can not find this package: {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_NOT_FIND_DOCUMENT_BY_PACKAGE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //check permission
        if (ObjectUtil.isNull(checkPackagePermission(talentRecruitmentProcessId, optionalPackage))) {
            log.error("[APN: OnBoarding Service @{}] REST find documents by package id error, you have no permission to visit this package : {}", SecurityUtils.getUserId(), id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_FIND_DOCUMENT_NOT_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return onBoardingPackageDocumentsRepository.findDocumentsByPackageId(id, SecurityUtils.getTenantId());
    }

    @Override
    public List<OnBoardingPackageDocumentsDTO> findAllDocuments(SearchDocumentsDTO searchDocumentsDTO) {
        checkPermission(searchDocumentsDTO.getTalentRecruitmentProcessId(), "[APN: OnBoarding Service @{}] REST request to find additional document list error", "Sorry, you have no permission to find additional document list .");
        List<OnBoardingPackageDocumentsDTO> result = new ArrayList<>();
        List<Object[]> queryResult = new ArrayList<>();
        StringBuilder datasql = new StringBuilder("select d.id as id ,d.name as name " +
                " ,d.action_required as actionRequired ,ifnull(b.selected, '0') as selected,d.s3_key from onboarding_documents d " +
                " left join (select dd.id ,'1' as selected from onboarding_documents dd where dd.tenant_id = ?1 and dd.activated = true ");
        if (CollectionUtil.isNotEmpty(searchDocumentsDTO.getDocuments())) {
            datasql.append(" and dd.id in (").append(StringUtils.join(searchDocumentsDTO.getDocuments().toArray(), StrUtil.COMMA)).append(")");
        } else {
            datasql.append(" and dd.id in (null)");
        }
        datasql.append(" ) as b on d.id = b.id where d.activated = true and d.tenant_id = ?1  ");
        if (ObjectUtil.isNotNull(searchDocumentsDTO.getName())) {
            datasql.append(" and LOCATE('").append(searchDocumentsDTO.getName()).append("',d.name) > 0 ");
        }
        datasql.append(" order by ifnull(b.selected, '0') desc ");
        queryResult = entityManager.createNativeQuery(datasql.toString())
                .setParameter(1, SecurityUtils.getTenantId()).getResultList();
        if (CollectionUtil.isNotEmpty(queryResult)) {
            queryResult.forEach(s -> {
                OnBoardingPackageDocumentsDTO dto = new OnBoardingPackageDocumentsDTO();
                dto.setId(Long.parseLong(StrUtil.toString(s[0])));
                dto.setName(StrUtil.toString(s[1]));
                if (ObjectUtil.isNotNull(s[2])) {
                    dto.setActionRequired(ActionRequiredType.fromDbValue(Integer.parseInt(StrUtil.toString(s[2]))));
                }
                if (ObjectUtil.isNotNull(s[3])) {
                    dto.setSelected(BooleanUtil.toBoolean(StrUtil.toString(s[3])));
                } else {
                    dto.setSelected(false);
                }
                dto.setS3Key(StrUtil.toString(s[4]));
                result.add(dto);
            });
        }
        return result;
    }

    @Override
    public List<OnBoardingProcessesHistoriesDTO> getHistories(Long id, String timezone) {
        checkPermission(id, "[APN: OnBoarding Service @{}] REST request to get history documents list error", "Sorry, you have no permission to get history documents document list .");
        List<OnBoardingProcessesHistoriesDTO> result = new ArrayList<>();
        //get histories list
        List<OnBoardingProcessHistories> historiesList = onBoardingProcessHistoriesRepository.findAllByTalentRecruitmentProcessIdAndTenantIdOrderByCreatedDateDesc(id, SecurityUtils.getTenantId());
        if (CollectionUtil.isNotEmpty(historiesList)) {
            //group histories list by process id
            Map<String, List<OnBoardingProcessHistories>> groupMap = historiesList.stream()
                    .collect(Collectors.groupingBy(OnBoardingProcessHistories::getProcessId, Collectors.toList()));
            for (String processId : groupMap.keySet()) {
                //format data
                OnBoardingProcessesHistoriesDTO dto = new OnBoardingProcessesHistoriesDTO();
                List<OnBoardingProcessHistories> groupList = groupMap.get(processId);
                dto.setProcessId(processId);
                Optional<OnBoardingProcessHistories> oneHistory = groupList.stream().filter(s -> ObjectUtil.isNotNull(s.getPackageId())).findFirst();
                //set package info
                if (oneHistory.isPresent()) {
                    dto.setPackageId(oneHistory.get().getPackageId());
                    dto.setPackageName(oneHistory.get().getPackageName());
                }
                //set details
                Optional<OnBoardingProcessHistories> oneDocument = groupList.stream().findAny();
                oneDocument.ifPresent(onBoardingProcessHistories -> dto.setDetail(DateUtil.fromInstantToDateString(onBoardingProcessHistories.getCreatedDate(), timezone) + StrUtil.SPACE + "by" + StrUtil.SPACE + attachCreatedUser(onBoardingProcessHistories.getCreatedBy())));
                dto.setDocuments(groupList.stream().map(OnBoardingProcessHistories::toRelationsDTO).collect(Collectors.toList()));
                result.add(dto);
            }
        }
        return result.stream().sorted(Comparator.comparing(OnBoardingProcessesHistoriesDTO::getDetail).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<OnBoardingProcessesCompletionsDTO> getCompletions(Long id, String timezone) {
        checkPermission(id, "[APN: OnBoarding Service @{}] REST request to get OnBoarding completions list error", "Sorry, you have no permission to get OnBoarding completions list .");
        return getOnBoardingProcessesCompletionsDTOS(id, Boolean.TRUE, timezone);
    }

    @NotNull
    private List<OnBoardingProcessesCompletionsDTO> getOnBoardingProcessesCompletionsDTOS(Long id, Boolean isAM, String timezone) {
        List<OnBoardingProcessesCompletionsDTO> result = new ArrayList<>();
        List<Object[]> queryResult = new ArrayList<>();
        StringBuilder dataSql = new StringBuilder("""
                select ph.id,ph.talent_recruitment_process_id,ph.process_id, ph.document_id, ph.document_name, ph.s3_key, d.name ,ph.s3_key_source
                 ,pad.approval_status , pad.created_date as approval_created_date, pad.created_by as approval_created_by, pod.operation_status, pod.created_date as operation_created_date 
                 ,pod.created_by AS operation_user_id, ph.created_date as history_created_date, ph.completion_status, ph.action_required,pad.s3_key as rejected_s3_key,ph.document_name_uploaded,
                 ph.onboarding_type,ph.package_name from onboarding_process_histories ph 
                 left join (select a.* from onboarding_process_approval_details as a right join (select history_id, max(created_date) as maxtime from onboarding_process_approval_details 
                 where history_id is not null group by history_id) as b on a.history_id=b.history_id and a.created_date=b.maxtime order by a.history_id asc) pad on ph.id = pad.history_id 
                 left join (select a.* from onboarding_process_operation_details as a right join (select history_id, max(created_date) as maxtime from onboarding_process_operation_details 
                 where history_id is not null group by history_id) as b on a.history_id=b.history_id and a.created_date=b.maxtime order by a.history_id asc)  pod on ph.id = pod.history_id 
                 left join onboarding_documents d on ph.document_id = d.id 
                 left join talent_recruitment_process trp on trp.id = ph.talent_recruitment_process_id 
                 where ph.tenant_id = ?1 and ph.talent_recruitment_process_id = ?2  
                """);
        if (!isAM) {
            dataSql.append(" and ph.activated = 1 ");
        }
        dataSql.append(" order by pad.approval_status asc,pad.created_date desc ");
        queryResult = entityManager.createNativeQuery(dataSql.toString())
                .setParameter(1, SecurityUtils.getTenantId())
                .setParameter(2, id).getResultList();
        if (CollectionUtil.isNotEmpty(queryResult)) {
            queryResult.forEach(s -> {
                OnBoardingProcessesCompletionsDTO dto = new OnBoardingProcessesCompletionsDTO();
                dto.setId(Long.parseLong(StrUtil.toString(s[0])));
                dto.setTalentRecruitmentProcessId(Long.parseLong(StrUtil.toString(s[1])));
                dto.setProcessId(StrUtil.toString(s[2]));
                dto.setDocumentId(Long.parseLong(StrUtil.toString(s[3])));
                dto.setDocumentName(StrUtil.toString(s[4]));
                dto.setS3Key(StrUtil.toString(s[5]));
                dto.setDocumentSourceName(StrUtil.toString(s[6]));
                dto.setS3KeySource(StrUtil.toString(s[7]));
                if (ObjectUtil.isNotNull(s[8])) {
                    dto.setApprovalStatus(ApprovalStatus.fromDbValue(Integer.parseInt(StrUtil.toString(s[8]))));
                }
                if (ObjectUtil.isNotNull(s[9])) {
                    dto.setLastApprovalDate(DateUtil.stringToInstanctTimezone(StrUtil.toString(s[9]), timezone));
                }
                if (ObjectUtil.isNotNull(s[10])) {
                    dto.setLastApprovalBy(attachCreatedUser(StrUtil.toString(s[10])));
                }
                if (ObjectUtil.isNotNull(s[11])) {
                    dto.setOperationStatus(OperationStatus.fromDbValue(Integer.parseInt(StrUtil.toString(s[11]))));
                }
                if (ObjectUtil.isNotNull(s[12])) {
                    dto.setLastOperationDate(DateUtil.stringToInstanctTimezone(StrUtil.toString(s[12]), timezone));
                }
                if (ObjectUtil.isNotNull(s[13])) {
                    //if upload document by AM, set username; if upload by talent, set talentName
                    dto.setLastOperationBy(attachCreatedUser(StrUtil.toString(s[13])));
                }
                if (ObjectUtil.isNotNull(s[14])) {
                    dto.setAssignedOnDate(DateUtil.stringToInstanctTimezone(StrUtil.toString(s[14]), timezone));
                }
                if (ObjectUtil.isNotNull(s[15])) {
                    dto.setCompletionStatus(CompletionStatus.fromDbValue(Integer.parseInt(StrUtil.toString(s[15]))));
                }
                if (ObjectUtil.isNotNull(s[16])) {
                    dto.setActionRequired(ActionRequiredType.fromDbValue(Integer.parseInt(StrUtil.toString(s[16]))));
                }
                if (ObjectUtil.isNotNull(s[17])) {
                    dto.setS3KeyRejected(StrUtil.toString(s[17]));
                }
                if (ObjectUtil.isNotNull(s[18])) {
                    dto.setDocumentNameUploaded(StrUtil.toString(s[18]));
                }
                if (ObjectUtil.isNotNull(s[19])) {
                    dto.setOnboardingType(StrUtil.toString(s[19]));
                }
                if (ObjectUtil.isNotNull(s[20])) {
                    dto.setPackageName(StrUtil.toString(s[20]));
                }
                result.add(dto);
            });
        }
        return result;
    }

    private List<MyOnBoardingHistoryDocumentsDTO> getMyOnBoardingHistoryDocumentsDTOS(Long talentRecruitmentProcessId, Long historyId, String timezone) {
        List<MyOnBoardingHistoryDocumentsDTO> result = new ArrayList<>();
        List<Object[]> queryResult = new ArrayList<>();
        String datasql = """
                    (select ph.id,ph.document_name,ph.created_date as history_created_date,'' as md5_code,pad.ip as ip, pad.approval_status as status, pad.created_date as created_date, pad.created_by as created_by from onboarding_process_histories ph 
                    left join onboarding_process_approval_details pad on ph.id = pad.history_id 
                    where ph.tenant_id = ?1 and ph.talent_recruitment_process_id = ?2 and ph.id = ?3 order by pad.created_date limit 1 ) 
                    union all 
                    (select ph.id,ph.document_name,ph.created_date as history_created_date,phs.md5_code,pod.ip as ip, pod.operation_status as status, pod.created_date as created_date, pod.created_by as created_by from onboarding_process_histories ph 
                    left join onboarding_process_operation_details pod on ph.id = pod.history_id 
                    left join onboarding_documents d on ph.document_id = d.id 
                    left join onboarding_process_history_signatures phs on phs.history_id = ph.id 
                    where ph.tenant_id = ?1 and ph.talent_recruitment_process_id = ?2 and ph.id = ?3 and pod.operation_status = ?4 order by pod.created_date desc limit 1)
                    union all 
                    (select ph.id,ph.document_name,ph.created_date as history_created_date,phs.md5_code,pod.ip as ip, pod.operation_status as status, pod.created_date as created_date, pod.created_by as created_by from onboarding_process_histories ph 
                    left join onboarding_process_operation_details pod on ph.id = pod.history_id 
                    left join onboarding_documents d on ph.document_id = d.id 
                    left join onboarding_process_history_signatures phs on phs.history_id = ph.id 
                    where ph.tenant_id = ?1 and ph.talent_recruitment_process_id = ?2 and ph.id = ?3 and (pod.operation_status = ?5 or pod.operation_status = ?6) order by pod.created_date desc limit 1)
                """;
        queryResult = entityManager.createNativeQuery(datasql)
                .setParameter(1, SecurityUtils.getTenantId())
                .setParameter(2, talentRecruitmentProcessId)
                .setParameter(3, historyId)
                .setParameter(4, OperationStatus.FIRST_VIEWED.toDbValue())
                .setParameter(5, OperationStatus.UPLOADED.toDbValue())
                .setParameter(6, OperationStatus.COMPLETE_AND_SIGNED.toDbValue()).getResultList();

        if (CollectionUtil.isNotEmpty(queryResult)) {
            queryResult.forEach(s -> {
                MyOnBoardingHistoryDocumentsDTO dto = new MyOnBoardingHistoryDocumentsDTO();
                dto.setId(Long.parseLong(StrUtil.toString(s[0])));
                dto.setDocumentName(StrUtil.toString(s[1]));
                if (ObjectUtil.isNotNull(s[2])) {
                    dto.setAssignedOnDate(DateUtil.fromInstantToDateString(DateUtil.fromStringToInstant(StrUtil.toString(s[2])), timezone));
                }
                dto.setMd5Code(StrUtil.toString(s[3]));
                dto.setIp(StrUtil.toString(s[4]));
                if (ObjectUtil.isNotNull(s[5])) {
                    dto.setStatus(OperationStatus.fromDbValue(Integer.parseInt(StrUtil.toString(s[5]))));
                }
                if (ObjectUtil.isNotNull(s[6])) {
                    dto.setCreatedDate(DateUtil.fromInstantToDateString(DateUtil.fromStringToInstant(StrUtil.toString(s[6])), timezone));
                }
                if (ObjectUtil.isNotNull(s[7])) {
                    dto.setCreatedByName(attachCreatedUser(StrUtil.toString(s[7])));
                }
                result.add(dto);
            });
        }
        return result;
    }

    @NotNull
    private TalentContact getTalentContactByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        //find talent primary email
        TalentRecruitmentProcessVO talentRecruitmentProcessVO = applicationService.getTalentRecruitmentProcessBrief(talentRecruitmentProcessId).getBody();
        Optional<TalentContact> talentContact = talentService.findAllByTalentIdInAndTypeAndStatus(Collections.singletonList(talentRecruitmentProcessVO.getTalentId())).getBody().stream().min(Comparator.comparing(TalentContact::getSort));
        if (talentContact.isEmpty()) {
            log.error("[APN: OnBoarding Service @{}] REST request to get talentContact by talentRecruitmentProcessId error, can not find talent emails in this application .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_TALENT_CONTACT_EMAIL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return talentContact.get();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void saveApproval(HttpServletRequest request, OnBoardingApprovementDTO dto) {
        checkPermission(dto.getTalentRecruitmentProcessId(), "[APN: OnBoarding Service @{}] REST request to save approvement error", "Sorry, you have no permission to save approval status .");
        OnBoardingProcessHistories history = onBoardingProcessHistoriesRepository.getById(dto.getId());
        if (ObjectUtil.isNull(history)) {
            log.error("[APN: OnBoarding Service @{}] REST request to save approvement error, can not find this history data .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_APPROVAL_HISTORY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(dto.getId()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        TalentRecruitmentProcessVO application = applicationService.getTalentRecruitmentProcessBrief(dto.getTalentRecruitmentProcessId()).getBody();
        if (ObjectUtil.isNull(application)) {
            log.error("[APN: OnBoarding Service @{}] REST request to save approvement error, can not find this application .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_APPROVAL_APPLICATION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(dto.getId()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        //if approvalStatus is Deleted, can not do any approval
        //if approvalStatus is Missing/Rejected/Accepted,can not do any approval but deleted
        //if approvalStatus is Pending, can do accepted/rejected/deleted
        OnBoardingProcessApprovalDetails approval = onBoardingProcessApprovalDetailsRepository.findOneByHistoryId(dto.getId());
        if (ApprovalStatus.DELETED.equals(approval.getApprovalStatus())) {
            log.error("[APN: OnBoarding Service @{}] REST request to save approvement error, this document has deleted .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_APPROVAL_DELETE_STATUS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if ((ApprovalStatus.MISSING.equals(approval.getApprovalStatus()) || ApprovalStatus.REJECTED.equals(approval.getApprovalStatus())
                || ApprovalStatus.ACCEPTED.equals(approval.getApprovalStatus())) && !ApprovalStatus.DELETED.equals(dto.getApproveStatus())) {
            log.error("[APN: OnBoarding Service @{}] REST request to save approvement error, this document has rejected/accepted or missing .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_APPROVAL_REJECT_STATUS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (!ApprovalStatus.ACCEPTED.equals(dto.getApproveStatus()) && !ApprovalStatus.REJECTED.equals(dto.getApproveStatus())
                && !ApprovalStatus.DELETED.equals(dto.getApproveStatus()) && ApprovalStatus.PENDING.equals(approval.getApprovalStatus())) {
            log.error("[APN: OnBoarding Service @{}] REST request to save approvement error, this document is pending .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_APPROVAL_PENDING_STATUS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        //add approval info
        OnBoardingProcessApprovalDetails approvalDetails = new OnBoardingProcessApprovalDetails();
        approvalDetails.setTalentRecruitmentProcessId(history.getTalentRecruitmentProcessId());
        approvalDetails.setProcessId(history.getProcessId());
        approvalDetails.setHistoryId(history.getId());
        approvalDetails.setApprovalStatus(dto.getApproveStatus());
        approvalDetails.setIp(CommonUtils.getIpAddress(request));
        //if status is rejected, save current signed document s3Link
        if (ApprovalStatus.REJECTED.equals(dto.getApproveStatus())) {
            approvalDetails.setS3Key(history.getS3Key());
        }
        onBoardingProcessApprovalDetailsRepository.save(approvalDetails);
        //update history info
        if (ApprovalStatus.REJECTED.equals(dto.getApproveStatus()) || ApprovalStatus.DELETED.equals(dto.getApproveStatus())) {
            if (ApprovalStatus.REJECTED.equals(dto.getApproveStatus())) {
                history.setCompletionStatus(CompletionStatus.INCOMPLETE);
                //if this document redo/reject and actionRequired is MUST_BE_SIGNED_AND_RETURNED , edit source documents and fill initial info
                history.setS3Key(fillOneDocuments(application.getTalentId(), history, dto.getTimeZone()));
                onBoardingProcessHistoriesRepository.saveAndFlush(history);
                //send reject email
                if (ObjectUtil.isNull(dto.getNotes())) {
                    log.error("[APN: OnBoarding Service @{}] REST request to save approvement error, notes can not be null when ApproveStatus is 'REJECTED' .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SAVE_APPROVAL_NOTE_REJECT_STATUS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
                }
                sendRejectEmail(Collections.singletonList(getTalentContactByTalentRecruitmentProcessId(history.getTalentRecruitmentProcessId()).getContact()), dto.getNotes(), history.getDocumentName());
            }
            if (ApprovalStatus.DELETED.equals(dto.getApproveStatus())) {
                history.setCompletionStatus(CompletionStatus.INCOMPLETE);
                history.setActivated(Boolean.FALSE);
                onBoardingProcessHistoriesRepository.saveAndFlush(history);
            }
        }
        //if all this process documents completed, update package status
        updatePackageStatus(history);
        //if all onBoarding document has been approved,send email
        List<OnBoardingProcessApprovalDetails> queryResult = onBoardingProcessApprovalDetailsRepository.findAllByTalentRecruitmentProcessId(history.getTalentRecruitmentProcessId());
        if (CollectionUtil.isNotEmpty(queryResult)) {
            //group histories list by process id
            AtomicReference<Boolean> approvalFlag = new AtomicReference<>(Boolean.TRUE);
            Map<Long, List<OnBoardingProcessApprovalDetails>> groupMap = queryResult.stream()
                    .collect(Collectors.groupingBy(OnBoardingProcessApprovalDetails::getHistoryId, Collectors.toList()));
            for (Long hisId : groupMap.keySet()) {
                List<OnBoardingProcessApprovalDetails> groupList = groupMap.get(hisId);
                Optional<OnBoardingProcessApprovalDetails> details = groupList.stream().sorted(Comparator.comparing(OnBoardingProcessApprovalDetails::getCreatedDate).reversed()).findFirst();
                if (details.isPresent()) {
                    //approval status not in MISSING or REJECTED
                    if (ApprovalStatus.MISSING.equals(details.get().getApprovalStatus())
                            || ApprovalStatus.REJECTED.equals(details.get().getApprovalStatus())) {
                        approvalFlag.set(false);
                    }
                }
            }
            if (approvalFlag.get()) {
                sendAllApprovedDocumentEmail(history.getTalentRecruitmentProcessId());
            }
        }
    }

    private void updatePackageStatus(OnBoardingProcessHistories history) {
        //if all this process documents completed, update package status
        List<OnBoardingProcessHistories> processHistoriesList = onBoardingProcessHistoriesRepository.findAllByProcessIdAndTenantIdAndActivated(history.getProcessId(), SecurityUtils.getTenantId(), Boolean.TRUE);
        if (CollectionUtil.isNotEmpty(processHistoriesList)) {
            Optional<OnBoardingProcessHistories> processHistories = processHistoriesList.stream()
                    .filter(s -> CompletionStatus.INCOMPLETE.equals(s.getCompletionStatus())).findAny();
            if (processHistories.isPresent()) {
                onBoardingProcessHistoriesRepository.saveAll(processHistoriesList.stream().peek(s -> s.setPackageStatus(CompletionStatus.INCOMPLETE)).collect(Collectors.toList()));
            } else {
                onBoardingProcessHistoriesRepository.saveAll(processHistoriesList.stream().peek(s -> s.setPackageStatus(CompletionStatus.COMPLETE)).collect(Collectors.toList()));
            }
        }
    }

    @Override
    public String getDownloadFileName(Long talentRecruitmentProcessId, String timezone) {
        String talentName = onBoardingProcessHistoriesRepository.findTalentNameByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (ObjectUtil.isNull(talentName)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get get download fileName error, can not find talent info .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_DOWNLOAD_FILE_TALENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return "Completed_Doc_" + talentName.replace(StrUtil.SPACE, StrUtil.EMPTY) + StrUtil.UNDERLINE + DateUtil.fromInstantToDateStringBrief(Instant.now(), timezone);
    }

    @Override
    public void remindCandidate(Long talentRecruitmentProcessId) {
        checkPermission(talentRecruitmentProcessId, "[APN: OnBoarding Service @{}] REST request to remind candidate error", "Sorry, you have no permission to remind candidate .");
        //find incomplete documents
        List<String> documentNames = new ArrayList<>();
        List<OnBoardingProcessHistories> historiesList = onBoardingProcessHistoriesRepository.findAllByTalentRecruitmentProcessIdAndTenantIdAndCompletionStatusAndActivated(talentRecruitmentProcessId, SecurityUtils.getTenantId(), CompletionStatus.INCOMPLETE, Boolean.TRUE);
        if (CollectionUtil.isNotEmpty(historiesList)) {
            documentNames = historiesList.stream().map(OnBoardingProcessHistories::getDocumentName).collect(Collectors.toList());
        }
        if (CollectionUtil.isNotEmpty(documentNames)) {
            //send remaind candidate
            sendRemindEmail(Collections.singletonList(getTalentContactByTalentRecruitmentProcessId(talentRecruitmentProcessId).getContact()), documentNames);
        }
    }

    @Override
    public MyOnBoardingPortalsDTO getMyOnBoardingPortals(String timezone) {
        //find application info
        MyOnBoardingPortalsDTO myOnBoardingPortalsDTO = new MyOnBoardingPortalsDTO();
        List<OnBoardingProcessesCompletionsDTO> historiesList = new ArrayList<>();
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUid(SecurityUtils.getUserUid());
        System.out.println(SecurityUtils.getUserIdFromCreatedBy(timeSheetUser.getUid()));
        List<Object[]> portalHeaderObj = onBoardingProcessHistoriesRepository.findByTalentIdAndStatus(SecurityUtils.getUserIdFromCreatedBy(timeSheetUser.getUid()), NodeType.ON_BOARD.toDbValue());
        if (CollectionUtil.isEmpty(portalHeaderObj)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get myOnBoarding portals error, can not find application info .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PORTALS_APPLICATION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        Object[] objects = null;
        for (Object[] obj : portalHeaderObj) {
            Integer status = (Integer) obj[3];
            if (status.equals(0)) {
                objects = obj;
                break;
            } else if (status.equals(10)) {
                objects = obj;
                break;
            } else if (status.equals(5)) {
                objects = obj;
                break;
            }
        }

        if (objects == null) {
            objects = portalHeaderObj.get(0);
        }

        if (null != objects) {
            if (ObjectUtil.isNotNull(objects[0])) {
                myOnBoardingPortalsDTO.setTalentRecruitmentProcessId(Long.parseLong(StrUtil.toString(objects[0])));
                historiesList = getOnBoardingProcessesCompletionsDTOS(Long.parseLong(StrUtil.toString(objects[0])), Boolean.FALSE, timezone);
            }
            if (ObjectUtil.isNotNull(objects[1])) {
                myOnBoardingPortalsDTO.setJobTitle(StrUtil.toString(objects[1]));
            }
            if (ObjectUtil.isNotNull(objects[2])) {
                myOnBoardingPortalsDTO.setStartDate(DateUtil.stringToLocalDate(StrUtil.toString(objects[2])));
            }
        }
        //find onBoarding histories
        if (CollectionUtil.isNotEmpty(historiesList)) {
            myOnBoardingPortalsDTO.setTodos(historiesList.stream().filter(s -> CompletionStatus.INCOMPLETE.equals(s.getCompletionStatus())).collect(Collectors.toList()));
            myOnBoardingPortalsDTO.setCompletions(historiesList.stream().filter(s -> CompletionStatus.COMPLETE.equals(s.getCompletionStatus())).collect(Collectors.toList()));
        }
        return myOnBoardingPortalsDTO;
    }

    public static Image loadImage(String path) {
        Image image = null;
        try {
            ClassPathResource imgFile = new ClassPathResource(path);
            if (imgFile.exists()) {
                byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
                image = Image.getInstance(bytes);
            }
        } catch (Exception e) {
            log.error("error", e);
        }
        return image;
    }

    @Override
    public List<Object> getDownloadCompletionDocuments(Long talentRecruitmentProcessId, List<CompleteDocumentsDataDTO> data) {
        checkPermission(talentRecruitmentProcessId, "[APN: OnBoarding Service @{}] REST request to download completion documents error", "Sorry, you have no permission to download completion documents .");
        if (ObjectUtil.isNull(talentRecruitmentProcessId)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get download completion documents error, application id can not be null .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_DOWNLOAD_COMPLETION_DOCUMENTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (ObjectUtil.isEmpty(talentRecruitmentProcessId)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get download completion documents error, history ids can not be null .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_DOWNLOAD_COMPLETION_DOCUMENTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (CollectionUtil.isEmpty(data)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get download completion documents error, download data can not be null .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_DOWNLOAD_COMPLETION_DOCUMENTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return new ArrayList<>(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void updateOperationStatus(HttpServletRequest request, MyOnBoardingOperationStatusDTO dto) {
        String filledS3Link = null;
        OnBoardingProcessHistories histories = onBoardingProcessHistoriesRepository.getById(dto.getId());
        if (ObjectUtil.isNull(histories)) {
            log.error("[APN: OnBoarding Service @{}] REST request to operate My onBoarding list error, can not find application info .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PORTALS_APPLICATION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //if operationStatus is COMPLETE_AND_SIGNED or UPLOADED, update document info
        if (OperationStatus.COMPLETE_AND_SIGNED.equals(dto.getOperationStatus()) || OperationStatus.UPLOADED.equals(dto.getOperationStatus())) {
            if (Objects.equals(OperationStatus.COMPLETE_AND_SIGNED, dto.getOperationStatus()) && ObjectUtil.isNull(dto.getSignature())) {
                log.error("[APN: OnBoarding Service @{}] REST request to operate My onBoarding list error, if operation status is 'COMPLETE_AND_SIGNED', signature can not be null .", SecurityUtils.getUserId());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_COMPLETE_AND_SIGNED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            }
            if (Objects.equals(OperationStatus.UPLOADED, dto.getOperationStatus()) && ObjectUtil.isNull(dto.getS3Key())) {
                log.error("[APN: OnBoarding Service @{}] REST request to operate My onBoarding list error, if operation status is 'UPLOADED', s3Link can not be null .", SecurityUtils.getUserId());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_UPLOAD.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            }
            if (OperationStatus.COMPLETE_AND_SIGNED.equals(dto.getOperationStatus())) {
                if (!dto.getSignature().getCheck()) {
                    log.error("[APN: OnBoarding Service @{}] REST request to add document signature error, E-signature check false .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_SIGNATURE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
                }
            }
        }

        //if approvalStatus is Deleted, can not do any operation
        //if approvalStatus is Rejected/Accepted,can not do reworked
        //if approvalStatus is Pending, can not do any operation but reworked
        //if approvalStatus is Missing, can not do reworked
        OnBoardingProcessApprovalDetails approvalDetails = onBoardingProcessApprovalDetailsRepository.findOneByHistoryId(dto.getId());
        if (ApprovalStatus.DELETED.equals(approvalDetails.getApprovalStatus())) {
            log.error("[APN: OnBoarding Service @{}] REST request to operate My onBoarding list error, this document has deleted .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_DELETE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if ((ApprovalStatus.REJECTED.equals(approvalDetails.getApprovalStatus()) || ApprovalStatus.ACCEPTED.equals(approvalDetails.getApprovalStatus()))
                && OperationStatus.REWORKED.equals(dto.getOperationStatus())) {
            log.error("[APN: OnBoarding Service @{}] REST request to operate My onBoarding list error, this document has accepted/rejected .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_REJECT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (ApprovalStatus.PENDING.equals(approvalDetails.getApprovalStatus()) && !OperationStatus.REWORKED.equals(dto.getOperationStatus())) {
            log.error("[APN: OnBoarding Service @{}] REST request to operate My onBoarding list error, this document is pending .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_PENDING.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (ApprovalStatus.MISSING.equals(approvalDetails.getApprovalStatus()) && OperationStatus.REWORKED.equals(dto.getOperationStatus())) {
            log.error("[APN: OnBoarding Service @{}] REST request to operate My onBoarding list error, this document is missing .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_MISS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        //update operation status
        if (OperationStatus.READ.equals(dto.getOperationStatus()) || OperationStatus.FIRST_VIEWED.equals(dto.getOperationStatus())) {
            Integer countHistory = onBoardingProcessOperationDetailsRepository.countByHistoryIdAndOperationStatus(dto.getId(), dto.getOperationStatus().toDbValue());
            if (countHistory == 0) {
                saveOperationStatus(request, dto, histories);
            }
        } else {
            saveOperationStatus(request, dto, histories);
        }
        if (OperationStatus.COMPLETE_AND_SIGNED.equals(dto.getOperationStatus()) || OperationStatus.UPLOADED.equals(dto.getOperationStatus())) {
            String s3Key = null;
            if (OperationStatus.COMPLETE_AND_SIGNED.equals(dto.getOperationStatus())) {
                s3Key = histories.getS3Key();
            } else {
                s3Key = dto.getS3Key();
            }
            //update document info
            CloudFileObjectMetadata fileObject = storeService.downloadDocument(CommonUtils.splitS3LinkFromUrl(s3Key), UploadTypeEnum.ONBOARDING.getKey()).getBody();
            if (ObjectUtil.isNull(fileObject)) {
                log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , document s3Link is invalid .", SecurityUtils.getUserId());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_S3LINK_INVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            }
            //add signature
            filledS3Link = addSignature(histories, fileObject, dto);
            if (ObjectUtil.isNull(filledS3Link)) {
                log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , create new s3Link error .", SecurityUtils.getUserId());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_CREATE_S3_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            }
        }
        //update approve status
        if (ObjectUtil.isNotNull(approvalDetails)) {
            saveApprovalStatus(request, dto, approvalDetails);
        }
        //update completion status
        if (OperationStatus.READ.equals(dto.getOperationStatus()) || OperationStatus.REWORKED.equals(dto.getOperationStatus())
                || OperationStatus.FIRST_VIEWED.equals(dto.getOperationStatus())) {
            histories.setCompletionStatus(CompletionStatus.INCOMPLETE);
            if (OperationStatus.REWORKED.equals(dto.getOperationStatus())) {
                //if this document redo/reject and actionRequired is MUST_BE_SIGNED_AND_RETURNED , edit source documents and fill initial info
                histories.setS3Key(fillOneDocuments(SecurityUtils.getUserId(), histories, dto.getTimezone()));
            }
        } else {
            if (OperationStatus.MARK_AS_COMPLETED.equals(dto.getOperationStatus())) {
                histories.setS3Key(histories.getS3Key());
            } else {
                histories.setS3Key(filledS3Link);
            }
            histories.setCompletionStatus(CompletionStatus.COMPLETE);
            //if upload operation, set documentNameUploaded
            if (OperationStatus.UPLOADED.equals(dto.getOperationStatus())) {
                histories.setDocumentNameUploaded(dto.getDocumentName());
            }
            histories.setDocumentName(histories.getDocumentName());
        }
        onBoardingProcessHistoriesRepository.saveAndFlush(histories);
        //set email info
        OnBoardingEmailContentsDTO emailDto = new OnBoardingEmailContentsDTO();
        emailDto.setOperationStatus(dto.getOperationStatus());
        setEmailDTO(histories.getId(), emailDto, dto.getTimezone());
        //if all this process documents completed, update package status
        updatePackageStatus(histories);
        //if all onBoarding documents completed, send email
        List<OnBoardingProcessHistories> historiesList = onBoardingProcessHistoriesRepository.findAllByTalentRecruitmentProcessIdAndTenantIdAndCompletionStatusAndActivated(histories.getTalentRecruitmentProcessId(), SecurityUtils.getTenantId(), CompletionStatus.INCOMPLETE, Boolean.TRUE);
        if (CollectionUtil.isEmpty(historiesList)) {
            //send email
            sendAllCompletedDocumentEmail(histories.getTalentRecruitmentProcessId(), emailDto);
        } else {
            if (OperationStatus.MARK_AS_COMPLETED.equals(dto.getOperationStatus()) || OperationStatus.UPLOADED.equals(dto.getOperationStatus())
                    || OperationStatus.COMPLETE_AND_SIGNED.equals(dto.getOperationStatus())) {
                //send completed document email
                sendCompletedDocumentEmail(histories.getTalentRecruitmentProcessId(), emailDto, dto.getId());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void uploadDocumentByAM(HttpServletRequest request, MyOnBoardingOperationStatusDTO dto) {
        String filledS3Link = null;
        OnBoardingProcessHistories histories = onBoardingProcessHistoriesRepository.getById(dto.getId());
        if (ObjectUtil.isNull(histories)) {
            log.error("[APN: OnBoarding Service @{}] REST request to upload document by AM error, can not find application info .", SecurityUtils.getUserId());
            throw new NotFoundException("can not find application info .", SecurityUtils.getUserId());
        }
        //if operationStatus is COMPLETE_AND_SIGNED or UPLOADED, update document info
        if (OperationStatus.UPLOADED.equals(dto.getOperationStatus())) {
            if (ObjectUtil.isNull(dto.getS3Key())) {
                log.error("[APN: OnBoarding Service @{}] REST request to upload document by AM error, if operation status is 'UPLOADED', s3Key can not be null .", SecurityUtils.getUserId());
                throw new CustomParameterizedException("if operation status is 'UPLOADED', s3Link can not be null .");
            }
        } else {
            log.error("[APN: OnBoarding Service @{}] REST request to upload document by AM error, the operation status is not 'UPLOADED', please check it.", SecurityUtils.getUserId());
            throw new CustomParameterizedException("The operation status is not 'UPLOADED', please check it.");
        }

        //if approvalStatus is Deleted, can not do any operation
        OnBoardingProcessApprovalDetails approvalDetails = onBoardingProcessApprovalDetailsRepository.findOneByHistoryId(dto.getId());
        if (ApprovalStatus.DELETED.equals(approvalDetails.getApprovalStatus())) {
            log.error("[APN: OnBoarding Service @{}] REST request to upload document by AM error, this document has deleted .", SecurityUtils.getUserId());
            throw new CustomParameterizedException("this document has deleted .");
        }

        //update operation status
        saveOperationStatus(request, dto, histories);

        if (OperationStatus.UPLOADED.equals(dto.getOperationStatus())) {
            String s3Key = dto.getS3Key();
            //update document info
            CloudFileObjectMetadata fileObject = storeService.downloadDocument(CommonUtils.splitS3LinkFromUrl(s3Key), UploadTypeEnum.ONBOARDING.getKey()).getBody();
            if (ObjectUtil.isNull(fileObject)) {
                log.error("[APN: OnBoarding Service @{}] REST request to upload document by AM , document s3Key is invalid .", SecurityUtils.getUserId());
                throw new NotFoundException("document s3Link is invalid .", SecurityUtils.getUserId());
            }
            //add signature
            filledS3Link = addSignature(histories, fileObject, dto);
            if (ObjectUtil.isNull(filledS3Link)) {
                log.error("[APN: OnBoarding Service @{}] REST request to upload document by AM , create new s3Link error .", SecurityUtils.getUserId());
                throw new CustomParameterizedException("incorrect PDF file format or items that have not been signed.");
            }
        }
        //update approve status
        if (ObjectUtil.isNotNull(approvalDetails)) {
            OnBoardingProcessApprovalDetails details = new OnBoardingProcessApprovalDetails();
            ServiceUtils.myCopyProperties(approvalDetails, details, new HashSet<>(Arrays.asList("id", "created_by", "created_date", "last_modified_by", "last_modified_date")));
            details.setApprovalStatus(ApprovalStatus.ACCEPTED);
            details.setIp(CommonUtils.getIpAddress(request));
            if (!approvalDetails.getApprovalStatus().equals(details.getApprovalStatus())) {
                onBoardingProcessApprovalDetailsRepository.saveAndFlush(details);
            }
        }
        //update completion status
        histories.setS3Key(filledS3Link);
        histories.setCompletionStatus(CompletionStatus.COMPLETE);
        //if upload operation, set documentNameUploaded
        histories.setDocumentNameUploaded(dto.getDocumentName());
        histories.setDocumentName(histories.getDocumentName());
        onBoardingProcessHistoriesRepository.saveAndFlush(histories);
        //set email info
        OnBoardingEmailContentsDTO emailDto = new OnBoardingEmailContentsDTO();
        emailDto.setOperationStatus(dto.getOperationStatus());
        setEmailDTO(histories.getId(), emailDto, dto.getTimezone());
        //if all this process documents completed, update package status
        updatePackageStatus(histories);
        //if all onBoarding documents completed, send email
        List<OnBoardingProcessHistories> historiesList = onBoardingProcessHistoriesRepository.findAllByTalentRecruitmentProcessIdAndTenantIdAndCompletionStatusAndActivated(histories.getTalentRecruitmentProcessId(), SecurityUtils.getTenantId(), CompletionStatus.INCOMPLETE, Boolean.TRUE);
        if (CollectionUtil.isEmpty(historiesList)) {
            //send email
            sendAllCompletedDocumentEmail(histories.getTalentRecruitmentProcessId(), emailDto);
        } else {
            //send completed document email
            sendCompletedDocumentEmail(histories.getTalentRecruitmentProcessId(), emailDto, dto.getId());
        }
    }

    private void saveApprovalStatus(HttpServletRequest request, MyOnBoardingOperationStatusDTO dto, OnBoardingProcessApprovalDetails approvalDetails) {
        OnBoardingProcessApprovalDetails details = new OnBoardingProcessApprovalDetails();
        ServiceUtils.myCopyProperties(approvalDetails, details, new HashSet<>(Arrays.asList("id", "created_by", "created_date", "last_modified_by", "last_modified_date")));
        if (OperationStatus.REWORKED.equals(dto.getOperationStatus()) || OperationStatus.READ.equals(dto.getOperationStatus())
                || OperationStatus.FIRST_VIEWED.equals(dto.getOperationStatus())) {
            details.setApprovalStatus(ApprovalStatus.MISSING);
        } else if (OperationStatus.MARK_AS_COMPLETED.equals(dto.getOperationStatus())) {
            details.setApprovalStatus(ApprovalStatus.ACCEPTED);
        } else {
            details.setApprovalStatus(ApprovalStatus.PENDING);
        }
        details.setIp(CommonUtils.getIpAddress(request));
        if (!approvalDetails.getApprovalStatus().equals(details.getApprovalStatus())) {
            onBoardingProcessApprovalDetailsRepository.saveAndFlush(details);
        }
    }

    private void saveOperationStatus(HttpServletRequest request, MyOnBoardingOperationStatusDTO dto, OnBoardingProcessHistories histories) {
        OnBoardingProcessOperationDetails save = new OnBoardingProcessOperationDetails();
        save.setTalentRecruitmentProcessId(histories.getTalentRecruitmentProcessId());
        save.setProcessId(histories.getProcessId());
        save.setHistoryId(dto.getId());
        save.setIp(CommonUtils.getIpAddress(request));
        save.setOperationStatus(dto.getOperationStatus());
        onBoardingProcessOperationDetailsRepository.saveAndFlush(save);
    }

    private String addSignature(OnBoardingProcessHistories histories, CloudFileObjectMetadata fileObject, MyOnBoardingOperationStatusDTO dto) {
        //initialize itext
        String s3Key = null;
        FileOutputStream outputStream = null;
        PDDocument document = null;
        List<AcroFields.FieldPosition> signaturePointList = null;
        int signaturePageNo = 0;
        File baseFile = new File(System.getProperty("java.io.tmpdir") + "onBoardingPDF");
        if (!baseFile.exists()) {
            baseFile.mkdir();
        }
        File sourcePdf = null;
        File signatureImage = null;
        File partPdfOutFile = null;
        PdfReader pdfReader = null;
        PdfStamper pdfStamper = null;
        String imagePath = null;

        InputStream byteInputStream = null;
        PDDocument doc = null;
        FileOutputStream fops = null;
        PdfReader reader = null;
        File pdfPageImage = null;
        PdfReader addReader = null;
        PdfStamper stamper = null;

        String addPath = null;
        String destPath = null;
        String signatureImageS3Key = null;
        Document addDocument = null;
        try {
            //1.add signature
            //set encoding
            BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
            Font font = new Font(baseFont, 12, Font.NORMAL);
            //file temple
            pdfReader = new PdfReader(fileObject.getContent());
            //output file path
            String path = baseFile.getAbsolutePath() + File.separator + IdUtil.simpleUUID() + ".pdf";
            sourcePdf = new File(path);
            sourcePdf.createNewFile();
            outputStream = new FileOutputStream(sourcePdf);
            pdfStamper = new PdfStamper(pdfReader, outputStream);
            AcroFields form = pdfStamper.getAcroFields();
            form.addSubstitutionFont(baseFont);
            signaturePointList = form.getFieldPositions(SIGNATURE_TAG);
            if (CollUtil.isEmpty(signaturePointList)) {
                log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , pdf file format is incorrect .", SecurityUtils.getUserId());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_ADD_SIGNATURE_INCORRECT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }

            // 获取所有表单域
            Map<String, AcroFields.Item> fields = form.getFields();
            List<String> checkBoxTagNames = new ArrayList<>();
            List<String> requiredTags = new ArrayList<>();
            List<String> optionalTags = new ArrayList<>();
            for (Map.Entry<String, AcroFields.Item> entry : fields.entrySet()) {
                String fieldName = entry.getKey();
                AcroFields.Item item = entry.getValue();
                // 获取字段的字典对象
                PdfDictionary dict = item.getMerged(0);
                // 判断表单域类型是否为checkbox
                if (dict.get(PdfName.FT).equals(PdfName.BTN)) {
                    //过滤掉不需要处理的checkbox tag
                    if (fieldName.contains(CHECKBOX_TAG_DEFAULT_CONTAINS)) {
                        checkBoxTagNames.add(fieldName);
                    }
                } else {
                    //非checkbox类型的tag
                    if (fieldName.contains(REQUIRED_TAG_PRE)) {
                        requiredTags.add(fieldName);
                    } else if (fieldName.contains(OPTIONAL_TAG_PRE)) {
                        optionalTags.add(fieldName);
                    }
                }
            }

            //find all {{{SIGNATURE}}} tag
            if (OperationStatus.UPLOADED.equals(dto.getOperationStatus())) {
                boolean allSignatureFieldsFilled = true;
                boolean allInitialFieldsFilled = true;
                boolean allRequiredFieldsFilled = true;
                Set<String> fieldNames = form.getFields().keySet();
                for (String fieldName : fieldNames) {
                    //检查signature tag是否填写
                    if (fieldName.equals(SIGNATURE_TAG)) {
                        if (org.apache.commons.lang3.StringUtils.isBlank(form.getField(fieldName))) {
                            allSignatureFieldsFilled = false;
                            break;
                        }
                    }
                    //必填全部tags {{{INITIAL}}}
                    if (fieldName.equals(INITIAL_TAG)) {
                        if (org.apache.commons.lang3.StringUtils.isBlank(form.getField(fieldName))) {
                            allInitialFieldsFilled = false;
                            break;
                        }
                    }
                    //检查必填tags  RequiredTextInput
                    if (requiredTags.contains(fieldName)) {
                        if (org.apache.commons.lang3.StringUtils.isBlank(form.getField(fieldName))) {
                            allRequiredFieldsFilled = false;
                            break;
                        }
                    }
                }
                if (!allSignatureFieldsFilled) {
                    log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , there are items that are not signed .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException("There are items that are not signed .");
                }
                if (!allInitialFieldsFilled) {
                    log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , there are initial tags that are not filled in .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException("There are initial tags that are not filled in .");
                }
                if (!allRequiredFieldsFilled) {
                    log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , there are required tags that are not filled in .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException("There are required tags that are not filled in .");
                }
            }

            signaturePageNo = signaturePointList.get(0).page;
            if (OperationStatus.COMPLETE_AND_SIGNED.equals(dto.getOperationStatus())) {
                //if use e-signatrue
                imagePath = baseFile.getAbsolutePath() + File.separator + IdUtil.simpleUUID() + ".png";
                // if operation status is COMPLETE_AND_SIGNED, put signature image to key area
                if (ObjectUtil.isNotNull(dto.getSignature().getFile())) {
                    //get image path
                    signatureImage = new File(imagePath);
                    ImageIO.write(ImgUtil.toImage(StrUtil.removePrefix(dto.getSignature().getFile(), Constants.BASE64_FILE_PRE)), "png", signatureImage);
                } else if (ObjectUtil.isNotNull(dto.getSignature().getFullName())) {
                    //create talentName signatrue image
                    //使用新的字体GreatVibes-Regular
                    // 使用类加载器获取资源输入流
                    InputStream fontStream = getClass().getClassLoader().getResourceAsStream("Allura-Regular.ttf");
                    java.awt.Font greatVibes = java.awt.Font.createFont(java.awt.Font.TRUETYPE_FONT, fontStream)
                            .deriveFont(java.awt.Font.PLAIN, 50);
                    BufferedImage imgMap = CommonUtils.drawTranslucentStringPic(greatVibes, 700, 50, dto.getSignature().getFullName());
                    ImageIO.write(imgMap, "png", new File(imagePath));
                }
                Image writeSignatureImage = Image.getInstance(imagePath);
                //write all {{{SIGNATURE}}} tag
                PdfContentByte under;
                Rectangle signRect;
                for (AcroFields.FieldPosition sp : signaturePointList) {
                    //put signature image to key area
                    signRect = sp.position;
                    under = pdfStamper.getOverContent(sp.page);
                    //set image size
                    writeSignatureImage.scaleAbsolute(signRect.getWidth(), signRect.getHeight());
                    //set image coordinates
                    writeSignatureImage.setAbsolutePosition(signRect.getLeft(), signRect.getBottom());
                    under.addImage(writeSignatureImage);
                }

                //1.2 特殊处理onboarding tags
                //必填全部tags且一致 {{{INITIAL}}}
                signaturePointList = form.getFieldPositions(INITIAL_TAG);
                if (CollUtil.isNotEmpty(signaturePointList)) {
                    //比较回填tag内容的数量是否匹配
                    List<TagInfoDTO> initialTags = dto.getTagsInfo()
                            .stream().filter(t -> INITIAL_TAG.equals(t.getTag()))
                            .collect(Collectors.toList());
                    if (signaturePointList.size() != initialTags.size()) {
                        log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , the number of initial tags is inconsistent  .", SecurityUtils.getUserId());
                        throw new CustomParameterizedException("The number of initial tags is inconsistent .");
                    }
                    String anyTagValue = initialTags.stream()
                            .filter(t -> ObjectUtil.isNotNull(t.getValue()))
                            .findAny().get().getValue();
                    if (ObjectUtil.isNull(anyTagValue)) {
                        log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , the initial tag is not filled in  .", SecurityUtils.getUserId());
                        throw new CustomParameterizedException("The initial tag is not filled in .");
                    }
                    // 填充标签值, 值相同
                    form.setField(INITIAL_TAG, anyTagValue);
                }

                //遍历所有checkbox
                List<TagInfoDTO> checkboxTags = dto.getTagsInfo()
                        .stream().filter(t -> TAG_TYPE_CHECKBOX.equals(t.getType()))
                        .sorted(Comparator.comparingInt(TagInfoDTO::getOrder))
                        .collect(Collectors.toList());
                if (checkBoxTagNames.size() != checkboxTags.size()) {
                    log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , the number of checkbox tags is inconsistent  .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException("The number of checkbox tags is inconsistent .");
                }
                //回填tags {{{CHECKBOX}}}
                for (int i = 0; i < checkBoxTagNames.size(); i++) {
                    if (checkboxTags.get(i).isCheck()) {
                        //勾选checkbox
                        form.setField(checkBoxTagNames.get(i), CHECKBOX_CHECK_ACTION, true);
                    } else {
                        form.setField(checkBoxTagNames.get(i), CHECKBOX_UNCHECK_ACTION);
                    }
                }

                //回填tags RequiredTextInput
                List<TagInfoDTO> requiredTagInfos = dto.getTagsInfo().stream()
                        .filter(t -> t.getTag().contains(REQUIRED_TAG_PRE))
                        .collect(Collectors.toList());
                if (requiredTags.size() != requiredTagInfos.stream().filter(tag -> ObjectUtil.isNotNull(tag.getValue())).count()) {
                    log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , the number of RequiredTextInput tags is inconsistent  .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException("The number of RequiredTextInput tags is inconsistent .");
                }
                for (TagInfoDTO info : requiredTagInfos) {
                    form.setField(info.getTag(), info.getValue());
                }

                //回填tags OptionalTextInput
                List<TagInfoDTO> optionalTagInfos = dto.getTagsInfo().stream()
                        .filter(t -> t.getTag().contains(OPTIONAL_TAG_PRE))
                        .collect(Collectors.toList());
                if (optionalTags.size() != optionalTagInfos.size()) {
                    log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , the number of OptionalTextInput tags is inconsistent  .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException("The number of OptionalTextInput tags is inconsistent .");
                }
                for (TagInfoDTO info : optionalTagInfos) {
                    form.setField(info.getTag(), info.getValue());
                }
            }

            //1.5  add dateSigned {{{DATESIGNED}}}
            form.setField(DATESIGNED_TAG, StringDateFormat(Instant.now().atZone(ZoneId.of(StrUtil.isNotBlank(dto.getTimezone()) ? dto.getTimezone() : DateUtil.US_LA_TIMEZONE)).toLocalDate().toString(), DATE_FORMAT_MM_DD_YYYY));
            pdfStamper.close();

            //2.get pdf signature page
            document = PDDocument.load(sourcePdf);
            Splitter splitter = new Splitter();
            splitter.setStartPage(signaturePageNo);
            splitter.setEndPage(signaturePageNo);
            List<PDDocument> pages = splitter.split(document);
            ListIterator<PDDocument> iterator = pages.listIterator();
            String partPdfOutPath = baseFile.getAbsolutePath() + File.separator + IdUtil.simpleUUID() + ".pdf";
            partPdfOutFile = new File(partPdfOutPath);
            while (iterator.hasNext()) {
                PDDocument pd = iterator.next();
                if (partPdfOutFile.exists()) {
                    partPdfOutFile.delete();
                }
                pd.save(partPdfOutFile);
                pd.close();
            }
            //2.5 create signature page screenshots
            String pdfImagePath = baseFile.getAbsolutePath() + File.separator + IdUtil.simpleUUID() + ".png";
            FileInputStream instream = new FileInputStream(partPdfOutFile);
            doc = PDDocument.load(instream);
            PDFRenderer renderer = new PDFRenderer(doc);
            BufferedImage image = renderer.renderImageWithDPI(0, 500);
            image.flush();
            ByteArrayOutputStream bs = new ByteArrayOutputStream();
            ImageOutputStream imOut;
            imOut = ImageIO.createImageOutputStream(bs);
            ImageIO.write(image, "png", imOut);
            byteInputStream = new ByteArrayInputStream(bs.toByteArray());
            pdfPageImage = new File(pdfImagePath);
            fops = new FileOutputStream(pdfPageImage);
            fops.write(PdfUtil.readInputStream(byteInputStream));
            fops.flush();

            //3.add last pdf page and set data
            //upload scaleScreenShotPath to s3
            InputStream signatureImageInputStream = new FileInputStream(pdfPageImage);
            MultipartFile signatureImageMultipartFile = new MockMultipartFile(pdfPageImage.getName(), null, Constants.CONTENT_TYPE_PDF, signatureImageInputStream);
            signatureImageS3Key = DigestUtils.md5Hex(signatureImageMultipartFile.getInputStream());
            storeService.uploadDocument(signatureImageMultipartFile, signatureImageS3Key, UploadTypeEnum.ONBOARDING.getKey());
            //save onBoarding signature data
            OnBoardingProcessHistorySignatures signatures = new OnBoardingProcessHistorySignatures();
            signatures.setHistoryId(histories.getId());
            signatures.setMd5Code(SecureUtil.md5(pdfPageImage));
            signaturesRepository.save(signatures);
            //create new document
            reader = new PdfReader(path);
            addPath = baseFile.getAbsolutePath() + File.separator + IdUtil.simpleUUID() + ".pdf";
            addDocument = new Document(reader.getPageSize(reader.getNumberOfPages()));
            PdfWriter.getInstance(addDocument, new FileOutputStream(addPath));

            //create pdf Content
            //set font
            com.itextpdf.text.Font FontChinese24 = new com.itextpdf.text.Font(baseFont, 24, com.itextpdf.text.Font.BOLD);
            com.itextpdf.text.Font FontChinese18 = new com.itextpdf.text.Font(baseFont, 18, com.itextpdf.text.Font.BOLD);
            com.itextpdf.text.Font FontChinese10 = new com.itextpdf.text.Font(baseFont, 10, com.itextpdf.text.Font.NORMAL);
            com.itextpdf.text.Font FontChinese9 = new com.itextpdf.text.Font(baseFont, 9, com.itextpdf.text.Font.ITALIC);
            com.itextpdf.text.Font FontChinese9Normal = new com.itextpdf.text.Font(baseFont, 9, com.itextpdf.text.Font.NORMAL);
            //set color
            BaseColor borderColor = new BaseColor(144, 188, 229);
            BaseColor backgroundColor = new BaseColor(235, 240, 244);

            addDocument.open();

            //load images
            Image lockImage = loadImage("images/pdf_lock.png");
            Image logoImage = loadImage("images/pdf_logo.png");
            Image writeImage = loadImage("images/pdf_write.png");
            Image searchImage = loadImage("images/pdf_search.png");
            Image completeImage = loadImage("images/pdf_complete.png");
            Image scaleImage = Image.getInstance(pdfImagePath);

            //add space
            PdfPTable tableTop = new PdfPTable(1);
            PdfPCell spaceTop = new PdfPCell(new Paragraph(""));
            spaceTop.setBorderColor(borderColor);
            spaceTop.setBorderWidth(2);
            spaceTop.setFixedHeight(143);
            spaceTop.disableBorderSide(2);
            tableTop.addCell(spaceTop);
            addDocument.add(tableTop);

            PdfPTable tableDocumentName = new PdfPTable(1);
            PdfPCell cellDocumentName = new PdfPCell(new Paragraph(histories.getDocumentName(), FontChinese24));
            cellDocumentName.disableBorderSide(3);
            cellDocumentName.setBorderColor(borderColor);
            cellDocumentName.setBorderWidth(2);
            cellDocumentName.setPaddingLeft(10);
            tableDocumentName.addCell(cellDocumentName);
            addDocument.add(tableDocumentName);

            PdfPTable tableCopyright = new PdfPTable(1);
            PdfPCell cellCompanyName = new PdfPCell(new Paragraph(Constants.ACCOUNT_NAME, FontChinese18));
            cellCompanyName.disableBorderSide(3);
            cellCompanyName.setBorderColor(borderColor);
            cellCompanyName.setBorderWidth(2);
            cellCompanyName.setPaddingLeft(10);
            tableCopyright.addCell(cellCompanyName);
            addDocument.add(tableCopyright);

            //add blank
            PdfPTable tableBlank = new PdfPTable(1);
            PdfPCell cellBlankRow = new PdfPCell(new Paragraph(18f, " ", FontChinese18));
            cellBlankRow.disableBorderSide(3);
            cellBlankRow.setBorderColor(borderColor);
            cellBlankRow.setBorderWidth(2);
            tableBlank.addCell(cellBlankRow);
            addDocument.add(tableBlank);
            addDocument.add(tableBlank);

            //add signature
            PdfPTable tableSignature = new PdfPTable(2);
            int[] width11 = {20, 80};
            tableSignature.setWidths(width11);

            //left image
            PdfPCell cellImage1 = new PdfPCell();
            cellImage1.disableBorderSide(11);
            cellImage1.setBorderColor(borderColor);
            cellImage1.setBorderWidth(2);
            cellImage1.addElement(scaleImage);
            cellImage1.setPaddingLeft(10);
            tableSignature.addCell(cellImage1);

            //find document histories
            List<MyOnBoardingHistoryDocumentsDTO> historyDocumentsList = getMyOnBoardingHistoryDocumentsDTOS(histories.getTalentRecruitmentProcessId(), histories.getId(), dto.getTimezone());
            MyOnBoardingHistoryDocumentsDTO info = new MyOnBoardingHistoryDocumentsDTO();
            //get last history info
            if (CollectionUtil.isNotEmpty(historyDocumentsList)) {
                info = historyDocumentsList.get(historyDocumentsList.size() - 1);
            }

            //get talent name
            String talentName = onBoardingProcessHistoriesRepository.findTalentNameByTalentRecruitmentProcessId(histories.getTalentRecruitmentProcessId());

            //right info
            PdfPCell cellSignatureInfo = new PdfPCell();
            cellSignatureInfo.disableBorderSide(7);
            cellSignatureInfo.setBorderColor(borderColor);
            cellSignatureInfo.setBorderWidth(2);

            //space table
            PdfPTable spaceTable = new PdfPTable(1);
            PdfPCell spaceCell = new PdfPCell(new Paragraph(" "));
            spaceCell.setBackgroundColor(backgroundColor);
            spaceCell.disableBorderSide(15);
            spaceTable.addCell(spaceCell);

            PdfPTable assignedOn = new PdfPTable(1);
            PdfPCell cellAssignedOn = new PdfPCell(new Paragraph("Document Assigned on: " + info.getAssignedOnDate(), FontChinese9Normal));
            cellAssignedOn.setBackgroundColor(backgroundColor);
            cellAssignedOn.disableBorderSide(15);
            assignedOn.addCell(cellAssignedOn);

            PdfPTable tableInitiated = new PdfPTable(1);
            PdfPCell cellInitiated = new PdfPCell(new Paragraph("Package Initiated by: " + Constants.ACCOUNT_NAME, FontChinese9Normal));
            cellInitiated.setBackgroundColor(backgroundColor);
            cellInitiated.disableBorderSide(15);
            tableInitiated.addCell(cellInitiated);

            PdfPTable tableDocumentStatus = new PdfPTable(1);
            PdfPCell cellDocumentStatus = new PdfPCell(new Paragraph("Document status: Signed", FontChinese9Normal));
            cellDocumentStatus.setBackgroundColor(backgroundColor);
            cellDocumentStatus.disableBorderSide(15);
            tableDocumentStatus.addCell(cellDocumentStatus);

            PdfPTable tableSignatureId = new PdfPTable(3);
            int[] widthTableSignatureId = {42, 4, 54};
            tableSignatureId.setWidths(widthTableSignatureId);
            PdfPCell cellSignatureId = new PdfPCell(new Paragraph("Signature Transaction ID: ", FontChinese10));
            cellSignatureId.setBackgroundColor(backgroundColor);
            cellSignatureId.disableBorderSide(15);
            tableSignatureId.addCell(cellSignatureId);
            PdfPCell cellSignatureIdImage = new PdfPCell();
            cellSignatureIdImage.disableBorderSide(15);
            lockImage.scaleAbsolute(10, 10);
            cellSignatureIdImage.setBackgroundColor(backgroundColor);
            cellSignatureIdImage.addElement(lockImage);
            cellSignatureIdImage.setPaddingTop(3);
            cellSignatureIdImage.setHorizontalAlignment(Element.ALIGN_CENTER);
            tableSignatureId.addCell(cellSignatureIdImage);
            PdfPCell cellSignatureIdValue = new PdfPCell(new Paragraph(info.getMd5Code(), FontChinese9Normal));
            cellSignatureIdValue.setBackgroundColor(backgroundColor);
            cellSignatureIdValue.disableBorderSide(15);
            tableSignatureId.addCell(cellSignatureIdValue);

            cellSignatureInfo.addElement(spaceTable);
            cellSignatureInfo.addElement(assignedOn);
            cellSignatureInfo.addElement(tableInitiated);
            cellSignatureInfo.addElement(tableDocumentStatus);
            cellSignatureInfo.addElement(tableSignatureId);
            cellSignatureInfo.addElement(spaceTable);
            cellSignatureInfo.addElement(spaceTable);

            tableSignature.addCell(cellSignatureInfo);
            addDocument.add(tableSignature);

            //add blank
            addDocument.add(tableBlank);

            //add document history
            PdfPTable tableDocumentHistoryTitle = new PdfPTable(1);
            PdfPCell cellDocumentHistoryTitle = new PdfPCell(new Paragraph("Document History", FontChinese18));
            cellDocumentHistoryTitle.disableBorderSide(3);
            cellDocumentHistoryTitle.setBorderColor(borderColor);
            cellDocumentHistoryTitle.setBorderWidth(2);
            cellDocumentHistoryTitle.setPaddingLeft(10);
            tableDocumentHistoryTitle.addCell(cellDocumentHistoryTitle);
            addDocument.add(tableDocumentHistoryTitle);

            //add blank
            addDocument.add(tableBlank);

            //add Document History
            PdfPTable tableDocumentHistory = new PdfPTable(1);
            PdfPCell cellHistory = new PdfPCell();
            cellHistory.disableBorderSide(3);
            cellHistory.setBorderColor(borderColor);
            cellHistory.setBorderWidth(2);

            PdfPTable tableHistory = new PdfPTable(2);
            int[] widthDocumentHistory = {5, 45};
            tableHistory.setWidths(widthDocumentHistory);

            PdfPCell cellTableInfo = new PdfPCell();
            PdfPCell cellTableInfoDate = new PdfPCell();
            final Boolean[] firstFlag = {true};
            historyDocumentsList.forEach(t -> {
                PdfPCell cellIcon = new PdfPCell();
                PdfPCell cellInfo = new PdfPCell();
                PdfPTable tableInfoOperation = new PdfPTable(1);
                PdfPTable tableInfoDate = new PdfPTable(1);
                //left image
                cellIcon.disableBorderSide(15);
                cellIcon.setPaddingTop(10);
                cellIcon.setPaddingLeft(-30);
                if (firstFlag[0]) {
                    //set default info
                    completeImage.scaleAbsolute(10, 10);
                    cellIcon.addElement(completeImage);
                } else if (OperationStatus.FIRST_VIEWED.equals(t.getStatus())) {
                    searchImage.scaleAbsolute(10, 10);
                    cellIcon.addElement(searchImage);
                } else if (OperationStatus.COMPLETE_AND_SIGNED.equals(t.getStatus()) || OperationStatus.UPLOADED.equals(t.getStatus())) {
                    writeImage.scaleAbsolute(10, 10);
                    cellIcon.addElement(writeImage);
                }

                //right info
                Paragraph paragraphOperationInfo = null;
                if (firstFlag[0]) {
                    paragraphOperationInfo = new Paragraph("Document assigned by " + Constants.ACCOUNT_NAME, FontChinese10);
                    firstFlag[0] = false;
                } else {
                    if (OperationStatus.COMPLETE_AND_SIGNED.equals(t.getStatus())) {
                        paragraphOperationInfo = new Paragraph("Document signed with Hitalent eSignature by " + talentName, FontChinese10);
                    } else {
                        //if upload document by AM, set username; if upload by talent, set talentName
                        paragraphOperationInfo = new Paragraph("Document " + t.getStatus().getName() + " by " + t.getCreatedByName(), FontChinese10);
                    }
                }
                cellInfo.disableBorderSide(15);
                cellTableInfo.addElement(paragraphOperationInfo);
                cellTableInfo.disableBorderSide(15);
                tableInfoOperation.addCell(cellTableInfo);

                Paragraph paragraphOperationDate = new Paragraph(t.getCreatedDate() + " from IP address " + t.getIp(), FontChinese9);
                cellTableInfoDate.addElement(paragraphOperationDate);
                cellTableInfoDate.disableBorderSide(15);
                tableInfoDate.addCell(cellTableInfoDate);

                cellInfo.addElement(paragraphOperationInfo);
                cellInfo.addElement(paragraphOperationDate);
                cellInfo.setPaddingLeft(-40);
                tableHistory.addCell(cellIcon);
                tableHistory.addCell(cellInfo);
            });
            cellHistory.addElement(tableHistory);
            tableDocumentHistory.addCell(cellHistory);
            addDocument.add(tableDocumentHistory);

            //add space
            PdfPTable tableFoot = new PdfPTable(1);
            PdfPCell spaceFoot = new PdfPCell(new Paragraph(""));
            spaceFoot.setBorderColor(borderColor);
            spaceFoot.setBorderWidth(2);
            spaceFoot.disableBorderSide(3);
            spaceFoot.setFixedHeight(120);//180
            tableFoot.addCell(spaceFoot);
            addDocument.add(tableFoot);

            //add logo
            PdfPTable tableLogo = new PdfPTable(1);
            PdfPCell cellLogo = new PdfPCell();
            cellLogo.disableBorderSide(1);
            cellLogo.setBorderColor(borderColor);
            cellLogo.setBorderWidth(2);
            logoImage.scaleAbsolute(120, 30);
            cellLogo.addElement(logoImage);
            cellLogo.setPaddingLeft(10);
            tableLogo.addCell(cellLogo);
            addDocument.add(tableLogo);

            addDocument.close();

            //cover source pdf
            addReader = new PdfReader(ResourceUtil.getStream(addPath));
            destPath = baseFile.getAbsolutePath() + File.separator + IdUtil.simpleUUID() + ".pdf";
            stamper = new PdfStamper(reader, new FileOutputStream(destPath));
            stamper.insertPage(reader.getNumberOfPages() + 1, reader.getPageSizeWithRotation(1));
            PdfContentByte page1 = stamper.getOverContent(reader.getNumberOfPages());
            PdfImportedPage page = stamper.getImportedPage(addReader, 1);
            page1.addTemplate(page, 0, 0);
            stamper.close();

            //upload filled file to s3
            InputStream inputStream = new FileInputStream(destPath);
            MultipartFile multipartFile = new MockMultipartFile(histories.getDocumentName(), null, Constants.CONTENT_TYPE_PDF, inputStream);
            s3Key = DigestUtils.md5Hex(multipartFile.getInputStream());
            storeService.uploadDocument(multipartFile, s3Key, UploadTypeEnum.ONBOARDING.getKey());
        } catch (Exception e) {
            log.error("onboarding pdf error", e);
            throw new CustomParameterizedException(e.getMessage());
        } finally {
            try {
                if (doc != null) {
                    doc.close();
                }
                if (pdfReader != null) {
                    pdfReader.close();
                }
                if (reader != null) {
                    reader.close();
                }
                if (addReader != null) {
                    addReader.close();
                }
//                if (pdfStamper != null) {
//                    pdfStamper.close();
//                }
                if (stamper != null) {
                    stamper.close();
                }
                if (byteInputStream != null) {
                    byteInputStream.close();
                }
                if (fops != null) {
                    fops.close();
                }
                if (document != null) {
                    document.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException | DocumentException e) {
                log.error("error", e);
                log.error("start onBoarding process error .", e);
            }
            if (signatureImage != null) {
                signatureImage.delete();
            }
            if (imagePath != null) {
                FileUtil.del(imagePath);
            }
            if (addPath != null) {
                FileUtil.del(addPath);
            }
            if (destPath != null) {
                FileUtil.del(destPath);
            }
            //delete files
            if (partPdfOutFile != null) {
                partPdfOutFile.delete();
            }
            if (pdfPageImage != null) {
                pdfPageImage.delete();
            }
            if (sourcePdf != null) {
                sourcePdf.delete();
            }
        }
        return s3Key;
    }

    private static String StringDateFormat(String inputDateStr, String format){
        java.util.Date inputDate = cn.hutool.core.date.DateUtil.parse(inputDateStr);
        // 将日期对象格式化为 "MM/dd/yyyy" 格式的字符串
        return cn.hutool.core.date.DateUtil.format(inputDate, format);
    }

    @Override
    public List<MyOnBoardingPortalsHistoriesDTO> getMyOnBoardingPortalsHistories(String timezone) {
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUid(SecurityUtils.getUserUid());
        checkCompletionStatus(timeSheetUser);
        List<Object[]> queryResult = new ArrayList<>();
        String dataSql = "select ph.id,j.title,c.full_business_name companyName,aol.start_date startDate,ph.talent_recruitment_process_id, ph.document_name, ph.s3_key, d.name,ph.completion_status " +
                ",pod.operation_status, pod.created_date as operation_created_date, pod.created_by as operation_created_by " +
                ", ph.created_date as history_created_date, pod.id podId,ph.document_name_uploaded " +
                "from onboarding_process_histories ph " +
                "left join talent_recruitment_process a on a.id = ph.talent_recruitment_process_id " +
                "left join start aol on ph.talent_recruitment_process_id = aol.talent_recruitment_process_id " +
                "left join job j on j.id = a.job_id " +
                "left join company c on c.id = j.company_id " +
                "left join onboarding_process_operation_details pod on ph.id = pod.history_id " +
                "left join onboarding_documents d on ph.document_id = d.id " +
                "where a.talent_id = ?1 and ph.activated = 1 order by aol.start_date desc ";
        queryResult = entityManager.createNativeQuery(dataSql)
                .setParameter(1, timeSheetUser.getUid()).getResultList();
        List<MyOnBoardingPortalsHistoriesDTO> result = new ArrayList<>();
        List<MyOnBoardingHistoriesDTO> myOnBoardingHistoriesList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(queryResult)) {
            queryResult.forEach(s -> {
                MyOnBoardingHistoriesDTO historiesDTO = new MyOnBoardingHistoriesDTO();
                historiesDTO.setHistoryId(Long.parseLong(StrUtil.toString(s[0])));
                historiesDTO.setJobTitle(StrUtil.toString(s[1]));
                historiesDTO.setCompany(StrUtil.toString(s[2]));
                historiesDTO.setStartDate(DateUtil.stringToLocalDate(StrUtil.toString(s[3])));
                historiesDTO.setCompletionStatus(CompletionStatus.fromDbValue(Integer.parseInt(StrUtil.toString(s[8]))));
                historiesDTO.setDocumentSourceName(StrUtil.toString(s[7]));
                historiesDTO.setAssignedOnDate(DateUtil.stringToInstanctTimezone(StrUtil.toString(s[12]), timezone));
                historiesDTO.setLastOperationBy(attachCreatedUser(StrUtil.toString(s[11])));
                historiesDTO.setLastOperationDate(DateUtil.stringToInstanctTimezone(StrUtil.toString(s[10]), timezone));
                if (ObjectUtil.isNotNull(StrUtil.toString(s[9]))) {
                    historiesDTO.setOperationStatus(OperationStatus.fromDbValue(Integer.parseInt(StrUtil.toString(s[9]))));
                }
                historiesDTO.setS3Key(StrUtil.toString(s[6]));
                historiesDTO.setDocumentName(StrUtil.toString(s[5]));
                historiesDTO.setDetailsId(Long.parseLong(StrUtil.toString(s[13])));
                historiesDTO.setTalentRecruitmentProcessId(Long.parseLong(StrUtil.toString(s[4])));
                if (ObjectUtil.isNotNull(StrUtil.toString(s[14]))) {
                    historiesDTO.setDocumentNameUploaded(StrUtil.toString(s[14]));
                }
                myOnBoardingHistoriesList.add(historiesDTO);
            });
            //group histories list by application id
            Map<Long, List<MyOnBoardingHistoriesDTO>> groupHistoryIdMap = myOnBoardingHistoriesList.stream()
                    .collect(Collectors.groupingBy(MyOnBoardingHistoriesDTO::getHistoryId, Collectors.toList()));
            List<MyOnBoardingHistoriesDTO> groupMapList = new ArrayList<>();
            List<MyOnBoardingHistoriesDTO> groupHistoryList = new ArrayList<>();
            for (Long historyId : groupHistoryIdMap.keySet()) {
                groupHistoryList = groupHistoryIdMap.get(historyId);
                groupHistoryList.sort(Comparator.comparing(MyOnBoardingHistoriesDTO::getDetailsId).reversed());
                groupHistoryList.stream().findFirst().ifPresent(groupMapList::add);
            }
            Map<Long, List<MyOnBoardingHistoriesDTO>> groupTalentRecruitmentProcessIdMap = groupMapList.stream()
                    .collect(Collectors.groupingBy(MyOnBoardingHistoriesDTO::getTalentRecruitmentProcessId, Collectors.toList()));
            for (Long talentRecruitmentProcessId : groupTalentRecruitmentProcessIdMap.keySet()) {
                //format data
                MyOnBoardingPortalsHistoriesDTO dto = new MyOnBoardingPortalsHistoriesDTO();
                List<MyOnBoardingHistoriesDTO> groupList = groupTalentRecruitmentProcessIdMap.get(talentRecruitmentProcessId);
                dto.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
                List<OnBoardingProcessesCompletionsDTO> documents = new ArrayList<>();
                groupList.forEach(historyObject -> {
                    dto.setJobTitle(historyObject.getJobTitle());
                    dto.setCompany(historyObject.getCompany());
                    dto.setStartDate(historyObject.getStartDate());
                    OnBoardingProcessesCompletionsDTO completionsDTO = new OnBoardingProcessesCompletionsDTO();
                    ServiceUtils.myCopyProperties(historyObject, completionsDTO);
                    documents.add(completionsDTO);
                });
                dto.setDocuments(documents);
                result.add(dto);
            }
        }
        return result;
    }

    private void checkCompletionStatus(TimeSheetUser user) {
        List<OnBoardingProcessHistories> historyList = onBoardingProcessHistoriesRepository.findAllHistoriesByTalentId(SecurityUtils.getUserIdFromCreatedBy(user.getUid()), user.getTenantId());
        if (CollectionUtil.isNotEmpty(historyList)) {
            Optional<OnBoardingProcessHistories> history = historyList.stream().filter(s -> CompletionStatus.INCOMPLETE.equals(s.getCompletionStatus())).findAny();
            if (history.isPresent()) {
                throw new CustomParameterizedException("Please login the system again.");
            }
        }
    }

    private String fillOneDocuments(Long talentId, OnBoardingProcessHistories histories, String timeZone) {
        String s3Link = null;
        //download source document file with s3Link
        if (ObjectUtil.isNotNull(histories.getS3KeySource())) {
            CloudFileObjectMetadata fileObject = storeService.downloadDocument(CommonUtils.splitS3LinkFromUrl(histories.getS3KeySource()), UploadTypeEnum.ONBOARDING.getKey()).getBody();
            if (ObjectUtil.isNull(fileObject)) {
                log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , document s3Link is invalid .", SecurityUtils.getUserId());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_S3LINK_INVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            }
            //fill pdf info
            s3Link = FillPDFIInfoUtil.fillPDFInfo(enumCurrencyService, assignmentRepository, commonService, fileObject, talentId, histories.getTalentRecruitmentProcessId(), applicationProperties.getBaseUrl(), timeZone);
        }
        //delete unused pdf TODO
//        log.error("delete " + histories.getS3Link() + " from s3 service");
//        storeService.s3Delete(properties.getStoreService().getOnboardingBucket(), CommonUtils.splitS3LinkFromUrl(histories.getS3Link()));
        //delete unused pdf signature
        signaturesRepository.deleteAllByHistoryId(histories.getId());
        return s3Link;
    }

    @Override
    @Transactional
    public void sendPortalEmail(Long talentId, OnBoardingStartProcessEmailDTO dto) {
        checkPortalPermission(talentId, "[APN: OnBoarding Service @{}] REST request to send portal email error", "Sorry, you have no permission to send portal email .");
        String email = Objects.requireNonNull(Objects.requireNonNull(userService.findById(SecurityUtils.getUserId()).getBody())).getEmail();
        if (ObjectUtil.isNull(email)) {
            log.error("[APN: OnBoarding Service @{}] REST request to send portal email error , can not find user email .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_SEND_PORTAL_EMAIL_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //create talent account and add email content
        String talentEmail = null;
        List<TalentContact> talentContacts = talentService.findAllByTalentIdInAndTypeAndStatus(Collections.singletonList(talentId)).getBody();
        if (CollectionUtil.isNotEmpty(talentContacts)) {
            Optional<TalentContact> talentContact = talentContacts.stream().min(Comparator.comparing(TalentContact::getSort));
            if (talentContact.isPresent()) {
                talentEmail = talentContact.get().getContact();
            }
        }
        String password = RandomUtil.randomString(RandomUtil.BASE_CHAR_NUMBER + RandomUtil.BASE_CHAR.toUpperCase() + RandomUtil.BASE_NUMBER, Constants.GENERATE_PASSWORD_LENGTH);
        String encodePassword = passwordEncoder.encode(password);
        if (ObjectUtil.isNull(talentEmail)) {
            log.error("[APN: OnBoarding Service @{}] REST request to send portal email error , talent email can not be null . ", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_COMMON_TALENT_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //create JobDiva account
        createJobDivaAccount(talentId, talentEmail, encodePassword, SecurityUtils.getTenantId());
        //add email content
        StringBuilder builder = new StringBuilder(dto.getHtmlContents());
        HtmlUtil.appendParagraphCell(builder, "Account：" + talentEmail + ";");
        HtmlUtil.appendParagraphCell(builder, "Temporary Password：" + password);
        MailVM mailVM = new MailVM(email, Collections.singletonList(dto.getTo()), dto.getBcc(), dto.getCc()
                , dto.getSubject(), builder.toString(), dto.getLinks(), false); //TODO
        mailService.sendHtmlMail(mailVM);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetPortalAccount(Long talentId, ResetingPortalAccountDTO dto) {
        checkPortalPermission(talentId, "[APN: OnBoarding Service @{}] REST request to reset portal account error", "Sorry, you have no permission to reset portal account .");
        if (ObjectUtil.isNotNull(dto.getPassword()) && ObjectUtil.isNotNull(dto.getRepeatPassword())) {
            if (!dto.getPassword().equals(dto.getRepeatPassword())) {
                log.error("[APN: OnBoarding Service @{}] REST request to reset portal account error , the entered passwords are inconsistent .", SecurityUtils.getUserId());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_RESET_PORTAL_ACCOUNT_PW.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            }
        }
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUidLikeAndUserType(talentId, TimeSheetUserType.TALENT.toDbValue());
        if (ObjectUtil.isNull(timeSheetUser)) {
            log.error("[APN: OnBoarding Service @{}] REST request to reset portal account error , can not found this talent account .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_RESET_PORTAL_ACCOUNT_NOT_FOUND_ACCOUNT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //if set portal account inactive
        if (dto.isInactive()) {
            timeSheetUser.setActivated(false);
            //delete timeSheet account token
            timesheetUserTokenStore.removeToken(timeSheetUser.getUid());
        } else {
            timeSheetUser.setActivated(true);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(dto.getPassword()) && org.apache.commons.lang3.StringUtils.isNotBlank(dto.getRepeatPassword())) {
                String decryptedPassword = LoginUtil.validatePassword(dto.getPassword(), applicationProperties.getSecret());
                timeSheetUser.setPassword(passwordEncoder.encode(decryptedPassword));
                //delete timeSheet account token
                timesheetUserTokenStore.removeToken(timeSheetUser.getUid());
            }
            //compare emails with timesheet username and update timeSheet account info
            compareTalentEmails(talentId, timeSheetUser);
        }
        timeSheetUserRepository.save(timeSheetUser);
        assignmentSyncToHrService.buildTimeSheetUserSyncToHrMq(timeSheetUser);
    }

    private void compareTalentEmails(Long talentId, TimeSheetUser timeSheetUser) {
        TalentDTOV3 talent = talentService.getTalentWithEntity(talentId).getBody();
        List<TalentContact> talentContactList = talentService.findPrimaryEmailAllByTalentIdInAndTypeAndStatus(Collections.singletonList(talentId)).getBody();
        if (CollUtil.isEmpty(talentContactList)) {
            return;
        }
        Optional<TalentContact> contact = talentContactList.stream().filter(s -> ContactType.EMAIL.equals(s.getType())).min(Comparator.comparing(TalentContact::getSort));
        if (contact.isPresent()) {
            //if primary email changed, update timeSheet account info and send email
            if (!contact.get().getContact().equals(timeSheetUser.getUsername())) {
                //update timeSheet user
                timeSheetUser.setUsername(contact.get().getContact());
                timeSheetUser.setEmail(contact.get().getContact());
                timeSheetUserRepository.save(timeSheetUser);
                //send change timeSheet userName email
                sendRemindAccountEmail(contact.get().getContact(), CommonUtils.formatFullName(talent.getFirstName(), talent.getLastName(), talent.getFullName()));
                //delete timeSheet account token
                timesheetUserTokenStore.removeToken(timeSheetUser.getUid());
            }
        }
    }

    public void sendRemindAccountEmail(String email, String fullName) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                String subject = "Your account has been updated";
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                HtmlUtil.appendParagraphCell(sb, "Hi " + fullName + StrUtil.COMMA);
                HtmlUtil.appendBrTags(sb, 1);
                HtmlUtil.appendParagraphCell(sb, fullName + StrUtil.COMMA + " when there are changes to your Hitalent account access we want to be sure it was you who");
                HtmlUtil.appendParagraphCell(sb, "authorized those changes.");
                HtmlUtil.appendBrTags(sb, 1);
                HtmlUtil.appendParagraphCell(sb, "Our records show account manager recently updated your login account. Your new account is " + email);
                HtmlUtil.appendParagraphCell(sb, "If you have any question, please contact your account manager ");
                HtmlUtil.appendBrTags(sb, 1);
                HtmlUtil.appendParagraphCell(sb, "Thank you");
                sb.append("</body>");
                mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), Collections.singletonList(email), null, null, subject, sb.toString(), null, true));
            }
        });
    }

    @Override
    public ResetingPortalAccountDTO getTalentAccountStatusInfo(Long talentId) {
        ResetingPortalAccountDTO dto = new ResetingPortalAccountDTO();
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUidLikeAndUserType(talentId, TimeSheetUserType.TALENT.toDbValue());
        if (ObjectUtil.isNotNull(timeSheetUser)) {
            dto.setHasAccount(true);
            dto.setInactive(!timeSheetUser.isActivated());
        } else {
            dto.setHasAccount(Boolean.FALSE);
            dto.setInactive(Boolean.TRUE);
        }
        return dto;
    }

    @Override
    public List<TagInfoDTO> getTagsInfo(String s3key) {
        //download source document file with s3Link
        CloudFileObjectMetadata fileObject = storeService.downloadDocument(CommonUtils.splitS3LinkFromUrl(s3key), UploadTypeEnum.ONBOARDING.getKey()).getBody();
        if (ObjectUtil.isNull(fileObject)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get talent jobDiva account status info .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_OPERATE_STATUS_S3LINK_INVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        //get tags info
        return FillPDFIInfoUtil.getTagsInfo(fileObject);
    }

    private void fillDocuments(Long talentId, List<OnBoardingDocuments> documentsList, List<OnBoardingDocuments> fillDocumentsList, Long talentRecruitmentProcessId, String timeZone) {
        CountDownLatch countDownLatch = new CountDownLatch(documentsList.size());
        documentsList.forEach(document -> getExecutorService().execute(new FillDocumentsThreadTask(storeService, countDownLatch, document, fillDocumentsList, talentId, talentRecruitmentProcessId
                , applicationProperties.getBaseUrl(), enumCurrencyService, assignmentRepository, commonService, timeZone)));
        try {
            countDownLatch.await(1, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[fillDocuments] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
        }
    }

    private ExecutorService getExecutorService() {
        if (executorService == null) {
            synchronized (OnBoardingServiceImpl.class) {
                if (executorService == null) {
                    executorService = new ThreadPoolExecutor(
                            applicationProperties.getThreadNum(),
                            applicationProperties.getThreadNum() * 2,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-scheduled-sync-talent-to-es", false));
                }
            }
        }
        return executorService;
    }

    public void sendStartEmail(OnBoardingStartProcessEmailDTO emailDto) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                String emailFrom = Objects.requireNonNull(Objects.requireNonNull(userService.findById(SecurityUtils.getUserId()).getBody())).getEmail();
                MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
                multiValueMap.put("from", CollUtil.newArrayList(emailFrom));
                multiValueMap.add("to", emailDto.getTo());
                multiValueMap.put("cc", CollUtil.newArrayList(emailDto.getCc()));
                multiValueMap.put("bcc", CollUtil.newArrayList(emailDto.getBcc()));
                multiValueMap.put("subject", CollUtil.newArrayList(emailDto.getSubject()));
                multiValueMap.put("html_content", CollUtil.newArrayList(emailDto.getHtmlContents()));
                multiValueMap.put("links", CollUtil.newArrayList(emailDto.getLinks()));
//                if (CollUtil.isNotEmpty(emailDto.getFiles())) {
//                    List<String> nameList = new ArrayList<>();
//                    List<String> fileNameList = new ArrayList<>();
//                    List<String> bufferByteList = new ArrayList<>();
//                    List<String> contentTypeList = new ArrayList<>();
//                    emailDto.getFiles().forEach(f -> {
//                        nameList.add(f.getName());
//                        fileNameList.add(f.getOriginalFilename());
//                        try {
//                            bufferByteList.add(Base64.encodeBase64String(f.getBytes()));
//                        } catch (IOException e) {
//                            log.error("error", e);
//                        }
//                        contentTypeList.add(f.getContentType());
//                    });
//                    multiValueMap.put("name", nameList);
//                    multiValueMap.put("fileName", fileNameList);
//                    multiValueMap.put("bufferByte", bufferByteList);
//                    multiValueMap.put("contentType", contentTypeList);
//                }
                mailService.sendRichMailByFeign(multiValueMap);
            }
        });
    }

    public void sendAllApprovedDocumentEmail(Long talentRecruitmentProcessId) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                List<String> emails = Collections.singletonList(getTalentContactByTalentRecruitmentProcessId(talentRecruitmentProcessId).getContact());
                String subject = "All On-boarding document has been approved";
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                HtmlUtil.appendParagraphCell(sb, "Your all On-boarding document has been approved.");
                HtmlUtil.appendParagraphCell(sb, "Please click <a href=" + applicationProperties.getJobDivaUrl() + "/CandidateTalent/candMyOnboarding" + ">here</a> " + "to view this entry.");
                sb.append("</body>");
                mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, sb.toString(), null, true));
            }
        });
    }

    public void sendAllCompletedDocumentEmail(Long talentRecruitmentProcessId, OnBoardingEmailContentsDTO emailDto) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                String subject = "On-boarding Package for " + emailDto.getTalentName() + " is complete";
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                HtmlUtil.appendParagraphCell(sb, "All On-Boarding Documents requiring return have been uploaded to " + emailDto.getTalentName() + StrUtil.HTML_APOS + "s record.");
                HtmlUtil.appendParagraphCell(sb, "These documents were assigned in regards to Job #" + emailDto.getJobId() + "-" + emailDto.getJobTitle() + ".");
                sb.append("</body>");
                mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), getAMByApplicationId(talentRecruitmentProcessId), null, null, subject, sb.toString(), null, true));
            }
        });
    }

    @Override
    public List<OnBoardingProcessHistories> getPreviewDocuments(OnBoardingStartProcessDTO dto) throws InterruptedException {
        checkPermission(dto.getTalentRecruitmentProcessId(), "[APN: OnBoarding Service @{}] REST request to preview selected documents before start onBoarding process ", "Sorry, you have no permission to start process .");
        if (CollectionUtil.isEmpty(dto.getDocuments())) {
            log.error("[APN: OnBoarding Service @{}] REST request to preview selected documents before start onBoarding process , documents can not be null .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PREVIEW_DOCUMENT_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        TalentRecruitmentProcessVO application = applicationService.getTalentRecruitmentProcessBrief(dto.getTalentRecruitmentProcessId()).getBody();
        if (ObjectUtil.isNull(application)) {
            log.error("[APN: OnBoarding Service @{}] REST request to preview selected documents before start onBoarding process , application id is error  .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PREVIEW_DOCUMENT_APPLICATION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        List<OnBoardingDocuments> documentsList = onBoardingDocumentsRepository.findAllByIdInAndTenantIdAndActivated(dto.getDocuments()
                .stream().map(OnBoardingDocumentsBriefDTO::getId).collect(Collectors.toList()), SecurityUtils.getTenantId(), Boolean.TRUE);
        //edit source documents and fill initial info
        List<OnBoardingDocuments> fillDocumentsList = new ArrayList<>();
        fillDocuments(application.getTalentId(), documentsList, fillDocumentsList, dto.getTalentRecruitmentProcessId(), dto.getTimeZone());
        //if fillDocuments error, throw exception
        if (documentsList.size() != fillDocumentsList.size()) {
            log.error("[APN: OnBoarding Service @{}] REST request to preview selected documents before start onBoarding process error, fill documents error  .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PREVIEW_DOCUMENT_FILE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        List<OnBoardingProcessHistories> historiesList = new ArrayList<>();
        documentsList.forEach(doc -> {
            OnBoardingProcessHistories history = new OnBoardingProcessHistories();
            history.setId(doc.getId());
            //set filled documents s3Link
            for (OnBoardingDocuments f : fillDocumentsList) {
                if (f.getId().equals(doc.getId())) {
                    history.setS3Key(f.getS3Key());
                    break;
                }
            }
            //set ordering
            for (OnBoardingDocumentsBriefDTO d : dto.getDocuments()) {
                if (d.getId().equals(doc.getId())) {
                    history.setOrdering(d.getOrdering());
                    break;
                }
            }
            historiesList.add(history);
        });
        return historiesList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public void startProcessAndSendEmail(HttpServletRequest request, OnBoardingStartProcessDTO dto) throws InterruptedException {
        checkPermission(dto.getTalentRecruitmentProcessId(), "[APN: OnBoarding Service @{}] REST request to start process error", "Sorry, you have no permission to start process .");
        if (CollectionUtil.isEmpty(dto.getDocuments())) {
            log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , documents can not be null .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PREVIEW_DOCUMENT_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        TalentRecruitmentProcessVO application = applicationService.getTalentRecruitmentProcessBrief(dto.getTalentRecruitmentProcessId()).getBody();
        if (ObjectUtil.isNull(application)) {
            log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , application id is error  .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PREVIEW_DOCUMENT_APPLICATION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        //save onBoarding process histories
        List<OnBoardingDocuments> documentsList = onBoardingDocumentsRepository.findAllByIdInAndTenantIdAndActivated(dto.getDocuments()
                .stream().map(OnBoardingDocumentsBriefDTO::getId).collect(Collectors.toList()), SecurityUtils.getTenantId(), Boolean.TRUE);
        //edit source documents and fill initial info
        List<OnBoardingDocuments> fillDocumentsList = new ArrayList<>();

        fillDocuments(application.getTalentId(), documentsList, fillDocumentsList, dto.getTalentRecruitmentProcessId(), dto.getTimeZone());
        //if fillDocuments error, throw exception
        if (documentsList.size() != fillDocumentsList.size()) {
            log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process error, fill documents error  .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PREVIEW_DOCUMENT_FILE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        List<OnBoardingProcessHistories> historiesList = new ArrayList<>();
        String processId = IdUtil.simpleUUID();
        documentsList.forEach(doc -> {
            OnBoardingProcessHistories history = new OnBoardingProcessHistories();
            history.setTalentRecruitmentProcessId(dto.getTalentRecruitmentProcessId());
            history.setProcessId(processId);
            history.setPackageId(dto.getPackageId());
            history.setTenantId(SecurityUtils.getTenantId());
            history.setPackageName(dto.getPackageName());
            history.setActionRequired(doc.getActionRequired());
            history.setDocumentId(doc.getId());
            history.setDocumentName(doc.getName());
            history.setDocumentType(doc.getDocumentType());
            history.setSpecialDocument(doc.getSpecialDocument());
            history.setS3KeySource(doc.getS3Key());
            //set filled documents s3Link
            for (OnBoardingDocuments f : fillDocumentsList) {
                if (f.getId().equals(doc.getId())) {
                    history.setS3Key(f.getS3Key());
                    break;
                }
            }
            //set ordering
            for (OnBoardingDocumentsBriefDTO d : dto.getDocuments()) {
                if (d.getId().equals(doc.getId())) {
                    history.setOrdering(d.getOrdering());
                    history.setOnboardingType(d.getOnboardingType());
                    break;
                }
            }
            historiesList.add(history);
        });
        List<OnBoardingProcessHistories> savedHistoriesList = onBoardingProcessHistoriesRepository.saveAll(historiesList);
        if (CollectionUtil.isNotEmpty(savedHistoriesList)) {
            List<OnBoardingProcessApprovalDetails> approvalDetailsList = new ArrayList<>();
            savedHistoriesList.forEach(s -> {
                //add approved history
                OnBoardingProcessApprovalDetails approvalDetails = new OnBoardingProcessApprovalDetails();
                approvalDetails.setTalentRecruitmentProcessId(s.getTalentRecruitmentProcessId());
                approvalDetails.setIp(CommonUtils.getIpAddress(request));
                approvalDetails.setHistoryId(s.getId());
                approvalDetails.setApprovalStatus(ApprovalStatus.MISSING);
                approvalDetails.setProcessId(s.getProcessId());
                approvalDetailsList.add(approvalDetails);
            });
            onBoardingProcessApprovalDetailsRepository.saveAll(approvalDetailsList);
        }
        //drop history draft
        onBoardingDraftsRepository.deleteAllByTalentRecruitmentProcessIdAndTenantId(dto.getTalentRecruitmentProcessId(), SecurityUtils.getTenantId());

        //create/update JobDiva account and add email contents
        String talentEmail = null;
        String password = RandomUtil.randomString(RandomUtil.BASE_CHAR_NUMBER + RandomUtil.BASE_CHAR.toUpperCase() + RandomUtil.BASE_NUMBER, Constants.GENERATE_PASSWORD_LENGTH);
        String encodePassword = passwordEncoder.encode(password);
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUidLikeAndUserType(application.getTalentId(), TimeSheetUserType.TALENT.toDbValue());
        List<TalentContact> talentContacts = talentService.findAllByTalentIdInAndTypeAndStatus(Collections.singletonList(application.getTalentId())).getBody();
        if (CollectionUtil.isNotEmpty(talentContacts)) {
            Optional<TalentContact> talentContact = talentContacts.stream().min(Comparator.comparing(TalentContact::getSort));
            if (talentContact.isPresent()) {
                talentEmail = talentContact.get().getContact();
            }
        }
        StringBuilder builder = new StringBuilder(dto.getEmail().getHtmlContents());
        if (ObjectUtil.isNotNull(timeSheetUser)) {
            Integer countHistories = onBoardingProcessHistoriesRepository.countHistoriesByTalentRecruitmentProcessId(dto.getTalentRecruitmentProcessId());
            if (countHistories == 0) {
                //cover password
                timeSheetUser.setPassword(encodePassword);
                timeSheetUserRepository.save(timeSheetUser);
                assignmentSyncToHrService.buildTimeSheetUserSyncToHrMq(timeSheetUser);
                HtmlUtil.appendParagraphCell(builder, "Account：" + timeSheetUser.getUsername() + ";" + StrUtil.TAB + "Temporary Password：" + password);
            } else {
                HtmlUtil.appendParagraphCell(builder, "You currently have an account. If you cannot remember your password, please follow the above link and click 'Forgot Password?'");
            }
        } else {
            //create jobDiva account
            createJobDivaAccount(application.getTalentId(), talentEmail, encodePassword, application.getTenantId());
            HtmlUtil.appendParagraphCell(builder, "Account：" + talentEmail + ";" + StrUtil.TAB + "Temporary Password：" + password);
        }
        dto.getEmail().setHtmlContents(builder.toString());
        //send start email
        log.error("[APN: OnBoarding Service @{}] REST request to start onBoarding process , ready to sendStartEmail, emailDto:{} .", SecurityUtils.getUserId(), dto.getEmail());

        sendStartEmail(dto.getEmail());
    }

    public void sendCompletedDocumentEmail(Long talentRecruitmentProcessId, OnBoardingEmailContentsDTO emailDto, Long currentHistoryId) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                //email contents part2
                List<String> missingDocumentsNames = new ArrayList<>();
                String documentName = null;
                if (OperationStatus.UPLOADED.equals(emailDto.getOperationStatus())) {
                    documentName = emailDto.getDocumentNameUploaded();
                } else {
                    documentName = emailDto.getDocumentName();
                }
                String partTwoSql = "select ph.document_name from onboarding_process_histories ph " +
                        "   left join onboarding_process_approval_details pad on ph.id = pad.history_id " +
                        "   where ph.talent_recruitment_process_id = ?1  and ph.tenant_id = ?2 and ph.activated = ?3 and pad.approval_status = ?4 and completion_status = ?5" +
                        "   and ph.id <> ?6 " +
                        "   group by ph.id";
                missingDocumentsNames = entityManager.createNativeQuery(partTwoSql)
                        .setParameter(1, talentRecruitmentProcessId)
                        .setParameter(2, SecurityUtils.getTenantId())
                        .setParameter(3, Boolean.TRUE)
                        .setParameter(4, ApprovalStatus.MISSING.toDbValue())
                        .setParameter(5, CompletionStatus.INCOMPLETE.toDbValue())
                        .setParameter(6, currentHistoryId).getResultList();
                //format email contents
                String subject = "A Document was " + emailDto.getOperationStatus().getName() + " by " + emailDto.getTalentName() + " View";
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                HtmlUtil.appendParagraphCell(sb, "<a href=" + applicationProperties.getBaseUrl() + "/candidates/detail/" + emailDto.getTalentId() + ">" + emailDto.getTalentName() + "</a>" + StrUtil.SPACE + "has" + StrUtil.SPACE + emailDto.getOperationStatus().getName() + StrUtil.SPACE
                        + StrUtil.HTML_QUOTE + documentName + StrUtil.HTML_QUOTE + StrUtil.SPACE + "as" + StrUtil.SPACE + StrUtil.HTML_QUOTE + emailDto.getDocumentName()
                        + StrUtil.HTML_QUOTE + StrUtil.SPACE + "in regards to job" + StrUtil.SPACE + "#" + emailDto.getJobId() + StrUtil.DASHED + emailDto.getJobTitle()
                        + StrUtil.SPACE + "on" + StrUtil.SPACE + emailDto.getCreatedDate() + StrUtil.DOT);
                HtmlUtil.appendParagraphCell(sb, "The following on-boarding documents are still missing:");
                missingDocumentsNames.forEach(name -> {
                    HtmlUtil.appendLiCell(sb, name);
                });
                sb.append("</body>");
                log.info("applicationProperties  = {}", applicationProperties.getBaseUrl());
                log.info("sendCompletedDocumentEmail content = {} ", sb);
                mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), getAMByApplicationId(talentRecruitmentProcessId), null, null, subject, sb.toString(), null, true));
            }
        });
    }

    public void sendRemindEmail(List<String> emails, List<String> documentNames) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                String subject = "On-boarding Document Missing";
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                HtmlUtil.appendParagraphCell(sb, "The following on-boarding documents are still missing:");
                documentNames.forEach(name -> {
                    HtmlUtil.appendLiCell(sb, name);
                });
                sb.append("</body>");
                mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, sb.toString(), null, true));
            }
        });
    }

    public void sendRejectEmail(List<String> emails, String notes, String documentName) {
        EmailUtil.executorService.execute(new CopyTokenChildThread() {
            @Override
            public void runTask() {
                String subject = "On-boarding document has been rejected";
                StringBuilder sb = new StringBuilder();
                sb.append("<body>");
                HtmlUtil.appendParagraphCell(sb, "Your On-boarding document " + documentName + " has been rejected.");
                HtmlUtil.appendParagraphCell(sb, "Comments: " + notes);
                HtmlUtil.appendParagraphCell(sb, "The rejected document and comments are available to view/print on the candidate portal.");
                sb.append("</body>");
                mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, sb.toString(), null, true));
            }
        });
    }

    private List<String> getAMByApplicationId(Long applicationId) {
        List<String> emailsList = new ArrayList<>();
        List<String> commissionAMEmails = onBoardingProcessHistoriesRepository.findCommissionAMEmailByTalentRecruitmentProcessId(applicationId, UserRole.AM.toDbValue());
        List<String> commissionCoAMEmails = onBoardingProcessHistoriesRepository.findCommissionAMEmailByTalentRecruitmentProcessId(applicationId, UserRole.CO_AM.toDbValue());
        List<String> companyAMEmails = onBoardingProcessHistoriesRepository.findCompanyAMEmailByTalentRecruitmentProcessId(applicationId);
        emailsList.addAll(commissionAMEmails);
        emailsList.addAll(companyAMEmails);
        emailsList.addAll(commissionCoAMEmails);
        if (CollectionUtil.isEmpty(emailsList)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get AM user by applicationId error, can not find AM user emails in this application .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_AM_EMAIL_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(applicationId),jobdivaApiPromptProperties.getJobdivaService()));
        }
        return emailsList.stream().distinct().collect(Collectors.toList());
    }

    private String attachCreatedUser(String createdBy) {
        String firstName = null;
        String lastName = null;
        if (createdBy.contains(",")) {
            UserBriefDTO user = userService.findById(Long.parseLong(createdBy.split(",")[0])).getBody();
            if (user != null) {
                firstName = user.getFirstName();
                lastName = user.getLastName();
            }
        } else if (createdBy.contains("_")) {
            TalentDTOV3 talent = talentService.getTalentWithEntity(Long.parseLong(createdBy.split("_")[0])).getBody();
            if (talent != null) {
                firstName = talent.getFirstName();
                lastName = talent.getLastName();
            }
        } else {
            return StrUtil.EMPTY;
        }
        return CommonUtils.formatFullName(firstName, lastName);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public OnBoardingStartProcessEmailDTO getPortalEmailInfo(Long talentId) {
        checkPortalPermission(talentId, "[APN: OnBoarding Service @{}] REST request to get portal email Info error", "Sorry, you have no permission to get portal email Info .");
        List<Object[]> talentObjList = onBoardingProcessHistoriesRepository.findPortalEmailInfoByTalentRecruitmentProcessId(ContactType.EMAIL.toDbValue(), TalentContactStatus.AVAILABLE.toDbValue(), talentId);
        if (CollectionUtil.isEmpty(talentObjList)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get portal email info error, can not find this talent .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_DOWNLOAD_FILE_TALENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        String talentName = null;
        String talentEmail = null;
        Optional<Object[]> talentObj = talentObjList.stream().findAny();
        if (talentObj.isPresent()) {
            talentName = StrUtil.toString(talentObj.get()[0]);
            if (ObjectUtil.isNull(talentObj.get()[3])) {
                log.error("[APN: OnBoarding Service @{}] REST request to get portal email Info error, talent email can not be null  .", SecurityUtils.getUserId());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PORTAL_EMAIL_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            }
            talentEmail = StrUtil.toString(talentObj.get()[3]);
        }
        OnBoardingStartProcessEmailDTO dto = new OnBoardingStartProcessEmailDTO();
        //if first time to create JobDiva account
        getCreateAccountEmailInfo(talentName, talentEmail, dto);
        return dto;
    }

    private void getCreateAccountEmailInfo(String talentName, String talentEmail, OnBoardingStartProcessEmailDTO dto) {
        String loginUrl = applicationProperties.getJobDivaUrl() + "/login";
        StringBuilder contents = new StringBuilder();
        HtmlUtil.appendParagraphCell(contents, "Hi " + talentName + StrUtil.COMMA);
        HtmlUtil.appendBrTags(contents, 1);
        HtmlUtil.appendParagraphCell(contents, "Please connect to the assignment portal via the link below.");
        HtmlUtil.appendParagraphCell(contents, "You can click " + "<a href=\"" + loginUrl + "\">" + "here</a>" + " to login, or copy/paste the following URL in your browser.");
        HtmlUtil.appendParagraphCell(contents, loginUrl);
        HtmlUtil.appendBrTags(contents, 1);
        dto.setHtmlContents(contents.toString());
        dto.setSubject("Onboarding Portal Login Password Setup");
        dto.setTo(talentEmail);
    }

    private void createJobDivaAccount(Long talentId, String talentEmail, String encodePassword, Long tenantId) {
        //if the timeSheet user exists
        TimeSheetUser timeSheetUser = timeSheetUserRepository.findByUsername(talentEmail);
        if (ObjectUtil.isNotNull(timeSheetUser)) {
            log.error("[APN: OnBoarding Service @{}] REST request to create account error , Username already exists . ", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_CREATE_JOBDIVA_ACCOUNT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        TimeSheetUser newTimeSheetUser = new TimeSheetUser();
        newTimeSheetUser.setPassword(encodePassword);
        newTimeSheetUser.setUsername(talentEmail);
        newTimeSheetUser.setEmail(talentEmail);
        newTimeSheetUser.setTenantId(tenantId);
        newTimeSheetUser.setUserType(TimeSheetUserType.TALENT);
        newTimeSheetUser.setPassChanged(false);
        newTimeSheetUser.setUid(talentId + StrUtil.UNDERLINE + tenantId + StrUtil.UNDERLINE + TimeSheetUserType.TALENT.toDbValue());
        newTimeSheetUser.setRoles(roleRepository.findByName(AuthoritiesConstants.ROLE_TALENT));
        timeSheetUserRepository.save(newTimeSheetUser);
        assignmentSyncToHrService.buildTimeSheetUserSyncToHrMq(newTimeSheetUser);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public OnBoardingStartProcessEmailDTO getStartEmailInfo(Long talentRecruitmentProcessId, List<Long> documents) {
        checkPermission(talentRecruitmentProcessId, "[APN: OnBoarding Service @{}] REST request to get start process email Info error", "Sorry, you have no permission to get start process email Info .");
        TalentRecruitmentProcessVO application = applicationService.getTalentRecruitmentProcessBrief(talentRecruitmentProcessId).getBody();
        if (ObjectUtil.isNull(application)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get start onBoarding email info , can not find application info .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_PORTALS_APPLICATION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (CollectionUtil.isEmpty(documents)) {
            log.error("[APN: OnBoarding Service @{}] REST request to get start onBoarding email info , document ids can not be bull .", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_START_EMAIL_INFO_DOCUMENTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }
        String talentName = null;
//        Long jobId = null;
//        Long talentId = null;
//        Long tenantId = null;
        String jobTitle = null;
        String talentEmail = null;
        OnBoardingStartProcessEmailDTO dto = new OnBoardingStartProcessEmailDTO();
        String loginUrl = applicationProperties.getJobDivaUrl() + "/login";
        StringBuilder contents = new StringBuilder();
        HtmlUtil.appendBoldCell(contents, "Please connect to the assignment portal via the link below.");
        HtmlUtil.appendParagraphCell(contents, "You can click " + "<a href=\"" + loginUrl + "\">" + "here</a>" + " to login, or copy/paste the following URL in your browser.");
        HtmlUtil.appendParagraphCell(contents, loginUrl);
        HtmlUtil.appendBrTags(contents, 1);
        //get job & talent info
        List<Object[]> infoObjList = onBoardingProcessHistoriesRepository.findStartEmailInfoByTalentRecruitmentProcessId(ContactType.EMAIL.toDbValue(), TalentContactStatus.AVAILABLE.toDbValue(), application.getTalentId(), talentRecruitmentProcessId);
        if (CollectionUtil.isNotEmpty(infoObjList)) {
            Optional<Object[]> infoObj = infoObjList.stream().findFirst();
            if (infoObj.isPresent()) {
//                jobId = Long.parseLong(StrUtil.toString(infoObj.get()[0]));
                jobTitle = StrUtil.toString(infoObj.get()[1]);
//                tenantId = Long.parseLong(StrUtil.toString(infoObj.get()[2]));
//                talentId = Long.parseLong(StrUtil.toString(infoObj.get()[3]));
                talentName = StrUtil.toString(infoObj.get()[6]);
                if (ObjectUtil.isNull(infoObj.get()[7])) {
                    log.error("[APN: OnBoarding Service @{}] REST request to get start onBoarding email info, talent email can not be null  .", SecurityUtils.getUserId());
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.ONBOARDING_COMMON_TALENT_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
                }
                talentEmail = StrUtil.toString(infoObj.get()[7]);
            }
        }
        String subject = talentName + ": Congratulations and Welcome to Intellipro Group Inc!";
        HtmlUtil.appendBrTags(contents, 1);
        HtmlUtil.appendParagraphCell(contents, "Dear " + talentName + ":");
        HtmlUtil.appendParagraphCell(contents, "The following on-boarding documents are ready for you to fill and sign from your candidate portal for job - " + jobTitle + ":");
        List<String> documentsNames = new ArrayList<>();
        onBoardingDocumentsRepository.findAllByIdInAndTenantIdAndActivated(documents, SecurityUtils.getTenantId(), Boolean.TRUE)
                .stream().map(OnBoardingDocuments::getName).collect(Collectors.toList()).forEach(name -> {
            HtmlUtil.appendLiCell(contents, name);
        });
        //set email info
        dto.setTo(talentEmail);
        if (ObjectUtil.isNotNull(contents)) {
            dto.setHtmlContents(contents.toString());
        }
        dto.setSubject(subject);
        return dto;
    }

    private void checkPermission(Long talentRecruitmentProcessId, String logInfo, String errorMessage) {
        Integer accountManagerIds = onBoardingProcessHistoriesRepository.countCompanyAccountManagerByTalentRecruitmentProcessId(talentRecruitmentProcessId, SecurityUtils.getUserId());
        if (!SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) && !SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.TENANT_ADMIN) && !SecurityUtils.isTimesheetAdmin() && accountManagerIds == 0) {
            log.error(logInfo, SecurityUtils.getUserId());
            throw new CustomParameterizedException(errorMessage);
        }
    }

    private void checkPortalPermission(Long talentId, String logInfo, String errorMessage) {
        Integer accountManagerIds = onBoardingProcessHistoriesRepository.countCompanyAccountManager(talentId, NodeType.ON_BOARD.toDbValue(), SecurityUtils.getUserId());
        if (!SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) && !SecurityUtils.isTimesheetAdmin() && !SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.TENANT_ADMIN) && accountManagerIds == 0) {
            log.error(logInfo, SecurityUtils.getUserId());
            throw new CustomParameterizedException(errorMessage);
        }
    }

    private OnBoardingPackages checkPackagePermission(Long talentRecruitmentProcessId, Optional<OnBoardingPackages> optionalPackage) {
        OnBoardingPackages packages = optionalPackage.get();
        if (SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.ADMIN) || SecurityUtils.isTimesheetAdmin()) {
            return packages;
        } else {
            if (!packages.getTenantId().equals(SecurityUtils.getTenantId())) {
                return null;
            }
            if (SecurityUtils.isCurrentUserInRole(AuthoritiesConstants.TENANT_ADMIN)) {
                return packages;
            }
            Integer countAM = onBoardingProcessHistoriesRepository.countCompanyAccountManagerByTalentRecruitmentProcessId(talentRecruitmentProcessId, SecurityUtils.getUserId());
            if (countAM == 0) {
                return null;
            }
            return packages;
        }
    }

    private void setEmailDTO(Long historyId, OnBoardingEmailContentsDTO emailDto, String timezone) {
        List<Object[]> partOneResult = new ArrayList<>();
        String partOneSql = "select j.id as jobId,j.title,t.id as talentId,t.full_name,ph.document_name,d.name,pod.created_date,ph.document_name_uploaded " +
                " from onboarding_process_histories ph " +
                " left join talent_recruitment_process a on a.id = ph.talent_recruitment_process_id " +
                " left join job j on a.job_id = j.id " +
                " left join talent t on a.talent_id = t.id  " +
                " left join onboarding_documents d on ph.document_id = d.id " +
                " left join (select operation_status,created_date,history_id from onboarding_process_operation_details where history_id = ?1 and operation_status = ?2 order by created_date desc limit 1)pod on ph.id = pod.history_id " +
                " where ph.id = ?1 and ph.tenant_id = ?3 ";
        partOneResult = entityManager.createNativeQuery(partOneSql)
                .setParameter(1, historyId)
                .setParameter(2, emailDto.getOperationStatus().toDbValue())
                .setParameter(3, SecurityUtils.getTenantId()).getResultList();
        log.info("[APN: OnBoarding Service @{}] REST request to get email info, historyId is {}, getOperationStatus is {}, getTenantId is {}", SecurityUtils.getUserId(), historyId, emailDto.getOperationStatus().toDbValue(), SecurityUtils.getTenantId());
        if (CollectionUtil.isNotEmpty(partOneResult)) {
            partOneResult.forEach(s -> {
                //email contents part1
                if (ObjectUtil.isNotNull(s[0])) {
                    emailDto.setJobId(Long.parseLong(StrUtil.toString(s[0])));
                }
                emailDto.setJobTitle(StrUtil.toString(s[1]));
                if (ObjectUtil.isNotNull(s[2])) {
                    emailDto.setTalentId(Long.parseLong(StrUtil.toString(s[2])));
                }
                if (ObjectUtil.isNotNull(s[3])) {
                    emailDto.setTalentName(StrUtil.toString(s[3]));
                }
                emailDto.setDocumentName(StrUtil.toString(s[4]));
                emailDto.setDocumentNameSource(StrUtil.toString(s[5]));
                emailDto.setCreatedDate(DateUtil.fromInstantToDateString(DateUtil.fromStringToInstant(StrUtil.toString(s[6])), timezone));
                if (ObjectUtil.isNotNull(StrUtil.toString(s[7]))) {
                    emailDto.setDocumentNameUploaded(StrUtil.toString(s[7]));
                }
            });
        }
    }

    @Override
    public void downloadCompletions(DownloadCompletionDTO downloadCompletionDTO, HttpServletResponse response) {
        ZipOutputStream zipstream = null;
        try {
            java.time.LocalDate localDate = java.time.Instant.now().atZone(java.time.ZoneId.of(downloadCompletionDTO.getTimeZone())).toLocalDate();
            String downloadName = downloadCompletionDTO.getTalentName().replaceAll(" ", "_") + "_CompletedDocuments_" + localDate + ".zip";
            setHeader(response, downloadName, "application/x-zip-compressed");
            zipstream = new ZipOutputStream(response.getOutputStream());
            // 用于记录每个文件名出现的次数
            Map<String, Integer> fileNameCountMap = new HashMap<>(16);
            for (DownloadFileDTO fileDTO : downloadCompletionDTO.getFileList()) {
                try {
                    CloudFileObjectMetadata cloudFileObjectByInvoice = storeService.getFileFromS3(fileDTO.getS3Key(), UploadTypeEnum.ONBOARDING.getKey()).getBody();
                    if (ObjectUtils.isEmpty(cloudFileObjectByInvoice)) {
                        log.error("[invoice: User @{}] REST request to group invoice file process , document s3Link is invalid .", SecurityUtils.getUserId());
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.S3_DOCUMENT_INVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                    }
                    String originalFileName = fileDTO.getFileName() + ".pdf";
                    String finalFileName = originalFileName;
                    // 检查文件名是否已经存在
                    if (fileNameCountMap.containsKey(originalFileName)) {
                        int count = fileNameCountMap.get(originalFileName);
                        count++;
                        // 生成新的文件名，追加序号
                        finalFileName = fileDTO.getFileName() + "(" + count + ").pdf";
                        fileNameCountMap.put(originalFileName, count);
                    } else {
                        fileNameCountMap.put(originalFileName, 0);
                    }
                    ZipEntry entry = new ZipEntry(finalFileName);
                    zipstream.putNextEntry(entry);
                    zipstream.write(cloudFileObjectByInvoice.getContent());
                    zipstream.flush();
                } catch (Exception e) {
                    log.error("onboarding: download completions error,{}", e);
                }
            }
        } catch (Exception e) {
            log.error("onboarding: download completions,{}", e);
        } finally {
            try {
                if (zipstream != null) {
                    zipstream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public void setHeader(HttpServletResponse response, String fileName,String contentType) throws Exception{
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("charset", "utf-8");
        response.setHeader("Content-Type", contentType);
        response.setHeader("Content-Disposition", "filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
    }

}
