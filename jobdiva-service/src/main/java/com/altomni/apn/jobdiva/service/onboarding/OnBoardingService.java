package com.altomni.apn.jobdiva.service.onboarding;


import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingProcessHistories;
import com.altomni.apn.jobdiva.service.dto.onboarding.*;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackageDocumentsDTO;
import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingPackagesDTO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface OnBoardingService {

    OnBoardingDraftsDTO getDraftByTalentRecruitmentProcessId(Long id);

    void saveDraft(OnBoardingDraftsDTO dto);

    void deleteDraft(Long id);

    List<OnBoardingPackageDocumentsDTO> findAllDocuments(SearchDocumentsDTO dto);

    List<OnBoardingProcessesHistoriesDTO> getHistories(Long id, String timezone);

    List<OnBoardingProcessesCompletionsDTO> getCompletions(Long id, String timezone);

    void saveApproval(HttpServletRequest request, OnBoardingApprovementDTO dto);

    void remindCandidate(Long talentRecruitmentProcessId);

    List<Object> getDownloadCompletionDocuments(Long talentRecruitmentProcessId, List<CompleteDocumentsDataDTO> data);

    String getDownloadFileName(Long talentRecruitmentProcessId, String timezone);

    MyOnBoardingPortalsDTO getMyOnBoardingPortals(String timezone);

    List<MyOnBoardingPortalsHistoriesDTO> getMyOnBoardingPortalsHistories(String timezone);

    void updateOperationStatus(HttpServletRequest request, MyOnBoardingOperationStatusDTO dto);

    void uploadDocumentByAM(HttpServletRequest request, MyOnBoardingOperationStatusDTO dto);

    List<OnBoardingProcessHistories> getPreviewDocuments(OnBoardingStartProcessDTO dto) throws InterruptedException;

    OnBoardingStartProcessEmailDTO getStartEmailInfo(Long talentRecruitmentProcessId, List<Long> documents);

    void startProcessAndSendEmail(HttpServletRequest request, OnBoardingStartProcessDTO dto) throws InterruptedException;

    List<OnBoardingPackagesDTO> getAllPackages(Long talentRecruitmentProcessId);

    List<OnBoardingPackageDocumentsDTO> findDocumentsByPackageId(Long talentRecruitmentProcessId, Long id);

    OnBoardingStartProcessEmailDTO getPortalEmailInfo(Long talentId);

    void sendPortalEmail(Long talentId, OnBoardingStartProcessEmailDTO dto);

    void resetPortalAccount(Long talentId, ResetingPortalAccountDTO dto);

    ResetingPortalAccountDTO getTalentAccountStatusInfo(Long talentId);

    List<TagInfoDTO> getTagsInfo(String s3key);

    void downloadCompletions(DownloadCompletionDTO downloadCompletionDTO, HttpServletResponse response);
}
