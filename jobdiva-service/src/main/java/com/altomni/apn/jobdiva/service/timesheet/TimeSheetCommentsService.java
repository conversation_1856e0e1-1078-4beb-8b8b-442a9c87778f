package com.altomni.apn.jobdiva.service.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetComments;

import java.time.LocalDate;
import java.util.List;

public interface TimeSheetCommentsService {

    TimeSheetComments findByWorkDateAndType(String endDate, CommentsType type, Long assignmentId, Integer recordIndex);

    default TimeSheetComments findByWorkDateAndType(String endDate, CommentsType type, Long assignmentId) {
        return findByWorkDateAndType(endDate, type, assignmentId, 0);
    }

    TimeSheetComments save(String comment, String endingDate, CommentsType type, Long talentId, Long assignmentId, Integer recordIndex);

    default TimeSheetComments save(String comment, String endingDate, CommentsType type, Long talentId, Long assignmentId) {
        return save(comment, endingDate, type, talentId, assignmentId, 0);
    }

    TimeSheetComments findByWorkDateAndType(String endDate, CommentsType type, Long talentId, Long assignmentId, Integer recordIndex);

    default TimeSheetComments findByWorkDateAndType(String endDate, CommentsType type, Long talentId, Long assignmentId) {
        return findByWorkDateAndType(endDate, type, talentId, assignmentId, 0);
    }

    void deleteComment(LocalDate endDate, CommentsType type, Long talentId, Long assignmentId, Integer recordIndex);

    default void deleteComment(LocalDate endDate, CommentsType type, Long talentId, Long assignmentId) {
        deleteComment(endDate, type, talentId, assignmentId, 0);
    }

    List<TimeSheetComments> findByWorkDateAndTypeByAm(String workDate, CommentsType type, Long assignmentId);
}
