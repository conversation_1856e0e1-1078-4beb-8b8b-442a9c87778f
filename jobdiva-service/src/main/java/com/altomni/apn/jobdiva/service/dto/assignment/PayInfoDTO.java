package com.altomni.apn.jobdiva.service.dto.assignment;

import com.altomni.apn.common.domain.enumeration.jobdiva.EmploymentCategoryType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetFrequencyType;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * A record
 */
@ApiModel(description = "PayInfo")
@Data
public class PayInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;
    private Long assignmentId;
    private Boolean isExcept;
    private EmploymentCategoryType employmentCategory ;
    private String  comments;
    private List<AssignmentPayRateInfo> payRateInfo;
    private TimeSheetFrequencyType frequency;
    private String corporation = "";

}
