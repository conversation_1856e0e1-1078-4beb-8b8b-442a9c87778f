package com.altomni.apn.jobdiva.service.dto.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.LinkedList;

/**
 * A record
 */
@ApiModel(description = "TimeSheetInfo")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class TimeSheetInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "approvers")
    private LinkedList<Long> approvers;
    private TimeSheetType timeSheetType ;
    private TimeSheetFrequencyType frequency;
    private WeekEndingType weekEnding;
    private Boolean allowSubmitTimeSheet;
    private Boolean  allowSubmitExpense;
    private String   instructions;

    @Convert(converter = CalculateMethodTypeConverter.class)
    private CalculateMethodType calculateMethodType;

}
