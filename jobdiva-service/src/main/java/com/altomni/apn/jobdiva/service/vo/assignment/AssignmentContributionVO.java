package com.altomni.apn.jobdiva.service.vo.assignment;

import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.enumeration.user.UserRoleConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A record
 */
@Data
public class AssignmentContributionVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long assignmentId;

    private Long  userId;

    @Convert(converter = UserRoleConverter.class)
    private UserRole userRole;

    private BigDecimal percentage;

    private  String firstName;

    private  String lastName;

    private String timezone;

    private String email;

    public AssignmentContributionVO() {
    }

    public AssignmentContributionVO(Long id, Long assignmentId, Long userId, UserRole role, BigDecimal contributionRate, String firstName, String lastName, String timezone, String email) {
        this.id = id;
        this.assignmentId = assignmentId;
        this.userId = userId;
        this.userRole = role;
        this.percentage = contributionRate;
        this.firstName = firstName;
        this.lastName = lastName;
        this.timezone = timezone;
        this.email = email;
    }

}
