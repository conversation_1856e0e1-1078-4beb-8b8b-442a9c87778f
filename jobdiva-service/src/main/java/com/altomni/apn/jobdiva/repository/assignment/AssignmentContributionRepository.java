package com.altomni.apn.jobdiva.repository.assignment;

import com.altomni.apn.jobdiva.domain.assignment.AssignmentContribution;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentContributionVO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface AssignmentContributionRepository extends JpaRepository<AssignmentContribution, Long> {

    void deleteByAssignmentId(Long assignmentId);

    List<AssignmentContribution> findByAssignmentId(Long assignmentId);

    @Query(value = """
    SELECT NEW com.altomni.apn.jobdiva.domain.assignment.AssignmentContribution(ASS.id,ASS.assignmentId,ASS.userId,ASS.userRole,ASS.percentage,U.firstName,U.lastName) from AssignmentContribution ASS left join User U on ASS.userId = U.id where ASS.assignmentId = ?1
    """,nativeQuery = false)
    List<AssignmentContribution> findByAssignmentIdWithName(Long assignmentId); //TODO: remove

    @Query(value = """
    SELECT NEW com.altomni.apn.jobdiva.service.vo.assignment.AssignmentContributionVO(ASS.id,ASS.assignmentId,ASS.userId,ASS.userRole,ASS.percentage,U.firstName,U.lastName,U.customTimezone,U.email)
    from AssignmentContribution ASS left join User U on ASS.userId = U.id
    where ASS.assignmentId = ?1 and U.activated = true
    """)
    List<AssignmentContributionVO> findByAssignmentIdWithNameAndTimezone(Long assignmentId);

    List<AssignmentContribution> findAllByAssignmentIdIn(List<Long> assignmentIds);

}
