package com.altomni.apn.jobdiva.domain.assignment;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.PayRateContentType;
import com.altomni.apn.common.domain.enumeration.jobdiva.PayRateContentTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.PayRateType;
import com.altomni.apn.common.domain.enumeration.jobdiva.PayRateTypeConverter;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A record
 */
@Entity
@Data
@Table(name = "assignment_pay_rate")
public class AssignmentPayRateInfo implements Serializable
{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;


    @Column(name = "assignment_id", nullable = false)
    private Long assignmentId;

    @Column(name = "pay_rate", nullable = false)
    private BigDecimal payRate ;

    @Column(name = "currency", nullable = false)
    private Integer currency;


    @Column(name = "time_unit", nullable = false)
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType timeUnit;

    @Column(name = "pay_type", nullable = false)
    @Convert(converter = PayRateTypeConverter.class)
    private PayRateType type;

    @Column(name = "content_type", nullable = false)
    @Convert(converter = PayRateContentTypeConverter.class)
    private PayRateContentType contentType;



}
