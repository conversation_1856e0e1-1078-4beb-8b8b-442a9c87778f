package com.altomni.apn.jobdiva.service.onboarding;

import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardDocumentResDto;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardPackageResDto;
import com.altomni.apn.jobdiva.service.dto.onboarding.dashboard.DashboardSearchDto;
import org.springframework.data.domain.Page;

/**
 * dashboard service
 * <AUTHOR>
 */
public interface DashboardService {

    /**
     * search documents page data by searchDto and pageable
     * @param searchDto
     * @return
     */
    Page<DashboardDocumentResDto> findDocumentsPageBySearchDtoAndPage(DashboardSearchDto searchDto);

    /**
     * search packages page data by searchDto and pageable
     * @param searchDto
     * @return
     */
    Page<DashboardPackageResDto> findPackagesPageBySearchDtoAndPage(DashboardSearchDto searchDto);

}
