package com.altomni.apn.jobdiva.service.mail;

import com.altomni.apn.common.dto.mail.MailVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "common-service")
public interface MailService {


    @PostMapping("/common/api/v3/campaign/send_html_mail")
    ResponseEntity<Long> sendHtmlMail(@RequestBody MailVM mailVM);

    @PostMapping(value = "/common/api/v3/campaign/send_rich_mail_by_feign")
    ResponseEntity<Void> sendRichMailByFeign(@RequestBody MultiValueMap<String, String> multiValueMap);

}
