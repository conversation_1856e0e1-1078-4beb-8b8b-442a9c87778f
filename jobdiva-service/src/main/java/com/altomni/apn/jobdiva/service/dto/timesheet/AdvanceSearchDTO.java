package com.altomni.apn.jobdiva.service.dto.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetTableOrderType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetTableSortType;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.RecordType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;
import java.util.Set;

@ApiModel
@Data
public class AdvanceSearchDTO {
    private Integer pageSize;
    private Integer pageNum;
    private TimeSheetTableOrderType orderBy;
    private TimeSheetTableSortType order;
    List<SearchConditionDTO> conditions;
    private Set<Long> recordIds;
    private RecordType recordType;
    private String timezone;

}
