package com.altomni.apn.jobdiva.config.idgenerator;

import cn.hutool.core.util.StrUtil;
import org.hibernate.HibernateException;
import org.hibernate.MappingException;
import org.hibernate.engine.spi.SharedSessionContractImplementor;
import org.hibernate.id.IdentifierGenerator;
import org.hibernate.service.ServiceRegistry;
import org.hibernate.type.Type;

import java.io.Serializable;
import java.util.Properties;

/**
 * JPA 雪花算法ID生成器
 */
public class RedisIdGenerator implements IdentifierGenerator {

  private String tableName;

  // 这个方法用于配置生成器（获取参数、初始化资源等）
  @Override
  public void configure(Type type, Properties params, ServiceRegistry serviceRegistry) throws MappingException {
    // 获取传入的参数，这里假设传入的参数是一个 "prefix"
    String table = params.getProperty("table");
    if (StrUtil.isBlank(table)) {
      throw new MappingException("table is blank");
    }
    this.tableName = params.getProperty("table");
  }

  @Override
  public Serializable generate(SharedSessionContractImplementor s, Object obj) throws HibernateException {
    Serializable id = s.getEntityPersister(null, obj).getClassMetadata().getIdentifier(obj, s);
    if (id != null && Long.parseLong(id.toString()) > 0L) {
      return id;
    } else {
      return RedisId.getBean().getMaxId(tableName, ("redis_id" + ":" + tableName).toUpperCase());
    }
  }

}

