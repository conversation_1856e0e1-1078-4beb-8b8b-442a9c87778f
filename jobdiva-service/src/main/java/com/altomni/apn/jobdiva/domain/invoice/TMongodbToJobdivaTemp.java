package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Date;

@Entity
@Data
@Table(name = "t_mongodb_to_jobdiva_temp")
public class TMongodbToJobdivaTemp extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id;

    @Column(name = "assignment_id")
    private BigInteger assignmentId ;

    /**  */
    @Column(name = "invoice_type")
    private Integer invoiceType ;

    /**  */
    @Column(name = "invoice_status")
    private Integer invoiceStatus ;

    /**  */
    @Column(name = "group_invoice_number")
    private String groupInvoiceNumber ;

    /**  */
    @Column(name = "invoice_number")
    private String invoiceNumber ;

    /**  */
    @Column(name = "group_invoice_status")
    private Integer groupInvoiceStatus ;

    /**  */
    @Column(name = "amount")
    private BigDecimal amount ;

    /**  */
    @Column(name = "weed_end_date")
    private LocalDate weedEndDate ;

    /** 0未处理 1已处理 */
    @Column(name = "status")
    private Integer status ;

    @Column(name = "invoice_date")
    private LocalDate invoiceDate ;

    /**  */
    @Column(name = "group_invoice_date")
    private LocalDate groupInvoiceDate ;

    /**  */
    @Column(name = "group_invoice_type")
    private Integer groupInvoiceType ;

    /**  */
    @Column(name = "company_id")
    private BigInteger companyId ;

    /**  */
    @Column(name = "company_name")
    private String companyName ;

    @Column(name = "invoice_id")
    private String invoiceId;

    @Column(name = "invoice_uuid")
    private String invoiceUuid;
}