package com.altomni.apn.jobdiva.service.vo.timesheet;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

/**
 * A record
 */
@ApiModel(description = "record for expense")
@Entity
@Data
public class ExpenseListAmVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelIgnore
    @Id
    private Long  id;

    @ApiModelProperty(value = "work date")
    @ColumnWidth(20)
    @ExcelProperty(value = "Week Ending",index = 0,converter = LocalDateExcelDateConverter.class)
    private LocalDate endingDate;

    @ExcelIgnore
    private LocalDate weekStart;

    @ExcelIgnore
    private LocalDate weekEnd;

    @ApiModelProperty(value = "applied date")
    @ColumnWidth(20)
    @ExcelIgnore
    private Instant appliedDate;

    @ExcelProperty(value = "Submitted Time",index = 9)
    @Transient
    private String appliedDateStr;

    @ApiModelProperty(value = "approved date")
    @ColumnWidth(20)
    @ExcelIgnore
    private Instant approvedDate;

    @ExcelProperty(value = "On",index = 12)
    @Transient
    private String approvedDateStr;

    @ApiModelProperty(value = "total expense")
//    @ColumnWidth(20)
//    @ExcelProperty(value = "Total",index = 4,converter = FloatConverter.class)
    @ExcelIgnore
    private Float amount;

    @ApiModelProperty(value = "total expense format")
    @Transient
    @ColumnWidth(20)
    @ExcelProperty(value = "Total",index = 5)
    private String amountFormat;

    @ExcelIgnore
    private Integer currency;

    @ColumnWidth(20)
    @ExcelProperty(value = "Manager/Approver",index = 10)
    @ApiModelProperty(value = "manager")
    private String manager;

    @ApiModelProperty(value = "talent name")
    @ColumnWidth(20)
    @ExcelProperty(value = "Employee Name",index = 1)
    private String talentName;

    @ApiModelProperty(value = "talent id")
    @ExcelIgnore
    private Long talentId;

    @ApiModelProperty(value = "talentName")
    @ColumnWidth(20)
    @ExcelProperty(value = "Job Title",index = 4)
    private String jobTitle;

    @ApiModelProperty(value = "companyName")
    @ColumnWidth(20)
    @ExcelProperty(value = "Company",index = 3)
    private String companyName;

    @ApiModelProperty(value = "job start date")
    @ExcelIgnore
    private LocalDate startDate;

    @ApiModelProperty(value = "job end date")
    @ExcelIgnore
    private LocalDate endDate;


    @ApiModelProperty(value = "status")
    @ColumnWidth(20)
    @ExcelProperty(value = "Status",index = 11,converter = TimeSheetStatusDateConverter.class)
    @Convert(converter = TimeSheetStatusConverter.class)
    private TimeSheetStatus status;


    @ApiModelProperty(value = "am name")
    @ColumnWidth(20)
    @ExcelProperty(value = "AM",index = 14)
    private String am;

    @ApiModelProperty(value = "am name")
    @ExcelIgnore
    private String amApprover;

    @ExcelIgnore
    private String amIds;

    @ExcelIgnore
    private Long amApproverId;

    @ApiModelProperty(value = "assignment id")
    @ExcelIgnore
    private Long assignmentId;

    @ExcelIgnore
    private String primaryManager;


    @ColumnWidth(20)
    @ExcelProperty(value = "Job Id",index = 13)
    private Long  jobId;

    @ApiModelProperty(value = "expense type")
    @ExcelIgnore
    private ExpenseType expenseType;

    @ExcelIgnore
    private Long tenantId;

    @ColumnWidth(20)
    @Convert(converter = AssignmentCategoryTypeConverter.class)
    @ExcelProperty(value = "Employee Category",index = 2,converter = AssignmentCategoryTypeDataConverter.class)
    private AssignmentCategoryType employmentCategory;

    @ColumnWidth(20)
    @Convert(converter = TimeSheetFrequencyTypeConverter.class)
    @ExcelProperty(value = "Billing Frequency",index = 6,converter = TimeSheetFrequencyTypeDataConverter.class)
    private TimeSheetFrequencyType billingFrequency;

    @ColumnWidth(20)
    @Convert(converter = TimeSheetFrequencyTypeConverter.class)
    @ExcelProperty(value = "Payment Frequency",index = 7,converter = TimeSheetFrequencyTypeDataConverter.class)
    private TimeSheetFrequencyType paymentFrequency;

    @ColumnWidth(20)
    @Convert(converter = AssignmentDivisionConverter.class)
    @ExcelProperty(value = "Assignment Division",index = 8,converter = AssignmentDivisionDataConverter.class)
    private AssignmentDivision assignmentDivision;

    @Transient
    @ExcelIgnore
    private Boolean invoiceFlag = false;

    @ExcelIgnore
    private LocalDate weekEndingDate;

    @ExcelIgnore
    private Integer expenseIndex;

}
