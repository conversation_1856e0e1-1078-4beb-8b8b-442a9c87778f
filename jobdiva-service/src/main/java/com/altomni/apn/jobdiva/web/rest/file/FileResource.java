package com.altomni.apn.jobdiva.web.rest.file;

import com.altomni.apn.common.dto.store.UploadUrlDto;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import com.altomni.apn.jobdiva.service.store.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 *  jobdiva 的账号专用 s3接口，因为timeSheetUser没有 apn 的权限配置，但是跳过权限有不需是 /jobdiva 开头的接口，导致 store的接口无法调用
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class FileResource {

    @Resource
    private StoreService storeService;

    @PostMapping("/s3/store/upload-url")
    public ResponseEntity<StoreGetUploadUrlVO> getPresignedCommonUploadUrlFromS3WithPostPolicy(@RequestBody UploadUrlDto uploadUrlDto) {
        return ResponseEntity.ok(storeService.getPresignedCommonUploadUrlFromS3WithPostPolicy(uploadUrlDto).getBody());
    }

    @GetMapping("/s3/store/url/{uuid}/{uploadType}")
    public ResponseEntity<String> getDisplayUrlFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType) {
        return ResponseEntity.ok(storeService.getDisplayUrlFromS3(uuid, uploadType).getBody());
    }

}
