package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.altomni.apn.jobdiva.service.dto.onboarding.settings.OnBoardingDocumentsBriefDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingStartProcessDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    private Long talentRecruitmentProcessId;

    private Long packageId;

    private String packageName;

    private List<OnBoardingDocumentsBriefDTO> documents;

    private OnBoardingStartProcessEmailDTO email;

    private String timeZone;

}
