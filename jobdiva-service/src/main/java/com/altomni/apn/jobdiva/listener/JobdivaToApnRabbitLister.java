package com.altomni.apn.jobdiva.listener;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.jobdiva.listener.handler.JobdivaToApnHandler;
import com.rabbitmq.client.Channel;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

@RefreshScope
@Slf4j
@Component
@AllArgsConstructor
public class JobdivaToApnRabbitLister {

    private final List<JobdivaToApnHandler> handlerList;

    @Resource
    private PlatformTransactionManager transactionManager;

    @RabbitListener(containerFactory = "jobdivaFactory", queues = {"${application.jobdiva.hr-to-apn-queue}"})
    @RabbitHandler
    public void process(Message message, Channel channel) throws IOException {
        log.info("[APN JobdivaToApnRabbitLister] rabbit Received message: {}，Business data：{}",  message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            String typeStr = jsonObject.getString("type");
            JobdivaDataSyncTypeEnum type = JobdivaDataSyncTypeEnum.valueOf(typeStr);
            JobdivaToApnHandler targetHandler = handlerList.stream().filter(handler -> handler.isSupport(type)).findFirst()
                    .orElseThrow(()-> new CustomParameterizedException("jobdiva to apn is error, Invalid type"));
            targetHandler.execute(jsonObject.getJSONObject("entity"));
            transactionManager.commit(status);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            log.info("jobdiva listener rabbit, is success");
        } catch (Exception e) {
            if (!status.isCompleted()) {
                transactionManager.rollback(status);
            }
            log.error("jobdiva listener rabbit,id:{}, is error, error message：{}", json, e.getMessage());
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
        }
    }

}
