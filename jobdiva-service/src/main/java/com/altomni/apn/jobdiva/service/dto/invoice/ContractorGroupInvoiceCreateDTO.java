package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;

/**
 * used in create group invoice interface
 */
@ApiModel(description = "ContractorGroupInvoiceCreateDTO")
@Data
public class ContractorGroupInvoiceCreateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Timestamp from;

    private Timestamp to;

    private BigInteger companyId;

    private List<BigInteger> employeeIdList;

    private List<InvoiceType> invoiceTypeList;

    private Integer groupInvoiceBy;

    private Integer dueDate;

    private Integer timesheetIncluded;

    private Long tenantId;

    private String timezone;
}
