package com.altomni.apn.jobdiva.web.rest.invoice;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import com.altomni.apn.jobdiva.service.dto.invoice.*;
import com.altomni.apn.jobdiva.service.invoice.ContractorGroupInvoiceService;
import com.altomni.apn.jobdiva.service.invoice.ContractorInvoiceService;
import com.altomni.apn.jobdiva.service.vo.invoice.*;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.util.List;

/**
 * group invoice controller
 *
 * <AUTHOR> zhang.lei
 * @date : 2023-6-14
 * @link : interface document address :https://intelliprogroup.larksuite.com/docx/D3CMdWMJDojvFYx3ytTuzYtJsze
 */
@Api(tags = {"groupInvoiceResource"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ContractorGroupInvoiceResource {

    @Autowired
    ContractorGroupInvoiceService contractorGroupInvoiceService;

    @Autowired
    ContractorInvoiceService contractorInvoiceService;

    @PostMapping("/contractor/group-invoice/search")
    @Timed
    public ResponseEntity<List<ContractorGroupInvoiceListVO>> search(@RequestBody ContractorGroupInvoiceSearchDTO dto, @PageableDefault Pageable pageable) {
        log.info("[group invoice: User @{}] REST search group invoice list:", SecurityUtils.getUserId());
        Page<ContractorGroupInvoiceListVO> contractorGroupInvoiceListVOS = contractorGroupInvoiceService.searchGroupInvoice(dto, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(contractorGroupInvoiceListVOS, "/api/v3/contractor/group-invoice/search");
        return ResponseEntity.ok().headers(headers).body(contractorGroupInvoiceListVOS.getContent());
    }

    @PostMapping("/contractor/group-invoice")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity save(@RequestBody ContractorGroupInvoiceCreateDTO dto) {
        log.info("[group invoice: User @{}] REST create group invoice method:", SecurityUtils.getUserId());
        log.info(" group invoice：create group invoice param:{}", JSON.toJSONString(dto));
        ContractorGroupInvoiceCreateVO contractorGroupInvoiceCreateVO = contractorGroupInvoiceService.save(dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(contractorGroupInvoiceCreateVO);
    }


    @GetMapping("/contractor/group-invoice/failedTimesheet/search")
    @Timed
    public ResponseEntity<List<ContractorInvoiceFailedVO>> searchFailedInfo(ContractorInvoiceCreateDTO dto,
                                                                            Pageable pageable) {
        log.info("[group invoice: User @{}] REST search failed timesheet and expense method:", SecurityUtils.getUserId());
        Page<ContractorInvoiceFailedVO> contractorInvoiceFailedVOPage = contractorInvoiceService.searchFailedTimesheetAndExpenseInfo(dto, pageable,true);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(contractorInvoiceFailedVOPage, "/api/v3/contractor/group-invoice/failedTimesheet/search");
        return ResponseEntity.ok().headers(headers).body(contractorInvoiceFailedVOPage.getContent());
    }

    @PostMapping("/contractor/group-invoice/failedInvoice/download")
    @Timed
    public void getFailedInvoice(@RequestBody ContractorInvoiceCreateDTO dto, HttpServletResponse response) {
        log.info("[group invoice: User @{}] REST download failed timesheet and expense:", SecurityUtils.getUserId());
        log.info(" download failed timesheet param {}:", JSON.toJSONString(dto));
        contractorInvoiceService.downloadGroupFailedInvoice(dto, response);
    }

    @PutMapping("/contractor/group-invoice")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity modify(@RequestBody ContractorGroupInvoiceEditDTO dto) {
        log.info("[group invoice: User @{}] REST group invoice modify method:", SecurityUtils.getUserId());
        if (null == dto.getId() || null == dto.getInvoiceDate()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        contractorGroupInvoiceService.modify(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @GetMapping("/contractor/group-invoice/{id}")
    @Timed
    public ResponseEntity<ContractorGroupInvoiceViewVO> getGroupInvoice(@PathVariable BigInteger id) {
        log.info("[group invoice: User @{}] get group invoice by id:", SecurityUtils.getUserId());
        return ResponseEntity.ok(contractorGroupInvoiceService.view(id));
    }

    @PutMapping("/contractor/group-invoice/ungroup")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity groupInvoiceUngroup(@RequestBody ContractorGroupInvoiceVoidAndPrintDTO dto) {
        log.info("[group invoice: User @{}] REST group invoice ungroup method:", SecurityUtils.getUserId());
        if (null == dto.getInvoiceIdList() || dto.getInvoiceIdList().isEmpty()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        contractorGroupInvoiceService.ungroup(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PutMapping("/contractor/group-invoice/void")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity groupInvoiceVoid(@RequestBody ContractorGroupInvoiceVoidAndPrintDTO dto) {
        log.info("[group invoice: User @{}] REST group invoice void method:", SecurityUtils.getUserId());
        if (null == dto.getInvoiceIdList() || dto.getInvoiceIdList().isEmpty()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        contractorGroupInvoiceService.groupInvoiceVoid(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/contractor/group-invoice/attachmentInfo")
    @Timed
    public ResponseEntity<List<ContractorGroupInvoiceAttachmentVO>> getAttachmentInfo(@RequestBody ContractorGroupInvoiceVoidAndPrintDTO dto) {
        log.info("[group invoice: User @{}] REST get attachmentInfo method, group invoice ids：{}", SecurityUtils.getUserId(), JSON.toJSONString(dto.getInvoiceIdList()));
        log.info(" get attachmentInfo group invoice param:{}", JSON.toJSONString(dto));
        List<ContractorGroupInvoiceAttachmentVO> vo = contractorGroupInvoiceService.getAttachmentList(dto);
        return ResponseEntity.status(HttpStatus.CREATED).body(vo);
    }

    @PostMapping("/contractor/group-invoice/email")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity sendEmail(@RequestBody ContractorGroupInvoiceSendEmailDTO dto) throws Exception{
        log.info("[group invoice: User @{}] REST send mail method {}", SecurityUtils.getUserId());
        log.info("group invoice: send email param :{}", JSON.toJSONString(dto));
        contractorGroupInvoiceService.sendEmail(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @GetMapping("/contractor/group-invoice/upload-url")
    @Timed
    public ResponseEntity<StoreGetUploadUrlVO> getUploadUrl(@RequestParam("uuid") String uuid, @RequestParam("fileName") String fileName){
        log.info("[group invoice: User @{}] REST get group invoice upload url method {}", SecurityUtils.getUserId());
        if (StringUtils.isBlank(uuid)) {
            throw new CustomParameterizedException("UUID cannot be null");
        }
        if (StringUtils.isBlank(fileName)) {
            throw new CustomParameterizedException("FileName cannot be empty");
        }
        return ResponseEntity.ok(contractorGroupInvoiceService.getUploadUrl(uuid,fileName));
    }

    @PostMapping("/contractor/group-invoice/print")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<ContractorGroupInvoicePrintVO> print(@RequestBody ContractorGroupInvoiceVoidAndPrintDTO dto) {
        log.info("[group invoice: User @{}] REST group invoice print method:", SecurityUtils.getUserId());
        if (null == dto.getInvoiceIdList() || dto.getInvoiceIdList().isEmpty()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        return ResponseEntity.ok(contractorGroupInvoiceService.print(dto));
    }

    /**
     * record payment start
     **/
    @PostMapping("/contractor/group-invoice/record-payment")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity recordPayment(@RequestBody RecordPaymentCreateDTO dto) {
        log.info("[group invoice: User @{}] add record payment:", SecurityUtils.getUserId());
        if (null == dto.getGroupInvoiceId() || null == dto.getPaymentAmount()
                || null == dto.getPaymentDate() || null == dto.getPaymentMethod()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        log.info("group invoice:  add record payment param {}:", JSON.toJSONString(dto));
        contractorGroupInvoiceService.addRecordPayment(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/contractor/group-invoice/bulkRecordPayment")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity bulkRecordPayment(@RequestBody BulkRecordPaymentCreateDTO dto) {
        log.info("[group invoice: User @{}] bulk record payment:", SecurityUtils.getUserId());
        if (null == dto.getInvoiceIdList() || dto.getInvoiceIdList().size() == 0
                || null == dto.getPaymentDate() || null == dto.getPaymentMethod()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        log.info("group invoice: bulk record payment param {}:", JSON.toJSONString(dto));
        contractorGroupInvoiceService.bulkRecordPayment(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @GetMapping("/contractor/group-invoice/{groupInvoiceId}/payments")
    @Timed
    public ResponseEntity<List<PaymentRecordListVO>> getPaymentList(@PathVariable BigInteger groupInvoiceId) {
        log.info("[group invoice: User @{}] get payment by Id{}:", SecurityUtils.getUserId(), groupInvoiceId);
        List<PaymentRecordListVO> vo = contractorGroupInvoiceService.getPaymentRecordList(groupInvoiceId);
        return ResponseEntity.ok().body(vo);
    }

    @PutMapping("/contractor/group-invoice/unreocrd-payment/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity unRecordPayment(@PathVariable BigInteger id) {
        log.info("[group invoice: User @{}] REST unrecord payment method:", SecurityUtils.getUserId());
        if (null == id) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        contractorGroupInvoiceService.unRecordPayment(id);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PutMapping("/contractor/group-invoice/sendToClient")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity sendToClient(@RequestBody ContractorGroupInvoiceSendEmailDTO dto) throws Exception{
        log.info("[group invoice: User @{}] REST send to client method {}", SecurityUtils.getUserId());
        log.info("group invoice: send to client param :{}", JSON.toJSONString(dto));
        if (null == dto.getSentToClient() || dto.getInvoiceIdList().isEmpty()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        contractorGroupInvoiceService.sendToClient(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }


    /**
     * 定时任务执行更新group invoice status
     * @return
     */
    @PostMapping("/contractor/group-invoice/xxl-job/update-group-invoice-status")
    @Timed
    public ResponseEntity<String> updateGroupInvoiceStatus() {
        log.info("[group invoice: User @{}] xxl-job update group invoice status :", SecurityUtils.getUserId());
        return ResponseEntity.ok(contractorGroupInvoiceService.updateGroupInvoiceStatus());
    }

    /**
     * 定时任务执行 查询未生成的file 发送到mq
     * @return
     */
    @PostMapping("/contractor/group-invoice/xxl-job/search_file_does_not_exist")
    @Timed
    public ResponseEntity<String> searchFileDoesNotExist() {
        log.info("[Group Invoice file is null: User @{}] xxl-job search file does not exist :", SecurityUtils.getUserId());
        return ResponseEntity.ok(contractorGroupInvoiceService.searchFileDoesNotExist());
    }

    @PostMapping("/contractor/group-invoice/dataMigrateGroupInvoice")
    @Timed
    public ResponseEntity dataMigrateGroupInvoice(){
        contractorGroupInvoiceService.dataMigrateGroupInvoice();
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * record purchase order payment start
     **/
    @PostMapping("/contractor/group-invoice/purchase-record-payment")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity purchaseRecordPayment(@RequestBody PurchaseOrderRecordPaymentCreateDTO dto) {
        log.info("[group invoice: User @{}] add purchase order record payment:", SecurityUtils.getUserId());
        if (null == dto.getGroupInvoiceIdList() || null == dto.getPaymentAmount()
                || null == dto.getPaymentMethod()
                || dto.getPurchaseOrderList().isEmpty()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        log.info("group invoice:  add purchase order record payment param {}:", JSON.toJSONString(dto));
        contractorGroupInvoiceService.addPurchaseOrderRecordPayment(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PostMapping("/contractor/group-invoice/init-payment-detail")
    @Timed
    public ResponseEntity initPaymentDetail(){
        contractorGroupInvoiceService.initPaymentDetail();
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}