package com.altomni.apn.jobdiva.service.dto.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.SearchConfigType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * A record
 */
@ApiModel(description = "save filter")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class SearchFilterDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "config ")
    private String config;

    @ApiModelProperty(value = "common filter or advance filter ")
    private SearchConfigType filterType;


    @ApiModelProperty(value = "")
    private SearchConfigType searchType;

    @ApiModelProperty(value = "filter template name")
    private  String name;

}
