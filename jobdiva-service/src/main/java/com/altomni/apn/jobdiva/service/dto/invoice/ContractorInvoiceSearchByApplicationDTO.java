package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.GroupInvoiceStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceStatusType;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ContractorInvoiceSearchByApplicationDTO {

    private String invoiceNumber;

    private String groupNumber;

    private String fullName;

    private Long talentId;

    private LocalDateTime invoiceStartTime;

    private LocalDateTime invoiceEndTime;

    private Instant createdStartTime;

    private Instant createdEndTime;

    private Long companyId;

    private String companyName;

    private Long companyContactId;

    private String companyContactName;

    private List<InvoiceStatusType> invoiceStatusList;

    private List<InvoiceType> invoiceTypeList;

    private List<AssignmentDivision> assignmentDivisionList;

    private List<GroupInvoiceStatus> groupInvoiceStatusList;

    private Integer size;

    private Integer page;

    private SearchSortDTO sort;

    private Long userId;

    private Long primaryTeamId;

}
