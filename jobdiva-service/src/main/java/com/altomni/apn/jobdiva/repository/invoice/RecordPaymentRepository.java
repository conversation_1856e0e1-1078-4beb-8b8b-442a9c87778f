package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.TRecordPaymentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.List;

@Repository
public interface RecordPaymentRepository extends JpaRepository<TRecordPaymentInfo,BigInteger> {

    /**
     * 根据group invoice id and status查询数据
     * @param groupInvoiceId
     * @param status
     * @return
     */
    List<TRecordPaymentInfo> findByGroupInvoiceIdAndStatus(BigInteger groupInvoiceId,Integer status);

    /**
     * 根据group invoice id and status查询数据
     * @param groupInvoiceId
     * @param status
     * @return
     */
    List<TRecordPaymentInfo> findByGroupInvoiceIdInAndStatus(List<BigInteger> groupInvoiceId,Integer status);

    @Modifying
    @Transactional
    @Query(value = "update  t_record_payment_info  set status=0,last_modified_date=now() where group_invoice_id in ?1 ",nativeQuery = true)
    void updateStatusByIds(List<BigInteger> groupInvoiceIdList);
}
