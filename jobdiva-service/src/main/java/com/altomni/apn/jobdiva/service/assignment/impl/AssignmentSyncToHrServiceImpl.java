package com.altomni.apn.jobdiva.service.assignment.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.jobdiva.CommentsType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentBillInfo;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayInfo;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentPayRateInfo;
import com.altomni.apn.jobdiva.domain.assignment.AssignmentTimeSheet;
import com.altomni.apn.jobdiva.domain.timesheet.*;
import com.altomni.apn.jobdiva.repository.assignment.BillInfoRepository;
import com.altomni.apn.jobdiva.repository.assignment.PayInfoRepository;
import com.altomni.apn.jobdiva.repository.assignment.PayRateRepository;
import com.altomni.apn.jobdiva.repository.timesheet.*;
import com.altomni.apn.jobdiva.service.assignment.AssignmentSyncToHrService;
import com.altomni.apn.jobdiva.service.rabbitmq.JobdivaToHrRabbitService;
import com.altomni.apn.jobdiva.service.talent.TalentService;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentExtraHrVO;
import com.altomni.apn.jobdiva.service.vo.assignment.TalentAssigmentVO;
import com.altomni.apn.jobdiva.service.vo.timesheet.TalentToHrVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Service
public class AssignmentSyncToHrServiceImpl implements AssignmentSyncToHrService {

    @Resource
    private TalentService talentService;

    @Resource
    private BillInfoRepository billInfoRepository;

    @Resource
    private TimeSheetRepository timeSheetRepository;

    @Resource
    private PayRateRepository payRateRepository;

    @Resource
    private PayInfoRepository payInfoRepository;

    @Resource
    private TalentAssignmentRepository talentAssignmentRepository;

    @Resource
    private TimeSheetRecordRepository timeSheetRecordRepository;

    @Resource
    private ExpenseRecordRepository expenseRecordRepository;

    @Resource
    private TimeSheetCommentsRepository commentsRepository;

    @Resource
    private ApproveRecordRepository approveRecordRepository;

    @Resource
    private TimeSheetBreakTimeRepository breakTimeRepository;

    @Resource
    private TimeSheetManagerRepository timeSheetManagerRepository;

    @Resource
    private TimeSheetHolidayRecordRepository holidayRecordRepository;

    @Resource
    private JobdivaToHrRabbitService jobdivaToHrRabbitService;

    @Resource
    private EntityManager entityManager;

    private final String ASSIGNMENT = "ASSIGNMENT";

    private final String ASSIGNMENT_IDS = "ASSIGNMENT_IDS";

    private final String ASSIGNMENT_TIMESHEET = "ASSIGNMENT_TIMESHEET";

    private final String ASSIGNMENT_PAY_INFO = "ASSIGNMENT_PAY_INFO";

    private final String ASSIGNMENT_PAY_RATE = "ASSIGNMENT_PAY_RATE";

    private final String ASSIGNMENT_BILL_INFO = "ASSIGNMENT_BILL_INFO";

    private final String ASSIGNMENT_TIMESHEET_MANAGER = "ASSIGNMENT_TIMESHEET_MANAGER";

    private final String TALENT = "TALENT";

    private final String HOLIDAY = "HOLIDAY";

    private final String TIMESHEET = "TIMESHEET";

    private final String TIMESHEET_BREAKTIME = "TIMESHEET_BREAKTIME";

    private final String TIMESHEET_EXPENSE = "TIMESHEET_EXPENSE";

    private final String TIMESHEET_APPROVE = "TIMESHEET_APPROVE";

    private final String TIMESHEET_COMMENTS = "TIMESHEET_COMMENTS";

    /**
     * 构建json 数据同步至hr
     * @param filedList
     *
     *  需要同步的数据
     *  timesheet_talent_assignment
     *  assignment_timesheet
     *  assignment_pay_info
     *  assignment_bill_info
     *  timesheet_manager
     *
     *  time_sheet_record
     *  timesheet_breaktime_record
     *  timesheet_expense_record
     *  timesheet_approve_record
     *  timesheet_comments
     *
     *  time_sheet_holiday_record
     *
     */
    @Override
    public void buildAssignmentSyncToHrMq(List<String> filedList, Long assignmentId) {
        entityManager.clear();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", JobdivaDataSyncTypeEnum.ASSIGNMENT);
        //assignment 需要拓展数据 companyName, jobTitle
        JSONObject entityObject = new JSONObject();
        //timesheet_talent_assignment
        TalentAssigment talentAssigment = talentAssignmentRepository.findById(assignmentId).orElseThrow();
        AssignmentExtraHrVO assignmentExtraHrVO = talentAssignmentRepository.findAssignmentExtraHrById(assignmentId);
        TalentAssigmentVO talentAssigmentVO = new TalentAssigmentVO();
        BeanUtil.copyProperties(talentAssigment, talentAssigmentVO);
        talentAssigmentVO.setCompanyName(assignmentExtraHrVO.getCompanyName());
        talentAssigmentVO.setJobTitle(assignmentExtraHrVO.getJobTitle());
        entityObject.put(ASSIGNMENT, talentAssigmentVO);
        //talent
        if (filedList.contains(TALENT)) {
            TalentToHrVO talent = talentService.getTalentDataSyncToHr(talentAssigment.getTalentId()).getBody();
            entityObject.put(TALENT, talent);
        }
        //assignment_timesheet
        AssignmentTimeSheet assignmentTimeSheet = timeSheetRepository.findByAssignmentId(assignmentId);
        entityObject.put(ASSIGNMENT_TIMESHEET, assignmentTimeSheet);
        //assignment_pay_rate
        List<AssignmentPayRateInfo> payRateInfoList = payRateRepository.findAllByAssignmentId(assignmentId);
        entityObject.put(ASSIGNMENT_PAY_RATE, payRateInfoList);
        //assignment_pay_info
        AssignmentPayInfo assignmentPayInfo = payInfoRepository.findByAssignmentId(assignmentId);
        entityObject.put(ASSIGNMENT_PAY_INFO, assignmentPayInfo);
        //assignment_bill_info
        AssignmentBillInfo assignmentBillInfo = billInfoRepository.findByAssignmentId(assignmentId);
        entityObject.put(ASSIGNMENT_BILL_INFO, assignmentBillInfo);
        //timesheet_manager
        List<TimeSheetManager> timeSheetManagerList = timeSheetManagerRepository.findByAssignmentId(assignmentId);
        entityObject.put(ASSIGNMENT_TIMESHEET_MANAGER, timeSheetManagerList);

        //holiday
        if (filedList.contains(HOLIDAY)) {
            List<TimeSheetHolidayRecord> holidayRecordList = holidayRecordRepository.findAllByAssignmentId(assignmentId);
            entityObject.put(HOLIDAY, holidayRecordList);
        }

        //timesheet
        if (filedList.contains(TIMESHEET)) {
            List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findAllByAssignmentId(assignmentId);
            entityObject.put(TIMESHEET, timeSheetRecordList);
            List<TimeSheetBreakTimeRecord> breakTimeRecordList = breakTimeRepository.findAllByAssignmentId(assignmentId);
            entityObject.put(TIMESHEET_BREAKTIME, breakTimeRecordList);
            List<TimeSheetComments> commentsList = commentsRepository.findAllByAssignmentId(assignmentId);
            entityObject.put(TIMESHEET_COMMENTS, commentsList);
            List<ApproveRecord> approveRecordList = approveRecordRepository.findAllByAssignmentId(assignmentId);
            entityObject.put(TIMESHEET_APPROVE, approveRecordList);
            List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByAssignmentId(assignmentId);
            entityObject.put(TIMESHEET_EXPENSE, expenseRecordList);
        }

        jsonObject.put("entity", entityObject);
        jobdivaToHrRabbitService.sendDataToHr(jsonObject);
    }

    @Override
    public void buildAssignmentListSyncToHrMq(List<Long> assignmentIdList) {
        entityManager.clear();
        List<String> fieldList = new ArrayList<>();
        fieldList.add(TALENT);
        fieldList.add(HOLIDAY);
        fieldList.add(TIMESHEET);
        for (Long assignmentId : assignmentIdList) {
            buildAssignmentSyncToHrMq(fieldList, assignmentId);
        }
    }

    @Override
    public void deleteAssignment(List<Long> assignmentId) {
        //审批的是，会涉及到 对应的打卡或者报销信息的变动
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", JobdivaDataSyncTypeEnum.ASSIGNMENT_DELETE);
        JSONObject entityObject = new JSONObject();
        //approve 的数据
        entityObject.put(ASSIGNMENT_IDS, assignmentId);
        jsonObject.put("entity", entityObject);
        jobdivaToHrRabbitService.sendDataToHr(jsonObject);
    }

    @Override
    public void buildApproveListSyncToHrMq(List<ApproveRecord> approveRecordList, CommentsType commentsType) {
        //审批的是，会涉及到 对应的打卡或者报销信息的变动
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", JobdivaDataSyncTypeEnum.TIME_SHEET_APPROVE);
        JSONObject entityObject = new JSONObject();
        //approve 的数据
        entityObject.put(TIMESHEET_APPROVE, approveRecordList);
        jsonObject.put("entity", entityObject);
        jobdivaToHrRabbitService.sendDataToHr(jsonObject);
    }

    @Override
    public void buildTimeSheetRecordListSyncToHrMq(Long assignmentId, LocalDate weekEnd, LocalDate weekendingDate) {
        entityManager.clear();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", JobdivaDataSyncTypeEnum.TIME_SHEET_RECORD);
        JSONObject entityObject = new JSONObject();
        //这个地方要扩大同步范围,被拆分之后的week_end 打卡后,可能影响到后续几天的打卡记录,需要已打卡的维度 weekendingDate 来处理
        List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findAllByAssignmentIdAndAndWeekEndingDate(assignmentId, weekendingDate);
        entityObject.put(TIMESHEET, timeSheetRecordList);
        TimeSheetRecord timeSheetRecord = timeSheetRecordList.stream().filter(record -> record.getWeekEnd().isEqual(weekEnd)).findFirst().orElseThrow();
        //处理 breakTime
        LocalDate startDate = weekendingDate.plusDays(-6);
        List<TimeSheetBreakTimeRecord> timeSheetBreakTimeRecordList = breakTimeRepository.findAllByDate(startDate, weekendingDate, timeSheetRecord.getTalentId(), timeSheetRecord.getAssignmentId());
        entityObject.put(TIMESHEET_BREAKTIME, timeSheetBreakTimeRecordList);
        //处理 comments
        TimeSheetComments comments = commentsRepository.findAllByDateAndType(timeSheetRecord.getWeekEnd(), timeSheetRecord.getTalentId(), CommentsType.TIME_SHEET.toDbValue(), timeSheetRecord.getAssignmentId());
        entityObject.put(TIMESHEET_COMMENTS, comments);
        //处理 holiday
        List<TimeSheetHolidayRecord> holidayRecordList = holidayRecordRepository.findAllByAssignmentId(assignmentId);
        entityObject.put(HOLIDAY, holidayRecordList);
        jsonObject.put("entity", entityObject);
        jobdivaToHrRabbitService.sendDataToHr(jsonObject);
    }

    @Override
    public void buildExpenseRecordListSyncToHrMq(Long assignmentId, LocalDate weekendingDate, Integer expenseIndex) {
        entityManager.clear();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", JobdivaDataSyncTypeEnum.TIME_SHEET_EXPENSE);
        JSONObject entityObject = new JSONObject();
        List<ExpenseRecord> expenseRecordList = expenseRecordRepository.findAllByAssignmentIdAndWeekEndAndExpenseIndex(assignmentId, weekendingDate, expenseIndex);
        entityObject.put(TIMESHEET_EXPENSE, expenseRecordList);
        ExpenseRecord expenseRecord = expenseRecordList.get(0);
        TimeSheetComments comments = commentsRepository.findAllByDateAndType(expenseRecord.getWeekEnd(), expenseRecord.getTalentId(), CommentsType.EXPENSE.toDbValue(), expenseRecord.getAssignmentId(), expenseRecord.getExpenseIndex());
        entityObject.put(TIMESHEET_COMMENTS, comments);
        jsonObject.put("entity", entityObject);
        jobdivaToHrRabbitService.sendDataToHr(jsonObject);
    }


    @Override
    public void buildHolidayListSyncToHrMq(List<TimeSheetHolidayRecord> holidayRecordList) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", JobdivaDataSyncTypeEnum.TIME_SHEET_HOLIDAY);
        JSONObject entityObject = new JSONObject();
        List<Long> assignmentIdList = holidayRecordList.stream().map(TimeSheetHolidayRecord::getAssignmentId).toList();
        List<TimeSheetHolidayRecord> allList = holidayRecordRepository.findAllByAssignmentIdIn(assignmentIdList);
        entityObject.put(HOLIDAY, allList);
        jsonObject.put("entity", entityObject);
        jobdivaToHrRabbitService.sendDataToHr(jsonObject);
    }

    @Override
    public void buildNoHoursSyncToHrMq(ApproveRecord approveRecord, List<TimeSheetRecord> timeSheetRecords, TimeSheetStatus status) {
        entityManager.clear();
        TimeSheetRecord record = timeSheetRecords.get(0);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", JobdivaDataSyncTypeEnum.TIME_SHEET_NO_HOURS);
        JSONObject entityObject = new JSONObject();
        timeSheetRecords = timeSheetRecordRepository.findAllByIdIn(timeSheetRecords.stream().map(TimeSheetRecord::getId).toList());
        entityObject.put(TIMESHEET, timeSheetRecords);
        if (TimeSheetStatus.APPROVED == status) {
            //save
            entityObject.put(TIMESHEET_APPROVE, approveRecord);
            //comments
            TimeSheetComments comments = commentsRepository.findAllByDateAndType(record.getWeekEnd(), record.getTalentId(), CommentsType.TIME_SHEET.toDbValue(), record.getAssignmentId());
            entityObject.put(TIMESHEET_COMMENTS, comments);
            //holiday
            List<TimeSheetHolidayRecord> holidayRecordList = holidayRecordRepository.findOneByAssignmentIdAndWeekEnd(record.getAssignmentId(), record.getWeekEnd());
            entityObject.put(HOLIDAY, holidayRecordList);
        }
        jsonObject.put("entity", entityObject);
        jobdivaToHrRabbitService.sendDataToHr(jsonObject);
    }

    @Override
    public void buildTimeSheetRecordListByExcelSyncToHrMq(List<TimeSheetRecord> weekendingDateList, List<ApproveRecord> approveRecordList) {
        entityManager.clear();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", JobdivaDataSyncTypeEnum.TIME_SHEET_RECORD_BY_EXCEL);
        JSONObject entityObject = new JSONObject();
        jsonObject.put("entity", entityObject);
        List<TimeSheetRecord> allTimeSheetRecordList = new ArrayList<>();
        for (TimeSheetRecord timeSheetRecord : weekendingDateList) {
            //这个地方要扩大同步范围,被拆分之后的week_end 打卡后,可能影响到后续几天的打卡记录,需要已打卡的维度 weekendingDate 来处理
            List<TimeSheetRecord> timeSheetRecordList = timeSheetRecordRepository.findAllByAssignmentIdAndAndWeekEndingDate(timeSheetRecord.getAssignmentId(), timeSheetRecord.getWeekEndingDate());
            allTimeSheetRecordList.addAll(timeSheetRecordList);
        }
        entityObject.put(TIMESHEET, allTimeSheetRecordList);
        entityObject.put(TIMESHEET_APPROVE, approveRecordList);
        jsonObject.put("entity", entityObject);
        jobdivaToHrRabbitService.sendDataToHr(jsonObject);
    }

    @Override
    public void buildTimeSheetUserSyncToHrMq(TimeSheetUser user) {
        JSONObject request = new JSONObject();
        request.put("id", user.getId());
        request.put("type", JobdivaDataSyncTypeEnum.TIME_SHEET_USER);
        request.put("entity", user);
        jobdivaToHrRabbitService.sendDataToHr(request);
    }

}
