package com.altomni.apn.jobdiva.repository.timesheet;

import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetWeekEndingRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 *
 * Spring Data JPA repository for the TimeSheetWeekEndingRecord entity.
 */
@Repository
public interface TimeSheetWeekEndingRecordRepository extends JpaRepository<TimeSheetWeekEndingRecord, Long> {

    TimeSheetWeekEndingRecord findTimeSheetWeekEndingRecordByAssignmentIdAndWorkDate(Long assignmentId, LocalDate workDate);

    List<TimeSheetWeekEndingRecord> findAllByAssignmentIdIn(List<Long> assignmentIds);
}
