package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * timesheet校验是否生成invoice
 * Timesheet verification to generate invoice
 */
@ApiModel(description = "contractorInvoiceCreateDTO")
@Data
public class ContractorInvoiceExistDTO {

    List<CheckInvoiceDTO> checkInvoiceList;

    Integer invoiceType = InvoiceType.REGULAR.toDbValue();
}
