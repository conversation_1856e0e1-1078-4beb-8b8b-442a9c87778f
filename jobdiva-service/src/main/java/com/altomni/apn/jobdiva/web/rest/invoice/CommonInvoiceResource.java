package com.altomni.apn.jobdiva.web.rest.invoice;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.service.invoice.CommonInvoiceService;
import com.altomni.apn.jobdiva.service.vo.invoice.InvoiceCommonVO;
import com.altomni.apn.jobdiva.service.vo.invoice.TenantUserAndEmailVO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;

/**
 * invoice controller
 *
 * <AUTHOR> zhang.lei
 * @date : 2023-6-13
 * @link : interface document address :https://intelliprogroup.larksuite.com/docx/D3CMdWMJDojvFYx3ytTuzYtJsze
 */
@Api(tags = {"CommonInvoiceResource"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class CommonInvoiceResource {

    @Autowired
    CommonInvoiceService commonInvoiceService;

    @GetMapping("/contractor/invoice/common/employee")
    @Timed
    public ResponseEntity<List<InvoiceCommonVO>> searchEmployee(@RequestParam(value = "name",required = false) String name,@RequestParam(value = "companyId",required = false) BigInteger companyId) {
        log.info("[invoice: User @{}] REST search employee method:", SecurityUtils.getUserId());
        List<InvoiceCommonVO> result = commonInvoiceService.queryEmployeeName(SecurityUtils.getTenantId(),name,companyId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/common/employee")
    @Timed
    public ResponseEntity<List<InvoiceCommonVO>> searchEmployeeByNameOrId(@RequestParam(value = "name",required = false) String name) {
        log.info("[invoice: User @{}] REST search employee method:", SecurityUtils.getUserId());
        List<InvoiceCommonVO> result = commonInvoiceService.queryEmployeeNameByNameOrId(SecurityUtils.getTenantId(),name);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/common/billCompany")
    @Timed
    public ResponseEntity<List<InvoiceCommonVO>> searchBillCompanyByName(@RequestParam(value = "name",required = false) String name) {
        log.info("[invoice: User @{}] REST search billCompany method:", SecurityUtils.getUserId());
        List<InvoiceCommonVO> result = commonInvoiceService.searchBillCompanyByName(SecurityUtils.getTenantId(),name);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/contractor/invoice/common/company/employee")
    @Timed
    public ResponseEntity<List<InvoiceCommonVO>> searchCompanyEmployee(@RequestParam(value = "companyId",required = false) BigInteger companyId) {
        log.info("[invoice: User @{}] REST search employee method:", SecurityUtils.getUserId());
        List<InvoiceCommonVO> result = commonInvoiceService.queryCompanyEmployeeName(SecurityUtils.getTenantId(),companyId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/contractor/invoice/common/billCompany")
    @Timed
    public ResponseEntity<List<InvoiceCommonVO>> searchBillCompany(@RequestParam(value = "name",required = false) String name) {
        log.info("[invoice: User @{}] REST search billCompany method:", SecurityUtils.getUserId());
        List<InvoiceCommonVO> result = commonInvoiceService.queryBillCompany(SecurityUtils.getTenantId(),name);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/contractor/invoice/common/billContact")
    @Timed
    public ResponseEntity<List<InvoiceCommonVO>> searchBillContact(@RequestParam(value = "name",required = false) String name) {
        log.info("[invoice: User @{}] REST search bill contact method:", SecurityUtils.getUserId());
        List<InvoiceCommonVO> result = commonInvoiceService.queryBillContact(SecurityUtils.getTenantId(),name);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/tenant/company/userEmailInfo")
    @Timed
    public ResponseEntity<List<TenantUserAndEmailVO>> searchUserAndEmail(){
        log.info("[invoice: User @{}] REST search user and email method:", SecurityUtils.getUserId());
        List<TenantUserAndEmailVO> result = commonInvoiceService.queryUserAndEmail(SecurityUtils.getTenantId());
        return ResponseEntity.ok(result);
    }
}