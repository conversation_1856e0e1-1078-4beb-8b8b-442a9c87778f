package com.altomni.apn.jobdiva.service.finance;

import com.altomni.apn.job.domain.enumeration.start.StartStatus;
import com.altomni.apn.job.service.dto.start.StartDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 *
 * <AUTHOR> zhang.lei
 * @date : 2023-6-27
 */
@Component
@FeignClient(value = "finance-service")
public interface FinanceService {

    @GetMapping("/finance/api/v3/sequence/{sequenceName}/{len}/{prefix}/{maxValue}")
    ResponseEntity<String> getCommonSequence(@PathVariable("sequenceName") String sequenceName,
                                             @PathVariable("len") Integer len,
                                             @PathVariable("prefix") String prefix,
                                             @PathVariable("maxValue") Long maxValue);


    @GetMapping("/finance/api/v3/starts/{talentId}/{talentRecruitmentProcessId}/{status}")
    ResponseEntity<StartDTO> getStartByTalentIdAndTalentRecruitmentProcessId(@PathVariable("talentId") Long talentId, @PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId,@PathVariable("status") StartStatus status);

}
