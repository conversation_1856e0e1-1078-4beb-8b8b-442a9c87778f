package com.altomni.apn.jobdiva.web.rest.timesheet;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.jobdiva.ManagerRoleType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.domain.timesheet.TimeSheetRecord;
import com.altomni.apn.jobdiva.service.dto.timesheet.BreakTimeDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.NoHourDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.RecordSearchDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.SummaryQueryDTO;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetRecordService;
import com.altomni.apn.jobdiva.service.vo.timesheet.SummaryDataVO;
import com.altomni.apn.jobdiva.util.TimeSheetUtil;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = {"TimeSheetRecord"})
@RestController
@RequestMapping("/api/v3/timesheet/record")
public class TimeSheetRecordResource {

    @Resource
    @Qualifier("timeSheetRecordService")
    private TimeSheetRecordService timeSheetRecordService;


    /**
     *
     * @param dto
     * @return
     */
    @PostMapping("/save")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<TimeSheetRecord>> save(@RequestBody BreakTimeDTO dto) {
        log.info("[timesheet: User @{}] request to save record, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        if (!TimeSheetUtil.checkTimeTypeAndOverTimeType(dto.getAssignmentId(), dto.getType(), dto.getOverTimeType())) {
            throw new CustomParameterizedException("please check the time sheet format");
        }
        List<TimeSheetRecord> newRecord = timeSheetRecordService.saveRecord(dto, SecurityUtils.getTenantId(), SecurityUtils.getUserId(), false);
        return ResponseEntity.ok(newRecord);
    }

    /**
     *
     * @param dto
     * @return
     */

    @PostMapping("/dateSelect")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<TimeSheetRecord>> saveRecordForDateSelect(@RequestBody BreakTimeDTO dto) {
        log.info("[timesheet: User @{}] request to save date select record, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        if (!TimeSheetUtil.checkTimeTypeAndOverTimeType(dto.getAssignmentId(), dto.getType(), dto.getOverTimeType())) {
            throw new CustomParameterizedException("please check the time sheet format");
        }
        List<TimeSheetRecord> newRecord = timeSheetRecordService.saveRecordForDateSelect(dto, SecurityUtils.getUserId(), SecurityUtils.getTenantId(), false);
        return ResponseEntity.ok(newRecord);
    }

    @PostMapping("/list")
    @Timed
    public ResponseEntity<BreakTimeDTO> list(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] find list , param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        BreakTimeDTO records = timeSheetRecordService.findRecords(dto, SecurityUtils.getUserId(), dto.getAssignmentId());
        records.hiddenRate();
        return ResponseEntity.ok(records);
    }

    /**
     *
     * @param dto
     * @return
     */
    @PostMapping("/client")
    @Timed
    public ResponseEntity<BreakTimeDTO> findById(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] find by detail , param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        BreakTimeDTO records = timeSheetRecordService.findRecordById(dto);
        return ResponseEntity.ok(records);
    }

    /**
     *GET talent breaktime record
     * @param dto
     * @return
     */
    @PostMapping("/breaktime/list")
    @Timed
    public ResponseEntity<BreakTimeDTO> getBreaktime(@RequestBody RecordSearchDTO dto) {
        log.info("[timesheet: User @{}] find break time list", SecurityUtils.getUserId());
        BreakTimeDTO records = timeSheetRecordService.findBreakTimeRecord(dto,SecurityUtils.getUserId());
        records.hiddenRate();
        return ResponseEntity.ok(records);
    }

    @PostMapping("/breaktime/save")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> saveBreakTime(@RequestBody BreakTimeDTO dto) {
        log.info("[timesheet: User @{}] save break time record, param = {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        if (!TimeSheetUtil.checkTimeTypeAndOverTimeType(dto.getAssignmentId(), dto.getType(), dto.getOverTimeType())) {
            throw new CustomParameterizedException("please check the time sheet format");
        }
        Integer records = timeSheetRecordService.saveBreakTime(dto, SecurityUtils.getUserId(), SecurityUtils.getTenantId(), false);
        return ResponseEntity.ok(records);
    }

    @PostMapping("/summary")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<SummaryDataVO> endingDateList(@RequestBody SummaryQueryDTO dto) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("summary");
        log.info("[timesheet: User @{}] query summary", SecurityUtils.getUserId());
        SummaryDataVO result = timeSheetRecordService.summary(dto,SecurityUtils.getUserId());
        stopWatch.stop();
        log.info(" summary time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return ResponseEntity.ok(result);
    }

    /**
     * no hour
     * @param dto
     * @return
     */

    @PostMapping("/nohour")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> noHour(@RequestBody NoHourDTO dto) {
        log.info("[timesheet: User @{}] request to no hour:", SecurityUtils.getUserId());
        dto.setRoleType(ManagerRoleType.CLIENT);
        if (!TimeSheetUtil.checkTimeTypeAndOverTimeType(dto.getAssignmentId(), dto.getType(), dto.getOverTimeType())) {
            throw new CustomParameterizedException("please check the time sheet format");
        }
        Integer result = timeSheetRecordService.noHour(dto,SecurityUtils.getUserId(), false);
        return ResponseEntity.ok(result);
    }

}
