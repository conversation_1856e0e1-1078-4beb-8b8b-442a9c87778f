package com.altomni.apn.jobdiva.service.vo.timesheet;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.FloatConverter;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.LocalDateExcelDateConverter;
import com.altomni.apn.jobdiva.domain.enumeration.timesheet.TimeSheetStatusDateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

/**
 * A record
 */
@ApiModel(description = "record for week ending")
@Entity
@Data
public class TimeSheetSummaryVO implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final Logger log = LoggerFactory.getLogger(TimeSheetSummaryVO.class);

    @Id
    private Long  id;

    @ApiModelProperty(value = "work date")
    @ColumnWidth(20)
    @ExcelIgnore
    private LocalDate endingDate;

    @ExcelIgnore
    private LocalDate weekStart;

    @ColumnWidth(20)
    @ExcelProperty(value = "Week Ending",index = 2,converter = LocalDateExcelDateConverter.class)
    private LocalDate weekEnd;

    @ApiModelProperty(value = "applied date")
    @ColumnWidth(20)
    @ExcelIgnore
    private Instant appliedDate;

    @ExcelProperty(value = "Submitted Time",index = 7)
    @Transient
    private String appliedDateStr;

    @ApiModelProperty(value = "approved date")
    @ColumnWidth(20)
    @ExcelIgnore
    private Instant approvedDate;

    @ExcelProperty(value = "On",index = 12)
    @Transient
    private String approvedDateStr;

    @ApiModelProperty(value = "work hours")
    @ColumnWidth(20)
    @ExcelProperty(value = "Total(hrs)",index = 3,converter = FloatConverter.class)
    private Float totalHours;

    @ApiModelProperty(value = "regularHours")
    @ColumnWidth(20)
    @ExcelProperty(value = "Regular",index = 4,converter = FloatConverter.class)
    private Float regularHours;

    @ApiModelProperty(value = "regularHours")
    @ColumnWidth(20)
    @ExcelProperty(value = "OT",index = 5,converter = FloatConverter.class)
    private Float overTime;

    @ApiModelProperty(value = "doubleTime")
    @ColumnWidth(20)
    @ExcelProperty(value = "DT",index = 6,converter = FloatConverter.class)
    private Float doubleTime;

    @ColumnWidth(20)
    @ExcelProperty(value = "Employee Name",index = 1)
    @ApiModelProperty(value = "talentName")
    private String talentName;

    @ApiModelProperty(value = "jobTitle")
    @ColumnWidth(20)
    @ExcelProperty(value = "Job Title",index = 9)
    private String jobTitle;

    @ApiModelProperty(value = "job id")
    private Long  jobId;

    @ApiModelProperty(value = "manager")
    @ColumnWidth(20)
    @ExcelProperty(value = "Manager",index = 10)
    private String manager;


    @ApiModelProperty(value = "company name")
    @ColumnWidth(20)
    @ExcelProperty(value = "Company",index = 8)
    private String companyName;


    @ApiModelProperty(value = "job start date")
    @ExcelIgnore
    private LocalDate startDate;

    @ApiModelProperty(value = "job end date")
    @ExcelIgnore
    private LocalDate endDate;

    @ApiModelProperty(value = "time sheet type")
    @Convert(converter = TimeSheetTypeConverter.class)
    @ExcelIgnore
    private TimeSheetType sheetType;

    @ApiModelProperty(value = "status")
    @Convert(converter = TimeSheetStatusConverter.class)
    @ColumnWidth(20)
    @ExcelProperty(value = "Status",index = 11,converter = TimeSheetStatusDateConverter.class)
    private TimeSheetStatus status;

    @ApiModelProperty(value = "calculate method")
    @ExcelIgnore
    @Convert(converter = CalculateMethodTypeConverter.class)
    private CalculateMethodType calculateMethod;

    @ApiModelProperty(value = "assignment id")
    @ExcelIgnore
    private Long assignmentId;

    @ExcelIgnore
    @Transient
    private String comments;


    @ExcelIgnore
    private String primaryManager;

    @Transient
    @ExcelIgnore
    private Integer weekEnding;

    @ExcelIgnore
    @Convert(converter = OverTimeConverter.class)
    private OverTimeType overtimeType;


    @Transient
    @ExcelIgnore
    private Long  talentId;

    @ExcelIgnore
    private  String am;

    @ExcelIgnore
    private  String instructions;

    @ApiModelProperty(value = "Returns whether overtime is exempt")
    @ExcelIgnore
    private Integer isExcept;

    @ExcelIgnore
    private LocalDate weekEndingDate;

    @ExcelIgnore
    private Long amApproverId;

    public TimeSheetSummaryVO() {
    }

    public TimeSheetSummaryVO(LocalDate endingDate, Instant appliedDate, Instant approvedDate, String talentName, String jobTitle, String manager, String companyName, LocalDate startDate, LocalDate endDate, TimeSheetStatus status, String comments,String primaryManger,Long talentId) {
        this.endingDate = endingDate;
        this.appliedDate = appliedDate;
        this.approvedDate = approvedDate;
        this.talentName = talentName;
        this.jobTitle = jobTitle;
        this.manager = manager;
        this.companyName = companyName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.status = status;
        this.comments = comments;
        this.primaryManager= primaryManger;
        this.talentId = talentId;
    }

    public TimeSheetSummaryVO(LocalDate endingDate, Instant appliedDate, Instant approvedDate, String talentName, String jobTitle, String manager, String companyName, LocalDate startDate, LocalDate endDate, TimeSheetStatus status, String comments,String primaryManger) {
        this.endingDate = endingDate;
        this.appliedDate = appliedDate;
        this.approvedDate = approvedDate;
        this.talentName = talentName;
        this.jobTitle = jobTitle;
        this.manager = manager;
        this.companyName = companyName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.status = status;
        this.comments = comments;
        this.primaryManager= primaryManger;
    }

    public TimeSheetSummaryVO(String talentName, String jobTitle,String companyName, LocalDate startDate, LocalDate endDate,String primaryManger) {
        this.talentName = talentName;
        this.jobTitle = jobTitle;
        this.companyName = companyName;
        this.startDate = startDate;
        this.endDate = endDate;
        this.primaryManager= primaryManger;
    }

}




