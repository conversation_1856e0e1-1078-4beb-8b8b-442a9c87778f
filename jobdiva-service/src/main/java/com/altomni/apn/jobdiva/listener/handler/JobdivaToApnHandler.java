package com.altomni.apn.jobdiva.listener.handler;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;

public interface JobdivaToApnHandler {

    String HOLIDAY = "HOLIDAY";

    String TIMESHEET = "TIMESHEET";

    String TIMESHEET_BREAKTIME = "TIMESHEET_BREAKTIME";

    String TIMESHEET_EXPENSE = "TIMESHEET_EXPENSE";

    String TIMESHEET_APPROVE = "TIMESHEET_APPROVE";

    String TIMESHEET_COMMENTS = "TIMESHEET_COMMENTS";

    void execute(J<PERSON><PERSON>O<PERSON> json);

    boolean isSupport(JobdivaDataSyncTypeEnum typeEnum);

}
