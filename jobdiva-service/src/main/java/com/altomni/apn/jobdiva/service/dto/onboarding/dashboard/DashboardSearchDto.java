package com.altomni.apn.jobdiva.service.dto.onboarding.dashboard;

import com.altomni.apn.common.dto.search.SearchParam;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * search dto
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DashboardSearchDto {

    @ApiModelProperty(value = "search condition in json string")
    private List<SearchParam> search;

    private Integer pageNumber;

    public Integer getPageNumber() {
        if (pageNumber == null) {
            return 0;
        }
        return pageNumber;
    }

    private Integer pageSize;

    public Integer getPageSize() {
        if (pageSize == null) {
            return 25;
        }
        return pageSize;
    }

    private SearchSortDTO sort;

    private String timezone;


}
