package com.altomni.apn.jobdiva.util;

import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.jobdiva.service.user.UserService;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CommonAmIdUtil {

    /**
     * 从对象列表中提取 amIds 和 amApproverId 并合并去重
     * 支持只有 amIds 或 amApproverId 其中一个的情况
     * @param list 对象列表
     * @param amIdsGetter 获取 amIds 的函数，可为 null
     * @param amApproverIdGetter 获取 amApproverId 的函数，可为 null
     * @param <T> 对象类型
     * @return 合并去重后的用户 ID 列表
     */
    public static <T> List<Long> getCombinedIds(List<T> list, Function<T, String> amIdsGetter, Function<T, Long> amApproverIdGetter) {
        return list.stream()
                .flatMap(item -> {
                    List<Long> ids = new ArrayList<>();
                    // 处理 amIds 字段
                    if (amIdsGetter != null) {
                        String amIds = amIdsGetter.apply(item);
                        if (amIds != null) {
                            ids.addAll(Arrays.stream(amIds.split(","))
                                    .map(String::trim)
                                    .filter(s -> !s.isEmpty())
                                    .map(Long::parseLong)
                                    .toList());
                        }
                    }
                    // 处理 amApproverId 字段
                    if (amApproverIdGetter != null) {
                        Long amApproverId = amApproverIdGetter.apply(item);
                        if (amApproverId != null) {
                            ids.add(amApproverId);
                        }
                    }
                    return ids.stream();
                })
                .distinct()
                .toList();
    }


    /**
     * 根据用户 ID 列表获取用户简要信息并构建用户 ID 到完整姓名的映射
     * @param userIds 用户 ID 列表
     * @param userService 获取用户简要信息的服务
     * @return 用户 ID 到完整姓名的映射
     */
    public static Map<Long, String> createUserIdToNameMap(List<Long> userIds, UserService userService) {
        if (userIds == null || userIds.isEmpty()) {
            return new HashMap<>();
        }
        List<UserBriefDTO> userBriefDTOList = userService.getBriefUsersByIds(userIds).getBody();
        return userBriefDTOList.stream()
                .collect(Collectors.toMap(
                        UserBriefDTO::getId,
                        user -> CommonUtils.formatFullName(user.getFirstName(), user.getLastName()),
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 将用户 ID 到姓名的映射补全到目标对象列表中
     * @param list 目标对象列表
     * @param amIdsGetter 获取 amIds 的函数
     * @param amApproverIdGetter 获取 amApproverId 的函数
     * @param amNamesSetter 设置 amNames 的函数
     * @param amApproverNameSetter 设置 amApproverName 的函数
     * @param userIdToNameMap 用户 ID 到姓名的映射
     * @param <T> 对象类型
     */
    public static <T> void mapNamesToObjects(List<T> list,
                                             Function<T, String> amIdsGetter,
                                             Function<T, Long> amApproverIdGetter,
                                             BiConsumer<T, String> amNamesSetter,
                                             BiConsumer<T, String> amApproverNameSetter,
                                             Map<Long, String> userIdToNameMap) {
        list.forEach(item -> {
            // 补全 amNames
            if (amIdsGetter != null) {
                String amIds = amIdsGetter.apply(item);
                if (amIds != null) {
                    String amNames = Arrays.stream(amIds.split(","))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .map(Long::parseLong)
                            .map(userIdToNameMap::get)
                            .filter(StrUtil::isNotBlank)
                            .collect(Collectors.joining(","));
                    amNamesSetter.accept(item, amNames);
                }
            }
            if (amApproverIdGetter != null) {
                // 补全 amApproverName
                Long amApproverId = amApproverIdGetter.apply(item);
                if (amApproverId != null) {
                    String amApproverName = userIdToNameMap.get(amApproverId);
                    if (amApproverName != null) {
                        amApproverNameSetter.accept(item, amApproverName);
                    }
                }
            }
        });
    }


}