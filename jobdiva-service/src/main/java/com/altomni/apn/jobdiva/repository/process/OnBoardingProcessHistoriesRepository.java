package com.altomni.apn.jobdiva.repository.process;

import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingProcessHistories;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the OnBoardingPackageDocuments entity.
 */
@Repository
public interface OnBoardingProcessHistoriesRepository extends JpaRepository<OnBoardingProcessHistories, Long> {

    List<OnBoardingProcessHistories> findAllByTalentRecruitmentProcessIdAndTenantIdOrderByCreatedDateDesc(Long talentRecruitmentProcessId, Long tenantId);

    List<OnBoardingProcessHistories> findAllByTalentRecruitmentProcessIdAndTenantIdAndCompletionStatusAndActivated(Long findTalentNameByTalentRecruitmentProcessId, Long tenantId, CompletionStatus status, <PERSON>olean activated);

    List<OnBoardingProcessHistories> findAllByProcessIdAndTenantIdAndActivated(String processId, Long tenantId, Boolean activated);

    @Query(value = "select ph.*  from onboarding_process_histories ph left join talent_recruitment_process a on ph.talent_recruitment_process_id = a.id " +
        " where a.talent_id = ?1   and ph.tenant_id = ?2  and ph.activated = 1 ", nativeQuery = true)
    List<OnBoardingProcessHistories> findAllHistoriesByTalentId(Long talentId, Long tenantId);

    @Query(value = "select a.id,j.title,aol.start_date,aol.status from talent_recruitment_process a left join start aol on a.id = aol.talent_recruitment_process_id " +
        "left join job j on a.job_id = j.id left join talent_recruitment_process_node rpn on a.id = rpn.talent_recruitment_process_id " +
        "where a.talent_id = ?1 and rpn.node_type = ?2 order by aol.created_date desc,aol.`status` asc", nativeQuery = true)
    List<Object[]> findByTalentIdAndStatus(Long talentId, Integer status);

    @Query(value = "select j.id as jobId,j.title,t.tenant_id,t.id as talentId,t.first_name,t.last_name,t.full_name,tc.contact from talent_recruitment_process a " +
        " left join job j on a.job_id = j.id left join talent t on a.talent_id = t.id " +
        " left join (select talent_id,contact from talent_contact where jhi_type = ?1 and status = ?2 and talent_id = ?3 order by sort limit 1 ) tc on t.id = tc.talent_id where a.id = ?4 ", nativeQuery = true)
    List<Object[]> findStartEmailInfoByTalentRecruitmentProcessId(Integer type, Integer status, Long talentId, Long talentRecruitmentProcessId);

    @Query(value = "select t.full_name from talent t left join talent_recruitment_process a on t.id = a.talent_id where a.id = ?1", nativeQuery = true)
    String findTalentNameByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Query(value = "select t.full_name,t.first_name,t.last_name,tc.contact from talent t left join (select talent_id,contact from talent_contact " +
        "         where jhi_type = ?1 and status = ?2 and talent_id = ?3 order by sort limit 1) tc on t.id = tc.talent_id where t.id = ?3", nativeQuery = true)
    List<Object[]> findPortalEmailInfoByTalentRecruitmentProcessId(Integer type, Integer status, Long talentId);

    @Query(value = "select u.email from talent_recruitment_process a left join talent_recruitment_process_kpi_user am on a.id = am.talent_recruitment_process_id " +
        "left join user u on u.id = am.user_id where a.id = ?1 and am.user_role = ?2 ", nativeQuery = true)
    List<String> findCommissionAMEmailByTalentRecruitmentProcessId(Long talentRecruitmentProcessId, Integer userRole);

    @Query(value = """
        select u.email from talent_recruitment_process a left join job j on j.id = a.job_id 
        inner join business_flow_administrator psl on j.company_id = psl.company_id 
        inner join user u on u.id = psl.user_id 
        where a.id = ?1 and psl.sales_lead_role in (0,3)
    """, nativeQuery = true)
    List<String> findCompanyAMEmailByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Query(value = "select count(his.id) from onboarding_process_histories his where his.talent_recruitment_process_id = ?1 ", nativeQuery = true)
    Integer countHistoriesByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Query(value = """
        select count(psl.id) from talent_recruitment_process a 
        inner join job j on j.id = a.job_id 
        inner join business_flow_administrator psl on j.company_id = psl.company_id 
        where a.id = ?1 and psl.user_id = ?2 and psl.sales_lead_role in (0,3)
    """, nativeQuery = true)
    Integer countCompanyAccountManagerByTalentRecruitmentProcessId(Long talentRecruitmentProcessId, Long userId);

    @Query(value = "select count(psl.id) from talent_recruitment_process a " +
            " inner join job j on j.id = a.job_id " +
            " inner join business_flow_administrator psl on j.company_id = psl.company_id " +
            " inner join talent_recruitment_process_node rpn on a.id = rpn.talent_recruitment_process_id " +
            " where a.talent_id = ?1 and rpn.node_type = ?2 and rpn.node_status = 1 and psl.user_id = ?3 and psl.sales_lead_role in (0,3) ", nativeQuery = true)
    Integer countCompanyAccountManager(Long talentId, Integer status, Long userId);


}
