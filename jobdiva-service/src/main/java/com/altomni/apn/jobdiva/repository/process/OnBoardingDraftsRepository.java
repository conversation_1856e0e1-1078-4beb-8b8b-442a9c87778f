package com.altomni.apn.jobdiva.repository.process;

import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingDrafts;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


/**
 * Spring Data JPA repository for the OnBoardingDrafts entity.
 */
@Repository
public interface OnBoardingDraftsRepository extends JpaRepository<OnBoardingDrafts, Long> {

    @Modifying
    @Transactional
    void deleteAllByPackageIdAndTenantId(Long id, Long tenantId);

    @Modifying
    @Transactional
    void deleteAllByDocumentIdAndTenantId(Long id, Long tenantId);

    @Modifying
    @Transactional
    void deleteAllByTalentRecruitmentProcessIdAndTenantId(Long talentRecruitmentProcessId, Long tenantId);

}
