package com.altomni.apn.jobdiva.service.dto.timesheet;

import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetTableOrderType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetTableSortType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Data
@ApiModel
public class SummaryQueryDTO implements Serializable {

    protected int pageNum=1;
    protected int pageSize=10;
    protected List<String> talentName;
    protected List<String> jobTitle;
    protected List<TimeSheetStatus> status;
    protected Set<Long> recordIds;
    protected LocalDate startDate;
    protected LocalDate endDate;
    protected TimeSheetTableOrderType orderBy;
    protected TimeSheetTableSortType order;
    private Long clientId;
    protected String timezone;


}
