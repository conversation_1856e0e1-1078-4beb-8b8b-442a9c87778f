package com.altomni.apn.jobdiva.config;

import cn.hutool.core.convert.ConverterRegistry;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.sql.Date;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

@Configuration
public class ConverterConfig {

    @PostConstruct
    public void initConverter() {
        ConverterRegistry.getInstance().putCustom(Instant.class, (value, defaultValue) -> {
            if (value == null) {
                return null;
            }
            if (value instanceof java.sql.Timestamp timestamp) {
                return timestamp.toInstant();
            } else if (value instanceof java.util.Date date) {
                return date.toInstant();
            }
            return defaultValue;
        });
        ConverterRegistry.getInstance().putCustom(LocalDate.class, (value, defaultValue) -> {
            if (value == null) {
                return null;
            }
            if (value instanceof java.sql.Timestamp timestamp) {
                return timestamp.toLocalDateTime().toLocalDate();
            } else if (value instanceof java.util.Date date) {
                try {
                    return date.toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
                } catch (Exception e) {
                    return ((Date) date).toLocalDate();
                }
            }
            return defaultValue;
        });
    }

}
