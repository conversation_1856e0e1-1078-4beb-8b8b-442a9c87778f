package com.altomni.apn.jobdiva.domain.enumeration.timesheet;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.springframework.util.ObjectUtils;

import java.text.DecimalFormat;
import java.time.LocalDate;

public class FloatConverter implements Converter<Float> {
    private static final DecimalFormat formatter = new DecimalFormat("#.##");
    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Float convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public CellData convertToExcelData(Float f, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (ObjectUtils.isEmpty(f)) {
            return new CellData("0");
        }
        if (f.floatValue() % 1 == 0) {
            return new CellData(String.valueOf(f.intValue()));
        } else {
            return new CellData(formatter.format(f));
        }
    }
}
