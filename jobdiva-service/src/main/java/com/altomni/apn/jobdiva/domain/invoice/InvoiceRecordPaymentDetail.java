package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

@ApiModel(value = "回款候选人明细",description = "")
@Entity
@Table(name="invoice_record_payment_detail")
@Data
public class InvoiceRecordPaymentDetail extends AbstractAuditingEntity implements Serializable,Cloneable{

    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id ;


    /**  */
    @Column(name = "invoice_id")
    private Long invoiceId ;

    /**  */
    @Column(name = "group_id")
    private Long groupId ;

    /** 支付id */
    @Column(name = "payment_id")
    private Long paymentId ;

    /**  */
    @Column(name = "talent_id")
    private Long talentId ;

    /**  */
    @Column(name = "talent_name")
    private String talentName ;

    /** 应支付金额 */
    @Column(name = "gp_amount")
    private BigDecimal gpAmount ;

    /** 支付金额 */
    @Column(name = "payment_amount")
    private BigDecimal paymentAmount ;

    /** 支付日期 */
    @ApiModelProperty(name = "支付日期")
    @Column(name = "payment_date")
    private Timestamp paymentDate ;

    /** 状态 0-无效 1-有效 */
    @Column(name = "status")
    private Integer status ;
}
