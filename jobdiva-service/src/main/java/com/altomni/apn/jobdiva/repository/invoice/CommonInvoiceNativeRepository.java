package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.service.vo.invoice.InvoiceCommonVO;
import com.altomni.apn.jobdiva.service.vo.invoice.TenantUserAndEmailVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.List;

@Repository
@Slf4j
public class CommonInvoiceNativeRepository {

    @Resource
    private EntityManager entityManager;

    /**
     * query employee name list by tenant id
     *
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoiceCommonVO> queryEmployeeName(Long tenantId, String name, BigInteger companyId) {
        StringBuilder dataSql = new StringBuilder("select t.talent_id as id,ta.full_name as resultName from timesheet_talent_assignment t " +
                " left join talent ta on ta.id = t.talent_id " +
                " where t.tenant_id= :tenantId");

        if (StringUtils.isNotBlank(name)) {
            dataSql.append(" and ta.full_name like :name");
        }
        if (null != companyId) {
            dataSql.append(" and t.company_id = " + companyId);
        }
        dataSql.append(" group by t.talent_id,ta.full_name order by t.talent_id desc ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", tenantId);
        if (StringUtils.isNotBlank(name)) {
            dataQuery.setParameter("name", "%" + name + "%");
        }
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoiceCommonVO.class));
        return dataQuery.getResultList();
    }

    /**
     * query employee name list by tenant id
     *
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoiceCommonVO> queryCompanyEmployeeName(Long tenantId, BigInteger companyId) {
        StringBuilder dataSql = new StringBuilder("select distinct tab.id,tab.resultName from (\n" +
                "select t.talent_id as id,ta.full_name as resultName,t.start_date,\n" +
                " t.end_date\n" +
                " from timesheet_talent_assignment t\n" +
                " left join talent ta on ta.id = t.talent_id " +
                " inner join start s on s.id = t.start_id\n" +
                " inner join start_client_info sci on sci.start_id = s.id \n" +
                " left join invoice_type_config itc on itc.tenant_id = s.tenant_id and itc.id = sci.invoice_type_id and itc.status=1" +
                " where itc.label='oversea contract' and t.tenant_id= :tenantId ");

        if (null != companyId) {
            dataSql.append(" and t.company_id = " + companyId);
        }
        dataSql.append(" ) tab where tab.end_date >= DATE_FORMAT(DATE_SUB(NOW(), interval 6 MONTH),'%y-%m-%d') order by tab.end_date desc ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", tenantId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoiceCommonVO.class));
        return dataQuery.getResultList();
    }

    /**
     * query employee name list by tenant id
     *
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoiceCommonVO> queryEmployeeNameByTermination(Long tenantId, BigInteger companyId) {
        StringBuilder dataSql = new StringBuilder("select distinct tab.id,tab.resultName from (\n" +
                "select t.talent_id as id,ta.full_name as resultName,t.start_date,\n" +
                "case when t.end_date is null then t.warranty_end_date else t.end_date end as end_date\n" +
                " from start t\n" +
                "left join talent ta on ta.id = t.talent_id " +
                " inner join start_client_info sci on sci.start_id = t.id\n" +
                " left join invoice_type_config itc on itc.tenant_id = t.tenant_id and itc.id = sci.invoice_type_id and itc.status=1" +
                " where t.tenant_id= :tenantId and t.`status` in(0,5,10) and itc.label='oversea contract' ");

        if (null != companyId) {
            dataSql.append(" and t.company_id = " + companyId);
        }

        dataSql.append(" ) tab where tab.end_date >= DATE_FORMAT(DATE_SUB(NOW(), interval 6 MONTH),'%y-%m-%d') order by tab.end_date desc ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", tenantId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoiceCommonVO.class));
        return dataQuery.getResultList();
    }

    /**
     * query bill company list by tenant id
     *
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoiceCommonVO> queryBillCompany(Long tenantId, String name) {
        StringBuilder dataSql = new StringBuilder("select t.company_id as id,com.full_business_name as resultName from timesheet_talent_assignment t\n" +
                " left join company com on com.id = t.company_id " +
                " where t.tenant_id= :tenantId ");

        if (StringUtils.isNotBlank(name)) {
            dataSql.append(" and com.full_business_name like :name");
        }

        dataSql.append(" group by t.company_id,com.full_business_name " +
                " order by t.company_id desc ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", tenantId);
        if (StringUtils.isNotBlank(name)) {
            dataQuery.setParameter("name", "%" + name + "%");
        }
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoiceCommonVO.class));
        return dataQuery.getResultList();
    }

    /**
     * query bill contact list by tenant id
     *
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoiceCommonVO> queryBillContact(Long tenantId, String name) {
        StringBuilder dataSql = new StringBuilder("SELECT\n" +
                "	c.id,\n" +
                "	ct.full_name AS resultName \n" +
                "FROM\n" +
                "	timesheet_talent_assignment t\n" +
                "	JOIN assignment_bill_info b ON b.assignment_id = t.id\n" +
                "	JOIN company_sales_lead_client_contact c ON c.id = b.contact_id \n" +
                "	LEFT JOIN talent ct ON ct.id = c.talent_id " +
                " where t.tenant_id= :tenantId ");

        if (StringUtils.isNotBlank(name)) {
            dataSql.append(" and ct.full_name like :name");
            ;
        }
        dataSql.append(" group by c.id,ct.full_name " +
                " order by c.id asc ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", tenantId);
        if (StringUtils.isNotBlank(name)) {
            dataQuery.setParameter("name", "%" + name + "%");
        }
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoiceCommonVO.class));
        return dataQuery.getResultList();
    }


    /**
     * query user and email list by tenant id
     *
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public List<TenantUserAndEmailVO> queryUserAndEmail(Long tenantId) {
        StringBuilder dataSql = new StringBuilder("SELECT DISTINCT\n" +
                "	tab.userName,\n" +
                "	tab.email \n" +
                "FROM\n" +
                "	(\n" +
                "	SELECT t.full_name AS\n" +
                "		userName,\n" +
                "	 	tc.contact as email \n" +
                "	FROM\n" +
                "		company_sales_lead_client_contact c \n" +
                "		LEFT JOIN talent t ON t.id = c.talent_id\n" +
                "		LEFT JOIN talent_contact tc ON tc.talent_id = t.id AND tc.jhi_type = 2\n" +
                "	WHERE\n" +
                "		c.tenant_id = :tenantId GROUP BY t.full_name,tc.contact UNION ALL\n" +
                "	SELECT\n" +
                "		username AS userName,\n" +
                "		email \n" +
                "	FROM\n" +
                "	`user` \n" +
                "	WHERE\n" +
                "		tenant_id = :tenantId\n" +
                "	) tab \n" +
                "WHERE\n" +
                "	tab.email IS NOT NULL");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", tenantId);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(TenantUserAndEmailVO.class));
        return dataQuery.getResultList();
    }

    /**
     * query employee name list by tenant id
     *
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoiceCommonVO> queryEmployeeNameByNameOrId(Long tenantId, String name) {
        StringBuilder dataSql = new StringBuilder("select t.id,t.full_name as resultName from talent t " +
                " where t.tenant_id= :tenantId");

        if (StringUtils.isNotBlank(name)) {
            dataSql.append(" and (t.full_name like :name or t.id like :name)");
        }

        dataSql.append(" order by t.id desc limit 100");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", tenantId);
        if (StringUtils.isNotBlank(name)) {
            dataQuery.setParameter("name", "%" + name + "%");
        }
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoiceCommonVO.class));
        return dataQuery.getResultList();
    }

    /**
     * query bill company list by tenant id
     *
     * @param tenantId
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoiceCommonVO> searchBillCompanyByName(Long tenantId, String name) {
        StringBuilder dataSql = new StringBuilder("select t.id as id,t.full_business_name as resultName from company t\n" +
                " where t.tenant_id= :tenantId ");

        if (StringUtils.isNotBlank(name)) {
            dataSql.append(" and t.full_business_name like :name");
        }

        dataSql.append(" order by t.id desc ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", tenantId);
        if (StringUtils.isNotBlank(name)) {
            dataQuery.setParameter("name", "%" + name + "%");
        }
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoiceCommonVO.class));
        return dataQuery.getResultList();
    }
}