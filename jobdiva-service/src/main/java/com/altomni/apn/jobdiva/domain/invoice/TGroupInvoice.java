package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;

/**
 * 分组发票;
 * <AUTHOR> zhang.lei
 * @date : 2023-6-9
 */
@ApiModel(value = "分组发票",description = "")
@Entity
@Table(name="t_group_invoice")
@Data
public class TGroupInvoice extends AbstractAuditingEntity implements Serializable,Cloneable{

    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id ;
    
    /** 分组编码 业务主键 */
    @ApiModelProperty(name = "分组编码 业务主键")
    @Column(name = "group_number")
    private String groupNumber ;

    /** 租户id */
    @ApiModelProperty(name = "租户id")
    @Column(name = "tenant_id")
    private BigInteger tenantId ;

    /** 公司id */
    @ApiModelProperty(name = "公司id")
    @Column(name = "company_id")
    private BigInteger companyId ;

    /** 公司名称 */
    @ApiModelProperty(name = "公司名称")
    @Column(name = "company_name")
    private String companyName ;

    /** 分组发票类型 1-all 2-regular 3-expense */
    @ApiModelProperty(name = "分组发票类型  1-regular 2-expense 3-all")
    @Column(name = "invoice_type")
    @Convert(converter = InvoiceTypeConverter.class)
    private InvoiceType invoiceType ;

    /** 分组类型 1-company 2-employee */
    @ApiModelProperty(name = "分组类型 1-company 2-employee")
    @Column(name = "group_type")
    @Convert(converter = GroupTypeConverter.class)
    private GroupType groupType ;

    /** 到期天数 */
    @ApiModelProperty(name = "到期天数")
    @Column(name = "due_within_days")
    private Integer dueWithinDays ;

    /** 付款到期日 */
    @ApiModelProperty(name = "付款到期日")
    @Column(name = "payment_due_date")
    private Timestamp paymentDueDate ;

    /** 发票日期 */
    @ApiModelProperty(name = "发票日期")
    @Column(name = "invoice_date")
    private LocalDate invoiceDate ;

    /** 包含打卡记录 1-包含 2-不包含 */
    @ApiModelProperty(name = "包含打卡记录 1-包含 2-不包含")
    @Column(name = "include_timesheet")
    @Convert(converter = IncludeTimesheetTypeConverter.class)
    private IncludeTimesheetType includeTimesheet ;


    /** 状态 1-Invoiced 2-Unpaid 3-Overdue 4-Paid 5-Void 6-Partially Paid */
    @ApiModelProperty(name = "状态 1-Invoiced 2-Unpaid 3-Overdue 4-Paid 5-Void 6-Partially Paid")
    @Column(name = "group_invoice_status")
    @Convert(converter = GroupInvoiceStatusConverter.class)
    private GroupInvoiceStatus groupInvoiceStatus ;

    /** 金额 */
    @ApiModelProperty(name = "金额")
    @Column(name = "invoice_amount")
    private BigDecimal invoiceAmount ;

    /** 待支付金额 */
    @ApiModelProperty(name = "待支付金额")
    @Column(name = "amount_due")
    private BigDecimal amountDue ;

    /** 货币类型 */
    @ApiModelProperty(name = "货币类型")
    @Column(name = "currency_id")
    private Integer currencyId;

    /** 文件地址 */
    @ApiModelProperty(name = "文件地址")
    @Column(name = "file_url")
    private String fileUrl ;

    /** 文件地址 */
    @ApiModelProperty(name = "文件地址 包含timesheet")
    @Column(name = "timesheet_file_url")
    private String timesheetFileUrl;

    /** 1-Altomni， 2-IPG， 3-Independent， 4-Other */
    @ApiModelProperty(name = "1-Altomni， 2-IPG， 3-Independent， 4-Other",notes = "")
    @Column(name = "assignment_division")
    private String assignmentDivision ;


    /** 操作人名称 */
    @ApiModelProperty(name = "操作人名称")
    @Column(name = "operated_by_name")
    private String operatedByName ;

    /** 操作人id */
    @ApiModelProperty(name = "操作人id")
    @Column(name = "operated_by_id")
    private BigInteger operatedById ;

    /** 操作日期 */
    @ApiModelProperty(name = "操作日期")
    @Column(name = "operated_date")
    private Timestamp operatedDate ;

    /** 状态 0-无效 1-有效 */
    @ApiModelProperty(name = "状态 0-无效 1-有效")
    @Column(name = "status")
    private Integer status ;
}
