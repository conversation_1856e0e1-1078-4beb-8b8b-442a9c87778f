package com.altomni.apn.jobdiva.repository.process;

import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingProcessHistorySignatures;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the OnBoardingProcessHistorySignatures entity.
 */
@Repository
public interface OnBoardingProcessHistorySignaturesRepository extends JpaRepository<OnBoardingProcessHistorySignatures, Long> {

    void deleteAllByHistoryId(Long historyId);

}
