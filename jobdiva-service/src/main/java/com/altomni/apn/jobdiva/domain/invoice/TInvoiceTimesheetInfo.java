package com.altomni.apn.jobdiva.domain.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.QuantityType;
import com.altomni.apn.common.domain.enumeration.jobdiva.QuantityTypeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * 发票子项明细;
 * <AUTHOR> http://www.chiner.pro
 * @date : 2023-6-6
 */
@ApiModel(value = "发票子项明细",description = "")
@Entity
@Table(name="t_invoice_timesheet_info")
@Data
public class TInvoiceTimesheetInfo extends AbstractAuditingEntity implements Serializable,Cloneable{
    /**  */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id ;
    
    /** 发票id */
    @ApiModelProperty(name = "发票id")
    @Column(name = "invoice_id")
    private BigInteger invoiceId ;
    
    /** 货币类型 */
    @ApiModelProperty(name = "货币类型")
    @Column(name = "currency_type")
    private Integer currencyType ;

    @Transient
    private String currencyName;
    
    /** 数量 */
    @ApiModelProperty(name = "数量")
    @Column(name = "quantity")
    private BigDecimal quantity ;
    
    /** 数量类型 1-rt 2-ot 3-dt 4-holiday ot 5-adjustment */
    @ApiModelProperty(name = "数量类型 1-rt 2-ot 3-dt 4-holiday ot 5-adjustment")
    @Column(name = "quantity_type")
    @Convert(converter = QuantityTypeConverter.class)
    private QuantityType quantityType ;
    
    /** 明细 */
    @ApiModelProperty(name = "明细")
    @Column(name = "item_description")
    private String itemDescription ;
    
    /** 收费利率 */
    @ApiModelProperty(name = "收费利率")
    @Column(name = "bill_rate")
    private BigDecimal billRate ;
    
    /** 单位 */
    @ApiModelProperty(name = "单位")
    @Column(name = "unit")
    private String unit ;
    /** 总额 */
    @ApiModelProperty(name = "总额")
    @Column(name = "total_amount")
    private BigDecimal totalAmount ;
}
