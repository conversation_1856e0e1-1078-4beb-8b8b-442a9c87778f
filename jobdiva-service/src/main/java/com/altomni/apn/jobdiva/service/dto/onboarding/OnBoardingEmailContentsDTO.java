package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.OperationStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class OnBoardingEmailContentsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long jobId;

    private String jobTitle;

    private Long talentId;

    private String talentName;

    private OperationStatus operationStatus;

    private String documentName;

    private String documentNameUploaded;

    private String documentNameSource;

    private String createdDate;

}
