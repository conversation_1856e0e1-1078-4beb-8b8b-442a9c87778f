package com.altomni.apn.jobdiva.service.vo.assignment;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel
@Data
public class AssignmentBriefInfoVO {

    protected Long id;

    protected Long startId;

    protected Float workingHours;

    protected String  province;

    private String zipCode;

    public AssignmentBriefInfoVO(Long id, Long startId, Float workingHours, String province, String zipCode)
    {
        this.id = id;
        this.startId = startId;
        this.workingHours = workingHours;
        this.province = province;
        this.zipCode = zipCode;
    }

}
