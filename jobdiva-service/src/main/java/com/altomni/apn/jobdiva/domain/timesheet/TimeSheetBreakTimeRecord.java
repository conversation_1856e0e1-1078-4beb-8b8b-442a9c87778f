package com.altomni.apn.jobdiva.domain.timesheet;

import com.altomni.apn.common.domain.AutoAbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetBreakTimeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetBreakTimeTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatusConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import org.hibernate.annotations.GenericGenerator;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * A record
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Data
@Table(name = "timesheet_breaktime_record")
public class TimeSheetBreakTimeRecord extends AutoAbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GenericGenerator(name = "RedisIdGenerator",
            strategy = "com.altomni.apn.jobdiva.config.idgenerator.RedisIdGenerator",
            parameters = { @org.hibernate.annotations.Parameter(name = "table", value = "timesheet_breaktime_record")})
    @GeneratedValue(generator = "RedisIdGenerator")
    @Access(AccessType.PROPERTY)
    private Long id;


    @Column(name = "tenant_id", nullable = false)
    @JsonIgnore
    private Long tenantId;

    @Column(name = "talent_id", nullable = false)
    @JsonIgnore
    private Long talentId;


    @Column(name = "work_date", nullable = false)
    @JsonIgnore
    private LocalDate workDate;


    @Column(name = "time", nullable = false)
    @JsonIgnore
    private String time;

    @Column(name = "week_day")
    private String  weekDay;


    @Column(name = "line_index")
    private Integer lineIndex;


    @Column(name = "assignment_id")
    private Long assignmentId;

    @Column(name = "status")
    @Convert(converter = TimeSheetStatusConverter.class)
    private TimeSheetStatus status;


    @Column(name = "break_time_type")
    @Convert(converter = TimeSheetBreakTimeTypeConverter.class)
    private TimeSheetBreakTimeType breakTimeType;





}
