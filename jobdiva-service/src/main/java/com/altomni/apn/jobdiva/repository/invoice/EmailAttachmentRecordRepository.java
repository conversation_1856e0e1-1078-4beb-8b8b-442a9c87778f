package com.altomni.apn.jobdiva.repository.invoice;

import com.altomni.apn.jobdiva.domain.invoice.TEmailAttachmentRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

/**
 * send email attachment record repository
 */
@Repository
public interface EmailAttachmentRecordRepository extends JpaRepository<TEmailAttachmentRecord, BigInteger> {
}
