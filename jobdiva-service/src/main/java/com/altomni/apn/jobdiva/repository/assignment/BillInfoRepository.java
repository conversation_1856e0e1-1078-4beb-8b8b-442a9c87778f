package com.altomni.apn.jobdiva.repository.assignment;

import com.altomni.apn.jobdiva.domain.assignment.AssignmentBillInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface BillInfoRepository extends JpaRepository<AssignmentBillInfo, Long> {

    void deleteByAssignmentId(Long assignment);

    AssignmentBillInfo findByAssignmentId(Long assignmentId);

    List<AssignmentBillInfo> findByAssignmentIdIn(List<Long> assignmentIds);

}
