package com.altomni.apn.jobdiva.service.dto.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.PaymentMethodType;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * used in create payment interface
 */
@ApiModel(description = "RecordPaymentCommonDTO")
@Data
public class RecordPaymentCommonDTO implements Serializable {

    private Timestamp paymentDate;

    private PaymentMethodType paymentMethod;

    private String note;
}
