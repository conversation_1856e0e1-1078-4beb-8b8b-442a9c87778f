package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.timesheet.TimesheetGoogleHoliday;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetGoogleHolidayRepository;
import com.altomni.apn.jobdiva.service.company.CompanyService;
import com.altomni.apn.jobdiva.service.dto.timesheet.TimesheetGoogleHolidayDTO;
import com.altomni.apn.jobdiva.service.timesheet.TimesheetGoogleHolidayService;
import com.altomni.apn.jobdiva.service.vo.timesheet.TimesheetGoogleHolidayVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TimesheetGoogleHolidayServiceImpl implements TimesheetGoogleHolidayService {

    @Resource
    TimeSheetGoogleHolidayRepository timeSheetGoogleHolidayRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Resource
    private EntityManager entityManager;

    @Resource
    CompanyService companyService;

    @Override
    public Page<TimesheetGoogleHolidayVO> findHolidayList(Pageable pageable) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select t.company_id as companyId,t.id,DATE_FORMAT(t.holiday_day,'%Y-%m-%d') as holidayDate,t.hours,c.`full_business_name` as companyName from timesheet_google_holiday t
                left join company c on c.id = t.company_id
                where t.tenant_id = :tenantId
                """);

        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (!Objects.isNull(order)) {
            dataSql.append(" order by t." + order.getProperty() + " " + order.getDirection());
        } else {
            dataSql.append(" order by t.holiday_day desc");

        }

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(countSql);

        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        countQuery.setParameter("tenantId", SecurityUtils.getTenantId());

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(TimesheetGoogleHolidayVO.class));
        dataQuery.setFirstResult((int) pageable.getOffset());
        dataQuery.setMaxResults(pageable.getPageSize());

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<TimesheetGoogleHolidayVO> holidayVOS = total > pageable.getOffset() ? dataQuery.getResultList() : new ArrayList<>();
        return new PageImpl<>(holidayVOS, pageable, total);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> insertRecord(List<TimesheetGoogleHolidayDTO> dtoList) {
        log.info("[insertRecord] insert holiday dto size:{}", dtoList.size());
        Map<String, Object> resultMap = new HashMap<>(16);
        resultMap.put("successNum", 0);
        resultMap.put("failNum", 0);
        resultMap.put("failList", new ArrayList<>());
        if (dtoList.isEmpty()) {
            return resultMap;
        }

        List<TimesheetGoogleHolidayDTO> holidayFailList = new ArrayList<>();
        List<TimesheetGoogleHolidayDTO> holidayDTOS = dtoList.stream().distinct().collect(Collectors.toList());
        List<TimesheetGoogleHoliday> holidays = new ArrayList<>();
        for (TimesheetGoogleHolidayDTO v : holidayDTOS) {
            if (checkHoliday(v)) {
                LocalDate date = DateUtil.stringToLocalDate(v.getHolidayDate(), "MM/dd/yyyy");
                TimesheetGoogleHoliday exist = timeSheetGoogleHolidayRepository.findByTenantIdAndHolidayDayAndCompanyId(SecurityUtils.getTenantId(), date, v.getCompanyId());
                if (exist == null) {
                    TimesheetGoogleHoliday bean = new TimesheetGoogleHoliday();
                    BeanUtils.copyProperties(v, bean);
                    bean.setTenantId(SecurityUtils.getTenantId());
                    bean.setHolidayDay(date);
                    bean.setHours(Double.valueOf(v.getHours()));
                    holidays.add(bean);
                } else {
                    holidayFailList.add(v);
                }
            } else {
                holidayFailList.add(v);
            }
        }

        log.info("[insertRecord] insert holidayDay size:{}", holidays.size());

        if (!holidays.isEmpty()) {
            timeSheetGoogleHolidayRepository.saveAll(holidays);
            log.info("[insertRecord] success insert holidayDay size:{}", holidays.size());
        }
        resultMap.put("successNum", holidays.size());
        resultMap.put("failNum", holidayDTOS.size() - holidays.size());
        resultMap.put("failList", holidayFailList);
        return resultMap;
    }

    public boolean checkHoliday(TimesheetGoogleHolidayDTO dto) {

        if (dto.getCompanyId() == null || StringUtils.isBlank(dto.getHolidayDate()) || StringUtils.isBlank(dto.getHours())) {
            return false;
        }

        if (!isNumber(dto.getHours())) {
            return false;
        }

        CompanyDTO companyDTO = companyService.getCompany(dto.getCompanyId()).getBody();
        if (companyDTO == null) {
            return false;
        }
        if (!companyDTO.getName().equals(dto.getCompanyName())) {
            return false;
        }

        try {
            if (!isValidDate(dto.getHolidayDate())) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }

        return true;
    }

    public static boolean isNumber(String str) {
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); // 判断小数点后2位的数字的正则表达式
        Matcher match = pattern.matcher(str);
        return match.matches();
    }

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    public static boolean isValidDate(String date) {
        try {
            LocalDate.parse(date, dateFormatter);
        } catch (DateTimeParseException e) {
            return false;
        }
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        if (null == id) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.JOBDIVA_INVOICE_NOT_FIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        timeSheetGoogleHolidayRepository.deleteById(id);
        log.info("[deleteById] delete by id :{}", id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(TimesheetGoogleHolidayDTO dto) {
        if (dto.getId() == null || dto.getCompanyId() == null || dto.getHolidayDate() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.MISSING_INPUT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        if (!checkHoliday(dto)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.PARAM_INPUT_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

        LocalDate date = DateUtil.stringToLocalDate(dto.getHolidayDate(), "MM/dd/yyyy");

        TimesheetGoogleHoliday exist = timeSheetGoogleHolidayRepository.findByTenantIdAndHolidayDayAndCompanyId(SecurityUtils.getTenantId(), date, dto.getCompanyId());
        if (null == exist) {
            exist = timeSheetGoogleHolidayRepository.findById(dto.getId()).orElse(null);
            if (null == exist) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_GOOGLE_HOLIDAY_DATE_EXISTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
            }
        } else {
            if (null != exist) {
                if (!exist.getId().equals(dto.getId())) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_GOOGLE_HOLIDAY_DATE_EXISTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
                }
            }
        }

        if (!exist.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.NOT_PERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }


        exist.setHolidayDay(date);
        exist.setCompanyId(dto.getCompanyId());
        exist.setHours(Double.valueOf(dto.getHours()));
        timeSheetGoogleHolidayRepository.save(exist);
        log.info("[modify] modify holiday day, param:{}", JSONUtil.toJsonStr(exist));
    }

    @Override
    public TimesheetGoogleHolidayVO exist(TimesheetGoogleHolidayDTO dto) {
        LocalDate date = DateUtil.stringToLocalDate(dto.getHolidayDate(), "MM/dd/yyyy");

        TimesheetGoogleHoliday exist = timeSheetGoogleHolidayRepository.findByTenantIdAndHolidayDayAndCompanyId(SecurityUtils.getTenantId(), date, dto.getCompanyId());
        TimesheetGoogleHolidayVO vo = new TimesheetGoogleHolidayVO();
        if (exist == null) {
            return null;
        } else {
            if (exist.getId().equals(dto.getId())) {
                return null;
            }
        }

        vo.setCompanyId(BigInteger.valueOf(exist.getCompanyId()));
        vo.setId(BigInteger.valueOf(exist.getId()));
        vo.setHolidayDate(DateUtil.generateDateString(exist.getHolidayDay()));
        vo.setHours(exist.getHours());
        return vo;
    }
}
