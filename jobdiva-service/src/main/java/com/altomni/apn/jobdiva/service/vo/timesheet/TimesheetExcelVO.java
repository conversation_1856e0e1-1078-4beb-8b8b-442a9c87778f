package com.altomni.apn.jobdiva.service.vo.timesheet;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class TimesheetExcelVO {

    @ExcelProperty(value = "Contractor Name", index = 0)
    private String talentName;

    @ExcelProperty(value = "Company Name", index = 1)
    private String companyName;

    @ExcelProperty(value = "RT", index = 2)
    private String regularHours;

    @ExcelProperty(value = "OT", index = 3)
    private String overTimes;

    @ExcelProperty(value = "DT", index = 4)
    private String doubleTimes;

    @ExcelProperty(value = "Week Ending On", index = 5)
    private String weekEnding;

    @ExcelProperty(value = "Fail Reason", index = 6)
    private String failReason;

}
