package com.altomni.apn.jobdiva.service.timesheet.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.config.email.EmailProperties;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.enumeration.user.CompletionStatus;
import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.user.LoginVM;
import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import com.altomni.apn.common.enumeration.enums.JobdivaAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetUserTokenStore;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.AES;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.jobdiva.config.env.ApplicationProperties;
import com.altomni.apn.jobdiva.config.env.JobdivaApiPromptProperties;
import com.altomni.apn.jobdiva.domain.onboarding.OnBoardingProcessHistories;
import com.altomni.apn.jobdiva.repository.process.OnBoardingProcessHistoriesRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TalentAssignmentRepository;
import com.altomni.apn.jobdiva.repository.timesheet.TimeSheetUserRepository;
import com.altomni.apn.jobdiva.service.assignment.AssignmentSyncToHrService;
import com.altomni.apn.jobdiva.service.dto.timesheet.ForgetPassDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.ForgetPassResetDTO;
import com.altomni.apn.jobdiva.service.dto.timesheet.ResetPasswordDTO;
import com.altomni.apn.jobdiva.service.mail.MailService;
import com.altomni.apn.jobdiva.service.talent.TalentService;
import com.altomni.apn.jobdiva.service.timesheet.TimeSheetUserService;
import com.altomni.apn.jobdiva.service.vo.assignment.AssignmentVO;
import com.altomni.apn.user.repository.permission.PermissionRoleRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class TimeSheetUserServiceImpl implements TimeSheetUserService {
    private final Logger log = LoggerFactory.getLogger(TimeSheetUserServiceImpl.class);

    public static final Integer MAXIMUM_LOGIN_FAILED_COUNT = 5;

    private final String TIME_SHEET_PASS_WORD_VALIDATE_KEY_PREFIX = "TIME_SHEET_PASS_WORD_VALIDATE_KEY_PREFIX";

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private TimeSheetUserRepository timeSheetUserRepository;

    @Resource
    private AssignmentSyncToHrService assignmentSyncToHrService;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private TalentAssignmentRepository assigmentRepository;

    @Resource
    private OnBoardingProcessHistoriesRepository onBoardingProcessHistoriesRepository;

    @Resource
    private PasswordEncoder passwordEncoder;


    @Resource
    protected TalentService talentService;

    @Resource
    private MailService mailService;

    @Resource
    private PermissionRoleRepository roleRepository;

    @Resource
    private EmailProperties properties;

    @Resource
    private TimesheetUserTokenStore timesheetUserTokenStore;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobdivaApiPromptProperties jobdivaApiPromptProperties;

    @Resource
    private AuthenticationManager authenticationManager;

    @Override
    public TimeSheetUser login(LoginVM loginVM) {
        String username = loginVM.getUsername();
        TimeSheetUser user = timeSheetUserRepository.findByUsername(username);
        if (ObjectUtil.isNull(user)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_LOGIN_USER_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (!user.isActivated()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_LOGIN_USER_NOT_ACTIVE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }
        if (TimeSheetUserType.CLIENT.equals(user.getUserType())) {
            Integer count = timeSheetUserRepository.countActiveClient(user.getId());
            if (count <= 0){
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_LOGIN_USER_NOT_ACTIVE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
            }
        }
        this.checkLockedAccount(username);
        String password = AES.decrypt(loginVM.getPassword(), applicationProperties.getSecret());
        try {
            UsernamePasswordAuthenticationToken usernamePasswordAuthenticationToken = new UsernamePasswordAuthenticationToken(username, password);
            Authentication authenticate = authenticationManager.authenticate(usernamePasswordAuthenticationToken);
            SecurityContextHolder.getContext().setAuthentication(authenticate);

            if (TimeSheetUserType.TALENT.equals(user.getUserType())) {
                try {
                    String photoUrl = talentService.findTalentPhotoUrl(SecurityUtils.getUserIdFromCreatedBy(user.getUid())).getBody();
                    if (ObjectUtil.isNotNull(photoUrl)) {
                        user.setPhotoUrl(photoUrl);
                    }
                } catch (Exception e) {
                    //ignore
                }
            }
            setPermissionFields(user);
            setCompletionStatus(user);
            user.setPassword(null);
            this.removeLoginFailedUser(username);
            String token = timesheetUserTokenStore.generateToken(user, applicationProperties.getActiveUserPeriod());
            this.saveActiveUser(user.getId(), user.getId(), token);
            CredentialDTO credentialDTO = new CredentialDTO();
            credentialDTO.setAccess_token(token);
            credentialDTO.setExpires_in(Instant.now().plus(applicationProperties.getActiveUserPeriod(), ChronoUnit.SECONDS).getNano());
            user.setCredential(credentialDTO);
            return user;
        } catch (Exception e) {
            loginVM.setUsername(username);
            log.error("[TimeSheetUserService.login] Login user {}, exception {}", loginVM, ExceptionUtils.getStackTrace(e));
            this.countLoginFailed(username);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_LOGIN_USER_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobdivaApiPromptProperties.getJobdivaService()));
        }

    }


    /**
     * If the user login failed for 5 times in 24 hours, this account will be locked for 30 minutes.
     * @param username username for login
     * <AUTHOR>
     */
    private void checkLockedAccount(String username){
        String lockedUserKey = String.format(RedisConstants.DATA_KEY_TIMESHEET_LOCKED_USER_ACCOUNT, username);
        if (commonRedisService.exists(lockedUserKey)){
            Long lockedInMinutes = commonRedisService.getTTL(lockedUserKey)/60 + 1;
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_LOGIN_USER_LOCK.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(lockedInMinutes),jobdivaApiPromptProperties.getJobdivaService()));
        }
    }

    private void removeLoginFailedUser(String username){
        String loginFailedUserKey = String.format(RedisConstants.DATA_KEY_TIMESHEET_COUNT_LOGIN_FAILED, username);
        commonRedisService.delete(loginFailedUserKey);
    }

    private void saveActiveUser(Long userIdFrom, Long userIdTo, String token){
        String activeUserKey = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, userIdFrom, userIdTo, token);
        commonRedisService.set(activeUserKey, "1", applicationProperties.getActiveUserPeriod());
    }

    private String countLoginFailed(String username){
        String loginFailedUserKey = String.format(RedisConstants.DATA_KEY_TIMESHEET_COUNT_LOGIN_FAILED, username);
        String message = null;
        if (commonRedisService.exists(loginFailedUserKey)  && Long.valueOf(commonRedisService.get(loginFailedUserKey)) < MAXIMUM_LOGIN_FAILED_COUNT){
            Long failedCount = commonRedisService.incr(loginFailedUserKey);
            if (failedCount < MAXIMUM_LOGIN_FAILED_COUNT){
                message = String.format("You have failed to login for %d times over the past %d hours. You have %d more chances left",
                        failedCount, RedisConstants.EXPIRE_IN_24_HOURS/3600, MAXIMUM_LOGIN_FAILED_COUNT  - failedCount);
            }else{
                String lockedUserKey = String.format(RedisConstants.DATA_KEY_TIMESHEET_LOCKED_USER_ACCOUNT, username);
                commonRedisService.set(lockedUserKey, "1", RedisConstants.EXPIRE_IN_30_MINUTES);
                message = String.format("You have failed to login for %d times over the past %d hours. Your account was locked for %d minutes",
                        MAXIMUM_LOGIN_FAILED_COUNT, RedisConstants.EXPIRE_IN_24_HOURS/3600, RedisConstants.EXPIRE_IN_30_MINUTES/60);
            }
        }else{
            commonRedisService.set(loginFailedUserKey, "1", RedisConstants.EXPIRE_IN_24_HOURS);
            message = String.format("You have failed to login for 1 time over the past %d hours. You have %d more chances left",
                    RedisConstants.EXPIRE_IN_24_HOURS/3600, MAXIMUM_LOGIN_FAILED_COUNT  - 1);
        }
        return message;
    }

    public CompletionStatus getCompletionStatus() {
        String uid = SecurityUtils.getUserUid();
        Long tenantId = SecurityUtils.getTenantId();
        List<OnBoardingProcessHistories> historyList = onBoardingProcessHistoriesRepository.findAllHistoriesByTalentId(SecurityUtils.getUserIdFromCreatedBy(uid), tenantId);
        if (CollectionUtil.isNotEmpty(historyList)) {
            Optional<OnBoardingProcessHistories> history = historyList.stream().filter(s -> CompletionStatus.INCOMPLETE.equals(s.getCompletionStatus())).findAny();
            if (history.isPresent()) {
                return CompletionStatus.INCOMPLETE;
            } else {
                return CompletionStatus.COMPLETE;
            }
        } else {
            return CompletionStatus.UNINVOLVED;
        }
    }

    private void setCompletionStatus(TimeSheetUser user) {
        //set onBoarding completion status
        List<OnBoardingProcessHistories> historyList = onBoardingProcessHistoriesRepository.findAllHistoriesByTalentId(SecurityUtils.getUserIdFromCreatedBy(user.getUid()), user.getTenantId());
        if (CollectionUtil.isNotEmpty(historyList)) {
            Optional<OnBoardingProcessHistories> history = historyList.stream().filter(s -> CompletionStatus.INCOMPLETE.equals(s.getCompletionStatus())).findAny();
            if (history.isPresent()) {
                user.setCompletionStatus(CompletionStatus.INCOMPLETE);
            } else {
                user.setCompletionStatus(CompletionStatus.COMPLETE);
            }
        } else {
            user.setCompletionStatus(CompletionStatus.UNINVOLVED);
        }
    }

    private void setPermissionFields(TimeSheetUser user) {
        List<AssignmentVO> assignmentList = assigmentRepository.findAllAssigmentInfoByTalentId(SecurityUtils.getUserIdFromCreatedBy(user.getUid()));
        if (CollectionUtils.isNotEmpty(assignmentList)) {
            user.setAllowSubmitTimeSheet(assignmentList.stream().anyMatch(AssignmentVO::getAllowSubmitTimeSheet));
            user.setAllowSubmitExpense(assignmentList.stream().anyMatch(AssignmentVO::getAllowSubmitExpense));
        }
    }

    @Override
    public TimeSheetUser resetPassword(ResetPasswordDTO resetPasswordDTO) {

        TimeSheetUser user = timeSheetUserRepository.findByUid(SecurityUtils.getUserUid());
        if (user == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RESET_PASSWORD_USER_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        String decryptedNewPassword = LoginUtil.validatePassword(resetPasswordDTO.getNewPassword(), applicationProperties.getSecret());

        String decryptedOldPassword = AES.decrypt(resetPasswordDTO.getOldPassword(), applicationProperties.getSecret());
        if (!passwordEncoder.matches(decryptedOldPassword, user.getPassword())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RESET_PASSWORD_NOT_MATCH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        user.setPassword(passwordEncoder.encode(decryptedNewPassword));
        user.setPassChanged(true);
        timeSheetUserRepository.saveAndFlush(user);
        assignmentSyncToHrService.buildTimeSheetUserSyncToHrMq(user);
        setPermissionFields(user);
        setCompletionStatus(user);
        user.setPassword(null);
        return user;
    }

    @Override
    public void forgetPass(ForgetPassDTO forgetPassDTO) {
        TimeSheetUser user = timeSheetUserRepository.findByEmail(forgetPassDTO.getEmail());
        if (user == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_FORGET_PW_INVALID_EMAIL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        String uuid = UUID.randomUUID() + "@" + user.getUid() + "-" + user.getUserType();
        commonRedisService.saveData(TIME_SHEET_PASS_WORD_VALIDATE_KEY_PREFIX + user.getUid() + "-" + user.getUserType(), uuid, 7200);

        String subject = "reset password";
        String link = forgetPassDTO.getResetPassPath() + "?code=" + uuid;
        String content = "<html lang=\"zh-cn\"><body><h2>Dear Candidate:</h2>" +
            "<p>To reset your password for the Intellipro Group Inc. Portal, please click the link below or copy and paste the link in your browser:</p>" +
            "<a href=\"" + link + "\">" + link + "</a></body></html>";

        List<String> emails = new ArrayList<>();
        emails.add(user.getEmail());

        MailVM mailVM = new MailVM(applicationProperties.getSupportSender(), emails, null, null, subject, content, null, true);
        try {
            LoginUtil.simulateLoginWithClient();
            mailService.sendHtmlMail(mailVM);
        } catch (Exception e) {
            log.error("error", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_FORGET_PW_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }


    }

    @Override
    public void resetPassForForget(ForgetPassResetDTO dto) {

        if (dto.getCode() == null || dto.getCode().trim().length() == 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RESET_FORGET_PW_CODE_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        String uuid = dto.getCode().split("@")[1];
        if (uuid == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RESET_FORGET_PW_INVALID_CODE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        String redisCode = commonRedisService.get(TIME_SHEET_PASS_WORD_VALIDATE_KEY_PREFIX + uuid);
        if (redisCode == null || !redisCode.equals(dto.getCode())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobdivaAPIMultilingualEnum.TIMESHEET_RESET_FORGET_PW_VALIDATE_CODE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobdivaApiPromptProperties.getJobdivaService()));
        }

        String decryptedNewPassword = LoginUtil.validatePassword(dto.getNewPass(), applicationProperties.getSecret());

        String[] uid = uuid.split("-");
        TimeSheetUser user = timeSheetUserRepository.findByUidAndUserType(StrUtil.toString(uid[0]), TimeSheetUserType.valueOf(uid[1]));
        String newPass = passwordEncoder.encode(decryptedNewPassword);
        user.setPassword(newPass);
        timeSheetUserRepository.saveAndFlush(user);
        assignmentSyncToHrService.buildTimeSheetUserSyncToHrMq(user);
    }

    @Override
    public TimeSheetUserDTO save(TimeSheetUserDTO timeSheetUserDTO) {
        timeSheetUserDTO.setTenantId(SecurityUtils.getTenantId());
        Set<Role> roles = timeSheetUserDTO.getRoles().stream().map(role -> roleRepository.getById(role.getId())).collect(Collectors.toSet());
        TimeSheetUser timeSheetUser = Convert.convert(TimeSheetUser.class, timeSheetUserDTO);
        timeSheetUser.setRoles(roles);
        TimeSheetUser save = timeSheetUserRepository.save(timeSheetUser);
        timeSheetUserDTO.setId(save.getId());
        assignmentSyncToHrService.buildTimeSheetUserSyncToHrMq(save);
        return timeSheetUserDTO;
    }


}
