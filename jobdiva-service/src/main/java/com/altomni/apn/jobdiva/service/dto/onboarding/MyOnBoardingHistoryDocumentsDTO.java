package com.altomni.apn.jobdiva.service.dto.onboarding;

import com.altomni.apn.jobdiva.domain.enumeration.onboarding.process.OperationStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class MyOnBoardingHistoryDocumentsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String documentName;

    private String assignedOnDate;

    private String md5Code;

    private String ip;

    private OperationStatus status;

    private String createdDate;

    private String createdByName;

}
