package com.altomni.apn.jobdiva.service.vo.assignment;

import com.altomni.apn.common.dto.activity.ChangeFieldDTO;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class AssignmentActivityVO {
    private Long assignmentId;

    private Long talentId;

    private Long jobId;

    private String candidateFullname;

    private LocalDate assigmentStartDate;

    private String recordType;

    private String operationType;

    private FullNameUserDTO createdBy;

    private String createdDate;

    private List<ChangeFieldDTO> changeFields;
}
