package com.altomni.apn.jobdiva.service.vo.invoice;

import com.altomni.apn.common.domain.enumeration.jobdiva.QuantityType;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

@ApiModel(description = "ContractorInvoiceTimesheetVO")
@AllArgsConstructor
@NoArgsConstructor
public class ContractorInvoiceTimesheetVO implements Serializable {

    private BigInteger id;

    private BigDecimal quantity;

    private String quantityType;

    private String itemDescription;

    private String billRate;

    private String unit;

    private BigDecimal totalAmount;

    private String currency;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getQuantityType() {
        return quantityType;
    }

    public void setQuantityType(String quantityType) {
        this.quantityType = StringUtils.isNotEmpty(quantityType) ? QuantityType.getNameFromDbValue(Integer.valueOf(quantityType)) : "";
    }

    public String getItemDescription() {
        return itemDescription;
    }

    public void setItemDescription(String itemDescription) {
        this.itemDescription = itemDescription;
    }

    public String getBillRate() {
        return billRate;
    }

    public void setBillRate(String billRate) {
        this.billRate = billRate;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
