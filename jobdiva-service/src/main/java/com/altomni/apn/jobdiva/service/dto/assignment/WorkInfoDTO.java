package com.altomni.apn.jobdiva.service.dto.assignment;

import com.altomni.apn.common.dto.address.LocationDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * A record
 */
@ApiModel(description = "record for expense")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
public class WorkInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long assignmentId;
    private LocationDTO location;
    private String  detailedAddress;
    private String  zipCode;
    private String  timeZone;

}
