package com.altomni.apn.jobdiva.service.vo.timesheet;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * A record
 */
@ApiModel(description = "record for week ending")
@Data
public class RecordHeadInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "Employee Name",index = 1)
    @ApiModelProperty(value = "talent name")
    private String talentName;

    @ApiModelProperty(value = "job title")
    private String jobTitle;

    @ApiModelProperty(value = "job id")
    private Long  jobId;

    @ApiModelProperty(value = "company name")
    private String companyName;

    @ApiModelProperty(value = "approver for client")
    private String approver;


    @ApiModelProperty(value = "approver for am")
    private String amApprover;

    @ApiModelProperty(value = "primary approver ")
    private String primaryAprover;

    private String invoiceNumber;

    private  Long talentId;

    public RecordHeadInfoVO() {
    }

    public RecordHeadInfoVO(String talentName, String jobTitle, Long jobId, String companyName) {
        this.talentName = talentName;
        this.jobTitle = jobTitle;
        this.jobId = jobId;
        this.companyName = companyName;
    }


    public RecordHeadInfoVO(String talentName,Long talentId, String jobTitle, Long jobId, String companyName,String approver,String primaryApprover,String amApprover) {
        this.talentName = talentName;
        this.jobTitle = jobTitle;
        this.jobId = jobId;
        this.talentId = talentId;
        this.companyName = companyName;
        this.approver = approver;
        this.amApprover = amApprover;
        this.primaryAprover = primaryApprover;
    }

    public String getApprover() {

        if(approver == null) return amApprover;
        return approver;
    }
}
