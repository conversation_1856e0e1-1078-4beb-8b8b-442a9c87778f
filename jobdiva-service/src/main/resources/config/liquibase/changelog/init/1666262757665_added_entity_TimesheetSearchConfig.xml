<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264813698-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="timesheet_search_config"/>
            </not>
        </preConditions>
        <createTable tableName="timesheet_search_config">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="uid" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="config" type="TEXT"/>
            <column name="name" type="VARCHAR(255)"/>
            <column name="is_default" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="search_type" type="TINYINT(3)"/>
            <column name="filter_type" type="TINYINT(3)"/>
            <column name="created_time" type="timestamp"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
