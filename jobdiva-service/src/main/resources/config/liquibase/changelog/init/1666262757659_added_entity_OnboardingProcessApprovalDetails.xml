<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263908374-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="onboarding_process_approval_details"/>
            </not>
        </preConditions>
        <createTable tableName="onboarding_process_approval_details">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="talent_recruitment_process_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="process_id" type="VARCHAR(32)">
                <constraints nullable="false"/>
            </column>
            <column name="history_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="approval_status" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="s3_link" type="VARCHAR(255)"/>
            <column name="ip" type="VARCHAR(20)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="index_opad_approval_status" tableName="onboarding_process_approval_details">
            <column name="approval_status"/>
        </createIndex>
        <createIndex indexName="index_opad_history_id" tableName="onboarding_process_approval_details">
            <column name="history_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
