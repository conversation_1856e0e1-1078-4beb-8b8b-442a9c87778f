<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264804200-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="timesheet_manager"/>
            </not>
        </preConditions>
        <createTable tableName="timesheet_manager">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="client_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="role" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="assignment_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="index_tm_assignment_id" tableName="timesheet_manager">
            <column name="assignment_id"/>
        </createIndex>
        <createIndex indexName="index_tm_client_id" tableName="timesheet_manager">
            <column name="client_id"/>
        </createIndex>
        <createIndex indexName="index_tm_role" tableName="timesheet_manager">
            <column name="role"/>
        </createIndex>
        <createIndex indexName="index_tm_tid_aid" tableName="timesheet_manager">
            <column name="talent_id"/>
            <column name="assignment_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
