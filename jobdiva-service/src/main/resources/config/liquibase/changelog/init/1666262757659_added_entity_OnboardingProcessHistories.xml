<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263918372-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="onboarding_process_histories"/>
            </not>
        </preConditions>
        <createTable tableName="onboarding_process_histories">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="talent_recruitment_process_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="process_id" type="VARCHAR(32)">
                <constraints nullable="false"/>
            </column>
            <column name="package_id" type="BIGINT"/>
            <column name="package_name" type="VARCHAR(255)"/>
            <column name="document_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="document_name" type="VARCHAR(260)"/>
            <column name="document_name_uploaded" type="VARCHAR(260)"/>
            <column name="s3_link_source" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="s3_link" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="action_required" type="TINYINT(3)"/>
            <column name="special_document" type="TINYINT(3)"/>
            <column name="document_type" type="TINYINT(3)"/>
            <column name="completion_status" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="ordering" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="onboarding_type" type="INT"/>
            <column defaultValueBoolean="true" name="activated" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="package_status" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_onboarding_process_histories_activated" tableName="onboarding_process_histories">
            <column defaultValueBoolean="true" name="activated"/>
        </createIndex>
        <createIndex indexName="idx_onboarding_process_histories_talent_recruitment_process_id"
                     tableName="onboarding_process_histories">
            <column name="talent_recruitment_process_id"/>
        </createIndex>
        <createIndex indexName="index_oph_process_id" tableName="onboarding_process_histories">
            <column name="process_id"/>
        </createIndex>
        <createIndex indexName="index_oph_tenant_id" tableName="onboarding_process_histories">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
