<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263208731-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="assignment_timesheet"/>
            </not>
        </preConditions>
        <createTable tableName="assignment_timesheet">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="assignment_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="timesheet_type" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="calculate_type" type="TINYINT(3)"/>
            <column name="frequency" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="week_ending" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="allow_submit_Timesheet" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="allow_submit_expense" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="instructions" type="TEXT"/>
        </createTable>

        <createIndex indexName="index_at_assignment_id" tableName="assignment_timesheet">
            <column name="assignment_id"/>
        </createIndex>
        <createIndex indexName="index_week_ending" tableName="assignment_timesheet">
            <column name="week_ending"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
