<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264746954-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="time_sheet_week_ending_record"/>
            </not>
        </preConditions>
        <createTable tableName="time_sheet_week_ending_record">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="record_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="job_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="manager" type="VARCHAR(50)"/>
            <column name="manager_id" type="BIGINT"/>
            <column name="am_approver" type="VARCHAR(50)"/>
            <column name="am_approver_id" type="BIGINT"/>
            <column name="primary_manager" type="VARCHAR(50)"/>
            <column name="primary_manager_id" type="BIGINT"/>
            <column name="am" type="VARCHAR(4000)"/>
            <column name="am_ids" type="VARCHAR(255)"/>
            <column name="instructions" type="TEXT"/>
            <column name="overtime_type" type="TINYINT(3)"/>
            <column name="is_except" type="BIT(1)"/>
            <column name="client_ids" type="VARCHAR(255)"/>
            <column name="allow_submit_timesheet" type="BIT(1)"/>
            <column name="job_title" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="company_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="full_name" type="VARCHAR(255)"/>
            <column defaultValueNumeric="0" name="calculate_type" type="TINYINT(3)"/>
            <column name="approved_date" type="timestamp"/>
            <column name="start_date" type="timestamp"/>
            <column name="end_date" type="timestamp"/>
            <column name="assignment_status" type="TINYINT(3)"/>
            <column name="assignment_id" type="BIGINT"/>
            <column name="work_date" type="timestamp"/>
            <column name="work_hours" type="DECIMAL(10, 2)"/>
            <column name="regular_hours" type="DECIMAL(10, 2)"/>
            <column name="over_time" type="DECIMAL(10, 2)"/>
            <column name="double_time" type="DECIMAL(10, 2)"/>
            <column name="total_hours" type="DECIMAL(10, 2)"/>
            <column name="submitted_date" type="timestamp"/>
            <column name="status" type="TINYINT(3)"/>
            <column name="time_sheet_type" type="TINYINT(3)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <addUniqueConstraint columnNames="talent_id, assignment_id, work_date"
                             constraintName="idx_unique_tswer_ti_ai_wd" tableName="time_sheet_week_ending_record"/>
        <createIndex indexName="idx_tswer_assignment_id" tableName="time_sheet_week_ending_record">
            <column name="assignment_id"/>
        </createIndex>
        <createIndex indexName="idx_tswer_assignment_stauts" tableName="time_sheet_week_ending_record">
            <column name="assignment_status"/>
        </createIndex>
        <createIndex indexName="idx_tswer_record_id" tableName="time_sheet_week_ending_record">
            <column name="record_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
