<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263163034-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="assignment_contribution"/>
            </not>
        </preConditions>
        <createTable tableName="assignment_contribution">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="assignment_id" type="BIGINT"/>
            <column name="user_id" type="BIGINT"/>
            <column name="user_role" type="TINYINT(3)"/>
            <column name="percentage" type="DECIMAL(10, 2)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
