<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264823188-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="timesheet_talent_assignment"/>
            </not>
        </preConditions>
        <createTable tableName="timesheet_talent_assignment">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="start_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="timestamp"/>
            <column name="end_date" type="timestamp"/>
            <column name="job_id" type="BIGINT"/>
            <column name="company_id" type="BIGINT"/>
            <column name="created_user_id" type="BIGINT"/>
            <column name="type" type="TINYINT(3)"/>
            <column name="status" type="TINYINT(3)"/>
            <column name="calculate_method" type="TINYINT(3)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="index_tta_company_id" tableName="timesheet_talent_assignment">
            <column name="company_id"/>
        </createIndex>
        <createIndex indexName="index_tta_job_id" tableName="timesheet_talent_assignment">
            <column name="job_id"/>
        </createIndex>
        <createIndex indexName="index_tta_status" tableName="timesheet_talent_assignment">
            <column name="status"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
