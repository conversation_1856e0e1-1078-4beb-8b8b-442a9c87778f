<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264727672-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="time_sheet_record"/>
            </not>
        </preConditions>
        <createTable tableName="time_sheet_record">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="assignment_id" type="BIGINT"/>
            <column name="work_date" type="timestamp"/>
            <column name="work_hours" type="DECIMAL(10, 2)"/>
            <column name="regular_hours" type="DECIMAL(10, 2)"/>
            <column name="over_time" type="DECIMAL(10, 2)"/>
            <column name="double_time" type="DECIMAL(10, 2)"/>
            <column name="week_day" type="VARCHAR(255)"/>
            <column name="total_hours" type="DECIMAL(10, 2)"/>
            <column name="submitted_date" type="timestamp"/>
            <column name="status" type="TINYINT(3)"/>
            <column name="time_sheet_type" type="TINYINT(3)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="index_tsr_assignment_id" tableName="time_sheet_record">
            <column name="assignment_id"/>
        </createIndex>
        <createIndex indexName="index_tsr_tenant_id_status" tableName="time_sheet_record">
            <column name="work_date"/>
        </createIndex>
        <createIndex indexName="index_tsr_ti_ai_wd" tableName="time_sheet_record">
            <column name="talent_id"/>
            <column name="assignment_id"/>
            <column name="work_date"/>
        </createIndex>
        <createIndex indexName="index_tsr_week_date" tableName="time_sheet_record">
            <column name="tenant_id"/>
            <column name="status"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
