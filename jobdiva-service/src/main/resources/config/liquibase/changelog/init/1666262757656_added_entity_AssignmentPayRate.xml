<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263197170-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="assignment_pay_rate"/>
            </not>
        </preConditions>
        <createTable tableName="assignment_pay_rate">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="assignment_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0.00" name="pay_rate" type="DECIMAL(10, 2)"/>
            <column name="currency" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="time_unit" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="pay_type" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="content_type" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_apr_ai_ct_pt" tableName="assignment_pay_rate">
            <column name="assignment_id"/>
            <column name="pay_type"/>
            <column name="content_type"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
