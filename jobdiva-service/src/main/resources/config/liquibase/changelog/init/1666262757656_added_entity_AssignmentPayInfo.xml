<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263185118-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="assignment_pay_info"/>
            </not>
        </preConditions>
        <createTable tableName="assignment_pay_info">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="assignment_id" type="BIGINT"/>
            <column name="is_except" type="BIT(1)"/>
            <column name="employment_category" type="TINYINT(3)"/>
            <column name="comments" type="TEXT"/>
            <column defaultValueNumeric="0.00" name="pay_rate" type="DECIMAL(10, 2)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
