<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263929529-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="onboarding_process_history_signatures"/>
            </not>
        </preConditions>
        <createTable tableName="onboarding_process_history_signatures">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="history_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="md5_code" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="s3_link" type="VARCHAR(255)"/>
        </createTable>

        <createIndex indexName="idx_onboarding_process_history_signatures_history_id"
                     tableName="onboarding_process_history_signatures">
            <column name="history_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
