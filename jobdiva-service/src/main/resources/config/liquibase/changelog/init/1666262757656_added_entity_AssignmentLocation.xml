<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263172746-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="assignment_location"/>
            </not>
        </preConditions>
        <createTable tableName="assignment_location">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="assignment_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="" name="city" type="VARCHAR(255)"/>
            <column defaultValue="" name="province" type="VARCHAR(255)"/>
            <column defaultValue="" name="province_code" type="VARCHAR(2)"/>
            <column defaultValue="" name="country" type="VARCHAR(255)"/>
            <column defaultValue="" name="country_code" type="VARCHAR(255)"/>
            <column defaultValue="" name="detailed_address" type="VARCHAR(255)"/>
            <column defaultValue="" name="zip_code" type="VARCHAR(255)"/>
            <column defaultValue="" name="time_zone" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
