<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264756493-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="timesheet_approve_record"/>
            </not>
        </preConditions>
        <createTable tableName="timesheet_approve_record">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="record_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="operator_id" type="BIGINT"/>
            <column name="role" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="record_type" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="opinion" type="TEXT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="index_tar_operator_id" tableName="timesheet_approve_record">
            <column name="operator_id"/>
        </createIndex>
        <createIndex indexName="index_tar_record_id" tableName="timesheet_approve_record">
            <column name="record_id"/>
        </createIndex>
        <createIndex indexName="index_tar_rt_r" tableName="timesheet_approve_record">
            <column name="record_type"/>
            <column name="role"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
