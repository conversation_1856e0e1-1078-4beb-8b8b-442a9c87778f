<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263153025-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="assignment_bill_info"/>
            </not>
        </preConditions>
        <createTable tableName="assignment_bill_info">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="assignment_id" type="BIGINT"/>
            <column name="contact_id" type="BIGINT"/>
            <column name="is_except" type="BIT(1)"/>
            <column name="overtime_type" type="TINYINT(3)"/>
            <column name="group_invoice_type" type="TINYINT(3)"/>
            <column name="group_invoice_content" type="VARCHAR(255)"/>
            <column name="group_invoice_content_type" type="TINYINT(3)"/>
            <column name="expense_invoice" type="TINYINT(3)"/>
            <column name="discount_type" type="TINYINT(3)"/>
            <column name="payment_terms" type="DECIMAL(10, 2)"/>
            <column name="net_bill_Rate" type="DECIMAL(10, 2)"/>
            <column name="net_overtime_rate" type="DECIMAL(10, 2)"/>
            <column name="net_doubletime_rate" type="DECIMAL(10, 2)"/>
            <column name="hourly_gm" type="DECIMAL(10, 2)"/>
        </createTable>

        <createIndex indexName="idx_abi_assignment_id" tableName="assignment_bill_info">
            <column name="assignment_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
