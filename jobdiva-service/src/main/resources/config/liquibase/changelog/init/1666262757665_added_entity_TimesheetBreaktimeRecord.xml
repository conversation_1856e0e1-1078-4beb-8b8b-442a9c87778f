<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264765915-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="timesheet_breaktime_record"/>
            </not>
        </preConditions>
        <createTable tableName="timesheet_breaktime_record">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="work_date" type="timestamp"/>
            <column defaultValue="" name="time" type="VARCHAR(255)"/>
            <column name="week_day" type="VARCHAR(255)"/>
            <column name="line_index" type="INT"/>
            <column name="assignment_id" type="BIGINT"/>
            <column name="status" type="TINYINT(3)"/>
            <column name="break_time_type" type="TINYINT(3)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_timesheet_breaktime_record_assignment_id" tableName="timesheet_breaktime_record">
            <column name="assignment_id"/>
        </createIndex>
        <createIndex indexName="idx_timesheet_breaktime_record_talent_id" tableName="timesheet_breaktime_record">
            <column name="talent_id"/>
        </createIndex>
        <createIndex indexName="idx_timesheet_breaktime_record_work_date" tableName="timesheet_breaktime_record">
            <column name="work_date"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
