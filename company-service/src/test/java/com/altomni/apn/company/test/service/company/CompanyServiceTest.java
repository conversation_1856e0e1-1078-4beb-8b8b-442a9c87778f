package com.altomni.apn.company.test.service.company;

//import com.altomni.apn.company.service.company.CompanyService;
import com.altomni.apn.company.test.common.company.CompanyCommon;
//import com.altomni.apn.company.web.rest.vm.company.CompanyClientVM;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class CompanyServiceTest
{
//    @Mock
//    private CompanyService companyService;
//
    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    public void testFindAllClientCompanies() {
//        CompanyClientVM companyClientVM = CompanyCommon.genTestData();
//        Mockito.when(companyService.findAllClientCompanies(null, null)).thenReturn(new PageImpl<>(List.of(companyClientVM)));
//        Page<CompanyClientVM> result = companyService.findAllClientCompanies(null, null);
//
//        Assertions.assertThat(result).isNotEmpty();
//        Assertions.assertThat(result.stream().findFirst().get().getId()).isEqualTo(companyClientVM.getId());
    }
}
