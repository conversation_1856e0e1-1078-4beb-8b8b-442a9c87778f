package com.altomni.apn.company.test.repository.company;

//import com.altomni.apn.company.domain.company.CompanyIntro;
//import com.altomni.apn.company.repository.company.CompanyIntroRepository;
//import com.altomni.apn.company.test.common.company.CompanyCommon;
//import com.altomni.apn.company.web.rest.vm.company.CompanyClientVM;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class CompanyIntroRepositoryTest {

//  @Mock
//  private CompanyIntroRepository companyIntroRepository;
//
//  @BeforeEach
//  public void init() {
//    MockitoAnnotations.openMocks(this);
//  }
//
  @Test
  public void testFindAll() {
//    CompanyClientVM companyClientVM = CompanyCommon.genTestData();
//    CompanyIntro companyIntro = new CompanyIntro();
//    companyIntro.setId(companyClientVM.getId());
//    Mockito.when(companyIntroRepository.findAll()).thenReturn(List.of(companyIntro));
//    List<CompanyIntro> result = companyIntroRepository.findAll();
//
//    Assertions.assertThat(result).isNotEmpty();
//    Assertions.assertThat(result.get(0).getId()).isEqualTo(companyIntro.getId());
  }
}
