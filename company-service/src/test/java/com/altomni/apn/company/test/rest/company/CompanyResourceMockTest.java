package com.altomni.apn.company.test.rest.company;

import com.altomni.apn.company.service.company.overview.CompanyOverviewService;
import com.altomni.apn.company.test.common.company.CompanyCommon;
import com.altomni.apn.company.web.rest.company.overview.CompanyOverviewResource;
//import com.altomni.apn.company.web.rest.vm.company.CompanyClientVM;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class CompanyResourceMockTest {
    @Mock
    private CompanyOverviewResource companyResource;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetAllCompanyClients() throws Exception {
//        CompanyClientVM companyClientVM = CompanyCommon.genTestData();
//        Mockito.when(companyResource.querySearchHistory()).thenReturn(new ResponseEntity<>(List.of(String), HttpStatus.OK));
//        ResponseEntity<List<CompanyClientVM>> response = companyResource.getAllCompanyClients(null, null);
//
//        Assertions.assertThat(response.getBody()).isNotEmpty();
//        Assertions.assertThat(response.getBody().get(0).getId()).isEqualTo(companyClientVM.getId());
    }
}
