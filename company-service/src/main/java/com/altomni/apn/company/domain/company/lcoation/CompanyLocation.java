package com.altomni.apn.company.domain.company.lcoation;

import com.altomni.apn.common.domain.ManualAbstractAuditingEntity;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.service.dto.location.CompanyLocationDTO;
import com.altomni.apn.company.service.dto.location.CompanyLocationMigrateDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
* A CompanyLocation.
* <AUTHOR>
* date:2023-04-13
*/
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "company_location")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CompanyLocation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "company_id", updatable = false)
    private Long accountCompanyId;

    @Column(name = "official_county")
    private String officialCounty;

    @Column(name = "official_country")
    private String officialCountry;

    @Column(name = "official_province")
    private String officialProvince;

    @Column(name = "official_city")
    private String officialCity;

    @Column(name = "original_loc")
    private String originalLoc;

    public static CompanyLocation fromCompanyLocationDTO(CompanyLocationDTO companyLocationDTO) {
        CompanyLocation companyLocation = new CompanyLocation();
        ServiceUtils.myCopyProperties(companyLocationDTO, companyLocation);
        companyLocation.setOriginalLoc(JsonUtil.toJson(companyLocationDTO.getLocation()));
        return companyLocation;
    }

    public static CompanyLocation fromCompanyLocationMigrateDTO(CompanyLocationMigrateDTO companyLocationDTO) {
        CompanyLocation companyLocation = new CompanyLocation();
        ServiceUtils.myCopyProperties(companyLocationDTO, companyLocation);
        return companyLocation;
    }

    public CompanyLocation(Long accountCompanyId, String originalLoc) {
        this.accountCompanyId = accountCompanyId;
        this.originalLoc = originalLoc;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompanyLocation that = (CompanyLocation) o;
        return originalLoc.equals(that.originalLoc);
    }

    @Override
    public int hashCode() {
        return Objects.hash(originalLoc);
    }
}
