package com.altomni.apn.company.service.dto.contact;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-18
*/
@Data
@ApiModel(description = "search for tenant's company contact")
public class CompanyContactTenantSearchDTO implements Serializable {

    @ApiModelProperty(value = "the maximum for result.")
    @NotNull
    private Integer limit;

    @ApiModelProperty(value = "the name for company contact.")
    private String name;

    @ApiModelProperty(value = "the active for company contact.")
    private Boolean active;

}
