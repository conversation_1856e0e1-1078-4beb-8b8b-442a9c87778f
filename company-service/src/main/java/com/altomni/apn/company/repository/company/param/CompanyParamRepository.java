package com.altomni.apn.company.repository.company.param;


import com.altomni.apn.common.domain.enumeration.company.CompanyType;
import com.altomni.apn.company.domain.company.param.CompanyParamConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyParamRepository extends JpaRepository<CompanyParamConfig, Long> {

    List<CompanyParamConfig> findAllByTenantId(Long tenantId);

    List<CompanyParamConfig> findAllByTenantIdAndType(Long tenantId, CompanyType companyType);

}
