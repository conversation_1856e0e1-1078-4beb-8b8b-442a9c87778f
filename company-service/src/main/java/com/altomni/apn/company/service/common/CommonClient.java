package com.altomni.apn.company.service.common;

import com.altomni.apn.common.dto.calendar.CalendarEventDTO;
import com.altomni.apn.common.vo.calendar.CalendarEventVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Component
@FeignClient(value = "common-service")
public interface CommonClient {

    @PostMapping("/common/api/v3/calendar-event")
    ResponseEntity<Void> createCalendarEvent(@Valid @RequestBody CalendarEventDTO calendarEventDto);

    @PutMapping("/common/api/v3/calendar-event")
    ResponseEntity<Void> updateCalendarEvent(@Valid @RequestBody CalendarEventDTO calendarEventDto);

    @GetMapping("/common/api/v3/calendar-event/{typeId}/{referenceId}")
    ResponseEntity<CalendarEventVO> getCalendarEventByTypeIdAndReferenceId(@PathVariable("typeId") Integer typeId, @PathVariable("referenceId") Long referenceId);

    @PostMapping("/common/api/v3/statistic/refreshApplicationStopStatistic")
    ResponseEntity<Integer> refreshApplicationStopStatistic(RefreshApplicationStopStatisticDTO dto);
}
