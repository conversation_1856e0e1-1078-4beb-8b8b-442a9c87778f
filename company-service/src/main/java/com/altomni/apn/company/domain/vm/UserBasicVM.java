package com.altomni.apn.company.domain.vm;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@NoArgsConstructor
@Entity
@Data
public class UserBasicVM implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    private String firstName;

    private String lastName;

    private boolean activated;

}
