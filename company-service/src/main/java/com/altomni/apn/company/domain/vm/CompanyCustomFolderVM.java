package com.altomni.apn.company.domain.vm;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class CompanyCustomFolderVM extends AbstractPermissionAuditingEntity implements Serializable {

    @Id
    private Long id;

    private String name;

    @Convert(converter = CategoryFolderTypeConverter.class)
    private CategoryFolderType category;

    private String note;

    private String creator;

    public CompanyCustomFolderVM(Long id, String name) {
        this.id = id;
        this.name = name;
    }
}
