package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Data
@Table(name = "company_purchase_order_detail")
@NoArgsConstructor
@AllArgsConstructor
public class CompanyPurchaseOrderDetail extends AbstractPermissionAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "order_number")
    private String orderNumber ;

    /** 币种id */
    @ApiModelProperty(name = "币种id")
    @Column(name = "currency_id")
    private Long currencyId ;

    @ApiModelProperty(name = "")
    @Column(name = "amount")
    private BigDecimal amount;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "balance")
    private BigDecimal balance ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "purchase_order_id")
    private Long purchaseOrderId ;
}