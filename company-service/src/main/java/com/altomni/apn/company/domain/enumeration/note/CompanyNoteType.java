package com.altomni.apn.company.domain.enumeration.note;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyNoteType enumeration.
 */
public enum CompanyNoteType implements ConvertedEnum<Integer> {
    PROGRESS_NOTE(10),
    CLIENT_NOTE(20);
    private final int dbValue;

    CompanyNoteType(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<CompanyNoteType, Integer> resolver =
        new ReverseEnumResolver<>(CompanyNoteType.class, CompanyNoteType::toDbValue);

    public static CompanyNoteType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
