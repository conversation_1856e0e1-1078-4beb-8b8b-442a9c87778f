package com.altomni.apn.company.service.contract;

import cn.hutool.json.JSONObject;
import com.altomni.apn.company.domain.contract.Contract;
import com.altomni.apn.company.vo.contract.ContractVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ContractService {

    List<ContractVO> saveContract(JSONObject jsonObject);

    ContractVO queryContract(Long contractId);

    Page<Contract> searchContractByCompanyId(Long companyId, Pageable pageable);

    List<ContractVO> toVo(List<Contract> contractList);

}
