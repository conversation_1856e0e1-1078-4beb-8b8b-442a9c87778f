package com.altomni.apn.company.domain.company.param;

import com.altomni.apn.common.domain.enumeration.company.CompanyType;
import com.altomni.apn.common.domain.enumeration.company.CompanyTypeConverter;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-17
*/
@Entity
@Table(name = "company_param_config")
@Data
public class CompanyParamConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "type")
    @Convert(converter = CompanyTypeConverter.class)
    private CompanyType type;

    @Column(name = "config")
    private String config;

    @Column(name = "tenant_id")
    private Long tenantId;

//    @Column(name = "versions")
//    @Convert(converter = CompanyConfigVersionConverter.class)
    private String versions;

}
