package com.altomni.apn.company.domain.contract;


import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A Contract.
 */
@Entity
@NoArgsConstructor
@Table(name = "company_contract_signer")
public class ContractSigner implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "contract_id")
    private Long contractId;

    @Column(name = "signer_id")
    private Long signerId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getContractId() { return contractId; }

    public ContractSigner contractId(Long contractId) {
        this.contractId = contractId;
        return this;
    }

    public void setContractId(Long contractId) { this.contractId = contractId; }

    public Long getSignerId() { return signerId; }

    public ContractSigner signerId(Long signerId) {
        this.signerId = signerId;
        return this;
    }

    public void setSignerId(Long signeeId) { this.signerId = signeeId; }

    public ContractSigner(Long contractId, Long signerId) {
        this.contractId = contractId;
        this.signerId = signerId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContractSigner that = (ContractSigner) o;
        return contractId.equals(that.contractId) && signerId.equals(that.signerId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contractId, signerId);
    }
}
