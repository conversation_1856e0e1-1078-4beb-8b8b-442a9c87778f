package com.altomni.apn.company.service.elastic;

import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.company.service.dto.es.NoteSearchDTO;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.time.Instant;
import java.util.List;

public interface EsCompanyDataService {

    HttpResponse searchFromCommonService(SearchGroup searchGroup, Pageable pageable) throws IOException;

    HttpResponse searchCacheAndRefreshCache(SearchGroup searchGroup, Pageable pageable) throws IOException;

    HttpResponse searchCache(String uuid, Pageable pageable) throws IOException;

    HttpResponse searchUnsignedCache(String uuid, Pageable pageable) throws IOException;

    HttpResponse searchCompanyCount() throws IOException;

    HttpResponse searchCompanyNote(NoteSearchDTO noteSearchDTO, Pageable pageable) throws IOException;

    HttpResponse searchCustomerMetric(Instant startTime, Instant endTime, List<Long> userIdList) throws IOException;

}
