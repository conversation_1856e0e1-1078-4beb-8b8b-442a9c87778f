package com.altomni.apn.company.repository.skipsubmit;

import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompany;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the SkipSubmitToAmCompany entity.
 * <AUTHOR>
 */
@Repository
public interface SkipSubmitToAmCompanyRepository extends JpaRepository<SkipSubmitToAmCompany, Long> {

    List<SkipSubmitToAmCompany> findAllByTenantId(Long tenantId);

}
