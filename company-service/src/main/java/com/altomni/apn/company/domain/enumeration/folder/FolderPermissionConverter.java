package com.altomni.apn.company.domain.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class FolderPermissionConverter extends AbstractAttributeConverter<FolderPermission, Integer> {
    public FolderPermissionConverter() {
        super(FolderPermission::toDbValue, FolderPermission::fromDbValue);
    }
}
