package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CompanyAccountProgressTypeConverter extends AbstractAttributeConverter<CompanyAccountProgressType, Integer> {
    public CompanyAccountProgressTypeConverter() {
        super(CompanyAccountProgressType::toDbValue, CompanyAccountProgressType::fromDbValue);
    }
}
