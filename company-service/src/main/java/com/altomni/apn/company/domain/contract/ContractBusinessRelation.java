package com.altomni.apn.company.domain.contract;


import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A Contract.
 */
@Entity
@Table(name = "company_contract_business_relation")
@NoArgsConstructor
@Data
public class ContractBusinessRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "contract_id")
    private Long contractId;

    @Column(name = "account_business_id")
    private Long accountBusinessId;

    public ContractBusinessRelation(Long contractId, Long accountBusinessId) {
        this.contractId = contractId;
        this.accountBusinessId = accountBusinessId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContractBusinessRelation that = (ContractBusinessRelation) o;
        return contractId.equals(that.contractId) && accountBusinessId.equals(that.accountBusinessId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contractId, accountBusinessId);
    }
}
