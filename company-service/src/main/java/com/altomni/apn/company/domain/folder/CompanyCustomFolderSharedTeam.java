package com.altomni.apn.company.domain.folder;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.company.domain.enumeration.folder.FolderPermission;
import com.altomni.apn.company.domain.enumeration.folder.FolderPermissionConverter;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A CompanyCustomFolderSharedTeam.
 */
@Entity
@Table(name = "company_custom_folder_shared_team")
@NoArgsConstructor
@Data
public class CompanyCustomFolderSharedTeam extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "folder_id")
    private Long folderId;

    @Column(name = "team_id")
    private Long teamId;

    @Column(name = "permission")
    @Convert(converter = FolderPermissionConverter.class)
    private FolderPermission permission;

    @Column(name = "ignore_team_user")
    private String ignoreTeamUser;

    public CompanyCustomFolderSharedTeam(Long folderId, Long teamId, FolderPermission permission) {
        this.folderId = folderId;
        this.teamId = teamId;
        this.permission = permission;
    }
}
