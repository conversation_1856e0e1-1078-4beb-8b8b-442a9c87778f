package com.altomni.apn.company.web.rest.company.location;

import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.service.company.location.CompanyLocationService;
import com.altomni.apn.company.service.dto.location.CompanyLocationDTO;
import com.altomni.apn.company.vo.location.CompanyLocationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * REST controller for managing CompanyLocation.
 */
@Api(tags = {"APN-Company-Location"})
@RestController
@RequestMapping("/api/v3/company")
@Slf4j
public class CompanyLocationResource {

    private static final String ENTITY_NAME = "Company Location";

    private static final String APPLICATION_NAME = "APN V3";

    @Resource
    private CompanyLocationService companyLocationService;

//    @PostMapping("/locations")
//    @ApiOperation(value = "Create a new company location")
//    public ResponseEntity<CompanyLocationVO> createCompanyLocation(@RequestBody @Valid CompanyLocationDTO locationDTO) {
////        log.info("[APN: Company Location @{}] REST request to create Company Location : {}", SecurityUtils.getUserId(), locationDTO);
//        return new ResponseEntity<>(companyLocationService.createCompanyLocation(locationDTO), HttpStatus.CREATED);
//
//    }

    @GetMapping("/locations/list")
    @ApiOperation(value = "Query company list")
    public ResponseEntity<List<CompanyLocationVO>> queryCompanyLocationList(@RequestParam("companyId") Long companyId) {
        log.info("[APN: Company Location @{}] REST request to query Company Location list : {}", SecurityUtils.getUserId(), companyId);
        return new ResponseEntity<>(companyLocationService.queryCompanyLocationList(companyId), HttpStatus.CREATED);
    }

    @GetMapping("/locations/{id}")
    @ApiOperation(value = "Query company detail")
    public ResponseEntity<LocationDTO> queryCompanyLocation(@PathVariable("id") Long id) {
        log.info("[APN: Company Location @{}] REST request to query Company Location detail : {}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(companyLocationService.queryCompanyLocation(id), HttpStatus.OK);
    }

    @PostMapping("/locations/check-compliance")
        @ApiOperation(value = "Check location compliance")
    public ResponseEntity<List<LocationDTO>> checkLocationCompliance(@RequestBody LocationDTO locationDTO) {
        return new ResponseEntity<>(companyLocationService.checkLocationCompliance(locationDTO), HttpStatus.OK);

    }

}
