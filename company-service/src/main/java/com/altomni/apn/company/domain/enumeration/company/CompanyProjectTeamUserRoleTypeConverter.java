package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class CompanyProjectTeamUserRoleTypeConverter extends AbstractAttributeConverter<CompanyProjectTeamUserRoleType, Integer> {
    public CompanyProjectTeamUserRoleTypeConverter() {
        super(CompanyProjectTeamUserRoleType::toDbValue, CompanyProjectTeamUserRoleType::fromDbValue);
    }
}
