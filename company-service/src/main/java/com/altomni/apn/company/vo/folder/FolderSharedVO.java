package com.altomni.apn.company.vo.folder;

import com.altomni.apn.company.domain.enumeration.folder.FolderPermission;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "sharedVO for folder")
public class FolderSharedVO implements Serializable {

    @ApiModelProperty(value = "The id for shared.")
    private Long id;

    @ApiModelProperty(value = "The permission for folder.")
    private FolderPermission permission;

    @ApiModelProperty(value = "The name for shared.")
    private String name;

}
