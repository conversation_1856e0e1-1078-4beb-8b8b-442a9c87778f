package com.altomni.apn.company.service.dto.salesLead;

import com.altomni.apn.company.aop.validation.ContributionSum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-25
*/
@Data
@ContributionSum
@ApiModel(description = "dto for client salesLead")
public class SalesLeadUpdateDTO implements Serializable {

    @ApiModelProperty(value = "The id for salesLead.")
    private Long id;

    @ApiModelProperty(value = "The accountProgress for salesLead.")
    private BigDecimal accountProgress;

    @ApiModelProperty(value = "The companyServiceTypes for salesLead.")
    @NotNull
    @UniqueElements
    private List<Long> companyServiceTypes;

    @ApiModelProperty(value = "The salesLead for company.")
    private List<Long> salesLeadClientContacts;

    @ApiModelProperty(value = "The am for salesLead.")
    @UniqueElements
    private List<Long> accountManagers;

    @ApiModelProperty(value = "The salesLeadsOwners for salesLead.")
    @NotNull
    @UniqueElements
    private List<SalesLeadAdministratorDTO> salesLeadsOwners;

    @ApiModelProperty(value = "The salesLeadsBdOwners for salesLead.")
    @NotNull
    @UniqueElements
    private List<SalesLeadAdministratorDTO> salesLeadsBdOwners;

    @ApiModelProperty(value = "The estimatedDealTime for salesLead.")
    private Instant estimatedDealTime;

    @ApiModelProperty(value = "The leadSource for salesLead.")
    private Long leadSource;
}