package com.altomni.apn.company.repository.company.folder;


import com.altomni.apn.company.domain.folder.CompanySearchFolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;


@Repository
public interface CompanySearchFolderRepository extends JpaRepository<CompanySearchFolder, Long> {

    List<CompanySearchFolder> findAllByPermissionUserIdAndName(Long userId, String name);

    Page<CompanySearchFolder> findAllByPermissionUserId(Long userId, Pageable pageable);

    Page<CompanySearchFolder> findAllByPermissionUserIdAndNameLike(Long userId, String name, Pageable pageable);

    Page<CompanySearchFolder> findAllByPermissionUserIdAndCreatedDateBetween(Long userId, Instant from, Instant to, Pageable pageable);

    Page<CompanySearchFolder> findAllByPermissionUserIdAndNameLikeAndCreatedDateBetween(Long userId, String name, Instant from, Instant to, Pageable pageable);

}
