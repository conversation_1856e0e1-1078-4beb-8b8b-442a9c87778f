package com.altomni.apn.company.vo.contract;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-18
*/
@AllArgsConstructor
@ApiModel(description = "Vo for contract signer")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class SignerVO implements Serializable {

    @ApiModelProperty(value = "the id for contract signer.")
    private Long signerId;

    @ApiModelProperty(value = "the name for contract signer.")
    private String signerName;

}
