package com.altomni.apn.company.domain.saleslead;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.Instant;

@Entity
@Data
@Table(name = "account_business")
public class SalesLeadReportBrief extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "name")
    private String name;

    @Column(name = "business_progress")
    private Integer businessProgress;

    @Column(name = "company_id", nullable = false)
    private Long accountCompanyId;

    @Column(name = "lead_source")
    private Long leadSource;

    @Column(name = "estimated_deal_time", nullable = true)
    private Instant estimatedDealTime;

}