package com.altomni.apn.company.service.dto.location;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "location for company")
public class CompanyLocationMigrateDTO implements Serializable {

    private Long accountCompanyId;

    private String officialCounty;

    private String officialCountry;

    private String officialProvince;

    private String officialCity;

    private String originalLoc;

}
