package com.altomni.apn.company.domain.business;


import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A ContactTagRelation.
 */
@Entity
@Table(name = "company_contact_tag_relation")
@NoArgsConstructor
@Data
public class ContactTagRelation extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    /**
     * crm contactId
     */
    @Column(name = "contact_id")
    private Long contactId;

    @Column(name = "company_id")
    private  Long accountCompanyId;

    @Column(name = "tag")
    private String tag;

}
