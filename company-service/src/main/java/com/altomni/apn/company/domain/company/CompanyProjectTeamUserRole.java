package com.altomni.apn.company.domain.company;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.company.domain.enumeration.company.CompanyProjectTeamUserRoleType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * A ProjectTeamUser.
 * <AUTHOR>
 */
@Entity
@Table(name = "company_project_team_user_role")
public class CompanyProjectTeamUserRole extends AbstractPermissionAuditingEntity implements Serializable
{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "team user id", required = true)
    @NotNull
    @Column(name = "team_user_id", nullable = false)
    private Long teamUserId;

    @JsonIgnore
    @ApiModelProperty(value = "user' role.")
    @Column(name = "role")
    private CompanyProjectTeamUserRoleType userRole;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTeamUserId() {
        return teamUserId;
    }

    public CompanyProjectTeamUserRole teamUserId(Long teamUserId) {
        this.teamUserId = teamUserId;
        return this;
    }

    public void setTeamUserId(Long teamUserId) {
        this.teamUserId = teamUserId;
    }

    public CompanyProjectTeamUserRoleType getUserRole() {
        return userRole;
    }

    public CompanyProjectTeamUserRole userRole(CompanyProjectTeamUserRoleType userRole) {
        this.userRole = userRole;
        return this;
    }

    public void setUserRole(CompanyProjectTeamUserRoleType userRole) {
        this.userRole = userRole;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CompanyProjectTeamUserRole projectTeamUserRole = (CompanyProjectTeamUserRole) o;
        if (projectTeamUserRole.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), projectTeamUserRole.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "CompanyProjectTeamUserRole{" +
                "id=" + id +
                ", teamUserId=" + teamUserId +
                ", userRole=" + userRole +
                '}';
    }
}
