package com.altomni.apn.company.domain.job;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;


/**
 * A Job location Company Brief Relationship.
 */
@ApiModel(description = "JobLocation entity. It comes from parser parsing JD, or user manually create job using ATS.")
@Entity
@Data
@Table(name = "job_location")
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobLocationCompanyBrief implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "job_id", updatable = false)
    private Long jobId;

    @Column(name = "official_county")
    private String officialCounty;

    @Column(name = "official_country")
    private String officialCountry;

    @Column(name = "official_province")
    private String officialProvince;

    @Column(name = "official_city")
    private String officialCity;

    @Column(name = "original_loc")
    private String originalLoc;

}
