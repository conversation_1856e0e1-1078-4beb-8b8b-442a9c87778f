package com.altomni.apn.company.service.dto.folder;

import com.altomni.apn.company.domain.enumeration.folder.FolderPermission;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "sharedDto for folder")
public class FolderSharedDTO implements Serializable {

    @ApiModelProperty(value = "The id for shared.")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "The permission for folder.")
    @NotNull
    private FolderPermission permission;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FolderSharedDTO that = (FolderSharedDTO) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
