package com.altomni.apn.company.web.rest.company;


import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.dto.company.BriefCompanyDTO;
import com.altomni.apn.common.dto.company.ClientContactCompany;
import com.altomni.apn.common.dto.company.ICompanyTeamUser;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.company.CompanyClientInfoVO;
import com.altomni.apn.common.vo.company.CompanyClientInvoicingInfoVO;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.company.domain.company.CompanyContact;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.service.company.CompanyClientInfoService;
import com.altomni.apn.company.service.company.CompanyClientInvoicingInfoService;
import com.altomni.apn.company.service.company.CompanyMergeService;
import com.altomni.apn.company.service.company.CompanyService;
import com.altomni.apn.company.service.dto.CompanyClientInfoDTO;
import com.altomni.apn.company.service.dto.CompanyClientInvoicingInfoDTO;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.company.service.dto.CompanyPurchaseOrderDTO;
import com.altomni.apn.company.service.elastic.impl.EsFillerCompanyServiceImpl;
import com.altomni.apn.company.service.job.JobClient;
import com.altomni.apn.company.service.store.StoreService;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.company.vo.company.CompanyPurchaseOrderVO;
import com.altomni.apn.company.web.rest.dto.FindAllByNamesDTO;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectSearchVM;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectVM;
import com.altomni.apn.company.web.rest.vm.company.CompanyVO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for managing Company.
 */
@Api(tags = {"APN-Company"})
@RestController
@RequestMapping("/api/v3")
public class CompanyResource {

    private final Logger log = LoggerFactory.getLogger(CompanyResource.class);

    private static final String ENTITY_NAME = "company";

    private static final String APPLICATION_NAME = "APN V3";

    private final CompanyService companyService;

    private final StoreService storeService;

    private final CompanyClientInfoService companyClientInfoService;

    private final CompanyClientInvoicingInfoService companyClientInvoicingInfoService;

    private final CompanyRepository companyRepository;

    private final CompanyMergeService companyMergeService;

    @Resource
    private JobClient jobClient;

    @Resource
    private EsFillerCompanyServiceImpl esFillerCompanyService;

    public CompanyResource(CompanyService companyService, StoreService storeService,
                           CompanyClientInfoService companyClientInfoService, CompanyClientInvoicingInfoService companyClientInvoicingInfoService, CompanyRepository companyRepository, CompanyMergeService companyMergeService) {
        this.companyService = companyService;
        this.storeService = storeService;
        this.companyClientInfoService = companyClientInfoService;
        this.companyClientInvoicingInfoService = companyClientInvoicingInfoService;
        this.companyRepository = companyRepository;
        this.companyMergeService = companyMergeService;
    }


// TODO: 2024/4/19  move to crm

    @PostMapping("/company/search/bd-report")
    public ResponseEntity<List<CompanyProspectVM>> searchAllCompanyForBDReport(@RequestParam Instant fromDate, @RequestParam Instant toDate, @RequestBody CompanyProspectSearchVM searchVM, @PageableDefault @SortDefault(sort = "id", direction = Sort.Direction.ASC) Pageable pageable){
        log.info("[APN: Company @{}] REST request to search all company prospect list ", SecurityUtils.getUserId());
        Page<CompanyProspectVM> companyProspects = companyService.searchAllCompaniesForBDReport(fromDate, toDate, searchVM, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(companyProspects, "/api/v3/company/search/bd-report");
        return ResponseEntity.ok().headers(headers).body(companyProspects.getContent());
    }

    @GetMapping("/company/{id}")
    public ResponseEntity<CompanyDTO> getCompany(@PathVariable("id") Long id) {
        log.info("[APN: Company info] REST request to get a company by id: {}", id);
        CompanyDTO companyDTO = companyService.getCompany(id);
        return ResponseEntity.ok(companyDTO);
    }

    @GetMapping("/company/{id}/company-contacts")
    public ResponseEntity<List<CompanyContact>> getCompanyContacts(@PathVariable("id") Long id) {
        log.info("[APN: Company info] REST request to get all company contacts by company id: {}", id);
        List<CompanyContact> companyContacts = companyService.getCompanyContacts(id);
        return ResponseEntity.ok(companyContacts);
    }

    @PostMapping("/company/find-by-ids")
    public ResponseEntity<List<CompanyDTO>> findALLByIds(@RequestBody List<Long> companyIds) {
        log.info("[APN: Company info] REST request to find all company by ids: {}", companyIds);
        List<CompanyDTO> companyDTOList = companyService.findALLByIds(companyIds);
        return ResponseEntity.ok(companyDTOList);
    }

    @PostMapping("/company/find-by-names")
    public ResponseEntity<List<ClientContactCompany>> findALLByNames(@RequestBody FindAllByNamesDTO dto) {
        log.info("[APN: Company info] REST request to find all company by names: {} ,excludeTalentId :{}", dto.getCompanyNames(), dto.getExcludeTalentId());
        List<String> companyNames = dto.getCompanyNames();
        Long excludeTalentId = dto.getExcludeTalentId();
        if (companyNames == null) {
            companyNames = new ArrayList<>();
        }
        List<ClientContactCompany> companyProspectVOList = companyService.findALLByNames(companyNames, excludeTalentId);
        return ResponseEntity.ok(companyProspectVOList);
    }

    /**
     * search company by tenantId
     * @return
     */

    @GetMapping("/tenantCompany")
    @Timed
    public ResponseEntity<List<AccountCompanyVO>> getTenantCompany() {
        log.info("[APN: Company @{}] REST request to get tenant companies list.", SecurityUtils.getUserId());
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(companyService.findAllByTenantId()));
    }

    /**
     * only use for talent-service get Portal Account permission
     */
    @GetMapping("/company/count-company-account-manager")
    @Timed
    public ResponseEntity<Integer> countCompanyAccountManager(@RequestParam("talentId") Long talentId, @RequestParam("userId") Long userId){
        log.info("[APN: Company @{}] REST request to get count company account manager, talentId = {}, userId = {}", SecurityUtils.getUserId(), talentId, userId);
        return ResponseEntity.ok(companyService.countCompanyAccountManager(talentId, userId));
    }

    /**
     * only use for job-service to get company teamUsers and AM
     */
    @GetMapping("/company/application-users/{companyId}")
    @Timed
    public ResponseEntity<List<AssignedUserDTO>> getApplicationUsers(@PathVariable("companyId") Long companyId) {
        log.info("[APN: Company @{}] REST request to get application users by companyId , companyId = {}", SecurityUtils.getUserId(), companyId);
        return ResponseEntity.ok(companyService.getApplicationUsers(companyId));
    }

    @GetMapping("/company/{companyId}/team-users")
    @Timed
    public ResponseEntity<List<ICompanyTeamUser.CompanyTeamUserDTO>> getCompanyTeamUsersByCompanyId(@PathVariable("companyId") Long companyId, @RequestParam(value = "salesLeadId", required = false) Long salesLeadId) {
        log.info("[APN: Company @{}] REST request to get team users by companyId , companyId = {}", SecurityUtils.getUserId(), companyId);
        return ResponseEntity.ok(companyService.getTeamUsersByCompanyId(companyId, salesLeadId));
    }

    @ApiOperation("Get company names by job Id list")
    @PostMapping("/company/job-to-company")
    @Timed
    public ResponseEntity<Map<Long, String>> getCompanyNamesByJobIds(@RequestBody List<Long> jobIds) {
        log.info("[APN: Company @{}] REST request to get company names by job ids {}", SecurityUtils.getUserId(), jobIds);
        return ResponseEntity.ok(companyService.getCompanyNamesByJobIds(jobIds));
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }

    /***
     * activity change field function : to get the name by Id;
     * @param companyId
     * @return
     */
    @GetMapping("/company/company-name/{companyId}/")
    @Timed
    public ResponseEntity<String> getCompanyNameByCompanyId(@PathVariable("companyId") Long companyId) {
        log.info("[APN: Company @{}] REST request to get team users by companyId , companyId = {}", SecurityUtils.getUserId(), companyId);
        return ResponseEntity.ok(companyService.getCompanyNameById(companyId));
    }

    @GetMapping("/company/tenant-company-name")
    @Timed
    public ResponseEntity<List<CompanyVO>> getTenantCompanyByName(@RequestParam(value = "name",required = false) String name) {
        log.info("[APN: Company @{}] REST request to get tenant companies list, page = {}, name={}", SecurityUtils.getUserId(),name);
        List<CompanyVO> page = companyService.findAllByTenantIdAndName(name);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(page));
    }

    @GetMapping("/company/list")
    public ResponseEntity<List<AccountCompanyVO>> getAllClientCompanyList(@RequestParam(value = "active", required = false) AccountCompanyStatus active, @RequestParam(value = "limit", required = false) Integer limit) {
        log.info("[APN: Company @{}] REST request to get company list, limit:{}", SecurityUtils.getUserId(), limit);
        return new ResponseEntity<>(companyService.getAllClientCompanyList(active, limit), HttpStatus.OK);
    }

    /***
     * Job-Service: Get brief company info for employer type company
     * @param tenantId
     * @return
     */
    @GetMapping("/company/brief-company-list")
    public ResponseEntity<List<BriefCompanyDTO>> getAllClientCompanyList(@RequestParam(value = "tenantId") Long tenantId) {
        log.info("[APN: Company @{}] REST request to get brief company list, tenantId:{}", SecurityUtils.getUserId(), tenantId);
        return new ResponseEntity<>(companyService.findBriefCompanyByTenantId(tenantId), HttpStatus.OK);
    }

    /***
     * Job-Service: Get signal company purchase order info
     * @param companyId
     * @return
     */
    @GetMapping("/company/purchase-order-list/{companyId}")
    public ResponseEntity<List<CompanyPurchaseOrderVO>> getPurchaseOrderList(@PathVariable("companyId") Long companyId) {
        log.info("[APN: Company purchase order @{}] REST request to get company purchase order info list, companyId:{}", SecurityUtils.getUserId(), companyId);
        return new ResponseEntity<>(companyService.getPurchaseOrderList(companyId), HttpStatus.OK);
    }

    /***
     * Job-Service: Get company purchase order info
     * @param companyIdList
     * @return
     */
    @GetMapping("/company/purchase-order-list/companyIds/{companyIdList}")
    public ResponseEntity<List<CompanyPurchaseOrderVO>> getCompanyPurchaseOrderList(@PathVariable("companyIdList") List<Long> companyIdList) {
        log.info("[APN: Company purchase order @{}] REST request to get company purchase order info list, companyId:{}", SecurityUtils.getUserId(), companyIdList);
        return new ResponseEntity<>(companyService.getCompanyPurchaseOrderList(companyIdList), HttpStatus.OK);
    }

    @PostMapping("/company/purchase-order")
    @NoRepeatSubmit
    public ResponseEntity createCompanyPurchaseOrder(@RequestBody CompanyPurchaseOrderDTO companyPurchaseOrderDTO) {
        log.info("[APN: Company purchase order ] REST add purchase order list: {}", companyPurchaseOrderDTO);
        companyService.createCompanyPurchaseOrder(companyPurchaseOrderDTO);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @PutMapping("/company/purchase-order")
    @NoRepeatSubmit
    public ResponseEntity modifyCompanyPurchaseOrder(@RequestBody CompanyPurchaseOrderDTO companyPurchaseOrderDTO) {
        log.info("[APN: Company purchase order ] REST modify purchase order list: {}", companyPurchaseOrderDTO);
        companyService.modifyCompanyPurchaseOrder(companyPurchaseOrderDTO);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /***
     * Job-Service: Get company client info
     * @param companyId
     * @return
     */
    @GetMapping("/company/client-list/{companyId}")
    public ResponseEntity<List<CompanyClientInfoVO>> getCompanyClientInfoList(@PathVariable("companyId") Long companyId) {
        log.info("[APN: Company client info @{}] REST request to get company client info list, companyId:{}", SecurityUtils.getUserId(), companyId);
        return new ResponseEntity<>(companyClientInfoService.findByCompanyId(companyId), HttpStatus.OK);
    }

    /***
     * Job-Service: Get company client info
     * @param id
     * @return
     */
    @GetMapping("/company/client-list/id/{id}")
    public ResponseEntity<CompanyClientInfoVO> getCompanyClientInfoById(@PathVariable("id") Long id) {
        log.info("[APN: Company client info @{}] REST request to get company client info by id, companyId:{}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(companyClientInfoService.getCompanyClientInfoById(id), HttpStatus.OK);
    }

    @PostMapping("/company/client-list")
    @NoRepeatSubmit
    public ResponseEntity createCompanyClientInfo(@RequestBody CompanyClientInfoDTO companyClientInfoDTO) {
        log.info("[APN: Company client info ] REST add client info list: {}", companyClientInfoDTO);
        companyClientInfoService.save(companyClientInfoDTO);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @PutMapping("/company/client-list")
    @NoRepeatSubmit
    public ResponseEntity modifyCompanyClientInfo(@RequestBody CompanyClientInfoDTO companyClientInfoDTO) {
        log.info("[APN: Company client info ] REST modify client info list: {}", companyClientInfoDTO);
        companyClientInfoService.modify(companyClientInfoDTO);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @DeleteMapping("/company/client-list/delete/{id}")
    @NoRepeatSubmit
    public ResponseEntity deleteCompanyClientInfo(@PathVariable Long id) {
        log.info("[APN: Company client info ] REST delete client info: {}", id);
        companyClientInfoService.delete(id);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    /***
     * Job-Service: Get company client invoicing info
     * @param companyId
     * @return
     */
    @GetMapping("/company/invoicing/client-list/{companyId}")
    public ResponseEntity<List<CompanyClientInvoicingInfoVO>> getCompanyInvoicingClientInfoList(@PathVariable("companyId") Long companyId) {
        log.info("[APN: Company client info @{}] REST request to get company invoicing client info list, companyId:{}", SecurityUtils.getUserId(), companyId);
        return new ResponseEntity<>(companyClientInvoicingInfoService.findByCompanyId(companyId), HttpStatus.OK);
    }

    @GetMapping("/company/invoicing/client/{id}")
    public ResponseEntity<CompanyClientInvoicingInfoVO> getInvoicingClientInfoById(@PathVariable("id") Long id) {
        log.info("[APN: Company client info @{}] REST request to get company invoicing client info list, id:{}", SecurityUtils.getUserId(), id);
        return new ResponseEntity<>(companyClientInvoicingInfoService.findById(id), HttpStatus.OK);
    }

    @GetMapping("/company/invoicing/preference")
    public ResponseEntity<String> getInvoicingPreference() {
        log.info("[APN: Company client info ] REST get invoicing preference: {}", SecurityUtils.getUserId());
        return new ResponseEntity<>(companyClientInvoicingInfoService.getInvoicingPreference(), HttpStatus.OK);
    }

    @PostMapping("/company/invoicing/client")
    @NoRepeatSubmit
    public ResponseEntity createCompanyInvoicingClientInfo(@RequestBody CompanyClientInvoicingInfoDTO companyClientInvoicingInfoDTO) {
        log.info("[APN: Company client info ] REST add invoicing client info list: {}", companyClientInvoicingInfoDTO);
        companyClientInvoicingInfoService.save(companyClientInvoicingInfoDTO);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @PutMapping("/company/invoicing/client")
    @NoRepeatSubmit
    public ResponseEntity modifyCompanyInvoicingClientInfo(@RequestBody CompanyClientInvoicingInfoDTO companyClientInvoicingInfoDTO) {
        log.info("[APN: Company client info ] REST modify invoicing client info list: {}", companyClientInvoicingInfoDTO);
        companyClientInvoicingInfoService.modify(companyClientInvoicingInfoDTO);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @DeleteMapping("/company/invoicing/client/delete/{id}")
    @NoRepeatSubmit
    public ResponseEntity deleteCompanyInvoicingClientInfo(@PathVariable Long id) {
        log.info("[APN: Company client info ] REST delete invoicing client info: {}", id);
        companyClientInvoicingInfoService.delete(id);
        return ResponseEntity.status(HttpStatus.OK).build();
    }

    @PutMapping("/company/sync-to-hr/{companyId}")
    public void updateCompanyNeedSyncToHr(@PathVariable("companyId") Long companyId) {
        companyRepository.updateCompanyNeedSyncToHr(companyId);
    }

    @PutMapping("/company/merge/{sourceCompanyId}/{targetCompanyId}")
    public ResponseEntity<Void> mergeCompany(@PathVariable("sourceCompanyId") Long sourceCompanyId, @PathVariable("targetCompanyId") Long targetCompanyId) {
        companyMergeService.doMerge(sourceCompanyId, targetCompanyId);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/business-name")
    public ResponseEntity<Void> innerModifyBusinessName(@PathVariable("id") Long id, @RequestBody String businessName) {
        companyMergeService.modifyCompanyBusinessName(id, businessName);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * 查询CRM公司信息
     * @param ids
     * @return
     */
    @PostMapping("/company/find-by-ids-from-crm")
    @ApiOperation(value = "query company by ids from crm")
    public ResponseEntity<List<AccountCompanyVO>> queryCompanyByIds(@RequestBody List<Long> ids) {
        log.info("[APN: Company @{}] REST request to query account company by ids from CRM, : {}", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(companyService.queryCRMCompanyByIds(ids), HttpStatus.OK);
    }


}
