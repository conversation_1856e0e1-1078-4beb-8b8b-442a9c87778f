package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.company.domain.enumeration.company.CompanyClientLevelType;
import com.altomni.apn.company.domain.enumeration.company.CompanyClientLevelTypeConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

@Data
@Entity
public class CompanyReportBrief extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The url link to the logo")
    @Column(name = "logo")
    private String logo;

    @ApiModelProperty(required = true, value = "Company name")
    @NotNull
    @Column(name = "name", nullable = false, unique = true)
    private String name;

    @Convert(converter = CompanyClientLevelTypeConverter.class)
    @Column(name = "client_level_type")
    private CompanyClientLevelType companyClientLevelType;

    @ApiModelProperty(name = "Whether the company is still in business. Default is true.")
    @Column(name = "active")
    private Boolean active = true;

    @ApiModelProperty(value = "tenant id")
    @Column(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty(value = "last successful sync to ES")
    @Column(name = "last_sync_time")
    private Instant lastSyncTime;

}