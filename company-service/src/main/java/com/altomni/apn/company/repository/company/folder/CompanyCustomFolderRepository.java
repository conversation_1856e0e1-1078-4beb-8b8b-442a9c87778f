package com.altomni.apn.company.repository.company.folder;


import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.folder.CompanyCustomFolder;
import com.altomni.apn.company.domain.vm.CompanyCustomFolderVM;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CompanyCustomFolderRepository extends JpaRepository<CompanyCustomFolder, Long> {

    List<CompanyCustomFolder> findAllByPermissionUserIdAndName(Long userId, String name);

    List<CompanyCustomFolder> findAllByPermissionUserIdAndNameAndIdNot(Long userId, String name, Long id);

    @Query(value = "SELECT new com.altomni.apn.company.domain.vm.CompanyCustomFolderVM(f.id, f.name) FROM CompanyCustomFolder f WHERE f.permissionUserId = ?1 AND f.category = ?2")
    List<CompanyCustomFolderVM> findAllByPermissionUserIdAndCategory(Long userId, CategoryFolderType category);

    Page<CompanyCustomFolder> findAllByPermissionUserId(Long userId, Pageable pageable);

    Page<CompanyCustomFolder> findAllByPermissionUserIdAndNameLike(Long userId, String name, Pageable pageable);

    Page<CompanyCustomFolder> findAllByPermissionUserIdAndCategory(Long userId, CategoryFolderType category, Pageable pageable);

    Page<CompanyCustomFolder> findAllByPermissionUserIdAndCategoryAndNameLike(Long userId, CategoryFolderType category, String name, Pageable pageable);

    @Query(value = "SELECT f\n" +
            "FROM CompanyCustomFolder f\n" +
            "WHERE f.permissionUserId <> ?1 AND (EXISTS (\n" +
            "    SELECT 1\n" +
            "    FROM CompanyCustomFolderSharedUser su\n" +
            "    WHERE f.id = su.folderId AND su.userId = ?1\n" +
            ") OR EXISTS (\n" +
            "    SELECT 1\n" +
            "    FROM CompanyCustomFolderSharedTeam st\n" +
            "    INNER JOIN PermissionUserTeam ptu ON st.teamId = ptu.teamId\n" +
            "    WHERE f.id = st.folderId AND ptu.userId = ?1 AND (st.ignoreTeamUser IS NULL OR st.ignoreTeamUser NOT LIKE CONCAT('%', ?1, '%'))\n" +
            "))\n")
    Page<CompanyCustomFolder> findAllSharedByUserId(Long userId, Pageable pageable);

    @Query(value = "SELECT f\n" +
            "FROM CompanyCustomFolder f\n" +
            "WHERE f.permissionUserId <> ?1 AND f.category = ?2 AND (EXISTS (\n" +
            "    SELECT 1\n" +
            "    FROM CompanyCustomFolderSharedUser su\n" +
            "    WHERE f.id = su.folderId AND su.userId = ?1\n" +
            ") OR EXISTS (\n" +
            "    SELECT 1\n" +
            "    FROM CompanyCustomFolderSharedTeam st\n" +
            "    INNER JOIN PermissionUserTeam ptu ON st.teamId = ptu.teamId\n" +
            "    WHERE f.id = st.folderId AND ptu.userId = ?1 AND (st.ignoreTeamUser IS NULL OR st.ignoreTeamUser NOT LIKE CONCAT('%', ?1, '%'))\n" +
            "))\n")
    Page<CompanyCustomFolder> findAllSharedByUserIdAndCategory(Long userId, CategoryFolderType category, Pageable pageable);

    @Query(value = "SELECT new com.altomni.apn.company.domain.vm.CompanyCustomFolderVM(f.id, f.name)\n" +
            "FROM CompanyCustomFolder f\n" +
            "WHERE f.permissionUserId <> ?1 AND f.category = ?2 AND (EXISTS (\n" +
            "    SELECT 1\n" +
            "    FROM CompanyCustomFolderSharedUser su\n" +
            "    WHERE f.id = su.folderId AND su.userId = ?1\n" +
            ") OR EXISTS (\n" +
            "    SELECT 1\n" +
            "    FROM CompanyCustomFolderSharedTeam st\n" +
            "    INNER JOIN PermissionUserTeam ptu ON st.teamId = ptu.teamId\n" +
            "    WHERE f.id = st.folderId AND ptu.userId = ?1 AND (st.ignoreTeamUser IS NULL OR st.ignoreTeamUser NOT LIKE CONCAT('%', ?1, '%'))\n" +
            "))\n")
    List<CompanyCustomFolderVM> findAllSharedByUserIdAndCategory(Long userId, CategoryFolderType category);

    @Query(value = "SELECT f\n" +
            "FROM CompanyCustomFolder f\n" +
            "WHERE f.permissionUserId <> ?1 AND (\n" +
            "\tEXISTS (\n" +
            "\t\tSELECT 1\n" +
            "\t\tFROM CompanyCustomFolderSharedUser su\n" +
            "\t\tWHERE f.id = su.folderId AND su.userId = ?1\n" +
            "\t) OR EXISTS (\n" +
            "\t\tSELECT 1\n" +
            "\t\tFROM CompanyCustomFolderSharedTeam st\n" +
            "\t\tINNER JOIN PermissionUserTeam ptu ON st.teamId = ptu.teamId\n" +
            "\t\tWHERE f.id = st.folderId AND ptu.userId = ?1 AND (st.ignoreTeamUser IS NULL OR st.ignoreTeamUser NOT LIKE CONCAT('%', ?1, '%'))\n" +
            "\t) \n" +
            ") AND f.name LIKE ?2")
    Page<CompanyCustomFolder> findAllSharedByIdAndNameLike(Long userId, String name, Pageable pageable);


    @Query(value = "SELECT f\n" +
            "FROM CompanyCustomFolder f\n" +
            "WHERE f.permissionUserId <> ?1 AND f.category = ?2 AND (\n" +
            "\tEXISTS (\n" +
            "\t\tSELECT 1\n" +
            "\t\tFROM CompanyCustomFolderSharedUser su\n" +
            "\t\tWHERE f.id = su.folderId AND su.userId = ?1\n" +
            "\t) OR EXISTS (\n" +
            "\t\tSELECT 1\n" +
            "\t\tFROM CompanyCustomFolderSharedTeam st\n" +
            "\t\tINNER JOIN PermissionUserTeam ptu ON st.teamId = ptu.teamId\n" +
            "\t\tWHERE f.id = st.folderId AND ptu.userId = ?1 AND (st.ignoreTeamUser IS NULL OR st.ignoreTeamUser NOT LIKE CONCAT('%', ?1, '%'))\n" +
            "\t) \n" +
            ") AND f.name LIKE ?3")
    Page<CompanyCustomFolder> findAllSharedByIdAndCategoryAndNameLike(Long userId, CategoryFolderType category, String name, Pageable pageable);

}
