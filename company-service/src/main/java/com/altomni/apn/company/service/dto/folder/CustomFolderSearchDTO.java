package com.altomni.apn.company.service.dto.folder;

import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.enumeration.folder.FolderType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "dto for customFolder")
public class CustomFolderSearchDTO implements Serializable {

    @ApiModelProperty(value = "The type for customFolder.", allowableValues = "CUSTOMIZED, SHARED")
    @NotNull(message = "folderType is required")
    private FolderType folderType;

    @ApiModelProperty(value = "The category for customFolder.", allowableValues = "PROSPECT_ALL_COMPANY, CLIENT_ALL_COMPANY")
    @NotNull(message = "category is required")
    private CategoryFolderType category;

    @ApiModelProperty(value = "The name for customFolder.")
    private String name;

    @ApiModelProperty(value = "The note for customFolder.")
    private String note;

    @ApiModelProperty(value = "The lastModifiedDateFrom for customFolder.")
    private Instant lastModifiedDateFrom;

    @ApiModelProperty(value = "The lastModifiedDateTo for customFolder.")
    private Instant lastModifiedDateTo;

    @ApiModelProperty(value = "The createdDateFrom for customFolder.")
    private Instant createdDateFrom;

    @ApiModelProperty(value = "The createdDateTo for customFolder.")
    private Instant createdDateTo;

    @ApiModelProperty(value = "The sharedUsers for customFolder.")
    @UniqueElements
    private List<Long> sharedUsers;

    @ApiModelProperty(value = "The sharedTeams for customFolder.")
    @UniqueElements
    private List<Long> sharedTeams;

    @ApiModelProperty(value = "The creators for customFolder.")
    @UniqueElements
    private List<Long> creators;

    @ApiModelProperty(value = "The generalTexts for search customFolder.")
    private String generalTexts;

}
