package com.altomni.apn.company.vo.contact;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;


@AllArgsConstructor
@ApiModel(description = "Vo for  contact")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class ContactOwnershipVO implements Serializable {

    @ApiModelProperty(value = "id for contact")
    private Long userId;

    private String fullName;

    private Long contactId;

    @ApiModelProperty(value = "indicate contact share user")
    private Boolean autoAssigned;


    public ContactOwnershipVO(Long userId, String fullName) {
        this.userId = userId;
        this.fullName = fullName;
    }

    public ContactOwnershipVO(Long userId, String fullName, Boolean autoAssigned) {
        this.userId = userId;
        this.fullName = fullName;
        this.autoAssigned = autoAssigned;
    }
}
