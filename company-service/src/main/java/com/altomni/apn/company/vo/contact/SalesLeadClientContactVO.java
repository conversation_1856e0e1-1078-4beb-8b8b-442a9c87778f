package com.altomni.apn.company.vo.contact;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.domain.vm.CompanyContactVM;
import com.altomni.apn.company.vo.business.AccountBusinessNameBriefVO;
import com.altomni.apn.company.vo.business.OwnerVO;
import com.altomni.apn.company.vo.company.TagVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 * date:2023-04-19
 */
@AllArgsConstructor
@ApiModel(description = "Vo for company & salesLead contact")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class SalesLeadClientContactVO implements Serializable, AttachConfidentialTalent {

    @ApiModelProperty(value = "id for contact")
    private Long id;

    @ApiModelProperty(value = "id for company")
    private Long companyId;

    @ApiModelProperty(value = "name for company")
    private String companyName;

    @ApiModelProperty(value = "name for company")
    private String company;

    @ApiModelProperty(value = "firstName for contact")
    private String firstName;

    @ApiModelProperty(value = "lastName for contact")
    private String lastName;

    @ApiModelProperty(value = "fullName for contact")
    private String name;

    @ApiModelProperty(value = "lastFollowUpTime for contact")
    private Instant lastFollowUpTime;

    @ApiModelProperty(value = "type for contact")
    private Integer contactCategory;

    @ApiModelProperty(value = "title for the contact")
    private String title;

    @ApiModelProperty(value = "department for the contact")
    private String department;

    @ApiModelProperty(value = "contact list")
    private List<TalentContactDTO> contacts;

    @ApiModelProperty(value = "id for the company location")
    private LocationDTO companyLocation;

    @ApiModelProperty(value = "note for the contact")
    private String note;

    @ApiModelProperty(value = "zipcode for the contact, custom version exclusive")
    private String zipcode;

    @ApiModelProperty(value = "businessUnit for the contact, custom version exclusive")
    private String businessUnit;

    @ApiModelProperty(value = "businessGroup for the contact, custom version exclusive")
    private String businessGroup;

    @ApiModelProperty(value = "linkedinProfile for the contact, custom version exclusive")
    private List<String> linkedinProfile;

    @ApiModelProperty(value = "status for the contact, true is active, false id inActive")
    private Boolean active;

    @ApiModelProperty(value = "contact related talentId")
    private Long talentId;

    @ApiModelProperty(value = "approverId for the contact")
    private Long approverId;

    @ApiModelProperty(value = "inactivate for the contact")
    @NotNull
    private Boolean inactived;

    @ApiModelProperty(value = "Receive email notifications when Timesheets or Expenses are submitted for my approval.")
    @NotNull
    private Boolean receiveEmail;

    @ApiModelProperty(value = "createUserId for the contact")
    private Long createUserId;

    @ApiModelProperty(value = "id for the company location")
    private Long companyLocationId;

    @ApiModelProperty(value = "linkedinProfile for the contact, custom version exclusive")
    private List<TagVO> tags;

    @ApiModelProperty(value = "id for the company location")
    private Long contactId;


    @ApiModelProperty(value = "")
    private List<ContactOwnershipVO> contactOwners;

    @ApiModelProperty(value = "contact share to certain user")
    private List<ContactOwnershipVO> contactShareUsers;

    @ApiModelProperty(value = "contact share to all user")
    private Boolean shareToAll;

    //    @ApiModelProperty(value = "all related business name list")
//    private List<String> businessName;
    @ApiModelProperty(value = "account Business under account company ids for the contact")
    private List<Long> accountBusiness;

    @ApiModelProperty(value = "all related business ID and name list")
    private List<AccountBusinessNameBriefVO> accountBusinesses;

    @ApiModelProperty(value = "service type relation")
    private List<Integer> associatedServiceTypes;

    @ApiModelProperty(value = "viewable flag")
    private Boolean isViewable = Boolean.TRUE;

    private Boolean hasPhone = Boolean.FALSE;
    private Boolean hasEmail = Boolean.FALSE;
    private Boolean hasWechat = Boolean.FALSE;
    private Boolean hasWhatsApp = Boolean.FALSE;
    private Boolean hasLinkedIn = Boolean.FALSE;

    /**
     * 关键联系人需求增加
     */
    private Boolean isKeyContact;

    private Boolean confidentialTalentViewAble;

    private ConfidentialInfoDto confidentialInfo;

    public List<TalentContactDTO> getContacts() {
        return contacts;
    }

    public SalesLeadClientContactVO setContacts(List<TalentContactDTO> contacts) {
        this.contacts = contacts;
        return this;
    }

    public static SalesLeadClientContactVO fromSalesLeadClientContact(SalesLeadClientContact salesLeadClientContact) {
        SalesLeadClientContactVO salesLeadClientContactVO = new SalesLeadClientContactVO();
        ServiceUtils.myCopyProperties(salesLeadClientContact, salesLeadClientContactVO);
        if (salesLeadClientContact.getCreatedBy().contains(",")) {
            String[] creator = salesLeadClientContact.getCreatedBy().split(",");
            salesLeadClientContactVO.setCreateUserId(Long.valueOf(creator[0]));
        }
        salesLeadClientContactVO.setContactId(salesLeadClientContact.getCrmContactId());
        return salesLeadClientContactVO;
    }

    public static SalesLeadClientContactVO fromCompanyContactVM(CompanyContactVM companyContactVM) {
        SalesLeadClientContactVO salesLeadClientContactVO = new SalesLeadClientContactVO();
        ServiceUtils.myCopyProperties(companyContactVM, salesLeadClientContactVO);
        return salesLeadClientContactVO;
    }

    public SalesLeadClientContactVO(Long id, String firstName, String lastName, String name) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.name = name;
    }

    public SalesLeadClientContactVO(Long id) {
        this.id = id;
    }

    @Override
    public void encrypt() {

    }
}
