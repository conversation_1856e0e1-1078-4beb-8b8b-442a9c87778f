package com.altomni.apn.company.repository.skipsubmit;

import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the SkipSubmitToAmCompanyUser entity.
 */
@Repository
public interface SkipSubmitToAmCompanyUserRepository extends JpaRepository<SkipSubmitToAmCompanyUser, Long> {

    List<SkipSubmitToAmCompanyUser> findAllByCompanyId(Long companyId);

    void deleteAllByCompanyId(Long companyId);

}
