package com.altomni.apn.company.repository.business;

import com.altomni.apn.company.domain.business.AccountBusiness;
import com.altomni.apn.company.domain.business.AccountBusinessContactRelation;
import com.altomni.apn.company.service.dto.salesLead.AccountBusinessNameDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface AccountBusinessContactRelationRepository extends JpaRepository<AccountBusinessContactRelation, Long>, JpaSpecificationExecutor<AccountBusinessContactRelation> {

    List<AccountBusinessContactRelation> findAllByAccountBusinessIdIn(List<Long> businessIds);

    List<AccountBusinessContactRelation> findAllByAccountBusinessId(Long businessId);

    @Query("select r from AccountBusinessContactRelation r inner join AccountBusiness b on r.accountBusinessId = b.id where r.clientContactId = ?1 and b.accountCompanyId = ?2")
    List<AccountBusinessContactRelation> findAllByAccountContactIdAndCompanyId(Long contactId, Long companyId);

    //可能有的商机未同步到apn，仅同步联系人关联的商机数据，需要兼容
    @Query("SELECT new com.altomni.apn.company.service.dto.salesLead.AccountBusinessNameDTO(br.clientContactId, br.accountBusinessId , ab.name) " +
            "FROM AccountBusinessContactRelation br " +
            "LEFT JOIN AccountBusiness ab ON ab.id = br.accountBusinessId " +
            "WHERE br.clientContactId IN :clientContactIdsList")
    List<AccountBusinessNameDTO> findAllByClientContactIdIn(@Param("clientContactIdsList") List<Long> clientContactIdsList);

    @Query("select ar from AccountBusinessContactRelation ar where ar.clientContactId in ?1")
    List<AccountBusinessContactRelation> findAllRelationByClientContactIdIn(List<Long> clientContactIds);
}