package com.altomni.apn.company.repository.company.note;

import com.altomni.apn.company.domain.company.note.CompanyClientNoteContactRelation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CompanyClientNoteContactRelationRepository extends JpaRepository<CompanyClientNoteContactRelation, Long> {

    List<CompanyClientNoteContactRelation> findAllByNoteId(Long noteId);
}
