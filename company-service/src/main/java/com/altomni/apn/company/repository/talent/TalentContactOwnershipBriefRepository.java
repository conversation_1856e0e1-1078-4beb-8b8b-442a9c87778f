package com.altomni.apn.company.repository.talent;


import com.altomni.apn.company.domain.talent.TalentOwnershipSyncBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TalentContactOwnershipBriefRepository extends JpaRepository<TalentOwnershipSyncBrief, Long> {

    List<TalentOwnershipSyncBrief> findAllByTalentIdIn(List<Long> talentIds);


}
