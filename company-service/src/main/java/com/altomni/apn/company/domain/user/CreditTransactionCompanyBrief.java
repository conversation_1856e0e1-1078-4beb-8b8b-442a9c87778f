package com.altomni.apn.company.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.enumeration.user.StatusConverter;
import com.altomni.apn.user.domain.enumeration.CreditType;
import com.altomni.apn.user.domain.enumeration.CreditTypeConverter;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A CreditTransaction.
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "credit_transaction")
public class CreditTransactionCompanyBrief extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "profile_id")
    private String profileId;

    @Column(name = "credit")
    private Integer credit;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "user_id")
    private Long userId;

    @Convert(converter = StatusConverter.class)
    @Column(name = "status")
    private Status status;

    @Column(name = "exists_contacts")
    private String existsContacts;

    @Column(name = "es_contacts")
    private String esContacts;

    @Column(name = "api_source")
    private String apiSource;

    @Convert(converter = CreditTypeConverter.class)
    @Column(name = "credit_type")
    private CreditType creditType;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "common_db_search_es_id")
    private String commonDBSearchESId;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("existsContacts", "esContacts"));

}
