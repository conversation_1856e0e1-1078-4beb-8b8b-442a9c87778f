package com.altomni.apn.company.vo.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@ApiModel(description = "Vo for company params config")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CompanyParamConfigVO implements Serializable {

    @ApiModelProperty(value = "the prospect company config.")
    private List<CompanyParamVO> prospect;

    @ApiModelProperty(value = "the client company config.")
    private List<CompanyParamVO> client;

}
