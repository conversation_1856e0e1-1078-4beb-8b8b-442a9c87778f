package com.altomni.apn.company.domain.contract;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.enumeration.company.NoPoachingType;
import com.altomni.apn.company.domain.enumeration.company.NoPoachingTypeConverter;
import com.altomni.apn.company.domain.enumeration.contract.ContractStatus;
import com.altomni.apn.company.domain.enumeration.contract.ContractStatusConverter;
import com.altomni.apn.company.service.dto.contract.ContractDTO;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * A Contract.
 */
@Entity
@Table(name = "company_contract")
@Data
public class Contract extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "company_id")
    private Long accountCompanyId;

    @Column(name = "name")
    private String name;

    @Column(name = "start_time")
    private Instant startDate;

    @Column(name = "end_time")
    private Instant endDate;

    @Column(name = "status")
    @Convert(converter = ContractStatusConverter.class)
    private ContractStatus status;

    @Column(name ="note")
    private String note;

    @Column(name = "previous_contract_id")
    private Long previousContractId;

    /**
     * No Poaching Client需求增加字段
     */
    @Column(name = "no_poaching")
    @Convert(converter = NoPoachingTypeConverter.class)
    private NoPoachingType noPoaching = NoPoachingType.NO_RESTRICTION;

}
