package com.altomni.apn.company.service.dto.contact;

import com.altomni.apn.common.dto.talent.TalentContactDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel(description = "dto for company check duplicated talent")
public class CompanyContactCheckDuplicatedTalentDTO {

    @ApiModelProperty(value = "contact list")
    @NotEmpty
    private List<TalentContactDTO> contacts;

    @ApiModelProperty(value = "linkedinProfile for the contact, custom version exclusive")
    private List<String> linkedinProfile;

}
