package com.altomni.apn.company.config.rabbit;

import com.altomni.apn.company.config.env.EsfillerMQProperties;
import com.altomni.apn.company.config.env.UpdateTalentCompanyMqProperties;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.SerializerMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;

@Configuration
@RefreshScope
public class RabbitMqConfig {

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource
    private UpdateTalentCompanyMqProperties updateTalentCompanyMqProperties;

    public CachingConnectionFactory connectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        return connectionFactory;
    }

    @Bean(name = "esfillerFactory")
    public SimpleRabbitListenerContainerFactory esfillerFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "esfillerConnectionFactory")
    @Primary
    public ConnectionFactory esfillerConnectionFactory() {
        return connectionFactory (esfillerMQProperties.getEsfillerMQHost(), esfillerMQProperties.getEsfillerMQPort(), esfillerMQProperties.getEsfillerMQVirtualHost(), esfillerMQProperties.getEsfillerMQUsername(), esfillerMQProperties.getEsfillerMQPassword());
    }

    @Bean(name = "esfillerRabbitTemplate")
    public RabbitTemplate esfillerRabbitTemplate(@Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate parsersRabbitTemplate = new RabbitTemplate(connectionFactory);
        parsersRabbitTemplate.setMandatory(true);
        parsersRabbitTemplate.setMessageConverter(new SerializerMessageConverter());
        return parsersRabbitTemplate;
    }

    @Bean(name = "esfillerAmqpAdmin")
    public AmqpAdmin esfillerAmqpAdmin(@Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory) {
        AmqpAdmin amqpAdmin = new RabbitAdmin(connectionFactory);
        return amqpAdmin;
    }

    @Bean
    Queue normalizedCompanyQueue() {
        return new Queue(esfillerMQProperties.getApnNormalizedCompanyQueue(), true, false, false);
    }

    @Bean
    DirectExchange toEsFillerExchange() {
        DirectExchange directExchange = new DirectExchange(esfillerMQProperties.getEsfillerMQExchange());
        directExchange.setDelayed(true);
        return directExchange;
    }

    @Bean
    Binding normalizedCompanyBinding(Queue normalizedCompanyQueue, DirectExchange toEsFillerExchange) {
        return BindingBuilder.bind(normalizedCompanyQueue).to(toEsFillerExchange).with(esfillerMQProperties.getApnNormalizedCompanyRoutingKey());
    }

    /****** start talent tx company consumer mq config  *******/
    @Bean(name = "companyConsumerConnectionFactory")
    public ConnectionFactory companyConsumerConnectionFactory() {
        return companyConsumerConnectionFactory(updateTalentCompanyMqProperties.getHost(), updateTalentCompanyMqProperties.getPort(), updateTalentCompanyMqProperties.getVirtualHost(), updateTalentCompanyMqProperties.getUserName(), updateTalentCompanyMqProperties.getPassword());
    }

    public CachingConnectionFactory companyConsumerConnectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        connectionFactory.setPublisherReturns(true);
        connectionFactory.setPublisherConfirms(true);
        return connectionFactory;
    }

    @Bean(name = "companyConsumerFactory")
    public SimpleRabbitListenerContainerFactory companyConsumerFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("companyConsumerConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(1);
        configurer.configure(factory, connectionFactory);
        return factory;
    }


    @Bean(name = "companyConsumerRabbitTemplate")
    public RabbitTemplate companyConsumerRabbitTemplate(
            @Qualifier("companyConsumerConnectionFactory") ConnectionFactory connectionFactory
    ) {
        RabbitTemplate companyConsumerRabbitTemplate = new RabbitTemplate(connectionFactory);
        return companyConsumerRabbitTemplate;
    }

    @Bean(name = "crmConnectionFactory")
    public ConnectionFactory emailConnectionFactory() {
        return connectionFactory(esfillerMQProperties.getEsfillerMQHost(), esfillerMQProperties.getEsfillerMQPort(),  esfillerMQProperties.getEsfillerMQVirtualHost(), esfillerMQProperties.getEsfillerMQUsername(), esfillerMQProperties.getEsfillerMQPassword());
    }

    @Bean(name = "crmFactory")
    public SimpleRabbitListenerContainerFactory secondFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("crmConnectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(1);
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "crmRabbitTemplate")
    @Primary
    public RabbitTemplate xxlJobRabbitTemplate(
            @Qualifier("crmConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate xxlJobRabbitTemplate = new RabbitTemplate(connectionFactory);
        xxlJobRabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return xxlJobRabbitTemplate;
    }

}
