package com.altomni.apn.company.service.dto.location;

import com.altomni.apn.common.dto.address.LocationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "location for company")
public class CompanyLocationDTO implements Serializable {

    @ApiModelProperty(value = "The AWS location returned by the location service interface.")
    @NotNull
    private LocationDTO location;

    @ApiModelProperty(value = "The companyId associated with the location.")
    @NotNull
    private Long companyId;

}
