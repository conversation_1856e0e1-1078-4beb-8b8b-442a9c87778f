package com.altomni.apn.company.repository.business;

import com.altomni.apn.common.dto.company.ICompanyTeamUser;
import com.altomni.apn.company.domain.business.BusinessFlowAdministrator;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.company.service.dto.AmInfoDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface AccountBusinessAdministratorRepository extends JpaRepository<BusinessFlowAdministrator, Long>, JpaSpecificationExecutor<BusinessFlowAdministrator> {

    @Query(value = """
        SELECT
        bfa.id,
        bfa.account_business_id,
        bfa.user_id,
        bfa.contribution,
        bfa.sales_lead_role,
        bfa.company_id,
        bfa.created_by,
        bfa.created_date,
        bfa.last_modified_by,
        bfa.last_modified_date,
        bfa.puser_id,
        bfa.pteam_id,
        bfa.country
    FROM
        business_flow_administrator bfa
        WHERE bfa.company_id = ?1 AND bfa.user_id = ?3 AND bfa.sales_lead_role in ?2 
    """, nativeQuery = true)
    List<BusinessFlowAdministrator> findAllByCompanyIdAndSalesLeadRoleTypeAndUserId(Long companyId, List<Integer> salesLeadRoleType, Long userId);

    @Query(" select new com.altomni.apn.company.service.dto.AmInfoDTO(u.email, ull.timeZone) from User u " +
            " left join BusinessFlowAdministrator sla on sla.userId = u.id and (sla.salesLeadRoleType = 0 or sla.salesLeadRoleType = 3) " +
            " left join UserLastLogin ull on ull.userId = u.id " +
            " where sla.companyId = ?1  ")
    List<AmInfoDTO> findAmByCompanyId(Long companyId);

    @Query(value = "select count(trp.id) from talent_recruitment_process trp " +
            "            inner join job j on j.id = trp.job_id " +
            "            inner join business_flow_administrator csla on j.company_id = csla.company_id " +
            "            where trp.talent_id = ?1 and csla.sales_lead_role in (0,3) and csla.user_id = ?2", nativeQuery = true)
    Integer countCompanyAccountManager(Long talentId, Long userId);

    @Query("select distinct a.userId from BusinessFlowAdministrator a " +
            " where a.companyId=:companyId and (a.salesLeadRoleType = 0 or a.salesLeadRoleType = 3)")
    List<Long> findAmIdsByCompanyId(@Param("companyId") Long companyId);

    @Query(value = "select a.user_id from business_flow_administrator a " +
            " left join job j on j.company_id = a.company_id " +
            " where j.id = ?1 and a.sales_lead_role in (0,3)", nativeQuery = true)
    List<Long> findAmIdsByjobId(@Param("jobId") Long jobId);

    @Query(value = "select csla.company_id, JSON_ARRAYAGG(csla.user_id) from company c" +
            " left join business_flow_administrator csla on c.id = csla.company_id " +
            " where c.tenant_id=:tenantId and csla.sales_lead_role in (0,3)" +
            " group by csla.company_id", nativeQuery = true)
    List<Object[]> findCompanyToAmsMap(@Param("tenantId") Long tenantId);

    @Query(value = "select cptu.user_id user_id,cptu.permission,u.first_name first_name, u.last_name last_name, u.username username, u.email email, u.activated activated from company_project_team_user cptu " +
            " left join user u on u.id = cptu.user_id " +
            " left join company_project_team cpt on cpt.id = cptu.team_id " +
            " where cpt.company_id = ?1 and cptu.permission < 256 and u.activated = 1" +
            " union all " +
            " (select csla.user_id user_id,256 permission,u.first_name first_name, u.last_name last_name, u.username username, u.email email, u.activated activated " +
            " from business_flow_administrator csla " +
            " left join user u on u.id = csla.user_id " +
            " where csla.sales_lead_role in (0,3) and csla.company_id = ?1) ", nativeQuery = true)
    List<Object[]> findApplicationUsersByCompanyId(@Param("companyId") Long companyId);

    @Query(value = "select cptu.user_id userId, " +
            " case cptu.permission " +
            "   when 4 then 'RECRUITER' " +
            "   when 8 then 'DELIVERY_MANAGER' " +
            "   when 16 then 'PRIMARY_RECRUITER' " +
            "   when 64 then 'AC' end role," +
            " u.first_name firstName, " +
            " u.last_name lastName " +
            " from company_project_team_user cptu " +
            " left join user u on u.id = cptu.user_id " +
            " left join company_project_team cpt on cpt.id = cptu.team_id " +
            " where cpt.company_id = :companyId and cptu.permission < 256 and u.activated = 1" +
            " union all " +
            " (select csla.user_id userId, " +
            "    CASE csla.sales_lead_role " +
            "        WHEN 0 THEN 'AM' " +
            "        WHEN 1 THEN 'SALES_LEAD_OWNER' " +
            "        WHEN 2 THEN 'BD_OWNER' " +
            "    END AS role, " +
            "u.first_name firstName, u.last_name lastName " +
            " from business_flow_administrator csla " +
            " left join user u on u.id = csla.user_id " +
            " where csla.sales_lead_role in (0,1,2) and csla.company_id = :companyId and (:salesLeadId is null or csla.account_business_id = :salesLeadId)) ", nativeQuery = true)
    List<ICompanyTeamUser> findTeamUsersByCompanyId(@Param("companyId") Long companyId, @Param("salesLeadId") Long salesLeadId);

    @Query(value = """
        select distinct c.id from company c 
        left join business_flow_administrator csla on csla.company_id = c.id and csla.sales_lead_role in (0,3)
        where csla.user_id = ?1 
    """, nativeQuery = true)
    List<Long> findCompanyIdsByUserIdWithAm(Long userId);

    @Query(value = """
        SELECT
        bfa.id,
        bfa.account_business_id,
        bfa.user_id,
        bfa.contribution,
        bfa.sales_lead_role,
        bfa.company_id,
        bfa.created_by,
        bfa.created_date,
        bfa.last_modified_by,
        bfa.last_modified_date,
        bfa.puser_id,
        bfa.pteam_id,
        bfa.country
    FROM
        business_flow_administrator bfa
        WHERE bfa.company_id = ?1 AND bfa.user_id = ?2 AND bfa.sales_lead_role = ?3 LIMIT 1
    """, nativeQuery = true)
    BusinessFlowAdministrator findOneByCompanyIdAndUserIdAndSalesLeadRoleType(Long companyId, Long userId, Integer type);

    @Query(value = """
    SELECT
        bfa.id,
        bfa.account_business_id,
        bfa.user_id,
        bfa.contribution,
        bfa.sales_lead_role,
        bfa.company_id,
        bfa.created_by,
        bfa.created_date,
        bfa.last_modified_by,
        bfa.last_modified_date,
        bfa.puser_id,
        bfa.pteam_id,
        bfa.country
    FROM
        business_flow_administrator bfa
        where bfa.account_business_id in (?1)
    """, nativeQuery = true)
    List<BusinessFlowAdministrator> findAllByAccountBusinessIdIn(List<Long> businessIds);

    //company编辑页前端使用crm user交互 & 同步es
    @Query("select ba from BusinessFlowAdministrator ba where ba.accountBusinessId in ?1")
    List<BusinessFlowAdministrator> findAllByAccountBusinessIdInForCrm(List<Long> businessIds);

    @Query("select ba from BusinessFlowAdministrator ba where ba.accountBusinessId in ?1 and ba.salesLeadRoleType = ?2")
    List<BusinessFlowAdministrator> findAllByAccountBusinessIdInAndRoleTypeForCrm(List<Long> businessIds, SalesLeadRoleType salesLeadRoleType);

    @Query("select max(ba.id) from BusinessFlowAdministrator ba")
    Long maxId();

    @Query(value = """
            select distinct u.email from business_flow_administrator t
            join user u on u.id = t.user_id
            where  account_business_id= ?2 and  company_id=?1 and sales_lead_role in (0,3)
            """, nativeQuery = true)
    List<String> findAmUserIdByCompanyIdAndSalesLeadId(Long companyId,Long salesLeadId);

    @Query(value = """
            select distinct u.email from business_flow_administrator t
            join user u on u.id = t.user_id
            where company_id=?1 and sales_lead_role in (0,3)
            """, nativeQuery = true)
    List<String> findAmUserIdByCompanyId(Long companyId);

    List<BusinessFlowAdministrator> findAllByAccountBusinessIdInAndSalesLeadRoleTypeIn(List<Long> businessIds, List<SalesLeadRoleType> salesLeadRoleTypes);


    @Query(value = """
        SELECT
        bfa.id,
        bfa.account_business_id,
        bfa.user_id,
        bfa.contribution,
        bfa.sales_lead_role,
        bfa.company_id,
        bfa.created_by,
        bfa.created_date,
        bfa.last_modified_by,
        bfa.last_modified_date,
        bfa.puser_id,
        bfa.pteam_id,
        bfa.country
    FROM
        business_flow_administrator bfa
        WHERE bfa.company_id = ?1  AND bfa.sales_lead_role in ?2 
    """, nativeQuery = true)
    List<BusinessFlowAdministrator> findAllByCompanyIdAndSalesLeadRoleType(Long companyId, List<Integer> salesLeadRoleType);

}