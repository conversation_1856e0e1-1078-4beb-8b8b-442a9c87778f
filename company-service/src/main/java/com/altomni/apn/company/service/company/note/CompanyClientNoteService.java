package com.altomni.apn.company.service.company.note;

import com.altomni.apn.company.domain.company.note.CompanyClientNote;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteDTO;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteSearchDTO;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteUpdateDTO;
import com.altomni.apn.company.vo.note.CompanyClientNoteDetailVO;
import com.altomni.apn.company.vo.note.CompanyClientNoteVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.List;

public interface CompanyClientNoteService {

    CompanyClientNoteVO createClientNote(CompanyClientNoteDTO companyClientNoteDTO);

    CompanyClientNoteVO updateClientNote(Long id, CompanyClientNoteUpdateDTO companyClientNoteUpdateDTO);

    void deleteClientNote(Long id);

    CompanyClientNoteDetailVO getClientNote(Long id);

    String searchCompanyClientNote(CompanyClientNoteSearchDTO companyClientNoteSearchDTO, Pageable pageable, HttpHeaders headers) throws IOException;

    void saveDefaultCompanyClientNote(Long companyId);

    void saveDefaultCompanyClientNote(List<Long> companyIds);

}
