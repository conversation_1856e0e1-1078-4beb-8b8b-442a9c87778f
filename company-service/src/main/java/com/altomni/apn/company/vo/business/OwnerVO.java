package com.altomni.apn.company.vo.business;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class OwnerVO implements Serializable {

    @ApiModelProperty(value = "id for owner")
    private Long id;

    @ApiModelProperty(value = "name for owner")
    private String name;

    @ApiModelProperty(value = "contribution for owner")
    private Integer contribution;

    @ApiModelProperty(value = "Whether user is activated. Default is true. Read Only.")
    private boolean activated;

    private Long country;

    public OwnerVO(Long id) {
        this.id = id;
    }

    public OwnerVO(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    public OwnerVO(Long id, String name, boolean activated) {
        this.id = id;
        this.name = name;
        this.activated = activated;
    }

    public OwnerVO(Long id, String name, Integer contribution) {
        this.id = id;
        this.name = name;
        this.contribution = contribution;
    }

    public OwnerVO(Long id, String name, Integer contribution,Long country) {
        this.id = id;
        this.name = name;
        this.contribution = contribution;
        this.country = country;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OwnerVO ownerVO = (OwnerVO) o;
        return id.equals(ownerVO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
