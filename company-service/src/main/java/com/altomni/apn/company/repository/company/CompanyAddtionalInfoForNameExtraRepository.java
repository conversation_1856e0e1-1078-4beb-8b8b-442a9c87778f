package com.altomni.apn.company.repository.company;

import com.altomni.apn.company.domain.company.CompanyAdditionalInfoForNameExtra;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface CompanyAddtionalInfoForNameExtraRepository extends JpaRepository<CompanyAdditionalInfoForNameExtra, Long> {

    List<CompanyAdditionalInfoForNameExtra> findAllByAccountCompanyIdIn(Collection<Long> companyIds);

}