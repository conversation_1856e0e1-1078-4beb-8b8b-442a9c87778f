package com.altomni.apn.company.service.dto.note;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * date:2023-04-14
 */
@Data
@ApiModel(description = "note search for progress company")
public class CompanyProgressNoteSearchDTO extends CompanyClientNoteSearchDTO implements Serializable {

    @ApiModelProperty(value = "the id for salesLead.")
    private Long salesLeadId;

    private Long clientContactId;

}
