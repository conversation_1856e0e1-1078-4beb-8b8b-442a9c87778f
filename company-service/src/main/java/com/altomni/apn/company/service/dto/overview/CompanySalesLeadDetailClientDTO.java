package com.altomni.apn.company.service.dto.overview;

import com.altomni.apn.company.service.dto.contact.CompanyContactDTO;
import com.altomni.apn.company.service.dto.salesLead.SalesLeadClientDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "dto for salesLead client company")
public class CompanySalesLeadDetailClientDTO extends SalesLeadClientDTO implements Serializable {

    @ApiModelProperty(value = "The salesLead for company.")
    @UniqueElements
    private List<CompanyContactDTO> salesLeadClientContacts;
}