package com.altomni.apn.company.vo.company;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.company.domain.enumeration.company.FortuneRankType;
import com.altomni.apn.company.domain.enumeration.company.NoPoachingType;
import com.altomni.apn.company.vo.business.AccountBusinessVO;
import com.altomni.apn.company.vo.contact.ContactInfoVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class AccountCompanyVO implements Serializable {

    @ApiModelProperty(value = "id for company")
    private Long id;

    @ApiModelProperty(value = "active for company")
    private AccountCompanyStatus active;

    @ApiModelProperty(value = "tenantId for company")
    private Long tenantId;

    @ApiModelProperty(value = "companyClientLevel for company")
    private Integer companyClientLevel;

    @ApiModelProperty(value = "locations for company")
    private List<LocationDTO> locations;

    @ApiModelProperty(value = "website for company")
    private List<String> websites;

    @ApiModelProperty(value = "fullBusinessName for company")
    private String fullBusinessName;
    private List<String> aliases;
    @ApiModelProperty(value = "Company relationship")
    private List<CompanyRelationshipDTO> relationshipList;

    @ApiModelProperty(value = "logo for company")
    private String logo;

    @ApiModelProperty(value = "industries for company")
    private List<Integer> industries;

    @ApiModelProperty(value = "nameEn for company")
    private String nameEn;

    @ApiModelProperty(value = "legalPerson for company")
    private String legalPerson;

    @ApiModelProperty(value = "annualTurnover for company")
    private JSONObject annualTurnover;

    @ApiModelProperty(value = "keywords for company")
    private List<Integer> keywords;

    @ApiModelProperty(value = "profile  for company")
    private String profile;

    @ApiModelProperty(value = "accountBusiness for company")
    private List<AccountBusinessVO> accountBusiness;

    @ApiModelProperty(value = "staffsNumber for company")
    private JSONObject staffsNumber;

    @ApiModelProperty(value = "establishDate for company")
    private LocalDate establishDate;

    @ApiModelProperty(value = "registeredCapital for company")
    private String registeredCapital;

    @ApiModelProperty(value = "revenueCurrency for company")
    private String revenueCurrency;

    @ApiModelProperty(value = "fortuneRank for account")
    private FortuneRankType fortuneRank;

    @ApiModelProperty(value = "linkedinCompanyProfile for account")
    private String linkedinCompanyProfile;

    @ApiModelProperty(value = "crunchbaseCompanyProfile for account")
    private String crunchbaseCompanyProfile;

    @ApiModelProperty(value = "The organizationName for company.")
    public String organizationName;

    @ApiModelProperty(value = "company phone")
    private List<ContactInfoVO> contacts;

    @ApiModelProperty(value = "apnTags for account")
    private List<Integer> apnTags;

    @ApiModelProperty(value = "tags for account")
    private List<TagVO> tags;

    //详情页使用
    private String companyName;
    private String displayNameInEn;

    /**
     * No Poaching需求增加字段
     */
    private Boolean noPoaching;

    private String dataModifyStatus;

    private String replaceFullBusinessName;

    public AccountCompanyVO(Long id, AccountCompanyStatus active, String fullBusinessName) {
        this.id = id;
        this.active = active;
        this.fullBusinessName = fullBusinessName;
    }

    public AccountCompanyVO(Long id, String fullBusinessName, String displayNameInEn) {
        this.id = id;
        this.fullBusinessName = fullBusinessName;
        this.displayNameInEn = displayNameInEn;
    }

}
