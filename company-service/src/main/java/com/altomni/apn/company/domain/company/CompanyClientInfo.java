package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@Table(name = "company_invoice_client_info")
@NoArgsConstructor
@AllArgsConstructor
public class CompanyClientInfo extends AbstractAuditingEntity implements Serializable,Cloneable{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id ;

    /**  */
    @Column(name = "company_id")
    private Long companyId ;

    /**  */
    @Column(name = "client_name")
    private String clientName ;

    /**  */
    @Column(name = "client_division")
    private String clientDivision ;

    /**  */
    @Column(name = "client_address")
    private String clientAddress ;

    /**  */
    @Column(name = "client_location")
    private String clientLocation ;

    /**  */
    @Column(name = "client_email")
    private String clientEmail ;

    /**  */
    @Column(name = "tenant_id")
    private Long tenantId ;
}
