package com.altomni.apn.company.domain.vm;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.altomni.apn.common.domain.enumeration.application.InterviewType;
import com.altomni.apn.common.domain.enumeration.application.InterviewTypeConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.altomni.apn.company.domain.enumeration.report.ReportTableType;
import com.altomni.apn.company.domain.enumeration.report.ReportTableTypeConverter;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.Instant;


@Data
@Entity
public class PerformanceDataVM {


    private String fullName;

    private Long candidateId;

    @Id
    private Long  id;

    private Long applicationId;

    private Long jobId;

    private String jobTitle;

    private Instant jobCreatedDate;

    @Convert(converter = JobTypeConverter.class)
    private JobType jobType;

    @Convert(converter = ReportTableTypeConverter.class)
    private ReportTableType activityStatus;

    private Instant submitDate;

    private Integer interviewInfoProgress;

    @Convert(converter = InterviewTypeConverter.class)
    private InterviewType interviewInfoType;

    private Instant interviewInfoAppointment;

    private String interviewInfoTimeZone;

    private String  hrContact;

    private Instant statusUpdateDate;

    private String  recruiter;

    private String sourcer;

    private Long pteamId;

    @Transient
    private boolean isPrivateJob;

    private Integer interviewCount;

    /**
     * true: 已离职，false: 未离职
     */
    private Boolean resigned;

    /**
     * true: 是 converted to FTE 流程
     * false: 非 converted to FTE 流程
     */
    @ExcelIgnore
    private Boolean convertedToFte;
}
