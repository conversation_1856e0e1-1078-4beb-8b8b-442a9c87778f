package com.altomni.apn.company.web.rest.am;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.domain.amreport.AmReportTalentJobNote;
import com.altomni.apn.company.domain.vo.AmReportVO;
import com.altomni.apn.company.service.am.AmReportService;
import com.altomni.apn.company.service.dto.AmReportSearchDTO;
import com.altomni.apn.company.service.dto.AmReportTalentJobNoteDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.net.URISyntaxException;

/**
 * am report controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/v3/am")
public class AmReportResource {

    private final AmReportService amReportService;

    @PostMapping("/reports")
    public ResponseEntity<AmReportVO> search(@RequestBody AmReportSearchDTO condition) throws URISyntaxException {
        log.info("[APN: AmReportResource @{}] REST request to create reports: {}, companyId: {}", SecurityUtils.getUserId(), condition, condition.getCompanyId());
        AmReportVO result = amReportService.findAmReport(condition.getJobType(),condition.getCandidate(),condition.getCompanyId(), SecurityUtils.getTenantId(), true,condition.getSalesLeadList());
        result.getJobData().forEach(jobData -> {
            if (jobData.getConfidentialTalentViewAble() != null && !jobData.getConfidentialTalentViewAble()) {
                jobData.encrypt();
            }
        });
        return ResponseEntity.ok(result);
    }

    @GetMapping("/download/{companyId}")
    public void get(@PathVariable Long companyId, HttpServletResponse response) {
        log.info("[APN: AmReportResource @{}] REST request to download reports : {}", SecurityUtils.getUserId(), companyId);
        amReportService.downloadReport(companyId,response);
    }


    @PostMapping("/save-high-lighted-experience")
    public ResponseEntity<AmReportTalentJobNote> saveHighlightedExperience(@RequestBody AmReportTalentJobNoteDto amReportDto) {
//        log.info("[APN: AmReportResource @{}] REST request to save-high-lighted-experience : {}", SecurityUtils.getUserId(), amReportDto);
        AmReportTalentJobNote result = amReportService.saveHighLightedExperience(amReportDto);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/save-am-update")
    public ResponseEntity<AmReportTalentJobNote> saveAmUpdate(@RequestBody AmReportTalentJobNoteDto amReportDto) {
//        log.info("[APN: AmReportResource @{}] REST request to save-am-update : {}", SecurityUtils.getUserId(), amReportDto);
        AmReportTalentJobNote result = amReportService.saveAmUpdate(amReportDto);
        return ResponseEntity.ok(result);
    }
}
