package com.altomni.apn.company.service.redis;


import java.util.Collection;
import java.util.Set;

public interface RedisService {

    void saveCompanyId(Collection<Long> companyIds);

    void saveFailedCompanyIds(Collection<Long> companyIds);

    void saveFailedCompanyClientNoteIds(Collection<Long> companyClientNoteIds);

    void saveFailedCompanyProgressNoteIds(Collection<Long> companyProgressNoteIds);

    Set<String> getCompanyIds(long count);

    Long checkSize(String key);


}
