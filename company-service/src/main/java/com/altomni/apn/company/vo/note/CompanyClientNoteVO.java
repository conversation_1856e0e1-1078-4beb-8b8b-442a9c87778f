package com.altomni.apn.company.vo.note;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@AllArgsConstructor
@ApiModel(description = "Vo for company client note")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CompanyClientNoteVO extends AbstractAuditingEntity implements Serializable {

    @ApiModelProperty(value = "the id for client note.")
    private Long id;

    @ApiModelProperty(value = "the id for company.")
    private Long companyId;

    @ApiModelProperty(value = "the id for clientContact.")
    private List<Long> clientContactIds;

    @ApiModelProperty(value = "the contactDate for note.")
    private LocalDate contactDate;

    @ApiModelProperty(value = "the text for note.")
    private String note;

    @ApiModelProperty(value = "the name for creator.")
    private String creator;

    @ApiModelProperty(value = "the id for creator.")
    private Long creatorId;

    @ApiModelProperty(value = "the text for note.")
    private Boolean synced;

    @ApiModelProperty(value = "the reminderTime for note.")
    private Instant reminderTime;

}
