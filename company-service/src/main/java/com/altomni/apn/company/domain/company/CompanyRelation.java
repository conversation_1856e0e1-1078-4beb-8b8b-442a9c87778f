package com.altomni.apn.company.domain.company;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum CompanyRelation implements ConvertedEnum<Integer> {
    PARENT(1),
    MSP(2);
    private final Integer dbValue;

    CompanyRelation(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<CompanyRelation, Integer> resolver =
            new ReverseEnumResolver<>(CompanyRelation.class, CompanyRelation::toDbValue);

    public static CompanyRelation fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
