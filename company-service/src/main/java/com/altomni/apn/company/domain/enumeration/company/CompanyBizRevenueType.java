package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The IndustryType enumeration.
 */
public enum CompanyBizRevenueType implements ConvertedEnum<Integer> {
    LESS_THAN_ONE_MILLION(1),
    ONE_MILLION_TO_TEN_MILLION(10),
    TEN_MILLION_TO_FIFTY_MILLION(50),
    FIFTY_MILLION_TO_ONE_HUNDRED_MILLION(100),
    ONE_HUNDRED_MILLION_TO_FIVE_HUNDRED_MILLION(500),
    FIVE_HUNDRED_MILLION_TO_ONE_BILLION(1000),
    ONE_BILLION_TO_TEN_BILLION(10000),
    ABOVE_TEN_BILLION(999999);

    private final int dbValue;

    CompanyBizRevenueType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CompanyBizRevenueType, Integer> resolver = new ReverseEnumResolver<>(CompanyBizRevenueType.class, CompanyBizRevenueType::toDbValue);

    public static CompanyBizRevenueType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
