package com.altomni.apn.company.vo.contact;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class ContactInfoVO implements Serializable {

    private Long id;

    private Integer type;

    private String contact;

    private String details;

}
