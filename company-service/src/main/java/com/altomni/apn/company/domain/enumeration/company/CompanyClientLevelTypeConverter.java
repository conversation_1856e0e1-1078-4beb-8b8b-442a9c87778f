package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CompanyClientLevelTypeConverter extends AbstractAttributeConverter<CompanyClientLevelType, Integer> {
    public CompanyClientLevelTypeConverter() {
        super(CompanyClientLevelType::toDbValue, CompanyClientLevelType::fromDbValue);
    }
}
