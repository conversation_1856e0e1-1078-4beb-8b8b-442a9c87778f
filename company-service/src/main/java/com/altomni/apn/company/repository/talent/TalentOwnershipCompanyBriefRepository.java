package com.altomni.apn.company.repository.talent;

import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;


/**
 * Spring Data  repository for the TalentOwner entity.
 */
@Repository
public interface TalentOwnershipCompanyBriefRepository extends JpaRepository<TalentOwnership, Long> {

    List<TalentOwnership> findAllByTalentId(Long talentId);

    List<TalentOwnership> findAllByTalentIdIn(List<Long> talentIds);

    List<TalentOwnership> findAllByTalentIdAndOwnershipTypeIn(Long talentId, List<TalentOwnershipType> types);

    List<TalentOwnership> findAllByTalentIdInAndOwnershipTypeIn(List<Long> talentIds, List<TalentOwnershipType> types);

}
