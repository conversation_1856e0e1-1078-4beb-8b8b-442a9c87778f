package com.altomni.apn.company.service.am.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.amreport.AmReportTalentJobNote;
import com.altomni.apn.company.domain.amreport.HrContact;
import com.altomni.apn.company.domain.business.BusinessFlowAdministrator;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.enumeration.job.InterviewType;
import com.altomni.apn.company.domain.enumeration.job.NodeType;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.company.domain.vo.AmReportSummaryVO;
import com.altomni.apn.company.domain.vo.AmReportVO;
import com.altomni.apn.company.domain.vo.CompanyVo;
import com.altomni.apn.company.domain.vo.JobDataVo;
import com.altomni.apn.company.repository.amreport.AmReportTalentJobNoteRepository;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.job.JobCompanyBriefRepository;
import com.altomni.apn.company.repository.business.AccountBusinessAdministratorRepository;
import com.altomni.apn.company.service.am.AmReportService;
import com.altomni.apn.company.service.dto.AmEmailDTO;
import com.altomni.apn.company.service.dto.AmInfoDTO;
import com.altomni.apn.company.service.dto.AmReportTalentJobNoteDto;
import com.altomni.apn.company.service.email.MailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.entity.ContentType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.common.utils.SqlUtil.PARTITION_COUNT_999;

/**
 * amReportService
 * <AUTHOR>
 */
@Slf4j
@Service("amReportService")
public class AmReportServiceImpl implements AmReportService {

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private MailService mailService;

    @Resource
    private EntityManager entityManager;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private AccountBusinessAdministratorRepository accountBusinessAdministratorRepository;

    @Resource
    private AmReportTalentJobNoteRepository amReportTalentJobNoteRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    @Resource
    private JobCompanyBriefRepository jobCompanyBriefRepository;

    @Resource
    private AmReportServiceImpl self;

    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Override
    public AmReportVO findAmReport(List<JobType> jobType, List<Long> contact, Long companyId, Long tenantId, Boolean needTotalFlag,List<Long> salesLeadList) {
        boolean loginFlag = ObjectUtil.isNotNull(tenantId);
        if (!loginFlag && !isCanAccessReport(companyId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_FINDAMREPORT_NOPERMISSON.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        if (CollUtil.isEmpty(jobType)) {
            jobType = CollUtil.newArrayList(JobType.CONTRACT, JobType.MSP, JobType.FULL_TIME);
        }
        AmReportVO amReportVO = new AmReportVO();
        AmReportSummaryVO amReportSummaryVO = new AmReportSummaryVO();
        amReportVO.setSummary(amReportSummaryVO);

        List<JobType> finalJobType = jobType;
        CompletableFuture<List<JobDataVo>> jobDataVoListFuture = CompletableFuture.supplyAsync(() -> findJobDataByCompany(finalJobType, contact, companyId, tenantId,salesLeadList), executor);
        CompletableFuture<List<JobDataVo>> allJobDataVoListFuture = CompletableFuture.supplyAsync(() -> findAllJobByCompany(List.of(JobType.CONTRACT, JobType.MSP, JobType.FULL_TIME), companyId, tenantId), executor);
        List<Long> longList = needTotalFlag ? statisticsOffersByCompany(companyId, tenantId) : Collections.emptyList();
        //table
        List<JobDataVo> jobDataVOList = jobDataVoListFuture.join();
        //summary
        List<JobDataVo> allJobDataVoList = allJobDataVoListFuture.join();

        List<JobDataVo> finalJobDataVOList = self.convertAndSortJobData(jobDataVOList);
        amReportVO.setJobData(finalJobDataVOList);
        getSummaryData(amReportSummaryVO, allJobDataVoList);
        if (needTotalFlag) {
            amReportSummaryVO.setTotalOffersByClient((longList.get(0)).intValue());
            amReportSummaryVO.setTotalOfferAccepted((longList.get(1)).intValue());
            amReportSummaryVO.setTotalOnBoard((longList.get(2)).intValue());
        }
        return amReportVO;
    }

    @ProcessConfidentialTalent(operation = ProcessConfidentialTalent.operation.DO_NOTHING)
    public List<JobDataVo> convertAndSortJobData(List<JobDataVo> jobDataVOList) {
        if (CollUtil.isEmpty(jobDataVOList)) {
            return Collections.emptyList();
        }
        jobDataVOList = jobDataVOList.stream().filter(jobStatus -> REQUIRED_ACTIVITY.contains(jobStatus.getNodeType())).collect(Collectors.toList());
        convertJobDate(jobDataVOList);
        return sortJobDataVOList(jobDataVOList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AmReportTalentJobNote saveHighLightedExperience(AmReportTalentJobNoteDto dto) {
        if (dto == null || dto.getTalentId() == null || dto.getJobId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        AmReportTalentJobNote old = amReportTalentJobNoteRepository.findAmReportTalentJobNoteByTalentIdAndJobIdAndFrequency(dto.getTalentId(), dto.getJobId(), 1);
        if (old == null) {
            AmReportTalentJobNote newEntity = new AmReportTalentJobNote();
            BeanUtil.copyProperties(dto, newEntity);
            newEntity.setFrequency(1);
            return amReportTalentJobNoteRepository.save(newEntity);
        }
        old.setHighLightedExperience(dto.getHighLightedExperience());
        return amReportTalentJobNoteRepository.save(old);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AmReportTalentJobNote saveAmUpdate(AmReportTalentJobNoteDto dto) {
        if (dto == null || dto.getTalentId() == null || dto.getJobId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        AmReportTalentJobNote old = amReportTalentJobNoteRepository.findAmReportTalentJobNoteByTalentIdAndJobIdAndFrequency(dto.getTalentId(), dto.getJobId(), 1);
        AmReportTalentJobNote newEntity = new AmReportTalentJobNote();
        BeanUtil.copyProperties(dto, newEntity);
        newEntity.setFrequency(1);
        if (old == null) {
            return amReportTalentJobNoteRepository.save(newEntity);
        }
        if (Objects.equals(dto.getAmUpdate(), old.getAmUpdate())) {
            return old;
        }
        old.setFrequency(0);
        amReportTalentJobNoteRepository.save(old);
        newEntity.setHighLightedExperience(old.getHighLightedExperience());
        return amReportTalentJobNoteRepository.save(newEntity);
    }

    @Override
    public void downloadReport(Long companyId, HttpServletResponse response) {
        AmReportVO amReportVO = findAmReport( null, null, companyId, SecurityUtils.getTenantId(), true,null);
        try {
            response.setHeader( "Content-Disposition", "attachment;filename=" + URLEncoder.encode("am-report.xls", StandardCharsets.UTF_8));
            createAmReportExcel(amReportVO, response.getOutputStream());
        } catch (IOException e) {
            log.error("error", e);
        }
    }

    @Override
    public void sendAmReportAmEmail(CompanyVo company, String mailTemplate, String imageTemplate) {
        // get send email am
        AmEmailDTO amEmailDTO = getCompanyAmEmail(company);
        if (amEmailDTO == null || CollUtil.isEmpty(amEmailDTO.getMails())) {
            return;
        }
        try {
            log.info("[apn] amReportNeedSendEmail, company id = [{}], email = [{}]", company.getId(), amEmailDTO.getMails());
            AmReportVO amReportVO = findAmReport(null, null, company.getId(), company.getTenantId(), true,null);
            String subject = "【" + company.getName() + "】" + " Weekly Candidates Pipeline Report -【" + amEmailDTO.getDate() + "】";
            MultipartFile multipartFile = createExcelFile(amReportVO,subject);
            log.info("[apn] amReportExcelCreateSuccess, company id = [{}]", company.getId());
            String base64 = createAmReportImage(amReportVO, company, imageTemplate);
            if (base64 == null) {
                return;
            }
            log.info("[apn] amReportImageCreateSuccess, company id = [{}]", company.getId());
            Map<String,Object> map  = new HashMap<>(16);
            map.put("image", base64);
            String content = Html2ImageUtils.convertHtmlTemplate(mailTemplate, map);
            MultiValueMap<String, String> multiValueMap = new LinkedMultiValueMap<>();
            multiValueMap.put("from", CollUtil.newArrayList(applicationProperties.getSupportSender()));
            multiValueMap.add("to", JSONUtil.toJsonStr(amEmailDTO.getMails()));
            multiValueMap.put("subject", CollUtil.newArrayList(subject));
            multiValueMap.put("html_content", CollUtil.newArrayList(content));
            if (multipartFile != null) {
                multiValueMap.add("name", multipartFile.getName());
                multiValueMap.add("fileName", multipartFile.getOriginalFilename());
                multiValueMap.add("bufferByte", Base64.encodeBase64String(multipartFile.getBytes()));
                multiValueMap.add("contentType", multipartFile.getContentType());
            }
            mailService.sendRichMailByFeign(multiValueMap);
            log.info("[apn] amReportSendEmailSuccess, company id = [{}], to = [{}]", company.getId(), JSONUtil.toJsonStr(amEmailDTO.getMails()));
        } catch (Exception e) {
            //todo compensation mechanism
            log.error("sendAmReportAmEmail param:{} error: {}", JSONUtil.toJsonStr(company), ExceptionUtils.getStackTrace(e));
        }
    }

    private AmEmailDTO getCompanyAmEmail(CompanyVo company) {
        List<AmInfoDTO> amInfoDTOList = accountBusinessAdministratorRepository.findAmByCompanyId(company.getId());
        if (CollUtil.isEmpty(amInfoDTOList)) {
            return null;
        }
        AmEmailDTO emailDTO = new AmEmailDTO();
        List<String> email = new LinkedList<>();
        amInfoDTOList.forEach(am -> {
            if (StrUtil.isBlank(am.getTimeZone())) {
                am.setTimeZone(DateUtil.US_LA_TIMEZONE);
            }
            //get am local time
            LocalDateTime date = LocalDateTime.now(ZoneId.of(am.getTimeZone()));
            log.info("amReportDateAndTimeZone = [{}, {}]", date, am.getTimeZone());
            int hour = date.getHour();
            DayOfWeek week = date.getDayOfWeek();
//            int startSendEmailTime = 9;
            int startSendEmailTime = 10;
            if (hour == startSendEmailTime && week == DayOfWeek.WEDNESDAY) {
                email.add(am.getEmail());
                emailDTO.setDate((date.getMonth().getValue())+ "/" + date.getDayOfMonth() + "/" + date.getYear());
            }
        });
        emailDTO.setMails(email);
        return emailDTO;
    }

    private MultipartFile createExcelFile(AmReportVO amReportVO,String name) {
        FileOutputStream outputStream = null;
        FileInputStream inputStream = null;
        MultipartFile multipartFile = null;
        File f = null;
        try {
            File baseFile = new File("amReportExcel");
            if(!baseFile.exists()) {
                FileUtil.mkdir(baseFile);
            }
            String path = baseFile.getAbsolutePath() + File.separator + IdUtil.fastSimpleUUID() +".xls";
            f = new File(path);
            outputStream = new FileOutputStream(f);
            inputStream = new FileInputStream(f);
            createAmReportExcel(amReportVO, outputStream);
            multipartFile = new MockMultipartFile(name + ".xls", name + ".xls", ContentType.APPLICATION_OCTET_STREAM.toString(), inputStream);
        } catch (IOException e) {
            log.error("error", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
                FileUtil.del(f);
            } catch (IOException e) {
                log.error("error", e);
            }
        }
        return multipartFile;
    }

    public String createAmReportImage( AmReportVO amReportVO ,CompanyVo company,String template) {
        List<JobDataVo> reports = amReportVO.getJobData();
        if(CollectionUtils.isEmpty(reports)) {
            log.info("[apn] create am report image is null, amReportVo = [{}]", JSONUtil.toJsonStr(amReportVO));
            return null;
        }
        try {
            log.info("[apn] create am report image start company id = [{}]", company.getId());
            Map<String,Object> valueMap = new HashMap<>(16);
            AmReportSummaryVO summary = amReportVO.getSummary();
            valueMap.put("companyName",company.getName());
            valueMap.put("activeJobs",summary.getActiveJob());
            valueMap.put("newCandidates",summary.getNewCandidate());
            valueMap.put("activeCandidates",summary.getActiveCandidate());
            valueMap.put("totalCandidates",summary.getTotalCandidate());
            valueMap.put("totalOffersByClients",summary.getTotalOffersByClient());
            valueMap.put("totalOffersAccepted",summary.getTotalOfferAccepted());
            valueMap.put("totalOnBoarded",summary.getTotalOnBoard());
            StringBuilder sb = new StringBuilder();
            Map<NodeType,String> nodeTypeMap = getActivityStatusClass();
            for(JobDataVo jobDataVO:reports) {
                sb.append("<tr>");
                sb.append("<td class=\"td\">").append(jobDataVO.getFullName()).append("</td>");
                sb.append("<td class=\"td\">").append(jobDataVO.getJobTitle()).append("</td>");
                sb.append(nodeTypeMap.get(jobDataVO.getNodeType())).append(jobDataVO.getNodeType().name()).append("</div></td>");
                sb.append("<td class=\"td\">").append(StrUtil.emptyToDefault(jobDataVO.getInterviewInfo(), "")).append("</td>");
                sb.append("<td class=\"td\">").append(DateUtil.instantToDateString(jobDataVO.getSubmitTime())).append("</td>");
                sb.append("<td class=\"td\">").append(ObjectUtil.isNull(jobDataVO.getAgingDays())? "": String.valueOf(jobDataVO.getAgingDays())).append("</td>");
                sb.append("<td class=\"td\">").append(StrUtil.emptyToDefault(jobDataVO.getHighLightedExperience(), "")).append("</td>");
                sb.append("<td class=\"td\">").append(StrUtil.emptyToDefault(jobDataVO.getAmUpdate(), "")).append("</td>");
                sb.append("</tr>");
            }
            valueMap.put("jobList",sb.toString());
            template = Html2ImageUtils.convertHtmlTemplate(template, valueMap);
            log.info("[apn] get am report image html, company id = [{}]", company.getId());
            //create photo
            byte[] bytes = Html2ImageUtils.html2Image(template);
            log.info("[apn] html to image, company id = [{}]", company.getId());
            String base64Str = new String(Base64.encodeBase64(bytes));
            return "data:image/png;base64," + base64Str;
        } catch (Exception e) {
            log.error("error", e);
            log.info("[apn] create am report image is error, company id = [{}], html = [{}], error = [{}]", company.getId(), template, ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    private Map<NodeType, String> getActivityStatusClass() {
        Map<NodeType,String> map = new HashMap<>(16);
        map.put(NodeType.SUBMIT_TO_CLIENT, commonStyle("#21B66E"));
        map.put(NodeType.INTERVIEW, commonStyle("#FDAB29"));
        map.put(NodeType.OFFER, commonStyle("#F56D50"));
        map.put(NodeType.OFFER_ACCEPT, commonStyle("#F56D50"));
        map.put(NodeType.ON_BOARD, commonStyle("#1890FF"));
        return map;
    }

    private String commonStyle(String s) {
        return "<td><div style=\" width:150px;" +
                "        height:20px;" +
                "        border-radius: 25px;" +
                "        background-color: " + s + ";" +
                "        font-size: 12px;" +
                "        line-height: 10px;" +
                "        color:azure;" +
                "        padding:5px;" +
                "        box-sizing: border-box;\">";
    }

    public void createAmReportExcel(AmReportVO amReportVO, OutputStream outputStream) {
        List<JobDataVo> jobData = amReportVO.getJobData().stream().map(d -> d.setNodeType(d.getResigned() ? NodeType.OFF_BOARDED : d.getNodeType())).toList();
        findHrContact(jobData);
        AmReportSummaryVO summaryVO = amReportVO.getSummary();
        List<AmReportSummaryVO> summaryVoS = new LinkedList<>();
        summaryVoS.add(summaryVO);
        ExcelWriter excelWriter = null;
        try {
            Map<JobType, List<JobDataVo>> typeMap = jobData.stream().collect(Collectors.groupingBy(JobDataVo::getJobType));
            excelWriter = EasyExcelFactory.write(outputStream).build();
            int sheetIndex = 0;
            //export summary
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(sheetIndex++,"SUMMARY").head(AmReportSummaryVO.class).needHead(true).build();
            excelWriter.write(summaryVoS, writeSheet);
            WriteTable writeTable;
            for (Map.Entry<JobType, List<JobDataVo>> entry :typeMap.entrySet()) {
                Map<Long, List<JobDataVo>> contact = entry.getValue().stream().filter(vo -> ObjectUtil.isNotEmpty(vo.getHrContactId())).collect(Collectors.groupingBy(JobDataVo::getHrContactId));
                writeSheet = EasyExcelFactory.writerSheet(sheetIndex++, entry.getKey().getName()).build();
                int i = 0;
                List<JobDataVo> value;
                for (Map.Entry<Long,List<JobDataVo>> entryContact :contact.entrySet()) {
                    value = entryContact.getValue();
                    if (CollUtil.isEmpty(value)) {
                        continue;
                    }
                    List<HrContact> hr = new ArrayList<>();
                    hr.add(new HrContact(value.get(0).getHrContact()));
                    writeTable = EasyExcelFactory.writerTable(i++).head(HrContact.class).needHead(false).build();
                    excelWriter.write(hr, writeSheet, writeTable);
                    writeTable = EasyExcelFactory.writerTable(i++).head(JobDataVo.class).needHead(true).build();
                    value.add(new JobDataVo());
                    //to update time str
                    value = value.stream().peek(vo -> {
                        if (ObjectUtil.isNotNull(vo.getSubmitTime())) {
                            vo.setSubmitTimeStr(DateUtil.instantToDateString(vo.getSubmitTime()));
                        }
                        // 没有保密候选人查看权限的情况下，需要将保密数据屏蔽
                        if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                            ExcelUtil.maskConfidentialTalentData(vo);
                        }
                    }).collect(Collectors.toList());
                    excelWriter.write(value, writeSheet, writeTable);
                }
            }
            outputStream.flush();
        } catch (Exception e) {
            log.error("error", e);
        } finally {
            if (excelWriter != null)  {
                excelWriter.finish();
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error("error", e);
                }
            }
        }
    }

    private void findHrContact(List<JobDataVo> jobData) {
        if (CollUtil.isEmpty(jobData)) {
            return;
        }
        List<Long> jobIdList = jobData.stream().map(JobDataVo::getJobId).distinct().collect(Collectors.toList());
        String selectSql = " select jcr.client_contact_id as hr_contact_id, t.full_name as hr_contact, jcr.job_id from company_sales_lead_client_contact cc left join talent t on t.id = cc.talent_id left join job_company_contact_relation jcr on jcr.client_contact_id = cc.id where jcr.job_id in (?1) ";
        List<HrContact> hrContactList = CollUtil.split(jobIdList, PARTITION_COUNT_999).stream().map(ids -> {
            Map<Integer, Object> paramMap = new HashMap<>(16);
            paramMap.put(1, ids);
            return searchData(selectSql, HrContact.class, paramMap);
        }).flatMap(List::stream).collect(Collectors.toList());
        if (CollUtil.isEmpty(hrContactList)) {
            return;
        }
        Map<Long, List<HrContact>> mapList = hrContactList.stream().collect(Collectors.groupingBy(HrContact::getJobId));
        jobData.forEach(job -> Optional.ofNullable(mapList.get(job.getJobId())).ifPresent(hr -> {
            HrContact hrContact = hr.get(0);
            job.setHrContact(hrContact.getHrContact());
            job.setHrContactId(hrContact.getHrContactId());
        }));
    }

    private List<Long> statisticsOffersByCompany(Long companyId, Long tenantId) {
        List<Long> longList = new LinkedList<>();
        StringBuilder commonStr = new StringBuilder();
        commonStr
                .append(" left join job J on J.id = trp.job_id ")
                .append(" left join company C on C.id = J.company_id ")
                .append(" where C.id =?1 and trp.tenant_id =?2 and J.status in (1,5,0,2,3,4) ");
        // offers by clients
        String offerStr = " select count(trpo.id) from talent_recruitment_process_offer trpo " +
                " left join talent_recruitment_process trp on trpo.talent_recruitment_process_id = trp.id " +
                commonStr;
        // offers accepted
        String offerAcceptStr = " select count(trpioa.id) from talent_recruitment_process_ipg_offer_accept trpioa " +
                " left join talent_recruitment_process trp on trpioa.talent_recruitment_process_id = trp.id " +
                commonStr;
        // on board
        String onboardStr = " select count(DISTINCT trpon.talent_recruitment_process_id) from talent_recruitment_process trp " +
                " LEFT JOIN talent_recruitment_process_node trpon ON trpon.talent_recruitment_process_id = trp.id and trpon.node_type=60  and trpon.node_status = 1 " +
                commonStr;
        Map<Integer, Object> param = new HashMap<>(16);
        param.put(1, companyId);
        param.put(2, tenantId);
        List<CompletableFuture<Long>> countFutureList = Stream.of(offerStr, offerAcceptStr, onboardStr)
            .map(sql -> CompletableFuture.supplyAsync(() -> searchCount(sql, param), executor)).toList();
        CompletableFuture.allOf(countFutureList.toArray(new CompletableFuture[0])).join();

        countFutureList.forEach(future -> {
            longList.add(future.join());
        });
        return longList;
    }

    private void convertJobDate(List<JobDataVo> jobDataVOList) {
        if (CollUtil.isNotEmpty(jobDataVOList)) {
            Set<Long> privateJobTeamIds = jobCompanyBriefRepository.findPrivateJobTeamIds(SecurityUtils.getTenantId());
            jobDataVOList.forEach(jobData -> {
                if (Objects.equals(NodeType.INTERVIEW, jobData.getNodeType())) {
                    String eventStage = jobData.getProgress() == null? "": "ROUND_" + jobData.getProgress();
                    String eventType = jobData.getInterviewType() == null? "": InterviewType.fromDbValue(jobData.getInterviewType()).name();
                    String timeZone = jobData.getTimeZone() == null? "": jobData.getTimeZone();
                    jobData.setInterviewInfo(eventStage + "   " + eventType + "   \r\n" + DateUtil.fromInstantToDate(jobData.getLatestInterviewDate(), jobData.getTimeZone()) + "   \r\n" + timeZone);
                    jobData.setEventStage(eventStage);
                    jobData.setEventType(eventType);
                }
                if (jobData.getLatestInterviewDate() != null && Objects.equals(jobData.getNodeType(), NodeType.INTERVIEW)) {
                    if (Instant.now().compareTo(jobData.getLatestInterviewDate()) > 0) {
                        jobData.setAgingDays(Integer.valueOf(DateUtil.calculateDays(Instant.now(), jobData.getLatestInterviewDate())));
                    } else {
                        jobData.setAgingDays(-Integer.parseInt(DateUtil.calculateDays(jobData.getLatestInterviewDate(), Instant.now())));
                    }
                    if (jobData.getAgingDays() != null && jobData.getAgingDays() < 0) {
                        jobData.setAgingDays(null);
                    }
                } else if (Objects.equals(jobData.getNodeType(), NodeType.ON_BOARD)) {
                    jobData.setAgingDays(null);
                } else {
                    jobData.setAgingDays(Integer.valueOf(DateUtil.calculateDays(Instant.now(), jobData.getActivityUpdateDate())));
                }
                jobData.setIsPrivateJob(privateJobTeamIds.contains(jobData.getPteamId()));
            });
        }
    }

    private List<JobDataVo> sortJobDataVOList(List<JobDataVo> jobDataVOList) {
        // 保证顺序, 在title 不相同的情况下不排序, 相同的情况下根据 agingDays 降序排列
        Map<String, List<JobDataVo>> groupedMap = jobDataVOList.stream()
                .collect(Collectors.groupingBy(JobDataVo::getJobTitle,
                        // linkedHashMap 保留原始顺序
                        LinkedHashMap::new, Collectors.toList()));

        List<JobDataVo> sortedList = new ArrayList<>();
        for (List<JobDataVo> group : groupedMap.values()) {
            sortedList.addAll(group.stream()
                    .sorted(Comparator.comparing(JobDataVo::getAgingDays, Comparator.nullsLast(Integer::compare)).reversed())
                    .collect(Collectors.toList())
            );
        }
        return sortedList;
    }

    private List<JobDataVo> findAllJobByCompany(List<JobType> jobType, Long companyId, Long tenantId) {
        StringBuilder searchSb = new StringBuilder();
        searchSb.append(" select coalesce(trp.id, j.id) as id, trp.id as application_id, tt.id as candidate_id, tt.full_name as full_name,  ")
                .append(" j.id as job_id, j.title as job_title, rp.job_type, j.status as job_status, j.pteam_id, ")
                .append(" tt.id as candidate_id, tt.full_name as full_name, ")
                .append(" trpstc.submit_time, ")
                .append(" trpn.node_type, trpn.last_modified_date as activity_update_date, trpn.node_status, ")
                .append(" trpi.from_time as latest_interview_date, trpi.progress, trpi.interview_type, trpi.time_zone, ")
                .append(" artjn.high_lighted_experience, artjn.am_update, null interview_count, if(resign.id is null, false, true) resigned, if(star.id is null, false, true) as converted_to_fte ")
                .append(" from job j left join talent_recruitment_process trp on j.id = trp.job_id ")
                .append(" left join recruitment_process rp on rp.id = j.recruitment_process_id ")
                .append(" left join (select * from talent_recruitment_process_node where node_status in (1,4)) trpn on trpn.talent_recruitment_process_id = trp.id ")
                .append(" left join talent tt on tt.id = trp.talent_id ")
                .append(" left join am_report_talent_job_note artjn on artjn.talent_id = trp.talent_id and artjn.job_id = trp.job_id and artjn.frequency = 1 ")
                .append(" left join talent_recruitment_process_submit_to_client trpstc on trpstc.talent_recruitment_process_id = trp.id ")
                .append(" left join talent_recruitment_process_resignation resign on resign.talent_recruitment_process_id = trp.id ")
                .append(" left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5 ")
                .append(" left join (select a.from_time,a.talent_recruitment_process_id,a.progress,a.interview_type,a.time_zone from talent_recruitment_process_interview a right join ")
                .append(" (select max(id) as id from talent_recruitment_process_interview group by talent_recruitment_process_id) b on a.id = b.id) trpi on trpi.talent_recruitment_process_id = trp.id ")
                .append(" where j.company_id = ?2 and j.tenant_id = ?3 and j.status in (0,1,2,3,4,5) ")
                .append(" AND (coalesce(?1) IS NULL OR rp.job_type IN (?1)) ");
        Map<Integer, Object> param = new HashMap<>(16);
        param.put(1, jobType.stream().map(JobType::toDbValue).collect(Collectors.toList()));
        param.put(2, companyId);
        param.put(3, tenantId);
        return searchData(searchSb.toString(), JobDataVo.class, param);
    }

    private <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        map.forEach((k,v) -> ReflectUtil.invoke(query, method, k, v));
        return query.getResultList();
    }

    private Long searchCount(String queryStr, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        map.forEach((k,v) -> ReflectUtil.invoke(query, method, k, v));
        return Long.parseLong(String.valueOf(query.getSingleResult()));
    }

    private List<JobDataVo> findJobDataByCompany(List<JobType> jobType, List<Long> contact, Long companyId, Long tenantId,List<Long> salesLeadList) {
        String sql = """
                select coalesce(trp.id, j.id) as id, trp.id as application_id, tt.id as candidate_id, tt.full_name as full_name, 
                j.id as job_id, j.title as job_title, rp.job_type, j.status as job_status, j.pteam_id,trpstc.submit_time,
                trpn.node_type, trpn.last_modified_date as activity_update_date, trpn.node_status,
                trpi.from_time as latest_interview_date, trpi.progress, trpi.interview_type, trpi.time_zone,
                artjn.high_lighted_experience, artjn.am_update, (select count(trpia.id) from talent_recruitment_process_interview trpia where trp.id = trpia.talent_recruitment_process_id) interview_count, 
                if(resign.id is null, false, true) resigned, if(star.id is null, false, true) as converted_to_fte  
                from talent_recruitment_process trp
                right join (select * from talent_recruitment_process_node where node_status = 1 and node_type > 10) trpn on trpn.talent_recruitment_process_id = trp.id
                left join job j on trp.job_id = j.id
                left join recruitment_process rp on rp.id = j.recruitment_process_id
                left join talent tt on tt.id = trp.talent_id
                left join job_company_contact_relation jcr on jcr.job_id = j.id
                left join talent_recruitment_process_submit_to_client trpstc on trpstc.talent_recruitment_process_id = trp.id
                left join talent_recruitment_process_resignation resign on resign.talent_recruitment_process_id = trp.id
                left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5
                left join am_report_talent_job_note artjn on artjn.talent_id = trp.talent_id and artjn.job_id = trp.job_id and artjn.frequency = 1
                left join (select a.from_time,a.talent_recruitment_process_id,a.progress,a.interview_type,a.time_zone from talent_recruitment_process_interview a right join
                (select max(id) as id from talent_recruitment_process_interview group by talent_recruitment_process_id) b on a.id = b.id) trpi on trpi.talent_recruitment_process_id = trp.id
                where j.company_id = ?3 and j.tenant_id = ?4 and j.status in (0,1,2,5)
                AND (coalesce(?1) IS NULL OR rp.job_type IN (?1))
                AND (coalesce(?2) IS NULL OR jcr.client_contact_id IN (?2))
                
                """;
        StringBuilder dataSql = new StringBuilder();
        dataSql.append(sql);
        if(CollUtil.isNotEmpty(salesLeadList)){
            dataSql.append(" and j.sales_lead_id in(?5)");
        }
        Map<Integer, Object> param = new HashMap<>(16);
        param.put(1, jobType.stream().map(JobType::toDbValue).collect(Collectors.toList()));
        param.put(2, contact);
        param.put(3, companyId);
        param.put(4, tenantId);
        if(CollUtil.isNotEmpty(salesLeadList)){
            param.put(5, salesLeadList);
        }
        return searchData(dataSql.toString(), JobDataVo.class, param).stream().map(d -> d.setResigned(BooleanUtils.isTrue(d.getConvertedToFte()) ? Boolean.FALSE : d.getResigned())).toList();
    }

    private boolean isCanAccessReport(Long companyId) {
        Company company = companyRepository.findById(companyId).orElse(null);
        if (ObjectUtil.isNull(company)) {
            return false;
        }
        List<BusinessFlowAdministrator> businessFlowAdministratorList = accountBusinessAdministratorRepository.findAllByCompanyIdAndSalesLeadRoleTypeAndUserId(companyId, Arrays.asList(SalesLeadRoleType.ACCOUNT_MANAGER.toDbValue(),SalesLeadRoleType.COOPERATE_ACCOUNT_MANAGER.toDbValue()), SecurityUtils.getUserId());
        return CollUtil.isNotEmpty(businessFlowAdministratorList);
    }

    static final List<NodeType> REQUIRED_ACTIVITY = Arrays.asList(
            NodeType.SUBMIT_TO_CLIENT,
            NodeType.INTERVIEW,
            NodeType.OFFER,
            NodeType.OFFER_ACCEPT,
            NodeType.ON_BOARD);

    static List<Integer> activeJobCondition = CollUtil.newArrayList(JobStatus.OPEN.toDbValue());

    static List<Integer> activeCandidateCondition = Arrays.asList(
            JobStatus.OPEN.toDbValue(),
            JobStatus.FILLED.toDbValue(),
            JobStatus.ONHOLD.toDbValue());

    static List<Integer> completeCondition = Arrays.asList(
            NodeType.SUBMIT_TO_CLIENT.toDbValue(),
            NodeType.INTERVIEW.toDbValue(),
            NodeType.OFFER.toDbValue(),
            NodeType.OFFER_ACCEPT.toDbValue());

    private void getSummaryData(AmReportSummaryVO amReportSummaryVO, List<JobDataVo> jobDataVOList) {
        Set<Long> activeCandidateSet = new HashSet<>();
        Set<Long> newCandidateSet = new HashSet<>();
        Set<Long> candidatesSet = new HashSet<>();
        Set<Long> jobIdsSet = new HashSet<>();
        if(CollectionUtils.isNotEmpty(jobDataVOList)) {
            for(JobDataVo jobDataVO : jobDataVOList) {
                if (activeJobCondition.contains(jobDataVO.getJobStatus().toDbValue())) {
                    jobIdsSet.add(jobDataVO.getJobId());
                }
                Long candidateId = jobDataVO.getCandidateId();
                if (jobDataVO.getCandidateId() == null) {continue;}
                candidatesSet.add(candidateId);
                if (jobDataVO.getNodeType() == null) {continue;}
                if (activeCandidateCondition.contains(jobDataVO.getJobStatus().toDbValue())
                        && completeCondition.contains(jobDataVO.getNodeType().toDbValue())
                        && NodeStatus.ACTIVE.toDbValue().equals(jobDataVO.getNodeStatus())) {
                    activeCandidateSet.add(candidateId);
                }
                if (completeCondition.contains(jobDataVO.getNodeType().toDbValue())
                        && jobDataVO.getActivityUpdateDate().isAfter(DateUtil.getLastWeekDate(Calendar.FRIDAY, jobDataVO.getTimeZone()))
                        && jobDataVO.getActivityUpdateDate().isBefore(DateUtil.getCurrentWeekDate(Calendar.THURSDAY, jobDataVO.getTimeZone()))
                        && NodeStatus.ACTIVE.toDbValue().equals(jobDataVO.getNodeStatus())) {
                    newCandidateSet.add(candidateId);
                }
            }
        }
        amReportSummaryVO.setActiveCandidate(activeCandidateSet.size());
        amReportSummaryVO.setActiveJob(jobIdsSet.size());
        amReportSummaryVO.setNewCandidate(newCandidateSet.size());
        amReportSummaryVO.setTotalCandidate(candidatesSet.size());
    }



}
























