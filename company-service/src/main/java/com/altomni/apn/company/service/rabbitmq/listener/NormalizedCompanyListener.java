package com.altomni.apn.company.service.rabbitmq.listener;

//import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.altomni.apn.company.service.elastic.EsFillerCompanyService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@RefreshScope
public class NormalizedCompanyListener {

    @Resource
    private EsFillerCompanyService esFillerCompanyService;

    /*@RabbitListener(queues = RabbitMqConfig.NORMALIZED_JOB_QUEUE)
    @RabbitHandler
    public void processHandler(Channel channel, Message message) throws IOException {
        //log.info("Normalized job message received：{}", message.toString());
        String normalizedJob = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("normalizedJob_from_es");
        log.info(normalizedJob);
        esFillerJobService.saveNormalizedJobInfos(normalizedJob);
        //channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }*/

    @RabbitListener(containerFactory = "esfillerFactory", queues = {"${application.esfillerMQ.apnNormalizedCompanyQueue}"})
    @RabbitHandler
    public void processHandler(Channel channel, Message message) throws IOException {
//        log.info("Normalized company message received：{}", message.toString());
        String normalizedCompany = new String(message.getBody(), StandardCharsets.UTF_8);
//        log.info("normalizedCompany_from_es: {}", normalizedCompany);
        esFillerCompanyService.saveNormalizedCompanyInfos(normalizedCompany);
        //channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }

//    @RabbitListener(containerFactory = "esfillerFactory", queues = {"${application.esfillerMQ.apnNormalizedCompanyClientNoteQueue}"})
//    @RabbitHandler
//    public void clientNoteProcessHandler(Channel channel, Message message) throws IOException {
////        log.info("Normalized companyClientNote message received：{}", message.toString());
//        String normalizedCompanyClientNote = new String(message.getBody(), StandardCharsets.UTF_8);
////        log.info("normalizedCompanyClientNote_from_es: {}", normalizedCompanyClientNote);
//        esFillerCompanyService.saveNormalizedCompanyClientNoteInfos(normalizedCompanyClientNote);
//        //channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//    }
//
//    @RabbitListener(containerFactory = "esfillerFactory", queues = {"${application.esfillerMQ.apnNormalizedCompanyProgressNoteQueue}"})
//    @RabbitHandler
//    public void progressNoteProcessHandler(Channel channel, Message message) throws IOException {
////        log.info("Normalized companyProgressNote message received：{}", message.toString());
//        String normalizedCompanyProgressNote = new String(message.getBody(), StandardCharsets.UTF_8);
////        log.info("normalizedCompanyProgressNote_from_es: {}", normalizedCompanyProgressNote);
//        esFillerCompanyService.saveNormalizedCompanyProgressNoteInfos(normalizedCompanyProgressNote);
//        //channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
//    }
}
