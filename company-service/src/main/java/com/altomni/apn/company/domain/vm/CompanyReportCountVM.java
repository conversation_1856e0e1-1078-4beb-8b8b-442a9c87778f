package com.altomni.apn.company.domain.vm;

import com.altomni.apn.company.domain.enumeration.report.ReportTableType;
import com.altomni.apn.company.domain.enumeration.report.ReportTableTypeConverter;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
public class CompanyReportCountVM implements Serializable {

    @Id
    private Long companyId;

    private int count;

    @Convert(converter = ReportTableTypeConverter.class)
    private ReportTableType type;

    String applicationIds;

}
