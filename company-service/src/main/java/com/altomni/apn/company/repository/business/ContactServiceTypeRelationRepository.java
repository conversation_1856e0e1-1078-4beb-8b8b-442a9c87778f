package com.altomni.apn.company.repository.business;

import com.altomni.apn.company.domain.business.ContactServiceTypeRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

@Repository
public interface ContactServiceTypeRelationRepository extends JpaRepository<ContactServiceTypeRelation, Long> {

    List<ContactServiceTypeRelation> findAllByContactId(Long contactId);

    List<ContactServiceTypeRelation> findAllByContactIdIn(Collection<Long> contactId);


    @Modifying
    @Transactional
    @Query("delete from ContactServiceTypeRelation where contactId = :contactId")
    void deleteAllByContactId(@Param("contactId") Long contactId);

}
