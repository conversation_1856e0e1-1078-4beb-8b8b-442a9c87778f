package com.altomni.apn.company.service.company.location;


import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.company.service.dto.location.CompanyLocationDTO;
import com.altomni.apn.company.vo.location.CompanyLocationVO;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


/**
*
* <AUTHOR>
* date:2023-04-13
*/
public interface CompanyLocationService {

    CompanyLocationVO createCompanyLocation(CompanyLocationDTO locationDTO);

    List<CompanyLocationVO> queryCompanyLocationList(Long companyId);

    LocationDTO queryCompanyLocation(Long id);

    List<LocationDTO> checkLocationCompliance(@RequestBody LocationDTO locationDTO);

}
