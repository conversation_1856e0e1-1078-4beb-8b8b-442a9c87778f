package com.altomni.apn.company.service.dto.salesLead;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "dto for salesLead")
public class CompanySalesLeadProspectDTO extends SalesLeadProspectDTO implements Serializable {

    @ApiModelProperty(value = "The companyId for salesLead.")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "The salesLeadClientContacts for salesLead.")
    @NotNull
    @NotEmpty
    @UniqueElements
    private List<Long> salesLeadClientContacts;

}