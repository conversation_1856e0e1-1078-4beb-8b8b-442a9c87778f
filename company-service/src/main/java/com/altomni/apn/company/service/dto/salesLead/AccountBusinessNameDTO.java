package com.altomni.apn.company.service.dto.salesLead;

import com.altomni.apn.company.aop.validation.ContributionSum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-25
*/
@Data
@ContributionSum
@ApiModel(description = "dto for client salesLead")
public class AccountBusinessNameDTO implements Serializable {

   private Long clientContactId;

    private Long businessId;

   private String name;

    public AccountBusinessNameDTO(Long clientContactId, Long businessId, String name) {
        this.clientContactId = clientContactId;
        this.businessId = businessId;
        this.name = name;
    }
}