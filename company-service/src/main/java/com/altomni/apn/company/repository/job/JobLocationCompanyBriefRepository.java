package com.altomni.apn.company.repository.job;

import com.altomni.apn.company.domain.job.JobLocationCompanyBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface JobLocationCompanyBriefRepository extends JpaRepository<JobLocationCompanyBrief, Long> {

    List<JobLocationCompanyBrief> findAllByJobIdIn(List<Long> jobIds);

}
