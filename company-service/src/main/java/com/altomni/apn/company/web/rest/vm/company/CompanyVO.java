package com.altomni.apn.company.web.rest.vm.company;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigInteger;

@ApiModel(description = "CompanyVO")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CompanyVO implements Serializable {

    private BigInteger id;

    private String resultName;
}
