package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The IndustryType enumeration.
 */
public enum LeadSourceType implements ConvertedEnum<Integer> {
    LINKED_IN(1),
    EVENT(2),
    REFERRAL(3),
    PERSONAL_NETWORK(4),
    COLD_CALL(5),
    OTHER(6);
    private final int dbValue;

    LeadSourceType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<LeadSourceType, Integer> resolver = new ReverseEnumResolver<>(LeadSourceType.class, LeadSourceType::toDbValue);

    public static LeadSourceType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
