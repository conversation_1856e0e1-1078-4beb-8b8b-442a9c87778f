package com.altomni.apn.company.service.dto.projectTeam;

import com.altomni.apn.company.aop.validation.ProjectTeamPermission;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-18
*/
@Data
@ProjectTeamPermission
@ApiModel(description = "projectTeam for client company")
public class CompanyProjectTeamDTO implements Serializable {

    @ApiModelProperty(value = "the id for projectTeam.")
    private Long id;

    @ApiModelProperty(value = "the id for company.")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "the name for projectTeam.")
    @Size(max = 40)
    @NotBlank
    private String name;

    @ApiModelProperty(value = "the id for projectTeam leader.")
    @NotNull
    private Long leaderUserId;

    @ApiModelProperty(value = "the user for projectTeam.")
    @NotNull
    @UniqueElements
    private List<CompanyProjectTeamUserDTO> companyProjectTeamUsers;

}
