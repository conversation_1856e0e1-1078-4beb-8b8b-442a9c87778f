package com.altomni.apn.company.web.rest.folder;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.folder.CompanySearchFolder;
import com.altomni.apn.company.domain.vm.CompanyCustomFolderVM;
import com.altomni.apn.company.service.dto.folder.*;
import com.altomni.apn.company.service.folder.FolderService;
import com.altomni.apn.company.vo.folder.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class FolderResource {

    @Resource
    private FolderService folderService;
    /**
     *
     * @return
     */
    @GetMapping("/category-folders/count")
    public ResponseEntity<List<CategoryFolderCountVO>> countCategoryFolders() throws IOException {
        log.info("[APN: Company folder @{}] REST request to query categoryFolder count.", SecurityUtils.getUserId());
        List<CategoryFolderCountVO> categoryFolderCountVO = folderService.countCategoryFolders();
        return new ResponseEntity<>(categoryFolderCountVO, HttpStatus.OK);
    }

//    @GetMapping("/folder/search/histories")
//    @ApiOperation(value = "query folder search history")
//    public ResponseEntity<List<String>> querySearchHistory() {
//        log.info("[APN: Company folder @{}] REST request to query folder search histories.", SecurityUtils.getUserId());
//        List<String> searchHistoryList = folderService.querySearchHistory();
//        return new ResponseEntity<>(searchHistoryList, HttpStatus.OK);
//    }

    /**
     *
     * @param searchFolderDTO
     * @return
     */
    @PostMapping("/search-folders")
    public ResponseEntity<SearchFolderVO> createSearchFolder(@Valid @RequestBody SearchFolderDTO searchFolderDTO) {
//        log.info("[APN: Company folder @{}] REST request to create searchFolder. {}", SecurityUtils.getUserId(), searchFolderDTO);
        SearchFolderVO searchFolderVO = folderService.createSearchFolder(searchFolderDTO);
        return new ResponseEntity<>(searchFolderVO, HttpStatus.CREATED);
    }

    /**
     *
     * @param id
     * @return
     */
    @GetMapping("/search-folders/{id}")
    public ResponseEntity<SearchFolderVO> querySearchFolder(@PathVariable("id") Long id) {
        log.info("[APN: Company folder @{}] REST request to query searchFolder. {}", SecurityUtils.getUserId(), id);
        SearchFolderVO searchFolderVO = folderService.querySearchFolder(id);
        return new ResponseEntity<>(searchFolderVO, HttpStatus.OK);
    }

    /**
     *
     * @param folderIdDTO
     * @return
     */
    @DeleteMapping("/search-folders")
    public ResponseEntity<HttpStatus> deleteSearchFolder(@RequestBody FolderIdDTO folderIdDTO) {
//        log.info("[APN: Company folder @{}] REST request to delete searchFolders. {}", SecurityUtils.getUserId(), folderIdDTO);
        folderService.deleteSearchFolder(folderIdDTO);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    /**
     *
     * @param folderSearchDTO
     * @param pageable
     * @return
     */
    @PostMapping("/search-folders/search")
    public ResponseEntity<List<SearchFolderVO>> searchSearchFolder(@Valid @RequestBody FolderSearchDTO folderSearchDTO, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
//        log.info("[APN: Company folder @{}] REST request to search searchFolder. {}", SecurityUtils.getUserId(), folderSearchDTO);
        Page<CompanySearchFolder> searchFolderPage = folderService.searchSearchFolder(folderSearchDTO, pageable);
        List<SearchFolderVO> result = folderService.toVo(searchFolderPage.getContent());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(searchFolderPage, "/api/v3/company/folders");
        headers.add("Access-Control-Expose-Headers","X-Total-Count");
        return ResponseEntity.ok().headers(headers).body(result);
    }

    /**
     *
     * @param customFolderDTO
     * @return
     */
    @PostMapping("/folders")
    public ResponseEntity<CustomFolderVO> createCustomFolder(@Valid @RequestBody CustomFolderDTO customFolderDTO) {
//        log.info("[APN: Company folder @{}] REST request to create customFolder. {}", SecurityUtils.getUserId(), customFolderDTO);
        CustomFolderVO customFolderVO = folderService.createCustomFolder(customFolderDTO);
        return new ResponseEntity<>(customFolderVO, HttpStatus.CREATED);
    }

    /**
     *
     * @param folderId
     * @param customFolderDTO
     * @return
     */
    @PutMapping("/folders/{folderId}")
    public ResponseEntity<CustomFolderVO> updateCustomFolder(@PathVariable("folderId") Long folderId, @Valid @RequestBody CustomFolderDTO customFolderDTO) {
//        log.info("[APN: Company folder @{}] REST request to update customFolder. {}", SecurityUtils.getUserId(), customFolderDTO);
        CustomFolderVO customFolderVO = folderService.updateCustomFolder(folderId, customFolderDTO);
        return new ResponseEntity<>(customFolderVO, HttpStatus.CREATED);
    }

    /**
     *
     * @param folderId
     * @return
     */
    @GetMapping("/folders/{folderId}")
    public ResponseEntity<CustomFolderVO> queryCustomFolder(@PathVariable("folderId") Long folderId) {
        log.info("[APN: Company folder @{}] REST request to query customFolder. {}", SecurityUtils.getUserId(), folderId);
        CustomFolderVO customFolderVO = folderService.queryCustomFolder(folderId);
        return new ResponseEntity<>(customFolderVO, HttpStatus.OK);
    }

    /**
     *
     * @param folderIdDTO
     * @return
     */
    @DeleteMapping("/folders")
    public ResponseEntity<HttpStatus> deleteCustomFolder(@Valid @RequestBody FolderIdDTO folderIdDTO) {
        log.info("[APN: Company folder @{}] REST request to delete customFolder. {}", SecurityUtils.getUserId(), folderIdDTO);
        folderService.deleteCustomFolder(folderIdDTO);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    /**
     *
     * @param customFolderSearchDTO
     * @param pageable
     * @return
     */
    @PostMapping("/folders/search")
    public ResponseEntity<List<CustomFolderSearchVO>> searchCustomFolder(@Valid @RequestBody CustomFolderSearchDTO customFolderSearchDTO, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN: Company folder @{}] REST request to search customFolder. {}", SecurityUtils.getUserId(), customFolderSearchDTO);
        Page<CompanyCustomFolderVM> customFolderPage = folderService.searchCustomFolder(customFolderSearchDTO, pageable);
        List<CustomFolderSearchVO> result = folderService.toVo(customFolderPage.getContent(), customFolderSearchDTO.getFolderType());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(customFolderPage, "/api/v3/company/folders");
        headers.add("Access-Control-Expose-Headers","X-Total-Count");
        return ResponseEntity.ok().headers(headers).body(result);
    }

    /**
     *
     * @param folderIdMoveCompanyDTO
     * @return
     */
    @PostMapping("/folders/companies")
    public ResponseEntity<HttpStatus> addCustomFolderCompany(@Valid @RequestBody FolderIdMoveCompanyDTO folderIdMoveCompanyDTO) {
        log.info("[APN: Company folder @{}] REST request to add customFolder company. {}", SecurityUtils.getUserId(), folderIdMoveCompanyDTO);
        folderService.addCustomFolderCompany(folderIdMoveCompanyDTO);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    /**
     *
     * @param folderIdDeleteCompanyDTO
     * @return
     */
    @DeleteMapping("/folders/companies")
    public ResponseEntity<HttpStatus> deleteCustomFolderCompany(@Valid @RequestBody FolderIdDeleteCompanyDTO folderIdDeleteCompanyDTO) {
        log.info("[APN: Company folder @{}] REST request to delete customFolder company. {}", SecurityUtils.getUserId(), folderIdDeleteCompanyDTO);
        folderService.deleteCustomFolderCompany(folderIdDeleteCompanyDTO);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    /**
     *
     * @param folderSearchDTO
     * @return
     */
    @PostMapping("/folders-all/search")
    public ResponseEntity<List<FolderSearchVO>> searchFolder(@Valid @RequestBody FolderSearchDTO folderSearchDTO) {
        log.info("[APN: Company folder @{}] REST request to search folder. {}", SecurityUtils.getUserId(), folderSearchDTO);
        List<FolderSearchVO> result = folderService.searchFolder(folderSearchDTO);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     *
     * @param folderIdDTO
     * @return
     */
    @DeleteMapping("/folders/shared-folders")
    public ResponseEntity<HttpStatus> deleteSharedFolder(@Valid @RequestBody FolderIdDTO folderIdDTO) {
        log.info("[APN: Company folder @{}] REST request to delete sharedFolder. {}", SecurityUtils.getUserId(), folderIdDTO);
        folderService.deleteSharedFolder(folderIdDTO);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @GetMapping("/folders-list")
    public ResponseEntity<List<FolderSearchVO>> searchFolderList(@RequestParam("type") CategoryFolderType type) {
        log.info("[APN: Company folder @{}] REST request to search folder list.", SecurityUtils.getUserId());
        List<FolderSearchVO> result = folderService.searchFolderList(type);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     *
     * @param createdBy
     * @return
     */
    @DeleteMapping("/folders/connect-client")
    public ResponseEntity<HttpStatus> deleteCustomFolderConnectClient(@RequestParam("createdBy")String createdBy) {
        log.info("[APN: Company folder @{}] REST request to delete CustomFolderConnectClient createdBy. {}", SecurityUtils.getUserId(), createdBy);
        folderService.deleteCustomFolderConnectClientByCreatedBy(createdBy);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
