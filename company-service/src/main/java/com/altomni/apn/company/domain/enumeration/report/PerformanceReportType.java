package com.altomni.apn.company.domain.enumeration.report;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractType enumeration.
 */
public enum PerformanceReportType implements ConvertedEnum<Integer> {
    SUBMISSION_DATE(10),
    STATUS_UPDATE_DATE(20);
    private final int dbValue;

    PerformanceReportType(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<PerformanceReportType, Integer> resolver =
        new ReverseEnumResolver<>(PerformanceReportType.class, PerformanceReportType::toDbValue);

    public static PerformanceReportType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
