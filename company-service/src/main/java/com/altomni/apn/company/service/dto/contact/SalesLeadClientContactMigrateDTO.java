package com.altomni.apn.company.service.dto.contact;

import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-18
*/
@Data
@ApiModel(description = "dto for client contact migrate")
public class SalesLeadClientContactMigrateDTO extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private Long id;

    private Integer contactCategory;

    private boolean active = true;

    private Instant lastFollowUpTime;

    private Long tenantId;

    private Long talentId;

    private Long companyId;

    private Long crmContactId;

    private String esId;

    private Long creditTransactionId;

    /**
     * 关键联系人需求增加
     */
    @ApiModelProperty(value = "is key contact")
    private Boolean isKeyContact;

    /**
     * 修改联系人关联公司 原始的公司id
     */
    private Long originalCompanyId;

    public static SalesLeadClientContact toSalesLeadClientContact(SalesLeadClientContactMigrateDTO salesLeadClientContactMigrateDTO) {
        SalesLeadClientContact salesLeadClientContact = new SalesLeadClientContact();
        ServiceUtils.myCopyProperties(salesLeadClientContactMigrateDTO, salesLeadClientContact);
        return salesLeadClientContact;
    }

}
