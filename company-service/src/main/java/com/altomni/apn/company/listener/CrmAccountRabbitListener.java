package com.altomni.apn.company.listener;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.application.LarkProperties;
import com.altomni.apn.common.domain.enumeration.calendar.*;
import com.altomni.apn.common.dto.calendar.CalendarEventAttendeeDTO;
import com.altomni.apn.common.dto.calendar.CalendarEventDTO;
import com.altomni.apn.common.dto.calendar.CalendarEventRelationDTO;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.vo.calendar.CalendarEventVO;
import com.altomni.apn.company.domain.business.AccountBusinessMigrate;
import com.altomni.apn.company.domain.enumeration.business.BusinessProgress;
import com.altomni.apn.company.service.business.AccountBusinessService;
import com.altomni.apn.company.service.business.SalesLeadClientContactService;
import com.altomni.apn.company.service.common.CommonClient;
import com.altomni.apn.company.service.company.overview.CompanyOverviewService;
import com.altomni.apn.company.service.contract.ContractService;
import com.altomni.apn.company.service.rabbitmq.RabbitMqService;
import com.altomni.apn.company.vo.company.CompanyCalendarVO;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CrmAccountRabbitListener {

    @Resource
    private CompanyOverviewService companyOverviewService;

    @Resource
    private AccountBusinessService accountBusinessService;

    @Resource
    private ContractService contractService;

    @Resource
    private SalesLeadClientContactService salesLeadClientContactService;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private LarkProperties larkProperties;

    @Resource
    private CommonClient commonClient;

    @RabbitListener(containerFactory = "crmFactory", queues = {"${application.account.queue}"})
    @RabbitHandler
    public void process(Message message, Channel channel) throws Exception {
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("[CrmAccountRabbitListener] Received message: {}", JSONUtil.toJsonStr(json));
        if (!JSONUtil.isJson(json)) {
            log.error("sync data error: {}", json);
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        LoginUtil.simulateLoginWithClient();
        String syncType = "";
        String ids = "";
        String operator = "";
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(definition);
        JSONObject backFillObj = new JSONObject();
        List<AccountBusinessMigrate> businessMigrates = new ArrayList<>();
        try {
            JSONObject jsonObject = JSONUtil.parseObj(json);
            syncType = jsonObject.getStr("type");
            ids = jsonObject.getStr("ids");
            operator = jsonObject.getStr("operator");
            LoginUtil.simulateLoginWithClient();
            backFillObj = salesLeadClientContactService.saveClientContact(jsonObject);
            contractService.saveContract(jsonObject);
            businessMigrates = accountBusinessService.saveAccountBusiness(jsonObject);
            companyOverviewService.saveCompany(jsonObject);
            transactionManager.commit(status);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            if (!status.isCompleted()) {
                transactionManager.rollback(status);
            }
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            log.error("[Company Sync Crm Data Error] error = [{}], message = {} ", ExceptionUtils.getStackTrace(e), message);
            NotificationUtils.sendAlertToLark(larkProperties.getLarkWebhookKey(), larkProperties.getLarkWebhookUrl(), "crm-account message consumption failed, please handle manually, syncType = " + syncType + ", ids = " + ids + ", operator:" + operator);
        }
        log.info("backFillObj:{}", backFillObj);
        if (!JSONUtil.isNull(backFillObj)) {
            rabbitMqService.saveCrmBackFillData(JSONUtil.toJsonStr(backFillObj));
        }

        addCalendar(businessMigrates);
    }

    //消费数据是1条所以直接调用
    private void addCalendar(List<AccountBusinessMigrate> businessMigrates) {
        log.info("[addCompanyCalendar] param info:{}", JSONUtil.toJsonStr(businessMigrates));
        if (null != businessMigrates && !businessMigrates.isEmpty()) {
            List<Long> companyId = businessMigrates.stream().filter(v -> v.getBusinessProgress() < BusinessProgress.ACCOUNT_BUSINESS_CONTRACTED.toDbValue())
                    .map(AccountBusinessMigrate::getAccountCompanyId).collect(Collectors.toList());
            if (null == companyId && companyId.isEmpty()) {
                return;
            }
            List<CompanyCalendarVO> companyCalendarVOS = companyOverviewService.searchCompanyCalendar(companyId);
            Map<Long, List<AccountBusinessMigrate>> busindessMap = businessMigrates.stream().filter(v -> v.getBusinessProgress() < BusinessProgress.ACCOUNT_BUSINESS_CONTRACTED.toDbValue())
                    .collect(Collectors.groupingBy(AccountBusinessMigrate::getAccountCompanyId));

            if (null != companyCalendarVOS && !companyCalendarVOS.isEmpty()) {
                for (CompanyCalendarVO vo : companyCalendarVOS) {
                    List<AccountBusinessMigrate> business = busindessMap.get(vo.getCompanyId().longValue());
                    for (AccountBusinessMigrate migrate : business) {
                        if (null == migrate.getCooperateExpireTime()) {
                            continue;
                        }

                        CalendarEventVO eventVO = commonClient.getCalendarEventByTypeIdAndReferenceId(3,migrate.getId()).getBody();
                        if (null != eventVO && eventVO.getId() != null) {
                            CalendarEventDTO dto = new CalendarEventDTO();
                            dto.setId(eventVO.getId());
                            Instant[] time = getStartAndEndOfDay(migrate.getCooperateExpireTime());
                            dto.setStartTime(time[0]);
                            dto.setEndTime(time[1]);
                            dto.setCalendarType(CalendarTypeEnum.DEADLINE);
                            dto.setPriority(CalendarPriorityEnum.HIGH);
                            dto.setTypeId(3);
                            dto.setGoToId(migrate.getId());
                            dto.setTitle("成单到日期");
                            dto.setDescription(vo.getFullBusinessName());
                            dto.setReferenceId(migrate.getId());
                            dto.setTenantId(vo.getTenantId().longValue());
                            CalendarEventAttendeeDTO attendeeDTO = new CalendarEventAttendeeDTO();
                            attendeeDTO.setEmail(vo.getEmail());
                            attendeeDTO.setIsReminder(CalendarEventAttendeeReminderTypeEnum.NO_REMINDER);
                            attendeeDTO.setIsOrganizer(CalendarEventAttendeeTypeEnum.ORGANIZER);
                            attendeeDTO.setUserId(vo.getUserId().longValue());
                            dto.setAttendees(Arrays.asList(attendeeDTO));

                            CalendarEventRelationDTO relationDTO = new CalendarEventRelationDTO();
                            relationDTO.setRelationId(vo.getCompanyId().longValue());
                            relationDTO.setRelationType(CalendarRelationEnum.COMPANY);
                            relationDTO.setRelationName(vo.getFullBusinessName());
                            dto.setRelationList(Arrays.asList(relationDTO));
                            commonClient.updateCalendarEvent(dto);
                        } else {
                            CalendarEventDTO dto = new CalendarEventDTO();
                            Instant[] time = getStartAndEndOfDay(migrate.getCooperateExpireTime());
                            dto.setStartTime(time[0]);
                            dto.setEndTime(time[1]);
                            dto.setCalendarType(CalendarTypeEnum.DEADLINE);
                            dto.setPriority(CalendarPriorityEnum.HIGH);
                            dto.setTypeId(3);
                            dto.setGoToId(migrate.getId());
                            dto.setTitle("成单到日期");
                            dto.setDescription(vo.getFullBusinessName());
                            dto.setReferenceId(migrate.getId());
                            dto.setTenantId(vo.getTenantId().longValue());
                            CalendarEventAttendeeDTO attendeeDTO = new CalendarEventAttendeeDTO();
                            attendeeDTO.setEmail(vo.getEmail());
                            attendeeDTO.setIsReminder(CalendarEventAttendeeReminderTypeEnum.NO_REMINDER);
                            attendeeDTO.setIsOrganizer(CalendarEventAttendeeTypeEnum.ORGANIZER);
                            attendeeDTO.setUserId(vo.getUserId().longValue());
                            dto.setAttendees(Arrays.asList(attendeeDTO));

                            CalendarEventRelationDTO relationDTO = new CalendarEventRelationDTO();
                            relationDTO.setRelationId(vo.getCompanyId().longValue());
                            relationDTO.setRelationType(CalendarRelationEnum.COMPANY);
                            relationDTO.setRelationName(vo.getFullBusinessName());
                            dto.setRelationList(Arrays.asList(relationDTO));
                            commonClient.createCalendarEvent(dto);
                        }
                    }
                }
            }

        }
    }

    public static Instant[] getStartAndEndOfDay(Instant instant) {

        // 将 UTC Instant 转换为 UTC LocalDate
        LocalDate utcDate = instant.atZone(ZoneOffset.UTC).toLocalDate();

        // 构造当天的 00:00:00 和 23:30:00 时间
        LocalDateTime startOfDay = utcDate.atStartOfDay();
        LocalDateTime endOfDay = utcDate.atTime(23, 30);

        // 转换为 Instant
        Instant startInstant = startOfDay.toInstant(ZoneOffset.UTC);
        Instant endInstant = endOfDay.toInstant(ZoneOffset.UTC);

        // 返回两个 Instant
        return new Instant[]{startInstant, endInstant};
    }
}