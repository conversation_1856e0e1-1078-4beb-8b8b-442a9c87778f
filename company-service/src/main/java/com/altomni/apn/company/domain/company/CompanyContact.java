package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.vo.contact.ContactInfoVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Data
@Table(name = "company_contact")
@NoArgsConstructor
@AllArgsConstructor
public class CompanyContact extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "type", nullable = false)
    private Integer type;

    @Column(name = "contact", nullable = false)
    private String contact;

    @Column(name = "details")
    private String details;

    @Column(name = "company_id")
    private Long companyId;

    public static ContactInfoVO toContactInfoVO(CompanyContact companyContact) {
        ContactInfoVO contactInfoVO = new ContactInfoVO();
        ServiceUtils.myCopyProperties(companyContact, contactInfoVO);
        return contactInfoVO;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompanyContact that = (CompanyContact) o;
        return type.equals(that.type) && contact.equals(that.contact) && details.equals(that.details) && companyId.equals(that.companyId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(type, contact, details, companyId);
    }
}
