package com.altomni.apn.company.service.jobdiva;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.ipg.resourceserver.client.ClientTokenHolder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.altomni.apn.common.constants.AuthConstants.AUTHORIZATION_HEADER;
import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Slf4j
@Service
public class HrJobdivaClient {

//    @Resource
//    private HttpService httpService;
//
//    @Resource
//    private ApplicationProperties applicationProperties;
//
//    public TimeSheetUserDTO saveTimeSheetUser(TimeSheetUserDTO dto) {
//        String url = applicationProperties.getHrUrl() + "/jobdiva/api/v3/timesheet/user/save";
//        log.info("[APN HrJobdivaClient @{}] request to save time sheet user, url = {}, param = {}", SecurityUtils.getUserId(), url, dto);
//        TimeSheetUserDTO timeSheetUserDTO;
//        try {
//            HttpResponse response = httpService.post(url, getRequestHeaders(), JSONUtil.toJsonPrettyStr(dto));
//            if (response != null && 200 == response.getCode()) {
//                timeSheetUserDTO = JSONUtil.toBean(JSONUtil.parseObj(response.getBody()), TimeSheetUserDTO.class);
//            } else {
//                throw new CustomParameterizedException(response.getBody());
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("[APN HrJobdivaClient @{}] request to save time sheet user is error, url = {}, param = {}", SecurityUtils.getUserId(), url, dto);
//            throw new CustomParameterizedException("save time sheet user error");
//        }
//        return timeSheetUserDTO;
//    }
//
//    public Headers getRequestHeaders() {
//        String currentUserToken = SecurityUtils.getCurrentUserToken();
//        Map<String, String> headersBuilder = new HashMap<>();
//        headersBuilder.put(AUTHORIZATION_HEADER, TOKEN_TYPE + " " + currentUserToken);
//        return Headers.of(headersBuilder);
//    }

}
