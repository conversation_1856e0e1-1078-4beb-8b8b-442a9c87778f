package com.altomni.apn.company.repository.business;

import com.altomni.apn.company.domain.business.SalesLeadClientContactAdditionalInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SalesLeadClientContactAddtionalInfoRepository extends JpaRepository<SalesLeadClientContactAdditionalInfo, Long> {

    SalesLeadClientContactAdditionalInfo findByCompanyContactId(Long contactId);

    List<SalesLeadClientContactAdditionalInfo> findByCompanyContactIdIn(List<Long> contactIds);

}