package com.altomni.apn.company.service.dto.note;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@ApiModel(description = "note for progress company")
public class CompanyProgressNoteDTO extends CompanyProgressNoteBasicDTO implements Serializable {

    @ApiModelProperty(value = "the id for company.")
    @NotNull
    private Long companyId;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    @Override
    public String toString() {
        return "CompanyProgressNoteDTO{" +
                "clientContactIds=" + clientContactIds +
                ", contactType=" + contactType +
                ", contactDate=" + contactDate +
                ", salesLeadId=" + salesLeadId +
                ", note='" + note + '\'' +
                ", companyId=" + companyId +
                '}';
    }
}
