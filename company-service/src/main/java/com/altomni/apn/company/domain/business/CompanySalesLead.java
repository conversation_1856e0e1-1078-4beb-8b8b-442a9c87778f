package com.altomni.apn.company.domain.business;

import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.vo.business.AccountBusinessVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Set;

/**
 * only use sync data to crm
 */
@Deprecated
@ApiModel(description = "CompanySalesLead")
@Entity
@Data
@Table(name = "company_sales_lead")
public class CompanySalesLead extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "account_progress")
    private BigDecimal accountProgress;

    @Column(name = "company_id", nullable = false)
    private Long accountCompanyId;

    @Column(name = "lead_source")
    private Long leadSource;

    @Column(name = "estimated_deal_time", nullable = true)
    private Instant estimatedDealTime;

}