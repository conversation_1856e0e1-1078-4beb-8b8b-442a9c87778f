package com.altomni.apn.company.repository.company;

import com.altomni.apn.company.domain.company.CompanyContact;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CompanyContactRepository extends JpaRepository<CompanyContact, Long> {

    List<CompanyContact> findAllByCompanyId(Long companyId);

    List<CompanyContact> findAllByCompanyIdIn(List<Long> companyIds);

    List<CompanyContact> findAllByCompanyIdOrderByIdAsc(Long companyId);
}
