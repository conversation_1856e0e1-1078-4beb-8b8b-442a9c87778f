package com.altomni.apn.company.repository.company;

import com.altomni.apn.company.domain.company.CompanyClientInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyClientInfoRepository extends JpaRepository<CompanyClientInfo, Long> {

    List<CompanyClientInfo> findAllByCompanyId(Long companyId);

    List<CompanyClientInfo> findAllByCompanyIdAndClientName(Long companyId,String clientName);

    List<CompanyClientInfo> findAllByCompanyIdAndClientNameAndClientAddress(Long companyId,String clientName,String clientAddress);

    List<CompanyClientInfo> findAllByCompanyIdAndClientNameAndClientAddressAndClientDivision(Long companyId,String clientName,String address,String clientDivision);

}
