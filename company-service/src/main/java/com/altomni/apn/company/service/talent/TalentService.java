package com.altomni.apn.company.service.talent;

import com.altomni.apn.common.dto.talent.TalentDTOV3;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
@FeignClient(value = "talent-service") // service name
public interface TalentService {
//    @GetMapping("/talent/api/v3/test")
//    ResponseEntity<Object> getCandidate();
//
//    @PostMapping("/talent/api/v3/save")
//    ResponseEntity<Object> saveCandidate();

    @GetMapping("/talent/api/v3/es-talents/update-client-contact-name/{clientId}")
    void updateClientContactNameFromEsDb(@PathVariable("clientId") Long clientId);

    @PostMapping("/talent/api/v3/talents/id/search-by-contacts-and-similarity")
    ResponseEntity<List<Long>> searchTalentsIdByContactAndSimilarity(@RequestBody TalentDTOV3 talentDTOV3);

    @GetMapping("/talent/api/v3/es-talents/getTalent")
    ResponseEntity<TalentDTOV3> getTalentFromEs(@RequestParam("id") String id);

    @GetMapping("/talent/api/v3/es-talents/getTalent/internal")
    ResponseEntity<TalentDTOV3> getTalentFromEsForInternal(@RequestParam("id") String id, @RequestParam("tenantId") Long tenantId);

    @PostMapping("/talent/api/v3/talents-basic/search-by-contacts")
    ResponseEntity<List<TalentDTOV3>> searchTalentsByContact(@RequestBody TalentDTOV3 talentDTOV3);

    @PostMapping("/talent/api/v3/talents-basic/find-by-ids")
    ResponseEntity<List<TalentDTOV3>> findTalentsBasicByIds(@RequestBody List<Long> ids);

    @PostMapping("/talent/api/v3/talent-ownerships/get-valid-talents")
    ResponseEntity<Set<Long>> getValidTalents(@RequestBody List<Long> talentIds);

    @PutMapping("/talent/api/v3/talent/sync-to-hr/{talentId}")
    void updateTalentNeedSyncToHr(@PathVariable("talentId") Long talentId);


}
