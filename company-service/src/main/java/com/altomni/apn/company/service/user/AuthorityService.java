package com.altomni.apn.company.service.user;

import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.LoginVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(value = "authority-service", contextId = "authority-company")
public interface AuthorityService {

    @GetMapping("/authority/api/v3/logoutByUid")
    ResponseEntity<Void> logoutByUid(@RequestParam("uid") String uid);

    @PostMapping("/authority/api/v3/credential")
    ResponseEntity<CredentialDTO> findCredentialUser(@RequestBody LoginVM loginVM);
}
