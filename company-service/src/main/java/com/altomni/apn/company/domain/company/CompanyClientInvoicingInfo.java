package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @deprecated 中国区客户开票信息
 */
@Entity
@Data
@Table(name = "company_client_invoicing_info")
@NoArgsConstructor
@AllArgsConstructor
public class CompanyClientInvoicingInfo extends AbstractAuditingEntity implements Serializable,Cloneable{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**  */
    @Column(name = "company_id")
    private Long companyId;

    /**  */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**  */
    @Column(name = "client_name")
    private String clientName;

    /**  */
    @Column(name = "social_credit_code")
    private String socialCreditCode;

    /**  */
    @Column(name = "bank_name")
    private String bankName;

    /**  */
    @Column(name = "bank_account")
    private String bankAccount;

    /**  */
    @Column(name = "invoicing_address")
    private String invoicingAddress;

    /** 发票邮寄地址 */
    @Column(name = "mailing_address")
    private String mailingAddress;

    /**  */
    @Column(name = "phone")
    private String phone;
}
