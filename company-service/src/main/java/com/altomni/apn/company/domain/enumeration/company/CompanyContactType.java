package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum CompanyContactType implements ConvertedEnum<Integer> {
//    ipg deprecated
    AD_CALL(0),
    CLIENT_VISIT(1),
    SKILL_MARKETING_MPC(2),
    FOLLOWUP_CALL_EMAIL(3),
    REFERENCE_CALL(4),
    NETWORKING_CONFERENCE(5),
    PROPOSAL_PRESENTATION(6),
    PROCUREMENT(7),

//    ipg using
    CALL(8),
    EMAIL(9),
    NEW_MEETING(10),
    FORMAT_MEETING(11),
    LUNCH_MEETING(12),
    OOC_MEETING(13),
    OPPORTUNITY_PIPELINE_MEETING(14),

    //General version use
    COMMUNICATED_BY_PHONE(100),
    COMMUNICATED_BY_EMAIL(110),
    CONSULTANT(120),
    OTHER(99999);

    private final int dbValue;

    CompanyContactType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CompanyContactType, Integer> resolver = new ReverseEnumResolver<>(CompanyContactType.class, CompanyContactType::toDbValue);

    public static CompanyContactType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
