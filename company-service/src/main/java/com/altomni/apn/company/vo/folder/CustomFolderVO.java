package com.altomni.apn.company.vo.folder;

import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.enumeration.folder.FolderPermission;
import com.altomni.apn.company.domain.folder.CompanyCustomFolder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "vo for customFolder")
public class CustomFolderVO implements Serializable {

    @ApiModelProperty(value = "The id for customFolder.")
    private Long id;

    @ApiModelProperty(value = "The name for customFolder.")
    private String name;

    @ApiModelProperty(value = "The permission for shared folder.")
    private FolderPermission permission;

    @ApiModelProperty(value = "The sharedTeams for customFolder.")
    private List<FolderSharedVO> sharedTeams;

    @ApiModelProperty(value = "The sharedUsers for customFolder.")
    private List<FolderSharedVO> sharedUsers;

    @ApiModelProperty(value = "The category for customFolder.", allowableValues = "PROSPECT_ALL_COMPANY, CLIENT_ALL_COMPANY")
    private CategoryFolderType category;

    @ApiModelProperty(value = "The note for customFolder.")
    private String note;

    public static CustomFolderVO fromCompanyCustomFolder(CompanyCustomFolder companyCustomFolder) {
        CustomFolderVO customFolderVO = new CustomFolderVO();
        ServiceUtils.myCopyProperties(companyCustomFolder, customFolderVO);
        return customFolderVO;
    }

}
