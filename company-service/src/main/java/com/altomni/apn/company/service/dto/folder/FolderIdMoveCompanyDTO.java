package com.altomni.apn.company.service.dto.folder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "id for folders move company")
public class FolderIdMoveCompanyDTO implements Serializable {

    @ApiModelProperty(value = "The id for toFolderIds.")
    @NotNull(message = "toFolderIds is required")
    @UniqueElements
    private List<Long> toFolderIds;

    @ApiModelProperty(value = "The id for company.")
    @NotNull(message = "companyIds is required")
    @UniqueElements
    private List<Long> companyIds;

    @ApiModelProperty(value = "The id for fromFolderId.")
    private Long fromFolderId;

}
