package com.altomni.apn.company.domain.vm;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EntityLocationVM implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @JsonIgnore
    private Long id;

    private String city;

    private String province;

    private String country;

    private String addressLine;

    private String county;

}
