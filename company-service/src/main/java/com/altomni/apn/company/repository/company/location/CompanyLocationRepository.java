package com.altomni.apn.company.repository.company.location;

import com.altomni.apn.company.domain.company.lcoation.CompanyLocation;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface CompanyLocationRepository extends JpaRepository<CompanyLocation, Long> {


    List<CompanyLocation> findAllByAccountCompanyId(Long companyId);

    @Modifying
    @Transactional
    @Query(value = "update company_location set official_city = ?2, official_country = ?3, official_province = ?4, official_county = ?5 where id = ?1", nativeQuery = true)
    void updateOfficialInfoById(Long id, String officialCity, String officialCountry, String officialProvince, String officialCounty);

    @Modifying
    @Transactional
    @Query(value = " delete from company_location where company_id = ?1 and original_loc is null ", nativeQuery = true)
    void deleteAllByCompanyIdAndOriginalLoc(Long jobId);


    @Query(value = "SELECT * FROM company_location WHERE original_loc NOT LIKE '%coordinate%'", nativeQuery = true)
    Page<CompanyLocation> findAllPossibleConflictLocation(Pageable pageable);

    List<CompanyLocation> findAllByAccountCompanyIdIn(List<Long> companyIds);

}
