package com.altomni.apn.company.domain.business;

import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.common.enumeration.SalesLeadRoleTypeConverter;
import com.altomni.apn.company.vo.business.OwnerVO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Data
@NoArgsConstructor
@Table(name = "business_flow_administrator")
public class BusinessFlowAdministrator extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    private Long id;

    @Column(name = "account_business_id")
    private Long accountBusinessId;

    //crm userId, not apn userId
    @Column(name = "user_id")
    private Long userId;

    @Column(name = "contribution")
    private Integer contribution;

    @Column(name = "sales_lead_role")
    @Convert(converter = SalesLeadRoleTypeConverter.class)
    private SalesLeadRoleType salesLeadRoleType;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "country")
    private Long country;

    public static OwnerVO toOwnerVO(BusinessFlowAdministrator businessFlowAdministrator) {
        OwnerVO ownerVO = new OwnerVO();
        ownerVO.setId(businessFlowAdministrator.getUserId());
        ownerVO.setContribution(businessFlowAdministrator.getContribution());
        ownerVO.setCountry(businessFlowAdministrator.getCountry());
        return ownerVO;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BusinessFlowAdministrator that = (BusinessFlowAdministrator) o;
        return accountBusinessId.equals(that.accountBusinessId) && userId.equals(that.userId) && salesLeadRoleType == that.salesLeadRoleType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), accountBusinessId, userId, salesLeadRoleType);
    }
}
