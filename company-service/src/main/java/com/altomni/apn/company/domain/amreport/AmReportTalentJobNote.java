package com.altomni.apn.company.domain.amreport;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.company.domain.enumeration.job.NodeType;
import com.altomni.apn.company.domain.enumeration.job.NodeTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "am_report_talent_job_note")
public class AmReportTalentJobNote extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "node_type")
    @Convert(converter = NodeTypeConverter.class)
    private NodeType nodeType;

    @Column(name = "high_lighted_experience")
    private String highLightedExperience;

    @Column(name = "am_update")
    private String amUpdate;

    @Column(name = "frequency")
    private Integer frequency;


}
