package com.altomni.apn.company.vo.location;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * A LocationVO.
 * <AUTHOR>
 */
@AllArgsConstructor
@ApiModel(description = "Vo for location")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class LocationVO implements Serializable {

    @ApiModelProperty(value = "location id.")
    private Long id;

    @ApiModelProperty(value = "custom location.")
    private String location;

    @ApiModelProperty(value = "addressLine location.")
    private String addressLine;

    @ApiModelProperty(value = "city location.")
    private String city;

    @ApiModelProperty(value = "province location.")
    private String province;

    @ApiModelProperty(value = "county location.")
    private String county;

    @ApiModelProperty(value = "country location.")
    private String country;

    @ApiModelProperty(value = "country originDisplay.")
    private String originDisplay;

    @ApiModelProperty(value = "coordinate location.")
    private List<Double> coordinate;

    public LocationVO setId(Long id) {
        this.id = id;
        return this;
    }
}
