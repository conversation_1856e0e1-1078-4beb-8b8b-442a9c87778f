package com.altomni.apn.company.domain.talent;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.ContactTypeConverterV2;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.enumeration.TalentContactStatusConverter;
import com.altomni.apn.common.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * A TalentContact.
 */
@ApiModel(description = "Talent's contacts. Talent can have many contact information, from different providers. They are also used to help identify if a talent " +
        "already exists, or should create a new one.")
@Data
@Entity
@NoArgsConstructor
@Table(name = "talent_contact")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentContactCompanyBrief extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Convert(converter = ContactTypeConverterV2.class)
    @Column(name = "jhi_type", nullable = false)
    private ContactType type;

    @ApiModelProperty(value = "the actual contact. e.g. Email address, Linkedin ID, etc.", required = true)
    @NotNull
    @Column(name = "contact", nullable = false)
    private String contact;

    @ApiModelProperty(value = "Whether the contact is verified by user. Default is false(0).")
    @Column(name = "verified")
    private Boolean verified;

    @ApiModelProperty(value = "The details in JSON to store additional information for the contact")
    @Column(name = "details")
    private String details;

    @ApiModelProperty(value = "The details in JSON to store additional information for the contact")
    @Column(name = "info")
    private String info;

    @ApiModelProperty(value = "talent id for easy search. Read Only.")
    @Column(name = "talent_id", updatable = false)
    private Long talentId;

    @ApiModelProperty(value = "The tenant id talent belongs to. This is saved from the user's tenant when creating talent.")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @ApiModelProperty(value = "status")
    @Convert(converter = TalentContactStatusConverter.class)
    @Column(name = "status")
    private TalentContactStatus status;

    @ApiModelProperty(value = "Save user sort")
    @Column(name = "sort")
    private Integer sort;

    public Integer getSort() {
        if(ObjectUtil.isNull(sort)){
            return 0;
        }
        return sort;
    }

    public String getTypeAndContact() {
        return type + contact;
    }

    /**
     * The ContactType enumeration. Each talent need to have a unique contact for these types
     */
    public static Set<ContactType> ExclusiveContactTypes = new HashSet<>(Arrays.asList(ContactType.PRIMARY_EMAIL,  ContactType.PRIMARY_PHONE, ContactType.LINKEDIN, ContactType.LIEPIN, ContactType.YIFENGJIANLI, ContactType.ZHILIAN, ContactType.MAIMAI));

    public static Set<ContactType> PrimaryContactTypes = new HashSet<>(Arrays.asList(ContactType.PRIMARY_EMAIL, ContactType.PRIMARY_PHONE));

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "talentId", "tenantId"));

    public JSONObject toJSON() {
        JSONObject result = new JSONObject();
        JsonUtil.fluentPut(result, "type", type);
        JsonUtil.fluentPut(result, "contact", contact);
        JsonUtil.fluentPut(result, "details", details);
        JsonUtil.fluentPut(result, "info", info);
        JsonUtil.fluentPut(result, "verified", verified);
        return result;
    }

    public TalentContactCompanyBrief(ContactType type, String contact, Long talentId, Long tenantId, TalentContactStatus status, Integer sort) {
        this.type = type;
        this.contact = contact;
        this.talentId = talentId;
        this.tenantId = tenantId;
        this.status = status;
        this.sort = sort;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TalentContactCompanyBrief that = (TalentContactCompanyBrief) o;
        return type == that.type && StringUtils.isNotEmpty(contact) && contact.equals(that.contact) && talentId != null && talentId.equals(that.talentId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(type, contact, talentId);
    }
}
