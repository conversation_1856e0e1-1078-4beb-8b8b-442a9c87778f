package com.altomni.apn.company.service.dashboard.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.service.dashboard.DashboardService;
import com.altomni.apn.company.service.elastic.EsCompanyDataService;
import com.altomni.apn.company.vo.folder.CategoryFolderCountVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Service("dashboardService")
public class DashboardServiceImpl implements DashboardService {

    @Resource
    private EsCompanyDataService esCompanyDataService;


    @Override
    public String customerMetric(Instant startTime, Instant endTime,List<Long> userIdList) throws IOException {
        HttpResponse response = esCompanyDataService.searchCustomerMetric(startTime, endTime,userIdList);
        return response.getBody();
    }

}
