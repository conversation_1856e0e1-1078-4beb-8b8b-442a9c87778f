package com.altomni.apn.company.web.rest.company.contact;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.company.*;
import com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.constants.Constants;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.service.business.SalesLeadClientContactService;
import com.altomni.apn.company.service.dto.contact.CompanyContactCheckDuplicatedTalentDTO;
import com.altomni.apn.company.service.dto.contact.CompanyContactSearchDTO;
import com.altomni.apn.company.service.dto.contact.CompanyContactTenantSearchDTO;
import com.altomni.apn.company.vo.contact.CompanyContactCheckDataExistVO;
import com.altomni.apn.company.vo.contact.CompanyContactVO;
import com.altomni.apn.company.vo.contact.SalesLeadClientContactVO;
import com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactProfile;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Set;

@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/api/v3/saleslead")
public class SalesLeadClientContactResource {

    private static final String ENTITY_NAME = "Company AccountBusiness Client Contact";

    @Resource
    private SalesLeadClientContactService salesLeadClientContactService;


    @PostMapping("/contacts")
    @NoRepeatSubmit
    public ResponseEntity<CompanyContactVO> createSalesLeadContact(@Valid @RequestBody CrmContactDTO crmContactDTO, @RequestHeader HttpHeaders headers) throws URISyntaxException {
        log.info("[APN: Company Client Contact @{}] REST request to save SalesLeadClientContact : {}", SecurityUtils.getUserId(), crmContactDTO);
        CompanyContactVO result = salesLeadClientContactService.createSalesLeadContact(crmContactDTO, headers);
        return ResponseEntity.created(new URI("/api/v3/company/saleslead/contacts" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @PostMapping("/contacts-verified")
    @NoRepeatSubmit
    public ResponseEntity<List<TalentDTOV3>> verifiedContact(@Valid @RequestBody CrmContactDTO crmContactDTO) throws URISyntaxException {
        log.info("[APN: Company Client Contact @{}] REST request to verified SalesLeadClientContact : {}", SecurityUtils.getUserId(), crmContactDTO);
        List<TalentDTOV3> result = salesLeadClientContactService.verifiedContactWithDetail(crmContactDTO);
        return ResponseEntity.ok(result);
    }

    @ProcessConfidentialTalent(operation = ProcessConfidentialTalent.operation.FILTER)
    @PostMapping("/client-contacts/search")
    public ResponseEntity<List<SalesLeadClientContactVO>> searchSalesLeadClientContact(@Valid @RequestBody CompanyContactSearchDTO companyContactSearchDTO) throws URISyntaxException {
        log.info("[APN: Company AccountBusiness Client Contact @{}] REST request to search SalesLeadClientContact : {}", SecurityUtils.getUserId(), companyContactSearchDTO);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList(Constants.HEADER_PAGINATION_COUNT, Constants.HEADER_PAGINATION_VIEW_COUNT));
        List<SalesLeadClientContact> salesLeadClientContactList = salesLeadClientContactService.searchSalesLeadClientContact(companyContactSearchDTO, headers);
        List<SalesLeadClientContactVO> result = salesLeadClientContactService.toVo(salesLeadClientContactList, headers);
        return new ResponseEntity<>(result, headers, HttpStatus.OK);
    }

    @PostMapping("/client-contacts/tenant/search")
    public ResponseEntity<List<SalesLeadClientContactVO>> searchTenantSalesLeadClientContact(@Valid @RequestBody CompanyContactTenantSearchDTO companyContactTenantSearchDTO) {
        log.info("[APN: Company AccountBusiness Client Contact @{}] REST request to search tenant's SalesLeadClientContact : {}", SecurityUtils.getUserId(), companyContactTenantSearchDTO);
        List<SalesLeadClientContactVO> result = salesLeadClientContactService.searchTenantSalesLeadClientContact(companyContactTenantSearchDTO);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    /**
     * For the drop-down list on the job list page
     * @param active
     * @return
     */
    @Deprecated
    @GetMapping("/client-contacts/tenant/list")
    public ResponseEntity<List<SalesLeadClientContactVO>> queryTenantSalesLeadClientContactList(@RequestParam(name = "active", required = false) Boolean active) {
        log.info("[APN: Company AccountBusiness Client Contact @{}] REST request to query tenant's SalesLeadClientContact list: {}", SecurityUtils.getUserId(), active);
        List<SalesLeadClientContactVO> result = salesLeadClientContactService.queryTenantSalesLeadClientContactList(active);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


    @PostMapping("/client-contacts/check-duplicated-talent")
    public ResponseEntity<List<CompanyContactVO>> checkContactDuplicatedTalent(@Valid @RequestBody CompanyContactCheckDuplicatedTalentDTO companyContactCheckDuplicatedTalentDTO) throws IOException {
        log.info("[APN: Company AccountBusiness Client Contact @{}] REST request to check company contact duplicated talent : {}", SecurityUtils.getUserId(), companyContactCheckDuplicatedTalentDTO);
        return new ResponseEntity<>(salesLeadClientContactService.checkContactDuplicatedTalent(companyContactCheckDuplicatedTalentDTO), HttpStatus.OK);
    }

    @GetMapping("/client-contacts/{id}")
    public ResponseEntity<SalesLeadClientContactVO> getSalesLeadClientContact(@PathVariable("id")  Long id, @RequestHeader HttpHeaders headers) {
        log.debug("[APN: Company AccountBusiness Client Contact] REST request to get SalesLeadClientContacts by id: {}, ", id);
        SalesLeadClientContactVO salesLeadClientContactVO = salesLeadClientContactService.findById(id, headers);
        return ResponseEntity.ok().body(salesLeadClientContactVO);
    }

    @PostMapping("/client-contact/brief-list")
    public ResponseEntity<List<SalesLeadClientContactVO>> getSalesLeadClientContact(@RequestBody List<Long> ids) {
        log.debug("[APN: Company AccountBusiness Client Contact] REST request to get brief SalesLeadClientContacts by ids: {}, ", ids);
        List<SalesLeadClientContactVO> salesLeadClientContactBriefs = salesLeadClientContactService.findByIdIn(ids);
        return ResponseEntity.ok().body(salesLeadClientContactBriefs);
    }

    @GetMapping("/client-contact/info/{id}")
    public ResponseEntity<SalesLeadClientContact> getSalesLeadClientContactById(@PathVariable("id")  Long id) {
        log.debug("[APN: Company AccountBusiness Client Contact @{}] REST request to get SalesLeadClientContacts by id: {}, ", SecurityUtils.getUserId(), id);
        SalesLeadClientContact SalesLeadClientContact = salesLeadClientContactService.findInfoById(id);
        return ResponseEntity.ok().body(SalesLeadClientContact);
    }


    @PostMapping("/client-contacts/by-contactId-and-receiveEmail")
    @Timed
    public ResponseEntity<List<ClientContactDTO>> findBriefContactByIdAndReceiveEmail(@RequestBody List<Long> contactIds) {
        List<ClientContactDTO> clientContactDTOList = salesLeadClientContactService.findBriefContactByIdAndReceiveEmail(contactIds);
        return ResponseEntity.ok().body(clientContactDTOList);
    }

    @ApiOperation(value = "contacts approver management")
    @PostMapping("/client-contacts/approver")
    @NoRepeatSubmit
    @Timed
    public ResponseEntity<ApproverVO> approver(@RequestBody ApproverDTO approverDTO) {
        log.info("[APN: ClientContact @{}] REST request to approver: {}", SecurityUtils.getUserId(), approverDTO);
        ApproverVO result = salesLeadClientContactService.approver(approverDTO);
        return ResponseEntity.ok().body(result);
    }

    @ApiOperation(value = "contacts hasApproverPermission")
    @GetMapping("/client-contacts/hasApproverPermission/{companyId}")
    @NoRepeatSubmit
    @Timed
    public ResponseEntity<Boolean> hasApproverPermission(@PathVariable Long companyId) {
        log.info("[APN: ClientContact @{}] REST request to hasApproverPermission: {}", SecurityUtils.getUserId(), companyId);
        Boolean result = salesLeadClientContactService.hasApproverPermission(companyId);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("/client-contact/info-profile/{id}")
    public ResponseEntity<SalesLeadClientContactProfile> findSalesLeadClientContactProfile(@PathVariable("id") Long id) {
        log.debug("[APN: Company AccountBusiness Client Contact @{}] REST request to findSalesLeadClientContactProfile : {}", SecurityUtils.getUserId(), id);
        SalesLeadClientContactProfile profile = salesLeadClientContactService.findSalesLeadClientContactProfile(id);
        return ResponseEntity.ok().body(profile);
    }

    @Deprecated
    @ApiOperation(value = "query contact locationId by talentId")
    @GetMapping("/client-contacts/talent/{talentId}")
    public ResponseEntity<Long> queryContactLocationIdByTalentId(@PathVariable ("talentId") Long talentId){
        return ResponseEntity.ok(salesLeadClientContactService.queryContactLocationIdByTalentId(talentId));
    }

    @Deprecated
    @ApiOperation(value = "update contact locationId by talentId")
    @PutMapping("/client-contacts/talent/{talentId}/location/{locationId}")
    @NoRepeatSubmit
    public ResponseEntity<Void> updateContactLocationIdByTalentId(@PathVariable ("talentId") Long talentId, @PathVariable ("locationId") Long locationId){
        salesLeadClientContactService.updateContactLocationIdByTalentId(talentId, locationId);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @ApiOperation(value = "query contact locationId by talentId")
    @GetMapping("/client-contacts/find-by-talent-id")
    public ResponseEntity<List<SalesLeadClientContactVO>> queryAllContactByTalentId(@RequestParam ("talentId") Long talentId){
        return ResponseEntity.ok(salesLeadClientContactService.queryAllContactByTalentId(talentId));
    }

    @ApiOperation(value = "query contact by companyIds")
    @PostMapping("/client-contacts/find-by-company-id")
    public ResponseEntity<List<SalesLeadClientContactVO>> findAllContactByCompany(@RequestBody CompanyContactSearchDTO companyContactSearchDTO){
        return ResponseEntity.ok(salesLeadClientContactService.findAllContactByCompany(companyContactSearchDTO.getCompanyIds()));
    }

    /***
     * Return client contact brief info for job processing
     * @param clientContactId
     * @return
     */
    @GetMapping("/client-contacts/company/{companyId}/job-contact-client/{clientContactId}")
    @Timed
    public ResponseEntity<ClientContactBriefInfoDTO> findBriefContactBriefInfoById(@PathVariable("companyId") Long companyId, @PathVariable("clientContactId") Long clientContactId) {
        ClientContactBriefInfoDTO clientContactDTO = salesLeadClientContactService.findClientContactBriefById(companyId, clientContactId);
        return ResponseEntity.ok().body(clientContactDTO);
    }

    @PostMapping("/client-contacts/talent-client-contact-status")
    @Timed
    public ResponseEntity<List<TalentClientContactStatusDTO>> getTalentAsClientContactStatus(@RequestBody List<Long> talentIds){
        var result = salesLeadClientContactService.getTalentClientContactStatus(talentIds);
        return ResponseEntity.ok().body(result);
    }

    @PostMapping("/client-contacts/talent-client-contact-status/by-contactIds")
    @Timed
    public ResponseEntity<List<TalentClientContactRelationDTO>> getTalentIdsByContactIds(@RequestBody Set<Long> contactIds){
        var result = salesLeadClientContactService.getTalentClientContactStatusByContactIds(contactIds);
        return ResponseEntity.ok().body(result);
    }

    @PostMapping("/client-contacts/talent-client-contact-status/by-talentIds")
    @Timed
    public ResponseEntity<List<TalentClientContactRelationDTO>> getContactIdsByTalentIds(@RequestBody Set<Long> talentIds){
        var result = salesLeadClientContactService.getTalentClientContactStatusByTalentIds(talentIds);
        return ResponseEntity.ok().body(result);
    }

    @PostMapping("/client-contacts/check-data-exist")
    @NoRepeatSubmit
    public ResponseEntity<CompanyContactCheckDataExistVO> contactCheckDataExist(@Valid @RequestBody CrmContactCheckDataExistDTO crmContactDTO) {
        log.info("[APN: Company Client Contact @{}] REST request to check contact relation data : {}", SecurityUtils.getUserId(), crmContactDTO);
        CompanyContactCheckDataExistVO result = salesLeadClientContactService.contactCheckDataExist(crmContactDTO);
        return ResponseEntity.ok().body(result);
    }
}
