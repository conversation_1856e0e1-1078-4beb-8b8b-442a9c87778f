package com.altomni.apn.company.repository.company.projectTeam;


import com.altomni.apn.company.domain.company.projectTeam.CompanyProjectTeamUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface CompanyProjectTeamUserRepository extends JpaRepository<CompanyProjectTeamUser, Long>, JpaSpecificationExecutor<CompanyProjectTeamUser> {

    @Modifying
    @Transactional
    @Query(value = " delete from company_project_team_user where team_id = ?1 ", nativeQuery = true)
    void deleteAllByTeamId(Long teamId);

    List<CompanyProjectTeamUser> findAllByTeamId(Long teamId);

    List<CompanyProjectTeamUser> findAllByTeamIdIn(List<Long> teamIds);

}
