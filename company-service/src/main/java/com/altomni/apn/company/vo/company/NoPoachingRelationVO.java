package com.altomni.apn.company.vo.company;

import com.altomni.apn.company.domain.enumeration.company.NoPoachingRelationType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class NoPoachingRelationVO implements Serializable {

    @ApiModelProperty(value = "no Poaching Parent id")
    private Long id;

    @ApiModelProperty(value = "no Poaching Parent relation type")
    private NoPoachingRelationType relationType;


}
