package com.altomni.apn.company.repository.company.note;

import com.altomni.apn.company.domain.company.note.CompanyProgressNoteContactRelation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CompanyProgressNoteContactRelationRepository extends JpaRepository<CompanyProgressNoteContactRelation, Long> {

    List<CompanyProgressNoteContactRelation> findAllByNoteId(Long noteId);

    List<CompanyProgressNoteContactRelation> findAllByNoteIdIn(List<Long> noteIds);

}
