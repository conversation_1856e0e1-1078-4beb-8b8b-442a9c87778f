package com.altomni.apn.company.vo.company;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.job.JobCompanyBrief;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.List;

/**
 * A CompanyJobVO.
 * <AUTHOR>
 */
@AllArgsConstructor
@ApiModel(description = "Vo for companyJob")
@NoArgsConstructor
@Data
public class CompanyJobVO extends AbstractAuditingEntity implements Serializable {

    @ApiModelProperty(value = "The id for job.")
    private Long id;

    @ApiModelProperty(value = "The id for company.")
    private Long companyId;

    @ApiModelProperty(value = "The title for job.")
    private String title;

    @ApiModelProperty(value = "The status for job.")
    @Convert(converter = JobStatusConverter.class)
    private JobStatus status;

    @ApiModelProperty(value = "The type for job.")
    private JobType jobType;

    @ApiModelProperty(value = "job lacation strings, one or more official city names ")
    private List<LocationDTO> locations;

    @ApiModelProperty(value = "submitToClient for job.")
    private Long submitToClient;

    @ApiModelProperty(value = "interview for job.")
    private Long interview;

    @ApiModelProperty(value = "onBoard for job.")
    private Long onBoard;

    @ApiModelProperty(value = "if or not this is a private job.")
    private boolean isPrivateJob;

    public static CompanyJobVO fromJobCompanyBrief(JobCompanyBrief jobCompanyBrief) {
        CompanyJobVO companyJobVO = new CompanyJobVO();
        ServiceUtils.myCopyProperties(jobCompanyBrief, companyJobVO);
        return companyJobVO;
    }

}
