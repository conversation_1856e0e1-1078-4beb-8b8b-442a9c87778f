package com.altomni.apn.company.repository.talent;

import com.altomni.apn.company.domain.talent.TalentCompanySyncBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import javax.swing.*;
import java.util.List;


@Repository
public interface TalentCompanySyncBriefRepository extends JpaRepository<TalentCompanySyncBrief, Long> {

    List<TalentCompanySyncBrief> findAllByIdIn(List<Long> ids);

}
