package com.altomni.apn.company.service.company.param.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.company.CompanyType;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.domain.company.param.CompanyParamConfig;
import com.altomni.apn.company.repository.company.param.CompanyParamRepository;
import com.altomni.apn.company.service.company.param.CompanyParamService;
import com.altomni.apn.company.vo.param.CompanyParamConfigVO;
import com.altomni.apn.company.vo.param.CompanyParamVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyParamServiceImpl implements CompanyParamService {

    private final Long UNIVERSAL_TENANT_ID = -4L;

    @Resource
    private CompanyParamRepository companyParamRepository;

    @Override
    @Cacheable(cacheNames = {"params_config_company"}, cacheManager = "concurrentMapCacheManager", key = "'tenant:' + T(com.altomni.apn.common.utils.SecurityUtils).getTenantId()", unless = "#result.prospect == null && #result.client == null")
    public CompanyParamConfigVO queryCompanyParamsConfig() {
        CompanyParamConfigVO companyParamConfigVO = new CompanyParamConfigVO();
        List<CompanyParamConfig> paramConfigList;
        paramConfigList = companyParamRepository.findAllByTenantId(SecurityUtils.getTenantId());
        if (CollectionUtils.isEmpty(paramConfigList)) {
            paramConfigList = companyParamRepository.findAllByTenantId(UNIVERSAL_TENANT_ID);
        }
        Map<CompanyType, String> paramConfigMap = paramConfigList.stream().collect(Collectors.toMap(CompanyParamConfig::getType, CompanyParamConfig::getConfig));
        if (paramConfigMap.containsKey(CompanyType.CLIENT)) {
            companyParamConfigVO.setClient(JSONUtil.toList(JSONUtil.parseArray(paramConfigMap.get(CompanyType.CLIENT)), CompanyParamVO.class));
        }
        if (paramConfigMap.containsKey(CompanyType.POTENTIAL_CLIENT)) {
            companyParamConfigVO.setProspect(JSONUtil.toList(JSONUtil.parseArray(paramConfigMap.get(CompanyType.POTENTIAL_CLIENT)), CompanyParamVO.class));
        }
        return companyParamConfigVO;
    }

    @Override
    @Cacheable(cacheNames = {"params_config_company"}, cacheManager = "concurrentMapCacheManager", key = "'vertion_tenant:' + T(com.altomni.apn.common.utils.SecurityUtils).getTenantId()", unless = "#result == null")
    public String queryCompanyParamsConfigVersion() {
        List<CompanyParamConfig> paramConfigList;
        paramConfigList = companyParamRepository.findAllByTenantId(SecurityUtils.getTenantId());
        if (CollectionUtils.isEmpty(paramConfigList)) {
            paramConfigList = companyParamRepository.findAllByTenantId(UNIVERSAL_TENANT_ID);
        }
        if (CollectionUtils.isEmpty(paramConfigList)) {
            return null;
        }
        return paramConfigList.get(0).getVersions();
    }

    @CacheEvict(cacheNames = "params_config_company", allEntries = true)
    @Override
    public void clearCompanyParamsConfigCache() {

    }


}
