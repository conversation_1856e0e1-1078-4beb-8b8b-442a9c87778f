package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The IndustryType enumeration.
 */
public enum StaffSizeType implements ConvertedEnum<Integer> {
    FROM_1_TO_10(1),
    FROM_11_TO_50(2),
    FROM_51_TO_100(3),
    FROM_101_TO_500(4),
    FROM_501_TO_1000(5),
    FROM_1001_TO_10000(6),
    ABOVE_10000(7);
    private final int dbValue;

    StaffSizeType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<StaffSizeType, Integer> resolver = new ReverseEnumResolver<>(StaffSizeType.class, StaffSizeType::toDbValue);

    public static StaffSizeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
