package com.altomni.apn.company.domain.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractType enumeration.
 */
public enum CategoryFolderType implements ConvertedEnum<Long> {
    PROSPECT_MY_COMPANY(10L),
    PROSPECT_ALL_COMPANY(20L),
    CLIENT_MY_COMPANY(30L),
    CLIENT_ALL_COMPANY(40L);

    private final Long dbValue;

    CategoryFolderType(Long dbValue) { this.dbValue = dbValue; }

    @Override
    public Long toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<CategoryFolderType, Long> resolver =
        new ReverseEnumResolver<>(CategoryFolderType.class, CategoryFolderType::toDbValue);

    public static CategoryFolderType fromDbValue(Long dbValue) {
        return resolver.get(dbValue);
    }
}
