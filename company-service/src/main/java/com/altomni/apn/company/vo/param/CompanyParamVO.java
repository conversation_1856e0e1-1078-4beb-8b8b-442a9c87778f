package com.altomni.apn.company.vo.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@AllArgsConstructor
@ApiModel(description = "Vo for company params")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CompanyParamVO implements Serializable {

    @ApiModelProperty(value = "the name for field.")
    private String field;

    @ApiModelProperty(value = "the enDisplay for field.")
    private String displayEn;

    @ApiModelProperty(value = "the cnDisplay for field.")
    private String displayCn;

    @ApiModelProperty(value = "is field required.")
    private Boolean required;

    @ApiModelProperty(value = "child params.")
    private List<CompanyParamVO> children;

}
