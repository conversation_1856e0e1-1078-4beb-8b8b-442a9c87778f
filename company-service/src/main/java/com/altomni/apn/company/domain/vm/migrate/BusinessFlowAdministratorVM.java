package com.altomni.apn.company.domain.vm.migrate;

import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
public class BusinessFlowAdministratorVM implements Serializable {

    private Long id;

    private Long accountBusinessId;

    //crm userId, not apn userId
    private Long userId;

    private Integer contribution;

    private SalesLeadRoleType salesLeadRoleType;

    private Long companyId;

    private Long permissionUserId;

    private Long permissionTeamId;

    private String createdBy;

    private Instant createdDate;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

}
