package com.altomni.apn.company.service.am;

import com.altomni.apn.company.domain.amreport.AmReportTalentJobNote;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.vo.AmReportVO;
import com.altomni.apn.company.domain.vo.CompanyVo;
import com.altomni.apn.company.service.dto.AmReportTalentJobNoteDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface AmReportService {

    AmReportVO findAmReport(List<JobType> jobType, List<Long> contact, Long companyId, Long tenantId, Boolean needTotalFlag,List<Long> salesLeadList);

    AmReportTalentJobNote saveHighLightedExperience(AmReportTalentJobNoteDto amReportTalentJobNote);

    AmReportTalentJobNote saveAmUpdate(AmReportTalentJobNoteDto amReportDto);

    void downloadReport(Long companyId, HttpServletResponse response);

    void sendAmReportAmEmail(CompanyVo company, String mailTemplate, String imageTemplate);

}
