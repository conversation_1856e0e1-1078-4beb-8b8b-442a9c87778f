package com.altomni.apn.company.config.env;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "crm-forward-url")
public class CrmForwardUrlProperties {

    private Map<String, List<String>> urlMap;

    private Map<String, List<String>> crmPublicUrlMap;

}
