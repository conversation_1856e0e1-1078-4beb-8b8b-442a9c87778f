package com.altomni.apn.company.repository.company;

import com.altomni.apn.common.vo.settings.SettingTrackRecordVO;
import com.altomni.apn.company.vo.company.CompanyPurchaseOrderDetailModifyVO;
import com.altomni.apn.company.vo.company.CompanyPurchaseOrderDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class CompanyPurchaseOrderNativeRepository {

    @Resource
    private EntityManager entityManager;


    @Transactional(readOnly = true)
    public List<CompanyPurchaseOrderDetailModifyVO> findByCompanyIdAndOrderNumber(List<String> orderNumberList, Long companyId) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder();
        dataSql.append("""
                select d.id,d.order_number as orderNumber,d.purchase_order_id as purchaseOrderId 
                            from company_purchase_order_detail d
                             left join company_purchase_order o on o.id = d.purchase_order_id
                             where d.order_number in(:numbers) and o.company_id = :companyId
                """);

        javax.persistence.Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("numbers", orderNumberList);
        dataQuery.setParameter("companyId", companyId);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(CompanyPurchaseOrderDetailModifyVO.class));
        return dataQuery.getResultList();
    }
}