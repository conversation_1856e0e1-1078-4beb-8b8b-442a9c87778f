package com.altomni.apn.company.repository.company.note;


import com.altomni.apn.company.domain.company.note.CompanyClientNote;
import com.altomni.apn.company.domain.enumeration.note.CompanyNoteCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

@Repository
public interface CompanyClientNoteRepository extends JpaRepository<CompanyClientNote, Long> {

    Page<CompanyClientNote> findAllByCompanyIdAndDeleted(Long companyId, Boolean deleted, Pageable pageable);

    Page<CompanyClientNote> findAllByCompanyIdAndNoteLikeAndDeleted(Long companyId, String note, <PERSON><PERSON><PERSON> deleted, Pageable pageable);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_client_note n SET n.last_sync_time=:lastSyncTime WHERE n.id=:noteId", nativeQuery = true)
    void updateCompanyClientNoteLastSyncTime(@Param("noteId") Long noteId, @Param("lastSyncTime") Instant lastSyncTime);

    Integer countByCompanyId(Long companyId);

    CompanyClientNote findFirstByCompanyIdAndAndCategory(Long companyId, CompanyNoteCategory category);

    List<CompanyClientNote> findAllByCompanyIdInAndCategory(List<Long> companyIds, CompanyNoteCategory category);

}
