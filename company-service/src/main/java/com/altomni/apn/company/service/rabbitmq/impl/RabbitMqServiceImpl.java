package com.altomni.apn.company.service.rabbitmq.impl;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.config.env.EsfillerMQProperties;
import com.altomni.apn.company.service.rabbitmq.RabbitMqService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.QueueInformation;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class RabbitMqServiceImpl implements RabbitMqService {

    private final Logger log = LoggerFactory.getLogger(RabbitMqServiceImpl.class);

    public static final String QUEUE_MESSAGE_COUNT = "QUEUE_MESSAGE_COUNT";

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource(name = "esfillerRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource(name = "esfillerAmqpAdmin")
    private AmqpAdmin esfillerAmqpAdmin;

    @Resource
    private ApplicationProperties applicationProperties;


    @Override
    public void saveCompanyProfile(String companyProfile, int priority) {
        try {
            log.info("[EsFillerCompanyService: syncCompanyToMQ] send company profile to rabbitMQ with profile: {}", companyProfile);
            rabbitTemplate.convertAndSend(esfillerMQProperties.getEsfillerMQExchange(), esfillerMQProperties.getToEsFillerRoutingKey(), companyProfile, message -> {
                message.getMessageProperties().setPriority(priority);
                return message;
            });
        }catch (Exception e) {
            log.error("[EsFillerCompanyService: syncCompanyToMQ @{}] send company profile to rabbitMQ error: {}, profile: {}", SecurityUtils.getUserId(), e.getMessage(), companyProfile);
        }
    }

    @Override
    public void saveCompanyClientNoteProfile(String companyClientNoteProfile, int priority) {
        try {
            rabbitTemplate.convertAndSend(esfillerMQProperties.getEsfillerMQExchange(), esfillerMQProperties.getToEsFillerRoutingKey(), companyClientNoteProfile, message -> {
                message.getMessageProperties().setPriority(priority);
                return message;
            });
        }catch (Exception e) {
            log.error("[EsFillerCompanyService: syncCompanyClientNoteToMQ @{}] send companyClientNote profile to rabbitMQ error: {}, profile: {}", SecurityUtils.getUserId(), e.getMessage(), companyClientNoteProfile);
        }
    }


    @Override
    public void saveCompanyProgressNoteProfile(String companyProgressNoteProfile, int priority) {
        try {
            rabbitTemplate.convertAndSend(esfillerMQProperties.getEsfillerMQExchange(), esfillerMQProperties.getToEsFillerRoutingKey(), companyProgressNoteProfile, message -> {
                message.getMessageProperties().setPriority(priority);
                return message;
            });
        }catch (Exception e) {
            log.error("[EsFillerCompanyService: syncCompanyProgressNoteToMQ @{}] send companyProgressNote profile to rabbitMQ error: {}, profile: {}", SecurityUtils.getUserId(), e.getMessage(), companyProgressNoteProfile);
        }
    }

    @Override
    public Integer checkMessageCount(String queue) {
        final QueueInformation queueInfo = esfillerAmqpAdmin.getQueueInfo(queue);
        int messageCount = queueInfo.getMessageCount();
        if (messageCount > 0){
            log.info("check message count: {}", messageCount);
        }
        return messageCount;
    }

    @Override
    public void saveCrmBackFillData(String backFillProfile) {
        try {
            rabbitTemplate.convertAndSend(esfillerMQProperties.getEsfillerMQExchange(), applicationProperties.getCrmBackFillRoutingKey(), backFillProfile);
        }catch (Exception e) {
            log.error("[CompanyRabbitService: save[CrmBackFillData @{}] send crmBackFill profile to rabbitMQ error: {}, profile: {}", SecurityUtils.getUserId(), e.getMessage(), backFillProfile);
        }
    }
}
