package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class NoPoachingRelationTypeConverter extends AbstractAttributeConverter<com.altomni.apn.company.domain.enumeration.company.NoPoachingRelationType, Integer> {
    public NoPoachingRelationTypeConverter() {
        super(NoPoachingRelationType::toDbValue, com.altomni.apn.company.domain.enumeration.company.NoPoachingRelationType::fromDbValue);
    }
}
