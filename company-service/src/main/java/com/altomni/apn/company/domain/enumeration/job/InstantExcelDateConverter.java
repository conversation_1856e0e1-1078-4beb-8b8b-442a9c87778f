package com.altomni.apn.company.domain.enumeration.job;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;

public class InstantExcelDateConverter implements Converter<Instant> {
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneId.of("Asia/Shanghai"));

    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }


    @Override
    public WriteCellData<?> convertToExcelData(Instant instant, ExcelContentProperty contentProperty, GlobalConfiguration configuration) throws Exception {
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        String dateString = formatter.format(zonedDateTime);
        return new WriteCellData<>(dateString);
    }
}
