package com.altomni.apn.company.repository.company.folder;


import com.altomni.apn.company.domain.folder.CompanyCustomFolderConnectClient;
import com.altomni.apn.company.domain.vm.EntityCountVM;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CompanyCustomFolderConnectClientRepository extends JpaRepository<CompanyCustomFolderConnectClient, Long> {

    @Query(value = "SELECT new com.altomni.apn.company.domain.vm.EntityCountVM(c.folderId, count(DISTINCT c.companyId)) FROM CompanyCustomFolderConnectClient c WHERE c.folderId IN ?1 GROUP BY c.folderId")
    List<EntityCountVM> countCompanyByFolderIdIn(List<Long> folders);

    List<CompanyCustomFolderConnectClient> findAllByFolderIdAndCompanyIdIn(Long folderId, List<Long> companyIds);

    List<CompanyCustomFolderConnectClient> findAllByFolderIdInAndCompanyIdIn(List<Long> folderIds, List<Long> companyIds);

    @Query(value = "SELECT id FROM company_custom_folder_connect_client WHERE folder_id IN ?1", nativeQuery = true)
    List<Long> findAllIdByFolderIdIn(List<Long> folderIds);

    @Query(value = "SELECT c.folderId FROM CompanyCustomFolderConnectClient c WHERE c.companyId = ?1")
    List<Long> findAllFolderIdByCompanyId(Long companyId);

    @Query(value = "SELECT c.folderId FROM CompanyCustomFolderConnectClient c INNER JOIN CompanyCustomFolder f ON f.id = c.folderId  WHERE c.companyId = ?1 AND f.category = ?2")
    List<Long> findAllFolderIdByCompanyIdAndCategory(Long companyId, Long category);

    @Modifying
    @Query(value = "DELETE FROM company_custom_folder_connect_client cf INNER JOIN company_custom_folder f ON cf.folder_id = f.id WHERE cf.company_id = ?1 AND f.category = ?2", nativeQuery = true)
    void deleteAllByCompanyIdAndCategory(Long companyId, Long category);

    List<CompanyCustomFolderConnectClient> findAllByFolderIdIn(List<Long> folderIds);

    void deleteAllByCreatedBy(String createdBy);

}
