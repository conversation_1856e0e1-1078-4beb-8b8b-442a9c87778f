package com.altomni.apn.company.service.company;

import com.altomni.apn.common.vo.company.CompanyClientInfoVO;
import com.altomni.apn.company.service.dto.CompanyClientInfoDTO;

import java.util.List;

public interface CompanyClientInfoService {

    void save(CompanyClientInfoDTO dto);

    void modify(CompanyClientInfoDTO dto);

    List<CompanyClientInfoVO> findByCompanyId(Long companyId);

    CompanyClientInfoVO getCompanyClientInfoById(Long id);

    void delete(Long id);
}
