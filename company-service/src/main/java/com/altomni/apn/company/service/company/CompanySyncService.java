package com.altomni.apn.company.service.company;

import com.altomni.apn.company.web.rest.vm.company.MqMessageCountVM;

import java.util.Collection;
import java.util.List;

public interface CompanySyncService {

    void sendEmailAmReport();

    MqMessageCountVM checkCompanyMqMessageCount();

    void syncCompaniesToMQ(Collection<Long> companyIds, int priority);

    void deleteFromEsFilter(List<Long> companyIds, int priority);

    MqMessageCountVM checkCompanyClientNoteMqMessageCount();

    void syncCompanyClientNoteToMQ(Collection<Long> companyClientNoteIds, int priority);

    MqMessageCountVM checkCompanyProgressNoteMqMessageCount();

    void syncCompanyProgressNoteToMQ(Collection<Long> companyProgressNoteIds, int priority);

    MqMessageCountVM checkCompanyContactMqMessageCount();

    void syncCompanyContactToMQ(Collection<Long> contactIds, int priority);

    void syncCompanyToHrMQ(Collection<Long> companyIds, int priority);
}
