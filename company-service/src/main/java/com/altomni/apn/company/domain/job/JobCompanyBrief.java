package com.altomni.apn.company.domain.job;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * A Job Company Brief Relationship.
 */
@ApiModel(description = "Job entity. It comes from parser parsing JD, or user manually create job using ATS.")
@Entity
@Table(name = "job")
@AllArgsConstructor
@NoArgsConstructor
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobCompanyBrief extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "title")
    private String title;

    @Convert(converter = JobStatusConverter.class)
    @Column(name = "status", nullable = false)
    private JobStatus status;

    @JsonIgnore
    @Column(name = "tenant_id", nullable = false, updatable = false)
    private Long tenantId;

    @ApiModelProperty(value = "the recruitment process id for current job")
    @Column(name = "recruitment_process_id")
    private Long recruitmentProcessId;

    @Column(name = "sales_lead_id")
    private Long salesLeadId;

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public Long getCompanyId() { return companyId; }

    public void setCompanyId(Long companyId) { this.companyId = companyId; }

    public JobStatus getStatus() { return status; }

    public void setStatus(JobStatus status) { this.status = status; }

    public Long getTenantId() { return tenantId; }

    public void setTenantId(Long tenantId) { this.tenantId = tenantId; }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getRecruitmentProcessId() {
        return recruitmentProcessId;
    }

    public void setRecruitmentProcessId(Long recruitmentProcessId) {
        this.recruitmentProcessId = recruitmentProcessId;
    }

    public Long getSalesLeadId() {
        return salesLeadId;
    }

    public void setSalesLeadId(Long salesLeadId) {
        this.salesLeadId = salesLeadId;
    }
}
