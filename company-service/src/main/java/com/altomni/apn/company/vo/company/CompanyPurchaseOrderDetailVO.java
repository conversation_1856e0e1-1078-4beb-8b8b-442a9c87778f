package com.altomni.apn.company.vo.company;

import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
public class CompanyPurchaseOrderDetailVO {

    private Long id ;
    private String orderNumber ;
    private BigDecimal amount;
    private BigDecimal balance ;
    private Long currencyId ;
    private Long purchaseOrderId ;
    private String addBy;
    private Instant addOn;
}