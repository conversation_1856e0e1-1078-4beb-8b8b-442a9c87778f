package com.altomni.apn.company.service.dto.note;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@Data
@ApiModel(description = "note for client company")
public class CompanyClientNoteDTO extends CompanyClientNoteUpdateDTO implements Serializable {

    @ApiModelProperty(value = "the id for company.")
    @NotNull
    private Long companyId;

}
