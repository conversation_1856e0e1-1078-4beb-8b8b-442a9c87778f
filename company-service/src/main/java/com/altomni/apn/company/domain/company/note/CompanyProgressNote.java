package com.altomni.apn.company.domain.company.note;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.enumeration.company.CompanyContactType;
import com.altomni.apn.company.domain.enumeration.company.CompanyContactTypeConverter;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * A ProgressNote.
 */
@Entity
@Table(name = "company_progress_note")
@Data
public class CompanyProgressNote extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "contact_type")
    @Convert(converter = CompanyContactTypeConverter.class)
    private CompanyContactType contactType;

    @Column(name = "contact_date")
    private Instant contactDate;

    @Column(name = "note")
    private String note;

    @Column(name = "sales_lead_id")
    private Long salesLeadId;

    @ApiModelProperty(value = "last successful sync to ES")
    @Column(name = "last_sync_time")
    private Instant lastSyncTime;

    public static CompanyProgressNote fromCompanyProgressNoteDTO(CompanyProgressNoteDTO companyProgressNoteDTO) {
        CompanyProgressNote companyProgressNote = new CompanyProgressNote();
        ServiceUtils.myCopyProperties(companyProgressNoteDTO, companyProgressNote);
        return companyProgressNote;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CompanyProgressNote)) {
            return false;
        }
        return id != null && id.equals(((CompanyProgressNote) o).id);
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "CompanyProgressNote{" +
            "id=" + getId() +
            ", companyId=" + getCompanyId() +
            ", contactType='" + getContactType() + "'" +
            ", note='" + getNote() + "'" +
            "}";
    }

}
