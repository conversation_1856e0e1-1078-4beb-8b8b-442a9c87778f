package com.altomni.apn.company.service.store;

import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.store.CopyObjectDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@Component
@FeignClient(value = "common-service")
public interface StoreService {

    @GetMapping("/common/api/v3/s3/store/url/{uuid}/{uploadType}")
    ResponseEntity<String> getPresignedContract(@PathVariable("uuid") String key, @PathVariable("uploadType") String uploadType);

    @PostMapping("/common/api/v3/s3/store/copyObject")
    ResponseEntity<Boolean> copyObject(@RequestBody CopyObjectDto copyObjectDto);

    @GetMapping("/common/api/v3/s3/store/file/{uuid}/{uploadType}")
    ResponseEntity<CloudFileObjectMetadata> downloadContractDisplayImg(@PathVariable("uuid") String key, @PathVariable("uploadType") String uploadType);

}
