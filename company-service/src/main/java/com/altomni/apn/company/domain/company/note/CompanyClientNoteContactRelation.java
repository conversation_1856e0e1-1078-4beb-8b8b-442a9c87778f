package com.altomni.apn.company.domain.company.note;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "BusinessFlow")
@Entity
@Data
@Table(name = "company_client_note_contact_relation")
@AllArgsConstructor
@NoArgsConstructor
public class CompanyClientNoteContactRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "client_note_id ")
    private Long noteId;

    @Column(name = "client_contact_id ")
    private Long clientContactId;

    public CompanyClientNoteContactRelation(Long noteId, Long clientContactId) {
        this.noteId = noteId;
        this.clientContactId = clientContactId;
    }
}
