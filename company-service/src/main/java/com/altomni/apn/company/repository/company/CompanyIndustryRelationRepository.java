package com.altomni.apn.company.repository.company;

import com.altomni.apn.common.domain.dict.CompanyIndustryRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface CompanyIndustryRelationRepository extends JpaRepository<CompanyIndustryRelation, Long> {


    List<CompanyIndustryRelation> findAllByAccountCompanyIdIn(List<Long> companyIds);

    List<CompanyIndustryRelation> findAllByAccountCompanyId(Long companyId);

}
