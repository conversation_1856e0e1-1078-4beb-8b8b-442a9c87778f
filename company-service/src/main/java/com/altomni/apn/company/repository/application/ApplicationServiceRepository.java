package com.altomni.apn.company.repository.application;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.common.domain.enumeration.NodeTypeTableEnum;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.company.domain.vm.EntityCountVM;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@Repository
public class ApplicationServiceRepository {

    @PersistenceContext
    private EntityManager entityManager;

     public List<EntityCountVM> findApplicationCountByTalentIds(List<Long> talentIds) {
        if (CollUtil.isEmpty(talentIds)) {
            return new ArrayList<>();
        }

        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, talentIds);

        StringBuffer querySql = new StringBuffer();
        querySql.append("SELECT\n" +
                "	trp.talent_id id,\n" +
                "	COUNT( trp.id ) count \n" +
                "FROM\n" +
                "	talent_recruitment_process trp \n" +
                "WHERE\n" +
                "	trp.talent_id IN ?1 \n" +
                "	AND trp.id NOT IN (\n" +
                "	SELECT\n" +
                "		trn.talent_recruitment_process_id \n" +
                "	FROM\n" +
                "		talent_recruitment_process_node trn \n" +
                "	WHERE\n" +
                "		trn.talent_recruitment_process_id = trp.id \n" +
                "		AND ((\n" +
                "				trn.node_status = 4 \n" +
                "				) \n" +
                "		OR ( trn.node_status = 1 AND trn.node_type > 50 ))) \n" +
                "GROUP BY\n" +
                "	trp.talent_id");
        return searchData(querySql.toString(), EntityCountVM.class, paramMap);
    }

    public List<EntityCountVM> findApplicationCountByJobIds(List<Long> jobIds, NodeTypeTableEnum type) {
        if (CollUtil.isEmpty(jobIds)) {
            return new ArrayList<>();
        }

        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, jobIds);

        StringBuffer querySql = new StringBuffer();
        createQueryApplicationSql(querySql, type);
        return searchData(querySql.toString(), EntityCountVM.class, paramMap);
    }

    private void createQueryApplicationSql(StringBuffer querySql, NodeTypeTableEnum type) {
        querySql.append("SELECT\n" +
                "	j.id id,\n" +
                "	COUNT( DISTINCT trp.id ) count \n" +
                "FROM\n" +
                "	talent_recruitment_process trp\n" +
                "	INNER JOIN ").append(type.getTableName()).append(" tr ON trp.id = tr.talent_recruitment_process_id\n" +
                "	INNER JOIN job j ON j.id = trp.job_id \n" +
                "WHERE\n" +
                "	trp.job_id IN ?1\n" +
                "	GROUP BY j.id");
    }


    private <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        entityManager.clear();
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private Integer checkInList(Map<Integer, Object> map) {
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new RuntimeException("query sql in condition list > 1000 more than 1");
        }
        return keyList.get(0);
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchData(queryStr, clazz, paramMap);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }


    private <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        return query.getResultList();
    }
}
