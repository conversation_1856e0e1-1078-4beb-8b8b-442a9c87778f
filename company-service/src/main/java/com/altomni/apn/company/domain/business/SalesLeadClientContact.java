package com.altomni.apn.company.domain.business;

import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import com.altomni.apn.common.dto.company.SalesLeadClientContactDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Entity
@Data
@Table(name = "company_sales_lead_client_contact")
public class SalesLeadClientContact extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    private Long id;

    @Column(name = "contact_category")
    private Integer contactCategory;

    @Column(name = "status")
    private boolean active = true;

    @Column(name = "last_contact_date")
    private Instant lastFollowUpTime;

    @Column(name = "tenant_id", nullable = false, updatable = false)
    private Long tenantId;

    @ApiModelProperty(value = "Receive email notifications when Timesheets or Expenses are submitted for my approval.")
    @Column(name = "receive_email")
    private Boolean receiveEmail;

    @Column(name = "approver_id")
    private Long approverId;

    @Column(name = "inactived")
    private Boolean inactived; //TODO: 好像没用了已经

    @Column(name = "zipcode")
    private String zipcode;

    @Column(name = "es_id")
    private String esId;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "company_id")
    private Long companyId;

//    @Column(name = "company_location_id")
//    private Long companyLocationId;

    @Column(name = "crm_contact_id")
    private Long crmContactId;

    /**
     * 关键联系人需求增加
     */
    @ApiModelProperty(value = "is key contact")
    @Column(name = "is_key_contact")
    private Boolean isKeyContact = false;


    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "tenantId", "receive", "approverId", "inactived", "esId", "talentId", "companyId"));

    public SalesLeadClientContact setTalentId(Long talentId) {
        this.talentId = talentId;
        return this;
    }

    public SalesLeadClientContact setCompanyId(Long companyId) {
        this.companyId = companyId;
        return this;
    }

    public static SalesLeadClientContact fromSalesLeadClientContactDTO(SalesLeadClientContactDTO salesLeadClientContactDTO) {
        SalesLeadClientContact salesLeadClientContact = new SalesLeadClientContact();
        ServiceUtils.myCopyProperties(salesLeadClientContactDTO, salesLeadClientContact);
        salesLeadClientContact.setTenantId(SecurityUtils.getTenantId());
        return salesLeadClientContact;
    }

}