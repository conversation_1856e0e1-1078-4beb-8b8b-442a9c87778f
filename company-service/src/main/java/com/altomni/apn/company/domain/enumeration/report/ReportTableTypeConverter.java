package com.altomni.apn.company.domain.enumeration.report;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class ReportTableTypeConverter extends AbstractAttributeConverter<ReportTableType, Integer> {
    public ReportTableTypeConverter() {
        super(ReportTableType::toDbValue, ReportTableType::fromDbValue);
    }
}