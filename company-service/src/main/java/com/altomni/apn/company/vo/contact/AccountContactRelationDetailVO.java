package com.altomni.apn.company.vo.contact;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class AccountContactRelationDetailVO implements Serializable {

    @ApiModelProperty(value = "id for contactRelation")
    private Long id;

    @ApiModelProperty(value = "firstName for contact")
    private String firstName;

    @ApiModelProperty(value = "lastName for contact")
    private String lastName;

    @ApiModelProperty(value = "name for contactRelation")
    private String name;

    @ApiModelProperty(value = "contactId for contactRelation")
    private Long contactId;

    @ApiModelProperty(value = "contacts for contact")
    @NotNull
    private List<ContactInfoVO> contacts;

    @ApiModelProperty(value = "status for the contact relation")
    private Boolean active;

    @ApiModelProperty(value = "contact category for the contact")
    private Integer contactCategory;

    @ApiModelProperty(value = "lastFollowupTime for contact on account company")
    private Instant lastFollowUpTime;

    @ApiModelProperty(value = "apnTags for contact on account company")
    private List<Integer> apnTags;

    @ApiModelProperty(value = "id for contact on account company")
    private Long accountCompanyId;

    @ApiModelProperty(value = "name for contact on account company")
    private String accountCompanyName;

    @ApiModelProperty(value = "note for contact on account company")
    private String note;

    @ApiModelProperty(value = "title for contact")
    private String title;

    @ApiModelProperty(value = "businessUnit for contact")
    private String businessUnit;

    @ApiModelProperty(value = "department for contact")
    private String department;

    @ApiModelProperty(value = "businessGroup for contact")
    private String businessGroup;


    @ApiModelProperty(value = "apnTalentId for the contact")
    private Long apnTalentId;

}
