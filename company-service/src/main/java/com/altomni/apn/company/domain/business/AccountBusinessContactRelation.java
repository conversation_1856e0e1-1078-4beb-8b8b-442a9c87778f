package com.altomni.apn.company.domain.business;

import com.altomni.apn.common.domain.ManualAbstractAuditingEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Data
@NoArgsConstructor
@Table(name = "account_business_contact_relation")
public class AccountBusinessContactRelation implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    private Long id;

    @Column(name = "account_business_id")
    private Long accountBusinessId;

    @Column(name = "client_contact_id")
    private Long clientContactId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccountBusinessContactRelation that = (AccountBusinessContactRelation) o;
        return accountBusinessId.equals(that.accountBusinessId) && clientContactId.equals(that.clientContactId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(accountBusinessId, clientContactId);
    }
}
