package com.altomni.apn.company.vo.contact;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-19
*/
@AllArgsConstructor
@ApiModel(description = "Vo for company contact")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CompanyContactVO implements Serializable {

    @ApiModelProperty(value = "the id for user.")
    public Long id;

    @ApiModelProperty(value = "the name for user.")
    public String name;

    @ApiModelProperty(value = "the talentId for contact.")
    public Long talentId;

    @ApiModelProperty(value = "the talentId for contact.")
    public List<Long> companyIds;

    public CompanyContactVO(Long id, String name) {
        this.id = id;
        this.name = name;
    }
}
