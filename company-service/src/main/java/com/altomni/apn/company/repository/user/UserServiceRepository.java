package com.altomni.apn.company.repository.user;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.company.domain.user.UserCompanyMigrateBrief;
import com.altomni.apn.company.domain.vm.EntityNameVM;
import com.altomni.apn.company.domain.vm.UserBasicVM;
import com.altomni.apn.company.vo.business.OwnerVO;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.domain.permission.PermissionTeamLeader;
import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import com.altomni.apn.user.domain.user.UserBrief;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@Repository
public class UserServiceRepository {

    @PersistenceContext
    private EntityManager entityManager;

    public List<UserBasicVM> getAllOwnerList(Long tenantId) {
        String findAllSql = """
                SELECT u.id, u.first_name, u.last_name, u.activated
                FROM `user` u
                WHERE u.tenant_id = %s
                """.formatted(tenantId);
        return searchData(findAllSql, UserBasicVM.class);
    }

    public EntityNameVM findUserNameById(Long id) {
        String findUserNameByIdtSql = "SELECT id, first_name, last_name FROM `user` WHERE id = " + id;
        return searchFirstData(findUserNameByIdtSql, EntityNameVM.class);
    }

    public List<UserCompanyMigrateBrief> findAll() {
        String findAllSql = "SELECT u.*, pu.team_id FROM `user` u LEFT JOIN permission_user_team pu ON u.id = pu.user_id AND pu.is_primary = 1 GROUP BY u.id";
        return searchData(findAllSql, UserCompanyMigrateBrief.class);
    }

    public List<Tenant> findAllTenant() {
        String findAllSql = "SELECT * FROM tenant";
        return searchData(findAllSql, Tenant.class);
    }

    public List<PermissionTeam> findAllPermissionTeam() {
        String findAllSql = "SELECT * FROM permission_team";
        return searchData(findAllSql, PermissionTeam.class);
    }

    public List<PermissionTeamLeader> findAllPermissionTeamLeader() {
        String findAllSql = "SELECT * FROM permission_team_leader";
        return searchData(findAllSql, PermissionTeamLeader.class);
    }

    public List<PermissionUserTeam> findAllPermissionUserTeam() {
        String findAllSql = "SELECT * FROM permission_user_team";
        return searchData(findAllSql, PermissionUserTeam.class);
    }

    public List<EntityNameVM> findUserNameByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        String joinedIds = ids.stream().map(Object::toString).collect(Collectors.joining(","));
        String findUserNameByIdtSql = "SELECT id, first_name, last_name FROM `user` WHERE id IN (" + joinedIds + ")";
        return searchData(findUserNameByIdtSql, EntityNameVM.class);
    }

    public List<EntityNameVM> findCrmUserNameByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        String joinedIds = ids.stream().map(Object::toString).collect(Collectors.joining(","));
        String findUserNameByIdtSql = """
                SELECT u.id, u.first_name, u.last_name, u.activated
                FROM `user` u
                WHERE u.id IN (SELECT distinct bfa.user_id from business_flow_administrator bfa WHERE bfa.user_id IN (%s))
                """.formatted(joinedIds);
        return searchData(findUserNameByIdtSql, EntityNameVM.class);
    }

    public EntityNameVM findCrmUserNameById(Long id) {
        String findUserNameByIdtSql = """
                SELECT u.id, u.first_name, u.last_name, u.activated
                FROM `user` u
                WHERE u.id IN (SELECT distinct bfa.user_id from business_flow_administrator bfa WHERE bfa.user_id = %s)
                """.formatted(id);
        return searchFirstData(findUserNameByIdtSql, EntityNameVM.class);
    }

    private <T> T searchFirstData(String query, Class<T> clazz) {
        Query dataQ = entityManager.createNativeQuery(query, clazz);
        return (T) dataQ.getResultList().stream().findFirst().orElse(null);
    }

    private <T> List<T> searchData(String query, Class<T> clazz) {
        Query dataQ = entityManager.createNativeQuery(query, clazz);
        return dataQ.getResultList();
    }
}
