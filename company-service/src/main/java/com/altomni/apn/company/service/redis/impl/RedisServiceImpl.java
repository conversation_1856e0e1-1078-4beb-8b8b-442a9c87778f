package com.altomni.apn.company.service.redis.impl;

import cn.hutool.core.convert.Convert;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.dto.redis.ImagesInfoParser;
import com.altomni.apn.common.dto.redis.ParserRedisResponse;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.company.service.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.nio.charset.Charset;
import java.util.*;

@Slf4j
@Service
@RefreshScope
public class RedisServiceImpl implements RedisService {

    private static JedisPool pool;

    private static JedisPoolConfig jedisPoolConfig;

    private static final int maxIdle = 10;

    private static final long maxWaitMillis = 50 * 1000;

    private static final int maxActive = 100;

    private static final int timeout = 2000;

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private Integer redisPort;

    @Value("${spring.redis.database}")
    private Integer redisDb;

    static {
        try {
            jedisPoolConfig = new JedisPoolConfig();
            jedisPoolConfig.setMaxTotal(maxActive);
            jedisPoolConfig.setMaxWaitMillis(maxWaitMillis);
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setTestOnBorrow(true);
            jedisPoolConfig.setTestOnReturn(true);
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    private void close(Jedis jedis) {
        if (jedis != null) {
            try {
                jedis.close();
            } catch (Exception e) {
                log.error("close jedis connection error: {}", e.getMessage());
                try {
                    jedis.disconnect();
                } catch (Exception e1) {
                    log.error("disconnect jedis connection error: {}" , e1.getMessage());
                }
            }
        }
    }

    private synchronized Jedis getJedis() {
        if (pool == null) {
            pool = new JedisPool(jedisPoolConfig, redisHost, redisPort, timeout, null, redisDb);
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 1st error message: {}", e.getMessage());
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 2cnd error message: {}", e.getMessage());
        }
        return pool.getResource();
    }

    @Override
    public void saveCompanyId(Collection<Long> companyIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long comapnyId : companyIds) {
                    jedis.sadd(RedisConstants.DATA_KEY_SYNCES_COMPANY, comapnyId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void saveFailedCompanyIds(Collection<Long> companyIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long companyId : companyIds) {
                    jedis.sadd(RedisConstants.DATA_KEY_SYNCES_FAILED_COMPANY, companyId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void saveFailedCompanyClientNoteIds(Collection<Long> companyClientNoteIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long clientNoteId : companyClientNoteIds) {
                    jedis.sadd(RedisConstants.DATA_KEY_SYNCES_FAILED_COMPANY_CLIENT_NOTE, clientNoteId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void saveFailedCompanyProgressNoteIds(Collection<Long> companyProgressNoteIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long progressNoteId : companyProgressNoteIds) {
                    jedis.sadd(RedisConstants.DATA_KEY_SYNCES_FAILED_COMPANY_PROGRESS_NOTE, progressNoteId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public Set<String> getCompanyIds(long count) {
        Set<String> result = new HashSet<>();
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                if (jedis.exists(RedisConstants.DATA_KEY_SYNCES_COMPANY)){
                    result.addAll(jedis.spop(RedisConstants.DATA_KEY_SYNCES_COMPANY, count));
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    @Override
    public Long checkSize(String key) {
        Long size = 0L;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                size = jedis.scard(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return size;
    }
}
