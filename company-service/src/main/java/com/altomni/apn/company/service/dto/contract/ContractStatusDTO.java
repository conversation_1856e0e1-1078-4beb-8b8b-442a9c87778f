package com.altomni.apn.company.service.dto.contract;

import com.altomni.apn.company.domain.contract.Contract;
import com.altomni.apn.company.domain.enumeration.contract.ContractStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A DTO for the {@link Contract} entity.
 */
@Data
@ApiModel(description = "contract status for update client company")
public class ContractStatusDTO implements Serializable {

    @ApiModelProperty(value = "the id for contract.")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "the status for contract.")
    @NotNull
    private ContractStatus status;

}
