package com.altomni.apn.company.web.rest.business;

import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.company.AccountBusinessDTO;
import com.altomni.apn.common.dto.company.AccountBusinessJobDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.domain.business.AccountBusiness;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.service.business.AccountBusinessService;
import com.altomni.apn.company.vo.business.OwnerVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class AccountBusinessResource {

    private static final String ENTITY_NAME = "Company AccountBusiness";

    @Resource
    private AccountBusinessService accountBusinessService;

    /**
     * POST  /saleslead : Create a new company salesLead client contact.
     *
     * @param salesLeadDTO the salesLeadDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new salesLeadClientContactDTO, or with status 400 (Bad Request) if the salesLeadClientContact has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
//    @PostMapping("/sales-leads")
//    public ResponseEntity<SalesLeadVO> createSalesLead(@Valid @RequestBody CompanySalesLeadProspectDTO salesLeadDTO) throws URISyntaxException {
//        log.debug("[APN: Company AccountBusiness @{}] REST request to create AccountBusiness:{}", SecurityUtils.getUserId(), salesLeadDTO);
//        SalesLeadVO result = accountBusinessService.createSalesLead(salesLeadDTO);
//        return ResponseEntity.created(new URI("/api/v3/saleslead" + result.getId()))
//                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId() != null ? result.getId().toString() : ""))
//                .body(result);
//    }

    // TODO: 2024/4/18 move to CRM
//    @GetMapping("/sales-leads/{companyId}")
//    @NoRepeatSubmit
//    public ResponseEntity<List<SalesLeadVO>> getAllProspectSalesLeadsByCompany(@PathVariable Long companyId, @RequestParam(name = "upgraded", required = false) Boolean upgraded) {
//        log.info("[APN: Company @{}] REST request to get all prospect salesleads by company id:{}", SecurityUtils.getUserId(), companyId);
//        List<SalesLeadVO> salesLeadDetails = accountBusinessService.getAllProspectSalesLeadsByCompany(companyId, upgraded);
//        return ResponseEntity.ok().body(salesLeadDetails);
//    }

    @GetMapping("/sales-leads/{companyId}/am")
    public ResponseEntity<List<Long>> getAllAmByCompany(@PathVariable("companyId") Long companyId) {
        log.info("[APN: Company @{}] REST request to get all AM IDs by company id:{}", SecurityUtils.getUserId(), companyId);
        return ResponseEntity.ok(accountBusinessService.getAllAmByCompany(companyId));
    }

    @GetMapping("/sales-leads/company/{talentId}")
    public ResponseEntity<List<SalesLeadClientContact>> queryCompanyByTalent(@PathVariable("talentId") Long talentId) {
        log.info("[APN: Company @{}] REST request to get company id by talent id:{}", SecurityUtils.getUserId(), talentId);
        return ResponseEntity.ok(accountBusinessService.findClientContactByTalentId(talentId));
    }

    @GetMapping("/sales-leads/job/{jobId}/am")
    public ResponseEntity<List<Long>> getAllAmByJob(@PathVariable("jobId") Long jobId) {
        log.info("[APN: Company @{}] REST request to get all AM IDs by job id:{}", SecurityUtils.getUserId(), jobId);
        return ResponseEntity.ok(accountBusinessService.getAllAmByJob(jobId));
    }

    @GetMapping("/sales-leads/am-group-by-company")
    public ResponseEntity<Map<Long, JSONArray>> getAllAmGroupByCompany() {
        log.info("[APN: Company @{}] REST request to get all AM IDs group by company id", SecurityUtils.getUserId());
        return ResponseEntity.ok(accountBusinessService.getAmsGroupByCompany());
    }

    @GetMapping("/salesLead/am-group-by-company-format")
    public ResponseEntity<List<Map<String, Object>>> getAllAmGroupByCompanyFormat() {
        log.info("[APN: Company @{}] REST request to get all AM IDs group by company id", SecurityUtils.getUserId());
        return ResponseEntity.ok(accountBusinessService.getAmsGroupByCompanyFormat());
    }

    @GetMapping("/salesLead/owner-list")
    public ResponseEntity<List<OwnerVO>> getAllOwnerList() {
        log.info("[APN: Company @{}] REST request to get all owner list", SecurityUtils.getUserId());
        return ResponseEntity.ok(accountBusinessService.getAllOwnerList());
    }

    @PostMapping("/salesLead/getDetail")
    public ResponseEntity<String> getAccountBusinessById(@RequestBody AccountBusinessDTO accountBusinessDTO) {
        log.info("[APN: Company @{}] REST request to get account business by Id", SecurityUtils.getUserId());
        return ResponseEntity.ok(accountBusinessService.getAccountBusinessById(accountBusinessDTO));
    }

    @PostMapping("/salesLead/getAllByBusinessUnit")
    public ResponseEntity<List<AccountBusiness>> getAllByBusinessUnit(@RequestBody AccountBusinessJobDTO accountBusinessDTO) {
        log.info("[APN: Company @{}] REST request to get account business by Id", SecurityUtils.getUserId());
        return ResponseEntity.ok(accountBusinessService.queryBusinessByBusinessUnitAndCountryLocation(accountBusinessDTO.getBusinessUnit(),accountBusinessDTO.getCountryLocation(),accountBusinessDTO.getCompanyId()));
    }
}
