package com.altomni.apn.company.domain.business;

import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.vo.business.AccountBusinessVO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@ApiModel(description = "AccountBusiness")
@Entity
@Data
@Table(name = "account_business")
public class AccountBusinessMigrate extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "name")
    private String name;

    @Column(name = "business_progress")
    private Integer businessProgress;

    @Column(name = "company_id", nullable = false)
    private Long accountCompanyId;

    @Column(name = "lead_source")
    private Long leadSource;

    @Column(name = "estimated_deal_time", nullable = true)
    private Instant estimatedDealTime;

    @Transient
    private Instant cooperateExpireTime;

    @Column(name = "location")
    private String countryLocation;

    @Column(name = "business_unit")
    private String businessUnit;

    @Column(name = "comment")
    private String comment;
}