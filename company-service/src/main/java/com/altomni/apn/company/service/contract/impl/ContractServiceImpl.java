package com.altomni.apn.company.service.contract.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.service.enums.EnumCompanyServiceTypeService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.business.BusinessFlowAdministrator;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.contract.*;
import com.altomni.apn.company.repository.business.AccountBusinessAdministratorRepository;
import com.altomni.apn.company.repository.business.AccountBusinessRepository;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.contract.*;
import com.altomni.apn.company.service.contract.ContractService;
import com.altomni.apn.company.service.store.StoreService;
import com.altomni.apn.company.service.user.UserService;
import com.altomni.apn.company.vo.contract.ContractVO;
import com.altomni.apn.company.vo.contract.SignerVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.Instant;
import java.time.ZoneId;
import java.util.List;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ContractServiceImpl implements ContractService {

    private final Double CLIENT_ACCOUNT_PROGRESS = 1.20;

    @Resource
    private ContractRepository contractRepository;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private UserService userService;

    @Resource
    private StoreService storeService;

    @Resource
    private ContractSignerRepository contractSignerRepository;

    @Resource
    private ContractServiceTypeRepository contractServiceTypeRepository;

    @Resource
    private AccountBusinessRepository accountBusinessRepository;

    @Resource
    private EnumCompanyServiceTypeService enumCompanyServiceTypeService;

    @Resource
    private AccountBusinessAdministratorRepository accountBusinessAdministratorRepository;

    @Resource
    private ContractBusinessRelationRepository contractBusinessRelationRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    private final String CONTRACT_RELATION = "contractRelations";

    private final String CONTRACT = "contracts";

    private final String CONTRACT_SINGER = "contractSingers";

    private final String CONTRACT_SERVICE_TYPE = "contractServiceTypes";

    private Company checkTenantId(Long companyId) {
        Company company = companyRepository.findById(companyId).orElseThrow(() ->
            new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_CHECKTENANTID_COMPANYNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(companyId),companyApiPromptProperties.getCompanyService()))
        );
        if(!Objects.equals(company.getTenantId(), SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return company;
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public ContractVO save(ContractDTO contractDTO) {
//        Company company = checkTenantId(contractDTO.getCompanyId());
//        List<AccountBusiness> accountBusinessList = checkSalesLeads(company.getId(), contractDTO.getSalesLeadIds());
//
//        if (contractDTO.getS3Key() != null && contractDTO.getS3Key().contains("/")) {
//            String[] s3KeyArray = contractDTO.getS3Key().split("/");
//            contractDTO.setS3Key(s3KeyArray[s3KeyArray.length - 1]);
//        }
//
//        if(contractDTO.getId() != null) {
//            Optional<Contract> originalContractOptional = contractRepository.findById(contractDTO.getId());
//            if (originalContractOptional.isEmpty()) {
//                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_SAVE_CONTRACTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(contractDTO.getId()),companyApiPromptProperties.getCompanyService()));
//            }
//            checkCompanyPermission(company);
//            Contract originalContract = originalContractOptional.get();
//            if (!originalContract.getS3Key().equals(contractDTO.getS3Key())) {
//                contractDTO.setUploadDate(LocalDate.now(ZoneId.of("UTC")));
//            }
//            else {
//                contractDTO.setUploadDate(originalContract.getUploadDate());
//            }
//        } else {
//            contractDTO.setUploadDate(LocalDate.now(ZoneId.of("UTC")));
//        }
//
//        Contract contract = Contract.formContractDTO(contractDTO);
//        contract.setStatus(ContractStatus.VALID);
//        if (contractDTO.getServiceTypes() == null || CollUtil.isEmpty(contractDTO.getServiceTypes())) {
//            contract.setEnumCompanyServiceTypes(new HashSet<>(enumCompanyServiceTypeService.findAllByIds(Arrays.asList(GENERAL_STAFFING))));
//        } else {
//            contract.setEnumCompanyServiceTypes(new HashSet<>(enumCompanyServiceTypeService.findAllByIds(contractDTO.getServiceTypes())));
//            if (CollUtil.isNotEmpty(accountBusinessList)) {
//                accountBusinessList.forEach(o -> o.setEnumCompanyServiceTypes(new HashSet<>(enumCompanyServiceTypeService.findAllByIds(contractDTO.getServiceTypes()))));
//            }
//        }
//        List<UserBriefDTO> signers = userService.getAllByIdIn(contractDTO.getSigners().stream().map(SignerDTO::getSignerId).collect(Collectors.toList())).getBody();
//        if (signers == null || signers.size() != contractDTO.getSigners().size()) {
//            throw new CustomParameterizedException(ErrorConstants.ERR_SIGNER_NOT_FOUND);
//        }
//
//        if (!signers.stream().allMatch(signer -> signer.getTenantId() != null && Objects.equals(signer.getTenantId(), SecurityUtils.getTenantId()))) {
//            throw new CustomParameterizedException(ErrorConstants.ERR_SIGNER_INVALID);
//        }
//        if (contractDTO.getPreviousContractId() != null) {
//            // check if this contract is a renewal of other contract
//            Optional<Contract> prevContractOptional  = contractRepository.findById(contractDTO.getPreviousContractId());
//            if (prevContractOptional.isEmpty()) {
//                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_SAVE_PREVIOUSCONTRACTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(contractDTO.getId()),companyApiPromptProperties.getCompanyService()));
//            }
//            Contract prevContract = prevContractOptional.get();
//            if (!prevContract.getCompanyId().equals(contractDTO.getCompanyId())) {
//                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_SAVE_RENEWALCONTRACTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(contractDTO.getId()),companyApiPromptProperties.getCompanyService()));
//            }
//            prevContract.setStatus(ContractStatus.INVALID);
//            contractRepository.save(prevContract);
//            contract.setPreviousContractId(contractDTO.getPreviousContractId());
//        }
//        Contract updateContract = contractRepository.save(contract);
//        saveSalesLeadRelation(updateContract.getId(), contractDTO.getSalesLeadIds());
//        accountBusinessRepository.saveAll(accountBusinessList);
//        saveSigners(updateContract, signers);
//        return toVo(updateContract, signers, contractDTO.getSalesLeadIds());
//    }

//    private List<AccountBusiness> checkSalesLeads(Long companyId, List<Long> ids) {
//        if (CollUtil.isEmpty(ids)) {
//            return new ArrayList<>();
//        }
//
//        List<AccountBusiness> accountBusinessList = accountBusinessRepository.findAllById(ids);
//        if (ids.size() != accountBusinessList.size() || accountBusinessList.stream().anyMatch(o -> !o.getCompanyId().equals(companyId))) {
//            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_CHECKSALESLEADS_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
//        }
//
//        if (accountBusinessList.stream().anyMatch(o -> o.getAccountProgress().compareTo(BigDecimal.valueOf(CLIENT_ACCOUNT_PROGRESS)) < 0)) {
//            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_CHECKSALESLEADS_UPGRADED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
//        }
//        return accountBusinessList;
//    }
//
//    private void saveSalesLeadRelation(Long contractId, List<Long> ids) {
//        Set<Long> salesLeadIdSet = ids == null ? new HashSet<>() : new HashSet<>(ids);
//
//        List<ContractBusinessRelation> existRelationList = contractBusinessRelationRepository.findAllByContractId(contractId);
//
//        List<ContractBusinessRelation> deleteRelationList = existRelationList.stream().filter(o -> !salesLeadIdSet.contains(o.getSalesLeadId())).collect(Collectors.toList());
//        existRelationList.removeAll(deleteRelationList);
//
//        Set<Long> existSalesLeadIds = existRelationList.stream().map(ContractBusinessRelation::getSalesLeadId).collect(Collectors.toSet());
//
//        salesLeadIdSet.forEach(o -> {
//            if (!existSalesLeadIds.contains(o)) {
//                existRelationList.add(new ContractBusinessRelation(contractId, o));
//            }
//        });
//
//        contractBusinessRelationRepository.deleteAllByIdInBatch(deleteRelationList.stream().map(ContractBusinessRelation::getId).collect(Collectors.toList()));
//        contractBusinessRelationRepository.saveAllAndFlush(existRelationList);
//    }

    private List<Long> queryBusinessIds(Long contractId) {
        List<ContractBusinessRelation> existRelationList = contractBusinessRelationRepository.findAllByContractId(contractId);
        return existRelationList.stream().map(ContractBusinessRelation::getAccountBusinessId).collect(Collectors.toList());
    }

//    private void checkCompanyPermission(Company company) {
//        if (SecurityUtils.isSystemAdmin() || SecurityUtils.isAdmin()){
//            return;
//        }
//
//        Long creatorId = company.getPermissionUserId();
//        if (ObjectUtil.isEmpty(creatorId)) {
//            creatorId = Long.valueOf(company.getCreatedBy().split(",")[0]);
//        }
//
//        BusinessFlowAdministrator bdOnwer = accountBusinessAdministratorRepository.findOneByCompanyIdAndUserIdAndSalesLeadRoleType(company.getId(), SecurityUtils.getUserId(), SalesLeadRoleType.BUSINESS_DEVELOPMENT.toDbValue());
//        if (bdOnwer != null) {
//            return;
//        }
//
//        BusinessFlowAdministrator companyAm = accountBusinessAdministratorRepository.findOneByCompanyIdAndUserIdAndSalesLeadRoleType(company.getId(), SecurityUtils.getUserId(), SalesLeadRoleType.ACCOUNT_MANAGER.toDbValue());
//        if (companyAm != null) {
//            return;
//        }
//        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
//    }

//    private void saveSigners(Contract updateContract, List<UserBriefDTO> signers) {
//        contractSignerRepository.deleteAllByContractId(updateContract.getId());
//        contractSignerRepository.saveAll(signers.stream().map(o -> new ContractSigner(updateContract.getId(), o.getId())).collect(Collectors.toList()));
//    }

    @Override
    public List<ContractVO> saveContract(JSONObject jsonObject) {
        if (!jsonObject.containsKey(CONTRACT)) {
            return new ArrayList<>();
        }
        List<Contract> contractList = jsonObject.containsKey(CONTRACT) ? jsonObject.getJSONArray(CONTRACT).stream().map(o -> {
            JSONObject item = JSONUtil.parseObj(o);
            return JSONUtil.toBean(item, Contract.class);
        }).toList() : new ArrayList<>();
        List<ContractSigner> contractSignerList = jsonObject.containsKey(CONTRACT_SINGER) ? jsonObject.getJSONArray(CONTRACT_SINGER).toList(ContractSigner.class) : new ArrayList<>();
        List<ContractServiceType> contractServiceTypeList = jsonObject.containsKey(CONTRACT_SERVICE_TYPE) ? jsonObject.getJSONArray(CONTRACT_SERVICE_TYPE).toList(ContractServiceType.class) : new ArrayList<>();

        List<Long> contractIds = contractList.stream().map(Contract::getId).toList();
        Map<Long, ContractSigner> contractSignerMap = contractSignerList.stream().collect(Collectors.toMap(ContractSigner::getId, o -> o));
        Map<Long, ContractServiceType> contractServiceTypeMap = contractServiceTypeList.stream().collect(Collectors.toMap(ContractServiceType::getId, o -> o));

        List<ContractSigner> existedContractSignerList = contractSignerRepository.findAllByContractIdIn(contractIds);
        List<ContractServiceType> existedContractServiceTypeList = contractServiceTypeRepository.findAllByContractIdIn(contractIds);

        List<Long> deleteSingerIds = existedContractSignerList.stream().map(ContractSigner::getId).filter(o -> !contractSignerMap.containsKey(o)).toList();
        List<Long> deleteServiceTypeRelationIds = existedContractServiceTypeList.stream().map(ContractServiceType::getId).filter(o -> !contractServiceTypeMap.containsKey(o)).toList();

        existedContractSignerList.forEach(item -> {
            if (contractSignerMap.containsKey(item.getId())) {
                ServiceUtils.myCopyProperties(contractSignerMap.get(item.getId()), item);
            }
        });
        existedContractServiceTypeList.forEach(item -> {
            if (contractServiceTypeMap.containsKey(item.getId())) {
                ServiceUtils.myCopyProperties(contractServiceTypeMap.get(item.getId()), item);
            }
        });

        //创建 & 更新contract会以contract维度更新关联数据
        if (jsonObject.containsKey(CONTRACT_RELATION)) {
            List<ContractBusinessRelation> contractBusinessRelationList = jsonObject.getJSONArray(CONTRACT_RELATION).toList(ContractBusinessRelation.class);
            Map<Long, ContractBusinessRelation> contractBusinessRelationMap = contractBusinessRelationList.stream().collect(Collectors.toMap(ContractBusinessRelation::getId, o -> o));
            List<ContractBusinessRelation> existedContractBusinessRelationList = contractBusinessRelationRepository.findAllByContractIdIn(contractIds);
            List<Long> deleteContractBusinessRelationIds = existedContractBusinessRelationList.stream().map(ContractBusinessRelation::getId).filter(o -> !contractBusinessRelationMap.containsKey(o)).toList();
            existedContractBusinessRelationList.forEach(item -> {
                if (contractBusinessRelationMap.containsKey(item.getId())) {
                    ServiceUtils.myCopyProperties(contractBusinessRelationMap.get(item.getId()), item);
                }
            });
            contractBusinessRelationRepository.deleteAllByIdInBatch(deleteContractBusinessRelationIds);
            contractBusinessRelationRepository.saveAll(contractBusinessRelationList);
        }

        contractSignerRepository.deleteAllByIdInBatch(deleteSingerIds);
        contractServiceTypeRepository.deleteAllByIdInBatch(deleteServiceTypeRelationIds);
        contractRepository.saveAll(contractList);
        contractSignerRepository.saveAll(contractSignerList);
        contractServiceTypeRepository.saveAll(contractServiceTypeList);
        return contractList.stream().map(ContractVO::fromContract).toList();
    }

    @Override
    public ContractVO queryContract(Long contractId) {
        Contract contract = contractRepository.findById(contractId).orElseThrow(() -> new NotFoundException("The contract with id: " + contractId + " doesn't exist!"));
        checkTenantId(contract.getAccountCompanyId());
        return toVo(contract);
    }

    @Override
    public Page<Contract> searchContractByCompanyId(Long companyId, Pageable pageable) {
        checkTenantId(companyId);
        return contractRepository.findAllByAccountCompanyId(companyId, pageable);
    }

    @Override
    public List<ContractVO> toVo(List<Contract> contractList) {
        if (CollUtil.isEmpty(contractList)) {
            return new ArrayList<>();
        }
        List<Long> contractIds = contractList.stream().map(Contract::getId).toList();
        List<ContractServiceType> contractServiceTypeList = contractServiceTypeRepository.findAllByContractIdIn(contractIds);
        Map<Long, List<Integer>> contractServiceTypeMap = contractServiceTypeList.stream().collect(Collectors.groupingBy(ContractServiceType::getContractId, Collectors.mapping(ContractServiceType::getServiceTypeId, Collectors.toList())));
        return contractList.stream().map(o -> {
            ContractVO contractVO = ContractVO.fromContract(o);
            contractVO.setServiceTypes(contractServiceTypeMap.get(o.getId()));
            return contractVO;
        }).toList();
    }


//    @Override
//    public ContractVO updateContractStatus(ContractStatusDTO contractStatusDTO) {
//        Contract contract = contractRepository.findById(contractStatusDTO.getId()).orElseThrow(() -> new NotFoundException("The contract with id: " + contractStatusDTO.getId() + " doesn't exist!"));
//        checkTenantId(contract.getCompanyId());
//        contract.setStatus(contractStatusDTO.getStatus());
//        return toVo(contractRepository.save(contract));
//    }


    private void checkDetailPermission(Long companyId) {

        if (SecurityUtils.isAdmin()) {
            return;
        }

        BusinessFlowAdministrator bdOnwer = accountBusinessAdministratorRepository.findOneByCompanyIdAndUserIdAndSalesLeadRoleType(companyId, SecurityUtils.getUserId(), SalesLeadRoleType.BUSINESS_DEVELOPMENT.toDbValue());
        if (bdOnwer != null) {
            return;
        }

        BusinessFlowAdministrator companyAm = accountBusinessAdministratorRepository.findOneByCompanyIdAndUserIdAndSalesLeadRoleType(companyId, SecurityUtils.getUserId(), SalesLeadRoleType.ACCOUNT_MANAGER.toDbValue());
        if (companyAm != null) {
            return;
        }
        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
    }

    public void addTextWatermarkToImage(byte[] imageData, String watermarkText, Color watermarkColor,
                                                 Font watermarkFont, float alpha, OutputStream outputStream) {
        try {
            InputStream inputStream = new ByteArrayInputStream(imageData);
            BufferedImage image = ImageIO.read(inputStream);

            Graphics2D g2d = image.createGraphics();

            g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha));

            g2d.setColor(watermarkColor);

            // The size of the watermark is appropriate, and the font size is calculated according to the height of the image
            int watermarkSize = image.getWidth() / 30;
            g2d.setFont(watermarkFont.deriveFont(Font.ITALIC, watermarkSize));

            int imageWidth = image.getWidth();
            int imageHeight = image.getHeight();

            int watermarkWidth = watermarkText.length() * watermarkSize;

            // The number of watermarks added to each line
            int watermarksPerRow = 2;
            // The number of watermarks added to each column
            int watermarksPerColumn = 3;

            // Watermark interval, calculated according to the image size
            int horizontalInterval = (imageWidth - watermarkWidth * watermarksPerRow) / (watermarksPerRow + 1);
            int verticalInterval = (imageHeight - watermarkSize * watermarksPerColumn) / (watermarksPerColumn + 1);

            for (int row = 0; row < watermarksPerColumn; row++) {
                int y = (row + 1) * verticalInterval + row * watermarkSize;

                for (int col = 0; col < watermarksPerRow; col++) {
                    int x = (col + 1) * horizontalInterval + col * watermarkWidth;

                    // add watermark
                    g2d.drawString(watermarkText, x, y);
                }
            }

            g2d.dispose();

            ImageIO.write(image, "png", outputStream);

        } catch (IOException e) {
            e.printStackTrace();
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_ADDTEXTWATERMARKTOIMAGE_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

    }

//    private ContractVO toVo(Contract contract, List<UserBriefDTO> signers, List<Long> salesLeadIds) {
//        ContractVO contractVO = ContractVO.fromContract(contract);
//        contractVO.setSalesLeadIds(salesLeadIds);
//        if (signers != null) {
//            contractVO.setSigners(signers.stream().map(o -> new SignerVO(o.getId(), CommonUtils.formatFullName(o.getFirstName(), o.getLastName()))).collect(Collectors.toSet()));
//        }
//        return contractVO;
//    }

    private ContractVO toVo(Contract contract) {
        ContractVO contractVO = ContractVO.fromContract(contract);
        contractVO.setAccountBusinessIds(queryBusinessIds(contract.getId()));
        List<Long> signerIdList = contractSignerRepository.findAllByContractId(contract.getId()).stream().map(ContractSigner::getSignerId).distinct().collect(Collectors.toList());
        List<UserBriefDTO> signers = userService.getAllBriefUsersByIds(signerIdList).getBody();
        if (signers != null) {
            contractVO.setSigners(signers.stream().map(o -> new SignerVO(o.getId(), CommonUtils.formatFullName(o.getFirstName(), o.getLastName()))).collect(Collectors.toSet()));
        }
        List<ContractServiceType> contractServiceTypeList = contractServiceTypeRepository.findAllByContractId(contract.getId());
        contractVO.setServiceTypes(contractServiceTypeList.stream().map(ContractServiceType::getServiceTypeId).toList());
        return contractVO;
    }

}
