package com.altomni.apn.company.service.company.projectTeam;

import com.altomni.apn.company.domain.company.projectTeam.CompanyProjectTeam;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamDTO;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamSearchDTO;
import com.altomni.apn.company.vo.projectTeam.CompanyProjectTeamVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface CompanyProjectTeamService {

    /**
     * Create a CompanyProjectTeam.
     *
     * @param companyProjectTeamDTO the entity to create
     * @return the persisted entity
     */
    CompanyProjectTeamVO create(CompanyProjectTeamDTO companyProjectTeamDTO);

    /**
     * Update a CompanyProjectTeam.
     *
     * @param companyProjectTeamDTO the entity to update
     * @return the persisted entity
     */

    CompanyProjectTeamVO update(CompanyProjectTeamDTO companyProjectTeamDTO);

    /**
     * Get all the CompanyProjectTeams.
     *
     * @return the list of entities
     */
    List<CompanyProjectTeamVO> getAllByTenantId();


    /**
     * Get the "id" CompanyProjectTeam.
     *
     * @param id the id of the entity
     * @return the entity
     */
    CompanyProjectTeamVO findOne(Long id);

    /**
     * Delete the "id" CompanyProjectTeam.
     *
     * @param id the id of the entity
     */
    void delete(Long id);

    /**
     * Get all the CompanyProjectTeams.
     *
     * @return the list of entities
     */
    Page<CompanyProjectTeam> searchProjectTeamByCompanyId(CompanyProjectTeamSearchDTO companyProjectTeamSearchDTO, Pageable pageable);

    /**
     * translate to vo
     * @param projectTeamList
     * @return
     */
    List<CompanyProjectTeamVO> toVo(List<CompanyProjectTeam> projectTeamList);
    
}
