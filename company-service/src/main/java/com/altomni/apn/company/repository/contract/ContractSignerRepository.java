package com.altomni.apn.company.repository.contract;

import com.altomni.apn.company.domain.contract.ContractSigner;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ContractSignerRepository extends JpaRepository<ContractSigner, Long> {

    List<ContractSigner> findAllByContractId(Long contractId);

    void deleteAllByContractId(Long contractId);

    List<ContractSigner> findAllByContractIdIn(List<Long> contractIds);

}
