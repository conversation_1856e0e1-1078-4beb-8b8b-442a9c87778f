package com.altomni.apn.company.service.dto.contact;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.enumeration.ContactCategoryType;
import com.altomni.apn.common.domain.enumeration.ContactCategoryTypeConverter;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;
import java.util.*;

/**
*
* <AUTHOR>
* date:2023-04-21
*/
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel(description = "dto for company contact")
public class CompanyContactDTO implements Serializable {

    @ApiModelProperty(value = "id for contact")
    private Long id;

    @ApiModelProperty(value = "firstName for contact")
    @NotEmpty
    private String firstName;

    @ApiModelProperty(value = "lastName for contact")
    @NotEmpty
    private String lastName;

    @ApiModelProperty(value = "status for contact")
    private Boolean active;

    @ApiModelProperty(value = "lastContactDate for contact")
    @NotNull
    private Instant lastContactDate;

    @ApiModelProperty(value = "type for contact")
    @NotNull
    @Convert(converter= ContactCategoryTypeConverter.class)
    private ContactCategoryType contactCategory;

    @ApiModelProperty(value = "title for the contact")
    @NotEmpty
    private String title;

    @ApiModelProperty(value = "department for the contact")
    private String department;

    @ApiModelProperty(value = "contact list")
    @NotEmpty
    @UniqueElements
    private List<TalentContactDTO> contacts;

    @ApiModelProperty(value = "note for the contact")
    private String note;

    @ApiModelProperty(value = "zipcode for the contact, custom version exclusive and required")
    private String zipcode;

    @ApiModelProperty(value = "businessUnit for the contact, custom version exclusive")
    private String businessUnit;

    @ApiModelProperty(value = "businessGroup for the contact, custom version exclusive")
    private String businessGroup;

    @ApiModelProperty(value = "linkedinProfile for the contact, custom version exclusive")
    private List<String> linkedinProfile;

    @ApiModelProperty(value = "esId for the contact.")
    private String esId;

    @ApiModelProperty(value = "id for the company location")
    private Long companyLocationId;

    @ApiModelProperty(value = "linkedinProfile for the contact, custom version exclusive")
    @UniqueElements
    private List<Long> tags;

    @JsonIgnore
    private Long talentId;

    @JsonIgnore
    private Long salesLeadId;

    public static Set<String> additionalInfoSkipProperties = new HashSet<>(Arrays.asList("id", "firstName", "lastName", "active", "lastContactDate", "contactCategory",
            "title", "email", "phone", "wechat", "zipcode", "linkedinProfile", "esId", "companyLocationId", "tags"));

}