package com.altomni.apn.company.service.rabbitmq.listener;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.audit.AuditUserHolder;
import com.altomni.apn.common.domain.transactionrecord.CommonMqConsumeFailedRecord;
import com.altomni.apn.common.enumeration.enums.MqTranRecordBusTypeEnums;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.repository.mqfailedrecord.CompanyMqTransactionFailedRecordRepository;
import com.altomni.apn.company.service.business.SalesLeadClientContactService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class UpdateTalentCompanyMqListener {

    @Autowired
    CompanyMqTransactionFailedRecordRepository companyMqTransactionFailedRecordRepository;

    @Autowired
    CommonRedisService commonRedisService;


    @Autowired
    private SalesLeadClientContactService salesLeadClientContactService;

    private UpdateTalentCompanyMqListener updateTalentCompanyMqListener;


    @Value("${application.notification.lark.mq.webhookKey}")
    private String LARK_WEBHOOK_KEY;

    @Value("${application.notification.lark.mq.webhookUrl}")
    private String LARK_WEBHOOK_URL;

    @PostConstruct
    public void init() {
        updateTalentCompanyMqListener = this;
        updateTalentCompanyMqListener.companyMqTransactionFailedRecordRepository = this.companyMqTransactionFailedRecordRepository;
        updateTalentCompanyMqListener.commonRedisService = this.commonRedisService;
        updateTalentCompanyMqListener.salesLeadClientContactService = this.salesLeadClientContactService;
    }

    private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String COMPANY_CONSUMER_PREFIX = "UPDATE_TALENT_COMPANY_TX_";

    @RabbitListener(bindings = {@QueueBinding(
            value = @Queue(value = "${application.update-talent-tx.company-queue}",durable = "true"),
            exchange = @Exchange(value = "${application.update-talent-tx.exchange}",durable = "true",type = ExchangeTypes.DIRECT),
            key = "${application.update-talent-tx.routing-company-key}")},containerFactory = "companyConsumerFactory")
    @RabbitHandler
    public void processHandler(Channel channel, Message message) throws IOException {
        log.info("update talent company tx ,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        try {

            JSONObject param = JSON.parseObject(json);
            if (!param.containsKey(AUTHORIZATION_HEADER)) {
                log.error("update talent company tx, {} , token is null param:{}", MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.getDesc(), param);
                channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            String token = param.getString(AUTHORIZATION_HEADER);
            param.remove(AUTHORIZATION_HEADER);

            Long talentId = param.getLong("talentId");
            Long companyLocationId = param.getLong("companyLocationId");
            if (!checkExits(talentId, param)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }

            if (param.containsKey(SecurityUtils.OPERATOR_UID)) {
                AuditUserHolder.set(param.getOrDefault(SecurityUtils.OPERATOR_UID, "").toString());
            }
            LoginUtil.simulateLoginWithClient();

            try {
                //do something
                updateTalentCompanyMqListener.salesLeadClientContactService.updateContactLocationIdByTalentId(talentId, companyLocationId);
                saveFailedRecord(talentId, param, 1);
                String key = COMPANY_CONSUMER_PREFIX.concat(talentId.toString());
                updateTalentCompanyMqListener.commonRedisService.delete(key);
                log.info("update talent company tx, {},rabbit mq consume success", MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.getDesc());
            } catch (Exception e) {
                saveFailedRecord(talentId, param, 0);
                NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, String.format("company-service-> 业务类型：%s ,消费数据失败，请人工处理。", MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.getDesc()));
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("update talent company tx, {},message is:{}, error message：{}", MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.getDesc(), json, e.getMessage());
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
        } finally {
            AuditUserHolder.clear();
        }
    }


    /**
     * 幂等校验
     *
     * @param talentId
     * @param param
     * @return
     */
    private boolean checkExits(Long talentId, JSONObject param) {
        String key = COMPANY_CONSUMER_PREFIX.concat(talentId.toString());
        //redis verify exists
        String value = updateTalentCompanyMqListener.commonRedisService.get(key);
        if (StringUtils.isNotBlank(value)) {
            log.error("update talent company tx, {}, redis search id exists,param:{}", MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.getDesc(), param);
            return false;
        }

        //数据库是否记录
//        CommonMqConsumeFailedRecord commonMqConsumeFailedRecord = updateTalentCompanyMqListener.companyMqTransactionFailedRecordRepository.findByBusIdAndBusTypeAndReceiceStatus(BigInteger.valueOf(talentId), MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.toDbValue(),1);
//        if (null != commonMqConsumeFailedRecord) {
//            log.error("update talent company tx, {}, id exists,param:{}", MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.getDesc(), param);
//            return false;
//        }
        updateTalentCompanyMqListener.commonRedisService.set(key, "1", 18000);
        return true;
    }

    /**
     * 保存消费记录
     *
     * @param talentId
     * @param param
     * @param status
     */
    private void saveFailedRecord(Long talentId, JSONObject param, Integer status) {
        CommonMqConsumeFailedRecord failedRecord = new CommonMqConsumeFailedRecord();
        failedRecord.setConsumeCount(1);
        failedRecord.setBusId(BigInteger.valueOf(talentId));
        failedRecord.setReceiveMessage(param.toJSONString());
        failedRecord.setBusType(MqTranRecordBusTypeEnums.UPDATE_TALENT_COMPANY.toDbValue());
        failedRecord.setReceiceStatus(status);
        updateTalentCompanyMqListener.companyMqTransactionFailedRecordRepository.save(failedRecord);
    }
}