package com.altomni.apn.company.repository.company;

import com.altomni.apn.company.domain.company.CompanyAdditionalInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface CompanyAddtionalInfoRepository extends JpaRepository<CompanyAdditionalInfo, Long> {

    CompanyAdditionalInfo findByAccountCompanyId(Long companyId);

    List<CompanyAdditionalInfo> findAllByAccountCompanyIdIn(Collection<Long> companyIds);

}