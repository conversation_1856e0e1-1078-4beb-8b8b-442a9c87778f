package com.altomni.apn.company.service.company.projectTeam.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.job.JobPermission;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.projectTeam.CompanyProjectTeam;
import com.altomni.apn.company.domain.company.projectTeam.CompanyProjectTeamUser;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.company.projectTeam.CompanyProjectTeamRepository;
import com.altomni.apn.company.repository.company.projectTeam.CompanyProjectTeamUserRepository;
import com.altomni.apn.company.service.company.projectTeam.CompanyProjectTeamService;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamDTO;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamSearchDTO;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamUserDTO;
import com.altomni.apn.company.service.user.UserService;
import com.altomni.apn.company.vo.projectTeam.CompanyProjectTeamUserVO;
import com.altomni.apn.company.vo.projectTeam.CompanyProjectTeamVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
*
* <AUTHOR>
* date:2023-04-18
*/
@Service
@Slf4j
public class CompanyProjectTeamServiceImpl implements CompanyProjectTeamService {

    @Resource
    private CompanyProjectTeamRepository companyProjectTeamRepository;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private CompanyProjectTeamUserRepository companyProjectTeamUserRepository;

    @Resource
    private UserService userService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    private void validate(Long companyId) {
        Company company = companyRepository.findById(companyId).orElseThrow(() -> new NotFoundException("The client's company does not exist."));
        if (!company.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.PROJECT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
    }

    private CompanyProjectTeamVO checkTeamPermission(Long teamId) {
        CompanyProjectTeam companyProjectTeam = companyProjectTeamRepository.findById(teamId).orElseThrow(() -> new NotFoundException("The project team does not exist."));
        if (!companyProjectTeam.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.PROJECT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return toVo(companyProjectTeam);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompanyProjectTeamVO create(CompanyProjectTeamDTO companyProjectTeamDTO) {
        if (companyProjectTeamDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.PROJECT_CREATE_IDEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        Integer count = companyProjectTeamRepository.countByCompanyIdAndName(companyProjectTeamDTO.getCompanyId(), companyProjectTeamDTO.getName());
        if (count != 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.PROJECT_CREATE_DUPLICATETEAM.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return toVo(createAndSave(companyProjectTeamDTO));
    }

    public CompanyProjectTeam createAndSave(CompanyProjectTeamDTO companyProjectTeamDTO) {
        log.info("Request to save CompanyProjectTeam : {}", companyProjectTeamDTO);
        validate(companyProjectTeamDTO.getCompanyId());
        CompanyProjectTeam projectTeam = CompanyProjectTeam.fromCompanyProjectTeamDTO(companyProjectTeamDTO);
        projectTeam.setTenantId(SecurityUtils.getTenantId());
        CompanyProjectTeam result = companyProjectTeamRepository.save(projectTeam);
        List<CompanyProjectTeamUserDTO> userList = companyProjectTeamDTO.getCompanyProjectTeamUsers();
        List<CompanyProjectTeamUser> projectTeamUsers = new LinkedList<>();
        if(!userList.isEmpty()) {
            for(CompanyProjectTeamUserDTO projectTeamUserDTO : userList) {
                Set<String> permissions = projectTeamUserDTO.getPermissions();
                if(!permissions.isEmpty()) {
                    for(String per: projectTeamUserDTO.getPermissions()) {
                        CompanyProjectTeamUser projectTeamUser  = new CompanyProjectTeamUser();
                        projectTeamUser.setUserId(projectTeamUserDTO.getUserId());
                        projectTeamUser.setTeamId(result.getId());
                        projectTeamUser.setPermission(JobPermission.getPermission(per));
                        projectTeamUsers.add(projectTeamUser);
                    }
                    continue;
                }
                CompanyProjectTeamUser projectTeamUser  = new CompanyProjectTeamUser();
                projectTeamUser.setTeamId(result.getId());
                projectTeamUser.setUserId(projectTeamUserDTO.getUserId());
                projectTeamUsers.add(projectTeamUser);

            }
            if(companyProjectTeamDTO.getId() != null) {
                companyProjectTeamUserRepository.deleteAllByTeamId(result.getId());
            }
            companyProjectTeamUserRepository.saveAll(projectTeamUsers);
        }
        return result;
    }

    private CompanyProjectTeamVO toVo(CompanyProjectTeam companyProjectTeam) {
        List<CompanyProjectTeamUser> teamUserList = companyProjectTeamUserRepository.findAllByTeamId(companyProjectTeam.getId());
        Map<Long, List<CompanyProjectTeamUser>> teamUserRoleMap = teamUserList.stream().collect(Collectors.groupingBy(CompanyProjectTeamUser::getUserId));

        List<Long> userIdList = Stream.concat(teamUserList.stream().map(CompanyProjectTeamUser::getUserId), Stream.of(companyProjectTeam.getLeaderUserId()))
                .distinct()
                .collect(Collectors.toList());

        List<UserBriefDTO> userBriefDTOList = userService.getAllBriefUsersByIds(userIdList).getBody();
        final Map<Long, String> userNameMap = userBriefDTOList == null ? new HashMap<>() : userBriefDTOList.stream().collect(Collectors.toMap(UserBriefDTO::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));

        CompanyProjectTeamVO companyProjectTeamVO = new CompanyProjectTeamVO();
        ServiceUtils.myCopyProperties(companyProjectTeam, companyProjectTeamVO);
        List<CompanyProjectTeamUserVO> companyProjectTeamUserVOList = new ArrayList<>();
        teamUserList.stream().map(CompanyProjectTeamUser::getUserId).distinct().collect(Collectors.toList()).forEach(o -> {
            CompanyProjectTeamUserVO companyProjectTeamUserVO = new CompanyProjectTeamUserVO();
            companyProjectTeamUserVO.setUserId(o);
            companyProjectTeamUserVO.setUserName(userNameMap.getOrDefault(o, null));
            if (teamUserRoleMap.containsKey(o)) {
                Set<String> permissionList = new HashSet<>();
                teamUserRoleMap.get(o).forEach(item -> permissionList.addAll(JobPermission.parseJobPermission(item.getPermission())));
                companyProjectTeamUserVO.setPermissions(permissionList);
            }
            companyProjectTeamUserVOList.add(companyProjectTeamUserVO);
        });
        companyProjectTeamVO.setCompanyProjectTeamUsers(companyProjectTeamUserVOList);
        companyProjectTeamVO.setLeaderName(userNameMap.getOrDefault(companyProjectTeamVO.getLeaderUserId(), null));
        return companyProjectTeamVO;
    }

    @Override
    public List<CompanyProjectTeamVO> toVo(List<CompanyProjectTeam> companyProjectTeamList) {
        List<CompanyProjectTeamVO> resultList = new ArrayList<>();

        List<Long> teamIdList = companyProjectTeamList.stream().map(CompanyProjectTeam::getId).collect(Collectors.toList());
        List<CompanyProjectTeamUser> allTeamUserList = companyProjectTeamUserRepository.findAllByTeamIdIn(teamIdList);
        List<Long> userIdList = Stream.concat(
                        allTeamUserList.stream().map(CompanyProjectTeamUser::getUserId),
                        companyProjectTeamList.stream().map(CompanyProjectTeam::getLeaderUserId)).distinct().collect(Collectors.toList());

        List<UserBriefDTO> userBriefDTOList = userService.getAllBriefUsersByIds(userIdList).getBody();
        final Map<Long, String> userNameMap = userBriefDTOList == null ? new HashMap<>() : userBriefDTOList.stream().collect(Collectors.toMap(UserBriefDTO::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));

        companyProjectTeamList.forEach(team -> {
            CompanyProjectTeamVO companyProjectTeamVO = new CompanyProjectTeamVO();
            ServiceUtils.myCopyProperties(team, companyProjectTeamVO);
            List<CompanyProjectTeamUserVO> companyProjectTeamUserVOList = new ArrayList<>();
            List<CompanyProjectTeamUser> teamUserList = allTeamUserList.stream().filter(o -> o.getTeamId().equals(team.getId())).collect(Collectors.toList());
            Map<Long, List<CompanyProjectTeamUser>> teamUserRoleMap = teamUserList.stream().collect(Collectors.groupingBy(CompanyProjectTeamUser::getUserId));
            teamUserList.stream().map(CompanyProjectTeamUser::getUserId).distinct().filter(userNameMap::containsKey).collect(Collectors.toList()).forEach(o -> {
                CompanyProjectTeamUserVO companyProjectTeamUserVO = new CompanyProjectTeamUserVO();
                companyProjectTeamUserVO.setUserId(o);
                companyProjectTeamUserVO.setUserName(userNameMap.getOrDefault(o, null));
                if (teamUserRoleMap.containsKey(o)) {
                    Set<String> permissionList = new HashSet<>();
                    teamUserRoleMap.get(o).forEach(item -> permissionList.addAll(JobPermission.parseJobPermission(item.getPermission())));
                    companyProjectTeamUserVO.setPermissions(permissionList);
                }
                companyProjectTeamUserVOList.add(companyProjectTeamUserVO);
            });
            companyProjectTeamVO.setCompanyProjectTeamUsers(companyProjectTeamUserVOList);
            companyProjectTeamVO.setLeaderName(userNameMap.getOrDefault(companyProjectTeamVO.getLeaderUserId(), null));
            resultList.add(companyProjectTeamVO);
        });

        return resultList;
    }

    /**
     * Update a projectTeam.
     *
     * @param companyProjectTeamDTO the entity to update
     * @return the persisted entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompanyProjectTeamVO update(CompanyProjectTeamDTO companyProjectTeamDTO) {
        checkTeamPermission(companyProjectTeamDTO.getId());
        Integer count = companyProjectTeamRepository.countByCompanyIdAndNameAndIdNot(companyProjectTeamDTO.getCompanyId(), companyProjectTeamDTO.getName(), companyProjectTeamDTO.getId());
        if (count != 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.PROJECT_CREATE_DUPLICATETEAM.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return toVo(createAndSave(companyProjectTeamDTO));
    }

    /**
     * Get one projectTeam by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    public CompanyProjectTeamVO findOne(Long id) {
        log.info("Request to get ProjectTeam : {}", id);
        return checkTeamPermission(id);
    }

    /**
     * Delete the projectTeam by id.
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        log.debug("Request to delete ProjectTeam : {}", id);
        checkTeamPermission(id);
        companyProjectTeamUserRepository.deleteAllByTeamId(id);
        companyProjectTeamRepository.deleteById(id);
    }


    /**
     * Get all the projectTeams.
     *
     * @return the list of entities
     */
    @Override
    public List<CompanyProjectTeamVO> getAllByTenantId() {
        List<CompanyProjectTeam> teamList = companyProjectTeamRepository.findAllByTenantId(SecurityUtils.getTenantId());
        if (CollectionUtils.isNotEmpty(teamList)) {
            return toVo(teamList);
        }
        return Collections.emptyList();
    }

    @Override
    public Page<CompanyProjectTeam> searchProjectTeamByCompanyId(CompanyProjectTeamSearchDTO companyProjectTeamSearchDTO, Pageable pageable) {
        validate(companyProjectTeamSearchDTO.getCompanyId());
        if (ObjectUtil.isNotEmpty(companyProjectTeamSearchDTO.getName())) {
            return companyProjectTeamRepository.findAllByCompanyIdAndNameLike(companyProjectTeamSearchDTO.getCompanyId(), String.format("%%%s%%", companyProjectTeamSearchDTO.getName()), pageable);
        }
        return companyProjectTeamRepository.findAllByCompanyId(companyProjectTeamSearchDTO.getCompanyId(), pageable);
    }

}
