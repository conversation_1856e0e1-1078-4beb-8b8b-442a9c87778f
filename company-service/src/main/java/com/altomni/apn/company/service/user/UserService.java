package com.altomni.apn.company.service.user;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamTreeDTO;
import com.altomni.apn.user.service.dto.user.CreditTransactionDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@Component
@FeignClient(value = "user-service")
public interface UserService {

    @PostMapping("/user/api/v3/users/get-all-by-uid-in")
    ResponseEntity<List<UserBriefDTO>> getAllByUidIn(@RequestBody List<String> uids);

    @PostMapping("/user/api/v3/users/all-brief-by-ids")
    ResponseEntity<List<UserBriefDTO>> getAllByIdIn(@RequestBody List<Long> ids);

    @PostMapping("/user/api/v3/users/all-brief-by-ids/including-inactive")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsersByIds(@RequestBody List<Long> ids);

    @GetMapping("/user/api/v3/users/get-user-ids/team/{teamId}")
    ResponseEntity<Set<Long>> findAllUserIdByTeamId(@PathVariable("teamId") Long teamId);

    @GetMapping("/user/api/v3/permissions/teams/tree")
    ResponseEntity<List<PermissionTeamTreeDTO>> getTeamsWithLeaders();

    @PutMapping("/user/api/v3/credit-transactions/commonPool")
    ResponseEntity<CreditTransactionDTO> updateCreditTalentIdForCommonPool(@RequestBody CreditTransactionDTO creditTransactionDTO);


    @PostMapping("/user/api/v3/users/same-team")
    ResponseEntity<Set<Long>> findUserIdsInSameTeam(@RequestBody List<Long> teamIds);
}
