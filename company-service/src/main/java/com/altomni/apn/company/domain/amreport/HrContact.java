package com.altomni.apn.company.domain.amreport;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class HrContact implements Serializable {

    @ExcelIgnore
    private Long hrContactId;

    @ColumnWidth(30)
    @ExcelProperty(value = "Hr Contact",index = 0)
    private String hrContact;

    @Id
    @ExcelIgnore
    private Long jobId;

    public HrContact(String hrContact) {
        this.hrContact = hrContact;
    }
}
