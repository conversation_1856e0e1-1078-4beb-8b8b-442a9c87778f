package com.altomni.apn.company.service.dto.note;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@ApiModel(description = "note for client company to update")
public class CompanyClientNoteUpdateDTO implements Serializable {

    @ApiModelProperty(value = "the clientContactId id for note.")
    @NotEmpty
    public List<Long> clientContactIds;

    @ApiModelProperty(value = "the contactDate for note.")
    @NotNull
    public Instant contactDate;

    @ApiModelProperty(value = "the text for note.")
    @NotEmpty
    public String note;

}
