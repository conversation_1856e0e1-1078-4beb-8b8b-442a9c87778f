package com.altomni.apn.company.domain.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractType enumeration.
 */
public enum FolderPermission implements ConvertedEnum<Integer> {
    VIEW(10),
    EDIT(20),

    //search folder used
    FOLDER_DELETE(100),
    NO_PERMISSION(200);
    private final int dbValue;

    FolderPermission(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<FolderPermission, Integer> resolver =
        new ReverseEnumResolver<>(FolderPermission.class, FolderPermission::toDbValue);

    public static FolderPermission fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
