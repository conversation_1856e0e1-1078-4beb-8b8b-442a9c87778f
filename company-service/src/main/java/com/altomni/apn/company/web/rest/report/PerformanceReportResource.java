package com.altomni.apn.company.web.rest.report;

import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.service.dto.report.PerformanceReportSearchDTO;
import com.altomni.apn.company.service.report.PerformanceReportService;
import com.altomni.apn.company.vo.report.PerformanceReportVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * performance report controller
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/api/v3/performance")
public class PerformanceReportResource {

    @Resource
    private PerformanceReportService performanceReportService;

    @PostMapping("/reports")
    public ResponseEntity<PerformanceReportVO> search(@Valid @RequestBody PerformanceReportSearchDTO condition) {
        log.info("[APN: PerformanceReportResource @{}] REST request to search performance reports: {}", SecurityUtils.getUserId());
        PerformanceReportVO result = performanceReportService.findPerformanceReport(condition);
        result.getJobData().forEach(data -> {
            if (data.getConfidentialTalentViewAble() != null && !data.getConfidentialTalentViewAble()) {
                data.encrypt();
            }
        });
        return ResponseEntity.ok(result);
    }

    @PostMapping("/reports/download")
    public void get(@Valid @RequestBody PerformanceReportSearchDTO condition, HttpServletResponse response)
    {
//        log.info("[APN: PerformanceReportResource @{}] REST request to download performance reports : {}", SecurityUtils.getUserId(), condition);
        performanceReportService.downloadReport(condition, response);
    }

}
