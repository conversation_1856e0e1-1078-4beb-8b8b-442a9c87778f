package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.base.Objects;
import lombok.Data;

//@Entity
@Data
//关系可以建立开发中客户和正式客户关系，因此直接查询crm，暂时不同步到apn
//@Table(name = "company_relationships")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CompanyRelationships extends AbstractAuditingEntity {
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

//    @Column(name = "company_id")
    private Long companyId;

//    @Column(name = "parent_company_id")
    private Long parentCompanyId;

//    @Column(name = "type")
//    @Convert(converter = CompanyRelationConverter.class)
    private CompanyRelation type;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        CompanyRelationships that = (CompanyRelationships) o;
        return Objects.equal(id, that.id) && Objects.equal(companyId, that.companyId) && Objects.equal(parentCompanyId, that.parentCompanyId) && type == that.type;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(super.hashCode(), id, companyId, parentCompanyId, type);
    }
}
