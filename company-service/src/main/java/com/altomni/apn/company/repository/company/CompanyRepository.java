package com.altomni.apn.company.repository.company;

import com.alibaba.nacos.shaded.com.google.common.collect.FluentIterable;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.CompanyMigrate;
import com.altomni.apn.company.domain.enumeration.contract.ContractStatus;
import com.altomni.apn.company.domain.vo.CompanyVo;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.company.web.rest.vm.job.JobIdCompanyNameVM;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Collection;
import java.util.List;

@Repository
public interface CompanyRepository extends JpaRepository<Company, Long>, JpaSpecificationExecutor<Company> {

    List<Company> findAllByIdIn(Collection<Long> ids);

//
//    List<Company> findAllByTypeOrType(CompanyType type1, CompanyType type2);
//
//    List<Company> findAllByTypeInAndTenantId(List<CompanyType> types, Long tenantId);
//
//    List<Company> findAllByTypeInAndIdNotInAndTenantId(List<CompanyType> types, List<Long> companyIds, Long tenantId);

//    @Query(value = "select new com.altomni.apn.company.web.rest.vm.job.CompanyJobAmountVM(j.companyId, count(j.id)) from JobCompanyBrief j where j.status=:status and j.tenantId=:tenantId and j.companyId in :companyIds and j.jobType in :jobTypeList group by j.companyId")
//    List<CompanyJobAmountVM> countAllByStatusAndCompanyIdIn(@Param("status") JobStatus status, @Param("tenantId") Long tenantId, @Param("companyIds") List<Long> companyIds, @Param("jobTypeList") List<JobType> jobTypeList);

    Page<Company> findAllByTenantId(Long tenantId, Pageable pageable);

    @Query(value = "SELECT c.* FROM company c INNER JOIN company_sales_lead_client_contact sc ON c.id = sc.company_id WHERE c.tenant_id = ?1 AND sc.`status` = 1 AND sc.talent_id = ?2", nativeQuery = true)
    List<Company> queryActiveContactCompany(Long tenantId, Long talentId);

    @Query(value = "SELECT DISTINCT c.* FROM company c INNER JOIN company_sales_lead_client_contact sc ON c.id = sc.company_id WHERE c.tenant_id = ?1 AND sc.`status` = 1 AND sc.talent_id IN ?2", nativeQuery = true)
    List<Company> queryActiveContactCompanyByTalentIds(Long tenantId, List<Long> talentIds);


    Integer countByTenantId(Long tenantId);

    @Query(value = "select j.id as jobId, c.full_business_name as companyName from company c right join job j on c.id=j.company_id where j.id in :jobIds", nativeQuery = true)
    List<JobIdCompanyNameVM> getCompanyNamesByJobIds(@Param("jobIds") List<Long> jobIds);

    @Query(value = "select c.full_business_name from company c where c.id =:companyId", nativeQuery = true)
    String getCompanyNameById(@Param("companyId") Long companyId);

    List<Company> findAllByTenantIdAndFullBusinessNameIn(Long tenantId, List<String> names);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company c SET c.last_sync_time=:lastSyncTime WHERE c.id=:companyId", nativeQuery = true)
    void updateCompanyLastSyncTime(@Param("companyId") Long companyId, @Param("lastSyncTime") Instant lastSyncTime);

    List<Company> findCompaniesByTenantIdAndActive(Long tenantId, AccountCompanyStatus active);

    @Query("select new com.altomni.apn.company.domain.company.Company(c.id, c.fullBusinessName, c.active, c.tenantId) from Company c where c.tenantId = ?1")
    List<Company> findAllByTenantId(Long tenantId);

    @Query(value = " SELECT new com.altomni.apn.company.domain.vo.CompanyVo(c.id, c.fullBusinessName, c.tenantId) FROM Company c where c.tenantId in ?1 ")
    List<CompanyVo> findCompanyByPageAndTenantIds(@Param("tenantIds") List<Long> tenantIds, Pageable pageable);

    @Query(value = " SELECT new com.altomni.apn.company.domain.vo.CompanyVo(c.id, c.fullBusinessName, c.tenantId) FROM Company c where c.id in ?1 ")
    List<CompanyVo> findCompanyByIds(@Param("companyIds") List<Long> companyIds);

    @Query("select c from CompanyMigrate c where exists (select 1 from CompanyProgressNote n where n.companyId = c.id)")
    Page<CompanyMigrate> findAllNoteIsNotNull(Pageable pageable);

    @Query("select c from CompanyMigrate c where c.tenantId is not null ")
    Page<CompanyMigrate> findAllCompanyMigrate(Pageable pageable);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_client_note c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateCompanyClientNote(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_contact c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateCompanyContact(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_contract c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateCompanyContract(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_custom_folder_connect_client c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateCompanyCustomFolderConnectClient(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_industry_relation c SET c.company_id=?1 WHERE c.id in ( ?2 )", nativeQuery = true)
    void updateCompanyIndustryRelation(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_location c SET c.company_id=?1 WHERE c.id in ( ?2 )", nativeQuery = true)
    void updateCompanyLocation(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_progress_note c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateProgressNote(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_project_team c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateCompanyProjectTeam(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_sales_lead c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateCompanySalesLead(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_sales_lead_administrator c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateCompanySalesLeadAdmin(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_sales_lead_client_contact c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateCompanySalesLeadClientContact(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE job c SET c.company_id=?1 WHERE c.company_id in ( ?2 )", nativeQuery = true)
    void updateCompanyJob(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_tag_relation c SET c.company_id=?1 WHERE c.id in ( ?2 )", nativeQuery = true)
    void updateCompanyTagRelation(Long companyId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "delete from company WHERE id in ( ?1 )", nativeQuery = true)
    void deleteCompanyInfo(List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE talent_additional_info c SET c.extended_info=?1 WHERE c.id =?2 ", nativeQuery = true)
    void updateTalentAdditionalInfo(String extendedInfo, Long id);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company c SET c.is_need_sync_hr = 1 WHERE c.id =?1 ", nativeQuery = true)
    void updateCompanyNeedSyncToHr(Long companyId);

    @Query(value = """
            SELECT c.id, c.full_business_name, JSON_UNQUOTE(JSON_EXTRACT(cai.extended_info, '$.displayNameInEn'))
                                  FROM company c
                                  INNER JOIN company_contract cc
                                      ON c.id = cc.company_id
                                  LEFT JOIN company_additional_info cai
                                      ON c.id = cai.company_id
                                  WHERE
                                      UTC_TIMESTAMP() >= cc.start_time
                                      AND (cc.end_time IS NULL OR UTC_TIMESTAMP() <= cc.end_time)
                                      AND cc.no_poaching <> ?1
                                      AND cc.status = ?2
                                      AND c.active <> ?3
                                      AND c.tenant_id = ?4
                                  GROUP BY c.id
            """, nativeQuery = true)
    List<Object[]> getAllNoPoachingCompanies(Integer noPoachingType, Integer contractStatus, Integer companyStatus, Long tenantId);

    @Query(value = """
        SELECT c.id, c.full_business_name, JSON_UNQUOTE(JSON_EXTRACT(cai.extended_info, '$.displayNameInEn'))
        FROM company c
        LEFT JOIN company_additional_info cai
        ON c.id = cai.company_id                          
        where c.tenant_id = ?1
    """, nativeQuery = true)
    List<Object[]> getDisplayNameInEnList(Long tenantId);

    @Query(value = """
        SELECT c.id, c.full_business_name, JSON_UNQUOTE(JSON_EXTRACT(cai.extended_info, '$.displayNameInEn'))
        FROM company c
        LEFT JOIN company_additional_info cai
        ON c.id = cai.company_id                          
        where c.tenant_id = ?1 and c.id in ( ?2 )
    """, nativeQuery = true)
    List<Object[]> getDisplayNameInEnList(Long tenantId, List<Long> accountCompanyIds);

    @Modifying
    @Transactional
    @Query(value = """
        update company c set c.full_business_name  = ?2 where c.id = ?1 
        """, nativeQuery = true
    )
    void updateCompanyNames(Long id, String name);

    @Modifying
    @Transactional
    @Query(value = "update calendar_event_relation_info set relation_name=?2 where relation_id=?1 and relation_type=0", nativeQuery = true)
    void updateCalendarCompanyName(Long companyId, String companyName);
}
