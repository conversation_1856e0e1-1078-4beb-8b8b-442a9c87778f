package com.altomni.apn.company.service.dto.note;

import com.altomni.apn.company.domain.enumeration.company.CompanyContactType;
import com.altomni.apn.company.domain.enumeration.company.CompanyContactTypeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@Data
@ApiModel(description = "note for progress company's basic")
public class CompanyProgressNoteBasicDTO implements Serializable {


    @ApiModelProperty(value = "the clientContactId id for note.")
    @NotEmpty
    public List<Long> clientContactIds;

    @ApiModelProperty(value = "the contactType for note.")
    @NotNull
    @Convert(converter = CompanyContactTypeConverter.class)
    public CompanyContactType contactType;

    @ApiModelProperty(value = "the contactDate for note.")
    @NotNull
    public Instant contactDate;

    @ApiModelProperty(value = "the id for salesLead.")
    @NotNull
    public Long salesLeadId;

    @ApiModelProperty(value = "the text for note.")
    @NotEmpty
    public String note;

}
