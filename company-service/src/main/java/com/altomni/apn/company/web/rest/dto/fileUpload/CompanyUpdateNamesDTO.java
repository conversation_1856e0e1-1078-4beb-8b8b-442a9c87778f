package com.altomni.apn.company.web.rest.dto.fileUpload;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ExcelIgnoreUnannotated
@AllArgsConstructor
@NoArgsConstructor
public class CompanyUpdateNamesDTO {

    @ExcelProperty("id")
    private String id;

    @ExcelProperty("full_business_name")
    private String fullBusinessName;

    @ExcelProperty("biz_pool_id")
    private String bizPoolId;

    @ExcelProperty("biz_pool_biz_name")
    private String bizPoolBizName;

    @ExcelProperty("biz_info_id")
    private String bizInfoId;

    @ExcelProperty("biz_info_biz_name")
    private String bizInfoBizName;

}
