package com.altomni.apn.company.repository.company;

import com.altomni.apn.company.domain.company.CompanyClientInvoicingInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyClientInvoicingInfoRepository extends JpaRepository<CompanyClientInvoicingInfo, Long> {

    List<CompanyClientInvoicingInfo> findAllByCompanyId(Long companyId);

    List<CompanyClientInvoicingInfo> findByClientNameAndTenantId(String clientName,Long tenantId);

    List<CompanyClientInvoicingInfo> findBySocialCreditCodeAndTenantId(String SocialCreditCode,Long tenantId);

    List<CompanyClientInvoicingInfo> findByBankAccountAndTenantId(String bankAccount,Long tenantId);
}
