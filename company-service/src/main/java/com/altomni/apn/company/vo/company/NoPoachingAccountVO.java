package com.altomni.apn.company.vo.company;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
public class NoPoachingAccountVO implements Serializable {

    @ApiModelProperty(value = "id for company")
    private Long id;

    @ApiModelProperty(value = "full Business Name")
    private String fullBusinessName;

    @ApiModelProperty (value = "display Name In En")
    private String displayNameInEn;


    public NoPoachingAccountVO(Long id, String fullBusinessName, String displayNameInEn) {
        this.id = id;
        this.fullBusinessName = fullBusinessName;
        this.displayNameInEn = displayNameInEn;
    }
}
