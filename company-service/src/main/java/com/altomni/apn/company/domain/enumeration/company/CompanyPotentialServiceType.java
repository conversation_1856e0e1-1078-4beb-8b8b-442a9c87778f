package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The IndustryType enumeration.
 */
public enum CompanyPotentialServiceType implements ConvertedEnum<Integer> {
    RECRUITING(1),
    CONTRACTING(2),
    SOW(3),
    FLAG_TRAINING(4),
    PAYROLL(5),
    MANAGEMENT_CONSULTING(6),
    SVLC_AND_THINKTANK(7),
    CAMPUS_RECRUITING(8),
    INTERNSHIP(9),
    HITALENT_SOFTWARE(10),
    ROP(11);

    private final int dbValue;

    CompanyPotentialServiceType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CompanyPotentialServiceType, Integer> resolver = new ReverseEnumResolver<>(CompanyPotentialServiceType.class, CompanyPotentialServiceType::toDbValue);

    public static CompanyPotentialServiceType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
