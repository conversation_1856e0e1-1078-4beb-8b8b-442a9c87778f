package com.altomni.apn.company.service.job;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

@Component
@FeignClient(value = "job-service")
public interface JobClient {

    @PostMapping("/job/api/v3/jobs/sync/byCompanyId/{companyId}")
    ResponseEntity<Void> syncJobByCompanyId(@PathVariable("companyId") Long companyId);

}
