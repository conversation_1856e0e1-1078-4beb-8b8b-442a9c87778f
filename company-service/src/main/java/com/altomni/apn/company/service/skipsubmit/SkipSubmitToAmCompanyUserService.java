package com.altomni.apn.company.service.skipsubmit;


import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompany;
import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;

import java.util.List;

/**
 * Service Interface for managing SkipSubmitToAmCompany.
 * <AUTHOR>
 */
public interface SkipSubmitToAmCompanyUserService {

    List<SkipSubmitToAmCompanyUser> findAllByCompanyId(Long companyId);

    List<SkipSubmitToAmCompanyUser> replaceUsers(Long companyId, List<Long> userIds);

    List<SkipSubmitToAmCompany> findAll();
}
