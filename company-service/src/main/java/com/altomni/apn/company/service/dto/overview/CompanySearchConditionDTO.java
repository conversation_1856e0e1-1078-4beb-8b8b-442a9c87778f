package com.altomni.apn.company.service.dto.overview;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.search.CommonPoolTalentType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.dto.search.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.validator.constraints.UniqueElements;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class CompanySearchConditionDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "search condition in json string")
    private List<SearchParam> search;

    @ApiModelProperty(value = "search filter with 'AND' relation, in json string")
    private List<SearchParam> filter;

    private ModuleType module;

    private CommonPoolTalentType commonPoolType;

    private String timezone;

    private LanguageEnum language = LanguageEnum.EN;

    @UniqueElements
    private List<String> folders;

    private String uuid;

    public static void checkPageable(Pageable pageable) {
        int from = 0;
        int size = 0;
        from = (pageable.getPageNumber() - 1) * pageable.getPageSize() <= -1 ? 0 : (pageable.getPageNumber() - 1) * pageable.getPageSize();
        size = pageable.getPageSize() <= 0 ? 10 : pageable.getPageSize();
        if (from > (Constants.LIST_SEARCH_AREA - size) || (from + size) > Constants.LIST_SEARCH_AREA) {
            throw new CustomParameterizedException("The search area cannot exceed " + Constants.LIST_SEARCH_AREA);
        }
    }

    public static CompanySearchConditionDTO constructFromGlobalSearch(GlobalSearchConditionDTO globalSearchDTO) {
        CompanySearchConditionDTO conditionDTO = new CompanySearchConditionDTO();
        conditionDTO.setModule(ModuleType.COMPANY_POOL);
        conditionDTO.setTimezone(globalSearchDTO.getTimezone());
        conditionDTO.setLanguage(globalSearchDTO.getLanguage());

        //construct search param
        ConditionParam generalSearchCondition = ConditionParam.of("generalText", globalSearchDTO.getSearch());
        ConditionParam activeSearchCondition = ConditionParam.of("active", true);

        List<ConditionParam> searchConditions = new ArrayList<>();
        searchConditions.add(generalSearchCondition);
        searchConditions.add(activeSearchCondition);

        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.AND);
        searchParam.setCondition(searchConditions);

        List<SearchParam> search = new ArrayList<>();
        search.add(searchParam);

        conditionDTO.setSearch(search);

        
        //construct filter search for job
        ConditionParam hasServiceTypeFilterCondition = ConditionParam.of("hasServiceTypes", true);
        List<ConditionParam> filterConditions = new ArrayList<>();
        filterConditions.add(hasServiceTypeFilterCondition);

        SearchParam filterParam = new SearchParam();
        filterParam.setRelation(Relation.AND);
        filterParam.setCondition(filterConditions);

        List<SearchParam> filter = new ArrayList<>();
        filter.add(filterParam);

        conditionDTO.setFilter(filter);

        return conditionDTO;
    }

}
