package com.altomni.apn.company.service.company;

import com.altomni.apn.common.vo.company.CompanyClientInvoicingInfoVO;
import com.altomni.apn.company.service.dto.CompanyClientInvoicingInfoDTO;

import java.util.List;

public interface CompanyClientInvoicingInfoService {

    void save(CompanyClientInvoicingInfoDTO dto);

    void modify(CompanyClientInvoicingInfoDTO dto);

    List<CompanyClientInvoicingInfoVO> findByCompanyId(Long companyId);

    void delete(Long id);

    CompanyClientInvoicingInfoVO findById(Long id);

    String getInvoicingPreference();

}
