package com.altomni.apn.company.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

@Data
public class AmReportSummaryVO {

    @ColumnWidth(20)
    @ExcelProperty(value = "Active Jobs",index = 0)
    private Integer activeJob;

    @ColumnWidth(20)
    @ExcelProperty(value = "Weekly New Candidates",index = 1)
    private Integer newCandidate;

    @ColumnWidth(20)
    @ExcelProperty(value = "Active Candidates",index = 2)
    private Integer activeCandidate;

    @ColumnWidth(20)
    @ExcelProperty(value = "Total Candidates",index = 3)
    private Integer totalCandidate;

    @ColumnWidth(20)
    @ExcelProperty(value = "Total Offered",index = 4)
    private Integer totalOffersByClient;

    @ColumnWidth(20)
    @ExcelProperty(value = "Total Offers Accepted",index = 5)
    private Integer totalOfferAccepted;

    @ColumnWidth(20)
    @ExcelProperty(value = "Total on Boarded",index = 6)
    private Integer totalOnBoard;




}
