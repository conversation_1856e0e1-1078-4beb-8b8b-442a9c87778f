package com.altomni.apn.company.vo.projectTeam;

import com.altomni.apn.common.domain.enumeration.job.JobPermission;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@AllArgsConstructor
@ApiModel(description = "Vo for company client note")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CompanyProjectTeamUserVO implements Serializable {

    @ApiModelProperty(value = "the id for company projectTeam user.")
    private Long userId;

    @ApiModelProperty(value = "the permissions for company projectTeam user.")
    private Set<String> permissions;

    @ApiModelProperty(value = "the name for company projectTeam user.")
    private String userName;

}
