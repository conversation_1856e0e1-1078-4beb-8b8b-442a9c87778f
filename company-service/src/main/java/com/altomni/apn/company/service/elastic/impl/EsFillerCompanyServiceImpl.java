package com.altomni.apn.company.service.elastic.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.ElasticSearchConstants;
import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import com.altomni.apn.common.constants.ResponsibilityConstants;
import com.altomni.apn.common.domain.dict.CompanyIndustryRelation;
import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.dict.EnumStatus;
import com.altomni.apn.common.dto.esfiller.EsFillerCompanyUpdateDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.management.TenantPhoneDTO;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.service.enums.EnumCompanyTagService;
import com.altomni.apn.common.service.enums.EnumIndustryService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.config.env.EsfillerMQProperties;
import com.altomni.apn.company.domain.business.AccountBusiness;
import com.altomni.apn.company.domain.business.BusinessFlowAdministrator;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.domain.company.*;
import com.altomni.apn.company.domain.company.lcoation.CompanyLocation;
import com.altomni.apn.company.domain.company.note.CompanyClientNote;
import com.altomni.apn.company.domain.company.note.CompanyClientNoteContactRelation;
import com.altomni.apn.company.domain.company.note.CompanyProgressNote;
import com.altomni.apn.company.domain.company.note.CompanyProgressNoteContactRelation;
import com.altomni.apn.company.domain.contract.Contract;
import com.altomni.apn.company.domain.contract.ContractBusinessRelation;
import com.altomni.apn.company.domain.enumeration.business.BusinessProgress;
import com.altomni.apn.company.domain.enumeration.company.NoPoachingType;
import com.altomni.apn.company.domain.enumeration.contract.ContractStatus;
import com.altomni.apn.company.domain.vm.EntityNameVM;
import com.altomni.apn.company.domain.vo.CompanyVo;
import com.altomni.apn.company.repository.business.AccountBusinessAdministratorRepository;
import com.altomni.apn.company.repository.business.AccountBusinessRepository;
import com.altomni.apn.company.repository.business.SalesLeadClientContactRepository;
import com.altomni.apn.company.repository.company.CompanyAddtionalInfoRepository;
import com.altomni.apn.company.repository.company.CompanyContactRepository;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.company.CompanyTagRelationRepository;
import com.altomni.apn.company.repository.company.folder.CompanyCustomFolderConnectClientRepository;
import com.altomni.apn.company.repository.company.folder.CompanyCustomFolderRepository;
import com.altomni.apn.company.repository.company.location.CompanyLocationRepository;
import com.altomni.apn.company.repository.company.note.CompanyClientNoteContactRelationRepository;
import com.altomni.apn.company.repository.company.note.CompanyClientNoteRepository;
import com.altomni.apn.company.repository.company.note.CompanyProgressNoteContactRelationRepository;
import com.altomni.apn.company.repository.company.note.CompanyProgressNoteRepository;
import com.altomni.apn.company.repository.contract.ContractBusinessRelationRepository;
import com.altomni.apn.company.repository.contract.ContractRepository;
import com.altomni.apn.company.repository.user.UserServiceRepository;
import com.altomni.apn.company.service.dto.location.LocationESDTO;
import com.altomni.apn.company.service.elastic.EsFillerCompanyService;
import com.altomni.apn.company.service.rabbitmq.RabbitMqService;
import com.altomni.apn.company.service.redis.RedisService;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.company.vo.company.CompanyRelationshipDTO;
import com.altomni.apn.company.vo.location.LocationVO;
import liquibase.pro.packaged.S;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;
import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;
import static com.altomni.apn.company.service.company.overview.impl.CompanyOverviewServiceImpl.convertToOkHttpHeaders;

@Slf4j
@Service("esFillerCompanyService")
public class EsFillerCompanyServiceImpl implements EsFillerCompanyService {

    private final Double CLIENT_ACCOUNT_PROGRESS = 1.20;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private HttpService httpService;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private EnumIndustryService enumIndustryService;

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource
    private RedisService redisService;

    @Resource
    private CanalService canalService;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private CompanyLocationRepository companyLocationRepository;

    @Resource
    private AccountBusinessRepository accountBusinessRepository;

    @Resource
    private AccountBusinessAdministratorRepository accountBusinessAdministratorRepository;

    @Resource
    private UserServiceRepository userServiceRepository;

    @Resource
    private CompanyCustomFolderConnectClientRepository companyCustomFolderConnectClientRepository;

    @Resource
    private CompanyCustomFolderRepository customFolderRepository;

    @Resource
    private ContractRepository contractRepository;

    @Resource
    private CompanyClientNoteRepository companyClientNoteRepository;

    @Resource
    private CompanyProgressNoteRepository companyProgressNoteRepository;

    @Resource
    private CompanyTagRelationRepository companyTagRelationRepository;

    @Resource
    private EnumCompanyTagService enumCompanyTagService;

    @Resource
    private ContractBusinessRelationRepository contractBusinessRelationRepository;

    @Resource
    private EnumCommonService enumCommonService;

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    @Resource
    private CompanyContactRepository companyContactRepository;

    @Resource
    private CompanyProgressNoteContactRelationRepository companyProgressNoteContactRelationRepository;

    @Resource
    private CompanyClientNoteContactRelationRepository companyClientNoteContactRelationRepository;

    @Resource
    private SalesLeadClientContactRepository salesLeadClientContactRepository;

    @Resource
    private CompanyAddtionalInfoRepository companyAddtionalInfoRepository;

    @Resource
    private JobdivaRabbitProperties jobdivaRabbitProperties;

    @Resource(name = "jobdivaRabbitTemplate")
    private RabbitTemplate jobdivaRabbitTemplate;

    private String updateCompaniesFolderUrl(Long tenantId) {
        return applicationProperties.getSyncUrl() + tenantId + "/companies_folder_update";
    }

    private String splitLocationUrl() {
        return applicationProperties.getBaseUrl() + "/v3/split_location_string";
    }

    @Override
    public void updateCompanyName(Long companyId) {
        // TODO: 2023/4/27 CompanyResource need update company name.
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            Optional<Company> companyOptional = companyRepository.findById(companyId);
            if (companyOptional.isEmpty()) {
                return;
            }
            Company company = companyOptional.get();
            EsFillerCompanyUpdateDTO companyUpdateDTO = new EsFillerCompanyUpdateDTO(company.getFullBusinessName());
//            if (ObjectUtil.isNotNull(company.getIndustry())) {
//                List<String> industries = IndustryType.translate(company.getIndustry());
//                companyUpdateDTO.setIndustries(industries);
//            }
            if (CollUtil.isNotEmpty(company.getIndustries())) {
                List<String> industries = enumIndustryService.getIndustriesByIds(company.getIndustries().stream().map(o -> String.valueOf(o.getEnumId())).collect(Collectors.toList()));
                companyUpdateDTO.setIndustries(industries);
            }
            String url = applicationProperties.getSyncUrl() + SecurityUtils.getTenantId() + "/company/" + companyId + "/update_es";
            try {
                HttpResponse response = httpService.post(url, JSON.toJSONString(companyUpdateDTO));
                if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.info("[APN: EsFillerCompanyService] Update company name to EsFiller success, id: {}, response code: {}, response message: {}", companyId, response.getCode(), response.getBody());
                } else {
                    log.error("[APN: EsFillerCompanyService] Update company name to EsFiller error, id: {}, response code: {}, response message: {}", companyId, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                }
            } catch (IOException e) {
                log.error("[APN: EsFillerCompanyService] Update company name IOException, company id {}", companyId);
            }
        });
    }

    @Override
    public void extractCompanyToMq(Collection<Long> companyIds, int priority) {
        List<JSONObject> companyProfiles = companyRepository.findAllById(companyIds).stream().map(company -> buildCompanyProfile(company, 0)).collect(Collectors.toList());
        log.info("[EsFillerCompanyService: syncCompanyToMQ @{}] companyProfiles length: {}, ids: {}", SecurityUtils.getUserId(), companyProfiles.size(), companyIds);
        for (JSONObject companyProfile : companyProfiles) {
            if (Objects.isNull(companyProfile)){
                continue;
            }
            Long id = companyProfile.getLong("_id");
            try {
                rabbitMqService.saveCompanyProfile(JSONUtil.toJsonStr(companyProfile), priority);
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.COMPANY);
                log.info("[EsFillerCompanyService: syncCompanyToMQ @{}] save company to MQ success, id: {}", SecurityUtils.getUserId(), id);
            } catch (Exception e) {
                log.error("[EsFillerCompanyService: syncCompanyToMQ @{}] save company to MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send company to ES Error" +
                        "\n\tCompany ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }


    @Override
    public void deleteCompanyToMq(Collection<Long> companyIds, int priority) {
        List<JSONObject> companyProfiles = companyRepository.findAllById(companyIds).stream().map(company -> {
            JSONObject request = new JSONObject();
            JSONObject source = translateEs(company.getId());
            request.put("_id", company.getId());
            request.put("_tenant_id", String.valueOf(company.getTenantId()));
            request.put("_type", "company");
            request.put("_return_routing_key", esfillerMQProperties.getApnNormalizedCompanyRoutingKey());
            request.put("_source", source);
            request.put("_deleted", true);
            return request;
        }).toList();
        for (JSONObject companyProfile : companyProfiles) {
            if (Objects.isNull(companyProfile)){
                continue;
            }
            Long id = companyProfile.getLong("_id");
            try {
                rabbitMqService.saveCompanyProfile(JSONUtil.toJsonStr(companyProfile), priority);
                log.info("[EsFillerCompanyService: syncCompanyToMQ @{}] delete company to MQ success, id: {}", SecurityUtils.getUserId(), id);
            } catch (Exception e) {
                log.error("[EsFillerCompanyService: syncCompanyToMQ @{}] delete company to MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send company to ES Error" +
                        "\n\tCompany ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    @Override
    public void saveNormalizedCompanyInfos(String normalizedCompany) {
        JSONObject responseJson = JsonUtil.fromJson(JSONUtil.toJsonStr(normalizedCompany), JSONObject.class);
        if (responseJson == null || !responseJson.containsKey("_id")) {
            return;
        }
        Long companyId = responseJson.getLong("_id");
        log.info("Normalized company message received：{}", companyId);
        Company company = companyRepository.findById(companyId).orElse(new Company());
        if (ObjectUtil.isEmpty(company.getId())) {
            return;
        }
        cn.hutool.json.JSONObject companyJson = responseJson.getJSONObject("_source");
        if (ObjectUtil.isNotEmpty(companyJson)) {
            JSONArray locations = companyJson.getJSONArray(ElasticSearchConstants.ESFILLER_KEY_LOCATIONS);
            if (ObjectUtil.isNotEmpty(locations)) {
                List<LocationESDTO> locationDTOList = locations.toList(LocationESDTO.class);
                if (CollUtil.isNotEmpty(locationDTOList)) {
                    List<CompanyLocation> companyLocationList = new ArrayList<>();
                    locationDTOList.forEach(s -> {
                        if (ObjectUtil.isNotEmpty(s.getOfficialCity()) || ObjectUtil.isNotEmpty(s.getOfficialCountry())
                                || ObjectUtil.isNotEmpty(s.getOfficialProvince()) || ObjectUtil.isNotEmpty(s.getOfficialCounty())) {
                            if (ObjectUtil.isNotEmpty(s.getId())) {
                                companyLocationRepository.updateOfficialInfoById(s.getId(), s.getOfficialCity(), s.getOfficialCountry(), s.getOfficialProvince(), s.getOfficialCounty());
                            }
                            else {
                                CompanyLocation location = new CompanyLocation();
                                location.setAccountCompanyId(company.getId());
                                ServiceUtils.myCopyProperties(s, location);
                                companyLocationList.add(location);
                            }
                        }
                    });
                    if (CollUtil.isNotEmpty(companyLocationList)) {
                        companyLocationRepository.deleteAllByCompanyIdAndOriginalLoc(company.getId());
                        companyLocationRepository.saveAll(companyLocationList);
                    }
                }
            }
            Short displayCompanyNameEn = companyJson.getShort(ElasticSearchConstants.ESFILLER_KEY_COMPANY_NAME_EN);
            if (ObjectUtils.isNotEmpty(displayCompanyNameEn)) {
                CompanyAdditionalInfo additionalInfo = companyAddtionalInfoRepository.findByAccountCompanyId(companyId);
                String extendedInfo = additionalInfo.getExtendedInfo();
                if (StringUtils.isBlank(extendedInfo)) {
                    extendedInfo = "{}";
                }
                JSONObject extendedInfoJson = JSONUtil.parseObj(extendedInfo);
                extendedInfoJson.put(ElasticSearchConstants.ESFILLER_KEY_COMPANY_NAME_EN, displayCompanyNameEn);
                additionalInfo.setExtendedInfo(JSONUtil.toJsonStr(extendedInfoJson));
                companyAddtionalInfoRepository.save(additionalInfo);
            }
        }

        companyRepository.updateCompanyLastSyncTime(company.getId(), Instant.now());
    }

    @Override
    public void updateCompaniesFolder(List<Long> companyIds, List<String> toFolderNames, List<String> fromFolderNames, Long tenantId) {
        if (CollUtil.isEmpty(companyIds) || (CollUtil.isEmpty(toFolderNames) && CollUtil.isEmpty(fromFolderNames))) {
            return;
        }

        JSONObject es = new JSONObject();
        es.put("ids", companyIds.stream().distinct().collect(Collectors.toList()));
        if (toFolderNames != null && CollUtil.isNotEmpty(toFolderNames)) {
            es.put("to", toFolderNames.stream().distinct().collect(Collectors.toList()));
        }
        if (fromFolderNames != null && CollUtil.isNotEmpty(fromFolderNames)) {
            es.put("from", fromFolderNames.stream().distinct().collect(Collectors.toList()));
        }
        if (ObjectUtil.isEmpty(es)) {
            return;
        }
        try {
            HttpResponse response = httpService.post(updateCompaniesFolderUrl(tenantId), JSONUtil.toJsonStr(es));
            if (response == null || !ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
//                redisService.saveFailedCompanyIds(companyIds);
                log.error("[APN: EsFillerCompanyService] updateCompaniesFolder to EsFiller error,request body:{} , response code: {}, response message: {}", es, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
//                String message = "Update companies folder to MQ Error" +
//                        "\n\tCompany IDs: " + companyIds +
//                        "\n\tError: " +
//                        "\n\t" + response;
//                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.UPDATECOMPANIESFOLDER_RESPONSEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(response),companyApiPromptProperties.getCompanyService()));
            }
        } catch (Exception e) {
//            redisService.saveFailedCompanyIds(companyIds);
            log.error("[APN: EsFillerCompanyService] updateCompaniesFolder Exception, company = {}, message = {}", JSONUtil.toJsonStr(es), ExceptionUtils.getMessage(e));
//            String message = "Update companies folder to MQ Error" +
//                    "\n\tCompany IDs: " + companyIds +
//                    "\n\tError: " +
//                    "\n\t" + ExceptionUtils.getStackTrace(e);
//            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.UPDATECOMPANIESFOLDER_RESPONSEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(ExceptionUtils.getStackTrace(e)), companyApiPromptProperties.getCompanyService()));
        }
    }

    @Override
    public void extractCompanyClientNoteToMq(Collection<Long> companyClientNoteIds, int priority) {
        List<JSONObject> companyClientNoteProfiles = companyClientNoteRepository.findAllById(companyClientNoteIds).stream().map(note -> buildCompanyClientNoteProfile(note, 0)).collect(Collectors.toList());
        log.info("[EsFillerCompanyService: syncCompanyClientNoteToMQ @{}] companyClientNoteProfiles length: {}, ids: {}", SecurityUtils.getUserId(), companyClientNoteProfiles.size(), companyClientNoteIds);
        for (JSONObject companyClientNoteProfile : companyClientNoteProfiles) {
            if (Objects.isNull(companyClientNoteProfile)){
                continue;
            }
            Long id = companyClientNoteProfile.getLong("_id");
            try {
                rabbitMqService.saveCompanyClientNoteProfile(JSONUtil.toJsonStr(companyClientNoteProfile), priority);
                companyClientNoteRepository.updateCompanyClientNoteLastSyncTime(id, Instant.now());
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.COMPANY_CLIENT_NOTE);
                log.info("[EsFillerCompanyService: syncCompanyClientNoteToMQ @{}] save companyClientNote to MQ success, id: {}", SecurityUtils.getUserId(), id);
            } catch (Exception e) {
                log.error("[EsFillerCompanyService: syncCompanyClientNoteToMQ @{}] save companyClientNote to MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.COMPANY_CLIENT_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send companyClientNote to ES Error" +
                        "\n\tNote ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    @Override
    public void extractCompanyProgressNoteToMq(Collection<Long> companyProgressNoteIds, int priority) {
        List<JSONObject> companyProgressNoteProfiles = companyProgressNoteRepository.findAllById(companyProgressNoteIds).stream().map(note -> buildCompanyProgressNoteProfile(note, 0)).collect(Collectors.toList());
        log.info("[EsFillerCompanyService: syncCompanyProgressNoteToMQ @{}] companyProgressNoteProfiles length: {}, ids: {}", SecurityUtils.getUserId(), companyProgressNoteProfiles.size(), companyProgressNoteIds);
        for (JSONObject companyProgressNoteProfile : companyProgressNoteProfiles) {
            if (Objects.isNull(companyProgressNoteProfile)){
                continue;
            }
            Long id = companyProgressNoteProfile.getLong("_id");
            try {
                rabbitMqService.saveCompanyProgressNoteProfile(JSONUtil.toJsonStr(companyProgressNoteProfile), priority);
                companyProgressNoteRepository.updateCompanyProgressNoteLastSyncTime(id, Instant.now());
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.COMPANY_PROGRESS_NOTE);
                log.info("[EsFillerCompanyService: syncCompanyProgressNoteToMQ @{}] save companyProgressNote to MQ success, id: {}", SecurityUtils.getUserId(), id);
            } catch (Exception e) {
                log.error("[EsFillerCompanyService: syncCompanyProgressNoteToMQ @{}] save companyProgressNote to MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.COMPANY_PROGRESS_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send companyProgressNote to ES Error" +
                        "\n\tNote ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    @Override
    public void extractCompanyContactToHrMq(Collection<Long> ids, int priority) {
        List<JSONObject> companyContactProfiles = salesLeadClientContactRepository.findAllById(ids).stream().map(this::buildCompanyContactProfile).toList();
        log.info("[EsFillerCompanyService: syncCompanyContactToHrMQ @{}] companyContactProfiles length: {}, ids: {}", SecurityUtils.getUserId(), companyContactProfiles.size(), ids);
        for (JSONObject companyContact : companyContactProfiles) {
            if (Objects.isNull(companyContact)){
                continue;
            }
            Long id = companyContact.getLong("id");
            try {
                jobdivaRabbitTemplate.convertAndSend(jobdivaRabbitProperties.getApnToJobdivaExchange(), jobdivaRabbitProperties.getApnToJobdivaRoutingKey(), JSONUtil.toJsonStr(companyContact), message -> {
                    message.getMessageProperties().setPriority(priority);
                    return message;
                });
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.HR_COMPANY_CONTACT);
                log.info("[EsFillerCompanyService: syncCompanyContactToHrMQ @{}] save companyContact to hr MQ success, id: {}", SecurityUtils.getUserId(), id);
            } catch (Exception e) {
                log.error("[EsFillerCompanyService: syncCompanyContactToHrMQ @{}] save companyContact to hr MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.HR_COMPANY_CONTACT, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send companyContact to ES Error" +
                        "\n\tcontact ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    @Override
    public void extractCompanyToHrMq(Collection<Long> ids, int priority) {
        List<JSONObject> companyHrProfiles = companyRepository.findCompanyByIds(ids.stream().toList()).stream().map(this::buildCompanyHrProfile).toList();
        log.info("[EsFillerCompanyService: syncCompanyToHrMQ @{}] syncCompanyToHrMQ length: {}, ids: {}", SecurityUtils.getUserId(), companyHrProfiles.size(), ids);
        for (JSONObject companyVo : companyHrProfiles) {
            Long id = companyVo.getLong("id");
            try {
                jobdivaRabbitTemplate.convertAndSend(jobdivaRabbitProperties.getApnToJobdivaExchange(), jobdivaRabbitProperties.getApnToJobdivaRoutingKey(), JSONUtil.toJsonStr(companyVo), message -> {
                    message.getMessageProperties().setPriority(priority);
                    return message;
                });
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.HR_COMPANY);
                log.info("[EsFillerCompanyService: syncCompanyToHrMQ @{}] save syncCompanyToHrMQ to MQ success, id: {}", SecurityUtils.getUserId(), id);
            } catch (Exception e) {
                log.error("[EsFillerCompanyService: syncCompanyToHrMQ @{}] save company to HR MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.HR_COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
                String message = "Send company to hr Error" +
                        "\n\tcompany ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    private JSONObject buildCompanyHrProfile(CompanyVo note) {
        JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        JSONObject request = new JSONObject(jsonConfig);
        request.put("id", note.getId());
        request.put("type", JobdivaDataSyncTypeEnum.COMPANY);
        JSONObject entity = new JSONObject();
        entity.put("id", note.getId());
        entity.put("companyName", note.getName());
        entity.put("tenantId", note.getTenantId());
        request.put("entity", entity);
        return request;
    }

    private JSONObject buildCompanyContactProfile(SalesLeadClientContact note) {
        JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        JSONObject request = new JSONObject(jsonConfig);
        request.put("id", note.getId());
        request.put("type", JobdivaDataSyncTypeEnum.COMPANY_CONTACT);
        JSONObject entity = new JSONObject();
        entity.put("id", note.getId());
        entity.put("companyId", note.getCompanyId());
        entity.put("approverId", note.getApproverId());
        entity.put("tenantId", note.getTenantId());
        entity.put("talentId", note.getTalentId());
        entity.put("receiveEmail", note.getReceiveEmail());
        entity.put("inactived", note.getInactived());
        request.put("entity", entity);
        return request;
    }

    private JSONObject translateCompanyContact(Long contactId) {
        return null;
    }

    @Override
    public void saveNormalizedCompanyClientNoteInfos(String normalizedCompanyClientNote) {
        cn.hutool.json.JSONObject responseJson = JsonUtil.fromJson(JSONUtil.toJsonStr(normalizedCompanyClientNote), cn.hutool.json.JSONObject.class);
        Long noteId = responseJson.getLong("_id");
        log.info("Normalized companyClientNote message received：{}", noteId);
        CompanyClientNote note = companyClientNoteRepository.findById(noteId).orElse(new CompanyClientNote());
        if (ObjectUtil.isEmpty(note.getId())) {
            return;
        }

        companyClientNoteRepository.updateCompanyClientNoteLastSyncTime(note.getId(), Instant.now());
    }

    @Override
    public void saveNormalizedCompanyProgressNoteInfos(String normalizedCompanyProgressNote) {
        cn.hutool.json.JSONObject responseJson = JsonUtil.fromJson(JSONUtil.toJsonStr(normalizedCompanyProgressNote), cn.hutool.json.JSONObject.class);
        Long noteId = responseJson.getLong("_id");
        log.info("Normalized companyProgressNote message received：{}", noteId);
        CompanyProgressNote note = companyProgressNoteRepository.findById(noteId).orElse(new CompanyProgressNote());
        if (ObjectUtil.isEmpty(note.getId())) {
            return;
        }

        companyProgressNoteRepository.updateCompanyProgressNoteLastSyncTime(note.getId(), Instant.now());
    }

    @Override
    public HttpResponse splitLocation(String location) {
        if (StringUtils.isEmpty(location)) {
            return null;
        }
        HttpResponse response = null;
        try {
            response = httpService.post(splitLocationUrl(), location);
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {

            } else {
                log.error("[APN: EsFillerCompanyService] SplitLocation from EsFiller error, location: {}, response code: {}, response message: {}", location, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                throw new ExternalServiceInterfaceException(response != null ? response.getBody() : null, response != null ? response.getCode() : null);
            }
        } catch (IOException e) {
            log.error("[APN: EsFillerCompanyService] SplitLocation from EsFiller IOException, location: {}", location);
            throw new ExternalServiceInterfaceException(e.getMessage());
        }
        return response;
    }


    private cn.hutool.json.JSONObject buildCompanyProfile(Company company, int deep){
        JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        cn.hutool.json.JSONObject request = new cn.hutool.json.JSONObject(jsonConfig);
        try {
            if (deep > 0){
                Thread.sleep(2000);
            }
            cn.hutool.json.JSONObject es = translateEs(company.getId());
            request.put("_id", company.getId());
            request.put("_tenant_id", String.valueOf(company.getTenantId()));
            request.put("_type", "company");
            request.put("_return_routing_key", esfillerMQProperties.getApnNormalizedCompanyRoutingKey());
            request.put("_source", es);
        }catch (Exception e){
            log.error("[EsFillerCompanyService: syncCompanyToMQ @{}] buildCompanyProfile error, id: {}, error: {}", SecurityUtils.getUserId(), company.getId(), ExceptionUtils.getStackTrace(e));
            if (deep < esfillerMQProperties.getRetryThreshold()){
                request = buildCompanyProfile(company, deep + 1);
            }else{
                redisService.saveFailedCompanyIds(Arrays.asList(company.getId()));
                request = null;
                String message = "Build Company Profile Error" +
                        "\n\tCompany ID: " + company.getId() +
                        "\n\tCompany name: " + company.getFullBusinessName() +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
        return request;
    }

    private cn.hutool.json.JSONObject buildCompanyClientNoteProfile(CompanyClientNote note, int deep){
        JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        cn.hutool.json.JSONObject request = new cn.hutool.json.JSONObject(jsonConfig);
        try {
            if (deep > 0){
                Thread.sleep(2000);
            }
            Company company = companyRepository.findById(note.getCompanyId()).orElse(null);
            if (company == null) {
                return null;
            }
            CompanyClientNote clientNote = companyClientNoteRepository.findById(note.getId()).orElse(null);
            if (clientNote == null) {
                return null;
            }
            cn.hutool.json.JSONObject es = translateCompanyClientNoteEs(clientNote);
            request.put("_id", note.getId());
            request.put("_tenant_id", String.valueOf(company.getTenantId()));
            request.put("_type", "company_client_note");
//            request.put("_return_routing_key", esfillerMQProperties.getApnNormalizedCompanyClientNoteRoutingKey());
            request.put("_source", es);
            if (clientNote.getDeleted()) {
                request.put("_deleted", true);
            }
        }catch (Exception e){
            log.error("[EsFillerCompanyService: syncCompanyClientNoteToMQ @{}] buildCompanyClientNoteProfile error, id: {}, error: {}", SecurityUtils.getUserId(), note.getId(), ExceptionUtils.getStackTrace(e));
            if (deep < esfillerMQProperties.getRetryThreshold()){
                request = buildCompanyClientNoteProfile(note, deep + 1);
            }else{
                redisService.saveFailedCompanyClientNoteIds(Arrays.asList(note.getId()));
                request = null;
                String message = "Build CompanyClientNote Profile Error" +
                        "\n\tNote ID: " + note.getId() +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
        return request;
    }

    private cn.hutool.json.JSONObject buildCompanyProgressNoteProfile(CompanyProgressNote note, int deep){
        JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        cn.hutool.json.JSONObject request = new cn.hutool.json.JSONObject(jsonConfig);
        try {
            if (deep > 0){
                Thread.sleep(2000);
            }
            Company company = companyRepository.findById(note.getCompanyId()).orElse(null);
            if (company == null) {
                return null;
            }
            cn.hutool.json.JSONObject es = translateCompanyProgressNoteEs(note.getId());
            request.put("_id", note.getId());
            request.put("_tenant_id", String.valueOf(company.getTenantId()));
            request.put("_type", "company_progress_note");
//            request.put("_return_routing_key", esfillerMQProperties.getApnNormalizedCompanyProgressNoteRoutingKey());
            request.put("_source", es);
        }catch (Exception e){
            log.error("[EsFillerCompanyService: syncCompanyProgressNoteToMQ @{}] buildCompanyProgressNoteProfile error, id: {}, error: {}", SecurityUtils.getUserId(), note.getId(), ExceptionUtils.getStackTrace(e));
            if (deep < esfillerMQProperties.getRetryThreshold()){
                request = buildCompanyProgressNoteProfile(note, deep + 1);
            }else{
                redisService.saveFailedCompanyProgressNoteIds(Arrays.asList(note.getId()));
                request = null;
                String message = "Build CompanyProgressNote Profile Error" +
                        "\n\tNote ID: " + note.getId() +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
            }
        }
        return request;
    }

    private JSONObject translateEs(Long companyId) {
        Company company = companyRepository.findById(companyId).orElse(null);
        if (company == null) {
            return new JSONObject();
        }
        EntityNameVM creator = userServiceRepository.findCrmUserNameById(company.getPermissionUserId());
        if (creator == null) {
            if (company.getCreatedBy().contains(",")) {
                creator = userServiceRepository.findCrmUserNameById(Long.valueOf(company.getCreatedBy().split(",")[0]));
            }
        }
        List<EnumUserResponsibility> enumUserResponsibility = enumCommonService.findAllEnumUserResponsibility();
        Map<String, EnumUserResponsibility> enumUserResponsibilityMap = enumUserResponsibility.stream().filter(o -> EnumStatus.ACTIVE.equals(o.getStatus())).collect(Collectors.toMap(EnumUserResponsibility::getLabel, o -> o));

        Long activeContactCount = salesLeadClientContactRepository.countAllByCompanyIdAndActive(companyId, Boolean.TRUE);

        List<CompanyTagRelation> companyTagRelationList = companyTagRelationRepository.findAllByAccountCompanyId(companyId);

        JSONObject companyToEsJson = new JSONObject();
        if (company.getActive() != null && !AccountCompanyStatus.PROSPECT.equals(company.getActive())) {
            companyToEsJson.put("active", AccountCompanyStatus.ACTIVE.equals(company.getActive()) ? Boolean.TRUE : Boolean.FALSE);
        }
        companyToEsJson.put("numberOfContacts", activeContactCount);
        companyToEsJson.put("businessName", company.getFullBusinessName().trim());
        CompanyAdditionalInfo additionalInfo = companyAddtionalInfoRepository.findByAccountCompanyId(companyId);
        if(additionalInfo != null) {
            JSONObject additionalInfoJSON = JSONUtil.parseObj(additionalInfo.getExtendedInfo());
            if(additionalInfoJSON.get("aliases") != null) {
                companyToEsJson.put("aliases", additionalInfoJSON.get("aliases"));
            }
            if(additionalInfoJSON.get("commonEsId") != null) {
                companyToEsJson.put("crmCommonEsId", additionalInfoJSON.get("commonEsId"));
            }
            //禁猎客户需求，增加CRM同步到APN salesLeadCompanyId，bdCompaniesBusinessInformationId,crmAccountId
            if(additionalInfoJSON.get("businessInfoCompanyId") != null) {
                companyToEsJson.put("businessInfoCompanyId", additionalInfoJSON.get("businessInfoCompanyId"));
            }
            if(additionalInfoJSON.get("leadsCompanyId") != null) {
                companyToEsJson.put("leadsCompanyId", additionalInfoJSON.getStr("leadsCompanyId"));
            }
            //这是禁猎客户状态 "poachingRestriction" : ["010_DIRECT", "020_VIA_AFFILIATED"]
            // 010_DIRECT：直接禁猎公司;  020_VIA_AFFILIATED：关联禁猎公司
            if(additionalInfoJSON.get("poachingRestriction") != null) {
                companyToEsJson.put("poachingRestriction", additionalInfoJSON.get("poachingRestriction"));
            }
            //这是禁猎客户状态 "poachingRestriction" : ["010_DIRECT", "020_VIA_AFFILIATED"]
            // 010_DIRECT：直接禁猎公司;  020_VIA_AFFILIATED：关联禁猎公司
            // 仅当字段存在且值非空数组时才进行设置
            if (additionalInfoJSON.containsKey("poachingRestriction")) {
                Object poachingRestriction = additionalInfoJSON.get("poachingRestriction");
                // 判断是否为非空数组
                if (poachingRestriction instanceof JSONArray) {
                    JSONArray restrictionArray = (JSONArray) poachingRestriction;
                    if (!restrictionArray.isEmpty()) {
                        companyToEsJson.put("poachingRestriction", restrictionArray.toList(String.class));
                    }
                }
            }
            if (additionalInfoJSON.containsKey("websites")) {
                if (CollUtil.isNotEmpty(additionalInfoJSON.getJSONArray("websites"))) {
                    List<String> websites = additionalInfoJSON.getJSONArray("websites").toList(String.class);
                    companyToEsJson.put("websites", websites);
                }
            }
        }
        companyToEsJson.put("crmAccountId", companyId.toString());
        companyToEsJson.put("createdDate", company.getCreatedDate());
        if (company.getCompanyClientLevel() != null) {
            companyToEsJson.put("importance", company.getCompanyClientLevel());
        }
        companyToEsJson.put("industries", company.getIndustries().stream().map(CompanyIndustryRelation::getEnumId).collect(Collectors.toList()));
        companyToEsJson.put("lastModifiedDate", company.getLastModifiedDate());
        companyToEsJson.put("logo", company.getLogo());
        companyToEsJson.put("locations", companyLocationRepository.findAllByAccountCompanyId(company.getId()).stream().map(o -> JSONUtil.toBean(o.getOriginalLoc(), LocationVO.class).setId(o.getId())).collect(Collectors.toList()));
        if (creator != null && enumUserResponsibilityMap.containsKey(ResponsibilityConstants.CREATED_BY)) {
            companyToEsJson.put(getResponsibilityEsKey(enumUserResponsibilityMap.get(ResponsibilityConstants.CREATED_BY).getCompanyEsKey()), Arrays.asList(getUserJson(creator.getId(), CommonUtils.formatFullName(creator.getFirstName(), creator.getLastName()))));
        }
        List<CompanyContact> companyContactList = companyContactRepository.findAllByCompanyIdOrderByIdAsc(company.getId());
        Optional<String> companyPhoneOptional = companyContactList.stream()
                .filter(o -> ContactType.PHONE.toDbValue().equals(o.getType()))
                .map(CompanyContact::getContact)
                .findFirst();

        companyPhoneOptional.ifPresent(companyPhone -> {
            TenantPhoneDTO tenantPhoneDTO = PhoneNumberUtils.parsePhoneNumber(companyPhone);
            String phone = "+" + tenantPhoneDTO.getCountryCode() + " " + tenantPhoneDTO.getPhone();
            companyToEsJson.put("phonesFormatted", phone);
        });
        setCompanyToEsFolders(companyToEsJson, company.getId());
        setCompanyToEsSalesLead(companyToEsJson, company.getId(), enumUserResponsibilityMap);
        setCompanyToEsContract(companyToEsJson, company.getId());
        setCompanyToEsTags(companyToEsJson, companyTagRelationList);
        setCompanyRelationships(companyToEsJson, company.getId());
        return JSONUtil.parseObj(JsonUtil.toJson(companyToEsJson));
    }


    private JSONObject translateCompanyClientNoteEs(CompanyClientNote note) {
        if (note == null) {
            return new JSONObject();
        }

        List<EnumUserResponsibility> enumUserResponsibility = enumCommonService.findAllEnumUserResponsibility();
        Map<String, EnumUserResponsibility> enumUserResponsibilityMap = enumUserResponsibility.stream().filter(o -> EnumStatus.ACTIVE.equals(o.getStatus())).collect(Collectors.toMap(EnumUserResponsibility::getLabel, o -> o));

        List<CompanyClientNoteContactRelation> contactRelationList = companyClientNoteContactRelationRepository.findAllByNoteId(note.getId());

        JSONObject noteObj = new JSONObject();
        if (CollUtil.isNotEmpty(contactRelationList)) {
            noteObj.put("clientContacts", contactRelationList.stream().map(o -> {
                JSONObject contactObj = new JSONObject();
                contactObj.put("clientContactId", String.valueOf(o.getClientContactId()));
                return contactObj;
            }).toList());
        }
        noteObj.put("companyId", String.valueOf(note.getCompanyId()));
        noteObj.put("createdDate", note.getCreatedDate());
        noteObj.put("lastModifiedDate", note.getLastModifiedDate());
        noteObj.put("note", HtmlUtil.unescape(HtmlUtil.cleanHtmlTag(note.getNote())));
        noteObj.put("richTextNote", note.getNote());
        if (note.getContactDate() != null) {
            noteObj.put("lastContactDate", DateUtil.stringToLocalDate(DateUtil.fromInstantToDate(note.getContactDate())));
        }

        JSONObject noteCreator = new JSONObject();
        noteCreator.put("id", note.getPermissionUserId().toString());
        if (enumUserResponsibilityMap.containsKey(ResponsibilityConstants.CREATED_BY)) {
            noteObj.put(getResponsibilityEsKey(enumUserResponsibilityMap.get(ResponsibilityConstants.CREATED_BY).getCompanyEsKey()), Arrays.asList(noteCreator));
        }

        JSONObject noteModifiedUser = new JSONObject();
        if (note.getLastModifiedBy() != null) {
            noteModifiedUser.put("id", note.getLastModifiedBy().split(",")[0]);
        }
        if (enumUserResponsibilityMap.containsKey(ResponsibilityConstants.LAST_MODIFIED_BY)) {
            noteObj.put(getResponsibilityEsKey(enumUserResponsibilityMap.get(ResponsibilityConstants.LAST_MODIFIED_BY).getCompanyEsKey()), Arrays.asList(noteModifiedUser));
        }

        return JSONUtil.parseObj(JsonUtil.toJson(noteObj));
    }

    private JSONObject translateCompanyProgressNoteEs(Long noteId) {
        CompanyProgressNote note = companyProgressNoteRepository.findById(noteId).orElse(null);
        if (note == null) {
            return new JSONObject();
        }

        List<EnumUserResponsibility> enumUserResponsibility = enumCommonService.findAllEnumUserResponsibility();
        Map<String, EnumUserResponsibility> enumUserResponsibilityMap = enumUserResponsibility.stream().filter(o -> EnumStatus.ACTIVE.equals(o.getStatus())).collect(Collectors.toMap(EnumUserResponsibility::getLabel, o -> o));

        List<CompanyProgressNoteContactRelation> contactRelationList = companyProgressNoteContactRelationRepository.findAllByNoteId(note.getId());

        JSONObject noteObj = new JSONObject();
        if (CollUtil.isNotEmpty(contactRelationList)) {
            noteObj.put("clientContacts", contactRelationList.stream().map(o -> {
                JSONObject contactObj = new JSONObject();
                contactObj.put("clientContactId", String.valueOf(o.getClientContactId()));
                return contactObj;
            }).toList());
        }
        noteObj.put("companyId", String.valueOf(note.getCompanyId()));
        noteObj.put("createdDate", note.getCreatedDate());
        noteObj.put("deleted", false);
        noteObj.put("lastModifiedDate", note.getLastModifiedDate());
        noteObj.put("note", HtmlUtil.unescape(HtmlUtil.cleanHtmlTag(note.getNote())));
        if (note.getSalesLeadId() != null) {
            noteObj.put("salesLeadId", note.getSalesLeadId().toString());
        }
        noteObj.put("contactType", note.getContactType());
        noteObj.put("contactTime", DateUtil.fromInstantToUtcDateTime(note.getContactDate()));


        JSONObject noteCreator = new JSONObject();
        noteCreator.put("id", note.getPermissionUserId().toString());
        if (enumUserResponsibilityMap.containsKey(ResponsibilityConstants.CREATED_BY)) {
            noteObj.put(getResponsibilityEsKey(enumUserResponsibilityMap.get(ResponsibilityConstants.CREATED_BY).getCompanyEsKey()), Arrays.asList(noteCreator));
        }

        JSONObject noteModifiedUser = new JSONObject();
        if (note.getLastModifiedBy() != null) {
            noteModifiedUser.put("id", note.getLastModifiedBy().split(",")[0]);
        }
        if (enumUserResponsibilityMap.containsKey(ResponsibilityConstants.LAST_MODIFIED_BY)) {
            noteObj.put(getResponsibilityEsKey(enumUserResponsibilityMap.get(ResponsibilityConstants.LAST_MODIFIED_BY).getCompanyEsKey()), Arrays.asList(noteModifiedUser));
        }

        return JSONUtil.parseObj(JsonUtil.toJson(noteObj));
    }

    private void setCompanyToEsContract(JSONObject companyToEsJson, Long companyId) {
        List<Contract> activeContractList = contractRepository.findAllByAccountCompanyIdAndStatus(companyId, ContractStatus.VALID);
        companyToEsJson.put("contracts", activeContractList.stream().map(o -> {
            JSONObject contract = new JSONObject();
            contract.put("name", o.getName());
            contract.put("endTime", o.getEndDate());
            return contract;
        }).collect(Collectors.toList()));
    }

    private void setCompanyToEsSalesLead(JSONObject companyToEsJson, Long companyId, Map<String, EnumUserResponsibility> enumUserResponsibilityMap) {
        List<AccountBusiness> accountBusinessList = accountBusinessRepository.findAllByAccountCompanyIdAndBusinessProgressIsNot(companyId, BusinessProgress.ACCOUNT_BUSINESS_LOST.toDbValue());
        if (CollUtil.isEmpty(accountBusinessList)) {
            return;
        }
        List<BusinessFlowAdministrator> businessFlowAdministratorList = accountBusinessAdministratorRepository.findAllByAccountBusinessIdInForCrm(accountBusinessList.stream().map(AccountBusiness::getId).collect(Collectors.toList()));
        Map<Long, String> userNameMap = userServiceRepository.findCrmUserNameByIds(businessFlowAdministratorList.stream().map(BusinessFlowAdministrator::getUserId).collect(Collectors.toList())).stream().collect(Collectors.toMap(EntityNameVM::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));
        setCompanyToEsAms(companyToEsJson, businessFlowAdministratorList, userNameMap, enumUserResponsibilityMap);
        setCompanyToEsCOAms(companyToEsJson, businessFlowAdministratorList, userNameMap, enumUserResponsibilityMap);
        setCompanyToEsCOAmsCountry(companyToEsJson, businessFlowAdministratorList, userNameMap);
        setClientCompanyToEsSalesLeadOwner(companyToEsJson, businessFlowAdministratorList, userNameMap, enumUserResponsibilityMap);
        setClientCompanyToEsBdOwner(companyToEsJson, businessFlowAdministratorList, userNameMap, enumUserResponsibilityMap);
        setClientCompanyToEsServiceTypes(companyToEsJson, accountBusinessList);
        List<Contract> companyContractList = contractRepository.findAllByAccountCompanyIdAndPreviousContractIdIsNull(companyId);
        List<ContractBusinessRelation> contractBusinessRelationList = contractBusinessRelationRepository.findAllByContractIdIn(companyContractList.stream().map(Contract::getId).collect(Collectors.toList()));
        Set<Long> existContractSalesLeadIds = contractBusinessRelationList.stream().map(ContractBusinessRelation::getAccountBusinessId).collect(Collectors.toSet());
        Set<Long> relatedSalesLeadContractIds = contractBusinessRelationList.stream().map(ContractBusinessRelation::getContractId).collect(Collectors.toSet());
//            In order to be compatible with the contract data of the transitional version, if the number of contracts with unassociated leads is less than the number of sales leads with unassociated contracts, the company is the contract customer to be uploaded.
        companyToEsJson.put("hasUnsignedSalesLead", companyContractList.stream().filter(o -> !relatedSalesLeadContractIds.contains(o.getId())).count() < accountBusinessList.stream().filter(o -> !existContractSalesLeadIds.contains(o.getId())).count());

    }

    private JSONObject getUserJson(Long userId, String name) {
        JSONObject administratorJson = new JSONObject();
        administratorJson.put("id", userId);
        administratorJson.put("name", name);
        return administratorJson;
    }

    private void setCompanyToEsTags(JSONObject companyToEsJson, List<CompanyTagRelation> companyTagRelationList) {
        if (CollUtil.isEmpty(companyTagRelationList)) {
            return;
        }
        companyToEsJson.put("tenantTags", companyTagRelationList.stream().map(o -> {
            JSONObject tagObj = new JSONObject();
            tagObj.put("content", o.getTag());
            tagObj.put("createdBy", o.getCreatedBy());
            return tagObj;
        }).toList());
    }

    private void setCompanyRelationships(JSONObject companyToEsJson, Long companyId) {
        List<CompanyRelationshipDTO> companyRelationships = fetchAccountRelationshipsAsync(companyId);
        if (companyRelationships == null || companyRelationships.isEmpty()) {
            return;
        }
        Set<Long> ids = companyRelationships.stream().flatMap(item -> Stream.concat(item.getCompanyId().stream(), item.getParentCompanyId().stream()))
                .collect(Collectors.toSet());
        Map<Long, AccountCompanyVO> companyVoMap = fetchAccountCompanyVos(ids).stream().collect(Collectors.toMap(AccountCompanyVO::getId, Function.identity()));
        Function<Long, JSONObject> toEsContent = id -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("companyId", id.toString());
            Optional.ofNullable(companyVoMap.get(id)).ifPresent(vo -> {
                jsonObject.put("companyName", vo.getCompanyName());
                jsonObject.put("displayNameInEn", vo.getDisplayNameInEn());
            });
            return jsonObject;
        };

        List<JSONObject> mspCompanies = new ArrayList<>();
        List<JSONObject> mspAssociatedCompanies = new ArrayList<>();
        List<JSONObject> childCompanies = new ArrayList<>();
        AtomicReference<JSONObject> parentCompanies = new AtomicReference<>(new JSONObject());
        for (CompanyRelationshipDTO relationship : companyRelationships) {
            switch (relationship.getRelation()) {
                case MSP -> {
                    // 当前公司是被 msp 服务的公司
                    if (relationship.getCompanyId().contains(companyId)) {
                        relationship.getParentCompanyId().forEach(id -> mspCompanies.add(toEsContent.apply(id)));
                    }
                    // 当前公司是提供 msp 服务的公司
                    if (relationship.getParentCompanyId().contains(companyId)) {
                        relationship.getCompanyId().forEach(id -> mspAssociatedCompanies.add(toEsContent.apply(id)));
                    }
                }
                case PARENT -> {
                    // 当前公司是子公司
                    if (relationship.getCompanyId().contains(companyId)) {
                        relationship.getParentCompanyId().stream().findFirst().ifPresent(id -> parentCompanies.set(toEsContent.apply(id)));
                    }
                    // 当前公司是母公司
                    if (relationship.getParentCompanyId().contains(companyId)) {
                        relationship.getCompanyId().stream().findFirst().ifPresent(id -> childCompanies.add(toEsContent.apply(id)));
                    }
                }
            }
        }
        companyToEsJson.put("MSPCompanies", mspCompanies);
        companyToEsJson.put("MSPAssociatedCompanies", mspAssociatedCompanies);
        companyToEsJson.put("parentCorporate", parentCompanies.get());
        companyToEsJson.put("childCompanies", childCompanies);

    }

    private List<CompanyRelationshipDTO> fetchAccountRelationshipsAsync(Long accountCompanyId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", TOKEN_TYPE + " " + SecurityUtils.getCurrentUserToken());
        httpHeaders.set("Content-Type", "application/json");
        try {
            HttpResponse response = httpService.get(applicationProperties.getCrmUrl() + getCompanyUrl(accountCompanyId), convertToOkHttpHeaders(httpHeaders));
            if (response == null || 200 != response.getCode()) {
                log.error("inactivate crm user error, response: {}", response);
            }
            CRMAccountCompany bean = JSONUtil.toBean(response.getBody(), CRMAccountCompany.class);
            return bean != null ? bean.getRelationshipList() : new ArrayList<>();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("inactivate crm user error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    // 批量获取公司信息, 因为关联关系可能有开发中和正式客户，所以从 crm 获取
    private List<AccountCompanyVO> fetchAccountCompanyVos(Set<Long> companyIds) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", TOKEN_TYPE + " " + SecurityUtils.getCurrentUserToken());
        httpHeaders.set("Content-Type", "application/json");
        try {
            HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + "/account/api/v1/companies/find-by-ids", convertToOkHttpHeaders(httpHeaders), JSONUtil.toJsonStr(companyIds));
            if (response == null || 200 != response.getCode()) {
                log.error("inactivate crm user error, response: {}", response);
            }
            List<AccountCompanyVO> bean = JSONUtil.toList(JSONUtil.parseArray(response.getBody()), AccountCompanyVO.class);
            return bean != null ? bean : new ArrayList<>();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("inactivate crm user error: {}", e.getMessage());
        }
        return Collections.emptyList();
    }

    private String getCompanyUrl(Long companyId) {
        return String.format("/account/api/v1/companies/%s/basic", companyId);
    }

    private void setCompanyToEsFolders(JSONObject companyToEsJson, Long companyId) {
        List<Long> folderIds = companyCustomFolderConnectClientRepository.findAllFolderIdByCompanyId(companyId);
        companyToEsJson.put("folders", folderIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()));
    }

    private void setCompanyToEsAms(JSONObject companyToEsJson, List<BusinessFlowAdministrator> businessFlowAdministratorList, Map<Long, String> userNameMap, Map<String, EnumUserResponsibility> enumUserResponsibilityMap) {
        if (enumUserResponsibilityMap.containsKey(ResponsibilityConstants.AM)) {
            companyToEsJson.put(getResponsibilityEsKey(enumUserResponsibilityMap.get(ResponsibilityConstants.AM).getCompanyEsKey()), businessFlowAdministratorList.stream().filter(admin -> admin.getSalesLeadRoleType().equals(SalesLeadRoleType.ACCOUNT_MANAGER) && userNameMap.containsKey(admin.getUserId())).map(o -> this.getUserJson(o.getUserId(), userNameMap.get(o.getUserId()))).collect(Collectors.toList()));
        }
    }

    private void setCompanyToEsCOAms(JSONObject companyToEsJson, List<BusinessFlowAdministrator> businessFlowAdministratorList, Map<Long, String> userNameMap, Map<String, EnumUserResponsibility> enumUserResponsibilityMap) {
        if (enumUserResponsibilityMap.containsKey(ResponsibilityConstants.COOPERATE_ACCOUNT_MANAGER)) {
            companyToEsJson.put(getResponsibilityEsKey(enumUserResponsibilityMap.get(ResponsibilityConstants.COOPERATE_ACCOUNT_MANAGER).getCompanyEsKey()), businessFlowAdministratorList.stream().filter(admin -> admin.getSalesLeadRoleType().equals(SalesLeadRoleType.COOPERATE_ACCOUNT_MANAGER) && userNameMap.containsKey(admin.getUserId())).map(o -> this.getUserJson(o.getUserId(), userNameMap.get(o.getUserId()))).collect(Collectors.toList()));
        }
    }

    private void setCompanyToEsCOAmsCountry(JSONObject companyToEsJson, List<BusinessFlowAdministrator> businessFlowAdministratorList, Map<Long, String> userNameMap) {
        Map<Long, List<BusinessFlowAdministrator>> coAmMap = businessFlowAdministratorList.stream().filter(v -> v.getSalesLeadRoleType().equals(SalesLeadRoleType.COOPERATE_ACCOUNT_MANAGER)).collect(Collectors.groupingBy(v -> v.getUserId()));
        if (null != coAmMap && !coAmMap.isEmpty()) {
            List<JSONObject> countryList = new ArrayList<>();
            coAmMap.forEach((k, v) -> {
                JSONObject administratorJson = new JSONObject();
                administratorJson.put("userId", k);
                administratorJson.put("countryIds", v.stream().map(x -> x.getCountry()).collect(Collectors.toList()));
                countryList.add(administratorJson);
            });
            companyToEsJson.put("coAMCountries", countryList);
        }
    }

    private void setClientCompanyToEsSalesLeadOwner(JSONObject companyToEsJson, List<BusinessFlowAdministrator> businessFlowAdministratorList, Map<Long, String> userNameMap, Map<String, EnumUserResponsibility> enumUserResponsibilityMap) {
        if (enumUserResponsibilityMap.containsKey(ResponsibilityConstants.OWNER)) {
            companyToEsJson.put(getResponsibilityEsKey(enumUserResponsibilityMap.get(ResponsibilityConstants.OWNER).getCompanyEsKey()), businessFlowAdministratorList.stream().filter(admin -> admin.getSalesLeadRoleType().equals(SalesLeadRoleType.SALES_LEAD_OWNER) && userNameMap.containsKey(admin.getUserId())).map(o -> this.getUserJson(o.getUserId(), userNameMap.get(o.getUserId()))).collect(Collectors.toList()));
        }
    }

    private void setClientCompanyToEsBdOwner(JSONObject companyToEsJson, List<BusinessFlowAdministrator> businessFlowAdministratorList, Map<Long, String> userNameMap, Map<String, EnumUserResponsibility> enumUserResponsibilityMap) {
        if (enumUserResponsibilityMap.containsKey(ResponsibilityConstants.BD_OWNER)) {
            companyToEsJson.put(getResponsibilityEsKey(enumUserResponsibilityMap.get(ResponsibilityConstants.BD_OWNER).getCompanyEsKey()), businessFlowAdministratorList.stream().filter(admin -> admin.getSalesLeadRoleType().equals(SalesLeadRoleType.BUSINESS_DEVELOPMENT) && userNameMap.containsKey(admin.getUserId())).map(o -> this.getUserJson(o.getUserId(), userNameMap.get(o.getUserId()))).collect(Collectors.toList()));
        }
    }

    private void setClientCompanyToEsServiceTypes(JSONObject companyToEsJson, List<AccountBusiness> accountBusinessList) {
        companyToEsJson.put("serviceTypes", accountBusinessList.stream()
                .flatMap(salesLead -> salesLead.getEnumCompanyServiceTypes().stream())
                .map(EnumCompanyServiceType::getName).distinct()
                .collect(Collectors.toList()));
    }

    private String getResponsibilityEsKey(String esKey) {
        if(esKey.contains(".")) {
            return esKey.substring(esKey.lastIndexOf(".") + 1, esKey.length());
        }
        return esKey;
    }
}
