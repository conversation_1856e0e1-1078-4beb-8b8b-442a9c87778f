package com.altomni.apn.company.domain.folder;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderTypeConverter;
import com.altomni.apn.company.service.dto.folder.CustomFolderDTO;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A CompanyCustomFolder.
 */
@Entity
@Table(name = "company_custom_folder")
@Data
public class CompanyCustomFolder extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Convert(converter = CategoryFolderTypeConverter.class)
    @Column(name = "category")
    private CategoryFolderType category;

    @Column(name = "note")
    private String note;

    public static Set<String> updateSkipProperties = new HashSet<>(Arrays.asList("id", "category"));

    public static CompanyCustomFolder fromCustomFolderDTO(CustomFolderDTO customFolderDTO) {
        CompanyCustomFolder companyCustomFolder = new CompanyCustomFolder();
        ServiceUtils.myCopyProperties(customFolderDTO, companyCustomFolder);
        return companyCustomFolder;
    }

}
