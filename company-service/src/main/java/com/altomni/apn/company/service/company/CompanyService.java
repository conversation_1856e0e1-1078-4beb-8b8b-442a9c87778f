package com.altomni.apn.company.service.company;

import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.dto.company.BriefCompanyDTO;
import com.altomni.apn.common.dto.company.ClientContactCompany;
import com.altomni.apn.common.dto.company.ICompanyTeamUser;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.CompanyContact;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.company.service.dto.CompanyPurchaseOrderDTO;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.company.vo.company.CompanyPurchaseOrderVO;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectSearchVM;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectVM;
import com.altomni.apn.company.web.rest.vm.company.CompanyVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;
import java.util.Map;

public interface CompanyService {

    CompanyDTO getCompany(Long id);

    List<CompanyContact> getCompanyContacts(Long id);

    List<CompanyDTO> findALLByIds(List<Long> ids);

    List<ClientContactCompany> findALLByNames(List<String> names, Long excludeTalentId);

    Page<Company> findAllByTenantId(Pageable pageable);

    List<AccountCompanyVO> findAllByTenantId();

    Integer countCompanyAccountManager(Long talentId, Long userId);

    List<AssignedUserDTO> getApplicationUsers(Long companyId);

    Map<Long, String> getCompanyNamesByJobIds(List<Long> jobIds);

    List<ICompanyTeamUser.CompanyTeamUserDTO> getTeamUsersByCompanyId(Long companyId, Long salesLeadId);

    String getCompanyNameById(Long companyId);

    Page<CompanyProspectVM> searchAllCompaniesForBDReport(Instant fromDate, Instant toDate, CompanyProspectSearchVM searchVM, Pageable pageable);

    List<CompanyVO> findAllByTenantIdAndName(String name);

    List<AccountCompanyVO> getAllClientCompanyList(AccountCompanyStatus active, Integer limit);

    List<BriefCompanyDTO> findBriefCompanyByTenantId(Long tenantId);

    List<CompanyPurchaseOrderVO> getPurchaseOrderList(Long companyId);

    List<CompanyPurchaseOrderVO> getCompanyPurchaseOrderList(List<Long> companyIdList);

    void createCompanyPurchaseOrder(CompanyPurchaseOrderDTO companyPurchaseOrderDTO);

    void modifyCompanyPurchaseOrder(CompanyPurchaseOrderDTO companyPurchaseOrderDTO);

    /**
     * 用于禁猎提醒根据accountCompanyIds查询CRM客户
     * @return
     */
    List<AccountCompanyVO> queryCRMCompanyByIds(List<Long> ids);

}
