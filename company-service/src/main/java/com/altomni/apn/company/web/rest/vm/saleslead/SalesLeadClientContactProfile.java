package com.altomni.apn.company.web.rest.vm.saleslead;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalesLeadClientContactProfile {

    private Long id;

    private String name;

    private String firstName;

    private String lastName;

    private Long companyId;

    private String company;

    private String title;

    private SalesLeadClientContactProfileContactInfoDTO email;

    private SalesLeadClientContactProfileContactInfoDTO phone;

    private SalesLeadClientContactProfileContactInfoDTO wechat;

    private SalesLeadClientContactProfileContactInfoDTO linkedinProfile;

    private Integer contactCategory;

    private String otherCategory;

    private String department;

    private String remark;

    private boolean active = true;

    private String businessGroup;

    private String businessUnit;

    private String address;

    private String address2;

    private String city;

    private String province;

    private String country;

    private String location;

    private Instant lastFollowUpTime;

    private Long tenantId;

    private String esId;

    private Boolean receiveEmail;

    private Long approverId;

    private Boolean inactived;

    private String zipcode;

    private Long companyLocationId;
}
