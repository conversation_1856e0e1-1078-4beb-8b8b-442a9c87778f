package com.altomni.apn.company.vo.company;

import com.altomni.apn.company.domain.enumeration.company.NoPoachingCompanyType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class NoCompanyAffiliatesVO implements Serializable {

    @ApiModelProperty(value = "id for company")
    private String id;

    @ApiModelProperty(value = "full Business Name")
    private String fullBusinessName;

    @ApiModelProperty (value = "display Name In En")
    private String displayNameInEn;

    @ApiModelProperty (value = "company type")
    private NoPoachingCompanyType companyType;

    @ApiModelProperty(value = "no Poaching Parents companies")
    private List<NoPoachingRelationVO> noPoachingParents;


}
