package com.altomni.apn.company.domain.enumeration.company;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The NoPoachingCompanyType enumeration.
 */
public enum NoPoachingCompanyType implements ConvertedEnum<Integer> {
    COMMON_SEARCH(1),
    LEAD(2),
    ACCOUNT(3),
    BUSINESS(4),
    TEXT(5);

    private final Integer dbValue;

    NoPoachingCompanyType(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<NoPoachingCompanyType, Integer> resolver =
            new ReverseEnumResolver<>(NoPoachingCompanyType.class, NoPoachingCompanyType::toDbValue);

    public static NoPoachingCompanyType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
