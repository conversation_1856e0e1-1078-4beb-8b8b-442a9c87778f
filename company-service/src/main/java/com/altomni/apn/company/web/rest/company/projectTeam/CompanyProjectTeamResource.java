package com.altomni.apn.company.web.rest.company.projectTeam;


import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.domain.company.projectTeam.CompanyProjectTeam;
import com.altomni.apn.company.service.company.projectTeam.CompanyProjectTeamService;
import com.altomni.apn.company.service.company.projectTeam.CompanyProjectTeamUserService;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamDTO;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamSearchDTO;
import com.altomni.apn.company.vo.projectTeam.CompanyProjectTeamVO;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Company projectTeam.
 */
@Api(tags = {"APN-Company"})
@RestController
@RequestMapping("/api/v3/company")
public class CompanyProjectTeamResource {

    private final Logger log = LoggerFactory.getLogger(CompanyProjectTeamResource.class);

    private static final String ENTITY_NAME = "Company Project Team";

    private static final String APPLICATION_NAME = "APN V3";

    @Resource
    private CompanyProjectTeamService companyProjectTeamService;

    @Resource
    private CompanyProjectTeamUserService companyProjectTeamUserService;

    /**
     * POST  /project-teams : Create a new projectTeam.
     *
     * @param projectTeam the projectTeam to create
     * @return the ResponseEntity with status 201 (Created) and with body the new projectTeam, or with status 400 (Bad Request) if the projectTeam has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/project-teams")
    @NoRepeatSubmit
    public ResponseEntity<CompanyProjectTeamVO> createProjectTeam(@Valid @RequestBody CompanyProjectTeamDTO projectTeam) throws URISyntaxException {
//        log.info("[APN: ProjectTeam @{}] REST request to create ProjectTeam : {}", SecurityUtils.getUserId(), projectTeam);
        CompanyProjectTeamVO result = companyProjectTeamService.create(projectTeam);
        return ResponseEntity.created(new URI("/api/project-teams/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    /**
     * PUT  /project-teams : Updates an existing projectTeam.
     *
     * @param projectTeam the projectTeam to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated projectTeam,
     * or with status 400 (Bad Request) if the projectTeam is not valid,
     * or with status 500 (Internal Server Error) if the projectTeam couldn't be updated
     */
    @PutMapping("/project-teams/{id}")
    @NoRepeatSubmit
    public ResponseEntity<CompanyProjectTeamVO> updateProjectTeam(@PathVariable Long id, @Valid @RequestBody CompanyProjectTeamDTO projectTeam) throws URISyntaxException {
//        log.info("[APN: ProjectTeam @{}] REST request to update ProjectTeam : {}", SecurityUtils.getUserId(), projectTeam);
        projectTeam.setId(id);
        CompanyProjectTeamVO result = companyProjectTeamService.update(projectTeam);
        return ResponseEntity.created(new URI("/api/project-teams/" + result.getId()))
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, projectTeam.getId().toString()))
                .body(result);
    }


    /**
     * GET  /project-teams : get all the projectTeams.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of projectTeams in body
     */
    @GetMapping("/project-teams")
    public List<CompanyProjectTeamVO> getAllProjectTeams() {
        log.info("REST request to get all ProjectTeams");
        return companyProjectTeamService.getAllByTenantId();
    }

    /**
     * GET  /project-teams/:id : get the "id" projectTeam.
     *
     * @param id the id of the projectTeam to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the projectTeam, or with status 404 (Not Found)
     */
    @GetMapping("/project-teams/{id}")
    public ResponseEntity<CompanyProjectTeamVO> queryProjectTeam(@PathVariable Long id) {
        log.info("[APN: ProjectTeam @{}] REST request to get ProjectTeam {}", SecurityUtils.getUserId(), id);
        CompanyProjectTeamVO projectTeam = companyProjectTeamService.findOne(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(projectTeam));
    }

    /**
     * POST  /project-teams/search : search projectTeam by company id.
     *
     * @param
     * @return the ResponseEntity with status 200 (OK) and with body the projectTeam, or with status 404 (Not Found)
     */
    @PostMapping("/project-teams/search")
    public ResponseEntity<List<CompanyProjectTeamVO>> searchProjectTeamByCompanyId(@Valid @RequestBody CompanyProjectTeamSearchDTO companyProjectTeamSearchDTO, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN: ProjectTeam @{}] REST request to search ProjectTeam by company id: {}", SecurityUtils.getUserId(), companyProjectTeamSearchDTO);
        Page<CompanyProjectTeam> projectTeamPage = companyProjectTeamService.searchProjectTeamByCompanyId(companyProjectTeamSearchDTO, pageable);
        List<CompanyProjectTeamVO> result = companyProjectTeamService.toVo(projectTeamPage.getContent());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(projectTeamPage, ENTITY_NAME);
        return new ResponseEntity<>(result, headers, HttpStatus.OK);
    }

    /**
     * DELETE  /project-teams/:id : delete the "id" projectTeam.
     *
     * @param id the id of the projectTeam to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/project-teams/{id}")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteProjectTeam(@PathVariable Long id) {
        log.info("[APN: ProjectTeam @{}] REST request to delete ProjectTeam {}", SecurityUtils.getUserId(), id);
        companyProjectTeamService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }


    //*****************************Project team user APIs**************************//

    @GetMapping("/project-teams/users/{teamId}")
    public ResponseEntity<List<UserBriefDTO>> getUsersByTeamId(@Valid @PathVariable Long teamId) {
        log.info("[APN: ProjectTeam @{}] REST request to get Users from ProjectTeam {}", SecurityUtils.getUserId(), teamId);
        List<UserBriefDTO> result = companyProjectTeamUserService.getUsersByTeamId(teamId);
        return ResponseEntity.ok(result);
    }
}
