package com.altomni.apn.company.vo.projectTeam;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@AllArgsConstructor
@ApiModel(description = "Vo for company client note")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CompanyProjectTeamVO extends AbstractAuditingEntity implements Serializable {

    @ApiModelProperty(value = "the id for projectTeam.")
    private Long id;

    @ApiModelProperty(value = "the id for company.")
    private Long companyId;

    @ApiModelProperty(value = "the name for projectTeam.")
    private String name;

    @ApiModelProperty(value = "the id for projectTeam leader.")
    private Long leaderUserId;

    @ApiModelProperty(value = "the name for projectTeam leader.")
    private String leaderName;

    @ApiModelProperty(value = "the user for projectTeam.")
    private List<CompanyProjectTeamUserVO> companyProjectTeamUsers;

}
