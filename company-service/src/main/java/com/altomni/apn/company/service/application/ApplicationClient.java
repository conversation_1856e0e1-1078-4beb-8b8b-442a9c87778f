package com.altomni.apn.company.service.application;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.Map;

@Component
@FeignClient(value = "application-service")
public interface ApplicationClient {

    @GetMapping("/application/api/v3/recruitment-processes/my-recruitment-process-ids")
    ResponseEntity<Map<Long, JobType>> getAllMyRecruitmentProcessIds();

    @GetMapping("/application/api/v3/talent-recruitment-processes-count/talentId/{talentId}")
    ResponseEntity<Integer> countTalentRecruitmentProcessByTalentId(@PathVariable("talentId") Long talentId);

}
