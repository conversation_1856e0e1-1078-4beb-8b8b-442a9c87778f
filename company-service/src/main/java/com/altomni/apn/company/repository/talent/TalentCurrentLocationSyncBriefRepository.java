package com.altomni.apn.company.repository.talent;

import com.altomni.apn.company.domain.talent.TalentCurrentLocationSyncBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TalentCurrentLocationSyncBriefRepository extends JpaRepository<TalentCurrentLocationSyncBrief, Long> {

    TalentCurrentLocationSyncBrief findByTalentId(Long talentId);

    List<TalentCurrentLocationSyncBrief> findAllByTalentIdIn(List<Long> talentIds);
}
