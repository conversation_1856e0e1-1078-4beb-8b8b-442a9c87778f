package com.altomni.apn.company.service.dto.note;

import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * date:2023-04-14
 */
@Data
@ApiModel(description = "note search for progress company")
public class CompanyProgressNoteSearchFilterDTO implements Serializable {

    @ApiModelProperty(value = "the id for salesLead.")
    @NotEmpty
    @UniqueElements
    private List<Long> clientContactIds;

    @ApiModelProperty(value = "the id for salesLead.")
    private List<Long> salesLeadIds;

    @UniqueElements
    @ApiModelProperty(value = "the id for company.")
    private List<Long> companyIds;

    @ApiModelProperty(value = "the text for note.")
    public String note;

    @ApiModelProperty(value = "the timezone for note.")
    private String timezone;

    @ApiModelProperty(value = "the language for note.")
    private LanguageEnum language = LanguageEnum.EN;

}
