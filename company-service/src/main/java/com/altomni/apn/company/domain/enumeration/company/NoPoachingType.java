package com.altomni.apn.company.domain.enumeration.company;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The NoPoachingType enumeration.
 */
public enum NoPoachingType implements ConvertedEnum<Integer> {
    NO_RESTRICTION(0),
    NO_COMPANY(1),
    NO_COMPANY_AND_AFFILIATES(2);

    private final Integer dbValue;

    NoPoachingType(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<NoPoachingType, Integer> resolver =
            new ReverseEnumResolver<>(NoPoachingType.class, NoPoachingType::toDbValue);

    public static NoPoachingType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
