package com.altomni.apn.company.repository.company;

import com.altomni.apn.company.domain.company.CompanyPurchaseOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CompanyPurchaseOrderRepository  extends JpaRepository<CompanyPurchaseOrder, Long> {

    List<CompanyPurchaseOrder> findCompanyPurchaseOrderByCompanyId(Long companyId);

    List<CompanyPurchaseOrder> findCompanyPurchaseOrderByCompanyIdIn(List<Long> companyIdList);

    List<CompanyPurchaseOrder> findCompanyPurchaseOrderByCompanyIdAndCurrencyIdAndAm(Long companyId,Long currencyId,Long am);

    List<CompanyPurchaseOrder> findByIdIn(List<Long> idlist);

}