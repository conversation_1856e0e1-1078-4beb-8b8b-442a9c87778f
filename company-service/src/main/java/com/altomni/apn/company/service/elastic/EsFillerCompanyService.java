package com.altomni.apn.company.service.elastic;

import com.altomni.apn.common.dto.http.HttpResponse;

import java.util.Collection;
import java.util.List;

public interface EsFillerCompanyService {

    void updateCompanyName(Long companyId);

    void extractCompanyToMq(Collection<Long> companyIds, int priority);

    void saveNormalizedCompanyInfos(String normalizedCompany);

    void updateCompaniesFolder(List<Long> companyIds, List<String> toFolderNames, List<String> fromFolderNames, Long tenantId);

    void extractCompanyClientNoteToMq(Collection<Long> companyClientNoteIds, int priority);

    void extractCompanyProgressNoteToMq(Collection<Long> companyProgressNoteIds, int priority);

    void saveNormalizedCompanyClientNoteInfos(String normalizedCompanyClientNote);

    void saveNormalizedCompanyProgressNoteInfos(String normalizedCompanyProgressNote);

    HttpResponse splitLocation(String location);

    void extractCompanyContactToHrMq(Collection<Long> ids, int priority);

    void extractCompanyToHrMq(Collection<Long> ids, int priority);

    void deleteCompanyToMq(Collection<Long> companyIds, int priority);
}
