package com.altomni.apn.company.service.elastic.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.enumeration.note.CompanyNoteType;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.company.note.CompanyClientNoteRepository;
import com.altomni.apn.company.repository.company.note.CompanyProgressNoteRepository;
import com.altomni.apn.company.service.dto.es.NoteSearchDTO;
import com.altomni.apn.company.service.elastic.EsCompanyDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("esCompanyDataService")
public class EsCompanyDataServiceImpl implements EsCompanyDataService {

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private HttpService httpService;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private CompanyClientNoteRepository companyClientNoteRepository;

    @Resource
    private CompanyProgressNoteRepository companyProgressNoteRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    private String commonServiceUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/hitalent";
    }

    private String commonServiceCacheUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/cache/company";
    }

    private String commonServiceUnsignedCacheUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/cache/company/with-unsigned-saleslead";
    }

    private String commonServiceCompanyCountUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/get-company-count";
    }

    private String commonServiceNoteUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/hitalent/company/note";
    }

    private String commonServiceCustomerMetricsUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/company/my-customer-metrics";
    }


    @Override
    public HttpResponse searchFromCommonService(SearchGroup searchGroup, Pageable pageable) throws IOException {
        String condition = JSONUtil.toJsonStr(JSONUtil.parse(searchGroup));
        log.info("COMPANY_SEARCH_CONDITION= " + condition);
        if (ObjectUtil.isNull(condition)) {
            return null;
        }
        String url = commonServiceUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = commonServiceUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                Iterator<Sort.Order> sorts = pageable.getSort().iterator();
                if (sorts.hasNext()) {
                    Sort.Order sort = sorts.next();
                    url += "&sort=" + sort.getProperty() + StrUtil.COMMA + sort.getDirection();
                }
            }
        }
        Instant start = Instant.now();
        HttpResponse response = httpService.post(url, condition);
        Instant end = Instant.now();
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsCompanyDataService @{}] search company from common service success, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData = 0;
                if(ModuleType.COMPANY_POOL.getName().equals(searchGroup.getModule())){
                    //search company by tenantId
                    countData = companyRepository.countByTenantId(SecurityUtils.getTenantId());
                }
                if(countData == 0){
                    return new HttpResponse();
                }else{
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: EsCompanyDataService @{}] search company from common service error, searchRequest: {}, pageable:{}, response code: {}, response message: {}", SecurityUtils.getUserId(), condition, pageable, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsCompanyDataService @{}] search company from common service error and response is null, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    @Override
    public HttpResponse searchCacheAndRefreshCache(SearchGroup searchGroup, Pageable pageable) throws IOException {
        String condition = JSONUtil.toJsonStr(JSONUtil.parse(searchGroup));
        log.info("COMPANY_SEARCH_CONDITION [refreshCache]: = " + condition);
        if (ObjectUtil.isNull(condition)) {
            return null;
        }
        String url = commonServiceCacheUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = url + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                Iterator<Sort.Order> sorts = pageable.getSort().iterator();
                if (sorts.hasNext()) {
                    Sort.Order sort = sorts.next();
                    url += "&sort=" + sort.getProperty() + StrUtil.COMMA + sort.getDirection();
                }
            }
        }

        HttpResponse response = httpService.post(url, condition);
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsCompanyDataService @{}] search company from common service and refresh cache success, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData = 0;
                if(ModuleType.COMPANY_POOL.getName().equals(searchGroup.getModule())){
                    //search company by tenantId
                    countData = companyRepository.countByTenantId(SecurityUtils.getTenantId());
                }
                if(countData == 0){
                    return new HttpResponse();
                }else{
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: EsCompanyDataService @{}] search company from common service and refresh cache error, searchRequest: {}, pageable:{}, response code: {}, response message: {}", SecurityUtils.getUserId(), condition, pageable, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsCompanyDataService @{}] search company from common service and refresh cache error and response is null, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    @Override
    public HttpResponse searchCache(String uuid, Pageable pageable) throws IOException {
        log.info("COMPANY_SEARCH_CONDITION [cache]: = " + uuid);
        if (uuid == null) {
            return null;
        }
        String url = commonServiceCacheUrl();
        url += "?uuid=" + uuid;
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = url + "&page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
        }
        HttpResponse response = httpService.get(url);
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsCompanyDataService @{}] search company from common service cache success, searchRequest: {}", SecurityUtils.getUserId(), uuid);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.BAD_REQUEST.value(), response.getCode())) {
                log.error("[APN: EsCompanyDataService @{}] search company from common service cache error, searchRequest: {}, message: {}", SecurityUtils.getUserId(), uuid, response.getBody());
            } else {
                log.error("[APN: EsCompanyDataService @{}] search company from common service cache error, searchRequest: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), uuid, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsCompanyDataService @{}] search company from common service cache error and response is null, searchRequest: {}", SecurityUtils.getUserId(), uuid);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    @Override
    public HttpResponse searchUnsignedCache(String uuid, Pageable pageable) throws IOException {
        log.info("COMPANY_SEARCH_CONDITION [unsigned cache]: = " + uuid);
        if (uuid == null) {
            return null;
        }
        String url = commonServiceUnsignedCacheUrl();
        url += "?uuid=" + uuid;
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = url + "&page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
        }
        HttpResponse response = httpService.get(url);
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsCompanyDataService @{}] search unsigned company from common service cache success, searchRequest: {}", SecurityUtils.getUserId(), uuid);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.BAD_REQUEST.value(), response.getCode())) {
                log.error("[APN: EsCompanyDataService @{}] search unsigned company from common service cache error, searchRequest: {}, message: {}", SecurityUtils.getUserId(), uuid, response.getBody());
            } else {
                log.error("[APN: EsCompanyDataService @{}] search unsigned company from common service cache error, searchRequest: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), uuid, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsCompanyDataService @{}] search unsigned company from common service cache error and response is null, searchRequest: {}", SecurityUtils.getUserId(), uuid);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    @Override
    public HttpResponse searchCompanyCount() throws IOException {
        log.info("COMPANY_SEARCH_CONDITION: userId: {}, tenantId: {}", SecurityUtils.getUserId(), SecurityUtils.getTenantId());

        String url = commonServiceCompanyCountUrl();
        JSONObject body = new JSONObject();
        body.put("userId", SecurityUtils.getUserId());
        body.put("index", "companies_" + SecurityUtils.getTenantId());

        HttpResponse response = httpService.post(url, JSONUtil.toJsonStr(body));
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsCompanyDataService @{}] get company count from common service success.", SecurityUtils.getUserId());
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else {
                log.error("[APN: EsCompanyDataService @{}] get company count from common service error, response code: {}, response message: {}", SecurityUtils.getUserId(), response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsCompanyDataService @{}] get company count from common service error and response is null.", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SEARCHCOMPANYCOUNT_RESPONSEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return response;

    }

    @Override
    public HttpResponse searchCompanyNote(NoteSearchDTO noteSearchDTO, Pageable pageable) throws IOException {
        if (CollUtil.isEmpty(noteSearchDTO.getCompanyIds())) {
            return null;
        }

        String url = commonServiceNoteUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = commonServiceNoteUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                Iterator<Sort.Order> sorts = pageable.getSort().iterator();
                if (sorts.hasNext()) {
                    Sort.Order sort = sorts.next();
                    url += "&sort=" + sort.getProperty() + StrUtil.COMMA + sort.getDirection();
                }
            }
        }
        HttpResponse response = httpService.post(url, JSONUtil.toJsonStr(noteSearchDTO));
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsCompanyDataService @{}] search company note from common service success. company: {}", SecurityUtils.getUserId(), noteSearchDTO.getCompanyIds());
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData;
                if (noteSearchDTO.getModule().equals(CompanyNoteType.CLIENT_NOTE)) {
                    countData = companyClientNoteRepository.countByCompanyId(noteSearchDTO.getCompanyIds().get(0));
                } else {
                    countData = companyProgressNoteRepository.countByCompanyId(noteSearchDTO.getCompanyIds().get(0));
                }
                if(countData == 0){
                    return new HttpResponse();
                }else{
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: EsCompanyDataService @{}] search company note from common serviceerror, response code: {}, response message: {}", SecurityUtils.getUserId(), response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsCompanyDataService @{}] search company note from common service error and response is null.", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SEARCHCOMPANYNOTE_RESPONSEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return response;
    }

    @Override
    public HttpResponse searchCustomerMetric(Instant startTime, Instant endTime, List<Long> userIdList) throws IOException {
        String url = commonServiceCustomerMetricsUrl();
        log.info("customerMetric: userId: {}, tenantId: {}, crmUserId:{} ", SecurityUtils.getUserId(), SecurityUtils.getTenantId(), userIdList);
        JSONObject body = new JSONObject();
        body.put("userIds", userIdList.isEmpty() ? List.of(-1L) : userIdList);
        body.put("index", "companies_" + SecurityUtils.getTenantId());
        Map<String, Object> dateRange = new HashMap<>(16);
        dateRange.put("lte", endTime);
        dateRange.put("gte", startTime);
        body.put("dateRange", dateRange);
        HttpResponse response = httpService.post(url, JSONUtil.toJsonStr(body));
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: EsCompanyDataService @{}] get customer metric from common service success.", SecurityUtils.getUserId());
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else {
                log.error("[APN: EsCompanyDataService @{}] get customer metric from common service error, response code: {}, response message: {}", SecurityUtils.getUserId(), response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[APN: EsCompanyDataService @{}] get customer metric from common service error and response is null.", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SEARCHCUSTOMERMETRIC_RESPONSEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return response;
    }

}
