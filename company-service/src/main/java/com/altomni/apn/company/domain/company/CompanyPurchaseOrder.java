package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Entity
@Data
@Table(name = "company_purchase_order")
@NoArgsConstructor
@AllArgsConstructor
public class CompanyPurchaseOrder extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id ;

    /** 币种id */
    @ApiModelProperty(name = "币种id")
    @Column(name = "currency_id")
    private Long currencyId ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "notification_balance")
    private BigDecimal notificationBalance ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "am")
    private Long am ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "company_id")
    private Long companyId ;

    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "purchase_order_id")
    private List<CompanyPurchaseOrderDetail> purchaseOrderDetailList;
}