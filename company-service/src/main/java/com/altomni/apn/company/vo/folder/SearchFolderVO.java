package com.altomni.apn.company.vo.folder;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.enumeration.folder.FolderPermission;
import com.altomni.apn.company.domain.folder.CompanySearchFolder;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* date:2023-04-19
*/
@AllArgsConstructor
@ApiModel(description = "Vo for categoryFolder")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class SearchFolderVO extends AbstractAuditingEntity implements Serializable {

    @ApiModelProperty(value = "The id for searchFolder.")
    private Long id;

    @ApiModelProperty(value = "The name for searchFolder.")
    private String name;

    @ApiModelProperty(value = "The searchCriteria for searchFolder.")
    private String searchCriteria;

    @ApiModelProperty(value = "The searchCriteria for searchFolder.")
    private CategoryFolderType searchCategory;

    @ApiModelProperty(value = "The param folder active for searchFolder.")
    private Boolean paramFolderActive;

    @ApiModelProperty(value = "The paramFolderPermission for searchFolder.")
    private FolderPermission paramFolderPermission;

    @ApiModelProperty(value = "The paramFolderCreatorId for searchFolder.")
    private Long paramFolderCreatorId;

    @ApiModelProperty(value = "The paramFolderCreatorName for searchFolder.")
    private String paramFolderCreatorName;

//    @JsonIgnore
//    private Long paramFolderId;

    @ApiModelProperty(value = "The id for custom folder.")
    private Long companyFolderId;

    public static SearchFolderVO fromCompanySearchFolder(CompanySearchFolder companySearchFolder) {
        SearchFolderVO searchFolderVO = new SearchFolderVO();
        ServiceUtils.myCopyProperties(companySearchFolder, searchFolderVO);
        return searchFolderVO;
    }

    public static List<SearchFolderVO> toVo(List<CompanySearchFolder> folderList) {
        return folderList.stream().map(SearchFolderVO::fromCompanySearchFolder).collect(Collectors.toList());
    }

}
