package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CompanyContactTypeConverter extends AbstractAttributeConverter<CompanyContactType, Integer> {
    public CompanyContactTypeConverter() {
        super(CompanyContactType::toDbValue, CompanyContactType::fromDbValue);
    }
}
