package com.altomni.apn.company.vo.company;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class TagVO implements Serializable {

    @ApiModelProperty(value = "id for tag")
    private Long id;

    @ApiModelProperty(value = "content for tag")
    private String content;

    @ApiModelProperty(value = "lastUseDate for tag")
    private Instant lastUseDate;

    @ApiModelProperty(value = "createdBy for tag")
    private String createdBy;

    @ApiModelProperty(value = "createdBy for company tag")
    private String companyTagCreatedBy;

    @ApiModelProperty(value = "tag for contact")
    private String tag;

    @ApiModelProperty(value = "userid for contact")
    private Long userId;

    @ApiModelProperty(value = "true: can be deleted, false: cannot be deleted")
    private Boolean deletable;

    public TagVO(String content, String companyTagCreatedBy) {
        this.content = content;
        this.companyTagCreatedBy = companyTagCreatedBy;
    }

    public TagVO(String tag, Long userId, Boolean deletable) {
        this.tag = tag;
        this.userId = userId;
        this.deletable = deletable;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TagVO tagVO = (TagVO) o;
        return Objects.equals(tag, tagVO.tag);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tag);
    }
}
