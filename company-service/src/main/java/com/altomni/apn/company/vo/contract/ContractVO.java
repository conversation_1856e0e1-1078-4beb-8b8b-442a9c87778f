package com.altomni.apn.company.vo.contract;

import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.contract.Contract;
import com.altomni.apn.company.domain.enumeration.contract.ContractStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.UniqueElements;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * A VO for the {@link Contract} entity.
 */
@AllArgsConstructor
@ApiModel(description = "Vo for contract")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class ContractVO implements Serializable {

    @ApiModelProperty(value = "the id for contract.")
    private Long id;

    @ApiModelProperty(value = "the id for company.")
    private Long accountCompanyId;

//    @ApiModelProperty(value = "the type for contract.")
//    private ContractType contractType;
    @ApiModelProperty(value = "The companyServiceTypes for salesLead.")
    private List<Integer> serviceTypes;

    @ApiModelProperty(value = "the fileName for contract.")
    private String fileName;

    @ApiModelProperty(value = "the name for contract.")
    private String name;

    @ApiModelProperty(value = "the note for contract.")
    private String note;

    @ApiModelProperty(value = "the s3Key for contract.")
    private String s3Key;

    @ApiModelProperty(value = "the signers for contract.")
    private Set<SignerVO> signers;

    @ApiModelProperty(value = "the uploadDate for contract.")
    private LocalDate uploadDate;

    @ApiModelProperty(value = "the startDate for contract.")
    private Instant startDate;

    @ApiModelProperty(value = "the endDate for contract.")
    private Instant endDate;

    @ApiModelProperty(value = "the status for contract.")
    private ContractStatus status;

    @ApiModelProperty(value = "the id for previousContract.")
    private Long previousContractId;

    @ApiModelProperty(value = "the ids for salesLead.")
    private List<Long> accountBusinessIds;

    public static ContractVO fromContract(Contract contract) {
        ContractVO contractVO = new ContractVO();
        ServiceUtils.myCopyProperties(contract, contractVO);
//        contractVO.setServiceTypes(contract.getEnumCompanyServiceTypes().stream().map(EnumCompanyServiceType::getId).collect(Collectors.toList()));
        return contractVO;
    }

}
