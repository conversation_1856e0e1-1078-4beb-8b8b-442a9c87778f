package com.altomni.apn.company.domain.user;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.permission.PermissionTeamSimple;
import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.domain.user.UserSecurityInterface;
import com.altomni.apn.common.dto.CredentialDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;

/**
 * A user.
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "user")
@Deprecated
public class UserCompanyMigrateBrief extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "[Internal] uid is used for Spring security to identify user internally. It has the format of <userId>,<tenantId>")
    @Column(name = "uid")
    private String uid;

    @ApiModelProperty(value = "Optional username. If exists, need to be unique.")
    @Pattern(regexp = Constants.USERNAME_REGEX)
    @Size(min = 1, max = 50,message = "username length can not be exceed 50 characters")
    @Column(length = 50, unique = true)
    private String username;

    @ApiModelProperty(value = "hashed password")
    @JsonIgnore
    @NotNull
    @Size(min = 60, max = 60,message ="password length can not be exceed 60 characters" )
    @Column(name = "password_hash",length = 60)
    private String password;

    @ApiModelProperty(value = "first name")
    @Size(max = 50)
    @Column(name = "first_name", length = 50)
    private String firstName;

    @ApiModelProperty(value = "last name")
    @Size(max = 50)
    @Column(name = "last_name", length = 50)
    private String lastName;

    @ApiModelProperty(value = "email address. need to be unique.")
    @org.hibernate.validator.constraints.Email(message = "email format is not correct")
    @Size(min = 5, max = 100)
    @Column(length = 100, unique = true)
    private String email;

    @ApiModelProperty(value = "Whether user is activated. Default is true. Read Only.")
    @NotNull
    @Column(nullable = false)
    private boolean activated = true;

    @ApiModelProperty(value = "Preferred language. e.g. en-US, zh-CN")
    @Size(min = 2, max = 5)
    @Column(name = "lang_key", length = 5)
    private String langKey;

    @ApiModelProperty(value = "url link to user's image")
    @Size(max = 256)
    @Column(name = "image_url", length = 256)
    private String imageUrl;

    @Size(max = 20)
    @Column(name = "activation_key", length = 20)
    @JsonIgnore
    private String activationKey;

    @Size(max = 20)
    @Column(name = "reset_key", length = 20)
    @JsonIgnore
    private String resetKey;

    @Column(name = "reset_date")
    private Instant resetDate = null;

    @ApiModelProperty(value = "credit")
    @Column(name = "credit")
    private Integer monthlyCredit = 0;

    @ApiModelProperty(value = "bulk credit")
    @Column(name = "bulk_credit")
    private Integer bulkCredit = 0;

    @ApiModelProperty(value = "phone number, this is used for consumer registered with phone.")
    @Column(name = "phone")
    private String phone;

    @ApiModelProperty(value = "The tenant user belongs to. Read Only.")
    @ManyToOne
    @JoinColumn(name = "tenant_id", updatable = false, insertable = false)
    private Tenant tenant;

    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @ApiModelProperty(value = "Note info.")
    @Column(name = "note")
    private String note;

    @ApiModelProperty(value = "job title for user.")
    @Column(name = "job_title")
    private String jobTitle;

    @ApiModelProperty(value = "used bulk credit")
    @Transient
    @JsonProperty
    public Integer usedBulkCredit ;

    @Column(name = "data_scope")
    private Integer dataScope;

    private Long teamId;

}
