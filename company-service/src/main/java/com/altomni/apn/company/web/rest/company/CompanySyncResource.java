package com.altomni.apn.company.web.rest.company;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.service.company.CompanyMigrateService;
import com.altomni.apn.company.service.company.CompanySyncService;
import com.altomni.apn.company.web.rest.vm.company.CompanySyncToMqVM;
import com.altomni.apn.company.web.rest.vm.company.MqMessageCountVM;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@Api(tags = {"company"})
@RestController
@RequestMapping("/api/v3")
public class CompanySyncResource {

    @Resource
    private CompanySyncService companySyncService;

    @Resource
    private CompanyMigrateService companyMigrateService;

    @PostMapping("/xxl-job/sync-am-report-send-email")
    public ResponseEntity<Void> sendEmailAmReport() {
        log.info("[XXL-JOB] sendEmailAmReport!");
        companySyncService.sendEmailAmReport();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/canal/check-company-mq-message-count")
    public ResponseEntity<MqMessageCountVM> checkCompanyMqMessageCount() {
        log.debug("[Canal] checkCompanyMqMessageCount!");
        return ResponseEntity.ok(companySyncService.checkCompanyMqMessageCount());
    }

    @PostMapping("/canal/sync-companies-to-mq")
    public ResponseEntity<Void> syncCompaniesToMQ(@RequestBody CompanySyncToMqVM companySyncToMqVM) {
//        log.info("[EsFillerCompanyService: syncCompanyToMQ @{}] syncCompaniesToMQ: {}", SecurityUtils.getUserId(), companySyncToMqVM);
        companySyncService.syncCompaniesToMQ(companySyncToMqVM.getCompanyIds(), companySyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/canal/check-company-client-note-mq-message-count")
    public ResponseEntity<MqMessageCountVM> checkCompanyClientNoteMqMessageCount() {
        log.debug("[Canal] checkCompanyClientNoteMqMessageCount!");
        return ResponseEntity.ok(companySyncService.checkCompanyClientNoteMqMessageCount());
    }

    @PostMapping("/canal/sync-company-client-note-to-mq")
    public ResponseEntity<Void> syncCompanyClientNoteToMQ(@RequestBody CompanySyncToMqVM companySyncToMqVM) {
//        log.info("[EsFillerCompanyService: syncCompanyClientNoteToMQ @{}] syncCompanyClientNoteToMQ: {}", SecurityUtils.getUserId(), companySyncToMqVM);
        companySyncService.syncCompanyClientNoteToMQ(companySyncToMqVM.getCompanyClientNoteIds(), companySyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/canal/check-company-progress-note-mq-message-count")
    public ResponseEntity<MqMessageCountVM> checkCompanyProgressNoteMqMessageCount() {
        log.info("[Canal] checkCompanyProgressNoteMqMessageCount!");
        return ResponseEntity.ok(companySyncService.checkCompanyProgressNoteMqMessageCount());
    }

    @PostMapping("/canal/sync-company-progress-note-to-mq")
    public ResponseEntity<Void> syncCompanyProgressNoteToMQ(@RequestBody CompanySyncToMqVM companySyncToMqVM) {
//        log.info("[EsFillerCompanyService: syncCompanyProgressNoteToMQ @{}] syncCompanyProgressNoteToMQ: {}", SecurityUtils.getUserId(), companySyncToMqVM);
        companySyncService.syncCompanyProgressNoteToMQ(companySyncToMqVM.getCompanyProgressNoteIds(), companySyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    /**
     * 仅合并重复的商机，后续会删除
      * @return
     */
    @GetMapping("/merge-duplicate-business")
    public ResponseEntity<HttpStatus> mergeDuplicateBusiness(@RequestHeader HttpHeaders headers) {
        log.info("[CompanySyncService: mergeDuplicateBusiness @{}] mergeDuplicateBusiness", SecurityUtils.getUserId());
        companyMigrateService.mergeDuplicateBusiness(SecurityContextHolder.getContext(), headers);
        return ResponseEntity.accepted().build();
    }

    @PostMapping("/canal/check-company-contact-hr-mq-message-count")
    public ResponseEntity<MqMessageCountVM> checkCompanyContactMqMessageCount() {
        log.info("[Canal] checkCompanyContactMqMessageCount!");
        return ResponseEntity.ok(companySyncService.checkCompanyContactMqMessageCount());
    }

    @PostMapping("/canal/sync-company-contact-to-hr-mq")
    public ResponseEntity<Void> syncCompanyContactToMQ(@RequestBody CompanySyncToMqVM companySyncToMqVM) {
//        log.info("[EsFillerCompanyService: syncCompanyProgressNoteToMQ @{}] syncCompanyProgressNoteToMQ: {}", SecurityUtils.getUserId(), companySyncToMqVM);
        companySyncService.syncCompanyContactToMQ(companySyncToMqVM.getContactIds(), companySyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/canal/sync-company-to-hr-mq")
    public ResponseEntity<Void> syncCompanyToHrMQ(@RequestBody CompanySyncToMqVM companySyncToMqVM) {
//        log.info("[EsFillerCompanyService: syncCompanyProgressNoteToMQ @{}] syncCompanyProgressNoteToMQ: {}", SecurityUtils.getUserId(), companySyncToMqVM);
        companySyncService.syncCompanyToHrMQ(companySyncToMqVM.getCompanyIds(), companySyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

}
