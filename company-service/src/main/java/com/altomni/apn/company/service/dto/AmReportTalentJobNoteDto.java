package com.altomni.apn.company.service.dto;

import com.altomni.apn.company.domain.enumeration.job.NodeType;
import com.altomni.apn.company.domain.enumeration.job.NodeTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AmReportTalentJobNoteDto implements Serializable {

    private Long id;

    private Long talentId;

    private Long jobId;

    @Convert(converter = NodeTypeConverter.class)
    private NodeType nodeType;

    private String highLightedExperience;

    private String amUpdate;

    private Integer frequency;

}
