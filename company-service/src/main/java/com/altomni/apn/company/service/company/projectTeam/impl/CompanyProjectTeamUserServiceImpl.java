package com.altomni.apn.company.service.company.projectTeam.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.company.projectTeam.CompanyProjectTeam;
import com.altomni.apn.company.domain.company.projectTeam.CompanyProjectTeamUser;
import com.altomni.apn.company.repository.company.projectTeam.CompanyProjectTeamRepository;
import com.altomni.apn.company.repository.company.projectTeam.CompanyProjectTeamUserRepository;
import com.altomni.apn.company.service.company.projectTeam.CompanyProjectTeamUserService;
import com.altomni.apn.company.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* date:2023-04-18
*/
@Service
@Slf4j
public class CompanyProjectTeamUserServiceImpl implements CompanyProjectTeamUserService {

    @Resource
    private CompanyProjectTeamRepository companyProjectTeamRepository;
    
    @Resource
    private CompanyProjectTeamUserRepository companyProjectTeamUserRepository;

    @Resource
    private UserService userService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    private void checkTeamPermission(Long teamId) {
        Optional<CompanyProjectTeam> teamOptional = companyProjectTeamRepository.findById(teamId);
        if (teamOptional.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.PROJECT_CHECKTEAMPERMISSION_NOTFOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(teamId),companyApiPromptProperties.getCompanyService()));
        }
        CompanyProjectTeam team = teamOptional.get();
        if (!team.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.PROJECT_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
    }

    @Override
    public List<UserBriefDTO> getUsersByTeamId(Long teamId) {
        checkTeamPermission(teamId);
        List<UserBriefDTO> result = new ArrayList<>();
        List<CompanyProjectTeamUser> projectTeamUserList = companyProjectTeamUserRepository.findAllByTeamId(teamId);
        if (ObjectUtil.isNotEmpty(projectTeamUserList)) {
            Set<Long> ids = projectTeamUserList.stream().map(CompanyProjectTeamUser::getUserId).collect(Collectors.toSet());
            return userService.getAllByIdIn(new ArrayList<>(ids)).getBody();
        }
        return result;
    }

}
