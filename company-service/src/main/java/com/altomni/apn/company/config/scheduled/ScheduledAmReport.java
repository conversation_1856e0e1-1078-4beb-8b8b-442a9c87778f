//package com.altomni.apn.company.config.scheduled;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.thread.ThreadUtil;
//import cn.hutool.core.util.ObjectUtil;
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONUtil;
//import com.altomni.apn.common.utils.CommonUtils;
//import com.altomni.apn.common.utils.LoginUtil;
//import com.altomni.apn.company.domain.company.CompanyDetail;
//import com.altomni.apn.company.domain.vo.CompanyVo;
//import com.altomni.apn.company.repository.company.CompanyDetailRepository;
//import com.altomni.apn.company.service.am.AmReportService;
//import com.altomni.apn.company.service.auth.AuthorityService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.exception.ExceptionUtils;
//import org.redisson.api.RBucket;
//import org.redisson.api.RLock;
//import org.redisson.api.RedissonClient;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.LinkedBlockingQueue;
//import java.util.concurrent.ThreadPoolExecutor;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
///**
// * am report scheduled component
// * <AUTHOR>
// */
//@Slf4j
//@RefreshScope
//@Component
//public class ScheduledAmReport {
//
//    @Resource
//    private ScheduledProperties scheduledProperties;
//
//    @Resource
//    private AuthorityService authorityService;
//
//    @Resource
//    private AmReportService amReportService;
//
//    @Resource
//    private CompanyDetailRepository companyDetailRepository;
//
//    @Resource
//    private RedissonClient redissonClient;
//
//    public static final ExecutorService executorService = new ThreadPoolExecutor(
//            Runtime.getRuntime().availableProcessors(),
//            Runtime.getRuntime().availableProcessors() * 2,
//            60L,
//            TimeUnit.SECONDS,
//            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-scheduled-sendEmail-AmReport", false));
//
//    @Scheduled(cron = "0 0 0/1 * * ?")
//    public void scheduledSendEmailAmReport() {
//        log.info("[ScheduledAmReport: scheduledSendEmailAmReport @-1] Scheduled start: {}, properties : {}", LocalDateTime.now(), JSONUtil.toJsonStr(scheduledProperties));
//        LoginUtil.simulateLogin(scheduledProperties.getUsername(), scheduledProperties.getPassword(), authorityService::findCredential);
//        String mailTemplate = CommonUtils.readFileToString("templates/am-report-mail.html");
//        String imageTemplate = CommonUtils.readFileToString("templates/am-report.html");
//        if (mailTemplate == null || imageTemplate == null) {
//            return;
//        }
//        int page = 0;
//        int size = 500;
//        log.info("tenantIds ======================= " + scheduledProperties.getTenantIds());
//        List<Long> tenantIdList = checkTenantIds();
//        boolean breakFlag = false;
//        while (true) {
//            RLock rLock = null;
//            List<CompanyVo> companyVoList = new ArrayList<>();
//            try {
//                //lock to get offset
//                rLock = redissonClient.getLock("am-report");
//                rLock.lock(1, TimeUnit.MINUTES);
//                RBucket<Integer> rBucket = redissonClient.getBucket("am-report-offset");
//                if (ObjectUtil.isNotNull(rBucket.get())) {
//                    page = rBucket.get();
//                }
//                companyVoList = companyDetailRepository.findCompanyByPageAndTenantIds(tenantIdList, PageRequest.of(page, size));
//                if (CollUtil.isEmpty(companyVoList)) {
//                    breakFlag = true;
//                }
//                rBucket.set(++page);
//            } catch (Exception e) {
//                breakFlag = true;
//                log.error("[apn] am report search company is error, message = [{}]", ExceptionUtils.getStackTrace(e));
//            } finally {
//                if (rLock != null) {
//                    rLock.unlock();
//                }
//            }
//            if (breakFlag) {
//                //Abnormal end
//                break;
//            }
//            companyVoList.forEach(vo -> {
//                CompanyDetail company = new CompanyDetail();
//                BeanUtil.copyProperties(vo, company);
//                executorService.execute(() -> amReportService.sendAmReportAmEmail(company, mailTemplate, imageTemplate));
//            });
//        }
//        log.info("[ScheduledAmReport: scheduledSendEmailAmReport @-1] Scheduled end: {}", LocalDateTime.now());
//    }
//
//    private List<Long> checkTenantIds() {
//        String tenantIds = "4";
//        if (StrUtil.isNotBlank(scheduledProperties.getTenantIds())) {
//            tenantIds = scheduledProperties.getTenantIds();
//        }
//        return Arrays.stream(tenantIds.split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
//    }
//
//}
//
