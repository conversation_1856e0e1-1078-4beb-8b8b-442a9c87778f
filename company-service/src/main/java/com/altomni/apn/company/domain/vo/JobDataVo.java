package com.altomni.apn.company.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.company.domain.enumeration.job.NodeType;
import com.altomni.apn.company.domain.enumeration.job.NodeTypeConverter;
import com.altomni.apn.company.domain.enumeration.job.NodeTypeExcelConverter;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Accessors(chain = true)
public class JobDataVo implements Serializable, AttachConfidentialTalent {

    @ColumnWidth(20)
    @ExcelProperty(value = "Candidate Name",index = 0)
    private String fullName;

    @ColumnWidth(20)
    @ExcelProperty(value = "Applied Job Title",index = 1)
    private String jobTitle;

    @ExcelIgnore
    private Long candidateId;

    @ExcelIgnore
    private JobType jobType;

    @ExcelIgnore
    private JobStatus jobStatus;

    @ExcelIgnore
    private Long pteamId;

    @ExcelIgnore
    private Long jobId;

    @Id
    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private Long applicationId;

    @ExcelIgnore
    private Instant submitTime;

    @Transient
    @ColumnWidth(30)
    @ExcelProperty(value = "Submitted Date",index = 4)
    private String submitTimeStr;

    @ColumnWidth(20)
    @Convert(converter = NodeTypeConverter.class)
    @ExcelProperty(value = "Current Status",index = 2, converter = NodeTypeExcelConverter.class)
    private NodeType nodeType;

    @ExcelIgnore
    private Integer nodeStatus;

    @ExcelIgnore
    private Instant activityUpdateDate;

    @ExcelIgnore
    private Instant latestInterviewDate;

    @ExcelIgnore
    private Integer progress;

    @ExcelIgnore
    private Integer interviewType;

    @Transient
    @ColumnWidth(25)
    @ExcelProperty(value = "Interview Info",index = 3)
    private String interviewInfo;

    @Transient
    @ColumnWidth(15)
    @ExcelProperty(value = "Aging Days",index = 5)
    private Integer agingDays;

    @ColumnWidth(25)
    @ExcelProperty(value = "Highlighted Experience",index = 6)
    private String highLightedExperience;

    @ExcelProperty(value = "AM Updates",index = 7)
    private String amUpdate;

    @ExcelIgnore
    @Transient
    private String hrContact;

    @ExcelIgnore
    @Transient
    private Long hrContactId;

    @ExcelIgnore
    private String timeZone;

    @ExcelIgnore
    @Transient
    private String eventType;

    @ExcelIgnore
    @Transient
    private String eventStage;

    @Transient
    @ExcelIgnore
    private Boolean isPrivateJob;

    @ExcelIgnore
    private Integer interviewCount;

    /**
     * true: 已离职， false: 未离职
     */
    @ExcelIgnore
    private Boolean resigned;

    /**
     * true: 是 converted to FTE 流程
     * false: 非 converted to FTE 流程
     */
    @ExcelIgnore
    private Boolean convertedToFte;

    @Transient
    @ExcelIgnore
    private Boolean confidentialTalentViewAble;

    @Transient
    @ExcelIgnore
    private ConfidentialInfoDto confidentialInfo;

    @Override
    public Long getTalentId() {
        return candidateId;
    }

    @Override
    public void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble) {
        this.confidentialTalentViewAble = confidentialTalentViewAble;
    }

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        this.confidentialInfo = confidentialInfo;
    }


    @Override
    public void encrypt() {
        this.fullName = null;
        this.jobTitle = null;
        this.jobType = null;
        this.jobStatus = null;
        this.jobId = null;
        this.pteamId = null;
        this.interviewInfo = null;
        this.highLightedExperience = null;
        this.amUpdate = null;
        this.hrContact = null;
        this.timeZone = null;
        this.eventType = null;
        this.eventStage = null;
        this.isPrivateJob = null;
        this.resigned = null;
        this.convertedToFte = null;
    }


}
