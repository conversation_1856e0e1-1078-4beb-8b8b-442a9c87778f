package com.altomni.apn.company.service.dto.contact;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "dto for company contact update status")
public class CompanyContactStatusDTO implements Serializable {

    @ApiModelProperty(value = "The id for contact.")
    @NotNull
    private Long id;

    @ApiModelProperty(value = "The active for contact.")
    @NotNull
    private Boolean active;

}
