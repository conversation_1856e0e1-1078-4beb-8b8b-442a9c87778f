package com.altomni.apn.company.domain.talent;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.ManualAbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.company.lcoation.CompanyLocation;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A TalentCurrentLocation.
 */
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "talent_current_location")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentCurrentLocationSyncBrief extends ManualAbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "official_county")
    private String officialCounty;

    @Column(name = "official_country")
    private String officialCountry;

    @Column(name = "official_province")
    private String officialProvince;

    @Column(name = "official_city")
    private String officialCity;

    @Column(name = "original_loc")
    private String originalLoc;

    @Column(name = "zip_code")
    private String zipCode;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "talentId"));

    public TalentCurrentLocationSyncBrief setTalentId(Long talentId) {
        this.talentId = talentId;
        return this;
    }

    public static TalentCurrentLocationSyncBrief fromCompanyLocation(CompanyLocation companyLocation) {
        TalentCurrentLocationSyncBrief talentCurrentLocationCompanyBrief = new TalentCurrentLocationSyncBrief();
        ServiceUtils.myCopyProperties(companyLocation, talentCurrentLocationCompanyBrief);
        return talentCurrentLocationCompanyBrief;
    }

    public TalentCurrentLocationSyncBrief(Long talentId, String originalLoc) {
        this.talentId = talentId;
        this.originalLoc = originalLoc;
    }
}
