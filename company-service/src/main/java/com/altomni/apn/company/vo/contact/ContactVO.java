package com.altomni.apn.company.vo.contact;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;


@AllArgsConstructor
@ApiModel(description = "Vo for  contact")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class ContactVO implements Serializable {

    @ApiModelProperty(value = "id for contact")
    private Long id;

    @ApiModelProperty(value = "firstName for contact")
    private String firstName;

    @ApiModelProperty(value = "lastName for contact")
    private String lastName;

    @ApiModelProperty(value = "fullName for contact")
    private String fullName;

    @ApiModelProperty(value = "id for company")
    private List<Long> accountCompanyId;


    @ApiModelProperty(value = "name for company")
    private String companyName;


    @ApiModelProperty(value = "lastFollowUpTime for contact")
    private Instant lastFollowUpTime;

//    @ApiModelProperty(value = "type for contact")
//    @Convert(converter= ContactCategoryTypeConverter.class)
//    private ContactCategoryType contactCategory;

    @ApiModelProperty(value = "title for the contact")
    private String title;

    @ApiModelProperty(value = "department for the contact")
    private String department;


//    @ApiModelProperty(value = "id for the company location")
//    private LocationDTO companyLocation;

    @ApiModelProperty(value = "note for the contact")
    private String note;


    @ApiModelProperty(value = "businessUnit for the contact, custom version exclusive")
    private String businessUnit;

    @ApiModelProperty(value = "businessGroup for the contact, custom version exclusive")
    private String businessGroup;

    @ApiModelProperty(value = "linkedinProfile for the contact, custom version exclusive")
    private List<String> linkedinProfile;

    @ApiModelProperty(value = "status for the contact, true is active, false id inActive")
    private Boolean active;

    @ApiModelProperty(value = "Receive email notifications when Timesheets or Expenses are submitted for my approval.")
    @NotNull
    private Boolean receiveEmail;

    @ApiModelProperty(value = "createUserId for the contact")
    private Long createUserId;

//    @ApiModelProperty(value = "id for the company location")
//    private Long companyLocationId;

    @ApiModelProperty(value = "tags for the contact")
    private List<String> tags;

    public ContactVO(Long id) {
        this.id = id;
    }

    public ContactVO(Long id, String firstName, String lastName, String name) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.fullName = name;
    }
}
