package com.altomni.apn.company.domain.contract;


import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
* A ContractServiceType
* <AUTHOR>
* date:2023-07-03
*/
@Entity
@Table(name = "company_contract_service_type")
@Data
public class ContractServiceType implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "contract_id")
    private Long contractId;

    @Column(name = "service_type_id")
    private Integer serviceTypeId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ContractServiceType that = (ContractServiceType) o;
        return contractId.equals(that.contractId) && serviceTypeId.equals(that.serviceTypeId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(contractId, serviceTypeId);
    }
}
