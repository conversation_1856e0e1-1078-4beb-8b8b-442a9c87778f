package com.altomni.apn.company.repository.business;

import com.altomni.apn.company.domain.business.AccountBusinessServiceTypeRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface AccountBusinessServiceTypeRelationRepository extends JpaRepository<AccountBusinessServiceTypeRelation, Long>, JpaSpecificationExecutor<AccountBusinessServiceTypeRelation> {

//    void deleteAllBySalesLeadIdIn(List<Long> salesLeadIds);
//
//    void deleteAllBySalesLeadId(Long salesLeadId);

    List<AccountBusinessServiceTypeRelation> findAllByAccountBusinessIdIn(List<Long> businessIds);

    @Query("select max(ar.id) from AccountBusinessServiceTypeRelation ar")
    Long maxId();

}