package com.altomni.apn.company.service.dto.folder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "id for folders")
public class FolderSearchDTO implements Serializable {

    @ApiModelProperty(value = "The name for folder.")
    private String name;

    @ApiModelProperty(value = "The size for folder.")
    private Integer limit;

    @ApiModelProperty(value = "The createdDateFrom for folder.")
    private Instant createdDateFrom;

    @ApiModelProperty(value = "The createdDateTo for folder.")
    private Instant createdDateTo;

    @ApiModelProperty(value = "The generalTexts for search folder.")
    private String generalTexts;

}
