package com.altomni.apn.company.service.dto.report;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.company.domain.enumeration.report.PerformanceReportType;
import com.altomni.apn.company.domain.enumeration.report.ReportTableType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * performance report search dto
 * <AUTHOR>
 */
@Data
public class PerformanceReportSearchDTO implements Serializable {

    @ApiModelProperty(value = "the id for company.")
    @NotNull(message = "companyId is required.")
    private Long companyId;

    @ApiModelProperty(value = "the type for jobs.")
    private List<JobType> jobType;

    @ApiModelProperty(value = "the id for users.")
    private List<Long> userId;

    @ApiModelProperty(value = "the role for users.")
    private UserRole userRole;

    @ApiModelProperty(value = "the fromDate for report.")
    private String fromDate;

    @ApiModelProperty(value = "the toDate for report.")
    private String toDate;

    @ApiModelProperty(value = "the reportType for report.")
    @NotNull(message = "reportType is required.")
    private PerformanceReportType reportType;

    @ApiModelProperty(value = "the applicationType for report.")
    private ReportTableType applicationType;

    private List<Long> salesLeadList;
}
