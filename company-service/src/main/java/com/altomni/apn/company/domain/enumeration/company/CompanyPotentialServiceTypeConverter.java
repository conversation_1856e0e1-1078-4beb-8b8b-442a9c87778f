package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CompanyPotentialServiceTypeConverter extends AbstractAttributeConverter<CompanyPotentialServiceType, Integer> {
    public CompanyPotentialServiceTypeConverter() {
        super(CompanyPotentialServiceType::toDbValue, CompanyPotentialServiceType::fromDbValue);
    }
}
