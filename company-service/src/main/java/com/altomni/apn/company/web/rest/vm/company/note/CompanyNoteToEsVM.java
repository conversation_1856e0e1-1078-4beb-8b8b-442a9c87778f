package com.altomni.apn.company.web.rest.vm.company.note;

import cn.hutool.json.JSONObject;
import com.altomni.apn.company.domain.enumeration.company.CompanyContactType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CompanyNoteToEsVM {

    /**
     * company contact
     */
    private JSONObject clientContacts;

    /**
     * company id
     */
    private String companyId;

    private Instant createdDate;

    /**
     * whether the note is deleted
     */
    private Boolean deleted;

    private Instant lastModifiedDate;

    /**
     * plain text
     */
    private String note;

    /**
     * rich text
     */
    private String richTextNote;

    private Instant reminderTime;

    private LocalDate lastContactDate;


    /**
     * progress note param
     */
    private String contactTime;

    private String salesLeadId;

    private CompanyContactType contactType;

    /**
     *  EsFillerConstants (job、talent、company)
     */
    //creator
    private List<JSONObject> responsibility5;

    /**
     *  EsFillerConstants (job、talent、company)
     */
    //last modified user
    private List<JSONObject> responsibility13;


}
