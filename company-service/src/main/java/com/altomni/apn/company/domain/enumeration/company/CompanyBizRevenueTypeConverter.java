package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CompanyBizRevenueTypeConverter extends AbstractAttributeConverter<CompanyBizRevenueType, Integer> {
    public CompanyBizRevenueTypeConverter() {
        super(CompanyBizRevenueType::toDbValue, CompanyBizRevenueType::fromDbValue);
    }
}
