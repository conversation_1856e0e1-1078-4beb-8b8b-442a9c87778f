package com.altomni.apn.company.vo.folder;

import com.altomni.apn.company.domain.enumeration.folder.FolderType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-19
*/
@AllArgsConstructor
@ApiModel(description = "Vo for categoryFolder")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class FolderSearchVO implements Serializable {

    @ApiModelProperty(value = "the type for folder.")
    private FolderType folderType;

    @ApiModelProperty(value = "the count for categoryFolder.")
    private List<CustomFolderSearchVO> folders;

}
