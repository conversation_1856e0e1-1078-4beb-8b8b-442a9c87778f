package com.altomni.apn.company.domain.vm;

import com.altomni.apn.common.domain.enumeration.ContactCategoryType;
import com.altomni.apn.common.domain.enumeration.ContactCategoryTypeConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.Instant;

@Data
@Entity
@NoArgsConstructor
@AllArgsConstructor
public class CompanyContactVM implements Serializable {

    @Id
    private Long id;

    private Integer contactCategory;

    private boolean active = true;

    private Instant lastFollowUpTime;

    private Long tenantId;

    private Long talentId;

    private Long companyId;

    private String name;

    private String companyName;

    private Long contactId;

    public CompanyContactVM(Long id, Integer contactCategory, boolean active, Instant lastFollowUpTime, Long tenantId, Long talentId, Long companyId, String name) {
        this.id = id;
        this.contactCategory = contactCategory;
        this.active = active;
        this.lastFollowUpTime = lastFollowUpTime;
        this.tenantId = tenantId;
        this.talentId = talentId;
        this.companyId = companyId;
        this.name = name;
    }

    public CompanyContactVM(Long id, Integer contactCategory, boolean active, Instant lastFollowUpTime, Long tenantId, Long talentId, Long companyId, String name, String companyName) {
        this.id = id;
        this.contactCategory = contactCategory;
        this.active = active;
        this.lastFollowUpTime = lastFollowUpTime;
        this.tenantId = tenantId;
        this.talentId = talentId;
        this.companyId = companyId;
        this.name = name;
        this.companyName = companyName;
    }

    public CompanyContactVM(Long id, Integer contactCategory, boolean active, Instant lastFollowUpTime, Long tenantId, Long talentId, Long companyId, String name, Long contactId) {
        this.id = id;
        this.contactCategory = contactCategory;
        this.active = active;
        this.lastFollowUpTime = lastFollowUpTime;
        this.tenantId = tenantId;
        this.talentId = talentId;
        this.companyId = companyId;
        this.name = name;
        this.contactId = contactId;
    }
}
