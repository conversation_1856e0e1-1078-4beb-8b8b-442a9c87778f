package com.altomni.apn.company.repository.amreport;

import com.altomni.apn.company.domain.amreport.AmReportTalentJobNote;
import com.altomni.apn.company.domain.company.note.CompanyProgressNote;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface AmReportTalentJobNoteRepository extends JpaRepository<AmReportTalentJobNote, Long>, JpaSpecificationExecutor<CompanyProgressNote> {

    AmReportTalentJobNote findAmReportTalentJobNoteByTalentIdAndJobIdAndFrequency(Long talentId, Long jobId, Integer frequency);

}
