package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyType enumeration.
 */
public enum CompanyClientLevelType implements ConvertedEnum<Integer> {

    EXTRA_HIGH_LEVEL(10, "EXTRA_HIGH_LEVEL"),
    HIGI_LEVEL(20, "HIGI_LEVEL"),
    MIDDLE_LEVEL(30, "MIDDLE_LEVEL"),
    LOW_LEVEL(40, "LOW_LEVEL");

    private final int dbValue;

    private final String display;

    CompanyClientLevelType(int dbValue, String display) {
        this.dbValue = dbValue;
        this.display = display;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDisplay() {
        return display;
    }

    public static final ReverseEnumResolver<CompanyClientLevelType, Integer> resolver = new ReverseEnumResolver<>(CompanyClientLevelType.class, CompanyClientLevelType::toDbValue);

    public static final ReverseEnumResolver<CompanyClientLevelType, String> nameResolver = new ReverseEnumResolver<>(CompanyClientLevelType.class, CompanyClientLevelType::getDisplay);

    public static CompanyClientLevelType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public static CompanyClientLevelType fromDisplay(String display) {
        return nameResolver.get(display);
    }
}
