package com.altomni.apn.company.service.company.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.dict.CompanyIndustryRelation;
import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.company.CompanyType;
import com.altomni.apn.common.domain.enumeration.job.JobPermission;
import com.altomni.apn.common.dto.company.BriefCompanyDTO;
import com.altomni.apn.common.dto.company.ClientContactCompany;
import com.altomni.apn.common.dto.company.ICompanyTeamUser;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.common.enumeration.enums.ClientContactCompanyType;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.business.AccountBusinessServiceTypeRelation;
import com.altomni.apn.company.domain.business.BusinessFlowAdministrator;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.domain.company.*;
import com.altomni.apn.company.domain.company.lcoation.CompanyLocation;
import com.altomni.apn.company.domain.enumeration.company.NoPoachingType;
import com.altomni.apn.company.domain.enumeration.contract.ContractStatus;
import com.altomni.apn.company.domain.saleslead.SalesLeadReportBrief;
import com.altomni.apn.company.domain.vm.EntityNameVM;
import com.altomni.apn.company.repository.business.AccountBusinessAdministratorRepository;
import com.altomni.apn.company.repository.business.AccountBusinessServiceTypeRelationRepository;
import com.altomni.apn.company.repository.business.SalesLeadClientContactRepository;
import com.altomni.apn.company.repository.company.*;
import com.altomni.apn.company.repository.company.location.CompanyLocationRepository;
import com.altomni.apn.company.repository.user.UserServiceRepository;
import com.altomni.apn.company.service.company.CompanyService;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.company.service.dto.CompanyPurchaseOrderDTO;
import com.altomni.apn.company.service.dto.CompanyPurchaseOrderDetailDTO;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.company.vo.company.CompanyPurchaseOrderDetailModifyVO;
import com.altomni.apn.company.vo.company.CompanyPurchaseOrderDetailVO;
import com.altomni.apn.company.vo.company.CompanyPurchaseOrderVO;
import com.altomni.apn.company.vo.contact.AccountContactRelationDetailVO;
import com.altomni.apn.company.vo.salesLead.SalesLeadAdministratorVO;
import com.altomni.apn.company.vo.salesLead.SalesLeadVO;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectSearchVM;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectVM;
import com.altomni.apn.company.web.rest.vm.company.CompanyVO;
import com.altomni.apn.company.web.rest.vm.job.JobIdCompanyNameVM;
import okhttp3.Headers;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;



@Service
@Transactional
public class CompanyServiceImpl implements CompanyService {

    private final Logger log = LoggerFactory.getLogger(CompanyServiceImpl.class);

    private final Double CLIENT_ACCOUNT_PROGRESS = 1.20;

    @Resource
    private SalesLeadClientContactRepository salesLeadClientContactRepository;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private CompanyNativeRepository companyNativeRepository;

    @Resource
    private AccountBusinessAdministratorRepository accountBusinessAdministratorRepository;

    @Resource
    private UserServiceRepository userServiceRepository;

    @Resource
    private CompanyLocationRepository companyLocationRepository;

    @Resource
    private CompanyIndustryRelationRepository companyIndustryRelationRepository;

    @Resource
    private AccountBusinessServiceTypeRelationRepository accountBusinessServiceTypeRelationRepository;

    @Resource
    private CompanyPurchaseOrderRepository companyPurchaseOrderRepository;

    @Resource
    private CompanyPurchaseOrderDetailRepository companyPurchaseOrderDetailRepository;

    @Resource
    private CompanyPurchaseOrderNativeRepository companyPurchaseOrderNativeRepository;

    @Resource
    private CompanyContactRepository companyContactRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    @Resource
    private HttpService httpService;

    @Resource
    private ApplicationProperties applicationProperties;

    private final String CRM_QUERY_COMPANY_BY_NAME_URL = "/account/api/v1/companies/find-by-name";

    private final String CRM_QUERY_COMPANY_BY_ID_URL = "/account/api/v1/companies/find-by-ids";


    private String crmQueryContactUrl(Long talentId) {
        return "/contact/api/v1/contact/find-by-talent?talentId=" + talentId;
    }


    @Override
    public Page<CompanyProspectVM> searchAllCompaniesForBDReport(Instant fromDate, Instant toDate, CompanyProspectSearchVM searchVM, Pageable pageable) {
        List<CompanyReportBrief> companyList = companyNativeRepository.searchAllCompaniesForBDReport(fromDate, toDate, searchVM, pageable);
        List<Long> companyIds = companyList.stream().map(CompanyReportBrief::getId).collect(Collectors.toList());
        List<Long> creatorIds = companyList.stream().map(CompanyReportBrief::getPermissionUserId).collect(Collectors.toList());
        List<CompanyProspectVM> resultList = companyList.stream().map(o -> {
            CompanyProspectVM item = new CompanyProspectVM();
            ServiceUtils.myCopyProperties(o, item);
            return item;
        }).collect(Collectors.toList());
        setReportIndustry(resultList, companyIds);
        setReportSalesLead(resultList, companyIds, searchVM);
        setReprotCreator(resultList, creatorIds);
        setReportCountry(resultList, companyIds);
        List<CompanyProspectVM> splitCompanyList = getSplitCompanyList(resultList);
        return new PageImpl<>(filterCompanyList(splitCompanyList, searchVM), Pageable.unpaged(), splitCompanyList.size());
    }

    private static final Map<BigDecimal, Integer> PROGRESS_MAP = Map.of(
            BigDecimal.ZERO, 40,
            BigDecimal.valueOf(0.25), 45,
            BigDecimal.valueOf(0.50), 50,
            BigDecimal.valueOf(0.75), 55,
            BigDecimal.ONE, 60
    );

    public Integer getMap(BigDecimal accountProgress) {
        return Optional.ofNullable(PROGRESS_MAP.get(accountProgress))
                .orElse(null);
    }

    /**
     * filter salesLead owner & bd owner & service type
     * @param splitCompanyList
     * @param searchVM
     */
    private List<CompanyProspectVM> filterCompanyList(List<CompanyProspectVM> splitCompanyList, CompanyProspectSearchVM searchVM) {
        if (CollUtil.isNotEmpty(searchVM.getTeamUserIds())) {
            splitCompanyList = splitCompanyList.stream().filter(o -> CollUtil.isNotEmpty(o.getSalesLeadBdOwners()) && o.getSalesLeadBdOwners().stream().map(SalesLeadAdministratorVO::getId).anyMatch(searchVM.getTeamUserIds()::contains)).collect(Collectors.toList());
        }

        if (CollUtil.isNotEmpty(searchVM.getBdOwners())) {
            splitCompanyList = splitCompanyList.stream().filter(o -> CollUtil.isNotEmpty(o.getSalesLeadBdOwners()) && o.getSalesLeadBdOwners().stream().map(item -> item.getName().toLowerCase()).anyMatch(owner -> owner.contains(searchVM.getBdOwners().iterator().next().getFullName().toLowerCase()))).collect(Collectors.toList());
        }

        if (CollUtil.isNotEmpty(searchVM.getOwners())) {
            splitCompanyList = splitCompanyList.stream().filter(o -> CollUtil.isNotEmpty(o.getSalesLeadOwners()) && o.getSalesLeadOwners().stream().map(item -> item.getName().toLowerCase()).anyMatch(owner -> owner.contains(searchVM.getOwners().iterator().next().getFullName().toLowerCase()))).collect(Collectors.toList());
        }

        if (CollUtil.isNotEmpty(searchVM.getServiceTypes())) {
            Set<Integer> serviceTypeIdList = searchVM.getServiceTypes().stream().map(EnumCompanyServiceType::getId).collect(Collectors.toSet());
            splitCompanyList = splitCompanyList.stream().filter(o -> CollUtil.isNotEmpty(o.getSalesLeads()) && o.getSalesLeads().stream().anyMatch(item -> item.getCompanyServiceTypes() != null && item.getCompanyServiceTypes().stream().anyMatch(serviceTypeIdList::contains))).collect(Collectors.toList());
        }

        return splitCompanyList;

    }


    private void setReportIndustry(List<CompanyProspectVM> resultList, List<Long> companyIds) {
        List<CompanyIndustryRelation> relationList = companyIndustryRelationRepository.findAllByAccountCompanyIdIn(companyIds);
        Map<Long, List<CompanyIndustryRelation>> relationMap = relationList.stream().collect(Collectors.groupingBy(CompanyIndustryRelation::getAccountCompanyId));
        resultList.forEach(o -> {
            if (relationMap.containsKey(o.getId())) {
                o.setIndustries(relationMap.get(o.getId()).stream().map(industry -> new EnumRelationDTO().setEnumId(String.valueOf(industry.getEnumId()))).collect(Collectors.toList()));
            }
        });
    }

    private List<CompanyProspectVM> getSplitCompanyList(List<CompanyProspectVM> resultList) {
        List<CompanyProspectVM> splitCompanyList = new ArrayList<>();
        resultList.forEach(item -> {
            item.setType(CompanyType.CLIENT);
            splitCompanyList.add(item);
        });
        splitCompanyList.forEach(o -> {
            if (CollUtil.isNotEmpty(o.getSalesLeads())) {
                List<SalesLeadAdministratorVO> salesLeadOwnerList = o.getSalesLeads().stream().filter(item -> CollUtil.isNotEmpty(item.getSalesLeadsOwners())).flatMap(salesLeadVO -> salesLeadVO.getSalesLeadsOwners().stream()).collect(Collectors.toList());
                List<SalesLeadAdministratorVO> salesLeadBdList = o.getSalesLeads().stream().filter(item -> CollUtil.isNotEmpty(item.getSalesLeadsBdOwners())).flatMap(salesLeadVO -> salesLeadVO.getSalesLeadsBdOwners().stream()).collect(Collectors.toList());
                o.setSalesLeadOwners(salesLeadOwnerList);
                o.setSalesLeadBdOwners(salesLeadBdList);
            }
        });
        return splitCompanyList;
    }

    private void setReportCountry(List<CompanyProspectVM> resultList, List<Long> companyIds) {
        List<CompanyLocation> locationList = companyLocationRepository.findAllByAccountCompanyIdIn(companyIds);
        Map<Long, List<CompanyLocation>> locationMap = locationList.stream().collect(Collectors.groupingBy(CompanyLocation::getAccountCompanyId));
        resultList.forEach(company -> {
            if (locationMap.containsKey(company.getId())) {
                company.setCountry(String.join(",", locationMap.get(company.getId()).stream().map(CompanyLocation::getOfficialCountry).collect(Collectors.toSet())));
            }
        });
    }

    private void setReprotCreator(List<CompanyProspectVM> resultList, List<Long> creatorIds) {
        List<EntityNameVM> entityNameList = userServiceRepository.findUserNameByIds(creatorIds.stream().distinct().collect(Collectors.toList()));
        Map<Long, String> userNameMap = entityNameList.stream().collect(Collectors.toMap(EntityNameVM::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));
        resultList.forEach(company -> {
            if (userNameMap.containsKey(company.getPermissionUserId())) {
                company.setCreatedBy(userNameMap.get(company.getPermissionUserId()));
            }
        });
    }

    private void setReportSalesLead(List<CompanyProspectVM> resultList, List<Long> companyIds, CompanyProspectSearchVM searchVM) {
        List<SalesLeadReportBrief> salesLeadList = companyNativeRepository.findAllByCompanyIdIn(companyIds);

        if (searchVM.getAccountProgress() != null) {
            salesLeadList = salesLeadList.stream().filter(o -> Objects.equals(searchVM.getAccountProgress(), o.getBusinessProgress())).collect(Collectors.toList());
        }

        List<AccountBusinessServiceTypeRelation> leadServiceTypeList = accountBusinessServiceTypeRelationRepository.findAllByAccountBusinessIdIn(salesLeadList.stream().map(SalesLeadReportBrief::getId).collect(Collectors.toList()));
        List<BusinessFlowAdministrator> businessFlowAdministratorList = accountBusinessAdministratorRepository.findAllByAccountBusinessIdIn(salesLeadList.stream().map(SalesLeadReportBrief::getId).collect(Collectors.toList()));

        Map<Long, List<AccountBusinessServiceTypeRelation>> leadServiceTypeMap = leadServiceTypeList.stream().collect(Collectors.groupingBy(AccountBusinessServiceTypeRelation::getAccountBusinessId));
        Map<Long, List<SalesLeadReportBrief>> salesLeadMap = salesLeadList.stream().collect(Collectors.groupingBy(SalesLeadReportBrief::getAccountCompanyId));
        Map<Long, List<BusinessFlowAdministrator>> salesLeadAdministratorMap = businessFlowAdministratorList.stream().collect(Collectors.groupingBy(BusinessFlowAdministrator::getAccountBusinessId));
        List<EntityNameVM> entityNameList = userServiceRepository.findUserNameByIds(businessFlowAdministratorList.stream().map(BusinessFlowAdministrator::getUserId).distinct().collect(Collectors.toList()));
        Map<Long, String> userNameMap = entityNameList.stream().collect(Collectors.toMap(EntityNameVM::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));
        resultList.forEach(company -> {
            if (salesLeadMap.containsKey(company.getId())) {
                company.setSalesLeads(salesLeadMap.get(company.getId()).stream().map(o -> {
                    SalesLeadVO salesLeadVO = SalesLeadVO.fromSalesLeadReportBrief(o);
                    if (leadServiceTypeMap.containsKey(salesLeadVO.getId())) {
                        salesLeadVO.setCompanyServiceTypes(leadServiceTypeMap.get(salesLeadVO.getId()).stream().map(AccountBusinessServiceTypeRelation::getEnumId).collect(Collectors.toList()));
                    }
                    if (salesLeadAdministratorMap.containsKey(o.getId())) {
                        salesLeadVO.setSalesLeadsOwners(salesLeadAdministratorMap.get(o.getId()).stream().filter(item -> item.getSalesLeadRoleType().equals(SalesLeadRoleType.SALES_LEAD_OWNER)).map(admin -> new SalesLeadAdministratorVO(admin.getUserId(), userNameMap.get(admin.getUserId()))).collect(Collectors.toList()));
                        salesLeadVO.setSalesLeadsBdOwners(salesLeadAdministratorMap.get(o.getId()).stream().filter(item -> item.getSalesLeadRoleType().equals(SalesLeadRoleType.BUSINESS_DEVELOPMENT)).map(admin -> new SalesLeadAdministratorVO(admin.getUserId(), userNameMap.get(admin.getUserId()))).collect(Collectors.toList()));
                    }
                    return salesLeadVO;
                }).collect(Collectors.toList()));
            }
        });
    }


    @Override
    public CompanyDTO getCompany(Long id) {
        return companyRepository.findById(id)
                .map(CompanyDTO::fromCompany)
                .orElse(null);
    }

    @Override
    public List<CompanyContact> getCompanyContacts(Long id) {
        return companyContactRepository.findAllByCompanyId(id);
    }

    @Override
    public List<CompanyDTO> findALLByIds(List<Long> ids) {
        return companyRepository.findAllById(ids).stream().map(CompanyDTO::fromCompany).collect(Collectors.toList());
    }

    @Override
    public List<ClientContactCompany> findALLByNames(final List<String> names, Long excludeTalentId) {
        //查apn（正式客户）数据，兼容没有crm账户的user
        List<Company> clientCompanyList = companyRepository.findAllByTenantIdAndFullBusinessNameIn(SecurityUtils.getTenantId(), names.stream().distinct().collect(Collectors.toList()));
        List<SalesLeadClientContact> clientContactList = salesLeadClientContactRepository.findAllByTalentId(excludeTalentId);
        List<Company> talentCompanyList = companyRepository.findAllByIdIn(clientContactList.stream().map(SalesLeadClientContact::getCompanyId).distinct().toList());
        Map<Long, String> talentCompanyMap = talentCompanyList.stream().collect(Collectors.toMap(Company::getId, Company::getFullBusinessName));
        //查crm（正式客户 & 开发中）数据
        List<AccountCompanyVO> companyList = findAllCrmCompany(names);
        List<AccountContactRelationDetailVO> salesLeadClientContactList = findAllCrmContact(excludeTalentId);

        List<AccountCompanyVO> allCompanyList = new ArrayList<>();
        List<AccountContactRelationDetailVO> allSalesLeadClientContactList = new ArrayList<>();
        allCompanyList.addAll(clientCompanyList.stream().map(Company::toAccountCompanyVO).toList());
        allCompanyList.addAll(companyList);
        allSalesLeadClientContactList.addAll(clientContactList.stream().map(o -> {
            AccountContactRelationDetailVO accountContactRelationDetailVO = new AccountContactRelationDetailVO();
            ServiceUtils.myCopyProperties(o, accountContactRelationDetailVO);
            accountContactRelationDetailVO.setAccountCompanyId(o.getCompanyId());
            accountContactRelationDetailVO.setAccountCompanyName(talentCompanyMap.get(o.getCompanyId()));
            return accountContactRelationDetailVO;
        }).toList());
        allSalesLeadClientContactList.addAll(salesLeadClientContactList);

        Map<Long, AccountContactRelationDetailVO> salesLeadClientContactMap = allSalesLeadClientContactList.stream()
                .collect(Collectors.toMap(AccountContactRelationDetailVO::getAccountCompanyId, Function.identity(), (existing, replacement) -> existing));
        List<Long> bindCompany = allSalesLeadClientContactList.stream().map(AccountContactRelationDetailVO::getAccountCompanyId).collect(Collectors.toList());

        List<ClientContactCompany> ret = allCompanyList.stream().map(c -> {
            ClientContactCompany company = new ClientContactCompany();
            company.setId(c.getId());
            company.setName(c.getFullBusinessName());
            if (bindCompany.contains(c.getId())) {
                AccountContactRelationDetailVO salesLeadClientContact = salesLeadClientContactMap.get(c.getId());
                company.setType(salesLeadClientContact.getActive() ? ClientContactCompanyType.ACTIVE_CLIENT : ClientContactCompanyType.INACTIVE_CLIENT);
                company.setClientContactId(salesLeadClientContact.getId());
                allSalesLeadClientContactList.remove(salesLeadClientContact);
            } else {
                company.setType(ClientContactCompanyType.NO_BIND);
            }
            return company;
        }).collect(Collectors.toList());
        allSalesLeadClientContactList.forEach(c -> {
            ClientContactCompany company = new ClientContactCompany();
            company.setId(c.getAccountCompanyId());
            if (ObjectUtil.isNotEmpty(c.getAccountCompanyName())) {
                company.setName(c.getAccountCompanyName());
                company.setType(c.getActive() ? ClientContactCompanyType.ACTIVE_CLIENT : ClientContactCompanyType.INACTIVE_CLIENT);
                company.setClientContactId(c.getId());
            }
            ret.add(company);
        });
        return ret.stream().distinct().toList();
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }


    /**
     * 通过crm接口查company，crm包含开发中 & 正式客户，apn仅有正式客户数据
     * @return
     */
    private List<AccountCompanyVO> findAllCrmCompany(List<String> companyNames) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", getAuthorizationHeader());
        httpHeaders.set("Content-Type", "application/json");
        try {
            HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + CRM_QUERY_COMPANY_BY_NAME_URL, convertToOkHttpHeaders(httpHeaders), JsonUtil.toJson(companyNames));
            if (response != null && 200 == response.getCode()) {
                return JSONUtil.toList(JSONUtil.parseArray(response.getBody()), AccountCompanyVO.class);
            } else {
                log.error("find crm company error, response: {}", response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("find crm company error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    /**
     * 通过crm接口查联系人，crm包含开发中 & 正式客户，apn仅有正式客户数据
     * @return
     */
    private List<AccountContactRelationDetailVO> findAllCrmContact(Long talentId) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", getAuthorizationHeader());
        httpHeaders.set("Content-Type", "application/json");
        try {
            HttpResponse response = httpService.get(applicationProperties.getCrmUrl() + crmQueryContactUrl(talentId), convertToOkHttpHeaders(httpHeaders));
            if (response != null && 200 == response.getCode()) {
                return JSONUtil.toList(JSONUtil.parseArray(response.getBody()), AccountContactRelationDetailVO.class);
            } else {
                log.error("find crm contact error, response: {}", response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("find crm contact error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public static Headers convertToOkHttpHeaders(HttpHeaders httpHeaders) {
        Headers.Builder builder = new Headers.Builder();
        httpHeaders.forEach((key, values) -> {
            for (String value : values) {
                builder.add(key, value);
            }
        });
        return builder.build();
    }


    @Override
    public Page<Company> findAllByTenantId(Pageable pageable) {
        return companyRepository.findAllByTenantId(SecurityUtils.getTenantId(), pageable);
    }

    @Override
    public List<AccountCompanyVO> findAllByTenantId() {
        return companyRepository.findAllByTenantId(SecurityUtils.getTenantId()).stream().map(Company::toAccountCompanyVO).collect(Collectors.toList());
    }

    @Override
    public Integer countCompanyAccountManager(Long talentId, Long userId) {
        return accountBusinessAdministratorRepository.countCompanyAccountManager(talentId, userId);
    }

    @Override
    public List<AssignedUserDTO> getApplicationUsers(Long companyId) {
        //find companyAM and teamUsers
        List<Object[]> objects = accountBusinessAdministratorRepository.findApplicationUsersByCompanyId(companyId);
        return objectToDTO(objects);
    }

    private List<AssignedUserDTO> objectToDTO(List<Object[]> objects) {
        if (CollUtil.isEmpty(objects)) {
            return List.of();
        }
        List<AssignedUserDTO> result = new ArrayList<>();
        for (Object[] obj : objects) {
            AssignedUserDTO assignedUserDTO = new AssignedUserDTO();
            if (ObjectUtil.isNotEmpty(obj[0])) {
                assignedUserDTO.setUserId(Long.valueOf(StringUtil.valueOf(obj[0])));
            }
            assignedUserDTO.setFirstName(StringUtil.valueOf(obj[2]));
            assignedUserDTO.setLastName(StringUtil.valueOf(obj[3]));
            assignedUserDTO.setUsername(StringUtil.valueOf(obj[4]));
            assignedUserDTO.setEmail(StringUtil.valueOf(obj[5]));
            assignedUserDTO.setActivated(BooleanUtil.toBoolean(StringUtil.valueOf(obj[6])));
            if(ObjectUtil.isNotEmpty(obj[1])) {
                //format jobPermission
                Set<String> permissionSet = JobPermission.parseJobPermission(Integer.parseInt(StringUtil.valueOf(obj[1])));
                if (ObjectUtil.isNotEmpty(permissionSet)) {
                    Iterator<String> permission = permissionSet.iterator();
                    if (permission.hasNext()) {
                        assignedUserDTO.setPermission(permission.next());
                        result.add(assignedUserDTO);
                    }
                }
            }
        }
        return result;
    }

    @Override
    public Map<Long, String> getCompanyNamesByJobIds(List<Long> jobIds) {
        return companyRepository.getCompanyNamesByJobIds(jobIds)
                .stream()
                .filter(t -> StringUtils.isNotEmpty(t.getCompanyName()))
                .collect(Collectors.toMap(JobIdCompanyNameVM::getJobId, JobIdCompanyNameVM::getCompanyName));
    }

    @Override
    public List<ICompanyTeamUser.CompanyTeamUserDTO> getTeamUsersByCompanyId(Long companyId, Long salesLeadId) {
        return accountBusinessAdministratorRepository.findTeamUsersByCompanyId(companyId, salesLeadId).stream().filter(v -> v.getUserId() != null).map(u
                -> new ICompanyTeamUser.CompanyTeamUserDTO(u.getUserId(), u.getFirstName(), u.getLastName(), u.getRole())).distinct().collect(Collectors.toList());
    }

    @Override
    public String getCompanyNameById(Long companyId) {
        return companyRepository.getCompanyNameById(companyId);
    }

    //
//    @Override
//    public List<Long> findCompanyIdsByUserIdWithAm(Long userId) {
//        return salesLeadAdministratorRepository.findCompanyIdsByUserIdWithAm(userId);
//    }

    @Override
    public List<CompanyVO> findAllByTenantIdAndName(String name) {
        return companyNativeRepository.queryCompanyName(SecurityUtils.getTenantId(),name);
    }

    @Override
    public List<AccountCompanyVO> getAllClientCompanyList(AccountCompanyStatus active, Integer limit) {
        Long tenantId = SecurityUtils.getTenantId();

        //查询所有需要禁猎的公司
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<AccountCompanyVO>> noPoachingListFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return getNoPoachingList(tenantId);
        });
        //查询所有displayNameInEn有值的公司
        CompletableFuture<List<AccountCompanyVO>> hasDisplayNameInEnListFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return getDisplayNameInEnList(tenantId);
        });


        List<AccountCompanyVO> noPoachingList = noPoachingListFuture.join();
        List<AccountCompanyVO> displayNameInEnList = hasDisplayNameInEnListFuture.join();

        List<AccountCompanyVO> companyMigrateList = companyNativeRepository.queryAllCompanyList(tenantId, active, limit)
                .stream().map(o -> new AccountCompanyVO(o.getId(), o.getActive(), o.getFullBusinessName())).collect(Collectors.toList());
        companyMigrateList.forEach(c -> {
            //对比company设置noPoaching
            if (noPoachingList.stream().anyMatch(n -> n.getId().equals(c.getId()))) {
                c.setNoPoaching(true);
            }

            //对比company设置enDisplay
            displayNameInEnList.forEach(n -> {
                if (n.getId().equals(c.getId()) && ObjectUtil.isNotNull(n.getDisplayNameInEn())) {
                    c.setDisplayNameInEn(n.getDisplayNameInEn());
                }
            });
        });
        return companyMigrateList;
    }

    private List<AccountCompanyVO> getNoPoachingList(Long tenantId) {
        return companyRepository.getAllNoPoachingCompanies(NoPoachingType.NO_RESTRICTION.toDbValue(), ContractStatus.VALID.toDbValue(), AccountCompanyStatus.INACTIVE.toDbValue(), tenantId)
                .stream().map(o -> new AccountCompanyVO(
                        Long.parseLong(StrUtil.toString(o[0]))
                        , StrUtil.toString(o[1])
                        ,  ObjectUtil.isNotNull(o[2]) ? StrUtil.toString(o[2]) : null))
                .collect(Collectors.toList());
    }

    /**
     * 通过crm接口查company，crm包含开发中 & 正式客户，apn仅有正式客户数据
     * @param ids
     * @return
     */
    @Override
    public List<AccountCompanyVO> queryCRMCompanyByIds(List<Long> ids) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.set("Authorization", getAuthorizationHeader());
        httpHeaders.set("Content-Type", "application/json");
        try {
            HttpResponse response = httpService.post(applicationProperties.getCrmUrl() + CRM_QUERY_COMPANY_BY_ID_URL, convertToOkHttpHeaders(httpHeaders), JsonUtil.toJson(ids));
            if (response != null && 200 == response.getCode()) {
                return JSONUtil.toList(JSONUtil.parseArray(response.getBody()), AccountCompanyVO.class);
            } else {
                log.error("find crm company error, response: {}", response);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("find crm company error: {}", e.getMessage());
        }
        return new ArrayList<>();
    }

    public List<AccountCompanyVO> getDisplayNameInEnList(Long tenantId) {
        return companyRepository.getDisplayNameInEnList(tenantId)
                .stream().map(o -> new AccountCompanyVO(
                        Long.parseLong(StrUtil.toString(o[0]))
                        , StrUtil.toString(o[1])
                        , ObjectUtil.isNotNull(o[2]) ? StrUtil.toString(o[2]) : null))
                .collect(Collectors.toList());
    }

    @Override
    public List<BriefCompanyDTO> findBriefCompanyByTenantId(Long tenantId) {
        List<Company> companyList;
        if (SecurityUtils.getUserType() == TenantUserTypeEnum.EMPLOYER) {
            companyList = companyRepository.findAllByTenantId(tenantId);
        } else {
            companyList = companyRepository.findCompaniesByTenantIdAndActive(tenantId, AccountCompanyStatus.ACTIVE);
        }

        return companyList.stream().map(company -> {
            BriefCompanyDTO companyDTO = new BriefCompanyDTO();
            ServiceUtils.myCopyProperties(company, companyDTO);
            return companyDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CompanyPurchaseOrderVO> getPurchaseOrderList(Long companyId) {
        if (companyId == null) {
            throw new CustomParameterizedException("parameter is null");
        }
        List<CompanyPurchaseOrder> companyPurchaseOrderList = companyPurchaseOrderRepository.findCompanyPurchaseOrderByCompanyId(companyId);
        if (companyPurchaseOrderList.isEmpty()) {
            return new ArrayList<>();
        }

        List<CompanyPurchaseOrderVO> purchaseOrderVOS = new ArrayList<>();
        companyPurchaseOrderList.forEach(v -> {
            CompanyPurchaseOrderVO vo = new CompanyPurchaseOrderVO();
            vo.setId(v.getId());
            vo.setCompanyId(v.getCompanyId());
            vo.setAm(v.getAm());
            vo.setCurrencyId(v.getCurrencyId());
            vo.setNotificationBalance(v.getNotificationBalance());
            if (!v.getPurchaseOrderDetailList().isEmpty()) {
                BigDecimal totalBalance = v.getPurchaseOrderDetailList().stream()
                        .map(p -> p.getBalance())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setTotalBalance(totalBalance);

                List<CompanyPurchaseOrderDetailVO> purchaseOrderDetailVOS = new ArrayList<>();
                v.getPurchaseOrderDetailList().forEach(z -> {
                    CompanyPurchaseOrderDetailVO detailVO = new CompanyPurchaseOrderDetailVO();
                    detailVO.setId(z.getId());
                    detailVO.setBalance(z.getBalance());
                    detailVO.setPurchaseOrderId(z.getPurchaseOrderId());
                    EntityNameVM vm = userServiceRepository.findUserNameById(z.getPermissionUserId());
                    detailVO.setAddBy(vm.getFirstName() + vm.getLastName());
                    detailVO.setAddOn(z.getCreatedDate());
                    detailVO.setAmount(z.getAmount());
                    detailVO.setCurrencyId(z.getCurrencyId());
                    detailVO.setOrderNumber(z.getOrderNumber());
                    purchaseOrderDetailVOS.add(detailVO);
                });

                vo.setDetailVOList(purchaseOrderDetailVOS);
            }

            purchaseOrderVOS.add(vo);
        });

        return purchaseOrderVOS;
    }

    @Override
    public List<CompanyPurchaseOrderVO> getCompanyPurchaseOrderList(List<Long> companyIdList) {
        if (companyIdList == null || companyIdList.isEmpty()) {
            throw new CustomParameterizedException("parameter is null");
        }
        List<CompanyPurchaseOrder> companyPurchaseOrderList = companyPurchaseOrderRepository.findCompanyPurchaseOrderByCompanyIdIn(companyIdList);
        if (companyPurchaseOrderList.isEmpty()) {
            return new ArrayList<>();
        }

        List<CompanyPurchaseOrderVO> purchaseOrderVOS = new ArrayList<>();
        companyPurchaseOrderList.forEach(v -> {
            CompanyPurchaseOrderVO vo = new CompanyPurchaseOrderVO();
            vo.setId(v.getId());
            vo.setCompanyId(v.getCompanyId());
            vo.setAm(v.getAm());
            vo.setCurrencyId(v.getCurrencyId());
            vo.setNotificationBalance(v.getNotificationBalance());
            if (!v.getPurchaseOrderDetailList().isEmpty()) {
                BigDecimal totalBalance = v.getPurchaseOrderDetailList().stream()
                        .map(p -> p.getBalance())
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                vo.setTotalBalance(totalBalance);

                List<CompanyPurchaseOrderDetailVO> purchaseOrderDetailVOS = new ArrayList<>();
                v.getPurchaseOrderDetailList().forEach(z -> {
                    CompanyPurchaseOrderDetailVO detailVO = new CompanyPurchaseOrderDetailVO();
                    detailVO.setId(z.getId());
                    detailVO.setBalance(z.getBalance());
                    detailVO.setPurchaseOrderId(z.getPurchaseOrderId());
                    EntityNameVM vm = userServiceRepository.findUserNameById(z.getPermissionUserId());
                    detailVO.setAddBy(vm.getFirstName() + vm.getLastName());
                    detailVO.setAddOn(z.getCreatedDate());
                    detailVO.setAmount(z.getAmount());
                    detailVO.setCurrencyId(z.getCurrencyId());
                    detailVO.setOrderNumber(z.getOrderNumber());
                    purchaseOrderDetailVOS.add(detailVO);
                });

                vo.setDetailVOList(purchaseOrderDetailVOS);
            }

            purchaseOrderVOS.add(vo);
        });

        return purchaseOrderVOS;
    }

    /**
     * 保存数据
     * @param companyPurchaseOrderDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCompanyPurchaseOrder(CompanyPurchaseOrderDTO companyPurchaseOrderDTO) {
        if (companyPurchaseOrderDTO.getCompanyId() == null || companyPurchaseOrderDTO.getAm() == null
                || companyPurchaseOrderDTO.getNotificationBalance() == null) {
            throw new CustomParameterizedException("parameter is null");
        }
        checkPermissionByCompanyId(companyPurchaseOrderDTO.getCompanyId());
        checkPurchaseOrder(companyPurchaseOrderDTO);

        List<CompanyPurchaseOrder> orderList = companyPurchaseOrderRepository.findCompanyPurchaseOrderByCompanyIdAndCurrencyIdAndAm(companyPurchaseOrderDTO.getCompanyId(), companyPurchaseOrderDTO.getCurrencyId(),companyPurchaseOrderDTO.getAm());
        if (!orderList.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_PURCHASE_ORDER_CURRENCY_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }

        if (null != companyPurchaseOrderDTO.getCurrencyId() && !companyPurchaseOrderDTO.getDetailDTOList().isEmpty()) {
            List<String> numberList = companyPurchaseOrderDTO.getDetailDTOList().stream().map(CompanyPurchaseOrderDetailDTO::getOrderNumber).collect(Collectors.toList());
            List<BigInteger> orderNumberList = companyPurchaseOrderDetailRepository.findByCompanyIdAndOrderNumber(numberList, companyPurchaseOrderDTO.getCompanyId());
            if (!orderNumberList.isEmpty()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_PURCHASE_ORDER_NUMBER_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
            }
        }


        CompanyPurchaseOrder order = new CompanyPurchaseOrder();
        order.setCompanyId(companyPurchaseOrderDTO.getCompanyId());
        order.setAm(companyPurchaseOrderDTO.getAm());
        order.setCurrencyId(companyPurchaseOrderDTO.getCurrencyId());
        order.setNotificationBalance(companyPurchaseOrderDTO.getNotificationBalance());
        companyPurchaseOrderRepository.save(order);
        log.info("[createCompanyPurchaseOrder] add company purchase order info :{}", JSONUtil.toJsonStr(order));

        if (!companyPurchaseOrderDTO.getDetailDTOList().isEmpty()) {
            List<CompanyPurchaseOrderDetail> detailList = new ArrayList<>();
            companyPurchaseOrderDTO.getDetailDTOList().forEach(v -> {
                CompanyPurchaseOrderDetail detail = new CompanyPurchaseOrderDetail();
                detail.setCurrencyId(v.getCurrencyId());
                detail.setPurchaseOrderId(order.getId());
                detail.setAmount(v.getAmount());
                detail.setBalance(v.getBalance());
                detail.setOrderNumber(v.getOrderNumber());
                detailList.add(detail);
            });
            if (!detailList.isEmpty()) {
                companyPurchaseOrderDetailRepository.saveAll(detailList);
                log.info("[createCompanyPurchaseOrder] add company purchase order detail info :{}", JSONUtil.toJsonStr(detailList));
            }
        }
    }

    private void checkPurchaseOrder(CompanyPurchaseOrderDTO companyPurchaseOrderDTO){
        if (!companyPurchaseOrderDTO.getDetailDTOList().isEmpty()) {
            Set<Long> currency = companyPurchaseOrderDTO.getDetailDTOList().stream().map(CompanyPurchaseOrderDetailDTO::getCurrencyId).collect(Collectors.toSet());
            if (currency.size() > 1 || currency.isEmpty()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_PURCHASE_ORDER_CURRENCY_DIFF.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
            } else {
                if (!companyPurchaseOrderDTO.getCurrencyId().equals(currency.stream().findFirst().get())) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_PURCHASE_ORDER_CURRENCY_DIFF.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
                }
            }
        }
    }

    public void checkPermissionByCompanyId(Long companyId) {
        if (Objects.isNull(companyId)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_PURCHASE_ORDER_CHECKPERMISSIONBYCOMPANYID_COMPANYISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        if (BooleanUtils.isTrue(SecurityUtils.isAdmin())){
            return;
        }
        List<Long> allAmIdsByCompanyId = accountBusinessAdministratorRepository.findAmIdsByCompanyId(companyId);
        boolean isCurrentCompanyAm = allAmIdsByCompanyId.contains(SecurityUtils.getUserId());
        if (BooleanUtils.isFalse(isCurrentCompanyAm)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_PURCHASE_ORDER_CHECKPERMISSIONBYCOMPANYID_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyCompanyPurchaseOrder(CompanyPurchaseOrderDTO companyPurchaseOrderDTO) {
        if (companyPurchaseOrderDTO.getCompanyId() == null || companyPurchaseOrderDTO.getAm() == null
                || companyPurchaseOrderDTO.getNotificationBalance() == null || companyPurchaseOrderDTO.getId() == null) {
            throw new CustomParameterizedException("parameter is null");
        }

        checkPermissionByCompanyId(companyPurchaseOrderDTO.getCompanyId());
        checkPurchaseOrder(companyPurchaseOrderDTO);

        List<CompanyPurchaseOrder> orderList = companyPurchaseOrderRepository.findCompanyPurchaseOrderByCompanyIdAndCurrencyIdAndAm(companyPurchaseOrderDTO.getCompanyId(), companyPurchaseOrderDTO.getCurrencyId(), companyPurchaseOrderDTO.getAm());
        if (!orderList.isEmpty()) {
            if (!orderList.get(0).getId().equals(companyPurchaseOrderDTO.getId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_PURCHASE_ORDER_CURRENCY_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
            }
        }



        if (null != companyPurchaseOrderDTO.getCurrencyId() && !companyPurchaseOrderDTO.getDetailDTOList().isEmpty()) {

            List<String> numberList = companyPurchaseOrderDTO.getDetailDTOList().stream().map(CompanyPurchaseOrderDetailDTO::getOrderNumber).collect(Collectors.toList());
            List<CompanyPurchaseOrderDetailModifyVO> orderNumberList = companyPurchaseOrderNativeRepository.findByCompanyIdAndOrderNumber(numberList, companyPurchaseOrderDTO.getCompanyId());
            if (!orderNumberList.isEmpty()) {
                for (CompanyPurchaseOrderDetailModifyVO detailVo : orderNumberList) {
                    for (CompanyPurchaseOrderDetailDTO detail : companyPurchaseOrderDTO.getDetailDTOList()) {
                        if (detailVo.getOrderNumber().equals(detail.getOrderNumber())) {
                            if (detail.getId() == null) {
                                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_PURCHASE_ORDER_NUMBER_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
                            } else {
                                if (!detail.getId().equals(detailVo.getId().longValue())) {
                                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_PURCHASE_ORDER_NUMBER_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
                                }
                            }
                        }
                    }
                }
            }
        }

        CompanyPurchaseOrder order = companyPurchaseOrderRepository.findById(companyPurchaseOrderDTO.getId()).orElse(null);
        if (order == null) {
            throw new CustomParameterizedException("No purchase order found");
        }

        order.setNotificationBalance(companyPurchaseOrderDTO.getNotificationBalance());
        order.setCompanyId(companyPurchaseOrderDTO.getCompanyId());
        order.setAm(companyPurchaseOrderDTO.getAm());
        order.setCurrencyId(companyPurchaseOrderDTO.getCurrencyId());

        if (!order.getPurchaseOrderDetailList().isEmpty()) {

            List<Long> deleteDetailId = new ArrayList<>();
            List<CompanyPurchaseOrderDetail> detailList = new ArrayList<>();

            List<CompanyPurchaseOrderDetailDTO> idNotNullList = companyPurchaseOrderDTO.getDetailDTOList().stream()
                    .filter(z -> z.getId() != null).collect(Collectors.toList());
            //存在的数据进行修改或者删除
            for (CompanyPurchaseOrderDetail od : order.getPurchaseOrderDetailList()) {
                boolean flag = false;
                for (CompanyPurchaseOrderDetailDTO dto : idNotNullList) {
                    if (dto.getId().equals(od.getId())) {
                        od.setCurrencyId(dto.getCurrencyId());
                        od.setAmount(dto.getAmount());
                        od.setBalance(dto.getBalance());
                        od.setOrderNumber(dto.getOrderNumber());
                        detailList.add(od);
                        flag = true;
                    }
                }
                if (!flag) {
                    deleteDetailId.add(od.getId());
                }
            }

            if (!deleteDetailId.isEmpty()) {
                List<CompanyPurchaseOrderDetail> details = order.getPurchaseOrderDetailList();
                Iterator<CompanyPurchaseOrderDetail> iterator = details.iterator();
                while (iterator.hasNext()) {
                    CompanyPurchaseOrderDetail i = iterator.next();
                    if (deleteDetailId.contains(i.getId())) {
                        iterator.remove();
                    }
                }
            }

            //获取新加入的数据
            List<CompanyPurchaseOrderDetailDTO> dtoList = companyPurchaseOrderDTO.getDetailDTOList().stream()
                    .filter(z -> z.getId() == null).collect(Collectors.toList());
            dtoList.forEach(v -> {
                CompanyPurchaseOrderDetail detail = new CompanyPurchaseOrderDetail();
                detail.setCurrencyId(v.getCurrencyId());
                detail.setPurchaseOrderId(order.getId());
                detail.setAmount(v.getAmount());
                detail.setBalance(v.getBalance());
                detail.setOrderNumber(v.getOrderNumber());
                detailList.add(detail);
            });

            companyPurchaseOrderRepository.save(order);
            log.info("[modifyCompanyPurchaseOrder] modify company purchase order info :{}", JSONUtil.toJsonStr(order));

            if (!detailList.isEmpty()) {
                companyPurchaseOrderDetailRepository.saveAll(detailList);
                log.info("[modifyCompanyPurchaseOrder] modify company purchase order detail info :{}", JSONUtil.toJsonStr(detailList));
            }
        }
    }
}
