package com.altomni.apn.company.vo.company;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class AccountCompanyCoAmFteVO implements Serializable {

    private boolean jobContainsFte;

    private boolean companyContainsFte;
}
