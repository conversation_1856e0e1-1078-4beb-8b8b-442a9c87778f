package com.altomni.apn.company.config.thread;

import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.config.env.EsfillerMQProperties;
import com.altomni.apn.company.service.elastic.EsFillerCompanyService;
import com.altomni.apn.company.service.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class CompanyContactSyncToMqThreadTask extends CopyTokenChildThread {

    private final EsFillerCompanyService esFillerCompanyService;

    private final RedisService redisService;

    private final CountDownLatch countDownLatch;

    private final List<Long> ids;

    private final int priority;

    private final EsfillerMQProperties esfillerMQProperties;

    private final CanalService canalService;

    public CompanyContactSyncToMqThreadTask(EsFillerCompanyService esFillerCompanyService, RedisService redisService, CountDownLatch countDownLatch, List<Long> ids, int priority, EsfillerMQProperties esfillerMQProperties, CanalService canalService) {
        super();
        this.esFillerCompanyService = esFillerCompanyService;
        this.redisService = redisService;
        this.countDownLatch = countDownLatch;
        this.ids = ids;
        this.priority = priority;
        this.esfillerMQProperties = esfillerMQProperties;
        this.canalService = canalService;
    }

    @Override
    public void runTask() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            esFillerCompanyService.extractCompanyContactToHrMq(ids, priority);
        } catch (Exception e) {
            log.error("[EsFillerCompanyService: syncCompanyContactToHrMQ @{}] syncCompanyContactToHrMQ is error, contactIds: {}, error: {}", SecurityUtils.getUserId(), ids, ExceptionUtils.getStackTrace(e));
            canalService.insertAll(ids, SyncIdTypeEnum.HR_COMPANY_CONTACT, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "Extract Company contact Error" +
                    "\n\tcontact IDs: " + ids +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        } finally {
            countDownLatch.countDown();
            stopWatch.stop();
            //log.info("[EsFillerJobService: syncJobToMQ @{}] SyncJobsToMQFinished time = [{}ms], successIds = [{}], failIds = [{}]", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), CollUtil.disjunction(ids, failedIds), failedIds);
        }
    }


}
