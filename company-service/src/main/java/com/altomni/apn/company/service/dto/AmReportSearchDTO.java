package com.altomni.apn.company.service.dto;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * am report search dto
 * <AUTHOR>
 */
@Data
public class AmReportSearchDTO implements Serializable {

    private Long companyId;
    private List<JobType> jobType;
    private List<Long> candidate;
    private UserRole userRole;
    private List<Long> salesLeadList;
}
