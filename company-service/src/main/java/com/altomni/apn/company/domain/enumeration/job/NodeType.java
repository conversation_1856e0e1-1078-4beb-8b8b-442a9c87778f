package com.altomni.apn.company.domain.enumeration.job;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The NodeType enumeration.
 * <AUTHOR>
 */
public enum NodeType implements ConvertedEnum<Integer> {

    SUBMIT_TO_JOB(10),

    SUBMIT_TO_CLIENT(20),

    INTERVIEW(30),

    OFFER(40),

    OFFER_ACCEPT(41),

    COMMISSION(50),

    ON_BOARD(60),

    OFF_BOARDED(100);

    private final Integer dbValue;

    NodeType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<NodeType, Integer> resolver =
        new ReverseEnumResolver<>(NodeType.class, NodeType::toDbValue);

    public static NodeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
