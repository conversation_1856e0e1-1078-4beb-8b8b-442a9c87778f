package com.altomni.apn.company.service.dto.projectTeam;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
/**
*
* <AUTHOR>
* date:2023-04-18
*/
@Data
@ApiModel(description = "search for projectTeam")
public class CompanyProjectTeamSearchDTO implements Serializable {

    @ApiModelProperty(value = "the id for company.")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "the name for projectTeam.")
    private String name;

}
