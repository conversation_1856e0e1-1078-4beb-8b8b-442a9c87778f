package com.altomni.apn.company.domain.company;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * A CompanyAdditionalInfo.
 */
@ApiModel(description = "Company additional info")
@Entity
@NoArgsConstructor
@Data
@Table(name = "company_additional_info")
public class CompanyAdditionalInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @ApiModelProperty(value = "The id for company")
    @Column(name = "company_id")
    private Long accountCompanyId;

    @ApiModelProperty(value = "The additionalInfo for company")
    @Column(name = "extended_info")
    private String extendedInfo;

}