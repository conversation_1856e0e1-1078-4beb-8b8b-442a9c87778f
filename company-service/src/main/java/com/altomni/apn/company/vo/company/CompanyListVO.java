package com.altomni.apn.company.vo.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@ApiModel(description = "Vo for companyList")
@NoArgsConstructor
@Data
public class CompanyListVO {

    @ApiModelProperty(value = "The data for company list.")
    private List<Object> data;

    @ApiModelProperty(value = "The uuid for search params.")
    private String uuid;

    @ApiModelProperty(value = "The count for no contract company.")
    private Integer noContractCount;

}
