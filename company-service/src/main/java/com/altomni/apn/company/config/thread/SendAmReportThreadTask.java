package com.altomni.apn.company.config.thread;

import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.vo.CompanyVo;
import com.altomni.apn.company.service.am.AmReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;

@Slf4j
public class SendAmReportThreadTask extends CopyTokenChildThread {

    private final AmReportService amReportService;

    private final CompanyVo company;

    private final String mailTemplate;

    private final String imageTemplate;

    public SendAmReportThreadTask(AmReportService amReportService, CompanyVo company, String mailTemplate, String imageTemplate) {
        super();
        this.amReportService = amReportService;
        this.company = company;
        this.mailTemplate = mailTemplate;
        this.imageTemplate = imageTemplate;
    }

    @Override
    public void runTask() {
        amReportService.sendAmReportAmEmail(company, mailTemplate, imageTemplate);
    }

}
