package com.altomni.apn.company.service.dto.overview;

import com.altomni.apn.company.service.dto.contact.CompanyContactDTO;
import com.altomni.apn.company.service.dto.salesLead.SalesLeadProspectDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "dto for salesLead prospect")
public class CompanySalesLeadDetailProspectDTO extends SalesLeadProspectDTO implements Serializable {

    @ApiModelProperty(value = "The salesLead for company.")
    @NotNull
    @UniqueElements
    private List<CompanyContactDTO> salesLeadClientContacts;
}