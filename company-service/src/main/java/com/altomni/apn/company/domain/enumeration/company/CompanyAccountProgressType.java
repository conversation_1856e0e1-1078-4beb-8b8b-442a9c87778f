package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The IndustryType enumeration.
 */
public enum CompanyAccountProgressType implements ConvertedEnum<Integer> {
    TWENTY_PERCENT(20),
    FORTY_PERCENT(40),
    SIXTY_PERCENT(60),
    EIGHTY_PERCENT(80);

    private final int dbValue;

    CompanyAccountProgressType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CompanyAccountProgressType, Integer> resolver = new ReverseEnumResolver<>(CompanyAccountProgressType.class, CompanyAccountProgressType::toDbValue);

    public static CompanyAccountProgressType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
