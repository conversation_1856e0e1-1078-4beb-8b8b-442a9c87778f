package com.altomni.apn.company.repository.company.folder;


import com.altomni.apn.company.domain.enumeration.folder.FolderPermission;
import com.altomni.apn.company.domain.folder.CompanyCustomFolderSharedUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CompanyCustomFolderSharedUserRepository extends JpaRepository<CompanyCustomFolderSharedUser, Long> {

    CompanyCustomFolderSharedUser findByFolderIdAndUserIdAndPermission(Long folderId, Long userId, FolderPermission permission);

    List<CompanyCustomFolderSharedUser> findAllByFolderIdInAndUserIdAndPermission(List<Long> folderIds, Long userId, FolderPermission permission);

    @Query(value = "SELECT id FROM company_custom_folder_shared_user WHERE folder_id = ?1", nativeQuery = true)
    List<Long> findAllUserIdByFolderId(Long folderId);

    List<CompanyCustomFolderSharedUser> findAllByFolderId(Long folderId);

    @Query(value = "SELECT id FROM company_custom_folder_shared_user WHERE folder_id IN ?1", nativeQuery = true)
    List<Long> findAllIdByFolderIdIn(List<Long> folderIds);

    @Query(value = "SELECT id FROM company_custom_folder_shared_user WHERE folder_id IN ?1 AND user_id = ?2", nativeQuery = true)
    List<Long> findAllIdByByFolderIdInAndUserId(List<Long> folderIds, Long userId);

    List<CompanyCustomFolderSharedUser> findAllByFolderIdInAndUserId(List<Long> folderIds, Long userId);

    List<CompanyCustomFolderSharedUser> findAllByFolderIdIn(List<Long> folderIds);
}
