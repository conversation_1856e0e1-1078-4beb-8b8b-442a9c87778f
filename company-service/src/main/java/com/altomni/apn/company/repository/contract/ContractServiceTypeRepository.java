package com.altomni.apn.company.repository.contract;

import com.altomni.apn.company.domain.contract.ContractServiceType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ContractServiceTypeRepository extends JpaRepository<ContractServiceType, Long> {

    List<ContractServiceType> findAllByContractId(Long contractId);

    List<ContractServiceType> findAllByContractIdIn(List<Long> contractIds);
}
