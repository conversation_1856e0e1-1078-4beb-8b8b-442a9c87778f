package com.altomni.apn.company.repository.company.folder;


import com.altomni.apn.company.domain.folder.CompanyCustomFolderSharedTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface CompanyCustomFolderSharedTeamRepository extends JpaRepository<CompanyCustomFolderSharedTeam, Long> {

    @Query(value = "SELECT st.* FROM company_custom_folder_shared_team st INNER JOIN permission_user_team ptu ON st.team_id = ptu.team_id WHERE st.folder_id = ?1 AND ptu.user_id = ?2 AND st.permission = ?3 LIMIT 1", nativeQuery = true)
    CompanyCustomFolderSharedTeam findByFolderIdAndUserIdAndPermission(Long folderId, Long userId, Integer permission);

    @Query(value = "SELECT st.* FROM company_custom_folder_shared_team st INNER JOIN permission_user_team ptu ON st.team_id = ptu.team_id WHERE st.folder_id IN ?1 AND ptu.user_id = ?2 AND st.permission = ?3 LIMIT 1", nativeQuery = true)
    List<CompanyCustomFolderSharedTeam> findByFolderIdInAndUserIdAndPermission(List<Long> folderIds, Long userId, Integer permission);

    @Query(value = "SELECT id FROM company_custom_folder_shared_team WHERE folder_id = ?1", nativeQuery = true)
    List<Long> findAllTeamIdByFolderId(Long folderId);

    List<CompanyCustomFolderSharedTeam> findAllByFolderId(Long folderId);

    @Query(value = "SELECT id FROM company_custom_folder_shared_team WHERE folder_id IN ?1", nativeQuery = true)
    List<Long> findAllIdByFolderIdIn(List<Long> folderIds);

    @Query(value = "SELECT st.* FROM company_custom_folder_shared_team st INNER JOIN permission_user_team ptu ON st.team_id = ptu.team_id WHERE st.folder_id IN ?1 AND ptu.user_id = ?2", nativeQuery = true)
    List<CompanyCustomFolderSharedTeam> findAllIdByFolderIdInAndUserId(List<Long> folderIds, Long userId);

    List<CompanyCustomFolderSharedTeam> findAllByFolderIdIn(List<Long> folderIds);

}
