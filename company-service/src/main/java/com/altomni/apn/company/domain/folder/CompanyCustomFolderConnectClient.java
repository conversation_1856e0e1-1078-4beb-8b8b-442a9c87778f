package com.altomni.apn.company.domain.folder;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A CompanyCustomFolderConnectClient.
 */
@Entity
@Table(name = "company_custom_folder_connect_client")
@NoArgsConstructor
@Data
public class CompanyCustomFolderConnectClient extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "folder_id")
    private Long folderId;

    @Column(name = "company_id")
    private Long companyId;

    public CompanyCustomFolderConnectClient(Long folderId, Long companyId) {
        this.folderId = folderId;
        this.companyId = companyId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CompanyCustomFolderConnectClient that = (CompanyCustomFolderConnectClient) o;
        return folderId.equals(that.folderId) && companyId.equals(that.companyId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(folderId, companyId);
    }
}
