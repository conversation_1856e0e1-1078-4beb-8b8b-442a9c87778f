package com.altomni.apn.company.repository.contract;

import com.altomni.apn.company.domain.contract.Contract;
import com.altomni.apn.company.domain.enumeration.contract.ContractStatus;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ContractRepository extends JpaRepository<Contract, Long>, JpaSpecificationExecutor<Contract> {

    Page<Contract> findAllByAccountCompanyId(Long companyId, Pageable pageable);

//    @Query(value = "select new com.altomni.apn.company.service.dto.ContractDTO(c.createdBy) from Contract c left join Company co on c.companyId = co.id and co.tenantId=:tenantId where c.id=:id")
//    ContractDTO findByIdAndTenantId(Long id, Long tenantId);

    @Query(value = "select distinct c.accountCompanyId from Contract c")
    List<Long> findAllContractedCompany();

    @NotNull
    @Override
    Optional<Contract> findById(@NotNull Long id);

    List<Contract> findAllByAccountCompanyIdAndPreviousContractIdIsNull(Long companyId);

    List<Contract> findAllByAccountCompanyIdAndStatus(Long companyId, ContractStatus status);

    List<Contract> findAllByAccountCompanyIdIn(List<Long> companyIds);

    @Query("select c from Contract c where c.id not in (select distinct cr.contractId from ContractBusinessRelation cr)")
    List<Contract> findNoBusinessContractList();


}
