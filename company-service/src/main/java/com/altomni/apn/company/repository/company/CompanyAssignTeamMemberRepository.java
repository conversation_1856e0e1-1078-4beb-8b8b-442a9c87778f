package com.altomni.apn.company.repository.company;

import com.altomni.apn.company.domain.company.CompanyAssignTeamMember;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface CompanyAssignTeamMemberRepository extends JpaRepository<CompanyAssignTeamMember, Long>, JpaSpecificationExecutor<CompanyAssignTeamMember> {

    void deleteAllByCompanyId(Long companyId);

}
