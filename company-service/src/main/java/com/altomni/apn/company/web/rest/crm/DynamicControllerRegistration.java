package com.altomni.apn.company.web.rest.crm;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.company.config.env.CrmForwardUrlProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Slf4j
@Configuration
public class DynamicControllerRegistration {

    @Resource
    private CrmForwardUrlProperties crmForwardUrlProperties;

    @Resource
    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    public void handleRequest(HttpServletRequest request, HttpServletResponse response) {
        //模拟注册使用
    }

    @PostConstruct
    public void registerDynamicControllers() {
        if (CollUtil.isNotEmpty(crmForwardUrlProperties.getUrlMap())) {
            crmForwardUrlProperties.getUrlMap().forEach((k, v) -> v.forEach(url -> {
                try {
                    registerHandler(url, RequestMethod.valueOf(k));
                    log.info("Registered handler for path: {}, method: {}", url, k);
                } catch (NoSuchMethodException e) {
                    throw new RuntimeException(e);
                }
            }));
        }
    }

    private void registerHandler(String path, RequestMethod method) throws NoSuchMethodException {
        requestMappingHandlerMapping.registerMapping(RequestMappingInfo
                        .paths("/crm" + path).methods(method).build(),
                this, this.getClass().getMethod("handleRequest", HttpServletRequest.class, HttpServletResponse.class));
    }
}
