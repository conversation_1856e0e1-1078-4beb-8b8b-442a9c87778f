package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class NoPoachingTypeConverter extends AbstractAttributeConverter<NoPoachingType, Integer> {
    public NoPoachingTypeConverter() {
        super(NoPoachingType::toDbValue, NoPoachingType::fromDbValue);
    }
}
