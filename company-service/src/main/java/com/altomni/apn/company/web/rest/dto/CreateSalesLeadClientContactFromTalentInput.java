package com.altomni.apn.company.web.rest.dto;

import com.altomni.apn.common.dto.company.SalesLeadClientContactDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@ApiModel(description = "dto for CreateSalesLeadClientContactFromTalent input")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CreateSalesLeadClientContactFromTalentInput extends SalesLeadClientContactDTO implements Serializable {
    @ApiModelProperty(value = "talent id")
    private Long talentId;
}
