package com.altomni.apn.company.domain.business;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "contact_service_type_relation")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ContactServiceTypeRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "contact_id", updatable = false)
    private Long contactId;

    @Column(name = "service_type_id")
    private Integer serviceTypeId;

    public ContactServiceTypeRelation(Long clientContactId, Integer serviceTypeId) {
        this.contactId = clientContactId;
        this.serviceTypeId = serviceTypeId;
    }
}
