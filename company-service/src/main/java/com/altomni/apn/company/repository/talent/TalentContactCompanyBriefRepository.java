package com.altomni.apn.company.repository.talent;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.talent.TalentContact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TalentContactCompanyBriefRepository extends JpaRepository<TalentContact, Long> {

    List<TalentContact> findAllByTalentIdAndStatusOrderBySortAsc(Long talentId, TalentContactStatus status);

    List<TalentContact> findAllByTalentIdInAndTypeInAndStatusOrderBySortAsc(List<Long> talentIds, List<ContactType> types, TalentContactStatus status);

    @Query(value = "SELECT tc FROM TalentContact tc WHERE tc.talentId IN (?1) AND tc.type IN (?2) AND tc.status = ?3 AND (tc.verificationStatus IS NULL OR tc.verificationStatus != com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus.WRONG_CONTACT)")
    List<TalentContact> findAllByTalentIdInAndTypeInAndStatusAndVerificationStatusOrderBySortAsc(List<Long> talentIds, List<ContactType> types, TalentContactStatus status);

    @Query(value = "SELECT * FROM talent_contact WHERE talent_id = ?1 AND jhi_type = ?2 AND status = ?3 ORDER BY sort", nativeQuery = true)
    List<TalentContact> findByTalentIdAndTypeAndStatusOrderBySortAsc(Long talentId, Integer type, Integer status);

    List<TalentContact> findAllByTalentId(Long talentId);

    List<TalentContact> findAllByTalentIdIn(List<Long> talentIds);

    List<TalentContact> findAllByTalentIdInAndTypeIn(List<Long> talentIds, List<ContactType> types);

    List<TalentContact> findAllByTalentIdInAndStatusOrderBySortAsc(List<Long> talentIds, TalentContactStatus status);

    List<TalentContact> findAllByTalentIdInAndStatus(List<Long> talentIds, TalentContactStatus status);

    List<TalentContact> findAllByTenantIdAndStatusAndTypeInAndContactIn(Long tenantId, TalentContactStatus status, List<ContactType> type, List<String> contacts);

    @Query(value = "SELECT tc FROM TalentContact tc WHERE tc.tenantId = ?1 AND tc.status = ?2 AND tc.type IN (?3) AND tc.contact IN (?4) AND (tc.verificationStatus IS NULL OR tc.verificationStatus != com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus.WRONG_CONTACT)")
    List<TalentContact> findAllByTenantIdAndStatusAndTypeInAndContactInAndVerificationStatusIsNot(Long tenantId, TalentContactStatus status, List<ContactType> type, List<String> contacts);

}
