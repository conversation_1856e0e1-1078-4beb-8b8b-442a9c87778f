package com.altomni.apn.company.domain.enumeration.note;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CompanyNoteCategoryConverter extends AbstractAttributeConverter<CompanyNoteCategory, Integer> {
    public CompanyNoteCategoryConverter() {
        super(CompanyNoteCategory::toDbValue, CompanyNoteCategory::fromDbValue);
    }
}
