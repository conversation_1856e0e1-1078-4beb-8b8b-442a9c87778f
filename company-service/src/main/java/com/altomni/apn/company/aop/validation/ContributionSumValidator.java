package com.altomni.apn.company.aop.validation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.company.service.dto.overview.CompanyClientDTO;
import com.altomni.apn.company.service.dto.overview.CompanySalesLeadDetailClientDTO;
import com.altomni.apn.company.service.dto.salesLead.SalesLeadAdministratorDTO;
import com.altomni.apn.company.service.dto.salesLead.SalesLeadClientDTO;
import com.altomni.apn.company.service.dto.salesLead.SalesLeadUpdateDTO;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

public class ContributionSumValidator implements ConstraintValidator<ContributionSum, Object> {
    private static final Class<?>[] SUPPORTED_CLASSES = { SalesLeadClientDTO.class, SalesLeadUpdateDTO.class, CompanyClientDTO.class};
    private final Double CLIENT_ACCOUNT_PROGRESS = 1.20;

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        if (value == null || Stream.of(SUPPORTED_CLASSES).noneMatch(cls -> cls.isInstance(value))) {
            return false;
        }

        int sum;
        if (value instanceof SalesLeadClientDTO) {
            SalesLeadClientDTO salesLeadClientDTO = (SalesLeadClientDTO) value;
            sum = calculateContributionSum(salesLeadClientDTO.getSalesLeadsOwners(), salesLeadClientDTO.getSalesLeadsBdOwners());
            return sum == 100;
        } else if (value instanceof CompanyClientDTO) {
            CompanyClientDTO companyClientDTO = (CompanyClientDTO) value;
            List<CompanySalesLeadDetailClientDTO> salesLeadDetails = companyClientDTO.getSalesLeadDetails();
            for (CompanySalesLeadDetailClientDTO item : salesLeadDetails) {
                if (100 != calculateContributionSum(item.getSalesLeadsOwners(), item.getSalesLeadsBdOwners())) {
                    context.buildConstraintViolationWithTemplate("The sum of contribution must be 100")
                            .addPropertyNode("salesLeadDetails")
                            .addConstraintViolation();
                    return false;
                }
            }
            return true;
        } else {
            SalesLeadUpdateDTO salesLeadUpdateDTO = (SalesLeadUpdateDTO) value;
            List<Long> accountManagers = salesLeadUpdateDTO.getAccountManagers();
            if (CollUtil.isNotEmpty(accountManagers) && ObjectUtil.isEmpty(salesLeadUpdateDTO.getId())) {
                context.buildConstraintViolationWithTemplate("AM must be empty if id is empty")
                        .addPropertyNode("accountManagers")
                        .addConstraintViolation();
                return false;
            }
            if (CollUtil.isNotEmpty(accountManagers) && ObjectUtil.isNotEmpty(salesLeadUpdateDTO.getAccountProgress()) && salesLeadUpdateDTO.getAccountProgress().compareTo(BigDecimal.valueOf(CLIENT_ACCOUNT_PROGRESS)) < 0) {
                context.buildConstraintViolationWithTemplate("AM must be empty if accountProgress is not empty")
                        .addPropertyNode("accountManagers")
                        .addConstraintViolation();
                return false;
            }
            sum = calculateContributionSum(salesLeadUpdateDTO.getSalesLeadsOwners(), salesLeadUpdateDTO.getSalesLeadsBdOwners());
            if (CollUtil.isEmpty(accountManagers) && sum != 0) {
                context.buildConstraintViolationWithTemplate("Contribution sum must empty")
                        .addPropertyNode("salesLeadsOwners")
                        .addConstraintViolation();
                return false;
            }
            return (sum == 100 && CollUtil.isNotEmpty(accountManagers)) || (sum == 0 && CollUtil.isEmpty(accountManagers));
        }
    }

    private int calculateContributionSum(List<SalesLeadAdministratorDTO> salesLeadsOwners, List<SalesLeadAdministratorDTO> salesLeadsBdOwners) {
        int sum = salesLeadsOwners.stream()
                .map(SalesLeadAdministratorDTO::getContribution)
                .filter(Objects::nonNull)
                .mapToInt(Integer::intValue)
                .sum();
        sum += salesLeadsBdOwners.stream()
                .map(SalesLeadAdministratorDTO::getContribution)
                .filter(Objects::nonNull)
                .mapToInt(Integer::intValue)
                .sum();
        return sum;
    }
}