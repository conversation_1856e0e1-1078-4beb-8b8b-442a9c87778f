package com.altomni.apn.company.repository.company;

import com.altomni.apn.company.domain.company.CompanyTagRelation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface CompanyTagRelationRepository extends JpaRepository<CompanyTagRelation, Long> {

    List<CompanyTagRelation> findAllByAccountCompanyId(Long companyId);

    List<CompanyTagRelation> findAllByAccountCompanyIdIn(List<Long> companyIds);

}
