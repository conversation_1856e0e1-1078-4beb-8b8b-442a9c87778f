package com.altomni.apn.company.service.company.note.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ElasticSearchUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.note.CompanyProgressNote;
import com.altomni.apn.company.domain.company.note.CompanyProgressNoteContactRelation;
import com.altomni.apn.company.domain.enumeration.note.CompanyNoteType;
import com.altomni.apn.company.domain.business.AccountBusiness;
import com.altomni.apn.company.domain.vm.EntityNameVM;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.company.note.CompanyProgressNoteContactRelationRepository;
import com.altomni.apn.company.repository.company.note.CompanyProgressNoteRepository;
import com.altomni.apn.company.repository.business.AccountBusinessRepository;
import com.altomni.apn.company.repository.business.SalesLeadClientContactRepository;
import com.altomni.apn.company.repository.talent.TalentServiceRepository;
import com.altomni.apn.company.repository.user.UserServiceRepository;
import com.altomni.apn.company.service.company.note.CompanyProgressNoteService;
import com.altomni.apn.company.service.dto.es.NoteSearchDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteBasicDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteSearchDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteSearchFilterDTO;
import com.altomni.apn.company.service.elastic.impl.EsCompanyDataServiceImpl;
import com.altomni.apn.company.vo.note.CompanyProgressNoteVO;
import com.altomni.apn.company.web.rest.vm.company.note.CompanyNoteToEsVM;
import com.altomni.apn.user.repository.user.UserBriefRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyProgressNoteServiceImpl implements CompanyProgressNoteService {

    private final Double CLIENT_ACCOUNT_PROGRESS = 1.20;

    @Resource
    private CompanyProgressNoteRepository companyProgressNoteRepository;

    @Resource
    private SalesLeadClientContactRepository salesLeadClientContactRepository;

    @Resource
    private UserBriefRepository userBriefRepository;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private AccountBusinessRepository accountBusinessRepository;

    @Resource
    private UserServiceRepository userServiceRepository;

    @Resource
    private TalentServiceRepository talentServiceRepository;

    @Resource
    private EsCompanyDataServiceImpl esCompanyDataService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    @Resource
    private CompanyProgressNoteContactRelationRepository companyProgressNoteContactRelationRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompanyProgressNoteVO createProgressNote(CompanyProgressNoteDTO companyProgressNoteDTO) {
        CompanyProgressNote companyProgressNote = companyProgressNoteRepository.save(CompanyProgressNote.fromCompanyProgressNoteDTO(companyProgressNoteDTO));
        saveNoteContact(companyProgressNote, companyProgressNoteDTO.getClientContactIds());
        return toVo(companyProgressNote);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompanyProgressNoteVO updateProgressNote(Long id, CompanyProgressNoteBasicDTO companyProgressNoteDTO) {
        CompanyProgressNote companyProgressNote = companyProgressNoteRepository.findById(id).orElseThrow(() -> new NotFoundException("note does not exist."));
        ServiceUtils.myCopyProperties(companyProgressNoteDTO, companyProgressNote);
        companyProgressNote.setLastModifiedDate(Instant.now());
        companyProgressNoteRepository.save(companyProgressNote);
        saveNoteContact(companyProgressNote, companyProgressNoteDTO.getClientContactIds());
        return toVo(companyProgressNote);
    }

    private CompanyProgressNoteVO toVo(CompanyProgressNote companyProgressNote) {
        CompanyProgressNoteVO companyProgressNoteVO = new CompanyProgressNoteVO();
        ServiceUtils.myCopyProperties(companyProgressNote, companyProgressNoteVO);
        return companyProgressNoteVO;
    }

    @Override
    public String searchCompanyProgressNote(CompanyProgressNoteSearchDTO companyProgressNoteSearchDTO, Pageable pageable, HttpHeaders headers) throws IOException {

        NoteSearchDTO noteSearchDTO = new NoteSearchDTO();
        noteSearchDTO.setTenantId(SecurityUtils.getTenantId());
        noteSearchDTO.setModule(CompanyNoteType.PROGRESS_NOTE);
        noteSearchDTO.setTimeZone(companyProgressNoteSearchDTO.getTimezone());
        noteSearchDTO.setLanguage(companyProgressNoteSearchDTO.getLanguage().toDbValue());
        noteSearchDTO.setCompanyIds(Arrays.asList(companyProgressNoteSearchDTO.getCompanyId()));
        noteSearchDTO.setSalesLeadIds(Arrays.asList(companyProgressNoteSearchDTO.getSalesLeadId()));
        noteSearchDTO.setNote(companyProgressNoteSearchDTO.getNote());
        noteSearchDTO.setClientContactIds(Arrays.asList(companyProgressNoteSearchDTO.getClientContactId()));

        HttpResponse response = esCompanyDataService.searchCompanyNote(noteSearchDTO, pageable);
        if (response == null || HttpStatus.OK.value() != response.getCode()) {
            headers.set("Pagination-Count", "0");
            return "[]";
        } else {
            headers.set("Pagination-Count", ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders()) + "");
            return response.getBody();
        }
    }

    @Override
    public String searchCompanyProgressNoteByContactId(CompanyProgressNoteSearchFilterDTO companyProgressNoteSearchFilterDTO, Pageable pageable, HttpHeaders headers) throws IOException {

        NoteSearchDTO noteSearchDTO = new NoteSearchDTO();
        noteSearchDTO.setTenantId(SecurityUtils.getTenantId());
        noteSearchDTO.setModule(CompanyNoteType.PROGRESS_NOTE);
        noteSearchDTO.setTimeZone(companyProgressNoteSearchFilterDTO.getTimezone());
        noteSearchDTO.setLanguage(companyProgressNoteSearchFilterDTO.getLanguage().toDbValue());
        noteSearchDTO.setCompanyIds(companyProgressNoteSearchFilterDTO.getCompanyIds());
        noteSearchDTO.setSalesLeadIds(companyProgressNoteSearchFilterDTO.getSalesLeadIds());
        noteSearchDTO.setNote(companyProgressNoteSearchFilterDTO.getNote());
        noteSearchDTO.setClientContactIds(companyProgressNoteSearchFilterDTO.getClientContactIds());

        HttpResponse response = esCompanyDataService.searchCompanyNote(noteSearchDTO, pageable);
        if (response == null) {
            return "";
        } else if (response.getCode() != HttpStatus.OK.value()) {
            throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
        } else {
            headers.set("Pagination-Count", ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders()) + "");
            return response.getBody();
        }
    }

    private void saveNoteContact(CompanyProgressNote note, List<Long> contactIdList) {
        Set<Long> contactIds = CollUtil.isEmpty(contactIdList) ? new HashSet<>() : new HashSet<>(contactIdList);

        List<CompanyProgressNoteContactRelation> existRelationList = companyProgressNoteContactRelationRepository.findAllByNoteId(note.getId());

        List<CompanyProgressNoteContactRelation> deleteRelationList = existRelationList.stream().filter(o -> !contactIds.contains(o.getClientContactId())).toList();
        existRelationList.removeAll(deleteRelationList);

        Set<Long> existRelationContactIds = existRelationList.stream().map(CompanyProgressNoteContactRelation::getClientContactId).collect(Collectors.toSet());
        contactIds.forEach(o -> {
            if (!existRelationContactIds.contains(o)) {
                existRelationList.add(new CompanyProgressNoteContactRelation(note.getId(), o));
            }
        });

        companyProgressNoteContactRelationRepository.deleteAllByIdInBatch(deleteRelationList.stream().map(CompanyProgressNoteContactRelation::getId).collect(Collectors.toList()));
        companyProgressNoteContactRelationRepository.saveAllAndFlush(existRelationList);
    }

}
