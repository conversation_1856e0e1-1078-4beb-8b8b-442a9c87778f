package com.altomni.apn.company.vo.note;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.company.domain.enumeration.company.CompanyContactType;
import com.altomni.apn.company.domain.enumeration.company.CompanyContactTypeConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@AllArgsConstructor
@ApiModel(description = "Vo for company progress note")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CompanyProgressNoteVO extends AbstractAuditingEntity implements Serializable {

    @ApiModelProperty(value = "the id for progress note.")
    private Long id;

    @ApiModelProperty(value = "the id for company.")
    private Long companyId;

    @ApiModelProperty(value = "the id for clientContact.")
    public List<Long> clientContactIds;

    @ApiModelProperty(value = "the name for clientContact.")
    private String clientContactName;

    @ApiModelProperty(value = "the contactType for note.")
    @Convert(converter = CompanyContactTypeConverter.class)
    private CompanyContactType contactType;

    @ApiModelProperty(value = "the contactDate for note.")
    private String contactTime;

    @ApiModelProperty(value = "the id for salesLead.")
    private Long salesLeadId;

    @ApiModelProperty(value = "the text for note.")
    private String note;

    @ApiModelProperty(value = "the name for creator.")
    private String creator;

    @ApiModelProperty(value = "the id for creator.")
    private Long creatorId;

    @Override
    public String toString() {
        return "CompanyProgressNoteVO{" +
                "id=" + id +
                ", companyId=" + companyId +
                ", clientContactIds=" + clientContactIds +
                ", clientContactName=" + clientContactName +
                ", contactType=" + contactType +
                ", salesLeadId=" + salesLeadId +
                ", note='" + note + '\'' +
                ", creator='" + creator + '\'' +
                '}';
    }
}
