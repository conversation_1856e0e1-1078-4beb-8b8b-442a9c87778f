package com.altomni.apn.company.domain.talent;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.talent.TalentAdditionalInfo;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A Talent.
 */
@ApiModel(description = "Talent entity. Used to synchronize contacts")
@Entity
@Data
@Table(name = "talent")
@AllArgsConstructor
@NoArgsConstructor
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Deprecated
public class TalentCompanyMigrateBrief extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The tenant id talent belongs to. This is saved from the user's tenant when creating talent.")
    @JsonIgnore
    @Column(name = "tenant_id", nullable = false, updatable = false)
    private Long tenantId;

    @ApiModelProperty(value = "first name. required in US.", required = true)
    @Column(name = "first_name")
    private String firstName;

    @ApiModelProperty(value = "last name. required in CN.", required = true)
    @Column(name = "last_name")
    private String lastName;

    @ApiModelProperty(value = "full name. Either firstName & lastName (in US) or full name (in CN) are required. Either way, " +
            "the full name can not be null.")
    @Column(name = "full_name", nullable = false)
    private String fullName;

    @ApiModelProperty(value = "url link to photo")
    @Column(name = "photo_url")
    private String photoUrl;

    @OneToOne(fetch = FetchType.EAGER, cascade = {CascadeType.ALL})
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "additional_info_id")
    private TalentAdditionalInfo talentAdditionalInfo;

}
