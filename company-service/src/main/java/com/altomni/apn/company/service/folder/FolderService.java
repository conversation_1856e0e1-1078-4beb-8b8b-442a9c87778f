package com.altomni.apn.company.service.folder;

import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.enumeration.folder.FolderType;
import com.altomni.apn.company.domain.folder.CompanySearchFolder;
import com.altomni.apn.company.domain.vm.CompanyCustomFolderVM;
import com.altomni.apn.company.service.dto.folder.*;
import com.altomni.apn.company.vo.folder.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;

public interface FolderService {

    List<CategoryFolderCountVO> countCategoryFolders() throws IOException;

//    List<String> querySearchHistory();

    SearchFolderVO createSearchFolder(SearchFolderDTO searchFolderDTO);

    SearchFolderVO querySearchFolder(Long id);

    void deleteSearchFolder(FolderIdDTO folderIdDTO);

    Page<CompanySearchFolder> searchSearchFolder(FolderSearchDTO folderSearchDTO, Pageable pageable);

    List<SearchFolderVO> toVo(List<CompanySearchFolder> companySearchFolderList);

    CustomFolderVO createCustomFolder(CustomFolderDTO customFolderDTO);

    CustomFolderVO updateCustomFolder(Long folderId, CustomFolderDTO customFolderDTO);

    CustomFolderVO queryCustomFolder(Long folderId);

    void deleteCustomFolder(FolderIdDTO folderIdDTO);

    Page<CompanyCustomFolderVM> searchCustomFolder(CustomFolderSearchDTO customFolderSearchDTO, Pageable pageable);

    List<CustomFolderSearchVO> toVo(List<CompanyCustomFolderVM> companyCustomFolderList, FolderType folderType);

    void addCustomFolderCompany(FolderIdMoveCompanyDTO folderIdMoveCompanyDTO);

    void deleteCustomFolderCompany(FolderIdDeleteCompanyDTO folderIdDeleteCompanyDTO);

    List<FolderSearchVO> searchFolder(FolderSearchDTO folderSearchDTO);

    void deleteSharedFolder(FolderIdDTO folderIdDTO);

    void updateCompaniesFolder(List<Long> companyIds, List<Long> toFolderIds, List<Long> fromFolderIds);

    List<FolderSearchVO> searchFolderList(CategoryFolderType type);

    void checkCustomFolderPermission(List<Long> folderIds);

    void deleteCustomFolderConnectClientByCreatedBy(String createdBy);

}
