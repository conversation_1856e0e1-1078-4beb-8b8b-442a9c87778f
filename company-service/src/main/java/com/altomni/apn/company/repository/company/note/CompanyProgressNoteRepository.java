package com.altomni.apn.company.repository.company.note;

import com.altomni.apn.company.domain.company.note.CompanyProgressNote;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

@Repository
public interface CompanyProgressNoteRepository extends JpaRepository<CompanyProgressNote, Long> {

    @Query(value = "SELECT n FROM CompanyProgressNote n WHERE n.companyId = ?1")
    Page<CompanyProgressNote> findAllByCompanyId(Long companyId, Pageable pageable);

    @Query(value = "SELECT n FROM CompanyProgressNote n INNER JOIN AccountBusiness sl ON n.salesLeadId = sl.id WHERE n.companyId = ?1 AND sl.id = ?2")
    Page<CompanyProgressNote> findAllByCompanyIdAndSalesLeadId(Long companyId, Long salesLeadId, Pageable pageable);

    @Query(value = "SELECT n FROM CompanyProgressNote n WHERE n.companyId = ?1 AND n.note LIKE %?2%")
    Page<CompanyProgressNote> findAllByCompanyIdAndNote(Long companyId, String note, Pageable pageable);

    @Query(value = "SELECT n FROM CompanyProgressNote n INNER JOIN AccountBusiness sl ON n.salesLeadId = sl.id WHERE n.companyId = ?1\n" +
            "AND sl.id = ?2 AND n.note LIKE %?3%")
    Page<CompanyProgressNote> findAllByCompanyIdAndSalesLeadIdAndNote(Long companyId, Long salesLeadId, String note, Pageable pageable);

    @Modifying
    @Transactional
    @Query(value = "UPDATE company_progress_note n SET n.last_sync_time=:lastSyncTime WHERE n.id=:noteId", nativeQuery = true)
    void updateCompanyProgressNoteLastSyncTime(@Param("noteId") Long noteId, @Param("lastSyncTime") Instant lastSyncTime);

    Integer countByCompanyId(Long companyId);

    List<CompanyProgressNote> findAllByCompanyIdIn(List<Long> companyIds);

}
