package com.altomni.apn.company.domain.enumeration.business;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class BusinessProgressConverter extends AbstractAttributeConverter<BusinessProgress, Integer> {
    public BusinessProgressConverter() {
        super(BusinessProgress::toDbValue, BusinessProgress::fromDbValue);
    }
}
