package com.altomni.apn.company.service.rabbitmq;

public interface RabbitMqService {
    void saveCompanyProfile(String companyProfile, int priority);

    void saveCompanyClientNoteProfile(String companyClientNoteProfile, int priority);

    void saveCompanyProgressNoteProfile(String companyProgressNoteProfile, int priority);

    Integer checkMessageCount(String queue);

    void saveCrmBackFillData(String backFillProfile);
}
