package com.altomni.apn.company.repository.business;

import com.altomni.apn.company.domain.business.AccountBusiness;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface AccountBusinessRepository extends JpaRepository<AccountBusiness, Long>, JpaSpecificationExecutor<AccountBusiness> {

    List<AccountBusiness> findAllByAccountCompanyId(Long companyId);

    List<AccountBusiness> findAllByAccountCompanyIdAndBusinessProgressIsNot(Long companyId, Integer businessProgress);

    List<AccountBusiness> findAllByAccountCompanyIdAndBusinessProgressNot(Long companyId, Integer businessProgress);

    List<AccountBusiness> findAllByBusinessUnitAndAccountCompanyId(String businessUnit,Long accountCompanyId);

    @Query("select ab from AccountBusiness ab where ab.accountCompanyId in ?1 and ab.businessProgress = ?2")
    List<AccountBusiness> findAllByCompanyIdInAndBusinessProgress(List<Long> companyIds, Integer businessProgress);

    List<AccountBusiness> findAllByAccountCompanyIdOrderByCreatedDateDesc(Long companyId);

    List<AccountBusiness> findAllByAccountCompanyIdIn(List<Long> companyIds);

    List<AccountBusiness> findAllByIdIn(List<Long> idList);

    AccountBusiness findFirstByAccountCompanyId(Long companyId);

    @Query("select max(ab.id) from AccountBusiness ab")
    Long maxId();

}