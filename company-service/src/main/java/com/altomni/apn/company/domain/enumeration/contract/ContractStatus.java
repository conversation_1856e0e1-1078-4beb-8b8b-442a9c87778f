package com.altomni.apn.company.domain.enumeration.contract;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractStatus enumeration.
 */
public enum ContractStatus implements ConvertedEnum<Integer> {
    INVALID(0),
    VALID(1);


    private final Integer dbValue;

    ContractStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ContractStatus, Integer> resolver =
        new ReverseEnumResolver<>(ContractStatus.class, ContractStatus::toDbValue);

    public static ContractStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
