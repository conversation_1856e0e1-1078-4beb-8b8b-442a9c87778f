package com.altomni.apn.company.domain.enumeration.business;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum BusinessProgress implements ConvertedEnum<Integer> {

    ACCOUNT_BUSINESS_NOT_FOLLOWED_UP(35, 35),   //商机未跟进
    ACCOUNT_BUSINESS_CUSTOMER_NOT_CONTACTED(40, 40),  //未联系客户
    ACCOUNT_BUSINESS_PLAN_COMMUNICATION(45, 45),   //方案沟通
    ACCOUNT_BUSINESS_PLAN_VERIFICATION(50, 50),     //报价/方案验证
    ACCOUNT_BUSINESS_NEGOTIATION(55, 55),     //商务谈判
    ACCOUNT_BUSINESS_CONTRACTED(60, 60),    //合同签订
    ACCOUNT_BUSINESS_LOST(65, 65) ;      //丢单

    private final int dbValue;
    private final int cnSortOrder;


    BusinessProgress(int dbValue, int cnSortOrder) {
        this.dbValue = dbValue;
        this.cnSortOrder = cnSortOrder;
    }

    public int getCnSortOrder() {
        return cnSortOrder;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<BusinessProgress, Integer> resolver = new ReverseEnumResolver<>(BusinessProgress.class, BusinessProgress::toDbValue);

    public static BusinessProgress fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
