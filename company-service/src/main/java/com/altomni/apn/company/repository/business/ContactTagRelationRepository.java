package com.altomni.apn.company.repository.business;

import com.altomni.apn.company.domain.business.ContactTagRelation;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface ContactTagRelationRepository extends JpaRepository<ContactTagRelation, Long> {

    List<ContactTagRelation> findAllByContactIdIn(List<Long> contactIds);

    List<ContactTagRelation> findAllByAccountCompanyIdAndContactIdIn(Long companyId, List<Long> contactIds);

    List<ContactTagRelation> findAllByAccountCompanyIdAndContactId(Long companyId, Long contactId);

}
