package com.altomni.apn.company.web.rest.vm.company;

import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.company.CompanyType;
import com.altomni.apn.company.vo.salesLead.SalesLeadAdministratorVO;
import com.altomni.apn.company.vo.salesLead.SalesLeadVO;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
public class CompanyProspectVM implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    private CompanyType type;

    public List<EnumRelationDTO> industries;

    private Long tenantId;

    private List<SalesLeadVO> salesLeads;

    private List<SalesLeadAdministratorVO> salesLeadOwners;

    private List<SalesLeadAdministratorVO> salesLeadBdOwners;

    private String country;

    private Instant createdDate;

    private Long permissionUserId;

    private String createdBy;
}
