package com.altomni.apn.company.aop.validation;

import com.altomni.apn.common.domain.enumeration.job.JobPermission;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamDTO;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamUserDTO;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class ProjectTeamPermissionValidator implements ConstraintValidator<ProjectTeamPermission, CompanyProjectTeamDTO>  {

    @Override
    public boolean isValid(CompanyProjectTeamDTO value, ConstraintValidatorContext context) {
        if (value == null) {
            return false;
        }
        List<CompanyProjectTeamUserDTO> companyProjectTeamUserDTOList = value.getCompanyProjectTeamUsers();
        Set<String> jobPermissionSet = new HashSet<>();
        for (CompanyProjectTeamUserDTO companyProjectTeamUserDTO : companyProjectTeamUserDTOList) {
            Set<String> permissions = companyProjectTeamUserDTO.getPermissions().stream().filter(o -> o.equals(JobPermission.PRIMARY_RECRUITER.name()) || o.equals(JobPermission.AM.name())).collect(Collectors.toSet());
            for (String jobPermission : permissions) {
                if (!jobPermissionSet.add(jobPermission)) {
                    if (jobPermission.equals(JobPermission.PRIMARY_RECRUITER.name())) {
                        context.buildConstraintViolationWithTemplate("Only 1 Primary Recruiter is required")
                                .addPropertyNode("companyProjectTeamUsers")
                                .addConstraintViolation();
                        return false;
                    } else {
                        context.buildConstraintViolationWithTemplate("Only 1 Account Manager is required")
                                .addPropertyNode("companyProjectTeamUsers")
                                .addConstraintViolation();
                        return false;
                    }
                }
            }
        }

        if (!jobPermissionSet.contains(JobPermission.PRIMARY_RECRUITER.name())) {
            context.buildConstraintViolationWithTemplate("Primary Recruiter is required")
                    .addPropertyNode("companyProjectTeamUsers")
                    .addConstraintViolation();
            return false;
        }

        if (!jobPermissionSet.contains(JobPermission.AM.name())) {
            context.buildConstraintViolationWithTemplate("Account Manager is required")
                    .addPropertyNode("companyProjectTeamUsers")
                    .addConstraintViolation();
            return false;
        }
        return true;
    }
}
