package com.altomni.apn.company.service.dto.projectTeam;

import com.altomni.apn.common.domain.enumeration.job.JobPermission;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;
import java.util.Set;

@Data
@ApiModel(description = "projectTeam user for client company")
public class CompanyProjectTeamUserDTO implements Serializable {

    @ApiModelProperty(value = "the id for company projectTeam user.")
    @NotNull
    private Long userId;

    @ApiModelProperty(value = "the permissions for company projectTeam user.")
    @NotNull
    private Set<String> permissions;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CompanyProjectTeamUserDTO that = (CompanyProjectTeamUserDTO) o;
        return userId.equals(that.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId);
    }
}
