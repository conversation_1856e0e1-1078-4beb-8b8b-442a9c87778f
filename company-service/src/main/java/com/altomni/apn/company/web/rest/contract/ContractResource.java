package com.altomni.apn.company.web.rest.contract;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.BadRequestAlertException;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.contract.Contract;
import com.altomni.apn.company.service.contract.ContractService;
import com.altomni.apn.company.service.dto.contract.ContractDTO;
import com.altomni.apn.company.service.dto.contract.ContractStatusDTO;
import com.altomni.apn.company.service.store.StoreService;
import com.altomni.apn.company.vo.contract.ContractVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ContractResource {

    private static final String ENTITY_NAME = "Company Contract";

    @Resource
    private ContractService contractService;

    @Resource
    private StoreService storeService;
    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    /**
     * POST  /contract : Create a new company contract.
     *
     * @param contractDTO the contractDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new contractDTO, or with status 400 (Bad Request) if the contractDTO has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
//    @PostMapping("/contracts")
//    public ResponseEntity<ContractVO> createContract(@Valid @RequestBody ContractDTO contractDTO) throws URISyntaxException {
////        log.info("[APN: Company Contract @{}] REST request to create contractDTO : {}", SecurityUtils.getUserId(), contractDTO);
//        if (contractDTO.getId() != null) {
//            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.CONTACT_SAVE_IDEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
//        }
//
//        ContractVO result = contractService.save(contractDTO);
//        return ResponseEntity.created(new URI("/api/v3/company/comtract" + result.getId()))
//                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId() != null ? result.getId().toString() : ""))
//                .body(result);
//    }

    /**
     * PUT  /contract : Create a new company contract.
     *
     * @param contractDTO the contractDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new contractDTO, or with status 400 (Bad Request) if the contractDTO has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
//    @PutMapping("/contracts/{contractId}")
//    public ResponseEntity<ContractVO> updateContract(@PathVariable("contractId") Long contractId, @Valid @RequestBody ContractDTO contractDTO) throws URISyntaxException {
////        log.info("[APN: Company Contract @{}] REST request to update contractDTO : {}", SecurityUtils.getUserId(), contractDTO);
//        contractDTO.setId(contractId);
//        ContractVO result = contractService.save(contractDTO);
//        return ResponseEntity.created(new URI("/api/v3/company/comtract" + result.getId())).body(result);
//    }

    /**
     * PUT  /contract : Update a new company contract status.
     *
     * @param contractStatusDTO the contractStatusDTO to update contract status
     * @return the ResponseEntity with status 201 (Created) and with body the new contractDTO, or with status 400 (Bad Request) if the contractDTO has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
//    @PutMapping("/contracts/status")
//    public ResponseEntity<ContractVO> updateContractStatus(@Valid @RequestBody ContractStatusDTO contractStatusDTO) throws URISyntaxException {
////        log.info("[APN: Company Contract @{}] REST request to update contract status : {}", SecurityUtils.getUserId(), contractStatusDTO);
//        ContractVO result = contractService.updateContractStatus(contractStatusDTO);
//        return ResponseEntity.created(new URI("/api/v3/company/comtract/status" + result.getId())).body(result);
//    }

    /**
     * PUT  /contract : Create a new company contract.
     *
     * @return the ResponseEntity with status 201 (Created) and with body the new contractDTO, or with status 400 (Bad Request) if the contractDTO has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
//    @GetMapping("/contracts/{contractId}")
//    public ResponseEntity<ContractVO> queryContract(@PathVariable("contractId") Long contractId) {
//        log.info("[APN: Company Contract @{}] REST request to update contractDTO : {}", SecurityUtils.getUserId(), contractId);
//        ContractVO result = contractService.queryContract(contractId);
//        return ResponseEntity.ok().body(result);
//    }
//
//    @GetMapping("/contracts/search")
//    @NoRepeatSubmit
//    public ResponseEntity<List<ContractVO>> searchContractByCompanyId(@RequestParam("companyId") Long companyId, @PageableDefault @SortDefault(sort = {"status", "uploadDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
//        log.info("[APN: Company Contract@{}] REST request to search contracts by company id:{}", SecurityUtils.getUserId(), companyId);
//        Page<Contract> page = contractService.searchContractByCompanyId(companyId, pageable);
//        List<ContractVO> contractVOList = contractService.toVo(page.getContent());
//        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/company/contract");
//        headers.add("Access-Control-Expose-Headers","X-Total-Count");
//        return ResponseEntity.ok().headers(headers).body(contractVOList);
//    }
//
//    @GetMapping("/contracts/detail/{contractId}")
//    @NoRepeatSubmit
//    public ResponseEntity<String> getContractDetailUrl(@PathVariable Long contractId) {
//        log.info("[APN: Company Contract@{}] REST request to get contract detail url by contractId: {}", SecurityUtils.getUserId(), contractId);
//        String url = contractService.getContractUploadUrl(contractId);
//        return ResponseEntity.ok().body(url);
//    }
//
//    @GetMapping("/contracts/display/{contractId}")
//    @NoRepeatSubmit
//    public void downloadContractDisplayImg(@PathVariable Long contractId, HttpServletResponse response) {
//        log.info("[APN: Company Contract@{}] REST request to download contract detail img by contractId: {}", SecurityUtils.getUserId(), contractId);
//        contractService.downloadContractDisplayImg(contractId, response);
//    }

//    @DeleteMapping("/contracts/{id}")
//    public ResponseEntity<Void> deleteContract(@PathVariable Long id) {
//        log.info("[APN: Company Contract @{}] REST request to delete Contract Id : {}", SecurityUtils.getUserId(), id);
//        contractService.delete(id);
//        return ResponseEntity.noContent().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
//    }

}
