package com.altomni.apn.company.domain.vm;

import com.altomni.apn.common.domain.dict.TalentIndustryRelation;
import com.altomni.apn.common.domain.dict.TalentJobFunctionRelation;
import com.altomni.apn.common.domain.dict.TalentLanguageRelation;
import com.altomni.apn.common.domain.dict.TalentWorkAuthorizationRelation;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentCompanyRelatedVM implements Serializable {

    @ApiModelProperty(value = "one or more job functions")
    private Set<TalentJobFunctionRelation> jobFunctions;


    @ApiModelProperty(value = "one or more languages")
    private Set<TalentLanguageRelation> languages;


    @ApiModelProperty(value = "one or more industries")
    private Set<TalentIndustryRelation> industries;

    @ApiModelProperty(value = "one workAuthorization")
    private Set<TalentWorkAuthorizationRelation> workAuthorization;
}
