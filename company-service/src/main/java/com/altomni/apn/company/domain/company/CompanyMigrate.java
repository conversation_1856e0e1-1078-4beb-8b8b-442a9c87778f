package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatusConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * A Company.
 */
@ApiModel(description = "Company entities collected. This would provide a dropdown list when user entering job/talent company " +
        "in ATS. This is just used as references.")
@Entity
@Data
@Table(name = "company")
@NoArgsConstructor
@AllArgsConstructor
public class CompanyMigrate extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @ApiModelProperty(value = "The url link to the logo")
    @Column(name = "logo")
    private String logo;

//    @ApiModelProperty(required = true, value = "Company name")
//    @NotNull
//    @Column(name = "name", nullable = false, unique = true)
//    private String name;

    @Column(name = "full_business_name")
    private String fullBusinessName;

    @Column(name = "client_level_type")
    private Integer companyClientLevel;

    @Column(name = "active")
    @Convert(converter = AccountCompanyStatusConverter.class)
    private AccountCompanyStatus active;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Transient
    //用于同步时接受 commonEsId 存入additionalInfo 同步es使用
    private String commonEsId;

    //禁猎客户需求，增加CRM同步到APN salesLeadCompanyId，bdCompaniesBusinessInformationId
    @Transient
    private Long salesLeadCompanyId;

    @Transient
    private String bdCompaniesBusinessInformationId;

    @ApiModelProperty(value = "last successful sync to ES")
    @Column(name = "last_sync_time")
    private Instant lastSyncTime;

    @Column(name = "last_edited_time")
    private Instant lastEditedTime;

    @Column(name = "request_date", updatable = false)
    private Instant requestDate;
}