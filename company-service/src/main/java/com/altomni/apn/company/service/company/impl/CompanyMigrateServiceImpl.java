package com.altomni.apn.company.service.company.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.domain.business.AccountBusiness;
import com.altomni.apn.company.domain.business.AccountBusinessContactRelation;
import com.altomni.apn.company.domain.business.BusinessFlowAdministrator;
import com.altomni.apn.company.domain.contract.ContractBusinessRelation;
import com.altomni.apn.common.enumeration.SalesLeadRoleType;
import com.altomni.apn.company.domain.vm.CompanyJobVM;
import com.altomni.apn.company.repository.business.AccountBusinessAdministratorRepository;
import com.altomni.apn.company.repository.business.AccountBusinessContactRelationRepository;
import com.altomni.apn.company.repository.business.AccountBusinessRepository;
import com.altomni.apn.company.repository.contract.ContractBusinessRelationRepository;
import com.altomni.apn.company.repository.job.JobCompanyBriefRepository;
import com.altomni.apn.company.service.company.CompanyMigrateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@Deprecated
@RequiredArgsConstructor
public class CompanyMigrateServiceImpl implements CompanyMigrateService {

    @PersistenceContext
    private EntityManager entityManager;

    private final AccountBusinessRepository accountBusinessRepository;

    private final AccountBusinessContactRelationRepository accountBusinessContactRelationRepository;

    private final AccountBusinessAdministratorRepository accountBusinessAdministratorRepository;

    private final ContractBusinessRelationRepository contractBusinessRelationRepository;

    private final JobCompanyBriefRepository jobCompanyBriefRepository;

    private final ApplicationProperties applicationProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void mergeDuplicateBusiness(SecurityContext context, HttpHeaders headers) {
        SecurityContextHolder.setContext(context);
        if (!SecurityUtils.isAdmin()) {
            throw new ForbiddenException("no permission");
        }
        Instant start = Instant.now();
        log.info("merge duplicate business start : {}", start);
        List<String> mergeBusinessList = findAllMergeBusiness(getQueryMergeBusiness());
        if (CollUtil.isEmpty(mergeBusinessList)) {
            log.info("merge duplicate business empty : {}", Instant.now());
            return;
        }
        List<Long> allMergeBusinessIds = new ArrayList<>();
        Map<Long, Long> allMergeBusinessMap = new HashMap<>();
        List<Long> deleteBusinessIds = new ArrayList<>();
        for (String item : mergeBusinessList) {
            String[] items = item.split(",");
            Long toMergeId = Long.parseLong(items[0]);
            for(String i : items) {
                allMergeBusinessIds.add(Long.parseLong(i));
                allMergeBusinessMap.put(Long.parseLong(i), toMergeId);
                //相同商机保留第一个，删除非第一个的商机
                if (!toMergeId.equals(Long.parseLong(i))) {
                    deleteBusinessIds.add(Long.parseLong(i));
                }
            }
        }
        //查需要删除的商机 & 关联数据
        List<AccountBusiness> deleteBusinessList = accountBusinessRepository.findAllByIdIn(deleteBusinessIds);
        List<AccountBusinessContactRelation> deleteBusinessContactRelationList = accountBusinessContactRelationRepository.findAllByAccountBusinessIdIn(deleteBusinessIds);

        //合并商机AM & 删除重复的商机owner
        List<BusinessFlowAdministrator> amList = accountBusinessAdministratorRepository.findAllByAccountBusinessIdInAndSalesLeadRoleTypeIn(allMergeBusinessIds, List.of(SalesLeadRoleType.ACCOUNT_MANAGER));
        List<BusinessFlowAdministrator> updateAmList = new ArrayList<>();
        List<BusinessFlowAdministrator> deleteAmList = new ArrayList<>();
        //AM需要合并
        amList.forEach(o -> o.setAccountBusinessId(allMergeBusinessMap.get(o.getAccountBusinessId())));
        //被合并的商机owner直接删除
        List<BusinessFlowAdministrator> deleteOwnerList = accountBusinessAdministratorRepository.findAllByAccountBusinessIdInAndSalesLeadRoleTypeIn(deleteBusinessIds, List.of(SalesLeadRoleType.SALES_LEAD_OWNER, SalesLeadRoleType.BUSINESS_DEVELOPMENT));

        for (BusinessFlowAdministrator item : amList) {
            //删除重复的AM
            if (!updateAmList.contains(item)) {
                updateAmList.add(item);
            } else {
                deleteAmList.add(item);
            }
        }

        //合并合同 —— 商机关联数据
        List<ContractBusinessRelation> contractBusinessRelationList = contractBusinessRelationRepository.findAllByAccountBusinessIdIn(allMergeBusinessIds);
        contractBusinessRelationList.forEach(o -> o.setAccountBusinessId(allMergeBusinessMap.get(o.getAccountBusinessId())));

        //合并job —— 商机关联数据
        List<CompanyJobVM> companyJobVMList = findAllMergeJob(allMergeBusinessIds);
        for (CompanyJobVM companyJobVM : companyJobVMList) {
            jobCompanyBriefRepository.updateJobSalesLeadId(allMergeBusinessMap.get(companyJobVM.getSalesLeadId()), companyJobVM.getId());
        }

        accountBusinessRepository.deleteAllByIdInBatch(deleteBusinessList.stream().map(AccountBusiness::getId).toList());
        accountBusinessContactRelationRepository.deleteAllByIdInBatch(deleteBusinessContactRelationList.stream().map(AccountBusinessContactRelation::getId).toList());
        accountBusinessAdministratorRepository.deleteAllByIdInBatch(deleteAmList.stream().map(BusinessFlowAdministrator::getId).toList());
        accountBusinessAdministratorRepository.saveAll(updateAmList);
        contractBusinessRelationRepository.saveAll(contractBusinessRelationList);

        JSONObject crmRequestData = new JSONObject();
        if (CollUtil.isNotEmpty(deleteBusinessList)) {
            crmRequestData.put("deleteBusinessIdList", deleteBusinessList.stream().map(AccountBusiness::getId).toList());
        }
        if (CollUtil.isNotEmpty(deleteBusinessContactRelationList)) {
            crmRequestData.put("deleteBusinessContactRelationIdList", deleteBusinessContactRelationList.stream().map(AccountBusinessContactRelation::getId).toList());
        }
        if (CollUtil.isNotEmpty(updateAmList)) {
            crmRequestData.put("updateAmList", updateAmList);
        }
        if (CollUtil.isNotEmpty(deleteAmList)) {
            crmRequestData.put("deleteAmIdList", deleteAmList.stream().map(BusinessFlowAdministrator::getId).toList());
        }
        if (CollUtil.isNotEmpty(contractBusinessRelationList)) {
            crmRequestData.put("contractBusinessRelationList", contractBusinessRelationList);
        }
        crmRequestData.put("mergeBusinessList", mergeBusinessList);

        log.info("merge duplicate business end : {}, crmRequestData: {}", Instant.now(), JsonUtil.toJson(crmRequestData));

        String syncCrmUrl = applicationProperties.getCrmUrl() + "/account/api/v1/merge-duplicate-business";
        String body = JsonUtil.toJson(crmRequestData);
        log.info("sync crm duplicate business url : {}, authorizationHeader: {}, body: {}", syncCrmUrl, headers.getFirst(HttpHeaders.AUTHORIZATION), body);
        cn.hutool.http.HttpResponse response = cn.hutool.http.HttpUtil.createRequest(cn.hutool.http.Method.POST, syncCrmUrl)
                .header("Authorization", headers.getFirst(HttpHeaders.AUTHORIZATION))
                .body(body).execute();
        log.info("sync crm duplicate business response code : {}, response message: {}", response.getStatus(), response.body());
        if (response.getStatus() != HttpStatus.CREATED.value()) {
            log.error("sync crm duplicate business error, response: {}", response);
            throw new CustomParameterizedException("sync crm duplicate business error");
        }
    }


    private String getQueryMergeBusiness() {
        StringBuilder sb = new StringBuilder();
        sb.append("SELECT\n" +
                "    GROUP_CONCAT(a.id ORDER BY a.id ASC) AS businessId\n" +
                "FROM (\n" +
                "    SELECT\n" +
                "        ab.id,\n" +
                "        ab.company_id AS companyId,\n" +
                "		 ab.business_progress,\n" +
                "        ab.tenant_id AS tenantId,\n" +
                "        GROUP_CONCAT(DISTINCT abs.service_type_id ORDER BY abs.service_type_id ASC) AS serviceTypeId,\n" +
                "        GROUP_CONCAT(DISTINCT abc.client_contact_id ORDER BY abc.client_contact_id ASC) AS clientContactId,\n" +
                "        GROUP_CONCAT(\n" +
                "            DISTINCT CASE\n" +
                "                WHEN bfa.sales_lead_role = 1 THEN CONCAT(bfa.user_id, ':', bfa.contribution)\n" +
                "            END ORDER BY bfa.user_id ASC\n" +
                "        ) AS salesLeadOwnerId,\n" +
                "        GROUP_CONCAT(\n" +
                "            DISTINCT CASE\n" +
                "                WHEN bfa.sales_lead_role = 2 THEN CONCAT(bfa.user_id, ':', bfa.contribution)\n" +
                "            END ORDER BY bfa.user_id ASC\n" +
                "        ) AS bdOwnerId\n" +
                "    FROM\n" +
                "        account_business ab\n" +
                "        LEFT JOIN account_business_service_type_relation abs ON ab.id = abs.account_business_id\n" +
                "        LEFT JOIN account_business_contact_relation abc ON ab.id = abc.account_business_id\n" +
                "        LEFT JOIN business_flow_administrator bfa ON ab.id = bfa.account_business_id\n" +
                "    GROUP BY\n" +
                "        ab.id\n" +
                ") a \n" +
                "GROUP BY\n" +
                "    a.companyId,\n" +
                "    a.serviceTypeId,\n" +
                "    a.clientContactId,\n" +
                "    a.salesLeadOwnerId,\n" +
                "    a.bdOwnerId,\n" +
                "		a.business_progress \n" +
                "HAVING\n" +
                "    COUNT(a.id) > 1");
        return sb.toString();
    }

    private List<CompanyJobVM> findAllMergeJob(List<Long> businessIds) {
        Map<Integer, Object> paramMap = new HashMap<>(16);
        StringBuilder sb = new StringBuilder().append("SELECT id, sales_lead_id FROM job WHERE sales_lead_id IN ?1");
        paramMap.put(1, businessIds);
        return searchData(sb.toString(), CompanyJobVM.class, paramMap);
    }

    private List<String> findAllMergeBusiness(String sql) {
        Query nativeQuery = entityManager.createNativeQuery(sql);
        List<?> resultList = nativeQuery.getResultList();
        return resultList.stream()
                .filter(item -> item instanceof String)
                .map(item -> (String) item)
                .collect(Collectors.toList());
    }


    private <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        entityManager.clear();
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchData(queryStr, clazz, paramMap);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }


    private <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        return query.getResultList();
    }

    private Integer checkInList(Map<Integer, Object> map) {
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new RuntimeException("query sql in condition list > 1000 more than 1");
        }
        return keyList.get(0);
    }
}