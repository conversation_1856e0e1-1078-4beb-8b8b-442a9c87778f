package com.altomni.apn.company.service.dto;

import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.company.IndustryTypeConverter;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatusConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CompanyDTO implements Serializable {

    private Long id;

    private String logo;

    private String name;

    @Convert(converter = IndustryTypeConverter.class)
    private Set<EnumRelationDTO> industries;

    private Integer companyClientLevel;

    @Convert(converter = AccountCompanyStatusConverter.class)
    private AccountCompanyStatus active;

    private Long tenantId;

    public static CompanyDTO fromCompany(Company company) {
        CompanyDTO companyDTO = new CompanyDTO();
        ServiceUtils.myCopyProperties(company, companyDTO);
        companyDTO.setName(company.getFullBusinessName());
        companyDTO.setIndustries(company.getIndustries().stream().map(o -> new EnumRelationDTO().setEnumId(String.valueOf(o.getEnumId()))).collect(Collectors.toSet()));
        return companyDTO;
    }
}
