package com.altomni.apn.company.domain.company;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A CompanyAdditionalInfo.
 */
@ApiModel(description = "Company additional info")
@Entity
@NoArgsConstructor
@Data
@Table(name = "company_additional_info")
public class CompanyAdditionalInfoForNameExtra implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The id for company")
    @Column(name = "company_id")
    private Long accountCompanyId;

    @ApiModelProperty(value = "The additionalInfo for company")
    @Column(name = "extended_info")
    private String extendedInfo;

}