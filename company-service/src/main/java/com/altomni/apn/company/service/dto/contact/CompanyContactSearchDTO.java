package com.altomni.apn.company.service.dto.contact;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-18
*/
@Data
@ApiModel(description = "search for company contact")
public class CompanyContactSearchDTO implements Serializable {

    @ApiModelProperty(value = "the id for company.")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "the id for company.")
    private List<Long> companyIds;

}
