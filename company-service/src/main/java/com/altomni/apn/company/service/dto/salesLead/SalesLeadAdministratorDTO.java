package com.altomni.apn.company.service.dto.salesLead;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
*
* <AUTHOR>
* date:2023-04-25
*/
@Data
@ApiModel(description = "dto for salesLead administrator")
public class SalesLeadAdministratorDTO implements Serializable {

    @ApiModelProperty(value = "The id for administrator.")
    @NotNull
    public Long id;

    @ApiModelProperty(value = "The contribution for administrator.")
    public Integer contribution;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SalesLeadAdministratorDTO that = (SalesLeadAdministratorDTO) o;
        return id.equals(that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}