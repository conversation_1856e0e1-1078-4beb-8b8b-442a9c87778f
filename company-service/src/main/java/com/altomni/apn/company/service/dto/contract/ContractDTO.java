package com.altomni.apn.company.service.dto.contract;

import com.altomni.apn.company.domain.contract.Contract;
import com.altomni.apn.company.domain.enumeration.company.NoPoachingType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Set;

/**
 * A DTO for the {@link Contract} entity.
 */
@Data
@ApiModel(description = "contract for client company")
public class ContractDTO implements Serializable {

    @ApiModelProperty(value = "the id for contract.")
    private Long id;

    @ApiModelProperty(value = "the id for company.")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "The serviceTypes for contract.")
    @UniqueElements
    public List<Long> serviceTypes;

    /**
     *改为可以上传多份合同，使用关系表存储
     *
     @ApiModelProperty(value = "the fileName for contract.")
     @NotNull
     @Size(max = 256)
     private String fileName;

     @ApiModelProperty(value = "the s3Key for contract.")
     @Size(max = 256)
     @NotNull
     private String s3Key;

     @ApiModelProperty(value = "the uploadDate for contract.")
     private LocalDate uploadDate;
     */

    /**
     * No Poaching需求增加字段
     */
    private NoPoachingType noPoaching;

    @ApiModelProperty(value = "the name for contract.")
    @NotNull
    @Size(max = 256)
    private String name;

    @ApiModelProperty(value = "the note for contract.")
    @NotNull
    private String note;

    @ApiModelProperty(value = "the signers for contract.")
    @NotNull
    private Set<SignerDTO> signers;

    @ApiModelProperty(value = "the startDate for contract.")
    @NotNull
    private Instant startDate;

    @ApiModelProperty(value = "the endDate for contract.")
    private Instant endDate;

    @ApiModelProperty(value = "the id for previousContract.")
    private Long previousContractId;

    @ApiModelProperty(value = "the ids for salesLead.")
    @UniqueElements
    private List<Long> salesLeadIds;

}
