package com.altomni.apn.company.aop.validation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ContributionSumValidator.class)
public @interface ContributionSum {
    String message() default "Contribution sum must equal 100";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}