package com.altomni.apn.company.domain.folder;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.company.domain.enumeration.folder.FolderPermission;
import com.altomni.apn.company.domain.enumeration.folder.FolderPermissionConverter;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A CompanyCustomFolderSharedUser.
 */
@Entity
@Table(name = "company_custom_folder_shared_user")
@NoArgsConstructor
@Data
public class CompanyCustomFolderSharedUser extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "folder_id")
    private Long folderId;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "permission")
    @Convert(converter = FolderPermissionConverter.class)
    private FolderPermission permission;

    public CompanyCustomFolderSharedUser(Long folderId, Long userId, FolderPermission permission) {
        this.folderId = folderId;
        this.userId = userId;
        this.permission = permission;
    }
}
