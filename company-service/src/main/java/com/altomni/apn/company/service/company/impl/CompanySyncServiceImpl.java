package com.altomni.apn.company.service.company.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.company.config.env.EsfillerMQProperties;
import com.altomni.apn.company.config.scheduled.ScheduledProperties;
import com.altomni.apn.company.config.thread.*;
import com.altomni.apn.company.domain.vo.CompanyVo;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.service.am.AmReportService;
import com.altomni.apn.company.service.company.CompanySyncService;
import com.altomni.apn.company.service.elastic.EsFillerCompanyService;
import com.altomni.apn.company.service.rabbitmq.RabbitMqService;
import com.altomni.apn.company.service.redis.RedisService;
import com.altomni.apn.company.web.rest.vm.company.MqMessageCountVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Service("companySyncService")
public class CompanySyncServiceImpl implements CompanySyncService {

    @Resource
    private ScheduledProperties scheduledProperties;

    @Resource
    private AmReportService amReportService;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource
    private EsFillerCompanyService esFillerCompanyService;

    @Resource
    private RedisService redisService;

    @Resource
    private CanalService canalService;

    @Resource
    private JobdivaRabbitProperties jobdivaRabbitProperties;

    public static final ExecutorService executorService = new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors() * 2,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-scheduled-sendEmail-AmReport", false));

    @Override
    public void sendEmailAmReport() {
        log.info("[ScheduledAmReport: scheduledSendEmailAmReport @-1] Scheduled start: {}", LocalDateTime.now());
        String mailTemplate = CommonUtils.readFileToString("templates/am-report-mail.html");
        String imageTemplate = CommonUtils.readFileToString("templates/am-report.html");
        if (mailTemplate == null || imageTemplate == null) {
            return;
        }
        int page = 0;
        int size = 500;
        log.info("tenantIds ======================= " + scheduledProperties.getTenantIds());
        List<Long> tenantIdList = checkTenantIds();
        boolean breakFlag = false;
        while (true) {
            RLock rLock = null;
            List<CompanyVo> companyVoList = new ArrayList<>();
            try {
                //lock to get offset
                rLock = redissonClient.getLock("am-report");
                rLock.lock(1, TimeUnit.MINUTES);
                RBucket<Integer> rBucket = redissonClient.getBucket("am-report-offset");
                if (ObjectUtil.isNotNull(rBucket.get())) {
                    page = rBucket.get();
                }
                companyVoList = companyRepository.findCompanyByPageAndTenantIds(tenantIdList, PageRequest.of(page, size));
                if (CollUtil.isEmpty(companyVoList)) {
                    breakFlag = true;
                    rBucket.set(null);
                } else {
                    rBucket.set(++page);
                }
                rBucket.expire(10, TimeUnit.MINUTES);
            } catch (Exception e) {
                breakFlag = true;
                log.error("[apn] am report search company is error, message = [{}]", ExceptionUtils.getStackTrace(e));
            } finally {
                if (rLock != null) {
                    rLock.unlock();
                }
            }
            if (breakFlag) {
                //Abnormal end
                break;
            }
            companyVoList.forEach(vo -> {
                executorService.execute(new SendAmReportThreadTask(amReportService, vo, mailTemplate, imageTemplate));
            });
        }
        log.info("[ScheduledAmReport: scheduledSendEmailAmReport @-1] Scheduled end: {}", LocalDateTime.now());
    }

    @Override
    public MqMessageCountVM checkCompanyMqMessageCount() {
        Integer messageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getToEsFillerQueue());
        Integer companyMessageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getApnNormalizedCompanyQueue());
        log.debug("COMPANY_TO_ES_QUEUE messageCount= " + messageCount);
        return new MqMessageCountVM(Math.max(messageCount, companyMessageCount), esfillerMQProperties.getToEsFillerMaximumMsgCount());
    }

    @Override
    public void syncCompaniesToMQ(Collection<Long> companyIds, int priority) {
        log.info("[syncCompaniesToMQ] sync companies start at: {}", LocalDateTime.now());
        List<List<Long>> companyGroups = CollUtil.split(companyIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(companyGroups.size());
        companyGroups.forEach(ids -> executorService.execute(new CompanySyncToMqThreadTask(esFillerCompanyService, redisService, countDownLatch, ids, priority, false, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[SyncCompaniesToMQ] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(companyIds, SyncIdTypeEnum.COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[SyncCompaniesToMQ] countDownLatch Error" +
                    "\n\tCompany ID: " + companyIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[SyncCompaniesToMQ] Sync Companies to MQ Done!");
    }

    @Override
    public void deleteFromEsFilter(List<Long> companyIds, int priority) {
       executorService.execute(new CompanySyncToMqThreadTask(esFillerCompanyService, redisService, new CountDownLatch(companyIds.size()), companyIds, priority, true, esfillerMQProperties, canalService));
        log.info("[SyncCompaniesToMQ] delete Companies to MQ Done!");
    }

    private List<Long> checkTenantIds() {
        String tenantIds = "4";
        if (StrUtil.isNotBlank(scheduledProperties.getTenantIds())) {
            tenantIds = scheduledProperties.getTenantIds();
        }
        return Arrays.stream(tenantIds.split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
    }

    @Override
    public MqMessageCountVM checkCompanyClientNoteMqMessageCount() {
        Integer messageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getToEsFillerQueue());
//        Integer companyClientNoteMessageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getApnNormalizedCompanyClientNoteQueue());
        log.debug("COMPANY_CLIENT_NOTE_TO_ES_QUEUE messageCount= " + messageCount);
        return new MqMessageCountVM(messageCount, esfillerMQProperties.getToEsFillerMaximumMsgCount());
    }

    @Override
    public void syncCompanyClientNoteToMQ(Collection<Long> companyClientNoteIds, int priority) {
        log.info("[syncCompanyClientNoteToMQ] sync company client note start at: {}", LocalDateTime.now());
        List<List<Long>> companyClientNoteGroups = CollUtil.split(companyClientNoteIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(companyClientNoteGroups.size());
        companyClientNoteGroups.forEach(ids -> executorService.execute(new CompanyClientNoteSyncToMqThreadTask(esFillerCompanyService, redisService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[SyncCompanyClientNoteToMQ] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(companyClientNoteIds, SyncIdTypeEnum.COMPANY_CLIENT_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[SyncCompanyClientNoteToMQ] countDownLatch Error" +
                    "\n\tCompany Client Note ID: " + companyClientNoteIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[SyncCompanyClientNoteToMQ] Sync Company client notes to MQ Done!");
    }

    @Override
    public MqMessageCountVM checkCompanyProgressNoteMqMessageCount() {
        Integer messageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getToEsFillerQueue());
//        Integer companyProgressNoteMessageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getApnNormalizedCompanyProgressNoteQueue());
        log.info("COMPANY_PROGRESS_NOTE_TO_ES_QUEUE messageCount= " + messageCount);
        return new MqMessageCountVM(messageCount, esfillerMQProperties.getToEsFillerMaximumMsgCount());
    }

    @Override
    public void syncCompanyProgressNoteToMQ(Collection<Long> companyProgressNoteIds, int priority) {
        log.info("[syncCompanyProgressNoteToMQ] sync company progress note start at: {}", LocalDateTime.now());
        List<List<Long>> companyProgressNoteGroups = CollUtil.split(companyProgressNoteIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(companyProgressNoteGroups.size());
        companyProgressNoteGroups.forEach(ids -> executorService.execute(new CompanyProgressNoteSyncToMqThreadTask(esFillerCompanyService, redisService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[SyncCompanyProgressNoteToMQ] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(companyProgressNoteIds, SyncIdTypeEnum.COMPANY_PROGRESS_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[SyncCompanyProgressNoteToMQ] countDownLatch Error" +
                    "\n\tCompany Progress Note ID: " + companyProgressNoteIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[SyncCompanyProgressNoteToMQ] Sync Company progress notes to MQ Done!");
    }

    @Override
    public MqMessageCountVM checkCompanyContactMqMessageCount() {
        Integer messageCount = rabbitMqService.checkMessageCount(jobdivaRabbitProperties.getApnToJobdivaQueue());
//        Integer companyProgressNoteMessageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getApnNormalizedCompanyProgressNoteQueue());
        log.info("COMPANY_CONTACT_TO_HR_QUEUE messageCount= " + messageCount);
        return new MqMessageCountVM(messageCount, esfillerMQProperties.getToEsFillerMaximumMsgCount());
    }

    @Override
    public void syncCompanyContactToMQ(Collection<Long> contactIds, int priority) {
        log.info("[syncCompanyContactToMQ]Sync Company contact to hr start at: {}", LocalDateTime.now());
        List<List<Long>> companyContactGroups = CollUtil.split(contactIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(companyContactGroups.size());
        companyContactGroups.forEach(ids -> executorService.execute(new CompanyContactSyncToMqThreadTask(esFillerCompanyService, redisService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[syncCompanyContactToMQ] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(contactIds, SyncIdTypeEnum.HR_COMPANY_CONTACT, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[syncCompanyContactToMQ] countDownLatch Error" +
                    "\n\tContact ID: " + contactIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[syncCompanyContactToMQ] Sync Company contact to hr MQ Done!");
    }

    @Override
    public void syncCompanyToHrMQ(Collection<Long> contactIds, int priority) {
        log.info("[syncCompanyToHrMQ]Sync Company to hr start at: {}", LocalDateTime.now());
        List<List<Long>> companyContactGroups = CollUtil.split(contactIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(companyContactGroups.size());
        companyContactGroups.forEach(ids -> executorService.execute(new CompanySyncToHrMqThreadTask(esFillerCompanyService, redisService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[syncCompanyToHrMQ] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(contactIds, SyncIdTypeEnum.HR_COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[syncCompanyToHrMQ] countDownLatch Error" +
                    "\n\tcompany ID: " + contactIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[syncCompanyToHrMQ] Sync Company to hr MQ Done!");
    }

}
