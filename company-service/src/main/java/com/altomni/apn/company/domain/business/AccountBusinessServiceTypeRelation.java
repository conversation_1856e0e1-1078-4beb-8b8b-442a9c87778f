package com.altomni.apn.company.domain.business;

import com.altomni.apn.common.domain.ManualAbstractAuditingEntity;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "account_business_service_type_relation")
@Data
@NoArgsConstructor
public class AccountBusinessServiceTypeRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @Column(name = "account_business_id")
    private Long accountBusinessId;

    @Column(name = "service_type_id")
    private Integer enumId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccountBusinessServiceTypeRelation that = (AccountBusinessServiceTypeRelation) o;
        return accountBusinessId.equals(that.accountBusinessId) && enumId.equals(that.enumId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(accountBusinessId, enumId);
    }
}
