package com.altomni.apn.company.service.jobdiva;

import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

@Component
@FeignClient(value = "jobdiva-service")
public interface JobdivaClient {

    @GetMapping("/jobdiva/api/v3/timesheet/user/getById")
    ResponseEntity<TimeSheetUserDTO> getTimeSheetUserById(@RequestParam("id") Long id);

    @GetMapping("/jobdiva/api/v3/timesheet/user/findByUsernameOrEmail")
    ResponseEntity<TimeSheetUserDTO> findByUsernameOrEmail(@RequestParam("username") String username);

    @PostMapping("/jobdiva/api/v3/timesheet/user/save")
    ResponseEntity<TimeSheetUserDTO> saveTimeSheetUser(@RequestBody TimeSheetUserDTO timeSheetUserDTO);

    @PostMapping("/jobdiva/api/v3/timesheet/user/findByUsernameOrEmailList")
    ResponseEntity<List<TimeSheetUserDTO>> findByUsernameOrEmailList(@RequestBody Set<String> emailList);

}
