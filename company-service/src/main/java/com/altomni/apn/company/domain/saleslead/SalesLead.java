package com.altomni.apn.company.domain.saleslead;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.service.dto.overview.CompanySalesLeadDetailClientDTO;
import com.altomni.apn.company.service.dto.overview.CompanySalesLeadDetailProspectDTO;
import com.altomni.apn.company.service.dto.salesLead.CompanySalesLeadProspectDTO;
import com.altomni.apn.company.service.dto.salesLead.SalesLeadUpdateDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@ApiModel(description = "saleslead")
@Entity
@Data
@Table(name = "company_sales_lead")
public class SalesLead extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "account_progress", nullable = false)
    private BigDecimal accountProgress;

    @Column(name = "company_id", nullable = false)
    private Long companyId;

    @Column(name = "lead_source")
    private Long leadSource;

    @Column(name = "estimated_deal_time", nullable = true)
    private Instant estimatedDealTime;

    @ManyToMany(fetch = FetchType.EAGER)
    @JoinTable(
            name = "company_sales_lead_service_type",
            joinColumns = @JoinColumn(name = "sales_lead_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "service_type_id", referencedColumnName = "id")
    )
    private Set<EnumCompanyServiceType> enumCompanyServiceTypes;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "companyId"));

    public static SalesLead fromSalesLeadDetailDTO(CompanySalesLeadProspectDTO companySalesLeadDetailDTO) {
        SalesLead salesLead = new SalesLead();
        ServiceUtils.myCopyProperties(companySalesLeadDetailDTO, salesLead);
        return salesLead;
    }

    public static SalesLead fromCompanySalesLeadDetailProspectDTO(CompanySalesLeadDetailProspectDTO companySalesLeadDetailProspectDTO) {
        SalesLead salesLead = new SalesLead();
        ServiceUtils.myCopyProperties(companySalesLeadDetailProspectDTO, salesLead);
        return salesLead;
    }

    public static SalesLead fromCompanySalesLeadDetailClientDTO(CompanySalesLeadDetailClientDTO companySalesLeadDetailClientDTO) {
        SalesLead salesLead = new SalesLead();
        ServiceUtils.myCopyProperties(companySalesLeadDetailClientDTO, salesLead);
        return salesLead;
    }

    public static SalesLead fromSalesLeadUpdateDTO(SalesLeadUpdateDTO salesLeadUpdateDTO) {
        SalesLead salesLead = new SalesLead();
        ServiceUtils.myCopyProperties(salesLeadUpdateDTO, salesLead);
        return salesLead;
    }

}