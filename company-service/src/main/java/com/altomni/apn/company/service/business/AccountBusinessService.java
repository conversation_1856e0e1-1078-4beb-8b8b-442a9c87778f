package com.altomni.apn.company.service.business;

import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.dto.company.AccountBusinessDTO;
import com.altomni.apn.company.domain.business.AccountBusiness;
import com.altomni.apn.company.domain.business.AccountBusinessMigrate;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.vo.business.AccountBusinessVO;
import com.altomni.apn.company.vo.business.OwnerVO;

import java.util.List;
import java.util.Map;

public interface AccountBusinessService {

//    SalesLeadVO createSalesLead(CompanySalesLeadProspectDTO salesLeadDTO);

//    SalesLeadDTO updateSalesLead(Set<SalesLeadDetailVM> salesLeadDetailVMs);

//    List<SalesLeadVO> getAllProspectSalesLeadsByCompany(Long companyId, Boolean upgraded);

    List<AccountBusinessMigrate> saveAccountBusiness(JSONObject jsonObject);

    List<Long> getAllAmByCompany(Long companyId);

    List<Long> getAllAmByJob(Long jobId);

    Map<Long, JSONArray> getAmsGroupByCompany();

    List<Map<String, Object>> getAmsGroupByCompanyFormat();

    List<SalesLeadClientContact> findClientContactByTalentId(Long talentId);

    List<AccountBusinessVO> queryBusinessListByCompanyId(Long companyId);

    List<AccountBusiness> queryBusinessByBusinessUnitAndCountryLocation(String businessUnit, String countryLocation,Long companyId);

    List<OwnerVO> getAllOwnerList();

    String getAccountBusinessById(AccountBusinessDTO accountBusinessDTO);
}
