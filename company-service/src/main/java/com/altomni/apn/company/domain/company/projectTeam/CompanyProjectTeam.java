package com.altomni.apn.company.domain.company.projectTeam;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamDTO;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

/**
 * A ProjectTeam.
 */
@Entity
@Table(name = "company_project_team")
public class CompanyProjectTeam extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The tenant id the team belongs to.")
    @Column(name = "tenant_id")
    @NotNull
    private Long tenantId;

    @ApiModelProperty(value = "The company id the team belongs to.")
    @Column(name = "company_id")
    @NotNull
    private Long companyId;

    @ApiModelProperty(value = "The team name. It should be unique in a tenant. Max length is 40.")
    @Column(name = "name")
    @Size(max = 40)
    @NotBlank
    private String name;

    @ApiModelProperty(value = "Project manager id")
    @Column(name = "leader_user_id")
    @NotNull
    private Long leaderUserId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getLeaderUserId() {
        return leaderUserId;
    }

    public void setLeaderUserId(Long leaderUserId) {
        this.leaderUserId = leaderUserId;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CompanyProjectTeam projectTeam = (CompanyProjectTeam) o;
        if (projectTeam.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), projectTeam.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "CompanyProjectTeam{" +
                "id=" + id +
                ", tenantId=" + tenantId +
                ", companyId=" + companyId +
                ", name='" + name + '\'' +
                ", leaderUserId=" + leaderUserId +
                '}';
    }

    public static CompanyProjectTeam fromCompanyProjectTeamDTO(CompanyProjectTeamDTO companyProjectTeamDTO) {
        CompanyProjectTeam companyProjectTeam = new CompanyProjectTeam();
        ServiceUtils.myCopyProperties(companyProjectTeamDTO, companyProjectTeam);
        return companyProjectTeam;
    }
}
