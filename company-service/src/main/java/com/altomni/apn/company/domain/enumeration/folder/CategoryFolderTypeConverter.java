package com.altomni.apn.company.domain.enumeration.folder;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class CategoryFolderTypeConverter extends AbstractAttributeConverter<CategoryFolderType, Long> {
    public CategoryFolderTypeConverter() {
        super(CategoryFolderType::toDbValue, CategoryFolderType::fromDbValue);
    }
}
