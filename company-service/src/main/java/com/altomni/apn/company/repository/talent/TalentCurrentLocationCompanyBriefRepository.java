package com.altomni.apn.company.repository.talent;

import com.altomni.apn.company.domain.talent.TalentCurrentLocationCompanyBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TalentCurrentLocationCompanyBriefRepository extends JpaRepository<TalentCurrentLocationCompanyBrief, Long> {

    TalentCurrentLocationCompanyBrief findByTalentId(Long talentId);

    List<TalentCurrentLocationCompanyBrief> findAllByTalentIdIn(List<Long> talentIds);
}
