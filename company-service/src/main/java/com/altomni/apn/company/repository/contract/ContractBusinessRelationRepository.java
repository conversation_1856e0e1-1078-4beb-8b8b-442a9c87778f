package com.altomni.apn.company.repository.contract;

import com.altomni.apn.company.domain.contract.ContractBusinessRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ContractBusinessRelationRepository extends JpaRepository<ContractBusinessRelation, Long>, JpaSpecificationExecutor<ContractBusinessRelation> {

    List<ContractBusinessRelation> findAllByContractId(Long contractId);

    List<ContractBusinessRelation> findAllByContractIdIn(List<Long> contractIds);

    List<ContractBusinessRelation> findAllByAccountBusinessIdIn(List<Long> businessIds);

    @Query("select max(cr.id) from ContractBusinessRelation cr")
    Long maxId();

}
