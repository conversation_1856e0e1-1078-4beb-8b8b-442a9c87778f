package com.altomni.apn.company.domain.vm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Data
public class EntityCompnayRelateTalentVM implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long companyId;

    private Long talentId;

}
