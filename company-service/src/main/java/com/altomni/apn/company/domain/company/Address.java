package com.altomni.apn.company.domain.company;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.company.domain.geoinfo.GeoInfoEN;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A Address.
 */
@MappedSuperclass
public abstract class Address extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(name = "city")
    private String city;

    @Column(name = "address")
    private String address;

    @Column(name = "address_2")
    private String address2;

    @OneToOne(fetch = FetchType.EAGER)
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "city_id", referencedColumnName = "id")
    private GeoInfoEN geoInfoEN;

    @Column(name = "zipcode")
    private String zipcode;

    // jhipster-needle-entity-add-field - J<PERSON>ip<PERSON> will add fields here, do not remove

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public GeoInfoEN getGeoInfoEN() {
        return geoInfoEN;
    }

    public void setGeoInfoEN(GeoInfoEN geoInfoEN) {
        this.geoInfoEN = geoInfoEN;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }


    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        }
//        if (o == null || getClass() != o.getClass()) {
//            return false;
//        }
//        Address address = (Address) o;
//        if (address.getId() == null || getId() == null) {
//            return false;
//        }
//        return Objects.equals(getId(), address.getId());
//    }

//    @Override
//    public int hashCode() {
//        return Objects.hashCode(getId());
//    }

    @Override
    public String toString() {
        return "Address{" +
            ", city='" + city + '\'' +
            ", address='" + address + '\'' +
            ", address2='" + address2 + '\'' +
            ", geoInfoEN=" + geoInfoEN +
            ", zipcode='" + zipcode + '\'' +
            '}';
    }
}
