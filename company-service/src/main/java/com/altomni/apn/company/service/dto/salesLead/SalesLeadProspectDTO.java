package com.altomni.apn.company.service.dto.salesLead;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-25
*/
@Data
@ApiModel(description = "dto for prospect salesLead")
public class SalesLeadProspectDTO implements Serializable {

    @ApiModelProperty(value = "The accountProgress for salesLead.")
    @NotNull
    public BigDecimal accountProgress;

    @ApiModelProperty(value = "The companyServiceTypes for salesLead.")
    @NotNull
    @UniqueElements
    public List<Long> companyServiceTypes;

    @ApiModelProperty(value = "The salesLeadsOwners for salesLead.")
    @NotNull
    @UniqueElements
    public List<SalesLeadAdministratorDTO> salesLeadsOwners;

    @ApiModelProperty(value = "The salesLeadsBdOwners for salesLead.")
    @NotNull
    @UniqueElements
    public List<SalesLeadAdministratorDTO> salesLeadsBdOwners;

    @ApiModelProperty(value = "The estimatedDealTime for salesLead.")
    @NotNull
    public Instant estimatedDealTime;

    @ApiModelProperty(value = "The leadSource for salesLead.")
    @NotNull
    public Long leadSource;
}