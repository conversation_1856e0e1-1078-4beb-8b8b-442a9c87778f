package com.altomni.apn.company.vo.business;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
public class AccountBusinessNameBriefVO implements Serializable {

    @ApiModelProperty(value = "id for accountBusiness")
    private Long id;

    @ApiModelProperty(value = "name for accountBusiness")
    private String name;

    public AccountBusinessNameBriefVO(Long id, String name) {
        this.id = id;
        this.name = name;
    }

}

