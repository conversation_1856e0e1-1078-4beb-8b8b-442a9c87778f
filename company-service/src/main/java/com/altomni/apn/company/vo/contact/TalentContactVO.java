package com.altomni.apn.company.vo.contact;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentContactVO extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -359001722869208228L;

    private Long id;

    private Integer type;

    private String contact;

    private Boolean verified;

    private String details;

    private String info;

    private Long talentId;

    private Long tenantId;

    private Boolean approverEmail = false;

    public static TalentContactVO fromTalentContact(TalentContact talentContact) {
        TalentContactVO talentContactVO = new TalentContactVO();
        ServiceUtils.myCopyProperties(talentContact, talentContactVO);
        talentContactVO.setType(talentContact.getType().toDbValue());
        return talentContactVO;
    }

}
