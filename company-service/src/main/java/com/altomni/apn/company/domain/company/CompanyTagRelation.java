package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

@ApiModel(description = "CompanyTagRelation")
@Entity
@Data
@NoArgsConstructor
@Table(name = "company_tag_relation")
public class CompanyTagRelation extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    private Long id;

    @Column(name = "company_id")
    private Long accountCompanyId;

    @Column(name = "tag")
    private String tag;
}
