package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The IndustryType enumeration.
 */
public enum FortuneRankType implements ConvertedEnum<Integer> {
    FORTUNE_500(1),
    FORTUNE_1000(2);
    private final int dbValue;

    FortuneRankType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<FortuneRankType, Integer> resolver = new ReverseEnumResolver<>(FortuneRankType.class, FortuneRankType::toDbValue);

    public static FortuneRankType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
