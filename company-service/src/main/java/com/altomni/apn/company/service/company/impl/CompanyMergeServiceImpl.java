package com.altomni.apn.company.service.company.impl;

import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.company.domain.business.AccountBusiness;
import com.altomni.apn.company.domain.business.AccountBusinessContactRelation;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.talent.TalentCompanyBrief;
import com.altomni.apn.company.repository.business.AccountBusinessContactRelationRepository;
import com.altomni.apn.company.repository.business.AccountBusinessRepository;
import com.altomni.apn.company.repository.business.SalesLeadClientContactRepository;
import com.altomni.apn.company.repository.company.CompanyClientInfoRepository;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.talent.TalentCompanyBriefRepository;
import com.altomni.apn.company.repository.talent.TalentContactCompanyBriefRepository;
import com.altomni.apn.company.service.company.CompanyMergeService;
import com.altomni.apn.company.service.company.CompanySyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CompanyMergeServiceImpl implements CompanyMergeService {

    private final EntityManager entityManager;
    private final PlatformTransactionManager transactionManager;
    private final CompanySyncService companySyncService;
    private final CompanyRepository companyRepository;
    private final AccountBusinessRepository accountBusinessRepository;
    private final AccountBusinessContactRelationRepository accountBusinessContactRelationRepository;
    private final SalesLeadClientContactRepository salesLeadClientContactRepository;

    @Override
    public void doMerge(Long sourceCompanyId, Long targetCompanyId) {

        TransactionStatus status = transactionManager.getTransaction(new DefaultTransactionDefinition());

        try {
            // 合并商机，去重
            mergeBusiness(sourceCompanyId, targetCompanyId);

            // 合并联系人, 去重
            mergeContacts(sourceCompanyId, targetCompanyId);

            // 合并职位
            Query updateJob = entityManager.createNativeQuery("UPDATE job SET company_id = :targetCompanyId, last_modified_date = NOW() WHERE company_id = :sourceCompanyId");
            executeUpdate(updateJob, sourceCompanyId, targetCompanyId);

            // 合并在职员工
            Query updateAssignment = entityManager.createNativeQuery("UPDATE timesheet_talent_assignment SET company_id = :targetCompanyId WHERE company_id = :sourceCompanyId");
            executeUpdate(updateAssignment, sourceCompanyId, targetCompanyId);

            // 合并项目团队
            Query updateTeam = entityManager.createNativeQuery("UPDATE company_project_team SET company_id = :targetCompanyId WHERE company_id = :sourceCompanyId");
            executeUpdate(updateTeam, sourceCompanyId, targetCompanyId);

            // 合并公司备注
            Query updateNote = entityManager.createNativeQuery("UPDATE company_client_note SET company_id = :targetCompanyId, last_modified_date = NOW() WHERE company_id = :sourceCompanyId");
            executeUpdate(updateNote, sourceCompanyId, targetCompanyId);

            //合并公司海外发票信息
            Query updateClientInvoice = entityManager.createNativeQuery("UPDATE company_invoice_client_info SET company_id = :targetCompanyId, last_modified_date = NOW() WHERE company_id = :sourceCompanyId");
            executeUpdate(updateClientInvoice, sourceCompanyId, targetCompanyId);
            // 合并公司中国区发票信息
            Query updateClientInvoiceChina = entityManager.createNativeQuery("UPDATE company_client_invoicing_info SET company_id = :targetCompanyId, last_modified_date = NOW() WHERE company_id = :sourceCompanyId");
            executeUpdate(updateClientInvoiceChina, sourceCompanyId, targetCompanyId);

            //合并 start 入职信息
            mergeStart(sourceCompanyId, targetCompanyId);

            // 同步目标公司到ES
            companySyncService.syncCompaniesToMQ(List.of(targetCompanyId), 1);

            // 从ES删除源公司
            companySyncService.deleteFromEsFilter(List.of(sourceCompanyId), 1);

            // 从数据库删除源公司
            entityManager.createNativeQuery("DELETE FROM company WHERE id = :sourceCompanyId").setParameter("sourceCompanyId", sourceCompanyId).executeUpdate();

            transactionManager.commit(status);
        } catch (Exception e) {
            log.error("Error while merging company", e);
            transactionManager.rollback(status);
            throw e;
        }
    }

    private void mergeBusiness(Long sourceCompanyId, Long targetCompanyId) {
        List<AccountBusiness> sourceBusiness = accountBusinessRepository.findAllByAccountCompanyId(sourceCompanyId);
        if (sourceBusiness.isEmpty()) {
            log.info("No business found in source company with id: {}", sourceCompanyId);
            return;
        }

        Map<Long, Set<EnumCompanyServiceType>> sourceBusinessServiceTypeMap = sourceBusiness.stream().collect(Collectors.groupingBy(AccountBusiness::getId,
                Collectors.mapping(AccountBusiness::getEnumCompanyServiceTypes,
                        Collectors.flatMapping(Set::stream, Collectors.toSet()))));

        List<AccountBusiness> targetBusiness = accountBusinessRepository.findAllByAccountCompanyId(targetCompanyId);
        Map<Long, Set<EnumCompanyServiceType>> targetBusinessServiceTypeMap = targetBusiness.stream().collect(Collectors.groupingBy(AccountBusiness::getId,
                Collectors.mapping(AccountBusiness::getEnumCompanyServiceTypes,
                        Collectors.flatMapping(Set::stream, Collectors.toSet()))));

        Map<Long, Set<Long>> sourceBusinessContactMap = accountBusinessContactRelationRepository.findAllByAccountBusinessIdIn(sourceBusiness.stream().map(AccountBusiness::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(AccountBusinessContactRelation::getAccountBusinessId,
                        Collectors.mapping(AccountBusinessContactRelation::getClientContactId, Collectors.toSet())));

        Map<Long, Set<Long>> targetBusinessContactMap = accountBusinessContactRelationRepository.findAllByAccountBusinessIdIn(targetBusiness.stream().map(AccountBusiness::getId).collect(Collectors.toList()))
                .stream().collect(Collectors.groupingBy(AccountBusinessContactRelation::getAccountBusinessId,
                        Collectors.mapping(AccountBusinessContactRelation::getClientContactId, Collectors.toSet())));
        BiFunction<AccountBusiness, AccountBusiness, Boolean> distinct = (source, target) -> {
            Set<EnumCompanyServiceType> sourceServiceType = sourceBusinessServiceTypeMap.getOrDefault(source.getId(), Set.of());
            Set<EnumCompanyServiceType> targetServiceType = targetBusinessServiceTypeMap.getOrDefault(target.getId(), Set.of());
            boolean equalsServiceType = sourceServiceType.equals(targetServiceType);

            Set<Long> sourceContacts = sourceBusinessContactMap.getOrDefault(source.getId(), Set.of());
            Set<Long> targetContacts = targetBusinessContactMap.getOrDefault(target.getId(), Set.of());
            boolean equalsContacts = sourceContacts.equals(targetContacts);
            return equalsServiceType && equalsContacts;
        };

        List<AccountBusiness> businessList = sourceBusiness.stream().filter(business -> targetBusiness.stream().noneMatch(target -> distinct.apply(business, target)))
                .peek(business -> business.setAccountCompanyId(targetCompanyId))
                .toList();
        accountBusinessRepository.saveAll(businessList);
    }

    private void mergeContacts(Long sourceCompanyId, Long targetCompanyId) {
        List<SalesLeadClientContact> sourceContacts = salesLeadClientContactRepository.findAllByCompanyId(sourceCompanyId);
        if (sourceContacts.isEmpty()) {
            log.info("No contacts found in source company with id: {}", sourceCompanyId);
        }

        List<SalesLeadClientContact> targetContacts = salesLeadClientContactRepository.findAllByCompanyId(targetCompanyId);

        List<SalesLeadClientContact> mergedContacts = new ArrayList<>();

        for (SalesLeadClientContact sourceContact : sourceContacts) {

            // 系统中不可能出现两个不同的 talent ，但是有相同的联系方式，所以只需要判断是否是同一个 talent 即可
            Optional<SalesLeadClientContact> equalTalent = targetContacts.stream().filter(targetContact -> targetContact.getTalentId().equals(sourceContact.getTalentId()))
                    .findFirst();
            // 重复的联系人，需要修改关联数据, 并且删除
            if (equalTalent.isPresent()) {
                Long targetId = equalTalent.get().getId();
                // job 客户联系人
                Query updateJobContact = entityManager.createNativeQuery("UPDATE job_company_contact_relation SET client_contact_id = :targetId WHERE client_contact_id = :sourceId");
                updateJobContact.setParameter("targetId", targetId);
                updateJobContact.setParameter("sourceId", sourceContact.getId());
                updateJobContact.executeUpdate();
                // 流程客户联系人
                Query applicationContactUpdate = entityManager.createNativeQuery("UPDATE talent_recruitment_process_onboard_client_info set client_contact_id = :targetId WHERE client_contact_id = :sourceId");
                applicationContactUpdate.setParameter("targetId", targetId);
                applicationContactUpdate.setParameter("sourceId", sourceContact.getId());
                applicationContactUpdate.executeUpdate();

                salesLeadClientContactRepository.delete(sourceContact);
            } else {
                mergedContacts.add(sourceContact);
            }
        }
        if (!mergedContacts.isEmpty()) {
            salesLeadClientContactRepository.saveAll(mergedContacts.stream().peek(contact -> contact.setCompanyId(targetCompanyId)).toList());
        }
    }

    private void mergeStart(Long sourceCompanyId, Long targetCompanyId) {
        List onboardTalentIds = entityManager.createNativeQuery("SELECT talent_id FROM start WHERE company_id = :sourceCompanyId").setParameter("sourceCompanyId", sourceCompanyId).getResultList();
        String targetCompanyName = companyRepository.findById(targetCompanyId).map(Company::getFullBusinessName).orElse("");
        if (!onboardTalentIds.isEmpty()) {
            for (Object onboardTalentId : onboardTalentIds) {
                BigInteger talentId = (BigInteger) onboardTalentId;
                entityManager.createNativeQuery("""
                        UPDATE talent_additional_info tai
                        JOIN talent t ON t.additional_info_id = tai.id
                        SET extended_info = JSON_REPLACE(
                            JSON_REPLACE(
                                extended_info,
                                '$.experiences[0].companyId',
                                :targetCompanyId
                            ),
                            '$.experiences[0].companyName',
                            :targetCompanyName
                        )
                        WHERE JSON_EXTRACT(extended_info, '$.experiences[0].companyId') = :sourceCompanyId AND t.id = :talentId
                        """)
                        .setParameter("targetCompanyId", targetCompanyId)
                        .setParameter("targetCompanyName", targetCompanyName)
                        .setParameter("sourceCompanyId", sourceCompanyId)
                        .setParameter("talentId", talentId)
                        .executeUpdate();
            }
        }
        Query updateStartInfo = entityManager.createNativeQuery("UPDATE start SET company_id = :targetCompanyId, last_modified_date = NOW() WHERE company_id = :sourceCompanyId");
        executeUpdate(updateStartInfo, sourceCompanyId, targetCompanyId);
    }


    @Transactional
    @Override
    public void modifyCompanyBusinessName(Long companyId, String businessName) {
        companyRepository.findById(companyId).ifPresent(company -> {
            company.setFullBusinessName(businessName);
            companyRepository.save(company);
            //修改日程工商全称
            companyRepository.updateCalendarCompanyName(companyId,businessName);
        });
    }


    private void executeUpdate(Query query, Long sourceCompanyId, Long targetCompanyId) {
        query.setParameter("targetCompanyId", targetCompanyId);
        query.setParameter("sourceCompanyId", sourceCompanyId);
        query.executeUpdate();
    }
}
