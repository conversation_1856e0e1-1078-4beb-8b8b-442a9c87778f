package com.altomni.apn.company.vo.folder;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.enumeration.folder.FolderPermission;
import com.altomni.apn.company.domain.folder.CompanySearchFolder;
import com.altomni.apn.company.domain.vm.CompanyCustomFolderVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "vo for customFolder")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CustomFolderSearchVO extends AbstractAuditingEntity implements Serializable {

    @ApiModelProperty(value = "The id for folder.")
    private Long id;

    @ApiModelProperty(value = "The name for folder.")
    private String name;

    @ApiModelProperty(value = "The note for folder.")
    private String note;

    @ApiModelProperty(value = "The category for customFolder.", allowableValues = "PROSPECT_ALL_COMPANY, CLIENT_ALL_COMPANY")
    private CategoryFolderType category;

    @ApiModelProperty(value = "The permission for shared folder.")
    private FolderPermission permission;

    @ApiModelProperty(value = "The sharedTeams for customFolder.")
    private List<FolderSharedVO> sharedTeams;

    @ApiModelProperty(value = "The sharedUsers for customFolder.")
    private List<FolderSharedVO> sharedUsers;

    @ApiModelProperty(value = "The note for folder.")
    private String creator;

    public static CustomFolderSearchVO fromCompanyCustomFolderVM(CompanyCustomFolderVM companyCustomFolderVM) {
        CustomFolderSearchVO customFolderSearchVO = new CustomFolderSearchVO();
        ServiceUtils.myCopyProperties(companyCustomFolderVM, customFolderSearchVO);
        return customFolderSearchVO;
    }

    public static CustomFolderSearchVO fromCompanySearchFolder(CompanySearchFolder companySearchFolder) {
        CustomFolderSearchVO customFolderSearchVO = new CustomFolderSearchVO();
        ServiceUtils.myCopyProperties(companySearchFolder, customFolderSearchVO);
        return customFolderSearchVO;
    }

}
