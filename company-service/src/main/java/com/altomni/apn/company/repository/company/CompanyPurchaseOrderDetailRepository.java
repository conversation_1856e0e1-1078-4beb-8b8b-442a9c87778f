package com.altomni.apn.company.repository.company;

import com.altomni.apn.company.domain.company.CompanyPurchaseOrderDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

@Repository
public interface CompanyPurchaseOrderDetailRepository extends JpaRepository<CompanyPurchaseOrderDetail, Long> {

    List<CompanyPurchaseOrderDetail> findCompanyPurchaseOrderDetailByPurchaseOrderId(Long purchaseOrderId);

    List<CompanyPurchaseOrderDetail> findCompanyPurchaseOrderDetailByPurchaseOrderIdIn(List<Long> purchaseOrderId);

    List<CompanyPurchaseOrderDetail> findCompanyPurchaseOrderDetailByPurchaseOrderIdAndOrderNumber(Long purchaseOrderId,String orderNumber);


    @Modifying
    @Transactional
    @Query(value = "update  company_purchase_order_detail  set balance=?1,last_modified_date=now() where id = ?2 ",nativeQuery = true)
    void updateBalanceById(BigDecimal balance,Long id);

    @Query(value = " select d.id from company_purchase_order_detail d\n" +
            " left join company_purchase_order o on o.id = d.purchase_order_id\n" +
            " where d.order_number in(?1) and o.company_id = ?2",nativeQuery = true)
    List<BigInteger> findByCompanyIdAndOrderNumber(List<String> orderNumberList,Long companyId);

    @Transactional
    @Query(value = "delete from company_purchase_order_detail where id in(?1)",nativeQuery = true)
    void deleteByIdIn(List<Long> idList);
}