package com.altomni.apn.company.service.report;

import com.altomni.apn.company.service.dto.report.PerformanceReportSearchDTO;
import com.altomni.apn.company.vo.report.PerformanceReportVO;

import javax.servlet.http.HttpServletResponse;

public interface PerformanceReportService {

    PerformanceReportVO findPerformanceReport(PerformanceReportSearchDTO condition);

    void downloadReport(PerformanceReportSearchDTO condition, HttpServletResponse response);
}
