package com.altomni.apn.company.domain.enumeration.business;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class SalesLeadStatusConverter extends AbstractAttributeConverter<SalesLeadStatus, Integer> {
    public SalesLeadStatusConverter() {
        super(SalesLeadStatus::toDbValue, SalesLeadStatus::fromDbValue);
    }
}
