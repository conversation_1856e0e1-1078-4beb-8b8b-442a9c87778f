package com.altomni.apn.company.service.dto.es;

import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.company.domain.enumeration.note.CompanyNoteType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(description = "dto for searchNote")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class NoteSearchDTO implements Serializable {

    @ApiModelProperty(value = "the companyIds for note.")
    private List<Long> companyIds;

    @ApiModelProperty(value = "the salesLeadIds for note.")
    private List<Long> salesLeadIds;

    @ApiModelProperty(value = "the module for note.")
    private CompanyNoteType module;

    @ApiModelProperty(value = "the text for note.")
    private String note;

    @ApiModelProperty(value = "the tenantId for note.")
    private Long tenantId;

    @ApiModelProperty(value = "the timeZone for note.")
    private String timeZone;

    @ApiModelProperty(value = "the language for note.")
    private String language;

    @ApiModelProperty(value = "the clientContactId for note.")
    private List<Long> clientContactIds;
}
