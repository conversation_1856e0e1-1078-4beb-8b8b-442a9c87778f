package com.altomni.apn.company.service.dto.folder;

import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "dto for customFolder")
public class CustomFolderDTO implements Serializable {

    @ApiModelProperty(value = "The name for customFolder.")
    @NotEmpty
    private String name;

    @ApiModelProperty(value = "The sharedTeams for customFolder.")
    private List<FolderSharedDTO> sharedTeams;

    @ApiModelProperty(value = "The sharedUsers for customFolder.")
    private List<FolderSharedDTO> sharedUsers;

    @ApiModelProperty(value = "The companyId for customFolder.")
    private List<Long> companyIds;

    @ApiModelProperty(value = "The category for customFolder.", allowableValues = "PROSPECT_ALL_COMPANY, CLIENT_ALL_COMPANY")
    private CategoryFolderType category;

    @ApiModelProperty(value = "The note for customFolder.")
    private String note;

}
