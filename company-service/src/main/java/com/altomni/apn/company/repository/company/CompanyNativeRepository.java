package com.altomni.apn.company.repository.company;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.common.vo.recruiting.KpiReportCompanyUpgradeToClientVO;
import com.altomni.apn.company.domain.company.CompanyMigrate;
import com.altomni.apn.company.domain.company.CompanyReportBrief;
import com.altomni.apn.company.domain.saleslead.SalesLeadReportBrief;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.company.vo.company.CompanyCalendarVO;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectSearchVM;
import com.altomni.apn.company.web.rest.vm.company.CompanyVO;
import okhttp3.Headers;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.io.IOException;
import java.lang.reflect.Method;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Repository
public class CompanyNativeRepository {

    @PersistenceContext
    private EntityManager entityManager;

    private final Double CLIENT_ACCOUNT_PROGRESS = 1.20;

    @Transactional(readOnly = true)
    public List<CompanyVO> queryCompanyName(Long tenantId, String name) {

        StringBuilder dataSql = new StringBuilder("select id,full_business_name as resultName from company \n" +
                "where id in( select company_id from timesheet_talent_assignment where tenant_id =:tenantId)");

        if (StringUtils.isNotBlank(name)) {
            dataSql.append(" and full_business_name like :name");
        }
        dataSql.append(" order by created_date desc ");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", tenantId);

        if (StringUtils.isNotBlank(name)) {
            dataQuery.setParameter("name", "%" + name + "%");
        }
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(CompanyVO.class));
        return dataQuery.getResultList();
    }

    public List<CompanyMigrate> queryAllCompanyList(Long tenantId, AccountCompanyStatus active, Integer limit) {
        Map<Integer, Object> paramMap = new HashMap<>(16);
        StringBuilder dataSql = new StringBuilder("SELECT c.* FROM company c INNER JOIN account_business ab ON c.id = ab.company_id AND ab.business_progress <> 65 WHERE c.tenant_id = ?1");
        paramMap.put(1, tenantId);

        if (active != null) {
            paramMap.put(paramMap.size() + 1, active.toDbValue());
            dataSql.append(" AND c.active = ?").append(paramMap.size());
        }

        dataSql.append(" GROUP BY c.id");

        if (limit != null) {
            paramMap.put(paramMap.size() + 1, limit);
            dataSql.append(" LIMIT ?").append(paramMap.size());
        }
        return searchData(dataSql.toString(), CompanyMigrate.class, paramMap);
    }

    private void createQuerySubmitToJobCount(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition, Pageable pageable) {
        sb.append("SELECT c.* FROM company c LEFT JOIN account_business ca ON c.id = ca.company_id \n" +
                "LEFT JOIN business_flow_administrator sa ON c.id = sa.company_id \n" +
                "LEFT JOIN `user` u ON sa.user_id = u.id \n " +
                "WHERE c.tenant_id = ?1 and u.activated = 1 \n" +
                "AND c.created_date BETWEEN ?2 AND ?3");

        setFilterCompanyName(sb, paramMap, condition);
        setFilterCompanyBdByUserId(sb, paramMap, condition);
        setFilterCompanySalesLeadAccountProgress(sb, paramMap, condition);
        setFilterCompanySalesLeadServiceType(sb, paramMap, condition);
        setFilterCompanyIndustry(sb, paramMap, condition);
        setFilterCompanySalesLeadOwner(sb, paramMap, condition);
        setFilterCompanySalesLeadBdOwner(sb, paramMap, condition);
//        setFilterCompanyType(sb, paramMap, condition);
        setFilterCompanyCountry(sb, paramMap, condition);
        setFilterCompanyCreator(sb, paramMap, condition);

        sb.append(" GROUP BY c.id");
    }

    public List<SalesLeadReportBrief> findAllByCompanyIdIn(List<Long> companyIds) {
        Map<Integer, Object> paramMap = new HashMap<>(16);
        StringBuilder sb = new StringBuilder().append("SELECT * FROM account_business cs WHERE cs.company_id IN ?1 and cs.business_progress != 65 ");
        paramMap.put(1, companyIds);
        return searchData(sb.toString(), SalesLeadReportBrief.class, paramMap);
    }

    public List<CompanyReportBrief> searchAllCompaniesForBDReport(Instant fromDate, Instant toDate, CompanyProspectSearchVM condition, Pageable pageable) {
        Map<Integer, Object> paramMap = new HashMap<>(16);
        StringBuilder sb = new StringBuilder();
        paramMap.put(1, SecurityUtils.getTenantId());
        paramMap.put(2, fromDate);
        paramMap.put(3, toDate);
        createQuerySubmitToJobCount(sb, paramMap, condition, pageable);
        return searchData(sb.toString(), CompanyReportBrief.class, paramMap);
    }

    private void setFilterCompanyName(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
        if (StringUtils.isNotEmpty(condition.getCompanyName())) {
            paramMap.put(paramMap.size() + 1, String.format("%%%s%%", condition.getCompanyName()));
            sb.append(" AND c.`full_business_name` LIKE ?").append(paramMap.size());
        }
    }

    private void setFilterCompanyBdByUserId(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
        if (CollUtil.isNotEmpty(condition.getTeamUserIds())) {
            paramMap.put(paramMap.size() + 1, condition.getTeamUserIds());
            sb.append(" AND EXISTS (SELECT 1 FROM business_flow_administrator WHERE company_id = c.id AND sales_lead_role = 2 AND user_id IN ?").append(paramMap.size()).append(")");
        }
    }

    private void setFilterCompanySalesLeadAccountProgress(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
        if (condition.getAccountProgress() != null) {
            paramMap.put(paramMap.size() + 1, condition.getAccountProgress());
            sb.append(" AND ca.business_progress = ?").append(paramMap.size());
        }
    }

    private void setFilterCompanySalesLeadServiceType(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
        if (CollUtil.isNotEmpty(condition.getServiceTypes())) {
            paramMap.put(paramMap.size() + 1, condition.getServiceTypes().stream().map(EnumCompanyServiceType::getId).collect(Collectors.toList()));
            sb.append(" AND EXISTS (SELECT 1 FROM account_business_service_type_relation cas WHERE cas.account_business_id = ca.id AND cas.service_type_id IN ?").append(paramMap.size()).append(")");
        }
    }

    private void setFilterCompanyIndustry(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
        if (CollUtil.isNotEmpty(condition.getIndustries())) {
            paramMap.put(paramMap.size() + 1, condition.getIndustries().stream().map(o -> Long.parseLong(o.getEnumId())).collect(Collectors.toList()));
            sb.append(" AND EXISTS (SELECT 1 FROM company_industry_relation cr WHERE cr.company_id = c.id AND cr.industry_id IN ?").append(paramMap.size()).append(")");
        }
    }

    private void setFilterCompanySalesLeadOwner(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
        if (CollUtil.isNotEmpty(condition.getOwners())) {
            paramMap.put(paramMap.size() + 1, String.format("%%%s%%", condition.getOwners().iterator().next().getFullName()));
            sb.append(" AND EXISTS (SELECT 1 FROM business_flow_administrator sa1 INNER JOIN `user` u1 ON sa1.user_id = u1.id WHERE sa1.company_id = c.id AND sa1.sales_lead_role = 1 AND CONCAT(u1.first_name,' ',u1.last_name) LIKE ?").append(paramMap.size()).append(")");
        }
    }

    private void setFilterCompanySalesLeadBdOwner(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
        if (CollUtil.isNotEmpty(condition.getBdOwners())) {
            paramMap.put(paramMap.size() + 1, String.format("%%%s%%", condition.getBdOwners().iterator().next().getFullName()));
            sb.append(" AND EXISTS (SELECT 1 FROM business_flow_administrator sa1 INNER JOIN `user` u1 ON sa1.user_id = u1.id WHERE sa1.company_id = c.id AND sa1.sales_lead_role = 2 AND CONCAT(u1.first_name,' ',u1.last_name) LIKE ?").append(paramMap.size()).append(")");
        }
    }

//    private void setFilterCompanyType(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
//        if (condition.getSalesLeadStatus() != null) {
//            if (condition.getSalesLeadStatus().equals(SalesLeadStatus.CLIENT)) {
//                sb.append(" AND ca.account_progress = 1.20 ");
//            } else {
//                sb.append(" AND ca.account_progress < 1.20 ");
//            }
//        }
//    }

    private void setFilterCompanyCountry(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
        if (CollUtil.isNotEmpty(condition.getCountries())) {
            paramMap.put(paramMap.size() + 1, String.format("%%%s%%", condition.getCountries().iterator().next()));
            sb.append(" AND EXISTS (SELECT 1 FROM company_location l WHERE l.company_id = c.id AND l.official_country LIKE ?").append(paramMap.size()).append(")");
        }
    }

    private void setFilterCompanyCreator(StringBuilder sb, Map<Integer, Object> paramMap, CompanyProspectSearchVM condition) {
        if (StringUtils.isNotEmpty(condition.getCreatedBy())) {
            paramMap.put(paramMap.size() + 1, String.format("%%%s%%", condition.getCreatedBy()));
            sb.append(" AND EXISTS (SELECT 1 FROM `user` uc WHERE uc.id = c.puser_id AND CONCAT(uc.first_name,' ',uc.last_name) LIKE ?").append(paramMap.size()).append(")");
        }
    }

    private <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        entityManager.clear();
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchData(queryStr, clazz, paramMap);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }


    private <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        return query.getResultList();
    }

    private Integer checkInList(Map<Integer, Object> map) {
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new RuntimeException("query sql in condition list > 1000 more than 1");
        }
        return keyList.get(0);
    }

    @Transactional(readOnly = true)
    public List<Map<String, Object>> selectTalentAdditionalInfoByIds(List<Long> companyIds) {

        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("select t.id,t.extended_info from talent_additional_info t,\n" +
                "    JSON_TABLE(extended_info\t, '$.experiences[*]' COLUMNS (\n" +
                "            id INT PATH '$.activeCompanyId',\n" +
                "               name VARCHAR(255) PATH '$.companyName'\n" +
                "            )) AS aaa\n" +
                "    where aaa.id in( :companyIds )");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("companyIds", companyIds);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return dataQuery.getResultList();
    }

    @Transactional(readOnly = true)
    public List<CompanyCalendarVO> searchCompanyCalendar(List<Long> companyIdList) {

        StringBuilder dataSql = new StringBuilder("""
                select c.full_business_name as fullBusinessName,c.id as companyId,
                bfa.user_id as userId,u.email,u.custom_timezone as timezone,u.tenant_id as tenantId
                from account_business ab
                left join business_flow_administrator bfa on bfa.account_business_id = ab.id
                left join company c on c.id = ab.company_id
                left join user u on u.id = bfa.user_id
                where ab.company_id in (:companyIds) and bfa.sales_lead_role=2
                """);


        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("companyIds", companyIdList);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(CompanyCalendarVO.class));
        return dataQuery.getResultList();
    }

    public boolean searchServerTypeBySalesLeadId(Long companyId,Long salesLeadId){
        StringBuilder dataSql = new StringBuilder("""
                select distinct cst.en_display from account_business t
                                left join account_business_service_type_relation tr on  tr.account_business_id = t.id
                                left join enum_company_service_type cst on cst.id = tr.service_type_id
                                where t.company_id =:companyId and t.id=:salesLeadId
                """);
        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());
        dataQuery.setParameter("companyId", companyId);
        dataQuery.setParameter("salesLeadId", salesLeadId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String,Object>> mapList = dataQuery.getResultList();
        if (null == mapList || mapList.isEmpty()) {
            return false;
        }
        List<String> strList = mapList.stream().map(m -> m.get("en_display").toString()).collect(Collectors.toList());
        boolean fteFlag = false;
        for (String v : strList) {
            if (v.indexOf("FTE") != -1) {
                fteFlag = true;
                break;
            }
        }
        return fteFlag;
    }
}
