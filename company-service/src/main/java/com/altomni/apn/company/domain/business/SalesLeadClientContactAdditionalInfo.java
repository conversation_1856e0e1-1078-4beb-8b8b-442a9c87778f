package com.altomni.apn.company.domain.business;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import liquibase.pro.packaged.A;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A SalesLeadClientContactAdditionalInfo.
 */
@ApiModel(description = "Company contact additional info")
@Entity
@Data
@Table(name = "company_contact_additional_info")
@NoArgsConstructor
@AllArgsConstructor
public class SalesLeadClientContactAdditionalInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The id for company contact")
    @Column(name = "company_contact_id")
    private Long companyContactId;

    @ApiModelProperty(value = "The additionalInfo for company contact")
    @Column(name = "extended_info")
    private String extendedInfo;

    public SalesLeadClientContactAdditionalInfo(Long companyContactId, String extendedInfo) {
        this.companyContactId = companyContactId;
        this.extendedInfo = extendedInfo;
    }
}