package com.altomni.apn.company.domain.vo.company;

import com.altomni.apn.company.vo.company.NoCompanyAffiliatesVO;
import com.altomni.apn.company.vo.company.NoPoachingAccountVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class NoPoachingAccountResultVO implements Serializable {

    @ApiModelProperty(value = "id for company")
    private List<NoPoachingAccountVO> noPoaching;

    @ApiModelProperty(value = "active for company")
    private List<NoCompanyAffiliatesVO> noCompanyAffiliates;



}
