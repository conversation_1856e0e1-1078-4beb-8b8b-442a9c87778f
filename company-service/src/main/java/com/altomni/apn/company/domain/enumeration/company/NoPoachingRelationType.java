package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum NoPoachingRelationType implements ConvertedEnum<Integer> {
    SUBSIDIARY(1),
    PARENT_COMPANY(2);

    private final Integer dbValue;

    NoPoachingRelationType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<NoPoachingRelationType, Integer> resolver =
            new ReverseEnumResolver<>(NoPoachingRelationType.class, NoPoachingRelationType::toDbValue);

    public static NoPoachingRelationType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}