package com.altomni.apn.company.vo.salesLead;

import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.saleslead.SalesLead;
import com.altomni.apn.company.domain.saleslead.SalesLeadReportBrief;
import com.altomni.apn.company.vo.contact.CompanyContactVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* date:2023-04-19
*/
@AllArgsConstructor
@ApiModel(description = "Vo for salesLead")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class SalesLeadVO implements Serializable {

    @ApiModelProperty(value = "the id for salesLead.")
    private Long id;

    @ApiModelProperty(value = "The accountProgress for salesLead.")
    private Integer businessProgress;

    @ApiModelProperty(value = "The companyServiceTypes for salesLead.")
    private List<Integer> companyServiceTypes;

    @ApiModelProperty(value = "The salesLeadClientContacts for salesLead.")
    private List<CompanyContactVO> salesLeadClientContacts;

    @ApiModelProperty(value = "The salesLeadsOwners for salesLead.")
    private List<SalesLeadAdministratorVO> salesLeadsOwners;

    @ApiModelProperty(value = "The salesLeadsBdOwners for salesLead.")
    private List<SalesLeadAdministratorVO> salesLeadsBdOwners;

    @ApiModelProperty(value = "The am for salesLead.")
    private List<SalesLeadAdministratorVO> accountManagers;

    @ApiModelProperty(value = "The estimatedDealTime for salesLead.")
    private Instant estimatedDealTime;

    @ApiModelProperty(value = "The leadSource for salesLead.")
    private Long leadSource;

    @ApiModelProperty(value = "The companyId for salesLead.")
    private Long accountCompanyId;

    public static SalesLeadVO fromSalesLeadReportBrief(SalesLeadReportBrief salesLead) {
        SalesLeadVO salesLeadVO = new SalesLeadVO();
        ServiceUtils.myCopyProperties(salesLead, salesLeadVO);
        return salesLeadVO;
    }
}