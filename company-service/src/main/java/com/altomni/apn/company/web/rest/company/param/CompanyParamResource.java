package com.altomni.apn.company.web.rest.company.param;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.service.company.param.CompanyParamService;
import com.altomni.apn.company.vo.param.CompanyParamConfigVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
*
* <AUTHOR>
* date:2023-04-17
*/
@Api(tags = {"APN-Company-Params"})
@Slf4j
@RestController
@RequestMapping("/api/v3/params")
public class CompanyParamResource {

    @Resource
    private CompanyParamService companyParamService;

    /**
     *
     * @param
     * @return
     */
    @GetMapping("/config")
    public ResponseEntity<CompanyParamConfigVO> queryCompanyParamsConfig() {
        log.info("[APN: Company params @{}] REST request to query company params config .", SecurityUtils.getUserId());
        CompanyParamConfigVO companyParamConfigVO = companyParamService.queryCompanyParamsConfig();
        return new ResponseEntity<>(companyParamConfigVO, HttpStatus.OK);
    }

    @GetMapping("/versions")
    public ResponseEntity<String> queryCompanyParamsConfigVersion() {
        log.info("[APN: Company params @{}] REST request to query company params config version .", SecurityUtils.getUserId());
        return new ResponseEntity<>(companyParamService.queryCompanyParamsConfigVersion(), HttpStatus.OK);
    }

    @GetMapping("/config/clear")
    public ResponseEntity<HttpStatus> clearCompanyParamsConfigCache() {
        log.info("[APN: Company params @{}] REST request to clear company params config cache.", SecurityUtils.getUserId());
        companyParamService.clearCompanyParamsConfigCache();
        return new ResponseEntity<>(HttpStatus.OK);
    }

}
