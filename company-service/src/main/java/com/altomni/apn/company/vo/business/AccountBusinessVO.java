package com.altomni.apn.company.vo.business;

import com.altomni.apn.company.vo.contact.ContactVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class AccountBusinessVO implements Serializable {

    @ApiModelProperty(value = "id for accountBusiness")
    private Long id;

    @ApiModelProperty(value = "name for accountBusiness")
    private String name;

    @ApiModelProperty(value = "progress for accountBusiness")
    private Integer businessProgress;

    @ApiModelProperty(value = "companyServiceTypes for accountBusiness")
    private List<Integer> companyServiceTypes;

    @ApiModelProperty(value = "name for accountBusiness")
    private List<ContactVO> clientContacts;

    @ApiModelProperty(value = "salesLeadsOwners for contact")
    private List<OwnerVO> salesLeadsOwners;

    @ApiModelProperty(value = "accountBusinessOwner for contact")
    private List<OwnerVO> accountBusinessOwners;

    @ApiModelProperty(value = "accountManager for accountBusiness")
    private List<OwnerVO> accountManagers;

    @ApiModelProperty(value = "co accountManager for accountBusiness")
    private List<OwnerVO> cooperateAccountManagers;

    @ApiModelProperty(value = "recruiter for accountBusiness")
    private List<OwnerVO> recruiter;

    @ApiModelProperty(value = "estimatedDealTime for contact")
    private Instant estimatedDealTime;

    @ApiModelProperty(value = "leadSource for contact")
    private Integer leadSource;

    @ApiModelProperty(value = "accountCompanyId for accountBusiness")
    private Long accountCompanyId;

    @ApiModelProperty(value = "createdDate for accountBusiness")
    private Instant createdDate;

    @ApiModelProperty(value = "contractIds for accountBusiness")
    private List<Long> contractIds;

    @ApiModelProperty(value = "countryLocation")
    private String countryLocation;

    @ApiModelProperty(value = "businessUnit")
    private String businessUnit;

    @ApiModelProperty(value = "comment")
    private String comment;

}
