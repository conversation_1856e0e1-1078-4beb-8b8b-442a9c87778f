package com.altomni.apn.company.domain.company.projectTeam;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * A ProjectTeamUser.
 * <AUTHOR>
 */
@Entity
@Table(name = "company_project_team_user")
public class CompanyProjectTeamUser extends AbstractPermissionAuditingEntity implements Serializable
{

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "team id", required = true)
    @NotNull
    @Column(name = "team_id", nullable = false)
    private Long teamId;

    @ApiModelProperty(value = "user id", required = true)
    @NotNull
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @JsonIgnore
    @ApiModelProperty(value = "user' permissionIntValue on job. It is required when create the entity.")
    @Column(name = "permission")
    private Integer permission;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTeamId() {
        return teamId;
    }

    public CompanyProjectTeamUser teamId(Long teamId) {
        setTeamId(teamId);
        return this;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getUserId() {
        return userId;
    }

    public CompanyProjectTeamUser userId(Long userId) {
        setUserId(userId);
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getPermission() { return this.permission; }

    public CompanyProjectTeamUser permission(Integer permission) {
        setPermission(permission);
        return this;
    }

    public void setPermission(Integer permission) { this.permission = permission; }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CompanyProjectTeamUser projectTeamUser = (CompanyProjectTeamUser) o;
        if (projectTeamUser.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), projectTeamUser.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "CompanyProjectTeamUser{" +
                "id=" + id +
                ", teamId=" + teamId +
                ", userId=" + userId +
                ", permission=" + permission +
                '}';
    }
}
