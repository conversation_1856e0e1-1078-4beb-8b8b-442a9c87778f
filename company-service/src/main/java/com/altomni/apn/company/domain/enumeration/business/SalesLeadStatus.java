package com.altomni.apn.company.domain.enumeration.business;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractStatus enumeration.
 */
public enum SalesLeadStatus implements ConvertedEnum<Integer> {
    PROSPECT(0),
    CLIENT(1);


    private final Integer dbValue;

    SalesLeadStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<SalesLeadStatus, Integer> resolver =
        new ReverseEnumResolver<>(SalesLeadStatus.class, SalesLeadStatus::toDbValue);

    public static SalesLeadStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
