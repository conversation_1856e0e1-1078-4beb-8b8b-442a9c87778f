package com.altomni.apn.company.web.rest.company.overview;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.company.BriefCompanyDTO;
import com.altomni.apn.common.dto.search.GlobalSearchConditionDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.service.company.overview.CompanyOverviewService;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteSearchFilterDTO;
import com.altomni.apn.company.service.dto.overview.*;
import com.altomni.apn.company.service.store.StoreService;
import com.altomni.apn.company.vo.company.*;
import com.altomni.apn.company.web.rest.vm.company.CompanyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-17
*/
@Api(tags = {"APN-Company-Overview"})
@Slf4j
@RestController
@RequestMapping("/api/v3/companies")
public class CompanyOverviewResource {

    @Resource
    private CompanyOverviewService companyOverviewService;

    @Resource
    private StoreService storeService;

//    @GetMapping("/search/histories")
//    @ApiOperation(value = "query company search history")
//    public ResponseEntity<List<String>> querySearchHistory() {
//        log.info("[APN: Company Overview @{}] REST request to query company search histories.", SecurityUtils.getUserId());
//        List<String> searchHistoryList = companyOverviewService.querySearchHistory();
//        return new ResponseEntity<>(searchHistoryList, HttpStatus.OK);
//    }

//    @PostMapping("/search/test")
//    public ResponseEntity<HttpStatus> testSearch(@RequestBody CompanyClientNoteSearchDTO companyClientNoteSearchDTO) {
//        log.info("[APN: Company search histories test @{}] REST request to test company search histories.", SecurityUtils.getUserId());
//        companyOverviewService.testSearch(companyClientNoteSearchDTO);
//        return new ResponseEntity<>(HttpStatus.OK);
//    }

    @PostMapping("/search")
    @ApiOperation(value = "search company list")
    public ResponseEntity<CompanyListVO> searchCompany(@RequestBody CompanySearchConditionDTO searchDto, Pageable pageable) throws Throwable {
        log.info("[APN: Company search @{}] REST request to search company list.", SecurityUtils.getUserId());
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        CompanyListVO result = companyOverviewService.searchCompany(searchDto, pageable, headers, false);
        return ResponseEntity.ok().headers(headers).body(result);
    }

    @PostMapping("/search/global-search")
    @ApiOperation(value = "search company list")
    public ResponseEntity<List<Object>> searchCompanyForApnGlobalSearch(@RequestBody GlobalSearchConditionDTO globalSearchDto) throws Throwable {
        log.info("[APN: Company search @{}] REST request to search company list.", SecurityUtils.getUserId());
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));

        List<Object> result = companyOverviewService.searchCompanyForApnGlobalSearch(globalSearchDto, headers);

        return ResponseEntity.ok().headers(headers).body(result);
    }

    /**
     *  create a prospect company.
     * @param companyProspectDTO
     * @return
     */
//    @PostMapping("/prospect")
//    @ApiOperation(value = "create a prospect company")
//    public ResponseEntity<CompanyProspectVO> createProspectCompany(@Valid @RequestBody CompanyProspectDTO companyProspectDTO) {
//        log.info("[APN: Company Overview @{}] REST request to create prospect company. {}", SecurityUtils.getUserId(), companyProspectDTO);
//        CompanyProspectVO companyProspectVO = companyOverviewService.createProspectCompany(companyProspectDTO);
//        return new ResponseEntity<>(companyProspectVO, HttpStatus.CREATED);
//    }

    /**
     *  create a client company. Only for general use.
     * @param companyClientDTO
     * @return
     */
//    @PostMapping("/client")
//    @ApiOperation(value = "create a client company")
//    public ResponseEntity<CompanyClientVO> createClientCompany(@Valid @RequestBody CompanyClientDTO companyClientDTO) {
//        log.info("[APN: Company Overview @{}] REST request to create client company. {}", SecurityUtils.getUserId(), companyClientDTO);
//        CompanyClientVO companyClientVO = companyOverviewService.createClientCompany(companyClientDTO);
//        return new ResponseEntity<>(companyClientVO, HttpStatus.CREATED);
//    }

    /**
     * query a prospect company detail
     * @param id
     * @return
     */
//    @GetMapping("/prospect/{companyId}")
//    @ApiOperation(value = "query a prospect company detail")
//    public ResponseEntity<CompanyProspectVO> queryProspectCompanyDetail(@PathVariable(value = "companyId") Long id) {
//        log.info("[APN: Company Overview @{}] REST request to query a prospect company detail. {}", SecurityUtils.getUserId(), id);
//        CompanyProspectVO companyProspectVO = companyOverviewService.queryProspectCompanyDetail(id);
//        return new ResponseEntity<>(companyProspectVO, HttpStatus.OK);
//    }

    /**
     * query a client company detail
     * @param id
     * @return
     */
//    @GetMapping("/client/{companyId}")
//    @ApiOperation(value = "query a client company detail")
//    public ResponseEntity<CompanyClientVO> queryClientCompanyDetail(@PathVariable(value = "companyId") Long id) {
//        log.info("[APN: Company Overview @{}] REST request to query a client company detail. {}", SecurityUtils.getUserId(), id);
//        CompanyClientVO companyClientVO = companyOverviewService.queryClientCompanyDetail(id);
//        return new ResponseEntity<>(companyClientVO, HttpStatus.OK);
//    }

    @GetMapping("/client/{companyId}")
    @ApiOperation(value = "query a company detail")
    public ResponseEntity<AccountCompanyVO> queryClientCompany(@PathVariable("companyId") Long id, @RequestHeader HttpHeaders headers) {
        log.info("queryClientCompany by id {}", id);
        return new ResponseEntity<>(companyOverviewService.queryClientCompany(id, headers), HttpStatus.OK);
    }

    @GetMapping("/client/{companyId}/company-business-users")
    @ApiOperation(value = "query a company co-am info")
    public ResponseEntity<List<AccountCompanyCoAmVO>> queryClientCompanyCoAm(@PathVariable("companyId") Long id) {
        return new ResponseEntity<>(companyOverviewService.queryClientCompanyCoAm(id), HttpStatus.OK);
    }

    @GetMapping("/client/{companyId}/{jobId}")
    @ApiOperation(value = "query a company fte info and sale lead fte info")
    public ResponseEntity<AccountCompanyCoAmFteVO> queryClientCompanyAndSalesLeadFte(@PathVariable("companyId") Long companyId,@PathVariable("jobId") Long jobId) {
        return new ResponseEntity<>(companyOverviewService.queryClientCompanyAndSalesLeadFte(companyId,jobId), HttpStatus.OK);
    }

    /**
     *  update a company basicInfo
     * @param companyBasicDTO
     * @return
     */
//    @PutMapping("/{companyId}/basic-info")
//    @ApiOperation(value = "update a company basicInfo")
//    public ResponseEntity<CompanyClientVO> updateCompanyBasicInfo(@PathVariable("companyId") Long companyId, @Valid @RequestBody CompanyBasicDTO companyBasicDTO) {
//        log.info("[APN: Company Overview @{}] REST request to update a company basicInfo. companyId:{}, dto:{}", SecurityUtils.getUserId(), companyId, companyBasicDTO);
//        CompanyClientVO companyClientVO = companyOverviewService.updateCompanyBasicInfo(companyId, companyBasicDTO);
//        return new ResponseEntity<>(companyClientVO, HttpStatus.CREATED);
//    }

    /**
     *  update a company salesLeads info
     * @param companyId
     * @param salesLeadUpdateDTOList
     * @return
     */
//    @PutMapping("/{companyId}/sales-leads")
//    @ApiOperation(value = "update a company salesLeads info")
//    public ResponseEntity<CompanyProspectVO> updateCompanySalesLeadInfo(@PathVariable("companyId") Long companyId, @Valid @RequestBody List<SalesLeadUpdateDTO> salesLeadUpdateDTOList) {
//        log.info("[APN: Company Overview @{}] REST request to update a company salesLeads info. companyId:{}, dto:{}", SecurityUtils.getUserId(), companyId, salesLeadUpdateDTOList);
//        CompanyProspectVO companyProspectVO = companyOverviewService.updateCompanySalesLeadInfo(companyId, salesLeadUpdateDTOList);
//        return new ResponseEntity<>(companyProspectVO, HttpStatus.CREATED);
//    }

    /**
     *  update a company detail
     * @param companyId
     * @param companyUpdateDTO
     * @return
     */
//    @PutMapping("/{companyId}")
//    @ApiOperation(value = "update a company")
//    public ResponseEntity<CompanyClientVO> updateCompany(@PathVariable("companyId") Long companyId, @Valid @RequestBody CompanyUpdateDTO companyUpdateDTO) {
//        log.info("[APN: Company Overview @{}] REST request to update a company. companyId:{}, dto:{}", SecurityUtils.getUserId(), companyId, companyUpdateDTO);
//        CompanyClientVO companyProspectVO = companyOverviewService.updateCompany(companyId, companyUpdateDTO);
//        return new ResponseEntity<>(companyProspectVO, HttpStatus.CREATED);
//    }

//    @PutMapping("/{companyId}/upgrade")
//    @ApiOperation(value = "upgrade a company")
//    public ResponseEntity<CompanyClientVO> upgradeCompany(@PathVariable("companyId") Long companyId, @Valid @RequestBody CompanyUpgradeDTO companyUpgradeDTO) {
//        log.info("[APN: Company Overview @{}] REST request to upgrade a company. companyId:{}, dto:{}", SecurityUtils.getUserId(), companyId, companyUpgradeDTO);
//        CompanyClientVO companyClientVO = companyOverviewService.upgradeCompany(companyId, companyUpgradeDTO);
//        return new ResponseEntity<>(companyClientVO, HttpStatus.CREATED);
//    }

    @GetMapping("/{companyId}/open-jobs")
    public ResponseEntity<List<CompanyJobVO>> queryOpenJobsByCompanyId(@PathVariable("companyId") Long companyId, @RequestParam("limit") Integer limit) {
        log.info("[APN: Company Overview @{}] REST request to query company's open jobs. companyId:{}, limit:{}", SecurityUtils.getUserId(), companyId, limit);
        return new ResponseEntity<>(companyOverviewService.queryOpenJobsByCompanyId(companyId, limit), HttpStatus.OK);
    }

    @PostMapping("/client/no-contracts")
    public ResponseEntity<List<AccountCompanyVO>> searchNoContractCompany(@RequestBody CompanySearchConditionDTO searchDto, Pageable pageable) {
        log.info("[APN: Company Overview @{}] REST request to search no contract company.", SecurityUtils.getUserId());
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<AccountCompanyVO> result = companyOverviewService.searchNoContractCompany(searchDto, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(result);
    }

    // TODO: 2024/4/18 move to CRM
//    @PostMapping("/salesLead")
//    @NoRepeatSubmit
//    public ResponseEntity<List<AccountCompanyVO>> querySalesLeadByCompanyId(@RequestBody CompanyProgressNoteSearchFilterDTO companyProgressNoteSearchFilterDTO) {
//        return new ResponseEntity<>(companyOverviewService.querySalesLeadByCompanyId(companyProgressNoteSearchFilterDTO.getCompanyIds()), HttpStatus.OK);
//    }

    /**
     *  create a company for employer type tenant. Only for creating tenant.
     * @param companyClientDTO
     * @return
     */
    @PostMapping("/employer-company")
    @ApiOperation(value = "create a client company")
    public ResponseEntity<BriefCompanyDTO> createEmployerTenantCompany(@RequestBody BriefCompanyDTO companyClientDTO) throws IOException {
        log.info("[APN: Company Overview @{}] REST request to create company on employer type. {}", SecurityUtils.getUserId(), companyClientDTO);
        BriefCompanyDTO companyClientVO = companyOverviewService.createEmployerTenantCompany(companyClientDTO);
        return new ResponseEntity<>(companyClientVO, HttpStatus.CREATED);
    }

    /**
     * only use contract replenish
     * @return
     */
//    @GetMapping("/replenish-contract-business")
//    @ApiOperation(value = "Replenish contract business")
//    public ResponseEntity<HttpStatus> replenishContractBusiness() {
//        companyOverviewService.replenishContractBusiness();
//        return new ResponseEntity<>(HttpStatus.ACCEPTED);
//    }

    /**
     * only use company migrate to crm
     * @return
     */
//    @GetMapping("/migrate-to-crm")
//    @ApiOperation(value = "Migrate company to crm")
//    public ResponseEntity<HttpStatus> migrateCompanyToCrm(@RequestHeader("Authorization") String authorizationHeader) {
//        companyOverviewService.migrateCompanyToCrm(authorizationHeader);
//        return new ResponseEntity<>(HttpStatus.ACCEPTED);
//    }

    /**
     * only use company migrate crm tags
     * @return
     */
    @GetMapping("/migrate-crm-tags")
    @ApiOperation(value = "Migrate crm tags")
    public ResponseEntity<HttpStatus> migrateCrmTags(@RequestHeader("Authorization") String authorizationHeader) {
        companyOverviewService.migrateCrmTags(authorizationHeader);
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }


//    @GetMapping("/migrateCompanyInfo")
//    public ResponseEntity<CompanyProspectVO> migrateCompanyInfo() {
//        companyOverviewService.migrateCompanyInfo();
//        return  ResponseEntity.status(HttpStatus.NO_CONTENT).build();
//    }
}
