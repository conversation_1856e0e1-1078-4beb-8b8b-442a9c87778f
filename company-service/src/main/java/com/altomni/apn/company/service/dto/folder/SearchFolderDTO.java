package com.altomni.apn.company.service.dto.folder;

import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "dto for searchFolder")
public class SearchFolderDTO implements Serializable {

    @ApiModelProperty(value = "The name for searchFolder.")
    @NotEmpty
    private String name;

    @ApiModelProperty(value = "The searchCriteria for searchFolder.")
    @NotEmpty
    private String searchCriteria;

    @ApiModelProperty(value = "The category for searchFolder.")
    @NotNull
    private CategoryFolderType searchCategory;

    @ApiModelProperty(value = "The id for custom folder.")
    private Long companyFolderId;

}
