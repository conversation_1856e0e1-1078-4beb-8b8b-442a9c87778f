package com.altomni.apn.company.domain.enumeration.report;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.altomni.apn.common.domain.enumeration.application.NodeType;

public class EventStageDateConverter implements Converter<NodeType>
{

    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }


    @Override
    public WriteCellData<String> convertToExcelData(NodeType eventStage, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(eventStage.getDescription());
    }
}
