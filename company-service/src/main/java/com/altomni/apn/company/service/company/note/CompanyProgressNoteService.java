package com.altomni.apn.company.service.company.note;

import com.altomni.apn.company.domain.company.note.CompanyProgressNote;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteBasicDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteSearchDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteSearchFilterDTO;
import com.altomni.apn.company.vo.note.CompanyProgressNoteVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.List;

public interface CompanyProgressNoteService {

    CompanyProgressNoteVO createProgressNote(CompanyProgressNoteDTO companyProgressNoteDTO);

    CompanyProgressNoteVO updateProgressNote(Long id, CompanyProgressNoteBasicDTO companyProgressNoteDTO);

    String searchCompanyProgressNote(CompanyProgressNoteSearchDTO companyProgressNoteSearchDTO, Pageable pageable, HttpHeaders headers) throws IOException;

    String searchCompanyProgressNoteByContactId(CompanyProgressNoteSearchFilterDTO companyProgressNoteSearchFilterDTO, Pageable pageable, HttpHeaders headers) throws IOException;
}
