package com.altomni.apn.company.service.dto.note;

import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * date:2023-04-14
 */
@Data
@ApiModel(description = "note search for client company")
public class CompanyClientNoteSearchDTO implements Serializable {

    @ApiModelProperty(value = "the id for company.")
    @NotNull
    public Long companyId;

    @ApiModelProperty(value = "the text for note.")
    public String note;

    @ApiModelProperty(value = "the timezone for note.")
    private String timezone;

    @ApiModelProperty(value = "the language for note.")
    private LanguageEnum language = LanguageEnum.EN;
}
