package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class NoPoachingCompanyTypeConverter extends AbstractAttributeConverter<NoPoachingCompanyType, Integer> {
    public NoPoachingCompanyTypeConverter() {
        super(NoPoachingCompanyType::toDbValue, NoPoachingCompanyType::fromDbValue);
    }
}
