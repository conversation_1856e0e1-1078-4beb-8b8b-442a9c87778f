package com.altomni.apn.company.vo.company;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class AccountCompanyCoAmVO implements Serializable {

    private Long userId;

    private String firstName;

    private String lastName;

    private String userName;

    private String role;

    private Long country;

    private Long accountBusinessId;
}
