package com.altomni.apn.company.domain.enumeration.note;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CompanyNoteCategory enumeration.
 */
public enum CompanyNoteCategory implements ConvertedEnum<Integer> {
    DEFAULT(10),
    NORMAL(20);
    private final int dbValue;

    CompanyNoteCategory(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<CompanyNoteCategory, Integer> resolver =
        new ReverseEnumResolver<>(CompanyNoteCategory.class, CompanyNoteCategory::toDbValue);

    public static CompanyNoteCategory fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
