package com.altomni.apn.company.vo.salesLead;

import com.altomni.apn.company.vo.contact.CompanyContactVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-19
*/
@AllArgsConstructor
@ApiModel(description = "Vo for salesLead admin")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class SalesLeadAdministratorVO extends CompanyContactVO implements Serializable {

    @ApiModelProperty(value = "the contribution for salesLead admin.")
    private Integer contribution;

    public SalesLeadAdministratorVO(Long id, String name) {
        super(id, name);
    }

    public SalesLeadAdministratorVO setContribution(Integer contribution) {
        this.contribution = contribution;
        return this;
    }
}