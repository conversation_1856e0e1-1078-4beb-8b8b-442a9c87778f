package com.altomni.apn.company.web.rest.company.note;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.service.company.note.CompanyProgressNoteService;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteBasicDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteSearchDTO;
import com.altomni.apn.company.service.dto.note.CompanyProgressNoteSearchFilterDTO;
import com.altomni.apn.company.vo.note.CompanyProgressNoteVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing ProgressNote.
 */
@Api(tags = {"APN-Company-ProgressNote"})
@Slf4j
@RestController
@RequestMapping("/api/v3/company")
public class CompanyProgressNoteResource {

    @Resource
    private CompanyProgressNoteService companyProgressNoteService;

    /**
     * {@code POST  /progressNote} : Create a new company progress note.
     *
     * @param companyProgressNoteDTO the companyProgressNoteDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new companyProgressNoteDTO, or with status {@code 400 (Bad Request)} if the company progress note has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("/progress-notes")
    public ResponseEntity<CompanyProgressNoteVO> createProgressNote(@RequestBody @Valid CompanyProgressNoteDTO companyProgressNoteDTO) {
        log.info("[APN: Company Progress Note @{}] REST request to create progress company note : {}", SecurityUtils.getUserId(), companyProgressNoteDTO);
        CompanyProgressNoteVO companyProgressNoteVO = companyProgressNoteService.createProgressNote(companyProgressNoteDTO);
        return new ResponseEntity<>(companyProgressNoteVO, HttpStatus.CREATED);
    }


    @PutMapping("/progress-notes/{id}")
    public ResponseEntity<CompanyProgressNoteVO> updateProgressNote(@PathVariable("id") Long id, @RequestBody @Valid CompanyProgressNoteBasicDTO companyProgressNoteDTO) {
        log.info("[APN: Company Progress Note @{}] REST request to update progress company note : {}", SecurityUtils.getUserId(), companyProgressNoteDTO);
        CompanyProgressNoteVO companyProgressNoteVO = companyProgressNoteService.updateProgressNote(id, companyProgressNoteDTO);
        return new ResponseEntity<>(companyProgressNoteVO, HttpStatus.CREATED);
    }

    /**
     * {@code Post  /progress-notes/search : get the company progressNote by company Id.
     *
     * @param companyId the company Id.

     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of contracts in body.
     */

    @PostMapping("/progress-notes/search")
    public ResponseEntity<String> searchCompanyProgressNote(@RequestBody @Valid CompanyProgressNoteSearchDTO companyProgressNoteSearchDTO, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[APN: Company Progress Note @{}] REST request to search Company Progress Note list: {}", SecurityUtils.getUserId(), companyProgressNoteSearchDTO);
//        Page<CompanyProgressNote> companyProgressNotePage = companyProgressNoteService.searchCompanyProgressNote(companyProgressNoteSearchDTO, pageable);
//        List<CompanyProgressNoteVO> result = companyProgressNoteService.toVo(companyProgressNotePage.getContent());
//        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(companyProgressNotePage, "/company");
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        String result = companyProgressNoteService.searchCompanyProgressNote(companyProgressNoteSearchDTO, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(result);
    }

    @PostMapping("/progress-notes/search-by-contact")
    public ResponseEntity<String> searchCompanyProgressNoteByContactId(@RequestBody @Valid CompanyProgressNoteSearchFilterDTO companyProgressNoteSearchFilterDTO, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        String result = companyProgressNoteService.searchCompanyProgressNoteByContactId(companyProgressNoteSearchFilterDTO, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(result);
    }


}
