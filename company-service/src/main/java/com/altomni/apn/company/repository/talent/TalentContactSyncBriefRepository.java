package com.altomni.apn.company.repository.talent;

import com.altomni.apn.common.domain.enumeration.TalentContactStatus;
import com.altomni.apn.company.domain.talent.TalentContactSyncBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface TalentContactSyncBriefRepository extends JpaRepository<TalentContactSyncBrief, Long> {

    List<TalentContactSyncBrief> findAllByTalentIdInAndStatusOrderBySortAsc(List<Long> talentIds, TalentContactStatus status);

}
