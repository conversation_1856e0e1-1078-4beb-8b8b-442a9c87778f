package com.altomni.apn.company.service.company.overview;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.dto.company.BriefCompanyDTO;
import com.altomni.apn.common.dto.search.GlobalSearchConditionDTO;
import com.altomni.apn.company.domain.company.CompanyMigrate;
import com.altomni.apn.company.service.dto.overview.CompanySearchConditionDTO;
import com.altomni.apn.company.vo.company.*;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.List;

public interface CompanyOverviewService {

//    List<String> querySearchHistory();

//    void testSearch(CompanyClientNoteSearchDTO companyClientNoteSearchDTO);

    CompanyListVO searchCompany(CompanySearchConditionDTO searchDto, Pageable pageable, HttpHeaders headers, boolean isForGlobalSearch) throws Throwable;

    List<Object> searchCompanyForApnGlobalSearch(GlobalSearchConditionDTO globalSearchDto, HttpHeaders headers) throws Throwable;

//    CompanyProspectVO createProspectCompany(CompanyProspectDTO companyProspectDTO);
//
//    CompanyClientVO createClientCompany(CompanyClientDTO companyClientDTO);

//    CompanyProspectVO queryProspectCompanyDetail(Long companyId);
//
//    CompanyClientVO queryClientCompanyDetail(Long companyId);

    List<CompanyMigrate> saveCompany(JSONObject jsonObject);

    AccountCompanyVO queryClientCompany(Long id, HttpHeaders headers);

    List<AccountCompanyCoAmVO> queryClientCompanyCoAm(Long id);

    AccountCompanyCoAmFteVO queryClientCompanyAndSalesLeadFte(Long companyId, Long jobId);

//    CompanyClientVO updateCompanyBasicInfo(Long companyId, CompanyBasicDTO companyBasicDTO);
//
//    CompanyProspectVO updateCompanySalesLeadInfo(Long companyId, List<SalesLeadUpdateDTO> salesLeadUpdateDTOList);
//
//    CompanyClientVO updateCompany(Long companyId, CompanyUpdateDTO companyUpdateDTO);
//
//    CompanyClientVO upgradeCompany(Long companyId, CompanyUpgradeDTO companyUpgradeDTO);

    List<CompanyJobVO> queryOpenJobsByCompanyId(Long companyId, Integer limit);

//    List<AccountCompanyVO> querySalesLeadByCompanyId(List<Long> companyIds);

    List<AccountCompanyVO> searchNoContractCompany(CompanySearchConditionDTO searchDto, Pageable pageable, HttpHeaders headers);

    BriefCompanyDTO createEmployerTenantCompany(BriefCompanyDTO companyClientDTO) throws IOException;

    void replenishContractBusiness();

    void migrateCompanyToCrm(String authorizationHeader);

    void migrateCrmTags(String authorizationHeader);

//    void migrateCompanyInfo();

    List<CompanyCalendarVO> searchCompanyCalendar(List<Long> companyIdList);
}
