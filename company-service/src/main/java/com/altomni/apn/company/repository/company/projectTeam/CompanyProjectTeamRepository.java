package com.altomni.apn.company.repository.company.projectTeam;

import com.altomni.apn.company.domain.company.projectTeam.CompanyProjectTeam;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


@Repository
public interface CompanyProjectTeamRepository extends JpaRepository<CompanyProjectTeam, Long>, JpaSpecificationExecutor<CompanyProjectTeam> {

    List<CompanyProjectTeam> findAllByTenantId(Long tenantId);

    Optional<CompanyProjectTeam> findFirstByNameAndCompanyId(String name, Long companyId);

    Integer countByCompanyIdAndName(Long companyId, String name);

    Integer countByCompanyIdAndNameAndIdNot(Long companyId, String name, Long id);

    Page<CompanyProjectTeam> findAllByCompanyId(Long companyId, Pageable pageable);

    Page<CompanyProjectTeam> findAllByCompanyIdAndNameLike(Long companyId, String name, Pageable pageable);

}
