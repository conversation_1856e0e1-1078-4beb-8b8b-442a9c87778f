package com.altomni.apn.company.web.rest.skipsubmit;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompany;
import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;
import com.altomni.apn.company.service.skipsubmit.SkipSubmitToAmCompanyUserService;
import io.micrometer.core.annotation.Timed;
import lombok.AllArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing SkipSubmitToAmCompany.
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/api/v3")
public class SkipSubmitToAmCompanyResource {

    private final SkipSubmitToAmCompanyUserService skipSubmitToAmCompanyUserService;

    @GetMapping("/skip-submit-to-am-companies")
    public List<SkipSubmitToAmCompany> getAllSkipSubmitToAmCompanies() {
        return skipSubmitToAmCompanyUserService.findAll();
    }

    @GetMapping("/skip-submit-to-am-companies/{companyId}/all-users")
    public List<SkipSubmitToAmCompanyUser> getAllSkipSubmitToAmCompanyUsers(@PathVariable Long companyId) {
        return skipSubmitToAmCompanyUserService.findAllByCompanyId(companyId);
    }

    @PostMapping("/skip-submit-to-am-companies/{companyId}/replace-users")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<SkipSubmitToAmCompanyUser>> replaceUsers(@PathVariable Long companyId, @RequestBody List<Long> userIds) {
        return ResponseEntity.ok(skipSubmitToAmCompanyUserService.replaceUsers(companyId, userIds));
    }

}
