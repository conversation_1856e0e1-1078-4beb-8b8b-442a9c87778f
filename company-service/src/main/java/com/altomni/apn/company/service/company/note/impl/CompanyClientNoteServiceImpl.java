package com.altomni.apn.company.service.company.note.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.search.ConditionParam;
import com.altomni.apn.common.dto.search.Relation;
import com.altomni.apn.common.dto.search.SearchParam;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ElasticSearchUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.CompanyAdditionalInfo;
import com.altomni.apn.company.domain.company.note.*;
import com.altomni.apn.company.domain.enumeration.note.CompanyNoteCategory;
import com.altomni.apn.company.domain.enumeration.note.CompanyNoteType;
import com.altomni.apn.company.domain.vm.EntityNameVM;
import com.altomni.apn.company.repository.company.CompanyAddtionalInfoRepository;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.company.note.CompanyClientNoteContactRelationRepository;
import com.altomni.apn.company.repository.company.note.CompanyClientNoteRepository;
import com.altomni.apn.company.repository.business.SalesLeadClientContactRepository;
import com.altomni.apn.company.repository.talent.TalentServiceRepository;
import com.altomni.apn.company.repository.user.UserServiceRepository;
import com.altomni.apn.company.service.company.note.CompanyClientNoteService;
import com.altomni.apn.company.service.company.overview.CompanyOverviewService;
import com.altomni.apn.company.service.dto.es.NoteSearchDTO;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteDTO;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteSearchDTO;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteUpdateDTO;
import com.altomni.apn.company.service.dto.overview.CompanySearchConditionDTO;
import com.altomni.apn.company.service.elastic.EsCompanyDataService;
import com.altomni.apn.company.vo.company.CompanyListVO;
import com.altomni.apn.company.vo.note.CompanyClientNoteDetailVO;
import com.altomni.apn.company.vo.note.CompanyClientNoteVO;
import com.altomni.apn.company.web.rest.vm.company.note.CompanyNoteToEsVM;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CompanyClientNoteServiceImpl implements CompanyClientNoteService {

    @Resource
    private CompanyClientNoteRepository companyClientNoteRepository;

    @Resource
    private SalesLeadClientContactRepository salesLeadClientContactRepository;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private UserServiceRepository userServiceRepository;

    @Resource
    private TalentServiceRepository talentServiceRepository;

    @Resource
    private CompanyAddtionalInfoRepository companyAddtionalInfoRepository;

    @Resource
    private HttpService httpService;

    @Resource
    private EsCompanyDataService esCompanyDataService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    @Resource
    private CompanyClientNoteContactRelationRepository companyClientNoteContactRelationRepository;

    @Autowired
    @Lazy
    private CompanyOverviewService companyOverviewService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompanyClientNoteVO createClientNote(CompanyClientNoteDTO companyClientNoteDTO) {
        Company existCompany = companyRepository.findById(companyClientNoteDTO.getCompanyId()).orElseThrow(()-> new NotFoundException("company does not exist."));
        if (!existCompany.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.NOTE_CREATECLIENTNOTE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        List<SalesLeadClientContact> clientContactList = salesLeadClientContactRepository.findAllById(companyClientNoteDTO.getClientContactIds());
        if (clientContactList.stream().anyMatch(o -> !o.getCompanyId().equals(companyClientNoteDTO.getCompanyId()))) {
            throw new NotFoundException("clientContact does not exist.");
        }
        if (clientContactList.stream().anyMatch(o -> o.isActive() == Boolean.FALSE)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.NOTE_CREATECLIENTNOTE_INACTIVE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        CompanyClientNote companyClientNote = companyClientNoteRepository.save(CompanyClientNote.fromCompanyClientNoteDTO(companyClientNoteDTO));
        saveNoteContact(companyClientNote, companyClientNoteDTO.getClientContactIds());
        return toVo(companyClientNote, clientContactList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CompanyClientNoteVO updateClientNote(Long id, CompanyClientNoteUpdateDTO companyClientNoteUpdateDTO) {
        CompanyClientNote existCompanyClientNote = companyClientNoteRepository.findById(id).orElseThrow(()-> new NotFoundException("note does not exist."));
        if (existCompanyClientNote.getDeleted()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.NOTE_UPDATECLIENTNOTE_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        Company existCompany = companyRepository.findById(existCompanyClientNote.getCompanyId()).orElseThrow(()-> new NotFoundException("company does not exist."));
        if (!existCompany.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.NOTE_CREATECLIENTNOTE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        List<SalesLeadClientContact> clientContactList = salesLeadClientContactRepository.findAllById(companyClientNoteUpdateDTO.getClientContactIds());
        if (clientContactList.stream().anyMatch(o -> !o.getCompanyId().equals(existCompanyClientNote.getCompanyId()))) {
            throw new NotFoundException("clientContact does not exist.");
        }
        if (clientContactList.stream().anyMatch(o -> o.isActive() == Boolean.FALSE)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.NOTE_CREATECLIENTNOTE_INACTIVE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        ServiceUtils.myCopyProperties(companyClientNoteUpdateDTO, existCompanyClientNote);
        existCompanyClientNote.setLastModifiedDate(Instant.now());
        CompanyClientNote companyClientNote = companyClientNoteRepository.save(existCompanyClientNote);
        saveNoteContact(companyClientNote, companyClientNoteUpdateDTO.getClientContactIds());
        return toVo(companyClientNote, clientContactList);
    }

    @Override
    public String searchCompanyClientNote(CompanyClientNoteSearchDTO companyClientNoteSearchDTO, Pageable pageable, HttpHeaders headers) throws IOException {
        List<CompanyClientNoteVO> result = new ArrayList<>();

        NoteSearchDTO noteSearchDTO = new NoteSearchDTO();
        noteSearchDTO.setTenantId(SecurityUtils.getTenantId());
        noteSearchDTO.setModule(CompanyNoteType.CLIENT_NOTE);
        noteSearchDTO.setTimeZone(companyClientNoteSearchDTO.getTimezone());
        noteSearchDTO.setLanguage(companyClientNoteSearchDTO.getLanguage().toDbValue());
        noteSearchDTO.setCompanyIds(Arrays.asList(companyClientNoteSearchDTO.getCompanyId()));
        noteSearchDTO.setNote(companyClientNoteSearchDTO.getNote());

        HttpResponse response = esCompanyDataService.searchCompanyNote(noteSearchDTO, pageable);
        if (response == null || HttpStatus.OK.value() != response.getCode()) {
            headers.set("Pagination-Count", "0");
            return "[]";
        } else {
            headers.set("Pagination-Count", ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders()) + "");
            return response.getBody();
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDefaultCompanyClientNote(Long companyId) {
        CompanyClientNote companyClientNote = companyClientNoteRepository.findFirstByCompanyIdAndAndCategory(companyId, CompanyNoteCategory.DEFAULT);
        if (companyClientNote != null) {
            return;
        }

        SalesLeadClientContact salesLeadClientContact = salesLeadClientContactRepository.findFirstByCompanyIdOrderByActiveDescCreatedDateDesc(companyId);

        companyClientNote = new CompanyClientNote(companyId, applicationProperties.getDefaultClientNote());
        companyClientNote.setContactDate(Instant.now());
        companyClientNote.setCategory(CompanyNoteCategory.DEFAULT);
        companyClientNoteRepository.save(companyClientNote);
        if (salesLeadClientContact != null) {
            saveNoteContact(companyClientNote, Arrays.asList(salesLeadClientContact.getId()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDefaultCompanyClientNote(List<Long> companyIds) {
        if (CollUtil.isEmpty(companyIds)) {
            return;
        }
        List<CompanyClientNote> clientNoteList = companyClientNoteRepository.findAllByCompanyIdInAndCategory(companyIds, CompanyNoteCategory.DEFAULT);
        Set<Long> existedCompanyIds = clientNoteList.stream().map(CompanyClientNote::getCompanyId).collect(Collectors.toSet());

        List<Long> updateCompanyIds = companyIds.stream().filter(o -> !existedCompanyIds.contains(o)).toList();
        List<SalesLeadClientContact> salesLeadClientContactList = salesLeadClientContactRepository.findTop1ByCompanyIdInOrderByActiveDescCreatedDateDesc(updateCompanyIds);
        Map<Long, Long> contactMap = salesLeadClientContactList.stream().collect(Collectors.toMap(SalesLeadClientContact::getCompanyId, SalesLeadClientContact::getId));

        List<CompanyClientNote> addClientNoteList = updateCompanyIds.stream().map(o -> {
            CompanyClientNote companyClientNote = new CompanyClientNote(o, applicationProperties.getDefaultClientNote());
            companyClientNote.setContactDate(Instant.now());
            companyClientNote.setCategory(CompanyNoteCategory.DEFAULT);
            return companyClientNote;
        }).toList();

        addClientNoteList = companyClientNoteRepository.saveAll(addClientNoteList);
        List<CompanyClientNoteContactRelation> companyClientNoteContactRelationList = addClientNoteList.stream().filter(item -> contactMap.containsKey(item.getCompanyId())).map(o -> new CompanyClientNoteContactRelation(o.getId(), contactMap.get(o.getCompanyId()))).toList();
        companyClientNoteContactRelationRepository.saveAll(companyClientNoteContactRelationList);
    }

    @Override
    public void deleteClientNote(Long id) {
        CompanyClientNote existCompanyClientNote = companyClientNoteRepository.findById(id).orElseThrow(()-> new NotFoundException("company does not exist."));

        Company existCompany = companyRepository.findById(existCompanyClientNote.getCompanyId()).orElseThrow(()-> new NotFoundException("company does not exist."));
        if (!existCompany.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.NOTE_CREATECLIENTNOTE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        existCompanyClientNote.setDeleted(Boolean.TRUE);
        companyClientNoteRepository.save(existCompanyClientNote);
    }

    private CompanyClientNoteVO toVo(CompanyClientNote companyClientNote, List<SalesLeadClientContact> clientContact) {
        CompanyClientNoteVO companyClientNoteVO = new CompanyClientNoteVO();
        ServiceUtils.myCopyProperties(companyClientNote, companyClientNoteVO);
        companyClientNoteVO.setClientContactIds(clientContact.stream().map(SalesLeadClientContact::getId).toList());
        EntityNameVM creatorName = userServiceRepository.findUserNameById(companyClientNote.getPermissionUserId());
        companyClientNoteVO.setCreator(CommonUtils.formatFullName(creatorName.getFirstName(), creatorName.getLastName()));
        return companyClientNoteVO;
    }

    private void saveNoteContact(CompanyClientNote note, List<Long> contactIdList) {
        Set<Long> contactIds = CollUtil.isEmpty(contactIdList) ? new HashSet<>() : new HashSet<>(contactIdList);

        List<CompanyClientNoteContactRelation> existRelationList = companyClientNoteContactRelationRepository.findAllByNoteId(note.getId());

        List<CompanyClientNoteContactRelation> deleteRelationList = existRelationList.stream().filter(o -> !contactIds.contains(o.getClientContactId())).toList();
        existRelationList.removeAll(deleteRelationList);

        Set<Long> existRelationContactIds = existRelationList.stream().map(CompanyClientNoteContactRelation::getClientContactId).collect(Collectors.toSet());
        contactIds.forEach(o -> {
            if (!existRelationContactIds.contains(o)) {
                existRelationList.add(new CompanyClientNoteContactRelation(note.getId(), o));
            }
        });

        companyClientNoteContactRelationRepository.deleteAllByIdInBatch(deleteRelationList.stream().map(CompanyClientNoteContactRelation::getId).collect(Collectors.toList()));
        companyClientNoteContactRelationRepository.saveAllAndFlush(existRelationList);
    }

    @Override
    public CompanyClientNoteDetailVO getClientNote(Long id) {
        CompanyClientNoteDetailVO vo = new CompanyClientNoteDetailVO();
        Optional<CompanyClientNote> optionalCompanyClientNote = companyClientNoteRepository.findById(id);
        if (!optionalCompanyClientNote.isPresent()) {
            return vo;
        }
        CompanyClientNote note = optionalCompanyClientNote.get();
        List<CompanyClientNoteContactRelation> relations = companyClientNoteContactRelationRepository.findAllByNoteId(id);
        List<Long> contactIdList = relations.stream().map(CompanyClientNoteContactRelation::getClientContactId).collect(Collectors.toList());
        vo.setClientContactIds(contactIdList);
        vo.setId(note.getId());
        vo.setCompanyId(note.getCompanyId());
        vo.setNote(note.getNote());
        vo.setCreatedBy(note.getCreatedBy());
        vo.setContactDate(note.getContactDate());
        vo.setCreatedDate(note.getCreatedDate());
        vo.setLastModifiedBy(note.getLastModifiedBy());
        vo.setLastModifiedDate(note.getLastModifiedDate());

        CompanySearchConditionDTO searchDto = new CompanySearchConditionDTO();
        searchDto.setModule(ModuleType.COMPANY_POOL);
        searchDto.setLanguage(LanguageEnum.ZH);
        searchDto.setTimezone(SecurityUtils.getUserTimeZone());

        List<SearchParam> search = new ArrayList<>();
        SearchParam sp = new SearchParam();
        sp.setRelation(Relation.AND);

        List<ConditionParam> condition = new ArrayList<>();
        ConditionParam cp = new ConditionParam();
        cp.setKey("active");
        JSONObject activeJs = new JSONObject();
        activeJs.put("data",true);
        cp.setValue(activeJs);
        condition.add(cp);

        cp = new ConditionParam();
        cp.setKey("id");
        activeJs = new JSONObject();
        activeJs.put("data",note.getCompanyId());
        cp.setValue(activeJs);
        condition.add(cp);
        sp.setCondition(condition);
        search.add(sp);
        searchDto.setSearch(search);

        List<SearchParam> filter = new ArrayList<>();
        SearchParam filterSP = new SearchParam();
        filterSP.setRelation(Relation.AND);
        List<ConditionParam> filterCondition = new ArrayList<>();
        ConditionParam filterCp = new ConditionParam();
        filterCp.setKey("hasServiceTypes");
        activeJs = new JSONObject();
        activeJs.put("data",true);
        filterCp.setValue(activeJs);
        filterCondition.add(filterCp);
        filterSP.setCondition(filterCondition);
        filter.add(filterSP);
        searchDto.setFilter(filter);

        Pageable pageable = PageRequest.of(1,100);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        try {
            CompanyListVO result = companyOverviewService.searchCompany(searchDto, pageable, headers, false);
            if(!result.getData().isEmpty()){
                JSONObject value = JSONUtil.parseObj(result.getData().get(0)) ;
                JSONObject source = value.getJSONObject("_source");
                vo.setCompanyName(source.getStr("companyName"));
                vo.setFullBusinessName(source.getStr("businessName"));
                vo.setDisplayNameInEn(source.getStr("displayNameInEn"));
            }
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }

        return vo;
    }
}
