package com.altomni.apn.company.service.folder.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.company.config.env.ApplicationProperties;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.enumeration.folder.FolderPermission;
import com.altomni.apn.company.domain.enumeration.folder.FolderType;
import com.altomni.apn.company.domain.folder.*;
import com.altomni.apn.company.domain.vm.CompanyCustomFolderVM;
import com.altomni.apn.company.domain.vm.EntityCountVM;
import com.altomni.apn.company.domain.vm.EntityNameVM;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.company.folder.*;
import com.altomni.apn.company.repository.business.SalesLeadServiceRepository;
import com.altomni.apn.company.repository.user.UserServiceRepository;
import com.altomni.apn.company.service.dto.folder.*;
import com.altomni.apn.company.service.elastic.EsCompanyDataService;
import com.altomni.apn.company.service.elastic.EsFillerCompanyService;
import com.altomni.apn.company.service.folder.FolderService;
import com.altomni.apn.company.service.user.UserService;
import com.altomni.apn.company.vo.folder.*;
import com.altomni.apn.user.service.dto.permission.PermissionTeamTreeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.io.IOException;
import java.lang.reflect.Method;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FolderServiceImpl implements FolderService {

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private CompanyRepository companyRepository;

    @Resource
    private CompanySearchFolderRepository searchFolderRepository;

    @Resource
    private CompanyCustomFolderRepository customFolderRepository;

    @Resource
    private CompanyCustomFolderConnectClientRepository companyCustomFolderConnectClientRepository;

    @Resource
    private CompanyCustomFolderSharedTeamRepository companyCustomFolderSharedTeamRepository;

    @Resource
    private CompanyCustomFolderSharedUserRepository companyCustomFolderSharedUserRepository;

    @Resource
    private UserService userService;

    @Resource
    private EsFillerCompanyService esFillerCompanyService;

    @Resource
    private SalesLeadServiceRepository salesLeadServiceRepository;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private EsCompanyDataService esCompanyDataService;

    @Resource
    private UserServiceRepository userServiceRepository;

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;


    private final String MY_PROSPECT = "myProspect";

    private final String MY_CLIENT = "myClient";

    private final String ALL_PROSPECT = "allProspect";

    private final String ALL_CLIENT = "allClient";

    @Override
    public List<CategoryFolderCountVO> countCategoryFolders() throws IOException {
        HttpResponse response = esCompanyDataService.searchCompanyCount();
        List<CategoryFolderCountVO> result = new ArrayList<>();
        if (response.getBody() != null) {
            JSONObject jsonObject = JSONUtil.parseObj(response.getBody());
            if (jsonObject.containsKey(MY_PROSPECT)) {
                result.add(new CategoryFolderCountVO(CategoryFolderType.PROSPECT_MY_COMPANY, Long.parseLong(jsonObject.get(MY_PROSPECT).toString())));
            }
            if (jsonObject.containsKey(MY_CLIENT)) {
                result.add(new CategoryFolderCountVO(CategoryFolderType.CLIENT_MY_COMPANY, Long.parseLong(jsonObject.get(MY_CLIENT).toString())));
            }
            if (jsonObject.containsKey(ALL_PROSPECT)) {
                result.add(new CategoryFolderCountVO(CategoryFolderType.PROSPECT_ALL_COMPANY, Long.parseLong(jsonObject.get(ALL_PROSPECT).toString())));
            }
            if (jsonObject.containsKey(ALL_CLIENT)) {
                result.add(new CategoryFolderCountVO(CategoryFolderType.CLIENT_ALL_COMPANY, Long.parseLong(jsonObject.get(ALL_CLIENT).toString())));
            }

        }
        return result;
    }

//    @Override
//    public List<String> querySearchHistory() {
//        return commonRedisService.getListData(String.format(DATA_KEY_FOLDER_COMPANY_SEARCH_HISTORY, SecurityUtils.getUserId()), 0, applicationProperties.getSearchHistoryMaxSize());
//    }

    @Override
    public SearchFolderVO createSearchFolder(SearchFolderDTO searchFolderDTO) {
        checkSearchFolderDuplicateName(searchFolderDTO.getName());
        if (ObjectUtil.isNotEmpty(searchFolderDTO.getCompanyFolderId())) {
            checkCustomFolderPermission(searchFolderDTO.getCompanyFolderId());
        }
        CompanySearchFolder companySearchFolder = CompanySearchFolder.fromSearchFolderDTO(searchFolderDTO);
        return SearchFolderVO.fromCompanySearchFolder(searchFolderRepository.save(companySearchFolder));
    }

    @Override
    public SearchFolderVO querySearchFolder(Long id) {
        CompanySearchFolder companySearchFolder = searchFolderRepository.findById(id).orElseThrow(() -> new NotFoundException("searchFolder does not exist"));
        if (!companySearchFolder.getPermissionUserId().equals(SecurityUtils.getUserId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_QUERYFOLDER_NOEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return SearchFolderVO.fromCompanySearchFolder(companySearchFolder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSearchFolder(FolderIdDTO folderIdDTO) {
        List<Long> foldIdList = folderIdDTO.getFolderIds().stream().distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(foldIdList)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_DELETEFOLDER_PARAMISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        List<CompanySearchFolder> companySearchFolderList = searchFolderRepository.findAllById(foldIdList);
        if (foldIdList.size() != companySearchFolderList.size() || companySearchFolderList.stream().anyMatch(o -> !o.getPermissionUserId().equals(SecurityUtils.getUserId()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_DELETESEARCHFOLDER_DATAISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        searchFolderRepository.deleteAllById(foldIdList);
    }

    @Override
    public Page<CompanySearchFolder> searchSearchFolder(FolderSearchDTO folderSearchDTO, Pageable pageable) {
        Map<Integer, Object> paramsMap = new HashMap<>(16);
        StringBuffer countSql = new StringBuffer();
        StringBuffer dataSql = new StringBuffer();
        setSearchFolderSql(folderSearchDTO, pageable, paramsMap, countSql, dataSql);
        Long total = searchCount(countSql.toString(), paramsMap);
        List<CompanySearchFolder> companySearchFolderList = searchData(dataSql.toString(), CompanySearchFolder.class, paramsMap);
//        saveSearchHistory(folderSearchDTO.getGeneralTexts());
        return new PageImpl<>(companySearchFolderList, pageable, total);
    }

    @Override
    public List<SearchFolderVO> toVo(List<CompanySearchFolder> companySearchFolderList) {
        if (CollUtil.isEmpty(companySearchFolderList)) {
            return new ArrayList<>();
        }

        List<SearchFolderVO> result = SearchFolderVO.toVo(companySearchFolderList);
        List<Long> folderIds = result.stream().peek(item -> item.setParamFolderActive(ObjectUtil.isEmpty(item.getCompanyFolderId()) ? Boolean.TRUE : Boolean.FALSE)).map(SearchFolderVO::getCompanyFolderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Set<Long> activeParamFolderIds = new HashSet<>();
        List<CompanyCustomFolder> companyCustomFolderList = customFolderRepository.findAllById(folderIds);
        Map<Long, CompanyCustomFolder> companyCustomFolderMap = companyCustomFolderList.stream().collect(Collectors.toMap(CompanyCustomFolder::getId, o -> o));
        List<Long> companyCustomFolderIdList = companyCustomFolderList.stream().map(CompanyCustomFolder::getId).collect(Collectors.toList());
        List<EntityNameVM> creatorNameList = userServiceRepository.findUserNameByIds(companyCustomFolderList.stream().map(CompanyCustomFolder::getPermissionUserId).distinct().collect(Collectors.toList()));
        Map<Long, EntityNameVM> creatorNameMap = creatorNameList.stream().collect(Collectors.toMap(EntityNameVM::getId, o -> o));

        result.forEach(item -> {
            if (item.getCompanyFolderId() != null && !companyCustomFolderIdList.contains(item.getCompanyFolderId())) {
                item.setParamFolderPermission(FolderPermission.FOLDER_DELETE);
            }
        });

        activeParamFolderIds.addAll(companyCustomFolderList.stream().filter(o -> o.getPermissionUserId().equals(SecurityUtils.getUserId())).map(CompanyCustomFolder::getId).collect(Collectors.toSet()));

        if (companyCustomFolderList.size() != activeParamFolderIds.size()) {
            companyCustomFolderIdList.removeAll(activeParamFolderIds);
            List<CompanyCustomFolderSharedUser> companyCustomFolderSharedUserList = companyCustomFolderSharedUserRepository.findAllByFolderIdInAndUserId(companyCustomFolderIdList, SecurityUtils.getUserId());
            activeParamFolderIds.addAll(companyCustomFolderSharedUserList.stream().map(CompanyCustomFolderSharedUser::getFolderId).collect(Collectors.toSet()));
        }

        if (companyCustomFolderList.size() != activeParamFolderIds.size()) {
            companyCustomFolderIdList.removeAll(activeParamFolderIds);
            List<CompanyCustomFolderSharedTeam> companyCustomFolderSharedTeamList = companyCustomFolderSharedTeamRepository.findAllIdByFolderIdInAndUserId(companyCustomFolderIdList, SecurityUtils.getUserId());
            activeParamFolderIds.addAll(companyCustomFolderSharedTeamList.stream().map(CompanyCustomFolderSharedTeam::getFolderId).collect(Collectors.toSet()));
        }

        result.forEach(o -> {
            if (o.getCompanyFolderId() != null) {
                o.setParamFolderActive(activeParamFolderIds.contains(o.getCompanyFolderId()));
                if (!o.getParamFolderActive() && o.getParamFolderPermission() == null) {
                    o.setParamFolderPermission(FolderPermission.NO_PERMISSION);
                    o.setParamFolderCreatorId(companyCustomFolderMap.containsKey(o.getCompanyFolderId()) ? companyCustomFolderMap.get(o.getCompanyFolderId()).getPermissionUserId() : null);
                    o.setParamFolderCreatorName(creatorNameMap.containsKey(o.getParamFolderCreatorId()) ? CommonUtils.formatFullName(creatorNameMap.get(o.getParamFolderCreatorId()).getFirstName(), creatorNameMap.get(o.getParamFolderCreatorId()).getLastName()) : null);
                }
            }
        });

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomFolderVO createCustomFolder(CustomFolderDTO customFolderDTO) {
        if (customFolderDTO.getCategory() == null || (!customFolderDTO.getCategory().equals(CategoryFolderType.PROSPECT_ALL_COMPANY) && !customFolderDTO.getCategory().equals(CategoryFolderType.CLIENT_ALL_COMPANY))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_CREATECUSTOMFOLDER_CATEGORYERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        checkCustomFolderDuplicateName(customFolderDTO.getName());

        CompanyCustomFolder customFolder = CompanyCustomFolder.fromCustomFolderDTO(customFolderDTO);
        CompanyCustomFolder createdCompanyCustomFolder = customFolderRepository.save(customFolder);
        CustomFolderVO customFolderVO = CustomFolderVO.fromCompanyCustomFolder(createdCompanyCustomFolder);

        if (customFolderDTO.getCompanyIds() != null && CollUtil.isNotEmpty(customFolderDTO.getCompanyIds())) {
            List<Company> companyList = checkCompanyPermission(customFolderDTO.getCompanyIds());
            checkCompanyCategory(customFolderDTO.getCompanyIds(), customFolderDTO.getCategory());
            companyCustomFolderConnectClientRepository.saveAll(companyList.stream().map(o -> new CompanyCustomFolderConnectClient(createdCompanyCustomFolder.getId(), o.getId())).collect(Collectors.toList()));
        }

        addCustomFolderSharedUser(customFolderDTO, createdCompanyCustomFolder.getId(), customFolderVO);
        addCustomFolderSharedTeam(customFolderDTO, createdCompanyCustomFolder.getId(), customFolderVO);

        updateCompaniesFolder(customFolderDTO.getCompanyIds(), Arrays.asList(customFolderVO.getId()), null);
        return customFolderVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CustomFolderVO updateCustomFolder(Long folderId, CustomFolderDTO customFolderDTO) {
        checkCustomFolderDuplicateName(customFolderDTO.getName(), folderId);
        CompanyCustomFolder existCompanyCustomFolder = checkCustomFolderPermission(folderId, FolderPermission.EDIT);
        ServiceUtils.myCopyProperties(customFolderDTO, existCompanyCustomFolder, CompanyCustomFolder.updateSkipProperties);
        customFolderRepository.save(existCompanyCustomFolder);

        List<CompanyCustomFolderSharedUser> existSharedUserList = companyCustomFolderSharedUserRepository.findAllByFolderId(existCompanyCustomFolder.getId());
        List<CompanyCustomFolderSharedTeam> existSharedTeamList = companyCustomFolderSharedTeamRepository.findAllByFolderId(existCompanyCustomFolder.getId());

        List<CompanyCustomFolderSharedUser> deleteSharedUserList = customFolderDTO.getSharedUsers() == null ? existSharedUserList : existSharedUserList.stream().filter(o -> !customFolderDTO.getSharedUsers().stream().map(FolderSharedDTO::getId).collect(Collectors.toSet()).contains(o.getUserId())).collect(Collectors.toList());
        List<CompanyCustomFolderSharedTeam> deleteSharedTeamList = customFolderDTO.getSharedTeams() == null ? existSharedTeamList : existSharedTeamList.stream().filter(o -> !customFolderDTO.getSharedTeams().stream().map(FolderSharedDTO::getId).collect(Collectors.toSet()).contains(o.getTeamId())).collect(Collectors.toList());

        existSharedUserList.removeAll(deleteSharedUserList);
        existSharedTeamList.removeAll(deleteSharedTeamList);

        if (CollUtil.isNotEmpty(existSharedUserList)) {
            Map<Long, FolderSharedDTO> sharedUserMap = customFolderDTO.getSharedUsers().stream().collect(Collectors.toMap(FolderSharedDTO::getId, o -> o));
            existSharedUserList.forEach(item -> item.setPermission(sharedUserMap.get(item.getUserId()).getPermission()));
        }
        if (CollUtil.isNotEmpty(existSharedTeamList)) {
            Map<Long, FolderSharedDTO> sharedTeamMap = customFolderDTO.getSharedTeams().stream().collect(Collectors.toMap(FolderSharedDTO::getId, o -> o));
            existSharedTeamList.forEach(item -> item.setPermission(sharedTeamMap.get(item.getTeamId()).getPermission()));
        }

        Set<Long> existSharedUserIdSet = existSharedUserList.stream().map(CompanyCustomFolderSharedUser::getUserId).collect(Collectors.toSet());
        Set<Long> existSharedTeamIdSet = existSharedTeamList.stream().map(CompanyCustomFolderSharedTeam::getTeamId).collect(Collectors.toSet());

        if (customFolderDTO.getSharedUsers() != null && CollUtil.isNotEmpty(customFolderDTO.getSharedUsers())) {
            customFolderDTO.getSharedUsers().forEach(item -> {
                if (!existSharedUserIdSet.contains(item.getId())) {
                    existSharedUserList.add(new CompanyCustomFolderSharedUser(folderId, item.getId(), item.getPermission()));
                }
            });
        }

        if (customFolderDTO.getSharedTeams() != null && CollUtil.isNotEmpty(customFolderDTO.getSharedTeams())) {
            customFolderDTO.getSharedTeams().forEach(item -> {
                if (!existSharedTeamIdSet.contains(item.getId())) {
                    existSharedTeamList.add(new CompanyCustomFolderSharedTeam(folderId, item.getId(), item.getPermission()));
                }
            });
        }

        companyCustomFolderSharedUserRepository.deleteAllById(deleteSharedUserList.stream().map(CompanyCustomFolderSharedUser::getId).collect(Collectors.toList()));
        companyCustomFolderSharedTeamRepository.deleteAllById(deleteSharedTeamList.stream().map(CompanyCustomFolderSharedTeam::getId).collect(Collectors.toList()));

        companyCustomFolderSharedUserRepository.saveAll(existSharedUserList);
        companyCustomFolderSharedTeamRepository.saveAll(existSharedTeamList);

        return toVo(existCompanyCustomFolder);
    }

    @Override
    public CustomFolderVO queryCustomFolder(Long folderId) {
        CompanyCustomFolder existCompanyCustomFolder = checkCustomFolderPermission(folderId);
        return toVo(existCompanyCustomFolder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCustomFolder(FolderIdDTO folderIdDTO) {
        List<Long> foldIdList = folderIdDTO.getFolderIds().stream().distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(foldIdList)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_DELETEFOLDER_PARAMISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        List<CompanyCustomFolder> companyCustomFolderList = customFolderRepository.findAllById(foldIdList);
        if (foldIdList.size() != companyCustomFolderList.size() || companyCustomFolderList.stream().anyMatch(o -> !o.getPermissionUserId().equals(SecurityUtils.getUserId()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_DELETECUSTOMFOLDER_PARAMISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        List<CompanyCustomFolderConnectClient> companyCustomFolderConnectClientList = companyCustomFolderConnectClientRepository.findAllByFolderIdIn(foldIdList);

        customFolderRepository.deleteAllById(foldIdList);

        List<Long> existSharedUserIdList = companyCustomFolderSharedUserRepository.findAllIdByFolderIdIn(foldIdList);
        List<Long> existSharedTeamIdList = companyCustomFolderSharedTeamRepository.findAllIdByFolderIdIn(foldIdList);
        List<Long> existFolderCompanyIdList = companyCustomFolderConnectClientRepository.findAllIdByFolderIdIn(foldIdList);
        companyCustomFolderSharedUserRepository.deleteAllById(existSharedUserIdList);
        companyCustomFolderSharedTeamRepository.deleteAllById(existSharedTeamIdList);
        companyCustomFolderConnectClientRepository.deleteAllById(existFolderCompanyIdList);
        //batch update folder to es
        batchDeleteCompaniesFolder(companyCustomFolderConnectClientList.stream().collect(Collectors.groupingBy(CompanyCustomFolderConnectClient::getFolderId, Collectors.mapping(CompanyCustomFolderConnectClient::getCompanyId, Collectors.toList()))));
    }

    @Override
    public Page<CompanyCustomFolderVM> searchCustomFolder(CustomFolderSearchDTO customFolderSearchDTO, Pageable pageable) {

        log.info("userId:{}", SecurityUtils.getUserId());
        Map<Integer, Object> paramsMap = new HashMap<>(16);
        StringBuffer countSql = new StringBuffer();
        StringBuffer dataSql = new StringBuffer();
        setSearchCustomFolderSql(customFolderSearchDTO, pageable, paramsMap, countSql, dataSql);
        Long total = searchCount(countSql.toString(), paramsMap);
        List<CompanyCustomFolderVM> customFolderList = searchData(dataSql.toString(), CompanyCustomFolderVM.class, paramsMap);
//        saveSearchHistory(customFolderSearchDTO.getGeneralTexts());
        return new PageImpl<>(customFolderList, pageable, total);
    }

//    private void saveSearchHistory(List<String> generalTexts) {
//        if (generalTexts == null || CollUtil.isEmpty(generalTexts)) {
//            return;
//        }
//        try {
//            Long userId = SecurityUtils.getUserId();
//            CompletableFuture.supplyAsync(() -> {
//                generalTexts.forEach(o -> commonRedisService.lPushWithMaxSizeAndExpire(String.format(DATA_KEY_FOLDER_COMPANY_SEARCH_HISTORY, userId), o.toString(), applicationProperties.getSearchHistoryMaxSize(), applicationProperties.getSearchHistoryExpireTime()));
//                return 0;
//            });
//        } catch (Exception e) {
//            log.error("[APN Company] Fail to save folder keyword history by user {}: generalTexts: {}, error: {}", SecurityUtils.getUserId(), generalTexts, e);
//        }
//    }

    private void setSearchCustomFolderSql(CustomFolderSearchDTO customFolderSearchDTO, Pageable pageable, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (customFolderSearchDTO.getFolderType().equals(FolderType.CUSTOMIZED)) {
            countSql.append("SELECT COUNT(f.id) FROM company_custom_folder f WHERE f.puser_id = ").append(SecurityUtils.getUserId()).append(" AND f.category = ").append(customFolderSearchDTO.getCategory().toDbValue());
            dataSql.append("SELECT f.*, CASE WHEN CONCAT(u.first_name, ' ', u.last_name) REGEXP '[A-Za-z]' THEN CONCAT(u.first_name, ' ', u.last_name)\n" +
                    "       ELSE CONCAT(u.last_name, u.first_name)\n" +
                    "  END AS creator FROM company_custom_folder f INNER JOIN `user` u ON u.id = f.puser_id WHERE f.puser_id = ").append(SecurityUtils.getUserId()).append(" AND f.category = ").append(customFolderSearchDTO.getCategory().toDbValue());
        } else if (customFolderSearchDTO.getFolderType().equals(FolderType.SHARED)) {
            countSql.append("SELECT COUNT(f.id) \n" +
                    "	FROM company_custom_folder f \n" +
                    "	WHERE f.puser_id <> ").append(SecurityUtils.getUserId()).append(" AND (EXISTS ( \n" +
                    "			SELECT 1 \n" +
                    "			FROM company_custom_folder_shared_user su \n" +
                    "			WHERE f.id = su.folder_id AND su.user_id = ").append(SecurityUtils.getUserId()).append("\n" +
                    "	) OR EXISTS ( \n" +
                    "			SELECT 1 \n" +
                    "			FROM company_custom_folder_shared_team st \n" +
                    "			INNER JOIN permission_user_team ptu ON st.team_id = ptu.team_id \n" +
                    "			WHERE f.id = st.folder_id AND ptu.user_id = ").append(SecurityUtils.getUserId()).append(" AND (st.ignore_team_user IS NULL OR st.ignore_team_user NOT LIKE CONCAT('%', ").append(SecurityUtils.getUserId()).append(", '%')) \n" +
                    "	)) AND category = ").append(customFolderSearchDTO.getCategory().toDbValue());
            dataSql.append("SELECT f.* \n" +
                    "	, CONCAT(u.first_name,' ',last_name) creator FROM company_custom_folder f INNER JOIN `user` u ON u.id = f.puser_id \n" +
                    "	WHERE f.puser_id <> ").append(SecurityUtils.getUserId()).append(" AND (EXISTS ( \n" +
                    "			SELECT 1 \n" +
                    "			FROM company_custom_folder_shared_user su \n" +
                    "			WHERE f.id = su.folder_id AND su.user_id = ").append(SecurityUtils.getUserId()).append("\n" +
                    "	) OR EXISTS ( \n" +
                    "			SELECT 1 \n" +
                    "			FROM company_custom_folder_shared_team st \n" +
                    "			INNER JOIN permission_user_team ptu ON st.team_id = ptu.team_id \n" +
                    "			WHERE f.id = st.folder_id AND ptu.user_id = ").append(SecurityUtils.getUserId()).append(" AND (st.ignore_team_user IS NULL OR st.ignore_team_user NOT LIKE CONCAT('%', ").append(SecurityUtils.getUserId()).append(", '%')) \n" +
                    "	)) AND category = ").append(customFolderSearchDTO.getCategory().toDbValue());
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_SETSEARCHCUSTOMFOLDERSQL_FOLDERTYPENOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        setFolderNameFilter(customFolderSearchDTO.getName(), paramsMap, countSql, dataSql);
        setFolderNoteFilter(customFolderSearchDTO.getNote(), paramsMap, countSql, dataSql);
        setFolderCreatedDateFilter(customFolderSearchDTO.getCreatedDateFrom(), customFolderSearchDTO.getCreatedDateTo(), paramsMap, countSql, dataSql);
        setFolderLastModifiedDateFilter(customFolderSearchDTO.getLastModifiedDateFrom(), customFolderSearchDTO.getLastModifiedDateTo(), paramsMap, countSql, dataSql);
        setFolderSharedFilter(customFolderSearchDTO.getSharedUsers(), customFolderSearchDTO.getSharedTeams(), paramsMap, countSql, dataSql);
        setFolderCreatorFilter(customFolderSearchDTO.getCreators(), paramsMap, countSql, dataSql);
        setGeneralTextFilter(customFolderSearchDTO.getGeneralTexts(), paramsMap, countSql, dataSql);

        setFolderPageable(pageable, dataSql);
    }

    private void setSearchFolderSql(FolderSearchDTO folderSearchDTO, Pageable pageable, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        countSql.append("SELECT COUNT(f.id) FROM company_search_folder f WHERE f.puser_id = ").append(SecurityUtils.getUserId());
        dataSql.append("SELECT * FROM company_search_folder f WHERE f.puser_id = ").append(SecurityUtils.getUserId());
        setFolderNameFilter(folderSearchDTO.getName(), paramsMap, countSql, dataSql);
        setFolderCreatedDateFilter(folderSearchDTO.getCreatedDateFrom(), folderSearchDTO.getCreatedDateTo(), paramsMap, countSql, dataSql);
        setGeneralTextFilter(folderSearchDTO.getGeneralTexts(), paramsMap, countSql, dataSql);
        setFolderPageable(pageable, dataSql);
    }

    private void setFolderPageable(Pageable pageable, StringBuffer dataSql) {
        int size = pageable.getPageSize();
        int page = pageable.getPageNumber();
        int start = size * page;
        if (pageable.getSort().isSorted()) {
            dataSql.append(" ORDER BY ");
            StringJoiner joiner = new StringJoiner(", ");
            for (Sort.Order order : pageable.getSort()) {
                String column = null;
                switch (order.getProperty()) {
                    case "createdDate" : column = "created_date"; break;
                    case "lastModifiedDate" : column = "last_modified_date"; break;
                    case "creator": column = "CASE WHEN creator IS NULL OR creator = '' THEN 0 ELSE 1 END DESC, CONVERT(creator USING gbk)"; break;
                    case "name": column = "CONVERT(name USING gbk)"; break;
                    default: column = order.getProperty();
                }
                joiner.add(column + " " + order.getDirection().toString());
            }
            dataSql.append(joiner.toString());
        }
        dataSql.append(" LIMIT ").append(start).append(", ").append(size);
    }

    private void setGeneralTextFilter(String generalTexts, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (StringUtils.isNotEmpty(generalTexts)) {
            countSql.append(" AND (");
            dataSql.append(" AND (");
            StringJoiner joiner = new StringJoiner(" OR ");
            Arrays.asList(generalTexts.split(" ")).forEach(generalText -> {
                joiner.add("f.`name` LIKE ?" + (paramsMap.size() + 1));
                paramsMap.put(paramsMap.size() + 1, String.format("%%%s%%", generalText));
            });
            countSql.append(joiner.toString()).append(")");
            dataSql.append(joiner.toString()).append(")");
        }
    }

    private void setFolderNameFilter(String name, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (name != null) {
            countSql.append(" AND f.name LIKE ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.name LIKE ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, String.format("%%%s%%", name));
        }
    }

    private void setFolderNoteFilter(String note, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (note != null) {
            countSql.append(" AND f.note LIKE ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.note LIKE ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, String.format("%%%s%%", note));
        }
    }

    private void setFolderCreatedDateFilter(Instant createdDateFrom, Instant createdDateTo, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (createdDateFrom != null) {
            countSql.append(" AND f.created_date > ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.created_date > ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, createdDateFrom);
        }
        if (createdDateTo != null) {
            countSql.append(" AND f.created_date < ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.created_date < ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, createdDateTo);
        }
    }

    private void setFolderLastModifiedDateFilter(Instant lastModifiedDateFrom, Instant lastModifiedDateTo, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (lastModifiedDateFrom != null) {
            countSql.append(" AND f.last_modified_date > ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.last_modified_date > ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, lastModifiedDateFrom);
        }
        if (lastModifiedDateTo != null) {
            countSql.append(" AND f.last_modified_date < ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.last_modified_date < ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, lastModifiedDateTo);
        }
    }

    private void setFolderSharedFilter(List<Long> sharedUsers, List<Long> sharedTeams, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (sharedUsers != null && CollUtil.isNotEmpty(sharedUsers)) {
            countSql.append(" AND EXISTS (SELECT 1 FROM company_custom_folder_shared_user fu WHERE fu.folder_id = f.id AND fu.user_id IN ?").append(paramsMap.size() + 1).append(")");
            dataSql.append(" AND EXISTS (SELECT 1 FROM company_custom_folder_shared_user fu WHERE fu.folder_id = f.id AND fu.user_id IN ?").append(paramsMap.size() + 1).append(")");
            paramsMap.put(paramsMap.size() + 1, sharedUsers);
        }
        if (sharedTeams != null && CollUtil.isNotEmpty(sharedTeams)) {
            countSql.append(" AND EXISTS (SELECT 1 FROM company_custom_folder_shared_team ft WHERE ft.folder_id = f.id AND ft.team_id IN ?").append(paramsMap.size() + 1).append(")");
            dataSql.append(" AND EXISTS (SELECT 1 FROM company_custom_folder_shared_team ft WHERE ft.folder_id = f.id AND ft.team_id IN ?").append(paramsMap.size() + 1).append(")");
            paramsMap.put(paramsMap.size() + 1, sharedTeams);
        }
    }

    private void setFolderCreatorFilter(List<Long> creators, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (creators != null && CollUtil.isNotEmpty(creators)) {
            countSql.append(" AND f.puser_id IN ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.puser_id IN ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, creators);
        }
    }


    @Override
    public List<CustomFolderSearchVO> toVo(List<CompanyCustomFolderVM> companyCustomFolderList, FolderType folderType) {
        List<CustomFolderSearchVO> resulList = companyCustomFolderList.stream().map(CustomFolderSearchVO::fromCompanyCustomFolderVM).collect(Collectors.toList());
        List<Long> sharedFolderIds = companyCustomFolderList.stream().map(CompanyCustomFolderVM::getId).collect(Collectors.toList());
        List<CompanyCustomFolderSharedUser> sharedUserList = companyCustomFolderSharedUserRepository.findAllByFolderIdIn(sharedFolderIds);
        List<CompanyCustomFolderSharedTeam> sharedTeamList = companyCustomFolderSharedTeamRepository.findAllByFolderIdIn(sharedFolderIds);
        Map<Long, List<CompanyCustomFolderSharedUser>> sharedUserMap = sharedUserList.stream().collect(Collectors.groupingBy(CompanyCustomFolderSharedUser::getFolderId));
        Map<Long, List<CompanyCustomFolderSharedTeam>> sharedTeamMap = sharedTeamList.stream().collect(Collectors.groupingBy(CompanyCustomFolderSharedTeam::getFolderId));


        List<Long> userIdList = sharedUserList.stream().map(CompanyCustomFolderSharedUser::getUserId).distinct().collect(Collectors.toList());
        List<UserBriefDTO> userList = userService.getAllByIdIn(userIdList).getBody();
        Map<Long, String> userNameMap = userList == null ? new HashMap<>() : userList.stream().collect(Collectors.toMap(UserBriefDTO::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));

        List<Long> teamIdList = sharedTeamList.stream().map(CompanyCustomFolderSharedTeam::getTeamId).distinct().collect(Collectors.toList());
        List<PermissionTeamTreeDTO> teamTreeList = userService.getTeamsWithLeaders().getBody();
        Map<Long, String> teamNameMap = teamTreeList == null ? new HashMap<>() : getPermissionTeamList(new HashSet<>(teamIdList), teamTreeList).stream().collect(Collectors.toMap(PermissionTeamTreeDTO::getId, PermissionTeamTreeDTO::getName));

        resulList.forEach(item -> {
            if (sharedUserMap.containsKey(item.getId())) {
                item.setSharedUsers(sharedUserMap.get(item.getId()).stream().map(o -> new FolderSharedVO(o.getUserId(), o.getPermission(), userNameMap.getOrDefault(o.getUserId(), null))).collect(Collectors.toList()));
            }
            if (sharedTeamMap.containsKey(item.getId())) {
                item.setSharedTeams(sharedTeamMap.get(item.getId()).stream().map(o -> new FolderSharedVO(o.getTeamId(), o.getPermission(), teamNameMap.getOrDefault(o.getTeamId(), null))).collect(Collectors.toList()));
            }
        });

        if (FolderType.SHARED.equals(folderType)) {
            List<CompanyCustomFolderSharedUser> sharedUserPermissionList = sharedUserList.stream().filter(o -> o.getUserId().equals(SecurityUtils.getUserId())).collect(Collectors.toList());
            Map<Long, FolderPermission> sharedUserPermissionMap = sharedUserPermissionList.stream().collect(Collectors.toMap(CompanyCustomFolderSharedUser::getFolderId, CompanyCustomFolderSharedUser::getPermission));
            resulList.forEach(o -> o.setPermission(sharedUserPermissionMap.get(o.getId())));
            if (resulList.stream().anyMatch(o -> o.getPermission() == null || o.getPermission() != FolderPermission.EDIT)) {
                List<CompanyCustomFolderSharedTeam> sharedTeamPermissionList = companyCustomFolderSharedTeamRepository.findAllIdByFolderIdInAndUserId(resulList.stream().filter(o -> o.getPermission() == null || o.getPermission() == FolderPermission.VIEW).map(CustomFolderSearchVO::getId).collect(Collectors.toList()), SecurityUtils.getUserId());
                Map<Long, FolderPermission> sharedTeamPermissionMap = sharedTeamPermissionList.stream().collect(Collectors.toMap(CompanyCustomFolderSharedTeam::getFolderId, CompanyCustomFolderSharedTeam::getPermission,
                        (existingPermission, newPermission) -> existingPermission.toDbValue() > newPermission.toDbValue() ? existingPermission : newPermission));
                resulList.stream().filter(item -> sharedTeamPermissionMap.containsKey(item.getId()) && (item.getPermission() == null || item.getPermission() == FolderPermission.VIEW)).forEach(o -> o.setPermission(sharedTeamPermissionMap.get(o.getId())));
            }
        }
        return resulList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCustomFolderCompany(FolderIdMoveCompanyDTO folderIdMoveCompanyDTO) {
        checkCustomFolderPermission(folderIdMoveCompanyDTO.getToFolderIds(), FolderPermission.EDIT);
        checkCompanyPermission(folderIdMoveCompanyDTO.getCompanyIds());
        checkCompanyCategory(folderIdMoveCompanyDTO.getCompanyIds(), folderIdMoveCompanyDTO.getToFolderIds());
//        if (ObjectUtil.isNotEmpty(folderIdMoveCompanyDTO.getFromFolderId())) {
//            List<Long> existConnectIdList = checkFolderCompany(folderIdMoveCompanyDTO.getFromFolderId(), folderIdMoveCompanyDTO.getCompanyIds());
//            companyCustomFolderConnectClientRepository.deleteAllById(existConnectIdList);
//        }

        companyCustomFolderConnectClientRepository.saveAll(getCompanyCustomFolderConnectClient(folderIdMoveCompanyDTO.getToFolderIds(), folderIdMoveCompanyDTO.getCompanyIds()));

        //sync folder to es
        updateCompaniesFolder(folderIdMoveCompanyDTO.getCompanyIds(), folderIdMoveCompanyDTO.getToFolderIds(), Arrays.asList(folderIdMoveCompanyDTO.getFromFolderId()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCustomFolderCompany(FolderIdDeleteCompanyDTO folderIdDeleteCompanyDTO) {
        checkCustomFolderPermission(folderIdDeleteCompanyDTO.getFolderId(), FolderPermission.EDIT);
//        checkCompanyPermission(folderIdDeleteCompanyDTO.getCompanyIds());
//        List<Long> existConnectIdList = checkFolderCompany(folderIdDeleteCompanyDTO.getFolderId(), folderIdDeleteCompanyDTO.getCompanyIds());
        List<CompanyCustomFolderConnectClient> existCompanyIdList = companyCustomFolderConnectClientRepository.findAllByFolderIdAndCompanyIdIn(folderIdDeleteCompanyDTO.getFolderId(), folderIdDeleteCompanyDTO.getCompanyIds());
        companyCustomFolderConnectClientRepository.deleteAllById(existCompanyIdList.stream().map(CompanyCustomFolderConnectClient::getId).collect(Collectors.toList()));

        //sync folder to es
        updateCompaniesFolder(folderIdDeleteCompanyDTO.getCompanyIds(), null, Arrays.asList(folderIdDeleteCompanyDTO.getFolderId()));
    }

    @Override
    public List<FolderSearchVO> searchFolder(FolderSearchDTO folderSearchDTO) {
        List<FolderSearchVO> result = new ArrayList<>();
        Pageable pageable = PageRequest.of(0, folderSearchDTO.getLimit(), Sort.by(Sort.Direction.ASC, "id"));
        //The userId cannot be obtained asynchronously.
        Long userId = SecurityUtils.getUserId();

        List<CompletableFuture<List<?>>> futures = new ArrayList<>();
        futures.add(CompletableFuture.supplyAsync(() -> customFolderRepository.findAllByPermissionUserIdAndCategoryAndNameLike(userId, CategoryFolderType.PROSPECT_ALL_COMPANY, String.format("%%%s%%", folderSearchDTO.getName()), pageable).getContent()));
        futures.add(CompletableFuture.supplyAsync(() -> customFolderRepository.findAllByPermissionUserIdAndCategoryAndNameLike(userId, CategoryFolderType.CLIENT_ALL_COMPANY, String.format("%%%s%%", folderSearchDTO.getName()), pageable).getContent()));
        futures.add(CompletableFuture.supplyAsync(() -> customFolderRepository.findAllSharedByIdAndCategoryAndNameLike(userId, CategoryFolderType.PROSPECT_ALL_COMPANY, String.format("%%%s%%", folderSearchDTO.getName()), pageable).getContent()));
        futures.add(CompletableFuture.supplyAsync(() -> customFolderRepository.findAllSharedByIdAndCategoryAndNameLike(userId, CategoryFolderType.CLIENT_ALL_COMPANY, String.format("%%%s%%", folderSearchDTO.getName()), pageable).getContent()));
        futures.add(CompletableFuture.supplyAsync(() -> searchFolderRepository.findAllByPermissionUserIdAndNameLike(userId, String.format("%%%s%%", folderSearchDTO.getName()), pageable).getContent()));

        List<List<?>> results = futures.stream().map(CompletableFuture::join).collect(Collectors.toList());

        List<CompanyCustomFolder> customFolderList = results.get(0).stream().filter(obj -> obj instanceof CompanyCustomFolder).map(obj -> (CompanyCustomFolder) obj).collect(Collectors.toList());
        customFolderList.addAll(results.get(1).stream().filter(obj -> obj instanceof CompanyCustomFolder).map(obj -> (CompanyCustomFolder) obj).collect(Collectors.toList()));
        List<CompanyCustomFolder> sharedFolderList = results.get(2).stream().filter(obj -> obj instanceof CompanyCustomFolder).map(obj -> (CompanyCustomFolder) obj).collect(Collectors.toList());
        sharedFolderList.addAll(results.get(3).stream().filter(obj -> obj instanceof CompanyCustomFolder).map(obj -> (CompanyCustomFolder) obj).collect(Collectors.toList()));
        List<CompanySearchFolder> searchFolderList = results.get(4).stream().filter(obj -> obj instanceof CompanySearchFolder).map(obj -> (CompanySearchFolder) obj).collect(Collectors.toList());

//        result.add(new FolderSearchVO(FolderType.CUSTOMIZED, toVo(customFolderList, FolderType.CUSTOMIZED)));
//        result.add(new FolderSearchVO(FolderType.SHARED, toVo(sharedFolderList, FolderType.SHARED)));
//        result.add(new FolderSearchVO(FolderType.SEARCHED, searchFolderList.stream().map(CustomFolderSearchVO::fromCompanySearchFolder).collect(Collectors.toList())));

        return result;
    }

    @Override
    public List<FolderSearchVO> searchFolderList(CategoryFolderType type) {
        List<FolderSearchVO> result = new ArrayList<>();
        //The userId cannot be obtained asynchronously.
        Long userId = SecurityUtils.getUserId();

        List<CompletableFuture<List<?>>> futures = new ArrayList<>();
        futures.add(CompletableFuture.supplyAsync(() -> customFolderRepository.findAllByPermissionUserIdAndCategory(userId, type)));
        futures.add(CompletableFuture.supplyAsync(() -> customFolderRepository.findAllSharedByUserIdAndCategory(userId, type)));
        List<List<?>> results = futures.stream().map(CompletableFuture::join).collect(Collectors.toList());
        List<CompanyCustomFolderVM> customFolderList = results.get(0).stream().filter(obj -> obj instanceof CompanyCustomFolderVM).map(obj -> (CompanyCustomFolderVM) obj).collect(Collectors.toList());
        List<CompanyCustomFolderVM> sharedFolderList = results.get(1).stream().filter(obj -> obj instanceof CompanyCustomFolderVM).map(obj -> (CompanyCustomFolderVM) obj).collect(Collectors.toList());

        List<CustomFolderSearchVO> sharedFolderVOList = sharedFolderList.stream().map(CustomFolderSearchVO::fromCompanyCustomFolderVM).collect(Collectors.toList());
        //query shared folder permission
        List<CompanyCustomFolderSharedUser> sharedUserList = companyCustomFolderSharedUserRepository.findAllByFolderIdInAndUserId(sharedFolderList.stream().map(CompanyCustomFolderVM::getId).collect(Collectors.toList()), SecurityUtils.getUserId());
        Map<Long, FolderPermission> sharedUserPermissionMap = sharedUserList.stream().collect(Collectors.toMap(CompanyCustomFolderSharedUser::getFolderId, CompanyCustomFolderSharedUser::getPermission));
        sharedFolderVOList.forEach(o -> o.setPermission(sharedUserPermissionMap.get(o.getId())));
        if (sharedFolderVOList.stream().anyMatch(o -> o.getPermission() == null || o.getPermission() != FolderPermission.EDIT)) {
            List<CompanyCustomFolderSharedTeam> sharedTeamPermissionList = companyCustomFolderSharedTeamRepository.findAllIdByFolderIdInAndUserId(sharedFolderVOList.stream().filter(o -> o.getPermission() == null || o.getPermission() == FolderPermission.VIEW).map(CustomFolderSearchVO::getId).collect(Collectors.toList()), SecurityUtils.getUserId());
            Map<Long, FolderPermission> sharedTeamPermissionMap = sharedTeamPermissionList.stream().collect(Collectors.toMap(CompanyCustomFolderSharedTeam::getFolderId, CompanyCustomFolderSharedTeam::getPermission,
                    (existingPermission, newPermission) -> existingPermission.toDbValue() > newPermission.toDbValue() ? existingPermission : newPermission));
            sharedFolderVOList.stream().filter(item -> sharedTeamPermissionMap.containsKey(item.getId()) && (item.getPermission() == null || item.getPermission() == FolderPermission.VIEW)).forEach(o -> o.setPermission(sharedTeamPermissionMap.get(o.getId())));
        }

        result.add(new FolderSearchVO(FolderType.CUSTOMIZED, customFolderList.stream().map(CustomFolderSearchVO::fromCompanyCustomFolderVM).collect(Collectors.toList())));
        result.add(new FolderSearchVO(FolderType.SHARED, sharedFolderVOList));

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteSharedFolder(FolderIdDTO folderIdDTO) {
        List<Long> userSharedFolderIdList = companyCustomFolderSharedUserRepository.findAllIdByByFolderIdInAndUserId(folderIdDTO.getFolderIds(), SecurityUtils.getUserId());
        List<CompanyCustomFolderSharedTeam> companyCustomFolderSharedTeamList = companyCustomFolderSharedTeamRepository.findAllIdByFolderIdInAndUserId(folderIdDTO.getFolderIds(), SecurityUtils.getUserId());
        if (userSharedFolderIdList.size() + companyCustomFolderSharedTeamList.size() != folderIdDTO.getFolderIds().size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_COMMON_FOLDERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        companyCustomFolderSharedUserRepository.deleteAllById(userSharedFolderIdList);
        companyCustomFolderSharedTeamList.forEach(o -> {
            if (ObjectUtil.isEmpty(o.getIgnoreTeamUser())) {
                o.setIgnoreTeamUser(String.valueOf(SecurityUtils.getUserId()));
            } else {
                Set<Long> ignoreTeamUserSet = Arrays.stream(o.getIgnoreTeamUser().split(","))
                        .map(Long::valueOf)
                        .collect(Collectors.toSet());
                ignoreTeamUserSet.add(SecurityUtils.getUserId());
                o.setIgnoreTeamUser(ignoreTeamUserSet.stream().map(Object::toString).collect(Collectors.joining(",")));
            }
        });
    }

    @Override
    public void updateCompaniesFolder(List<Long> companyIds, List<Long> toFolderIds, List<Long> fromFolderIds) {
        if (CollUtil.isEmpty(companyIds) || (CollUtil.isEmpty(toFolderIds) && CollUtil.isEmpty(fromFolderIds))) {
            return;
        }

        Long tenantId = SecurityUtils.getTenantId();
        esFillerCompanyService.updateCompaniesFolder(companyIds, toFolderIds == null ? null : toFolderIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()), fromFolderIds == null ? null : fromFolderIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()), tenantId);
//        CompletableFuture.supplyAsync(() -> {
//            esFillerCompanyService.updateCompaniesFolder(companyIds, toFolderIds == null ? null : toFolderIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()), fromFolderIds == null ? null : fromFolderIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()), tenantId);
//            return 0;
//        });
    }

    @Override
    public void checkCustomFolderPermission(List<Long> folderIds) {
        if(CollUtil.isEmpty(folderIds)) {
            return;
        }
        List<Long> ids = new ArrayList<>(folderIds);
        List<CompanyCustomFolder> companyCustomFolderList = customFolderRepository.findAllById(ids);
        if (CollUtil.isEmpty(companyCustomFolderList) || companyCustomFolderList.size() != ids.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_COMMON_FOLDERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        ids.removeAll(companyCustomFolderList.stream().filter(o -> o.getPermissionUserId().equals(SecurityUtils.getUserId())).map(CompanyCustomFolder::getId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        List<CompanyCustomFolderSharedUser> companyCustomFolderSharedUserList = companyCustomFolderSharedUserRepository.findAllByFolderIdInAndUserId(ids, SecurityUtils.getUserId());
        ids.removeAll(companyCustomFolderSharedUserList.stream().map(CompanyCustomFolderSharedUser::getFolderId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        List<CompanyCustomFolderSharedTeam> companyCustomFolderSharedTeamList = companyCustomFolderSharedTeamRepository.findAllIdByFolderIdInAndUserId(ids, SecurityUtils.getUserId());
        ids.removeAll(companyCustomFolderSharedTeamList.stream().map(CompanyCustomFolderSharedTeam::getFolderId).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(ids)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_COMMON_FOLDERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCustomFolderConnectClientByCreatedBy(String createdBy){
        companyCustomFolderConnectClientRepository.deleteAllByCreatedBy(createdBy);
    }

    public CompanyCustomFolder checkCustomFolderPermission(Long folderId) {
        if(ObjectUtil.isEmpty(folderId)) {
            throw new NotFoundException("folder does not exists, id is empty.");
        }

        CompanyCustomFolder companyCustomFolder = customFolderRepository.findById(folderId).orElseThrow(() -> new NotFoundException("folder does not exists"));
        if (companyCustomFolder.getPermissionUserId().equals(SecurityUtils.getUserId())) {
            return companyCustomFolder;
        }

        List<CompanyCustomFolderSharedUser> companyCustomFolderSharedUserList = companyCustomFolderSharedUserRepository.findAllByFolderIdInAndUserId(Arrays.asList(folderId), SecurityUtils.getUserId());
        if (CollUtil.isNotEmpty(companyCustomFolderSharedUserList)) {
            return companyCustomFolder;
        }

        List<CompanyCustomFolderSharedTeam> companyCustomFolderSharedTeamList = companyCustomFolderSharedTeamRepository.findAllIdByFolderIdInAndUserId(Arrays.asList(folderId), SecurityUtils.getUserId());
        if (CollUtil.isEmpty(companyCustomFolderSharedTeamList)) {
            throw new ForbiddenException("folder does not exists");
        }
        return companyCustomFolder;
    }

    private void batchDeleteCompaniesFolder(Map<Long, List<Long>> folderCompanyMap) {
        Long tenantId = SecurityUtils.getTenantId();
        folderCompanyMap.forEach((key, value) -> {
            if (key != null && value != null && CollUtil.isNotEmpty(value)) {
                esFillerCompanyService.updateCompaniesFolder(value, null, Arrays.asList(String.valueOf(key)), tenantId);
            }
        });
//        CompletableFuture.supplyAsync(() -> {
//            folderCompanyMap.forEach((key, value) -> {
//                if (key != null && value != null && CollUtil.isNotEmpty(value)) {
//                    esFillerCompanyService.updateCompaniesFolder(value, null, Arrays.asList(String.valueOf(key)), tenantId);
//                }
//            });
//            return 0;
//        });
    }


    private List<CompanyCustomFolderConnectClient> getCompanyCustomFolderConnectClient(List<Long> toFolderIds, List<Long> companyIds) {
        Set<CompanyCustomFolderConnectClient> existSet = new HashSet<>(companyCustomFolderConnectClientRepository.findAllByFolderIdInAndCompanyIdIn(toFolderIds, companyIds));

        return toFolderIds.stream().flatMap(folder ->
                        companyIds.stream().map(company -> new CompanyCustomFolderConnectClient(folder, company)))
                .filter(item -> !existSet.contains(item))
                .collect(Collectors.toList());

    }

    private CustomFolderVO toVo(CompanyCustomFolder existCompanyCustomFolder) {
        CustomFolderVO customFolderVO = CustomFolderVO.fromCompanyCustomFolder(existCompanyCustomFolder);
        List<CompanyCustomFolderSharedUser> sharedUserList = companyCustomFolderSharedUserRepository.findAllByFolderId(existCompanyCustomFolder.getId());
        List<CompanyCustomFolderSharedTeam> sharedTeamList = companyCustomFolderSharedTeamRepository.findAllByFolderId(existCompanyCustomFolder.getId());

        List<Long> userIdList = sharedUserList.stream().map(CompanyCustomFolderSharedUser::getUserId).distinct().collect(Collectors.toList());
        List<UserBriefDTO> userList = userService.getAllByIdIn(userIdList).getBody();
        if (userList != null) {
            Map<Long, String> userNameMap = userList.stream().collect(Collectors.toMap(UserBriefDTO::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));
            List<FolderSharedVO> sharedUsers = sharedUserList.stream().map(o -> new FolderSharedVO(o.getUserId(), o.getPermission(), userNameMap.getOrDefault(o.getUserId(), null))).collect(Collectors.toList());
            customFolderVO.setSharedUsers(sharedUsers);
        }

        List<Long> teamIdList = sharedTeamList.stream().map(CompanyCustomFolderSharedTeam::getTeamId).distinct().collect(Collectors.toList());
        List<PermissionTeamTreeDTO> teamTreeList = userService.getTeamsWithLeaders().getBody();
        if (teamTreeList != null) {
            List<PermissionTeamTreeDTO> teamList = getPermissionTeamList(new HashSet<>(teamIdList), teamTreeList);
            Map<Long, String> teamNameMap = teamList.stream().collect(Collectors.toMap(PermissionTeamTreeDTO::getId, PermissionTeamTreeDTO::getName));
            List<FolderSharedVO> sharedTeams = sharedTeamList.stream().map(o -> new FolderSharedVO(o.getTeamId(), o.getPermission(), teamNameMap.getOrDefault(o.getTeamId(), null))).collect(Collectors.toList());
            customFolderVO.setSharedTeams(sharedTeams);
        }

        if (SecurityUtils.getUserId().equals(existCompanyCustomFolder.getPermissionUserId())) {
            customFolderVO.setPermission(FolderPermission.EDIT);
        } else {
            Optional<CompanyCustomFolderSharedUser> sharedUserPermission = sharedUserList.stream().filter(o -> o.getUserId().equals(SecurityUtils.getUserId())).findFirst();
            if (sharedUserPermission.isPresent()) {
                customFolderVO.setPermission(sharedUserPermission.get().getPermission());
            }
      
            if (!FolderPermission.EDIT.equals(customFolderVO.getPermission())) {
                CompanyCustomFolderSharedTeam companyCustomFolderSharedTeam = companyCustomFolderSharedTeamRepository.findByFolderIdAndUserIdAndPermission(existCompanyCustomFolder.getId(), SecurityUtils.getUserId(), FolderPermission.EDIT.toDbValue());
                if (companyCustomFolderSharedTeam != null) {
                    customFolderVO.setPermission(companyCustomFolderSharedTeam.getPermission());
                }
            }
        }

        return customFolderVO;
    }

    private void addCustomFolderSharedUser(CustomFolderDTO customFolderDTO, Long folderId, CustomFolderVO customFolderVO) {
        if (customFolderDTO.getSharedUsers() != null && CollUtil.isNotEmpty(customFolderDTO.getSharedUsers())) {
            List<Long> userIdList = customFolderDTO.getSharedUsers().stream().distinct().map(FolderSharedDTO::getId).collect(Collectors.toList());
            List<UserBriefDTO> userList = userService.getAllByIdIn(userIdList).getBody();
            if (userList == null || userList.size() != userIdList.size() || userList.stream().anyMatch(o -> !o.getTenantId().equals(SecurityUtils.getTenantId()))) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_ADDCUSTOMFOLDERSHAREDUSER_USERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
            }
            Map<Long, String> userNameMap = userList.stream().collect(Collectors.toMap(UserBriefDTO::getId, o -> CommonUtils.formatFullName(o.getFirstName(), o.getLastName())));
            List<CompanyCustomFolderSharedUser> sharedUserList = companyCustomFolderSharedUserRepository.saveAll(customFolderDTO.getSharedUsers().stream().distinct().map(o -> new CompanyCustomFolderSharedUser(folderId, o.getId(), o.getPermission())).collect(Collectors.toList()));
            List<FolderSharedVO> sharedUsers = sharedUserList.stream().map(o -> new FolderSharedVO(o.getUserId(), o.getPermission(), userNameMap.getOrDefault(o.getUserId(), null))).collect(Collectors.toList());
            customFolderVO.setSharedUsers(sharedUsers);
        }
    }

    private void addCustomFolderSharedTeam(CustomFolderDTO customFolderDTO, Long folderId, CustomFolderVO customFolderVO) {
        if (customFolderDTO.getSharedTeams() != null && CollUtil.isNotEmpty(customFolderDTO.getSharedTeams())) {
            List<Long> teamIdList = customFolderDTO.getSharedTeams().stream().distinct().map(FolderSharedDTO::getId).collect(Collectors.toList());
            List<PermissionTeamTreeDTO> teamTreeList = userService.getTeamsWithLeaders().getBody();
            if (teamTreeList == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_ADDCUSTOMFOLDERSHAREDTEAM_PARAMISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
            }
            List<PermissionTeamTreeDTO> teamList = getPermissionTeamList(new HashSet<>(teamIdList), teamTreeList);
            if (teamIdList.size() != teamList.size()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_ADDCUSTOMFOLDERSHAREDTEAM_TEAMNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
            }
            Map<Long, String> teamNameMap = teamList.stream().collect(Collectors.toMap(PermissionTeamTreeDTO::getId, PermissionTeamTreeDTO::getName));
            List<CompanyCustomFolderSharedTeam> sharedTeamList = companyCustomFolderSharedTeamRepository.saveAll(customFolderDTO.getSharedTeams().stream().distinct().map(o -> new CompanyCustomFolderSharedTeam(folderId, o.getId(), o.getPermission())).collect(Collectors.toList()));
            List<FolderSharedVO> sharedTeams = sharedTeamList.stream().map(o -> new FolderSharedVO(o.getTeamId(), o.getPermission(), teamNameMap.getOrDefault(o.getTeamId(), null))).collect(Collectors.toList());
            customFolderVO.setSharedTeams(sharedTeams);
        }
    }

    private List<PermissionTeamTreeDTO> getPermissionTeamList(Set<Long> teamIdSet, List<PermissionTeamTreeDTO> teamTreeList) {
        List<PermissionTeamTreeDTO> teamList = new ArrayList<>();
        teamTreeList.forEach(item -> helpFindPermissionTeam(teamList, teamIdSet, item));
        return teamList;
    }

    private void helpFindPermissionTeam(List<PermissionTeamTreeDTO> teamList, Set<Long> teamIdSet, PermissionTeamTreeDTO permissionTeamTreeDTO) {
        if (teamIdSet.contains(permissionTeamTreeDTO.getId())) {
            teamList.add(permissionTeamTreeDTO);
        }
        if (permissionTeamTreeDTO.getChildren() == null || CollUtil.isEmpty(permissionTeamTreeDTO.getChildren())) {
            return;
        }
        permissionTeamTreeDTO.getChildren().forEach(child -> {
            helpFindPermissionTeam(teamList, teamIdSet, child);
        });
    }

    private void checkSearchFolderDuplicateName(String name) {
        List<CompanySearchFolder> companySearchFolderList = searchFolderRepository.findAllByPermissionUserIdAndName(SecurityUtils.getUserId(), name);
        if (CollUtil.isNotEmpty(companySearchFolderList) && companySearchFolderList.size() > 0) {
            throw new CustomParameterizedException("name already exists.");
        }
    }

    private void checkCustomFolderDuplicateName(String name) {
        List<CompanyCustomFolder> companyCustomFolderList = customFolderRepository.findAllByPermissionUserIdAndName(SecurityUtils.getUserId(), name);
        if (CollUtil.isNotEmpty(companyCustomFolderList) && companyCustomFolderList.size() > 0) {
            throw new CustomParameterizedException("name already exists.");
        }
    }

    private CompanyCustomFolder checkCustomFolderPermission(Long id, FolderPermission permission) {
        CompanyCustomFolder companyCustomFolder = customFolderRepository.findById(id).orElseThrow(() -> new NotFoundException("folder does not exists"));
        if (companyCustomFolder.getPermissionUserId().equals(SecurityUtils.getUserId())) {
            return companyCustomFolder;
        }

        CompanyCustomFolderSharedUser companyCustomFolderSharedUser = companyCustomFolderSharedUserRepository.findByFolderIdAndUserIdAndPermission(id, SecurityUtils.getUserId(), permission);
        if (companyCustomFolderSharedUser != null) {
            return companyCustomFolder;
        }

        CompanyCustomFolderSharedTeam companyCustomFolderSharedTeam = companyCustomFolderSharedTeamRepository.findByFolderIdAndUserIdAndPermission(id, SecurityUtils.getUserId(), permission.toDbValue());
        if (companyCustomFolderSharedTeam == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_COMMON_FOLDERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return companyCustomFolder;
    }

    private void checkCustomFolderPermission(List<Long> toFolderIds, FolderPermission permission) {
        List<Long> ids = new ArrayList<>(toFolderIds);

        List<CompanyCustomFolder> companyCustomFolderList = customFolderRepository.findAllById(ids);
        if (CollUtil.isEmpty(companyCustomFolderList) || companyCustomFolderList.size() != ids.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_COMMON_FOLDERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        ids.removeAll(companyCustomFolderList.stream().filter(o -> o.getPermissionUserId().equals(SecurityUtils.getUserId())).map(CompanyCustomFolder::getId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        List<CompanyCustomFolderSharedUser> companyCustomFolderSharedUserList = companyCustomFolderSharedUserRepository.findAllByFolderIdInAndUserIdAndPermission(ids, SecurityUtils.getUserId(), permission);
        ids.removeAll(companyCustomFolderSharedUserList.stream().map(CompanyCustomFolderSharedUser::getFolderId).collect(Collectors.toList()));
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        List<CompanyCustomFolderSharedTeam> companyCustomFolderSharedTeamList = companyCustomFolderSharedTeamRepository.findByFolderIdInAndUserIdAndPermission(ids, SecurityUtils.getUserId(), permission.toDbValue());
        ids.removeAll(companyCustomFolderSharedTeamList.stream().map(CompanyCustomFolderSharedTeam::getFolderId).collect(Collectors.toList()));
        if (CollUtil.isNotEmpty(ids)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_COMMON_FOLDERNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
    }

    private void checkCustomFolderDuplicateName(String name, Long id) {
        List<CompanyCustomFolder> companyCustomFolderList = customFolderRepository.findAllByPermissionUserIdAndNameAndIdNot(SecurityUtils.getUserId(), name, id);
        if (CollUtil.isNotEmpty(companyCustomFolderList) && companyCustomFolderList.size() > 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_CHECKCUSTOMFOLDERNAME_EXISTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
    }

    private List<Company> checkCompanyPermission(List<Long> ids) {
        List<Company> companyList = companyRepository.findAllById(ids);
        if (ids.size() != companyList.size() || companyList.stream().anyMatch(o -> !o.getTenantId().equals(SecurityUtils.getTenantId()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_CHECKCOMPANYPERMISSION_NOTEXISTS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return companyList;
    }

    private void checkCompanyCategory(List<Long> companyIds, CategoryFolderType category) {
        List<EntityCountVM> countSalesLeadList = salesLeadServiceRepository.countSalesLeadByCompanyId(companyIds, category);
        if (companyIds.size() != countSalesLeadList.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(category.equals(CategoryFolderType.CLIENT_ALL_COMPANY) ? CompanyAPIMultilingualEnum.FOLDER_CHECKCOMPANYCATEGORY_NOESCALATED.getKey() : CompanyAPIMultilingualEnum.FOLDER_CHECKCOMPANYCATEGORY_NOUPGRADED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
    }

    private void checkCompanyCategory(List<Long> companyIds, List<Long> toFolderIds) {
        List<CompanyCustomFolder> folderList = customFolderRepository.findAllById(toFolderIds);
        if (folderList.stream().map(CompanyCustomFolder::getCategory).collect(Collectors.toSet()).size() != 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_CHECKCOMPANYCATEGORY_MUSTSAME.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }

        checkCompanyCategory(companyIds, folderList.get(0).getCategory());
    }

    private List<Long> checkFolderCompany(Long folderId, List<Long> companyIds) {
        List<CompanyCustomFolderConnectClient> existCompanyIdList = companyCustomFolderConnectClientRepository.findAllByFolderIdAndCompanyIdIn(folderId, companyIds);
        if (!CollectionUtils.isEqualCollection(companyIds, existCompanyIdList.stream().map(CompanyCustomFolderConnectClient::getCompanyId).collect(Collectors.toList()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_CHECKFOLDERCOMPANY_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return existCompanyIdList.stream().map(CompanyCustomFolderConnectClient::getId).collect(Collectors.toList());
    }


    private <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        entityManager.clear();
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchData(queryStr, clazz, paramMap);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }


    private <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        return query.getResultList();
    }

    private Integer checkInList(Map<Integer, Object> map) {
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.FOLDER_CHECKINLIST_QUERYCONDITION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
        return keyList.get(0);
    }

    private Long searchCount(String queryStr, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        map.forEach((k,v) -> ReflectUtil.invoke(query, method, k, v));
        return Long.parseLong(String.valueOf(query.getSingleResult()));
    }
}
