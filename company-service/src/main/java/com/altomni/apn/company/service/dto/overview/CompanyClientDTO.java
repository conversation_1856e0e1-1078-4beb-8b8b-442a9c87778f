package com.altomni.apn.company.service.dto.overview;

import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.company.aop.validation.ContributionSum;
import com.altomni.apn.company.domain.enumeration.company.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.persistence.Convert;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * date:2023-04-13
 */
@Data
@ApiModel(description = "detail for company")
@ContributionSum
public class CompanyClientDTO implements Serializable {

    @ApiModelProperty(value = "The name for company.")
    @NotEmpty
    private String name;

    @ApiModelProperty(value = "The logo for company.")
    private String logo;

    @ApiModelProperty(value = "The industries for company.")
    @NotNull
    private List<EnumRelationDTO> industries;

    @ApiModelProperty(value = "The website for company.")
    private String website;

    @ApiModelProperty(value = "The staffSizeType for company.")
    @Convert(converter = StaffSizeTypeConverter.class)
    private StaffSizeType staffSize;

    @ApiModelProperty(value = "The location for company.")
    @NotNull
    private List<LocationDTO> companyLocations;

    @ApiModelProperty(value = "The fortuneRank for company.")
    private FortuneRankType fortuneRank;

    @ApiModelProperty(value = "The businessRevenue for company.")
    @Convert(converter = CompanyBizRevenueTypeConverter.class)
    private CompanyBizRevenueType businessRevenue;

    @ApiModelProperty(value = "The linkedinCompanyProfile for company.")
    private String linkedinCompanyProfile;

    @ApiModelProperty(value = "The crunchbaseCompanyProfile for company.")
    private String crunchbaseCompanyProfile;

    @ApiModelProperty(value = "The level for company.")
    @NotNull
    private CompanyClientLevelType companyClientLevel;

    @ApiModelProperty(value = "The active for company.")
    private Boolean active;

    @ApiModelProperty(value = "The salesLeadDetails for company.")
    @NotNull
    @Valid
    private List<CompanySalesLeadDetailClientDTO> salesLeadDetails;

    @ApiModelProperty(value = "The tags for company.")
    @UniqueElements
    private List<Long> tags;

}