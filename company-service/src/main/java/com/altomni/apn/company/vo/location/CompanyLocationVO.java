package com.altomni.apn.company.vo.location;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.company.lcoation.CompanyLocation;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * A CompanyLocationVO.
 * <AUTHOR>
 */
@AllArgsConstructor
@ApiModel(description = "Vo for company location")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CompanyLocationVO implements Serializable {

    @ApiModelProperty(value = "Location id.")
    private Long id;

    @ApiModelProperty(value = "The AWS location returned by the location service interface.")
    private LocationVO location;

    @ApiModelProperty(value = "The companyId associated with the location.")
    private Long accountCompanyId;

    @Override
    public String toString() {
        return "CompanyLocationVO{" +
                "id=" + id +
                ", location=" + location +
                ", accountCompanyId=" + accountCompanyId +
                '}';
    }

    public static CompanyLocationVO fromCompanyLocation(CompanyLocation companyLocation) {
        CompanyLocationVO companyLocationVO = new CompanyLocationVO();
        ServiceUtils.myCopyProperties(companyLocation, companyLocationVO);
        companyLocationVO.setLocation(JSONUtil.toBean(companyLocation.getOriginalLoc(), LocationVO.class));
        return companyLocationVO;
    }
}
