package com.altomni.apn.company.domain.company.note;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.enumeration.note.CompanyNoteCategory;
import com.altomni.apn.company.domain.enumeration.note.CompanyNoteCategoryConverter;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@Entity
@Table(name = "company_client_note")
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CompanyClientNote extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "last_contact_date")
    private Instant contactDate;

    @Column(name = "note")
    private String note;

//    @Column(name = "synced")
//    private Boolean synced;
//
//    @Column(name = "reminder_time")
//    private Instant reminderTime;

    @Column(name = "deleted")
    private Boolean deleted = false;

    @ApiModelProperty(value = "last successful sync to ES")
    @Column(name = "last_sync_time")
    private Instant lastSyncTime;

    @Column(name = "category")
    @Convert(converter = CompanyNoteCategoryConverter.class)
    private CompanyNoteCategory category = CompanyNoteCategory.NORMAL;

    public CompanyClientNote(Long companyId, String note) {
        this.companyId = companyId;
        this.note = note;
    }

    public static CompanyClientNote fromCompanyClientNoteDTO(CompanyClientNoteDTO companyClientNoteDTO) {
        CompanyClientNote companyClientNote = new CompanyClientNote();
        ServiceUtils.myCopyProperties(companyClientNoteDTO, companyClientNote);
        return companyClientNote;
    }
}
