package com.altomni.apn.company.web.rest.dashboard;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.service.dashboard.DashboardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class DashboardResource {

    @Resource
    private DashboardService dashboardService;

    @GetMapping("/dashboard/customer-metric")
    public ResponseEntity<String> customerMetric(@RequestParam("startTime") Instant startTime, @RequestParam("endTime") Instant endTime, @RequestParam("userIdList") List<Long> userIdList) throws IOException {
        log.info("[APN: DashboardResource @{}] REST request to search customer metric.", SecurityUtils.getUserId());
        return ResponseEntity.ok(dashboardService.customerMetric(startTime, endTime,userIdList));
    }

}
