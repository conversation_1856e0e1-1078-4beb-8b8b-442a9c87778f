package com.altomni.apn.company.service.company.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.company.CompanyClientInfoVO;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.company.CompanyClientInfo;
import com.altomni.apn.company.repository.company.CompanyClientInfoRepository;
import com.altomni.apn.company.service.company.CompanyClientInfoService;
import com.altomni.apn.company.service.dto.CompanyClientInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class CompanyClientInfoServiceImpl implements CompanyClientInfoService {

    @Resource
    CompanyClientInfoRepository companyClientInfoRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    @Resource
    CommonRedisService commonRedisService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(CompanyClientInfoDTO dto) {
        checkParam(dto);
        //checkClientNameExist(dto, 1);
        CompanyClientInfo info = new CompanyClientInfo();
        BeanUtils.copyProperties(dto, info);
        info.setTenantId(SecurityUtils.getTenantId());
        companyClientInfoRepository.save(info);
        log.info("[companyClientInfo] save info,{}", JSONUtil.toJsonStr(info));

        commonRedisService.set(String.format(RedisConstants.INVOICE_PREFERENCE, SecurityUtils.getTenantId(), SecurityUtils.getUserId()), "2");
    }

    public void checkParam(CompanyClientInfoDTO dto) {
        if (dto.getCompanyId() == null || StringUtils.isBlank(dto.getClientAddress())
                || StringUtils.isBlank(dto.getClientName()) || StringUtils.isBlank(dto.getClientLocation())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
    }

    /**
     * 校验客户名称
     *
     * @param dto
     * @param operateType 1 save 2 update
     */
    public void checkClientNameExist(CompanyClientInfoDTO dto, Integer operateType) {
        List<CompanyClientInfo> infos = companyClientInfoRepository.findAllByCompanyIdAndClientName(dto.getCompanyId(), dto.getClientName().trim());
        if (infos != null && !infos.isEmpty()) {
            if (operateType == 1) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_CLIENT_INVOICING_CLIENTNAME_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
            } else {
                if (infos.size() > 1) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_CLIENT_INVOICING_CLIENTNAME_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
                }
                CompanyClientInfo info = infos.get(0);
                if (!info.getId().equals(dto.getId())) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_CLIENT_INVOICING_CLIENTNAME_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CompanyClientInfoDTO dto) {
        checkParam(dto);
        //checkClientNameExist(dto, 2);
        Optional<CompanyClientInfo> infoOpt = companyClientInfoRepository.findById(dto.getId());
        if (!infoOpt.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
        CompanyClientInfo info = infoOpt.get();
        info.setClientAddress(dto.getClientAddress());
        info.setClientName(dto.getClientName());
        info.setCompanyId(dto.getCompanyId());
        info.setClientEmail(dto.getClientEmail());
        info.setClientLocation(dto.getClientLocation());
        info.setClientDivision(dto.getClientDivision());
        info.setTenantId(SecurityUtils.getTenantId());
        companyClientInfoRepository.save(info);
        log.info("[companyClientInfo] save info,{}", JSONUtil.toJsonStr(info));
    }

    @Override
    public List<CompanyClientInfoVO> findByCompanyId(Long companyId) {
        List<CompanyClientInfo> infos = companyClientInfoRepository.findAllByCompanyId(companyId);
        List<CompanyClientInfoVO> voList = new ArrayList<>();
        infos.forEach(v -> {
            CompanyClientInfoVO bean = new CompanyClientInfoVO();
            BeanUtils.copyProperties(v, bean);
            bean.setInvoicingArea(2);
            voList.add(bean);
        });
        return voList;
    }

    @Override
    public CompanyClientInfoVO getCompanyClientInfoById(Long id) {
        Optional<CompanyClientInfo> companyClientInfoOpt = companyClientInfoRepository.findById(id);
        CompanyClientInfoVO bean = new CompanyClientInfoVO();
        CompanyClientInfo info = companyClientInfoOpt.get();
        BeanUtils.copyProperties(info, bean);
        return bean;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        log.info("[companyClientInfo] delete info, id:{},userId:{}", id, SecurityUtils.getUserId());
        Optional<CompanyClientInfo> companyClientInfoOpt = companyClientInfoRepository.findById(id);
        if (companyClientInfoOpt.isPresent()) {
            CompanyClientInfo info = companyClientInfoOpt.get();
            if (!info.getTenantId().equals(SecurityUtils.getTenantId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_FINDAMREPORT_NOPERMISSON.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
            }

            companyClientInfoRepository.delete(info);
            log.info("[companyClientInfo] delete info,{}", JSONUtil.toJsonStr(info));
        }
    }
}
