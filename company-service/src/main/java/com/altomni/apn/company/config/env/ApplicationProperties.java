package com.altomni.apn.company.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.emailService.supportSender}")
    private String supportSender;

    @Value("${application.esfiller.syncUrl}")
    private String syncUrl;

    @Value("${application.esfiller.baseUrl}")
    private String baseUrl;

    @Value("${application.mainPath.jobDivaUrl}")
    private String jobDivaUrl;

    @Value("${application.security.aes.secretKey}")
    private String secret;

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.port}")
    private Integer redisPort;

    @Value("${spring.redis.database}")
    private Integer redisDb;


//    @Value("${application.company.searchHistoryQueue.maxSize}")
//    private Integer searchHistoryMaxSize;
//
//    @Value("${application.company.searchHistoryQueue.expireTime}")
//    private Integer searchHistoryExpireTime;
//
    @Value("${application.commonService}")
    private String apnCommonServiceUrl;

//    @Value("${application.gpt.token}")
//    private String gptToken;

    @Value("${application.company.defaultClientNote}")
    private String defaultClientNote;

    @Value("${application.crmUrl}")
    private String crmUrl;

//    @Value("${application.hrUrl}")
//    private String hrUrl;

    @Value("${application.account.backFillRoutingKey}")
    private String crmBackFillRoutingKey;

    @Value("${oauth2.username}")
    private String username;

    @Value("${oauth2.password}")
    private String password;

    @Value("${public-statistic.monitor.company.tenants:}")
    private String monitorCompanyForTenants;

    @Value("${public-statistic.monitor.company.threshold:10}")
    private int monitorCompanyThreshold;

    @Value("${public-statistic.monitor.company.url:}")
    private String monitorCompanyUrl;

    @Value("${public-statistic.monitor.company.whitelist:}")
    private String monitorCompanyWhitelist;

    @Value("${public-statistic.monitor.company.lark.enabled:false}")
    private boolean monitorEnabled;

    @Value("${public-statistic.monitor.company.lark.webhookKey:}")
    private String monitorWebhookKey;

    @Value("${public-statistic.monitor.company.lark.webhookUrl:}")
    private String monitorWebhookUrl;
}
