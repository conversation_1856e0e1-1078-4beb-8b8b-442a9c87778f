package com.altomni.apn.company.repository.business;

import com.altomni.apn.company.domain.business.CompanySalesLead;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Deprecated
@Repository
public interface CompanySalesLeadRepository extends JpaRepository<CompanySalesLead, Long>, JpaSpecificationExecutor<CompanySalesLead> {

    List<CompanySalesLead> findAllByAccountCompanyIdIn(List<Long> companyIds);

}