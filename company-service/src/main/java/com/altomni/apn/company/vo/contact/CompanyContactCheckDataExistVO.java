package com.altomni.apn.company.vo.contact;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CompanyContactCheckDataExistVO implements Serializable {

    public boolean jobContact;

    public boolean invoiceContact;

    public boolean timesheetContact;

    public boolean processContact;

    public boolean assignmentContact;

}
