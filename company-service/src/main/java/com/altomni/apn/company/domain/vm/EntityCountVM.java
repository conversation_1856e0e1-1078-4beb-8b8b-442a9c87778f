package com.altomni.apn.company.domain.vm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.Objects;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Data
public class EntityCountVM implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    private Long count;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EntityCountVM that = (EntityCountVM) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
}
