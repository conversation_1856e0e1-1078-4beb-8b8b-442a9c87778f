package com.altomni.apn.company.repository.user;

import com.altomni.apn.company.domain.user.CreditTransactionCompanyBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;


/**
 * Spring Data JPA repository for the CreditTransactionDTO entity.
 */
@Repository
public interface CreditTransactionCompanyBriefRepository extends JpaRepository<CreditTransactionCompanyBrief, Long> {

    @Modifying
    @Transactional
    @Query(value = "UPDATE credit_transaction c SET c.talent_id=:talentId WHERE c.id=:id", nativeQuery = true)
    void updateTalentId(@Param("talentId") Long talentId, @Param("id") Long id);
}
