package com.altomni.apn.company.service.dto.salesLead;

import com.altomni.apn.company.aop.validation.ContributionSum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-25
*/
@Data
@ContributionSum
@ApiModel(description = "dto for client salesLead")
public class SalesLeadClientDTO implements Serializable {

    @ApiModelProperty(value = "The companyServiceTypes for salesLead.")
    @NotNull
    @UniqueElements
    public List<Long> companyServiceTypes;

    @ApiModelProperty(value = "The am for salesLead.")
    @NotNull
    @UniqueElements
    public List<Long> accountManagers;

    @ApiModelProperty(value = "The salesLeadsOwners for salesLead.")
    @NotNull
    @UniqueElements
    public List<SalesLeadAdministratorDTO> salesLeadsOwners;

    @ApiModelProperty(value = "The salesLeadsBdOwners for salesLead.")
    @NotNull
    @UniqueElements
    public List<SalesLeadAdministratorDTO> salesLeadsBdOwners;

    @ApiModelProperty(value = "The estimatedDealTime for salesLead.")
    public Instant estimatedDealTime;

    @ApiModelProperty(value = "The leadSource for salesLead.")
    public Long leadSource;
}