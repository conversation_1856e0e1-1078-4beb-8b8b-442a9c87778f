package com.altomni.apn.company.domain.company;

import com.altomni.apn.common.domain.ManualAbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.dict.CompanyIndustryRelation;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatusConverter;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Set;

/**
 * A Company.
 */
@ApiModel(description = "Company entities collected. This would provide a dropdown list when user entering job/talent company " +
        "in ATS. This is just used as references.")
@Entity
@Data
@Table(name = "company")
@NoArgsConstructor
@AllArgsConstructor
public class Company extends ManualAbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @ApiModelProperty(value = "The url link to the logo")
    @Column(name = "logo")
    private String logo;

//    @ApiModelProperty(required = true, value = "Company name")
//    @NotNull
//    @Column(name = "name", nullable = false, unique = true)
//    private String name;

    @ApiModelProperty(value = "one or more industries")
    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "company_id")
    private Set<CompanyIndustryRelation> industries;

    @Column(name = "full_business_name")
    private String fullBusinessName;

    @Column(name = "client_level_type")
    private Integer companyClientLevel;

    @Column(name = "active")
    @Convert(converter = AccountCompanyStatusConverter.class)
    private AccountCompanyStatus active;

    @Column(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty(value = "last successful sync to ES")
    @Column(name = "last_sync_time")
    private Instant lastSyncTime;

    @Column(name = "last_edited_time")
    private Instant lastEditedTime;

    @Column(name = "is_need_sync_hr")
    private Boolean isNeedSyncHr;

    public Company(Long id, String fullBusinessName, AccountCompanyStatus active, Long tenantId) {
        this.id = id;
        this.fullBusinessName = fullBusinessName;
        this.active = active;
        this.tenantId = tenantId;
    }

    public static AccountCompanyVO toAccountCompanyVO(Company accountCompany) {
        AccountCompanyVO accountCompanyVO = new AccountCompanyVO();
        ServiceUtils.myCopyProperties(accountCompany, accountCompanyVO);
        return accountCompanyVO;
    }
}