package com.altomni.apn.company.vo.report;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

@Data
public class PerformanceReportSummaryVO {


    @ColumnWidth(20)
    @ExcelProperty(value = "Submission",index = 0)
    private Integer submitToAm;

    @ExcelIgnore
    @JsonIgnore
    private String submitToAmIds;

    @ColumnWidth(20)
    @ExcelProperty(value = "Submit to Client",index = 1)
    private Integer submitToClient;

    @ExcelIgnore
    @JsonIgnore
    private String submitToClientIds;

    @ColumnWidth(20)
    @ExcelProperty(value = "Interview",index = 2)
    private Integer interview;

    @ExcelIgnore
    @JsonIgnore
    private String interviewIds;

    @ColumnWidth(20)
    @ExcelProperty(value = "Offer by Client",index = 3)
    private Integer offerByClient;

    @ExcelIgnore
    @JsonIgnore
    private String offerByClientIds;

    @ColumnWidth(20)
    @ExcelProperty(value = "Offers Accepted ",index = 4)
    private Integer offerAccept;

    @ExcelIgnore
    @JsonIgnore
    private String offerAcceptIds;


    @ColumnWidth(20)
    @ExcelProperty(value = "Total on Boarded",index = 5)
    private Integer onBoard;

    @ExcelIgnore
    @JsonIgnore
    private String onBoardIds;

    public PerformanceReportSummaryVO setSubmitToAm(Integer submitToAm) {
        this.submitToAm = submitToAm;
        return this;
    }

    public PerformanceReportSummaryVO setSubmitToClient(Integer submitToClient) {
        this.submitToClient = submitToClient;
        return this;
    }

    public PerformanceReportSummaryVO setInterview(Integer interview) {
        this.interview = interview;
        return this;
    }

    public PerformanceReportSummaryVO setOfferByClient(Integer offerByClient) {
        this.offerByClient = offerByClient;
        return this;
    }

    public PerformanceReportSummaryVO setOfferAccept(Integer offerAccept) {
        this.offerAccept = offerAccept;
        return this;
    }

    public PerformanceReportSummaryVO setOnBoard(Integer onBoard) {
        this.onBoard = onBoard;
        return this;
    }
}
