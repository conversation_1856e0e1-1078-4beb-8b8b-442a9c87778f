package com.altomni.apn.company.repository.company;

import com.altomni.apn.common.domain.enumeration.company.InvoicingStatus;
import com.altomni.apn.common.domain.enumeration.jobdiva.InvoiceStatusType;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class CompanyClientInvoicingInfoNativeRepository {

    @Resource
    private EntityManager entityManager;

    /**
     * 查询是不是流程中的AM
     *
     * @param companyId
     * @return
     */
    @Transactional(readOnly = true)
    public Boolean selectIsStartAm(Long companyId) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
               select s.* from start s\s
               left join talent_recruitment_process_eliminate trpe\s
                on s.talent_recruitment_process_id = trpe.talent_recruitment_process_id\s
                join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = s.talent_recruitment_process_id
                where s.tenant_id=:talentId and trpe.id is null and kpi.user_id =:userId and s.company_id=:companyId and kpi.user_role=0
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("companyId", companyId);
        dataQuery.setParameter("talentId", SecurityUtils.getTenantId());
        dataQuery.setParameter("userId", SecurityUtils.getUserId());
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty() || mapList == null) {
            return false;
        }
        return true;
    }

    /**
     * 查询是不是客户的AM
     *
     * @param companyId
     * @return
     */
    @Transactional(readOnly = true)
    public Boolean selectIsBDInfoAm(Long companyId) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select bfa.* from business_flow_administrator bfa
                where bfa.company_id=:companyId and bfa.user_id=:userId and bfa.sales_lead_role in (0,3)
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("companyId", companyId);
        dataQuery.setParameter("userId", SecurityUtils.getUserId());
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty() || mapList == null) {
            return false;
        }
        return true;
    }

    /**
     * 查询客户信息是否被使用
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    public Boolean selectClientInfoIsUse(Long id) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select id from invoicing_application_info 
                where client_invoicing_id =:id and status = 1 and 
                invoicing_application_type=1 and invoicing_status !=:invoicingStatus           
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("id", id);
        dataQuery.setParameter("invoicingStatus", InvoicingStatus.VOIDED.toDbValue());
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty() || mapList == null) {
            return false;
        }
        return true;
    }
}
