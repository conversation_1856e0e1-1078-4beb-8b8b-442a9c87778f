package com.altomni.apn.company.repository.talent;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.company.domain.talent.TalentCompanyMigrateBrief;
import com.altomni.apn.company.domain.vm.EntityTalentNameVM;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@Repository
public class TalentServiceRepository {

    @PersistenceContext
    private EntityManager entityManager;

    public EntityTalentNameVM findTalentNameById(Long id) {
        String findUserNameByIdtSql = "SELECT id, first_name, last_name, full_name FROM talent WHERE id = " + id;
        return searchFirstData(findUserNameByIdtSql, EntityTalentNameVM.class);
    }

    public List<EntityTalentNameVM> findTalentNameByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        String joinedIds = ids.stream().map(Object::toString).collect(Collectors.joining(","));
        String findTalentNameByIdtSql = "SELECT id, first_name, last_name, full_name FROM talent WHERE id IN (" + joinedIds + ")";
        return searchData(findTalentNameByIdtSql, EntityTalentNameVM.class);
    }

    public List<TalentCompanyMigrateBrief> findAllByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        String joinedIds = ids.stream().map(Object::toString).collect(Collectors.joining(","));
        String findAllSql = "SELECT * FROM talent WHERE id IN (" + joinedIds + ")";
        return searchData(findAllSql, TalentCompanyMigrateBrief.class);
    }


    private <T> T searchFirstData(String query, Class<T> clazz) {
        entityManager.clear();
        Query dataQ = entityManager.createNativeQuery(query, clazz);
        return (T) dataQ.getResultList().stream().findFirst().orElse(null);
    }

    private <T> List<T> searchData(String query, Class<T> clazz) {
        entityManager.clear();
        Query dataQ = entityManager.createNativeQuery(query, clazz);
        return dataQ.getResultList();
    }
}
