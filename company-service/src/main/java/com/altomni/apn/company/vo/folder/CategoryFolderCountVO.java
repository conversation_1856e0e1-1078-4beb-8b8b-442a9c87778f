package com.altomni.apn.company.vo.folder;

import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.vm.EntityCountVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-19
*/
@AllArgsConstructor
@ApiModel(description = "Vo for categoryFolder")
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class CategoryFolderCountVO implements Serializable {

    @ApiModelProperty(value = "the type for categoryFolder.")
    private CategoryFolderType categoryFolderType;

    @ApiModelProperty(value = "the count for categoryFolder.")
    private Long count;

    public static CategoryFolderCountVO fromEntityCountVM(EntityCountVM entityCountVM) {
        CategoryFolderCountVO categoryFolderCountVO = new CategoryFolderCountVO();
        ServiceUtils.myCopyProperties(entityCountVM, categoryFolderCountVO);
        categoryFolderCountVO.setCategoryFolderType(CategoryFolderType.fromDbValue(entityCountVM.getId()));
        return categoryFolderCountVO;
    }

}
