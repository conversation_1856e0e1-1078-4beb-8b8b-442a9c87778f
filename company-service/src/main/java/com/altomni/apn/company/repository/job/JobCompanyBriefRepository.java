package com.altomni.apn.company.repository.job;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.company.domain.job.JobCompanyBrief;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;


@Repository
public interface JobCompanyBriefRepository extends JpaRepository<JobCompanyBrief, Long> {

//    List<JobCompanyBrief> findAllByCompanyIdAndStatusAndJobTypeIn(Long companyId, JobStatus status, List<JobType> types, Pageable pageable);

    List<JobCompanyBrief> findAllByCompanyIdAndStatus(Long companyId, JobStatus status, Pageable pageable);

    @Query("select j from JobCompanyBrief j where j.companyId =:companyId and j.status=:status and (j.permissionUserId=:puserId or j.permissionTeamId in :teamIds)")
    List<JobCompanyBrief> findAllByCompanyIdAndStatusAndPermissionTeamIdIn(@Param("companyId") Long companyId, @Param("status") JobStatus status, @Param("puserId") Long puserId,@Param("teamIds") List<Long> teamIds, Pageable pageable);

    @Query("select j from JobCompanyBrief j where j.companyId =:companyId and j.status=:status and (j.permissionUserId=:puserId or j.permissionTeamId in :teamIds or j.id in :jobIds)")
    List<JobCompanyBrief> findAllByCompanyIdAndStatusAndPermissionTeamIdIn(@Param("companyId") Long companyId, @Param("status") JobStatus status, @Param("puserId") Long puserId,@Param("teamIds") List<Long> teamIds, @Param("jobIds") Set<Long> jobIds, Pageable pageable);

    List<JobCompanyBrief> findAllByCompanyIdAndStatusAndPermissionUserId(Long companyId, JobStatus status, Long userId, Pageable pageable);

    @Query("select j from JobCompanyBrief j where j.companyId =:companyId and j.status=:status and (j.permissionUserId=:puserId or j.id in :jobIds)")
    List<JobCompanyBrief> findAllByCompanyIdAndStatusAndPermissionUserIdOrIdIn(@Param("companyId") Long companyId,
                                                                                         @Param("status") JobStatus status,
                                                                                         @Param("puserId") Long puserId,
                                                                                         @Param("jobIds") Set<Long> jobIds, Pageable pageable);

    @Query(value = "select j.id from job_project j " +
            "where j.tenant_id =:tenantId", nativeQuery = true)
    Set<Long> findPrivateJobTeamIds(@Param("tenantId") Long tenantId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE job SET sales_lead_id = ?1 WHERE id = ?2", nativeQuery = true)
    void updateJobSalesLeadId(Long salesLeadId, Long id);

}
