package com.altomni.apn.company.domain.enumeration.company;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractStatus enumeration.
 */
public enum CompanyProjectTeamUserRoleType implements ConvertedEnum<Integer> {
    ACCOUNT_MANAGER(0),
    ACCOUNT_COORDINATOR(1),
    DELIVERY_MANAGER(2),
    RECRUITER(3),
    PRIMARY_RECRUITER(4);


    private final Integer dbValue;

    CompanyProjectTeamUserRoleType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<CompanyProjectTeamUserRoleType, Integer> resolver =
        new ReverseEnumResolver<>(CompanyProjectTeamUserRoleType.class, CompanyProjectTeamUserRoleType::toDbValue);

    public static CompanyProjectTeamUserRoleType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}