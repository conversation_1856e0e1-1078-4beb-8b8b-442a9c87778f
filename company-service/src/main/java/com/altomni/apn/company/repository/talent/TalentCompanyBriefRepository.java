package com.altomni.apn.company.repository.talent;

import com.altomni.apn.company.domain.talent.TalentCompanyBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Repository
public interface TalentCompanyBriefRepository extends JpaRepository<TalentCompanyBrief, Long> {

    @Transactional
    @Modifying
    @Query("UPDATE TalentCompanyBrief t SET t.ownedByTenants = ?2 WHERE t.id = ?1")
    void updateTalentCompanyBriefOwnedByTenantsByTalentId(Long talentId, Long ownedByTenants);

    @Query(value = "select t from TalentCompanyBrief t where t.id in ?1")
    List<TalentCompanyBrief> findAllByIdIn(List<Long> talentIds);

}
