package com.altomni.apn.company.web.rest.vm.company;

import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.company.domain.enumeration.business.SalesLeadStatus;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

public class CompanyProspectSearchVM {

    private String companyName;

    public List<EnumRelationDTO> industries;

    private SalesLeadStatus salesLeadStatus;

    private Set<UserBriefDTO> owners;

    private Set<UserBriefDTO> bdOwners;

    private Set<Long> teamUserIds;

    private Integer accountProgress;

    private Set<EnumCompanyServiceType> serviceTypes;

    private Set<String> countries;

    private String createdBy;

    private Long teamId;

    public List<EnumRelationDTO> getIndustries() {
        return industries;
    }

    public void setIndustries(List<EnumRelationDTO> industries) {
        this.industries = industries;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Set<Long> getTeamUserIds() { return teamUserIds; }

    public void setTeamUserIds(Set<Long> teamUserIds) { this.teamUserIds = teamUserIds; }

    public Integer getAccountProgress() {
        return accountProgress;
    }

    public void setAccountProgress(Integer accountProgress) {
        this.accountProgress = accountProgress;
    }

    public Set<EnumCompanyServiceType> getServiceTypes() {
        return serviceTypes;
    }

    public void setServiceTypes(Set<EnumCompanyServiceType> serviceTypes) {
        this.serviceTypes = serviceTypes;
    }

    public Set<String> getCountries() {
        return countries;
    }

    public void setCountries(Set<String> countries) {
        this.countries = countries;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public SalesLeadStatus getSalesLeadStatus() {
        return salesLeadStatus;
    }

    public void setSalesLeadStatus(SalesLeadStatus salesLeadStatus) {
        this.salesLeadStatus = salesLeadStatus;
    }

    public Set<UserBriefDTO> getOwners() {
        return owners;
    }

    public void setOwners(Set<UserBriefDTO> owners) {
        this.owners = owners;
    }

    public Set<UserBriefDTO> getBdOwners() {
        return bdOwners;
    }

    public void setBdOwners(Set<UserBriefDTO> bdOwners) {
        this.bdOwners = bdOwners;
    }
}
