package com.altomni.apn.company.service.skipsubmit.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompany;
import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;
import com.altomni.apn.company.repository.company.CompanyRepository;
import com.altomni.apn.company.repository.skipsubmit.SkipSubmitToAmCompanyRepository;
import com.altomni.apn.company.repository.skipsubmit.SkipSubmitToAmCompanyUserRepository;
import com.altomni.apn.company.service.skipsubmit.SkipSubmitToAmCompanyUserService;
import com.altomni.apn.company.service.user.UserService;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * skipSubmitToAmCompanyUser service
 * <AUTHOR>
 */
@AllArgsConstructor
@Service("skipSubmitToAmCompanyService")
public class SkipSubmitToAmCompanyUserServiceImpl implements SkipSubmitToAmCompanyUserService {

    private final SkipSubmitToAmCompanyUserRepository skipSubmitUserRepository;

    private final SkipSubmitToAmCompanyRepository skipSubmitToAmCompanyRepository;

    private final CompanyRepository companyRepository;

    private final UserService userService;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final CompanyApiPromptProperties companyApiPromptProperties;

    @Override
    public List<SkipSubmitToAmCompany> findAll() {
        return skipSubmitToAmCompanyRepository.findAllByTenantId(SecurityUtils.getTenantId());
    }

    @Override
    public List<SkipSubmitToAmCompanyUser> findAllByCompanyId(Long companyId) {
        checkCompanyPermission(companyId);
        return skipSubmitUserRepository.findAllByCompanyId(companyId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SkipSubmitToAmCompanyUser> replaceUsers(Long companyId, List<Long> userIds) {
        List<SkipSubmitToAmCompanyUser> skipSubmitToAmCompanyUsers = new ArrayList<>();
        checkCompanyPermission(companyId);
        skipSubmitUserRepository.deleteAllByCompanyId(companyId);
        if (CollUtil.isNotEmpty(userIds)) {
            checkUserPermission(userIds);
            for (Long userId : userIds) {
                SkipSubmitToAmCompanyUser skipSubmitToAmCompanyUser = new SkipSubmitToAmCompanyUser();
                skipSubmitToAmCompanyUser.setCompanyId(companyId);
                skipSubmitToAmCompanyUser.setTenantId(SecurityUtils.getTenantId());
                skipSubmitToAmCompanyUser.setUserId(userId);
                skipSubmitToAmCompanyUsers.add(skipSubmitToAmCompanyUser);
            }
            skipSubmitUserRepository.saveAll(skipSubmitToAmCompanyUsers);
        }
        return skipSubmitToAmCompanyUsers;
    }

    private void checkCompanyPermission(Long companyId) {
        Company company = companyRepository.findById(companyId).orElse(null);
        if (ObjectUtil.isNull(company)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SKIPSUBMIT_CHECKCOMPANYPERMISSION_NOTFOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(companyId),companyApiPromptProperties.getCompanyService()));
        }
        if (!company.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SKIPSUBMIT_CHECKCOMPANYPERMISSION_NOTPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
    }

    private void checkUserPermission(List<Long> userIds) {
        List<UserBriefDTO> users = userService.getAllByIdIn(new ArrayList<>(userIds)).getBody();
        String activeJson = CollUtil.isEmpty(users)? "": users.stream().map(UserBriefDTO::getId).sorted().map(String::valueOf).collect(Collectors.joining(","));
        String allJson = userIds.stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
        if (!Objects.equals(activeJson, allJson)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.SKIPSUBMIT_CHECKUSERPERMISSION_NOTPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),companyApiPromptProperties.getCompanyService()));
        }
    }

}
