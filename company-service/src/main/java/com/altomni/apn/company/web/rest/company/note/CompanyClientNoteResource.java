package com.altomni.apn.company.web.rest.company.note;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.service.company.note.CompanyClientNoteService;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteDTO;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteSearchDTO;
import com.altomni.apn.company.service.dto.note.CompanyClientNoteUpdateDTO;
import com.altomni.apn.company.vo.note.CompanyClientNoteDetailVO;
import com.altomni.apn.company.vo.note.CompanyClientNoteVO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URISyntaxException;

/**
 * REST controller for managing ClientNote.
 */
@Api(tags = {"APN-Company-ClientNote"})
@Slf4j
@RestController
@RequestMapping("/api/v3/company")
public class CompanyClientNoteResource {

    @Resource
    private CompanyClientNoteService companyClientNoteService;

    /**
     * {@code POST  /clientNote} : Create a new company client note.
     *
     * @param companyClientNoteDTO the companyClientNoteDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new CompanyClientNoteVO, or with status {@code 400 (Bad Request)} if the company progress note has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("/client-notes")
    @NoRepeatSubmit
    public ResponseEntity<CompanyClientNoteVO> createClientNote(@RequestBody @Valid CompanyClientNoteDTO companyClientNoteDTO) {
        log.info("[APN: Company Client Note @{}] REST request to create client company note : {}", SecurityUtils.getUserId(), companyClientNoteDTO);
        CompanyClientNoteVO companyClientNoteVO = companyClientNoteService.createClientNote(companyClientNoteDTO);
        return new ResponseEntity<>(companyClientNoteVO, HttpStatus.CREATED);
    }

    /**
     *
     * @param id
     * @param companyClientNoteUpdateDTO
     * @return
     */
    @PutMapping("/client-notes/{noteId}")
    @NoRepeatSubmit
    public ResponseEntity<CompanyClientNoteVO> updateClientNote(@PathVariable("noteId") Long id, @RequestBody @Valid CompanyClientNoteUpdateDTO companyClientNoteUpdateDTO) {
        log.info("[APN: Company Client Note @{}] REST request to update client company note : {}", SecurityUtils.getUserId(), companyClientNoteUpdateDTO);
        CompanyClientNoteVO companyClientNoteVO = companyClientNoteService.updateClientNote(id, companyClientNoteUpdateDTO);
        return new ResponseEntity<>(companyClientNoteVO, HttpStatus.CREATED);
    }

    /**
     * {@code Post  /client-notes/search : get the company clientNote by company Id.
     *
     * @param companyId the company Id.

     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of contracts in body.
     */

    @PostMapping("/client-notes/search")
    public ResponseEntity<String> searchCompanyClientNote(@RequestBody @Valid CompanyClientNoteSearchDTO companyClientNoteSearchDTO, @PageableDefault @SortDefault(sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[APN: Company Client Note @{}] REST request to search Company Client Note list: {}", SecurityUtils.getUserId(), companyClientNoteSearchDTO);
//        Page<CompanyClientNote> companyProgressNotePage = companyClientNoteService.searchCompanyClientNote(companyClientNoteSearchDTO, pageable);
//        List<CompanyClientNoteVO> result = companyClientNoteService.toVo(companyProgressNotePage.getContent());
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        String result = companyClientNoteService.searchCompanyClientNote(companyClientNoteSearchDTO, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(result);
    }

    /**
     *
     * @param id
     * @return
     */
    @DeleteMapping("/client-notes/{noteId}")
    @NoRepeatSubmit
    public ResponseEntity<HttpStatus> deleteClientNote(@PathVariable("noteId") Long id) {
        log.info("[APN: Company Client Note @{}] REST request to delete a client company note : {}", SecurityUtils.getUserId(), id);
        companyClientNoteService.deleteClientNote(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    /**
     *
     * @param id
     * @return
     */
    @GetMapping("/client-notes/{noteId}")
    public ResponseEntity<CompanyClientNoteDetailVO> getClientNote(@PathVariable("noteId") Long id) {
        log.info("[APN: Company Client Note @{}] REST request to get a client company note : {}", SecurityUtils.getUserId(), id);
        return ResponseEntity.ok().body(companyClientNoteService.getClientNote(id));
    }

}
