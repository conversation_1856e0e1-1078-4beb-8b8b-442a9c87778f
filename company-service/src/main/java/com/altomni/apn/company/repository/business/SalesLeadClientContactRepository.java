package com.altomni.apn.company.repository.business;

import com.altomni.apn.common.dto.company.ClientContactBriefInfoDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.domain.vm.CompanyContactVM;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;


@Repository
public interface SalesLeadClientContactRepository extends JpaRepository<SalesLeadClientContact, Long>, JpaSpecificationExecutor<SalesLeadClientContact> {

//    List<SalesLeadClientContact> findAllByNameContaining(String keyWord);

//    @Modifying
//    @Query(value = "update SalesLeadClientContact set  companyAddressId = null where companyAddressId=:id")
//    void deleteClientContactCompanyAddressByCompanyAddressId(@Param("id") Long id);
//
//    @Query(value = "select new com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactVM(s.id, s.name, s.title, s.email, s.phone, s.wechat, s.contactCategory, s.otherCategory, s.department, s.remark, s.active, s.businessGroup, s.businessUnit, s.companyAddressId, s.linkedinProfile, s.lastContactDate, s.tenantId) from SalesLeadClientContact s where s.id in :ids")
//    List<SalesLeadClientContactVM> findAllClientContactInClientContactIds(@Param("ids") List<Long> ids);
//
//    @Query(value = "select new com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactVM(s.id, s.name, s.title, s.email, s.phone, s.wechat, s.contactCategory, s.otherCategory, s.department, s.remark, s.active, s.businessGroup, s.businessUnit, s.companyAddressId, s.linkedinProfile, s.lastContactDate, s.tenantId) from SalesLeadClientContact s where s.id=:id")
//    Optional<SalesLeadClientContactVM> findSalesLeadClientContactDetailById(@Param("id") Long id);
//
//    List<SalesLeadClientContact> findAllByIdIn(@Param("ids") List<Long> ids);
//
//    @Query(value = "select new com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactBriefVM(s.id, s.name, s.firstName, s.lastName, s.email) from SalesLeadClientContact s where s.id in :contactIds and s.email is not null")
//    List<SalesLeadClientContactBriefVM> findBriefClientContactsByIdIn(@Param("contactIds") List<Long> contactIds);


    Optional<SalesLeadClientContact> findByEsIdAndCompanyId(String esId, Long companyEntityId);

//    @Query(value = "select * from company_sales_lead_client_contact s left join company_sales_lead_connect_client_contact sc " +
//            "on sc.client_contact_id = s.id where s.first_name = ?1 and s.last_name = ?2 and s.email = ?3 and sc.company_id = ?4 ", nativeQuery = true)
//    List<SalesLeadClientContact> distinctNameEmail(String firstName, String lastName, String email, Long companyId);
//
//    @Query(value = "select * from company_sales_lead_client_contact s left join company_sales_lead_connect_client_contact sc " +
//            "on sc.client_contact_id = s.id where s.first_name = ?1 and s.last_name = ?2 and s.email = ?3 and sc.company_id = ?4 and s.id <>?4", nativeQuery = true)
//    List<SalesLeadClientContact> distinctNameEmail(String name, String email, Long companyId, Long id);

//    @Query(value = "SELECT created_by FROM company_sales_lead_client_contact where id=?1", nativeQuery = true)
//    String findClientContactCreatedBy(Long id);


    Optional<SalesLeadClientContact> findByIdAndCompanyId(Long id, Long companyId);

    List<SalesLeadClientContact> findAllByCompanyIdAndTalentIdAndActive(Long companyId, Long talentId, boolean active);

    Page<SalesLeadClientContact> findAllByCompanyId(Long companyId, Pageable pageable);

    Page<SalesLeadClientContact> findAll(Pageable pageable);

    List<SalesLeadClientContact> findAllByCompanyId(Long companyId);

    List<SalesLeadClientContact> findAllByTenantIdAndCompanyId(Long tenantId, Long companyId);

    List<SalesLeadClientContact> findAllByTenantIdAndCompanyIdAndPermissionUserId(Long tenantId, Long companyId, Long userId);

    List<SalesLeadClientContact> findAllByTenantIdAndCompanyIdAndPermissionTeamIdIn(Long tenantId, Long companyId, Collection<Long> teamIds);

    @Query(value =
            "select new com.altomni.apn.common.dto.company.ClientContactBriefInfoDTO(sc.id, t.firstName, t.lastName, t.id) " +
                    "from SalesLeadClientContact sc " +
                    "inner join TalentV3 t on t.id = sc.talentId " +
                    "where sc.id=:id AND sc.companyId=:companyId")
    Optional<ClientContactBriefInfoDTO> findBrieClientContactById(@Param("companyId") Long companyId, @Param("id") Long id);

    @Query(value = "select new com.altomni.apn.company.domain.vm.CompanyContactVM(sc.id, sc.contactCategory, sc.active, sc.lastFollowUpTime, sc.tenantId, sc.talentId, sc.companyId, t.fullName) " +
                    "from SalesLeadClientContact sc " +
                    "inner join TalentV3 t on t.id = sc.talentId " +
                    "where sc.tenantId = :tenantId")
    List<CompanyContactVM> findByTenantIdWithLimit(@Param("tenantId") Long tenantId, Pageable pageable);

    @Query(value = "select new com.altomni.apn.company.domain.vm.CompanyContactVM(sc.id, sc.contactCategory, sc.active, sc.lastFollowUpTime, sc.tenantId, sc.talentId, sc.companyId, t.fullName) " +
            "from SalesLeadClientContact sc " +
            "inner join TalentV3 t on t.id = sc.talentId " +
            "where sc.tenantId = :tenantId and sc.active = :active")
    List<CompanyContactVM> findByTenantIdAndActiveWithLimit(@Param("tenantId") Long tenantId, @Param("active") Boolean active, Pageable pageable);

    @Query(value = "select new com.altomni.apn.company.domain.vm.CompanyContactVM(sc.id, sc.contactCategory, sc.active, sc.lastFollowUpTime, sc.tenantId, sc.talentId, sc.companyId, t.fullName) " +
            "from SalesLeadClientContact sc " +
            "inner join TalentV3 t on t.id = sc.talentId " +
            "where sc.tenantId = :tenantId and t.fullName like concat('%', :name, '%')")
    List<CompanyContactVM> findByTenantIdAndNameLikeWithLimit(@Param("tenantId") Long tenantId, @Param("name") String name, Pageable pageable);

    @Query(value = "select new com.altomni.apn.company.domain.vm.CompanyContactVM(sc.id, sc.contactCategory, sc.active, sc.lastFollowUpTime, sc.tenantId, sc.talentId, sc.companyId, t.fullName) " +
            "from SalesLeadClientContact sc " +
            "inner join TalentV3 t on t.id = sc.talentId " +
            "where sc.tenantId = :tenantId and sc.active = :active and t.fullName like concat('%', :name, '%')")
    List<CompanyContactVM> findByTenantIdAndActiveAndNameLikeWithLimit(@Param("tenantId") Long tenantId, @Param("active") Boolean active, @Param("name") String name, Pageable pageable);

    @Query(value = """
            select new com.altomni.apn.company.domain.vm.CompanyContactVM(sc.id, sc.contactCategory, sc.active, sc.lastFollowUpTime, sc.tenantId, sc.talentId, sc.companyId, t.fullName, c.fullBusinessName)
            from SalesLeadClientContact sc inner join Company c on sc.companyId = c.id
            inner join TalentV3 t on t.id = sc.talentId
            left join TalentOwnership ownership on ownership.talentId = t.id
            where sc.tenantId = :tenantId
            and (ownership.userId = -1 or ownership.userId = :userId or sc.createdBy = :uid)
            group by sc.id
            """)
    List<CompanyContactVM> findAllByTenantId(@Param("tenantId") Long tenantId, @Param("userId") Long userId, @Param("uid") String uid);

    @Query(value = """
            select new com.altomni.apn.company.domain.vm.CompanyContactVM(sc.id, sc.contactCategory, sc.active, sc.lastFollowUpTime, sc.tenantId, sc.talentId, sc.companyId, t.fullName, c.fullBusinessName)
            from SalesLeadClientContact sc inner join Company c on sc.companyId = c.id
            inner join TalentV3 t on t.id = sc.talentId
            left join TalentOwnership ownership on ownership.talentId = t.id
            where sc.tenantId = :tenantId and sc.active = :active
            and (ownership.userId = -1 or ownership.userId = :userId or sc.createdBy = :uid)
            group by sc.id
            """)
    List<CompanyContactVM> findAllByTenantIdAndActive(@Param("tenantId") Long tenantId, @Param("active") Boolean active, @Param("userId") Long userId, @Param("uid") String uid);

    List<SalesLeadClientContact> findAllByTalentIdInAndActive(List<Long> talentIds, Boolean active);

    List<SalesLeadClientContact> findAllByTalentIdAndActive(Long talentId, Boolean active);

    List<SalesLeadClientContact> findAllByTalentId(Long talentId);

    List<SalesLeadClientContact> findAllByTenantIdAndTalentId(Long tenantId, Long talentId);

    @Query(value = "select t.* from company_sales_lead_client_contact t where t.talent_id = ?1", nativeQuery = true)
    List<SalesLeadClientContact> findClientContactByTalentId(Long talentId);


    @Query(value = "select company_id from company_sales_lead_client_contact where talent_id = ?1", nativeQuery = true)
    List<Long> findTalentBindCompany(Long talentId);

    List<SalesLeadClientContact> findAllByActiveAndCompanyId(Boolean active, Long companyId);

    SalesLeadClientContact findFirstByCompanyIdOrderByActiveDescCreatedDateDesc(Long companyId);

    List<SalesLeadClientContact> findTop1ByCompanyIdInOrderByActiveDescCreatedDateDesc(List<Long> companyIds);

    List<SalesLeadClientContact> findAllByCompanyIdInAndActive(List<Long> companyIds, Boolean active);

    List<SalesLeadClientContact> findAllByCompanyIdIn(List<Long> companyIds);

    @Query("select distinct c.talentId from SalesLeadClientContact c where c.tenantId = ?1")
    List<Long> findTalentIdByTenantId(Long tenantId);

    @Query("select distinct c.talentId from SalesLeadClientContact c where c.tenantId = ?1 and c.talentId in ?2")
    List<Long> findTalentIdByTenantIdAndTalentIdIn(Long tenantId, List<Long> talentIds);

    @Query(value = "select new com.altomni.apn.company.domain.vm.CompanyContactVM(sc.id, sc.contactCategory, sc.active, sc.lastFollowUpTime, sc.tenantId, sc.talentId, sc.companyId, t.fullName, sc.crmContactId) " +
            "from SalesLeadClientContact sc " +
            "inner join TalentV3 t on t.id = sc.talentId " +
            "where sc.tenantId = ?1 and sc.companyId in ?2")
    List<CompanyContactVM> findAllByTenantIdAndCompanyIdIn(Long tenantId, List<Long> companyIds);

    @Modifying
    @Transactional
    @Query(value = "update company_sales_lead_client_contact  slcc set slcc.inactived = ?1 where approver_id = ?2 and tenant_id = ?3",nativeQuery = true)
    void updateInactivedByApproverIdAndTenantId(Boolean inactived, Long approverId, Long tenantId);

    @Query(value = " select count(1) from company_sales_lead_client_contact where approver_id = ?1 and status = true", nativeQuery = true)
    Integer countActiveClient(Long approverId);

    List<SalesLeadClientContact> findAllByCrmContactIdIn(List<Long> contactIds);

    List<SalesLeadClientContact> findByCrmContactIdAndTenantId(Long contactIds,Long tenantId);

    Long countAllByCompanyIdAndActive(Long companyId, Boolean active);

    @Query("SELECT new com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO(slcc.talentId, true) " +
            "FROM SalesLeadClientContact slcc " +
            "WHERE slcc.talentId IN :talentIds AND slcc.active = :active " +
            "GROUP BY slcc.talentId")
    List<TalentClientContactStatusDTO> findClientContactStatusByTalentIdInAndActive(@Param("talentIds") List<Long> talentIds, @Param("active") Boolean active);

    @Query("SELECT new com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO(slcc.talentId, slcc.crmContactId) " +
            "FROM SalesLeadClientContact slcc " +
            "WHERE slcc.crmContactId IN :contactIds AND slcc.active = :active " +
            "GROUP BY slcc.crmContactId")
    List<TalentClientContactRelationDTO> findTalentIdsByClientContactIds(@Param("contactIds") Set<Long> contactIds, @Param("active") Boolean active);

    @Query("SELECT new com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO(slcc.talentId, slcc.crmContactId) " +
            "FROM SalesLeadClientContact slcc " +
            "WHERE slcc.talentId IN :talentIds AND slcc.active = :active " +
            "GROUP BY slcc.talentId")
    List<TalentClientContactRelationDTO> findContactIdsByTalentIds(@Param("talentIds") Set<Long> contactIds, @Param("active") Boolean active);

    @Query(value = """
        select approver_id
         from company_sales_lead_client_contact where crm_contact_id=?1 and tenant_id=?2 and company_id=?3
        """, nativeQuery = true)
    List<Long> findTimesheetManagerByContactId(Long contactId,Long tenantId,Long companyId);

    @Query(value = """
        select jcc.id from job_company_contact_relation jcc
      inner join job j on j.id = jcc.job_id
      where client_contact_id = ?1 and j.tenant_id=?2
        """, nativeQuery = true)
    List<Long> findJobContactByContactId(Long contactId,Long tenantId);

    @Query(value = """
         select oci.id from talent_recruitment_process_onboard_client_info oci\s
         inner join talent_recruitment_process icp on icp.id = oci.talent_recruitment_process_id
         where client_contact_id = ?1 and icp.tenant_id=?2
        union all
        select s.id from start s where client_contact_id=?1 and tenant_id=?2
        """, nativeQuery = true)
    List<Long> findProcessContactByContactId(Long contactId,Long tenantId);

    @Query(value = """
        select id from invoice where client_contact_id = ?1 and tenant_id=?2
        """, nativeQuery = true)
    List<Long> findInvoiceContactByContactId(Long contactId,Long tenantId);

    @Query(value = """
        select assignment_id from assignment_bill_info where contact_id=?1
        """, nativeQuery = true)
    List<Long> findAssignmentContactByContactId(Long contactId);
}