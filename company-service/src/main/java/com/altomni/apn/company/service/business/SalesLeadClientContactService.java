package com.altomni.apn.company.service.business;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.dto.company.*;
import com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.company.service.dto.contact.*;
import com.altomni.apn.company.vo.contact.CompanyContactCheckDataExistVO;
import com.altomni.apn.company.vo.contact.CompanyContactVO;
import com.altomni.apn.company.vo.contact.SalesLeadClientContactVO;
import com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactProfile;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.List;
import java.util.Set;

public interface SalesLeadClientContactService {
//
//    SalesLeadClientContactVO save(SalesLeadClientContactDTO salesLeadClientContactDTO);
//
//    SalesLeadClientContactVO updateSalesLeadClientContact(Long id, CrmContactDTO companyContactDTO);
//
//    SalesLeadClientContactVO updateSalesLeadClientContactStatus(CompanyContactStatusDTO companyContactStatusDTO);

    TalentDTOV3 verifiedContact(CrmContactDTO crmContactDTO);

    List<TalentDTOV3> verifiedContactWithDetail(CrmContactDTO crmContactDTO);

    CompanyContactVO createSalesLeadContact(CrmContactDTO crmContactDTO, HttpHeaders headers);

    JSONObject saveClientContact(JSONObject jsonObject);

    List<SalesLeadClientContact> searchSalesLeadClientContact(CompanyContactSearchDTO companyContactSearchDTO, HttpHeaders headers);

    List<SalesLeadClientContactVO> searchTenantSalesLeadClientContact(CompanyContactTenantSearchDTO companyContactTenantSearchDTO);

    List<SalesLeadClientContactVO> queryTenantSalesLeadClientContactList(Boolean active);

    List<SalesLeadClientContactVO> toVo(List<SalesLeadClientContact> salesLeadClientContactList, HttpHeaders headers);

    List<CompanyContactVO> checkContactDuplicatedTalent(CompanyContactCheckDuplicatedTalentDTO companyContactCheckDuplicatedTalentDTO) throws IOException;

//    SalesLeadClientContactDTO update(SalesLeadClientContactDTO salesLeadClientContactDTO);

//    List<SalesLeadClientContactVM> findAllByCompanyId(Long companyId);

    SalesLeadClientContactVO findById(Long id, HttpHeaders headers);

    SalesLeadClientContactProfile findSalesLeadClientContactProfile(Long id);

    List<SalesLeadClientContactVO> findByIdIn(List<Long> ids);

    SalesLeadClientContact findInfoById(Long id);

//    List<SalesLeadClientContactDTO> batchSave(List<SalesLeadClientContactDTO> salesLeadClientContactDTO);

//    SalesLeadClientContactVO createClientContactFromCommonPool(CreateSalesLeadClientContactFromTalentInput contact);

//    Integer contactNameSplit();

    ApproverVO approver(ApproverDTO approverDTO);

    List<ClientContactDTO> findBriefContactByIdAndReceiveEmail(List<Long> contactIds);

    Boolean hasApproverPermission(Long companyId);

    ClientContactBriefInfoDTO findClientContactBriefById(Long companyId, Long clientContactId);

    Long queryContactLocationIdByTalentId(Long talentId);

    List<SalesLeadClientContactVO> queryAllContactByTalentId(Long talentId);

    List<SalesLeadClientContactVO> findAllContactByCompany(List<Long> companyIds);

    void updateContactLocationIdByTalentId(Long talentId, Long locationId);

    List<TalentClientContactStatusDTO> getTalentClientContactStatus(List<Long> talentIds);

    List<TalentClientContactRelationDTO> getTalentClientContactStatusByContactIds(Set<Long> contactIds);

    List<TalentClientContactRelationDTO> getTalentClientContactStatusByTalentIds(Set<Long> talentIds);

    CompanyContactCheckDataExistVO contactCheckDataExist(CrmContactCheckDataExistDTO crmContactDTO);

//    SalesLeadClientContactVO saveFromTalent(CreateSalesLeadClientContactFromTalentInput input);
}
