package com.altomni.apn.company.repository.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.vm.EntityCompnayRelateTalentVM;
import com.altomni.apn.company.domain.vm.EntityCountVM;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class SalesLeadServiceRepository {

    @PersistenceContext
    private EntityManager entityManager;

    public EntityCompnayRelateTalentVM findFirstExistContact(Long tenantId, Long companyId) {
        String findFirstContactByCompanyIdtSql = "SELECT DISTINCT company_id, talent_id\n" +
                "FROM company_sales_lead_client_contact\n" +
                "WHERE talent_id IN (\n" +
                "    SELECT talent_id\n" +
                "    FROM company_sales_lead_client_contact\n" +
                "    WHERE tenant_id = " + tenantId + " AND company_id = " + companyId + " AND `status` = 1\n" +
                ")\n" +
                "AND tenant_id = " + tenantId + " AND company_id != " + companyId + " AND `status` = 1 LIMIT 1";
        return searchFirstData(findFirstContactByCompanyIdtSql, EntityCompnayRelateTalentVM.class);
    }

    public List<EntityCountVM> countSalesLeadByCompanyId(List<Long> companyIds, CategoryFolderType category) {
        StringBuffer countSql = new StringBuffer();
        if (category.equals(CategoryFolderType.PROSPECT_ALL_COMPANY)) {
            countSql.append("SELECT DISTINCT company_id id, COUNT(1) count FROM account_business WHERE company_id IN ?1 GROUP BY company_id");
        } else if (category.equals(CategoryFolderType.CLIENT_ALL_COMPANY)){
            countSql.append("SELECT DISTINCT company_id id, COUNT(1) count FROM account_business WHERE company_id IN ?1 GROUP BY company_id");
        } else {
            return new ArrayList<>();
        }
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, companyIds);
        return searchData(countSql.toString(), EntityCountVM.class, paramMap);
    }

    private <T> T searchFirstData(String query, Class<T> clazz) {
        Query dataQ = entityManager.createNativeQuery(query, clazz);
        return (T) dataQ.getResultList().stream().findFirst().orElse(null);
    }

    private <T> List<T> searchData(String query, Class<T> clazz) {
        Query dataQ = entityManager.createNativeQuery(query, clazz);
        return dataQ.getResultList();
    }

    private <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        entityManager.clear();
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private Integer checkInList(Map<Integer, Object> map) {
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new RuntimeException("query sql in condition list > 1000 more than 1");
        }
        return keyList.get(0);
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchData(queryStr, clazz, paramMap);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }


    private <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        return query.getResultList();
    }
}
