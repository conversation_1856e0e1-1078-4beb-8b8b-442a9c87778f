package com.altomni.apn.company.service.company.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.enumeration.enums.CompanyAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.company.CompanyClientInvoicingInfoVO;
import com.altomni.apn.company.config.env.CompanyApiPromptProperties;
import com.altomni.apn.company.domain.company.CompanyClientInvoicingInfo;
import com.altomni.apn.company.repository.company.CompanyClientInvoicingInfoNativeRepository;
import com.altomni.apn.company.repository.company.CompanyClientInvoicingInfoRepository;
import com.altomni.apn.company.service.company.CompanyClientInvoicingInfoService;
import com.altomni.apn.company.service.dto.CompanyClientInvoicingInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class CompanyClientInvoicingInfoServiceImpl implements CompanyClientInvoicingInfoService {

    @Resource
    CompanyClientInvoicingInfoRepository companyClientInvoicingInfoRepository;

    @Resource
    CompanyClientInvoicingInfoNativeRepository companyClientInvoicingInfoNativeRepository;

    @Resource
    CommonRedisService commonRedisService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CompanyApiPromptProperties companyApiPromptProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(CompanyClientInvoicingInfoDTO dto) {
        checkParam(dto);
        checkPermission(dto.getCompanyId());
        checkSocialCreditCodeAndClientName(dto, 1);
        CompanyClientInvoicingInfo invoicingInfo = new CompanyClientInvoicingInfo();
        BeanUtils.copyProperties(dto, invoicingInfo);
        invoicingInfo.setTenantId(SecurityUtils.getTenantId());
        companyClientInvoicingInfoRepository.save(invoicingInfo);
        log.info("[CompanyClientInvoicingInfo] save info,{}", JSONUtil.toJsonStr(invoicingInfo));

        commonRedisService.set(String.format(RedisConstants.INVOICE_PREFERENCE, SecurityUtils.getTenantId(), SecurityUtils.getUserId()), "1");
    }

    public void checkPermission(Long companyId) {
        if (!SecurityUtils.isAdmin()) {
            if (!companyClientInvoicingInfoNativeRepository.selectIsBDInfoAm(companyId)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_FINDAMREPORT_NOPERMISSON.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
            }
        }
    }

    public void checkParam(CompanyClientInvoicingInfoDTO dto) {
        if (dto.getCompanyId() == null || StringUtils.isBlank(dto.getPhone())
                || StringUtils.isBlank(dto.getClientName()) || StringUtils.isBlank(dto.getSocialCreditCode())
                || StringUtils.isBlank(dto.getInvoicingAddress()) || StringUtils.isBlank(dto.getBankName())
                || StringUtils.isBlank(dto.getBankAccount())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }

        if (dto.getSocialCreditCode().length() != 18) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_CLIENT_INVOICING_SOCIALCREDITCODE_LENGTH.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
    }

    /**
     * 校验客户名称和和社会编码
     *
     * @param dto
     * @param operateType 1 save 2 update
     */
    public void checkSocialCreditCodeAndClientName(CompanyClientInvoicingInfoDTO dto, Integer operateType) {
        List<CompanyClientInvoicingInfo> infos = companyClientInvoicingInfoRepository.findByClientNameAndTenantId(dto.getClientName().trim(),SecurityUtils.getTenantId());
        checkExist(infos, operateType, dto, CompanyAPIMultilingualEnum.COMPANY_CLIENT_INVOICING_CLIENTNAME_EXIST.getKey());
        List<CompanyClientInvoicingInfo> infoSocialCreditCode = companyClientInvoicingInfoRepository.findBySocialCreditCodeAndTenantId(dto.getSocialCreditCode().trim(),SecurityUtils.getTenantId());
        checkExist(infoSocialCreditCode, operateType, dto, CompanyAPIMultilingualEnum.COMPANY_CLIENT_INVOICING_SOCIALCREDITCODE_EXIST.getKey());
        List<CompanyClientInvoicingInfo> infoBankAccount = companyClientInvoicingInfoRepository.findByBankAccountAndTenantId(dto.getBankAccount().trim(),SecurityUtils.getTenantId());
        checkExist(infoBankAccount, operateType, dto, CompanyAPIMultilingualEnum.COMPANY_CLIENT_INVOICING_BANKACCOUNT_EXIST.getKey());
    }

    public void checkExist(List<CompanyClientInvoicingInfo> infos, Integer operateType, CompanyClientInvoicingInfoDTO dto, String flag) {
        if (infos != null && !infos.isEmpty()) {
            if (operateType == 1) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(flag, CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
            } else {
                if (infos.size() > 1) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(flag, CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
                }
                CompanyClientInvoicingInfo info = infos.get(0);
                if (!info.getId().equals(dto.getId())) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(flag, CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
                }
            }
        }
    }

    public void checkInvoicingUse(Long id) {
        if (companyClientInvoicingInfoNativeRepository.selectClientInfoIsUse(id)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.COMPANY_CLIENT_INVOICING_CLIENTNAME_ISUSE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CompanyClientInvoicingInfoDTO dto) {
        checkParam(dto);
        checkPermission(dto.getCompanyId());
        checkSocialCreditCodeAndClientName(dto, 2);
        checkInvoicingUse(dto.getId());

        Optional<CompanyClientInvoicingInfo> infoOpt = companyClientInvoicingInfoRepository.findById(dto.getId());
        if (!infoOpt.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_COMMON_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }

        CompanyClientInvoicingInfo info = infoOpt.get();

        if (!info.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_FINDAMREPORT_NOPERMISSON.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
        }

        info.setPhone(dto.getPhone());
        info.setClientName(dto.getClientName());
        info.setCompanyId(dto.getCompanyId());
        info.setBankName(dto.getBankName());
        info.setBankAccount(dto.getBankAccount());
        info.setInvoicingAddress(dto.getInvoicingAddress());
        info.setMailingAddress(dto.getMailingAddress());
        info.setSocialCreditCode(dto.getSocialCreditCode());
        companyClientInvoicingInfoRepository.save(info);
        log.info("[CompanyClientInvoicingInfo] modify info,{}", JSONUtil.toJsonStr(info));
    }

    @Override
    public List<CompanyClientInvoicingInfoVO> findByCompanyId(Long companyId) {
        List<CompanyClientInvoicingInfo> infos = companyClientInvoicingInfoRepository.findAllByCompanyId(companyId);
        List<CompanyClientInvoicingInfoVO> voList = new ArrayList<>();
        infos.forEach(v -> {
            CompanyClientInvoicingInfoVO bean = new CompanyClientInvoicingInfoVO();
            BeanUtils.copyProperties(v, bean);
            bean.setInvoicingArea(1);
            voList.add(bean);
        });
        return voList;
    }

    @Override
    public CompanyClientInvoicingInfoVO findById(Long id) {
        CompanyClientInvoicingInfo info = companyClientInvoicingInfoRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("Cannot find invoice info"));
        CompanyClientInvoicingInfoVO bean = new CompanyClientInvoicingInfoVO();
        BeanUtils.copyProperties(info, bean);
        return bean;
    }

    @Override
    public String getInvoicingPreference() {
        String val = commonRedisService.get(String.format(RedisConstants.INVOICE_PREFERENCE, SecurityUtils.getTenantId(), SecurityUtils.getUserId()));
        if (StringUtils.isNotBlank(val)) {
            return val;
        }
        return "1";
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        log.info("[CompanyClientInvoicingInfo] delete info, id:{},userId:{}", id, SecurityUtils.getUserId());
        checkInvoicingUse(id);
        Optional<CompanyClientInvoicingInfo> companyClientInvoicingInfoOpt = companyClientInvoicingInfoRepository.findById(id);
        if (companyClientInvoicingInfoOpt.isPresent()) {
            CompanyClientInvoicingInfo info = companyClientInvoicingInfoOpt.get();
            if (!info.getTenantId().equals(SecurityUtils.getTenantId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CompanyAPIMultilingualEnum.AM_FINDAMREPORT_NOPERMISSON.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), companyApiPromptProperties.getCompanyService()));
            }
            companyClientInvoicingInfoRepository.delete(info);
            log.info("[CompanyClientInvoicingInfo] delete info,{}", JSONUtil.toJsonStr(info));
        }
    }
}
