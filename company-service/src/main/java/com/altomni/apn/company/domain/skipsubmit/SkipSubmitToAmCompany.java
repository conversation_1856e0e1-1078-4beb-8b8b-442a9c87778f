package com.altomni.apn.company.domain.skipsubmit;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A company who can configure user to skip SUBMIT TO AM.
 * Skip SUBMIT TO AM means the application created by these users will update to SUBMITTED TO CLIENT directly.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "skip_submit_to_am_company")
public class SkipSubmitToAmCompany extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5628360071812235032L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The tenant id the team belongs to.")
    @Column(name = "tenant_id")
    @NotNull
    private Long tenantId;

    @ApiModelProperty(value = "The company id the team belongs to.")
    @Column(name = "company_id")
    @NotNull
    private Long companyId;
}
