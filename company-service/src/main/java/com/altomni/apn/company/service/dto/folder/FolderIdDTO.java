package com.altomni.apn.company.service.dto.folder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.UniqueElements;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Data
@ApiModel(description = "id for folders")
public class FolderIdDTO implements Serializable {

    @ApiModelProperty(value = "The id for folders.")
    @NotNull
    @UniqueElements
    private List<Long> folderIds;

}
