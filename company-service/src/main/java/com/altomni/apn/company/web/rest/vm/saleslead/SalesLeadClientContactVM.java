package com.altomni.apn.company.web.rest.vm.saleslead;

import com.altomni.apn.common.dto.talent.TalentContactDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SalesLeadClientContactVM {

    private Long id;

    private String name;

    private String firstName;

    private String lastName;

    private String company;

    private String title;

    private List<TalentContactDTO> contacts;

    private Integer contactCategory;

    private String otherCategory;

    private String department;

    private String remark;

    private boolean active = true;

    private String businessGroup;

    private String businessUnit;

    private Long companyAddressId;

    private String address;

    private String address2;

    private String city;

    private String province;

    private String country;

    private List<String> linkedinProfile;

    private Instant lastFollowUpTime;

    private Long tenantId;

    private String esId;

    private Boolean receiveEmail;

    private Long approverId;

    private Boolean inactived;

    private String zipcode;

    private Long createUserId;

}
