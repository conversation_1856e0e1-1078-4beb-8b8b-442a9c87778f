package com.altomni.apn.company.service.dto.contract;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
*
* <AUTHOR>
* date:2023-04-18
*/
@Data
@ApiModel(description = "signer for contract")
public class SignerDTO implements Serializable {

    @ApiModelProperty(value = "the id for contract signer.")
    @NotNull
    private Long signerId;

}
