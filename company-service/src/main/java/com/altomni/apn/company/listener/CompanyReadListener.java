package com.altomni.apn.company.listener;

import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import com.altomni.apn.company.web.rest.dto.fileUpload.CompanyUpdateNamesDTO;

import java.util.ArrayList;
import java.util.List;

public class CompanyReadListener extends AnalysisEventListener<CompanyUpdateNamesDTO> {
    private final List<CompanyUpdateNamesDTO> dataList = new ArrayList<>();

    @Override
    public void invoke(CompanyUpdateNamesDTO companyUpdateNamesDTO, AnalysisContext context) {
        dataList.add(companyUpdateNamesDTO); // 逐行处理数据
        if (dataList.size() % 10000 == 0) {
            System.out.println("已读取 " + dataList.size() + " 条数据");
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        System.out.println("总计读取数据量：" + dataList.size());
    }

    public List<CompanyUpdateNamesDTO> getDataList() {
        return dataList;
    }
}