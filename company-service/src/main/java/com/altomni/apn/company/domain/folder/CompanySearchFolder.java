package com.altomni.apn.company.domain.folder;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderType;
import com.altomni.apn.company.domain.enumeration.folder.CategoryFolderTypeConverter;
import com.altomni.apn.company.service.dto.folder.SearchFolderDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
/**
 * A CompanySearchFolder.
 */
@Entity
@Table(name = "company_search_folder")
@Data
public class CompanySearchFolder extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "search_criteria")
    private String searchCriteria;

    @Column(name = "search_category")
    @Convert(converter = CategoryFolderTypeConverter.class)
    private CategoryFolderType searchCategory;

    @Column(name = "company_folder_id")
    private Long companyFolderId;

    public static CompanySearchFolder fromSearchFolderDTO(SearchFolderDTO searchFolderDTO) {
        CompanySearchFolder searchFolder = new CompanySearchFolder();
        ServiceUtils.myCopyProperties(searchFolderDTO, searchFolder);
        return searchFolder;
    }

}
