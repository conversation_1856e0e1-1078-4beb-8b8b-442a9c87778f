package com.altomni.apn.company.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.application.InterviewType;
import com.altomni.apn.common.domain.enumeration.application.InterviewTypeConverter;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.company.domain.enumeration.job.InstantExcelDateConverter;
import com.altomni.apn.company.domain.enumeration.job.NodeTypeConverter;
import com.altomni.apn.company.domain.enumeration.report.EventStageDateConverter;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.Instant;


@Data
@Entity
@Accessors(chain = true)
public class PerformanceDataVO implements AttachConfidentialTalent {


    @ColumnWidth(50)
    @ExcelProperty(value = "Candidate Name",index = 0)
    private String fullName;

    @ColumnWidth(20)
    @ExcelProperty(value = "Applied Job Title",index = 1)
    private String jobTitle;


    @ColumnWidth(30)
    @ExcelProperty(value = "Submitted Date",index = 4, converter = InstantExcelDateConverter.class)
    //submit to client date
    private Instant submitDate;

    @ColumnWidth(20)
    @ExcelProperty(value = "Current Status",index = 2, converter = EventStageDateConverter.class)
    @Convert(converter = NodeTypeConverter.class)
    private NodeType activityStatus;

    @ColumnWidth(25)
    @ExcelProperty(value = "HR Contact",index = 5)
    private String  hrContact;

    @ColumnWidth(25)
    @ExcelProperty(value = "Recruiter",index = 6)
    private String  recruiter;

    @ColumnWidth(25)
    @ExcelProperty(value = "Sourcer",index = 7)
    private String sourcer;

    @ColumnWidth(25)
    @ExcelProperty(value = "Interview Info",index = 3)
    private String interviewInfo;

    @ColumnWidth(30)
    @ExcelProperty(value = "Last Status Update Date",index = 8, converter = InstantExcelDateConverter.class)
    private Instant statusUpdateDate;

    @ExcelIgnore
    private Instant latestInterviewDate;

    @ExcelIgnore
    private Long candidateId;

    @ExcelIgnore
    @Convert(converter = InterviewTypeConverter.class)
    private InterviewType eventType;

    @ExcelIgnore
    private Integer eventStage;

    @ExcelIgnore
    private String eventTimeZone;

    @ExcelIgnore
    private Long jobId;

    @ExcelIgnore
    @Convert(converter = JobTypeConverter.class)
    private JobType jobType;

    @ExcelIgnore
    private Long applicationId;

    @ExcelIgnore
    @Id
    private Long  id;

    @ExcelIgnore
    private Instant jobCreatedDate;

    @ExcelIgnore
    private Long pteamId;

    @ExcelIgnore
    @Transient
    private Boolean isPrivateJob;

    @ExcelIgnore
    @Transient
    private Integer interviewCount;

    /**
     * true: 已离职，false: 未离职
     */
    @ExcelIgnore
    private Boolean resigned;

    /**
     * true: 是 converted to FTE 流程
     * false: 非 converted to FTE 流程
     */
    @ExcelIgnore
    private Boolean convertedToFte;

    @Transient
    @ExcelIgnore
    private Boolean confidentialTalentViewAble;

    @Transient
    @ExcelIgnore
    private ConfidentialInfoDto confidentialInfo;

    @Override
    public Long getTalentId() {
        return candidateId;
    }

    @Override
    public void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble) {
        this.confidentialTalentViewAble = confidentialTalentViewAble;
    }

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        this.confidentialInfo = confidentialInfo;
    }

    @Override
    public void encrypt() {
        this.fullName = null;
        this.applicationId = null;
        this.jobId = null;
        this.jobTitle = null;
        this.jobCreatedDate = null;
        this.jobType = null;
        this.submitDate = null;
        this.hrContact = null;
        this.interviewInfo = null;
        this.latestInterviewDate = null;
        this.eventType = null;
        this.eventStage = null;
        this.eventTimeZone = null;
        this.pteamId = null;
        this.statusUpdateDate = null;
        this.recruiter = null;
        this.sourcer = null;
        this.resigned = null;
        this.convertedToFte = null;
        this.isPrivateJob = null;
        this.interviewCount = null;
    }
}
