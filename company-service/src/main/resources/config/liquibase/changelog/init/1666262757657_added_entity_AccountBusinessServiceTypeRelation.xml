<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="*************-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="account_business_service_type_relation"/>
            </not>
        </preConditions>
        <createTable tableName="account_business_service_type_relation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="account_business_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="service_type_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="company_sales_lead_service_type_sales_lead_id_index"
                     tableName="account_business_service_type_relation">
            <column name="account_business_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
