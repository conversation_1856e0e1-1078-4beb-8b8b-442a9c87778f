<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666262757668-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_client_note"/>
            </not>
        </preConditions>
        <createTable tableName="company_client_note">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="note" type="MEDIUMTEXT">
                <constraints nullable="false"/>
            </column>
            <column name="last_contact_date" type="timestamp"/>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
<!--            <column name="synced" type="BIT"/>-->
<!--            <column name="reminder_time" type="timestamp"/>-->
            <column name="deleted" type="BIT">
                <constraints nullable="false"/>
            </column>
            <column name="last_sync_time" type="timestamp"/>
            <column name="category" type="TINYINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_company_client_note_company_id" tableName="company_client_note">
            <column name="company_id"/>
        </createIndex>
        
    </changeSet>

</databaseChangeLog>
