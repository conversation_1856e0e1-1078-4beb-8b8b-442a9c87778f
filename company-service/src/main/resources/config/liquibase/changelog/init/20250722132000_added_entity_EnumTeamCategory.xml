<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20250722132000-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="enum_team_category"/>
            </not>
        </preConditions>
        <createTable tableName="enum_team_category">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(128)">
                <constraints nullable="false"/>
            </column>
            <column name="en_display" type="VARCHAR(128)"/>
            <column name="cn_display" type="VARCHAR(128)"/>
            <column name="en_display_order" type="INT"/>
            <column name="cn_display_order" type="INT"/>
        </createTable>

    </changeSet>

</databaseChangeLog>
