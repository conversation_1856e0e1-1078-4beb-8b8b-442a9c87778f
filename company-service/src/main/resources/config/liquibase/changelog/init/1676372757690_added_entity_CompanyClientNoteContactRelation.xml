<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1676372757690-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_client_note_contact_relation"/>
            </not>
        </preConditions>
        <createTable tableName="company_client_note_contact_relation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="client_note_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="client_contact_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_client_note_contact_id" tableName="company_client_note_contact_relation" unique="true">
            <column name="client_note_id"/>
            <column name="client_contact_id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>
