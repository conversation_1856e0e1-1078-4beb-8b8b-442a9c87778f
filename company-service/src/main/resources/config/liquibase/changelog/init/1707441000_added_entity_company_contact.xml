<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="generated" id="1707441000-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_contact"/>
            </not>
        </preConditions>
        <createTable tableName="company_contact">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="type" type="TINYINT">
                <constraints nullable="false"/>
            </column>
            <column name="contact" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="details" type="VARCHAR(400)"/>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="timestamp(3)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp(3)"/>
        </createTable>
        <createIndex indexName="idx_company_contact_company_id" tableName="company_contact">
            <column name="company_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
