<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1676372757710-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_client_info"/>
            </not>
        </preConditions>
        <createTable tableName="company_client_info">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="company_id" type="BIGINT"/>
            <column name="tenant_id" type="BIGINT"/>
            <column name="client_name" type="VARCHAR(255)"/>
            <column name="client_division" type="VARCHAR(255)"/>
            <column name="client_address" type="VARCHAR(255)"/>
            <column name="client_location" type="VARCHAR(255)"/>
            <column name="client_email" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="timestamp(3)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp(3)"/>
        </createTable>

    </changeSet>

</databaseChangeLog>
