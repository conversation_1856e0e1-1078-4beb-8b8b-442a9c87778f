<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263313749-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company"/>
            </not>
        </preConditions>
        <createTable tableName="company">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="logo" type="VARCHAR(255)"/>
            <column name="full_business_name" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
<!--            <column name="website" type="VARCHAR(255)"/>-->
            <column name="client_level_type" type="TINYINT"/>
            <column name="active" type="TINYINT"/>
            <column name="last_sync_time" type="timestamp"/>
<!--            <column name="phone" type="VARCHAR(100)"/>-->
            <column name="last_edited_time" type="timestamp"/>
            <column name="tenant_id" type="BIGINT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <addUniqueConstraint columnNames="id, tenant_id" constraintName="idx_tenantId_id_type"
                             tableName="company"/>
        <createIndex indexName="idx_company_name" tableName="company">
            <column name="name"/>
        </createIndex>
        <createIndex indexName="idx_company_tenant_id" tableName="company">
            <column name="tenant_id"/>
        </createIndex>
        <createIndex indexName="idx_company_tenant_created_date" tableName="company">
            <column name="tenant_id"/>
            <column name="created_date"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
