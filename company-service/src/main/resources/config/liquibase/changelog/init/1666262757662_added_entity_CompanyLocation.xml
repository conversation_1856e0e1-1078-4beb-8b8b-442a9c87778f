<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666262757662-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_location"/>
            </not>
        </preConditions>
        <createTable tableName="company_location">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="official_country" type="VARCHAR(200)"/>
            <column name="official_county" type="VARCHAR(200)"/>
            <column name="official_province" type="VARCHAR(200)"/>
            <column name="official_city" type="VARCHAR(200)"/>

            <column name="original_loc" type="TEXT"/>
        </createTable>

        <createIndex indexName="idx_company_location_company_id" tableName="company_location">
            <column name="company_id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>
