<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="20241028091000" author="generated">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="contact_service_type_relation"/>
            </not>
        </preConditions>
        <createTable tableName="contact_service_type_relation">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="contact_id" type="BIGINT"/>
            <column name="service_type_id" type="INT"/>
        </createTable>
        <createIndex indexName="idx_contact_service_type_relation_contact_id" tableName="contact_service_type_relation">
            <column name="contact_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
