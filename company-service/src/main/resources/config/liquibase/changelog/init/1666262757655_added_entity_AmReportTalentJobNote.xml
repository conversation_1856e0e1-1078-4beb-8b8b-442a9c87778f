<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263085690-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="am_report_talent_job_note"/>
            </not>
        </preConditions>
        <createTable tableName="am_report_talent_job_note">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="job_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="node_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="high_lighted_experience" type="VARCHAR(500)"/>
            <column name="am_update" type="VARCHAR(5000)"/>
            <column name="frequency" type="INT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_artjn_talent_id_job_id" tableName="am_report_talent_job_note">
            <column name="talent_id"/>
            <column name="job_id"/>
            <column name="frequency"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
