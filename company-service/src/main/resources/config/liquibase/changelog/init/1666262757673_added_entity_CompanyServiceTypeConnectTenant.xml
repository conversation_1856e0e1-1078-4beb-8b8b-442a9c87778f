<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666262757673-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_service_type_connect_tenant"/>
            </not>
        </preConditions>
        <createTable tableName="company_service_type_connect_tenant">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="enum_company_service_type_id" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_company_service_type_connect_tenant_tenant_id" tableName="company_service_type_connect_tenant">
            <column name="tenant_id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>
