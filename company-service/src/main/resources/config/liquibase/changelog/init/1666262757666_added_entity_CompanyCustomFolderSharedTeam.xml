<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666262757666-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_custom_folder_shared_team"/>
            </not>
        </preConditions>
        <createTable tableName="company_custom_folder_shared_team">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="folder_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="team_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="permission" type="TINYINT">
                <constraints nullable="false"/>
            </column>
            <column name="ignore_team_user" type="TEXT"/>

            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_company_custom_folder_shared_team_folder_id_team_id" tableName="company_custom_folder_shared_team" unique="true">
            <column name="folder_id"/>
            <column name="team_id"/>
        </createIndex>
        
    </changeSet>

</databaseChangeLog>
