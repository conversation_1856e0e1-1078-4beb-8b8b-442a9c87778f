<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263428214-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_sales_lead_client_contact"/>
            </not>
        </preConditions>
        <createTable tableName="company_sales_lead_client_contact">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="contact_category" type="INT"/>
            <column name="status" type="BIT(1)"/>
            <column name="last_contact_date" type="datetime"/>
            <column name="tenant_id" type="BIGINT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="receive_email" type="BIT(1)"/>
            <column name="approver_id" type="BIGINT"/>
            <column name="inactived" type="BIT(1)"/>
            <column name="zipcode" type="VARCHAR(255)"/>
            <column name="es_id" type="VARCHAR(255)"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
            <column name="company_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="is_key_contact" type="BIT(1)" defaultValue="0">
                <constraints nullable="false"/>
            </column>
<!--            <column name="company_location_id" type="BIGINT"/>-->
            <column name="crm_contact_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_client_contact_tenant_id" tableName="company_sales_lead_client_contact">
            <column name="tenant_id"/>
        </createIndex>
        <createIndex indexName="idx_client_contact_company_id" tableName="company_sales_lead_client_contact">
            <column name="company_id"/>
        </createIndex>
        <createIndex indexName="idx_client_contact_talent_id" tableName="company_sales_lead_client_contact">
            <column name="talent_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
