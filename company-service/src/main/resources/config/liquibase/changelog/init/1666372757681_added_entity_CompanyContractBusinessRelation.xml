<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666372757681-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="company_contract_business_relation"/>
            </not>
        </preConditions>
        <createTable tableName="company_contract_business_relation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="contract_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="account_business_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_company_contract_sales_lead_relation_contract_id" tableName="company_contract_business_relation">
            <column name="contract_id"/>
        </createIndex>

        <createIndex indexName="idx_company_contract_sales_lead_relation_business_id" tableName="company_contract_business_relation">
            <column name="account_business_id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>
