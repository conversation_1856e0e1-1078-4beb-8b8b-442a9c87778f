<?xml version='1.0' encoding='utf-8'?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <include file="config/liquibase/changelog/init/1666262757655_added_entity_AmReportTalentJobNote.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757656_added_entity_Company.xml" relativeToChangelogFile="false"/>
<!--    <include file="config/liquibase/changelog/init/1666262757656_added_entity_CompanyAddress.xml" relativeToChangelogFile="false"/>-->
<!--    <include file="config/liquibase/changelog/init/1666262757656_added_entity_CompanyAssignTeamMember.xml" relativeToChangelogFile="false"/>-->
    <include file="config/liquibase/changelog/init/1666262757656_added_entity_CompanyContract.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757656_added_entity_CompanyContractSigner.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_CompanyProgressNote.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_CompanyProjectTeam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_CompanyProjectTeamUser.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_AccountBusiness.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_BusinessFlowAdministrator.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_CompanySalesLeadClientContact.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_AccountBusinessContactRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_AccountBusinessServiceTypeRelation.xml" relativeToChangelogFile="false"/>
<!--    <include file="config/liquibase/changelog/init/1666262757657_added_entity_CompanyServiceType.xml" relativeToChangelogFile="false"/>-->
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_SkipSubmitToAmCompany.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_SkipSubmitToAmCompanyUser.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/1666262757662_added_entity_CompanyLocation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_CompanySearchFolder.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_CompanyCustomFolder.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757665_added_entity_CompanyCustomFolderConnectClient.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_CompanyCustomFolderSharedTeam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757667_added_entity_CompanyCustomFolderSharedUser.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757668_added_entity_CompanyClientNote.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757669_added_entity_CompanyParamConfig.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757670_added_entity_EnumCompanySaleLeadSource.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757670_added_entity_EnumCompanyServiceType.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757671_added_entity_CompanyAdditionalInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757672_added_entity_CompanyIndustryRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757673_added_entity_CompanyContactAdditionalInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757673_added_entity_CompanySalesLeadSourceConnectTenant.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757673_added_entity_CompanyServiceTypeConnectTenant.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757684_added_entity_CompanyContractServiceType.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/1666362757661_added_entity_EnumCompanyTag.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666372757661_added_entity_CompanyTagRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666372757681_added_entity_CompanyContractBusinessRelation.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/1676362767661_added_entity_EnumCompanyContactTag.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1676372757661_added_entity_CompanyContactTagRelation.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/1676372757680_added_entity_CompanyProgressNoteContactRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1676372757690_added_entity_CompanyClientNoteContactRelation.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/1707441000_added_entity_company_contact.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1707442500_added_entity_enum_company_contact_category.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1707442800_added_entity_enum_company_client_level.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1676372757710_added_entity_CompanyClientInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20241028091000_added_entity_contact_service_type_relation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250722132000_added_entity_EnumTeamCategory.xml" relativeToChangelogFile="false"/>

</databaseChangeLog>